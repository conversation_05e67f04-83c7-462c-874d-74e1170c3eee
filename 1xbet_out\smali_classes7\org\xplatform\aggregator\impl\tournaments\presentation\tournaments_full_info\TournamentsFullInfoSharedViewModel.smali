.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$a;,
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$b;,
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;,
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$d;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00fe\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0011\n\u0002\u0010\u0003\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008+\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010%\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0008\u0000\u0018\u0000 \u00eb\u00012\u00020\u0001:\u0006\u00ec\u0001\u00ed\u0001\u00ee\u0001B\u00b9\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u00a2\u0006\u0004\u0008.\u0010/J\u000f\u00101\u001a\u000200H\u0007\u00a2\u0006\u0004\u00081\u00102J\u0013\u00105\u001a\u0008\u0012\u0004\u0012\u00020403\u00a2\u0006\u0004\u00085\u00106J\u0013\u00109\u001a\u0008\u0012\u0004\u0012\u00020807\u00a2\u0006\u0004\u00089\u0010:J\u000f\u0010<\u001a\u00020;H\u0014\u00a2\u0006\u0004\u0008<\u0010=J\u001d\u0010A\u001a\u00020;2\u0006\u0010>\u001a\u00020\u00122\u0006\u0010@\u001a\u00020?\u00a2\u0006\u0004\u0008A\u0010BJ\u0015\u0010D\u001a\u00020;2\u0006\u0010C\u001a\u00020?\u00a2\u0006\u0004\u0008D\u0010EJ\u001d\u0010H\u001a\u00020;2\u0006\u0010F\u001a\u00020\u001c2\u0006\u0010G\u001a\u00020\u0018\u00a2\u0006\u0004\u0008H\u0010IJ\u0015\u0010J\u001a\u00020;2\u0006\u0010F\u001a\u00020\u001c\u00a2\u0006\u0004\u0008J\u0010KJ\u0015\u0010M\u001a\u00020;2\u0006\u0010L\u001a\u00020\u0018\u00a2\u0006\u0004\u0008M\u0010NJ\r\u0010O\u001a\u00020;\u00a2\u0006\u0004\u0008O\u0010=J\r\u0010P\u001a\u00020;\u00a2\u0006\u0004\u0008P\u0010=J\u0015\u0010S\u001a\u00020;2\u0006\u0010R\u001a\u00020Q\u00a2\u0006\u0004\u0008S\u0010TJ\u0015\u0010U\u001a\u00020;2\u0006\u0010F\u001a\u00020\u001c\u00a2\u0006\u0004\u0008U\u0010KJ\u001d\u0010X\u001a\u00020;2\u0006\u0010W\u001a\u00020V2\u0006\u0010F\u001a\u00020\u001c\u00a2\u0006\u0004\u0008X\u0010YJ\r\u0010Z\u001a\u00020;\u00a2\u0006\u0004\u0008Z\u0010=J\r\u0010[\u001a\u00020;\u00a2\u0006\u0004\u0008[\u0010=J+\u0010`\u001a\u00020;2\u0006\u0010F\u001a\u00020\u001c2\u0006\u0010\\\u001a\u00020\u00182\u000c\u0010_\u001a\u0008\u0012\u0004\u0012\u00020^0]\u00a2\u0006\u0004\u0008`\u0010aJ-\u0010e\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020d0c032\u0006\u0010\u0019\u001a\u00020\u00182\u0008\u0010b\u001a\u0004\u0018\u00010\u0018H\u0002\u00a2\u0006\u0004\u0008e\u0010fJ/\u0010i\u001a\u00020;2\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010h\u001a\u00020g2\u0006\u0010\u001d\u001a\u00020\u001c2\u0006\u0010F\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008i\u0010jJ\'\u0010m\u001a\u00020;2\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010l\u001a\u00020k2\u0006\u0010F\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008m\u0010nJ\u0017\u0010o\u001a\u00020?2\u0006\u0010l\u001a\u00020kH\u0002\u00a2\u0006\u0004\u0008o\u0010pJ\u0015\u0010r\u001a\u0008\u0012\u0004\u0012\u00020q03H\u0002\u00a2\u0006\u0004\u0008r\u00106J\u0017\u0010t\u001a\u00020;2\u0006\u0010s\u001a\u00020QH\u0002\u00a2\u0006\u0004\u0008t\u0010TJ\u000f\u0010v\u001a\u00020uH\u0002\u00a2\u0006\u0004\u0008v\u0010wJ\u000f\u0010x\u001a\u00020;H\u0002\u00a2\u0006\u0004\u0008x\u0010=R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008y\u0010zR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008{\u0010|R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008}\u0010~R\u0015\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u007f\u0010\u0080\u0001R\u0016\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0081\u0001\u0010\u0082\u0001R\u0016\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0083\u0001\u0010\u0084\u0001R\u0016\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0085\u0001\u0010\u0086\u0001R\u0016\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0087\u0001\u0010\u0088\u0001R\u0016\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0089\u0001\u0010\u008a\u0001R\u0016\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008b\u0001\u0010\u008c\u0001R\u0016\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008d\u0001\u0010\u008e\u0001R\u0016\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008f\u0001\u0010\u0090\u0001R\u0016\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0091\u0001\u0010\u0092\u0001R\u0016\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0093\u0001\u0010\u0094\u0001R\u0016\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0095\u0001\u0010\u0096\u0001R\u0016\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0097\u0001\u0010\u0098\u0001R\u0016\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0099\u0001\u0010\u009a\u0001R\u0016\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009b\u0001\u0010\u009c\u0001R\u0016\u0010)\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009d\u0001\u0010\u009e\u0001R\u0016\u0010+\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009f\u0001\u0010\u00a0\u0001R\u001c\u0010\u00a4\u0001\u001a\u0005\u0018\u00010\u00a1\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a2\u0001\u0010\u00a3\u0001R\u001c\u0010\u00a6\u0001\u001a\u0005\u0018\u00010\u00a1\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a5\u0001\u0010\u00a3\u0001R\u0019\u0010\u00a9\u0001\u001a\u00020?8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a7\u0001\u0010\u00a8\u0001R\u001e\u0010\u00ad\u0001\u001a\t\u0012\u0004\u0012\u0002040\u00aa\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ab\u0001\u0010\u00ac\u0001R\u001e\u0010\u00b1\u0001\u001a\n\u0012\u0005\u0012\u00030\u00af\u00010\u00ae\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008Z\u0010\u00b0\u0001R\u001e\u0010\u00b5\u0001\u001a\t\u0012\u0004\u0012\u00020;0\u00b2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b3\u0001\u0010\u00b4\u0001R\u001e\u0010\u00b7\u0001\u001a\t\u0012\u0004\u0012\u00020\u00120\u00ae\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b6\u0001\u0010\u00b0\u0001R\u001f\u0010\u00ba\u0001\u001a\n\u0012\u0005\u0012\u00030\u00b8\u00010\u00ae\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b9\u0001\u0010\u00b0\u0001R\u001e\u0010\u00be\u0001\u001a\t\u0012\u0004\u0012\u00020q0\u00bb\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bc\u0001\u0010\u00bd\u0001R\u0018\u0010\u00c2\u0001\u001a\u00030\u00bf\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c0\u0001\u0010\u00c1\u0001R\u0017\u0010\u00c5\u0001\u001a\u0002008\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c3\u0001\u0010\u00c4\u0001R$\u0010\u00c9\u0001\u001a\u000f\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020d0\u00c6\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c7\u0001\u0010\u00c8\u0001R\u001c\u0010\u00cd\u0001\u001a\u00020?8\u0006\u00a2\u0006\u0010\n\u0006\u0008\u00ca\u0001\u0010\u00a8\u0001\u001a\u0006\u0008\u00cb\u0001\u0010\u00cc\u0001R+\u0010\u00d3\u0001\u001a\u0011\u0012\u000c\u0012\n\u0012\u0005\u0012\u00030\u00cf\u00010\u00ce\u00010\u00bb\u00018\u0006\u00a2\u0006\u0010\n\u0006\u0008\u00d0\u0001\u0010\u00bd\u0001\u001a\u0006\u0008\u00d1\u0001\u0010\u00d2\u0001R+\u0010\u00d7\u0001\u001a\u0011\u0012\u000c\u0012\n\u0012\u0005\u0012\u00030\u00d4\u00010\u00ce\u00010\u00bb\u00018\u0006\u00a2\u0006\u0010\n\u0006\u0008\u00d5\u0001\u0010\u00bd\u0001\u001a\u0006\u0008\u00d6\u0001\u0010\u00d2\u0001R+\u0010\u00db\u0001\u001a\u0011\u0012\u000c\u0012\n\u0012\u0005\u0012\u00030\u00d8\u00010\u00ce\u00010\u00bb\u00018\u0006\u00a2\u0006\u0010\n\u0006\u0008\u00d9\u0001\u0010\u00bd\u0001\u001a\u0006\u0008\u00da\u0001\u0010\u00d2\u0001R+\u0010\u00df\u0001\u001a\u0011\u0012\u000c\u0012\n\u0012\u0005\u0012\u00030\u00dc\u00010\u00ce\u00010\u00bb\u00018\u0006\u00a2\u0006\u0010\n\u0006\u0008\u00dd\u0001\u0010\u00bd\u0001\u001a\u0006\u0008\u00de\u0001\u0010\u00d2\u0001R+\u0010\u00e3\u0001\u001a\u0011\u0012\u000c\u0012\n\u0012\u0005\u0012\u00030\u00e0\u00010\u00ce\u00010\u00bb\u00018\u0006\u00a2\u0006\u0010\n\u0006\u0008\u00e1\u0001\u0010\u00bd\u0001\u001a\u0006\u0008\u00e2\u0001\u0010\u00d2\u0001R(\u0010\u00e8\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e4\u00010c038\u0006\u00a2\u0006\u000f\n\u0006\u0008\u00e5\u0001\u0010\u00e6\u0001\u001a\u0005\u0008\u00e7\u0001\u00106R\u001e\u0010\u00ea\u0001\u001a\t\u0012\u0004\u0012\u00020?0\u00ae\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e9\u0001\u0010\u00b0\u0001\u00a8\u0006\u00ef\u0001"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lw81/c;",
        "getTournamentFullInfoScenario",
        "Lgk/b;",
        "getCurrencyByIdUseCase",
        "Lm8/a;",
        "dispatchers",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lw81/g;",
        "takePartTournamentsScenario",
        "Lw81/d;",
        "getTournamentGamesScenario",
        "Lw81/e;",
        "getTournamentsConditionsGamesScenario",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lorg/xplatform/aggregator/api/navigation/TournamentsPage;",
        "startPage",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "openGameDelegate",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "",
        "tournamentId",
        "LHX0/e;",
        "resourceManager",
        "",
        "tournamentTitle",
        "LwX0/C;",
        "routerHolder",
        "LP91/b;",
        "aggregatorNavigator",
        "Lorg/xbet/analytics/domain/scope/g;",
        "aggregatorTournamentsAnalytics",
        "Lorg/xbet/analytics/domain/scope/g0;",
        "myAggregatorAnalytics",
        "LnR/a;",
        "aggregatorGamesFatmanLogger",
        "LnR/d;",
        "aggregatorTournamentFatmanLogger",
        "Leu/i;",
        "getCurrentCountryIdUseCase",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "<init>",
        "(Lw81/c;Lgk/b;Lm8/a;Lorg/xbet/ui_common/utils/internet/a;Lw81/g;Lw81/d;Lw81/e;Lorg/xbet/ui_common/utils/M;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LSX0/c;JLHX0/e;Ljava/lang/String;LwX0/C;LP91/b;Lorg/xbet/analytics/domain/scope/g;Lorg/xbet/analytics/domain/scope/g0;LnR/a;LnR/d;Leu/i;Lorg/xbet/remoteconfig/domain/usecases/i;)V",
        "",
        "c4",
        "()I",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$b;",
        "getEvents",
        "()Lkotlinx/coroutines/flow/e;",
        "Lkotlinx/coroutines/flow/Z;",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
        "a4",
        "()Lkotlinx/coroutines/flow/Z;",
        "",
        "onCleared",
        "()V",
        "tournamentPage",
        "",
        "openSingleGame",
        "E4",
        "(Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Z)V",
        "isSwitchingInTabs",
        "F4",
        "(Z)V",
        "screenName",
        "gameId",
        "u4",
        "(Ljava/lang/String;J)V",
        "C4",
        "(Ljava/lang/String;)V",
        "prizeId",
        "y4",
        "(J)V",
        "B4",
        "A4",
        "",
        "error",
        "m4",
        "(Ljava/lang/Throwable;)V",
        "z4",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;",
        "buttonAction",
        "q4",
        "(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Ljava/lang/String;)V",
        "H4",
        "D4",
        "conditionId",
        "",
        "Lkb1/l;",
        "items",
        "r4",
        "(Ljava/lang/String;JLjava/util/List;)V",
        "stageId",
        "Landroidx/paging/PagingData;",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "i4",
        "(JLjava/lang/Long;)Lkotlinx/coroutines/flow/e;",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "kind",
        "w4",
        "(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V",
        "Lh81/b;",
        "result",
        "o4",
        "(JLh81/b;Ljava/lang/String;)V",
        "G4",
        "(Lh81/b;)Z",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;",
        "n4",
        "throwable",
        "s4",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "b4",
        "()Lorg/xbet/uikit/components/lottie_empty/n;",
        "p4",
        "v1",
        "Lw81/c;",
        "x1",
        "Lgk/b;",
        "y1",
        "Lm8/a;",
        "F1",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "H1",
        "Lw81/g;",
        "I1",
        "Lw81/d;",
        "P1",
        "Lw81/e;",
        "S1",
        "Lorg/xbet/ui_common/utils/M;",
        "V1",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "b2",
        "LSX0/c;",
        "v2",
        "J",
        "x2",
        "LHX0/e;",
        "y2",
        "Ljava/lang/String;",
        "F2",
        "LwX0/C;",
        "H2",
        "LP91/b;",
        "I2",
        "Lorg/xbet/analytics/domain/scope/g;",
        "P2",
        "Lorg/xbet/analytics/domain/scope/g0;",
        "S2",
        "LnR/a;",
        "V2",
        "LnR/d;",
        "X2",
        "Leu/i;",
        "Lkotlinx/coroutines/x0;",
        "F3",
        "Lkotlinx/coroutines/x0;",
        "connectionJob",
        "H3",
        "openSingleGameJob",
        "I3",
        "Z",
        "firstGamesLoaded",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "S3",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "eventsFlow",
        "Lkotlinx/coroutines/flow/V;",
        "Lkb1/E;",
        "Lkotlinx/coroutines/flow/V;",
        "errorFlow",
        "Lkotlinx/coroutines/flow/U;",
        "X4",
        "Lkotlinx/coroutines/flow/U;",
        "refreshFlow",
        "v5",
        "currentPage",
        "Lkb1/k;",
        "w5",
        "currentConditions",
        "Lkotlinx/coroutines/flow/f0;",
        "x5",
        "Lkotlinx/coroutines/flow/f0;",
        "fullInfoStateFlow",
        "Lek0/o;",
        "y5",
        "Lek0/o;",
        "remoteConfig",
        "z5",
        "I",
        "initialGamesStyle",
        "",
        "A5",
        "Ljava/util/Map;",
        "gamesMap",
        "B5",
        "l4",
        "()Z",
        "virtual",
        "Lkb1/F;",
        "Lkb1/u;",
        "C5",
        "f4",
        "()Lkotlinx/coroutines/flow/f0;",
        "tournamentResultState",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;",
        "D5",
        "g4",
        "tournamentsContainerState",
        "Lkb1/f;",
        "E5",
        "d4",
        "tournamentConditionState",
        "Lkb1/n;",
        "F5",
        "e4",
        "tournamentMainInfoState",
        "Lkb1/m;",
        "G5",
        "k4",
        "tournamentsGamesScreenState",
        "LN21/d;",
        "H5",
        "Lkotlinx/coroutines/flow/e;",
        "h4",
        "tournamentsGamesPaging",
        "I5",
        "switchingInTabsState",
        "J5",
        "c",
        "b",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final J5:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final A5:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Long;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B5:Z

.field public final C5:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Lkb1/F<",
            "Lkb1/u;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final D5:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Lkb1/F<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final E5:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Lkb1/F<",
            "Lkb1/f;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F1:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public F3:Lkotlinx/coroutines/x0;

.field public final F5:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Lkb1/F<",
            "Lkb1/n;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final G5:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Lkb1/F<",
            "Lkb1/m;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lw81/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:LP91/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public H3:Lkotlinx/coroutines/x0;

.field public final H4:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lkb1/E;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H5:Lkotlinx/coroutines/flow/e;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/e<",
            "Landroidx/paging/PagingData<",
            "LN21/d;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lw81/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lorg/xbet/analytics/domain/scope/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public I3:Z

.field public final I5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lw81/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lorg/xbet/analytics/domain/scope/g0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:LnR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S3:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:LnR/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X2:Leu/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X4:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lw81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:J

.field public final v5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/api/navigation/TournamentsPage;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lkb1/k;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Lgk/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x5:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y5:Lek0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z5:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->J5:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$a;

    return-void
.end method

.method public constructor <init>(Lw81/c;Lgk/b;Lm8/a;Lorg/xbet/ui_common/utils/internet/a;Lw81/g;Lw81/d;Lw81/e;Lorg/xbet/ui_common/utils/M;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LSX0/c;JLHX0/e;Ljava/lang/String;LwX0/C;LP91/b;Lorg/xbet/analytics/domain/scope/g;Lorg/xbet/analytics/domain/scope/g0;LnR/a;LnR/d;Leu/i;Lorg/xbet/remoteconfig/domain/usecases/i;)V
    .locals 7
    .param p1    # Lw81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lgk/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lw81/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lw81/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lw81/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xplatform/aggregator/api/navigation/TournamentsPage;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # LP91/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lorg/xbet/analytics/domain/scope/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # Lorg/xbet/analytics/domain/scope/g0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # LnR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # LnR/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # Leu/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v1:Lw81/c;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->x1:Lgk/b;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->y1:Lm8/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->F1:Lorg/xbet/ui_common/utils/internet/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H1:Lw81/g;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I1:Lw81/d;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->P1:Lw81/e;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->S1:Lorg/xbet/ui_common/utils/M;

    .line 19
    .line 20
    move-object/from16 p1, p10

    .line 21
    .line 22
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->V1:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 23
    .line 24
    move-object/from16 p1, p11

    .line 25
    .line 26
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->b2:LSX0/c;

    .line 27
    .line 28
    move-wide/from16 p1, p12

    .line 29
    .line 30
    iput-wide p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v2:J

    .line 31
    .line 32
    move-object/from16 p1, p14

    .line 33
    .line 34
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->x2:LHX0/e;

    .line 35
    .line 36
    move-object/from16 p1, p15

    .line 37
    .line 38
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->y2:Ljava/lang/String;

    .line 39
    .line 40
    move-object/from16 p1, p16

    .line 41
    .line 42
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->F2:LwX0/C;

    .line 43
    .line 44
    move-object/from16 p1, p17

    .line 45
    .line 46
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H2:LP91/b;

    .line 47
    .line 48
    move-object/from16 p1, p18

    .line 49
    .line 50
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I2:Lorg/xbet/analytics/domain/scope/g;

    .line 51
    .line 52
    move-object/from16 p1, p19

    .line 53
    .line 54
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->P2:Lorg/xbet/analytics/domain/scope/g0;

    .line 55
    .line 56
    move-object/from16 p1, p20

    .line 57
    .line 58
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->S2:LnR/a;

    .line 59
    .line 60
    move-object/from16 p1, p21

    .line 61
    .line 62
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->V2:LnR/d;

    .line 63
    .line 64
    move-object/from16 p1, p22

    .line 65
    .line 66
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->X2:Leu/i;

    .line 67
    .line 68
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 69
    .line 70
    sget-object p2, Lkotlinx/coroutines/channels/BufferOverflow;->DROP_OLDEST:Lkotlinx/coroutines/channels/BufferOverflow;

    .line 71
    .line 72
    const/4 p4, 0x1

    .line 73
    invoke-direct {p1, p4, p2}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;)V

    .line 74
    .line 75
    .line 76
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->S3:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 77
    .line 78
    sget-object p1, Lkb1/E$c;->a:Lkb1/E$c;

    .line 79
    .line 80
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 85
    .line 86
    const/4 p2, 0x6

    .line 87
    const/4 v0, 0x0

    .line 88
    const/4 v1, 0x0

    .line 89
    invoke-static {p4, v0, v1, p2, v1}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    .line 90
    .line 91
    .line 92
    move-result-object p2

    .line 93
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->X4:Lkotlinx/coroutines/flow/U;

    .line 94
    .line 95
    invoke-static/range {p9 .. p9}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 96
    .line 97
    .line 98
    move-result-object p4

    .line 99
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 100
    .line 101
    sget-object v2, Lkb1/k$b;->a:Lkb1/k$b;

    .line 102
    .line 103
    invoke-static {v2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 104
    .line 105
    .line 106
    move-result-object v2

    .line 107
    iput-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->w5:Lkotlinx/coroutines/flow/V;

    .line 108
    .line 109
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$special$$inlined$flatMapLatest$1;

    .line 110
    .line 111
    invoke-direct {v3, v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$special$$inlined$flatMapLatest$1;-><init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)V

    .line 112
    .line 113
    .line 114
    invoke-static {p2, v3}, Lkotlinx/coroutines/flow/g;->C0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 115
    .line 116
    .line 117
    move-result-object p2

    .line 118
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$fullInfoStateFlow$2;

    .line 119
    .line 120
    invoke-direct {v3, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$fullInfoStateFlow$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 121
    .line 122
    .line 123
    invoke-static {p2, v3}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 124
    .line 125
    .line 126
    move-result-object p2

    .line 127
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$fullInfoStateFlow$3;

    .line 128
    .line 129
    invoke-direct {v3, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$fullInfoStateFlow$3;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 130
    .line 131
    .line 132
    invoke-static {p2, v3}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 133
    .line 134
    .line 135
    move-result-object p2

    .line 136
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 137
    .line 138
    .line 139
    move-result-object v3

    .line 140
    sget-object v4, Lkotlinx/coroutines/flow/d0;->a:Lkotlinx/coroutines/flow/d0$a;

    .line 141
    .line 142
    invoke-virtual {v4}, Lkotlinx/coroutines/flow/d0$a;->d()Lkotlinx/coroutines/flow/d0;

    .line 143
    .line 144
    .line 145
    move-result-object v5

    .line 146
    sget-object v6, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$b;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$b;

    .line 147
    .line 148
    invoke-static {p2, v3, v5, v6}, Lkotlinx/coroutines/flow/g;->v0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlinx/coroutines/flow/d0;Ljava/lang/Object;)Lkotlinx/coroutines/flow/f0;

    .line 149
    .line 150
    .line 151
    move-result-object p2

    .line 152
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->x5:Lkotlinx/coroutines/flow/f0;

    .line 153
    .line 154
    invoke-interface/range {p23 .. p23}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 155
    .line 156
    .line 157
    move-result-object v3

    .line 158
    iput-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->y5:Lek0/o;

    .line 159
    .line 160
    invoke-virtual {v3}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 161
    .line 162
    .line 163
    move-result-object v5

    .line 164
    invoke-static {v5, v0}, Ls81/b;->b(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Z)I

    .line 165
    .line 166
    .line 167
    move-result v0

    .line 168
    iput v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->z5:I

    .line 169
    .line 170
    new-instance v0, Ljava/util/LinkedHashMap;

    .line 171
    .line 172
    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    .line 173
    .line 174
    .line 175
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->A5:Ljava/util/Map;

    .line 176
    .line 177
    invoke-virtual {v3}, Lek0/o;->y1()Z

    .line 178
    .line 179
    .line 180
    move-result v0

    .line 181
    iput-boolean v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->B5:Z

    .line 182
    .line 183
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->D4()V

    .line 184
    .line 185
    .line 186
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;

    .line 187
    .line 188
    invoke-direct {v0, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 189
    .line 190
    .line 191
    invoke-static {p2, p1, v0}, Lkotlinx/coroutines/flow/g;->o(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 192
    .line 193
    .line 194
    move-result-object v0

    .line 195
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$2;

    .line 196
    .line 197
    invoke-direct {v3, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 198
    .line 199
    .line 200
    invoke-static {v0, v3}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 201
    .line 202
    .line 203
    move-result-object v0

    .line 204
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 205
    .line 206
    .line 207
    move-result-object v3

    .line 208
    invoke-virtual {v4}, Lkotlinx/coroutines/flow/d0$a;->d()Lkotlinx/coroutines/flow/d0;

    .line 209
    .line 210
    .line 211
    move-result-object v5

    .line 212
    sget-object v6, Lkb1/F$e;->a:Lkb1/F$e;

    .line 213
    .line 214
    invoke-static {v0, v3, v5, v6}, Lkotlinx/coroutines/flow/g;->v0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlinx/coroutines/flow/d0;Ljava/lang/Object;)Lkotlinx/coroutines/flow/f0;

    .line 215
    .line 216
    .line 217
    move-result-object v0

    .line 218
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->C5:Lkotlinx/coroutines/flow/f0;

    .line 219
    .line 220
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentsContainerState$1;

    .line 221
    .line 222
    invoke-direct {v0, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentsContainerState$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 223
    .line 224
    .line 225
    invoke-static {p2, p1, p4, v0}, Lkotlinx/coroutines/flow/g;->p(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/o;)Lkotlinx/coroutines/flow/e;

    .line 226
    .line 227
    .line 228
    move-result-object p4

    .line 229
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentsContainerState$2;

    .line 230
    .line 231
    invoke-direct {v0, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentsContainerState$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 232
    .line 233
    .line 234
    invoke-static {p4, v0}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 235
    .line 236
    .line 237
    move-result-object p4

    .line 238
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 239
    .line 240
    .line 241
    move-result-object v0

    .line 242
    invoke-virtual {v4}, Lkotlinx/coroutines/flow/d0$a;->d()Lkotlinx/coroutines/flow/d0;

    .line 243
    .line 244
    .line 245
    move-result-object v3

    .line 246
    invoke-static {p4, v0, v3, v6}, Lkotlinx/coroutines/flow/g;->v0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlinx/coroutines/flow/d0;Ljava/lang/Object;)Lkotlinx/coroutines/flow/f0;

    .line 247
    .line 248
    .line 249
    move-result-object p4

    .line 250
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->D5:Lkotlinx/coroutines/flow/f0;

    .line 251
    .line 252
    new-instance p4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentConditionState$1;

    .line 253
    .line 254
    invoke-direct {p4, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentConditionState$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 255
    .line 256
    .line 257
    invoke-static {p2, p1, v2, p4}, Lkotlinx/coroutines/flow/g;->p(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/o;)Lkotlinx/coroutines/flow/e;

    .line 258
    .line 259
    .line 260
    move-result-object p4

    .line 261
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentConditionState$2;

    .line 262
    .line 263
    invoke-direct {v0, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentConditionState$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 264
    .line 265
    .line 266
    invoke-static {p4, v0}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 267
    .line 268
    .line 269
    move-result-object p4

    .line 270
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 271
    .line 272
    .line 273
    move-result-object v0

    .line 274
    invoke-interface {p3}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 275
    .line 276
    .line 277
    move-result-object v2

    .line 278
    invoke-static {v0, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 279
    .line 280
    .line 281
    move-result-object v0

    .line 282
    invoke-virtual {v4}, Lkotlinx/coroutines/flow/d0$a;->d()Lkotlinx/coroutines/flow/d0;

    .line 283
    .line 284
    .line 285
    move-result-object v2

    .line 286
    invoke-static {p4, v0, v2, v6}, Lkotlinx/coroutines/flow/g;->v0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlinx/coroutines/flow/d0;Ljava/lang/Object;)Lkotlinx/coroutines/flow/f0;

    .line 287
    .line 288
    .line 289
    move-result-object p4

    .line 290
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->E5:Lkotlinx/coroutines/flow/f0;

    .line 291
    .line 292
    new-instance p4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;

    .line 293
    .line 294
    invoke-direct {p4, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 295
    .line 296
    .line 297
    invoke-static {p2, p1, p4}, Lkotlinx/coroutines/flow/g;->o(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 298
    .line 299
    .line 300
    move-result-object p4

    .line 301
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$2;

    .line 302
    .line 303
    invoke-direct {v0, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 304
    .line 305
    .line 306
    invoke-static {p4, v0}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 307
    .line 308
    .line 309
    move-result-object p4

    .line 310
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 311
    .line 312
    .line 313
    move-result-object v0

    .line 314
    invoke-interface {p3}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 315
    .line 316
    .line 317
    move-result-object v2

    .line 318
    invoke-static {v0, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 319
    .line 320
    .line 321
    move-result-object v0

    .line 322
    invoke-virtual {v4}, Lkotlinx/coroutines/flow/d0$a;->d()Lkotlinx/coroutines/flow/d0;

    .line 323
    .line 324
    .line 325
    move-result-object v2

    .line 326
    invoke-static {p4, v0, v2, v6}, Lkotlinx/coroutines/flow/g;->v0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlinx/coroutines/flow/d0;Ljava/lang/Object;)Lkotlinx/coroutines/flow/f0;

    .line 327
    .line 328
    .line 329
    move-result-object p4

    .line 330
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->F5:Lkotlinx/coroutines/flow/f0;

    .line 331
    .line 332
    new-instance p4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentsGamesScreenState$1;

    .line 333
    .line 334
    invoke-direct {p4, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentsGamesScreenState$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 335
    .line 336
    .line 337
    invoke-static {p2, p1, p4}, Lkotlinx/coroutines/flow/g;->o(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 338
    .line 339
    .line 340
    move-result-object p1

    .line 341
    new-instance p2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentsGamesScreenState$2;

    .line 342
    .line 343
    invoke-direct {p2, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentsGamesScreenState$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 344
    .line 345
    .line 346
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 347
    .line 348
    .line 349
    move-result-object p1

    .line 350
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 351
    .line 352
    .line 353
    move-result-object p2

    .line 354
    invoke-interface {p3}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 355
    .line 356
    .line 357
    move-result-object p3

    .line 358
    invoke-static {p2, p3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 359
    .line 360
    .line 361
    move-result-object p2

    .line 362
    invoke-virtual {v4}, Lkotlinx/coroutines/flow/d0$a;->d()Lkotlinx/coroutines/flow/d0;

    .line 363
    .line 364
    .line 365
    move-result-object p3

    .line 366
    invoke-static {p1, p2, p3, v6}, Lkotlinx/coroutines/flow/g;->v0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlinx/coroutines/flow/d0;Ljava/lang/Object;)Lkotlinx/coroutines/flow/f0;

    .line 367
    .line 368
    .line 369
    move-result-object p1

    .line 370
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->G5:Lkotlinx/coroutines/flow/f0;

    .line 371
    .line 372
    new-instance p2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$special$$inlined$filter$1;

    .line 373
    .line 374
    invoke-direct {p2, p1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$special$$inlined$filter$1;-><init>(Lkotlinx/coroutines/flow/e;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)V

    .line 375
    .line 376
    .line 377
    new-instance p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$special$$inlined$flatMapLatest$2;

    .line 378
    .line 379
    invoke-direct {p1, v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$special$$inlined$flatMapLatest$2;-><init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)V

    .line 380
    .line 381
    .line 382
    invoke-static {p2, p1}, Lkotlinx/coroutines/flow/g;->C0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 383
    .line 384
    .line 385
    move-result-object p1

    .line 386
    new-instance p2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentsGamesPaging$3;

    .line 387
    .line 388
    invoke-direct {p2, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentsGamesPaging$3;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 389
    .line 390
    .line 391
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->d0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 392
    .line 393
    .line 394
    move-result-object p1

    .line 395
    new-instance p2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentsGamesPaging$4;

    .line 396
    .line 397
    invoke-direct {p2, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentsGamesPaging$4;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 398
    .line 399
    .line 400
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 401
    .line 402
    .line 403
    move-result-object p1

    .line 404
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 405
    .line 406
    .line 407
    move-result-object p2

    .line 408
    invoke-static {p1, p2}, Landroidx/paging/CachedPagingDataKt;->a(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/flow/e;

    .line 409
    .line 410
    .line 411
    move-result-object p1

    .line 412
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H5:Lkotlinx/coroutines/flow/e;

    .line 413
    .line 414
    sget-object p1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 415
    .line 416
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 417
    .line 418
    .line 419
    move-result-object p1

    .line 420
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I5:Lkotlinx/coroutines/flow/V;

    .line 421
    .line 422
    return-void
.end method

.method public static final synthetic A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/uikit/components/lottie_empty/n;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->b4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->S3:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I3:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic D3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lkotlinx/coroutines/flow/f0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->x5:Lkotlinx/coroutines/flow/f0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Ljava/util/Map;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->A5:Ljava/util/Map;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic F3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lgk/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->x1:Lgk/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic G3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lw81/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v1:Lw81/c;

    .line 2
    .line 3
    return-object p0
.end method

.method private final G4(Lh81/b;)Z
    .locals 1

    .line 1
    instance-of v0, p1, Lh81/b$c;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    instance-of p1, p1, Lh81/b$g;

    .line 6
    .line 7
    if-nez p1, :cond_0

    .line 8
    .line 9
    const/4 p1, 0x1

    .line 10
    return p1

    .line 11
    :cond_0
    const/4 p1, 0x0

    .line 12
    return p1
.end method

.method public static final synthetic H3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lw81/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->P1:Lw81/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic I3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LSX0/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->b2:LSX0/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic J3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/analytics/domain/scope/g0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->P2:Lorg/xbet/analytics/domain/scope/g0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic K3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->V1:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic L3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->X4:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic M3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lek0/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->y5:Lek0/o;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->x2:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic O3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LwX0/C;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->F2:LwX0/C;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic P3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic Q3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lw81/g;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H1:Lw81/g;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic R3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v2:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public static final synthetic S3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->y2:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic T3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLjava/lang/Long;)Lkotlinx/coroutines/flow/e;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->i4(JLjava/lang/Long;)Lkotlinx/coroutines/flow/e;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic U3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lkotlinx/coroutines/flow/e;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->n4()Lkotlinx/coroutines/flow/e;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic V3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLh81/b;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->o4(JLh81/b;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic W3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->s4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic X3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->w4(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic Y3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I3:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic Z3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lh81/b;)Z
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->G4(Lh81/b;)Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method private final b4()Lorg/xbet/uikit/components/lottie_empty/n;
    .locals 12

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->b2:LSX0/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 4
    .line 5
    sget v6, Lpb/k;->data_retrieval_error:I

    .line 6
    .line 7
    const/16 v10, 0x1de

    .line 8
    .line 9
    const/4 v11, 0x0

    .line 10
    const/4 v2, 0x0

    .line 11
    const/4 v3, 0x0

    .line 12
    const/4 v4, 0x0

    .line 13
    const/4 v5, 0x0

    .line 14
    const/4 v7, 0x0

    .line 15
    const/4 v8, 0x0

    .line 16
    const/4 v9, 0x0

    .line 17
    invoke-static/range {v0 .. v11}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    return-object v0
.end method

.method public static final j4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Landroidx/paging/PagingSource;
    .locals 2

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->X2:Leu/i;

    .line 4
    .line 5
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I1:Lw81/d;

    .line 6
    .line 7
    invoke-direct {v0, v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource;-><init>(Leu/i;Lw81/d;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method private final o4(JLh81/b;Ljava/lang/String;)V
    .locals 7

    .line 1
    instance-of v3, p3, Lh81/b$a;

    .line 2
    .line 3
    instance-of v0, p3, Lh81/b$b;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    check-cast p3, Lh81/b$b;

    .line 9
    .line 10
    goto :goto_0

    .line 11
    :cond_0
    move-object p3, v1

    .line 12
    :goto_0
    if-eqz p3, :cond_1

    .line 13
    .line 14
    invoke-virtual {p3}, Lh81/b$b;->a()I

    .line 15
    .line 16
    .line 17
    move-result p3

    .line 18
    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    :cond_1
    move-object v4, v1

    .line 23
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I2:Lorg/xbet/analytics/domain/scope/g;

    .line 24
    .line 25
    const-string v5, "inside_tournament"

    .line 26
    .line 27
    move-wide v1, p1

    .line 28
    invoke-virtual/range {v0 .. v5}, Lorg/xbet/analytics/domain/scope/g;->c(JZLjava/lang/Integer;Ljava/lang/String;)V

    .line 29
    .line 30
    .line 31
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->V2:LnR/d;

    .line 32
    .line 33
    const-string v5, "inside_tournament"

    .line 34
    .line 35
    move-object v6, v4

    .line 36
    move v4, v3

    .line 37
    move-wide v2, v1

    .line 38
    move-object v1, p4

    .line 39
    invoke-interface/range {v0 .. v6}, LnR/d;->i(Ljava/lang/String;JZLjava/lang/String;Ljava/lang/Integer;)V

    .line 40
    .line 41
    .line 42
    return-void
.end method

.method public static synthetic p3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->t4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final p4()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->F3:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    const/4 v2, 0x0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->F1:Lorg/xbet/ui_common/utils/internet/a;

    .line 11
    .line 12
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$observeConnection$$inlined$filter$1;

    .line 17
    .line 18
    invoke-direct {v3, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$observeConnection$$inlined$filter$1;-><init>(Lkotlinx/coroutines/flow/e;)V

    .line 19
    .line 20
    .line 21
    invoke-static {v3, v1}, Lkotlinx/coroutines/flow/g;->w0(Lkotlinx/coroutines/flow/e;I)Lkotlinx/coroutines/flow/e;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$observeConnection$2;

    .line 26
    .line 27
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$observeConnection$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$observeConnection$3;

    .line 35
    .line 36
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$observeConnection$3;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 37
    .line 38
    .line 39
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->y1:Lm8/a;

    .line 48
    .line 49
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->F3:Lkotlinx/coroutines/x0;

    .line 62
    .line 63
    return-void
.end method

.method public static synthetic q3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Landroidx/paging/PagingSource;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->j4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Landroidx/paging/PagingSource;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->x4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final s4(Ljava/lang/Throwable;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->S1:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/x;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/x;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static final synthetic t3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LnR/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->S2:LnR/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final t4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    instance-of v1, p1, Ljava/net/SocketTimeoutException;

    .line 4
    .line 5
    if-nez v1, :cond_1

    .line 6
    .line 7
    instance-of v1, p1, Ljava/net/ConnectException;

    .line 8
    .line 9
    if-nez v1, :cond_1

    .line 10
    .line 11
    instance-of p1, p1, Ljava/net/UnknownHostException;

    .line 12
    .line 13
    if-eqz p1, :cond_0

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p0, Lkb1/E$a;

    .line 17
    .line 18
    invoke-direct {p0, p2}, Lkb1/E$a;-><init>(Ljava/lang/String;)V

    .line 19
    .line 20
    .line 21
    goto :goto_1

    .line 22
    :cond_1
    :goto_0
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->p4()V

    .line 23
    .line 24
    .line 25
    sget-object p0, Lkb1/E$b;->a:Lkb1/E$b;

    .line 26
    .line 27
    :goto_1
    invoke-interface {v0, p0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 31
    .line 32
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LnR/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->V2:LnR/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/analytics/domain/scope/g;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I2:Lorg/xbet/analytics/domain/scope/g;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final v4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->s4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->F3:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method private final w4(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V
    .locals 11

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v8

    .line 5
    new-instance v9, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/w;

    .line 6
    .line 7
    invoke-direct {v9, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/w;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->y1:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v10

    .line 16
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    move-object v1, p0

    .line 20
    move-wide v2, p1

    .line 21
    move-object v4, p3

    .line 22
    move-object v6, p4

    .line 23
    move-object/from16 v5, p5

    .line 24
    .line 25
    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 26
    .line 27
    .line 28
    const/16 v6, 0xa

    .line 29
    .line 30
    const/4 v2, 0x0

    .line 31
    const/4 v4, 0x0

    .line 32
    move-object v5, v0

    .line 33
    move-object v0, v8

    .line 34
    move-object v1, v9

    .line 35
    move-object v3, v10

    .line 36
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method public static final synthetic x3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->w5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final x4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->s4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final A4()V
    .locals 15

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H2:LP91/b;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 4
    .line 5
    new-instance v6, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsProvidersScreen;

    .line 6
    .line 7
    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v2:J

    .line 8
    .line 9
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->y2:Ljava/lang/String;

    .line 10
    .line 11
    invoke-direct {v6, v2, v3, v4}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsProvidersScreen;-><init>(JLjava/lang/String;)V

    .line 12
    .line 13
    .line 14
    const/16 v13, 0xf7

    .line 15
    .line 16
    const/4 v14, 0x0

    .line 17
    const/4 v2, 0x0

    .line 18
    const/4 v3, 0x0

    .line 19
    const-wide/16 v4, 0x0

    .line 20
    .line 21
    const/4 v7, 0x0

    .line 22
    const-wide/16 v8, 0x0

    .line 23
    .line 24
    const-wide/16 v10, 0x0

    .line 25
    .line 26
    const/4 v12, 0x0

    .line 27
    invoke-direct/range {v1 .. v14}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {v0, v1}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final B4()V
    .locals 15

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H2:LP91/b;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 4
    .line 5
    new-instance v6, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentStagesScreen;

    .line 6
    .line 7
    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v2:J

    .line 8
    .line 9
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->y2:Ljava/lang/String;

    .line 10
    .line 11
    const-string v5, ""

    .line 12
    .line 13
    invoke-direct {v6, v2, v3, v4, v5}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentStagesScreen;-><init>(JLjava/lang/String;Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    const/16 v13, 0xf7

    .line 17
    .line 18
    const/4 v14, 0x0

    .line 19
    const/4 v2, 0x0

    .line 20
    const/4 v3, 0x0

    .line 21
    const-wide/16 v4, 0x0

    .line 22
    .line 23
    const/4 v7, 0x0

    .line 24
    const-wide/16 v8, 0x0

    .line 25
    .line 26
    const-wide/16 v10, 0x0

    .line 27
    .line 28
    const/4 v12, 0x0

    .line 29
    invoke-direct/range {v1 .. v14}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 30
    .line 31
    .line 32
    invoke-virtual {v0, v1}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public final C4(Ljava/lang/String;)V
    .locals 10
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H3:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->y1:Lm8/a;

    .line 18
    .line 19
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 20
    .line 21
    .line 22
    move-result-object v5

    .line 23
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$openSingleGame$1;

    .line 24
    .line 25
    invoke-direct {v3, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$openSingleGame$1;-><init>(Ljava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    new-instance v7, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$openSingleGame$2;

    .line 29
    .line 30
    const/4 v0, 0x0

    .line 31
    invoke-direct {v7, p0, p1, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$openSingleGame$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/16 v8, 0xa

    .line 35
    .line 36
    const/4 v9, 0x0

    .line 37
    const/4 v4, 0x0

    .line 38
    const/4 v6, 0x0

    .line 39
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H3:Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    return-void
.end method

.method public final D4()V
    .locals 6

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$refresh$1;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-direct {v3, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$refresh$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 9
    .line 10
    .line 11
    const/4 v4, 0x3

    .line 12
    const/4 v5, 0x0

    .line 13
    const/4 v2, 0x0

    .line 14
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public final E4(Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Z)V
    .locals 6
    .param p1    # Lorg/xplatform/aggregator/api/navigation/TournamentsPage;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$d;->a:[I

    .line 7
    .line 8
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 9
    .line 10
    .line 11
    move-result v1

    .line 12
    aget v0, v0, v1

    .line 13
    .line 14
    const/4 v1, 0x1

    .line 15
    if-eq v0, v1, :cond_3

    .line 16
    .line 17
    const/4 v1, 0x2

    .line 18
    if-eq v0, v1, :cond_2

    .line 19
    .line 20
    const/4 v1, 0x3

    .line 21
    if-eq v0, v1, :cond_1

    .line 22
    .line 23
    const/4 v1, 0x4

    .line 24
    if-ne v0, v1, :cond_0

    .line 25
    .line 26
    sget-object v0, Lorg/xbet/fatmananalytics/api/logger/aggregator/models/TournamentTabMenu;->RULES:Lorg/xbet/fatmananalytics/api/logger/aggregator/models/TournamentTabMenu;

    .line 27
    .line 28
    goto :goto_0

    .line 29
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 30
    .line 31
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 32
    .line 33
    .line 34
    throw p1

    .line 35
    :cond_1
    sget-object v0, Lorg/xbet/fatmananalytics/api/logger/aggregator/models/TournamentTabMenu;->GAMES:Lorg/xbet/fatmananalytics/api/logger/aggregator/models/TournamentTabMenu;

    .line 36
    .line 37
    goto :goto_0

    .line 38
    :cond_2
    sget-object v0, Lorg/xbet/fatmananalytics/api/logger/aggregator/models/TournamentTabMenu;->RESULT:Lorg/xbet/fatmananalytics/api/logger/aggregator/models/TournamentTabMenu;

    .line 39
    .line 40
    goto :goto_0

    .line 41
    :cond_3
    sget-object v0, Lorg/xbet/fatmananalytics/api/logger/aggregator/models/TournamentTabMenu;->DESCRIPTION:Lorg/xbet/fatmananalytics/api/logger/aggregator/models/TournamentTabMenu;

    .line 42
    .line 43
    :goto_0
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I5:Lkotlinx/coroutines/flow/V;

    .line 44
    .line 45
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    check-cast v1, Ljava/lang/Boolean;

    .line 50
    .line 51
    invoke-virtual {v1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 52
    .line 53
    .line 54
    move-result v1

    .line 55
    const-class v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 56
    .line 57
    if-eqz v1, :cond_4

    .line 58
    .line 59
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I2:Lorg/xbet/analytics/domain/scope/g;

    .line 60
    .line 61
    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v2:J

    .line 62
    .line 63
    invoke-virtual {v1, v3, v4, p1}, Lorg/xbet/analytics/domain/scope/g;->l(JLorg/xplatform/aggregator/api/navigation/TournamentsPage;)V

    .line 64
    .line 65
    .line 66
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->V2:LnR/d;

    .line 67
    .line 68
    invoke-virtual {v2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 69
    .line 70
    .line 71
    move-result-object v3

    .line 72
    iget-wide v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v2:J

    .line 73
    .line 74
    invoke-interface {v1, v3, v4, v5, v0}, LnR/d;->b(Ljava/lang/String;JLorg/xbet/fatmananalytics/api/logger/aggregator/models/TournamentTabMenu;)V

    .line 75
    .line 76
    .line 77
    :cond_4
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I5:Lkotlinx/coroutines/flow/V;

    .line 78
    .line 79
    sget-object v1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 80
    .line 81
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 82
    .line 83
    .line 84
    sget-object v0, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->GAMES:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 85
    .line 86
    if-ne p1, v0, :cond_5

    .line 87
    .line 88
    if-eqz p2, :cond_5

    .line 89
    .line 90
    invoke-virtual {v2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->C4(Ljava/lang/String;)V

    .line 95
    .line 96
    .line 97
    :cond_5
    return-void
.end method

.method public final F4(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final H4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lkb1/E$b;->a:Lkb1/E$b;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    new-instance v0, Ljava/net/ConnectException;

    .line 9
    .line 10
    invoke-direct {v0}, Ljava/net/ConnectException;-><init>()V

    .line 11
    .line 12
    .line 13
    invoke-direct {p0, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->s4(Ljava/lang/Throwable;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final a4()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->V1:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->q()Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final c4()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->z5:I

    .line 2
    .line 3
    return v0
.end method

.method public final d4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lkb1/F<",
            "Lkb1/f;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->E5:Lkotlinx/coroutines/flow/f0;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lkb1/F<",
            "Lkb1/n;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->F5:Lkotlinx/coroutines/flow/f0;

    .line 2
    .line 3
    return-object v0
.end method

.method public final f4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lkb1/F<",
            "Lkb1/u;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->C5:Lkotlinx/coroutines/flow/f0;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lkb1/F<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->D5:Lkotlinx/coroutines/flow/f0;

    .line 2
    .line 3
    return-object v0
.end method

.method public final getEvents()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->S3:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Landroidx/paging/PagingData<",
            "LN21/d;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H5:Lkotlinx/coroutines/flow/e;

    .line 2
    .line 3
    return-object v0
.end method

.method public final i4(JLjava/lang/Long;)Lkotlinx/coroutines/flow/e;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/lang/Long;",
            ")",
            "Lkotlinx/coroutines/flow/e<",
            "Landroidx/paging/PagingData<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .line 1
    new-instance v0, Landroidx/paging/C;

    .line 2
    .line 3
    const/16 v7, 0x30

    .line 4
    .line 5
    const/4 v8, 0x0

    .line 6
    const/16 v1, 0x10

    .line 7
    .line 8
    const/4 v2, 0x5

    .line 9
    const/4 v3, 0x0

    .line 10
    const/16 v4, 0x20

    .line 11
    .line 12
    const/4 v5, 0x0

    .line 13
    const/4 v6, 0x0

    .line 14
    invoke-direct/range {v0 .. v8}, Landroidx/paging/C;-><init>(IIZIIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 15
    .line 16
    .line 17
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;

    .line 18
    .line 19
    const/4 v2, 0x0

    .line 20
    invoke-direct {v1, p1, p2, p3, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;-><init>(JLjava/lang/Long;I)V

    .line 21
    .line 22
    .line 23
    new-instance p1, Landroidx/paging/Pager;

    .line 24
    .line 25
    new-instance p2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/v;

    .line 26
    .line 27
    invoke-direct {p2, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/v;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)V

    .line 28
    .line 29
    .line 30
    invoke-direct {p1, v0, v1, p2}, Landroidx/paging/Pager;-><init>(Landroidx/paging/C;Ljava/lang/Object;Lkotlin/jvm/functions/Function0;)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p1}, Landroidx/paging/Pager;->a()Lkotlinx/coroutines/flow/e;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    return-object p1
.end method

.method public final k4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lkb1/F<",
            "Lkb1/m;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->G5:Lkotlinx/coroutines/flow/f0;

    .line 2
    .line 3
    return-object v0
.end method

.method public final l4()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->B5:Z

    .line 2
    .line 3
    return v0
.end method

.method public final m4(Ljava/lang/Throwable;)V
    .locals 0
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->s4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final n4()Lkotlinx/coroutines/flow/e;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v1:Lw81/c;

    .line 2
    .line 3
    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v2:J

    .line 4
    .line 5
    const/4 v3, 0x0

    .line 6
    invoke-interface {v0, v1, v2, v3}, Lw81/c;->a(JZ)Lkotlinx/coroutines/flow/e;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$loadFullInfoState$$inlined$map$1;

    .line 11
    .line 12
    invoke-direct {v1, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$loadFullInfoState$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;)V

    .line 13
    .line 14
    .line 15
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$loadFullInfoState$2;

    .line 16
    .line 17
    const/4 v2, 0x0

    .line 18
    invoke-direct {v0, p0, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$loadFullInfoState$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    .line 19
    .line 20
    .line 21
    invoke-static {v1, v0}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    return-object v0
.end method

.method public onCleared()V
    .locals 3

    .line 1
    sget-object v0, LVa1/u;->a:LVa1/u;

    .line 2
    .line 3
    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v2:J

    .line 4
    .line 5
    invoke-virtual {v0, v1, v2}, LVa1/u;->a(J)V

    .line 6
    .line 7
    .line 8
    invoke-super {p0}, Lorg/xbet/ui_common/viewmodel/core/b;->onCleared()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final q4(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Ljava/lang/String;)V
    .locals 6
    .param p1    # Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onButtonClick$1;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-direct {v3, p1, p0, p2, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onButtonClick$1;-><init>(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 9
    .line 10
    .line 11
    const/4 v4, 0x3

    .line 12
    const/4 v5, 0x0

    .line 13
    const/4 v2, 0x0

    .line 14
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public final r4(Ljava/lang/String;JLjava/util/List;)V
    .locals 9
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "J",
            "Ljava/util/List<",
            "+",
            "Lkb1/l;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;

    .line 11
    .line 12
    const/4 v8, 0x0

    .line 13
    move-object v4, p0

    .line 14
    move-object v7, p1

    .line 15
    move-wide v5, p2

    .line 16
    move-object v3, p4

    .line 17
    invoke-direct/range {v2 .. v8}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;-><init>(Ljava/util/List;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLjava/lang/String;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    const/16 v6, 0xe

    .line 21
    .line 22
    const/4 v7, 0x0

    .line 23
    move-object v5, v2

    .line 24
    const/4 v2, 0x0

    .line 25
    const/4 v3, 0x0

    .line 26
    const/4 v4, 0x0

    .line 27
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public final u4(Ljava/lang/String;J)V
    .locals 14
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->A5:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static/range {p2 .. p3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 4
    .line 5
    .line 6
    move-result-object v2

    .line 7
    invoke-interface {v0, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    move-object v5, v0

    .line 12
    check-cast v5, Lorg/xplatform/aggregator/api/model/Game;

    .line 13
    .line 14
    if-eqz v5, :cond_0

    .line 15
    .line 16
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 17
    .line 18
    .line 19
    move-result-object v7

    .line 20
    move-object v8, v7

    .line 21
    new-instance v7, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/y;

    .line 22
    .line 23
    invoke-direct {v7, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/y;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)V

    .line 24
    .line 25
    .line 26
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onGameClick$1$2;

    .line 27
    .line 28
    const/4 v6, 0x0

    .line 29
    move-object v1, p0

    .line 30
    move-object v4, p1

    .line 31
    move-wide/from16 v2, p2

    .line 32
    .line 33
    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onGameClick$1$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLjava/lang/String;Lorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)V

    .line 34
    .line 35
    .line 36
    const/16 v12, 0xe

    .line 37
    .line 38
    const/4 v13, 0x0

    .line 39
    move-object v6, v8

    .line 40
    const/4 v8, 0x0

    .line 41
    const/4 v9, 0x0

    .line 42
    const/4 v10, 0x0

    .line 43
    move-object v11, v0

    .line 44
    invoke-static/range {v6 .. v13}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 45
    .line 46
    .line 47
    :cond_0
    return-void
.end method

.method public final y4(J)V
    .locals 20

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->x5:Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    invoke-interface {v1}, Lkotlinx/coroutines/flow/Z;->a()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    invoke-interface {v1, v2}, Ljava/util/List;->listIterator(I)Ljava/util/ListIterator;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    :cond_0
    invoke-interface {v1}, Ljava/util/ListIterator;->hasPrevious()Z

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    const/4 v3, 0x0

    .line 22
    if-eqz v2, :cond_1

    .line 23
    .line 24
    invoke-interface {v1}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v2

    .line 28
    move-object v4, v2

    .line 29
    check-cast v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;

    .line 30
    .line 31
    instance-of v4, v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$a;

    .line 32
    .line 33
    if-eqz v4, :cond_0

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_1
    move-object v2, v3

    .line 37
    :goto_0
    instance-of v1, v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$a;

    .line 38
    .line 39
    if-eqz v1, :cond_2

    .line 40
    .line 41
    move-object v3, v2

    .line 42
    check-cast v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$a;

    .line 43
    .line 44
    :cond_2
    if-eqz v3, :cond_4

    .line 45
    .line 46
    invoke-virtual {v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$a;->a()Li81/a;

    .line 47
    .line 48
    .line 49
    move-result-object v1

    .line 50
    if-eqz v1, :cond_4

    .line 51
    .line 52
    invoke-virtual {v1}, Li81/a;->e()Lk81/a;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    if-eqz v1, :cond_4

    .line 57
    .line 58
    invoke-virtual {v1}, Lk81/a;->e()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v1

    .line 62
    if-nez v1, :cond_3

    .line 63
    .line 64
    goto :goto_2

    .line 65
    :cond_3
    :goto_1
    move-object v5, v1

    .line 66
    goto :goto_3

    .line 67
    :cond_4
    :goto_2
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->y2:Ljava/lang/String;

    .line 68
    .line 69
    goto :goto_1

    .line 70
    :goto_3
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H2:LP91/b;

    .line 71
    .line 72
    new-instance v8, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 73
    .line 74
    new-instance v2, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentPrizesScreen;

    .line 75
    .line 76
    iget-wide v3, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v2:J

    .line 77
    .line 78
    move-wide/from16 v6, p1

    .line 79
    .line 80
    invoke-direct/range {v2 .. v7}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentPrizesScreen;-><init>(JLjava/lang/String;J)V

    .line 81
    .line 82
    .line 83
    const/16 v18, 0xf7

    .line 84
    .line 85
    const/16 v19, 0x0

    .line 86
    .line 87
    const/4 v7, 0x0

    .line 88
    move-object v6, v8

    .line 89
    const/4 v8, 0x0

    .line 90
    const-wide/16 v9, 0x0

    .line 91
    .line 92
    const/4 v12, 0x0

    .line 93
    const-wide/16 v13, 0x0

    .line 94
    .line 95
    const-wide/16 v15, 0x0

    .line 96
    .line 97
    const/16 v17, 0x0

    .line 98
    .line 99
    move-object v11, v2

    .line 100
    invoke-direct/range {v6 .. v19}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 101
    .line 102
    .line 103
    invoke-virtual {v1, v6}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 104
    .line 105
    .line 106
    return-void
.end method

.method public final z4(Ljava/lang/String;)V
    .locals 20
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I2:Lorg/xbet/analytics/domain/scope/g;

    .line 4
    .line 5
    iget-wide v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v2:J

    .line 6
    .line 7
    invoke-virtual {v1, v2, v3}, Lorg/xbet/analytics/domain/scope/g;->a(J)V

    .line 8
    .line 9
    .line 10
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->V2:LnR/d;

    .line 11
    .line 12
    iget-wide v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v2:J

    .line 13
    .line 14
    move-object/from16 v4, p1

    .line 15
    .line 16
    invoke-interface {v1, v4, v2, v3}, LnR/d;->j(Ljava/lang/String;J)V

    .line 17
    .line 18
    .line 19
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->x5:Lkotlinx/coroutines/flow/f0;

    .line 20
    .line 21
    invoke-interface {v1}, Lkotlinx/coroutines/flow/Z;->a()Ljava/util/List;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 26
    .line 27
    .line 28
    move-result v2

    .line 29
    invoke-interface {v1, v2}, Ljava/util/List;->listIterator(I)Ljava/util/ListIterator;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    :cond_0
    invoke-interface {v1}, Ljava/util/ListIterator;->hasPrevious()Z

    .line 34
    .line 35
    .line 36
    move-result v2

    .line 37
    const/4 v3, 0x0

    .line 38
    if-eqz v2, :cond_1

    .line 39
    .line 40
    invoke-interface {v1}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    .line 41
    .line 42
    .line 43
    move-result-object v2

    .line 44
    move-object v4, v2

    .line 45
    check-cast v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;

    .line 46
    .line 47
    instance-of v4, v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$a;

    .line 48
    .line 49
    if-eqz v4, :cond_0

    .line 50
    .line 51
    goto :goto_0

    .line 52
    :cond_1
    move-object v2, v3

    .line 53
    :goto_0
    instance-of v1, v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$a;

    .line 54
    .line 55
    if-eqz v1, :cond_2

    .line 56
    .line 57
    move-object v3, v2

    .line 58
    check-cast v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$a;

    .line 59
    .line 60
    :cond_2
    if-eqz v3, :cond_4

    .line 61
    .line 62
    invoke-virtual {v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$a;->a()Li81/a;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    if-eqz v1, :cond_4

    .line 67
    .line 68
    invoke-virtual {v1}, Li81/a;->e()Lk81/a;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    if-eqz v1, :cond_4

    .line 73
    .line 74
    invoke-virtual {v1}, Lk81/a;->e()Ljava/lang/String;

    .line 75
    .line 76
    .line 77
    move-result-object v1

    .line 78
    if-nez v1, :cond_3

    .line 79
    .line 80
    goto :goto_2

    .line 81
    :cond_3
    :goto_1
    move-object v5, v1

    .line 82
    goto :goto_3

    .line 83
    :cond_4
    :goto_2
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->y2:Ljava/lang/String;

    .line 84
    .line 85
    goto :goto_1

    .line 86
    :goto_3
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H2:LP91/b;

    .line 87
    .line 88
    new-instance v8, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 89
    .line 90
    new-instance v2, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentPrizesScreen;

    .line 91
    .line 92
    iget-wide v3, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v2:J

    .line 93
    .line 94
    const-wide/16 v6, 0x0

    .line 95
    .line 96
    invoke-direct/range {v2 .. v7}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentPrizesScreen;-><init>(JLjava/lang/String;J)V

    .line 97
    .line 98
    .line 99
    const/16 v18, 0xf7

    .line 100
    .line 101
    const/16 v19, 0x0

    .line 102
    .line 103
    const/4 v7, 0x0

    .line 104
    move-object v6, v8

    .line 105
    const/4 v8, 0x0

    .line 106
    const-wide/16 v9, 0x0

    .line 107
    .line 108
    const/4 v12, 0x0

    .line 109
    const-wide/16 v13, 0x0

    .line 110
    .line 111
    const-wide/16 v15, 0x0

    .line 112
    .line 113
    const/16 v17, 0x0

    .line 114
    .line 115
    move-object v11, v2

    .line 116
    invoke-direct/range {v6 .. v19}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 117
    .line 118
    .line 119
    invoke-virtual {v1, v6}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 120
    .line 121
    .line 122
    return-void
.end method
