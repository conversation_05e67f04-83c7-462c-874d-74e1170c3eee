.class public final Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lfy/b;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0006\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J(\u0010\r\u001a\u00020\u000c2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\nH\u0096B\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u000f\u001a\u00020\u00082\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\u0011\u00a8\u0006\u0012"
    }
    d2 = {
        "Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl;",
        "Lfy/b;",
        "Lorg/xbet/customer_io/impl/domain/b;",
        "customerIORepository",
        "<init>",
        "(Lorg/xbet/customer_io/impl/domain/b;)V",
        "Lgy/b;",
        "updateDataModel",
        "",
        "newUser",
        "",
        "token",
        "",
        "a",
        "(Lgy/b;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "b",
        "(Z)Z",
        "Lorg/xbet/customer_io/impl/domain/b;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/customer_io/impl/domain/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/customer_io/impl/domain/b;)V
    .locals 0
    .param p1    # Lorg/xbet/customer_io/impl/domain/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl;->a:Lorg/xbet/customer_io/impl/domain/b;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a(Lgy/b;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 6
    .param p1    # Lgy/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgy/b;",
            "Z",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p4, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p4

    .line 6
    check-cast v0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p4}, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;-><init>(Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p4, v0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x1

    .line 35
    if-eqz v2, :cond_3

    .line 36
    .line 37
    if-eq v2, v4, :cond_2

    .line 38
    .line 39
    if-ne v2, v3, :cond_1

    .line 40
    .line 41
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    goto :goto_3

    .line 45
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 46
    .line 47
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 48
    .line 49
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 50
    .line 51
    .line 52
    throw p1

    .line 53
    :cond_2
    iget-object p1, v0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;->L$1:Ljava/lang/Object;

    .line 54
    .line 55
    move-object p3, p1

    .line 56
    check-cast p3, Ljava/lang/String;

    .line 57
    .line 58
    iget-object p1, v0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;->L$0:Ljava/lang/Object;

    .line 59
    .line 60
    check-cast p1, Lgy/b;

    .line 61
    .line 62
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 63
    .line 64
    .line 65
    goto :goto_1

    .line 66
    :cond_3
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 67
    .line 68
    .line 69
    invoke-virtual {p0, p2}, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl;->b(Z)Z

    .line 70
    .line 71
    .line 72
    move-result p2

    .line 73
    if-nez p2, :cond_6

    .line 74
    .line 75
    iget-object p2, p0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl;->a:Lorg/xbet/customer_io/impl/domain/b;

    .line 76
    .line 77
    iput-object p1, v0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;->L$0:Ljava/lang/Object;

    .line 78
    .line 79
    iput-object p3, v0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;->L$1:Ljava/lang/Object;

    .line 80
    .line 81
    iput v4, v0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;->label:I

    .line 82
    .line 83
    invoke-interface {p2, p1, v0}, Lorg/xbet/customer_io/impl/domain/b;->e(Lgy/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 84
    .line 85
    .line 86
    move-result-object p2

    .line 87
    if-ne p2, v1, :cond_4

    .line 88
    .line 89
    goto :goto_2

    .line 90
    :cond_4
    :goto_1
    iget-object p2, p0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl;->a:Lorg/xbet/customer_io/impl/domain/b;

    .line 91
    .line 92
    invoke-virtual {p1}, Lgy/b;->b()J

    .line 93
    .line 94
    .line 95
    move-result-wide v4

    .line 96
    const/4 p1, 0x0

    .line 97
    iput-object p1, v0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;->L$0:Ljava/lang/Object;

    .line 98
    .line 99
    iput-object p1, v0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;->L$1:Ljava/lang/Object;

    .line 100
    .line 101
    iput v3, v0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl$invoke$1;->label:I

    .line 102
    .line 103
    invoke-interface {p2, p3, v4, v5, v0}, Lorg/xbet/customer_io/impl/domain/b;->b(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 104
    .line 105
    .line 106
    move-result-object p1

    .line 107
    if-ne p1, v1, :cond_5

    .line 108
    .line 109
    :goto_2
    return-object v1

    .line 110
    :cond_5
    :goto_3
    iget-object p1, p0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl;->a:Lorg/xbet/customer_io/impl/domain/b;

    .line 111
    .line 112
    invoke-interface {p1}, Lorg/xbet/customer_io/impl/domain/b;->c()V

    .line 113
    .line 114
    .line 115
    :cond_6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 116
    .line 117
    return-object p1
.end method

.method public final b(Z)Z
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    const/4 p1, 0x0

    .line 4
    return p1

    .line 5
    :cond_0
    iget-object p1, p0, Lorg/xbet/customer_io/impl/domain/PlugCustomerIOUseCaseImpl;->a:Lorg/xbet/customer_io/impl/domain/b;

    .line 6
    .line 7
    invoke-interface {p1}, Lorg/xbet/customer_io/impl/domain/b;->a()Z

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    return p1
.end method
