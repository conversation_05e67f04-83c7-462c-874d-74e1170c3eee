.class public final Leb1/e$a;
.super Landroidx/recyclerview/widget/i$f;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Leb1/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroidx/recyclerview/widget/i$f<",
        "LVX0/i;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0004\u0008\u00c2\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u001f\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\n\u001a\u00020\u00072\u0006\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\n\u0010\t\u00a8\u0006\u000b"
    }
    d2 = {
        "Leb1/e$a;",
        "Landroidx/recyclerview/widget/i$f;",
        "LVX0/i;",
        "<init>",
        "()V",
        "oldItem",
        "newItem",
        "",
        "e",
        "(LVX0/i;LVX0/i;)Z",
        "d",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:Leb1/e$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Leb1/e$a;

    .line 2
    .line 3
    invoke-direct {v0}, Leb1/e$a;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Leb1/e$a;->a:Leb1/e$a;

    .line 7
    .line 8
    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Landroidx/recyclerview/widget/i$f;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, LVX0/i;

    .line 2
    .line 3
    check-cast p2, LVX0/i;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, Leb1/e$a;->d(LVX0/i;LVX0/i;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public bridge synthetic b(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, LVX0/i;

    .line 2
    .line 3
    check-cast p2, LVX0/i;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, Leb1/e$a;->e(LVX0/i;LVX0/i;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public d(LVX0/i;LVX0/i;)Z
    .locals 1
    .param p1    # LVX0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LVX0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, Llb1/b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    instance-of v0, p2, Llb1/b;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    return p1

    .line 14
    :cond_0
    instance-of v0, p1, Llb1/m;

    .line 15
    .line 16
    if-eqz v0, :cond_1

    .line 17
    .line 18
    instance-of v0, p2, Llb1/m;

    .line 19
    .line 20
    if-eqz v0, :cond_1

    .line 21
    .line 22
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    return p1

    .line 27
    :cond_1
    instance-of v0, p1, Llb1/p;

    .line 28
    .line 29
    if-eqz v0, :cond_2

    .line 30
    .line 31
    instance-of v0, p2, Llb1/p;

    .line 32
    .line 33
    if-eqz v0, :cond_2

    .line 34
    .line 35
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 36
    .line 37
    .line 38
    move-result p1

    .line 39
    return p1

    .line 40
    :cond_2
    instance-of v0, p1, Llb1/g;

    .line 41
    .line 42
    if-eqz v0, :cond_3

    .line 43
    .line 44
    instance-of v0, p2, Llb1/g;

    .line 45
    .line 46
    if-eqz v0, :cond_3

    .line 47
    .line 48
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 49
    .line 50
    .line 51
    move-result p1

    .line 52
    return p1

    .line 53
    :cond_3
    instance-of v0, p1, Llb1/i;

    .line 54
    .line 55
    if-eqz v0, :cond_4

    .line 56
    .line 57
    instance-of v0, p2, Llb1/i;

    .line 58
    .line 59
    if-eqz v0, :cond_4

    .line 60
    .line 61
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 62
    .line 63
    .line 64
    move-result p1

    .line 65
    return p1

    .line 66
    :cond_4
    instance-of v0, p1, Llb1/k;

    .line 67
    .line 68
    if-eqz v0, :cond_5

    .line 69
    .line 70
    instance-of v0, p2, Llb1/k;

    .line 71
    .line 72
    if-eqz v0, :cond_5

    .line 73
    .line 74
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 75
    .line 76
    .line 77
    move-result p1

    .line 78
    return p1

    .line 79
    :cond_5
    instance-of v0, p1, Llb1/r;

    .line 80
    .line 81
    if-eqz v0, :cond_6

    .line 82
    .line 83
    instance-of v0, p2, Llb1/r;

    .line 84
    .line 85
    if-eqz v0, :cond_6

    .line 86
    .line 87
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 88
    .line 89
    .line 90
    move-result p1

    .line 91
    return p1

    .line 92
    :cond_6
    instance-of v0, p1, Llb1/c;

    .line 93
    .line 94
    if-eqz v0, :cond_7

    .line 95
    .line 96
    instance-of v0, p2, Llb1/c;

    .line 97
    .line 98
    if-eqz v0, :cond_7

    .line 99
    .line 100
    const/4 p1, 0x1

    .line 101
    return p1

    .line 102
    :cond_7
    instance-of v0, p1, Llb1/e;

    .line 103
    .line 104
    if-eqz v0, :cond_8

    .line 105
    .line 106
    instance-of v0, p2, Llb1/e;

    .line 107
    .line 108
    if-eqz v0, :cond_8

    .line 109
    .line 110
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 111
    .line 112
    .line 113
    move-result p1

    .line 114
    return p1

    .line 115
    :cond_8
    const/4 p1, 0x0

    .line 116
    return p1
.end method

.method public e(LVX0/i;LVX0/i;)Z
    .locals 6
    .param p1    # LVX0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LVX0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, Llb1/b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    instance-of v0, p2, Llb1/b;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    return p1

    .line 14
    :cond_0
    instance-of v0, p1, Llb1/m;

    .line 15
    .line 16
    const/4 v1, 0x0

    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    move-object v2, p1

    .line 20
    check-cast v2, Llb1/m;

    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_1
    move-object v2, v1

    .line 24
    :goto_0
    if-eqz v2, :cond_2

    .line 25
    .line 26
    invoke-virtual {v2}, Llb1/m;->d()Le31/b;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    goto :goto_1

    .line 31
    :cond_2
    move-object v2, v1

    .line 32
    :goto_1
    instance-of v2, v2, Le31/a;

    .line 33
    .line 34
    const/4 v3, 0x1

    .line 35
    const/4 v4, 0x0

    .line 36
    if-eqz v2, :cond_a

    .line 37
    .line 38
    instance-of v2, p2, Llb1/m;

    .line 39
    .line 40
    if-eqz v2, :cond_3

    .line 41
    .line 42
    move-object v5, p2

    .line 43
    check-cast v5, Llb1/m;

    .line 44
    .line 45
    goto :goto_2

    .line 46
    :cond_3
    move-object v5, v1

    .line 47
    :goto_2
    if-eqz v5, :cond_4

    .line 48
    .line 49
    invoke-virtual {v5}, Llb1/m;->d()Le31/b;

    .line 50
    .line 51
    .line 52
    move-result-object v5

    .line 53
    goto :goto_3

    .line 54
    :cond_4
    move-object v5, v1

    .line 55
    :goto_3
    instance-of v5, v5, Le31/a;

    .line 56
    .line 57
    if-eqz v5, :cond_a

    .line 58
    .line 59
    if-eqz v0, :cond_5

    .line 60
    .line 61
    check-cast p1, Llb1/m;

    .line 62
    .line 63
    goto :goto_4

    .line 64
    :cond_5
    move-object p1, v1

    .line 65
    :goto_4
    if-eqz p1, :cond_6

    .line 66
    .line 67
    invoke-virtual {p1}, Llb1/m;->d()Le31/b;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    goto :goto_5

    .line 72
    :cond_6
    move-object p1, v1

    .line 73
    :goto_5
    check-cast p1, Le31/a;

    .line 74
    .line 75
    invoke-virtual {p1}, Le31/a;->a()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    if-eqz v2, :cond_7

    .line 80
    .line 81
    check-cast p2, Llb1/m;

    .line 82
    .line 83
    goto :goto_6

    .line 84
    :cond_7
    move-object p2, v1

    .line 85
    :goto_6
    if-eqz p2, :cond_8

    .line 86
    .line 87
    invoke-virtual {p2}, Llb1/m;->d()Le31/b;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    :cond_8
    check-cast v1, Le31/a;

    .line 92
    .line 93
    invoke-virtual {v1}, Le31/a;->a()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 94
    .line 95
    .line 96
    move-result-object p2

    .line 97
    if-ne p1, p2, :cond_9

    .line 98
    .line 99
    return v3

    .line 100
    :cond_9
    return v4

    .line 101
    :cond_a
    instance-of v0, p1, Llb1/p;

    .line 102
    .line 103
    if-eqz v0, :cond_b

    .line 104
    .line 105
    instance-of v0, p2, Llb1/p;

    .line 106
    .line 107
    if-eqz v0, :cond_b

    .line 108
    .line 109
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 110
    .line 111
    .line 112
    move-result p1

    .line 113
    return p1

    .line 114
    :cond_b
    instance-of v0, p1, Llb1/e;

    .line 115
    .line 116
    if-eqz v0, :cond_c

    .line 117
    .line 118
    instance-of v0, p2, Llb1/e;

    .line 119
    .line 120
    if-eqz v0, :cond_c

    .line 121
    .line 122
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 123
    .line 124
    .line 125
    move-result p1

    .line 126
    return p1

    .line 127
    :cond_c
    instance-of v0, p1, Llb1/g;

    .line 128
    .line 129
    if-eqz v0, :cond_d

    .line 130
    .line 131
    instance-of v0, p2, Llb1/g;

    .line 132
    .line 133
    if-eqz v0, :cond_d

    .line 134
    .line 135
    check-cast p1, Llb1/g;

    .line 136
    .line 137
    invoke-virtual {p1}, Llb1/g;->d()Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a;

    .line 138
    .line 139
    .line 140
    move-result-object p1

    .line 141
    check-cast p2, Llb1/g;

    .line 142
    .line 143
    invoke-virtual {p2}, Llb1/g;->d()Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a;

    .line 144
    .line 145
    .line 146
    move-result-object p2

    .line 147
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 148
    .line 149
    .line 150
    move-result p1

    .line 151
    return p1

    .line 152
    :cond_d
    instance-of v0, p1, Llb1/c;

    .line 153
    .line 154
    if-eqz v0, :cond_e

    .line 155
    .line 156
    move-object v2, p1

    .line 157
    check-cast v2, Llb1/c;

    .line 158
    .line 159
    goto :goto_7

    .line 160
    :cond_e
    move-object v2, v1

    .line 161
    :goto_7
    if-eqz v2, :cond_f

    .line 162
    .line 163
    invoke-virtual {v2}, Llb1/c;->d()LV21/d;

    .line 164
    .line 165
    .line 166
    move-result-object v2

    .line 167
    goto :goto_8

    .line 168
    :cond_f
    move-object v2, v1

    .line 169
    :goto_8
    instance-of v2, v2, LV21/b;

    .line 170
    .line 171
    if-eqz v2, :cond_17

    .line 172
    .line 173
    instance-of v2, p2, Llb1/c;

    .line 174
    .line 175
    if-eqz v2, :cond_10

    .line 176
    .line 177
    move-object v5, p2

    .line 178
    check-cast v5, Llb1/c;

    .line 179
    .line 180
    goto :goto_9

    .line 181
    :cond_10
    move-object v5, v1

    .line 182
    :goto_9
    if-eqz v5, :cond_11

    .line 183
    .line 184
    invoke-virtual {v5}, Llb1/c;->d()LV21/d;

    .line 185
    .line 186
    .line 187
    move-result-object v5

    .line 188
    goto :goto_a

    .line 189
    :cond_11
    move-object v5, v1

    .line 190
    :goto_a
    instance-of v5, v5, LV21/b;

    .line 191
    .line 192
    if-eqz v5, :cond_17

    .line 193
    .line 194
    if-eqz v0, :cond_12

    .line 195
    .line 196
    check-cast p1, Llb1/c;

    .line 197
    .line 198
    goto :goto_b

    .line 199
    :cond_12
    move-object p1, v1

    .line 200
    :goto_b
    if-eqz p1, :cond_13

    .line 201
    .line 202
    invoke-virtual {p1}, Llb1/c;->d()LV21/d;

    .line 203
    .line 204
    .line 205
    move-result-object p1

    .line 206
    goto :goto_c

    .line 207
    :cond_13
    move-object p1, v1

    .line 208
    :goto_c
    check-cast p1, LV21/b;

    .line 209
    .line 210
    invoke-virtual {p1}, LV21/b;->b()Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;

    .line 211
    .line 212
    .line 213
    move-result-object p1

    .line 214
    if-eqz v2, :cond_14

    .line 215
    .line 216
    check-cast p2, Llb1/c;

    .line 217
    .line 218
    goto :goto_d

    .line 219
    :cond_14
    move-object p2, v1

    .line 220
    :goto_d
    if-eqz p2, :cond_15

    .line 221
    .line 222
    invoke-virtual {p2}, Llb1/c;->d()LV21/d;

    .line 223
    .line 224
    .line 225
    move-result-object v1

    .line 226
    :cond_15
    check-cast v1, LV21/b;

    .line 227
    .line 228
    invoke-virtual {v1}, LV21/b;->b()Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;

    .line 229
    .line 230
    .line 231
    move-result-object p2

    .line 232
    if-ne p1, p2, :cond_16

    .line 233
    .line 234
    return v3

    .line 235
    :cond_16
    return v4

    .line 236
    :cond_17
    if-eqz v0, :cond_18

    .line 237
    .line 238
    move-object v0, p1

    .line 239
    check-cast v0, Llb1/c;

    .line 240
    .line 241
    goto :goto_e

    .line 242
    :cond_18
    move-object v0, v1

    .line 243
    :goto_e
    if-eqz v0, :cond_19

    .line 244
    .line 245
    invoke-virtual {v0}, Llb1/c;->d()LV21/d;

    .line 246
    .line 247
    .line 248
    move-result-object v0

    .line 249
    goto :goto_f

    .line 250
    :cond_19
    move-object v0, v1

    .line 251
    :goto_f
    instance-of v0, v0, LV21/e;

    .line 252
    .line 253
    if-eqz v0, :cond_1c

    .line 254
    .line 255
    instance-of v0, p2, Llb1/c;

    .line 256
    .line 257
    if-eqz v0, :cond_1a

    .line 258
    .line 259
    move-object v0, p2

    .line 260
    check-cast v0, Llb1/c;

    .line 261
    .line 262
    goto :goto_10

    .line 263
    :cond_1a
    move-object v0, v1

    .line 264
    :goto_10
    if-eqz v0, :cond_1b

    .line 265
    .line 266
    invoke-virtual {v0}, Llb1/c;->d()LV21/d;

    .line 267
    .line 268
    .line 269
    move-result-object v1

    .line 270
    :cond_1b
    instance-of v0, v1, LV21/e;

    .line 271
    .line 272
    if-eqz v0, :cond_1c

    .line 273
    .line 274
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 275
    .line 276
    .line 277
    move-result p1

    .line 278
    return p1

    .line 279
    :cond_1c
    instance-of v0, p1, Llb1/k;

    .line 280
    .line 281
    if-eqz v0, :cond_1d

    .line 282
    .line 283
    instance-of v0, p2, Llb1/k;

    .line 284
    .line 285
    if-eqz v0, :cond_1d

    .line 286
    .line 287
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 288
    .line 289
    .line 290
    move-result p1

    .line 291
    return p1

    .line 292
    :cond_1d
    instance-of v0, p1, Llb1/i;

    .line 293
    .line 294
    if-eqz v0, :cond_1e

    .line 295
    .line 296
    instance-of v0, p2, Llb1/i;

    .line 297
    .line 298
    if-eqz v0, :cond_1e

    .line 299
    .line 300
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 301
    .line 302
    .line 303
    move-result p1

    .line 304
    return p1

    .line 305
    :cond_1e
    instance-of v0, p1, Llb1/r;

    .line 306
    .line 307
    if-eqz v0, :cond_1f

    .line 308
    .line 309
    instance-of v0, p2, Llb1/r;

    .line 310
    .line 311
    if-eqz v0, :cond_1f

    .line 312
    .line 313
    check-cast p1, Llb1/r;

    .line 314
    .line 315
    invoke-virtual {p1}, Llb1/r;->f()Ljava/util/List;

    .line 316
    .line 317
    .line 318
    move-result-object p1

    .line 319
    check-cast p2, Llb1/r;

    .line 320
    .line 321
    invoke-virtual {p2}, Llb1/r;->f()Ljava/util/List;

    .line 322
    .line 323
    .line 324
    move-result-object p2

    .line 325
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 326
    .line 327
    .line 328
    move-result p1

    .line 329
    return p1

    .line 330
    :cond_1f
    return v4
.end method
