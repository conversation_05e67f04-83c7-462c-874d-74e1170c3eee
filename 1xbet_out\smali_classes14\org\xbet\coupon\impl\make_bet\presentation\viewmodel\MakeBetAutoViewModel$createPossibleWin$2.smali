.class final Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.coupon.impl.make_bet.presentation.viewmodel.MakeBetAutoViewModel$createPossibleWin$2"
    f = "MakeBetAutoViewModel.kt"
    l = {
        0x3a5
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->P4(Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $betSumStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

.field final synthetic $coefStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;

.field final synthetic $couponTypeModel:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

.field final synthetic $taxModel:Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;

.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;",
            "Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;",
            "Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;",
            "Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;",
            "Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    iput-object p2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->$taxModel:Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;

    iput-object p3, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->$couponTypeModel:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    iput-object p4, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->$betSumStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    iput-object p5, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->$coefStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p6}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;

    iget-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->$taxModel:Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;

    iget-object v3, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->$couponTypeModel:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    iget-object v4, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->$betSumStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    iget-object v5, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->$coefStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;

    move-object v6, p2

    invoke-direct/range {v0 .. v6}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->L$0:Ljava/lang/Object;

    .line 13
    .line 14
    check-cast v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 15
    .line 16
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    iget-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 32
    .line 33
    const/4 p1, 0x1

    .line 34
    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->$taxModel:Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;

    .line 35
    .line 36
    iget-object v3, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->$couponTypeModel:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 37
    .line 38
    iget-object v4, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->$betSumStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    .line 39
    .line 40
    iget-object v5, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->$coefStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;

    .line 41
    .line 42
    iput-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->L$0:Ljava/lang/Object;

    .line 43
    .line 44
    iput p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$createPossibleWin$2;->label:I

    .line 45
    .line 46
    move-object v6, p0

    .line 47
    invoke-static/range {v1 .. v6}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->E3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    if-ne p1, v0, :cond_2

    .line 52
    .line 53
    return-object v0

    .line 54
    :cond_2
    move-object v0, v1

    .line 55
    :goto_0
    check-cast p1, LHx/f;

    .line 56
    .line 57
    invoke-static {v0, p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->G4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;LHx/f;)V

    .line 58
    .line 59
    .line 60
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 61
    .line 62
    return-object p1
.end method
