.class public final Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;
.super Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0010\r\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0010\n\u0002\u0018\u0002\n\u0002\u0008\u0015\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0001\u0018\u0000 Y2\u00020\u0001:\u0001ZB\u001b\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u000f\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000b\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\nJ\u001f\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u000c\u001a\u00020\u00082\u0006\u0010\r\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J7\u0010\u0017\u001a\u00020\u000e2\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0013\u001a\u00020\u00082\u0006\u0010\u0014\u001a\u00020\u00082\u0006\u0010\u0015\u001a\u00020\u00082\u0006\u0010\u0016\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0019\u0010\u001b\u001a\u00020\u000e2\u0008\u0010\u001a\u001a\u0004\u0018\u00010\u0019H\u0016\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0019\u0010\u001d\u001a\u00020\u000e2\u0008\u0010\u001a\u001a\u0004\u0018\u00010\u0019H\u0016\u00a2\u0006\u0004\u0008\u001d\u0010\u001cJ\u0019\u0010\u001e\u001a\u00020\u000e2\u0008\u0010\u001a\u001a\u0004\u0018\u00010\u0019H\u0016\u00a2\u0006\u0004\u0008\u001e\u0010\u001cJ\u0017\u0010!\u001a\u00020\u000e2\u0006\u0010 \u001a\u00020\u001fH\u0016\u00a2\u0006\u0004\u0008!\u0010\"J\u000f\u0010#\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008#\u0010$J\u000f\u0010%\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008%\u0010$J\u000f\u0010&\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008&\u0010$J\u000f\u0010\'\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\'\u0010$J\u000f\u0010(\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008(\u0010$J\u000f\u0010)\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008)\u0010$J\u000f\u0010*\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008*\u0010$J\u000f\u0010+\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008+\u0010$J\u000f\u0010,\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008,\u0010$J\u000f\u0010-\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008-\u0010$J\u001b\u0010.\u001a\u00020\u000e2\n\u0008\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u0019H\u0002\u00a2\u0006\u0004\u0008.\u0010\u001cJ\u001b\u0010/\u001a\u00020\u000e2\n\u0008\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u0019H\u0002\u00a2\u0006\u0004\u0008/\u0010\u001cJ#\u00103\u001a\u00020\u000e*\u0002002\u0006\u00101\u001a\u00020\u00082\u0006\u00102\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u00083\u00104R\u0014\u00106\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u00105R\u0014\u00108\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00087\u00105R\u0014\u00109\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u00105R\u0014\u0010:\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u00105R\u0014\u0010;\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u00105R\u0014\u0010<\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008+\u00105R\u0014\u0010=\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008)\u00105R\u0014\u0010>\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u00105R\u0014\u0010?\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u00105R\u0014\u0010@\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008%\u00105R\u0014\u0010A\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u00105R\u0014\u0010B\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u00105R\u0014\u0010C\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u00105R\u0014\u0010E\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u00105R\u0014\u0010I\u001a\u00020F8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010HR\u0014\u0010M\u001a\u00020J8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008K\u0010LR\u0014\u0010O\u001a\u00020J8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008N\u0010LR\u0014\u0010Q\u001a\u00020J8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010LR\u0014\u0010U\u001a\u00020R8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008S\u0010TR\u001a\u0010X\u001a\u00020\u00088\u0014X\u0094\u0004\u00a2\u0006\u000c\n\u0004\u0008V\u00105\u001a\u0004\u0008W\u0010\n\u00a8\u0006["
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attributeSet",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "getIvStatusIconStart",
        "()I",
        "getCommonTextViewMeasuredWidth",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "",
        "text",
        "setStageNumberText",
        "(Ljava/lang/CharSequence;)V",
        "setTitleText",
        "setCaptionText",
        "Lc31/a;",
        "state",
        "setState",
        "(Lc31/a;)V",
        "q",
        "()V",
        "o",
        "p",
        "n",
        "m",
        "l",
        "j",
        "k",
        "i",
        "h",
        "f",
        "d",
        "Landroid/widget/TextView;",
        "minSize",
        "maxSize",
        "r",
        "(Landroid/widget/TextView;II)V",
        "I",
        "size24",
        "g",
        "size32",
        "size60",
        "space4",
        "space12",
        "space14",
        "space16",
        "space18",
        "textSize1",
        "textSize12",
        "textSize14",
        "colorBackground",
        "colorPrimary",
        "s",
        "colorStaticGreen",
        "Landroid/view/View;",
        "t",
        "Landroid/view/View;",
        "vStageNumberBackground",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "u",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "tvStageNumber",
        "v",
        "tvTitle",
        "w",
        "tvCaption",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "x",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "ivStatusIcon",
        "y",
        "getCardHeight",
        "cardHeight",
        "z",
        "a",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final A:I

.field public static final z:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:I

.field public final m:I

.field public final n:I

.field public final o:I

.field public final p:I

.field public final q:I

.field public final r:I

.field public final s:I

.field public final t:Landroid/view/View;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final u:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x:Landroidx/appcompat/widget/AppCompatImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->z:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->A:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 11
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->size_24:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->f:I

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_32:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->g:I

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->size_60:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->h:I

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v3, LlZ0/g;->space_4:I

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    iput v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->i:I

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v3, LlZ0/g;->space_12:I

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    iput v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->j:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v3, LlZ0/g;->space_14:I

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    iput v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->k:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v3, LlZ0/g;->space_16:I

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    iput v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->l:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v3, LlZ0/g;->space_18:I

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    iput v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->m:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v3, LlZ0/g;->text_1:I

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    iput v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->n:I

    .line 12
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v3, LlZ0/g;->text_12:I

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    iput v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->o:I

    .line 13
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->text_14:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->p:I

    .line 14
    sget v4, LlZ0/d;->uikitBackground:I

    const/4 v5, 0x0

    const/4 v6, 0x2

    invoke-static {p1, v4, v5, v6, v5}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v4

    iput v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->q:I

    .line 15
    sget v4, LlZ0/d;->uikitPrimary:I

    invoke-static {p1, v4, v5, v6, v5}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v4

    iput v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->r:I

    .line 16
    sget v4, LlZ0/d;->uikitStaticGreen:I

    invoke-static {p1, v4, v5, v6, v5}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v4

    iput v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->s:I

    .line 17
    new-instance v4, Landroid/view/View;

    invoke-direct {v4, p1}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    .line 18
    new-instance v5, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {v5, v0, v0}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v4, v5}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 19
    sget v5, LlZ0/h;->rounded_background_6:I

    invoke-virtual {v4, v5}, Landroid/view/View;->setBackgroundResource(I)V

    .line 20
    iput-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->t:Landroid/view/View;

    .line 21
    new-instance v5, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v5, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 22
    const-string v7, "DSAggregatorTournamentStagesCell.TAG_TV_STAGE_NUMBER"

    invoke-virtual {v5, v7}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 23
    sget v7, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    invoke-static {v5, v7}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 24
    new-instance v7, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {v7, p2, v0}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v5, v7}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    const/16 v0, 0x11

    .line 25
    invoke-virtual {v5, v0}, Landroid/widget/TextView;->setGravity(I)V

    const/4 v0, 0x1

    .line 26
    invoke-virtual {v5, v0}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 27
    invoke-virtual {p0, v5, v2, v3}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->r(Landroid/widget/TextView;II)V

    .line 28
    iput-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 29
    new-instance v0, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v0, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 30
    const-string v2, "DSAggregatorTournamentStagesCell.TAG_TV_TITLE"

    invoke-virtual {v0, v2}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 31
    sget v2, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    invoke-static {v0, v2}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 32
    invoke-virtual {v0, v6}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 33
    sget-object v2, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    const/4 v3, 0x0

    .line 34
    invoke-virtual {v0, v3}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 35
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->c()Z

    move-result v7

    const/4 v8, 0x3

    const/4 v9, 0x5

    if-eqz v7, :cond_0

    const/4 v7, 0x5

    goto :goto_0

    :cond_0
    const/4 v7, 0x3

    :goto_0
    invoke-virtual {v0, v7}, Landroid/widget/TextView;->setGravity(I)V

    .line 36
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 37
    new-instance v7, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v7, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 38
    const-string v10, "DSAggregatorTournamentStagesCell.TAG_TV_CAPTION"

    invoke-virtual {v7, v10}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 39
    sget v10, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    invoke-static {v7, v10}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 40
    invoke-virtual {v7, v6}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 41
    invoke-virtual {v7, v2}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 42
    invoke-virtual {v7, v3}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 43
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->c()Z

    move-result v2

    if-eqz v2, :cond_1

    const/4 v8, 0x5

    :cond_1
    invoke-virtual {v7, v8}, Landroid/widget/TextView;->setGravity(I)V

    .line 44
    iput-object v7, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 45
    new-instance v2, Landroidx/appcompat/widget/AppCompatImageView;

    invoke-direct {v2, p1}, Landroidx/appcompat/widget/AppCompatImageView;-><init>(Landroid/content/Context;)V

    .line 46
    const-string p1, "DSAggregatorTournamentStagesCell.TAG_IV_STATUS_ICON"

    invoke-virtual {v2, p1}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 47
    new-instance p1, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {p1, p2, p2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v2, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 48
    iput-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->x:Landroidx/appcompat/widget/AppCompatImageView;

    .line 49
    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->y:I

    .line 50
    sget p1, LlZ0/h;->rounded_background_16_content:I

    invoke-virtual {p0, p1}, Landroid/view/View;->setBackgroundResource(I)V

    .line 51
    invoke-virtual {p0, v4}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 52
    invoke-virtual {p0, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 53
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 54
    invoke-virtual {p0, v7}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 55
    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 1
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic e(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;Ljava/lang/CharSequence;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    if-eqz p2, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 6
    .line 7
    invoke-virtual {p1}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    :cond_0
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->d(Ljava/lang/CharSequence;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public static synthetic g(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;Ljava/lang/CharSequence;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    if-eqz p2, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 6
    .line 7
    invoke-virtual {p1}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    :cond_0
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->f(Ljava/lang/CharSequence;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method private final getCommonTextViewMeasuredWidth()I
    .locals 2

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->getIvStatusIconStart()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->g:I

    .line 6
    .line 7
    sub-int/2addr v0, v1

    .line 8
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->j:I

    .line 9
    .line 10
    mul-int/lit8 v1, v1, 0x3

    .line 11
    .line 12
    sub-int/2addr v0, v1

    .line 13
    return v0
.end method

.method private final getIvStatusIconStart()I
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->l:I

    .line 6
    .line 7
    sub-int/2addr v0, v1

    .line 8
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->x:Landroidx/appcompat/widget/AppCompatImageView;

    .line 9
    .line 10
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    sub-int/2addr v0, v1

    .line 15
    return v0
.end method


# virtual methods
.method public final d(Ljava/lang/CharSequence;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->getCommonTextViewMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    sget v2, LlZ0/g;->text_10:I

    .line 8
    .line 9
    sget v3, LlZ0/g;->text_12:I

    .line 10
    .line 11
    if-eqz p1, :cond_0

    .line 12
    .line 13
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 p1, 0x0

    .line 19
    :goto_0
    if-nez p1, :cond_1

    .line 20
    .line 21
    const-string p1, ""

    .line 22
    .line 23
    :cond_1
    invoke-static {v0, v1, v2, v3, p1}, Lorg/xbet/uikit/utils/M;->d(Landroid/widget/TextView;IIILjava/lang/String;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final f(Ljava/lang/CharSequence;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->getCommonTextViewMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    sget v2, LlZ0/g;->text_12:I

    .line 8
    .line 9
    sget v3, LlZ0/g;->text_14:I

    .line 10
    .line 11
    if-eqz p1, :cond_0

    .line 12
    .line 13
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 p1, 0x0

    .line 19
    :goto_0
    if-nez p1, :cond_1

    .line 20
    .line 21
    const-string p1, ""

    .line 22
    .line 23
    :cond_1
    invoke-static {v0, v1, v2, v3, p1}, Lorg/xbet/uikit/utils/M;->d(Landroid/widget/TextView;IIILjava/lang/String;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public getCardHeight()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->y:I

    .line 2
    .line 3
    return v0
.end method

.method public final h()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->x:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->getIvStatusIconStart()I

    .line 4
    .line 5
    .line 6
    move-result v2

    .line 7
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->m:I

    .line 8
    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->l:I

    .line 14
    .line 15
    sub-int v4, v0, v4

    .line 16
    .line 17
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->m:I

    .line 18
    .line 19
    iget-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->x:Landroidx/appcompat/widget/AppCompatImageView;

    .line 20
    .line 21
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredHeight()I

    .line 22
    .line 23
    .line 24
    move-result v5

    .line 25
    add-int/2addr v5, v0

    .line 26
    move-object v0, p0

    .line 27
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public final i()V
    .locals 8

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v0, v0, Lc31/a$c;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getHelperRect()Landroid/graphics/Rect;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v0, v1}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getHelperRect()Landroid/graphics/Rect;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 23
    .line 24
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->i:I

    .line 25
    .line 26
    add-int/2addr v0, v1

    .line 27
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 28
    .line 29
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 30
    .line 31
    .line 32
    move-result v1

    .line 33
    add-int v7, v0, v1

    .line 34
    .line 35
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 36
    .line 37
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->g:I

    .line 38
    .line 39
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->j:I

    .line 40
    .line 41
    mul-int/lit8 v1, v1, 0x2

    .line 42
    .line 43
    add-int v4, v0, v1

    .line 44
    .line 45
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredHeight()I

    .line 46
    .line 47
    .line 48
    move-result v0

    .line 49
    sub-int v5, v7, v0

    .line 50
    .line 51
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->getIvStatusIconStart()I

    .line 52
    .line 53
    .line 54
    move-result v0

    .line 55
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->j:I

    .line 56
    .line 57
    sub-int v6, v0, v1

    .line 58
    .line 59
    move-object v2, p0

    .line 60
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 61
    .line 62
    .line 63
    :cond_0
    return-void
.end method

.method public final j()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->l:I

    .line 4
    .line 5
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->k:I

    .line 6
    .line 7
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    add-int v4, v2, v0

    .line 12
    .line 13
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->k:I

    .line 14
    .line 15
    iget-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 16
    .line 17
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredHeight()I

    .line 18
    .line 19
    .line 20
    move-result v5

    .line 21
    add-int/2addr v5, v0

    .line 22
    move-object v0, p0

    .line 23
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final k()V
    .locals 15

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lc31/a$a;

    .line 6
    .line 7
    if-nez v1, :cond_0

    .line 8
    .line 9
    instance-of v1, v0, Lc31/a$b;

    .line 10
    .line 11
    if-eqz v1, :cond_1

    .line 12
    .line 13
    :cond_0
    move-object v3, p0

    .line 14
    goto :goto_1

    .line 15
    :cond_1
    instance-of v0, v0, Lc31/a$c;

    .line 16
    .line 17
    if-eqz v0, :cond_3

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 20
    .line 21
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    const/16 v1, 0x8

    .line 26
    .line 27
    if-ne v0, v1, :cond_2

    .line 28
    .line 29
    const/4 v0, 0x0

    .line 30
    goto :goto_0

    .line 31
    :cond_2
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 32
    .line 33
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 34
    .line 35
    .line 36
    move-result v0

    .line 37
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->i:I

    .line 38
    .line 39
    add-int/2addr v0, v1

    .line 40
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 41
    .line 42
    .line 43
    move-result v1

    .line 44
    div-int/lit8 v1, v1, 0x2

    .line 45
    .line 46
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 47
    .line 48
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 49
    .line 50
    .line 51
    move-result v2

    .line 52
    add-int/2addr v2, v0

    .line 53
    div-int/lit8 v2, v2, 0x2

    .line 54
    .line 55
    sub-int v6, v1, v2

    .line 56
    .line 57
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 58
    .line 59
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->g:I

    .line 60
    .line 61
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->j:I

    .line 62
    .line 63
    mul-int/lit8 v1, v1, 0x2

    .line 64
    .line 65
    add-int v5, v0, v1

    .line 66
    .line 67
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->getIvStatusIconStart()I

    .line 68
    .line 69
    .line 70
    move-result v0

    .line 71
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->j:I

    .line 72
    .line 73
    sub-int v7, v0, v1

    .line 74
    .line 75
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 76
    .line 77
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 78
    .line 79
    .line 80
    move-result v0

    .line 81
    add-int v8, v6, v0

    .line 82
    .line 83
    move-object v3, p0

    .line 84
    invoke-static/range {v3 .. v8}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 85
    .line 86
    .line 87
    return-void

    .line 88
    :cond_3
    move-object v3, p0

    .line 89
    return-void

    .line 90
    :goto_1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 91
    .line 92
    .line 93
    move-result v0

    .line 94
    div-int/lit8 v0, v0, 0x2

    .line 95
    .line 96
    iget-object v1, v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 97
    .line 98
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 99
    .line 100
    .line 101
    move-result v1

    .line 102
    div-int/lit8 v1, v1, 0x2

    .line 103
    .line 104
    sub-int v12, v0, v1

    .line 105
    .line 106
    iget-object v10, v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 107
    .line 108
    iget v0, v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->g:I

    .line 109
    .line 110
    iget v1, v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->j:I

    .line 111
    .line 112
    mul-int/lit8 v1, v1, 0x2

    .line 113
    .line 114
    add-int v11, v0, v1

    .line 115
    .line 116
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->getIvStatusIconStart()I

    .line 117
    .line 118
    .line 119
    move-result v0

    .line 120
    iget v1, v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->j:I

    .line 121
    .line 122
    sub-int v13, v0, v1

    .line 123
    .line 124
    iget-object v0, v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 125
    .line 126
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 127
    .line 128
    .line 129
    move-result v0

    .line 130
    add-int v14, v12, v0

    .line 131
    .line 132
    move-object v9, v3

    .line 133
    invoke-static/range {v9 .. v14}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 134
    .line 135
    .line 136
    return-void
.end method

.method public final l()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->t:Landroid/view/View;

    .line 2
    .line 3
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->j:I

    .line 4
    .line 5
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->k:I

    .line 6
    .line 7
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    add-int v4, v2, v0

    .line 12
    .line 13
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->k:I

    .line 14
    .line 15
    iget-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->t:Landroid/view/View;

    .line 16
    .line 17
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredHeight()I

    .line 18
    .line 19
    .line 20
    move-result v5

    .line 21
    add-int/2addr v5, v0

    .line 22
    move-object v0, p0

    .line 23
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final m()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->x:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v1, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 8
    .line 9
    const/high16 v2, 0x40000000    # 2.0f

    .line 10
    .line 11
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->x:Landroidx/appcompat/widget/AppCompatImageView;

    .line 16
    .line 17
    invoke-virtual {v3}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    iget v3, v3, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 22
    .line 23
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public final n()V
    .locals 3

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {p0, v0, v1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->e(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;Ljava/lang/CharSequence;ILjava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 7
    .line 8
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->getIvStatusIconStart()I

    .line 9
    .line 10
    .line 11
    move-result v1

    .line 12
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->g:I

    .line 13
    .line 14
    sub-int/2addr v1, v2

    .line 15
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->j:I

    .line 16
    .line 17
    mul-int/lit8 v2, v2, 0x3

    .line 18
    .line 19
    sub-int/2addr v1, v2

    .line 20
    const/high16 v2, 0x40000000    # 2.0f

    .line 21
    .line 22
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 23
    .line 24
    .line 25
    move-result v1

    .line 26
    const/4 v2, 0x0

    .line 27
    invoke-static {v2, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 28
    .line 29
    .line 30
    move-result v2

    .line 31
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public final o()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v1, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 8
    .line 9
    const/high16 v2, 0x40000000    # 2.0f

    .line 10
    .line 11
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 16
    .line 17
    invoke-virtual {v3}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    iget v3, v3, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 22
    .line 23
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-super/range {p0 .. p5}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->onLayout(ZIIII)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->l()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->j()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->h()V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->k()V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->i()V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public onMeasure(II)V
    .locals 1

    .line 1
    invoke-super {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    const/high16 p2, 0x40000000    # 2.0f

    .line 9
    .line 10
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->getCardHeight()I

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    invoke-static {v0, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 19
    .line 20
    .line 21
    move-result p2

    .line 22
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->m()V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->q()V

    .line 29
    .line 30
    .line 31
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->o()V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->p()V

    .line 35
    .line 36
    .line 37
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->n()V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public final p()V
    .locals 5

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {p0, v0, v1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->g(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;Ljava/lang/CharSequence;ILjava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    instance-of v1, v0, Lc31/a$a;

    .line 11
    .line 12
    const/high16 v2, 0x40000000    # 2.0f

    .line 13
    .line 14
    const/4 v3, 0x0

    .line 15
    if-nez v1, :cond_2

    .line 16
    .line 17
    instance-of v1, v0, Lc31/a$b;

    .line 18
    .line 19
    if-eqz v1, :cond_0

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    instance-of v0, v0, Lc31/a$c;

    .line 23
    .line 24
    if-eqz v0, :cond_1

    .line 25
    .line 26
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 27
    .line 28
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->getIvStatusIconStart()I

    .line 29
    .line 30
    .line 31
    move-result v1

    .line 32
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->g:I

    .line 33
    .line 34
    sub-int/2addr v1, v4

    .line 35
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->j:I

    .line 36
    .line 37
    mul-int/lit8 v4, v4, 0x3

    .line 38
    .line 39
    sub-int/2addr v1, v4

    .line 40
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 41
    .line 42
    .line 43
    move-result v1

    .line 44
    invoke-static {v3, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 49
    .line 50
    .line 51
    :cond_1
    return-void

    .line 52
    :cond_2
    :goto_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 53
    .line 54
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->getIvStatusIconStart()I

    .line 55
    .line 56
    .line 57
    move-result v1

    .line 58
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->g:I

    .line 59
    .line 60
    sub-int/2addr v1, v4

    .line 61
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->j:I

    .line 62
    .line 63
    mul-int/lit8 v4, v4, 0x3

    .line 64
    .line 65
    sub-int/2addr v1, v4

    .line 66
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 67
    .line 68
    .line 69
    move-result v1

    .line 70
    invoke-static {v3, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 71
    .line 72
    .line 73
    move-result v2

    .line 74
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 75
    .line 76
    .line 77
    return-void
.end method

.method public final q()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->t:Landroid/view/View;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v1, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 8
    .line 9
    const/high16 v2, 0x40000000    # 2.0f

    .line 10
    .line 11
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->t:Landroid/view/View;

    .line 16
    .line 17
    invoke-virtual {v3}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    iget v3, v3, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 22
    .line 23
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public final r(Landroid/widget/TextView;II)V
    .locals 2

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->n:I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {p1, p2, p3, v0, v1}, LX0/o;->h(Landroid/widget/TextView;IIII)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public setCaptionText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->d(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 5
    .line 6
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public setStageNumberText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setState(Lc31/a;)V
    .locals 6
    .param p1    # Lc31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->setState(Lc31/a;)V

    .line 2
    .line 3
    .line 4
    instance-of v0, p1, Lc31/a$a;

    .line 5
    .line 6
    const/4 v1, 0x0

    .line 7
    const/4 v2, 0x2

    .line 8
    const/16 v3, 0x8

    .line 9
    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 13
    .line 14
    sget v4, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    .line 15
    .line 16
    invoke-static {v0, v4}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 17
    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->t:Landroid/view/View;

    .line 20
    .line 21
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->q:I

    .line 22
    .line 23
    invoke-static {v4}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    invoke-virtual {v0, v4}, Landroid/view/View;->setBackgroundTintList(Landroid/content/res/ColorStateList;)V

    .line 28
    .line 29
    .line 30
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 31
    .line 32
    sget v4, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    .line 33
    .line 34
    invoke-static {v0, v4}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 35
    .line 36
    .line 37
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 38
    .line 39
    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 40
    .line 41
    .line 42
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 43
    .line 44
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 45
    .line 46
    .line 47
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->x:Landroidx/appcompat/widget/AppCompatImageView;

    .line 48
    .line 49
    sget v2, LlZ0/h;->ic_status_gray_waiting_circle:I

    .line 50
    .line 51
    invoke-virtual {v0, v2}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 52
    .line 53
    .line 54
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->x:Landroidx/appcompat/widget/AppCompatImageView;

    .line 55
    .line 56
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 57
    .line 58
    .line 59
    goto/16 :goto_1

    .line 60
    .line 61
    :cond_0
    instance-of v0, p1, Lc31/a$c;

    .line 62
    .line 63
    if-eqz v0, :cond_4

    .line 64
    .line 65
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 66
    .line 67
    sget v2, LlZ0/n;->TextStyle_Text_Bold_StaticWhite:I

    .line 68
    .line 69
    invoke-static {v0, v2}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 70
    .line 71
    .line 72
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->t:Landroid/view/View;

    .line 73
    .line 74
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->r:I

    .line 75
    .line 76
    invoke-static {v2}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 77
    .line 78
    .line 79
    move-result-object v2

    .line 80
    invoke-virtual {v0, v2}, Landroid/view/View;->setBackgroundTintList(Landroid/content/res/ColorStateList;)V

    .line 81
    .line 82
    .line 83
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 84
    .line 85
    sget v2, LlZ0/n;->TextStyle_Text_Bold_TextPrimary:I

    .line 86
    .line 87
    invoke-static {v0, v2}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 88
    .line 89
    .line 90
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 91
    .line 92
    const/4 v2, 0x1

    .line 93
    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 94
    .line 95
    .line 96
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 97
    .line 98
    move-object v4, p1

    .line 99
    check-cast v4, Lc31/a$c;

    .line 100
    .line 101
    invoke-virtual {v4}, Lc31/a$c;->c()Ljava/lang/String;

    .line 102
    .line 103
    .line 104
    move-result-object v4

    .line 105
    const/4 v5, 0x0

    .line 106
    if-eqz v4, :cond_2

    .line 107
    .line 108
    invoke-interface {v4}, Ljava/lang/CharSequence;->length()I

    .line 109
    .line 110
    .line 111
    move-result v4

    .line 112
    if-nez v4, :cond_1

    .line 113
    .line 114
    goto :goto_0

    .line 115
    :cond_1
    const/4 v2, 0x0

    .line 116
    :cond_2
    :goto_0
    if-nez v2, :cond_3

    .line 117
    .line 118
    const/4 v3, 0x0

    .line 119
    :cond_3
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 120
    .line 121
    .line 122
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->x:Landroidx/appcompat/widget/AppCompatImageView;

    .line 123
    .line 124
    sget v2, LlZ0/h;->ic_glyph_watch_face:I

    .line 125
    .line 126
    invoke-virtual {v0, v2}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 127
    .line 128
    .line 129
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->x:Landroidx/appcompat/widget/AppCompatImageView;

    .line 130
    .line 131
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 132
    .line 133
    .line 134
    goto :goto_1

    .line 135
    :cond_4
    instance-of v0, p1, Lc31/a$b;

    .line 136
    .line 137
    if-eqz v0, :cond_5

    .line 138
    .line 139
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 140
    .line 141
    sget v1, LlZ0/n;->TextStyle_Text_Medium_StaticWhite:I

    .line 142
    .line 143
    invoke-static {v0, v1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 144
    .line 145
    .line 146
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->t:Landroid/view/View;

    .line 147
    .line 148
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->s:I

    .line 149
    .line 150
    invoke-static {v1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 151
    .line 152
    .line 153
    move-result-object v1

    .line 154
    invoke-virtual {v0, v1}, Landroid/view/View;->setBackgroundTintList(Landroid/content/res/ColorStateList;)V

    .line 155
    .line 156
    .line 157
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 158
    .line 159
    sget v1, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    .line 160
    .line 161
    invoke-static {v0, v1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 162
    .line 163
    .line 164
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 165
    .line 166
    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 167
    .line 168
    .line 169
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 170
    .line 171
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 172
    .line 173
    .line 174
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->x:Landroidx/appcompat/widget/AppCompatImageView;

    .line 175
    .line 176
    sget v1, LlZ0/h;->ic_glyph_checkmark:I

    .line 177
    .line 178
    invoke-virtual {v0, v1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 179
    .line 180
    .line 181
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->x:Landroidx/appcompat/widget/AppCompatImageView;

    .line 182
    .line 183
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->s:I

    .line 184
    .line 185
    invoke-static {v1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 186
    .line 187
    .line 188
    move-result-object v1

    .line 189
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 190
    .line 191
    .line 192
    :goto_1
    invoke-interface {p1}, Lc31/a;->b()Ljava/lang/String;

    .line 193
    .line 194
    .line 195
    move-result-object v0

    .line 196
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->setStageNumberText(Ljava/lang/CharSequence;)V

    .line 197
    .line 198
    .line 199
    invoke-interface {p1}, Lc31/a;->a()Ljava/lang/String;

    .line 200
    .line 201
    .line 202
    move-result-object v0

    .line 203
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->setTitleText(Ljava/lang/CharSequence;)V

    .line 204
    .line 205
    .line 206
    invoke-interface {p1}, Lc31/a;->c()Ljava/lang/String;

    .line 207
    .line 208
    .line 209
    move-result-object p1

    .line 210
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->setCaptionText(Ljava/lang/CharSequence;)V

    .line 211
    .line 212
    .line 213
    return-void

    .line 214
    :cond_5
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 215
    .line 216
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 217
    .line 218
    .line 219
    throw p1
.end method

.method public setTitleText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->f(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 5
    .line 6
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method
