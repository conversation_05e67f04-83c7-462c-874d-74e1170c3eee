.class public final Lorg/xbet/coupon/impl/promocode/presentation/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/coupon/impl/coupon/domain/usecases/G;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ltw/d;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lfk/m;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ltw/o;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LMx/a;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ltw/e;",
            ">;"
        }
    .end annotation
.end field

.field public final l:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/user/c;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;",
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/coupon/impl/coupon/domain/usecases/G;",
            ">;",
            "LBc/a<",
            "Ltw/d;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lfk/m;",
            ">;",
            "LBc/a<",
            "Ltw/o;",
            ">;",
            "LBc/a<",
            "LMx/a;",
            ">;",
            "LBc/a<",
            "Ltw/e;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/user/c;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->j:LBc/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->k:LBc/a;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->l:LBc/a;

    .line 27
    .line 28
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/coupon/impl/promocode/presentation/h;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;",
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/coupon/impl/coupon/domain/usecases/G;",
            ">;",
            "LBc/a<",
            "Ltw/d;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lfk/m;",
            ">;",
            "LBc/a<",
            "Ltw/o;",
            ">;",
            "LBc/a<",
            "LMx/a;",
            ">;",
            "LBc/a<",
            "Ltw/e;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/user/c;",
            ">;)",
            "Lorg/xbet/coupon/impl/promocode/presentation/h;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/coupon/impl/promocode/presentation/h;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object/from16 v4, p3

    .line 7
    .line 8
    move-object/from16 v5, p4

    .line 9
    .line 10
    move-object/from16 v6, p5

    .line 11
    .line 12
    move-object/from16 v7, p6

    .line 13
    .line 14
    move-object/from16 v8, p7

    .line 15
    .line 16
    move-object/from16 v9, p8

    .line 17
    .line 18
    move-object/from16 v10, p9

    .line 19
    .line 20
    move-object/from16 v11, p10

    .line 21
    .line 22
    move-object/from16 v12, p11

    .line 23
    .line 24
    invoke-direct/range {v0 .. v12}, Lorg/xbet/coupon/impl/promocode/presentation/h;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 25
    .line 26
    .line 27
    return-object v0
.end method

.method public static c(ZZLorg/xbet/coupon/impl/coupon/domain/usecases/G;Ltw/d;Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;LHX0/e;Lm8/a;Lfk/m;Ltw/o;LMx/a;Ltw/e;Lcom/xbet/onexuser/domain/user/c;)Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;
    .locals 13

    .line 1
    new-instance v0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 2
    .line 3
    move v1, p0

    .line 4
    move v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object/from16 v4, p3

    .line 7
    .line 8
    move-object/from16 v5, p4

    .line 9
    .line 10
    move-object/from16 v6, p5

    .line 11
    .line 12
    move-object/from16 v7, p6

    .line 13
    .line 14
    move-object/from16 v8, p7

    .line 15
    .line 16
    move-object/from16 v9, p8

    .line 17
    .line 18
    move-object/from16 v10, p9

    .line 19
    .line 20
    move-object/from16 v11, p10

    .line 21
    .line 22
    move-object/from16 v12, p11

    .line 23
    .line 24
    invoke-direct/range {v0 .. v12}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;-><init>(ZZLorg/xbet/coupon/impl/coupon/domain/usecases/G;Ltw/d;Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;LHX0/e;Lm8/a;Lfk/m;Ltw/o;LMx/a;Ltw/e;Lcom/xbet/onexuser/domain/user/c;)V

    .line 25
    .line 26
    .line 27
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;
    .locals 13

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/lang/Boolean;

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->b:LBc/a;

    .line 14
    .line 15
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    check-cast v0, Ljava/lang/Boolean;

    .line 20
    .line 21
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->c:LBc/a;

    .line 26
    .line 27
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    move-object v3, v0

    .line 32
    check-cast v3, Lorg/xbet/coupon/impl/coupon/domain/usecases/G;

    .line 33
    .line 34
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->d:LBc/a;

    .line 35
    .line 36
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    move-object v4, v0

    .line 41
    check-cast v4, Ltw/d;

    .line 42
    .line 43
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->e:LBc/a;

    .line 44
    .line 45
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    move-object v5, v0

    .line 50
    check-cast v5, Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;

    .line 51
    .line 52
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->f:LBc/a;

    .line 53
    .line 54
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    move-object v6, v0

    .line 59
    check-cast v6, LHX0/e;

    .line 60
    .line 61
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->g:LBc/a;

    .line 62
    .line 63
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    move-object v7, v0

    .line 68
    check-cast v7, Lm8/a;

    .line 69
    .line 70
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->h:LBc/a;

    .line 71
    .line 72
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    move-object v8, v0

    .line 77
    check-cast v8, Lfk/m;

    .line 78
    .line 79
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->i:LBc/a;

    .line 80
    .line 81
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object v0

    .line 85
    move-object v9, v0

    .line 86
    check-cast v9, Ltw/o;

    .line 87
    .line 88
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->j:LBc/a;

    .line 89
    .line 90
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    move-object v10, v0

    .line 95
    check-cast v10, LMx/a;

    .line 96
    .line 97
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->k:LBc/a;

    .line 98
    .line 99
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 100
    .line 101
    .line 102
    move-result-object v0

    .line 103
    move-object v11, v0

    .line 104
    check-cast v11, Ltw/e;

    .line 105
    .line 106
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/h;->l:LBc/a;

    .line 107
    .line 108
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 109
    .line 110
    .line 111
    move-result-object v0

    .line 112
    move-object v12, v0

    .line 113
    check-cast v12, Lcom/xbet/onexuser/domain/user/c;

    .line 114
    .line 115
    invoke-static/range {v1 .. v12}, Lorg/xbet/coupon/impl/promocode/presentation/h;->c(ZZLorg/xbet/coupon/impl/coupon/domain/usecases/G;Ltw/d;Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;LHX0/e;Lm8/a;Lfk/m;Ltw/o;LMx/a;Ltw/e;Lcom/xbet/onexuser/domain/user/c;)Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 116
    .line 117
    .line 118
    move-result-object v0

    .line 119
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/promocode/presentation/h;->b()Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
