.class public final synthetic LmM0/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/animation/ValueAnimator$AnimatorUpdateListener;


# instance fields
.field public final synthetic a:Landroid/animation/ValueAnimator;

.field public final synthetic b:Landroid/animation/ValueAnimator;

.field public final synthetic c:Lkotlin/jvm/internal/Ref$IntRef;

.field public final synthetic d:Lkotlin/jvm/internal/Ref$IntRef;

.field public final synthetic e:Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/view/bracket/BracketScrollView;

.field public final synthetic f:Z


# direct methods
.method public synthetic constructor <init>(Landroid/animation/ValueAnimator;Landroid/animation/ValueAnimator;Lkotlin/jvm/internal/Ref$IntRef;Lkotlin/jvm/internal/Ref$IntRef;Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/view/bracket/BracketScrollView;Z)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LmM0/c;->a:Landroid/animation/ValueAnimator;

    iput-object p2, p0, LmM0/c;->b:Landroid/animation/ValueAnimator;

    iput-object p3, p0, LmM0/c;->c:Lkotlin/jvm/internal/Ref$IntRef;

    iput-object p4, p0, LmM0/c;->d:Lkotlin/jvm/internal/Ref$IntRef;

    iput-object p5, p0, LmM0/c;->e:Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/view/bracket/BracketScrollView;

    iput-boolean p6, p0, LmM0/c;->f:Z

    return-void
.end method


# virtual methods
.method public final onAnimationUpdate(Landroid/animation/ValueAnimator;)V
    .locals 7

    .line 1
    iget-object v0, p0, LmM0/c;->a:Landroid/animation/ValueAnimator;

    iget-object v1, p0, LmM0/c;->b:Landroid/animation/ValueAnimator;

    iget-object v2, p0, LmM0/c;->c:Lkotlin/jvm/internal/Ref$IntRef;

    iget-object v3, p0, LmM0/c;->d:Lkotlin/jvm/internal/Ref$IntRef;

    iget-object v4, p0, LmM0/c;->e:Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/view/bracket/BracketScrollView;

    iget-boolean v5, p0, LmM0/c;->f:Z

    move-object v6, p1

    invoke-static/range {v0 .. v6}, Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/view/bracket/BracketScrollView;->a(Landroid/animation/ValueAnimator;Landroid/animation/ValueAnimator;Lkotlin/jvm/internal/Ref$IntRef;Lkotlin/jvm/internal/Ref$IntRef;Lorg/xbet/statistic/stage/impl/champ_statistic_tour_net/presentation/view/bracket/BracketScrollView;ZLandroid/animation/ValueAnimator;)V

    return-void
.end method
