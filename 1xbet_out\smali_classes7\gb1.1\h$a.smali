.class public final Lgb1/h$a;
.super Landroidx/recyclerview/widget/i$f;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgb1/h;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroidx/recyclerview/widget/i$f<",
        "LVX0/i;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0004\u0008\u00c2\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u001f\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\n\u001a\u00020\u00072\u0006\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\n\u0010\t\u00a8\u0006\u000b"
    }
    d2 = {
        "Lgb1/h$a;",
        "Landroidx/recyclerview/widget/i$f;",
        "LVX0/i;",
        "<init>",
        "()V",
        "oldItem",
        "newItem",
        "",
        "e",
        "(LVX0/i;LVX0/i;)Z",
        "d",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:Lgb1/h$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lgb1/h$a;

    .line 2
    .line 3
    invoke-direct {v0}, Lgb1/h$a;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Lgb1/h$a;->a:Lgb1/h$a;

    .line 7
    .line 8
    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Landroidx/recyclerview/widget/i$f;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, LVX0/i;

    .line 2
    .line 3
    check-cast p2, LVX0/i;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, Lgb1/h$a;->d(LVX0/i;LVX0/i;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public bridge synthetic b(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, LVX0/i;

    .line 2
    .line 3
    check-cast p2, LVX0/i;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, Lgb1/h$a;->e(LVX0/i;LVX0/i;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public d(LVX0/i;LVX0/i;)Z
    .locals 4
    .param p1    # LVX0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LVX0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, Llb1/e;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    move-object v2, p1

    .line 7
    check-cast v2, Llb1/e;

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    move-object v2, v1

    .line 11
    :goto_0
    if-eqz v2, :cond_1

    .line 12
    .line 13
    invoke-virtual {v2}, Llb1/e;->d()Lq21/b;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    goto :goto_1

    .line 18
    :cond_1
    move-object v2, v1

    .line 19
    :goto_1
    instance-of v2, v2, Lq21/a;

    .line 20
    .line 21
    if-eqz v2, :cond_8

    .line 22
    .line 23
    instance-of v2, p2, Llb1/e;

    .line 24
    .line 25
    if-eqz v2, :cond_2

    .line 26
    .line 27
    move-object v3, p2

    .line 28
    check-cast v3, Llb1/e;

    .line 29
    .line 30
    goto :goto_2

    .line 31
    :cond_2
    move-object v3, v1

    .line 32
    :goto_2
    if-eqz v3, :cond_3

    .line 33
    .line 34
    invoke-virtual {v3}, Llb1/e;->d()Lq21/b;

    .line 35
    .line 36
    .line 37
    move-result-object v3

    .line 38
    goto :goto_3

    .line 39
    :cond_3
    move-object v3, v1

    .line 40
    :goto_3
    instance-of v3, v3, Lq21/a;

    .line 41
    .line 42
    if-eqz v3, :cond_8

    .line 43
    .line 44
    if-eqz v0, :cond_4

    .line 45
    .line 46
    check-cast p1, Llb1/e;

    .line 47
    .line 48
    goto :goto_4

    .line 49
    :cond_4
    move-object p1, v1

    .line 50
    :goto_4
    if-eqz p1, :cond_5

    .line 51
    .line 52
    invoke-virtual {p1}, Llb1/e;->d()Lq21/b;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    goto :goto_5

    .line 57
    :cond_5
    move-object p1, v1

    .line 58
    :goto_5
    check-cast p1, Lq21/a;

    .line 59
    .line 60
    invoke-virtual {p1}, Lq21/a;->c()Lq21/f;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    if-eqz v2, :cond_6

    .line 65
    .line 66
    check-cast p2, Llb1/e;

    .line 67
    .line 68
    goto :goto_6

    .line 69
    :cond_6
    move-object p2, v1

    .line 70
    :goto_6
    if-eqz p2, :cond_7

    .line 71
    .line 72
    invoke-virtual {p2}, Llb1/e;->d()Lq21/b;

    .line 73
    .line 74
    .line 75
    move-result-object v1

    .line 76
    :cond_7
    check-cast v1, Lq21/a;

    .line 77
    .line 78
    invoke-virtual {v1}, Lq21/a;->c()Lq21/f;

    .line 79
    .line 80
    .line 81
    move-result-object p2

    .line 82
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 83
    .line 84
    .line 85
    move-result p1

    .line 86
    return p1

    .line 87
    :cond_8
    instance-of v0, p1, Lkb1/q;

    .line 88
    .line 89
    if-eqz v0, :cond_9

    .line 90
    .line 91
    instance-of v0, p2, Lkb1/q;

    .line 92
    .line 93
    if-eqz v0, :cond_9

    .line 94
    .line 95
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 96
    .line 97
    .line 98
    move-result p1

    .line 99
    return p1

    .line 100
    :cond_9
    instance-of v0, p1, Llb1/f;

    .line 101
    .line 102
    if-eqz v0, :cond_a

    .line 103
    .line 104
    instance-of v0, p2, Llb1/f;

    .line 105
    .line 106
    if-eqz v0, :cond_a

    .line 107
    .line 108
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 109
    .line 110
    .line 111
    move-result p1

    .line 112
    return p1

    .line 113
    :cond_a
    instance-of p1, p1, Lkb1/r;

    .line 114
    .line 115
    if-eqz p1, :cond_b

    .line 116
    .line 117
    instance-of p1, p2, Lkb1/r;

    .line 118
    .line 119
    if-eqz p1, :cond_b

    .line 120
    .line 121
    const/4 p1, 0x1

    .line 122
    return p1

    .line 123
    :cond_b
    const/4 p1, 0x0

    .line 124
    return p1
.end method

.method public e(LVX0/i;LVX0/i;)Z
    .locals 6
    .param p1    # LVX0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LVX0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, Llb1/e;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    move-object v2, p1

    .line 7
    check-cast v2, Llb1/e;

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    move-object v2, v1

    .line 11
    :goto_0
    if-eqz v2, :cond_1

    .line 12
    .line 13
    invoke-virtual {v2}, Llb1/e;->d()Lq21/b;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    goto :goto_1

    .line 18
    :cond_1
    move-object v2, v1

    .line 19
    :goto_1
    instance-of v2, v2, Lq21/a;

    .line 20
    .line 21
    const/4 v3, 0x1

    .line 22
    const/4 v4, 0x0

    .line 23
    if-eqz v2, :cond_9

    .line 24
    .line 25
    instance-of v2, p2, Llb1/e;

    .line 26
    .line 27
    if-eqz v2, :cond_2

    .line 28
    .line 29
    move-object v5, p2

    .line 30
    check-cast v5, Llb1/e;

    .line 31
    .line 32
    goto :goto_2

    .line 33
    :cond_2
    move-object v5, v1

    .line 34
    :goto_2
    if-eqz v5, :cond_3

    .line 35
    .line 36
    invoke-virtual {v5}, Llb1/e;->d()Lq21/b;

    .line 37
    .line 38
    .line 39
    move-result-object v5

    .line 40
    goto :goto_3

    .line 41
    :cond_3
    move-object v5, v1

    .line 42
    :goto_3
    instance-of v5, v5, Lq21/a;

    .line 43
    .line 44
    if-eqz v5, :cond_9

    .line 45
    .line 46
    if-eqz v0, :cond_4

    .line 47
    .line 48
    check-cast p1, Llb1/e;

    .line 49
    .line 50
    goto :goto_4

    .line 51
    :cond_4
    move-object p1, v1

    .line 52
    :goto_4
    if-eqz p1, :cond_5

    .line 53
    .line 54
    invoke-virtual {p1}, Llb1/e;->d()Lq21/b;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    goto :goto_5

    .line 59
    :cond_5
    move-object p1, v1

    .line 60
    :goto_5
    check-cast p1, Lq21/a;

    .line 61
    .line 62
    invoke-virtual {p1}, Lq21/a;->a()Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    if-eqz v2, :cond_6

    .line 67
    .line 68
    check-cast p2, Llb1/e;

    .line 69
    .line 70
    goto :goto_6

    .line 71
    :cond_6
    move-object p2, v1

    .line 72
    :goto_6
    if-eqz p2, :cond_7

    .line 73
    .line 74
    invoke-virtual {p2}, Llb1/e;->d()Lq21/b;

    .line 75
    .line 76
    .line 77
    move-result-object v1

    .line 78
    :cond_7
    check-cast v1, Lq21/a;

    .line 79
    .line 80
    invoke-virtual {v1}, Lq21/a;->a()Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;

    .line 81
    .line 82
    .line 83
    move-result-object p2

    .line 84
    if-ne p1, p2, :cond_8

    .line 85
    .line 86
    return v3

    .line 87
    :cond_8
    return v4

    .line 88
    :cond_9
    if-eqz v0, :cond_a

    .line 89
    .line 90
    move-object v2, p1

    .line 91
    check-cast v2, Llb1/e;

    .line 92
    .line 93
    goto :goto_7

    .line 94
    :cond_a
    move-object v2, v1

    .line 95
    :goto_7
    if-eqz v2, :cond_b

    .line 96
    .line 97
    invoke-virtual {v2}, Llb1/e;->d()Lq21/b;

    .line 98
    .line 99
    .line 100
    move-result-object v2

    .line 101
    goto :goto_8

    .line 102
    :cond_b
    move-object v2, v1

    .line 103
    :goto_8
    instance-of v2, v2, Lq21/e;

    .line 104
    .line 105
    if-eqz v2, :cond_13

    .line 106
    .line 107
    instance-of v2, p2, Llb1/e;

    .line 108
    .line 109
    if-eqz v2, :cond_c

    .line 110
    .line 111
    move-object v5, p2

    .line 112
    check-cast v5, Llb1/e;

    .line 113
    .line 114
    goto :goto_9

    .line 115
    :cond_c
    move-object v5, v1

    .line 116
    :goto_9
    if-eqz v5, :cond_d

    .line 117
    .line 118
    invoke-virtual {v5}, Llb1/e;->d()Lq21/b;

    .line 119
    .line 120
    .line 121
    move-result-object v5

    .line 122
    goto :goto_a

    .line 123
    :cond_d
    move-object v5, v1

    .line 124
    :goto_a
    instance-of v5, v5, Lq21/e;

    .line 125
    .line 126
    if-eqz v5, :cond_13

    .line 127
    .line 128
    if-eqz v0, :cond_e

    .line 129
    .line 130
    check-cast p1, Llb1/e;

    .line 131
    .line 132
    goto :goto_b

    .line 133
    :cond_e
    move-object p1, v1

    .line 134
    :goto_b
    if-eqz p1, :cond_f

    .line 135
    .line 136
    invoke-virtual {p1}, Llb1/e;->d()Lq21/b;

    .line 137
    .line 138
    .line 139
    move-result-object p1

    .line 140
    goto :goto_c

    .line 141
    :cond_f
    move-object p1, v1

    .line 142
    :goto_c
    check-cast p1, Lq21/e;

    .line 143
    .line 144
    invoke-virtual {p1}, Lq21/e;->a()Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;

    .line 145
    .line 146
    .line 147
    move-result-object p1

    .line 148
    if-eqz v2, :cond_10

    .line 149
    .line 150
    check-cast p2, Llb1/e;

    .line 151
    .line 152
    goto :goto_d

    .line 153
    :cond_10
    move-object p2, v1

    .line 154
    :goto_d
    if-eqz p2, :cond_11

    .line 155
    .line 156
    invoke-virtual {p2}, Llb1/e;->d()Lq21/b;

    .line 157
    .line 158
    .line 159
    move-result-object v1

    .line 160
    :cond_11
    check-cast v1, Lq21/e;

    .line 161
    .line 162
    invoke-virtual {v1}, Lq21/e;->a()Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;

    .line 163
    .line 164
    .line 165
    move-result-object p2

    .line 166
    if-ne p1, p2, :cond_12

    .line 167
    .line 168
    return v3

    .line 169
    :cond_12
    return v4

    .line 170
    :cond_13
    instance-of v0, p1, Lkb1/q;

    .line 171
    .line 172
    if-eqz v0, :cond_14

    .line 173
    .line 174
    instance-of v0, p2, Lkb1/q;

    .line 175
    .line 176
    if-eqz v0, :cond_14

    .line 177
    .line 178
    check-cast p1, Lkb1/q;

    .line 179
    .line 180
    invoke-virtual {p1}, Lkb1/q;->d()Lmb1/c;

    .line 181
    .line 182
    .line 183
    move-result-object p1

    .line 184
    check-cast p2, Lkb1/q;

    .line 185
    .line 186
    invoke-virtual {p2}, Lkb1/q;->d()Lmb1/c;

    .line 187
    .line 188
    .line 189
    move-result-object p2

    .line 190
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 191
    .line 192
    .line 193
    move-result p1

    .line 194
    return p1

    .line 195
    :cond_14
    instance-of v0, p1, Lkb1/s;

    .line 196
    .line 197
    if-eqz v0, :cond_15

    .line 198
    .line 199
    instance-of v0, p2, Lkb1/s;

    .line 200
    .line 201
    if-eqz v0, :cond_15

    .line 202
    .line 203
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 204
    .line 205
    .line 206
    move-result p1

    .line 207
    return p1

    .line 208
    :cond_15
    instance-of v0, p1, Lkb1/r;

    .line 209
    .line 210
    if-eqz v0, :cond_16

    .line 211
    .line 212
    instance-of v0, p2, Lkb1/r;

    .line 213
    .line 214
    if-eqz v0, :cond_16

    .line 215
    .line 216
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 217
    .line 218
    .line 219
    move-result p1

    .line 220
    return p1

    .line 221
    :cond_16
    instance-of v0, p1, Llb1/f;

    .line 222
    .line 223
    if-eqz v0, :cond_17

    .line 224
    .line 225
    instance-of v0, p2, Llb1/f;

    .line 226
    .line 227
    if-eqz v0, :cond_17

    .line 228
    .line 229
    check-cast p1, Llb1/f;

    .line 230
    .line 231
    invoke-virtual {p1}, Llb1/f;->d()Lq21/b;

    .line 232
    .line 233
    .line 234
    move-result-object p1

    .line 235
    check-cast p2, Llb1/f;

    .line 236
    .line 237
    invoke-virtual {p2}, Llb1/f;->d()Lq21/b;

    .line 238
    .line 239
    .line 240
    move-result-object p2

    .line 241
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 242
    .line 243
    .line 244
    move-result p1

    .line 245
    return p1

    .line 246
    :cond_17
    return v4
.end method
