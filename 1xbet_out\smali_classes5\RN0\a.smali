.class public final LRN0/a;
.super Ljava/lang/Object;


# static fields
.field public static allTimeFifaPlace:I = 0x7f0a00eb

.field public static arrowIcon:I = 0x7f0a0114

.field public static bottomShimmer:I = 0x7f0a0267

.field public static center:I = 0x7f0a03f4

.field public static centerLine:I = 0x7f0a03f8

.field public static chartView:I = 0x7f0a042c

.field public static contentBackground:I = 0x7f0a057c

.field public static emptyView:I = 0x7f0a0710

.field public static fifaPlace:I = 0x7f0a07c8

.field public static firstBlock:I = 0x7f0a07ed

.field public static flShimmer:I = 0x7f0a089e

.field public static flStatusView:I = 0x7f0a08a2

.field public static footballTableHeader:I = 0x7f0a08c4

.field public static fourthBlockSubtitle:I = 0x7f0a08cf

.field public static fourthBlockTitle:I = 0x7f0a08d0

.field public static gContent:I = 0x7f0a08f9

.field public static gShimmers:I = 0x7f0a0902

.field public static gameCount:I = 0x7f0a0918

.field public static goalCount:I = 0x7f0a0970

.field public static groupContent:I = 0x7f0a099c

.field public static guideline1:I = 0x7f0a0a06

.field public static guideline2:I = 0x7f0a0a07

.field public static guideline3:I = 0x7f0a0a08

.field public static guideline4:I = 0x7f0a0a09

.field public static guideline5:I = 0x7f0a0a0a

.field public static guideline6:I = 0x7f0a0a0c

.field public static header:I = 0x7f0a0a67

.field public static ivCountryIcon:I = 0x7f0a0c21

.field public static ivGameBackground:I = 0x7f0a0c74

.field public static ivPlayer:I = 0x7f0a0cc7

.field public static ivTeamOne:I = 0x7f0a0d2d

.field public static ivTeamTwo:I = 0x7f0a0d3a

.field public static layout:I = 0x7f0a0dd1

.field public static llFilterShimmer:I = 0x7f0a0e73

.field public static llFirstBlockCards:I = 0x7f0a0e74

.field public static llForecastContainer:I = 0x7f0a0e77

.field public static llSecondBlockCards:I = 0x7f0a0e8e

.field public static llTeamOneCardContainer:I = 0x7f0a0e9a

.field public static llTeamTwoCardContainer:I = 0x7f0a0e9d

.field public static llThirdBlockCards:I = 0x7f0a0ea2

.field public static loader:I = 0x7f0a0ec3

.field public static lottie:I = 0x7f0a0eeb

.field public static lottieEmptyView:I = 0x7f0a0eef

.field public static menuShimmer:I = 0x7f0a0f51

.field public static navigationBar:I = 0x7f0a0faf

.field public static oneTeamCard:I = 0x7f0a1008

.field public static playerAge:I = 0x7f0a10b2

.field public static playerName:I = 0x7f0a10c2

.field public static playerNumber:I = 0x7f0a10c3

.field public static recyclerView:I = 0x7f0a11ad

.field public static redCards:I = 0x7f0a11c3

.field public static rvContent:I = 0x7f0a126b

.field public static rvFilters:I = 0x7f0a1272

.field public static rvMenuList:I = 0x7f0a128d

.field public static secondBlock:I = 0x7f0a1325

.field public static segmentedGroup:I = 0x7f0a13a6

.field public static segmentedGroupContainer:I = 0x7f0a13a7

.field public static segmentsShimmer:I = 0x7f0a13ab

.field public static separator:I = 0x7f0a13c7

.field public static shadow:I = 0x7f0a13f6

.field public static shimmer:I = 0x7f0a1400

.field public static shimmerFirst:I = 0x7f0a1427

.field public static shimmerFourth:I = 0x7f0a142b

.field public static shimmerGroup:I = 0x7f0a143a

.field public static shimmerSecond:I = 0x7f0a145e

.field public static shimmerThird:I = 0x7f0a1474

.field public static shimmerView1:I = 0x7f0a147e

.field public static shimmerView2:I = 0x7f0a147f

.field public static shimmerView3:I = 0x7f0a1480

.field public static shimmers:I = 0x7f0a148b

.field public static staticNavigationBar:I = 0x7f0a15f5

.field public static tableHeader:I = 0x7f0a1686

.field public static tableHeaderContent:I = 0x7f0a1687

.field public static tabsShimmer:I = 0x7f0a168d

.field public static teamCardView:I = 0x7f0a16d0

.field public static teamMenuViewPager:I = 0x7f0a16dd

.field public static teamsLayout:I = 0x7f0a1702

.field public static thirdBlock:I = 0x7f0a1797

.field public static toolbar:I = 0x7f0a183e

.field public static topShimmer:I = 0x7f0a1883

.field public static tvAveragePlaceValue:I = 0x7f0a194b

.field public static tvCurrentPlaceValue:I = 0x7f0a1a03

.field public static tvDate:I = 0x7f0a1a0b

.field public static tvHeader:I = 0x7f0a1ace

.field public static tvLeft:I = 0x7f0a1af4

.field public static tvLine:I = 0x7f0a1af9

.field public static tvMenuTitle:I = 0x7f0a1b1d

.field public static tvName:I = 0x7f0a1b31

.field public static tvPlayerName:I = 0x7f0a1b83

.field public static tvRight:I = 0x7f0a1bda

.field public static tvSubTitle:I = 0x7f0a1c5e

.field public static tvTeamOne:I = 0x7f0a1c78

.field public static tvTeamTwo:I = 0x7f0a1c81

.field public static tvTitle:I = 0x7f0a1cac

.field public static tvTransferType:I = 0x7f0a1cd2

.field public static view15:I = 0x7f0a1f11

.field public static viewEmpty1:I = 0x7f0a1f3e

.field public static viewPagerTabs:I = 0x7f0a1f63

.field public static viewPagerTabsContainer:I = 0x7f0a1f64

.field public static yellowCards:I = 0x7f0a2021


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
