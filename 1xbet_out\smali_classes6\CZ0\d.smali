.class public final LCZ0/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0004\u001a/\u0010\u0006\u001a\u00020\u00042\u0008\u0008\u0002\u0010\u0001\u001a\u00020\u00002\u0014\u0008\u0002\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00040\u0002H\u0007\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "Landroidx/compose/ui/l;",
        "modifier",
        "Lkotlin/Function1;",
        "Lorg/xbet/uikit/components/bottombar/BottomBar;",
        "",
        "update",
        "d",
        "(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V",
        "uikit_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lorg/xbet/uikit/components/bottombar/BottomBar;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, LCZ0/d;->e(Lorg/xbet/uikit/components/bottombar/BottomBar;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, LCZ0/d;->g(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/content/Context;)Lorg/xbet/uikit/components/bottombar/BottomBar;
    .locals 0

    .line 1
    invoke-static {p0}, LCZ0/d;->f(Landroid/content/Context;)Lorg/xbet/uikit/components/bottombar/BottomBar;

    move-result-object p0

    return-object p0
.end method

.method public static final d(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/l;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/uikit/components/bottombar/BottomBar;",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .annotation runtime LlZ0/a;
    .end annotation

    .line 1
    const v0, 0x2ac86be7

    .line 2
    .line 3
    .line 4
    invoke-interface {p2, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 5
    .line 6
    .line 7
    move-result-object v4

    .line 8
    and-int/lit8 p2, p4, 0x1

    .line 9
    .line 10
    if-eqz p2, :cond_0

    .line 11
    .line 12
    or-int/lit8 v1, p3, 0x6

    .line 13
    .line 14
    goto :goto_1

    .line 15
    :cond_0
    and-int/lit8 v1, p3, 0x6

    .line 16
    .line 17
    if-nez v1, :cond_2

    .line 18
    .line 19
    invoke-interface {v4, p0}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 20
    .line 21
    .line 22
    move-result v1

    .line 23
    if-eqz v1, :cond_1

    .line 24
    .line 25
    const/4 v1, 0x4

    .line 26
    goto :goto_0

    .line 27
    :cond_1
    const/4 v1, 0x2

    .line 28
    :goto_0
    or-int/2addr v1, p3

    .line 29
    goto :goto_1

    .line 30
    :cond_2
    move v1, p3

    .line 31
    :goto_1
    and-int/lit8 v2, p4, 0x2

    .line 32
    .line 33
    if-eqz v2, :cond_3

    .line 34
    .line 35
    or-int/lit8 v1, v1, 0x30

    .line 36
    .line 37
    goto :goto_3

    .line 38
    :cond_3
    and-int/lit8 v3, p3, 0x30

    .line 39
    .line 40
    if-nez v3, :cond_5

    .line 41
    .line 42
    invoke-interface {v4, p1}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 43
    .line 44
    .line 45
    move-result v3

    .line 46
    if-eqz v3, :cond_4

    .line 47
    .line 48
    const/16 v3, 0x20

    .line 49
    .line 50
    goto :goto_2

    .line 51
    :cond_4
    const/16 v3, 0x10

    .line 52
    .line 53
    :goto_2
    or-int/2addr v1, v3

    .line 54
    :cond_5
    :goto_3
    and-int/lit8 v3, v1, 0x13

    .line 55
    .line 56
    const/16 v5, 0x12

    .line 57
    .line 58
    if-ne v3, v5, :cond_7

    .line 59
    .line 60
    invoke-interface {v4}, Landroidx/compose/runtime/j;->c()Z

    .line 61
    .line 62
    .line 63
    move-result v3

    .line 64
    if-nez v3, :cond_6

    .line 65
    .line 66
    goto :goto_4

    .line 67
    :cond_6
    invoke-interface {v4}, Landroidx/compose/runtime/j;->n()V

    .line 68
    .line 69
    .line 70
    goto :goto_5

    .line 71
    :cond_7
    :goto_4
    if-eqz p2, :cond_8

    .line 72
    .line 73
    sget-object p0, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 74
    .line 75
    :cond_8
    const p2, 0x6e3c21fe

    .line 76
    .line 77
    .line 78
    if-eqz v2, :cond_a

    .line 79
    .line 80
    invoke-interface {v4, p2}, Landroidx/compose/runtime/j;->t(I)V

    .line 81
    .line 82
    .line 83
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 84
    .line 85
    .line 86
    move-result-object p1

    .line 87
    sget-object v2, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 88
    .line 89
    invoke-virtual {v2}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 90
    .line 91
    .line 92
    move-result-object v2

    .line 93
    if-ne p1, v2, :cond_9

    .line 94
    .line 95
    new-instance p1, LCZ0/a;

    .line 96
    .line 97
    invoke-direct {p1}, LCZ0/a;-><init>()V

    .line 98
    .line 99
    .line 100
    invoke-interface {v4, p1}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 101
    .line 102
    .line 103
    :cond_9
    check-cast p1, Lkotlin/jvm/functions/Function1;

    .line 104
    .line 105
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 106
    .line 107
    .line 108
    :cond_a
    move-object v3, p1

    .line 109
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 110
    .line 111
    .line 112
    move-result p1

    .line 113
    if-eqz p1, :cond_b

    .line 114
    .line 115
    const/4 p1, -0x1

    .line 116
    const-string v2, "org.xbet.uikit.components.bottombar.compose.BottomBar (BottomBar.kt:17)"

    .line 117
    .line 118
    invoke-static {v0, v1, p1, v2}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 119
    .line 120
    .line 121
    :cond_b
    invoke-interface {v4, p2}, Landroidx/compose/runtime/j;->t(I)V

    .line 122
    .line 123
    .line 124
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 125
    .line 126
    .line 127
    move-result-object p1

    .line 128
    sget-object p2, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 129
    .line 130
    invoke-virtual {p2}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    move-result-object p2

    .line 134
    if-ne p1, p2, :cond_c

    .line 135
    .line 136
    new-instance p1, LCZ0/b;

    .line 137
    .line 138
    invoke-direct {p1}, LCZ0/b;-><init>()V

    .line 139
    .line 140
    .line 141
    invoke-interface {v4, p1}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 142
    .line 143
    .line 144
    :cond_c
    check-cast p1, Lkotlin/jvm/functions/Function1;

    .line 145
    .line 146
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 147
    .line 148
    .line 149
    shl-int/lit8 p2, v1, 0x3

    .line 150
    .line 151
    and-int/lit8 v0, p2, 0x70

    .line 152
    .line 153
    or-int/lit8 v0, v0, 0x6

    .line 154
    .line 155
    and-int/lit16 p2, p2, 0x380

    .line 156
    .line 157
    or-int v5, v0, p2

    .line 158
    .line 159
    const/4 v6, 0x0

    .line 160
    move-object v2, p0

    .line 161
    move-object v1, p1

    .line 162
    invoke-static/range {v1 .. v6}, Landroidx/compose/ui/viewinterop/AndroidView_androidKt;->a(Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 163
    .line 164
    .line 165
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 166
    .line 167
    .line 168
    move-result p0

    .line 169
    if-eqz p0, :cond_d

    .line 170
    .line 171
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 172
    .line 173
    .line 174
    :cond_d
    move-object p0, v2

    .line 175
    move-object p1, v3

    .line 176
    :goto_5
    invoke-interface {v4}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 177
    .line 178
    .line 179
    move-result-object p2

    .line 180
    if-eqz p2, :cond_e

    .line 181
    .line 182
    new-instance v0, LCZ0/c;

    .line 183
    .line 184
    invoke-direct {v0, p0, p1, p3, p4}, LCZ0/c;-><init>(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;II)V

    .line 185
    .line 186
    .line 187
    invoke-interface {p2, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 188
    .line 189
    .line 190
    :cond_e
    return-void
.end method

.method public static final e(Lorg/xbet/uikit/components/bottombar/BottomBar;)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final f(Landroid/content/Context;)Lorg/xbet/uikit/components/bottombar/BottomBar;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/bottombar/BottomBar;

    .line 2
    .line 3
    const/4 v4, 0x6

    .line 4
    const/4 v5, 0x0

    .line 5
    const/4 v2, 0x0

    .line 6
    const/4 v3, 0x0

    .line 7
    move-object v1, p0

    .line 8
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/bottombar/BottomBar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method

.method public static final g(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    or-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    invoke-static {p2}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    invoke-static {p0, p1, p4, p2, p3}, LCZ0/d;->d(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method
