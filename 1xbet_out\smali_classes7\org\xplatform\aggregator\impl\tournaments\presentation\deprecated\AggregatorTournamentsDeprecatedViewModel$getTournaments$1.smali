.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.deprecated.AggregatorTournamentsDeprecatedViewModel$getTournaments$1"
    f = "AggregatorTournamentsDeprecatedViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->A4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/util/List<",
        "+",
        "Lorg/xplatform/banners/api/domain/models/BannerModel;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\u0010\u0000\u001a\u00020\u00012\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "value",
        "",
        "Lorg/xplatform/banners/api/domain/models/BannerModel;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;->invoke(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_2

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Ljava/util/List;

    .line 14
    .line 15
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    if-nez v0, :cond_1

    .line 20
    .line 21
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 22
    .line 23
    new-instance v1, Ljava/util/ArrayList;

    .line 24
    .line 25
    const/16 v2, 0xa

    .line 26
    .line 27
    invoke-static {p1, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 28
    .line 29
    .line 30
    move-result v2

    .line 31
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 32
    .line 33
    .line 34
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 39
    .line 40
    .line 41
    move-result v2

    .line 42
    if-eqz v2, :cond_0

    .line 43
    .line 44
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    move-result-object v2

    .line 48
    check-cast v2, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 49
    .line 50
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->p4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;)Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    invoke-static {v2, v3}, Ljb1/l;->b(Lorg/xplatform/banners/api/domain/models/BannerModel;Ljava/lang/String;)Lkb1/b;

    .line 55
    .line 56
    .line 57
    move-result-object v2

    .line 58
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 59
    .line 60
    .line 61
    goto :goto_0

    .line 62
    :cond_0
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 63
    .line 64
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->o4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;)Lkotlinx/coroutines/flow/V;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$d;

    .line 69
    .line 70
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$d;-><init>(Ljava/util/List;)V

    .line 71
    .line 72
    .line 73
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 74
    .line 75
    .line 76
    goto :goto_1

    .line 77
    :cond_1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 78
    .line 79
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->t4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;)V

    .line 80
    .line 81
    .line 82
    :goto_1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 83
    .line 84
    const/4 v0, 0x1

    .line 85
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->s4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;Z)V

    .line 86
    .line 87
    .line 88
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 89
    .line 90
    return-object p1

    .line 91
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 92
    .line 93
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 94
    .line 95
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 96
    .line 97
    .line 98
    throw p1
.end method
