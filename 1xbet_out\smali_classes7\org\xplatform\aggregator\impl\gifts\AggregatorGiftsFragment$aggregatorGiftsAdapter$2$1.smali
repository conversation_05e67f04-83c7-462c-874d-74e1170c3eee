.class final synthetic Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$1;
.super Lkotlin/jvm/internal/FunctionReferenceImpl;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;-><init>()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1001
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/FunctionReferenceImpl;",
        "LOc/n<",
        "Lorg/xplatform/aggregator/api/model/PartitionType;",
        "Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;",
        "Lha1/b;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>(Ljava/lang/Object;)V
    .locals 7

    const-string v5, "setGiftsStateButton(Lorg/xplatform/aggregator/api/model/PartitionType;Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;Lorg/xplatform/aggregator/impl/gifts/containers/CallbackClickModelContainer;)V"

    const/4 v6, 0x0

    const/4 v1, 0x3

    const-class v3, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    const-string v4, "setGiftsStateButton"

    move-object v0, p0

    move-object v2, p1

    invoke-direct/range {v0 .. v6}, Lkotlin/jvm/internal/FunctionReferenceImpl;-><init>(ILjava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/api/model/PartitionType;

    check-cast p2, Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    check-cast p3, Lha1/b;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$1;->invoke(Lorg/xplatform/aggregator/api/model/PartitionType;Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;Lha1/b;)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/api/model/PartitionType;Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;Lha1/b;)V
    .locals 1

    .line 2
    iget-object v0, p0, Lkotlin/jvm/internal/CallableReference;->receiver:Ljava/lang/Object;

    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    invoke-virtual {v0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->X5(Lorg/xplatform/aggregator/api/model/PartitionType;Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;Lha1/b;)V

    return-void
.end method
