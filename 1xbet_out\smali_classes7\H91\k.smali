.class public final LH91/k;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0001\u001a\u00020\u0000*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0001\u0010\u0002\u00a8\u0006\u0003"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;",
        "a",
        "(Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;)Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;)Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;
    .locals 5
    .param p0    # Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;->getId()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;->getName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;->getId()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    const-string v4, "0"

    .line 16
    .line 17
    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result v3

    .line 21
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;->e0()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object p0

    .line 25
    invoke-direct {v0, v1, v2, v3, p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;-><init>(Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;)V

    .line 26
    .line 27
    .line 28
    return-object v0
.end method
