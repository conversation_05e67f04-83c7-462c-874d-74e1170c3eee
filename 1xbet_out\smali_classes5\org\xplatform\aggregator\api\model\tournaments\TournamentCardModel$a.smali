.class public final Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\r\u0008\u0086\u0008\u0018\u00002\u00020\u0001B/\u0012\u0008\u0010\u0003\u001a\u0004\u0018\u00010\u0002\u0012\u0008\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0010\u0006\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0010\u0008\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0010\u0010\u000b\u001a\u00020\u0007H\u00d6\u0001\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0010\u0010\u000e\u001a\u00020\rH\u00d6\u0001\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u001a\u0010\u0012\u001a\u00020\u00112\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0012\u0010\u0013R\u0019\u0010\u0003\u001a\u0004\u0018\u00010\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0014\u0010\u0015\u001a\u0004\u0008\u0016\u0010\u0017R\u0019\u0010\u0005\u001a\u0004\u0018\u00010\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0018\u0010\u0019\u001a\u0004\u0008\u001a\u0010\u001bR\u0019\u0010\u0006\u001a\u0004\u0018\u00010\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0016\u0010\u0019\u001a\u0004\u0008\u0014\u0010\u001bR\u0019\u0010\u0008\u001a\u0004\u0018\u00010\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010\u001d\u001a\u0004\u0008\u0018\u0010\u000c\u00a8\u0006\u001e"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;",
        "",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;",
        "type",
        "Ljava/util/Date;",
        "eventDate",
        "expireDate",
        "",
        "title",
        "<init>",
        "(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;Ljava/util/Date;Ljava/util/Date;Ljava/lang/String;)V",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;",
        "c",
        "()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;",
        "b",
        "Ljava/util/Date;",
        "getEventDate",
        "()Ljava/util/Date;",
        "d",
        "Ljava/lang/String;",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;

.field public final b:Ljava/util/Date;

.field public final c:Ljava/util/Date;

.field public final d:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;Ljava/util/Date;Ljava/util/Date;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->a:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->b:Ljava/util/Date;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->c:Ljava/util/Date;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->d:Ljava/lang/String;

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public final a()Ljava/util/Date;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->c:Ljava/util/Date;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->d:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->a:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->a:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->a:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;

    if-eq v1, v3, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->b:Ljava/util/Date;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->b:Ljava/util/Date;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->c:Ljava/util/Date;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->c:Ljava/util/Date;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_4

    return v2

    :cond_4
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->d:Ljava/lang/String;

    iget-object p1, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->d:Ljava/lang/String;

    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_5

    return v2

    :cond_5
    return v0
.end method

.method public hashCode()I
    .locals 3

    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->a:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    :goto_0
    mul-int/lit8 v0, v0, 0x1f

    iget-object v2, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->b:Ljava/util/Date;

    if-nez v2, :cond_1

    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    invoke-virtual {v2}, Ljava/util/Date;->hashCode()I

    move-result v2

    :goto_1
    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-object v2, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->c:Ljava/util/Date;

    if-nez v2, :cond_2

    const/4 v2, 0x0

    goto :goto_2

    :cond_2
    invoke-virtual {v2}, Ljava/util/Date;->hashCode()I

    move-result v2

    :goto_2
    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-object v2, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->d:Ljava/lang/String;

    if-nez v2, :cond_3

    goto :goto_3

    :cond_3
    invoke-virtual {v2}, Ljava/lang/String;->hashCode()I

    move-result v1

    :goto_3
    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 6
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->a:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->b:Ljava/util/Date;

    iget-object v2, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->c:Ljava/util/Date;

    iget-object v3, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->d:Ljava/lang/String;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "Counter(type="

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", eventDate="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", expireDate="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", title="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
