.class final Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.new_games.presentation.NewGamesFolderFragment$onObserveData$5"
    f = "NewGamesFolderFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
        "event",
        "",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;->invoke(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_5

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    .line 16
    .line 17
    const/4 v1, 0x0

    .line 18
    invoke-static {v0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->B3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Z)V

    .line 19
    .line 20
    .line 21
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$a;

    .line 22
    .line 23
    if-eqz v0, :cond_0

    .line 24
    .line 25
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    .line 26
    .line 27
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->z3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V

    .line 28
    .line 29
    .line 30
    goto :goto_0

    .line 31
    :cond_0
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$d;

    .line 32
    .line 33
    if-eqz v0, :cond_1

    .line 34
    .line 35
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    .line 36
    .line 37
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->D3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V

    .line 38
    .line 39
    .line 40
    goto :goto_0

    .line 41
    :cond_1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$c;

    .line 42
    .line 43
    if-eqz v0, :cond_2

    .line 44
    .line 45
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    .line 46
    .line 47
    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$c;

    .line 48
    .line 49
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$c;->a()Lkotlin/jvm/functions/Function0;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->C3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lkotlin/jvm/functions/Function0;)V

    .line 54
    .line 55
    .line 56
    goto :goto_0

    .line 57
    :cond_2
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$b;

    .line 58
    .line 59
    if-eqz v0, :cond_3

    .line 60
    .line 61
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    .line 62
    .line 63
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->d4()V

    .line 68
    .line 69
    .line 70
    goto :goto_0

    .line 71
    :cond_3
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$e;

    .line 72
    .line 73
    if-eqz v0, :cond_4

    .line 74
    .line 75
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    .line 76
    .line 77
    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$e;

    .line 78
    .line 79
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$e;->a()Lorg/xplatform/aggregator/api/model/Game;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->A3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xplatform/aggregator/api/model/Game;)V

    .line 84
    .line 85
    .line 86
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 87
    .line 88
    return-object p1

    .line 89
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 90
    .line 91
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 92
    .line 93
    .line 94
    throw p1

    .line 95
    :cond_5
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 96
    .line 97
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 98
    .line 99
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 100
    .line 101
    .line 102
    throw p1
.end method
