.class public final synthetic Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/D;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetSimpleViewModel;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetSimpleViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/D;->a:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetSimpleViewModel;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/D;->a:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetSimpleViewModel;

    check-cast p1, Ljava/lang/Throwable;

    check-cast p2, Ljava/lang/String;

    invoke-static {v0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetSimpleViewModel;->p3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetSimpleViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
