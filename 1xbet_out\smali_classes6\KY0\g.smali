.class public final LKY0/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LKY0/h;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LKY0/g$a;,
        LKY0/g$b;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "LKY0/h<",
        "LKY0/c;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0086\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0011\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0007\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0007\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0002\u001a!B5\u0012\u0012\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u0003\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u0012\u000e\u0008\u0002\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bBC\u0008\u0016\u0012\u001e\u0010\u0005\u001a\u0010\u0012\u000c\u0008\u0001\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u000c\"\u0008\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u0012\u000e\u0008\u0002\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u0008\u00a2\u0006\u0004\u0008\n\u0010\rJ!\u0010\u0010\u001a\u00020\u000f2\u0012\u0010\u000e\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u0003\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J-\u0010\u0012\u001a\u00020\u000f2\u001e\u0010\u000e\u001a\u0010\u0012\u000c\u0008\u0001\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u000c\"\u0008\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u001f\u0010\u001a\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0019\u001a\u00020\u0018H\u0016\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJI\u0010!\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\u00162\u000c\u0010\u001d\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u001c2\u000e\u0010\u001e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00020\u001c2\u0012\u0010 \u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u000f0\u001fH\u0016\u00a2\u0006\u0004\u0008!\u0010\"J\u0017\u0010#\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\u0016H\u0016\u00a2\u0006\u0004\u0008#\u0010$J\u0017\u0010&\u001a\u00020%2\u0006\u0010\u0017\u001a\u00020\u0016H\u0016\u00a2\u0006\u0004\u0008&\u0010\'J9\u0010)\u001a\u00020\u000f2\u0006\u0010\u0019\u001a\u00020\u00182\u0012\u0010(\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u000f0\u001f2\u000c\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u0008H\u0002\u00a2\u0006\u0004\u0008)\u0010*JC\u0010.\u001a\u00020\u00022\u0012\u0010\u000e\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00032\u000e\u0008\u0002\u0010,\u001a\u0008\u0012\u0004\u0012\u00020\u00180+2\u000e\u0008\u0002\u0010-\u001a\u0008\u0012\u0004\u0012\u00020\u00180+H\u0002\u00a2\u0006\u0004\u0008.\u0010/R\u001a\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u00100R\u0018\u00102\u001a\u0004\u0018\u00010\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001a\u00101R\u0016\u00105\u001a\u0002038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0014\u00104R0\u0010:\u001a\u001e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020706j\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u000207`88\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u00109R\u0014\u0010<\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010;RD\u0010\u000e\u001a2\u0012\u0014\u0012\u0012\u0012\u0004\u0012\u00020\u00040=j\u0008\u0012\u0004\u0012\u00020\u0004`>0=j\u0018\u0012\u0014\u0012\u0012\u0012\u0004\u0012\u00020\u00040=j\u0008\u0012\u0004\u0012\u00020\u0004`>`>8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008?\u0010@\u00a8\u0006A"
    }
    d2 = {
        "LKY0/g;",
        "LKY0/h;",
        "LKY0/c;",
        "",
        "LKY0/a;",
        "entryCollections",
        "Ljava/util/concurrent/Executor;",
        "backgroundExecutor",
        "LLY0/b;",
        "diffProcessor",
        "<init>",
        "(Ljava/util/List;Ljava/util/concurrent/Executor;LLY0/b;)V",
        "",
        "([Ljava/util/List;Ljava/util/concurrent/Executor;LLY0/b;)V",
        "entries",
        "",
        "n",
        "(Ljava/util/List;)V",
        "o",
        "([Ljava/util/List;)V",
        "c",
        "()LKY0/c;",
        "",
        "key",
        "",
        "progress",
        "b",
        "(Ljava/lang/Object;F)V",
        "Lkotlin/Function0;",
        "updateListener",
        "getOldModel",
        "Lkotlin/Function1;",
        "onModel",
        "a",
        "(Ljava/lang/Object;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;)V",
        "e",
        "(Ljava/lang/Object;)V",
        "",
        "d",
        "(Ljava/lang/Object;)Z",
        "modelReceiver",
        "l",
        "(FLkotlin/jvm/functions/Function1;LLY0/b;)V",
        "LTc/d;",
        "yRange",
        "stackedPositiveYRange",
        "i",
        "(Ljava/util/List;LTc/d;LTc/d;)LKY0/c;",
        "LLY0/b;",
        "LKY0/c;",
        "cachedModel",
        "",
        "I",
        "entriesHashCode",
        "Ljava/util/HashMap;",
        "LKY0/g$b;",
        "Lkotlin/collections/HashMap;",
        "Ljava/util/HashMap;",
        "updateReceivers",
        "Ljava/util/concurrent/Executor;",
        "executor",
        "Ljava/util/ArrayList;",
        "Lkotlin/collections/ArrayList;",
        "f",
        "Ljava/util/ArrayList;",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LLY0/b;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LLY0/b<",
            "LKY0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:LKY0/c;

.field public c:I

.field public final d:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/Object;",
            "LKY0/g$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Ljava/util/concurrent/Executor;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Ljava/util/ArrayList<",
            "LKY0/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Ljava/util/List;Ljava/util/concurrent/Executor;LLY0/b;)V
    .locals 0
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/concurrent/Executor;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LLY0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "+",
            "LKY0/a;",
            ">;>;",
            "Ljava/util/concurrent/Executor;",
            "LLY0/b<",
            "LKY0/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p3, p0, LKY0/g;->a:LLY0/b;

    .line 3
    new-instance p3, Ljava/util/HashMap;

    invoke-direct {p3}, Ljava/util/HashMap;-><init>()V

    iput-object p3, p0, LKY0/g;->d:Ljava/util/HashMap;

    .line 4
    iput-object p2, p0, LKY0/g;->e:Ljava/util/concurrent/Executor;

    .line 5
    new-instance p2, Ljava/util/ArrayList;

    invoke-direct {p2}, Ljava/util/ArrayList;-><init>()V

    iput-object p2, p0, LKY0/g;->f:Ljava/util/ArrayList;

    .line 6
    invoke-virtual {p0, p1}, LKY0/g;->n(Ljava/util/List;)V

    return-void
.end method

.method public synthetic constructor <init>(Ljava/util/List;Ljava/util/concurrent/Executor;LLY0/b;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 1

    and-int/lit8 p5, p4, 0x2

    const/4 v0, 0x4

    if-eqz p5, :cond_0

    .line 7
    invoke-static {v0}, Ljava/util/concurrent/Executors;->newFixedThreadPool(I)Ljava/util/concurrent/ExecutorService;

    move-result-object p2

    :cond_0
    and-int/2addr p4, v0

    if-eqz p4, :cond_1

    .line 8
    new-instance p3, LLY0/a;

    invoke-direct {p3}, LLY0/a;-><init>()V

    .line 9
    :cond_1
    invoke-direct {p0, p1, p2, p3}, LKY0/g;-><init>(Ljava/util/List;Ljava/util/concurrent/Executor;LLY0/b;)V

    return-void
.end method

.method public constructor <init>([Ljava/util/List;Ljava/util/concurrent/Executor;LLY0/b;)V
    .locals 0
    .param p1    # [Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/concurrent/Executor;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LLY0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/util/List<",
            "+",
            "LKY0/a;",
            ">;",
            "Ljava/util/concurrent/Executor;",
            "LLY0/b<",
            "LKY0/a;",
            ">;)V"
        }
    .end annotation

    .line 13
    invoke-static {p1}, Lkotlin/collections/r;->K1([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    invoke-direct {p0, p1, p2, p3}, LKY0/g;-><init>(Ljava/util/List;Ljava/util/concurrent/Executor;LLY0/b;)V

    return-void
.end method

.method public synthetic constructor <init>([Ljava/util/List;Ljava/util/concurrent/Executor;LLY0/b;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 1

    and-int/lit8 p5, p4, 0x2

    const/4 v0, 0x4

    if-eqz p5, :cond_0

    .line 10
    invoke-static {v0}, Ljava/util/concurrent/Executors;->newFixedThreadPool(I)Ljava/util/concurrent/ExecutorService;

    move-result-object p2

    :cond_0
    and-int/2addr p4, v0

    if-eqz p4, :cond_1

    .line 11
    new-instance p3, LLY0/a;

    invoke-direct {p3}, LLY0/a;-><init>()V

    .line 12
    :cond_1
    invoke-direct {p0, p1, p2, p3}, LKY0/g;-><init>([Ljava/util/List;Ljava/util/concurrent/Executor;LLY0/b;)V

    return-void
.end method

.method public static synthetic f(LKY0/g;FLkotlin/jvm/functions/Function1;LLY0/b;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, LKY0/g;->k(LKY0/g;FLkotlin/jvm/functions/Function1;LLY0/b;)V

    return-void
.end method

.method public static synthetic g(LKY0/g;ILKY0/g$b;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, LKY0/g;->p(LKY0/g;ILKY0/g$b;Ljava/util/List;)V

    return-void
.end method

.method public static synthetic h(LKY0/g;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LKY0/g;->m(LKY0/g;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V

    return-void
.end method

.method public static synthetic j(LKY0/g;Ljava/util/List;LTc/d;LTc/d;ILjava/lang/Object;)LKY0/c;
    .locals 2

    .line 1
    and-int/lit8 p5, p4, 0x2

    .line 2
    .line 3
    if-eqz p5, :cond_2

    .line 4
    .line 5
    invoke-static {p1}, Lkotlin/collections/w;->A(Ljava/lang/Iterable;)Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 10
    .line 11
    .line 12
    move-result-object p2

    .line 13
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 14
    .line 15
    .line 16
    move-result p5

    .line 17
    if-nez p5, :cond_0

    .line 18
    .line 19
    const/4 p2, 0x0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object p5

    .line 25
    check-cast p5, LKY0/a;

    .line 26
    .line 27
    invoke-interface {p5}, LKY0/a;->getY()F

    .line 28
    .line 29
    .line 30
    move-result p5

    .line 31
    move v0, p5

    .line 32
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 33
    .line 34
    .line 35
    move-result v1

    .line 36
    if-eqz v1, :cond_1

    .line 37
    .line 38
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    check-cast v1, LKY0/a;

    .line 43
    .line 44
    invoke-interface {v1}, LKY0/a;->getY()F

    .line 45
    .line 46
    .line 47
    move-result v1

    .line 48
    invoke-static {p5, v1}, Ljava/lang/Math;->min(FF)F

    .line 49
    .line 50
    .line 51
    move-result p5

    .line 52
    invoke-static {v0, v1}, Ljava/lang/Math;->max(FF)F

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    goto :goto_0

    .line 57
    :cond_1
    invoke-static {p5, v0}, LTc/i;->c(FF)LTc/d;

    .line 58
    .line 59
    .line 60
    move-result-object p2

    .line 61
    :goto_1
    if-nez p2, :cond_2

    .line 62
    .line 63
    const/4 p2, 0x0

    .line 64
    invoke-static {p2, p2}, LTc/i;->c(FF)LTc/d;

    .line 65
    .line 66
    .line 67
    move-result-object p2

    .line 68
    :cond_2
    and-int/lit8 p4, p4, 0x4

    .line 69
    .line 70
    if-eqz p4, :cond_3

    .line 71
    .line 72
    invoke-static {p1}, LKY0/b;->a(Ljava/lang/Iterable;)LTc/d;

    .line 73
    .line 74
    .line 75
    move-result-object p3

    .line 76
    :cond_3
    invoke-virtual {p0, p1, p2, p3}, LKY0/g;->i(Ljava/util/List;LTc/d;LTc/d;)LKY0/c;

    .line 77
    .line 78
    .line 79
    move-result-object p0

    .line 80
    return-object p0
.end method

.method public static final k(LKY0/g;FLkotlin/jvm/functions/Function1;LLY0/b;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, LKY0/g;->l(FLkotlin/jvm/functions/Function1;LLY0/b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final m(LKY0/g;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V
    .locals 1

    .line 1
    iget-object v0, p0, LKY0/g;->a:LLY0/b;

    .line 2
    .line 3
    invoke-interface {p1}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    check-cast p1, LKY0/c;

    .line 8
    .line 9
    if-eqz p1, :cond_0

    .line 10
    .line 11
    invoke-interface {p1}, LKY0/c;->r()Ljava/util/List;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    goto :goto_0

    .line 16
    :cond_0
    const/4 p1, 0x0

    .line 17
    :goto_0
    if-nez p1, :cond_1

    .line 18
    .line 19
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    :cond_1
    iget-object p0, p0, LKY0/g;->f:Ljava/util/ArrayList;

    .line 24
    .line 25
    invoke-interface {v0, p1, p0}, LLY0/b;->b(Ljava/util/List;Ljava/util/List;)V

    .line 26
    .line 27
    .line 28
    invoke-interface {p2}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public static final p(LKY0/g;ILKY0/g$b;Ljava/util/List;)V
    .locals 0

    .line 1
    iput p1, p0, LKY0/g;->c:I

    .line 2
    .line 3
    invoke-virtual {p2}, LKY0/g$b;->c()LLY0/b;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    invoke-virtual {p2}, LKY0/g$b;->d()Lkotlin/jvm/functions/Function0;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-interface {p1}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    check-cast p1, LKY0/c;

    .line 16
    .line 17
    if-eqz p1, :cond_0

    .line 18
    .line 19
    invoke-interface {p1}, LKY0/c;->r()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    goto :goto_0

    .line 24
    :cond_0
    const/4 p1, 0x0

    .line 25
    :goto_0
    if-nez p1, :cond_1

    .line 26
    .line 27
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    :cond_1
    invoke-interface {p0, p1, p3}, LLY0/b;->b(Ljava/util/List;Ljava/util/List;)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p2}, LKY0/g$b;->e()Lkotlin/jvm/functions/Function0;

    .line 35
    .line 36
    .line 37
    move-result-object p0

    .line 38
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    return-void
.end method


# virtual methods
.method public a(Ljava/lang/Object;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;)V
    .locals 3
    .param p1    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "+",
            "LKY0/c;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LKY0/c;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LKY0/g;->d:Ljava/util/HashMap;

    .line 2
    .line 3
    new-instance v1, LKY0/g$b;

    .line 4
    .line 5
    iget-object v2, p0, LKY0/g;->a:LLY0/b;

    .line 6
    .line 7
    invoke-direct {v1, p2, p4, v2, p3}, LKY0/g$b;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;LLY0/b;Lkotlin/jvm/functions/Function0;)V

    .line 8
    .line 9
    .line 10
    invoke-interface {v0, p1, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    iget-object p1, p0, LKY0/g;->e:Ljava/util/concurrent/Executor;

    .line 14
    .line 15
    new-instance p4, LKY0/f;

    .line 16
    .line 17
    invoke-direct {p4, p0, p3, p2}, LKY0/f;-><init>(LKY0/g;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V

    .line 18
    .line 19
    .line 20
    invoke-interface {p1, p4}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public b(Ljava/lang/Object;F)V
    .locals 3
    .param p1    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LKY0/g;->d:Ljava/util/HashMap;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    check-cast p1, LKY0/g$b;

    .line 8
    .line 9
    if-nez p1, :cond_0

    .line 10
    .line 11
    return-void

    .line 12
    :cond_0
    invoke-virtual {p1}, LKY0/g$b;->a()Lkotlin/jvm/functions/Function1;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-virtual {p1}, LKY0/g$b;->b()LLY0/b;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    iget-object v1, p0, LKY0/g;->e:Ljava/util/concurrent/Executor;

    .line 21
    .line 22
    new-instance v2, LKY0/e;

    .line 23
    .line 24
    invoke-direct {v2, p0, p2, v0, p1}, LKY0/e;-><init>(LKY0/g;FLkotlin/jvm/functions/Function1;LLY0/b;)V

    .line 25
    .line 26
    .line 27
    invoke-interface {v1, v2}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public c()LKY0/c;
    .locals 7
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LKY0/g;->b:LKY0/c;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v2, p0, LKY0/g;->f:Ljava/util/ArrayList;

    .line 6
    .line 7
    const/4 v5, 0x6

    .line 8
    const/4 v6, 0x0

    .line 9
    const/4 v3, 0x0

    .line 10
    const/4 v4, 0x0

    .line 11
    move-object v1, p0

    .line 12
    invoke-static/range {v1 .. v6}, LKY0/g;->j(LKY0/g;Ljava/util/List;LTc/d;LTc/d;ILjava/lang/Object;)LKY0/c;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iput-object v0, v1, LKY0/g;->b:LKY0/c;

    .line 17
    .line 18
    return-object v0

    .line 19
    :cond_0
    move-object v1, p0

    .line 20
    return-object v0
.end method

.method public d(Ljava/lang/Object;)Z
    .locals 1
    .param p1    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LKY0/g;->d:Ljava/util/HashMap;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Ljava/util/HashMap;->containsKey(Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public e(Ljava/lang/Object;)V
    .locals 1
    .param p1    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LKY0/g;->d:Ljava/util/HashMap;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Ljava/util/HashMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final i(Ljava/util/List;LTc/d;LTc/d;)LKY0/c;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "+",
            "LKY0/a;",
            ">;>;",
            "LTc/d<",
            "Ljava/lang/Float;",
            ">;",
            "LTc/d<",
            "Ljava/lang/Float;",
            ">;)",
            "LKY0/c;"
        }
    .end annotation

    .line 1
    invoke-static {p1}, Lkotlin/collections/w;->A(Ljava/lang/Iterable;)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    const/4 v2, 0x0

    .line 14
    if-nez v1, :cond_0

    .line 15
    .line 16
    move-object v0, v2

    .line 17
    goto :goto_1

    .line 18
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    check-cast v1, LKY0/a;

    .line 23
    .line 24
    invoke-interface {v1}, LKY0/a;->getX()F

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    move v3, v1

    .line 29
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 30
    .line 31
    .line 32
    move-result v4

    .line 33
    if-eqz v4, :cond_1

    .line 34
    .line 35
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v4

    .line 39
    check-cast v4, LKY0/a;

    .line 40
    .line 41
    invoke-interface {v4}, LKY0/a;->getX()F

    .line 42
    .line 43
    .line 44
    move-result v4

    .line 45
    invoke-static {v1, v4}, Ljava/lang/Math;->min(FF)F

    .line 46
    .line 47
    .line 48
    move-result v1

    .line 49
    invoke-static {v3, v4}, Ljava/lang/Math;->max(FF)F

    .line 50
    .line 51
    .line 52
    move-result v3

    .line 53
    goto :goto_0

    .line 54
    :cond_1
    invoke-static {v1, v3}, LTc/i;->c(FF)LTc/d;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    :goto_1
    const/4 v1, 0x0

    .line 59
    if-nez v0, :cond_2

    .line 60
    .line 61
    invoke-static {v1, v1}, LTc/i;->c(FF)LTc/d;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    :cond_2
    invoke-interface {v0}, LTc/e;->a()Ljava/lang/Comparable;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    check-cast v0, Ljava/lang/Number;

    .line 70
    .line 71
    invoke-virtual {v0}, Ljava/lang/Number;->floatValue()F

    .line 72
    .line 73
    .line 74
    move-result v5

    .line 75
    invoke-static {p1}, Lkotlin/collections/w;->A(Ljava/lang/Iterable;)Ljava/util/List;

    .line 76
    .line 77
    .line 78
    move-result-object v0

    .line 79
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 84
    .line 85
    .line 86
    move-result v3

    .line 87
    if-nez v3, :cond_3

    .line 88
    .line 89
    goto :goto_3

    .line 90
    :cond_3
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 91
    .line 92
    .line 93
    move-result-object v2

    .line 94
    check-cast v2, LKY0/a;

    .line 95
    .line 96
    invoke-interface {v2}, LKY0/a;->getX()F

    .line 97
    .line 98
    .line 99
    move-result v2

    .line 100
    move v3, v2

    .line 101
    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 102
    .line 103
    .line 104
    move-result v4

    .line 105
    if-eqz v4, :cond_4

    .line 106
    .line 107
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 108
    .line 109
    .line 110
    move-result-object v4

    .line 111
    check-cast v4, LKY0/a;

    .line 112
    .line 113
    invoke-interface {v4}, LKY0/a;->getX()F

    .line 114
    .line 115
    .line 116
    move-result v4

    .line 117
    invoke-static {v2, v4}, Ljava/lang/Math;->min(FF)F

    .line 118
    .line 119
    .line 120
    move-result v2

    .line 121
    invoke-static {v3, v4}, Ljava/lang/Math;->max(FF)F

    .line 122
    .line 123
    .line 124
    move-result v3

    .line 125
    goto :goto_2

    .line 126
    :cond_4
    invoke-static {v2, v3}, LTc/i;->c(FF)LTc/d;

    .line 127
    .line 128
    .line 129
    move-result-object v2

    .line 130
    :goto_3
    if-nez v2, :cond_5

    .line 131
    .line 132
    invoke-static {v1, v1}, LTc/i;->c(FF)LTc/d;

    .line 133
    .line 134
    .line 135
    move-result-object v2

    .line 136
    :cond_5
    invoke-interface {v2}, LTc/e;->e()Ljava/lang/Comparable;

    .line 137
    .line 138
    .line 139
    move-result-object v0

    .line 140
    check-cast v0, Ljava/lang/Number;

    .line 141
    .line 142
    invoke-virtual {v0}, Ljava/lang/Number;->floatValue()F

    .line 143
    .line 144
    .line 145
    move-result v6

    .line 146
    invoke-interface {p2}, LTc/e;->a()Ljava/lang/Comparable;

    .line 147
    .line 148
    .line 149
    move-result-object v0

    .line 150
    check-cast v0, Ljava/lang/Number;

    .line 151
    .line 152
    invoke-virtual {v0}, Ljava/lang/Number;->floatValue()F

    .line 153
    .line 154
    .line 155
    move-result v7

    .line 156
    invoke-interface {p2}, LTc/e;->e()Ljava/lang/Comparable;

    .line 157
    .line 158
    .line 159
    move-result-object p2

    .line 160
    check-cast p2, Ljava/lang/Number;

    .line 161
    .line 162
    invoke-virtual {p2}, Ljava/lang/Number;->floatValue()F

    .line 163
    .line 164
    .line 165
    move-result v8

    .line 166
    invoke-interface/range {p3 .. p3}, LTc/e;->e()Ljava/lang/Comparable;

    .line 167
    .line 168
    .line 169
    move-result-object p2

    .line 170
    check-cast p2, Ljava/lang/Number;

    .line 171
    .line 172
    invoke-virtual {p2}, Ljava/lang/Number;->floatValue()F

    .line 173
    .line 174
    .line 175
    move-result v9

    .line 176
    invoke-interface/range {p3 .. p3}, LTc/e;->a()Ljava/lang/Comparable;

    .line 177
    .line 178
    .line 179
    move-result-object p2

    .line 180
    check-cast p2, Ljava/lang/Number;

    .line 181
    .line 182
    invoke-virtual {p2}, Ljava/lang/Number;->floatValue()F

    .line 183
    .line 184
    .line 185
    move-result v10

    .line 186
    invoke-static {p1}, LKY0/b;->b(Ljava/lang/Iterable;)F

    .line 187
    .line 188
    .line 189
    move-result v11

    .line 190
    iget v12, p0, LKY0/g;->c:I

    .line 191
    .line 192
    new-instance v3, LKY0/g$a;

    .line 193
    .line 194
    move-object v4, p1

    .line 195
    invoke-direct/range {v3 .. v12}, LKY0/g$a;-><init>(Ljava/util/List;FFFFFFFI)V

    .line 196
    .line 197
    .line 198
    return-object v3
.end method

.method public final l(FLkotlin/jvm/functions/Function1;LLY0/b;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(F",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LKY0/c;",
            "Lkotlin/Unit;",
            ">;",
            "LLY0/b<",
            "LKY0/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p3, p1}, LLY0/b;->d(F)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {p3, p1}, LLY0/b;->a(F)LTc/d;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-interface {p3, p1}, LLY0/b;->c(F)LTc/d;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {p0, v0, v1, p1}, LKY0/g;->i(Ljava/util/List;LTc/d;LTc/d;)LKY0/c;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    invoke-interface {p2, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final n(Ljava/util/List;)V
    .locals 5
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "+",
            "LKY0/a;",
            ">;>;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LKY0/g;->f:Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-static {v0, p1}, LMY0/c;->f(Ljava/util/ArrayList;Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p1}, Ljava/lang/Object;->hashCode()I

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    const/4 v1, 0x0

    .line 11
    iput-object v1, p0, LKY0/g;->b:LKY0/c;

    .line 12
    .line 13
    iget-object v1, p0, LKY0/g;->d:Ljava/util/HashMap;

    .line 14
    .line 15
    invoke-virtual {v1}, Ljava/util/HashMap;->values()Ljava/util/Collection;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    check-cast v1, Ljava/lang/Iterable;

    .line 20
    .line 21
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 26
    .line 27
    .line 28
    move-result v2

    .line 29
    if-eqz v2, :cond_0

    .line 30
    .line 31
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v2

    .line 35
    check-cast v2, LKY0/g$b;

    .line 36
    .line 37
    iget-object v3, p0, LKY0/g;->e:Ljava/util/concurrent/Executor;

    .line 38
    .line 39
    new-instance v4, LKY0/d;

    .line 40
    .line 41
    invoke-direct {v4, p0, v0, v2, p1}, LKY0/d;-><init>(LKY0/g;ILKY0/g$b;Ljava/util/List;)V

    .line 42
    .line 43
    .line 44
    invoke-interface {v3, v4}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V

    .line 45
    .line 46
    .line 47
    goto :goto_0

    .line 48
    :cond_0
    return-void
.end method

.method public final varargs o([Ljava/util/List;)V
    .locals 0
    .param p1    # [Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/util/List<",
            "+",
            "LKY0/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-static {p1}, Lkotlin/collections/r;->K1([Ljava/lang/Object;)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p0, p1}, LKY0/g;->n(Ljava/util/List;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method
