.class public final synthetic LcY0/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lorg/xbet/ui_common/viewcomponents/recycler/managers/ScrollingLinearLayoutManager;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/ui_common/viewcomponents/recycler/managers/ScrollingLinearLayoutManager;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LcY0/c;->a:Lorg/xbet/ui_common/viewcomponents/recycler/managers/ScrollingLinearLayoutManager;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    .line 1
    iget-object v0, p0, LcY0/c;->a:Lorg/xbet/ui_common/viewcomponents/recycler/managers/ScrollingLinearLayoutManager;

    invoke-static {v0}, Lorg/xbet/ui_common/viewcomponents/recycler/managers/ScrollingLinearLayoutManager;->k(Lorg/xbet/ui_common/viewcomponents/recycler/managers/ScrollingLinearLayoutManager;)V

    return-void
.end method
