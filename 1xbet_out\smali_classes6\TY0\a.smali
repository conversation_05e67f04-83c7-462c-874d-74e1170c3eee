.class public final LTY0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LTY0/a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 \u000f2\u00020\u0001:\u0001\tB\u001b\u0012\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u0002\u0012\u0008\u0008\u0002\u0010\u0004\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0013\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00080\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\r\u0010\u000c\u001a\u00020\u000b\u00a2\u0006\u0004\u0008\u000c\u0010\rR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\u000eR\u0014\u0010\u0004\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\u000e\u00a8\u0006\u0010"
    }
    d2 = {
        "LTY0/a;",
        "",
        "Lkotlin/ranges/c;",
        "xRange",
        "yRange",
        "<init>",
        "(Lkotlin/ranges/c;Lkotlin/ranges/c;)V",
        "",
        "LKY0/i;",
        "a",
        "()Ljava/util/List;",
        "LKY0/c;",
        "b",
        "()LKY0/c;",
        "Lkotlin/ranges/c;",
        "c",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final c:LTY0/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final d:I


# instance fields
.field public final a:Lkotlin/ranges/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/ranges/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LTY0/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LTY0/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LTY0/a;->c:LTY0/a$a;

    .line 8
    .line 9
    const/16 v0, 0x8

    .line 10
    .line 11
    sput v0, LTY0/a;->d:I

    .line 12
    .line 13
    return-void
.end method

.method public constructor <init>()V
    .locals 2

    const/4 v0, 0x0

    const/4 v1, 0x3

    .line 1
    invoke-direct {p0, v0, v0, v1, v0}, LTY0/a;-><init>(Lkotlin/ranges/c;Lkotlin/ranges/c;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Lkotlin/ranges/c;Lkotlin/ranges/c;)V
    .locals 0
    .param p1    # Lkotlin/ranges/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/ranges/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, LTY0/a;->a:Lkotlin/ranges/c;

    .line 4
    iput-object p2, p0, LTY0/a;->b:Lkotlin/ranges/c;

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/ranges/c;Lkotlin/ranges/c;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 1

    and-int/lit8 p4, p3, 0x1

    const/4 v0, 0x0

    if-eqz p4, :cond_0

    .line 5
    new-instance p1, Lkotlin/ranges/IntRange;

    const/16 p4, 0xa

    invoke-direct {p1, v0, p4}, Lkotlin/ranges/IntRange;-><init>(II)V

    :cond_0
    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_1

    .line 6
    new-instance p2, Lkotlin/ranges/IntRange;

    const/16 p3, 0x14

    invoke-direct {p2, v0, p3}, Lkotlin/ranges/IntRange;-><init>(II)V

    .line 7
    :cond_1
    invoke-direct {p0, p1, p2}, LTY0/a;-><init>(Lkotlin/ranges/c;Lkotlin/ranges/c;)V

    return-void
.end method


# virtual methods
.method public final a()Ljava/util/List;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "LKY0/i;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 4
    .line 5
    .line 6
    iget-object v1, p0, LTY0/a;->b:Lkotlin/ranges/c;

    .line 7
    .line 8
    invoke-virtual {v1}, Lkotlin/ranges/c;->i()I

    .line 9
    .line 10
    .line 11
    move-result v1

    .line 12
    iget-object v2, p0, LTY0/a;->b:Lkotlin/ranges/c;

    .line 13
    .line 14
    invoke-virtual {v2}, Lkotlin/ranges/c;->f()I

    .line 15
    .line 16
    .line 17
    move-result v2

    .line 18
    sub-int/2addr v1, v2

    .line 19
    iget-object v2, p0, LTY0/a;->a:Lkotlin/ranges/c;

    .line 20
    .line 21
    invoke-virtual {v2}, Lkotlin/ranges/c;->f()I

    .line 22
    .line 23
    .line 24
    move-result v3

    .line 25
    invoke-virtual {v2}, Lkotlin/ranges/c;->i()I

    .line 26
    .line 27
    .line 28
    move-result v4

    .line 29
    invoke-virtual {v2}, Lkotlin/ranges/c;->j()I

    .line 30
    .line 31
    .line 32
    move-result v2

    .line 33
    if-lez v2, :cond_0

    .line 34
    .line 35
    if-le v3, v4, :cond_1

    .line 36
    .line 37
    :cond_0
    if-gez v2, :cond_2

    .line 38
    .line 39
    if-gt v4, v3, :cond_2

    .line 40
    .line 41
    :cond_1
    :goto_0
    int-to-float v5, v3

    .line 42
    iget-object v6, p0, LTY0/a;->b:Lkotlin/ranges/c;

    .line 43
    .line 44
    invoke-virtual {v6}, Lkotlin/ranges/c;->f()I

    .line 45
    .line 46
    .line 47
    move-result v6

    .line 48
    int-to-float v6, v6

    .line 49
    sget-object v7, Lkotlin/random/Random;->Default:Lkotlin/random/Random$Default;

    .line 50
    .line 51
    invoke-virtual {v7}, Lkotlin/random/Random$Default;->nextFloat()F

    .line 52
    .line 53
    .line 54
    move-result v7

    .line 55
    int-to-float v8, v1

    .line 56
    mul-float v7, v7, v8

    .line 57
    .line 58
    add-float/2addr v6, v7

    .line 59
    invoke-static {v5, v6}, LKY0/b;->c(FF)LKY0/i;

    .line 60
    .line 61
    .line 62
    move-result-object v5

    .line 63
    invoke-interface {v0, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 64
    .line 65
    .line 66
    if-eq v3, v4, :cond_2

    .line 67
    .line 68
    add-int/2addr v3, v2

    .line 69
    goto :goto_0

    .line 70
    :cond_2
    return-object v0
.end method

.method public final b()LKY0/c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, LTY0/a;->c:LTY0/a$a;

    .line 2
    .line 3
    invoke-virtual {v0, p0}, LTY0/a$a;->a(LTY0/a;)LKY0/h;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-interface {v0}, LKY0/h;->c()LKY0/c;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0
.end method
