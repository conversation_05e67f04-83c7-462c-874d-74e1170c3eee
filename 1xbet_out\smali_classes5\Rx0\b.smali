.class public final synthetic LRx0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LHX0/e;


# direct methods
.method public synthetic constructor <init>(LHX0/e;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LRx0/b;->a:LHX0/e;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LRx0/b;->a:LHX0/e;

    invoke-static {v0}, LRx0/d;->b(LHX0/e;)LVX0/i;

    move-result-object v0

    return-object v0
.end method
