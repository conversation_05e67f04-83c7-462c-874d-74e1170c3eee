.class public final Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0018\u0000 62\u00020\u0001:\u00017B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u000f\u0010\u0006\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0006\u0010\u0003J\u000f\u0010\u0007\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0007\u0010\u0003J\u000f\u0010\u0008\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0008\u0010\u0003J\u0019\u0010\u000b\u001a\u00020\u00042\u0008\u0010\n\u001a\u0004\u0018\u00010\tH\u0014\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u000f\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0017\u0010\u0012\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\u0011H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u001f\u0010\u0018\u001a\u00020\u00042\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0016H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0019R\"\u0010!\u001a\u00020\u001a8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u001b\u0010\u001c\u001a\u0004\u0008\u001d\u0010\u001e\"\u0004\u0008\u001f\u0010 R\"\u0010)\u001a\u00020\"8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008#\u0010$\u001a\u0004\u0008%\u0010&\"\u0004\u0008\'\u0010(R\u001b\u0010/\u001a\u00020*8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.R\u001b\u00105\u001a\u0002008BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00081\u00102\u001a\u0004\u00083\u00104\u00a8\u00068"
    }
    d2 = {
        "Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "G2",
        "H2",
        "u2",
        "v2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b;",
        "action",
        "F2",
        "(Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b;)V",
        "Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b$b;",
        "J2",
        "(Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b$b;)V",
        "Lorg/xbet/uikit/components/lottie/a;",
        "lottieConfig",
        "",
        "message",
        "L2",
        "(Lorg/xbet/uikit/components/lottie/a;Ljava/lang/String;)V",
        "LzX0/k;",
        "i0",
        "LzX0/k;",
        "C2",
        "()LzX0/k;",
        "setSnackbarManager",
        "(LzX0/k;)V",
        "snackbarManager",
        "Landroidx/lifecycle/e0$c;",
        "j0",
        "Landroidx/lifecycle/e0$c;",
        "E2",
        "()Landroidx/lifecycle/e0$c;",
        "setViewModelFactory",
        "(Landroidx/lifecycle/e0$c;)V",
        "viewModelFactory",
        "Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel;",
        "k0",
        "Lkotlin/j;",
        "D2",
        "()Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel;",
        "viewModel",
        "LPW0/o;",
        "l0",
        "LRc/c;",
        "B2",
        "()LPW0/o;",
        "binding",
        "m0",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final m0:Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic n0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public i0:LzX0/k;

.field public j0:Landroidx/lifecycle/e0$c;

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getBinding()Lorg/xbet/ui_common/databinding/FragmentWebBrowserBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;

    .line 7
    .line 8
    const-string v4, "binding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->n0:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->m0:Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$a;

    .line 31
    .line 32
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LIW0/x;->fragment_web_browser:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xbet/reward_system/impl/presentation/a;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lorg/xbet/reward_system/impl/presentation/a;-><init>(Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;)V

    .line 9
    .line 10
    .line 11
    new-instance v1, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$special$$inlined$viewModels$default$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 17
    .line 18
    new-instance v3, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$special$$inlined$viewModels$default$2;

    .line 19
    .line 20
    invoke-direct {v3, v1}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 21
    .line 22
    .line 23
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const-class v2, Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel;

    .line 28
    .line 29
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    new-instance v3, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$special$$inlined$viewModels$default$3;

    .line 34
    .line 35
    invoke-direct {v3, v1}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 36
    .line 37
    .line 38
    new-instance v4, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$special$$inlined$viewModels$default$4;

    .line 39
    .line 40
    const/4 v5, 0x0

    .line 41
    invoke-direct {v4, v5, v1}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->k0:Lkotlin/j;

    .line 49
    .line 50
    sget-object v0, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$binding$2;->INSTANCE:Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$binding$2;

    .line 51
    .line 52
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->l0:LRc/c;

    .line 57
    .line 58
    return-void
.end method

.method public static final synthetic A2(Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->K2(Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final G2()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->B2()LPW0/o;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LPW0/o;->l:Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;

    .line 6
    .line 7
    invoke-virtual {v0}, Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;->getFixedWebView()Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView$SafeWebView;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    if-eqz v0, :cond_0

    .line 12
    .line 13
    invoke-virtual {v0}, Landroid/webkit/WebView;->getSettings()Landroid/webkit/WebSettings;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    const/4 v2, 0x1

    .line 18
    invoke-virtual {v1, v2}, Landroid/webkit/WebSettings;->setDomStorageEnabled(Z)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {v0}, Landroid/webkit/WebView;->getSettings()Landroid/webkit/WebSettings;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-virtual {v1, v2}, Landroid/webkit/WebSettings;->setJavaScriptEnabled(Z)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {v0}, Landroid/webkit/WebView;->getSettings()Landroid/webkit/WebSettings;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    invoke-virtual {v1, v2}, Landroid/webkit/WebSettings;->setLoadWithOverviewMode(Z)V

    .line 33
    .line 34
    .line 35
    invoke-virtual {v0, v2}, Landroid/webkit/WebView;->setInitialScale(I)V

    .line 36
    .line 37
    .line 38
    invoke-virtual {v0}, Landroid/webkit/WebView;->getSettings()Landroid/webkit/WebSettings;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-virtual {v1, v2}, Landroid/webkit/WebSettings;->setUseWideViewPort(Z)V

    .line 43
    .line 44
    .line 45
    invoke-virtual {v0}, Landroid/webkit/WebView;->getSettings()Landroid/webkit/WebSettings;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    invoke-virtual {v1, v2}, Landroid/webkit/WebSettings;->setAllowFileAccess(Z)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {v0}, Landroid/webkit/WebView;->getSettings()Landroid/webkit/WebSettings;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    invoke-virtual {v1, v2}, Landroid/webkit/WebSettings;->setBuiltInZoomControls(Z)V

    .line 57
    .line 58
    .line 59
    const/4 v1, 0x2

    .line 60
    const/4 v2, 0x0

    .line 61
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->setLayerType(ILandroid/graphics/Paint;)V

    .line 62
    .line 63
    .line 64
    :cond_0
    return-void
.end method

.method private final H2()V
    .locals 5

    .line 1
    invoke-virtual {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->B2()LPW0/o;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LPW0/o;->i:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 6
    .line 7
    sget v1, Lpb/k;->reward_system:I

    .line 8
    .line 9
    invoke-virtual {p0, v1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;->setTitle(Ljava/lang/CharSequence;)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->B2()LPW0/o;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    iget-object v0, v0, LPW0/o;->i:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 21
    .line 22
    new-instance v1, Lorg/xbet/reward_system/impl/presentation/b;

    .line 23
    .line 24
    invoke-direct {v1, p0}, Lorg/xbet/reward_system/impl/presentation/b;-><init>(Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;)V

    .line 25
    .line 26
    .line 27
    const/4 v2, 0x1

    .line 28
    const/4 v3, 0x0

    .line 29
    const/4 v4, 0x0

    .line 30
    invoke-static {v0, v4, v1, v2, v3}, LK01/d$a;->a(LK01/d;ZLkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p0}, LXW0/a;->x2()V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public static final I2(Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->D2()Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel;->onBackPressed()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final synthetic K2(Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->F2(Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final M2(Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->E2()Landroidx/lifecycle/e0$c;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic y2(Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->I2(Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->M2(Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final B2()LPW0/o;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->l0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->n0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LPW0/o;

    .line 13
    .line 14
    return-object v0
.end method

.method public final C2()LzX0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->i0:LzX0/k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final D2()Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final E2()Landroidx/lifecycle/e0$c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->j0:Landroidx/lifecycle/e0$c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final F2(Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b;)V
    .locals 1

    .line 1
    instance-of v0, p1, Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b$a;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->B2()LPW0/o;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iget-object v0, v0, LPW0/o;->l:Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;

    .line 10
    .line 11
    invoke-virtual {v0}, Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;->p()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    check-cast p1, Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b$a;

    .line 18
    .line 19
    invoke-virtual {p1}, Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b$a;->a()Z

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    invoke-static {p1}, Landroid/webkit/WebView;->setWebContentsDebuggingEnabled(Z)V

    .line 24
    .line 25
    .line 26
    :cond_0
    return-void

    .line 27
    :cond_1
    instance-of v0, p1, Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b$b;

    .line 28
    .line 29
    if-eqz v0, :cond_2

    .line 30
    .line 31
    check-cast p1, Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b$b;

    .line 32
    .line 33
    invoke-virtual {p0, p1}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->J2(Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b$b;)V

    .line 34
    .line 35
    .line 36
    return-void

    .line 37
    :cond_2
    instance-of v0, p1, Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b$c;

    .line 38
    .line 39
    if-eqz v0, :cond_3

    .line 40
    .line 41
    check-cast p1, Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b$c;

    .line 42
    .line 43
    invoke-virtual {p1}, Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b$c;->b()Lorg/xbet/uikit/components/lottie/a;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    invoke-virtual {p1}, Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b$c;->a()Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    invoke-virtual {p0, v0, p1}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->L2(Lorg/xbet/uikit/components/lottie/a;Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    return-void

    .line 55
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 56
    .line 57
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 58
    .line 59
    .line 60
    throw p1
.end method

.method public final J2(Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b$b;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->B2()LPW0/o;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LPW0/o;->l:Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;

    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel$b$b;->a()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-virtual {v0, p1}, Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;->q(Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final L2(Lorg/xbet/uikit/components/lottie/a;Ljava/lang/String;)V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->B2()LPW0/o;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LPW0/o;->c:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/lottie/LottieView;->L(Lorg/xbet/uikit/components/lottie/a;)V

    .line 8
    .line 9
    .line 10
    const/4 p1, 0x0

    .line 11
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->B2()LPW0/o;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    iget-object p1, p1, LPW0/o;->l:Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;

    .line 19
    .line 20
    const/16 v0, 0x8

    .line 21
    .line 22
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 23
    .line 24
    .line 25
    invoke-interface {p2}, Ljava/lang/CharSequence;->length()I

    .line 26
    .line 27
    .line 28
    move-result p1

    .line 29
    if-lez p1, :cond_0

    .line 30
    .line 31
    invoke-virtual {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->C2()LzX0/k;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    new-instance v1, Ly01/g;

    .line 36
    .line 37
    sget-object v2, Ly01/i$c;->a:Ly01/i$c;

    .line 38
    .line 39
    const/16 v8, 0x3c

    .line 40
    .line 41
    const/4 v9, 0x0

    .line 42
    const/4 v4, 0x0

    .line 43
    const/4 v5, 0x0

    .line 44
    const/4 v6, 0x0

    .line 45
    const/4 v7, 0x0

    .line 46
    move-object v3, p2

    .line 47
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 48
    .line 49
    .line 50
    const/16 v10, 0x1fc

    .line 51
    .line 52
    const/4 v11, 0x0

    .line 53
    const/4 v3, 0x0

    .line 54
    const/4 v5, 0x0

    .line 55
    const/4 v6, 0x0

    .line 56
    const/4 v8, 0x0

    .line 57
    move-object v2, p0

    .line 58
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 59
    .line 60
    .line 61
    :cond_0
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->H2()V

    .line 5
    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->G2()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    instance-of v1, v0, LQW0/b;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    check-cast v0, LQW0/b;

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    move-object v0, v2

    .line 21
    :goto_0
    const-class v1, LGl0/i;

    .line 22
    .line 23
    if-eqz v0, :cond_3

    .line 24
    .line 25
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, LBc/a;

    .line 34
    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, LQW0/a;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object v0, v2

    .line 45
    :goto_1
    instance-of v3, v0, LGl0/i;

    .line 46
    .line 47
    if-nez v3, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    move-object v2, v0

    .line 51
    :goto_2
    check-cast v2, LGl0/i;

    .line 52
    .line 53
    if-eqz v2, :cond_3

    .line 54
    .line 55
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    invoke-virtual {v2, v0}, LGl0/i;->a(LwX0/c;)LGl0/h;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-interface {v0, p0}, LGl0/h;->a(Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;)V

    .line 64
    .line 65
    .line 66
    return-void

    .line 67
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 68
    .line 69
    new-instance v2, Ljava/lang/StringBuilder;

    .line 70
    .line 71
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 72
    .line 73
    .line 74
    const-string v3, "Cannot create dependency "

    .line 75
    .line 76
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 77
    .line 78
    .line 79
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 80
    .line 81
    .line 82
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 83
    .line 84
    .line 85
    move-result-object v1

    .line 86
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 91
    .line 92
    .line 93
    throw v0
.end method

.method public v2()V
    .locals 12

    .line 1
    invoke-super {p0}, LXW0/a;->v2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment;->D2()Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemViewModel;->z1()Lkotlinx/coroutines/flow/e;

    .line 9
    .line 10
    .line 11
    move-result-object v2

    .line 12
    new-instance v5, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$onObserveData$1;

    .line 13
    .line 14
    invoke-direct {v5, p0}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$onObserveData$1;-><init>(Ljava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 18
    .line 19
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 20
    .line 21
    .line 22
    move-result-object v3

    .line 23
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    new-instance v1, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 28
    .line 29
    const/4 v6, 0x0

    .line 30
    invoke-direct/range {v1 .. v6}, Lorg/xbet/reward_system/impl/presentation/RewardSystemFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 31
    .line 32
    .line 33
    const/4 v10, 0x3

    .line 34
    const/4 v11, 0x0

    .line 35
    const/4 v7, 0x0

    .line 36
    const/4 v8, 0x0

    .line 37
    move-object v6, v0

    .line 38
    move-object v9, v1

    .line 39
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    return-void
.end method
