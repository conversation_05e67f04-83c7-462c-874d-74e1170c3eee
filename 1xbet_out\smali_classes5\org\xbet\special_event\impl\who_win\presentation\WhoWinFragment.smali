.class public final Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0001\u0018\u0000 <2\u00020\u0001:\u0001=B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u0019\u0010\u0008\u001a\u00020\u00042\u0008\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u0014\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\n\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\n\u0010\u0003J\u0017\u0010\r\u001a\u00020\u00042\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u000f\u001a\u00020\u00042\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u000eJ\u0017\u0010\u0010\u001a\u00020\u00042\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u000eJ\u0017\u0010\u0012\u001a\u00020\u00042\u0006\u0010\u000c\u001a\u00020\u0011H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013R\"\u0010\u001b\u001a\u00020\u00148\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u0015\u0010\u0016\u001a\u0004\u0008\u0017\u0010\u0018\"\u0004\u0008\u0019\u0010\u001aR\"\u0010#\u001a\u00020\u001c8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u001d\u0010\u001e\u001a\u0004\u0008\u001f\u0010 \"\u0004\u0008!\u0010\"R\u001b\u0010)\u001a\u00020$8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008%\u0010&\u001a\u0004\u0008\'\u0010(R\u001b\u0010/\u001a\u00020*8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.R\u001a\u00105\u001a\u0002008\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u00081\u00102\u001a\u0004\u00083\u00104R\u001b\u0010;\u001a\u0002068BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00087\u00108\u001a\u0004\u00089\u0010:\u00a8\u0006>"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "LMy0/c$a;",
        "uiState",
        "I2",
        "(LMy0/c$a;)V",
        "M2",
        "L2",
        "LMy0/c$b;",
        "J2",
        "(LMy0/c$b;)V",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "i0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "H2",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "LSX0/a;",
        "j0",
        "LSX0/a;",
        "D2",
        "()LSX0/a;",
        "setLottieConfigurator",
        "(LSX0/a;)V",
        "lottieConfigurator",
        "Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;",
        "k0",
        "Lkotlin/j;",
        "G2",
        "()Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;",
        "viewModel",
        "LGq0/H;",
        "l0",
        "LRc/c;",
        "F2",
        "()LGq0/H;",
        "viewBinding",
        "",
        "m0",
        "Z",
        "r2",
        "()Z",
        "showNavBar",
        "Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;",
        "n0",
        "LeX0/h;",
        "E2",
        "()Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;",
        "params",
        "o0",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final synthetic b1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final k1:I

.field public static final o0:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public i0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public j0:LSX0/a;

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:Z

.field public final n0:LeX0/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;

    .line 4
    .line 5
    const-string v2, "viewBinding"

    .line 6
    .line 7
    const-string v3, "getViewBinding()Lorg/xbet/special_event/impl/databinding/FragmentWhoWinBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "params"

    .line 20
    .line 21
    const-string v5, "getParams()Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    const/4 v2, 0x2

    .line 31
    new-array v2, v2, [Lkotlin/reflect/m;

    .line 32
    .line 33
    aput-object v0, v2, v4

    .line 34
    .line 35
    const/4 v0, 0x1

    .line 36
    aput-object v1, v2, v0

    .line 37
    .line 38
    sput-object v2, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->b1:[Lkotlin/reflect/m;

    .line 39
    .line 40
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$a;

    .line 41
    .line 42
    const/4 v1, 0x0

    .line 43
    invoke-direct {v0, v1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    sput-object v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->o0:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$a;

    .line 47
    .line 48
    const/16 v0, 0x8

    .line 49
    .line 50
    sput v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->k1:I

    .line 51
    .line 52
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LUo0/c;->fragment_who_win:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/a;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/who_win/presentation/a;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)V

    .line 9
    .line 10
    .line 11
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$special$$inlined$viewModels$default$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 17
    .line 18
    new-instance v3, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$special$$inlined$viewModels$default$2;

    .line 19
    .line 20
    invoke-direct {v3, v1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 21
    .line 22
    .line 23
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const-class v2, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 28
    .line 29
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    new-instance v3, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$special$$inlined$viewModels$default$3;

    .line 34
    .line 35
    invoke-direct {v3, v1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 36
    .line 37
    .line 38
    new-instance v4, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$special$$inlined$viewModels$default$4;

    .line 39
    .line 40
    const/4 v5, 0x0

    .line 41
    invoke-direct {v4, v5, v1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->k0:Lkotlin/j;

    .line 49
    .line 50
    sget-object v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$viewBinding$2;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$viewBinding$2;

    .line 51
    .line 52
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->l0:LRc/c;

    .line 57
    .line 58
    const/4 v0, 0x1

    .line 59
    iput-boolean v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->m0:Z

    .line 60
    .line 61
    new-instance v0, LeX0/h;

    .line 62
    .line 63
    const-string v1, "BUNDLE_PARAMS"

    .line 64
    .line 65
    const/4 v2, 0x2

    .line 66
    invoke-direct {v0, v1, v5, v2, v5}, LeX0/h;-><init>(Ljava/lang/String;Landroid/os/Parcelable;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 67
    .line 68
    .line 69
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->n0:LeX0/h;

    .line 70
    .line 71
    return-void
.end method

.method public static final synthetic A2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->E2()Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic B2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;LMy0/c$a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->I2(LMy0/c$a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;LMy0/c$b;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->J2(LMy0/c$b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final K2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->G2()Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->onBackPressed()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final N2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->H2()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic y2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->N2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->K2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final D2()LSX0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->j0:LSX0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final E2()Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->n0:LeX0/h;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/h;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Landroid/os/Parcelable;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;

    .line 13
    .line 14
    return-object v0
.end method

.method public final F2()LGq0/H;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->l0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LGq0/H;

    .line 13
    .line 14
    return-object v0
.end method

.method public final G2()Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final H2()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->i0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final I2(LMy0/c$a;)V
    .locals 4

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->M2(LMy0/c$a;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->L2(LMy0/c$a;)V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->F2()LGq0/H;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iget-object v1, v0, LGq0/H;->b:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 12
    .line 13
    const/16 v2, 0x8

    .line 14
    .line 15
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 16
    .line 17
    .line 18
    iget-object v1, v0, LGq0/H;->e:Landroidx/fragment/app/FragmentContainerView;

    .line 19
    .line 20
    const/4 v3, 0x0

    .line 21
    invoke-virtual {v1, v3}, Landroid/view/View;->setVisibility(I)V

    .line 22
    .line 23
    .line 24
    iget-object v0, v0, LGq0/H;->d:Landroid/widget/FrameLayout;

    .line 25
    .line 26
    invoke-virtual {p1}, LMy0/c$a;->c()Z

    .line 27
    .line 28
    .line 29
    move-result p1

    .line 30
    if-eqz p1, :cond_0

    .line 31
    .line 32
    const/4 v2, 0x0

    .line 33
    :cond_0
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public final J2(LMy0/c$b;)V
    .locals 9

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->F2()LGq0/H;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, LGq0/H;->b:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->D2()LSX0/a;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    sget-object v3, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 12
    .line 13
    invoke-virtual {p1}, LMy0/c$b;->c()I

    .line 14
    .line 15
    .line 16
    move-result v4

    .line 17
    invoke-virtual {p1}, LMy0/c$b;->a()I

    .line 18
    .line 19
    .line 20
    move-result v5

    .line 21
    invoke-virtual {p1}, LMy0/c$b;->b()J

    .line 22
    .line 23
    .line 24
    move-result-wide v7

    .line 25
    new-instance v6, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$handleErrorState$1$1;

    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->G2()Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-direct {v6, p1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$handleErrorState$1$1;-><init>(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    invoke-interface/range {v2 .. v8}, LSX0/a;->a(Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;J)Lorg/xbet/uikit/components/lottie/a;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    sget v2, Lpb/k;->update_again_after:I

    .line 39
    .line 40
    invoke-virtual {v1, p1, v2}, Lorg/xbet/uikit/components/lottie/LottieView;->P(Lorg/xbet/uikit/components/lottie/a;I)V

    .line 41
    .line 42
    .line 43
    iget-object p1, v0, LGq0/H;->b:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 44
    .line 45
    const/4 v1, 0x0

    .line 46
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 47
    .line 48
    .line 49
    iget-object p1, v0, LGq0/H;->e:Landroidx/fragment/app/FragmentContainerView;

    .line 50
    .line 51
    const/16 v1, 0x8

    .line 52
    .line 53
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 54
    .line 55
    .line 56
    iget-object p1, v0, LGq0/H;->d:Landroid/widget/FrameLayout;

    .line 57
    .line 58
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 59
    .line 60
    .line 61
    return-void
.end method

.method public final L2(LMy0/c$a;)V
    .locals 11

    .line 1
    invoke-virtual {p1}, LMy0/c$a;->b()LMy0/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    instance-of v0, p1, LMy0/a$b;

    .line 6
    .line 7
    const/4 v1, 0x1

    .line 8
    if-eqz v0, :cond_7

    .line 9
    .line 10
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->F2()LGq0/H;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    iget-object v0, v0, LGq0/H;->e:Landroidx/fragment/app/FragmentContainerView;

    .line 19
    .line 20
    invoke-virtual {v0}, Landroid/view/View;->getId()I

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    sget-object v2, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->x1:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$a;

    .line 25
    .line 26
    invoke-virtual {v2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$a;->a()Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    invoke-virtual {p1}, Landroidx/fragment/app/FragmentManager;->H0()Ljava/util/List;

    .line 31
    .line 32
    .line 33
    move-result-object v3

    .line 34
    new-instance v4, Ljava/util/ArrayList;

    .line 35
    .line 36
    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 37
    .line 38
    .line 39
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    :cond_0
    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 44
    .line 45
    .line 46
    move-result v5

    .line 47
    if-eqz v5, :cond_1

    .line 48
    .line 49
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object v5

    .line 53
    move-object v6, v5

    .line 54
    check-cast v6, Landroidx/fragment/app/Fragment;

    .line 55
    .line 56
    invoke-virtual {v6}, Landroidx/fragment/app/Fragment;->isHidden()Z

    .line 57
    .line 58
    .line 59
    move-result v7

    .line 60
    if-nez v7, :cond_0

    .line 61
    .line 62
    invoke-virtual {v6}, Landroidx/fragment/app/Fragment;->getTag()Ljava/lang/String;

    .line 63
    .line 64
    .line 65
    move-result-object v6

    .line 66
    invoke-static {v6, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 67
    .line 68
    .line 69
    move-result v6

    .line 70
    if-nez v6, :cond_0

    .line 71
    .line 72
    invoke-interface {v4, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 73
    .line 74
    .line 75
    goto :goto_0

    .line 76
    :cond_1
    invoke-virtual {p1, v2}, Landroidx/fragment/app/FragmentManager;->r0(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    .line 77
    .line 78
    .line 79
    move-result-object v3

    .line 80
    invoke-interface {v4}, Ljava/util/List;->isEmpty()Z

    .line 81
    .line 82
    .line 83
    move-result v5

    .line 84
    if-eqz v5, :cond_2

    .line 85
    .line 86
    if-eqz v3, :cond_2

    .line 87
    .line 88
    invoke-virtual {v3}, Landroidx/fragment/app/Fragment;->isVisible()Z

    .line 89
    .line 90
    .line 91
    move-result v5

    .line 92
    if-ne v5, v1, :cond_2

    .line 93
    .line 94
    return-void

    .line 95
    :cond_2
    invoke-virtual {p1}, Landroidx/fragment/app/FragmentManager;->r()Landroidx/fragment/app/N;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    invoke-static {p1, v1}, LXW0/g;->a(Landroidx/fragment/app/N;Z)V

    .line 100
    .line 101
    .line 102
    if-eqz v3, :cond_4

    .line 103
    .line 104
    invoke-virtual {v3}, Landroidx/fragment/app/Fragment;->isVisible()Z

    .line 105
    .line 106
    .line 107
    move-result v0

    .line 108
    if-eqz v0, :cond_3

    .line 109
    .line 110
    invoke-virtual {v3}, Landroidx/fragment/app/Fragment;->isHidden()Z

    .line 111
    .line 112
    .line 113
    move-result v0

    .line 114
    if-eqz v0, :cond_5

    .line 115
    .line 116
    :cond_3
    sget-object v0, Landroidx/lifecycle/Lifecycle$State;->RESUMED:Landroidx/lifecycle/Lifecycle$State;

    .line 117
    .line 118
    invoke-virtual {p1, v3, v0}, Landroidx/fragment/app/N;->x(Landroidx/fragment/app/Fragment;Landroidx/lifecycle/Lifecycle$State;)Landroidx/fragment/app/N;

    .line 119
    .line 120
    .line 121
    invoke-virtual {p1, v3}, Landroidx/fragment/app/N;->z(Landroidx/fragment/app/Fragment;)Landroidx/fragment/app/N;

    .line 122
    .line 123
    .line 124
    goto :goto_1

    .line 125
    :cond_4
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->x1:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$a;

    .line 126
    .line 127
    new-instance v3, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;

    .line 128
    .line 129
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->A2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;

    .line 130
    .line 131
    .line 132
    move-result-object v5

    .line 133
    invoke-virtual {v5}, Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;->G()I

    .line 134
    .line 135
    .line 136
    move-result v5

    .line 137
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->A2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;

    .line 138
    .line 139
    .line 140
    move-result-object v6

    .line 141
    invoke-virtual {v6}, Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;->b()J

    .line 142
    .line 143
    .line 144
    move-result-wide v6

    .line 145
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->A2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;

    .line 146
    .line 147
    .line 148
    move-result-object v8

    .line 149
    invoke-virtual {v8}, Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;->a()I

    .line 150
    .line 151
    .line 152
    move-result v8

    .line 153
    invoke-direct {v3, v5, v6, v7, v8}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;-><init>(IJI)V

    .line 154
    .line 155
    .line 156
    invoke-virtual {v1, v3}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$a;->b(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    .line 157
    .line 158
    .line 159
    move-result-object v1

    .line 160
    invoke-virtual {p1, v0, v1, v2}, Landroidx/fragment/app/N;->c(ILandroidx/fragment/app/Fragment;Ljava/lang/String;)Landroidx/fragment/app/N;

    .line 161
    .line 162
    .line 163
    :cond_5
    :goto_1
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 164
    .line 165
    .line 166
    move-result-object v0

    .line 167
    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 168
    .line 169
    .line 170
    move-result v1

    .line 171
    if-eqz v1, :cond_6

    .line 172
    .line 173
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 174
    .line 175
    .line 176
    move-result-object v1

    .line 177
    check-cast v1, Landroidx/fragment/app/Fragment;

    .line 178
    .line 179
    sget-object v2, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 180
    .line 181
    invoke-virtual {p1, v1, v2}, Landroidx/fragment/app/N;->x(Landroidx/fragment/app/Fragment;Landroidx/lifecycle/Lifecycle$State;)Landroidx/fragment/app/N;

    .line 182
    .line 183
    .line 184
    invoke-virtual {p1, v1}, Landroidx/fragment/app/N;->p(Landroidx/fragment/app/Fragment;)Landroidx/fragment/app/N;

    .line 185
    .line 186
    .line 187
    goto :goto_2

    .line 188
    :cond_6
    invoke-virtual {p1}, Landroidx/fragment/app/N;->k()V

    .line 189
    .line 190
    .line 191
    return-void

    .line 192
    :cond_7
    instance-of v0, p1, LMy0/a$c;

    .line 193
    .line 194
    if-eqz v0, :cond_f

    .line 195
    .line 196
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 197
    .line 198
    .line 199
    move-result-object p1

    .line 200
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->F2()LGq0/H;

    .line 201
    .line 202
    .line 203
    move-result-object v0

    .line 204
    iget-object v0, v0, LGq0/H;->e:Landroidx/fragment/app/FragmentContainerView;

    .line 205
    .line 206
    invoke-virtual {v0}, Landroid/view/View;->getId()I

    .line 207
    .line 208
    .line 209
    move-result v0

    .line 210
    sget-object v2, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment;->b1:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment$a;

    .line 211
    .line 212
    invoke-virtual {v2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment$a;->a()Ljava/lang/String;

    .line 213
    .line 214
    .line 215
    move-result-object v2

    .line 216
    new-instance v3, Ljava/lang/StringBuilder;

    .line 217
    .line 218
    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 219
    .line 220
    .line 221
    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 222
    .line 223
    .line 224
    const-string v2, "PLAY_OFF"

    .line 225
    .line 226
    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 227
    .line 228
    .line 229
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 230
    .line 231
    .line 232
    move-result-object v2

    .line 233
    invoke-virtual {p1}, Landroidx/fragment/app/FragmentManager;->H0()Ljava/util/List;

    .line 234
    .line 235
    .line 236
    move-result-object v3

    .line 237
    new-instance v4, Ljava/util/ArrayList;

    .line 238
    .line 239
    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 240
    .line 241
    .line 242
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 243
    .line 244
    .line 245
    move-result-object v3

    .line 246
    :cond_8
    :goto_3
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 247
    .line 248
    .line 249
    move-result v5

    .line 250
    if-eqz v5, :cond_9

    .line 251
    .line 252
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 253
    .line 254
    .line 255
    move-result-object v5

    .line 256
    move-object v6, v5

    .line 257
    check-cast v6, Landroidx/fragment/app/Fragment;

    .line 258
    .line 259
    invoke-virtual {v6}, Landroidx/fragment/app/Fragment;->isHidden()Z

    .line 260
    .line 261
    .line 262
    move-result v7

    .line 263
    if-nez v7, :cond_8

    .line 264
    .line 265
    invoke-virtual {v6}, Landroidx/fragment/app/Fragment;->getTag()Ljava/lang/String;

    .line 266
    .line 267
    .line 268
    move-result-object v6

    .line 269
    invoke-static {v6, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 270
    .line 271
    .line 272
    move-result v6

    .line 273
    if-nez v6, :cond_8

    .line 274
    .line 275
    invoke-interface {v4, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 276
    .line 277
    .line 278
    goto :goto_3

    .line 279
    :cond_9
    invoke-virtual {p1, v2}, Landroidx/fragment/app/FragmentManager;->r0(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    .line 280
    .line 281
    .line 282
    move-result-object v3

    .line 283
    invoke-interface {v4}, Ljava/util/List;->isEmpty()Z

    .line 284
    .line 285
    .line 286
    move-result v5

    .line 287
    if-eqz v5, :cond_a

    .line 288
    .line 289
    if-eqz v3, :cond_a

    .line 290
    .line 291
    invoke-virtual {v3}, Landroidx/fragment/app/Fragment;->isVisible()Z

    .line 292
    .line 293
    .line 294
    move-result v5

    .line 295
    if-ne v5, v1, :cond_a

    .line 296
    .line 297
    return-void

    .line 298
    :cond_a
    invoke-virtual {p1}, Landroidx/fragment/app/FragmentManager;->r()Landroidx/fragment/app/N;

    .line 299
    .line 300
    .line 301
    move-result-object p1

    .line 302
    invoke-static {p1, v1}, LXW0/g;->a(Landroidx/fragment/app/N;Z)V

    .line 303
    .line 304
    .line 305
    if-eqz v3, :cond_c

    .line 306
    .line 307
    invoke-virtual {v3}, Landroidx/fragment/app/Fragment;->isVisible()Z

    .line 308
    .line 309
    .line 310
    move-result v0

    .line 311
    if-eqz v0, :cond_b

    .line 312
    .line 313
    invoke-virtual {v3}, Landroidx/fragment/app/Fragment;->isHidden()Z

    .line 314
    .line 315
    .line 316
    move-result v0

    .line 317
    if-eqz v0, :cond_d

    .line 318
    .line 319
    :cond_b
    sget-object v0, Landroidx/lifecycle/Lifecycle$State;->RESUMED:Landroidx/lifecycle/Lifecycle$State;

    .line 320
    .line 321
    invoke-virtual {p1, v3, v0}, Landroidx/fragment/app/N;->x(Landroidx/fragment/app/Fragment;Landroidx/lifecycle/Lifecycle$State;)Landroidx/fragment/app/N;

    .line 322
    .line 323
    .line 324
    invoke-virtual {p1, v3}, Landroidx/fragment/app/N;->z(Landroidx/fragment/app/Fragment;)Landroidx/fragment/app/N;

    .line 325
    .line 326
    .line 327
    goto :goto_4

    .line 328
    :cond_c
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment;->b1:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment$a;

    .line 329
    .line 330
    new-instance v5, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;

    .line 331
    .line 332
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->A2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;

    .line 333
    .line 334
    .line 335
    move-result-object v3

    .line 336
    invoke-virtual {v3}, Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;->G()I

    .line 337
    .line 338
    .line 339
    move-result v6

    .line 340
    sget-object v7, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageType;->PLAY_OFF:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageType;

    .line 341
    .line 342
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->A2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;

    .line 343
    .line 344
    .line 345
    move-result-object v3

    .line 346
    invoke-virtual {v3}, Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;->b()J

    .line 347
    .line 348
    .line 349
    move-result-wide v8

    .line 350
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->A2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;

    .line 351
    .line 352
    .line 353
    move-result-object v3

    .line 354
    invoke-virtual {v3}, Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;->a()I

    .line 355
    .line 356
    .line 357
    move-result v10

    .line 358
    invoke-direct/range {v5 .. v10}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;-><init>(ILorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageType;JI)V

    .line 359
    .line 360
    .line 361
    invoke-virtual {v1, v5}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment$a;->b(Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;)Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment;

    .line 362
    .line 363
    .line 364
    move-result-object v1

    .line 365
    invoke-virtual {p1, v0, v1, v2}, Landroidx/fragment/app/N;->c(ILandroidx/fragment/app/Fragment;Ljava/lang/String;)Landroidx/fragment/app/N;

    .line 366
    .line 367
    .line 368
    :cond_d
    :goto_4
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 369
    .line 370
    .line 371
    move-result-object v0

    .line 372
    :goto_5
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 373
    .line 374
    .line 375
    move-result v1

    .line 376
    if-eqz v1, :cond_e

    .line 377
    .line 378
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 379
    .line 380
    .line 381
    move-result-object v1

    .line 382
    check-cast v1, Landroidx/fragment/app/Fragment;

    .line 383
    .line 384
    sget-object v2, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 385
    .line 386
    invoke-virtual {p1, v1, v2}, Landroidx/fragment/app/N;->x(Landroidx/fragment/app/Fragment;Landroidx/lifecycle/Lifecycle$State;)Landroidx/fragment/app/N;

    .line 387
    .line 388
    .line 389
    invoke-virtual {p1, v1}, Landroidx/fragment/app/N;->p(Landroidx/fragment/app/Fragment;)Landroidx/fragment/app/N;

    .line 390
    .line 391
    .line 392
    goto :goto_5

    .line 393
    :cond_e
    invoke-virtual {p1}, Landroidx/fragment/app/N;->k()V

    .line 394
    .line 395
    .line 396
    return-void

    .line 397
    :cond_f
    instance-of v0, p1, LMy0/a$a;

    .line 398
    .line 399
    if-eqz v0, :cond_17

    .line 400
    .line 401
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 402
    .line 403
    .line 404
    move-result-object p1

    .line 405
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->F2()LGq0/H;

    .line 406
    .line 407
    .line 408
    move-result-object v0

    .line 409
    iget-object v0, v0, LGq0/H;->e:Landroidx/fragment/app/FragmentContainerView;

    .line 410
    .line 411
    invoke-virtual {v0}, Landroid/view/View;->getId()I

    .line 412
    .line 413
    .line 414
    move-result v0

    .line 415
    sget-object v2, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment;->b1:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment$a;

    .line 416
    .line 417
    invoke-virtual {v2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment$a;->a()Ljava/lang/String;

    .line 418
    .line 419
    .line 420
    move-result-object v2

    .line 421
    new-instance v3, Ljava/lang/StringBuilder;

    .line 422
    .line 423
    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 424
    .line 425
    .line 426
    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 427
    .line 428
    .line 429
    const-string v2, "FINAL"

    .line 430
    .line 431
    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 432
    .line 433
    .line 434
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 435
    .line 436
    .line 437
    move-result-object v2

    .line 438
    invoke-virtual {p1}, Landroidx/fragment/app/FragmentManager;->H0()Ljava/util/List;

    .line 439
    .line 440
    .line 441
    move-result-object v3

    .line 442
    new-instance v4, Ljava/util/ArrayList;

    .line 443
    .line 444
    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 445
    .line 446
    .line 447
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 448
    .line 449
    .line 450
    move-result-object v3

    .line 451
    :cond_10
    :goto_6
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 452
    .line 453
    .line 454
    move-result v5

    .line 455
    if-eqz v5, :cond_11

    .line 456
    .line 457
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 458
    .line 459
    .line 460
    move-result-object v5

    .line 461
    move-object v6, v5

    .line 462
    check-cast v6, Landroidx/fragment/app/Fragment;

    .line 463
    .line 464
    invoke-virtual {v6}, Landroidx/fragment/app/Fragment;->isHidden()Z

    .line 465
    .line 466
    .line 467
    move-result v7

    .line 468
    if-nez v7, :cond_10

    .line 469
    .line 470
    invoke-virtual {v6}, Landroidx/fragment/app/Fragment;->getTag()Ljava/lang/String;

    .line 471
    .line 472
    .line 473
    move-result-object v6

    .line 474
    invoke-static {v6, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 475
    .line 476
    .line 477
    move-result v6

    .line 478
    if-nez v6, :cond_10

    .line 479
    .line 480
    invoke-interface {v4, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 481
    .line 482
    .line 483
    goto :goto_6

    .line 484
    :cond_11
    invoke-virtual {p1, v2}, Landroidx/fragment/app/FragmentManager;->r0(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    .line 485
    .line 486
    .line 487
    move-result-object v3

    .line 488
    invoke-interface {v4}, Ljava/util/List;->isEmpty()Z

    .line 489
    .line 490
    .line 491
    move-result v5

    .line 492
    if-eqz v5, :cond_12

    .line 493
    .line 494
    if-eqz v3, :cond_12

    .line 495
    .line 496
    invoke-virtual {v3}, Landroidx/fragment/app/Fragment;->isVisible()Z

    .line 497
    .line 498
    .line 499
    move-result v5

    .line 500
    if-ne v5, v1, :cond_12

    .line 501
    .line 502
    return-void

    .line 503
    :cond_12
    invoke-virtual {p1}, Landroidx/fragment/app/FragmentManager;->r()Landroidx/fragment/app/N;

    .line 504
    .line 505
    .line 506
    move-result-object p1

    .line 507
    invoke-static {p1, v1}, LXW0/g;->a(Landroidx/fragment/app/N;Z)V

    .line 508
    .line 509
    .line 510
    if-eqz v3, :cond_14

    .line 511
    .line 512
    invoke-virtual {v3}, Landroidx/fragment/app/Fragment;->isVisible()Z

    .line 513
    .line 514
    .line 515
    move-result v0

    .line 516
    if-eqz v0, :cond_13

    .line 517
    .line 518
    invoke-virtual {v3}, Landroidx/fragment/app/Fragment;->isHidden()Z

    .line 519
    .line 520
    .line 521
    move-result v0

    .line 522
    if-eqz v0, :cond_15

    .line 523
    .line 524
    :cond_13
    sget-object v0, Landroidx/lifecycle/Lifecycle$State;->RESUMED:Landroidx/lifecycle/Lifecycle$State;

    .line 525
    .line 526
    invoke-virtual {p1, v3, v0}, Landroidx/fragment/app/N;->x(Landroidx/fragment/app/Fragment;Landroidx/lifecycle/Lifecycle$State;)Landroidx/fragment/app/N;

    .line 527
    .line 528
    .line 529
    invoke-virtual {p1, v3}, Landroidx/fragment/app/N;->z(Landroidx/fragment/app/Fragment;)Landroidx/fragment/app/N;

    .line 530
    .line 531
    .line 532
    goto :goto_7

    .line 533
    :cond_14
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment;->b1:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment$a;

    .line 534
    .line 535
    new-instance v5, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;

    .line 536
    .line 537
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->A2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;

    .line 538
    .line 539
    .line 540
    move-result-object v3

    .line 541
    invoke-virtual {v3}, Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;->G()I

    .line 542
    .line 543
    .line 544
    move-result v6

    .line 545
    sget-object v7, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageType;->FINAL:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageType;

    .line 546
    .line 547
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->A2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;

    .line 548
    .line 549
    .line 550
    move-result-object v3

    .line 551
    invoke-virtual {v3}, Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;->b()J

    .line 552
    .line 553
    .line 554
    move-result-wide v8

    .line 555
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->A2(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;

    .line 556
    .line 557
    .line 558
    move-result-object v3

    .line 559
    invoke-virtual {v3}, Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;->a()I

    .line 560
    .line 561
    .line 562
    move-result v10

    .line 563
    invoke-direct/range {v5 .. v10}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;-><init>(ILorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageType;JI)V

    .line 564
    .line 565
    .line 566
    invoke-virtual {v1, v5}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment$a;->b(Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;)Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment;

    .line 567
    .line 568
    .line 569
    move-result-object v1

    .line 570
    invoke-virtual {p1, v0, v1, v2}, Landroidx/fragment/app/N;->c(ILandroidx/fragment/app/Fragment;Ljava/lang/String;)Landroidx/fragment/app/N;

    .line 571
    .line 572
    .line 573
    :cond_15
    :goto_7
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 574
    .line 575
    .line 576
    move-result-object v0

    .line 577
    :goto_8
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 578
    .line 579
    .line 580
    move-result v1

    .line 581
    if-eqz v1, :cond_16

    .line 582
    .line 583
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 584
    .line 585
    .line 586
    move-result-object v1

    .line 587
    check-cast v1, Landroidx/fragment/app/Fragment;

    .line 588
    .line 589
    sget-object v2, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 590
    .line 591
    invoke-virtual {p1, v1, v2}, Landroidx/fragment/app/N;->x(Landroidx/fragment/app/Fragment;Landroidx/lifecycle/Lifecycle$State;)Landroidx/fragment/app/N;

    .line 592
    .line 593
    .line 594
    invoke-virtual {p1, v1}, Landroidx/fragment/app/N;->p(Landroidx/fragment/app/Fragment;)Landroidx/fragment/app/N;

    .line 595
    .line 596
    .line 597
    goto :goto_8

    .line 598
    :cond_16
    invoke-virtual {p1}, Landroidx/fragment/app/N;->k()V

    .line 599
    .line 600
    .line 601
    return-void

    .line 602
    :cond_17
    if-nez p1, :cond_19

    .line 603
    .line 604
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 605
    .line 606
    .line 607
    move-result-object p1

    .line 608
    invoke-virtual {p1}, Landroidx/fragment/app/FragmentManager;->H0()Ljava/util/List;

    .line 609
    .line 610
    .line 611
    move-result-object p1

    .line 612
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 613
    .line 614
    .line 615
    move-result-object p1

    .line 616
    :goto_9
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 617
    .line 618
    .line 619
    move-result v0

    .line 620
    if-eqz v0, :cond_18

    .line 621
    .line 622
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 623
    .line 624
    .line 625
    move-result-object v0

    .line 626
    check-cast v0, Landroidx/fragment/app/Fragment;

    .line 627
    .line 628
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 629
    .line 630
    .line 631
    move-result-object v1

    .line 632
    invoke-virtual {v1}, Landroidx/fragment/app/FragmentManager;->r()Landroidx/fragment/app/N;

    .line 633
    .line 634
    .line 635
    move-result-object v1

    .line 636
    invoke-virtual {v1, v0}, Landroidx/fragment/app/N;->r(Landroidx/fragment/app/Fragment;)Landroidx/fragment/app/N;

    .line 637
    .line 638
    .line 639
    invoke-virtual {v1}, Landroidx/fragment/app/N;->i()I

    .line 640
    .line 641
    .line 642
    goto :goto_9

    .line 643
    :cond_18
    return-void

    .line 644
    :cond_19
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 645
    .line 646
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 647
    .line 648
    .line 649
    throw p1
.end method

.method public final M2(LMy0/c$a;)V
    .locals 7

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->F2()LGq0/H;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, LGq0/H;->f:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 6
    .line 7
    invoke-virtual {v1}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->getSegments()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    new-instance v2, Ljava/util/ArrayList;

    .line 12
    .line 13
    const/16 v3, 0xa

    .line 14
    .line 15
    invoke-static {v0, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 16
    .line 17
    .line 18
    move-result v4

    .line 19
    invoke-direct {v2, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 20
    .line 21
    .line 22
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 27
    .line 28
    .line 29
    move-result v4

    .line 30
    if-eqz v4, :cond_0

    .line 31
    .line 32
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v4

    .line 36
    check-cast v4, Lu01/a;

    .line 37
    .line 38
    invoke-virtual {v4}, Lu01/a;->b()Ljava/lang/CharSequence;

    .line 39
    .line 40
    .line 41
    move-result-object v4

    .line 42
    invoke-static {v4}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v4

    .line 46
    invoke-interface {v2, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 47
    .line 48
    .line 49
    goto :goto_0

    .line 50
    :cond_0
    invoke-virtual {p1}, LMy0/c$a;->a()Ljava/util/List;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    new-instance v4, Ljava/util/ArrayList;

    .line 55
    .line 56
    invoke-static {v0, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 57
    .line 58
    .line 59
    move-result v3

    .line 60
    invoke-direct {v4, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 61
    .line 62
    .line 63
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 68
    .line 69
    .line 70
    move-result v3

    .line 71
    if-eqz v3, :cond_1

    .line 72
    .line 73
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    move-result-object v3

    .line 77
    check-cast v3, LMy0/a;

    .line 78
    .line 79
    invoke-interface {v3}, LMy0/a;->getTitle()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object v3

    .line 83
    invoke-interface {v4, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 84
    .line 85
    .line 86
    goto :goto_1

    .line 87
    :cond_1
    invoke-static {v2, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 88
    .line 89
    .line 90
    move-result v0

    .line 91
    if-nez v0, :cond_2

    .line 92
    .line 93
    invoke-virtual {v1}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->r()V

    .line 94
    .line 95
    .line 96
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 97
    .line 98
    .line 99
    move-result-object v0

    .line 100
    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 101
    .line 102
    .line 103
    move-result v2

    .line 104
    if-eqz v2, :cond_2

    .line 105
    .line 106
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 107
    .line 108
    .line 109
    move-result-object v2

    .line 110
    check-cast v2, Ljava/lang/String;

    .line 111
    .line 112
    move-object v3, v2

    .line 113
    new-instance v2, Lu01/a;

    .line 114
    .line 115
    invoke-direct {v2}, Lu01/a;-><init>()V

    .line 116
    .line 117
    .line 118
    invoke-virtual {v2, v3}, Lu01/a;->d(Ljava/lang/CharSequence;)V

    .line 119
    .line 120
    .line 121
    const/4 v5, 0x6

    .line 122
    const/4 v6, 0x0

    .line 123
    const/4 v3, 0x0

    .line 124
    const/4 v4, 0x0

    .line 125
    invoke-static/range {v1 .. v6}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->h(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Lu01/a;IZILjava/lang/Object;)V

    .line 126
    .line 127
    .line 128
    goto :goto_2

    .line 129
    :cond_2
    invoke-virtual {p1}, LMy0/c$a;->a()Ljava/util/List;

    .line 130
    .line 131
    .line 132
    move-result-object v0

    .line 133
    invoke-virtual {p1}, LMy0/c$a;->b()LMy0/a;

    .line 134
    .line 135
    .line 136
    move-result-object p1

    .line 137
    invoke-static {v0, p1}, Lkotlin/collections/CollectionsKt;->B0(Ljava/util/List;Ljava/lang/Object;)I

    .line 138
    .line 139
    .line 140
    move-result p1

    .line 141
    const/4 v0, -0x1

    .line 142
    if-eq p1, v0, :cond_3

    .line 143
    .line 144
    invoke-virtual {v1, p1}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->setSelectedPosition(I)V

    .line 145
    .line 146
    .line 147
    :cond_3
    return-void
.end method

.method public r2()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->m0:Z

    .line 2
    .line 3
    return v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 5

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->F2()LGq0/H;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object v0, p1, LGq0/H;->c:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 9
    .line 10
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/b;

    .line 11
    .line 12
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/who_win/presentation/b;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)V

    .line 13
    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    const/4 v3, 0x1

    .line 17
    const/4 v4, 0x0

    .line 18
    invoke-static {v0, v2, v1, v3, v4}, LK01/d$a;->a(LK01/d;ZLkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    iget-object p1, p1, LGq0/H;->f:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 22
    .line 23
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$onInitView$1$2;

    .line 24
    .line 25
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->G2()Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    invoke-direct {v0, v1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$onInitView$1$2;-><init>(Ljava/lang/Object;)V

    .line 30
    .line 31
    .line 32
    invoke-static {p1, v4, v0, v3, v4}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->setOnSegmentSelectedListener$default(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public u2()V
    .locals 6

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    instance-of v1, v0, LQW0/b;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    check-cast v0, LQW0/b;

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    move-object v0, v2

    .line 21
    :goto_0
    const-class v1, Lzy0/d;

    .line 22
    .line 23
    if-eqz v0, :cond_3

    .line 24
    .line 25
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, LBc/a;

    .line 34
    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, LQW0/a;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object v0, v2

    .line 45
    :goto_1
    instance-of v3, v0, Lzy0/d;

    .line 46
    .line 47
    if-nez v3, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    move-object v2, v0

    .line 51
    :goto_2
    check-cast v2, Lzy0/d;

    .line 52
    .line 53
    if-eqz v2, :cond_3

    .line 54
    .line 55
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->E2()Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;->G()I

    .line 60
    .line 61
    .line 62
    move-result v0

    .line 63
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 64
    .line 65
    .line 66
    move-result-object v1

    .line 67
    sget-object v3, Lyy0/c;->a:Lyy0/c;

    .line 68
    .line 69
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->E2()Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;

    .line 70
    .line 71
    .line 72
    move-result-object v4

    .line 73
    invoke-virtual {v4}, Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;->G()I

    .line 74
    .line 75
    .line 76
    move-result v4

    .line 77
    invoke-static {p0}, LQW0/h;->a(Landroidx/fragment/app/Fragment;)Lorg/xbet/ui_common/router/NavBarScreenTypes;

    .line 78
    .line 79
    .line 80
    move-result-object v5

    .line 81
    invoke-virtual {v5}, Lorg/xbet/ui_common/router/NavBarScreenTypes;->getTag()Ljava/lang/String;

    .line 82
    .line 83
    .line 84
    move-result-object v5

    .line 85
    invoke-virtual {v3, v4, v5}, Lyy0/c;->c(ILjava/lang/String;)Ljava/lang/String;

    .line 86
    .line 87
    .line 88
    move-result-object v4

    .line 89
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 90
    .line 91
    .line 92
    move-result-object v5

    .line 93
    invoke-virtual {v5}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 94
    .line 95
    .line 96
    move-result-object v5

    .line 97
    invoke-virtual {v3, v4, v5}, Lyy0/c;->d(Ljava/lang/String;Landroid/app/Application;)Lyy0/d;

    .line 98
    .line 99
    .line 100
    move-result-object v3

    .line 101
    invoke-virtual {v2, v0, v1, v3}, Lzy0/d;->a(ILwX0/c;Lyy0/d;)Lzy0/c;

    .line 102
    .line 103
    .line 104
    move-result-object v0

    .line 105
    invoke-interface {v0, p0}, Lzy0/c;->a(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;)V

    .line 106
    .line 107
    .line 108
    return-void

    .line 109
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 110
    .line 111
    new-instance v2, Ljava/lang/StringBuilder;

    .line 112
    .line 113
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 114
    .line 115
    .line 116
    const-string v3, "Cannot create dependency "

    .line 117
    .line 118
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 119
    .line 120
    .line 121
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 122
    .line 123
    .line 124
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 125
    .line 126
    .line 127
    move-result-object v1

    .line 128
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 129
    .line 130
    .line 131
    move-result-object v1

    .line 132
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 133
    .line 134
    .line 135
    throw v0
.end method

.method public v2()V
    .locals 12

    .line 1
    invoke-super {p0}, LXW0/a;->v2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->G2()Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->E0()Lkotlinx/coroutines/flow/e;

    .line 9
    .line 10
    .line 11
    move-result-object v2

    .line 12
    new-instance v5, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$onObserveData$1;

    .line 13
    .line 14
    const/4 v0, 0x0

    .line 15
    invoke-direct {v5, p0, v0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$onObserveData$1;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 19
    .line 20
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 21
    .line 22
    .line 23
    move-result-object v3

    .line 24
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 29
    .line 30
    const/4 v6, 0x0

    .line 31
    invoke-direct/range {v1 .. v6}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/4 v10, 0x3

    .line 35
    const/4 v11, 0x0

    .line 36
    const/4 v7, 0x0

    .line 37
    const/4 v8, 0x0

    .line 38
    move-object v6, v0

    .line 39
    move-object v9, v1

    .line 40
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 41
    .line 42
    .line 43
    return-void
.end method
