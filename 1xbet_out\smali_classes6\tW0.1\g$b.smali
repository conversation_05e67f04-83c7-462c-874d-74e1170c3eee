.class public final LtW0/g$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LtW0/u;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LtW0/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LtW0/g$b$d;,
        LtW0/g$b$c;,
        LtW0/g$b$a;,
        LtW0/g$b$b;,
        LtW0/g$b$e;
    }
.end annotation


# instance fields
.field public final a:LTZ0/a;

.field public final b:LzX0/k;

.field public final c:Lak/b;

.field public final d:LtW0/g$b;

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwW0/g;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxW0/a;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_jackpot/impl/domain/scenario/c;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_jackpot/impl/domain/scenario/f;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/o;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/r;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/b;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lek/d;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/profile/ProfileInteractor;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/w;",
            ">;"
        }
    .end annotation
.end field

.field public r:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/I;

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LtW0/u$b;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lak/b;Lak/a;LTZ0/a;LwW0/g;LxW0/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/c;Lorg/xbet/toto_jackpot/impl/domain/scenario/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;LxX0/a;Lorg/xbet/ui_common/utils/M;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;LzX0/k;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LtW0/g$b;->d:LtW0/g$b;

    .line 4
    iput-object p3, p0, LtW0/g$b;->a:LTZ0/a;

    .line 5
    iput-object p12, p0, LtW0/g$b;->b:LzX0/k;

    .line 6
    iput-object p1, p0, LtW0/g$b;->c:Lak/b;

    .line 7
    invoke-virtual/range {p0 .. p12}, LtW0/g$b;->b(Lak/b;Lak/a;LTZ0/a;LwW0/g;LxW0/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/c;Lorg/xbet/toto_jackpot/impl/domain/scenario/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;LxX0/a;Lorg/xbet/ui_common/utils/M;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;LzX0/k;)V

    return-void
.end method

.method public synthetic constructor <init>(Lak/b;Lak/a;LTZ0/a;LwW0/g;LxW0/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/c;Lorg/xbet/toto_jackpot/impl/domain/scenario/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;LxX0/a;Lorg/xbet/ui_common/utils/M;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;LzX0/k;LtW0/h;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p12}, LtW0/g$b;-><init>(Lak/b;Lak/a;LTZ0/a;LwW0/g;LxW0/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/c;Lorg/xbet/toto_jackpot/impl/domain/scenario/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;LxX0/a;Lorg/xbet/ui_common/utils/M;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;LzX0/k;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LtW0/g$b;->c(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetFragment;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(Lak/b;Lak/a;LTZ0/a;LwW0/g;LxW0/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/c;Lorg/xbet/toto_jackpot/impl/domain/scenario/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;LxX0/a;Lorg/xbet/ui_common/utils/M;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;LzX0/k;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p2

    .line 4
    .line 5
    invoke-static/range {p4 .. p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    iput-object v2, v0, LtW0/g$b;->e:Ldagger/internal/h;

    .line 10
    .line 11
    invoke-static/range {p5 .. p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    iput-object v2, v0, LtW0/g$b;->f:Ldagger/internal/h;

    .line 16
    .line 17
    invoke-static/range {p8 .. p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    iput-object v2, v0, LtW0/g$b;->g:Ldagger/internal/h;

    .line 22
    .line 23
    invoke-static/range {p6 .. p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    iput-object v2, v0, LtW0/g$b;->h:Ldagger/internal/h;

    .line 28
    .line 29
    invoke-static/range {p7 .. p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    iput-object v2, v0, LtW0/g$b;->i:Ldagger/internal/h;

    .line 34
    .line 35
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 36
    .line 37
    .line 38
    move-result-object v2

    .line 39
    iput-object v2, v0, LtW0/g$b;->j:Ldagger/internal/h;

    .line 40
    .line 41
    new-instance v2, LtW0/g$b$d;

    .line 42
    .line 43
    invoke-direct {v2, v1}, LtW0/g$b$d;-><init>(Lak/a;)V

    .line 44
    .line 45
    .line 46
    iput-object v2, v0, LtW0/g$b;->k:Ldagger/internal/h;

    .line 47
    .line 48
    new-instance v2, LtW0/g$b$c;

    .line 49
    .line 50
    invoke-direct {v2, v1}, LtW0/g$b$c;-><init>(Lak/a;)V

    .line 51
    .line 52
    .line 53
    iput-object v2, v0, LtW0/g$b;->l:Ldagger/internal/h;

    .line 54
    .line 55
    new-instance v2, LtW0/g$b$a;

    .line 56
    .line 57
    invoke-direct {v2, v1}, LtW0/g$b$a;-><init>(Lak/a;)V

    .line 58
    .line 59
    .line 60
    iput-object v2, v0, LtW0/g$b;->m:Ldagger/internal/h;

    .line 61
    .line 62
    new-instance v2, LtW0/g$b$b;

    .line 63
    .line 64
    invoke-direct {v2, v1}, LtW0/g$b$b;-><init>(Lak/a;)V

    .line 65
    .line 66
    .line 67
    iput-object v2, v0, LtW0/g$b;->n:Ldagger/internal/h;

    .line 68
    .line 69
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 70
    .line 71
    .line 72
    move-result-object v2

    .line 73
    iput-object v2, v0, LtW0/g$b;->o:Ldagger/internal/h;

    .line 74
    .line 75
    invoke-static/range {p11 .. p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 76
    .line 77
    .line 78
    move-result-object v2

    .line 79
    iput-object v2, v0, LtW0/g$b;->p:Ldagger/internal/h;

    .line 80
    .line 81
    new-instance v15, LtW0/g$b$e;

    .line 82
    .line 83
    invoke-direct {v15, v1}, LtW0/g$b$e;-><init>(Lak/a;)V

    .line 84
    .line 85
    .line 86
    iput-object v15, v0, LtW0/g$b;->q:Ldagger/internal/h;

    .line 87
    .line 88
    iget-object v3, v0, LtW0/g$b;->e:Ldagger/internal/h;

    .line 89
    .line 90
    iget-object v4, v0, LtW0/g$b;->f:Ldagger/internal/h;

    .line 91
    .line 92
    iget-object v5, v0, LtW0/g$b;->g:Ldagger/internal/h;

    .line 93
    .line 94
    iget-object v6, v0, LtW0/g$b;->h:Ldagger/internal/h;

    .line 95
    .line 96
    iget-object v7, v0, LtW0/g$b;->i:Ldagger/internal/h;

    .line 97
    .line 98
    iget-object v8, v0, LtW0/g$b;->j:Ldagger/internal/h;

    .line 99
    .line 100
    iget-object v9, v0, LtW0/g$b;->k:Ldagger/internal/h;

    .line 101
    .line 102
    iget-object v10, v0, LtW0/g$b;->l:Ldagger/internal/h;

    .line 103
    .line 104
    iget-object v11, v0, LtW0/g$b;->m:Ldagger/internal/h;

    .line 105
    .line 106
    iget-object v12, v0, LtW0/g$b;->n:Ldagger/internal/h;

    .line 107
    .line 108
    iget-object v13, v0, LtW0/g$b;->o:Ldagger/internal/h;

    .line 109
    .line 110
    iget-object v14, v0, LtW0/g$b;->p:Ldagger/internal/h;

    .line 111
    .line 112
    invoke-static/range {v3 .. v15}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/I;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/I;

    .line 113
    .line 114
    .line 115
    move-result-object v1

    .line 116
    iput-object v1, v0, LtW0/g$b;->r:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/I;

    .line 117
    .line 118
    invoke-static {v1}, LtW0/x;->c(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/I;)Ldagger/internal/h;

    .line 119
    .line 120
    .line 121
    move-result-object v1

    .line 122
    iput-object v1, v0, LtW0/g$b;->s:Ldagger/internal/h;

    .line 123
    .line 124
    return-void
.end method

.method public final c(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetFragment;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LtW0/g$b;->s:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LtW0/u$b;

    .line 8
    .line 9
    invoke-static {p1, v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/t;->d(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetFragment;LtW0/u$b;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, LtW0/g$b;->a:LTZ0/a;

    .line 13
    .line 14
    invoke-static {p1, v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/t;->a(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetFragment;LTZ0/a;)V

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, LtW0/g$b;->b:LzX0/k;

    .line 18
    .line 19
    invoke-static {p1, v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/t;->c(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetFragment;LzX0/k;)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, LtW0/g$b;->c:Lak/b;

    .line 23
    .line 24
    invoke-interface {v0}, Lak/b;->a()Lck/a;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    check-cast v0, Lck/a;

    .line 33
    .line 34
    invoke-static {p1, v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/t;->b(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetFragment;Lck/a;)V

    .line 35
    .line 36
    .line 37
    return-object p1
.end method
