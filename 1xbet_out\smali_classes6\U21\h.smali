.class public final synthetic LU21/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/DSAggregatorTournamentCardsOldTitle;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/DSAggregatorTournamentCardsOldTitle;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LU21/h;->a:Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/DSAggregatorTournamentCardsOldTitle;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LU21/h;->a:Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/DSAggregatorTournamentCardsOldTitle;

    invoke-static {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/DSAggregatorTournamentCardsOldTitle;->a(Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/DSAggregatorTournamentCardsOldTitle;)Lorg/xbet/uikit/utils/z;

    move-result-object v0

    return-object v0
.end method
