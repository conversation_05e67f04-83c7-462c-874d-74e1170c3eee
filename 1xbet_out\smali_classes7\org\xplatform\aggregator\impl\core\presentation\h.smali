.class public final synthetic Lorg/xplatform/aggregator/impl/core/presentation/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Landroidx/fragment/app/Fragment;

.field public final synthetic b:Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/h;->a:Landroidx/fragment/app/Fragment;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/h;->b:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/h;->a:Landroidx/fragment/app/Fragment;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/h;->b:Lkotlin/jvm/functions/Function1;

    check-cast p1, Landroid/os/Bundle;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/core/presentation/i;->a(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;Landroid/os/Bundle;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
