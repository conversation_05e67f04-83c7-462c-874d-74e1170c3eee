.class public final Lorg/xplatform/aggregator/impl/gifts/s;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lorg/xplatform/aggregator/impl/core/presentation/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->b1:Lorg/xplatform/aggregator/impl/core/presentation/c;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;LSX0/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->k1:LSX0/c;

    .line 2
    .line 3
    return-void
.end method

.method public static c(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->o0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    return-void
.end method
