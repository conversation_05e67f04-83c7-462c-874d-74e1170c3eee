.class public interface abstract Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008`\u0018\u00002\u00020\u0001J\u0015\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u0002H&\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J1\u0010\r\u001a\u00020\u000c2\u0006\u0010\u0007\u001a\u00020\u00062\u0008\u0010\u0008\u001a\u0004\u0018\u00010\u00062\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u000b\u001a\u00020\u0006H&\u00a2\u0006\u0004\u0008\r\u0010\u000eJ1\u0010\u000f\u001a\u00020\u000c2\u0006\u0010\u0007\u001a\u00020\u00062\u0008\u0010\u0008\u001a\u0004\u0018\u00010\u00062\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u000b\u001a\u00020\u0006H&\u00a2\u0006\u0004\u0008\u000f\u0010\u000eJ\u001f\u0010\u0014\u001a\u00020\u000c2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u0012H&\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u000f\u0010\u0016\u001a\u00020\u000cH&\u00a2\u0006\u0004\u0008\u0016\u0010\u0017\u00a8\u0006\u0018"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;",
        "",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;",
        "A1",
        "()Lkotlinx/coroutines/flow/e;",
        "",
        "opponentId",
        "gameId",
        "",
        "sportId",
        "champId",
        "",
        "c3",
        "(ILjava/lang/Integer;JI)V",
        "J",
        "Lorg/xbet/betting/core/coupon/models/SingleBetGame;",
        "singleBetGame",
        "Lorg/xbet/betting/core/coupon/models/SimpleBetZip;",
        "simpleBetZip",
        "f2",
        "(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;)V",
        "j1",
        "()V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract A1()Lkotlinx/coroutines/flow/e;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract J(ILjava/lang/Integer;JI)V
.end method

.method public abstract c3(ILjava/lang/Integer;JI)V
.end method

.method public abstract f2(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;)V
    .param p1    # Lorg/xbet/betting/core/coupon/models/SingleBetGame;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/betting/core/coupon/models/SimpleBetZip;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract j1()V
.end method
