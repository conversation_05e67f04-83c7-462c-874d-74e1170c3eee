.class public final synthetic Lorg/xbet/games_section/feature/jackpot/presentation/fragments/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/a;->a:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/a;->a:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;

    invoke-static {v0, p1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->B2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Landroid/view/View;)V

    return-void
.end method
