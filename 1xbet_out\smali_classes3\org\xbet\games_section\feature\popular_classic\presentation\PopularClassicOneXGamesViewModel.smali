.class public final Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/c;
.source "SourceFile"

# interfaces
.implements Li50/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$a;,
        Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$b;,
        Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;,
        Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$d;,
        Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;,
        Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00e6\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u0003\n\u0002\u0008\u0005\n\u0002\u0010\u0000\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008/\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008)\u0018\u0000 \u00ed\u00012\u00020\u00012\u00020\u0002:\u000c\u00ee\u0001\u00ef\u0001\u00f0\u0001\u00f1\u0001\u00f2\u0001\u00f3\u0001B\u00c5\u0001\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0008\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000c\u001a\u00020\u000b\u0012\u0006\u0010\u000e\u001a\u00020\r\u0012\u0006\u0010\u0010\u001a\u00020\u000f\u0012\u0006\u0010\u0012\u001a\u00020\u0011\u0012\u0006\u0010\u0014\u001a\u00020\u0013\u0012\u0006\u0010\u0016\u001a\u00020\u0015\u0012\u0006\u0010\u0018\u001a\u00020\u0017\u0012\u0006\u0010\u001a\u001a\u00020\u0019\u0012\u0006\u0010\u001c\u001a\u00020\u001b\u0012\u0006\u0010\u001e\u001a\u00020\u001d\u0012\u0006\u0010 \u001a\u00020\u001f\u0012\u0006\u0010\"\u001a\u00020!\u0012\u0006\u0010$\u001a\u00020#\u0012\u0006\u0010&\u001a\u00020%\u0012\u0006\u0010(\u001a\u00020\'\u0012\u0006\u0010*\u001a\u00020)\u0012\u0006\u0010,\u001a\u00020+\u0012\u0008\u0008\u0001\u0010.\u001a\u00020-\u0012\u0008\u0008\u0001\u00100\u001a\u00020/\u00a2\u0006\u0004\u00081\u00102J\u001f\u00108\u001a\u0002072\u0006\u00104\u001a\u0002032\u0006\u00106\u001a\u000205H\u0002\u00a2\u0006\u0004\u00088\u00109J\u0017\u0010:\u001a\u0002072\u0006\u00104\u001a\u000203H\u0002\u00a2\u0006\u0004\u0008:\u0010;J\u0018\u0010<\u001a\u0002072\u0006\u00106\u001a\u000205H\u0082@\u00a2\u0006\u0004\u0008<\u0010=J\u0017\u0010@\u001a\u0002072\u0006\u0010?\u001a\u00020>H\u0002\u00a2\u0006\u0004\u0008@\u0010AJ%\u0010E\u001a\u0002072\u000c\u0010D\u001a\u0008\u0012\u0004\u0012\u00020C0B2\u0006\u0010?\u001a\u00020>H\u0002\u00a2\u0006\u0004\u0008E\u0010FJ\u0017\u0010G\u001a\u0002072\u0006\u00104\u001a\u000203H\u0002\u00a2\u0006\u0004\u0008G\u0010;J\u000f\u0010H\u001a\u000207H\u0002\u00a2\u0006\u0004\u0008H\u0010IJ\u0017\u0010L\u001a\u0002072\u0006\u0010K\u001a\u00020JH\u0002\u00a2\u0006\u0004\u0008L\u0010MJ\u000f\u0010N\u001a\u000207H\u0002\u00a2\u0006\u0004\u0008N\u0010IJ\u0017\u0010O\u001a\u0002072\u0006\u0010K\u001a\u00020JH\u0002\u00a2\u0006\u0004\u0008O\u0010MJ\u0017\u0010P\u001a\u0002072\u0006\u0010K\u001a\u00020JH\u0002\u00a2\u0006\u0004\u0008P\u0010MJ\u0017\u0010Q\u001a\u0002072\u0006\u0010K\u001a\u00020JH\u0002\u00a2\u0006\u0004\u0008Q\u0010MJ\u0010\u0010S\u001a\u00020RH\u0082@\u00a2\u0006\u0004\u0008S\u0010TJ#\u0010W\u001a\u0008\u0012\u0004\u0012\u00020U0B2\u000c\u0010V\u001a\u0008\u0012\u0004\u0012\u00020U0BH\u0002\u00a2\u0006\u0004\u0008W\u0010XJ\u001f\u0010]\u001a\u0002072\u0006\u0010Z\u001a\u00020Y2\u0006\u0010\\\u001a\u00020[H\u0002\u00a2\u0006\u0004\u0008]\u0010^JM\u0010i\u001a\u0008\u0012\u0004\u0012\u00020h0B2\u000c\u0010a\u001a\u0008\u0012\u0004\u0012\u00020`0_2\u000c\u0010c\u001a\u0008\u0012\u0004\u0012\u00020b0_2\u000c\u0010e\u001a\u0008\u0012\u0004\u0012\u00020d0_2\u000c\u0010g\u001a\u0008\u0012\u0004\u0012\u00020f0_H\u0002\u00a2\u0006\u0004\u0008i\u0010jJ\u0017\u0010l\u001a\u0002072\u0006\u0010k\u001a\u00020RH\u0002\u00a2\u0006\u0004\u0008l\u0010mJ\u0017\u0010o\u001a\u0002072\u0006\u0010n\u001a\u00020RH\u0002\u00a2\u0006\u0004\u0008o\u0010mJ\u0017\u0010r\u001a\u0002072\u0006\u0010q\u001a\u00020pH\u0002\u00a2\u0006\u0004\u0008r\u0010sJ\u000f\u0010t\u001a\u00020RH\u0002\u00a2\u0006\u0004\u0008t\u0010uJ#\u0010x\u001a\u00020R\"\u0008\u0008\u0000\u0010w*\u00020v*\u0008\u0012\u0004\u0012\u00028\u00000_H\u0002\u00a2\u0006\u0004\u0008x\u0010yJ\u0013\u0010{\u001a\u00020R*\u00020zH\u0002\u00a2\u0006\u0004\u0008{\u0010|J3\u0010\u0081\u0001\u001a\u0002072\u0006\u0010~\u001a\u00020}2\u0006\u00106\u001a\u0002052\u0006\u0010\u007f\u001a\u00020}2\u0007\u0010\u0080\u0001\u001a\u00020RH\u0016\u00a2\u0006\u0006\u0008\u0081\u0001\u0010\u0082\u0001J\"\u0010\u0083\u0001\u001a\u0002072\u0006\u0010~\u001a\u00020}2\u0006\u0010\u007f\u001a\u00020}H\u0016\u00a2\u0006\u0006\u0008\u0083\u0001\u0010\u0084\u0001J\u001b\u0010\u0086\u0001\u001a\u0002072\u0007\u0010\u0085\u0001\u001a\u00020[H\u0016\u00a2\u0006\u0006\u0008\u0086\u0001\u0010\u0087\u0001J\u0017\u0010\u0089\u0001\u001a\t\u0012\u0004\u0012\u00020z0\u0088\u0001\u00a2\u0006\u0006\u0008\u0089\u0001\u0010\u008a\u0001J\u0018\u0010\u008d\u0001\u001a\n\u0012\u0005\u0012\u00030\u008c\u00010\u008b\u0001\u00a2\u0006\u0006\u0008\u008d\u0001\u0010\u008e\u0001J\u001d\u0010\u0090\u0001\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020h0B0\u008f\u0001\u00a2\u0006\u0006\u0008\u0090\u0001\u0010\u0091\u0001J\u0018\u0010\u0093\u0001\u001a\n\u0012\u0005\u0012\u00030\u0092\u00010\u008b\u0001\u00a2\u0006\u0006\u0008\u0093\u0001\u0010\u008e\u0001R\u0016\u0010\u0004\u001a\u00020\u00038\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0094\u0001\u0010\u0095\u0001R\u0016\u0010\u0006\u001a\u00020\u00058\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0096\u0001\u0010\u0097\u0001R\u0016\u0010\u0008\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0098\u0001\u0010\u0099\u0001R\u0016\u0010\n\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009a\u0001\u0010\u009b\u0001R\u0016\u0010\u000c\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009c\u0001\u0010\u009d\u0001R\u0016\u0010\u000e\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009e\u0001\u0010\u009f\u0001R\u0016\u0010\u0010\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a0\u0001\u0010\u00a1\u0001R\u0016\u0010\u0012\u001a\u00020\u00118\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a2\u0001\u0010\u00a3\u0001R\u0016\u0010\u0014\u001a\u00020\u00138\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a4\u0001\u0010\u00a5\u0001R\u0016\u0010\u0016\u001a\u00020\u00158\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a6\u0001\u0010\u00a7\u0001R\u0016\u0010\u0018\u001a\u00020\u00178\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a8\u0001\u0010\u00a9\u0001R\u0016\u0010\u001a\u001a\u00020\u00198\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00aa\u0001\u0010\u00ab\u0001R\u0016\u0010\u001c\u001a\u00020\u001b8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ac\u0001\u0010\u00ad\u0001R\u0016\u0010\u001e\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ae\u0001\u0010\u00af\u0001R\u0016\u0010 \u001a\u00020\u001f8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b0\u0001\u0010\u00b1\u0001R\u0016\u0010\"\u001a\u00020!8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b2\u0001\u0010\u00b3\u0001R\u0016\u0010$\u001a\u00020#8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b4\u0001\u0010\u00b5\u0001R\u0016\u0010&\u001a\u00020%8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b6\u0001\u0010\u00b7\u0001R\u0016\u0010(\u001a\u00020\'8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b8\u0001\u0010\u00b9\u0001R\u0016\u0010*\u001a\u00020)8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ba\u0001\u0010\u00bb\u0001R\u0016\u0010,\u001a\u00020+8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bc\u0001\u0010\u00bd\u0001R\u0016\u0010.\u001a\u00020-8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00be\u0001\u0010\u00bf\u0001R\u0016\u00100\u001a\u00020/8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c0\u0001\u0010\u00c1\u0001R\u001d\u0010\u00c4\u0001\u001a\t\u0012\u0004\u0012\u00020z0\u00c2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008r\u0010\u00c3\u0001R\u001f\u0010\u00c8\u0001\u001a\n\u0012\u0005\u0012\u00030\u008c\u00010\u00c5\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c6\u0001\u0010\u00c7\u0001R\u001f\u0010\u00ca\u0001\u001a\n\u0012\u0005\u0012\u00030\u0092\u00010\u00c5\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c9\u0001\u0010\u00c7\u0001R\u001c\u0010\u00ce\u0001\u001a\u0005\u0018\u00010\u00cb\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00cc\u0001\u0010\u00cd\u0001R\u001f\u0010\u00d1\u0001\u001a\u0008\u0012\u0004\u0012\u00020Y0B8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00cf\u0001\u0010\u00d0\u0001R\u001c\u0010\u00d3\u0001\u001a\u0005\u0018\u00010\u00cb\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00d2\u0001\u0010\u00cd\u0001R\u001c\u0010\u00d5\u0001\u001a\u0005\u0018\u00010\u00cb\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00d4\u0001\u0010\u00cd\u0001R\u001c\u0010\u00d7\u0001\u001a\u0005\u0018\u00010\u00cb\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00d6\u0001\u0010\u00cd\u0001R\u001c\u0010\u00d9\u0001\u001a\u0005\u0018\u00010\u00cb\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00d8\u0001\u0010\u00cd\u0001R\u0019\u0010\u00dc\u0001\u001a\u00020R8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00da\u0001\u0010\u00db\u0001R\u001b\u0010\u00df\u0001\u001a\u0004\u0018\u0001038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00dd\u0001\u0010\u00de\u0001R\u0019\u0010\u00e1\u0001\u001a\u00020R8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00e0\u0001\u0010\u00db\u0001R$\u0010\u00e3\u0001\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020`0_0\u00c2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e2\u0001\u0010\u00c3\u0001R$\u0010\u00e5\u0001\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020b0_0\u00c2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e4\u0001\u0010\u00c3\u0001R$\u0010\u00e7\u0001\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020d0_0\u00c2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e6\u0001\u0010\u00c3\u0001R$\u0010\u00e9\u0001\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020f0_0\u00c2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e8\u0001\u0010\u00c3\u0001R$\u0010\u00ec\u0001\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020h0B0\u008f\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ea\u0001\u0010\u00eb\u0001\u00a8\u0006\u00f4\u0001"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/c;",
        "Li50/a;",
        "Lorg/xbet/core/domain/usecases/d;",
        "choiceErrorActionScenario",
        "Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;",
        "getGameItemsByCategoryScenario",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Li8/j;",
        "getServiceUseCase",
        "Lorg/xbet/core/domain/usecases/game_info/n;",
        "getGameMetaUseCase",
        "Lm8/a;",
        "coroutineDispatchers",
        "LJT/c;",
        "addOneXGameLastActionUseCase",
        "LwX0/a;",
        "appScreensProvider",
        "LVg0/a;",
        "promotionsNewsScreenFactory",
        "Lw30/o;",
        "getGamesSectionWalletUseCase",
        "LR40/c;",
        "jackpotUseCase",
        "Lgk/b;",
        "getCurrencyByIdUseCase",
        "Lw30/i;",
        "getDemoAvailableForGameScenario",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "Lw30/b;",
        "clearAllGamesInfoUseCase",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "Lcom/xbet/onexuser/domain/user/c;",
        "userInteractor",
        "Ltf0/a;",
        "popularClassicFeature",
        "Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase;",
        "popularOneXGamesLuckyWheelUseCase",
        "Lv30/a;",
        "getCenterOfAttentionGameScenario",
        "Lorg/xbet/games_section/feature/popular_classic/presentation/a;",
        "buildContentDelegate",
        "LwX0/c;",
        "router",
        "Landroidx/lifecycle/Q;",
        "savedStateHandle",
        "<init>",
        "(Lorg/xbet/core/domain/usecases/d;Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;Lorg/xbet/ui_common/utils/internet/a;Li8/j;Lorg/xbet/core/domain/usecases/game_info/n;Lm8/a;LJT/c;LwX0/a;LVg0/a;Lw30/o;LR40/c;Lgk/b;Lw30/i;Lp9/c;Lw30/b;LSX0/c;Lcom/xbet/onexuser/domain/user/c;Ltf0/a;Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase;Lv30/a;Lorg/xbet/games_section/feature/popular_classic/presentation/a;LwX0/c;Landroidx/lifecycle/Q;)V",
        "",
        "gameId",
        "Lk50/f;",
        "oneXGameWithCategoryUiModel",
        "",
        "P4",
        "(JLk50/f;)V",
        "p4",
        "(J)V",
        "Q4",
        "(Lk50/f;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;",
        "gameType",
        "L4",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V",
        "",
        "Lg9/i;",
        "balances",
        "T4",
        "(Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V",
        "R4",
        "K4",
        "()V",
        "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;",
        "requestType",
        "w4",
        "(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;)V",
        "u4",
        "B4",
        "y4",
        "D4",
        "",
        "J4",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lf50/a;",
        "gameItemsWithCategoryList",
        "s4",
        "(Ljava/util/List;)Ljava/util/List;",
        "Lorg/xplatform/banners/api/domain/models/BannerModel;",
        "banner",
        "",
        "position",
        "N4",
        "(Lorg/xplatform/banners/api/domain/models/BannerModel;I)V",
        "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;",
        "Lk50/c;",
        "jackpotState",
        "Lk50/h;",
        "gamesState",
        "Lk50/d;",
        "luckyWheelState",
        "Lk50/a;",
        "centerOfAttentionState",
        "LVX0/i;",
        "A4",
        "(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;)Ljava/util/List;",
        "emptyGamesEvents",
        "V4",
        "(Z)V",
        "emptyGameEvents",
        "U4",
        "",
        "throwable",
        "H4",
        "(Ljava/lang/Throwable;)V",
        "r4",
        "()Z",
        "",
        "T",
        "q4",
        "(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;)Z",
        "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e;",
        "I4",
        "(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e;)Z",
        "",
        "screenName",
        "categoryId",
        "fromBanner",
        "Q1",
        "(Ljava/lang/String;Lk50/f;Ljava/lang/String;Z)V",
        "k",
        "(Ljava/lang/String;Ljava/lang/String;)V",
        "bannerId",
        "X1",
        "(I)V",
        "Lkotlinx/coroutines/flow/e;",
        "G4",
        "()Lkotlinx/coroutines/flow/e;",
        "Lkotlinx/coroutines/flow/Z;",
        "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$d;",
        "F4",
        "()Lkotlinx/coroutines/flow/Z;",
        "Lkotlinx/coroutines/flow/f0;",
        "x4",
        "()Lkotlinx/coroutines/flow/f0;",
        "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$a;",
        "t4",
        "x1",
        "Lorg/xbet/core/domain/usecases/d;",
        "y1",
        "Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;",
        "F1",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "H1",
        "Li8/j;",
        "I1",
        "Lorg/xbet/core/domain/usecases/game_info/n;",
        "P1",
        "Lm8/a;",
        "S1",
        "LJT/c;",
        "V1",
        "LwX0/a;",
        "b2",
        "LVg0/a;",
        "v2",
        "Lw30/o;",
        "x2",
        "LR40/c;",
        "y2",
        "Lgk/b;",
        "F2",
        "Lw30/i;",
        "H2",
        "Lp9/c;",
        "I2",
        "Lw30/b;",
        "P2",
        "LSX0/c;",
        "S2",
        "Lcom/xbet/onexuser/domain/user/c;",
        "V2",
        "Ltf0/a;",
        "X2",
        "Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase;",
        "F3",
        "Lv30/a;",
        "H3",
        "Lorg/xbet/games_section/feature/popular_classic/presentation/a;",
        "I3",
        "LwX0/c;",
        "S3",
        "Landroidx/lifecycle/Q;",
        "Lkotlinx/coroutines/flow/V;",
        "Lkotlinx/coroutines/flow/V;",
        "viewStateFlow",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "X4",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "notification",
        "v5",
        "actionBannerEvent",
        "Lkotlinx/coroutines/x0;",
        "w5",
        "Lkotlinx/coroutines/x0;",
        "networkConnectionJob",
        "x5",
        "Ljava/util/List;",
        "bannerList",
        "y5",
        "loadJackpotJob",
        "z5",
        "loadGamesJob",
        "A5",
        "loadLuckyWheelJob",
        "B5",
        "loadCenterOfAttentionJob",
        "C5",
        "Z",
        "gameRequestWithAuth",
        "D5",
        "Ljava/lang/Long;",
        "lastUpdateJackpotTime",
        "E5",
        "firstRequest",
        "F5",
        "jackpotInfoModelFlow",
        "G5",
        "oneXGamesWithCategoryListFlow",
        "H5",
        "luckyWheelModelFlow",
        "I5",
        "centerOfAttentionGameFlow",
        "J5",
        "Lkotlinx/coroutines/flow/f0;",
        "mutableContentListsState",
        "K5",
        "RequestType",
        "e",
        "d",
        "a",
        "c",
        "b",
        "popular_classic_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final K5:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public A5:Lkotlinx/coroutines/x0;

.field public B5:Lkotlinx/coroutines/x0;

.field public C5:Z

.field public D5:Ljava/lang/Long;

.field public E5:Z

.field public final F1:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:Lw30/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F3:Lv30/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c<",
            "Lk50/c;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final G5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c<",
            "Lk50/h;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Li8/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H3:Lorg/xbet/games_section/feature/popular_classic/presentation/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H4:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c<",
            "Lk50/d;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lorg/xbet/core/domain/usecases/game_info/n;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lw30/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I3:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c<",
            "Lk50/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final J5:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:LJT/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:Lcom/xbet/onexuser/domain/user/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S3:Landroidx/lifecycle/Q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:Ltf0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X2:Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:LVg0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lw30/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public w5:Lkotlinx/coroutines/x0;

.field public final x1:Lorg/xbet/core/domain/usecases/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:LR40/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public x5:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:Lgk/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public y5:Lkotlinx/coroutines/x0;

.field public z5:Lkotlinx/coroutines/x0;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->K5:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$b;

    return-void
.end method

.method public constructor <init>(Lorg/xbet/core/domain/usecases/d;Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;Lorg/xbet/ui_common/utils/internet/a;Li8/j;Lorg/xbet/core/domain/usecases/game_info/n;Lm8/a;LJT/c;LwX0/a;LVg0/a;Lw30/o;LR40/c;Lgk/b;Lw30/i;Lp9/c;Lw30/b;LSX0/c;Lcom/xbet/onexuser/domain/user/c;Ltf0/a;Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase;Lv30/a;Lorg/xbet/games_section/feature/popular_classic/presentation/a;LwX0/c;Landroidx/lifecycle/Q;)V
    .locals 4
    .param p1    # Lorg/xbet/core/domain/usecases/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/core/domain/usecases/game_info/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LJT/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LVg0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lw30/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LR40/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lgk/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lw30/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lw30/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lcom/xbet/onexuser/domain/user/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Ltf0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Lv30/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # Lorg/xbet/games_section/feature/popular_classic/presentation/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Landroidx/lifecycle/Q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p23

    .line 2
    .line 3
    invoke-static/range {p21 .. p21}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {p0, v0, v1}, Lorg/xbet/ui_common/viewmodel/core/c;-><init>(Landroidx/lifecycle/Q;Ljava/util/List;)V

    .line 8
    .line 9
    .line 10
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->x1:Lorg/xbet/core/domain/usecases/d;

    .line 11
    .line 12
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->y1:Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;

    .line 13
    .line 14
    iput-object p3, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->F1:Lorg/xbet/ui_common/utils/internet/a;

    .line 15
    .line 16
    iput-object p4, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H1:Li8/j;

    .line 17
    .line 18
    iput-object p5, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I1:Lorg/xbet/core/domain/usecases/game_info/n;

    .line 19
    .line 20
    iput-object p6, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->P1:Lm8/a;

    .line 21
    .line 22
    iput-object p7, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->S1:LJT/c;

    .line 23
    .line 24
    iput-object p8, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->V1:LwX0/a;

    .line 25
    .line 26
    iput-object p9, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->b2:LVg0/a;

    .line 27
    .line 28
    iput-object p10, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->v2:Lw30/o;

    .line 29
    .line 30
    iput-object p11, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->x2:LR40/c;

    .line 31
    .line 32
    move-object/from16 p1, p12

    .line 33
    .line 34
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->y2:Lgk/b;

    .line 35
    .line 36
    move-object/from16 p1, p13

    .line 37
    .line 38
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->F2:Lw30/i;

    .line 39
    .line 40
    move-object/from16 p1, p14

    .line 41
    .line 42
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H2:Lp9/c;

    .line 43
    .line 44
    move-object/from16 p1, p15

    .line 45
    .line 46
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I2:Lw30/b;

    .line 47
    .line 48
    move-object/from16 p1, p16

    .line 49
    .line 50
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->P2:LSX0/c;

    .line 51
    .line 52
    move-object/from16 p1, p17

    .line 53
    .line 54
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->S2:Lcom/xbet/onexuser/domain/user/c;

    .line 55
    .line 56
    move-object/from16 p1, p18

    .line 57
    .line 58
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->V2:Ltf0/a;

    .line 59
    .line 60
    move-object/from16 p1, p19

    .line 61
    .line 62
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->X2:Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase;

    .line 63
    .line 64
    move-object/from16 p1, p20

    .line 65
    .line 66
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->F3:Lv30/a;

    .line 67
    .line 68
    move-object/from16 p1, p21

    .line 69
    .line 70
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H3:Lorg/xbet/games_section/feature/popular_classic/presentation/a;

    .line 71
    .line 72
    move-object/from16 p1, p22

    .line 73
    .line 74
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I3:LwX0/c;

    .line 75
    .line 76
    iput-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->S3:Landroidx/lifecycle/Q;

    .line 77
    .line 78
    sget-object p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e$d;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e$d;

    .line 79
    .line 80
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 85
    .line 86
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 87
    .line 88
    const/4 p2, 0x0

    .line 89
    const/4 p3, 0x0

    .line 90
    const/4 p4, 0x3

    .line 91
    invoke-direct {p1, p2, p3, p4, p3}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 92
    .line 93
    .line 94
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->X4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 95
    .line 96
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 97
    .line 98
    invoke-direct {p1, p2, p3, p4, p3}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 99
    .line 100
    .line 101
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->v5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 102
    .line 103
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 104
    .line 105
    .line 106
    move-result-object p1

    .line 107
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->x5:Ljava/util/List;

    .line 108
    .line 109
    sget-object p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$a;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$a;

    .line 110
    .line 111
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 112
    .line 113
    .line 114
    move-result-object p2

    .line 115
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->F5:Lkotlinx/coroutines/flow/V;

    .line 116
    .line 117
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 118
    .line 119
    .line 120
    move-result-object p4

    .line 121
    iput-object p4, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->G5:Lkotlinx/coroutines/flow/V;

    .line 122
    .line 123
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 124
    .line 125
    .line 126
    move-result-object p5

    .line 127
    iput-object p5, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H5:Lkotlinx/coroutines/flow/V;

    .line 128
    .line 129
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 130
    .line 131
    .line 132
    move-result-object p1

    .line 133
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I5:Lkotlinx/coroutines/flow/V;

    .line 134
    .line 135
    new-instance v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$mutableContentListsState$1;

    .line 136
    .line 137
    invoke-direct {v0, p0, p3}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$mutableContentListsState$1;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 138
    .line 139
    .line 140
    invoke-static {p2, p4, p5, p1, v0}, Lkotlinx/coroutines/flow/g;->q(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/p;)Lkotlinx/coroutines/flow/e;

    .line 141
    .line 142
    .line 143
    move-result-object p1

    .line 144
    new-instance p2, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$mutableContentListsState$2;

    .line 145
    .line 146
    invoke-direct {p2, p0, p3}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$mutableContentListsState$2;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 147
    .line 148
    .line 149
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 150
    .line 151
    .line 152
    move-result-object p1

    .line 153
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 154
    .line 155
    .line 156
    move-result-object p2

    .line 157
    invoke-interface {p6}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 158
    .line 159
    .line 160
    move-result-object p3

    .line 161
    invoke-static {p2, p3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 162
    .line 163
    .line 164
    move-result-object p2

    .line 165
    sget-object p3, Lkotlinx/coroutines/flow/d0;->a:Lkotlinx/coroutines/flow/d0$a;

    .line 166
    .line 167
    const/4 p4, 0x3

    .line 168
    const/4 p5, 0x0

    .line 169
    const-wide/16 v0, 0x0

    .line 170
    .line 171
    const-wide/16 v2, 0x0

    .line 172
    .line 173
    move-object p9, p5

    .line 174
    move-wide p4, v0

    .line 175
    move-wide p6, v2

    .line 176
    const/4 p8, 0x3

    .line 177
    invoke-static/range {p3 .. p9}, Lkotlinx/coroutines/flow/d0$a;->b(Lkotlinx/coroutines/flow/d0$a;JJILjava/lang/Object;)Lkotlinx/coroutines/flow/d0;

    .line 178
    .line 179
    .line 180
    move-result-object p3

    .line 181
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 182
    .line 183
    .line 184
    move-result-object p4

    .line 185
    invoke-static {p1, p2, p3, p4}, Lkotlinx/coroutines/flow/g;->v0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlinx/coroutines/flow/d0;Ljava/lang/Object;)Lkotlinx/coroutines/flow/f0;

    .line 186
    .line 187
    .line 188
    move-result-object p1

    .line 189
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->J5:Lkotlinx/coroutines/flow/f0;

    .line 190
    .line 191
    return-void
.end method

.method public static final synthetic A3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)LJT/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->S1:LJT/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)LwX0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->V1:LwX0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Ljava/util/List;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->x5:Ljava/util/List;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final C4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->F5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$c;

    .line 4
    .line 5
    invoke-direct {v1, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$c;-><init>(Ljava/lang/Throwable;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    new-instance p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$2$1;

    .line 12
    .line 13
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->x1:Lorg/xbet/core/domain/usecases/d;

    .line 14
    .line 15
    invoke-direct {p1, p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$2$1;-><init>(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 19
    .line 20
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lorg/xbet/core/domain/usecases/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->x1:Lorg/xbet/core/domain/usecases/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final E4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;

    .line 4
    .line 5
    invoke-interface {p0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final synthetic F3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->w4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic G3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->E5:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic H3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->C5:Z

    .line 2
    .line 3
    return p0
.end method

.method private final H4(Ljava/lang/Throwable;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->P1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    sget-object v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$handleGameError$1;->INSTANCE:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$handleGameError$1;

    .line 12
    .line 13
    new-instance v5, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$handleGameError$2;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$handleGameError$2;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/16 v6, 0xa

    .line 20
    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public static final synthetic I3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lp9/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H2:Lp9/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic J3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lv30/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->F3:Lv30/a;

    .line 2
    .line 3
    return-object p0
.end method

.method private final J4(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/lang/Boolean;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$luckyWheelEnabled$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$luckyWheelEnabled$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$luckyWheelEnabled$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$luckyWheelEnabled$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$luckyWheelEnabled$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$luckyWheelEnabled$1;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$luckyWheelEnabled$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$luckyWheelEnabled$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H1:Li8/j;

    .line 54
    .line 55
    invoke-interface {p1}, Li8/j;->invoke()Ljava/lang/String;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    iget-object v2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I1:Lorg/xbet/core/domain/usecases/game_info/n;

    .line 60
    .line 61
    sget-object v4, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->LUCKY_WHEEL:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 62
    .line 63
    invoke-virtual {v4}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->getGameId()J

    .line 64
    .line 65
    .line 66
    move-result-wide v4

    .line 67
    iput v3, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$luckyWheelEnabled$1;->label:I

    .line 68
    .line 69
    invoke-virtual {v2, v4, v5, p1, v0}, Lorg/xbet/core/domain/usecases/game_info/n;->a(JLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    if-ne p1, v1, :cond_3

    .line 74
    .line 75
    return-object v1

    .line 76
    :cond_3
    :goto_1
    check-cast p1, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;

    .line 77
    .line 78
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getEnable()Z

    .line 79
    .line 80
    .line 81
    move-result p1

    .line 82
    invoke-static {p1}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    return-object p1
.end method

.method public static final synthetic K3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lgk/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->y2:Lgk/b;

    .line 2
    .line 3
    return-object p0
.end method

.method private final K4()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->w5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->F1:Lorg/xbet/ui_common/utils/internet/a;

    .line 14
    .line 15
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->E(Lkotlinx/coroutines/flow/e;I)Lkotlinx/coroutines/flow/e;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$observeConnection$1;

    .line 24
    .line 25
    const/4 v2, 0x0

    .line 26
    invoke-direct {v1, p0, v2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$observeConnection$1;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    iget-object v3, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->P1:Lm8/a;

    .line 38
    .line 39
    invoke-interface {v3}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    invoke-static {v1, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    new-instance v3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$observeConnection$2;

    .line 48
    .line 49
    invoke-direct {v3, p0, v2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$observeConnection$2;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 50
    .line 51
    .line 52
    invoke-static {v0, v1, v3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->w5:Lkotlinx/coroutines/x0;

    .line 57
    .line 58
    return-void
.end method

.method public static final synthetic L3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lw30/i;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->F2:Lw30/i;

    .line 2
    .line 3
    return-object p0
.end method

.method private final L4(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->P1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$1;-><init>(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    new-instance v2, Lorg/xbet/games_section/feature/popular_classic/presentation/m;

    .line 17
    .line 18
    invoke-direct {v2, p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/m;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)V

    .line 19
    .line 20
    .line 21
    new-instance v5, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;

    .line 22
    .line 23
    const/4 v4, 0x0

    .line 24
    invoke-direct {v5, p0, p1, v4}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    const/16 v6, 0x8

    .line 28
    .line 29
    const/4 v7, 0x0

    .line 30
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public static final synthetic M3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->y1:Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final M4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e$c;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e$c;-><init>(Z)V

    .line 7
    .line 8
    .line 9
    invoke-interface {p0, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final synthetic N3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lw30/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->v2:Lw30/o;

    .line 2
    .line 3
    return-object p0
.end method

.method private final N4(Lorg/xplatform/banners/api/domain/models/BannerModel;I)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->P1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/j;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/j;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openBannerInfo$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, p2, v2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openBannerInfo$2;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public static final synthetic O3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->A4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final O4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openBannerInfo$1$1;->INSTANCE:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openBannerInfo$1$1;

    .line 6
    .line 7
    new-instance v5, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openBannerInfo$1$2;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openBannerInfo$1$2;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    const/16 v6, 0xe

    .line 14
    .line 15
    const/4 v7, 0x0

    .line 16
    const/4 v3, 0x0

    .line 17
    const/4 v4, 0x0

    .line 18
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 19
    .line 20
    .line 21
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 22
    .line 23
    return-object p0
.end method

.method public static final synthetic P3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->F5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic Q3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)LR40/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->x2:LR40/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic R3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Ljava/lang/Long;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->D5:Ljava/lang/Long;

    .line 2
    .line 3
    return-object p0
.end method

.method private final R4(J)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/l;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/games_section/feature/popular_classic/presentation/l;-><init>()V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openWebPage$2;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, p1, p2, v2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openWebPage$2;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;JLkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public static final synthetic S3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->y5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final S4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic T3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method private final T4(Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lg9/i;",
            ">;",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    if-nez p1, :cond_0

    .line 6
    .line 7
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->X4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 8
    .line 9
    sget-object p2, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$d$a;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$d$a;

    .line 10
    .line 11
    invoke-virtual {p1, p2}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    invoke-static {p2}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 16
    .line 17
    .line 18
    move-result-wide p1

    .line 19
    invoke-direct {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->R4(J)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final synthetic U3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->w5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method private final U4(Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->V4(Z)V

    .line 2
    .line 3
    .line 4
    sget-object p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;->INIT:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;

    .line 5
    .line 6
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->w4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public static final synthetic V3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->G5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method private final V4(Z)V
    .locals 14

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e$a;

    .line 4
    .line 5
    iget-object v2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->P2:LSX0/c;

    .line 6
    .line 7
    sget-object v3, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 8
    .line 9
    if-eqz p1, :cond_0

    .line 10
    .line 11
    sget v4, Lpb/k;->currently_no_events:I

    .line 12
    .line 13
    :goto_0
    move v8, v4

    .line 14
    goto :goto_1

    .line 15
    :cond_0
    sget v4, Lpb/k;->data_retrieval_error:I

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :goto_1
    if-eqz p1, :cond_1

    .line 19
    .line 20
    sget v4, Lpb/k;->refresh_data:I

    .line 21
    .line 22
    :goto_2
    move v10, v4

    .line 23
    goto :goto_3

    .line 24
    :cond_1
    sget v4, Lpb/k;->try_again_text:I

    .line 25
    .line 26
    goto :goto_2

    .line 27
    :goto_3
    new-instance v11, Lorg/xbet/games_section/feature/popular_classic/presentation/h;

    .line 28
    .line 29
    invoke-direct {v11, p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/h;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Z)V

    .line 30
    .line 31
    .line 32
    const/16 v12, 0x5e

    .line 33
    .line 34
    const/4 v13, 0x0

    .line 35
    const/4 v4, 0x0

    .line 36
    const/4 v5, 0x0

    .line 37
    const/4 v6, 0x0

    .line 38
    const/4 v7, 0x0

    .line 39
    const/4 v9, 0x0

    .line 40
    invoke-static/range {v2 .. v13}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    invoke-direct {v1, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 45
    .line 46
    .line 47
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 48
    .line 49
    .line 50
    return-void
.end method

.method public static final synthetic W3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Ltf0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->V2:Ltf0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final W4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Z)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->U4(Z)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic X3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->X2:Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic Y3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)LVg0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->b2:LVg0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic Z3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)LwX0/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I3:LwX0/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic a4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lcom/xbet/onexuser/domain/user/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->S2:Lcom/xbet/onexuser/domain/user/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic b4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic c4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic d4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e;)Z
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e;)Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic e4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->J4(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic f4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->K4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic g4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->L4(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic h4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lorg/xplatform/banners/api/domain/models/BannerModel;I)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->N4(Lorg/xplatform/banners/api/domain/models/BannerModel;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic i4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lk50/f;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->Q4(Lk50/f;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic j4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;J)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->R4(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic k4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->T4(Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic l4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->E5:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic m4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->C5:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic n4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Long;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->D5:Ljava/lang/Long;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic o4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->V4(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic p3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->C4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final p4(J)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->P1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$addLastAction$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$addLastAction$1;-><init>(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$addLastAction$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, p2, v2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$addLastAction$2;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;JLkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public static synthetic q3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->O4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->v4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->z4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final s4(Ljava/util/List;)Ljava/util/List;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lf50/a;",
            ">;)",
            "Ljava/util/List<",
            "Lf50/a;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    const/16 v1, 0xa

    .line 4
    .line 5
    invoke-static {p1, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 10
    .line 11
    .line 12
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    if-eqz v1, :cond_2

    .line 21
    .line 22
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    move-object v2, v1

    .line 27
    check-cast v2, Lf50/a;

    .line 28
    .line 29
    invoke-virtual {v2}, Lf50/a;->e()Ljava/util/List;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    new-instance v3, Ljava/util/ArrayList;

    .line 34
    .line 35
    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 36
    .line 37
    .line 38
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    :cond_0
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 43
    .line 44
    .line 45
    move-result v4

    .line 46
    if-eqz v4, :cond_1

    .line 47
    .line 48
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v4

    .line 52
    move-object v5, v4

    .line 53
    check-cast v5, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 54
    .line 55
    invoke-virtual {v5}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->f()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 56
    .line 57
    .line 58
    move-result-object v6

    .line 59
    invoke-static {v6}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 60
    .line 61
    .line 62
    move-result-wide v6

    .line 63
    sget-object v8, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->LUCKY_WHEEL:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 64
    .line 65
    invoke-virtual {v8}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->getGameId()J

    .line 66
    .line 67
    .line 68
    move-result-wide v8

    .line 69
    cmp-long v10, v6, v8

    .line 70
    .line 71
    if-eqz v10, :cond_0

    .line 72
    .line 73
    invoke-virtual {v5}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->b()Z

    .line 74
    .line 75
    .line 76
    move-result v5

    .line 77
    if-eqz v5, :cond_0

    .line 78
    .line 79
    invoke-interface {v3, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 80
    .line 81
    .line 82
    goto :goto_1

    .line 83
    :cond_1
    const/4 v6, 0x6

    .line 84
    const/4 v7, 0x0

    .line 85
    const/4 v4, 0x0

    .line 86
    const/4 v5, 0x0

    .line 87
    invoke-static/range {v2 .. v7}, Lf50/a;->b(Lf50/a;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lf50/a;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    invoke-interface {v0, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 92
    .line 93
    .line 94
    goto :goto_0

    .line 95
    :cond_2
    return-object v0
.end method

.method public static synthetic t3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Z)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->W4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Z)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic u3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->M4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final u4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->P1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/f;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/f;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    iput-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->B5:Lkotlinx/coroutines/x0;

    .line 31
    .line 32
    return-void
.end method

.method public static synthetic v3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->E4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final v4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;

    .line 4
    .line 5
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    new-instance p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$1$1;

    .line 9
    .line 10
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->x1:Lorg/xbet/core/domain/usecases/d;

    .line 11
    .line 12
    invoke-direct {p1, p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$1$1;-><init>(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static synthetic w3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->S4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;J)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->p4(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic y3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/util/List;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->s4(Ljava/util/List;)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->v5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final z4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->G5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$c;

    .line 4
    .line 5
    invoke-direct {v1, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$c;-><init>(Ljava/lang/Throwable;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    new-instance p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$2$1;

    .line 12
    .line 13
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->x1:Lorg/xbet/core/domain/usecases/d;

    .line 14
    .line 15
    invoke-direct {p1, p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$2$1;-><init>(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 19
    .line 20
    return-object p0
.end method


# virtual methods
.method public final A4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c<",
            "+",
            "Lk50/c;",
            ">;",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c<",
            "+",
            "Lk50/h;",
            ">;",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c<",
            "+",
            "Lk50/d;",
            ">;",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c<",
            "+",
            "Lk50/a;",
            ">;)",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$c;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 6
    .line 7
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    instance-of p1, p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e$a;

    .line 12
    .line 13
    if-nez p1, :cond_0

    .line 14
    .line 15
    const/4 p1, 0x0

    .line 16
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->V4(Z)V

    .line 17
    .line 18
    .line 19
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    return-object p1

    .line 24
    :cond_1
    instance-of v0, p2, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;

    .line 25
    .line 26
    if-eqz v0, :cond_3

    .line 27
    .line 28
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 29
    .line 30
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    instance-of p1, p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e$a;

    .line 35
    .line 36
    if-nez p1, :cond_2

    .line 37
    .line 38
    const/4 p1, 0x1

    .line 39
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->V4(Z)V

    .line 40
    .line 41
    .line 42
    :cond_2
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    return-object p1

    .line 47
    :cond_3
    instance-of v0, p2, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 48
    .line 49
    if-eqz v0, :cond_4

    .line 50
    .line 51
    instance-of v1, p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;

    .line 52
    .line 53
    if-nez v1, :cond_4

    .line 54
    .line 55
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 56
    .line 57
    sget-object v2, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e$b;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e$b;

    .line 58
    .line 59
    invoke-interface {v1, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 60
    .line 61
    .line 62
    :cond_4
    instance-of v1, p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 63
    .line 64
    if-eqz v1, :cond_5

    .line 65
    .line 66
    check-cast p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 67
    .line 68
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;->a()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    check-cast p1, Lk50/c;

    .line 73
    .line 74
    goto :goto_1

    .line 75
    :cond_5
    instance-of v1, p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;

    .line 76
    .line 77
    if-nez v1, :cond_8

    .line 78
    .line 79
    sget-object v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$a;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$a;

    .line 80
    .line 81
    invoke-static {p1, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 82
    .line 83
    .line 84
    move-result v1

    .line 85
    if-eqz v1, :cond_6

    .line 86
    .line 87
    goto :goto_0

    .line 88
    :cond_6
    instance-of p1, p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$c;

    .line 89
    .line 90
    if-eqz p1, :cond_7

    .line 91
    .line 92
    sget-object p1, Lk50/c$c;->a:Lk50/c$c;

    .line 93
    .line 94
    goto :goto_1

    .line 95
    :cond_7
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 96
    .line 97
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 98
    .line 99
    .line 100
    throw p1

    .line 101
    :cond_8
    :goto_0
    sget-object p1, Lk50/c$b;->a:Lk50/c$b;

    .line 102
    .line 103
    :goto_1
    if-eqz v0, :cond_9

    .line 104
    .line 105
    check-cast p2, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 106
    .line 107
    invoke-virtual {p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;->a()Ljava/lang/Object;

    .line 108
    .line 109
    .line 110
    move-result-object p2

    .line 111
    check-cast p2, Lk50/h;

    .line 112
    .line 113
    goto :goto_2

    .line 114
    :cond_9
    sget-object p2, Lk50/h$b;->a:Lk50/h$b;

    .line 115
    .line 116
    :goto_2
    instance-of v0, p3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 117
    .line 118
    if-eqz v0, :cond_a

    .line 119
    .line 120
    check-cast p3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 121
    .line 122
    invoke-virtual {p3}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;->a()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object p3

    .line 126
    check-cast p3, Lk50/d;

    .line 127
    .line 128
    goto :goto_4

    .line 129
    :cond_a
    instance-of v0, p3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;

    .line 130
    .line 131
    if-nez v0, :cond_c

    .line 132
    .line 133
    sget-object v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$a;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$a;

    .line 134
    .line 135
    invoke-static {p3, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 136
    .line 137
    .line 138
    move-result v0

    .line 139
    if-nez v0, :cond_c

    .line 140
    .line 141
    instance-of p3, p3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$c;

    .line 142
    .line 143
    if-eqz p3, :cond_b

    .line 144
    .line 145
    goto :goto_3

    .line 146
    :cond_b
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 147
    .line 148
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 149
    .line 150
    .line 151
    throw p1

    .line 152
    :cond_c
    :goto_3
    sget-object p3, Lk50/d$b;->a:Lk50/d$b;

    .line 153
    .line 154
    :goto_4
    instance-of v0, p4, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 155
    .line 156
    if-eqz v0, :cond_d

    .line 157
    .line 158
    check-cast p4, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 159
    .line 160
    invoke-virtual {p4}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;->a()Ljava/lang/Object;

    .line 161
    .line 162
    .line 163
    move-result-object p4

    .line 164
    check-cast p4, Lk50/a;

    .line 165
    .line 166
    goto :goto_6

    .line 167
    :cond_d
    instance-of v0, p4, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$c;

    .line 168
    .line 169
    if-nez v0, :cond_f

    .line 170
    .line 171
    sget-object v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$a;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$a;

    .line 172
    .line 173
    invoke-static {p4, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 174
    .line 175
    .line 176
    move-result v0

    .line 177
    if-nez v0, :cond_f

    .line 178
    .line 179
    sget-object v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;

    .line 180
    .line 181
    invoke-static {p4, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 182
    .line 183
    .line 184
    move-result p4

    .line 185
    if-eqz p4, :cond_e

    .line 186
    .line 187
    goto :goto_5

    .line 188
    :cond_e
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 189
    .line 190
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 191
    .line 192
    .line 193
    throw p1

    .line 194
    :cond_f
    :goto_5
    sget-object p4, Lk50/a$b;->a:Lk50/a$b;

    .line 195
    .line 196
    :goto_6
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H3:Lorg/xbet/games_section/feature/popular_classic/presentation/a;

    .line 197
    .line 198
    invoke-virtual {v0, p2, p1, p3, p4}, Lorg/xbet/games_section/feature/popular_classic/presentation/a;->l(Lk50/h;Lk50/c;Lk50/d;Lk50/a;)Ljava/util/List;

    .line 199
    .line 200
    .line 201
    move-result-object p1

    .line 202
    return-object p1
.end method

.method public final B4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;)V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->y5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    sget-object v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;->REOPEN_FRAGMENT:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;

    .line 14
    .line 15
    if-ne p1, v0, :cond_1

    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->r4()Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-eqz v0, :cond_1

    .line 22
    .line 23
    return-void

    .line 24
    :cond_1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->F5:Lkotlinx/coroutines/flow/V;

    .line 25
    .line 26
    :cond_2
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    move-object v3, v2

    .line 31
    check-cast v3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;

    .line 32
    .line 33
    instance-of v4, v3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 34
    .line 35
    if-nez v4, :cond_3

    .line 36
    .line 37
    instance-of v4, v3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$c;

    .line 38
    .line 39
    if-nez v4, :cond_3

    .line 40
    .line 41
    sget-object v3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;

    .line 42
    .line 43
    :cond_3
    invoke-interface {v0, v2, v3}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 44
    .line 45
    .line 46
    move-result v2

    .line 47
    if-eqz v2, :cond_2

    .line 48
    .line 49
    new-instance v0, Lkotlin/jvm/internal/Ref$BooleanRef;

    .line 50
    .line 51
    invoke-direct {v0}, Lkotlin/jvm/internal/Ref$BooleanRef;-><init>()V

    .line 52
    .line 53
    .line 54
    sget-object v2, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;->INIT:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;

    .line 55
    .line 56
    if-ne p1, v2, :cond_4

    .line 57
    .line 58
    goto :goto_0

    .line 59
    :cond_4
    const/4 v1, 0x0

    .line 60
    :goto_0
    iput-boolean v1, v0, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    .line 61
    .line 62
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 63
    .line 64
    .line 65
    move-result-object v2

    .line 66
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->P1:Lm8/a;

    .line 67
    .line 68
    invoke-interface {p1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 69
    .line 70
    .line 71
    move-result-object v5

    .line 72
    new-instance v3, Lorg/xbet/games_section/feature/popular_classic/presentation/g;

    .line 73
    .line 74
    invoke-direct {v3, p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/g;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)V

    .line 75
    .line 76
    .line 77
    new-instance v7, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3;

    .line 78
    .line 79
    const/4 p1, 0x0

    .line 80
    invoke-direct {v7, p0, v0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/jvm/internal/Ref$BooleanRef;Lkotlin/coroutines/e;)V

    .line 81
    .line 82
    .line 83
    const/16 v8, 0xa

    .line 84
    .line 85
    const/4 v9, 0x0

    .line 86
    const/4 v4, 0x0

    .line 87
    const/4 v6, 0x0

    .line 88
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->y5:Lkotlinx/coroutines/x0;

    .line 93
    .line 94
    return-void
.end method

.method public final D4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;)V
    .locals 14

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->A5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    sget-object v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;->REOPEN_FRAGMENT:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;

    .line 14
    .line 15
    if-ne p1, v0, :cond_1

    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->r4()Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-eqz v0, :cond_1

    .line 22
    .line 23
    return-void

    .line 24
    :cond_1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H5:Lkotlinx/coroutines/flow/V;

    .line 25
    .line 26
    :cond_2
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    move-object v3, v2

    .line 31
    check-cast v3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;

    .line 32
    .line 33
    instance-of v4, v3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 34
    .line 35
    if-nez v4, :cond_3

    .line 36
    .line 37
    sget-object v3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;

    .line 38
    .line 39
    :cond_3
    invoke-interface {v0, v2, v3}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 40
    .line 41
    .line 42
    move-result v2

    .line 43
    if-eqz v2, :cond_2

    .line 44
    .line 45
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 46
    .line 47
    .line 48
    move-result-object v3

    .line 49
    sget-object v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;->INIT:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;

    .line 50
    .line 51
    if-ne p1, v0, :cond_4

    .line 52
    .line 53
    const/4 v4, 0x1

    .line 54
    goto :goto_0

    .line 55
    :cond_4
    const/4 v1, 0x0

    .line 56
    const/4 v4, 0x0

    .line 57
    :goto_0
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->P1:Lm8/a;

    .line 58
    .line 59
    invoke-interface {p1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 60
    .line 61
    .line 62
    move-result-object v6

    .line 63
    new-instance v10, Lorg/xbet/games_section/feature/popular_classic/presentation/i;

    .line 64
    .line 65
    invoke-direct {v10, p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/i;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)V

    .line 66
    .line 67
    .line 68
    new-instance v11, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getLuckyWheel$3;

    .line 69
    .line 70
    const/4 p1, 0x0

    .line 71
    invoke-direct {v11, p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getLuckyWheel$3;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 72
    .line 73
    .line 74
    const/16 v12, 0x18

    .line 75
    .line 76
    const/4 v13, 0x0

    .line 77
    const-string v5, "PopularClassicOneXGamesViewModel.getLuckyWheel"

    .line 78
    .line 79
    const/4 v7, 0x0

    .line 80
    const-wide/16 v8, 0x0

    .line 81
    .line 82
    invoke-static/range {v3 .. v13}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->B(Lkotlinx/coroutines/N;ZLjava/lang/String;Lkotlin/coroutines/CoroutineContext;IJLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->A5:Lkotlinx/coroutines/x0;

    .line 87
    .line 88
    return-void
.end method

.method public final F4()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->X4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final G4()Lkotlinx/coroutines/flow/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getViewState$1;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v1, p0, v2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getViewState$1;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getViewState$2;

    .line 14
    .line 15
    invoke-direct {v1, p0, v2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getViewState$2;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getViewState$3;

    .line 23
    .line 24
    invoke-direct {v1, p0, v2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getViewState$3;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->h0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    return-object v0
.end method

.method public final I4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e;)Z
    .locals 0

    .line 1
    instance-of p1, p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e$a;

    .line 2
    .line 3
    return p1
.end method

.method public final P4(JLk50/f;)V
    .locals 10

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openGame$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openGame$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->P1:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v4, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openGame$2;

    .line 17
    .line 18
    const/4 v9, 0x0

    .line 19
    move-object v5, p0

    .line 20
    move-wide v6, p1

    .line 21
    move-object v8, p3

    .line 22
    invoke-direct/range {v4 .. v9}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openGame$2;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;JLk50/f;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    const/16 v6, 0xa

    .line 26
    .line 27
    const/4 v7, 0x0

    .line 28
    const/4 v2, 0x0

    .line 29
    move-object v5, v4

    .line 30
    const/4 v4, 0x0

    .line 31
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public Q1(Ljava/lang/String;Lk50/f;Ljava/lang/String;Z)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lk50/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p2}, Lk50/f;->c()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-static {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 6
    .line 7
    .line 8
    move-result-wide p3

    .line 9
    invoke-virtual {p0, p3, p4, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->P4(JLk50/f;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final Q4(Lk50/f;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lk50/f;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openNativeGame$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openNativeGame$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openNativeGame$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openNativeGame$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openNativeGame$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openNativeGame$1;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openNativeGame$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openNativeGame$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-wide v1, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openNativeGame$1;->J$0:J

    .line 39
    .line 40
    iget-object p1, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openNativeGame$1;->L$0:Ljava/lang/Object;

    .line 41
    .line 42
    check-cast p1, Lb30/L;

    .line 43
    .line 44
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    move-object v4, p1

    .line 48
    move-wide v5, v1

    .line 49
    goto :goto_1

    .line 50
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 51
    .line 52
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 53
    .line 54
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    throw p1

    .line 58
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 59
    .line 60
    .line 61
    invoke-virtual {p1}, Lk50/f;->c()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    invoke-static {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 66
    .line 67
    .line 68
    move-result-wide p1

    .line 69
    sget-object v2, Lb30/L;->a:Lb30/L;

    .line 70
    .line 71
    iget-object v4, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->F2:Lw30/i;

    .line 72
    .line 73
    iput-object v2, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openNativeGame$1;->L$0:Ljava/lang/Object;

    .line 74
    .line 75
    iput-wide p1, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openNativeGame$1;->J$0:J

    .line 76
    .line 77
    iput v3, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openNativeGame$1;->label:I

    .line 78
    .line 79
    invoke-interface {v4, p1, p2, v0}, Lw30/i;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    if-ne v0, v1, :cond_3

    .line 84
    .line 85
    return-object v1

    .line 86
    :cond_3
    move-wide v5, p1

    .line 87
    move-object p2, v0

    .line 88
    move-object v4, v2

    .line 89
    :goto_1
    check-cast p2, Ljava/lang/Boolean;

    .line 90
    .line 91
    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    .line 92
    .line 93
    .line 94
    move-result p1

    .line 95
    xor-int/lit8 v8, p1, 0x1

    .line 96
    .line 97
    const/4 v9, 0x2

    .line 98
    const/4 v10, 0x0

    .line 99
    const/4 v7, 0x0

    .line 100
    invoke-static/range {v4 .. v10}, Lb30/L;->b(Lb30/L;JLorg/xbet/games_section/api/models/GameBonus;ZILjava/lang/Object;)LwX0/B;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    if-eqz p1, :cond_4

    .line 105
    .line 106
    iget-object p2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I3:LwX0/c;

    .line 107
    .line 108
    invoke-virtual {p2, p1}, LwX0/c;->m(Lq4/q;)V

    .line 109
    .line 110
    .line 111
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 112
    .line 113
    return-object p1
.end method

.method public X1(I)V
    .locals 2

    .line 1
    const/4 v0, 0x1

    .line 2
    if-eq p1, v0, :cond_1

    .line 3
    .line 4
    const/4 v0, 0x2

    .line 5
    if-eq p1, v0, :cond_0

    .line 6
    .line 7
    return-void

    .line 8
    :cond_0
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I3:LwX0/c;

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->V1:LwX0/a;

    .line 11
    .line 12
    invoke-interface {v0}, LwX0/a;->u()Lq4/q;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 17
    .line 18
    .line 19
    return-void

    .line 20
    :cond_1
    sget-object p1, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->LUCKY_WHEEL:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 21
    .line 22
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->getGameId()J

    .line 23
    .line 24
    .line 25
    move-result-wide v0

    .line 26
    invoke-direct {p0, v0, v1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->p4(J)V

    .line 27
    .line 28
    .line 29
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I3:LwX0/c;

    .line 30
    .line 31
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->V1:LwX0/a;

    .line 32
    .line 33
    invoke-interface {v0}, LwX0/a;->s()Lq4/q;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public k(Ljava/lang/String;Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p2}, Lkotlin/text/StringsKt;->toIntOrNull(Ljava/lang/String;)Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 p1, 0x0

    .line 13
    :goto_0
    iget-object p2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I3:LwX0/c;

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->V1:LwX0/a;

    .line 16
    .line 17
    invoke-interface {v0, p1}, LwX0/a;->C(I)Lq4/q;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-virtual {p2, p1}, LwX0/c;->m(Lq4/q;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final q4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c<",
            "+TT;>;)Z"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$c;

    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$c;->a()Ljava/lang/Throwable;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-static {p1}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    if-nez p1, :cond_0

    .line 16
    .line 17
    const/4 p1, 0x1

    .line 18
    return p1

    .line 19
    :cond_0
    const/4 p1, 0x0

    .line 20
    return p1
.end method

.method public final r4()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->F5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;

    .line 8
    .line 9
    invoke-virtual {p0, v0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->q4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;)Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-eqz v0, :cond_0

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->G5:Lkotlinx/coroutines/flow/V;

    .line 16
    .line 17
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;

    .line 22
    .line 23
    invoke-virtual {p0, v0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->q4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;)Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-eqz v0, :cond_0

    .line 28
    .line 29
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H5:Lkotlinx/coroutines/flow/V;

    .line 30
    .line 31
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    check-cast v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;

    .line 36
    .line 37
    invoke-virtual {p0, v0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->q4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;)Z

    .line 38
    .line 39
    .line 40
    move-result v0

    .line 41
    if-eqz v0, :cond_0

    .line 42
    .line 43
    const/4 v0, 0x1

    .line 44
    return v0

    .line 45
    :cond_0
    const/4 v0, 0x0

    .line 46
    return v0
.end method

.method public final t4()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->v5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final w4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;)V
    .locals 1

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->B4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->u4()V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H5:Lkotlinx/coroutines/flow/V;

    .line 8
    .line 9
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    instance-of v0, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 14
    .line 15
    if-nez v0, :cond_0

    .line 16
    .line 17
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->D4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;)V

    .line 18
    .line 19
    .line 20
    :cond_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->G5:Lkotlinx/coroutines/flow/V;

    .line 21
    .line 22
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    check-cast v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;

    .line 27
    .line 28
    instance-of v0, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 29
    .line 30
    if-nez v0, :cond_1

    .line 31
    .line 32
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->y4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;)V

    .line 33
    .line 34
    .line 35
    return-void

    .line 36
    :cond_1
    iget-boolean v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->C5:Z

    .line 37
    .line 38
    if-nez v0, :cond_2

    .line 39
    .line 40
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I2:Lw30/b;

    .line 41
    .line 42
    invoke-interface {v0}, Lw30/b;->invoke()V

    .line 43
    .line 44
    .line 45
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->y4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;)V

    .line 46
    .line 47
    .line 48
    :cond_2
    return-void
.end method

.method public final x4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->J5:Lkotlinx/coroutines/flow/f0;

    .line 2
    .line 3
    return-object v0
.end method

.method public final y4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;)V
    .locals 14

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->z5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    sget-object v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;->REOPEN_FRAGMENT:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;

    .line 14
    .line 15
    if-ne p1, v0, :cond_1

    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->r4()Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-eqz v0, :cond_1

    .line 22
    .line 23
    return-void

    .line 24
    :cond_1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->G5:Lkotlinx/coroutines/flow/V;

    .line 25
    .line 26
    :cond_2
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    move-object v3, v2

    .line 31
    check-cast v3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;

    .line 32
    .line 33
    instance-of v4, v3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 34
    .line 35
    if-nez v4, :cond_3

    .line 36
    .line 37
    sget-object v3, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$a;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$a;

    .line 38
    .line 39
    :cond_3
    invoke-interface {v0, v2, v3}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 40
    .line 41
    .line 42
    move-result v2

    .line 43
    if-eqz v2, :cond_2

    .line 44
    .line 45
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 46
    .line 47
    .line 48
    move-result-object v3

    .line 49
    sget-object v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;->INIT:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;

    .line 50
    .line 51
    if-ne p1, v0, :cond_4

    .line 52
    .line 53
    const/4 v4, 0x1

    .line 54
    goto :goto_0

    .line 55
    :cond_4
    const/4 v1, 0x0

    .line 56
    const/4 v4, 0x0

    .line 57
    :goto_0
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->P1:Lm8/a;

    .line 58
    .line 59
    invoke-interface {p1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 60
    .line 61
    .line 62
    move-result-object v6

    .line 63
    new-instance v10, Lorg/xbet/games_section/feature/popular_classic/presentation/k;

    .line 64
    .line 65
    invoke-direct {v10, p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/k;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)V

    .line 66
    .line 67
    .line 68
    new-instance v11, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;

    .line 69
    .line 70
    const/4 p1, 0x0

    .line 71
    invoke-direct {v11, p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 72
    .line 73
    .line 74
    const/16 v12, 0x18

    .line 75
    .line 76
    const/4 v13, 0x0

    .line 77
    const-string v5, "PopularClassicOneXGamesViewModel.getGames"

    .line 78
    .line 79
    const/4 v7, 0x0

    .line 80
    const-wide/16 v8, 0x0

    .line 81
    .line 82
    invoke-static/range {v3 .. v13}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->B(Lkotlinx/coroutines/N;ZLjava/lang/String;Lkotlin/coroutines/CoroutineContext;IJLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->z5:Lkotlinx/coroutines/x0;

    .line 87
    .line 88
    return-void
.end method
