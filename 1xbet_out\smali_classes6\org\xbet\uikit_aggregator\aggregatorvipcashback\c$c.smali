.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0006\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u000e\u0008\u0087\u0008\u0018\u00002\u00020\u0001B7\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\n\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0010\u0010\r\u001a\u00020\u0002H\u00d6\u0001\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0010\u0010\u0010\u001a\u00020\u000fH\u00d6\u0001\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u001a\u0010\u0015\u001a\u00020\u00142\u0008\u0010\u0013\u001a\u0004\u0018\u00010\u0012H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0015\u0010\u0016R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0017\u0010\u0018\u001a\u0004\u0008\u0019\u0010\u000eR\u0017\u0010\u0004\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u0018\u001a\u0004\u0008\u0017\u0010\u000eR\u0017\u0010\u0005\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001b\u0010\u0018\u001a\u0004\u0008\u001a\u0010\u000eR\u0017\u0010\u0007\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010\u001d\u001a\u0004\u0008\u001b\u0010\u001eR\u001a\u0010\t\u001a\u00020\u00088\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u001f\u0010 \u001a\u0004\u0008\u001f\u0010!R\u001a\u0010\n\u001a\u00020\u00088\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0019\u0010 \u001a\u0004\u0008\u001c\u0010!\u00a8\u0006\""
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c;",
        "",
        "title",
        "caption",
        "cashback",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;",
        "level",
        "",
        "progress",
        "maxProgress",
        "<init>",
        "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;JJ)V",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "Ljava/lang/String;",
        "f",
        "b",
        "c",
        "d",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;",
        "()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;",
        "e",
        "J",
        "()J",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:J

.field public final f:J


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;JJ)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->a:Ljava/lang/String;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->b:Ljava/lang/String;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->c:Ljava/lang/String;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;

    .line 11
    .line 12
    iput-wide p5, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->e:J

    .line 13
    .line 14
    iput-wide p7, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->f:J

    .line 15
    .line 16
    return-void
.end method


# virtual methods
.method public final a()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->b:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->c:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;

    .line 2
    .line 3
    return-object v0
.end method

.method public d()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->f:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public e()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->e:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 7

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;

    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->a:Ljava/lang/String;

    iget-object v3, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->a:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->b:Ljava/lang/String;

    iget-object v3, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->b:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->c:Ljava/lang/String;

    iget-object v3, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->c:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_4

    return v2

    :cond_4
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;

    iget-object v3, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;

    if-eq v1, v3, :cond_5

    return v2

    :cond_5
    iget-wide v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->e:J

    iget-wide v5, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->e:J

    cmp-long v1, v3, v5

    if-eqz v1, :cond_6

    return v2

    :cond_6
    iget-wide v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->f:J

    iget-wide v5, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->f:J

    cmp-long p1, v3, v5

    if-eqz p1, :cond_7

    return v2

    :cond_7
    return v0
.end method

.method public final f()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->a:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public hashCode()I
    .locals 3

    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->a:Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->b:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->c:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-wide v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->e:J

    invoke-static {v1, v2}, Lu/l;->a(J)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-wide v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->f:J

    invoke-static {v1, v2}, Lu/l;->a(J)I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 10
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->a:Ljava/lang/String;

    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->b:Ljava/lang/String;

    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->c:Ljava/lang/String;

    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;

    iget-wide v4, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->e:J

    iget-wide v6, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->f:J

    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, "Status(title="

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", caption="

    invoke-virtual {v8, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", cashback="

    invoke-virtual {v8, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", level="

    invoke-virtual {v8, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", progress="

    invoke-virtual {v8, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, v4, v5}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v0, ", maxProgress="

    invoke-virtual {v8, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, v6, v7}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v8, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
