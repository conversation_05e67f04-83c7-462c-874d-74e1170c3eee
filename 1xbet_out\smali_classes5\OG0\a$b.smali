.class public final LOG0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LOG0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LOG0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LOG0/a$b$g;,
        LOG0/a$b$e;,
        LOG0/a$b$b;,
        LOG0/a$b$a;,
        LOG0/a$b$f;,
        LOG0/a$b$h;,
        LOG0/a$b$c;,
        LOG0/a$b$d;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/l;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/l;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LaF0/a;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LGL0/b;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LbL0/a;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQN0/c;",
            ">;"
        }
    .end annotation
.end field

.field public I:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LdM0/a;",
            ">;"
        }
    .end annotation
.end field

.field public J:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LAP0/a;",
            ">;"
        }
    .end annotation
.end field

.field public K:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LaN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public L:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/main/plain_list/presentation/MainStatisticViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public M:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public N:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDH0/a;",
            ">;"
        }
    .end annotation
.end field

.field public O:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LkC0/a;",
            ">;"
        }
    .end annotation
.end field

.field public P:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQD0/d;",
            ">;"
        }
    .end annotation
.end field

.field public Q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/i;",
            ">;"
        }
    .end annotation
.end field

.field public R:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/main/common/presentation/viewmodel/StatisticViewModelDelegateImpl;",
            ">;"
        }
    .end annotation
.end field

.field public S:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public T:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/lineup/data/c;",
            ">;"
        }
    .end annotation
.end field

.field public U:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/lineup/data/StatisticLineUpRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public V:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFG0/b;",
            ">;"
        }
    .end annotation
.end field

.field public W:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDE0/b;",
            ">;"
        }
    .end annotation
.end field

.field public X:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/game_events/data/repository/GameEventsRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public Y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/game_events/domain/c;",
            ">;"
        }
    .end annotation
.end field

.field public Z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/game_events/domain/GetGameEventsScenarioImpl;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LOG0/a$b;

.field public a0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LpG0/c;",
            ">;"
        }
    .end annotation
.end field

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public b0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LpG0/a;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/d;",
            ">;"
        }
    .end annotation
.end field

.field public c0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/lastgames/data/repository/LastGameRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/j;",
            ">;"
        }
    .end annotation
.end field

.field public d0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxG0/i;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public e0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxG0/f;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LNG0/a;",
            ">;"
        }
    .end annotation
.end field

.field public f0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LqE0/b;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lz7/a;",
            ">;"
        }
    .end annotation
.end field

.field public g0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/forecast/data/repository/ForecastStatisticsRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LPG0/c;",
            ">;"
        }
    .end annotation
.end field

.field public h0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwE0/b;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/m;",
            ">;"
        }
    .end annotation
.end field

.field public i0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LPG0/a;",
            ">;"
        }
    .end annotation
.end field

.field public j0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/main/common/presentation/viewmodel/MainStatisticUdfViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LNo0/b;",
            ">;"
        }
    .end annotation
.end field

.field public k0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/game_events/presentation/viewmodel/GameEventsViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public l0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/forecast/presentation/viewmodel/ForecastStatisticViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public m0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/lineup/presentation/LineUpViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/f;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LPH0/b;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/scope/StatisticAnalytics;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LiS/a;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LMl0/a;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTn/a;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/GetSportUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LJo0/a;LEN0/f;LPH0/a;LGL0/a;LQN0/b;Ldk0/p;LbL0/a;LSX0/c;LkC0/a;LDH0/a;LwX0/c;Lorg/xbet/ui_common/utils/M;Lz7/a;Lf8/g;LTn/a;LMl0/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;Li8/m;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;LfX/b;LSX0/a;Li8/j;Lc8/h;LHX0/e;LaF0/a;LAP0/a;LdM0/a;LaN0/a;LiS/a;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LOG0/a$b;->a:LOG0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p35}, LOG0/a$b;->c(LQW0/c;LJo0/a;LEN0/f;LPH0/a;LGL0/a;LQN0/b;Ldk0/p;LbL0/a;LSX0/c;LkC0/a;LDH0/a;LwX0/c;Lorg/xbet/ui_common/utils/M;Lz7/a;Lf8/g;LTn/a;LMl0/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;Li8/m;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;LfX/b;LSX0/a;Li8/j;Lc8/h;LHX0/e;LaF0/a;LAP0/a;LdM0/a;LaN0/a;LiS/a;)V

    .line 5
    invoke-virtual/range {p0 .. p35}, LOG0/a$b;->d(LQW0/c;LJo0/a;LEN0/f;LPH0/a;LGL0/a;LQN0/b;Ldk0/p;LbL0/a;LSX0/c;LkC0/a;LDH0/a;LwX0/c;Lorg/xbet/ui_common/utils/M;Lz7/a;Lf8/g;LTn/a;LMl0/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;Li8/m;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;LfX/b;LSX0/a;Li8/j;Lc8/h;LHX0/e;LaF0/a;LAP0/a;LdM0/a;LaN0/a;LiS/a;)V

    .line 6
    invoke-virtual/range {p0 .. p35}, LOG0/a$b;->e(LQW0/c;LJo0/a;LEN0/f;LPH0/a;LGL0/a;LQN0/b;Ldk0/p;LbL0/a;LSX0/c;LkC0/a;LDH0/a;LwX0/c;Lorg/xbet/ui_common/utils/M;Lz7/a;Lf8/g;LTn/a;LMl0/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;Li8/m;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;LfX/b;LSX0/a;Li8/j;Lc8/h;LHX0/e;LaF0/a;LAP0/a;LdM0/a;LaN0/a;LiS/a;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LJo0/a;LEN0/f;LPH0/a;LGL0/a;LQN0/b;Ldk0/p;LbL0/a;LSX0/c;LkC0/a;LDH0/a;LwX0/c;Lorg/xbet/ui_common/utils/M;Lz7/a;Lf8/g;LTn/a;LMl0/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;Li8/m;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;LfX/b;LSX0/a;Li8/j;Lc8/h;LHX0/e;LaF0/a;LAP0/a;LdM0/a;LaN0/a;LiS/a;LOG0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p35}, LOG0/a$b;-><init>(LQW0/c;LJo0/a;LEN0/f;LPH0/a;LGL0/a;LQN0/b;Ldk0/p;LbL0/a;LSX0/c;LkC0/a;LDH0/a;LwX0/c;Lorg/xbet/ui_common/utils/M;Lz7/a;Lf8/g;LTn/a;LMl0/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;Li8/m;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;LfX/b;LSX0/a;Li8/j;Lc8/h;LHX0/e;LaF0/a;LAP0/a;LdM0/a;LaN0/a;LiS/a;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/main/plain_list/presentation/MainStatisticFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LOG0/a$b;->g(Lorg/xbet/statistic/main/plain_list/presentation/MainStatisticFragment;)Lorg/xbet/statistic/main/plain_list/presentation/MainStatisticFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/statistic/main/common/presentation/MainStatisticComponentFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LOG0/a$b;->f(Lorg/xbet/statistic/main/common/presentation/MainStatisticComponentFragment;)Lorg/xbet/statistic/main/common/presentation/MainStatisticComponentFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final c(LQW0/c;LJo0/a;LEN0/f;LPH0/a;LGL0/a;LQN0/b;Ldk0/p;LbL0/a;LSX0/c;LkC0/a;LDH0/a;LwX0/c;Lorg/xbet/ui_common/utils/M;Lz7/a;Lf8/g;LTn/a;LMl0/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;Li8/m;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;LfX/b;LSX0/a;Li8/j;Lc8/h;LHX0/e;LaF0/a;LAP0/a;LdM0/a;LaN0/a;LiS/a;)V
    .locals 0

    .line 1
    new-instance p5, LOG0/a$b$g;

    .line 2
    .line 3
    invoke-direct {p5, p3}, LOG0/a$b$g;-><init>(LEN0/f;)V

    .line 4
    .line 5
    .line 6
    iput-object p5, p0, LOG0/a$b;->b:Ldagger/internal/h;

    .line 7
    .line 8
    invoke-static {p5}, Lorg/xbet/statistic/statistic_core/domain/usecases/e;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/e;

    .line 9
    .line 10
    .line 11
    move-result-object p3

    .line 12
    iput-object p3, p0, LOG0/a$b;->c:Ldagger/internal/h;

    .line 13
    .line 14
    invoke-static/range {p28 .. p28}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 15
    .line 16
    .line 17
    move-result-object p3

    .line 18
    iput-object p3, p0, LOG0/a$b;->d:Ldagger/internal/h;

    .line 19
    .line 20
    invoke-static/range {p29 .. p29}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 21
    .line 22
    .line 23
    move-result-object p3

    .line 24
    iput-object p3, p0, LOG0/a$b;->e:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static {p3}, LNG0/b;->a(LBc/a;)LNG0/b;

    .line 27
    .line 28
    .line 29
    move-result-object p3

    .line 30
    iput-object p3, p0, LOG0/a$b;->f:Ldagger/internal/h;

    .line 31
    .line 32
    invoke-static {p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 33
    .line 34
    .line 35
    move-result-object p3

    .line 36
    iput-object p3, p0, LOG0/a$b;->g:Ldagger/internal/h;

    .line 37
    .line 38
    iget-object p5, p0, LOG0/a$b;->f:Ldagger/internal/h;

    .line 39
    .line 40
    invoke-static {p5, p3}, LPG0/d;->a(LBc/a;LBc/a;)LPG0/d;

    .line 41
    .line 42
    .line 43
    move-result-object p3

    .line 44
    iput-object p3, p0, LOG0/a$b;->h:Ldagger/internal/h;

    .line 45
    .line 46
    invoke-static/range {p22 .. p22}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 47
    .line 48
    .line 49
    move-result-object p3

    .line 50
    iput-object p3, p0, LOG0/a$b;->i:Ldagger/internal/h;

    .line 51
    .line 52
    iget-object p5, p0, LOG0/a$b;->d:Ldagger/internal/h;

    .line 53
    .line 54
    iget-object p6, p0, LOG0/a$b;->h:Ldagger/internal/h;

    .line 55
    .line 56
    invoke-static {p5, p6, p3}, LPG0/b;->a(LBc/a;LBc/a;LBc/a;)LPG0/b;

    .line 57
    .line 58
    .line 59
    move-result-object p3

    .line 60
    iput-object p3, p0, LOG0/a$b;->j:Ldagger/internal/h;

    .line 61
    .line 62
    new-instance p3, LOG0/a$b$e;

    .line 63
    .line 64
    invoke-direct {p3, p2}, LOG0/a$b$e;-><init>(LJo0/a;)V

    .line 65
    .line 66
    .line 67
    iput-object p3, p0, LOG0/a$b;->k:Ldagger/internal/h;

    .line 68
    .line 69
    invoke-static {p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 70
    .line 71
    .line 72
    move-result-object p2

    .line 73
    iput-object p2, p0, LOG0/a$b;->l:Ldagger/internal/h;

    .line 74
    .line 75
    invoke-static/range {p18 .. p18}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 76
    .line 77
    .line 78
    move-result-object p2

    .line 79
    iput-object p2, p0, LOG0/a$b;->m:Ldagger/internal/h;

    .line 80
    .line 81
    invoke-static/range {p24 .. p24}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 82
    .line 83
    .line 84
    move-result-object p2

    .line 85
    iput-object p2, p0, LOG0/a$b;->n:Ldagger/internal/h;

    .line 86
    .line 87
    invoke-static {p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 88
    .line 89
    .line 90
    move-result-object p2

    .line 91
    iput-object p2, p0, LOG0/a$b;->o:Ldagger/internal/h;

    .line 92
    .line 93
    iget-object p2, p0, LOG0/a$b;->b:Ldagger/internal/h;

    .line 94
    .line 95
    invoke-static {p2}, Lorg/xbet/statistic/statistic_core/domain/usecases/g;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/g;

    .line 96
    .line 97
    .line 98
    move-result-object p2

    .line 99
    iput-object p2, p0, LOG0/a$b;->p:Ldagger/internal/h;

    .line 100
    .line 101
    new-instance p2, LOG0/a$b$b;

    .line 102
    .line 103
    invoke-direct {p2, p4}, LOG0/a$b$b;-><init>(LPH0/a;)V

    .line 104
    .line 105
    .line 106
    iput-object p2, p0, LOG0/a$b;->q:Ldagger/internal/h;

    .line 107
    .line 108
    invoke-static/range {p25 .. p25}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 109
    .line 110
    .line 111
    move-result-object p2

    .line 112
    iput-object p2, p0, LOG0/a$b;->r:Ldagger/internal/h;

    .line 113
    .line 114
    invoke-static/range {p35 .. p35}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 115
    .line 116
    .line 117
    move-result-object p2

    .line 118
    iput-object p2, p0, LOG0/a$b;->s:Ldagger/internal/h;

    .line 119
    .line 120
    invoke-static/range {p27 .. p27}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 121
    .line 122
    .line 123
    move-result-object p2

    .line 124
    iput-object p2, p0, LOG0/a$b;->t:Ldagger/internal/h;

    .line 125
    .line 126
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 127
    .line 128
    .line 129
    move-result-object p2

    .line 130
    iput-object p2, p0, LOG0/a$b;->u:Ldagger/internal/h;

    .line 131
    .line 132
    invoke-static/range {p30 .. p30}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 133
    .line 134
    .line 135
    move-result-object p2

    .line 136
    iput-object p2, p0, LOG0/a$b;->v:Ldagger/internal/h;

    .line 137
    .line 138
    new-instance p2, LOG0/a$b$a;

    .line 139
    .line 140
    invoke-direct {p2, p1}, LOG0/a$b$a;-><init>(LQW0/c;)V

    .line 141
    .line 142
    .line 143
    iput-object p2, p0, LOG0/a$b;->w:Ldagger/internal/h;

    .line 144
    .line 145
    invoke-static/range {p16 .. p16}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 146
    .line 147
    .line 148
    move-result-object p1

    .line 149
    iput-object p1, p0, LOG0/a$b;->x:Ldagger/internal/h;

    .line 150
    .line 151
    iget-object p2, p0, LOG0/a$b;->w:Ldagger/internal/h;

    .line 152
    .line 153
    invoke-static {p2, p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/h;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/h;

    .line 154
    .line 155
    .line 156
    move-result-object p1

    .line 157
    iput-object p1, p0, LOG0/a$b;->y:Ldagger/internal/h;

    .line 158
    .line 159
    iget-object p1, p0, LOG0/a$b;->b:Ldagger/internal/h;

    .line 160
    .line 161
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/j;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/j;

    .line 162
    .line 163
    .line 164
    move-result-object p1

    .line 165
    iput-object p1, p0, LOG0/a$b;->z:Ldagger/internal/h;

    .line 166
    .line 167
    return-void
.end method

.method public final d(LQW0/c;LJo0/a;LEN0/f;LPH0/a;LGL0/a;LQN0/b;Ldk0/p;LbL0/a;LSX0/c;LkC0/a;LDH0/a;LwX0/c;Lorg/xbet/ui_common/utils/M;Lz7/a;Lf8/g;LTn/a;LMl0/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;Li8/m;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;LfX/b;LSX0/a;Li8/j;Lc8/h;LHX0/e;LaF0/a;LAP0/a;LdM0/a;LaN0/a;LiS/a;)V
    .locals 27

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, LOG0/a$b;->b:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v1}, Lorg/xbet/statistic/statistic_core/domain/usecases/m;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/m;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iput-object v1, v0, LOG0/a$b;->A:Ldagger/internal/h;

    .line 10
    .line 11
    iget-object v2, v0, LOG0/a$b;->p:Ldagger/internal/h;

    .line 12
    .line 13
    iget-object v3, v0, LOG0/a$b;->y:Ldagger/internal/h;

    .line 14
    .line 15
    iget-object v4, v0, LOG0/a$b;->z:Ldagger/internal/h;

    .line 16
    .line 17
    iget-object v5, v0, LOG0/a$b;->o:Ldagger/internal/h;

    .line 18
    .line 19
    iget-object v6, v0, LOG0/a$b;->v:Ldagger/internal/h;

    .line 20
    .line 21
    iget-object v7, v0, LOG0/a$b;->m:Ldagger/internal/h;

    .line 22
    .line 23
    move-object/from16 p28, v1

    .line 24
    .line 25
    move-object/from16 p24, v2

    .line 26
    .line 27
    move-object/from16 p25, v3

    .line 28
    .line 29
    move-object/from16 p26, v4

    .line 30
    .line 31
    move-object/from16 p27, v5

    .line 32
    .line 33
    move-object/from16 p29, v6

    .line 34
    .line 35
    move-object/from16 p30, v7

    .line 36
    .line 37
    invoke-static/range {p24 .. p30}, Lorg/xbet/statistic/statistic_core/presentation/delegates/o;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/o;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    iput-object v1, v0, LOG0/a$b;->B:Ldagger/internal/h;

    .line 42
    .line 43
    invoke-static/range {p23 .. p23}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    iput-object v1, v0, LOG0/a$b;->C:Ldagger/internal/h;

    .line 48
    .line 49
    invoke-static/range {p21 .. p21}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 50
    .line 51
    .line 52
    move-result-object v1

    .line 53
    iput-object v1, v0, LOG0/a$b;->D:Ldagger/internal/h;

    .line 54
    .line 55
    invoke-static/range {p31 .. p31}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    iput-object v1, v0, LOG0/a$b;->E:Ldagger/internal/h;

    .line 60
    .line 61
    new-instance v1, LOG0/a$b$f;

    .line 62
    .line 63
    move-object/from16 v2, p5

    .line 64
    .line 65
    invoke-direct {v1, v2}, LOG0/a$b$f;-><init>(LGL0/a;)V

    .line 66
    .line 67
    .line 68
    iput-object v1, v0, LOG0/a$b;->F:Ldagger/internal/h;

    .line 69
    .line 70
    invoke-static/range {p8 .. p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    iput-object v1, v0, LOG0/a$b;->G:Ldagger/internal/h;

    .line 75
    .line 76
    new-instance v1, LOG0/a$b$h;

    .line 77
    .line 78
    move-object/from16 v2, p6

    .line 79
    .line 80
    invoke-direct {v1, v2}, LOG0/a$b$h;-><init>(LQN0/b;)V

    .line 81
    .line 82
    .line 83
    iput-object v1, v0, LOG0/a$b;->H:Ldagger/internal/h;

    .line 84
    .line 85
    invoke-static/range {p33 .. p33}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 86
    .line 87
    .line 88
    move-result-object v1

    .line 89
    iput-object v1, v0, LOG0/a$b;->I:Ldagger/internal/h;

    .line 90
    .line 91
    invoke-static/range {p32 .. p32}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 92
    .line 93
    .line 94
    move-result-object v1

    .line 95
    iput-object v1, v0, LOG0/a$b;->J:Ldagger/internal/h;

    .line 96
    .line 97
    invoke-static/range {p34 .. p34}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 98
    .line 99
    .line 100
    move-result-object v1

    .line 101
    iput-object v1, v0, LOG0/a$b;->K:Ldagger/internal/h;

    .line 102
    .line 103
    iget-object v2, v0, LOG0/a$b;->c:Ldagger/internal/h;

    .line 104
    .line 105
    iget-object v3, v0, LOG0/a$b;->j:Ldagger/internal/h;

    .line 106
    .line 107
    iget-object v4, v0, LOG0/a$b;->k:Ldagger/internal/h;

    .line 108
    .line 109
    iget-object v5, v0, LOG0/a$b;->l:Ldagger/internal/h;

    .line 110
    .line 111
    iget-object v6, v0, LOG0/a$b;->m:Ldagger/internal/h;

    .line 112
    .line 113
    iget-object v7, v0, LOG0/a$b;->n:Ldagger/internal/h;

    .line 114
    .line 115
    iget-object v8, v0, LOG0/a$b;->o:Ldagger/internal/h;

    .line 116
    .line 117
    iget-object v9, v0, LOG0/a$b;->p:Ldagger/internal/h;

    .line 118
    .line 119
    iget-object v10, v0, LOG0/a$b;->q:Ldagger/internal/h;

    .line 120
    .line 121
    iget-object v11, v0, LOG0/a$b;->r:Ldagger/internal/h;

    .line 122
    .line 123
    iget-object v12, v0, LOG0/a$b;->s:Ldagger/internal/h;

    .line 124
    .line 125
    iget-object v13, v0, LOG0/a$b;->t:Ldagger/internal/h;

    .line 126
    .line 127
    iget-object v14, v0, LOG0/a$b;->u:Ldagger/internal/h;

    .line 128
    .line 129
    iget-object v15, v0, LOG0/a$b;->v:Ldagger/internal/h;

    .line 130
    .line 131
    move-object/from16 v26, v1

    .line 132
    .line 133
    iget-object v1, v0, LOG0/a$b;->B:Ldagger/internal/h;

    .line 134
    .line 135
    move-object/from16 v16, v1

    .line 136
    .line 137
    iget-object v1, v0, LOG0/a$b;->C:Ldagger/internal/h;

    .line 138
    .line 139
    move-object/from16 v17, v1

    .line 140
    .line 141
    iget-object v1, v0, LOG0/a$b;->D:Ldagger/internal/h;

    .line 142
    .line 143
    move-object/from16 v18, v1

    .line 144
    .line 145
    iget-object v1, v0, LOG0/a$b;->w:Ldagger/internal/h;

    .line 146
    .line 147
    move-object/from16 v19, v1

    .line 148
    .line 149
    iget-object v1, v0, LOG0/a$b;->E:Ldagger/internal/h;

    .line 150
    .line 151
    move-object/from16 v20, v1

    .line 152
    .line 153
    iget-object v1, v0, LOG0/a$b;->F:Ldagger/internal/h;

    .line 154
    .line 155
    move-object/from16 v21, v1

    .line 156
    .line 157
    iget-object v1, v0, LOG0/a$b;->G:Ldagger/internal/h;

    .line 158
    .line 159
    move-object/from16 v22, v1

    .line 160
    .line 161
    iget-object v1, v0, LOG0/a$b;->H:Ldagger/internal/h;

    .line 162
    .line 163
    move-object/from16 v23, v1

    .line 164
    .line 165
    iget-object v1, v0, LOG0/a$b;->I:Ldagger/internal/h;

    .line 166
    .line 167
    move-object/from16 v24, v1

    .line 168
    .line 169
    iget-object v1, v0, LOG0/a$b;->J:Ldagger/internal/h;

    .line 170
    .line 171
    move-object/from16 v25, v1

    .line 172
    .line 173
    invoke-static/range {v2 .. v26}, Lorg/xbet/statistic/main/plain_list/presentation/k;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/main/plain_list/presentation/k;

    .line 174
    .line 175
    .line 176
    move-result-object v1

    .line 177
    iput-object v1, v0, LOG0/a$b;->L:Ldagger/internal/h;

    .line 178
    .line 179
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 180
    .line 181
    .line 182
    move-result-object v1

    .line 183
    iput-object v1, v0, LOG0/a$b;->M:Ldagger/internal/h;

    .line 184
    .line 185
    invoke-static/range {p11 .. p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 186
    .line 187
    .line 188
    move-result-object v1

    .line 189
    iput-object v1, v0, LOG0/a$b;->N:Ldagger/internal/h;

    .line 190
    .line 191
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 192
    .line 193
    .line 194
    move-result-object v1

    .line 195
    iput-object v1, v0, LOG0/a$b;->O:Ldagger/internal/h;

    .line 196
    .line 197
    new-instance v1, LOG0/a$b$c;

    .line 198
    .line 199
    move-object/from16 v2, p3

    .line 200
    .line 201
    invoke-direct {v1, v2}, LOG0/a$b$c;-><init>(LEN0/f;)V

    .line 202
    .line 203
    .line 204
    iput-object v1, v0, LOG0/a$b;->P:Ldagger/internal/h;

    .line 205
    .line 206
    iget-object v2, v0, LOG0/a$b;->N:Ldagger/internal/h;

    .line 207
    .line 208
    iget-object v3, v0, LOG0/a$b;->l:Ldagger/internal/h;

    .line 209
    .line 210
    iget-object v4, v0, LOG0/a$b;->n:Ldagger/internal/h;

    .line 211
    .line 212
    iget-object v5, v0, LOG0/a$b;->O:Ldagger/internal/h;

    .line 213
    .line 214
    invoke-static {v2, v3, v4, v5, v1}, Lorg/xbet/statistic/statistic_core/presentation/delegates/j;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/j;

    .line 215
    .line 216
    .line 217
    move-result-object v1

    .line 218
    iput-object v1, v0, LOG0/a$b;->Q:Ldagger/internal/h;

    .line 219
    .line 220
    iget-object v1, v0, LOG0/a$b;->B:Ldagger/internal/h;

    .line 221
    .line 222
    invoke-static {v1}, Lorg/xbet/statistic/main/common/presentation/viewmodel/i0;->a(LBc/a;)Lorg/xbet/statistic/main/common/presentation/viewmodel/i0;

    .line 223
    .line 224
    .line 225
    move-result-object v1

    .line 226
    iput-object v1, v0, LOG0/a$b;->R:Ldagger/internal/h;

    .line 227
    .line 228
    invoke-static/range {p15 .. p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 229
    .line 230
    .line 231
    move-result-object v1

    .line 232
    iput-object v1, v0, LOG0/a$b;->S:Ldagger/internal/h;

    .line 233
    .line 234
    invoke-static {v1}, Lorg/xbet/statistic/lineup/data/d;->a(LBc/a;)Lorg/xbet/statistic/lineup/data/d;

    .line 235
    .line 236
    .line 237
    move-result-object v1

    .line 238
    iput-object v1, v0, LOG0/a$b;->T:Ldagger/internal/h;

    .line 239
    .line 240
    iget-object v2, v0, LOG0/a$b;->w:Ldagger/internal/h;

    .line 241
    .line 242
    iget-object v3, v0, LOG0/a$b;->e:Ldagger/internal/h;

    .line 243
    .line 244
    invoke-static {v2, v1, v3}, Lorg/xbet/statistic/lineup/data/e;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/lineup/data/e;

    .line 245
    .line 246
    .line 247
    move-result-object v1

    .line 248
    iput-object v1, v0, LOG0/a$b;->U:Ldagger/internal/h;

    .line 249
    .line 250
    invoke-static {v1}, LFG0/c;->a(LBc/a;)LFG0/c;

    .line 251
    .line 252
    .line 253
    move-result-object v1

    .line 254
    iput-object v1, v0, LOG0/a$b;->V:Ldagger/internal/h;

    .line 255
    .line 256
    iget-object v1, v0, LOG0/a$b;->S:Ldagger/internal/h;

    .line 257
    .line 258
    invoke-static {v1}, LDE0/c;->a(LBc/a;)LDE0/c;

    .line 259
    .line 260
    .line 261
    move-result-object v1

    .line 262
    iput-object v1, v0, LOG0/a$b;->W:Ldagger/internal/h;

    .line 263
    .line 264
    iget-object v2, v0, LOG0/a$b;->w:Ldagger/internal/h;

    .line 265
    .line 266
    iget-object v3, v0, LOG0/a$b;->e:Ldagger/internal/h;

    .line 267
    .line 268
    invoke-static {v1, v2, v3}, Lorg/xbet/statistic/game_events/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/game_events/data/repository/a;

    .line 269
    .line 270
    .line 271
    move-result-object v1

    .line 272
    iput-object v1, v0, LOG0/a$b;->X:Ldagger/internal/h;

    .line 273
    .line 274
    invoke-static {v1}, Lorg/xbet/statistic/game_events/domain/d;->a(LBc/a;)Lorg/xbet/statistic/game_events/domain/d;

    .line 275
    .line 276
    .line 277
    move-result-object v1

    .line 278
    iput-object v1, v0, LOG0/a$b;->Y:Ldagger/internal/h;

    .line 279
    .line 280
    return-void
.end method

.method public final e(LQW0/c;LJo0/a;LEN0/f;LPH0/a;LGL0/a;LQN0/b;Ldk0/p;LbL0/a;LSX0/c;LkC0/a;LDH0/a;LwX0/c;Lorg/xbet/ui_common/utils/M;Lz7/a;Lf8/g;LTn/a;LMl0/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;Li8/m;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;LfX/b;LSX0/a;Li8/j;Lc8/h;LHX0/e;LaF0/a;LAP0/a;LdM0/a;LaN0/a;LiS/a;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, LOG0/a$b;->d:Ldagger/internal/h;

    .line 4
    .line 5
    iget-object v2, v0, LOG0/a$b;->Y:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {v1, v2}, Lorg/xbet/statistic/game_events/domain/b;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/game_events/domain/b;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    iput-object v1, v0, LOG0/a$b;->Z:Ldagger/internal/h;

    .line 12
    .line 13
    iget-object v1, v0, LOG0/a$b;->S:Ldagger/internal/h;

    .line 14
    .line 15
    invoke-static {v1}, LpG0/d;->a(LBc/a;)LpG0/d;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    iput-object v1, v0, LOG0/a$b;->a0:Ldagger/internal/h;

    .line 20
    .line 21
    invoke-static {}, LtG0/g;->a()LtG0/g;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-static {v1}, Ldagger/internal/c;->d(Ldagger/internal/h;)Ldagger/internal/h;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    iput-object v1, v0, LOG0/a$b;->b0:Ldagger/internal/h;

    .line 30
    .line 31
    iget-object v2, v0, LOG0/a$b;->w:Ldagger/internal/h;

    .line 32
    .line 33
    iget-object v3, v0, LOG0/a$b;->a0:Ldagger/internal/h;

    .line 34
    .line 35
    iget-object v4, v0, LOG0/a$b;->e:Ldagger/internal/h;

    .line 36
    .line 37
    invoke-static {v2, v3, v1, v4}, Lorg/xbet/statistic/lastgames/data/repository/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/lastgames/data/repository/a;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    iput-object v1, v0, LOG0/a$b;->c0:Ldagger/internal/h;

    .line 42
    .line 43
    invoke-static {v1}, LxG0/j;->a(LBc/a;)LxG0/j;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    iput-object v1, v0, LOG0/a$b;->d0:Ldagger/internal/h;

    .line 48
    .line 49
    iget-object v1, v0, LOG0/a$b;->c0:Ldagger/internal/h;

    .line 50
    .line 51
    invoke-static {v1}, LxG0/g;->a(LBc/a;)LxG0/g;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    iput-object v1, v0, LOG0/a$b;->e0:Ldagger/internal/h;

    .line 56
    .line 57
    iget-object v1, v0, LOG0/a$b;->S:Ldagger/internal/h;

    .line 58
    .line 59
    invoke-static {v1}, LqE0/c;->a(LBc/a;)LqE0/c;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    iput-object v1, v0, LOG0/a$b;->f0:Ldagger/internal/h;

    .line 64
    .line 65
    iget-object v2, v0, LOG0/a$b;->e:Ldagger/internal/h;

    .line 66
    .line 67
    iget-object v3, v0, LOG0/a$b;->w:Ldagger/internal/h;

    .line 68
    .line 69
    invoke-static {v1, v2, v3}, Lorg/xbet/statistic/forecast/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/forecast/data/repository/a;

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    iput-object v1, v0, LOG0/a$b;->g0:Ldagger/internal/h;

    .line 74
    .line 75
    invoke-static {v1}, LwE0/c;->a(LBc/a;)LwE0/c;

    .line 76
    .line 77
    .line 78
    move-result-object v1

    .line 79
    iput-object v1, v0, LOG0/a$b;->h0:Ldagger/internal/h;

    .line 80
    .line 81
    new-instance v1, LOG0/a$b$d;

    .line 82
    .line 83
    move-object/from16 v2, p7

    .line 84
    .line 85
    invoke-direct {v1, v2}, LOG0/a$b$d;-><init>(Ldk0/p;)V

    .line 86
    .line 87
    .line 88
    iput-object v1, v0, LOG0/a$b;->i0:Ldagger/internal/h;

    .line 89
    .line 90
    iget-object v2, v0, LOG0/a$b;->m:Ldagger/internal/h;

    .line 91
    .line 92
    iget-object v3, v0, LOG0/a$b;->n:Ldagger/internal/h;

    .line 93
    .line 94
    iget-object v4, v0, LOG0/a$b;->c:Ldagger/internal/h;

    .line 95
    .line 96
    iget-object v5, v0, LOG0/a$b;->j:Ldagger/internal/h;

    .line 97
    .line 98
    iget-object v6, v0, LOG0/a$b;->k:Ldagger/internal/h;

    .line 99
    .line 100
    iget-object v7, v0, LOG0/a$b;->l:Ldagger/internal/h;

    .line 101
    .line 102
    iget-object v8, v0, LOG0/a$b;->o:Ldagger/internal/h;

    .line 103
    .line 104
    iget-object v9, v0, LOG0/a$b;->p:Ldagger/internal/h;

    .line 105
    .line 106
    iget-object v10, v0, LOG0/a$b;->q:Ldagger/internal/h;

    .line 107
    .line 108
    iget-object v11, v0, LOG0/a$b;->r:Ldagger/internal/h;

    .line 109
    .line 110
    iget-object v12, v0, LOG0/a$b;->s:Ldagger/internal/h;

    .line 111
    .line 112
    iget-object v13, v0, LOG0/a$b;->M:Ldagger/internal/h;

    .line 113
    .line 114
    iget-object v14, v0, LOG0/a$b;->u:Ldagger/internal/h;

    .line 115
    .line 116
    iget-object v15, v0, LOG0/a$b;->v:Ldagger/internal/h;

    .line 117
    .line 118
    move-object/from16 p33, v1

    .line 119
    .line 120
    iget-object v1, v0, LOG0/a$b;->C:Ldagger/internal/h;

    .line 121
    .line 122
    move-object/from16 p15, v1

    .line 123
    .line 124
    iget-object v1, v0, LOG0/a$b;->i:Ldagger/internal/h;

    .line 125
    .line 126
    move-object/from16 p16, v1

    .line 127
    .line 128
    iget-object v1, v0, LOG0/a$b;->D:Ldagger/internal/h;

    .line 129
    .line 130
    move-object/from16 p17, v1

    .line 131
    .line 132
    iget-object v1, v0, LOG0/a$b;->w:Ldagger/internal/h;

    .line 133
    .line 134
    move-object/from16 p18, v1

    .line 135
    .line 136
    iget-object v1, v0, LOG0/a$b;->E:Ldagger/internal/h;

    .line 137
    .line 138
    move-object/from16 p19, v1

    .line 139
    .line 140
    iget-object v1, v0, LOG0/a$b;->F:Ldagger/internal/h;

    .line 141
    .line 142
    move-object/from16 p20, v1

    .line 143
    .line 144
    iget-object v1, v0, LOG0/a$b;->Q:Ldagger/internal/h;

    .line 145
    .line 146
    move-object/from16 p21, v1

    .line 147
    .line 148
    iget-object v1, v0, LOG0/a$b;->R:Ldagger/internal/h;

    .line 149
    .line 150
    move-object/from16 p22, v1

    .line 151
    .line 152
    iget-object v1, v0, LOG0/a$b;->G:Ldagger/internal/h;

    .line 153
    .line 154
    move-object/from16 p23, v1

    .line 155
    .line 156
    iget-object v1, v0, LOG0/a$b;->H:Ldagger/internal/h;

    .line 157
    .line 158
    move-object/from16 p24, v1

    .line 159
    .line 160
    iget-object v1, v0, LOG0/a$b;->I:Ldagger/internal/h;

    .line 161
    .line 162
    move-object/from16 p25, v1

    .line 163
    .line 164
    iget-object v1, v0, LOG0/a$b;->V:Ldagger/internal/h;

    .line 165
    .line 166
    move-object/from16 p26, v1

    .line 167
    .line 168
    iget-object v1, v0, LOG0/a$b;->J:Ldagger/internal/h;

    .line 169
    .line 170
    move-object/from16 p27, v1

    .line 171
    .line 172
    iget-object v1, v0, LOG0/a$b;->K:Ldagger/internal/h;

    .line 173
    .line 174
    move-object/from16 p28, v1

    .line 175
    .line 176
    iget-object v1, v0, LOG0/a$b;->Z:Ldagger/internal/h;

    .line 177
    .line 178
    move-object/from16 p29, v1

    .line 179
    .line 180
    iget-object v1, v0, LOG0/a$b;->d0:Ldagger/internal/h;

    .line 181
    .line 182
    move-object/from16 p30, v1

    .line 183
    .line 184
    iget-object v1, v0, LOG0/a$b;->e0:Ldagger/internal/h;

    .line 185
    .line 186
    move-object/from16 p31, v1

    .line 187
    .line 188
    iget-object v1, v0, LOG0/a$b;->h0:Ldagger/internal/h;

    .line 189
    .line 190
    move-object/from16 p32, v1

    .line 191
    .line 192
    move-object/from16 p1, v2

    .line 193
    .line 194
    move-object/from16 p2, v3

    .line 195
    .line 196
    move-object/from16 p3, v4

    .line 197
    .line 198
    move-object/from16 p4, v5

    .line 199
    .line 200
    move-object/from16 p5, v6

    .line 201
    .line 202
    move-object/from16 p6, v7

    .line 203
    .line 204
    move-object/from16 p7, v8

    .line 205
    .line 206
    move-object/from16 p8, v9

    .line 207
    .line 208
    move-object/from16 p9, v10

    .line 209
    .line 210
    move-object/from16 p10, v11

    .line 211
    .line 212
    move-object/from16 p11, v12

    .line 213
    .line 214
    move-object/from16 p12, v13

    .line 215
    .line 216
    move-object/from16 p13, v14

    .line 217
    .line 218
    move-object/from16 p14, v15

    .line 219
    .line 220
    invoke-static/range {p1 .. p33}, Lorg/xbet/statistic/main/common/presentation/viewmodel/e0;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/main/common/presentation/viewmodel/e0;

    .line 221
    .line 222
    .line 223
    move-result-object v1

    .line 224
    iput-object v1, v0, LOG0/a$b;->j0:Ldagger/internal/h;

    .line 225
    .line 226
    iget-object v1, v0, LOG0/a$b;->Z:Ldagger/internal/h;

    .line 227
    .line 228
    iget-object v2, v0, LOG0/a$b;->t:Ldagger/internal/h;

    .line 229
    .line 230
    iget-object v3, v0, LOG0/a$b;->B:Ldagger/internal/h;

    .line 231
    .line 232
    iget-object v4, v0, LOG0/a$b;->C:Ldagger/internal/h;

    .line 233
    .line 234
    iget-object v5, v0, LOG0/a$b;->o:Ldagger/internal/h;

    .line 235
    .line 236
    iget-object v6, v0, LOG0/a$b;->n:Ldagger/internal/h;

    .line 237
    .line 238
    iget-object v7, v0, LOG0/a$b;->m:Ldagger/internal/h;

    .line 239
    .line 240
    iget-object v8, v0, LOG0/a$b;->D:Ldagger/internal/h;

    .line 241
    .line 242
    iget-object v9, v0, LOG0/a$b;->w:Ldagger/internal/h;

    .line 243
    .line 244
    move-object/from16 p1, v1

    .line 245
    .line 246
    move-object/from16 p2, v2

    .line 247
    .line 248
    move-object/from16 p3, v3

    .line 249
    .line 250
    move-object/from16 p4, v4

    .line 251
    .line 252
    move-object/from16 p5, v5

    .line 253
    .line 254
    move-object/from16 p6, v6

    .line 255
    .line 256
    move-object/from16 p7, v7

    .line 257
    .line 258
    move-object/from16 p8, v8

    .line 259
    .line 260
    move-object/from16 p9, v9

    .line 261
    .line 262
    invoke-static/range {p1 .. p9}, Lorg/xbet/statistic/game_events/presentation/viewmodel/c;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/game_events/presentation/viewmodel/c;

    .line 263
    .line 264
    .line 265
    move-result-object v1

    .line 266
    iput-object v1, v0, LOG0/a$b;->k0:Ldagger/internal/h;

    .line 267
    .line 268
    iget-object v1, v0, LOG0/a$b;->h0:Ldagger/internal/h;

    .line 269
    .line 270
    iget-object v2, v0, LOG0/a$b;->m:Ldagger/internal/h;

    .line 271
    .line 272
    iget-object v3, v0, LOG0/a$b;->n:Ldagger/internal/h;

    .line 273
    .line 274
    iget-object v4, v0, LOG0/a$b;->o:Ldagger/internal/h;

    .line 275
    .line 276
    iget-object v5, v0, LOG0/a$b;->t:Ldagger/internal/h;

    .line 277
    .line 278
    iget-object v6, v0, LOG0/a$b;->B:Ldagger/internal/h;

    .line 279
    .line 280
    iget-object v7, v0, LOG0/a$b;->C:Ldagger/internal/h;

    .line 281
    .line 282
    iget-object v8, v0, LOG0/a$b;->D:Ldagger/internal/h;

    .line 283
    .line 284
    iget-object v9, v0, LOG0/a$b;->w:Ldagger/internal/h;

    .line 285
    .line 286
    move-object/from16 p1, v1

    .line 287
    .line 288
    move-object/from16 p2, v2

    .line 289
    .line 290
    move-object/from16 p3, v3

    .line 291
    .line 292
    move-object/from16 p4, v4

    .line 293
    .line 294
    move-object/from16 p5, v5

    .line 295
    .line 296
    move-object/from16 p6, v6

    .line 297
    .line 298
    move-object/from16 p7, v7

    .line 299
    .line 300
    move-object/from16 p8, v8

    .line 301
    .line 302
    move-object/from16 p9, v9

    .line 303
    .line 304
    invoke-static/range {p1 .. p9}, Lorg/xbet/statistic/forecast/presentation/viewmodel/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/forecast/presentation/viewmodel/a;

    .line 305
    .line 306
    .line 307
    move-result-object v1

    .line 308
    iput-object v1, v0, LOG0/a$b;->l0:Ldagger/internal/h;

    .line 309
    .line 310
    iget-object v1, v0, LOG0/a$b;->m:Ldagger/internal/h;

    .line 311
    .line 312
    iget-object v2, v0, LOG0/a$b;->n:Ldagger/internal/h;

    .line 313
    .line 314
    iget-object v3, v0, LOG0/a$b;->V:Ldagger/internal/h;

    .line 315
    .line 316
    iget-object v4, v0, LOG0/a$b;->o:Ldagger/internal/h;

    .line 317
    .line 318
    iget-object v5, v0, LOG0/a$b;->q:Ldagger/internal/h;

    .line 319
    .line 320
    iget-object v6, v0, LOG0/a$b;->v:Ldagger/internal/h;

    .line 321
    .line 322
    iget-object v7, v0, LOG0/a$b;->t:Ldagger/internal/h;

    .line 323
    .line 324
    iget-object v8, v0, LOG0/a$b;->C:Ldagger/internal/h;

    .line 325
    .line 326
    iget-object v9, v0, LOG0/a$b;->l:Ldagger/internal/h;

    .line 327
    .line 328
    move-object/from16 p1, v1

    .line 329
    .line 330
    move-object/from16 p2, v2

    .line 331
    .line 332
    move-object/from16 p3, v3

    .line 333
    .line 334
    move-object/from16 p4, v4

    .line 335
    .line 336
    move-object/from16 p5, v5

    .line 337
    .line 338
    move-object/from16 p6, v6

    .line 339
    .line 340
    move-object/from16 p7, v7

    .line 341
    .line 342
    move-object/from16 p8, v8

    .line 343
    .line 344
    move-object/from16 p9, v9

    .line 345
    .line 346
    invoke-static/range {p1 .. p9}, Lorg/xbet/statistic/lineup/presentation/g;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/lineup/presentation/g;

    .line 347
    .line 348
    .line 349
    move-result-object v1

    .line 350
    iput-object v1, v0, LOG0/a$b;->m0:Ldagger/internal/h;

    .line 351
    .line 352
    return-void
.end method

.method public final f(Lorg/xbet/statistic/main/common/presentation/MainStatisticComponentFragment;)Lorg/xbet/statistic/main/common/presentation/MainStatisticComponentFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LOG0/a$b;->i()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/main/common/presentation/d;->a(Lorg/xbet/statistic/main/common/presentation/MainStatisticComponentFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final g(Lorg/xbet/statistic/main/plain_list/presentation/MainStatisticFragment;)Lorg/xbet/statistic/main/plain_list/presentation/MainStatisticFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LOG0/a$b;->i()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/main/plain_list/presentation/h;->a(Lorg/xbet/statistic/main/plain_list/presentation/MainStatisticFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final h()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x5

    .line 2
    invoke-static {v0}, Ldagger/internal/f;->b(I)Ldagger/internal/f;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    const-class v1, Lorg/xbet/statistic/main/plain_list/presentation/MainStatisticViewModel;

    .line 7
    .line 8
    iget-object v2, p0, LOG0/a$b;->L:Ldagger/internal/h;

    .line 9
    .line 10
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    const-class v1, Lorg/xbet/statistic/main/common/presentation/viewmodel/MainStatisticUdfViewModel;

    .line 15
    .line 16
    iget-object v2, p0, LOG0/a$b;->j0:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    const-class v1, Lorg/xbet/statistic/game_events/presentation/viewmodel/GameEventsViewModel;

    .line 23
    .line 24
    iget-object v2, p0, LOG0/a$b;->k0:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    const-class v1, Lorg/xbet/statistic/forecast/presentation/viewmodel/ForecastStatisticViewModel;

    .line 31
    .line 32
    iget-object v2, p0, LOG0/a$b;->l0:Ldagger/internal/h;

    .line 33
    .line 34
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    const-class v1, Lorg/xbet/statistic/lineup/presentation/LineUpViewModel;

    .line 39
    .line 40
    iget-object v2, p0, LOG0/a$b;->m0:Ldagger/internal/h;

    .line 41
    .line 42
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    invoke-virtual {v0}, Ldagger/internal/f;->a()Ljava/util/Map;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    return-object v0
.end method

.method public final i()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LOG0/a$b;->h()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
