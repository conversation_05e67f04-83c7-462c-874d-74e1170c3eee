.class public final Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lmg/b;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u000b\n\u0002\u0008\r\n\u0002\u0010!\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u0000 \u001c2\u00020\u0001:\u0001\u0011B\u001f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\u000b\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u001f\u0010\u0011\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u000fH\u0016\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0017\u0010\u0015\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0013H\u0016\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0019\u0010\u0018\u001a\u00020\n2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u000fH\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0017\u0010\u001c\u001a\u00020\u001b2\u0006\u0010\u001a\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u0017\u0010\u001e\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u0016J\u000f\u0010\u001f\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010\u000cJ\u0017\u0010 \u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008 \u0010\u0016J\u0017\u0010!\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008!\u0010\u0016J\u0017\u0010\"\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008\"\u0010\u0016J\u0017\u0010#\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008#\u0010\u0016J\u0017\u0010$\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008$\u0010\u0016J\u0017\u0010%\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008%\u0010\u0016R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010&R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\'R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010(R\u001a\u0010,\u001a\u0008\u0012\u0004\u0012\u00020\u00130)8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u00100\u001a\u00020-8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0018\u00104\u001a\u0004\u0018\u0001018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0018\u00106\u001a\u0004\u0018\u00010\u000f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0018\u00105R\u0018\u00108\u001a\u0004\u0018\u00010\r8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001f\u00107\u00a8\u00069"
    }
    d2 = {
        "Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;",
        "Lmg/b;",
        "Ls41/a;",
        "userAgreementDocumentsDialogProvider",
        "LTZ0/a;",
        "actionDialogManager",
        "LHX0/e;",
        "resourceManager",
        "<init>",
        "(Ls41/a;LTZ0/a;LHX0/e;)V",
        "",
        "c",
        "()V",
        "Landroidx/fragment/app/FragmentManager;",
        "fragmentManager",
        "Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;",
        "alertsPipeType",
        "a",
        "(Landroidx/fragment/app/FragmentManager;Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)V",
        "Lmg/a;",
        "alertsPipeParams",
        "b",
        "(Lmg/a;)V",
        "newShowedAlert",
        "g",
        "(Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)V",
        "alertType",
        "",
        "i",
        "(Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)Z",
        "j",
        "h",
        "l",
        "q",
        "m",
        "n",
        "o",
        "p",
        "Ls41/a;",
        "LTZ0/a;",
        "LHX0/e;",
        "",
        "d",
        "Ljava/util/List;",
        "dialogsToShowingList",
        "Lkotlinx/coroutines/N;",
        "e",
        "Lkotlinx/coroutines/N;",
        "scope",
        "Lkotlinx/coroutines/x0;",
        "f",
        "Lkotlinx/coroutines/x0;",
        "executionJob",
        "Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;",
        "showedAlert",
        "Landroidx/fragment/app/FragmentManager;",
        "showedDialogActualFragmentManager",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final i:Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Ls41/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LTZ0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lmg/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lkotlinx/coroutines/N;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:Lkotlinx/coroutines/x0;

.field public g:Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;

.field public h:Landroidx/fragment/app/FragmentManager;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->i:Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$a;

    return-void
.end method

.method public constructor <init>(Ls41/a;LTZ0/a;LHX0/e;)V
    .locals 0
    .param p1    # Ls41/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LTZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->a:Ls41/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->b:LTZ0/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->c:LHX0/e;

    .line 9
    .line 10
    new-instance p1, Ljava/util/ArrayList;

    .line 11
    .line 12
    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    .line 13
    .line 14
    .line 15
    iput-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->d:Ljava/util/List;

    .line 16
    .line 17
    const/4 p1, 0x0

    .line 18
    const/4 p2, 0x1

    .line 19
    invoke-static {p1, p2, p1}, Lkotlinx/coroutines/Q0;->b(Lkotlinx/coroutines/x0;ILjava/lang/Object;)Lkotlinx/coroutines/z;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-static {}, Lkotlinx/coroutines/b0;->a()Lkotlinx/coroutines/J;

    .line 24
    .line 25
    .line 26
    move-result-object p2

    .line 27
    invoke-interface {p1, p2}, Lkotlin/coroutines/CoroutineContext;->plus(Lkotlin/coroutines/CoroutineContext;)Lkotlin/coroutines/CoroutineContext;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-static {p1}, Lkotlinx/coroutines/O;->a(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    iput-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->e:Lkotlinx/coroutines/N;

    .line 36
    .line 37
    return-void
.end method

.method public static synthetic d(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->k(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic e(Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;)Ljava/util/List;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->d:Ljava/util/List;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic f(Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;Lmg/a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->j(Lmg/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final k(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method


# virtual methods
.method public a(Landroidx/fragment/app/FragmentManager;Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)V
    .locals 0
    .param p1    # Landroidx/fragment/app/FragmentManager;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->h:Landroidx/fragment/app/FragmentManager;

    .line 2
    .line 3
    invoke-virtual {p0, p2}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->g(Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public b(Lmg/a;)V
    .locals 9
    .param p1    # Lmg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->d:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    iget-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->f:Lkotlinx/coroutines/x0;

    .line 7
    .line 8
    if-eqz p1, :cond_0

    .line 9
    .line 10
    invoke-interface {p1}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    const/4 v0, 0x1

    .line 15
    if-ne p1, v0, :cond_0

    .line 16
    .line 17
    return-void

    .line 18
    :cond_0
    iget-object v1, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->e:Lkotlinx/coroutines/N;

    .line 19
    .line 20
    new-instance v2, Lorg/xbet/alerts_pipe_impl/presentation/a;

    .line 21
    .line 22
    invoke-direct {v2}, Lorg/xbet/alerts_pipe_impl/presentation/a;-><init>()V

    .line 23
    .line 24
    .line 25
    new-instance v6, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;

    .line 26
    .line 27
    const/4 p1, 0x0

    .line 28
    invoke-direct {v6, p0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;-><init>(Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/16 v7, 0xe

    .line 32
    .line 33
    const/4 v8, 0x0

    .line 34
    const/4 v3, 0x0

    .line 35
    const/4 v4, 0x0

    .line 36
    const/4 v5, 0x0

    .line 37
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    iput-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->f:Lkotlinx/coroutines/x0;

    .line 42
    .line 43
    return-void
.end method

.method public c()V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-virtual {p0, v0}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->g(Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)V

    .line 3
    .line 4
    .line 5
    iput-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->h:Landroidx/fragment/app/FragmentManager;

    .line 6
    .line 7
    return-void
.end method

.method public final g(Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->g:Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;

    .line 2
    .line 3
    return-void
.end method

.method public final h()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->h:Landroidx/fragment/app/FragmentManager;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-virtual {v0}, Landroidx/fragment/app/FragmentManager;->H0()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    if-eqz v1, :cond_1

    .line 20
    .line 21
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    check-cast v1, Landroidx/fragment/app/Fragment;

    .line 26
    .line 27
    instance-of v2, v1, Landroidx/fragment/app/l;

    .line 28
    .line 29
    if-eqz v2, :cond_0

    .line 30
    .line 31
    check-cast v1, Landroidx/fragment/app/l;

    .line 32
    .line 33
    invoke-virtual {v1}, Landroidx/fragment/app/l;->dismissAllowingStateLoss()V

    .line 34
    .line 35
    .line 36
    goto :goto_0

    .line 37
    :cond_1
    const/4 v0, 0x0

    .line 38
    invoke-virtual {p0, v0}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->g(Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)V

    .line 39
    .line 40
    .line 41
    iput-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->h:Landroidx/fragment/app/FragmentManager;

    .line 42
    .line 43
    return-void
.end method

.method public final i(Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)Z
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->g:Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-nez v0, :cond_0

    .line 5
    .line 6
    return v1

    .line 7
    :cond_0
    if-eqz v0, :cond_2

    .line 8
    .line 9
    invoke-static {p1}, Lorg/xbet/alerts_pipe_api/presentation/a;->a(Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)I

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    invoke-static {v0}, Lorg/xbet/alerts_pipe_api/presentation/a;->a(Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)I

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    if-ge p1, v0, :cond_1

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_1
    const/4 p1, 0x0

    .line 21
    return p1

    .line 22
    :cond_2
    :goto_0
    return v1
.end method

.method public final j(Lmg/a;)V
    .locals 1

    .line 1
    invoke-virtual {p1}, Lmg/a;->a()Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0, v0}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->i(Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->h()V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->l(Lmg/a;)V

    .line 15
    .line 16
    .line 17
    :cond_0
    return-void
.end method

.method public final l(Lmg/a;)V
    .locals 2

    .line 1
    invoke-virtual {p1}, Lmg/a;->b()Landroidx/fragment/app/FragmentManager;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iput-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->h:Landroidx/fragment/app/FragmentManager;

    .line 6
    .line 7
    invoke-virtual {p1}, Lmg/a;->a()Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    instance-of v1, v0, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;

    .line 12
    .line 13
    if-eqz v1, :cond_0

    .line 14
    .line 15
    invoke-virtual {p0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->q(Lmg/a;)V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :cond_0
    sget-object v1, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$ActivatePhoneKzAlert;->INSTANCE:Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$ActivatePhoneKzAlert;

    .line 20
    .line 21
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    if-eqz v1, :cond_1

    .line 26
    .line 27
    invoke-virtual {p0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->n(Lmg/a;)V

    .line 28
    .line 29
    .line 30
    return-void

    .line 31
    :cond_1
    sget-object v1, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;->INSTANCE:Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;

    .line 32
    .line 33
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    if-eqz v1, :cond_2

    .line 38
    .line 39
    invoke-virtual {p0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->m(Lmg/a;)V

    .line 40
    .line 41
    .line 42
    return-void

    .line 43
    :cond_2
    sget-object v1, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$KzFirstDepositBottom;->INSTANCE:Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$KzFirstDepositBottom;

    .line 44
    .line 45
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 46
    .line 47
    .line 48
    move-result v1

    .line 49
    if-eqz v1, :cond_3

    .line 50
    .line 51
    invoke-virtual {p0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->o(Lmg/a;)V

    .line 52
    .line 53
    .line 54
    return-void

    .line 55
    :cond_3
    instance-of v0, v0, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$NotIdentifiedKzAlert;

    .line 56
    .line 57
    if-eqz v0, :cond_4

    .line 58
    .line 59
    invoke-virtual {p0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->p(Lmg/a;)V

    .line 60
    .line 61
    .line 62
    return-void

    .line 63
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 64
    .line 65
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 66
    .line 67
    .line 68
    throw p1
.end method

.method public final m(Lmg/a;)V
    .locals 21

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/alerts_pipe_impl/presentation/b;->o0:Lorg/xbet/alerts_pipe_impl/presentation/b$a;

    .line 4
    .line 5
    iget-object v2, v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->c:LHX0/e;

    .line 6
    .line 7
    sget v3, Lpb/k;->kz_identification_alert_dialog_freebet_title:I

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    new-array v5, v4, [Ljava/lang/Object;

    .line 11
    .line 12
    invoke-interface {v2, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v7

    .line 16
    iget-object v2, v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->c:LHX0/e;

    .line 17
    .line 18
    sget v3, Lpb/k;->kz_identification_alert_dialog_freebet_body:I

    .line 19
    .line 20
    new-array v5, v4, [Ljava/lang/Object;

    .line 21
    .line 22
    invoke-interface {v2, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v8

    .line 26
    iget-object v2, v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->c:LHX0/e;

    .line 27
    .line 28
    sget v3, Lpb/k;->pass_identification:I

    .line 29
    .line 30
    new-array v5, v4, [Ljava/lang/Object;

    .line 31
    .line 32
    invoke-interface {v2, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object v9

    .line 36
    iget-object v2, v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->c:LHX0/e;

    .line 37
    .line 38
    sget v3, Lpb/k;->later:I

    .line 39
    .line 40
    new-array v4, v4, [Ljava/lang/Object;

    .line 41
    .line 42
    invoke-interface {v2, v3, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v10

    .line 46
    sget-object v17, Lorg/xbet/uikit/components/dialog/AlertType;->INFO:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 47
    .line 48
    invoke-virtual/range {p1 .. p1}, Lmg/a;->c()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object v12

    .line 52
    new-instance v6, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 53
    .line 54
    const/16 v19, 0xbd0

    .line 55
    .line 56
    const/16 v20, 0x0

    .line 57
    .line 58
    const/4 v11, 0x0

    .line 59
    const/4 v13, 0x0

    .line 60
    const/4 v14, 0x0

    .line 61
    const/4 v15, 0x0

    .line 62
    const/16 v16, 0x0

    .line 63
    .line 64
    const/16 v18, 0x0

    .line 65
    .line 66
    invoke-direct/range {v6 .. v20}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 67
    .line 68
    .line 69
    invoke-virtual/range {p1 .. p1}, Lmg/a;->a()Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;

    .line 70
    .line 71
    .line 72
    move-result-object v2

    .line 73
    invoke-virtual {v1, v6, v2}, Lorg/xbet/alerts_pipe_impl/presentation/b$a;->a(Lorg/xbet/uikit/components/dialog/DialogFields;Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)Lorg/xbet/alerts_pipe_impl/presentation/b;

    .line 74
    .line 75
    .line 76
    move-result-object v1

    .line 77
    iget-object v2, v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->b:LTZ0/a;

    .line 78
    .line 79
    invoke-virtual/range {p1 .. p1}, Lmg/a;->b()Landroidx/fragment/app/FragmentManager;

    .line 80
    .line 81
    .line 82
    move-result-object v3

    .line 83
    invoke-virtual {v2, v1, v3}, LTZ0/a;->c(LTZ0/h;Landroidx/fragment/app/FragmentManager;)V

    .line 84
    .line 85
    .line 86
    return-void
.end method

.method public final n(Lmg/a;)V
    .locals 21

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/alerts_pipe_impl/presentation/b;->o0:Lorg/xbet/alerts_pipe_impl/presentation/b$a;

    .line 4
    .line 5
    iget-object v2, v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->c:LHX0/e;

    .line 6
    .line 7
    sget v3, Lpb/k;->kz_identification_alert_dialog_phone_title:I

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    new-array v5, v4, [Ljava/lang/Object;

    .line 11
    .line 12
    invoke-interface {v2, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v7

    .line 16
    iget-object v2, v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->c:LHX0/e;

    .line 17
    .line 18
    sget v3, Lpb/k;->kz_identification_alert_dialog_link_phone_body:I

    .line 19
    .line 20
    new-array v5, v4, [Ljava/lang/Object;

    .line 21
    .line 22
    invoke-interface {v2, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v8

    .line 26
    iget-object v2, v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->c:LHX0/e;

    .line 27
    .line 28
    sget v3, Lpb/k;->kz_identification_alert_dialog_phone_positive:I

    .line 29
    .line 30
    new-array v5, v4, [Ljava/lang/Object;

    .line 31
    .line 32
    invoke-interface {v2, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object v9

    .line 36
    iget-object v2, v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->c:LHX0/e;

    .line 37
    .line 38
    sget v3, Lpb/k;->later:I

    .line 39
    .line 40
    new-array v4, v4, [Ljava/lang/Object;

    .line 41
    .line 42
    invoke-interface {v2, v3, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v10

    .line 46
    sget-object v17, Lorg/xbet/uikit/components/dialog/AlertType;->INFO:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 47
    .line 48
    invoke-virtual/range {p1 .. p1}, Lmg/a;->c()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object v12

    .line 52
    new-instance v6, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 53
    .line 54
    const/16 v19, 0xbd0

    .line 55
    .line 56
    const/16 v20, 0x0

    .line 57
    .line 58
    const/4 v11, 0x0

    .line 59
    const/4 v13, 0x0

    .line 60
    const/4 v14, 0x0

    .line 61
    const/4 v15, 0x0

    .line 62
    const/16 v16, 0x0

    .line 63
    .line 64
    const/16 v18, 0x0

    .line 65
    .line 66
    invoke-direct/range {v6 .. v20}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 67
    .line 68
    .line 69
    invoke-virtual/range {p1 .. p1}, Lmg/a;->a()Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;

    .line 70
    .line 71
    .line 72
    move-result-object v2

    .line 73
    invoke-virtual {v1, v6, v2}, Lorg/xbet/alerts_pipe_impl/presentation/b$a;->a(Lorg/xbet/uikit/components/dialog/DialogFields;Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)Lorg/xbet/alerts_pipe_impl/presentation/b;

    .line 74
    .line 75
    .line 76
    move-result-object v1

    .line 77
    iget-object v2, v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->b:LTZ0/a;

    .line 78
    .line 79
    invoke-virtual/range {p1 .. p1}, Lmg/a;->b()Landroidx/fragment/app/FragmentManager;

    .line 80
    .line 81
    .line 82
    move-result-object v3

    .line 83
    invoke-virtual {v2, v1, v3}, LTZ0/a;->c(LTZ0/h;Landroidx/fragment/app/FragmentManager;)V

    .line 84
    .line 85
    .line 86
    return-void
.end method

.method public final o(Lmg/a;)V
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;->m0:Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$a;

    .line 2
    .line 3
    invoke-virtual {p1}, Lmg/a;->b()Landroidx/fragment/app/FragmentManager;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$a;->a(Landroidx/fragment/app/FragmentManager;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final p(Lmg/a;)V
    .locals 21

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->b1:Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$a;

    .line 4
    .line 5
    iget-object v2, v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->c:LHX0/e;

    .line 6
    .line 7
    sget v3, Lpb/k;->pass_identification_title:I

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    new-array v5, v4, [Ljava/lang/Object;

    .line 11
    .line 12
    invoke-interface {v2, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v7

    .line 16
    invoke-virtual/range {p1 .. p1}, Lmg/a;->a()Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    instance-of v3, v2, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$NotIdentifiedKzAlert;

    .line 21
    .line 22
    const/4 v5, 0x0

    .line 23
    if-eqz v3, :cond_0

    .line 24
    .line 25
    check-cast v2, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$NotIdentifiedKzAlert;

    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_0
    move-object v2, v5

    .line 29
    :goto_0
    if-eqz v2, :cond_1

    .line 30
    .line 31
    invoke-virtual {v2}, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$NotIdentifiedKzAlert;->a()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v5

    .line 35
    :cond_1
    if-nez v5, :cond_2

    .line 36
    .line 37
    const-string v5, ""

    .line 38
    .line 39
    :cond_2
    move-object v8, v5

    .line 40
    iget-object v2, v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->c:LHX0/e;

    .line 41
    .line 42
    sget v3, Lpb/k;->pass_identification:I

    .line 43
    .line 44
    new-array v5, v4, [Ljava/lang/Object;

    .line 45
    .line 46
    invoke-interface {v2, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object v9

    .line 50
    iget-object v2, v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->c:LHX0/e;

    .line 51
    .line 52
    sget v3, Lpb/k;->later:I

    .line 53
    .line 54
    new-array v4, v4, [Ljava/lang/Object;

    .line 55
    .line 56
    invoke-interface {v2, v3, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object v10

    .line 60
    sget-object v17, Lorg/xbet/uikit/components/dialog/AlertType;->INFO:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 61
    .line 62
    invoke-virtual/range {p1 .. p1}, Lmg/a;->c()Ljava/lang/String;

    .line 63
    .line 64
    .line 65
    move-result-object v12

    .line 66
    new-instance v6, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 67
    .line 68
    const/16 v19, 0xbd0

    .line 69
    .line 70
    const/16 v20, 0x0

    .line 71
    .line 72
    const/4 v11, 0x0

    .line 73
    const/4 v13, 0x0

    .line 74
    const/4 v14, 0x0

    .line 75
    const/4 v15, 0x0

    .line 76
    const/16 v16, 0x0

    .line 77
    .line 78
    const/16 v18, 0x0

    .line 79
    .line 80
    invoke-direct/range {v6 .. v20}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 81
    .line 82
    .line 83
    invoke-virtual {v1, v6}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$a;->a(Lorg/xbet/uikit/components/dialog/DialogFields;)Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    iget-object v2, v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->b:LTZ0/a;

    .line 88
    .line 89
    invoke-virtual/range {p1 .. p1}, Lmg/a;->b()Landroidx/fragment/app/FragmentManager;

    .line 90
    .line 91
    .line 92
    move-result-object v3

    .line 93
    invoke-virtual {v2, v1, v3}, LTZ0/a;->c(LTZ0/h;Landroidx/fragment/app/FragmentManager;)V

    .line 94
    .line 95
    .line 96
    return-void
.end method

.method public final q(Lmg/a;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->a:Ls41/a;

    .line 2
    .line 3
    invoke-virtual {p1}, Lmg/a;->b()Landroidx/fragment/app/FragmentManager;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p1}, Lmg/a;->a()Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    check-cast v2, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;

    .line 12
    .line 13
    invoke-virtual {v2}, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;->a()Z

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    invoke-virtual {p1}, Lmg/a;->c()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-interface {v0, v1, v2, p1}, Ls41/a;->a(Landroidx/fragment/app/FragmentManager;ZLjava/lang/String;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method
