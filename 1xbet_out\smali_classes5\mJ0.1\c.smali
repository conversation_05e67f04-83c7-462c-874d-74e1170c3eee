.class public final synthetic LmJ0/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Landroid/graphics/drawable/Drawable;


# direct methods
.method public synthetic constructor <init>(Landroid/graphics/drawable/Drawable;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LmJ0/c;->a:Landroid/graphics/drawable/Drawable;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LmJ0/c;->a:Landroid/graphics/drawable/Drawable;

    check-cast p1, LB4/a;

    invoke-static {v0, p1}, Lorg/xbet/statistic/player/impl/player/player_transfers/presentation/adapter/PlayerTransferAdapterDelegateKt;->a(Landroid/graphics/drawable/Drawable;LB4/a;)<PERSON><PERSON><PERSON>/Unit;

    move-result-object p1

    return-object p1
.end method
