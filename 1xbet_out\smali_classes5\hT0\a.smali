.class public final LhT0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LhT0/a$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u0000 \u00082\u00020\u0001:\u0001\u0008B\t\u0008\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J \u0010\u0008\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0006H\u0086\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "LhT0/a;",
        "",
        "<init>",
        "()V",
        "",
        "currentHour",
        "Lorg/xbet/themesettings/impl/domain/model/TimeFrame;",
        "timeFrame",
        "a",
        "(ILorg/xbet/themesettings/impl/domain/model/TimeFrame;)I",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:LhT0/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LhT0/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LhT0/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LhT0/a;->a:LhT0/a$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final a(ILorg/xbet/themesettings/impl/domain/model/TimeFrame;)I
    .locals 2
    .param p2    # Lorg/xbet/themesettings/impl/domain/model/TimeFrame;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/16 v0, 0xc

    .line 2
    .line 3
    if-ne p1, v0, :cond_0

    .line 4
    .line 5
    sget-object v1, Lorg/xbet/themesettings/impl/domain/model/TimeFrame;->AM:Lorg/xbet/themesettings/impl/domain/model/TimeFrame;

    .line 6
    .line 7
    if-ne p2, v1, :cond_0

    .line 8
    .line 9
    const/4 p1, 0x0

    .line 10
    return p1

    .line 11
    :cond_0
    if-ge p1, v0, :cond_1

    .line 12
    .line 13
    sget-object v1, Lorg/xbet/themesettings/impl/domain/model/TimeFrame;->PM:Lorg/xbet/themesettings/impl/domain/model/TimeFrame;

    .line 14
    .line 15
    if-ne p2, v1, :cond_1

    .line 16
    .line 17
    add-int/2addr p1, v0

    .line 18
    :cond_1
    return p1
.end method
