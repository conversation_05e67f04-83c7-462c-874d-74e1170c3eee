.class final Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.promo.presentation.OneXGamesPromoViewModel$getPromoItems$2"
    f = "OneXGamesPromoViewModel.kt"
    l = {
        0x115,
        0x117,
        0x118,
        0x11e
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->a4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field I$0:I

.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;

    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 14

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->label:I

    .line 6
    .line 7
    const/16 v2, 0xa

    .line 8
    .line 9
    const/4 v3, 0x4

    .line 10
    const/4 v4, 0x3

    .line 11
    const/4 v5, 0x2

    .line 12
    const/4 v6, 0x0

    .line 13
    const/4 v7, 0x1

    .line 14
    if-eqz v1, :cond_4

    .line 15
    .line 16
    if-eq v1, v7, :cond_3

    .line 17
    .line 18
    if-eq v1, v5, :cond_2

    .line 19
    .line 20
    if-eq v1, v4, :cond_1

    .line 21
    .line 22
    if-ne v1, v3, :cond_0

    .line 23
    .line 24
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->L$0:Ljava/lang/Object;

    .line 25
    .line 26
    check-cast v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 27
    .line 28
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    move-object v3, p1

    .line 32
    goto/16 :goto_9

    .line 33
    .line 34
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 35
    .line 36
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 37
    .line 38
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 39
    .line 40
    .line 41
    throw v0

    .line 42
    :cond_1
    iget v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->I$0:I

    .line 43
    .line 44
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    goto/16 :goto_7

    .line 48
    .line 49
    :cond_2
    iget v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->I$0:I

    .line 50
    .line 51
    iget-object v5, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->L$0:Ljava/lang/Object;

    .line 52
    .line 53
    check-cast v5, Ljava/util/List;

    .line 54
    .line 55
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    move-object v8, v5

    .line 59
    move-object v5, p1

    .line 60
    goto/16 :goto_3

    .line 61
    .line 62
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 63
    .line 64
    .line 65
    move-object v1, p1

    .line 66
    goto :goto_0

    .line 67
    :cond_4
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 68
    .line 69
    .line 70
    iget-object v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 71
    .line 72
    invoke-static {v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->C3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Lorg/xbet/core/domain/usecases/GetPromoItemsUseCase;

    .line 73
    .line 74
    .line 75
    move-result-object v1

    .line 76
    iput v7, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->label:I

    .line 77
    .line 78
    invoke-virtual {v1, p0}, Lorg/xbet/core/domain/usecases/GetPromoItemsUseCase;->c(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    move-result-object v1

    .line 82
    if-ne v1, v0, :cond_5

    .line 83
    .line 84
    goto/16 :goto_8

    .line 85
    .line 86
    :cond_5
    :goto_0
    iget-object v8, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 87
    .line 88
    check-cast v1, Ljava/util/List;

    .line 89
    .line 90
    invoke-static {v8, v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->u3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/util/List;)Ljava/util/List;

    .line 91
    .line 92
    .line 93
    move-result-object v1

    .line 94
    new-instance v8, Ljava/util/ArrayList;

    .line 95
    .line 96
    invoke-direct {v8}, Ljava/util/ArrayList;-><init>()V

    .line 97
    .line 98
    .line 99
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 100
    .line 101
    .line 102
    move-result-object v1

    .line 103
    :cond_6
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 104
    .line 105
    .line 106
    move-result v9

    .line 107
    if-eqz v9, :cond_7

    .line 108
    .line 109
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 110
    .line 111
    .line 112
    move-result-object v9

    .line 113
    move-object v10, v9

    .line 114
    check-cast v10, LTv/f;

    .line 115
    .line 116
    invoke-virtual {v10}, LTv/f;->b()Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 117
    .line 118
    .line 119
    move-result-object v10

    .line 120
    sget-object v11, Lorg/xbet/games_section/api/models/OneXGamesPromoType;->CASHBACK:Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 121
    .line 122
    if-eq v10, v11, :cond_6

    .line 123
    .line 124
    invoke-interface {v8, v9}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 125
    .line 126
    .line 127
    goto :goto_1

    .line 128
    :cond_7
    invoke-interface {v8}, Ljava/util/Collection;->isEmpty()Z

    .line 129
    .line 130
    .line 131
    move-result v1

    .line 132
    if-eqz v1, :cond_9

    .line 133
    .line 134
    :cond_8
    const/4 v1, 0x0

    .line 135
    goto :goto_2

    .line 136
    :cond_9
    invoke-interface {v8}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 137
    .line 138
    .line 139
    move-result-object v1

    .line 140
    :cond_a
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 141
    .line 142
    .line 143
    move-result v9

    .line 144
    if-eqz v9, :cond_8

    .line 145
    .line 146
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 147
    .line 148
    .line 149
    move-result-object v9

    .line 150
    check-cast v9, LTv/f;

    .line 151
    .line 152
    invoke-virtual {v9}, LTv/f;->b()Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 153
    .line 154
    .line 155
    move-result-object v10

    .line 156
    sget-object v11, Lorg/xbet/games_section/api/models/OneXGamesPromoType;->LUCKY_WHEEL:Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 157
    .line 158
    if-ne v10, v11, :cond_a

    .line 159
    .line 160
    invoke-virtual {v9}, LTv/f;->a()Lcom/xbet/onexuser/domain/entity/onexgame/OneXGamesActionResult;

    .line 161
    .line 162
    .line 163
    move-result-object v9

    .line 164
    invoke-virtual {v9}, Lcom/xbet/onexuser/domain/entity/onexgame/OneXGamesActionResult;->getUnderMaintenance()Z

    .line 165
    .line 166
    .line 167
    move-result v9

    .line 168
    if-eqz v9, :cond_a

    .line 169
    .line 170
    const/4 v1, 0x1

    .line 171
    :goto_2
    iget-object v9, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 172
    .line 173
    iput-object v8, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->L$0:Ljava/lang/Object;

    .line 174
    .line 175
    iput v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->I$0:I

    .line 176
    .line 177
    iput v5, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->label:I

    .line 178
    .line 179
    invoke-static {v9, p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->N3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 180
    .line 181
    .line 182
    move-result-object v5

    .line 183
    if-ne v5, v0, :cond_b

    .line 184
    .line 185
    goto/16 :goto_8

    .line 186
    .line 187
    :cond_b
    :goto_3
    check-cast v5, Ljava/lang/Boolean;

    .line 188
    .line 189
    invoke-virtual {v5}, Ljava/lang/Boolean;->booleanValue()Z

    .line 190
    .line 191
    .line 192
    move-result v5

    .line 193
    iget-object v9, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 194
    .line 195
    new-instance v10, Ljava/util/ArrayList;

    .line 196
    .line 197
    invoke-direct {v10}, Ljava/util/ArrayList;-><init>()V

    .line 198
    .line 199
    .line 200
    invoke-interface {v8}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 201
    .line 202
    .line 203
    move-result-object v8

    .line 204
    :goto_4
    invoke-interface {v8}, Ljava/util/Iterator;->hasNext()Z

    .line 205
    .line 206
    .line 207
    move-result v11

    .line 208
    if-eqz v11, :cond_d

    .line 209
    .line 210
    invoke-interface {v8}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 211
    .line 212
    .line 213
    move-result-object v11

    .line 214
    move-object v12, v11

    .line 215
    check-cast v12, LTv/f;

    .line 216
    .line 217
    invoke-virtual {v12}, LTv/f;->b()Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 218
    .line 219
    .line 220
    move-result-object v12

    .line 221
    sget-object v13, Lorg/xbet/games_section/api/models/OneXGamesPromoType;->LUCKY_WHEEL:Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 222
    .line 223
    if-ne v12, v13, :cond_c

    .line 224
    .line 225
    if-nez v5, :cond_c

    .line 226
    .line 227
    goto :goto_4

    .line 228
    :cond_c
    invoke-interface {v10, v11}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 229
    .line 230
    .line 231
    goto :goto_4

    .line 232
    :cond_d
    new-instance v5, Ljava/util/ArrayList;

    .line 233
    .line 234
    invoke-static {v10, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 235
    .line 236
    .line 237
    move-result v8

    .line 238
    invoke-direct {v5, v8}, Ljava/util/ArrayList;-><init>(I)V

    .line 239
    .line 240
    .line 241
    invoke-interface {v10}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 242
    .line 243
    .line 244
    move-result-object v8

    .line 245
    :goto_5
    invoke-interface {v8}, Ljava/util/Iterator;->hasNext()Z

    .line 246
    .line 247
    .line 248
    move-result v10

    .line 249
    if-eqz v10, :cond_f

    .line 250
    .line 251
    invoke-interface {v8}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 252
    .line 253
    .line 254
    move-result-object v10

    .line 255
    check-cast v10, LTv/f;

    .line 256
    .line 257
    if-eqz v1, :cond_e

    .line 258
    .line 259
    const/4 v11, 0x1

    .line 260
    goto :goto_6

    .line 261
    :cond_e
    const/4 v11, 0x0

    .line 262
    :goto_6
    invoke-static {v10, v11}, Lp50/b;->a(LTv/f;Z)Lq50/a;

    .line 263
    .line 264
    .line 265
    move-result-object v10

    .line 266
    invoke-interface {v5, v10}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 267
    .line 268
    .line 269
    goto :goto_5

    .line 270
    :cond_f
    const/4 v8, 0x0

    .line 271
    iput-object v8, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->L$0:Ljava/lang/Object;

    .line 272
    .line 273
    iput v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->I$0:I

    .line 274
    .line 275
    iput v4, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->label:I

    .line 276
    .line 277
    invoke-static {v9, v5, p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->L3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 278
    .line 279
    .line 280
    move-result-object v4

    .line 281
    if-ne v4, v0, :cond_10

    .line 282
    .line 283
    goto :goto_8

    .line 284
    :cond_10
    :goto_7
    if-eqz v1, :cond_17

    .line 285
    .line 286
    iget-object v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 287
    .line 288
    invoke-static {v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->B3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Lw30/q;

    .line 289
    .line 290
    .line 291
    move-result-object v8

    .line 292
    iput-object v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->L$0:Ljava/lang/Object;

    .line 293
    .line 294
    iput v3, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$getPromoItems$2;->label:I

    .line 295
    .line 296
    const/4 v9, 0x0

    .line 297
    const/4 v10, 0x0

    .line 298
    const/4 v12, 0x3

    .line 299
    const/4 v13, 0x0

    .line 300
    move-object v11, p0

    .line 301
    invoke-static/range {v8 .. v13}, Lw30/q$a;->a(Lw30/q;ZILkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 302
    .line 303
    .line 304
    move-result-object v3

    .line 305
    if-ne v3, v0, :cond_11

    .line 306
    .line 307
    :goto_8
    return-object v0

    .line 308
    :cond_11
    move-object v0, v1

    .line 309
    :goto_9
    check-cast v3, Ljava/lang/Iterable;

    .line 310
    .line 311
    new-instance v1, Ljava/util/ArrayList;

    .line 312
    .line 313
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 314
    .line 315
    .line 316
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 317
    .line 318
    .line 319
    move-result-object v3

    .line 320
    :cond_12
    :goto_a
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 321
    .line 322
    .line 323
    move-result v4

    .line 324
    if-eqz v4, :cond_15

    .line 325
    .line 326
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 327
    .line 328
    .line 329
    move-result-object v4

    .line 330
    move-object v5, v4

    .line 331
    check-cast v5, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;

    .line 332
    .line 333
    invoke-virtual {v5}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getUnderMaintenance()Z

    .line 334
    .line 335
    .line 336
    move-result v8

    .line 337
    if-nez v8, :cond_14

    .line 338
    .line 339
    invoke-virtual {v5}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getEnable()Z

    .line 340
    .line 341
    .line 342
    move-result v5

    .line 343
    if-nez v5, :cond_13

    .line 344
    .line 345
    goto :goto_b

    .line 346
    :cond_13
    const/4 v5, 0x0

    .line 347
    goto :goto_c

    .line 348
    :cond_14
    :goto_b
    const/4 v5, 0x1

    .line 349
    :goto_c
    if-eqz v5, :cond_12

    .line 350
    .line 351
    invoke-interface {v1, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 352
    .line 353
    .line 354
    goto :goto_a

    .line 355
    :cond_15
    new-instance v3, Ljava/util/ArrayList;

    .line 356
    .line 357
    invoke-static {v1, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 358
    .line 359
    .line 360
    move-result v2

    .line 361
    invoke-direct {v3, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 362
    .line 363
    .line 364
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 365
    .line 366
    .line 367
    move-result-object v1

    .line 368
    :goto_d
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 369
    .line 370
    .line 371
    move-result v2

    .line 372
    if-eqz v2, :cond_16

    .line 373
    .line 374
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 375
    .line 376
    .line 377
    move-result-object v2

    .line 378
    check-cast v2, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;

    .line 379
    .line 380
    invoke-virtual {v2}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getId()J

    .line 381
    .line 382
    .line 383
    move-result-wide v4

    .line 384
    invoke-static {v4, v5}, LHc/a;->f(J)Ljava/lang/Long;

    .line 385
    .line 386
    .line 387
    move-result-object v2

    .line 388
    invoke-interface {v3, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 389
    .line 390
    .line 391
    goto :goto_d

    .line 392
    :cond_16
    invoke-static {v0, v3}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->U3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/util/List;)V

    .line 393
    .line 394
    .line 395
    :cond_17
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 396
    .line 397
    return-object v0
.end method
