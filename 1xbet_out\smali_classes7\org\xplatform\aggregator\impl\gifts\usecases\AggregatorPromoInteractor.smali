.class public final Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J$\u0010\r\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000c0\u000b0\n2\u0006\u0010\t\u001a\u00020\u0008H\u0086@\u00a2\u0006\u0004\u0008\r\u0010\u000eJ,\u0010\u0012\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00110\u000b0\n2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u0010\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0016\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000bH\u0086@\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0016\u0010\u0016\u001a\u0008\u0012\u0004\u0012\u00020\u00110\u000bH\u0086@\u00a2\u0006\u0004\u0008\u0016\u0010\u0015J(\u0010\u001c\u001a\u00020\u001b2\u0006\u0010\u0017\u001a\u00020\u00082\u0006\u0010\u0018\u001a\u00020\u000f2\u0006\u0010\u001a\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ#\u0010\u001f\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000b2\u000c\u0010\u001e\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000bH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 J#\u0010!\u001a\u0008\u0012\u0004\u0012\u00020\u00110\u000b2\u000c\u0010\u001e\u001a\u0008\u0012\u0004\u0012\u00020\u00110\u000bH\u0002\u00a2\u0006\u0004\u0008!\u0010 R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010#R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010%\u00a8\u0006&"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;",
        "",
        "Lya1/a;",
        "promoRepository",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "tokenRefresher",
        "<init>",
        "(Lya1/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)V",
        "",
        "accountId",
        "Lkotlin/Result;",
        "",
        "Lxa1/a;",
        "f",
        "(JLkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "countryId",
        "Lxa1/c;",
        "g",
        "(JILkotlin/coroutines/e;)Ljava/lang/Object;",
        "h",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "i",
        "currentAccountId",
        "bonusId",
        "Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;",
        "status",
        "Lxa1/b;",
        "j",
        "(JILorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "bonusList",
        "d",
        "(Ljava/util/List;)Ljava/util/List;",
        "e",
        "a",
        "Lya1/a;",
        "b",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lya1/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lya1/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)V
    .locals 0
    .param p1    # Lya1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lcom/xbet/onexuser/domain/managers/TokenRefresher;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->a:Lya1/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 7
    .line 8
    return-void
.end method

.method public static final synthetic a(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;Ljava/util/List;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->d(Ljava/util/List;)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic b(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;Ljava/util/List;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->e(Ljava/util/List;)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic c(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;)Lya1/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->a:Lya1/a;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final d(Ljava/util/List;)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lxa1/a;",
            ">;)",
            "Ljava/util/List<",
            "Lxa1/a;",
            ">;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x4

    .line 2
    new-array v0, v0, [Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 3
    .line 4
    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->ACTIVE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 5
    .line 6
    const/4 v2, 0x0

    .line 7
    aput-object v1, v0, v2

    .line 8
    .line 9
    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->READY:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 10
    .line 11
    const/4 v2, 0x1

    .line 12
    aput-object v1, v0, v2

    .line 13
    .line 14
    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->DELETE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 15
    .line 16
    const/4 v2, 0x2

    .line 17
    aput-object v1, v0, v2

    .line 18
    .line 19
    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->INTERRUPT:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 20
    .line 21
    const/4 v2, 0x3

    .line 22
    aput-object v1, v0, v2

    .line 23
    .line 24
    invoke-static {v0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    new-instance v1, Ljava/util/ArrayList;

    .line 29
    .line 30
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 31
    .line 32
    .line 33
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 38
    .line 39
    .line 40
    move-result v2

    .line 41
    if-eqz v2, :cond_1

    .line 42
    .line 43
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    move-object v3, v2

    .line 48
    check-cast v3, Lxa1/a;

    .line 49
    .line 50
    invoke-virtual {v3}, Lxa1/a;->i()Lxa1/h;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    invoke-virtual {v3}, Lxa1/h;->a()Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 55
    .line 56
    .line 57
    move-result-object v3

    .line 58
    invoke-interface {v0, v3}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 59
    .line 60
    .line 61
    move-result v3

    .line 62
    if-eqz v3, :cond_0

    .line 63
    .line 64
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 65
    .line 66
    .line 67
    goto :goto_0

    .line 68
    :cond_1
    return-object v1
.end method

.method public final e(Ljava/util/List;)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lxa1/c;",
            ">;)",
            "Ljava/util/List<",
            "Lxa1/c;",
            ">;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x5

    .line 2
    new-array v0, v0, [Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 3
    .line 4
    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->ACTIVE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 5
    .line 6
    const/4 v2, 0x0

    .line 7
    aput-object v1, v0, v2

    .line 8
    .line 9
    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->READY:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 10
    .line 11
    const/4 v2, 0x1

    .line 12
    aput-object v1, v0, v2

    .line 13
    .line 14
    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->DELETE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 15
    .line 16
    const/4 v2, 0x2

    .line 17
    aput-object v1, v0, v2

    .line 18
    .line 19
    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->INTERRUPT:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 20
    .line 21
    const/4 v2, 0x3

    .line 22
    aput-object v1, v0, v2

    .line 23
    .line 24
    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->AWAITING_BY_OPERATOR:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 25
    .line 26
    const/4 v2, 0x4

    .line 27
    aput-object v1, v0, v2

    .line 28
    .line 29
    invoke-static {v0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    new-instance v1, Ljava/util/ArrayList;

    .line 34
    .line 35
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 36
    .line 37
    .line 38
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 43
    .line 44
    .line 45
    move-result v2

    .line 46
    if-eqz v2, :cond_1

    .line 47
    .line 48
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    move-object v3, v2

    .line 53
    check-cast v3, Lxa1/c;

    .line 54
    .line 55
    invoke-virtual {v3}, Lxa1/c;->f()Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 56
    .line 57
    .line 58
    move-result-object v3

    .line 59
    invoke-interface {v0, v3}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 60
    .line 61
    .line 62
    move-result v3

    .line 63
    if-eqz v3, :cond_0

    .line 64
    .line 65
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 66
    .line 67
    .line 68
    goto :goto_0

    .line 69
    :cond_1
    return-object v1
.end method

.method public final f(JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 12
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+",
            "Ljava/util/List<",
            "Lxa1/a;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p3

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p3}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p3, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x0

    .line 35
    const/4 v5, 0x1

    .line 36
    if-eqz v2, :cond_4

    .line 37
    .line 38
    if-eq v2, v5, :cond_3

    .line 39
    .line 40
    if-ne v2, v3, :cond_2

    .line 41
    .line 42
    iget p1, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->I$1:I

    .line 43
    .line 44
    iget p2, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->I$0:I

    .line 45
    .line 46
    iget-wide v6, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->J$0:J

    .line 47
    .line 48
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->L$0:Ljava/lang/Object;

    .line 49
    .line 50
    check-cast v2, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 51
    .line 52
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    :cond_1
    move p3, p1

    .line 56
    move-object v10, v0

    .line 57
    move v0, p2

    .line 58
    move-wide p1, v6

    .line 59
    move-object v6, v2

    .line 60
    move-object v2, v10

    .line 61
    goto :goto_1

    .line 62
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 63
    .line 64
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 65
    .line 66
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 67
    .line 68
    .line 69
    throw p1

    .line 70
    :cond_3
    iget p1, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->I$1:I

    .line 71
    .line 72
    iget p2, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->I$0:I

    .line 73
    .line 74
    iget-wide v6, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->J$0:J

    .line 75
    .line 76
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->L$0:Ljava/lang/Object;

    .line 77
    .line 78
    check-cast v2, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 79
    .line 80
    :try_start_0
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 81
    .line 82
    .line 83
    goto :goto_2

    .line 84
    :catchall_0
    move-exception p3

    .line 85
    goto :goto_3

    .line 86
    :cond_4
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 87
    .line 88
    .line 89
    const/4 p3, 0x0

    .line 90
    move-object v6, p0

    .line 91
    move-object v2, v0

    .line 92
    const/4 v0, 0x0

    .line 93
    :goto_1
    :try_start_1
    sget-object v7, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 94
    .line 95
    iget-object v7, v6, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 96
    .line 97
    new-instance v8, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$2$1;

    .line 98
    .line 99
    const/4 v9, 0x0

    .line 100
    invoke-direct {v8, v6, p1, p2, v9}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$2$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;JLkotlin/coroutines/e;)V

    .line 101
    .line 102
    .line 103
    iput-object v6, v2, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->L$0:Ljava/lang/Object;

    .line 104
    .line 105
    iput-wide p1, v2, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->J$0:J

    .line 106
    .line 107
    iput v0, v2, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->I$0:I

    .line 108
    .line 109
    iput p3, v2, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->I$1:I

    .line 110
    .line 111
    iput v5, v2, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->label:I

    .line 112
    .line 113
    invoke-virtual {v7, v8, v2}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object v7
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 117
    if-ne v7, v1, :cond_5

    .line 118
    .line 119
    goto/16 :goto_7

    .line 120
    .line 121
    :cond_5
    move-wide v10, p1

    .line 122
    move p1, p3

    .line 123
    move p2, v0

    .line 124
    move-object v0, v2

    .line 125
    move-object v2, v6

    .line 126
    move-object p3, v7

    .line 127
    move-wide v6, v10

    .line 128
    :goto_2
    :try_start_2
    check-cast p3, Ljava/util/List;

    .line 129
    .line 130
    invoke-static {p3}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    move-result-object p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 134
    goto/16 :goto_9

    .line 135
    .line 136
    :catchall_1
    move-exception v7

    .line 137
    move-wide v10, p1

    .line 138
    move p1, p3

    .line 139
    move p2, v0

    .line 140
    move-object v0, v2

    .line 141
    move-object v2, v6

    .line 142
    move-object p3, v7

    .line 143
    move-wide v6, v10

    .line 144
    :goto_3
    if-eqz p2, :cond_6

    .line 145
    .line 146
    instance-of v8, p3, Lcom/xbet/onexcore/data/model/ServerException;

    .line 147
    .line 148
    if-eqz v8, :cond_6

    .line 149
    .line 150
    move-object v8, p3

    .line 151
    check-cast v8, Lcom/xbet/onexcore/data/model/ServerException;

    .line 152
    .line 153
    invoke-virtual {v8}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 154
    .line 155
    .line 156
    move-result v8

    .line 157
    if-eqz v8, :cond_6

    .line 158
    .line 159
    const/4 v8, 0x1

    .line 160
    goto :goto_4

    .line 161
    :cond_6
    const/4 v8, 0x0

    .line 162
    :goto_4
    instance-of v9, p3, Ljava/util/concurrent/CancellationException;

    .line 163
    .line 164
    if-nez v9, :cond_c

    .line 165
    .line 166
    instance-of v9, p3, Ljava/net/ConnectException;

    .line 167
    .line 168
    if-nez v9, :cond_c

    .line 169
    .line 170
    if-nez v8, :cond_c

    .line 171
    .line 172
    instance-of v8, p3, Lcom/xbet/onexcore/data/model/ServerException;

    .line 173
    .line 174
    if-eqz v8, :cond_9

    .line 175
    .line 176
    move-object v8, p3

    .line 177
    check-cast v8, Lcom/xbet/onexcore/data/model/ServerException;

    .line 178
    .line 179
    invoke-virtual {v8}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 180
    .line 181
    .line 182
    move-result v9

    .line 183
    if-nez v9, :cond_8

    .line 184
    .line 185
    invoke-virtual {v8}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 186
    .line 187
    .line 188
    move-result v8

    .line 189
    if-eqz v8, :cond_7

    .line 190
    .line 191
    goto :goto_5

    .line 192
    :cond_7
    const/4 v8, 0x0

    .line 193
    goto :goto_6

    .line 194
    :cond_8
    :goto_5
    const/4 v8, 0x1

    .line 195
    goto :goto_6

    .line 196
    :cond_9
    invoke-static {p3}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 197
    .line 198
    .line 199
    move-result v8

    .line 200
    if-nez v8, :cond_7

    .line 201
    .line 202
    goto :goto_5

    .line 203
    :goto_6
    add-int/2addr p1, v5

    .line 204
    const/4 v9, 0x3

    .line 205
    if-gt p1, v9, :cond_b

    .line 206
    .line 207
    if-eqz v8, :cond_a

    .line 208
    .line 209
    goto :goto_8

    .line 210
    :cond_a
    new-instance v8, Ljava/lang/StringBuilder;

    .line 211
    .line 212
    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    .line 213
    .line 214
    .line 215
    const-string v9, "error ("

    .line 216
    .line 217
    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 218
    .line 219
    .line 220
    invoke-virtual {v8, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 221
    .line 222
    .line 223
    const-string v9, "): "

    .line 224
    .line 225
    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 226
    .line 227
    .line 228
    invoke-virtual {v8, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 229
    .line 230
    .line 231
    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 232
    .line 233
    .line 234
    move-result-object p3

    .line 235
    sget-object v8, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 236
    .line 237
    invoke-virtual {v8, p3}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 238
    .line 239
    .line 240
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->L$0:Ljava/lang/Object;

    .line 241
    .line 242
    iput-wide v6, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->J$0:J

    .line 243
    .line 244
    iput p2, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->I$0:I

    .line 245
    .line 246
    iput p1, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->I$1:I

    .line 247
    .line 248
    iput v3, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableBonuses$1;->label:I

    .line 249
    .line 250
    const-wide/16 v8, 0xbb8

    .line 251
    .line 252
    invoke-static {v8, v9, v0}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 253
    .line 254
    .line 255
    move-result-object p3

    .line 256
    if-ne p3, v1, :cond_1

    .line 257
    .line 258
    :goto_7
    return-object v1

    .line 259
    :cond_b
    :goto_8
    sget-object p1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 260
    .line 261
    invoke-static {p3}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 262
    .line 263
    .line 264
    move-result-object p1

    .line 265
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 266
    .line 267
    .line 268
    move-result-object p1

    .line 269
    :goto_9
    return-object p1

    .line 270
    :cond_c
    throw p3
.end method

.method public final g(JILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 19
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JI",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+",
            "Ljava/util/List<",
            "Lxa1/c;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p4

    .line 2
    .line 3
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;

    .line 9
    .line 10
    iget v2, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->label:I

    .line 11
    .line 12
    const/high16 v3, -0x80000000

    .line 13
    .line 14
    and-int v4, v2, v3

    .line 15
    .line 16
    if-eqz v4, :cond_0

    .line 17
    .line 18
    sub-int/2addr v2, v3

    .line 19
    iput v2, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->label:I

    .line 20
    .line 21
    move-object/from16 v2, p0

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;

    .line 25
    .line 26
    move-object/from16 v2, p0

    .line 27
    .line 28
    invoke-direct {v1, v2, v0}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    :goto_0
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->result:Ljava/lang/Object;

    .line 32
    .line 33
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v3

    .line 37
    iget v4, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->label:I

    .line 38
    .line 39
    const/4 v5, 0x2

    .line 40
    const/4 v6, 0x0

    .line 41
    const/4 v7, 0x1

    .line 42
    if-eqz v4, :cond_4

    .line 43
    .line 44
    if-eq v4, v7, :cond_3

    .line 45
    .line 46
    if-ne v4, v5, :cond_2

    .line 47
    .line 48
    iget v4, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->I$2:I

    .line 49
    .line 50
    iget v8, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->I$1:I

    .line 51
    .line 52
    iget v9, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->I$0:I

    .line 53
    .line 54
    iget-wide v10, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->J$0:J

    .line 55
    .line 56
    iget-object v12, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->L$0:Ljava/lang/Object;

    .line 57
    .line 58
    check-cast v12, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 59
    .line 60
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 61
    .line 62
    .line 63
    :cond_1
    move/from16 v17, v9

    .line 64
    .line 65
    move-wide v15, v10

    .line 66
    move-object v14, v12

    .line 67
    goto :goto_1

    .line 68
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 69
    .line 70
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 71
    .line 72
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 73
    .line 74
    .line 75
    throw v0

    .line 76
    :cond_3
    iget v4, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->I$2:I

    .line 77
    .line 78
    iget v8, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->I$1:I

    .line 79
    .line 80
    iget v9, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->I$0:I

    .line 81
    .line 82
    iget-wide v10, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->J$0:J

    .line 83
    .line 84
    iget-object v12, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->L$0:Ljava/lang/Object;

    .line 85
    .line 86
    check-cast v12, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 87
    .line 88
    :try_start_0
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 89
    .line 90
    .line 91
    goto :goto_2

    .line 92
    :catchall_0
    move-exception v0

    .line 93
    goto :goto_4

    .line 94
    :cond_4
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 95
    .line 96
    .line 97
    move-wide/from16 v15, p1

    .line 98
    .line 99
    move/from16 v17, p3

    .line 100
    .line 101
    move-object v14, v2

    .line 102
    const/4 v4, 0x0

    .line 103
    const/4 v8, 0x0

    .line 104
    :goto_1
    :try_start_1
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 105
    .line 106
    iget-object v0, v14, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 107
    .line 108
    new-instance v13, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;

    .line 109
    .line 110
    const/16 v18, 0x0

    .line 111
    .line 112
    invoke-direct/range {v13 .. v18}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;JILkotlin/coroutines/e;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_2

    .line 113
    .line 114
    .line 115
    move-wide v10, v15

    .line 116
    move/from16 v9, v17

    .line 117
    .line 118
    :try_start_2
    iput-object v14, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->L$0:Ljava/lang/Object;

    .line 119
    .line 120
    iput-wide v10, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->J$0:J

    .line 121
    .line 122
    iput v9, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->I$0:I

    .line 123
    .line 124
    iput v8, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->I$1:I

    .line 125
    .line 126
    iput v4, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->I$2:I

    .line 127
    .line 128
    iput v7, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->label:I

    .line 129
    .line 130
    invoke-virtual {v0, v13, v1}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    move-result-object v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 134
    if-ne v0, v3, :cond_5

    .line 135
    .line 136
    goto/16 :goto_8

    .line 137
    .line 138
    :cond_5
    move-object v12, v14

    .line 139
    :goto_2
    :try_start_3
    check-cast v0, Ljava/util/List;

    .line 140
    .line 141
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 142
    .line 143
    .line 144
    move-result-object v0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 145
    goto/16 :goto_a

    .line 146
    .line 147
    :catchall_1
    move-exception v0

    .line 148
    :goto_3
    move-object v12, v14

    .line 149
    goto :goto_4

    .line 150
    :catchall_2
    move-exception v0

    .line 151
    move-wide v10, v15

    .line 152
    move/from16 v9, v17

    .line 153
    .line 154
    goto :goto_3

    .line 155
    :goto_4
    if-eqz v8, :cond_6

    .line 156
    .line 157
    instance-of v13, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 158
    .line 159
    if-eqz v13, :cond_6

    .line 160
    .line 161
    move-object v13, v0

    .line 162
    check-cast v13, Lcom/xbet/onexcore/data/model/ServerException;

    .line 163
    .line 164
    invoke-virtual {v13}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 165
    .line 166
    .line 167
    move-result v13

    .line 168
    if-eqz v13, :cond_6

    .line 169
    .line 170
    const/4 v13, 0x1

    .line 171
    goto :goto_5

    .line 172
    :cond_6
    const/4 v13, 0x0

    .line 173
    :goto_5
    instance-of v14, v0, Ljava/util/concurrent/CancellationException;

    .line 174
    .line 175
    if-nez v14, :cond_c

    .line 176
    .line 177
    instance-of v14, v0, Ljava/net/ConnectException;

    .line 178
    .line 179
    if-nez v14, :cond_c

    .line 180
    .line 181
    if-nez v13, :cond_c

    .line 182
    .line 183
    instance-of v13, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 184
    .line 185
    if-eqz v13, :cond_9

    .line 186
    .line 187
    move-object v13, v0

    .line 188
    check-cast v13, Lcom/xbet/onexcore/data/model/ServerException;

    .line 189
    .line 190
    invoke-virtual {v13}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 191
    .line 192
    .line 193
    move-result v14

    .line 194
    if-nez v14, :cond_8

    .line 195
    .line 196
    invoke-virtual {v13}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 197
    .line 198
    .line 199
    move-result v13

    .line 200
    if-eqz v13, :cond_7

    .line 201
    .line 202
    goto :goto_6

    .line 203
    :cond_7
    const/4 v13, 0x0

    .line 204
    goto :goto_7

    .line 205
    :cond_8
    :goto_6
    const/4 v13, 0x1

    .line 206
    goto :goto_7

    .line 207
    :cond_9
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 208
    .line 209
    .line 210
    move-result v13

    .line 211
    if-nez v13, :cond_7

    .line 212
    .line 213
    goto :goto_6

    .line 214
    :goto_7
    add-int/2addr v4, v7

    .line 215
    const/4 v14, 0x3

    .line 216
    if-gt v4, v14, :cond_b

    .line 217
    .line 218
    if-eqz v13, :cond_a

    .line 219
    .line 220
    goto :goto_9

    .line 221
    :cond_a
    new-instance v13, Ljava/lang/StringBuilder;

    .line 222
    .line 223
    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    .line 224
    .line 225
    .line 226
    const-string v14, "error ("

    .line 227
    .line 228
    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 229
    .line 230
    .line 231
    invoke-virtual {v13, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 232
    .line 233
    .line 234
    const-string v14, "): "

    .line 235
    .line 236
    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 237
    .line 238
    .line 239
    invoke-virtual {v13, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 240
    .line 241
    .line 242
    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 243
    .line 244
    .line 245
    move-result-object v0

    .line 246
    sget-object v13, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 247
    .line 248
    invoke-virtual {v13, v0}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 249
    .line 250
    .line 251
    iput-object v12, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->L$0:Ljava/lang/Object;

    .line 252
    .line 253
    iput-wide v10, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->J$0:J

    .line 254
    .line 255
    iput v9, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->I$0:I

    .line 256
    .line 257
    iput v8, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->I$1:I

    .line 258
    .line 259
    iput v4, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->I$2:I

    .line 260
    .line 261
    iput v5, v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$1;->label:I

    .line 262
    .line 263
    const-wide/16 v13, 0xbb8

    .line 264
    .line 265
    invoke-static {v13, v14, v1}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 266
    .line 267
    .line 268
    move-result-object v0

    .line 269
    if-ne v0, v3, :cond_1

    .line 270
    .line 271
    :goto_8
    return-object v3

    .line 272
    :cond_b
    :goto_9
    sget-object v1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 273
    .line 274
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 275
    .line 276
    .line 277
    move-result-object v0

    .line 278
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 279
    .line 280
    .line 281
    move-result-object v0

    .line 282
    :goto_a
    return-object v0

    .line 283
    :cond_c
    throw v0
.end method

.method public final h(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lxa1/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableBonuses$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableBonuses$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableBonuses$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableBonuses$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableBonuses$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableBonuses$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableBonuses$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableBonuses$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-object v0, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableBonuses$1;->L$0:Ljava/lang/Object;

    .line 39
    .line 40
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 41
    .line 42
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    goto :goto_1

    .line 46
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 47
    .line 48
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 49
    .line 50
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    throw p1

    .line 54
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->a:Lya1/a;

    .line 58
    .line 59
    iput-object p0, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableBonuses$1;->L$0:Ljava/lang/Object;

    .line 60
    .line 61
    iput v3, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableBonuses$1;->label:I

    .line 62
    .line 63
    invoke-interface {p1, v0}, Lya1/a;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    if-ne p1, v1, :cond_3

    .line 68
    .line 69
    return-object v1

    .line 70
    :cond_3
    move-object v0, p0

    .line 71
    :goto_1
    check-cast p1, Ljava/util/List;

    .line 72
    .line 73
    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->d(Ljava/util/List;)Ljava/util/List;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    return-object p1
.end method

.method public final i(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lxa1/c;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableFreeSpins$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableFreeSpins$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableFreeSpins$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableFreeSpins$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableFreeSpins$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableFreeSpins$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableFreeSpins$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableFreeSpins$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-object v0, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableFreeSpins$1;->L$0:Ljava/lang/Object;

    .line 39
    .line 40
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 41
    .line 42
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    goto :goto_1

    .line 46
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 47
    .line 48
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 49
    .line 50
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    throw p1

    .line 54
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->a:Lya1/a;

    .line 58
    .line 59
    iput-object p0, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableFreeSpins$1;->L$0:Ljava/lang/Object;

    .line 60
    .line 61
    iput v3, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getLocalAvailableFreeSpins$1;->label:I

    .line 62
    .line 63
    invoke-interface {p1, v0}, Lya1/a;->c(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    if-ne p1, v1, :cond_3

    .line 68
    .line 69
    return-object v1

    .line 70
    :cond_3
    move-object v0, p0

    .line 71
    :goto_1
    check-cast p1, Ljava/util/List;

    .line 72
    .line 73
    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->e(Ljava/util/List;)Ljava/util/List;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    return-object p1
.end method

.method public final j(JILorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8
    .param p4    # Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JI",
            "Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lxa1/b;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$setStatusBonus$2;

    .line 4
    .line 5
    const/4 v7, 0x0

    .line 6
    move-object v2, p0

    .line 7
    move-wide v3, p1

    .line 8
    move v5, p3

    .line 9
    move-object v6, p4

    .line 10
    invoke-direct/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$setStatusBonus$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;JILorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {v0, v1, p5}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    return-object p1
.end method
