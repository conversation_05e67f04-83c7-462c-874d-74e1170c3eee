.class final Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.core.presentation.OpenGameDelegate$launchGame$1$1"
    f = "OpenGameDelegate.kt"
    l = {
        0x5f,
        0x62,
        0x6a
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->r(Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $callOnError:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic $game:Lorg/xplatform/aggregator/api/model/Game;

.field final synthetic $subCategoryId:I

.field J$0:J

.field private synthetic L$0:Ljava/lang/Object;

.field Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "I",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    iput p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$subCategoryId:I

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$callOnError:Lkotlin/jvm/functions/Function1;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method

.method public static synthetic a(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/api/model/Game;IJLkotlin/jvm/functions/Function1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->c(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/api/model/Game;IJLkotlin/jvm/functions/Function1;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/api/model/Game;IJLkotlin/jvm/functions/Function1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->d(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/api/model/Game;IJLkotlin/jvm/functions/Function1;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    iget v3, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$subCategoryId:I

    iget-object v4, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$callOnError:Lkotlin/jvm/functions/Function1;

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 12

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    iget v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x3

    .line 8
    const/4 v3, 0x2

    .line 9
    const/4 v4, 0x1

    .line 10
    const/4 v5, 0x0

    .line 11
    if-eqz v0, :cond_3

    .line 12
    .line 13
    if-eq v0, v4, :cond_2

    .line 14
    .line 15
    if-eq v0, v3, :cond_1

    .line 16
    .line 17
    if-ne v0, v2, :cond_0

    .line 18
    .line 19
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->Z$0:Z

    .line 20
    .line 21
    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->J$0:J

    .line 22
    .line 23
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 24
    .line 25
    .line 26
    move-object v9, p0

    .line 27
    move-wide v5, v1

    .line 28
    goto/16 :goto_6

    .line 29
    .line 30
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 31
    .line 32
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 33
    .line 34
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 35
    .line 36
    .line 37
    throw p1

    .line 38
    :cond_1
    :try_start_0
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 39
    .line 40
    .line 41
    move-object v9, p0

    .line 42
    goto :goto_1

    .line 43
    :catchall_0
    move-exception v0

    .line 44
    move-object p1, v0

    .line 45
    move-object v9, p0

    .line 46
    goto :goto_3

    .line 47
    :cond_2
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->L$0:Ljava/lang/Object;

    .line 48
    .line 49
    check-cast v0, Lkotlinx/coroutines/N;

    .line 50
    .line 51
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 52
    .line 53
    .line 54
    goto :goto_0

    .line 55
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->L$0:Ljava/lang/Object;

    .line 59
    .line 60
    check-cast p1, Lkotlinx/coroutines/N;

    .line 61
    .line 62
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 63
    .line 64
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->f(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)LJT/a;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    iget-object v6, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 69
    .line 70
    invoke-virtual {v6}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 71
    .line 72
    .line 73
    move-result-wide v6

    .line 74
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->L$0:Ljava/lang/Object;

    .line 75
    .line 76
    iput v4, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->label:I

    .line 77
    .line 78
    invoke-interface {v0, v6, v7, p0}, LJT/a;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    if-ne p1, v1, :cond_4

    .line 83
    .line 84
    move-object v9, p0

    .line 85
    goto/16 :goto_5

    .line 86
    .line 87
    :cond_4
    :goto_0
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 88
    .line 89
    :try_start_1
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 90
    .line 91
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->k(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)Lek/d;

    .line 92
    .line 93
    .line 94
    move-result-object v6

    .line 95
    sget-object v7, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 96
    .line 97
    iput-object v5, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->L$0:Ljava/lang/Object;

    .line 98
    .line 99
    iput v3, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->label:I
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_2

    .line 100
    .line 101
    const/4 v8, 0x0

    .line 102
    const/4 v10, 0x2

    .line 103
    const/4 v11, 0x0

    .line 104
    move-object v9, p0

    .line 105
    :try_start_2
    invoke-static/range {v6 .. v11}, Lek/d$a;->a(Lek/d;Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceRefreshType;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 106
    .line 107
    .line 108
    move-result-object p1

    .line 109
    if-ne p1, v1, :cond_5

    .line 110
    .line 111
    goto :goto_5

    .line 112
    :cond_5
    :goto_1
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 113
    .line 114
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 115
    .line 116
    .line 117
    move-result-object p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 118
    goto :goto_4

    .line 119
    :catchall_1
    move-exception v0

    .line 120
    :goto_2
    move-object p1, v0

    .line 121
    goto :goto_3

    .line 122
    :catchall_2
    move-exception v0

    .line 123
    move-object v9, p0

    .line 124
    goto :goto_2

    .line 125
    :goto_3
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 126
    .line 127
    invoke-static {p1}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 128
    .line 129
    .line 130
    move-result-object p1

    .line 131
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 132
    .line 133
    .line 134
    move-result-object p1

    .line 135
    :goto_4
    invoke-static {p1}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 136
    .line 137
    .line 138
    move-result v0

    .line 139
    if-eqz v0, :cond_6

    .line 140
    .line 141
    move-object p1, v5

    .line 142
    :cond_6
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 143
    .line 144
    if-eqz p1, :cond_c

    .line 145
    .line 146
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 147
    .line 148
    .line 149
    move-result-wide v3

    .line 150
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 151
    .line 152
    .line 153
    move-result-object p1

    .line 154
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/configs/TypeAccount;->isBonus()Z

    .line 155
    .line 156
    .line 157
    move-result v0

    .line 158
    iget-object p1, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 159
    .line 160
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getNeedTransfer()Z

    .line 161
    .line 162
    .line 163
    move-result p1

    .line 164
    if-eqz p1, :cond_7

    .line 165
    .line 166
    if-eqz v0, :cond_7

    .line 167
    .line 168
    iget-object p1, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 169
    .line 170
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->h(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 171
    .line 172
    .line 173
    move-result-object p1

    .line 174
    sget-object v0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$d;->a:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$d;

    .line 175
    .line 176
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 177
    .line 178
    .line 179
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 180
    .line 181
    return-object p1

    .line 182
    :cond_7
    iget-object p1, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 183
    .line 184
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getNeedTransfer()Z

    .line 185
    .line 186
    .line 187
    move-result p1

    .line 188
    if-nez p1, :cond_a

    .line 189
    .line 190
    iget-object p1, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 191
    .line 192
    iput-object v5, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->L$0:Ljava/lang/Object;

    .line 193
    .line 194
    iput-wide v3, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->J$0:J

    .line 195
    .line 196
    iput-boolean v0, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->Z$0:Z

    .line 197
    .line 198
    iput v2, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->label:I

    .line 199
    .line 200
    invoke-static {p1, p0}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->e(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 201
    .line 202
    .line 203
    move-result-object p1

    .line 204
    if-ne p1, v1, :cond_8

    .line 205
    .line 206
    :goto_5
    return-object v1

    .line 207
    :cond_8
    move-wide v5, v3

    .line 208
    :goto_6
    check-cast p1, Ljava/lang/Boolean;

    .line 209
    .line 210
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 211
    .line 212
    .line 213
    move-result p1

    .line 214
    if-eqz p1, :cond_9

    .line 215
    .line 216
    iget-object v2, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 217
    .line 218
    iget-object v3, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 219
    .line 220
    iget v4, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$subCategoryId:I

    .line 221
    .line 222
    iget-object v7, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$callOnError:Lkotlin/jvm/functions/Function1;

    .line 223
    .line 224
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/B;

    .line 225
    .line 226
    invoke-direct/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/core/presentation/B;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/api/model/Game;IJLkotlin/jvm/functions/Function1;)V

    .line 227
    .line 228
    .line 229
    iget-object p1, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 230
    .line 231
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->h(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 232
    .line 233
    .line 234
    move-result-object p1

    .line 235
    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$c;

    .line 236
    .line 237
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$c;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 238
    .line 239
    .line 240
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 241
    .line 242
    .line 243
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 244
    .line 245
    return-object p1

    .line 246
    :cond_9
    move-wide v3, v5

    .line 247
    :cond_a
    iget-object p1, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 248
    .line 249
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getNoLoyalty()Z

    .line 250
    .line 251
    .line 252
    move-result p1

    .line 253
    if-eqz p1, :cond_b

    .line 254
    .line 255
    if-eqz v0, :cond_b

    .line 256
    .line 257
    iget-object p1, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 258
    .line 259
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->h(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 260
    .line 261
    .line 262
    move-result-object p1

    .line 263
    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$e;

    .line 264
    .line 265
    iget-object v1, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 266
    .line 267
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$e;-><init>(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 268
    .line 269
    .line 270
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 271
    .line 272
    .line 273
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 274
    .line 275
    return-object p1

    .line 276
    :cond_b
    iget-object p1, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 277
    .line 278
    iget-object v0, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 279
    .line 280
    iget v1, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->$subCategoryId:I

    .line 281
    .line 282
    invoke-static {p1, v0, v1, v3, v4}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->n(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/api/model/Game;IJ)V

    .line 283
    .line 284
    .line 285
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 286
    .line 287
    return-object p1

    .line 288
    :cond_c
    iget-object p1, v9, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 289
    .line 290
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->h(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 291
    .line 292
    .line 293
    move-result-object p1

    .line 294
    sget-object v0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$a;->a:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$a;

    .line 295
    .line 296
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 297
    .line 298
    .line 299
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 300
    .line 301
    return-object p1
.end method
