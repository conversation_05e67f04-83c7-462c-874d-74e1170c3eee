.class final synthetic Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$setResults$1$1;
.super Lkotlin/jvm/internal/FunctionReferenceImpl;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setResults(Ljava/util/List;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1001
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/FunctionReferenceImpl;",
        "Lkotlin/jvm/functions/Function1<",
        "Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;",
        "Landroid/graphics/drawable/Drawable;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>(Ljava/lang/Object;)V
    .locals 7

    const-string v5, "getTintedDrawable(Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;)Landroid/graphics/drawable/Drawable;"

    const/4 v6, 0x0

    const/4 v1, 0x1

    const-class v3, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;

    const-string v4, "getTintedDrawable"

    move-object v0, p0

    move-object v2, p1

    invoke-direct/range {v0 .. v6}, Lkotlin/jvm/internal/FunctionReferenceImpl;-><init>(ILjava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public final invoke(Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;)Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 1
    iget-object v0, p0, Lkotlin/jvm/internal/CallableReference;->receiver:Ljava/lang/Object;

    check-cast v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;

    invoke-static {v0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->y(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$setResults$1$1;->invoke(Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    return-object p1
.end method
