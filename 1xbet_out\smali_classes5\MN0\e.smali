.class public final LMN0/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0003\u001a+\u0010\u0007\u001a\u00020\u0006*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u001a\u0013\u0010\u000b\u001a\u00020\n*\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "LOD0/a;",
        "LNN0/i;",
        "gameStatus",
        "",
        "gameSportTitle",
        "vsString",
        "LNN0/n;",
        "b",
        "(LOD0/a;LNN0/i;Ljava/lang/String;Ljava/lang/String;)LNN0/n;",
        "LND0/k;",
        "",
        "a",
        "(LND0/k;)Z",
        "statistic_core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LND0/k;)Z
    .locals 6

    .line 1
    invoke-virtual {p0}, LND0/k;->f()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const-string p0, "/"

    .line 6
    .line 7
    filled-new-array {p0}, [Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    const/4 v4, 0x6

    .line 12
    const/4 v5, 0x0

    .line 13
    const/4 v2, 0x0

    .line 14
    const/4 v3, 0x0

    .line 15
    invoke-static/range {v0 .. v5}, Lkotlin/text/StringsKt;->split$default(Ljava/lang/CharSequence;[Ljava/lang/String;ZIILjava/lang/Object;)Ljava/util/List;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-interface {p0}, Ljava/util/List;->size()I

    .line 20
    .line 21
    .line 22
    move-result p0

    .line 23
    const/4 v0, 0x1

    .line 24
    if-le p0, v0, :cond_0

    .line 25
    .line 26
    return v0

    .line 27
    :cond_0
    const/4 p0, 0x0

    .line 28
    return p0
.end method

.method public static final b(LOD0/a;LNN0/i;Ljava/lang/String;Ljava/lang/String;)LNN0/n;
    .locals 23
    .param p0    # LOD0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LNN0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v11, p1

    .line 2
    .line 3
    const/4 v0, 0x3

    .line 4
    const/4 v2, 0x4

    .line 5
    const/4 v3, 0x1

    .line 6
    const/4 v4, 0x0

    .line 7
    const/4 v5, 0x3

    .line 8
    new-instance v0, LNN0/n;

    .line 9
    .line 10
    invoke-virtual/range {p0 .. p0}, LOD0/a;->s()LND0/k;

    .line 11
    .line 12
    .line 13
    move-result-object v6

    .line 14
    invoke-virtual {v6}, LND0/k;->f()Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v6

    .line 18
    invoke-static {v6}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    .line 19
    .line 20
    .line 21
    move-result v6

    .line 22
    if-eqz v6, :cond_0

    .line 23
    .line 24
    sget-object v6, Lorg/xbet/statistic/statistic_core/presentation/models/GameTypeModel;->ONE_TEAM:Lorg/xbet/statistic/statistic_core/presentation/models/GameTypeModel;

    .line 25
    .line 26
    goto :goto_0

    .line 27
    :cond_0
    sget-object v6, Lorg/xbet/statistic/statistic_core/presentation/models/GameTypeModel;->TWO_TEAM:Lorg/xbet/statistic/statistic_core/presentation/models/GameTypeModel;

    .line 28
    .line 29
    :goto_0
    invoke-virtual/range {p0 .. p0}, LOD0/a;->q()J

    .line 30
    .line 31
    .line 32
    move-result-wide v7

    .line 33
    invoke-virtual/range {p0 .. p0}, LOD0/a;->o()Z

    .line 34
    .line 35
    .line 36
    move-result v9

    .line 37
    const-wide/16 v12, 0xb6

    .line 38
    .line 39
    const-wide/16 v14, 0x9

    .line 40
    .line 41
    const-wide/16 v16, 0x38

    .line 42
    .line 43
    const-wide/16 v18, 0xbd

    .line 44
    .line 45
    if-nez v9, :cond_2

    .line 46
    .line 47
    instance-of v9, v11, LNN0/i$b;

    .line 48
    .line 49
    if-eqz v9, :cond_1

    .line 50
    .line 51
    goto :goto_1

    .line 52
    :cond_1
    const/16 v22, 0x2

    .line 53
    .line 54
    goto :goto_2

    .line 55
    :cond_2
    :goto_1
    invoke-static/range {v18 .. v19}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 56
    .line 57
    .line 58
    move-result-object v9

    .line 59
    invoke-static/range {v16 .. v17}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 60
    .line 61
    .line 62
    move-result-object v10

    .line 63
    invoke-static {v14, v15}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 64
    .line 65
    .line 66
    move-result-object v20

    .line 67
    invoke-static {v12, v13}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 68
    .line 69
    .line 70
    move-result-object v21

    .line 71
    const/16 v22, 0x2

    .line 72
    .line 73
    new-array v1, v2, [Ljava/lang/Long;

    .line 74
    .line 75
    aput-object v9, v1, v4

    .line 76
    .line 77
    aput-object v10, v1, v3

    .line 78
    .line 79
    aput-object v20, v1, v22

    .line 80
    .line 81
    aput-object v21, v1, v5

    .line 82
    .line 83
    invoke-static {v1}, Lkotlin/collections/Z;->i([Ljava/lang/Object;)Ljava/util/Set;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    invoke-virtual/range {p0 .. p0}, LOD0/a;->q()J

    .line 88
    .line 89
    .line 90
    move-result-wide v9

    .line 91
    invoke-static {v9, v10}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 92
    .line 93
    .line 94
    move-result-object v9

    .line 95
    invoke-interface {v1, v9}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    .line 96
    .line 97
    .line 98
    move-result v1

    .line 99
    if-nez v1, :cond_3

    .line 100
    .line 101
    invoke-virtual/range {p0 .. p0}, LOD0/a;->l()Ljava/lang/String;

    .line 102
    .line 103
    .line 104
    move-result-object v1

    .line 105
    goto :goto_3

    .line 106
    :cond_3
    :goto_2
    move-object/from16 v1, p3

    .line 107
    .line 108
    :goto_3
    invoke-virtual/range {p0 .. p0}, LOD0/a;->o()Z

    .line 109
    .line 110
    .line 111
    move-result v9

    .line 112
    if-nez v9, :cond_4

    .line 113
    .line 114
    instance-of v9, v11, LNN0/i$b;

    .line 115
    .line 116
    if-eqz v9, :cond_5

    .line 117
    .line 118
    :cond_4
    invoke-static/range {v18 .. v19}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 119
    .line 120
    .line 121
    move-result-object v9

    .line 122
    invoke-static/range {v16 .. v17}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 123
    .line 124
    .line 125
    move-result-object v10

    .line 126
    invoke-static {v14, v15}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 127
    .line 128
    .line 129
    move-result-object v14

    .line 130
    invoke-static {v12, v13}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 131
    .line 132
    .line 133
    move-result-object v12

    .line 134
    new-array v2, v2, [Ljava/lang/Long;

    .line 135
    .line 136
    aput-object v9, v2, v4

    .line 137
    .line 138
    aput-object v10, v2, v3

    .line 139
    .line 140
    aput-object v14, v2, v22

    .line 141
    .line 142
    aput-object v12, v2, v5

    .line 143
    .line 144
    invoke-static {v2}, Lkotlin/collections/Z;->i([Ljava/lang/Object;)Ljava/util/Set;

    .line 145
    .line 146
    .line 147
    move-result-object v2

    .line 148
    invoke-virtual/range {p0 .. p0}, LOD0/a;->q()J

    .line 149
    .line 150
    .line 151
    move-result-wide v9

    .line 152
    invoke-static {v9, v10}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 153
    .line 154
    .line 155
    move-result-object v5

    .line 156
    invoke-interface {v2, v5}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    .line 157
    .line 158
    .line 159
    move-result v2

    .line 160
    if-nez v2, :cond_5

    .line 161
    .line 162
    new-instance v2, Lorg/xbet/uikit_sport/score/a$a;

    .line 163
    .line 164
    invoke-virtual/range {p0 .. p0}, LOD0/a;->m()I

    .line 165
    .line 166
    .line 167
    move-result v5

    .line 168
    invoke-static {v5}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 169
    .line 170
    .line 171
    move-result-object v5

    .line 172
    invoke-virtual/range {p0 .. p0}, LOD0/a;->n()I

    .line 173
    .line 174
    .line 175
    move-result v9

    .line 176
    invoke-static {v9}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 177
    .line 178
    .line 179
    move-result-object v9

    .line 180
    invoke-direct {v2, v5, v9, v4, v4}, Lorg/xbet/uikit_sport/score/a$a;-><init>(Ljava/lang/String;Ljava/lang/String;ZZ)V

    .line 181
    .line 182
    .line 183
    goto :goto_4

    .line 184
    :cond_5
    sget-object v2, Lorg/xbet/uikit_sport/score/a$b;->b:Lorg/xbet/uikit_sport/score/a$b;

    .line 185
    .line 186
    :goto_4
    new-instance v12, LNN0/l;

    .line 187
    .line 188
    invoke-virtual/range {p0 .. p0}, LOD0/a;->r()LND0/k;

    .line 189
    .line 190
    .line 191
    move-result-object v4

    .line 192
    invoke-virtual {v4}, LND0/k;->c()Ljava/lang/String;

    .line 193
    .line 194
    .line 195
    move-result-object v13

    .line 196
    invoke-virtual/range {p0 .. p0}, LOD0/a;->r()LND0/k;

    .line 197
    .line 198
    .line 199
    move-result-object v4

    .line 200
    invoke-virtual {v4}, LND0/k;->f()Ljava/lang/String;

    .line 201
    .line 202
    .line 203
    move-result-object v14

    .line 204
    invoke-virtual/range {p0 .. p0}, LOD0/a;->r()LND0/k;

    .line 205
    .line 206
    .line 207
    move-result-object v4

    .line 208
    invoke-virtual {v4}, LND0/k;->d()Ljava/lang/String;

    .line 209
    .line 210
    .line 211
    move-result-object v15

    .line 212
    sget-object v4, LDX0/e;->a:LDX0/e;

    .line 213
    .line 214
    invoke-virtual/range {p0 .. p0}, LOD0/a;->r()LND0/k;

    .line 215
    .line 216
    .line 217
    move-result-object v5

    .line 218
    invoke-virtual {v5}, LND0/k;->e()Ljava/util/List;

    .line 219
    .line 220
    .line 221
    move-result-object v5

    .line 222
    invoke-static {v5}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 223
    .line 224
    .line 225
    move-result-object v5

    .line 226
    check-cast v5, LND0/j;

    .line 227
    .line 228
    const/4 v9, 0x0

    .line 229
    if-eqz v5, :cond_6

    .line 230
    .line 231
    invoke-virtual {v5}, LND0/j;->a()Ljava/lang/String;

    .line 232
    .line 233
    .line 234
    move-result-object v5

    .line 235
    goto :goto_5

    .line 236
    :cond_6
    move-object v5, v9

    .line 237
    :goto_5
    const-string v10, ""

    .line 238
    .line 239
    if-nez v5, :cond_7

    .line 240
    .line 241
    move-object v5, v10

    .line 242
    :cond_7
    invoke-virtual {v4, v5}, LDX0/e;->c(Ljava/lang/String;)Ljava/lang/String;

    .line 243
    .line 244
    .line 245
    move-result-object v16

    .line 246
    invoke-virtual/range {p0 .. p0}, LOD0/a;->r()LND0/k;

    .line 247
    .line 248
    .line 249
    move-result-object v5

    .line 250
    invoke-virtual {v5}, LND0/k;->e()Ljava/util/List;

    .line 251
    .line 252
    .line 253
    move-result-object v5

    .line 254
    invoke-static {v5, v3}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 255
    .line 256
    .line 257
    move-result-object v5

    .line 258
    check-cast v5, LND0/j;

    .line 259
    .line 260
    if-eqz v5, :cond_8

    .line 261
    .line 262
    invoke-virtual {v5}, LND0/j;->a()Ljava/lang/String;

    .line 263
    .line 264
    .line 265
    move-result-object v5

    .line 266
    goto :goto_6

    .line 267
    :cond_8
    move-object v5, v9

    .line 268
    :goto_6
    if-nez v5, :cond_9

    .line 269
    .line 270
    move-object v5, v10

    .line 271
    :cond_9
    invoke-virtual {v4, v5}, LDX0/e;->c(Ljava/lang/String;)Ljava/lang/String;

    .line 272
    .line 273
    .line 274
    move-result-object v17

    .line 275
    invoke-virtual/range {p0 .. p0}, LOD0/a;->r()LND0/k;

    .line 276
    .line 277
    .line 278
    move-result-object v5

    .line 279
    invoke-static {v5}, LMN0/e;->a(LND0/k;)Z

    .line 280
    .line 281
    .line 282
    move-result v18

    .line 283
    invoke-direct/range {v12 .. v18}, LNN0/l;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    .line 284
    .line 285
    .line 286
    new-instance v13, LNN0/l;

    .line 287
    .line 288
    invoke-virtual/range {p0 .. p0}, LOD0/a;->s()LND0/k;

    .line 289
    .line 290
    .line 291
    move-result-object v5

    .line 292
    invoke-virtual {v5}, LND0/k;->c()Ljava/lang/String;

    .line 293
    .line 294
    .line 295
    move-result-object v14

    .line 296
    invoke-virtual/range {p0 .. p0}, LOD0/a;->s()LND0/k;

    .line 297
    .line 298
    .line 299
    move-result-object v5

    .line 300
    invoke-virtual {v5}, LND0/k;->f()Ljava/lang/String;

    .line 301
    .line 302
    .line 303
    move-result-object v15

    .line 304
    invoke-virtual/range {p0 .. p0}, LOD0/a;->s()LND0/k;

    .line 305
    .line 306
    .line 307
    move-result-object v5

    .line 308
    invoke-virtual {v5}, LND0/k;->d()Ljava/lang/String;

    .line 309
    .line 310
    .line 311
    move-result-object v16

    .line 312
    invoke-virtual/range {p0 .. p0}, LOD0/a;->s()LND0/k;

    .line 313
    .line 314
    .line 315
    move-result-object v5

    .line 316
    invoke-virtual {v5}, LND0/k;->e()Ljava/util/List;

    .line 317
    .line 318
    .line 319
    move-result-object v5

    .line 320
    invoke-static {v5}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 321
    .line 322
    .line 323
    move-result-object v5

    .line 324
    check-cast v5, LND0/j;

    .line 325
    .line 326
    if-eqz v5, :cond_a

    .line 327
    .line 328
    invoke-virtual {v5}, LND0/j;->a()Ljava/lang/String;

    .line 329
    .line 330
    .line 331
    move-result-object v5

    .line 332
    goto :goto_7

    .line 333
    :cond_a
    move-object v5, v9

    .line 334
    :goto_7
    if-nez v5, :cond_b

    .line 335
    .line 336
    move-object v5, v10

    .line 337
    :cond_b
    invoke-virtual {v4, v5}, LDX0/e;->c(Ljava/lang/String;)Ljava/lang/String;

    .line 338
    .line 339
    .line 340
    move-result-object v17

    .line 341
    invoke-virtual/range {p0 .. p0}, LOD0/a;->s()LND0/k;

    .line 342
    .line 343
    .line 344
    move-result-object v5

    .line 345
    invoke-virtual {v5}, LND0/k;->e()Ljava/util/List;

    .line 346
    .line 347
    .line 348
    move-result-object v5

    .line 349
    invoke-static {v5, v3}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 350
    .line 351
    .line 352
    move-result-object v3

    .line 353
    check-cast v3, LND0/j;

    .line 354
    .line 355
    if-eqz v3, :cond_c

    .line 356
    .line 357
    invoke-virtual {v3}, LND0/j;->a()Ljava/lang/String;

    .line 358
    .line 359
    .line 360
    move-result-object v9

    .line 361
    :cond_c
    if-nez v9, :cond_d

    .line 362
    .line 363
    goto :goto_8

    .line 364
    :cond_d
    move-object v10, v9

    .line 365
    :goto_8
    invoke-virtual {v4, v10}, LDX0/e;->c(Ljava/lang/String;)Ljava/lang/String;

    .line 366
    .line 367
    .line 368
    move-result-object v18

    .line 369
    invoke-virtual/range {p0 .. p0}, LOD0/a;->s()LND0/k;

    .line 370
    .line 371
    .line 372
    move-result-object v3

    .line 373
    invoke-static {v3}, LMN0/e;->a(LND0/k;)Z

    .line 374
    .line 375
    .line 376
    move-result v19

    .line 377
    invoke-direct/range {v13 .. v19}, LNN0/l;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    .line 378
    .line 379
    .line 380
    invoke-virtual/range {p0 .. p0}, LOD0/a;->i()I

    .line 381
    .line 382
    .line 383
    move-result v9

    .line 384
    invoke-virtual/range {p0 .. p0}, LOD0/a;->j()I

    .line 385
    .line 386
    .line 387
    move-result v10

    .line 388
    move-object v5, v1

    .line 389
    move-object v1, v6

    .line 390
    move-object v6, v2

    .line 391
    move-wide v2, v7

    .line 392
    move-object v7, v12

    .line 393
    invoke-virtual/range {p0 .. p0}, LOD0/a;->t()Ljava/lang/String;

    .line 394
    .line 395
    .line 396
    move-result-object v12

    .line 397
    invoke-virtual/range {p0 .. p0}, LOD0/a;->h()J

    .line 398
    .line 399
    .line 400
    move-result-wide v14

    .line 401
    move-object/from16 v4, p2

    .line 402
    .line 403
    move-object v8, v13

    .line 404
    move-wide v13, v14

    .line 405
    invoke-direct/range {v0 .. v14}, LNN0/n;-><init>(Lorg/xbet/statistic/statistic_core/presentation/models/GameTypeModel;JLjava/lang/String;Ljava/lang/String;Lorg/xbet/uikit_sport/score/a;LNN0/l;LNN0/l;IILNN0/i;Ljava/lang/String;J)V

    .line 406
    .line 407
    .line 408
    return-object v0
.end method
