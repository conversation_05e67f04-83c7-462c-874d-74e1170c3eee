.class public final Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "e"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0006\u0010\u0007\u001a\u0004\u0008\u0006\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$e;",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "<init>",
        "(Lorg/xplatform/aggregator/api/model/Game;)V",
        "a",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "()Lorg/xplatform/aggregator/api/model/Game;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/api/model/Game;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$e;->a:Lorg/xplatform/aggregator/api/model/Game;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a()Lorg/xplatform/aggregator/api/model/Game;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$e;->a:Lorg/xplatform/aggregator/api/model/Game;

    .line 2
    .line 3
    return-object v0
.end method
