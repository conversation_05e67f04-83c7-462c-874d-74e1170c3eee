.class public final Ljb1/B;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000F\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u001a!\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u001a\u0017\u0010\n\u001a\u00020\t2\u0006\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u000b\u001a7\u0010\u0014\u001a\u00020\t2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015\u001a\u0017\u0010\u0016\u001a\u00020\u00122\u0006\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017\u001a\'\u0010\u0019\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0018\u001a\u00020\u00122\u0006\u0010\u0011\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001a\u001a7\u0010\u001f\u001a\u00020\u001e2\u0006\u0010\u001b\u001a\u00020\u00122\u0006\u0010\u001c\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u001d\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 \u001a\u0017\u0010!\u001a\u00020\u001e2\u0006\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008!\u0010\"\u00a8\u0006#"
    }
    d2 = {
        "Li81/a;",
        "LHX0/e;",
        "resourceManager",
        "",
        "Lkb1/D;",
        "g",
        "(Li81/a;LHX0/e;)Ljava/util/List;",
        "Ln81/d;",
        "stage",
        "",
        "d",
        "(Ln81/d;)Ljava/lang/String;",
        "",
        "meParticipating",
        "",
        "crmParticipantCurrentStage",
        "Lo81/b;",
        "stageItemModel",
        "",
        "currentPoints",
        "c",
        "(ZJLHX0/e;Lo81/b;I)Ljava/lang/String;",
        "b",
        "(Ln81/d;)I",
        "prevStageNecessaryPoints",
        "a",
        "(IILo81/b;)I",
        "currentStageIndex",
        "currentStage",
        "participating",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;",
        "e",
        "(IIILo81/b;Z)Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;",
        "f",
        "(Ln81/d;)Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(IILo81/b;)I
    .locals 2

    .line 1
    invoke-virtual {p2}, Lo81/b;->b()I

    .line 2
    .line 3
    .line 4
    move-result p2

    .line 5
    sub-int/2addr p2, p1

    .line 6
    sub-int/2addr p0, p1

    .line 7
    int-to-double p0, p0

    .line 8
    int-to-double v0, p2

    .line 9
    div-double/2addr p0, v0

    .line 10
    const/16 p2, 0x64

    .line 11
    .line 12
    int-to-double v0, p2

    .line 13
    mul-double p0, p0, v0

    .line 14
    .line 15
    double-to-int p0, p0

    .line 16
    return p0
.end method

.method public static final b(Ln81/d;)I
    .locals 6

    .line 1
    new-instance v0, Ljava/util/Date;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/Date;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Ljava/util/Date;->getTime()J

    .line 7
    .line 8
    .line 9
    move-result-wide v0

    .line 10
    invoke-virtual {p0}, Ln81/d;->c()Ljava/util/Date;

    .line 11
    .line 12
    .line 13
    move-result-object v2

    .line 14
    invoke-virtual {v2}, Ljava/util/Date;->getTime()J

    .line 15
    .line 16
    .line 17
    move-result-wide v2

    .line 18
    invoke-virtual {p0}, Ln81/d;->d()Ljava/util/Date;

    .line 19
    .line 20
    .line 21
    move-result-object p0

    .line 22
    invoke-virtual {p0}, Ljava/util/Date;->getTime()J

    .line 23
    .line 24
    .line 25
    move-result-wide v4

    .line 26
    sget-object p0, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 27
    .line 28
    sub-long/2addr v2, v4

    .line 29
    invoke-virtual {p0, v2, v3}, Ljava/util/concurrent/TimeUnit;->toSeconds(J)J

    .line 30
    .line 31
    .line 32
    move-result-wide v2

    .line 33
    sub-long/2addr v0, v4

    .line 34
    invoke-virtual {p0, v0, v1}, Ljava/util/concurrent/TimeUnit;->toSeconds(J)J

    .line 35
    .line 36
    .line 37
    move-result-wide v0

    .line 38
    long-to-double v0, v0

    .line 39
    long-to-double v2, v2

    .line 40
    div-double/2addr v0, v2

    .line 41
    const/16 p0, 0x64

    .line 42
    .line 43
    int-to-double v2, p0

    .line 44
    mul-double v0, v0, v2

    .line 45
    .line 46
    double-to-int p0, v0

    .line 47
    return p0
.end method

.method public static final c(ZJLHX0/e;Lo81/b;I)Ljava/lang/String;
    .locals 5

    .line 1
    const/4 v0, 0x1

    .line 2
    const/4 v1, 0x0

    .line 3
    invoke-virtual {p4}, Lo81/b;->b()I

    .line 4
    .line 5
    .line 6
    move-result v2

    .line 7
    if-le v2, p5, :cond_0

    .line 8
    .line 9
    sub-int/2addr v2, p5

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    const/4 v2, 0x0

    .line 12
    :goto_0
    if-eqz p0, :cond_1

    .line 13
    .line 14
    invoke-virtual {p4}, Lo81/b;->c()J

    .line 15
    .line 16
    .line 17
    move-result-wide v3

    .line 18
    cmp-long p0, p1, v3

    .line 19
    .line 20
    if-nez p0, :cond_1

    .line 21
    .line 22
    sget p0, Lpb/k;->points_count:I

    .line 23
    .line 24
    invoke-virtual {p4}, Lo81/b;->b()I

    .line 25
    .line 26
    .line 27
    move-result p1

    .line 28
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    new-array p2, v0, [Ljava/lang/Object;

    .line 33
    .line 34
    aput-object p1, p2, v1

    .line 35
    .line 36
    invoke-interface {p3, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    sget p1, Lpb/k;->tournament_stage_points_left_without_explain:I

    .line 41
    .line 42
    invoke-static {v2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object p2

    .line 46
    new-array p4, v0, [Ljava/lang/Object;

    .line 47
    .line 48
    aput-object p2, p4, v1

    .line 49
    .line 50
    invoke-interface {p3, p1, p4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    new-instance p2, Ljava/lang/StringBuilder;

    .line 55
    .line 56
    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    .line 57
    .line 58
    .line 59
    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 60
    .line 61
    .line 62
    const-string p0, " "

    .line 63
    .line 64
    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 65
    .line 66
    .line 67
    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 68
    .line 69
    .line 70
    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object p0

    .line 74
    return-object p0

    .line 75
    :cond_1
    sget p0, Lpb/k;->points_count:I

    .line 76
    .line 77
    invoke-virtual {p4}, Lo81/b;->b()I

    .line 78
    .line 79
    .line 80
    move-result p1

    .line 81
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    new-array p2, v0, [Ljava/lang/Object;

    .line 86
    .line 87
    aput-object p1, p2, v1

    .line 88
    .line 89
    invoke-interface {p3, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 90
    .line 91
    .line 92
    move-result-object p0

    .line 93
    return-object p0
.end method

.method public static final d(Ln81/d;)Ljava/lang/String;
    .locals 7

    .line 1
    sget-object v0, Ll8/b;->a:Ll8/b;

    .line 2
    .line 3
    invoke-virtual {p0}, Ln81/d;->d()Ljava/util/Date;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    const/4 v4, 0x4

    .line 8
    const/4 v5, 0x0

    .line 9
    const-string v2, "d MMMM"

    .line 10
    .line 11
    const/4 v3, 0x0

    .line 12
    invoke-static/range {v0 .. v5}, Ll8/b;->h(Ll8/b;Ljava/util/Date;Ljava/lang/String;Ljava/util/Locale;ILjava/lang/Object;)Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v6

    .line 16
    invoke-virtual {p0}, Ln81/d;->c()Ljava/util/Date;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    const-string v2, "d MMMM"

    .line 21
    .line 22
    invoke-static/range {v0 .. v5}, Ll8/b;->h(Ll8/b;Ljava/util/Date;Ljava/lang/String;Ljava/util/Locale;ILjava/lang/Object;)Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object p0

    .line 26
    new-instance v0, Ljava/lang/StringBuilder;

    .line 27
    .line 28
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 29
    .line 30
    .line 31
    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 32
    .line 33
    .line 34
    const-string v1, " - "

    .line 35
    .line 36
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 37
    .line 38
    .line 39
    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 40
    .line 41
    .line 42
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object p0

    .line 46
    return-object p0
.end method

.method public static final e(IIILo81/b;Z)Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;
    .locals 0

    .line 1
    invoke-virtual {p3}, Lo81/b;->b()I

    .line 2
    .line 3
    .line 4
    move-result p3

    .line 5
    if-lt p2, p3, :cond_0

    .line 6
    .line 7
    const/4 p2, 0x1

    .line 8
    goto :goto_0

    .line 9
    :cond_0
    const/4 p2, 0x0

    .line 10
    :goto_0
    if-ne p1, p0, :cond_2

    .line 11
    .line 12
    if-eqz p4, :cond_2

    .line 13
    .line 14
    if-nez p2, :cond_1

    .line 15
    .line 16
    sget-object p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;->PRESENT:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;

    .line 17
    .line 18
    return-object p0

    .line 19
    :cond_1
    sget-object p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;->PASSED:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;

    .line 20
    .line 21
    return-object p0

    .line 22
    :cond_2
    if-le p1, p0, :cond_3

    .line 23
    .line 24
    sget-object p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;->PASSED:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;

    .line 25
    .line 26
    return-object p0

    .line 27
    :cond_3
    sget-object p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;->FUTURE:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;

    .line 28
    .line 29
    return-object p0
.end method

.method public static final f(Ln81/d;)Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;
    .locals 3

    .line 1
    new-instance v0, Ljava/util/Date;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/Date;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Ln81/d;->d()Ljava/util/Date;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {p0}, Ln81/d;->c()Ljava/util/Date;

    .line 11
    .line 12
    .line 13
    move-result-object p0

    .line 14
    invoke-virtual {v0, v1}, Ljava/util/Date;->before(Ljava/util/Date;)Z

    .line 15
    .line 16
    .line 17
    move-result v2

    .line 18
    if-eqz v2, :cond_0

    .line 19
    .line 20
    sget-object p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;->FUTURE:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;

    .line 21
    .line 22
    return-object p0

    .line 23
    :cond_0
    sget-object v2, Ll8/b;->a:Ll8/b;

    .line 24
    .line 25
    invoke-virtual {v2, v0, v1, p0}, Ll8/b;->f(Ljava/util/Date;Ljava/util/Date;Ljava/util/Date;)Z

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    if-eqz v1, :cond_1

    .line 30
    .line 31
    sget-object p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;->PRESENT:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;

    .line 32
    .line 33
    return-object p0

    .line 34
    :cond_1
    invoke-virtual {p0, v0}, Ljava/util/Date;->before(Ljava/util/Date;)Z

    .line 35
    .line 36
    .line 37
    move-result p0

    .line 38
    if-eqz p0, :cond_2

    .line 39
    .line 40
    sget-object p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;->PASSED:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;

    .line 41
    .line 42
    return-object p0

    .line 43
    :cond_2
    sget-object p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;->PASSED:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;

    .line 44
    .line 45
    return-object p0
.end method

.method public static final g(Li81/a;LHX0/e;)Ljava/util/List;
    .locals 15
    .param p0    # Li81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "LHX0/e;",
            ")",
            "Ljava/util/List<",
            "Lkb1/D;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;->PROVIDER:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 6
    .line 7
    const/16 v2, 0xa

    .line 8
    .line 9
    const/4 v3, 0x0

    .line 10
    if-ne v0, v1, :cond_2

    .line 11
    .line 12
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-virtual {v0}, Ln81/b;->c()Ljava/util/List;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    new-instance v1, Ljava/util/ArrayList;

    .line 21
    .line 22
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 27
    .line 28
    .line 29
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 34
    .line 35
    .line 36
    move-result v2

    .line 37
    if-eqz v2, :cond_1

    .line 38
    .line 39
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    add-int/lit8 v11, v3, 0x1

    .line 44
    .line 45
    if-gez v3, :cond_0

    .line 46
    .line 47
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 48
    .line 49
    .line 50
    :cond_0
    check-cast v2, Ln81/d;

    .line 51
    .line 52
    invoke-static {v2}, Ljb1/B;->d(Ln81/d;)Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v7

    .line 56
    invoke-static {v2}, Ljb1/B;->b(Ln81/d;)I

    .line 57
    .line 58
    .line 59
    move-result v10

    .line 60
    invoke-virtual {v2}, Ln81/d;->b()J

    .line 61
    .line 62
    .line 63
    move-result-wide v5

    .line 64
    invoke-virtual {p0}, Li81/a;->p()Z

    .line 65
    .line 66
    .line 67
    move-result v8

    .line 68
    invoke-static {v2}, Ljb1/B;->f(Ln81/d;)Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;

    .line 69
    .line 70
    .line 71
    move-result-object v9

    .line 72
    new-instance v4, Lkb1/D;

    .line 73
    .line 74
    invoke-direct/range {v4 .. v11}, Lkb1/D;-><init>(JLjava/lang/String;ZLorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;II)V

    .line 75
    .line 76
    .line 77
    invoke-interface {v1, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 78
    .line 79
    .line 80
    move v3, v11

    .line 81
    goto :goto_0

    .line 82
    :cond_1
    return-object v1

    .line 83
    :cond_2
    invoke-virtual {p0}, Li81/a;->i()Lo81/a;

    .line 84
    .line 85
    .line 86
    move-result-object v0

    .line 87
    invoke-virtual {v0}, Lo81/a;->a()Ljava/util/List;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    new-instance v1, Ljava/util/ArrayList;

    .line 92
    .line 93
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 94
    .line 95
    .line 96
    move-result v2

    .line 97
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 98
    .line 99
    .line 100
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 101
    .line 102
    .line 103
    move-result-object v0

    .line 104
    const/4 v2, 0x0

    .line 105
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 106
    .line 107
    .line 108
    move-result v4

    .line 109
    if-eqz v4, :cond_e

    .line 110
    .line 111
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 112
    .line 113
    .line 114
    move-result-object v4

    .line 115
    add-int/lit8 v12, v2, 0x1

    .line 116
    .line 117
    if-gez v2, :cond_3

    .line 118
    .line 119
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 120
    .line 121
    .line 122
    :cond_3
    move-object v9, v4

    .line 123
    check-cast v9, Lo81/b;

    .line 124
    .line 125
    invoke-virtual {p0}, Li81/a;->p()Z

    .line 126
    .line 127
    .line 128
    move-result v5

    .line 129
    invoke-virtual {p0}, Li81/a;->l()J

    .line 130
    .line 131
    .line 132
    move-result-wide v6

    .line 133
    invoke-virtual {p0}, Li81/a;->f()Lm81/a;

    .line 134
    .line 135
    .line 136
    move-result-object v4

    .line 137
    invoke-virtual {v4}, Lm81/a;->a()Ljava/util/List;

    .line 138
    .line 139
    .line 140
    move-result-object v4

    .line 141
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 142
    .line 143
    .line 144
    move-result-object v4

    .line 145
    :cond_4
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 146
    .line 147
    .line 148
    move-result v8

    .line 149
    const/4 v11, 0x0

    .line 150
    if-eqz v8, :cond_5

    .line 151
    .line 152
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 153
    .line 154
    .line 155
    move-result-object v8

    .line 156
    move-object v10, v8

    .line 157
    check-cast v10, Lm81/b;

    .line 158
    .line 159
    invoke-virtual {v10}, Lm81/b;->b()Z

    .line 160
    .line 161
    .line 162
    move-result v10

    .line 163
    if-eqz v10, :cond_4

    .line 164
    .line 165
    goto :goto_2

    .line 166
    :cond_5
    move-object v8, v11

    .line 167
    :goto_2
    check-cast v8, Lm81/b;

    .line 168
    .line 169
    if-eqz v8, :cond_6

    .line 170
    .line 171
    invoke-virtual {v8}, Lm81/b;->d()I

    .line 172
    .line 173
    .line 174
    move-result v4

    .line 175
    move v10, v4

    .line 176
    :goto_3
    move-object/from16 v8, p1

    .line 177
    .line 178
    goto :goto_4

    .line 179
    :cond_6
    const/4 v10, 0x0

    .line 180
    goto :goto_3

    .line 181
    :goto_4
    invoke-static/range {v5 .. v10}, Ljb1/B;->c(ZJLHX0/e;Lo81/b;I)Ljava/lang/String;

    .line 182
    .line 183
    .line 184
    move-result-object v4

    .line 185
    invoke-virtual {p0}, Li81/a;->f()Lm81/a;

    .line 186
    .line 187
    .line 188
    move-result-object v5

    .line 189
    invoke-virtual {v5}, Lm81/a;->a()Ljava/util/List;

    .line 190
    .line 191
    .line 192
    move-result-object v5

    .line 193
    invoke-interface {v5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 194
    .line 195
    .line 196
    move-result-object v5

    .line 197
    :cond_7
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    .line 198
    .line 199
    .line 200
    move-result v6

    .line 201
    if-eqz v6, :cond_8

    .line 202
    .line 203
    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 204
    .line 205
    .line 206
    move-result-object v6

    .line 207
    move-object v7, v6

    .line 208
    check-cast v7, Lm81/b;

    .line 209
    .line 210
    invoke-virtual {v7}, Lm81/b;->b()Z

    .line 211
    .line 212
    .line 213
    move-result v7

    .line 214
    if-eqz v7, :cond_7

    .line 215
    .line 216
    goto :goto_5

    .line 217
    :cond_8
    move-object v6, v11

    .line 218
    :goto_5
    check-cast v6, Lm81/b;

    .line 219
    .line 220
    if-eqz v6, :cond_9

    .line 221
    .line 222
    invoke-virtual {v6}, Lm81/b;->d()I

    .line 223
    .line 224
    .line 225
    move-result v5

    .line 226
    goto :goto_6

    .line 227
    :cond_9
    const/4 v5, 0x0

    .line 228
    :goto_6
    invoke-virtual {p0}, Li81/a;->i()Lo81/a;

    .line 229
    .line 230
    .line 231
    move-result-object v6

    .line 232
    invoke-virtual {v6}, Lo81/a;->a()Ljava/util/List;

    .line 233
    .line 234
    .line 235
    move-result-object v6

    .line 236
    add-int/lit8 v2, v2, -0x1

    .line 237
    .line 238
    invoke-static {v6, v2}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 239
    .line 240
    .line 241
    move-result-object v2

    .line 242
    check-cast v2, Lo81/b;

    .line 243
    .line 244
    if-eqz v2, :cond_a

    .line 245
    .line 246
    invoke-virtual {v2}, Lo81/b;->b()I

    .line 247
    .line 248
    .line 249
    move-result v2

    .line 250
    goto :goto_7

    .line 251
    :cond_a
    const/4 v2, 0x0

    .line 252
    :goto_7
    invoke-static {v5, v2, v9}, Ljb1/B;->a(IILo81/b;)I

    .line 253
    .line 254
    .line 255
    move-result v2

    .line 256
    invoke-virtual {v9}, Lo81/b;->c()J

    .line 257
    .line 258
    .line 259
    move-result-wide v6

    .line 260
    invoke-virtual {p0}, Li81/a;->p()Z

    .line 261
    .line 262
    .line 263
    move-result v5

    .line 264
    invoke-virtual {p0}, Li81/a;->l()J

    .line 265
    .line 266
    .line 267
    move-result-wide v13

    .line 268
    long-to-int v8, v13

    .line 269
    invoke-virtual {p0}, Li81/a;->f()Lm81/a;

    .line 270
    .line 271
    .line 272
    move-result-object v10

    .line 273
    invoke-virtual {v10}, Lm81/a;->a()Ljava/util/List;

    .line 274
    .line 275
    .line 276
    move-result-object v10

    .line 277
    invoke-interface {v10}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 278
    .line 279
    .line 280
    move-result-object v10

    .line 281
    :cond_b
    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    .line 282
    .line 283
    .line 284
    move-result v13

    .line 285
    if-eqz v13, :cond_c

    .line 286
    .line 287
    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 288
    .line 289
    .line 290
    move-result-object v13

    .line 291
    move-object v14, v13

    .line 292
    check-cast v14, Lm81/b;

    .line 293
    .line 294
    invoke-virtual {v14}, Lm81/b;->b()Z

    .line 295
    .line 296
    .line 297
    move-result v14

    .line 298
    if-eqz v14, :cond_b

    .line 299
    .line 300
    move-object v11, v13

    .line 301
    :cond_c
    check-cast v11, Lm81/b;

    .line 302
    .line 303
    if-eqz v11, :cond_d

    .line 304
    .line 305
    invoke-virtual {v11}, Lm81/b;->d()I

    .line 306
    .line 307
    .line 308
    move-result v10

    .line 309
    goto :goto_8

    .line 310
    :cond_d
    const/4 v10, 0x0

    .line 311
    :goto_8
    invoke-virtual {p0}, Li81/a;->p()Z

    .line 312
    .line 313
    .line 314
    move-result v11

    .line 315
    invoke-static {v12, v8, v10, v9, v11}, Ljb1/B;->e(IIILo81/b;Z)Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;

    .line 316
    .line 317
    .line 318
    move-result-object v10

    .line 319
    move v9, v5

    .line 320
    new-instance v5, Lkb1/D;

    .line 321
    .line 322
    move v11, v2

    .line 323
    move-object v8, v4

    .line 324
    invoke-direct/range {v5 .. v12}, Lkb1/D;-><init>(JLjava/lang/String;ZLorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentStageType;II)V

    .line 325
    .line 326
    .line 327
    invoke-interface {v1, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 328
    .line 329
    .line 330
    move v2, v12

    .line 331
    goto/16 :goto_1

    .line 332
    .line 333
    :cond_e
    return-object v1
.end method
