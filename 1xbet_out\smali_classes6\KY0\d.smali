.class public final synthetic LKY0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:LKY0/g;

.field public final synthetic b:I

.field public final synthetic c:LKY0/g$b;

.field public final synthetic d:Ljava/util/List;


# direct methods
.method public synthetic constructor <init>(LKY0/g;ILKY0/g$b;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LKY0/d;->a:LKY0/g;

    iput p2, p0, LKY0/d;->b:I

    iput-object p3, p0, LKY0/d;->c:LKY0/g$b;

    iput-object p4, p0, LKY0/d;->d:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 4

    .line 1
    iget-object v0, p0, LKY0/d;->a:LKY0/g;

    iget v1, p0, LKY0/d;->b:I

    iget-object v2, p0, LKY0/d;->c:LKY0/g$b;

    iget-object v3, p0, LKY0/d;->d:Ljava/util/List;

    invoke-static {v0, v1, v2, v3}, LKY0/g;->g(LKY0/g;ILKY0/g$b;Ljava/util/List;)V

    return-void
.end method
