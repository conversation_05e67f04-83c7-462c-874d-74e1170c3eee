.class public final Lorg/xbet/themesettings/impl/presentation/timepicker/r;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/themesettings/impl/presentation/timepicker/r$a;,
        Lorg/xbet/themesettings/impl/presentation/timepicker/r$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0086\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u000f\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0008\u0018\n\u0002\u0018\u0002\n\u0002\u0008\u000f\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u0000 g2\u00020\u0001:\u0001hBI\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0013\u0010\u0016\u001a\u0008\u0012\u0004\u0012\u00020\u00150\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0013\u0010\u0018\u001a\u0008\u0012\u0004\u0012\u00020\u00150\u0014\u00a2\u0006\u0004\u0008\u0018\u0010\u0017J\u0013\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00150\u0014\u00a2\u0006\u0004\u0008\u0019\u0010\u0017J\u0013\u0010\u001a\u001a\u0008\u0012\u0004\u0012\u00020\u00100\u0014\u00a2\u0006\u0004\u0008\u001a\u0010\u0017J\u0013\u0010\u001c\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u0014\u00a2\u0006\u0004\u0008\u001c\u0010\u0017J\u0013\u0010\u001d\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u0014\u00a2\u0006\u0004\u0008\u001d\u0010\u0017J\u0013\u0010\u001e\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u0014\u00a2\u0006\u0004\u0008\u001e\u0010\u0017J\u0013\u0010!\u001a\u0008\u0012\u0004\u0012\u00020 0\u001f\u00a2\u0006\u0004\u0008!\u0010\"J\u0015\u0010%\u001a\u00020$2\u0006\u0010#\u001a\u00020\u0010\u00a2\u0006\u0004\u0008%\u0010&J\u0015\u0010\'\u001a\u00020$2\u0006\u0010#\u001a\u00020\u0010\u00a2\u0006\u0004\u0008\'\u0010&J\u0015\u0010)\u001a\u00020$2\u0006\u0010(\u001a\u00020\u0015\u00a2\u0006\u0004\u0008)\u0010*J\u0015\u0010,\u001a\u00020$2\u0006\u0010+\u001a\u00020\u0015\u00a2\u0006\u0004\u0008,\u0010*J\u0015\u0010.\u001a\u00020$2\u0006\u0010-\u001a\u00020\u0015\u00a2\u0006\u0004\u0008.\u0010*J\u0015\u0010/\u001a\u00020$2\u0006\u0010#\u001a\u00020\u0010\u00a2\u0006\u0004\u0008/\u0010&J\u0017\u00100\u001a\u00020$2\u0006\u0010#\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u00080\u0010&J\u000f\u00101\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u00081\u00102J\u0017\u00103\u001a\u00020$2\u0006\u0010#\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u00083\u0010&J\u0013\u00106\u001a\u000205*\u000204H\u0002\u00a2\u0006\u0004\u00086\u00107J\u001b\u00109\u001a\u0004\u0018\u00010\u001b*\u0008\u0012\u0004\u0012\u00020\u001508H\u0002\u00a2\u0006\u0004\u00089\u0010:J\u0013\u0010;\u001a\u00020\u0015*\u000204H\u0002\u00a2\u0006\u0004\u0008;\u0010<J\u0013\u0010=\u001a\u000204*\u00020\u0015H\u0002\u00a2\u0006\u0004\u0008=\u0010>J\u0013\u0010?\u001a\u00020$*\u00020 H\u0002\u00a2\u0006\u0004\u0008?\u0010@R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008A\u0010BR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008C\u0010DR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008E\u0010FR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010HR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008I\u0010JR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008K\u0010LR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008M\u0010NR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008O\u0010PR\u001a\u0010T\u001a\u0008\u0012\u0004\u0012\u00020\u001b0Q8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008R\u0010SR\u001a\u0010V\u001a\u0008\u0012\u0004\u0012\u00020\u001b0Q8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008U\u0010SR\u001a\u0010X\u001a\u0008\u0012\u0004\u0012\u00020\u001b0Q8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008W\u0010SR\u001a\u0010Z\u001a\u0008\u0012\u0004\u0012\u00020\u00150Q8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Y\u0010SR\u001a\u0010\\\u001a\u0008\u0012\u0004\u0012\u00020\u00150Q8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008[\u0010SR\u001a\u0010^\u001a\u0008\u0012\u0004\u0012\u00020\u00150Q8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008]\u0010SR\u001a\u0010`\u001a\u0008\u0012\u0004\u0012\u00020\u00100Q8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008_\u0010SR\u001a\u0010d\u001a\u0008\u0012\u0004\u0012\u00020 0a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008b\u0010cR\u0016\u0010f\u001a\u00020\u00108\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008e\u0010P\u00a8\u0006i"
    }
    d2 = {
        "Lorg/xbet/themesettings/impl/presentation/timepicker/r;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "LmT0/c;",
        "updateThemeWorkersScenario",
        "LgT0/a;",
        "getTimeModelScenario",
        "LhT0/a;",
        "convertHourTo24FormatUseCase",
        "Li8/m;",
        "getThemeUseCase",
        "LhT0/g;",
        "getTimeTableTimeModelUseCase",
        "LhT0/t;",
        "setTimeTableTurnOnUseCase",
        "LhT0/r;",
        "setTimeTableTurnOffUseCase",
        "",
        "turnOn",
        "<init>",
        "(LmT0/c;LgT0/a;LhT0/a;Li8/m;LhT0/g;LhT0/t;LhT0/r;Z)V",
        "Lkotlinx/coroutines/flow/f0;",
        "",
        "u3",
        "()Lkotlinx/coroutines/flow/f0;",
        "x3",
        "z3",
        "A3",
        "LkT0/a;",
        "t3",
        "w3",
        "y3",
        "Lkotlinx/coroutines/flow/e;",
        "LkT0/b;",
        "s3",
        "()Lkotlinx/coroutines/flow/e;",
        "is24Format",
        "",
        "G3",
        "(Z)V",
        "E3",
        "hour",
        "C3",
        "(I)V",
        "minute",
        "D3",
        "timeFrame",
        "F3",
        "B3",
        "H3",
        "v3",
        "()LkT0/a;",
        "I3",
        "Lorg/xbet/themesettings/impl/domain/model/TimeFrame;",
        "",
        "q3",
        "(Lorg/xbet/themesettings/impl/domain/model/TimeFrame;)Ljava/lang/String;",
        "",
        "J3",
        "(Ljava/util/List;)LkT0/a;",
        "p3",
        "(Lorg/xbet/themesettings/impl/domain/model/TimeFrame;)I",
        "K3",
        "(I)Lorg/xbet/themesettings/impl/domain/model/TimeFrame;",
        "r3",
        "(LkT0/b;)V",
        "v1",
        "LmT0/c;",
        "x1",
        "LgT0/a;",
        "y1",
        "LhT0/a;",
        "F1",
        "Li8/m;",
        "H1",
        "LhT0/g;",
        "I1",
        "LhT0/t;",
        "P1",
        "LhT0/r;",
        "S1",
        "Z",
        "Lkotlinx/coroutines/flow/V;",
        "V1",
        "Lkotlinx/coroutines/flow/V;",
        "hourListState",
        "b2",
        "minuteListState",
        "v2",
        "timeFrameListState",
        "x2",
        "hourState",
        "y2",
        "minuteState",
        "F2",
        "timeFrameState",
        "H2",
        "timeFrameVisibilityState",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "I2",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "actionFlow",
        "P2",
        "firstLaunch",
        "S2",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final S2:Lorg/xbet/themesettings/impl/presentation/timepicker/r$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final F1:Li8/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:LhT0/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:LhT0/t;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "LkT0/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:LhT0/r;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public P2:Z

.field public final S1:Z

.field public final V1:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LkT0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LkT0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:LmT0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LkT0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LgT0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:LhT0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/themesettings/impl/presentation/timepicker/r$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/themesettings/impl/presentation/timepicker/r$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->S2:Lorg/xbet/themesettings/impl/presentation/timepicker/r$a;

    return-void
.end method

.method public constructor <init>(LmT0/c;LgT0/a;LhT0/a;Li8/m;LhT0/g;LhT0/t;LhT0/r;Z)V
    .locals 0
    .param p1    # LmT0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LgT0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LhT0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Li8/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LhT0/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LhT0/t;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LhT0/r;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->v1:LmT0/c;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->x1:LgT0/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->y1:LhT0/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->F1:Li8/m;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->H1:LhT0/g;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->I1:LhT0/t;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->P1:LhT0/r;

    .line 17
    .line 18
    iput-boolean p8, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->S1:Z

    .line 19
    .line 20
    sget-object p1, LkT0/a;->d:LkT0/a$a;

    .line 21
    .line 22
    invoke-virtual {p1}, LkT0/a$a;->a()LkT0/a;

    .line 23
    .line 24
    .line 25
    move-result-object p2

    .line 26
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 27
    .line 28
    .line 29
    move-result-object p2

    .line 30
    iput-object p2, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->V1:Lkotlinx/coroutines/flow/V;

    .line 31
    .line 32
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->v3()LkT0/a;

    .line 33
    .line 34
    .line 35
    move-result-object p2

    .line 36
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 37
    .line 38
    .line 39
    move-result-object p2

    .line 40
    iput-object p2, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->b2:Lkotlinx/coroutines/flow/V;

    .line 41
    .line 42
    invoke-virtual {p1}, LkT0/a$a;->a()LkT0/a;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    iput-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->v2:Lkotlinx/coroutines/flow/V;

    .line 51
    .line 52
    const/4 p1, 0x0

    .line 53
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 54
    .line 55
    .line 56
    move-result-object p2

    .line 57
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 58
    .line 59
    .line 60
    move-result-object p3

    .line 61
    iput-object p3, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->x2:Lkotlinx/coroutines/flow/V;

    .line 62
    .line 63
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 64
    .line 65
    .line 66
    move-result-object p3

    .line 67
    iput-object p3, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->y2:Lkotlinx/coroutines/flow/V;

    .line 68
    .line 69
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 70
    .line 71
    .line 72
    move-result-object p2

    .line 73
    iput-object p2, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->F2:Lkotlinx/coroutines/flow/V;

    .line 74
    .line 75
    sget-object p2, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 76
    .line 77
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 78
    .line 79
    .line 80
    move-result-object p2

    .line 81
    iput-object p2, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->H2:Lkotlinx/coroutines/flow/V;

    .line 82
    .line 83
    new-instance p2, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 84
    .line 85
    const/4 p3, 0x0

    .line 86
    const/4 p4, 0x3

    .line 87
    invoke-direct {p2, p1, p3, p4, p3}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 88
    .line 89
    .line 90
    iput-object p2, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->I2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 91
    .line 92
    const/4 p1, 0x1

    .line 93
    iput-boolean p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->P2:Z

    .line 94
    .line 95
    return-void
.end method


# virtual methods
.method public final A3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->H2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final B3(Z)V
    .locals 4

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    iget-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->x2:Lkotlinx/coroutines/flow/V;

    .line 4
    .line 5
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    check-cast p1, Ljava/lang/Number;

    .line 10
    .line 11
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    goto :goto_0

    .line 16
    :cond_0
    iget-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->y1:LhT0/a;

    .line 17
    .line 18
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->x2:Lkotlinx/coroutines/flow/V;

    .line 19
    .line 20
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    check-cast v0, Ljava/lang/Number;

    .line 25
    .line 26
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    iget-object v1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->F2:Lkotlinx/coroutines/flow/V;

    .line 31
    .line 32
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    check-cast v1, Ljava/lang/Number;

    .line 37
    .line 38
    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    .line 39
    .line 40
    .line 41
    move-result v1

    .line 42
    invoke-virtual {p0, v1}, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->K3(I)Lorg/xbet/themesettings/impl/domain/model/TimeFrame;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    invoke-virtual {p1, v0, v1}, LhT0/a;->a(ILorg/xbet/themesettings/impl/domain/model/TimeFrame;)I

    .line 47
    .line 48
    .line 49
    move-result p1

    .line 50
    :goto_0
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->y2:Lkotlinx/coroutines/flow/V;

    .line 51
    .line 52
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    check-cast v0, Ljava/lang/Number;

    .line 57
    .line 58
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 59
    .line 60
    .line 61
    move-result v0

    .line 62
    iget-object v1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->H1:LhT0/g;

    .line 63
    .line 64
    iget-boolean v2, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->S1:Z

    .line 65
    .line 66
    invoke-virtual {v1, v2}, LhT0/g;->a(Z)LfT0/a;

    .line 67
    .line 68
    .line 69
    move-result-object v1

    .line 70
    invoke-virtual {v1}, LfT0/a;->a()I

    .line 71
    .line 72
    .line 73
    move-result v2

    .line 74
    const/4 v3, 0x0

    .line 75
    if-ne v2, p1, :cond_1

    .line 76
    .line 77
    invoke-virtual {v1}, LfT0/a;->b()I

    .line 78
    .line 79
    .line 80
    move-result v1

    .line 81
    if-eq v1, v0, :cond_4

    .line 82
    .line 83
    :cond_1
    iget-object v1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->F1:Li8/m;

    .line 84
    .line 85
    invoke-interface {v1}, Li8/m;->invoke()Lcom/xbet/onexcore/themes/Theme;

    .line 86
    .line 87
    .line 88
    move-result-object v1

    .line 89
    iget-boolean v2, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->S1:Z

    .line 90
    .line 91
    if-eqz v2, :cond_2

    .line 92
    .line 93
    iget-object v2, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->I1:LhT0/t;

    .line 94
    .line 95
    invoke-virtual {v2, p1, v0}, LhT0/t;->a(II)V

    .line 96
    .line 97
    .line 98
    goto :goto_1

    .line 99
    :cond_2
    iget-object v2, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->P1:LhT0/r;

    .line 100
    .line 101
    invoke-virtual {v2, p1, v0}, LhT0/r;->a(II)V

    .line 102
    .line 103
    .line 104
    :goto_1
    iget-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->v1:LmT0/c;

    .line 105
    .line 106
    invoke-interface {p1}, LmT0/c;->invoke()V

    .line 107
    .line 108
    .line 109
    iget-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->F1:Li8/m;

    .line 110
    .line 111
    invoke-interface {p1}, Li8/m;->invoke()Lcom/xbet/onexcore/themes/Theme;

    .line 112
    .line 113
    .line 114
    move-result-object p1

    .line 115
    if-ne p1, v1, :cond_3

    .line 116
    .line 117
    goto :goto_2

    .line 118
    :cond_3
    move-object v3, v1

    .line 119
    :cond_4
    :goto_2
    if-eqz v3, :cond_5

    .line 120
    .line 121
    new-instance p1, LkT0/b$c;

    .line 122
    .line 123
    invoke-direct {p1, v3}, LkT0/b$c;-><init>(Lcom/xbet/onexcore/themes/Theme;)V

    .line 124
    .line 125
    .line 126
    invoke-virtual {p0, p1}, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->r3(LkT0/b;)V

    .line 127
    .line 128
    .line 129
    return-void

    .line 130
    :cond_5
    sget-object p1, LkT0/b$b;->a:LkT0/b$b;

    .line 131
    .line 132
    invoke-virtual {p0, p1}, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->r3(LkT0/b;)V

    .line 133
    .line 134
    .line 135
    return-void
.end method

.method public final C3(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->x2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final D3(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->y2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final E3(Z)V
    .locals 1

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->H2:Lkotlinx/coroutines/flow/V;

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    check-cast v0, Ljava/lang/Boolean;

    .line 10
    .line 11
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-nez v0, :cond_1

    .line 16
    .line 17
    :cond_0
    if-nez p1, :cond_2

    .line 18
    .line 19
    iget-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->H2:Lkotlinx/coroutines/flow/V;

    .line 20
    .line 21
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    check-cast p1, Ljava/lang/Boolean;

    .line 26
    .line 27
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 28
    .line 29
    .line 30
    move-result p1

    .line 31
    if-nez p1, :cond_2

    .line 32
    .line 33
    :cond_1
    sget-object p1, LkT0/b$a;->a:LkT0/b$a;

    .line 34
    .line 35
    invoke-virtual {p0, p1}, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->r3(LkT0/b;)V

    .line 36
    .line 37
    .line 38
    :cond_2
    return-void
.end method

.method public final F3(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->F2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final G3(Z)V
    .locals 3

    .line 1
    iget-boolean v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->P2:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->x1:LgT0/a;

    .line 7
    .line 8
    iget-boolean v1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->S1:Z

    .line 9
    .line 10
    invoke-virtual {v0, v1, p1}, LgT0/a;->a(ZZ)LfT0/a;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    iget-object v1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->H2:Lkotlinx/coroutines/flow/V;

    .line 15
    .line 16
    xor-int/lit8 v2, p1, 0x1

    .line 17
    .line 18
    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    invoke-interface {v1, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p0, p1}, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->H3(Z)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0, p1}, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->I3(Z)V

    .line 29
    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->x2:Lkotlinx/coroutines/flow/V;

    .line 32
    .line 33
    invoke-virtual {v0}, LfT0/a;->a()I

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    invoke-interface {p1, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    iget-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->y2:Lkotlinx/coroutines/flow/V;

    .line 45
    .line 46
    invoke-virtual {v0}, LfT0/a;->b()I

    .line 47
    .line 48
    .line 49
    move-result v1

    .line 50
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    invoke-interface {p1, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    iget-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->F2:Lkotlinx/coroutines/flow/V;

    .line 58
    .line 59
    invoke-virtual {v0}, LfT0/a;->c()Lorg/xbet/themesettings/impl/domain/model/TimeFrame;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-virtual {p0, v0}, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->p3(Lorg/xbet/themesettings/impl/domain/model/TimeFrame;)I

    .line 64
    .line 65
    .line 66
    move-result v0

    .line 67
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 72
    .line 73
    .line 74
    const/4 p1, 0x0

    .line 75
    iput-boolean p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->P2:Z

    .line 76
    .line 77
    return-void
.end method

.method public final H3(Z)V
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    xor-int/lit8 v1, p1, 0x1

    .line 6
    .line 7
    if-nez p1, :cond_0

    .line 8
    .line 9
    const/16 p1, 0xc

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/16 p1, 0x17

    .line 13
    .line 14
    :goto_0
    if-gt v1, p1, :cond_1

    .line 15
    .line 16
    :goto_1
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    invoke-static {v0, v2}, Lkotlin/collections/CollectionsKt;->a1(Ljava/util/Collection;Ljava/lang/Object;)Ljava/util/List;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    if-eq v1, p1, :cond_1

    .line 25
    .line 26
    add-int/lit8 v1, v1, 0x1

    .line 27
    .line 28
    goto :goto_1

    .line 29
    :cond_1
    invoke-virtual {p0, v0}, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->J3(Ljava/util/List;)LkT0/a;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    if-eqz p1, :cond_2

    .line 34
    .line 35
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->V1:Lkotlinx/coroutines/flow/V;

    .line 36
    .line 37
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 38
    .line 39
    .line 40
    :cond_2
    return-void
.end method

.method public final I3(Z)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->v2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    if-nez p1, :cond_0

    .line 4
    .line 5
    new-instance p1, LkT0/a;

    .line 6
    .line 7
    sget-object v1, Lorg/xbet/themesettings/impl/domain/model/TimeFrame;->AM:Lorg/xbet/themesettings/impl/domain/model/TimeFrame;

    .line 8
    .line 9
    invoke-virtual {p0, v1}, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->q3(Lorg/xbet/themesettings/impl/domain/model/TimeFrame;)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    sget-object v2, Lorg/xbet/themesettings/impl/domain/model/TimeFrame;->PM:Lorg/xbet/themesettings/impl/domain/model/TimeFrame;

    .line 14
    .line 15
    invoke-virtual {p0, v2}, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->q3(Lorg/xbet/themesettings/impl/domain/model/TimeFrame;)Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    filled-new-array {v1, v2}, [Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-static {v1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const/4 v2, 0x0

    .line 28
    const/4 v3, 0x1

    .line 29
    invoke-direct {p1, v2, v3, v1}, LkT0/a;-><init>(IILjava/util/List;)V

    .line 30
    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_0
    sget-object p1, LkT0/a;->d:LkT0/a$a;

    .line 34
    .line 35
    invoke-virtual {p1}, LkT0/a$a;->a()LkT0/a;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    :goto_0
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    return-void
.end method

.method public final J3(Ljava/util/List;)LkT0/a;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;)",
            "LkT0/a;"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    const/4 p1, 0x0

    .line 8
    return-object p1

    .line 9
    :cond_0
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->x0(Ljava/util/List;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, Ljava/lang/Number;

    .line 14
    .line 15
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->I0(Ljava/util/List;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    check-cast v1, Ljava/lang/Number;

    .line 24
    .line 25
    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    new-instance v2, Ljava/util/ArrayList;

    .line 30
    .line 31
    const/16 v3, 0xa

    .line 32
    .line 33
    invoke-static {p1, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 34
    .line 35
    .line 36
    move-result v3

    .line 37
    invoke-direct {v2, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 38
    .line 39
    .line 40
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 45
    .line 46
    .line 47
    move-result v3

    .line 48
    if-eqz v3, :cond_1

    .line 49
    .line 50
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    check-cast v3, Ljava/lang/Number;

    .line 55
    .line 56
    invoke-virtual {v3}, Ljava/lang/Number;->intValue()I

    .line 57
    .line 58
    .line 59
    move-result v3

    .line 60
    invoke-static {v3}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 61
    .line 62
    .line 63
    move-result-object v3

    .line 64
    const/4 v4, 0x2

    .line 65
    const/16 v5, 0x30

    .line 66
    .line 67
    invoke-static {v3, v4, v5}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v3

    .line 71
    invoke-interface {v2, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 72
    .line 73
    .line 74
    goto :goto_0

    .line 75
    :cond_1
    new-instance p1, LkT0/a;

    .line 76
    .line 77
    invoke-direct {p1, v0, v1, v2}, LkT0/a;-><init>(IILjava/util/List;)V

    .line 78
    .line 79
    .line 80
    return-object p1
.end method

.method public final K3(I)Lorg/xbet/themesettings/impl/domain/model/TimeFrame;
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    if-ne p1, v0, :cond_0

    .line 3
    .line 4
    sget-object p1, Lorg/xbet/themesettings/impl/domain/model/TimeFrame;->PM:Lorg/xbet/themesettings/impl/domain/model/TimeFrame;

    .line 5
    .line 6
    return-object p1

    .line 7
    :cond_0
    sget-object p1, Lorg/xbet/themesettings/impl/domain/model/TimeFrame;->AM:Lorg/xbet/themesettings/impl/domain/model/TimeFrame;

    .line 8
    .line 9
    return-object p1
.end method

.method public final p3(Lorg/xbet/themesettings/impl/domain/model/TimeFrame;)I
    .locals 3

    .line 1
    sget-object v0, Lorg/xbet/themesettings/impl/presentation/timepicker/r$b;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    const/4 v0, 0x0

    .line 10
    const/4 v1, 0x1

    .line 11
    if-eq p1, v1, :cond_1

    .line 12
    .line 13
    const/4 v2, 0x2

    .line 14
    if-eq p1, v2, :cond_0

    .line 15
    .line 16
    return v0

    .line 17
    :cond_0
    return v1

    .line 18
    :cond_1
    return v0
.end method

.method public final q3(Lorg/xbet/themesettings/impl/domain/model/TimeFrame;)Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/themesettings/impl/presentation/timepicker/r$b;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p1, v0, :cond_2

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p1, v0, :cond_1

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-ne p1, v0, :cond_0

    .line 17
    .line 18
    const-string p1, ""

    .line 19
    .line 20
    return-object p1

    .line 21
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 22
    .line 23
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    const-string p1, "PM"

    .line 28
    .line 29
    return-object p1

    .line 30
    :cond_2
    const-string p1, "AM"

    .line 31
    .line 32
    return-object p1
.end method

.method public final r3(LkT0/b;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->I2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final s3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LkT0/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->I2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final t3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "LkT0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->V1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final u3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->x2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final v3()LkT0/a;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    :goto_0
    const/16 v2, 0x3c

    .line 7
    .line 8
    if-ge v1, v2, :cond_0

    .line 9
    .line 10
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 11
    .line 12
    .line 13
    move-result-object v2

    .line 14
    invoke-static {v0, v2}, Lkotlin/collections/CollectionsKt;->a1(Ljava/util/Collection;Ljava/lang/Object;)Ljava/util/List;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    add-int/lit8 v1, v1, 0x1

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    invoke-virtual {p0, v0}, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->J3(Ljava/util/List;)LkT0/a;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    if-nez v0, :cond_1

    .line 26
    .line 27
    sget-object v0, LkT0/a;->d:LkT0/a$a;

    .line 28
    .line 29
    invoke-virtual {v0}, LkT0/a$a;->a()LkT0/a;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    :cond_1
    return-object v0
.end method

.method public final w3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "LkT0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->b2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final x3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->y2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final y3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "LkT0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->v2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final z3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;->F2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method
