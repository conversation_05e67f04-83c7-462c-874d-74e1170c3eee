.class public final synthetic LN1/q;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public static a(LN1/r;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static b(LN1/r;)LN1/r;
    .locals 0

    .line 1
    return-object p0
.end method
