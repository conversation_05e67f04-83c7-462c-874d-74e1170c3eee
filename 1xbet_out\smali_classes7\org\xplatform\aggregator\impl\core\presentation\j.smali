.class public final Lorg/xplatform/aggregator/impl/core/presentation/j;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u000e\n\u0002\u0008\u0004\u0018\u00002\u00020\u0001B!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\'\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000eH\u0000\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\'\u0010\u0013\u001a\u00020\u00102\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0012R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0014R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0015R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0017R\u0014\u0010\u001b\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u001a\u00a8\u0006\u001c"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/presentation/j;",
        "",
        "LwX0/a;",
        "appScreensProvider",
        "LfX/b;",
        "testRepository",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "<init>",
        "(LwX0/a;LfX/b;Lorg/xbet/remoteconfig/domain/usecases/i;)V",
        "Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;",
        "type",
        "Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;",
        "item",
        "",
        "clearParams",
        "Lq4/q;",
        "a",
        "(Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;Z)Lq4/q;",
        "b",
        "LwX0/a;",
        "LfX/b;",
        "c",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "",
        "d",
        "Ljava/lang/String;",
        "altDsAggregatorBrandScreenStyle",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LfX/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LwX0/a;LfX/b;Lorg/xbet/remoteconfig/domain/usecases/i;)V
    .locals 0
    .param p1    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LfX/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/j;->a:LwX0/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/j;->b:LfX/b;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/j;->c:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 9
    .line 10
    invoke-interface {p3}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    invoke-virtual {p1}, Lek0/o;->f()Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/j;->d:Ljava/lang/String;

    .line 19
    .line 20
    return-void
.end method


# virtual methods
.method public final a(Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;Z)Lq4/q;
    .locals 10
    .param p1    # Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$PromoScreen;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    new-instance p2, LP91/m;

    .line 6
    .line 7
    if-eqz p3, :cond_0

    .line 8
    .line 9
    sget-object p1, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$None;->INSTANCE:Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$None;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$PromoScreen;

    .line 13
    .line 14
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$PromoScreen;->getPromoTypeToOpen()Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    :goto_0
    invoke-direct {p2, p1}, LP91/m;-><init>(Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen;)V

    .line 19
    .line 20
    .line 21
    return-object p2

    .line 22
    :cond_1
    instance-of v0, p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$FavoritesScreen;

    .line 23
    .line 24
    if-eqz v0, :cond_2

    .line 25
    .line 26
    new-instance p2, LP91/i;

    .line 27
    .line 28
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$FavoritesScreen;

    .line 29
    .line 30
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$FavoritesScreen;->getFavoriteType()Lorg/xplatform/aggregator/api/navigation/FavoriteType;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    invoke-direct {p2, p1}, LP91/i;-><init>(Lorg/xplatform/aggregator/api/navigation/FavoriteType;)V

    .line 35
    .line 36
    .line 37
    return-object p2

    .line 38
    :cond_2
    instance-of v0, p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsScreen;

    .line 39
    .line 40
    if-eqz v0, :cond_4

    .line 41
    .line 42
    new-instance p2, LP91/t;

    .line 43
    .line 44
    if-eqz p3, :cond_3

    .line 45
    .line 46
    const-wide/16 v0, 0x0

    .line 47
    .line 48
    goto :goto_1

    .line 49
    :cond_3
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsScreen;

    .line 50
    .line 51
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsScreen;->getBannerId()J

    .line 52
    .line 53
    .line 54
    move-result-wide v0

    .line 55
    :goto_1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/j;->b:LfX/b;

    .line 56
    .line 57
    invoke-interface {p1}, LfX/b;->e0()Z

    .line 58
    .line 59
    .line 60
    move-result p1

    .line 61
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/j;->c:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 62
    .line 63
    invoke-interface {p3}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 64
    .line 65
    .line 66
    move-result-object p3

    .line 67
    invoke-virtual {p3}, Lek0/o;->o()Lek0/a;

    .line 68
    .line 69
    .line 70
    move-result-object p3

    .line 71
    invoke-virtual {p3}, Lek0/a;->i()Z

    .line 72
    .line 73
    .line 74
    move-result p3

    .line 75
    invoke-direct {p2, v0, v1, p1, p3}, LP91/t;-><init>(JZZ)V

    .line 76
    .line 77
    .line 78
    return-object p2

    .line 79
    :cond_4
    instance-of v0, p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;

    .line 80
    .line 81
    if-eqz v0, :cond_9

    .line 82
    .line 83
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/j;->c:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 84
    .line 85
    invoke-interface {p2}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 86
    .line 87
    .line 88
    move-result-object p2

    .line 89
    invoke-virtual {p2}, Lek0/o;->z()Ljava/lang/String;

    .line 90
    .line 91
    .line 92
    move-result-object p2

    .line 93
    invoke-virtual {p2}, Ljava/lang/String;->hashCode()I

    .line 94
    .line 95
    .line 96
    move-result p3

    .line 97
    sparse-switch p3, :sswitch_data_0

    .line 98
    .line 99
    .line 100
    goto :goto_2

    .line 101
    :sswitch_0
    const-string p3, "fixed"

    .line 102
    .line 103
    invoke-virtual {p2, p3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 104
    .line 105
    .line 106
    move-result p2

    .line 107
    if-nez p2, :cond_5

    .line 108
    .line 109
    goto :goto_2

    .line 110
    :cond_5
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;

    .line 111
    .line 112
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentId()J

    .line 113
    .line 114
    .line 115
    move-result-wide v1

    .line 116
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentPage()Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 117
    .line 118
    .line 119
    move-result-object v4

    .line 120
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentTitle()Ljava/lang/String;

    .line 121
    .line 122
    .line 123
    move-result-object v3

    .line 124
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getOpenSingleGame()Z

    .line 125
    .line 126
    .line 127
    move-result v5

    .line 128
    new-instance v0, LP91/C;

    .line 129
    .line 130
    invoke-direct/range {v0 .. v5}, LP91/C;-><init>(JLjava/lang/String;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Z)V

    .line 131
    .line 132
    .line 133
    return-object v0

    .line 134
    :sswitch_1
    const-string p3, "gradient"

    .line 135
    .line 136
    invoke-virtual {p2, p3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 137
    .line 138
    .line 139
    move-result p2

    .line 140
    if-nez p2, :cond_6

    .line 141
    .line 142
    goto :goto_2

    .line 143
    :cond_6
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;

    .line 144
    .line 145
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentId()J

    .line 146
    .line 147
    .line 148
    move-result-wide v1

    .line 149
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentPage()Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 150
    .line 151
    .line 152
    move-result-object v4

    .line 153
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentTitle()Ljava/lang/String;

    .line 154
    .line 155
    .line 156
    move-result-object v3

    .line 157
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getOpenSingleGame()Z

    .line 158
    .line 159
    .line 160
    move-result v5

    .line 161
    new-instance v0, LP91/D;

    .line 162
    .line 163
    invoke-direct/range {v0 .. v5}, LP91/D;-><init>(JLjava/lang/String;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Z)V

    .line 164
    .line 165
    .line 166
    return-object v0

    .line 167
    :sswitch_2
    const-string p3, "picture"

    .line 168
    .line 169
    invoke-virtual {p2, p3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 170
    .line 171
    .line 172
    move-result p2

    .line 173
    if-eqz p2, :cond_7

    .line 174
    .line 175
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;

    .line 176
    .line 177
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentId()J

    .line 178
    .line 179
    .line 180
    move-result-wide v1

    .line 181
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentPage()Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 182
    .line 183
    .line 184
    move-result-object v4

    .line 185
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentTitle()Ljava/lang/String;

    .line 186
    .line 187
    .line 188
    move-result-object v3

    .line 189
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getOpenSingleGame()Z

    .line 190
    .line 191
    .line 192
    move-result v5

    .line 193
    new-instance v0, LP91/F;

    .line 194
    .line 195
    invoke-direct/range {v0 .. v5}, LP91/F;-><init>(JLjava/lang/String;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Z)V

    .line 196
    .line 197
    .line 198
    return-object v0

    .line 199
    :sswitch_3
    const-string p3, "pictureHead"

    .line 200
    .line 201
    invoke-virtual {p2, p3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 202
    .line 203
    .line 204
    move-result p2

    .line 205
    if-nez p2, :cond_8

    .line 206
    .line 207
    :cond_7
    :goto_2
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;

    .line 208
    .line 209
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentId()J

    .line 210
    .line 211
    .line 212
    move-result-wide v1

    .line 213
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentPage()Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 214
    .line 215
    .line 216
    move-result-object v4

    .line 217
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentTitle()Ljava/lang/String;

    .line 218
    .line 219
    .line 220
    move-result-object v3

    .line 221
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getOpenSingleGame()Z

    .line 222
    .line 223
    .line 224
    move-result v5

    .line 225
    new-instance v0, LP91/F;

    .line 226
    .line 227
    invoke-direct/range {v0 .. v5}, LP91/F;-><init>(JLjava/lang/String;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Z)V

    .line 228
    .line 229
    .line 230
    return-object v0

    .line 231
    :cond_8
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;

    .line 232
    .line 233
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentId()J

    .line 234
    .line 235
    .line 236
    move-result-wide v1

    .line 237
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentPage()Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 238
    .line 239
    .line 240
    move-result-object v4

    .line 241
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getTournamentTitle()Ljava/lang/String;

    .line 242
    .line 243
    .line 244
    move-result-object v3

    .line 245
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;->getOpenSingleGame()Z

    .line 246
    .line 247
    .line 248
    move-result v5

    .line 249
    new-instance v0, LP91/E;

    .line 250
    .line 251
    invoke-direct/range {v0 .. v5}, LP91/E;-><init>(JLjava/lang/String;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Z)V

    .line 252
    .line 253
    .line 254
    return-object v0

    .line 255
    :cond_9
    instance-of v0, p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$ProvidersScreen;

    .line 256
    .line 257
    if-eqz v0, :cond_a

    .line 258
    .line 259
    new-instance p1, LP91/g;

    .line 260
    .line 261
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/j;->c:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 262
    .line 263
    invoke-interface {p2}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 264
    .line 265
    .line 266
    move-result-object p2

    .line 267
    invoke-virtual {p2}, Lek0/o;->o()Lek0/a;

    .line 268
    .line 269
    .line 270
    move-result-object p2

    .line 271
    invoke-virtual {p2}, Lek0/a;->c()Z

    .line 272
    .line 273
    .line 274
    move-result p2

    .line 275
    invoke-direct {p1, p2}, LP91/g;-><init>(Z)V

    .line 276
    .line 277
    .line 278
    return-object p1

    .line 279
    :cond_a
    instance-of v0, p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$MyAggregatorScreen;

    .line 280
    .line 281
    if-eqz v0, :cond_c

    .line 282
    .line 283
    if-eqz p3, :cond_b

    .line 284
    .line 285
    new-instance v1, LP91/z;

    .line 286
    .line 287
    sget-object p2, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 288
    .line 289
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 290
    .line 291
    .line 292
    move-result-wide v6

    .line 293
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$MyAggregatorScreen;

    .line 294
    .line 295
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$MyAggregatorScreen;->isVirtual()Z

    .line 296
    .line 297
    .line 298
    move-result v8

    .line 299
    const-wide/16 v2, 0x0

    .line 300
    .line 301
    const-wide/16 v4, 0x0

    .line 302
    .line 303
    invoke-direct/range {v1 .. v8}, LP91/z;-><init>(JJJZ)V

    .line 304
    .line 305
    .line 306
    return-object v1

    .line 307
    :cond_b
    new-instance v2, LP91/z;

    .line 308
    .line 309
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$MyAggregatorScreen;

    .line 310
    .line 311
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$MyAggregatorScreen;->getIdToOpen()J

    .line 312
    .line 313
    .line 314
    move-result-wide v3

    .line 315
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$MyAggregatorScreen;->getBannerId()J

    .line 316
    .line 317
    .line 318
    move-result-wide v5

    .line 319
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$MyAggregatorScreen;->getPartitionId()J

    .line 320
    .line 321
    .line 322
    move-result-wide v7

    .line 323
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$MyAggregatorScreen;->isVirtual()Z

    .line 324
    .line 325
    .line 326
    move-result v9

    .line 327
    invoke-direct/range {v2 .. v9}, LP91/z;-><init>(JJJZ)V

    .line 328
    .line 329
    .line 330
    return-object v2

    .line 331
    :cond_c
    instance-of v0, p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$MyVirtualScreen;

    .line 332
    .line 333
    if-eqz v0, :cond_e

    .line 334
    .line 335
    if-eqz p3, :cond_d

    .line 336
    .line 337
    new-instance v1, LP91/z;

    .line 338
    .line 339
    sget-object p1, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 340
    .line 341
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 342
    .line 343
    .line 344
    move-result-wide v6

    .line 345
    const/4 v8, 0x1

    .line 346
    const-wide/16 v2, 0x0

    .line 347
    .line 348
    const-wide/16 v4, 0x0

    .line 349
    .line 350
    invoke-direct/range {v1 .. v8}, LP91/z;-><init>(JJJZ)V

    .line 351
    .line 352
    .line 353
    return-object v1

    .line 354
    :cond_d
    new-instance v2, LP91/z;

    .line 355
    .line 356
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$MyVirtualScreen;

    .line 357
    .line 358
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$MyVirtualScreen;->getIdToOpen()J

    .line 359
    .line 360
    .line 361
    move-result-wide v3

    .line 362
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$MyVirtualScreen;->getBannerId()J

    .line 363
    .line 364
    .line 365
    move-result-wide v5

    .line 366
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$MyVirtualScreen;->getPartitionId()J

    .line 367
    .line 368
    .line 369
    move-result-wide v7

    .line 370
    const/4 v9, 0x1

    .line 371
    invoke-direct/range {v2 .. v9}, LP91/z;-><init>(JJJZ)V

    .line 372
    .line 373
    .line 374
    return-object v2

    .line 375
    :cond_e
    instance-of v0, p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$CategoriesScreen;

    .line 376
    .line 377
    if-eqz v0, :cond_10

    .line 378
    .line 379
    new-instance p2, LP91/f;

    .line 380
    .line 381
    if-eqz p3, :cond_f

    .line 382
    .line 383
    new-instance v0, Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;

    .line 384
    .line 385
    const/16 v8, 0x1f

    .line 386
    .line 387
    const/4 v9, 0x0

    .line 388
    const/4 v1, 0x0

    .line 389
    const-wide/16 v2, 0x0

    .line 390
    .line 391
    const/4 v4, 0x0

    .line 392
    const/4 v5, 0x0

    .line 393
    const-wide/16 v6, 0x0

    .line 394
    .line 395
    invoke-direct/range {v0 .. v9}, Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;-><init>(Ljava/lang/String;JLjava/util/List;Ljava/util/List;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 396
    .line 397
    .line 398
    goto :goto_3

    .line 399
    :cond_f
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$CategoriesScreen;

    .line 400
    .line 401
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$CategoriesScreen;->getCategoryToOpen()Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;

    .line 402
    .line 403
    .line 404
    move-result-object v0

    .line 405
    :goto_3
    invoke-direct {p2, v0}, LP91/f;-><init>(Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;)V

    .line 406
    .line 407
    .line 408
    return-object p2

    .line 409
    :cond_10
    instance-of v0, p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorCategoryItemScreen;

    .line 410
    .line 411
    if-eqz v0, :cond_11

    .line 412
    .line 413
    new-instance p1, LP91/h;

    .line 414
    .line 415
    invoke-direct {p1, p2}, LP91/h;-><init>(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 416
    .line 417
    .line 418
    return-object p1

    .line 419
    :cond_11
    instance-of v0, p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorSearch;

    .line 420
    .line 421
    if-eqz v0, :cond_12

    .line 422
    .line 423
    new-instance p1, LP91/r;

    .line 424
    .line 425
    invoke-virtual {p2}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->f()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 426
    .line 427
    .line 428
    move-result-object p2

    .line 429
    invoke-virtual {p2}, Lorg/xbet/analytics/domain/scope/search/SearchScreenType;->getSearchScreenValue()Ljava/lang/String;

    .line 430
    .line 431
    .line 432
    move-result-object p2

    .line 433
    invoke-direct {p1, p2}, LP91/r;-><init>(Ljava/lang/String;)V

    .line 434
    .line 435
    .line 436
    return-object p1

    .line 437
    :cond_12
    instance-of v0, p1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$RecommendedScreen;

    .line 438
    .line 439
    if-eqz v0, :cond_13

    .line 440
    .line 441
    new-instance p1, LP91/B;

    .line 442
    .line 443
    invoke-virtual {p2}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->e()Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;

    .line 444
    .line 445
    .line 446
    move-result-object p2

    .line 447
    check-cast p2, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$RecommendedScreen;

    .line 448
    .line 449
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$RecommendedScreen;->getPartitionId()J

    .line 450
    .line 451
    .line 452
    move-result-wide p2

    .line 453
    invoke-direct {p1, p2, p3}, LP91/B;-><init>(J)V

    .line 454
    .line 455
    .line 456
    return-object p1

    .line 457
    :cond_13
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/core/presentation/j;->b(Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;Z)Lq4/q;

    .line 458
    .line 459
    .line 460
    move-result-object p1

    .line 461
    return-object p1

    .line 462
    nop

    .line 463
    :sswitch_data_0
    .sparse-switch
        -0x4be01002 -> :sswitch_3
        -0x226fa302 -> :sswitch_2
        0x557f730 -> :sswitch_1
        0x5cee774 -> :sswitch_0
    .end sparse-switch
.end method

.method public final b(Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;Z)Lq4/q;
    .locals 23

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    instance-of v2, v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$NewGamesFolderScreen;

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    new-instance v2, LP91/A;

    .line 10
    .line 11
    check-cast v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$NewGamesFolderScreen;

    .line 12
    .line 13
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$NewGamesFolderScreen;->getFromSuccessfulSearch()Z

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    const/4 v3, 0x0

    .line 18
    move-object/from16 v4, p2

    .line 19
    .line 20
    invoke-direct {v2, v4, v1, v3}, LP91/A;-><init>(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;ZZ)V

    .line 21
    .line 22
    .line 23
    return-object v2

    .line 24
    :cond_0
    move-object/from16 v4, p2

    .line 25
    .line 26
    instance-of v2, v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$GiftsScreen;

    .line 27
    .line 28
    if-eqz v2, :cond_1

    .line 29
    .line 30
    new-instance v2, LP91/k;

    .line 31
    .line 32
    check-cast v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$GiftsScreen;

    .line 33
    .line 34
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$GiftsScreen;->getBonusesCount()I

    .line 35
    .line 36
    .line 37
    move-result v3

    .line 38
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$GiftsScreen;->getFreeSpinsCount()I

    .line 39
    .line 40
    .line 41
    move-result v4

    .line 42
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$GiftsScreen;->getGiftTypeId()I

    .line 43
    .line 44
    .line 45
    move-result v5

    .line 46
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$GiftsScreen;->getAfterAuth()Z

    .line 47
    .line 48
    .line 49
    move-result v1

    .line 50
    invoke-direct {v2, v3, v4, v5, v1}, LP91/k;-><init>(IIIZ)V

    .line 51
    .line 52
    .line 53
    return-object v2

    .line 54
    :cond_1
    instance-of v2, v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AllProvidersScreen;

    .line 55
    .line 56
    if-eqz v2, :cond_2

    .line 57
    .line 58
    new-instance v3, LP91/e;

    .line 59
    .line 60
    invoke-virtual/range {p2 .. p2}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->h()Ljava/lang/String;

    .line 61
    .line 62
    .line 63
    move-result-object v4

    .line 64
    invoke-virtual/range {p2 .. p2}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->d()J

    .line 65
    .line 66
    .line 67
    move-result-wide v5

    .line 68
    check-cast v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AllProvidersScreen;

    .line 69
    .line 70
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AllProvidersScreen;->getSortType()Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object v7

    .line 74
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AllProvidersScreen;->getSearchQuery()Ljava/lang/String;

    .line 75
    .line 76
    .line 77
    move-result-object v8

    .line 78
    invoke-direct/range {v3 .. v8}, LP91/e;-><init>(Ljava/lang/String;JLjava/lang/String;Ljava/lang/String;)V

    .line 79
    .line 80
    .line 81
    return-object v3

    .line 82
    :cond_2
    instance-of v2, v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$Rules;

    .line 83
    .line 84
    if-eqz v2, :cond_3

    .line 85
    .line 86
    iget-object v3, v0, Lorg/xplatform/aggregator/impl/core/presentation/j;->a:LwX0/a;

    .line 87
    .line 88
    sget v7, Lpb/k;->rules:I

    .line 89
    .line 90
    check-cast v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$Rules;

    .line 91
    .line 92
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$Rules;->getKey()Ljava/lang/String;

    .line 93
    .line 94
    .line 95
    move-result-object v4

    .line 96
    const/16 v10, 0x16

    .line 97
    .line 98
    const/4 v11, 0x0

    .line 99
    const/4 v5, 0x0

    .line 100
    const/4 v6, 0x0

    .line 101
    const/4 v8, 0x0

    .line 102
    const/4 v9, 0x1

    .line 103
    invoke-static/range {v3 .. v11}, LwX0/a$a;->c(LwX0/a;Ljava/lang/String;Ljava/util/Map;Ljava/lang/String;IZZILjava/lang/Object;)Lq4/q;

    .line 104
    .line 105
    .line 106
    move-result-object v1

    .line 107
    return-object v1

    .line 108
    :cond_3
    instance-of v2, v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$VipCashBackScreen;

    .line 109
    .line 110
    if-eqz v2, :cond_4

    .line 111
    .line 112
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/core/presentation/j;->a:LwX0/a;

    .line 113
    .line 114
    const/4 v2, 0x1

    .line 115
    invoke-interface {v1, v2}, LwX0/a;->N(Z)Lq4/q;

    .line 116
    .line 117
    .line 118
    move-result-object v1

    .line 119
    return-object v1

    .line 120
    :cond_4
    instance-of v2, v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$DailyTasksScreen;

    .line 121
    .line 122
    if-eqz v2, :cond_5

    .line 123
    .line 124
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/core/presentation/j;->a:LwX0/a;

    .line 125
    .line 126
    invoke-interface {v1}, LwX0/a;->i()Lq4/q;

    .line 127
    .line 128
    .line 129
    move-result-object v1

    .line 130
    return-object v1

    .line 131
    :cond_5
    instance-of v2, v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentPrizesScreen;

    .line 132
    .line 133
    if-eqz v2, :cond_6

    .line 134
    .line 135
    new-instance v3, LP91/s;

    .line 136
    .line 137
    check-cast v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentPrizesScreen;

    .line 138
    .line 139
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentPrizesScreen;->getTournamentId()J

    .line 140
    .line 141
    .line 142
    move-result-wide v4

    .line 143
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentPrizesScreen;->getTournamentTitle()Ljava/lang/String;

    .line 144
    .line 145
    .line 146
    move-result-object v6

    .line 147
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentPrizesScreen;->getStageTournamentID()J

    .line 148
    .line 149
    .line 150
    move-result-wide v7

    .line 151
    invoke-direct/range {v3 .. v8}, LP91/s;-><init>(JLjava/lang/String;J)V

    .line 152
    .line 153
    .line 154
    return-object v3

    .line 155
    :cond_6
    instance-of v2, v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentStagesScreen;

    .line 156
    .line 157
    if-eqz v2, :cond_7

    .line 158
    .line 159
    new-instance v2, LP91/u;

    .line 160
    .line 161
    check-cast v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentStagesScreen;

    .line 162
    .line 163
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentStagesScreen;->getTournamentId()J

    .line 164
    .line 165
    .line 166
    move-result-wide v3

    .line 167
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentStagesScreen;->getTournamentTitle()Ljava/lang/String;

    .line 168
    .line 169
    .line 170
    move-result-object v5

    .line 171
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentStagesScreen;->getTournamentType()Ljava/lang/String;

    .line 172
    .line 173
    .line 174
    move-result-object v1

    .line 175
    invoke-direct {v2, v3, v4, v5, v1}, LP91/u;-><init>(JLjava/lang/String;Ljava/lang/String;)V

    .line 176
    .line 177
    .line 178
    return-object v2

    .line 179
    :cond_7
    instance-of v2, v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsProvidersScreen;

    .line 180
    .line 181
    if-eqz v2, :cond_8

    .line 182
    .line 183
    new-instance v2, LP91/G;

    .line 184
    .line 185
    check-cast v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsProvidersScreen;

    .line 186
    .line 187
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsProvidersScreen;->getTournamentId()J

    .line 188
    .line 189
    .line 190
    move-result-wide v3

    .line 191
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsProvidersScreen;->getTournamentTitle()Ljava/lang/String;

    .line 192
    .line 193
    .line 194
    move-result-object v1

    .line 195
    invoke-direct {v2, v3, v4, v1}, LP91/G;-><init>(JLjava/lang/String;)V

    .line 196
    .line 197
    .line 198
    return-object v2

    .line 199
    :cond_8
    instance-of v2, v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;

    .line 200
    .line 201
    if-eqz v2, :cond_a

    .line 202
    .line 203
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/core/presentation/j;->d:Ljava/lang/String;

    .line 204
    .line 205
    const-string v3, "picture"

    .line 206
    .line 207
    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 208
    .line 209
    .line 210
    move-result v2

    .line 211
    if-eqz v2, :cond_9

    .line 212
    .line 213
    new-instance v3, LP91/x;

    .line 214
    .line 215
    check-cast v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;

    .line 216
    .line 217
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getPartitionId()J

    .line 218
    .line 219
    .line 220
    move-result-wide v4

    .line 221
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getBrandId()J

    .line 222
    .line 223
    .line 224
    move-result-wide v6

    .line 225
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getBrandName()Ljava/lang/String;

    .line 226
    .line 227
    .line 228
    move-result-object v8

    .line 229
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getImgBanner()Ljava/lang/String;

    .line 230
    .line 231
    .line 232
    move-result-object v9

    .line 233
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getSubCategoryId()I

    .line 234
    .line 235
    .line 236
    move-result v15

    .line 237
    sget-object v16, Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;->UNKNOWN:Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;

    .line 238
    .line 239
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getPartitions()Ljava/util/List;

    .line 240
    .line 241
    .line 242
    move-result-object v17

    .line 243
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getDescription()Ljava/lang/String;

    .line 244
    .line 245
    .line 246
    move-result-object v18

    .line 247
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getFullInfoEnabled()Z

    .line 248
    .line 249
    .line 250
    move-result v19

    .line 251
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getFromPopularSearch()Z

    .line 252
    .line 253
    .line 254
    move-result v20

    .line 255
    const/16 v21, 0xe0

    .line 256
    .line 257
    const/16 v22, 0x0

    .line 258
    .line 259
    const/4 v10, 0x1

    .line 260
    const-wide/16 v11, 0x0

    .line 261
    .line 262
    const/4 v13, 0x0

    .line 263
    const/4 v14, 0x0

    .line 264
    invoke-direct/range {v3 .. v22}, LP91/x;-><init>(JJLjava/lang/String;Ljava/lang/String;ZJIZILorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;Ljava/util/List;Ljava/lang/String;ZZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 265
    .line 266
    .line 267
    return-object v3

    .line 268
    :cond_9
    new-instance v4, LP91/w;

    .line 269
    .line 270
    check-cast v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;

    .line 271
    .line 272
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getPartitionId()J

    .line 273
    .line 274
    .line 275
    move-result-wide v5

    .line 276
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getBrandId()J

    .line 277
    .line 278
    .line 279
    move-result-wide v7

    .line 280
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getBrandName()Ljava/lang/String;

    .line 281
    .line 282
    .line 283
    move-result-object v9

    .line 284
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getSubCategoryId()I

    .line 285
    .line 286
    .line 287
    move-result v15

    .line 288
    sget-object v16, Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;->UNKNOWN:Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;

    .line 289
    .line 290
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getPartitions()Ljava/util/List;

    .line 291
    .line 292
    .line 293
    move-result-object v17

    .line 294
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getDescription()Ljava/lang/String;

    .line 295
    .line 296
    .line 297
    move-result-object v18

    .line 298
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getFullInfoEnabled()Z

    .line 299
    .line 300
    .line 301
    move-result v19

    .line 302
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandGamesScreen;->getFromPopularSearch()Z

    .line 303
    .line 304
    .line 305
    move-result v20

    .line 306
    const/16 v21, 0x70

    .line 307
    .line 308
    const/16 v22, 0x0

    .line 309
    .line 310
    const/4 v10, 0x1

    .line 311
    const-wide/16 v11, 0x0

    .line 312
    .line 313
    const/4 v13, 0x0

    .line 314
    const/4 v14, 0x0

    .line 315
    invoke-direct/range {v4 .. v22}, LP91/w;-><init>(JJLjava/lang/String;ZJIZILorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;Ljava/util/List;Ljava/lang/String;ZZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 316
    .line 317
    .line 318
    return-object v4

    .line 319
    :cond_a
    instance-of v2, v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorPublisherGamesFragmentScreen;

    .line 320
    .line 321
    if-eqz v2, :cond_b

    .line 322
    .line 323
    new-instance v3, LP91/p;

    .line 324
    .line 325
    check-cast v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorPublisherGamesFragmentScreen;

    .line 326
    .line 327
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorPublisherGamesFragmentScreen;->getPartitionId()J

    .line 328
    .line 329
    .line 330
    move-result-wide v4

    .line 331
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorPublisherGamesFragmentScreen;->getProductId()J

    .line 332
    .line 333
    .line 334
    move-result-wide v6

    .line 335
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorPublisherGamesFragmentScreen;->getName()Ljava/lang/String;

    .line 336
    .line 337
    .line 338
    move-result-object v8

    .line 339
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorPublisherGamesFragmentScreen;->getShowBalanceSelector()Z

    .line 340
    .line 341
    .line 342
    move-result v9

    .line 343
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorPublisherGamesFragmentScreen;->getAccountId()J

    .line 344
    .line 345
    .line 346
    move-result-wide v10

    .line 347
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorPublisherGamesFragmentScreen;->getBonusId()I

    .line 348
    .line 349
    .line 350
    move-result v12

    .line 351
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorPublisherGamesFragmentScreen;->getShowFavorites()Z

    .line 352
    .line 353
    .line 354
    move-result v13

    .line 355
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorPublisherGamesFragmentScreen;->getSubCategoryId()I

    .line 356
    .line 357
    .line 358
    move-result v14

    .line 359
    sget-object v15, Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;->UNKNOWN:Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;

    .line 360
    .line 361
    invoke-direct/range {v3 .. v15}, LP91/p;-><init>(JJLjava/lang/String;ZJIZILorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;)V

    .line 362
    .line 363
    .line 364
    return-object v3

    .line 365
    :cond_b
    instance-of v2, v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandInfoScreen;

    .line 366
    .line 367
    if-eqz v2, :cond_c

    .line 368
    .line 369
    new-instance v2, LP91/y;

    .line 370
    .line 371
    check-cast v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandInfoScreen;

    .line 372
    .line 373
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandInfoScreen;->getTitle()Ljava/lang/String;

    .line 374
    .line 375
    .line 376
    move-result-object v3

    .line 377
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$BrandInfoScreen;->getDescription()Ljava/lang/String;

    .line 378
    .line 379
    .line 380
    move-result-object v1

    .line 381
    invoke-direct {v2, v3, v1}, LP91/y;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 382
    .line 383
    .line 384
    return-object v2

    .line 385
    :cond_c
    instance-of v2, v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$None;

    .line 386
    .line 387
    if-eqz v2, :cond_d

    .line 388
    .line 389
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 390
    .line 391
    const-string v2, "No screen for AggregatorScreenType.None"

    .line 392
    .line 393
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 394
    .line 395
    .line 396
    throw v1

    .line 397
    :cond_d
    new-instance v2, Ljava/lang/IllegalStateException;

    .line 398
    .line 399
    new-instance v3, Ljava/lang/StringBuilder;

    .line 400
    .line 401
    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 402
    .line 403
    .line 404
    const-string v4, "No screen for unexpected type "

    .line 405
    .line 406
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 407
    .line 408
    .line 409
    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 410
    .line 411
    .line 412
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 413
    .line 414
    .line 415
    move-result-object v1

    .line 416
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 417
    .line 418
    .line 419
    move-result-object v1

    .line 420
    invoke-direct {v2, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 421
    .line 422
    .line 423
    throw v2
.end method
