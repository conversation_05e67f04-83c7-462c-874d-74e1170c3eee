.class public final LsO0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LsO0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LsO0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LsO0/a$b$a;
    }
.end annotation


# instance fields
.field public final a:LsO0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LqO0/b;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/team/impl/team_completed_match/data/repository/TeamTeamCompletedMatchesRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LvO0/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDH0/a;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LkC0/a;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQD0/d;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/i;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/team/impl/team_completed_match/presentation/viewmodel/TeamCompletedMatchesViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;LkC0/a;LDH0/a;LwX0/c;Ljava/lang/Long;LQD0/d;LSX0/a;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LsO0/a$b;->a:LsO0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p12}, LsO0/a$b;->b(LQW0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;LkC0/a;LDH0/a;LwX0/c;Ljava/lang/Long;LQD0/d;LSX0/a;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;LkC0/a;LDH0/a;LwX0/c;Ljava/lang/Long;LQD0/d;LSX0/a;Lc8/h;LsO0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p12}, LsO0/a$b;-><init>(LQW0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;LkC0/a;LDH0/a;LwX0/c;Ljava/lang/Long;LQD0/d;LSX0/a;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/team/impl/team_completed_match/presentation/fragment/TeamCompletedMatchesFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LsO0/a$b;->c(Lorg/xbet/statistic/team/impl/team_completed_match/presentation/fragment/TeamCompletedMatchesFragment;)Lorg/xbet/statistic/team/impl/team_completed_match/presentation/fragment/TeamCompletedMatchesFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;LkC0/a;LDH0/a;LwX0/c;Ljava/lang/Long;LQD0/d;LSX0/a;Lc8/h;)V
    .locals 0

    .line 1
    invoke-static {p2}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    iput-object p2, p0, LsO0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    new-instance p2, LsO0/a$b$a;

    .line 8
    .line 9
    invoke-direct {p2, p1}, LsO0/a$b$a;-><init>(LQW0/c;)V

    .line 10
    .line 11
    .line 12
    iput-object p2, p0, LsO0/a$b;->c:Ldagger/internal/h;

    .line 13
    .line 14
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    iput-object p1, p0, LsO0/a$b;->d:Ldagger/internal/h;

    .line 19
    .line 20
    invoke-static {p1}, LqO0/c;->a(LBc/a;)LqO0/c;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iput-object p1, p0, LsO0/a$b;->e:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static {p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iput-object p1, p0, LsO0/a$b;->f:Ldagger/internal/h;

    .line 31
    .line 32
    iget-object p2, p0, LsO0/a$b;->c:Ldagger/internal/h;

    .line 33
    .line 34
    iget-object p3, p0, LsO0/a$b;->e:Ldagger/internal/h;

    .line 35
    .line 36
    invoke-static {p2, p3, p1}, Lorg/xbet/statistic/team/impl/team_completed_match/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/team/impl/team_completed_match/data/repository/a;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    iput-object p1, p0, LsO0/a$b;->g:Ldagger/internal/h;

    .line 41
    .line 42
    invoke-static {p1}, LvO0/b;->a(LBc/a;)LvO0/b;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    iput-object p1, p0, LsO0/a$b;->h:Ldagger/internal/h;

    .line 47
    .line 48
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iput-object p1, p0, LsO0/a$b;->i:Ldagger/internal/h;

    .line 53
    .line 54
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    iput-object p1, p0, LsO0/a$b;->j:Ldagger/internal/h;

    .line 59
    .line 60
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    iput-object p1, p0, LsO0/a$b;->k:Ldagger/internal/h;

    .line 65
    .line 66
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    iput-object p1, p0, LsO0/a$b;->l:Ldagger/internal/h;

    .line 71
    .line 72
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    iput-object p1, p0, LsO0/a$b;->m:Ldagger/internal/h;

    .line 77
    .line 78
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    iput-object p1, p0, LsO0/a$b;->n:Ldagger/internal/h;

    .line 83
    .line 84
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    iput-object p1, p0, LsO0/a$b;->o:Ldagger/internal/h;

    .line 89
    .line 90
    invoke-static {p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    iput-object p1, p0, LsO0/a$b;->p:Ldagger/internal/h;

    .line 95
    .line 96
    iget-object p2, p0, LsO0/a$b;->l:Ldagger/internal/h;

    .line 97
    .line 98
    iget-object p3, p0, LsO0/a$b;->m:Ldagger/internal/h;

    .line 99
    .line 100
    iget-object p4, p0, LsO0/a$b;->n:Ldagger/internal/h;

    .line 101
    .line 102
    iget-object p5, p0, LsO0/a$b;->o:Ldagger/internal/h;

    .line 103
    .line 104
    invoke-static {p2, p3, p4, p5, p1}, Lorg/xbet/statistic/statistic_core/presentation/delegates/j;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/j;

    .line 105
    .line 106
    .line 107
    move-result-object p11

    .line 108
    iput-object p11, p0, LsO0/a$b;->q:Ldagger/internal/h;

    .line 109
    .line 110
    iget-object p6, p0, LsO0/a$b;->b:Ldagger/internal/h;

    .line 111
    .line 112
    iget-object p7, p0, LsO0/a$b;->h:Ldagger/internal/h;

    .line 113
    .line 114
    iget-object p8, p0, LsO0/a$b;->i:Ldagger/internal/h;

    .line 115
    .line 116
    iget-object p9, p0, LsO0/a$b;->j:Ldagger/internal/h;

    .line 117
    .line 118
    iget-object p10, p0, LsO0/a$b;->k:Ldagger/internal/h;

    .line 119
    .line 120
    invoke-static/range {p6 .. p11}, Lorg/xbet/statistic/team/impl/team_completed_match/presentation/viewmodel/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/team/impl/team_completed_match/presentation/viewmodel/a;

    .line 121
    .line 122
    .line 123
    move-result-object p1

    .line 124
    iput-object p1, p0, LsO0/a$b;->r:Ldagger/internal/h;

    .line 125
    .line 126
    return-void
.end method

.method public final c(Lorg/xbet/statistic/team/impl/team_completed_match/presentation/fragment/TeamCompletedMatchesFragment;)Lorg/xbet/statistic/team/impl/team_completed_match/presentation/fragment/TeamCompletedMatchesFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LsO0/a$b;->e()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/team/impl/team_completed_match/presentation/fragment/d;->a(Lorg/xbet/statistic/team/impl/team_completed_match/presentation/fragment/TeamCompletedMatchesFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final d()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/statistic/team/impl/team_completed_match/presentation/viewmodel/TeamCompletedMatchesViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LsO0/a$b;->r:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final e()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LsO0/a$b;->d()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
