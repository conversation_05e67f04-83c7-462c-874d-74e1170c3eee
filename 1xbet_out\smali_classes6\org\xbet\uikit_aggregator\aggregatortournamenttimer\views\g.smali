.class public final synthetic Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/animation/ValueAnimator$AnimatorUpdateListener;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/g;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    return-void
.end method


# virtual methods
.method public final onAnimationUpdate(Landroid/animation/ValueAnimator;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/g;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    invoke-static {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->b(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;Landroid/animation/ValueAnimator;)V

    return-void
.end method
