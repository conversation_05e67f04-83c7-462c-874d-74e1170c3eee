.class public final synthetic Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/a;->a:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/a;->a:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    invoke-static {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->z2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)Landroidx/lifecycle/e0$c;

    move-result-object v0

    return-object v0
.end method
