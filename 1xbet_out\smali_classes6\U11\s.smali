.class public final synthetic LU11/s;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroid/graphics/drawable/Drawable;

.field public final synthetic b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureLStyleBonus;


# direct methods
.method public synthetic constructor <init>(Landroid/graphics/drawable/Drawable;Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureLStyleBonus;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LU11/s;->a:Landroid/graphics/drawable/Drawable;

    iput-object p2, p0, LU11/s;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureLStyleBonus;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, LU11/s;->a:Landroid/graphics/drawable/Drawable;

    iget-object v1, p0, LU11/s;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureLStyleBonus;

    invoke-static {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureLStyleBonus;->t(Landroid/graphics/drawable/Drawable;Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureLStyleBonus;)V

    return-void
.end method
