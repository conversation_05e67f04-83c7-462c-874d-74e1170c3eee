.class final Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.alerts_pipe_impl.presentation.kz_first_deposit_bottom_dialog.KzFirstDepositBottomSheet$onObserveData$1"
    f = "KzFirstDepositBottomSheet.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;->R2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/String;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "link",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;


# direct methods
.method public constructor <init>(Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;->this$0:Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method

.method public static synthetic a(Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;->c(Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;->O2()Log/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    iget-object p0, p0, Log/a;->c:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 6
    .line 7
    const/4 p1, 0x0

    .line 8
    invoke-virtual {p0, p1}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;

    iget-object v1, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;->this$0:Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;

    invoke-direct {v0, v1, p2}, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;-><init>(Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;->invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 12

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    move-object v2, p1

    .line 14
    check-cast v2, Ljava/lang/String;

    .line 15
    .line 16
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 17
    .line 18
    iget-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;->this$0:Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;

    .line 19
    .line 20
    invoke-virtual {p1}, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;->O2()Log/a;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iget-object v1, p1, Log/a;->c:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 25
    .line 26
    sget v3, Lpb/g;->onboarding_placeholder:I

    .line 27
    .line 28
    const/4 p1, 0x0

    .line 29
    new-array v6, p1, [LYW0/d;

    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;->this$0:Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;

    .line 32
    .line 33
    new-instance v8, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/d;

    .line 34
    .line 35
    invoke-direct {v8, p1}, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/d;-><init>(Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;)V

    .line 36
    .line 37
    .line 38
    const/16 v10, 0xa4

    .line 39
    .line 40
    const/4 v11, 0x0

    .line 41
    const/4 v4, 0x0

    .line 42
    const/4 v5, 0x1

    .line 43
    const/4 v7, 0x0

    .line 44
    const/4 v9, 0x0

    .line 45
    invoke-static/range {v0 .. v11}, LCX0/l;->w(LCX0/l;Landroid/widget/ImageView;Ljava/lang/String;IIZ[LYW0/d;LYW0/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 46
    .line 47
    .line 48
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 49
    .line 50
    return-object p1

    .line 51
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 52
    .line 53
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 54
    .line 55
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 56
    .line 57
    .line 58
    throw p1
.end method
