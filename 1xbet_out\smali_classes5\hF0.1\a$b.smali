.class public final LhF0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LhF0/g;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LhF0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LhF0/a$b$a;,
        LhF0/a$b$b;
    }
.end annotation


# instance fields
.field public final a:LhF0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LdF0/c;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LdF0/a;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/heat_map/impl/data/repository/HeatMapStatisticsRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LkF0/c;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LkF0/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LkF0/e;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/f;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTn/a;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/GetSportUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/l;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/l;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/heat_map/impl/presentation/viewmodel/HeatMapStatisticViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LSX0/a;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LhF0/a$b;->a:LhF0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p15}, LhF0/a$b;->c(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LSX0/a;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LSX0/a;Lc8/h;LhF0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p15}, LhF0/a$b;-><init>(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LSX0/a;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/heat_map/impl/presentation/fragment/TeamHeatMapFragment;)V
    .locals 0

    .line 1
    return-void
.end method

.method public b(Lorg/xbet/statistic/heat_map/impl/presentation/fragment/HeatMapStatisticFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LhF0/a$b;->d(Lorg/xbet/statistic/heat_map/impl/presentation/fragment/HeatMapStatisticFragment;)Lorg/xbet/statistic/heat_map/impl/presentation/fragment/HeatMapStatisticFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final c(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LSX0/a;Lc8/h;)V
    .locals 7

    .line 1
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    iput-object p3, p0, LhF0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p3}, LdF0/d;->a(LBc/a;)LdF0/d;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    iput-object p3, p0, LhF0/a$b;->c:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {}, LhF0/f;->a()LhF0/f;

    .line 14
    .line 15
    .line 16
    move-result-object p3

    .line 17
    invoke-static {p3}, Ldagger/internal/c;->d(Ldagger/internal/h;)Ldagger/internal/h;

    .line 18
    .line 19
    .line 20
    move-result-object p3

    .line 21
    iput-object p3, p0, LhF0/a$b;->d:Ldagger/internal/h;

    .line 22
    .line 23
    invoke-static/range {p15 .. p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 24
    .line 25
    .line 26
    move-result-object p3

    .line 27
    iput-object p3, p0, LhF0/a$b;->e:Ldagger/internal/h;

    .line 28
    .line 29
    new-instance p3, LhF0/a$b$a;

    .line 30
    .line 31
    invoke-direct {p3, p1}, LhF0/a$b$a;-><init>(LQW0/c;)V

    .line 32
    .line 33
    .line 34
    iput-object p3, p0, LhF0/a$b;->f:Ldagger/internal/h;

    .line 35
    .line 36
    iget-object p1, p0, LhF0/a$b;->c:Ldagger/internal/h;

    .line 37
    .line 38
    iget-object v0, p0, LhF0/a$b;->d:Ldagger/internal/h;

    .line 39
    .line 40
    iget-object v1, p0, LhF0/a$b;->e:Ldagger/internal/h;

    .line 41
    .line 42
    invoke-static {p1, v0, v1, p3}, Lorg/xbet/statistic/heat_map/impl/data/repository/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/heat_map/impl/data/repository/a;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    iput-object p1, p0, LhF0/a$b;->g:Ldagger/internal/h;

    .line 47
    .line 48
    invoke-static {p1}, LkF0/d;->a(LBc/a;)LkF0/d;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iput-object p1, p0, LhF0/a$b;->h:Ldagger/internal/h;

    .line 53
    .line 54
    iget-object p1, p0, LhF0/a$b;->g:Ldagger/internal/h;

    .line 55
    .line 56
    invoke-static {p1}, LkF0/b;->a(LBc/a;)LkF0/b;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    iput-object p1, p0, LhF0/a$b;->i:Ldagger/internal/h;

    .line 61
    .line 62
    iget-object p1, p0, LhF0/a$b;->g:Ldagger/internal/h;

    .line 63
    .line 64
    invoke-static {p1}, LkF0/f;->a(LBc/a;)LkF0/f;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    iput-object p1, p0, LhF0/a$b;->j:Ldagger/internal/h;

    .line 69
    .line 70
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    iput-object p1, p0, LhF0/a$b;->k:Ldagger/internal/h;

    .line 75
    .line 76
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    iput-object p1, p0, LhF0/a$b;->l:Ldagger/internal/h;

    .line 81
    .line 82
    invoke-static/range {p14 .. p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    iput-object p1, p0, LhF0/a$b;->m:Ldagger/internal/h;

    .line 87
    .line 88
    new-instance p1, LhF0/a$b$b;

    .line 89
    .line 90
    invoke-direct {p1, p2}, LhF0/a$b$b;-><init>(LEN0/f;)V

    .line 91
    .line 92
    .line 93
    iput-object p1, p0, LhF0/a$b;->n:Ldagger/internal/h;

    .line 94
    .line 95
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/g;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/g;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    iput-object p1, p0, LhF0/a$b;->o:Ldagger/internal/h;

    .line 100
    .line 101
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    iput-object p1, p0, LhF0/a$b;->p:Ldagger/internal/h;

    .line 106
    .line 107
    iget-object p2, p0, LhF0/a$b;->f:Ldagger/internal/h;

    .line 108
    .line 109
    invoke-static {p2, p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/h;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/h;

    .line 110
    .line 111
    .line 112
    move-result-object p1

    .line 113
    iput-object p1, p0, LhF0/a$b;->q:Ldagger/internal/h;

    .line 114
    .line 115
    iget-object p1, p0, LhF0/a$b;->n:Ldagger/internal/h;

    .line 116
    .line 117
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/j;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/j;

    .line 118
    .line 119
    .line 120
    move-result-object p1

    .line 121
    iput-object p1, p0, LhF0/a$b;->r:Ldagger/internal/h;

    .line 122
    .line 123
    iget-object p1, p0, LhF0/a$b;->n:Ldagger/internal/h;

    .line 124
    .line 125
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/m;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/m;

    .line 126
    .line 127
    .line 128
    move-result-object p1

    .line 129
    iput-object p1, p0, LhF0/a$b;->s:Ldagger/internal/h;

    .line 130
    .line 131
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 132
    .line 133
    .line 134
    move-result-object p1

    .line 135
    iput-object p1, p0, LhF0/a$b;->t:Ldagger/internal/h;

    .line 136
    .line 137
    iget-object p2, p0, LhF0/a$b;->o:Ldagger/internal/h;

    .line 138
    .line 139
    iget-object p3, p0, LhF0/a$b;->q:Ldagger/internal/h;

    .line 140
    .line 141
    iget-object p4, p0, LhF0/a$b;->r:Ldagger/internal/h;

    .line 142
    .line 143
    iget-object v0, p0, LhF0/a$b;->l:Ldagger/internal/h;

    .line 144
    .line 145
    iget-object v1, p0, LhF0/a$b;->s:Ldagger/internal/h;

    .line 146
    .line 147
    iget-object v2, p0, LhF0/a$b;->k:Ldagger/internal/h;

    .line 148
    .line 149
    move-object p7, p1

    .line 150
    move-object p5, v0

    .line 151
    move-object p6, v1

    .line 152
    move-object p8, v2

    .line 153
    invoke-static/range {p2 .. p8}, Lorg/xbet/statistic/statistic_core/presentation/delegates/o;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/o;

    .line 154
    .line 155
    .line 156
    move-result-object p1

    .line 157
    iput-object p1, p0, LhF0/a$b;->u:Ldagger/internal/h;

    .line 158
    .line 159
    invoke-static/range {p11 .. p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 160
    .line 161
    .line 162
    move-result-object p1

    .line 163
    iput-object p1, p0, LhF0/a$b;->v:Ldagger/internal/h;

    .line 164
    .line 165
    invoke-static/range {p13 .. p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 166
    .line 167
    .line 168
    move-result-object p1

    .line 169
    iput-object p1, p0, LhF0/a$b;->w:Ldagger/internal/h;

    .line 170
    .line 171
    invoke-static/range {p12 .. p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 172
    .line 173
    .line 174
    move-result-object p1

    .line 175
    iput-object p1, p0, LhF0/a$b;->x:Ldagger/internal/h;

    .line 176
    .line 177
    iget-object p2, p0, LhF0/a$b;->h:Ldagger/internal/h;

    .line 178
    .line 179
    iget-object p3, p0, LhF0/a$b;->i:Ldagger/internal/h;

    .line 180
    .line 181
    iget-object p4, p0, LhF0/a$b;->j:Ldagger/internal/h;

    .line 182
    .line 183
    iget-object v0, p0, LhF0/a$b;->k:Ldagger/internal/h;

    .line 184
    .line 185
    iget-object v1, p0, LhF0/a$b;->l:Ldagger/internal/h;

    .line 186
    .line 187
    iget-object v2, p0, LhF0/a$b;->m:Ldagger/internal/h;

    .line 188
    .line 189
    iget-object v3, p0, LhF0/a$b;->u:Ldagger/internal/h;

    .line 190
    .line 191
    iget-object v4, p0, LhF0/a$b;->v:Ldagger/internal/h;

    .line 192
    .line 193
    iget-object v5, p0, LhF0/a$b;->w:Ldagger/internal/h;

    .line 194
    .line 195
    iget-object v6, p0, LhF0/a$b;->f:Ldagger/internal/h;

    .line 196
    .line 197
    move-object/from16 p11, p1

    .line 198
    .line 199
    move-object p5, v0

    .line 200
    move-object p6, v1

    .line 201
    move-object p7, v2

    .line 202
    move-object p8, v3

    .line 203
    move-object/from16 p9, v4

    .line 204
    .line 205
    move-object/from16 p10, v5

    .line 206
    .line 207
    move-object/from16 p12, v6

    .line 208
    .line 209
    invoke-static/range {p2 .. p12}, Lorg/xbet/statistic/heat_map/impl/presentation/viewmodel/c;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/heat_map/impl/presentation/viewmodel/c;

    .line 210
    .line 211
    .line 212
    move-result-object p1

    .line 213
    iput-object p1, p0, LhF0/a$b;->y:Ldagger/internal/h;

    .line 214
    .line 215
    return-void
.end method

.method public final d(Lorg/xbet/statistic/heat_map/impl/presentation/fragment/HeatMapStatisticFragment;)Lorg/xbet/statistic/heat_map/impl/presentation/fragment/HeatMapStatisticFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LhF0/a$b;->f()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/heat_map/impl/presentation/fragment/c;->a(Lorg/xbet/statistic/heat_map/impl/presentation/fragment/HeatMapStatisticFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final e()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/statistic/heat_map/impl/presentation/viewmodel/HeatMapStatisticViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LhF0/a$b;->y:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final f()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LhF0/a$b;->e()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
