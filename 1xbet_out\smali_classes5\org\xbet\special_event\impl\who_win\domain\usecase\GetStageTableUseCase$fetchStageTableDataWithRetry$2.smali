.class final Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.who_win.domain.usecase.GetStageTableUseCase$fetchStageTableDataWithRetry$2"
    f = "GetStageTableUseCase.kt"
    l = {
        0x4c
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->e(ILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;ZZLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/Integer;",
        "Lkotlin/coroutines/e<",
        "-",
        "Ljava/util/List<",
        "+",
        "LDy0/a;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "",
        "it",
        "",
        "LDy0/a;",
        "<anonymous>",
        "(I)Ljava/util/List;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $enCoefView:Lorg/xbet/coef_type/api/domain/models/EnCoefView;

.field final synthetic $eventId:I

.field final synthetic $hasAuthorized:Z

.field final synthetic $userRegistrationCountryId:Ljava/lang/Integer;

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;ZILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
            "ZI",
            "Ljava/lang/Integer;",
            "Lorg/xbet/coef_type/api/domain/models/EnCoefView;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    iput-boolean p2, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->$hasAuthorized:Z

    iput p3, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->$eventId:I

    iput-object p4, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->$userRegistrationCountryId:Ljava/lang/Integer;

    iput-object p5, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->$enCoefView:Lorg/xbet/coef_type/api/domain/models/EnCoefView;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p6}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    iget-boolean v2, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->$hasAuthorized:Z

    iget v3, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->$eventId:I

    iget-object v4, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->$userRegistrationCountryId:Ljava/lang/Integer;

    iget-object v5, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->$enCoefView:Lorg/xbet/coef_type/api/domain/models/EnCoefView;

    move-object v6, p2

    invoke-direct/range {v0 .. v6}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;-><init>(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;ZILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public final invoke(ILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "+",
            "LDy0/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, Ljava/lang/Number;

    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    move-result p1

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->invoke(ILkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    return-object p1

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->b(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;)LEy0/a;

    .line 30
    .line 31
    .line 32
    move-result-object v3

    .line 33
    iget-boolean p1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->$hasAuthorized:Z

    .line 34
    .line 35
    const/4 v1, 0x0

    .line 36
    if-eqz p1, :cond_2

    .line 37
    .line 38
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 39
    .line 40
    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->c(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;)Lo9/a;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    invoke-interface {p1, v1}, Lo9/a;->d(Z)Z

    .line 45
    .line 46
    .line 47
    move-result v1

    .line 48
    move v8, v1

    .line 49
    goto :goto_0

    .line 50
    :cond_2
    const/4 v8, 0x0

    .line 51
    :goto_0
    iget-boolean p1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->$hasAuthorized:Z

    .line 52
    .line 53
    if-eqz p1, :cond_3

    .line 54
    .line 55
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 56
    .line 57
    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->c(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;)Lo9/a;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    invoke-interface {p1}, Lo9/a;->E()Lcom/xbet/onexuser/domain/user/model/UserInfo;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/user/model/UserInfo;->getUserId()J

    .line 66
    .line 67
    .line 68
    move-result-wide v4

    .line 69
    invoke-static {v4, v5}, LHc/a;->f(J)Ljava/lang/Long;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    :goto_1
    move-object v7, p1

    .line 74
    goto :goto_2

    .line 75
    :cond_3
    const/4 p1, 0x0

    .line 76
    goto :goto_1

    .line 77
    :goto_2
    iget v4, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->$eventId:I

    .line 78
    .line 79
    iget-object v5, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->$userRegistrationCountryId:Ljava/lang/Integer;

    .line 80
    .line 81
    iget-object v6, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->$enCoefView:Lorg/xbet/coef_type/api/domain/models/EnCoefView;

    .line 82
    .line 83
    iput v2, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;->label:I

    .line 84
    .line 85
    move-object v9, p0

    .line 86
    invoke-interface/range {v3 .. v9}, LEy0/a;->b(ILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;Ljava/lang/Long;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    if-ne p1, v0, :cond_4

    .line 91
    .line 92
    return-object v0

    .line 93
    :cond_4
    return-object p1
.end method
