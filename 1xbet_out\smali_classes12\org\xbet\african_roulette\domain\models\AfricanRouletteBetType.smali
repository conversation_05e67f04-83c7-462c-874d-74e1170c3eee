.class public final enum Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType$a;,
        Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType$b;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u001a\u0008\u0086\u0081\u0002\u0018\u0000 \u000b2\u0008\u0012\u0004\u0012\u00020\u00000\u0001:\u0001\u000cB\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\r\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\r\u0010\u0007\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0007\u0010\u0006J\r\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\t\u0010\nj\u0002\u0008\rj\u0002\u0008\u000ej\u0002\u0008\u000fj\u0002\u0008\u0010j\u0002\u0008\u0011j\u0002\u0008\u0012j\u0002\u0008\u0013j\u0002\u0008\u0014j\u0002\u0008\u0015j\u0002\u0008\u0016j\u0002\u0008\u0017j\u0002\u0008\u0018j\u0002\u0008\u0019j\u0002\u0008\u001aj\u0002\u0008\u001bj\u0002\u0008\u001cj\u0002\u0008\u001dj\u0002\u0008\u001ej\u0002\u0008\u001fj\u0002\u0008 j\u0002\u0008!\u00a8\u0006\""
    }
    d2 = {
        "Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "",
        "isNotEmpty",
        "()Z",
        "isEmpty",
        "",
        "getBetText",
        "()Ljava/lang/String;",
        "Companion",
        "a",
        "ZERO",
        "ONE",
        "TWO",
        "THREE",
        "FOUR",
        "FIVE",
        "SIX",
        "SEVEN",
        "EIGHT",
        "NINE",
        "TEN",
        "ELEVEN",
        "TWELVE",
        "FIRST_HALF",
        "LAST_HALF",
        "LOW",
        "MIDDLE",
        "HIGH",
        "RED",
        "BLACK",
        "EMPTY",
        "african_roulette_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum BLACK:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final Companion:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final enum EIGHT:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum ELEVEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum EMPTY:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum FIRST_HALF:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum FIVE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum FOUR:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum HIGH:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum LAST_HALF:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum LOW:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum MIDDLE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum NINE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum ONE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum RED:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum SEVEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum SIX:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum TEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum THREE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum TWELVE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum TWO:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

.field public static final enum ZERO:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 2
    .line 3
    const-string v1, "ZERO"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->ZERO:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 12
    .line 13
    const-string v1, "ONE"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->ONE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 20
    .line 21
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 22
    .line 23
    const-string v1, "TWO"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->TWO:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 30
    .line 31
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 32
    .line 33
    const-string v1, "THREE"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->THREE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 42
    .line 43
    const-string v1, "FOUR"

    .line 44
    .line 45
    const/4 v2, 0x4

    .line 46
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 47
    .line 48
    .line 49
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->FOUR:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 50
    .line 51
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 52
    .line 53
    const-string v1, "FIVE"

    .line 54
    .line 55
    const/4 v2, 0x5

    .line 56
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->FIVE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 60
    .line 61
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 62
    .line 63
    const-string v1, "SIX"

    .line 64
    .line 65
    const/4 v2, 0x6

    .line 66
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 67
    .line 68
    .line 69
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->SIX:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 70
    .line 71
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 72
    .line 73
    const-string v1, "SEVEN"

    .line 74
    .line 75
    const/4 v2, 0x7

    .line 76
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 77
    .line 78
    .line 79
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->SEVEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 80
    .line 81
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 82
    .line 83
    const-string v1, "EIGHT"

    .line 84
    .line 85
    const/16 v2, 0x8

    .line 86
    .line 87
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 88
    .line 89
    .line 90
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->EIGHT:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 91
    .line 92
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 93
    .line 94
    const-string v1, "NINE"

    .line 95
    .line 96
    const/16 v2, 0x9

    .line 97
    .line 98
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 99
    .line 100
    .line 101
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->NINE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 102
    .line 103
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 104
    .line 105
    const-string v1, "TEN"

    .line 106
    .line 107
    const/16 v2, 0xa

    .line 108
    .line 109
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 110
    .line 111
    .line 112
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->TEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 113
    .line 114
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 115
    .line 116
    const-string v1, "ELEVEN"

    .line 117
    .line 118
    const/16 v2, 0xb

    .line 119
    .line 120
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 121
    .line 122
    .line 123
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->ELEVEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 124
    .line 125
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 126
    .line 127
    const-string v1, "TWELVE"

    .line 128
    .line 129
    const/16 v2, 0xc

    .line 130
    .line 131
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 132
    .line 133
    .line 134
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->TWELVE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 135
    .line 136
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 137
    .line 138
    const-string v1, "FIRST_HALF"

    .line 139
    .line 140
    const/16 v2, 0xd

    .line 141
    .line 142
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 143
    .line 144
    .line 145
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->FIRST_HALF:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 146
    .line 147
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 148
    .line 149
    const-string v1, "LAST_HALF"

    .line 150
    .line 151
    const/16 v2, 0xe

    .line 152
    .line 153
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 154
    .line 155
    .line 156
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->LAST_HALF:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 157
    .line 158
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 159
    .line 160
    const-string v1, "LOW"

    .line 161
    .line 162
    const/16 v2, 0xf

    .line 163
    .line 164
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 165
    .line 166
    .line 167
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->LOW:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 168
    .line 169
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 170
    .line 171
    const-string v1, "MIDDLE"

    .line 172
    .line 173
    const/16 v2, 0x10

    .line 174
    .line 175
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 176
    .line 177
    .line 178
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->MIDDLE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 179
    .line 180
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 181
    .line 182
    const-string v1, "HIGH"

    .line 183
    .line 184
    const/16 v2, 0x11

    .line 185
    .line 186
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 187
    .line 188
    .line 189
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->HIGH:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 190
    .line 191
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 192
    .line 193
    const-string v1, "RED"

    .line 194
    .line 195
    const/16 v2, 0x12

    .line 196
    .line 197
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 198
    .line 199
    .line 200
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->RED:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 201
    .line 202
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 203
    .line 204
    const-string v1, "BLACK"

    .line 205
    .line 206
    const/16 v2, 0x13

    .line 207
    .line 208
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 209
    .line 210
    .line 211
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->BLACK:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 212
    .line 213
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 214
    .line 215
    const-string v1, "EMPTY"

    .line 216
    .line 217
    const/16 v2, 0x14

    .line 218
    .line 219
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;-><init>(Ljava/lang/String;I)V

    .line 220
    .line 221
    .line 222
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->EMPTY:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 223
    .line 224
    invoke-static {}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->a()[Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 225
    .line 226
    .line 227
    move-result-object v0

    .line 228
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->$VALUES:[Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 229
    .line 230
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 231
    .line 232
    .line 233
    move-result-object v0

    .line 234
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->$ENTRIES:Lkotlin/enums/a;

    .line 235
    .line 236
    new-instance v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType$a;

    .line 237
    .line 238
    const/4 v1, 0x0

    .line 239
    invoke-direct {v0, v1}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 240
    .line 241
    .line 242
    sput-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->Companion:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType$a;

    .line 243
    .line 244
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
    .locals 3

    .line 1
    const/16 v0, 0x15

    new-array v0, v0, [Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->ZERO:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->ONE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->TWO:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->THREE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->FOUR:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->FIVE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->SIX:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->SEVEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->EIGHT:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/16 v2, 0x8

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->NINE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/16 v2, 0x9

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->TEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/16 v2, 0xa

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->ELEVEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/16 v2, 0xb

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->TWELVE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/16 v2, 0xc

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->FIRST_HALF:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/16 v2, 0xd

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->LAST_HALF:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/16 v2, 0xe

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->LOW:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/16 v2, 0xf

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->MIDDLE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/16 v2, 0x10

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->HIGH:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/16 v2, 0x11

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->RED:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/16 v2, 0x12

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->BLACK:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/16 v2, 0x13

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->EMPTY:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    const/16 v2, 0x14

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->$VALUES:[Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getBetText()Ljava/lang/String;
    .locals 2
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType$b;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    packed-switch v0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw v0

    .line 18
    :pswitch_0
    const-string v0, ""

    .line 19
    .line 20
    return-object v0

    .line 21
    :pswitch_1
    const-string v0, "BLACK"

    .line 22
    .line 23
    return-object v0

    .line 24
    :pswitch_2
    const-string v0, "RED"

    .line 25
    .line 26
    return-object v0

    .line 27
    :pswitch_3
    const-string v0, "HI"

    .line 28
    .line 29
    return-object v0

    .line 30
    :pswitch_4
    const-string v0, "MID"

    .line 31
    .line 32
    return-object v0

    .line 33
    :pswitch_5
    const-string v0, "LO"

    .line 34
    .line 35
    return-object v0

    .line 36
    :pswitch_6
    const-string v0, "7-12"

    .line 37
    .line 38
    return-object v0

    .line 39
    :pswitch_7
    const-string v0, "1-6"

    .line 40
    .line 41
    return-object v0

    .line 42
    :pswitch_8
    const-string v0, "12"

    .line 43
    .line 44
    return-object v0

    .line 45
    :pswitch_9
    const-string v0, "11"

    .line 46
    .line 47
    return-object v0

    .line 48
    :pswitch_a
    const-string v0, "10"

    .line 49
    .line 50
    return-object v0

    .line 51
    :pswitch_b
    const-string v0, "9"

    .line 52
    .line 53
    return-object v0

    .line 54
    :pswitch_c
    const-string v0, "8"

    .line 55
    .line 56
    return-object v0

    .line 57
    :pswitch_d
    const-string v0, "7"

    .line 58
    .line 59
    return-object v0

    .line 60
    :pswitch_e
    const-string v0, "6"

    .line 61
    .line 62
    return-object v0

    .line 63
    :pswitch_f
    const-string v0, "5"

    .line 64
    .line 65
    return-object v0

    .line 66
    :pswitch_10
    const-string v0, "4"

    .line 67
    .line 68
    return-object v0

    .line 69
    :pswitch_11
    const-string v0, "3"

    .line 70
    .line 71
    return-object v0

    .line 72
    :pswitch_12
    const-string v0, "2"

    .line 73
    .line 74
    return-object v0

    .line 75
    :pswitch_13
    const-string v0, "1"

    .line 76
    .line 77
    return-object v0

    .line 78
    :pswitch_14
    const-string v0, "0"

    .line 79
    .line 80
    return-object v0

    .line 81
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_14
        :pswitch_13
        :pswitch_12
        :pswitch_11
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final isEmpty()Z
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->EMPTY:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 2
    .line 3
    if-ne p0, v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x1

    .line 6
    return v0

    .line 7
    :cond_0
    const/4 v0, 0x0

    .line 8
    return v0
.end method

.method public final isNotEmpty()Z
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->EMPTY:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 2
    .line 3
    if-eq p0, v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x1

    .line 6
    return v0

    .line 7
    :cond_0
    const/4 v0, 0x0

    .line 8
    return v0
.end method
