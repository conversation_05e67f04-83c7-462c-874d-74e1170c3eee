.class public final Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsChipAdapterDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001aC\u0010\n\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\t0\u00080\u00072\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u00002\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0005H\u0000\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "Lkotlin/Function2;",
        "Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
        "",
        "",
        "clickListener",
        "Lkotlin/Function0;",
        "getCheckedIndex",
        "LA4/c;",
        "",
        "Lga1/a;",
        "e",
        "(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;)LA4/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lkotlin/jvm/functions/Function2;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsChipAdapterDelegateKt;->h(Lkotlin/jvm/functions/Function2;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/s1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsChipAdapterDelegateKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/s1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsChipAdapterDelegateKt;->g(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LB4/a;Lkotlin/jvm/functions/Function0;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsChipAdapterDelegateKt;->i(LB4/a;Lkotlin/jvm/functions/Function0;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;)LA4/c;
    .locals 3
    .param p0    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Ljava/lang/Integer;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "Lga1/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lba1/z;

    .line 2
    .line 3
    invoke-direct {v0}, Lba1/z;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lba1/A;

    .line 7
    .line 8
    invoke-direct {v1, p0, p1}, Lba1/A;-><init>(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsChipAdapterDelegateKt$giftsChipAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsChipAdapterDelegateKt$giftsChipAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsChipAdapterDelegateKt$giftsChipAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsChipAdapterDelegateKt$giftsChipAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v2, LB4/b;

    .line 19
    .line 20
    invoke-direct {v2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v2
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/s1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/s1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/s1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 2
    .line 3
    new-instance v1, Lba1/B;

    .line 4
    .line 5
    invoke-direct {v1, p0, p2}, Lba1/B;-><init>(Lkotlin/jvm/functions/Function2;LB4/a;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lba1/C;

    .line 12
    .line 13
    invoke-direct {p0, p2, p1}, Lba1/C;-><init>(LB4/a;Lkotlin/jvm/functions/Function0;)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p2, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 17
    .line 18
    .line 19
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 20
    .line 21
    return-object p0
.end method

.method public static final h(Lkotlin/jvm/functions/Function2;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, Lga1/a;

    .line 6
    .line 7
    invoke-virtual {p2}, Lga1/a;->d()Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getAbsoluteAdapterPosition()I

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-interface {p0, p2, p1}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final i(LB4/a;Lkotlin/jvm/functions/Function0;Ljava/util/List;)Lkotlin/Unit;
    .locals 3

    .line 1
    iget-object p2, p0, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 2
    .line 3
    const/4 v0, 0x1

    .line 4
    invoke-virtual {p2, v0}, Landroid/view/View;->setClickable(Z)V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    check-cast p2, LS91/s1;

    .line 12
    .line 13
    iget-object p2, p2, LS91/s1;->b:Lorg/xbet/uikit/components/chips/Chip;

    .line 14
    .line 15
    iget-object v1, p0, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 16
    .line 17
    invoke-virtual {v1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    check-cast v2, Lga1/a;

    .line 26
    .line 27
    invoke-virtual {v2}, Lga1/a;->d()Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 28
    .line 29
    .line 30
    move-result-object v2

    .line 31
    invoke-static {v2}, Lga1/b;->a(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;)I

    .line 32
    .line 33
    .line 34
    move-result v2

    .line 35
    invoke-virtual {v1, v2}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    invoke-virtual {p2, v1}, Lorg/xbet/uikit/components/chips/Chip;->setText(Ljava/lang/CharSequence;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    move-result-object p2

    .line 46
    check-cast p2, Lga1/a;

    .line 47
    .line 48
    invoke-virtual {p2}, Lga1/a;->e()I

    .line 49
    .line 50
    .line 51
    move-result p2

    .line 52
    if-eqz p2, :cond_0

    .line 53
    .line 54
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 55
    .line 56
    .line 57
    move-result-object p2

    .line 58
    check-cast p2, LS91/s1;

    .line 59
    .line 60
    iget-object p2, p2, LS91/s1;->b:Lorg/xbet/uikit/components/chips/Chip;

    .line 61
    .line 62
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    check-cast v1, Lga1/a;

    .line 67
    .line 68
    invoke-virtual {v1}, Lga1/a;->e()I

    .line 69
    .line 70
    .line 71
    move-result v1

    .line 72
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 73
    .line 74
    .line 75
    move-result-object v1

    .line 76
    invoke-virtual {p2, v1}, Lorg/xbet/uikit/components/chips/Chip;->setCount(Ljava/lang/Integer;)V

    .line 77
    .line 78
    .line 79
    goto :goto_0

    .line 80
    :cond_0
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 81
    .line 82
    .line 83
    move-result-object p2

    .line 84
    check-cast p2, LS91/s1;

    .line 85
    .line 86
    iget-object p2, p2, LS91/s1;->b:Lorg/xbet/uikit/components/chips/Chip;

    .line 87
    .line 88
    const/4 v1, 0x0

    .line 89
    invoke-virtual {p2, v1}, Lorg/xbet/uikit/components/chips/Chip;->setCount(Ljava/lang/Integer;)V

    .line 90
    .line 91
    .line 92
    :goto_0
    invoke-interface {p1}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    check-cast p1, Ljava/lang/Number;

    .line 97
    .line 98
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    .line 99
    .line 100
    .line 101
    move-result p1

    .line 102
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView$D;->getAbsoluteAdapterPosition()I

    .line 103
    .line 104
    .line 105
    move-result p2

    .line 106
    if-ne p1, p2, :cond_1

    .line 107
    .line 108
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 109
    .line 110
    .line 111
    move-result-object p0

    .line 112
    check-cast p0, LS91/s1;

    .line 113
    .line 114
    iget-object p0, p0, LS91/s1;->b:Lorg/xbet/uikit/components/chips/Chip;

    .line 115
    .line 116
    invoke-virtual {p0, v0}, Lorg/xbet/uikit/components/chips/Chip;->setSelected(Z)V

    .line 117
    .line 118
    .line 119
    goto :goto_1

    .line 120
    :cond_1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 121
    .line 122
    .line 123
    move-result-object p0

    .line 124
    check-cast p0, LS91/s1;

    .line 125
    .line 126
    iget-object p0, p0, LS91/s1;->b:Lorg/xbet/uikit/components/chips/Chip;

    .line 127
    .line 128
    const/4 p1, 0x0

    .line 129
    invoke-virtual {p0, p1}, Lorg/xbet/uikit/components/chips/Chip;->setSelected(Z)V

    .line 130
    .line 131
    .line 132
    :goto_1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 133
    .line 134
    return-object p0
.end method
