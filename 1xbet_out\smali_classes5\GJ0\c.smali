.class public interface abstract LGJ0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LGJ0/c$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008a\u0018\u00002\u00020\u0001:\u0001\u0008J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0017\u0010\u0008\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0007H&\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\u000b\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\nH&\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "LGJ0/c;",
        "",
        "Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/PlayersStatisticCricketResultsFragment;",
        "fragment",
        "",
        "b",
        "(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/PlayersStatisticCricketResultsFragment;)V",
        "Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/AdditionalInfoBottomSheetDialogFragment;",
        "a",
        "(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/AdditionalInfoBottomSheetDialogFragment;)V",
        "Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/ChoseTableDataTypeBottomSheetDialogFragment;",
        "c",
        "(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/ChoseTableDataTypeBottomSheetDialogFragment;)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/AdditionalInfoBottomSheetDialogFragment;)V
    .param p1    # Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/AdditionalInfoBottomSheetDialogFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract b(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/PlayersStatisticCricketResultsFragment;)V
    .param p1    # Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/PlayersStatisticCricketResultsFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract c(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/ChoseTableDataTypeBottomSheetDialogFragment;)V
    .param p1    # Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/ChoseTableDataTypeBottomSheetDialogFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method
