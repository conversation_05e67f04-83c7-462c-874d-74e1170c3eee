.class public final LHN0/c;
.super Landroidx/recyclerview/widget/RecyclerView$o;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u0008\u0007\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J/\u0010\r\u001a\u00020\u000c2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008\r\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "LHN0/c;",
        "Landroidx/recyclerview/widget/RecyclerView$o;",
        "<init>",
        "()V",
        "Landroid/graphics/Rect;",
        "outRect",
        "Landroid/view/View;",
        "view",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "parent",
        "Landroidx/recyclerview/widget/RecyclerView$z;",
        "state",
        "",
        "getItemOffsets",
        "(Landroid/graphics/Rect;Landroid/view/View;Landroidx/recyclerview/widget/RecyclerView;Landroidx/recyclerview/widget/RecyclerView$z;)V",
        "statistic_core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Landroidx/recyclerview/widget/RecyclerView$o;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public getItemOffsets(Landroid/graphics/Rect;Landroid/view/View;Landroidx/recyclerview/widget/RecyclerView;Landroidx/recyclerview/widget/RecyclerView$z;)V
    .locals 5
    .param p1    # Landroid/graphics/Rect;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Landroidx/recyclerview/widget/RecyclerView;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Landroidx/recyclerview/widget/RecyclerView$z;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p3, p2}, Landroidx/recyclerview/widget/RecyclerView;->getChildViewHolder(Landroid/view/View;)Landroidx/recyclerview/widget/RecyclerView$D;

    .line 2
    .line 3
    .line 4
    move-result-object p4

    .line 5
    instance-of v0, p4, LB4/a;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    check-cast p4, LB4/a;

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object p4, v1

    .line 14
    :goto_0
    if-nez p4, :cond_1

    .line 15
    .line 16
    goto :goto_3

    .line 17
    :cond_1
    invoke-virtual {p3, p2}, Landroidx/recyclerview/widget/RecyclerView;->getChildAdapterPosition(Landroid/view/View;)I

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    const/4 v2, 0x0

    .line 22
    const/4 v3, 0x1

    .line 23
    if-nez v0, :cond_2

    .line 24
    .line 25
    const/4 v4, 0x1

    .line 26
    goto :goto_1

    .line 27
    :cond_2
    const/4 v4, 0x0

    .line 28
    :goto_1
    invoke-virtual {p3}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 29
    .line 30
    .line 31
    move-result-object p3

    .line 32
    if-eqz p3, :cond_3

    .line 33
    .line 34
    invoke-virtual {p3}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 35
    .line 36
    .line 37
    move-result p3

    .line 38
    add-int/2addr v0, v3

    .line 39
    if-ne p3, v0, :cond_3

    .line 40
    .line 41
    const/4 p3, 0x1

    .line 42
    goto :goto_2

    .line 43
    :cond_3
    const/4 p3, 0x0

    .line 44
    :goto_2
    invoke-virtual {p4}, LB4/a;->e()LL2/a;

    .line 45
    .line 46
    .line 47
    move-result-object p4

    .line 48
    invoke-interface {p4}, LL2/a;->getRoot()Landroid/view/View;

    .line 49
    .line 50
    .line 51
    move-result-object p4

    .line 52
    instance-of v0, p4, Lorg/xbet/uikit/components/cells/SettingsCell;

    .line 53
    .line 54
    if-eqz v0, :cond_4

    .line 55
    .line 56
    move-object v1, p4

    .line 57
    check-cast v1, Lorg/xbet/uikit/components/cells/SettingsCell;

    .line 58
    .line 59
    :cond_4
    if-nez v1, :cond_5

    .line 60
    .line 61
    :goto_3
    return-void

    .line 62
    :cond_5
    if-eqz v4, :cond_6

    .line 63
    .line 64
    if-eqz p3, :cond_6

    .line 65
    .line 66
    invoke-virtual {v1, v3}, Lorg/xbet/uikit/components/cells/BaseCell;->setFirst(Z)V

    .line 67
    .line 68
    .line 69
    invoke-virtual {v1, v3}, Lorg/xbet/uikit/components/cells/BaseCell;->setLast(Z)V

    .line 70
    .line 71
    .line 72
    goto :goto_4

    .line 73
    :cond_6
    if-eqz v4, :cond_7

    .line 74
    .line 75
    invoke-virtual {v1, v3}, Lorg/xbet/uikit/components/cells/BaseCell;->setFirst(Z)V

    .line 76
    .line 77
    .line 78
    invoke-virtual {v1, v2}, Lorg/xbet/uikit/components/cells/BaseCell;->setLast(Z)V

    .line 79
    .line 80
    .line 81
    goto :goto_4

    .line 82
    :cond_7
    if-eqz p3, :cond_8

    .line 83
    .line 84
    invoke-virtual {v1, v2}, Lorg/xbet/uikit/components/cells/BaseCell;->setFirst(Z)V

    .line 85
    .line 86
    .line 87
    invoke-virtual {v1, v3}, Lorg/xbet/uikit/components/cells/BaseCell;->setLast(Z)V

    .line 88
    .line 89
    .line 90
    invoke-virtual {p2}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 91
    .line 92
    .line 93
    move-result-object p3

    .line 94
    sget p4, Lpb/f;->space_24:I

    .line 95
    .line 96
    invoke-virtual {p3, p4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 97
    .line 98
    .line 99
    move-result p3

    .line 100
    iput p3, p1, Landroid/graphics/Rect;->bottom:I

    .line 101
    .line 102
    goto :goto_4

    .line 103
    :cond_8
    invoke-virtual {v1, v2}, Lorg/xbet/uikit/components/cells/BaseCell;->setFirst(Z)V

    .line 104
    .line 105
    .line 106
    invoke-virtual {v1, v2}, Lorg/xbet/uikit/components/cells/BaseCell;->setLast(Z)V

    .line 107
    .line 108
    .line 109
    :goto_4
    invoke-virtual {p2}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 110
    .line 111
    .line 112
    move-result-object p3

    .line 113
    sget p4, LlZ0/g;->large_horizontal_margin_dynamic:I

    .line 114
    .line 115
    invoke-virtual {p3, p4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 116
    .line 117
    .line 118
    move-result p3

    .line 119
    iput p3, p1, Landroid/graphics/Rect;->left:I

    .line 120
    .line 121
    invoke-virtual {p2}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 122
    .line 123
    .line 124
    move-result-object p2

    .line 125
    sget p3, LlZ0/g;->large_horizontal_margin_dynamic:I

    .line 126
    .line 127
    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 128
    .line 129
    .line 130
    move-result p2

    .line 131
    iput p2, p1, Landroid/graphics/Rect;->right:I

    .line 132
    .line 133
    return-void
.end method
