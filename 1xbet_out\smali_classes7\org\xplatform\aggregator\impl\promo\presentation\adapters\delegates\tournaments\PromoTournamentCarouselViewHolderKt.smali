.class public final Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001aW\u0010\r\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000c0\u000b0\n2\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u00002\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00052\u000c\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00052\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\r\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "Lkotlin/Function2;",
        "",
        "",
        "",
        "onTournamentClick",
        "Lkotlin/Function0;",
        "onHeaderClick",
        "scrollToTopOnTournamentsLoaded",
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "LA4/c;",
        "",
        "LVX0/i;",
        "g",
        "(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;LUX0/k;)LA4/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lkotlin/jvm/functions/Function2;JLjava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt;->k(Lkotlin/jvm/functions/Function2;JLjava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt;->j(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic c(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt;->m(LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/A0;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt;->h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/A0;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function2;LUX0/k;Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt;->i(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function2;LUX0/k;Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(LB4/a;LUX0/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt;->l(LB4/a;LUX0/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;LUX0/k;)LA4/c;
    .locals 2
    .param p0    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LUX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "LUX0/k;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LDa1/e;

    .line 2
    .line 3
    invoke-direct {v0}, LDa1/e;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LDa1/f;

    .line 7
    .line 8
    invoke-direct {v1, p1, p0, p3, p2}, LDa1/f;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function2;LUX0/k;Lkotlin/jvm/functions/Function0;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$promoAggregatorTournamentCardsCollectionViewHolder$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$promoAggregatorTournamentCardsCollectionViewHolder$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$promoAggregatorTournamentCardsCollectionViewHolder$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$promoAggregatorTournamentCardsCollectionViewHolder$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance p2, LB4/b;

    .line 19
    .line 20
    invoke-direct {p2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object p2
.end method

.method public static final h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/A0;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/A0;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/A0;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final i(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function2;LUX0/k;Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p4}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LS91/A0;

    .line 6
    .line 7
    iget-object v1, v0, LS91/A0;->b:Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;

    .line 8
    .line 9
    new-instance v2, LDa1/g;

    .line 10
    .line 11
    invoke-direct {v2, p0}, LDa1/g;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {v1, v2}, Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;->setButtonClickListener(Landroid/view/View$OnClickListener;)V

    .line 15
    .line 16
    .line 17
    iget-object p0, v0, LS91/A0;->b:Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;

    .line 18
    .line 19
    new-instance v0, LDa1/h;

    .line 20
    .line 21
    invoke-direct {v0, p1}, LDa1/h;-><init>(Lkotlin/jvm/functions/Function2;)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function2;)V

    .line 25
    .line 26
    .line 27
    new-instance p0, LDa1/i;

    .line 28
    .line 29
    invoke-direct {p0, p4, p2}, LDa1/i;-><init>(LB4/a;LUX0/k;)V

    .line 30
    .line 31
    .line 32
    invoke-virtual {p4, p0}, LB4/a;->r(Lkotlin/jvm/functions/Function0;)V

    .line 33
    .line 34
    .line 35
    new-instance p0, LDa1/j;

    .line 36
    .line 37
    invoke-direct {p0, p2, p4}, LDa1/j;-><init>(LUX0/k;LB4/a;)V

    .line 38
    .line 39
    .line 40
    invoke-virtual {p4, p0}, LB4/a;->s(Lkotlin/jvm/functions/Function0;)V

    .line 41
    .line 42
    .line 43
    new-instance p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;

    .line 44
    .line 45
    invoke-direct {p0, p4, p3, p4}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;-><init>(LB4/a;Lkotlin/jvm/functions/Function0;LB4/a;)V

    .line 46
    .line 47
    .line 48
    invoke-virtual {p4, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 49
    .line 50
    .line 51
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 52
    .line 53
    return-object p0
.end method

.method public static final j(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final k(Lkotlin/jvm/functions/Function2;JLjava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-interface {p0, p1, p3}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final l(LB4/a;LUX0/k;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LS91/A0;

    .line 6
    .line 7
    iget-object v0, v0, LS91/A0;->b:Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;

    .line 8
    .line 9
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView$D;->getAbsoluteAdapterPosition()I

    .line 10
    .line 11
    .line 12
    move-result p0

    .line 13
    invoke-static {p0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    invoke-virtual {p1, p0}, LUX0/k;->b(Ljava/lang/String;)Landroid/os/Parcelable;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    invoke-virtual {v0, p0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;->t(Landroid/os/Parcelable;)V

    .line 22
    .line 23
    .line 24
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 25
    .line 26
    return-object p0
.end method

.method public static final m(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getAbsoluteAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, LS91/A0;

    .line 14
    .line 15
    iget-object p1, p1, LS91/A0;->b:Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;

    .line 16
    .line 17
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;->u()Landroid/os/Parcelable;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-virtual {p0, v0, p1}, LUX0/k;->d(Ljava/lang/String;Landroid/os/Parcelable;)V

    .line 22
    .line 23
    .line 24
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 25
    .line 26
    return-object p0
.end method
