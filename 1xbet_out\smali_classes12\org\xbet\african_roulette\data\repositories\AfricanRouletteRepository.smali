.class public final Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0090\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0006\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0010\u0007\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0018\u0000 \'2\u00020\u0001:\u0001FB1\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ6\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u000f\u001a\u00020\u000e2\u000c\u0010\u0012\u001a\u0008\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0016\u001a\u00020\u0015H\u0080@\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u001b\u0010\u001b\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00110\u00100\u001aH\u0000\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0017\u0010\u001f\u001a\u00020\u001e2\u0006\u0010\u001d\u001a\u00020\u0011H\u0000\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0017\u0010!\u001a\u00020\u001e2\u0006\u0010\u001d\u001a\u00020\u0011H\u0000\u00a2\u0006\u0004\u0008!\u0010 J\u000f\u0010#\u001a\u00020\"H\u0000\u00a2\u0006\u0004\u0008#\u0010$J\u000f\u0010%\u001a\u00020\u001eH\u0000\u00a2\u0006\u0004\u0008%\u0010&J\u000f\u0010\'\u001a\u00020\u001eH\u0000\u00a2\u0006\u0004\u0008\'\u0010&J\u0017\u0010*\u001a\u00020\u001e2\u0006\u0010)\u001a\u00020(H\u0000\u00a2\u0006\u0004\u0008*\u0010+J\u000f\u0010,\u001a\u00020(H\u0000\u00a2\u0006\u0004\u0008,\u0010-J\u000f\u0010.\u001a\u00020\u001eH\u0000\u00a2\u0006\u0004\u0008.\u0010&J\u000f\u00100\u001a\u00020/H\u0000\u00a2\u0006\u0004\u00080\u00101J\u0017\u00103\u001a\u00020\u001e2\u0006\u00102\u001a\u00020/H\u0000\u00a2\u0006\u0004\u00083\u00104J\u0017\u00106\u001a\u00020\u001e2\u0006\u00105\u001a\u00020\u0017H\u0000\u00a2\u0006\u0004\u00086\u00107J\u000f\u00108\u001a\u00020\u0017H\u0000\u00a2\u0006\u0004\u00088\u00109J\u0017\u0010<\u001a\u00020\u001e2\u0006\u0010;\u001a\u00020:H\u0000\u00a2\u0006\u0004\u0008<\u0010=J\u000f\u0010>\u001a\u00020:H\u0000\u00a2\u0006\u0004\u0008>\u0010?J\u000f\u0010@\u001a\u00020:H\u0000\u00a2\u0006\u0004\u0008@\u0010?J\u0017\u0010B\u001a\u00020A2\u0006\u00102\u001a\u00020/H\u0000\u00a2\u0006\u0004\u0008B\u0010CJ\u0015\u0010D\u001a\u0008\u0012\u0004\u0012\u00020\u00110\u0010H\u0000\u00a2\u0006\u0004\u0008D\u0010ER\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010GR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010HR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008I\u0010JR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010KR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010LR\u001a\u0010P\u001a\u0008\u0012\u0004\u0012\u00020N0M8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010O\u00a8\u0006Q"
    }
    d2 = {
        "Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;",
        "",
        "Lf8/g;",
        "serviceGenerator",
        "Ldg/c;",
        "africanRouletteGameModelMapper",
        "Ldg/a;",
        "africanRouletteBetRequestMapper",
        "Lbg/a;",
        "africanRouletteDataSource",
        "Lc8/h;",
        "requestParamsDataSource",
        "<init>",
        "(Lf8/g;Ldg/c;Ldg/a;Lbg/a;Lc8/h;)V",
        "",
        "token",
        "",
        "Lig/a;",
        "rouletteBets",
        "",
        "activeId",
        "Lorg/xbet/games_section/api/models/GameBonus;",
        "gameBonus",
        "Lig/b;",
        "o",
        "(Ljava/lang/String;Ljava/util/List;JLorg/xbet/games_section/api/models/GameBonus;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lkotlinx/coroutines/flow/e;",
        "l",
        "()Lkotlinx/coroutines/flow/e;",
        "bet",
        "",
        "q",
        "(Lig/a;)V",
        "b",
        "",
        "k",
        "()D",
        "p",
        "()V",
        "g",
        "Lorg/xbet/games_section/api/models/GameBonusType;",
        "bonus",
        "s",
        "(Lorg/xbet/games_section/api/models/GameBonusType;)V",
        "j",
        "()Lorg/xbet/games_section/api/models/GameBonusType;",
        "e",
        "Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
        "n",
        "()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
        "africanRouletteBetType",
        "u",
        "(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V",
        "africanRouletteGameModel",
        "r",
        "(Lig/b;)V",
        "h",
        "()Lig/b;",
        "",
        "offset",
        "t",
        "(F)V",
        "m",
        "()F",
        "d",
        "",
        "f",
        "(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)Z",
        "i",
        "()Ljava/util/List;",
        "a",
        "Lf8/g;",
        "Ldg/c;",
        "c",
        "Ldg/a;",
        "Lbg/a;",
        "Lc8/h;",
        "Lkotlin/Function0;",
        "Lcg/a;",
        "Lkotlin/jvm/functions/Function0;",
        "africanRouletteApi",
        "african_roulette_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final g:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Ldg/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Ldg/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lbg/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lcg/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->g:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$a;

    return-void
.end method

.method public constructor <init>(Lf8/g;Ldg/c;Ldg/a;Lbg/a;Lc8/h;)V
    .locals 0
    .param p1    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ldg/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ldg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lbg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->a:Lf8/g;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->b:Ldg/c;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->c:Ldg/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->e:Lc8/h;

    .line 13
    .line 14
    new-instance p1, Lorg/xbet/african_roulette/data/repositories/a;

    .line 15
    .line 16
    invoke-direct {p1, p0}, Lorg/xbet/african_roulette/data/repositories/a;-><init>(Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;)V

    .line 17
    .line 18
    .line 19
    iput-object p1, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->f:Lkotlin/jvm/functions/Function0;

    .line 20
    .line 21
    return-void
.end method

.method public static synthetic a(Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;)Lcg/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->c(Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;)Lcg/a;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;)Lcg/a;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->a:Lf8/g;

    .line 2
    .line 3
    const-class v0, Lcg/a;

    .line 4
    .line 5
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p0, v0}, Lf8/g;->c(Lkotlin/reflect/d;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, Lcg/a;

    .line 14
    .line 15
    return-object p0
.end method


# virtual methods
.method public final b(Lig/a;)V
    .locals 1
    .param p1    # Lig/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lbg/a;->a(Lig/a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final d()F
    .locals 3

    .line 1
    new-instance v0, Lkotlin/ranges/IntRange;

    .line 2
    .line 3
    const/16 v1, 0x14

    .line 4
    .line 5
    const/16 v2, 0xa0

    .line 6
    .line 7
    invoke-direct {v0, v1, v2}, Lkotlin/ranges/IntRange;-><init>(II)V

    .line 8
    .line 9
    .line 10
    sget-object v1, Lkotlin/random/Random;->Default:Lkotlin/random/Random$Default;

    .line 11
    .line 12
    invoke-static {v0, v1}, Lkotlin/ranges/f;->w(Lkotlin/ranges/IntRange;Lkotlin/random/Random;)I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    int-to-float v0, v0

    .line 17
    return v0
.end method

.method public final e()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lbg/a;->b()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final f(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)Z
    .locals 1
    .param p1    # Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lbg/a;->c(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public final g()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lbg/a;->d()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final h()Lig/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lbg/a;->e()Lig/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final i()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lig/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lbg/a;->f()Ljava/util/List;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final j()Lorg/xbet/games_section/api/models/GameBonusType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lbg/a;->g()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final k()D
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lbg/a;->h()D

    .line 4
    .line 5
    .line 6
    move-result-wide v0

    .line 7
    return-wide v0
.end method

.method public final l()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Lig/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lbg/a;->i()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final m()F
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lbg/a;->j()F

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public final n()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lbg/a;->k()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final o(Ljava/lang/String;Ljava/util/List;JLorg/xbet/games_section/api/models/GameBonus;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 21
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/games_section/api/models/GameBonus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lig/a;",
            ">;J",
            "Lorg/xbet/games_section/api/models/GameBonus;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lig/b;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p6

    .line 4
    .line 5
    instance-of v2, v1, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$play$1;

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    move-object v2, v1

    .line 10
    check-cast v2, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$play$1;

    .line 11
    .line 12
    iget v3, v2, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$play$1;->label:I

    .line 13
    .line 14
    const/high16 v4, -0x80000000

    .line 15
    .line 16
    and-int v5, v3, v4

    .line 17
    .line 18
    if-eqz v5, :cond_0

    .line 19
    .line 20
    sub-int/2addr v3, v4

    .line 21
    iput v3, v2, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$play$1;->label:I

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v2, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$play$1;

    .line 25
    .line 26
    invoke-direct {v2, v0, v1}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$play$1;-><init>(Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    :goto_0
    iget-object v1, v2, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$play$1;->result:Ljava/lang/Object;

    .line 30
    .line 31
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    iget v4, v2, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$play$1;->label:I

    .line 36
    .line 37
    const/4 v5, 0x1

    .line 38
    if-eqz v4, :cond_2

    .line 39
    .line 40
    if-ne v4, v5, :cond_1

    .line 41
    .line 42
    iget-object v2, v2, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$play$1;->L$0:Ljava/lang/Object;

    .line 43
    .line 44
    check-cast v2, Ldg/c;

    .line 45
    .line 46
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 47
    .line 48
    .line 49
    goto/16 :goto_2

    .line 50
    .line 51
    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 52
    .line 53
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 54
    .line 55
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 56
    .line 57
    .line 58
    throw v1

    .line 59
    :cond_2
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 60
    .line 61
    .line 62
    iget-object v1, v0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->b:Ldg/c;

    .line 63
    .line 64
    iget-object v4, v0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->f:Lkotlin/jvm/functions/Function0;

    .line 65
    .line 66
    invoke-interface {v4}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object v4

    .line 70
    check-cast v4, Lcg/a;

    .line 71
    .line 72
    iget-object v6, v0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->c:Ldg/a;

    .line 73
    .line 74
    new-instance v8, Ljava/util/ArrayList;

    .line 75
    .line 76
    const/16 v7, 0xa

    .line 77
    .line 78
    move-object/from16 v9, p2

    .line 79
    .line 80
    invoke-static {v9, v7}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 81
    .line 82
    .line 83
    move-result v7

    .line 84
    invoke-direct {v8, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 85
    .line 86
    .line 87
    invoke-interface {v9}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 88
    .line 89
    .line 90
    move-result-object v7

    .line 91
    :goto_1
    invoke-interface {v7}, Ljava/util/Iterator;->hasNext()Z

    .line 92
    .line 93
    .line 94
    move-result v9

    .line 95
    if-eqz v9, :cond_3

    .line 96
    .line 97
    invoke-interface {v7}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 98
    .line 99
    .line 100
    move-result-object v9

    .line 101
    check-cast v9, Lig/a;

    .line 102
    .line 103
    invoke-virtual {v6, v9}, Ldg/a;->a(Lig/a;)Leg/a;

    .line 104
    .line 105
    .line 106
    move-result-object v9

    .line 107
    invoke-interface {v8, v9}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 108
    .line 109
    .line 110
    goto :goto_1

    .line 111
    :cond_3
    invoke-virtual/range {p5 .. p5}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusId()J

    .line 112
    .line 113
    .line 114
    move-result-wide v11

    .line 115
    sget-object v6, Lorg/xbet/core/data/LuckyWheelBonusType;->Companion:Lorg/xbet/core/data/LuckyWheelBonusType$a;

    .line 116
    .line 117
    invoke-virtual/range {p5 .. p5}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 118
    .line 119
    .line 120
    move-result-object v7

    .line 121
    invoke-virtual {v6, v7}, Lorg/xbet/core/data/LuckyWheelBonusType$a;->b(Lorg/xbet/games_section/api/models/GameBonusType;)Lorg/xbet/core/data/LuckyWheelBonusType;

    .line 122
    .line 123
    .line 124
    move-result-object v13

    .line 125
    iget-object v6, v0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->e:Lc8/h;

    .line 126
    .line 127
    invoke-interface {v6}, Lc8/h;->c()Ljava/lang/String;

    .line 128
    .line 129
    .line 130
    move-result-object v16

    .line 131
    iget-object v6, v0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->e:Lc8/h;

    .line 132
    .line 133
    invoke-interface {v6}, Lc8/h;->d()I

    .line 134
    .line 135
    .line 136
    move-result v17

    .line 137
    new-instance v7, Leg/b;

    .line 138
    .line 139
    const/16 v18, 0x2

    .line 140
    .line 141
    const/16 v19, 0x0

    .line 142
    .line 143
    const-wide/16 v9, 0x0

    .line 144
    .line 145
    move-wide/from16 v14, p3

    .line 146
    .line 147
    invoke-direct/range {v7 .. v19}, Leg/b;-><init>(Ljava/util/List;DJLorg/xbet/core/data/LuckyWheelBonusType;JLjava/lang/String;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 148
    .line 149
    .line 150
    iput-object v1, v2, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$play$1;->L$0:Ljava/lang/Object;

    .line 151
    .line 152
    iput v5, v2, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository$play$1;->label:I

    .line 153
    .line 154
    move-object/from16 v5, p1

    .line 155
    .line 156
    invoke-interface {v4, v5, v7, v2}, Lcg/a;->a(Ljava/lang/String;Leg/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 157
    .line 158
    .line 159
    move-result-object v2

    .line 160
    if-ne v2, v3, :cond_4

    .line 161
    .line 162
    return-object v3

    .line 163
    :cond_4
    move-object/from16 v20, v2

    .line 164
    .line 165
    move-object v2, v1

    .line 166
    move-object/from16 v1, v20

    .line 167
    .line 168
    :goto_2
    check-cast v1, Lg9/d;

    .line 169
    .line 170
    invoke-virtual {v1}, Lg9/d;->a()Ljava/lang/Object;

    .line 171
    .line 172
    .line 173
    move-result-object v1

    .line 174
    check-cast v1, Lfg/a;

    .line 175
    .line 176
    invoke-virtual {v2, v1}, Ldg/c;->a(Lfg/a;)Lig/b;

    .line 177
    .line 178
    .line 179
    move-result-object v1

    .line 180
    return-object v1
.end method

.method public final p()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lbg/a;->l()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final q(Lig/a;)V
    .locals 1
    .param p1    # Lig/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lbg/a;->m(Lig/a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final r(Lig/b;)V
    .locals 1
    .param p1    # Lig/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lbg/a;->n(Lig/b;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final s(Lorg/xbet/games_section/api/models/GameBonusType;)V
    .locals 1
    .param p1    # Lorg/xbet/games_section/api/models/GameBonusType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lbg/a;->o(Lorg/xbet/games_section/api/models/GameBonusType;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final t(F)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lbg/a;->p(F)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final u(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V
    .locals 1
    .param p1    # Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d:Lbg/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lbg/a;->q(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
