.class public final LSF0/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0018\u0008\u0007\u0018\u00002\u00020\u0001Ba\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\'\u0010!\u001a\u00020 2\u0006\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020\u001eH\u0000\u00a2\u0006\u0004\u0008!\u0010\"R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010#R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010%R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010)R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00105R\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107\u00a8\u00068"
    }
    d2 = {
        "LSF0/c;",
        "LQW0/a;",
        "LQW0/c;",
        "coroutinesLib",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lf8/g;",
        "serviceGenerator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "LSX0/a;",
        "lottieConfigurator",
        "LHX0/e;",
        "resourceManager",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "Li8/m;",
        "getThemeUseCase",
        "LEN0/f;",
        "statisticCoreFeature",
        "Lorg/xbet/onexdatabase/OnexDatabase;",
        "onexDatabase",
        "Lc8/h;",
        "requestParamsDataSource",
        "<init>",
        "(LQW0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;LHX0/e;LSX0/c;Li8/m;LEN0/f;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;)V",
        "LwX0/c;",
        "router",
        "",
        "gameId",
        "",
        "sportId",
        "LSF0/g;",
        "a",
        "(LwX0/c;Ljava/lang/String;J)LSF0/g;",
        "LQW0/c;",
        "b",
        "Lorg/xbet/ui_common/utils/M;",
        "c",
        "Lf8/g;",
        "d",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "e",
        "LSX0/a;",
        "f",
        "LHX0/e;",
        "g",
        "LSX0/c;",
        "h",
        "Li8/m;",
        "i",
        "LEN0/f;",
        "j",
        "Lorg/xbet/onexdatabase/OnexDatabase;",
        "k",
        "Lc8/h;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LQW0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Li8/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:LEN0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lorg/xbet/onexdatabase/OnexDatabase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(LQW0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;LHX0/e;LSX0/c;Li8/m;LEN0/f;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;)V
    .locals 0
    .param p1    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Li8/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LEN0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/onexdatabase/OnexDatabase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LSF0/c;->a:LQW0/c;

    .line 5
    .line 6
    iput-object p2, p0, LSF0/c;->b:Lorg/xbet/ui_common/utils/M;

    .line 7
    .line 8
    iput-object p3, p0, LSF0/c;->c:Lf8/g;

    .line 9
    .line 10
    iput-object p4, p0, LSF0/c;->d:Lorg/xbet/ui_common/utils/internet/a;

    .line 11
    .line 12
    iput-object p5, p0, LSF0/c;->e:LSX0/a;

    .line 13
    .line 14
    iput-object p6, p0, LSF0/c;->f:LHX0/e;

    .line 15
    .line 16
    iput-object p7, p0, LSF0/c;->g:LSX0/c;

    .line 17
    .line 18
    iput-object p8, p0, LSF0/c;->h:Li8/m;

    .line 19
    .line 20
    iput-object p9, p0, LSF0/c;->i:LEN0/f;

    .line 21
    .line 22
    iput-object p10, p0, LSF0/c;->j:Lorg/xbet/onexdatabase/OnexDatabase;

    .line 23
    .line 24
    iput-object p11, p0, LSF0/c;->k:Lc8/h;

    .line 25
    .line 26
    return-void
.end method


# virtual methods
.method public final a(LwX0/c;Ljava/lang/String;J)LSF0/g;
    .locals 17
    .param p1    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, LSF0/a;->a()LSF0/g$a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, v0, LSF0/c;->a:LQW0/c;

    .line 8
    .line 9
    iget-object v5, v0, LSF0/c;->b:Lorg/xbet/ui_common/utils/M;

    .line 10
    .line 11
    iget-object v6, v0, LSF0/c;->c:Lf8/g;

    .line 12
    .line 13
    iget-object v10, v0, LSF0/c;->h:Li8/m;

    .line 14
    .line 15
    iget-object v11, v0, LSF0/c;->f:LHX0/e;

    .line 16
    .line 17
    iget-object v4, v0, LSF0/c;->g:LSX0/c;

    .line 18
    .line 19
    iget-object v12, v0, LSF0/c;->d:Lorg/xbet/ui_common/utils/internet/a;

    .line 20
    .line 21
    iget-object v14, v0, LSF0/c;->e:LSX0/a;

    .line 22
    .line 23
    iget-object v15, v0, LSF0/c;->j:Lorg/xbet/onexdatabase/OnexDatabase;

    .line 24
    .line 25
    iget-object v3, v0, LSF0/c;->i:LEN0/f;

    .line 26
    .line 27
    iget-object v7, v0, LSF0/c;->k:Lc8/h;

    .line 28
    .line 29
    move-object/from16 v13, p1

    .line 30
    .line 31
    move-wide/from16 v8, p3

    .line 32
    .line 33
    move-object/from16 v16, v7

    .line 34
    .line 35
    move-object/from16 v7, p2

    .line 36
    .line 37
    invoke-interface/range {v1 .. v16}, LSF0/g$a;->a(LQW0/c;LEN0/f;LSX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;JLi8/m;LHX0/e;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;)LSF0/g;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    return-object v1
.end method
