.class public final Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lxg/c;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0007\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J(\u0010\r\u001a\u00020\u000c2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\n\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\u0008H\u0096@\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u000f\u0010\u000f\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\u0011R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0012\u00a8\u0006\u0013"
    }
    d2 = {
        "Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl;",
        "Lxg/c;",
        "Lug/n;",
        "referralAssetsLocalDataSource",
        "Lug/i;",
        "customBTagBetPariRemoteDataSource",
        "<init>",
        "(Lug/n;Lug/i;)V",
        "",
        "campaignShort",
        "afSub1",
        "pid",
        "",
        "a",
        "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "b",
        "()Ljava/lang/String;",
        "Lug/n;",
        "Lug/i;",
        "analytics_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lug/n;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lug/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lug/n;Lug/i;)V
    .locals 0
    .param p1    # Lug/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lug/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl;->a:Lug/n;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl;->b:Lug/i;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 6
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p4, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl$requestTag$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p4

    .line 6
    check-cast v0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl$requestTag$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl$requestTag$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl$requestTag$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl$requestTag$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p4}, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl$requestTag$1;-><init>(Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p4, v0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl$requestTag$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl$requestTag$1;->label:I

    .line 32
    .line 33
    const-string v3, ""

    .line 34
    .line 35
    const/4 v4, 0x2

    .line 36
    const/4 v5, 0x1

    .line 37
    if-eqz v2, :cond_3

    .line 38
    .line 39
    if-eq v2, v5, :cond_2

    .line 40
    .line 41
    if-ne v2, v4, :cond_1

    .line 42
    .line 43
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 44
    .line 45
    .line 46
    goto :goto_4

    .line 47
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 48
    .line 49
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 50
    .line 51
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    throw p1

    .line 55
    :cond_2
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    goto :goto_1

    .line 59
    :cond_3
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 60
    .line 61
    .line 62
    invoke-virtual {p0}, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl;->b()Ljava/lang/String;

    .line 63
    .line 64
    .line 65
    move-result-object p4

    .line 66
    invoke-interface {p4}, Ljava/lang/CharSequence;->length()I

    .line 67
    .line 68
    .line 69
    move-result p4

    .line 70
    if-lez p4, :cond_4

    .line 71
    .line 72
    goto :goto_6

    .line 73
    :cond_4
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 74
    .line 75
    .line 76
    move-result p4

    .line 77
    if-lez p4, :cond_5

    .line 78
    .line 79
    iget-object p2, p0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl;->a:Lug/n;

    .line 80
    .line 81
    invoke-interface {p2, p1}, Lug/n;->e(Ljava/lang/String;)V

    .line 82
    .line 83
    .line 84
    goto :goto_6

    .line 85
    :cond_5
    invoke-interface {p2}, Ljava/lang/CharSequence;->length()I

    .line 86
    .line 87
    .line 88
    move-result p1

    .line 89
    if-lez p1, :cond_8

    .line 90
    .line 91
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl;->b:Lug/i;

    .line 92
    .line 93
    iput v5, v0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl$requestTag$1;->label:I

    .line 94
    .line 95
    invoke-virtual {p1, p2, v0}, Lug/i;->c(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object p4

    .line 99
    if-ne p4, v1, :cond_6

    .line 100
    .line 101
    goto :goto_3

    .line 102
    :cond_6
    :goto_1
    check-cast p4, Lvg/b;

    .line 103
    .line 104
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl;->a:Lug/n;

    .line 105
    .line 106
    invoke-virtual {p4}, Lvg/b;->a()Ljava/lang/String;

    .line 107
    .line 108
    .line 109
    move-result-object p2

    .line 110
    if-nez p2, :cond_7

    .line 111
    .line 112
    goto :goto_2

    .line 113
    :cond_7
    move-object v3, p2

    .line 114
    :goto_2
    invoke-interface {p1, v3}, Lug/n;->e(Ljava/lang/String;)V

    .line 115
    .line 116
    .line 117
    goto :goto_6

    .line 118
    :cond_8
    invoke-interface {p3}, Ljava/lang/CharSequence;->length()I

    .line 119
    .line 120
    .line 121
    move-result p1

    .line 122
    if-lez p1, :cond_b

    .line 123
    .line 124
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl;->b:Lug/i;

    .line 125
    .line 126
    iput v4, v0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl$requestTag$1;->label:I

    .line 127
    .line 128
    invoke-virtual {p1, p3, v0}, Lug/i;->b(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 129
    .line 130
    .line 131
    move-result-object p4

    .line 132
    if-ne p4, v1, :cond_9

    .line 133
    .line 134
    :goto_3
    return-object v1

    .line 135
    :cond_9
    :goto_4
    check-cast p4, Lvg/b;

    .line 136
    .line 137
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl;->a:Lug/n;

    .line 138
    .line 139
    invoke-virtual {p4}, Lvg/b;->a()Ljava/lang/String;

    .line 140
    .line 141
    .line 142
    move-result-object p2

    .line 143
    if-nez p2, :cond_a

    .line 144
    .line 145
    goto :goto_5

    .line 146
    :cond_a
    move-object v3, p2

    .line 147
    :goto_5
    invoke-interface {p1, v3}, Lug/n;->e(Ljava/lang/String;)V

    .line 148
    .line 149
    .line 150
    :cond_b
    :goto_6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 151
    .line 152
    return-object p1
.end method

.method public final b()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/CustomBTagBetPariRepositoryImpl;->a:Lug/n;

    .line 2
    .line 3
    invoke-interface {v0}, Lug/n;->d()Lj9/i;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lj9/i;->b()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    if-nez v0, :cond_0

    .line 12
    .line 13
    const-string v0, ""

    .line 14
    .line 15
    :cond_0
    return-object v0
.end method
