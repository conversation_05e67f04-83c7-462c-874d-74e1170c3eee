.class public final LGB0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LGB0/d;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LGB0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:LGB0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LyB0/e;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/core/data/datasource/local/j;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/core/data/datasource/local/l;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/a;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/data/repositories/MarketsRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LhB0/i;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/core/data/datasource/local/r;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/markets/impl/data/repositories/c;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LtB0/c;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lm8/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;Lorg/xbet/sportgame/core/data/datasource/local/l;Lorg/xbet/sportgame/core/data/datasource/local/j;LEP/b;LKB0/a;Lorg/xbet/sportgame/core/data/datasource/local/r;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LGB0/a$b;->a:LGB0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p13}, LGB0/a$b;->e(Lm8/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;Lorg/xbet/sportgame/core/data/datasource/local/l;Lorg/xbet/sportgame/core/data/datasource/local/j;LEP/b;LKB0/a;Lorg/xbet/sportgame/core/data/datasource/local/r;)V

    return-void
.end method

.method public synthetic constructor <init>(Lm8/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;Lorg/xbet/sportgame/core/data/datasource/local/l;Lorg/xbet/sportgame/core/data/datasource/local/j;LEP/b;LKB0/a;Lorg/xbet/sportgame/core/data/datasource/local/r;LGB0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p13}, LGB0/a$b;-><init>(Lm8/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;Lorg/xbet/sportgame/core/data/datasource/local/l;Lorg/xbet/sportgame/core/data/datasource/local/j;LEP/b;LKB0/a;Lorg/xbet/sportgame/core/data/datasource/local/r;)V

    return-void
.end method


# virtual methods
.method public a()LhB0/i;
    .locals 1

    .line 1
    iget-object v0, p0, LGB0/a$b;->i:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LhB0/i;

    .line 8
    .line 9
    return-object v0
.end method

.method public b()LtB0/b;
    .locals 1

    .line 1
    invoke-virtual {p0}, LGB0/a$b;->d()Lorg/xbet/sportgame/markets/impl/domain/usecases/j;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public c()LtB0/c;
    .locals 1

    .line 1
    iget-object v0, p0, LGB0/a$b;->l:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LtB0/c;

    .line 8
    .line 9
    return-object v0
.end method

.method public final d()Lorg/xbet/sportgame/markets/impl/domain/usecases/j;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/sportgame/markets/impl/domain/usecases/j;

    .line 2
    .line 3
    iget-object v1, p0, LGB0/a$b;->i:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    check-cast v1, LhB0/i;

    .line 10
    .line 11
    invoke-direct {v0, v1}, Lorg/xbet/sportgame/markets/impl/domain/usecases/j;-><init>(LhB0/i;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public final e(Lm8/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;Lorg/xbet/sportgame/core/data/datasource/local/l;Lorg/xbet/sportgame/core/data/datasource/local/j;LEP/b;LKB0/a;Lorg/xbet/sportgame/core/data/datasource/local/r;)V
    .locals 0

    .line 1
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, LGB0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p1}, LyB0/f;->a(LBc/a;)LyB0/f;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    iput-object p1, p0, LGB0/a$b;->c:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iput-object p1, p0, LGB0/a$b;->d:Ldagger/internal/h;

    .line 18
    .line 19
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    iput-object p1, p0, LGB0/a$b;->e:Ldagger/internal/h;

    .line 24
    .line 25
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    iput-object p1, p0, LGB0/a$b;->f:Ldagger/internal/h;

    .line 30
    .line 31
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    iput-object p1, p0, LGB0/a$b;->g:Ldagger/internal/h;

    .line 36
    .line 37
    iget-object p2, p0, LGB0/a$b;->c:Ldagger/internal/h;

    .line 38
    .line 39
    iget-object p3, p0, LGB0/a$b;->d:Ldagger/internal/h;

    .line 40
    .line 41
    iget-object p4, p0, LGB0/a$b;->e:Ldagger/internal/h;

    .line 42
    .line 43
    iget-object p5, p0, LGB0/a$b;->f:Ldagger/internal/h;

    .line 44
    .line 45
    invoke-static {p2, p3, p4, p5, p1}, Lorg/xbet/sportgame/markets/impl/data/repositories/b;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/sportgame/markets/impl/data/repositories/b;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    iput-object p1, p0, LGB0/a$b;->h:Ldagger/internal/h;

    .line 50
    .line 51
    invoke-static {p1}, Ldagger/internal/c;->d(Ldagger/internal/h;)Ldagger/internal/h;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    iput-object p1, p0, LGB0/a$b;->i:Ldagger/internal/h;

    .line 56
    .line 57
    invoke-static {p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    iput-object p1, p0, LGB0/a$b;->j:Ldagger/internal/h;

    .line 62
    .line 63
    invoke-static {p1}, Lorg/xbet/sportgame/markets/impl/data/repositories/d;->a(LBc/a;)Lorg/xbet/sportgame/markets/impl/data/repositories/d;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    iput-object p1, p0, LGB0/a$b;->k:Ldagger/internal/h;

    .line 68
    .line 69
    invoke-static {p1}, Ldagger/internal/c;->d(Ldagger/internal/h;)Ldagger/internal/h;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    iput-object p1, p0, LGB0/a$b;->l:Ldagger/internal/h;

    .line 74
    .line 75
    return-void
.end method
