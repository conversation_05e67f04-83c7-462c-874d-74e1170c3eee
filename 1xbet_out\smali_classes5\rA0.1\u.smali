.class public final synthetic LrA0/u;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function2;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function2;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LrA0/u;->a:Lkotlin/jvm/functions/Function2;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LrA0/u;->a:Lkotlin/jvm/functions/Function2;

    check-cast p1, LB4/a;

    invoke-static {v0, p1}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/gameinfo/withtimer/viewholders/PenaltyViewHolderKt;->c(<PERSON><PERSON><PERSON>/jvm/functions/Function2;LB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
