.class public final LiP0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u001e\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\t0\u00082\u0006\u0010\u0007\u001a\u00020\u0006H\u0086B\u00a2\u0006\u0004\u0008\n\u0010\u000bR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\n\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "LiP0/a;",
        "",
        "LhP0/a;",
        "repository",
        "<init>",
        "(LhP0/a;)V",
        "Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;",
        "typeParam",
        "",
        "LgP0/b;",
        "a",
        "(Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "LhP0/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LhP0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LhP0/a;)V
    .locals 0
    .param p1    # LhP0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LiP0/a;->a:LhP0/a;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .param p1    # Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "LgP0/b;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LiP0/a;->a:LhP0/a;

    .line 2
    .line 3
    invoke-interface {v0, p1, p2}, LhP0/a;->b(Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method
