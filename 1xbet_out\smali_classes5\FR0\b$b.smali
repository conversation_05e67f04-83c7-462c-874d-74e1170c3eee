.class public final LFR0/b$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LFR0/b;->b(Ltc1/a;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "LOc/n<",
        "Landroidx/compose/foundation/layout/Y;",
        "Landroidx/compose/runtime/j;",
        "Ljava/lang/Integer;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Landroidx/compose/runtime/r1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/compose/runtime/r1<",
            "LHR0/i;",
            ">;"
        }
    .end annotation
.end field

.field public final synthetic b:Landroidx/compose/runtime/h0;

.field public final synthetic c:Ltc1/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ltc1/a<",
            "LHR0/c;",
            "LHR0/i;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroidx/compose/runtime/r1;Landroidx/compose/runtime/h0;Ltc1/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/runtime/r1<",
            "LHR0/i;",
            ">;",
            "Landroidx/compose/runtime/h0;",
            "Ltc1/a<",
            "LHR0/c;",
            "LHR0/i;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LFR0/b$b;->a:Landroidx/compose/runtime/r1;

    .line 2
    .line 3
    iput-object p2, p0, LFR0/b$b;->b:Landroidx/compose/runtime/h0;

    .line 4
    .line 5
    iput-object p3, p0, LFR0/b$b;->c:Ltc1/a;

    .line 6
    .line 7
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static synthetic a(Landroidx/compose/runtime/r1;Landroid/content/Context;)Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LFR0/b$b;->k(Landroidx/compose/runtime/r1;Landroid/content/Context;)Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroidx/compose/runtime/r1;Landroid/content/Context;)Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LFR0/b$b;->j(Landroidx/compose/runtime/r1;Landroid/content/Context;)Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Ltc1/a;LNN0/h;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LFR0/b$b;->g(Ltc1/a;LNN0/h;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Landroidx/compose/runtime/h0;Lt0/t;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LFR0/b$b;->f(Landroidx/compose/runtime/h0;Lt0/t;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private static final f(Landroidx/compose/runtime/h0;Lt0/t;)Lkotlin/Unit;
    .locals 4

    .line 1
    invoke-virtual {p1}, Lt0/t;->j()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    const-wide v2, 0xffffffffL

    .line 6
    .line 7
    .line 8
    .line 9
    .line 10
    and-long/2addr v0, v2

    .line 11
    long-to-int p1, v0

    .line 12
    invoke-static {p0, p1}, LFR0/b;->e(Landroidx/compose/runtime/h0;I)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method private static final g(Ltc1/a;LNN0/h;)Lkotlin/Unit;
    .locals 1

    .line 1
    instance-of v0, p1, LHR0/f;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, LHR0/f;

    .line 6
    .line 7
    invoke-static {p1}, LHR0/b;->b(LHR0/f;)LHR0/f;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-static {p1}, LHR0/b;->a(LHR0/f;)LHR0/b;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    invoke-virtual {p0, p1}, Ltc1/a;->o3(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 19
    .line 20
    return-object p0
.end method

.method private static final j(Landroidx/compose/runtime/r1;Landroid/content/Context;)Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 2
    .line 3
    const/4 v4, 0x6

    .line 4
    const/4 v5, 0x0

    .line 5
    const/4 v2, 0x0

    .line 6
    const/4 v3, 0x0

    .line 7
    move-object v1, p1

    .line 8
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 9
    .line 10
    .line 11
    invoke-interface {p0}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    check-cast p0, LHR0/i;

    .line 16
    .line 17
    invoke-virtual {p0}, LHR0/i;->c()Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    check-cast p0, Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r$b;

    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r$b;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    invoke-virtual {v0, p0}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 28
    .line 29
    .line 30
    const/4 p0, 0x0

    .line 31
    invoke-virtual {v0, p0}, Landroid/view/View;->setVisibility(I)V

    .line 32
    .line 33
    .line 34
    return-object v0
.end method

.method private static final k(Landroidx/compose/runtime/r1;Landroid/content/Context;)Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 2
    .line 3
    const/4 v4, 0x6

    .line 4
    const/4 v5, 0x0

    .line 5
    const/4 v2, 0x0

    .line 6
    const/4 v3, 0x0

    .line 7
    move-object v1, p1

    .line 8
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 9
    .line 10
    .line 11
    invoke-interface {p0}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    check-cast p0, LHR0/i;

    .line 16
    .line 17
    invoke-virtual {p0}, LHR0/i;->c()Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    check-cast p0, Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r$c;

    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r$c;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    invoke-virtual {v0, p0}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 28
    .line 29
    .line 30
    const/4 p0, 0x0

    .line 31
    invoke-virtual {v0, p0}, Landroid/view/View;->setVisibility(I)V

    .line 32
    .line 33
    .line 34
    return-object v0
.end method


# virtual methods
.method public final e(Landroidx/compose/foundation/layout/Y;Landroidx/compose/runtime/j;I)V
    .locals 28

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    move-object/from16 v4, p2

    .line 6
    .line 7
    and-int/lit8 v2, p3, 0x6

    .line 8
    .line 9
    if-nez v2, :cond_1

    .line 10
    .line 11
    invoke-interface {v4, v1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    if-eqz v2, :cond_0

    .line 16
    .line 17
    const/4 v2, 0x4

    .line 18
    goto :goto_0

    .line 19
    :cond_0
    const/4 v2, 0x2

    .line 20
    :goto_0
    or-int v2, p3, v2

    .line 21
    .line 22
    goto :goto_1

    .line 23
    :cond_1
    move/from16 v2, p3

    .line 24
    .line 25
    :goto_1
    and-int/lit8 v3, v2, 0x13

    .line 26
    .line 27
    const/16 v5, 0x12

    .line 28
    .line 29
    if-ne v3, v5, :cond_3

    .line 30
    .line 31
    invoke-interface {v4}, Landroidx/compose/runtime/j;->c()Z

    .line 32
    .line 33
    .line 34
    move-result v3

    .line 35
    if-nez v3, :cond_2

    .line 36
    .line 37
    goto :goto_2

    .line 38
    :cond_2
    invoke-interface {v4}, Landroidx/compose/runtime/j;->n()V

    .line 39
    .line 40
    .line 41
    return-void

    .line 42
    :cond_3
    :goto_2
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 43
    .line 44
    .line 45
    move-result v3

    .line 46
    if-eqz v3, :cond_4

    .line 47
    .line 48
    const/4 v3, -0x1

    .line 49
    const-string v5, "org.xbet.statistic.winter_games.impl.winter_game.presentation.components.ListWidgetsWinterGameMenuComponent.<anonymous> (ListWidgetsWinterGameMenuComponent.kt:66)"

    .line 50
    .line 51
    const v6, 0x6af5b2d8

    .line 52
    .line 53
    .line 54
    invoke-static {v6, v2, v3, v5}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 55
    .line 56
    .line 57
    :cond_4
    sget-object v10, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 58
    .line 59
    const/4 v11, 0x0

    .line 60
    const/4 v12, 0x1

    .line 61
    const/4 v13, 0x0

    .line 62
    invoke-static {v10, v11, v12, v13}, Landroidx/compose/foundation/layout/SizeKt;->f(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 63
    .line 64
    .line 65
    move-result-object v2

    .line 66
    invoke-static {v2, v1}, Landroidx/compose/foundation/layout/WindowInsetsPaddingKt;->a(Landroidx/compose/ui/l;Landroidx/compose/foundation/layout/Y;)Landroidx/compose/ui/l;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    iget-object v14, v0, LFR0/b$b;->a:Landroidx/compose/runtime/r1;

    .line 71
    .line 72
    iget-object v3, v0, LFR0/b$b;->b:Landroidx/compose/runtime/h0;

    .line 73
    .line 74
    iget-object v15, v0, LFR0/b$b;->c:Ltc1/a;

    .line 75
    .line 76
    sget-object v5, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 77
    .line 78
    invoke-virtual {v5}, Landroidx/compose/foundation/layout/Arrangement;->h()Landroidx/compose/foundation/layout/Arrangement$m;

    .line 79
    .line 80
    .line 81
    move-result-object v5

    .line 82
    sget-object v16, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 83
    .line 84
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/ui/e$a;->k()Landroidx/compose/ui/e$b;

    .line 85
    .line 86
    .line 87
    move-result-object v6

    .line 88
    const/4 v7, 0x0

    .line 89
    invoke-static {v5, v6, v4, v7}, Landroidx/compose/foundation/layout/k;->a(Landroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 90
    .line 91
    .line 92
    move-result-object v5

    .line 93
    invoke-static {v4, v7}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 94
    .line 95
    .line 96
    move-result v6

    .line 97
    invoke-interface {v4}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 98
    .line 99
    .line 100
    move-result-object v8

    .line 101
    invoke-static {v4, v2}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 102
    .line 103
    .line 104
    move-result-object v2

    .line 105
    sget-object v17, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 106
    .line 107
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 108
    .line 109
    .line 110
    move-result-object v9

    .line 111
    invoke-interface {v4}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 112
    .line 113
    .line 114
    move-result-object v18

    .line 115
    invoke-static/range {v18 .. v18}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 116
    .line 117
    .line 118
    move-result v18

    .line 119
    if-nez v18, :cond_5

    .line 120
    .line 121
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 122
    .line 123
    .line 124
    :cond_5
    invoke-interface {v4}, Landroidx/compose/runtime/j;->l()V

    .line 125
    .line 126
    .line 127
    invoke-interface {v4}, Landroidx/compose/runtime/j;->B()Z

    .line 128
    .line 129
    .line 130
    move-result v18

    .line 131
    if-eqz v18, :cond_6

    .line 132
    .line 133
    invoke-interface {v4, v9}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 134
    .line 135
    .line 136
    goto :goto_3

    .line 137
    :cond_6
    invoke-interface {v4}, Landroidx/compose/runtime/j;->h()V

    .line 138
    .line 139
    .line 140
    :goto_3
    invoke-static {v4}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 141
    .line 142
    .line 143
    move-result-object v9

    .line 144
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 145
    .line 146
    .line 147
    move-result-object v7

    .line 148
    invoke-static {v9, v5, v7}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 149
    .line 150
    .line 151
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 152
    .line 153
    .line 154
    move-result-object v5

    .line 155
    invoke-static {v9, v8, v5}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 156
    .line 157
    .line 158
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 159
    .line 160
    .line 161
    move-result-object v5

    .line 162
    invoke-interface {v9}, Landroidx/compose/runtime/j;->B()Z

    .line 163
    .line 164
    .line 165
    move-result v7

    .line 166
    if-nez v7, :cond_7

    .line 167
    .line 168
    invoke-interface {v9}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 169
    .line 170
    .line 171
    move-result-object v7

    .line 172
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 173
    .line 174
    .line 175
    move-result-object v8

    .line 176
    invoke-static {v7, v8}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 177
    .line 178
    .line 179
    move-result v7

    .line 180
    if-nez v7, :cond_8

    .line 181
    .line 182
    :cond_7
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 183
    .line 184
    .line 185
    move-result-object v7

    .line 186
    invoke-interface {v9, v7}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 187
    .line 188
    .line 189
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 190
    .line 191
    .line 192
    move-result-object v6

    .line 193
    invoke-interface {v9, v6, v5}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 194
    .line 195
    .line 196
    :cond_8
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 197
    .line 198
    .line 199
    move-result-object v5

    .line 200
    invoke-static {v9, v2, v5}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 201
    .line 202
    .line 203
    sget-object v2, Landroidx/compose/foundation/layout/n;->a:Landroidx/compose/foundation/layout/n;

    .line 204
    .line 205
    invoke-interface {v14}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 206
    .line 207
    .line 208
    move-result-object v2

    .line 209
    check-cast v2, LHR0/i;

    .line 210
    .line 211
    invoke-virtual {v2}, LHR0/i;->b()LHR0/d;

    .line 212
    .line 213
    .line 214
    move-result-object v2

    .line 215
    invoke-virtual {v2}, LHR0/d;->c()Ljava/lang/String;

    .line 216
    .line 217
    .line 218
    move-result-object v2

    .line 219
    invoke-interface {v14}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 220
    .line 221
    .line 222
    move-result-object v5

    .line 223
    check-cast v5, LHR0/i;

    .line 224
    .line 225
    invoke-virtual {v5}, LHR0/i;->b()LHR0/d;

    .line 226
    .line 227
    .line 228
    move-result-object v5

    .line 229
    invoke-virtual {v5}, LHR0/d;->b()Ljava/lang/String;

    .line 230
    .line 231
    .line 232
    move-result-object v5

    .line 233
    invoke-interface {v14}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 234
    .line 235
    .line 236
    move-result-object v6

    .line 237
    check-cast v6, LHR0/i;

    .line 238
    .line 239
    invoke-virtual {v6}, LHR0/i;->b()LHR0/d;

    .line 240
    .line 241
    .line 242
    move-result-object v6

    .line 243
    invoke-virtual {v6}, LHR0/d;->a()Ljava/lang/String;

    .line 244
    .line 245
    .line 246
    move-result-object v6

    .line 247
    invoke-interface {v14}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 248
    .line 249
    .line 250
    move-result-object v7

    .line 251
    check-cast v7, LHR0/i;

    .line 252
    .line 253
    invoke-virtual {v7}, LHR0/i;->b()LHR0/d;

    .line 254
    .line 255
    .line 256
    move-result-object v7

    .line 257
    invoke-virtual {v7}, LHR0/d;->d()Z

    .line 258
    .line 259
    .line 260
    move-result v7

    .line 261
    invoke-static {v10, v11, v12, v13}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 262
    .line 263
    .line 264
    move-result-object v8

    .line 265
    const/4 v9, 0x0

    .line 266
    invoke-static {v4, v9}, LA11/d;->c(Landroidx/compose/runtime/j;I)LA11/b;

    .line 267
    .line 268
    .line 269
    move-result-object v18

    .line 270
    invoke-virtual/range {v18 .. v18}, LA11/b;->a()F

    .line 271
    .line 272
    .line 273
    move-result v11

    .line 274
    invoke-static {v4, v9}, LA11/d;->c(Landroidx/compose/runtime/j;I)LA11/b;

    .line 275
    .line 276
    .line 277
    move-result-object v18

    .line 278
    invoke-virtual/range {v18 .. v18}, LA11/b;->a()F

    .line 279
    .line 280
    .line 281
    move-result v9

    .line 282
    sget-object v18, LA11/a;->a:LA11/a;

    .line 283
    .line 284
    invoke-virtual/range {v18 .. v18}, LA11/a;->L1()F

    .line 285
    .line 286
    .line 287
    move-result v20

    .line 288
    invoke-interface {v1}, Landroidx/compose/foundation/layout/Y;->d()F

    .line 289
    .line 290
    .line 291
    move-result v21

    .line 292
    add-float v20, v20, v21

    .line 293
    .line 294
    invoke-static/range {v20 .. v20}, Lt0/i;->k(F)F

    .line 295
    .line 296
    .line 297
    move-result v12

    .line 298
    invoke-virtual/range {v18 .. v18}, LA11/a;->q1()F

    .line 299
    .line 300
    .line 301
    move-result v13

    .line 302
    invoke-static {v8, v11, v12, v9, v13}, Landroidx/compose/foundation/layout/PaddingKt;->l(Landroidx/compose/ui/l;FFFF)Landroidx/compose/ui/l;

    .line 303
    .line 304
    .line 305
    move-result-object v8

    .line 306
    const v11, 0x4c5de2

    .line 307
    .line 308
    .line 309
    invoke-interface {v4, v11}, Landroidx/compose/runtime/j;->t(I)V

    .line 310
    .line 311
    .line 312
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 313
    .line 314
    .line 315
    move-result-object v9

    .line 316
    sget-object v12, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 317
    .line 318
    invoke-virtual {v12}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 319
    .line 320
    .line 321
    move-result-object v13

    .line 322
    if-ne v9, v13, :cond_9

    .line 323
    .line 324
    new-instance v9, LFR0/c;

    .line 325
    .line 326
    invoke-direct {v9, v3}, LFR0/c;-><init>(Landroidx/compose/runtime/h0;)V

    .line 327
    .line 328
    .line 329
    invoke-interface {v4, v9}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 330
    .line 331
    .line 332
    :cond_9
    check-cast v9, Lkotlin/jvm/functions/Function1;

    .line 333
    .line 334
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 335
    .line 336
    .line 337
    invoke-static {v8, v9}, Landroidx/compose/ui/layout/b0;->a(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;)Landroidx/compose/ui/l;

    .line 338
    .line 339
    .line 340
    move-result-object v3

    .line 341
    const/4 v8, 0x0

    .line 342
    const/4 v9, 0x0

    .line 343
    move-object v13, v6

    .line 344
    move-object v6, v3

    .line 345
    move-object v3, v5

    .line 346
    move v5, v7

    .line 347
    move-object v7, v4

    .line 348
    move-object v4, v13

    .line 349
    const/4 v13, 0x0

    .line 350
    invoke-static/range {v2 .. v9}, LIN0/k;->c(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 351
    .line 352
    .line 353
    move-object v4, v7

    .line 354
    invoke-static {}, LV01/s;->f()Landroidx/compose/runtime/x0;

    .line 355
    .line 356
    .line 357
    move-result-object v2

    .line 358
    invoke-interface {v4, v2}, Landroidx/compose/runtime/j;->G(Landroidx/compose/runtime/s;)Ljava/lang/Object;

    .line 359
    .line 360
    .line 361
    move-result-object v2

    .line 362
    check-cast v2, Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 363
    .line 364
    invoke-virtual {v2}, Lorg/xbet/uikit/compose/color/ThemeColors;->getBackground-0d7_KjU()J

    .line 365
    .line 366
    .line 367
    move-result-wide v2

    .line 368
    invoke-virtual/range {v18 .. v18}, LA11/a;->u1()F

    .line 369
    .line 370
    .line 371
    move-result v23

    .line 372
    invoke-virtual/range {v18 .. v18}, LA11/a;->u1()F

    .line 373
    .line 374
    .line 375
    move-result v22

    .line 376
    const/16 v26, 0xc

    .line 377
    .line 378
    const/16 v27, 0x0

    .line 379
    .line 380
    const/16 v24, 0x0

    .line 381
    .line 382
    const/16 v25, 0x0

    .line 383
    .line 384
    invoke-static/range {v22 .. v27}, LR/i;->h(FFFFILjava/lang/Object;)LR/h;

    .line 385
    .line 386
    .line 387
    move-result-object v5

    .line 388
    invoke-static {v10, v2, v3, v5}, Landroidx/compose/foundation/BackgroundKt;->c(Landroidx/compose/ui/l;JLandroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    .line 389
    .line 390
    .line 391
    move-result-object v2

    .line 392
    invoke-virtual/range {v18 .. v18}, LA11/a;->u1()F

    .line 393
    .line 394
    .line 395
    move-result v23

    .line 396
    invoke-virtual/range {v18 .. v18}, LA11/a;->u1()F

    .line 397
    .line 398
    .line 399
    move-result v22

    .line 400
    invoke-static/range {v22 .. v27}, LR/i;->h(FFFFILjava/lang/Object;)LR/h;

    .line 401
    .line 402
    .line 403
    move-result-object v3

    .line 404
    invoke-static {v2, v3}, Landroidx/compose/ui/draw/d;->a(Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    .line 405
    .line 406
    .line 407
    move-result-object v2

    .line 408
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/ui/e$a;->o()Landroidx/compose/ui/e;

    .line 409
    .line 410
    .line 411
    move-result-object v3

    .line 412
    invoke-static {v3, v13}, Landroidx/compose/foundation/layout/BoxKt;->g(Landroidx/compose/ui/e;Z)Landroidx/compose/ui/layout/J;

    .line 413
    .line 414
    .line 415
    move-result-object v3

    .line 416
    invoke-static {v4, v13}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 417
    .line 418
    .line 419
    move-result v5

    .line 420
    invoke-interface {v4}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 421
    .line 422
    .line 423
    move-result-object v6

    .line 424
    invoke-static {v4, v2}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 425
    .line 426
    .line 427
    move-result-object v2

    .line 428
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 429
    .line 430
    .line 431
    move-result-object v7

    .line 432
    invoke-interface {v4}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 433
    .line 434
    .line 435
    move-result-object v8

    .line 436
    invoke-static {v8}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 437
    .line 438
    .line 439
    move-result v8

    .line 440
    if-nez v8, :cond_a

    .line 441
    .line 442
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 443
    .line 444
    .line 445
    :cond_a
    invoke-interface {v4}, Landroidx/compose/runtime/j;->l()V

    .line 446
    .line 447
    .line 448
    invoke-interface {v4}, Landroidx/compose/runtime/j;->B()Z

    .line 449
    .line 450
    .line 451
    move-result v8

    .line 452
    if-eqz v8, :cond_b

    .line 453
    .line 454
    invoke-interface {v4, v7}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 455
    .line 456
    .line 457
    goto :goto_4

    .line 458
    :cond_b
    invoke-interface {v4}, Landroidx/compose/runtime/j;->h()V

    .line 459
    .line 460
    .line 461
    :goto_4
    invoke-static {v4}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 462
    .line 463
    .line 464
    move-result-object v7

    .line 465
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 466
    .line 467
    .line 468
    move-result-object v8

    .line 469
    invoke-static {v7, v3, v8}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 470
    .line 471
    .line 472
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 473
    .line 474
    .line 475
    move-result-object v3

    .line 476
    invoke-static {v7, v6, v3}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 477
    .line 478
    .line 479
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 480
    .line 481
    .line 482
    move-result-object v3

    .line 483
    invoke-interface {v7}, Landroidx/compose/runtime/j;->B()Z

    .line 484
    .line 485
    .line 486
    move-result v6

    .line 487
    if-nez v6, :cond_c

    .line 488
    .line 489
    invoke-interface {v7}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 490
    .line 491
    .line 492
    move-result-object v6

    .line 493
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 494
    .line 495
    .line 496
    move-result-object v8

    .line 497
    invoke-static {v6, v8}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 498
    .line 499
    .line 500
    move-result v6

    .line 501
    if-nez v6, :cond_d

    .line 502
    .line 503
    :cond_c
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 504
    .line 505
    .line 506
    move-result-object v6

    .line 507
    invoke-interface {v7, v6}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 508
    .line 509
    .line 510
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 511
    .line 512
    .line 513
    move-result-object v5

    .line 514
    invoke-interface {v7, v5, v3}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 515
    .line 516
    .line 517
    :cond_d
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 518
    .line 519
    .line 520
    move-result-object v3

    .line 521
    invoke-static {v7, v2, v3}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 522
    .line 523
    .line 524
    sget-object v2, Landroidx/compose/foundation/layout/BoxScopeInstance;->a:Landroidx/compose/foundation/layout/BoxScopeInstance;

    .line 525
    .line 526
    invoke-interface {v14}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 527
    .line 528
    .line 529
    move-result-object v2

    .line 530
    check-cast v2, LHR0/i;

    .line 531
    .line 532
    invoke-virtual {v2}, LHR0/i;->c()Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r;

    .line 533
    .line 534
    .line 535
    move-result-object v2

    .line 536
    instance-of v3, v2, Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r$a;

    .line 537
    .line 538
    if-eqz v3, :cond_10

    .line 539
    .line 540
    const v1, 0x3697886e

    .line 541
    .line 542
    .line 543
    invoke-interface {v4, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 544
    .line 545
    .line 546
    invoke-interface {v14}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 547
    .line 548
    .line 549
    move-result-object v1

    .line 550
    check-cast v1, LHR0/i;

    .line 551
    .line 552
    invoke-virtual {v1}, LHR0/i;->c()Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r;

    .line 553
    .line 554
    .line 555
    move-result-object v1

    .line 556
    check-cast v1, Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r$a;

    .line 557
    .line 558
    invoke-virtual {v1}, Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r$a;->a()LHd/c;

    .line 559
    .line 560
    .line 561
    move-result-object v1

    .line 562
    const/4 v2, 0x0

    .line 563
    const/4 v3, 0x1

    .line 564
    const/4 v5, 0x0

    .line 565
    invoke-static {v10, v2, v3, v5}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 566
    .line 567
    .line 568
    move-result-object v2

    .line 569
    invoke-interface {v4, v11}, Landroidx/compose/runtime/j;->t(I)V

    .line 570
    .line 571
    .line 572
    invoke-interface {v4, v15}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 573
    .line 574
    .line 575
    move-result v3

    .line 576
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 577
    .line 578
    .line 579
    move-result-object v5

    .line 580
    if-nez v3, :cond_e

    .line 581
    .line 582
    invoke-virtual {v12}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 583
    .line 584
    .line 585
    move-result-object v3

    .line 586
    if-ne v5, v3, :cond_f

    .line 587
    .line 588
    :cond_e
    new-instance v5, LFR0/d;

    .line 589
    .line 590
    invoke-direct {v5, v15}, LFR0/d;-><init>(Ltc1/a;)V

    .line 591
    .line 592
    .line 593
    invoke-interface {v4, v5}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 594
    .line 595
    .line 596
    :cond_f
    move-object v3, v5

    .line 597
    check-cast v3, Lkotlin/jvm/functions/Function1;

    .line 598
    .line 599
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 600
    .line 601
    .line 602
    const/16 v6, 0x30

    .line 603
    .line 604
    const/16 v7, 0x8

    .line 605
    .line 606
    const/4 v4, 0x0

    .line 607
    move-object/from16 v5, p2

    .line 608
    .line 609
    invoke-static/range {v1 .. v7}, Lorg/xbet/statistic/statistic_core/presentation/composable/common/menu/ContentMenuKt;->d(LHd/c;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;ILandroidx/compose/runtime/j;II)V

    .line 610
    .line 611
    .line 612
    move-object v4, v5

    .line 613
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 614
    .line 615
    .line 616
    goto/16 :goto_5

    .line 617
    .line 618
    :cond_10
    instance-of v3, v2, Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r$b;

    .line 619
    .line 620
    if-eqz v3, :cond_13

    .line 621
    .line 622
    const v1, 0x36a0fa8d

    .line 623
    .line 624
    .line 625
    invoke-interface {v4, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 626
    .line 627
    .line 628
    const/4 v2, 0x0

    .line 629
    const/4 v3, 0x1

    .line 630
    const/4 v5, 0x0

    .line 631
    invoke-static {v10, v2, v3, v5}, Landroidx/compose/foundation/layout/SizeKt;->f(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 632
    .line 633
    .line 634
    move-result-object v2

    .line 635
    invoke-interface {v4, v11}, Landroidx/compose/runtime/j;->t(I)V

    .line 636
    .line 637
    .line 638
    invoke-interface {v4, v14}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 639
    .line 640
    .line 641
    move-result v1

    .line 642
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 643
    .line 644
    .line 645
    move-result-object v3

    .line 646
    if-nez v1, :cond_11

    .line 647
    .line 648
    invoke-virtual {v12}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 649
    .line 650
    .line 651
    move-result-object v1

    .line 652
    if-ne v3, v1, :cond_12

    .line 653
    .line 654
    :cond_11
    new-instance v3, LFR0/e;

    .line 655
    .line 656
    invoke-direct {v3, v14}, LFR0/e;-><init>(Landroidx/compose/runtime/r1;)V

    .line 657
    .line 658
    .line 659
    invoke-interface {v4, v3}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 660
    .line 661
    .line 662
    :cond_12
    move-object v1, v3

    .line 663
    check-cast v1, Lkotlin/jvm/functions/Function1;

    .line 664
    .line 665
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 666
    .line 667
    .line 668
    const/16 v5, 0x30

    .line 669
    .line 670
    const/4 v6, 0x4

    .line 671
    const/4 v3, 0x0

    .line 672
    invoke-static/range {v1 .. v6}, Landroidx/compose/ui/viewinterop/AndroidView_androidKt;->a(Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 673
    .line 674
    .line 675
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 676
    .line 677
    .line 678
    goto/16 :goto_5

    .line 679
    .line 680
    :cond_13
    instance-of v3, v2, Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r$c;

    .line 681
    .line 682
    if-eqz v3, :cond_16

    .line 683
    .line 684
    const v1, 0x36aae88d

    .line 685
    .line 686
    .line 687
    invoke-interface {v4, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 688
    .line 689
    .line 690
    const/4 v2, 0x0

    .line 691
    const/4 v3, 0x1

    .line 692
    const/4 v5, 0x0

    .line 693
    invoke-static {v10, v2, v3, v5}, Landroidx/compose/foundation/layout/SizeKt;->f(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 694
    .line 695
    .line 696
    move-result-object v2

    .line 697
    invoke-interface {v4, v11}, Landroidx/compose/runtime/j;->t(I)V

    .line 698
    .line 699
    .line 700
    invoke-interface {v4, v14}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 701
    .line 702
    .line 703
    move-result v1

    .line 704
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 705
    .line 706
    .line 707
    move-result-object v3

    .line 708
    if-nez v1, :cond_14

    .line 709
    .line 710
    invoke-virtual {v12}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 711
    .line 712
    .line 713
    move-result-object v1

    .line 714
    if-ne v3, v1, :cond_15

    .line 715
    .line 716
    :cond_14
    new-instance v3, LFR0/f;

    .line 717
    .line 718
    invoke-direct {v3, v14}, LFR0/f;-><init>(Landroidx/compose/runtime/r1;)V

    .line 719
    .line 720
    .line 721
    invoke-interface {v4, v3}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 722
    .line 723
    .line 724
    :cond_15
    move-object v1, v3

    .line 725
    check-cast v1, Lkotlin/jvm/functions/Function1;

    .line 726
    .line 727
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 728
    .line 729
    .line 730
    const/16 v5, 0x30

    .line 731
    .line 732
    const/4 v6, 0x4

    .line 733
    const/4 v3, 0x0

    .line 734
    invoke-static/range {v1 .. v6}, Landroidx/compose/ui/viewinterop/AndroidView_androidKt;->a(Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 735
    .line 736
    .line 737
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 738
    .line 739
    .line 740
    goto :goto_5

    .line 741
    :cond_16
    sget-object v3, Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r$d;->a:Lorg/xbet/statistic/winter_games/impl/winter_game/presentation/r$d;

    .line 742
    .line 743
    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 744
    .line 745
    .line 746
    move-result v2

    .line 747
    if-eqz v2, :cond_18

    .line 748
    .line 749
    const v2, 0x36b4db84

    .line 750
    .line 751
    .line 752
    invoke-interface {v4, v2}, Landroidx/compose/runtime/j;->t(I)V

    .line 753
    .line 754
    .line 755
    invoke-static {}, Landroidx/compose/ui/platform/AndroidCompositionLocals_androidKt;->f()Landroidx/compose/runtime/x0;

    .line 756
    .line 757
    .line 758
    move-result-object v2

    .line 759
    invoke-interface {v4, v2}, Landroidx/compose/runtime/j;->G(Landroidx/compose/runtime/s;)Ljava/lang/Object;

    .line 760
    .line 761
    .line 762
    move-result-object v2

    .line 763
    check-cast v2, Landroid/content/res/Configuration;

    .line 764
    .line 765
    iget v2, v2, Landroid/content/res/Configuration;->screenHeightDp:I

    .line 766
    .line 767
    int-to-float v2, v2

    .line 768
    invoke-static {v2}, Lt0/i;->k(F)F

    .line 769
    .line 770
    .line 771
    move-result v2

    .line 772
    invoke-static {}, LFR0/b;->f()F

    .line 773
    .line 774
    .line 775
    move-result v3

    .line 776
    sub-float/2addr v2, v3

    .line 777
    invoke-static {v2}, Lt0/i;->k(F)F

    .line 778
    .line 779
    .line 780
    move-result v2

    .line 781
    invoke-interface {v1}, Landroidx/compose/foundation/layout/Y;->d()F

    .line 782
    .line 783
    .line 784
    move-result v1

    .line 785
    sub-float/2addr v2, v1

    .line 786
    invoke-static {v2}, Lt0/i;->k(F)F

    .line 787
    .line 788
    .line 789
    move-result v1

    .line 790
    invoke-static {v1}, LIN0/D;->a(F)I

    .line 791
    .line 792
    .line 793
    move-result v1

    .line 794
    const/4 v2, 0x0

    .line 795
    const/4 v3, 0x1

    .line 796
    const/4 v5, 0x0

    .line 797
    invoke-static {v10, v2, v3, v5}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 798
    .line 799
    .line 800
    move-result-object v14

    .line 801
    invoke-static {v4, v13}, LA11/d;->c(Landroidx/compose/runtime/j;I)LA11/b;

    .line 802
    .line 803
    .line 804
    move-result-object v2

    .line 805
    invoke-virtual {v2}, LA11/b;->a()F

    .line 806
    .line 807
    .line 808
    move-result v15

    .line 809
    invoke-static {v4, v13}, LA11/d;->c(Landroidx/compose/runtime/j;I)LA11/b;

    .line 810
    .line 811
    .line 812
    move-result-object v2

    .line 813
    invoke-virtual {v2}, LA11/b;->a()F

    .line 814
    .line 815
    .line 816
    move-result v17

    .line 817
    const/16 v19, 0xa

    .line 818
    .line 819
    const/16 v20, 0x0

    .line 820
    .line 821
    const/16 v16, 0x0

    .line 822
    .line 823
    const/16 v18, 0x0

    .line 824
    .line 825
    invoke-static/range {v14 .. v20}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 826
    .line 827
    .line 828
    move-result-object v2

    .line 829
    invoke-static {v1, v2, v4, v13, v13}, LIN0/t;->h(ILandroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 830
    .line 831
    .line 832
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 833
    .line 834
    .line 835
    :goto_5
    invoke-interface {v4}, Landroidx/compose/runtime/j;->j()V

    .line 836
    .line 837
    .line 838
    invoke-interface {v4}, Landroidx/compose/runtime/j;->j()V

    .line 839
    .line 840
    .line 841
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 842
    .line 843
    .line 844
    move-result v1

    .line 845
    if-eqz v1, :cond_17

    .line 846
    .line 847
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 848
    .line 849
    .line 850
    :cond_17
    return-void

    .line 851
    :cond_18
    const v1, 0x755fb640

    .line 852
    .line 853
    .line 854
    invoke-interface {v4, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 855
    .line 856
    .line 857
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 858
    .line 859
    .line 860
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    .line 861
    .line 862
    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 863
    .line 864
    .line 865
    throw v1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/foundation/layout/Y;

    .line 2
    .line 3
    check-cast p2, Landroidx/compose/runtime/j;

    .line 4
    .line 5
    check-cast p3, Ljava/lang/Number;

    .line 6
    .line 7
    invoke-virtual {p3}, Ljava/lang/Number;->intValue()I

    .line 8
    .line 9
    .line 10
    move-result p3

    .line 11
    invoke-virtual {p0, p1, p2, p3}, LFR0/b$b;->e(Landroidx/compose/foundation/layout/Y;Landroidx/compose/runtime/j;I)V

    .line 12
    .line 13
    .line 14
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p1
.end method
