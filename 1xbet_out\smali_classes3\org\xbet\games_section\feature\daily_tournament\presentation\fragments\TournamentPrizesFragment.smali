.class public final Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;
.super LXW0/a;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/a;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0008\u0005*\u00017\u0018\u00002\u00020\u00012\u00020\u0002B\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u001d\u0010\t\u001a\u00020\u00082\u000c\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u0005H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000b\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u0004J\u000f\u0010\r\u001a\u00020\u000cH\u0016\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u000f\u0010\u000f\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u000f\u0010\u0004J\u0019\u0010\u0012\u001a\u00020\u00082\u0008\u0010\u0011\u001a\u0004\u0018\u00010\u0010H\u0014\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u0014\u0010\u0004J\u000f\u0010\u0015\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008\u0015\u0010\u0004J\u000f\u0010\u0016\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0004J\u0017\u0010\u0019\u001a\u00020\u00082\u0006\u0010\u0018\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aR\"\u0010\"\u001a\u00020\u001b8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u001c\u0010\u001d\u001a\u0004\u0008\u001e\u0010\u001f\"\u0004\u0008 \u0010!R\u001b\u0010(\u001a\u00020#8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008$\u0010%\u001a\u0004\u0008&\u0010\'R\u001b\u0010+\u001a\u00020\u000c8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008)\u0010%\u001a\u0004\u0008*\u0010\u000eR\u001b\u00100\u001a\u00020,8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008-\u0010%\u001a\u0004\u0008.\u0010/R\u001b\u00106\u001a\u0002018BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00082\u00103\u001a\u0004\u00084\u00105R\u0014\u0010:\u001a\u0002078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00109\u00a8\u0006;"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;",
        "LXW0/a;",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/a;",
        "<init>",
        "()V",
        "",
        "Lp40/c;",
        "data",
        "",
        "N2",
        "(Ljava/util/List;)V",
        "L2",
        "Ln40/e;",
        "a1",
        "()Ln40/e;",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "onDestroyView",
        "x2",
        "Lorg/xbet/uikit/components/lottie/a;",
        "config",
        "M2",
        "(Lorg/xbet/uikit/components/lottie/a;)V",
        "Ln40/e$b;",
        "i0",
        "Ln40/e$b;",
        "K2",
        "()Ln40/e$b;",
        "setViewModelFactory",
        "(Ln40/e$b;)V",
        "viewModelFactory",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;",
        "j0",
        "Lkotlin/j;",
        "J2",
        "()Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;",
        "viewModel",
        "k0",
        "G2",
        "component",
        "Lr40/a;",
        "l0",
        "H2",
        "()Lr40/a;",
        "dailyPrizesAdapter",
        "Lm40/c;",
        "m0",
        "LRc/c;",
        "I2",
        "()Lm40/c;",
        "viewBinding",
        "org/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$a",
        "n0",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$a;",
        "scrollCallback",
        "daily_tournament_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final synthetic o0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public i0:Ln40/e$b;

.field public final j0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getViewBinding()Lorg/xbet/games_section/feature/daily_tournament/databinding/DailyTournamentPrizesFgBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;

    .line 7
    .line 8
    const-string v4, "viewBinding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->o0:[Lkotlin/reflect/m;

    .line 23
    .line 24
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, Lh40/b;->daily_tournament_prizes_fg:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/n;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/n;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;)V

    .line 9
    .line 10
    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$special$$inlined$viewModels$default$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 17
    .line 18
    new-instance v3, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$special$$inlined$viewModels$default$2;

    .line 19
    .line 20
    invoke-direct {v3, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 21
    .line 22
    .line 23
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const-class v2, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;

    .line 28
    .line 29
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    new-instance v3, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$special$$inlined$viewModels$default$3;

    .line 34
    .line 35
    invoke-direct {v3, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 36
    .line 37
    .line 38
    new-instance v4, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$special$$inlined$viewModels$default$4;

    .line 39
    .line 40
    const/4 v5, 0x0

    .line 41
    invoke-direct {v4, v5, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->j0:Lkotlin/j;

    .line 49
    .line 50
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/o;

    .line 51
    .line 52
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/o;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;)V

    .line 53
    .line 54
    .line 55
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->k0:Lkotlin/j;

    .line 60
    .line 61
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/p;

    .line 62
    .line 63
    invoke-direct {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/p;-><init>()V

    .line 64
    .line 65
    .line 66
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->l0:Lkotlin/j;

    .line 71
    .line 72
    sget-object v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$viewBinding$2;->INSTANCE:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$viewBinding$2;

    .line 73
    .line 74
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->m0:LRc/c;

    .line 79
    .line 80
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$a;

    .line 81
    .line 82
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$a;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;)V

    .line 83
    .line 84
    .line 85
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->n0:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$a;

    .line 86
    .line 87
    return-void
.end method

.method public static synthetic A2()Lr40/a;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->F2()Lr40/a;

    move-result-object v0

    return-object v0
.end method

.method public static final synthetic B2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;)Lm40/c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->I2()Lm40/c;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic C2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;Lorg/xbet/uikit/components/lottie/a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->M2(Lorg/xbet/uikit/components/lottie/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->N2(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final E2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;)Ln40/e;
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/a;

    .line 6
    .line 7
    invoke-interface {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/a;->a1()Ln40/e;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    return-object p0
.end method

.method public static final F2()Lr40/a;
    .locals 1

    .line 1
    new-instance v0, Lr40/a;

    .line 2
    .line 3
    invoke-direct {v0}, Lr40/a;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method private final G2()Ln40/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ln40/e;

    .line 8
    .line 9
    return-object v0
.end method

.method private final L2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->I2()Lm40/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lm40/c;->d:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->I2()Lm40/c;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iget-object v0, v0, Lm40/c;->e:Landroid/widget/FrameLayout;

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 19
    .line 20
    .line 21
    return-void
.end method

.method private final N2(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lp40/c;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->L2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->I2()Lm40/c;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, Lm40/c;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 9
    .line 10
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->H2()Lr40/a;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 19
    .line 20
    .line 21
    move-result v0

    .line 22
    if-nez v0, :cond_0

    .line 23
    .line 24
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->I2()Lm40/c;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    iget-object v0, v0, Lm40/c;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 29
    .line 30
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->H2()Lr40/a;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 35
    .line 36
    .line 37
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->H2()Lr40/a;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    invoke-virtual {v0, p1}, LUX0/h;->B(Ljava/util/List;)V

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method public static final O2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;)Landroidx/lifecycle/e0$c;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/a;

    .line 2
    .line 3
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->K2()Ln40/e$b;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, v1, p0}, Lorg/xbet/ui_common/viewmodel/core/a;-><init>(LwX0/c;LQW0/i;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;)Ln40/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->E2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;)Ln40/e;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->O2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final H2()Lr40/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->l0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lr40/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public final I2()Lm40/c;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->m0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->o0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lm40/c;

    .line 13
    .line 14
    return-object v0
.end method

.method public final J2()Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->j0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final K2()Ln40/e$b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->i0:Ln40/e$b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final M2(Lorg/xbet/uikit/components/lottie/a;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->I2()Lm40/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lm40/c;->e:Landroid/widget/FrameLayout;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->I2()Lm40/c;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    iget-object v0, v0, Lm40/c;->d:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 16
    .line 17
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/lottie/LottieView;->L(Lorg/xbet/uikit/components/lottie/a;)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public a1()Ln40/e;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->G2()Ln40/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public onDestroyView()V
    .locals 2

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroyView()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->I2()Lm40/c;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, Lm40/c;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 9
    .line 10
    const/4 v1, 0x0

    .line 11
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 2

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->I2()Lm40/c;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object p1, p1, Lm40/c;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 9
    .line 10
    new-instance v0, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->I2()Lm40/c;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    iget-object v1, v1, Lm40/c;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 17
    .line 18
    invoke-virtual {v1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    invoke-direct {v0, v1}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->I2()Lm40/c;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    iget-object p1, p1, Lm40/c;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 33
    .line 34
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->n0:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$a;

    .line 35
    .line 36
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->addOnScrollListener(Landroidx/recyclerview/widget/RecyclerView$s;)V

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method public u2()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->G2()Ln40/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0, p0}, Ln40/e;->a(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public v2()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;->J2()Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->y3()Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$onObserveData$1;

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    invoke-direct {v5, p0, v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$onObserveData$1;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPrizesFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    move-object v6, v0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public x2()V
    .locals 0

    .line 1
    return-void
.end method
