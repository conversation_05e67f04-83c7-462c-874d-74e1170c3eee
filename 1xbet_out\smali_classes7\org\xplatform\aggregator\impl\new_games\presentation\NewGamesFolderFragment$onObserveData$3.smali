.class final Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.new_games.presentation.NewGamesFolderFragment$onObserveData$3"
    f = "NewGamesFolderFragment.kt"
    l = {
        0xb9,
        0xbc
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;->invoke(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    goto :goto_1

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    goto :goto_2

    .line 31
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;->L$0:Ljava/lang/Object;

    .line 35
    .line 36
    check-cast p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;

    .line 37
    .line 38
    instance-of v1, p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$a;

    .line 39
    .line 40
    if-eqz v1, :cond_3

    .line 41
    .line 42
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    .line 43
    .line 44
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->u3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)LS91/U;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    iget-object v1, v1, LS91/U;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 49
    .line 50
    check-cast p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$a;

    .line 51
    .line 52
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$a;->a()Landroidx/paging/PagingData;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    iput v3, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;->label:I

    .line 57
    .line 58
    invoke-virtual {v1, p1, p0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->m(Landroidx/paging/PagingData;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    if-ne p1, v0, :cond_6

    .line 63
    .line 64
    goto :goto_0

    .line 65
    :cond_3
    sget-object v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$c;->a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$c;

    .line 66
    .line 67
    invoke-static {p1, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 68
    .line 69
    .line 70
    move-result v1

    .line 71
    if-eqz v1, :cond_5

    .line 72
    .line 73
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    .line 74
    .line 75
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->u3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)LS91/U;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    iget-object p1, p1, LS91/U;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 80
    .line 81
    sget-object v1, Landroidx/paging/PagingData;->e:Landroidx/paging/PagingData$Companion;

    .line 82
    .line 83
    invoke-virtual {v1}, Landroidx/paging/PagingData$Companion;->a()Landroidx/paging/PagingData;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    iput v2, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;->label:I

    .line 88
    .line 89
    invoke-virtual {p1, v1, p0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->m(Landroidx/paging/PagingData;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 90
    .line 91
    .line 92
    move-result-object p1

    .line 93
    if-ne p1, v0, :cond_4

    .line 94
    .line 95
    :goto_0
    return-object v0

    .line 96
    :cond_4
    :goto_1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    .line 97
    .line 98
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->u3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)LS91/U;

    .line 99
    .line 100
    .line 101
    move-result-object p1

    .line 102
    iget-object p1, p1, LS91/U;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 103
    .line 104
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->o()V

    .line 105
    .line 106
    .line 107
    goto :goto_2

    .line 108
    :cond_5
    instance-of p1, p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$b;

    .line 109
    .line 110
    if-eqz p1, :cond_7

    .line 111
    .line 112
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    .line 113
    .line 114
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->u3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)LS91/U;

    .line 115
    .line 116
    .line 117
    move-result-object p1

    .line 118
    iget-object p1, p1, LS91/U;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 119
    .line 120
    const/16 v0, 0x8

    .line 121
    .line 122
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 123
    .line 124
    .line 125
    :cond_6
    :goto_2
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 126
    .line 127
    return-object p1

    .line 128
    :cond_7
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 129
    .line 130
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 131
    .line 132
    .line 133
    throw p1
.end method
