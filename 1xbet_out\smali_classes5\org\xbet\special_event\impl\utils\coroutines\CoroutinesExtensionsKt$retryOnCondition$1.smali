.class final Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/o;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.utils.coroutines.CoroutinesExtensionsKt$retryOnCondition$1"
    f = "CoroutinesExtensions.kt"
    l = {
        0x12
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt;->a(Lkotlinx/coroutines/flow/e;JJLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/o<",
        "Lkotlinx/coroutines/flow/f<",
        "-TT;>;",
        "Ljava/lang/Throwable;",
        "Ljava/lang/Long;",
        "Lkotlin/coroutines/e<",
        "-",
        "Ljava/lang/Boolean;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0003\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0002\u0010\u0007\u001a\u00020\u0006\"\u0004\u0008\u0000\u0010\u0000*\u0008\u0012\u0004\u0012\u00028\u00000\u00012\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0004H\n\u00a2\u0006\u0004\u0008\u0007\u0010\u0008"
    }
    d2 = {
        "T",
        "Lkotlinx/coroutines/flow/f;",
        "",
        "cause",
        "",
        "attempt",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/flow/f;Ljava/lang/Throwable;J)Z"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $count:J

.field final synthetic $delayInSec:J

.field final synthetic $isCondition:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Throwable;",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic $negativeAction:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic $positiveAction:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field synthetic J$0:J

.field synthetic L$0:Ljava/lang/Object;

.field label:I


# direct methods
.method public constructor <init>(JLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;JLkotlin/jvm/functions/Function0;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Throwable;",
            "Ljava/lang/Boolean;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;J",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;",
            ">;)V"
        }
    .end annotation

    iput-wide p1, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$count:J

    iput-object p3, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$isCondition:Lkotlin/jvm/functions/Function1;

    iput-object p4, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$positiveAction:Lkotlin/jvm/functions/Function0;

    iput-wide p5, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$delayInSec:J

    iput-object p7, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$negativeAction:Lkotlin/jvm/functions/Function0;

    const/4 p1, 0x4

    invoke-direct {p0, p1, p8}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    move-object v1, p1

    check-cast v1, Lkotlinx/coroutines/flow/f;

    move-object v2, p2

    check-cast v2, Ljava/lang/Throwable;

    check-cast p3, Ljava/lang/Number;

    invoke-virtual {p3}, Ljava/lang/Number;->longValue()J

    move-result-wide v3

    move-object v5, p4

    check-cast v5, Lkotlin/coroutines/e;

    move-object v0, p0

    invoke-virtual/range {v0 .. v5}, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->invoke(Lkotlinx/coroutines/flow/f;Ljava/lang/Throwable;JLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/flow/f;Ljava/lang/Throwable;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/f<",
            "-TT;>;",
            "Ljava/lang/Throwable;",
            "J",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/lang/Boolean;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;

    iget-wide v1, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$count:J

    iget-object v3, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$isCondition:Lkotlin/jvm/functions/Function1;

    iget-object v4, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$positiveAction:Lkotlin/jvm/functions/Function0;

    iget-wide v5, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$delayInSec:J

    iget-object v7, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$negativeAction:Lkotlin/jvm/functions/Function0;

    move-object v8, p5

    invoke-direct/range {v0 .. v8}, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;-><init>(JLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;JLkotlin/jvm/functions/Function0;Lkotlin/coroutines/e;)V

    iput-object p2, v0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->L$0:Ljava/lang/Object;

    iput-wide p3, v0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->J$0:J

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->L$0:Ljava/lang/Object;

    .line 28
    .line 29
    check-cast p1, Ljava/lang/Throwable;

    .line 30
    .line 31
    iget-wide v3, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->J$0:J

    .line 32
    .line 33
    iget-wide v5, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$count:J

    .line 34
    .line 35
    cmp-long v1, v3, v5

    .line 36
    .line 37
    if-nez v1, :cond_3

    .line 38
    .line 39
    iget-object v1, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$isCondition:Lkotlin/jvm/functions/Function1;

    .line 40
    .line 41
    invoke-interface {v1, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    check-cast p1, Ljava/lang/Boolean;

    .line 46
    .line 47
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 48
    .line 49
    .line 50
    move-result p1

    .line 51
    if-eqz p1, :cond_3

    .line 52
    .line 53
    iget-object p1, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$positiveAction:Lkotlin/jvm/functions/Function0;

    .line 54
    .line 55
    if-eqz p1, :cond_2

    .line 56
    .line 57
    invoke-interface {p1}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    :cond_2
    iget-wide v3, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$delayInSec:J

    .line 61
    .line 62
    const-wide/16 v5, 0x3e8

    .line 63
    .line 64
    mul-long v3, v3, v5

    .line 65
    .line 66
    iput v2, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->label:I

    .line 67
    .line 68
    invoke-static {v3, v4, p0}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    if-ne p1, v0, :cond_5

    .line 73
    .line 74
    return-object v0

    .line 75
    :cond_3
    iget-object p1, p0, Lorg/xbet/special_event/impl/utils/coroutines/CoroutinesExtensionsKt$retryOnCondition$1;->$negativeAction:Lkotlin/jvm/functions/Function0;

    .line 76
    .line 77
    if-eqz p1, :cond_4

    .line 78
    .line 79
    invoke-interface {p1}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    :cond_4
    const/4 v2, 0x0

    .line 83
    :cond_5
    :goto_0
    invoke-static {v2}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 84
    .line 85
    .line 86
    move-result-object p1

    .line 87
    return-object p1
.end method
