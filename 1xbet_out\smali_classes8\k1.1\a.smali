.class public Lk1/a;
.super Lk1/d;
.source "SourceFile"


# static fields
.field public static final c:[F


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const/16 v0, 0xc9

    .line 2
    .line 3
    new-array v0, v0, [F

    .line 4
    .line 5
    fill-array-data v0, :array_0

    .line 6
    .line 7
    .line 8
    sput-object v0, Lk1/a;->c:[F

    .line 9
    .line 10
    return-void

    .line 11
    :array_0
    .array-data 4
        0x0
        0x38d1b717
        0x3951b717
        0x3a03126f
        0x3a51b717
        0x3aaa64c3
        0x3aebedfa
        0x3b1d4952
        0x3b51b717
        0x3b83126f
        0x3ba0902e
        0x3bc154ca
        0x3be21965
        0x3c04b5dd
        0x3c185f07
        0x3c2dab9f
        0x3c449ba6
        0x3c5d2f1b
        0x3c7765fe
        0x3c88ce70
        0x3c96bb99
        0x3ca4a8c1
        0x3cb43958
        0x3cc3c9ef
        0x3cd42c3d
        0x3ce48e8a
        0x3cf5c28f
        0x3d03e426
        0x3d0d4fdf
        0x3d16bb99
        0x3d202752
        0x3d2a64c3
        0x3d34a234
        0x3d3edfa4
        0x3d4985f0
        0x3d549518
        0x3d600d1b
        0x3d6b1c43
        0x3d76fd22
        0x3d816f00
        0x3d8793de
        0x3d8db8bb
        0x3d93dd98
        0x3d9a6b51
        0x3da0c49c
        0x3da75254
        0x3dae147b
        0x3db50b0f
        0x3dbbcd36
        0x3dc2f838
        0x3dc9eecc
        0x3dd119ce
        0x3dd8793e
        0x3ddfd8ae
        0x3de76c8b
        0x3def0069
        0x3df6c8b4
        0x3dfe9100
        0x3e032ca5
        0x3e072b02
        0x3e0b295f
        0x3e0f41f2
        0x3e135a86
        0x3e178d50
        0x3e1bc01a
        0x3e1ff2e5
        0x3e243fe6
        0x3e28a71e
        0x3e2cf41f
        0x3e315b57
        0x3e35dcc6
        0x3e3a5e35
        0x3e3edfa4
        0x3e437b4a
        0x3e4816f0
        0x3e4cb296
        0x3e516873
        0x3e561e4f
        0x3e5aee63
        0x3e5fa440
        0x3e648e8a
        0x3e695e9e
        0x3e6e48e9
        0x3e734d6a
        0x3e7837b5
        0x3e7d3c36
        0x3e812d77
        0x3e83afb8
        0x3e863f14
        0x3e88db8c
        0x3e8b6ae8
        0x3e8e075f
        0x3e90b0f2
        0x3e934d6a
        0x3e95f6fd
        0x3e98adac
        0x3e9b573f
        0x3e9e0ded
        0x3ea0d1b7
        0x3ea38866
        0x3ea64c30
        0x3ea90ff9
        0x3eabe0df
        0x3eaeb1c4
        0x3eb182aa
        0x3eb4538f
        0x3eb73190
        0x3eba0f91
        0x3ebced91
        0x3ebfd8ae
        0x3ec2c3ca
        0x3ec5aee6
        0x3ec89a02
        0x3ecb923a
        0x3ece8a72
        0x3ed182aa
        0x3ed487fd
        0x3ed78d50
        0x3eda92a3
        0x3edda512
        0x3ee0aa65
        0x3ee3bcd3
        0x3ee6cf42
        0x3ee9eecc
        0x3eed0e56
        0x3ef02de0
        0x3ef34d6a
        0x3ef67a10
        0x3ef9a6b5
        0x3efcd35b
        0x3f000000    # 0.5f
        0x3f019ce0
        0x3f0339c1
        0x3f04d6a1
        0x3f067382
        0x3f0816f0
        0x3f09ba5e
        0x3f0b5dcc
        0x3f0d013b
        0x3f0eab36
        0x3f105532
        0x3f11ff2e
        0x3f13a92a
        0x3f1559b4
        0x3f170a3d
        0x3f18bac7
        0x3f1a71de
        0x3f1c2268
        0x3f1dd97f
        0x3f1f9097
        0x3f214e3c
        0x3f230be1
        0x3f24c2f8
        0x3f26872b
        0x3f2844d0
        0x3f2a0903
        0x3f2bc6a8
        0x3f2d9168
        0x3f2f559b
        0x3f31205c
        0x3f32e48f
        0x3f34b5dd
        0x3f36809d
        0x3f384b5e
        0x3f3a1cac
        0x3f3bedfa
        0x3f3dc5d6
        0x3f3f9724
        0x3f416f00
        0x3f4346dc
        0x3f451eb8
        0x3f46fd22
        0x3f48db8c
        0x3f4ab9f5
        0x3f4c985f
        0x3f4e7d56
        0x3f50624e
        0x3f524745
        0x3f542c3d
        0x3f5617c2
        0x3f57fcb9
        0x3f59eecc
        0x3f5bda51
        0x3f5dcc64
        0x3f5fbe77
        0x3f61b08a
        0x3f63a29c
        0x3f659b3d
        0x3f6793de
        0x3f69930c
        0x3f6b8bac
        0x3f6d9168
        0x3f6f9097
        0x3f719653
        0x3f739c0f
        0x3f75a1cb
        0x3f77ae14
        0x3f79ba5e
        0x3f7bcd36
        0x3f7de69b
        0x3f800000    # 1.0f
    .end array-data
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    sget-object v0, Lk1/a;->c:[F

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lk1/d;-><init>([F)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public bridge synthetic getInterpolation(F)F
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lk1/d;->getInterpolation(F)F

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method
