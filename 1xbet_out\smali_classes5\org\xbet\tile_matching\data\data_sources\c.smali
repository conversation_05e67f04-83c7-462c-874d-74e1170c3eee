.class public final synthetic Lorg/xbet/tile_matching/data/data_sources/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lf8/g;


# direct methods
.method public synthetic constructor <init>(Lf8/g;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/tile_matching/data/data_sources/c;->a:Lf8/g;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/data_sources/c;->a:Lf8/g;

    invoke-static {v0}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->a(Lf8/g;)LsT0/b;

    move-result-object v0

    return-object v0
.end method
