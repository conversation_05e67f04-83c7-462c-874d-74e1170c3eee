.class public final LgS0/a$d$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/h;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LgS0/a$d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/h<",
        "Lorg/xbet/analytics/domain/b;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LQv/v;


# direct methods
.method public constructor <init>(LQv/v;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LgS0/a$d$a;->a:LQv/v;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a()Lorg/xbet/analytics/domain/b;
    .locals 1

    .line 1
    iget-object v0, p0, LgS0/a$d$a;->a:LQv/v;

    .line 2
    .line 3
    invoke-interface {v0}, LQv/v;->v()Lorg/xbet/analytics/domain/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, Lorg/xbet/analytics/domain/b;

    .line 12
    .line 13
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LgS0/a$d$a;->a()Lorg/xbet/analytics/domain/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
