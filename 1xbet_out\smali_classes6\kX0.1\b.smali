.class public final LkX0/b;
.super LkX0/a;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u00002\u00020\u0001B\u0011\u0008\u0000\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\'\u0010\u000c\u001a\u00020\u000b2\u000e\u0010\u0008\u001a\n\u0012\u0006\u0008\u0001\u0012\u00020\u00070\u00062\u0006\u0010\n\u001a\u00020\tH\u0014\u00a2\u0006\u0004\u0008\u000c\u0010\rR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u000f\u00a8\u0006\u0010"
    }
    d2 = {
        "LkX0/b;",
        "LkX0/a;",
        "Landroid/app/Activity;",
        "activity",
        "<init>",
        "(Landroid/app/Activity;)V",
        "",
        "",
        "permissions",
        "LpX0/e;",
        "runtimeHandlerProvider",
        "LnX0/b;",
        "b",
        "([Ljava/lang/String;LpX0/e;)LnX0/b;",
        "c",
        "Landroid/app/Activity;",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final c:Landroid/app/Activity;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Landroid/app/Activity;)V
    .locals 0
    .param p1    # Landroid/app/Activity;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, LkX0/a;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LkX0/b;->c:Landroid/app/Activity;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public b([Ljava/lang/String;LpX0/e;)LnX0/b;
    .locals 2
    .param p1    # [Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LpX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    .line 2
    .line 3
    const/16 v1, 0x17

    .line 4
    .line 5
    if-lt v0, v1, :cond_0

    .line 6
    .line 7
    invoke-interface {p2}, LpX0/e;->a()LpX0/d;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    new-instance v0, LpX0/f;

    .line 12
    .line 13
    iget-object v1, p0, LkX0/b;->c:Landroid/app/Activity;

    .line 14
    .line 15
    invoke-direct {v0, v1, p1, p2}, LpX0/f;-><init>(Landroid/app/Activity;[Ljava/lang/String;LpX0/d;)V

    .line 16
    .line 17
    .line 18
    return-object v0

    .line 19
    :cond_0
    new-instance p2, LoX0/a;

    .line 20
    .line 21
    iget-object v0, p0, LkX0/b;->c:Landroid/app/Activity;

    .line 22
    .line 23
    invoke-direct {p2, v0, p1}, LoX0/a;-><init>(Landroid/content/Context;[Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    return-object p2
.end method
