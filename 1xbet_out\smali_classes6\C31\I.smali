.class public final LC31/I;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LL2/a;


# instance fields
.field public final a:Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipMediumClear;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipMediumClear;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipMediumClear;Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipMediumClear;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipMediumClear;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipMediumClear;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LC31/I;->a:Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipMediumClear;

    .line 5
    .line 6
    iput-object p2, p0, LC31/I;->b:Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipMediumClear;

    .line 7
    .line 8
    iput-object p3, p0, LC31/I;->c:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 9
    .line 10
    iput-object p4, p0, LC31/I;->d:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 11
    .line 12
    iput-object p5, p0, LC31/I;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 13
    .line 14
    return-void
.end method

.method public static a(Landroid/view/View;)LC31/I;
    .locals 6
    .param p0    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    move-object v1, p0

    .line 2
    check-cast v1, Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipMediumClear;

    .line 3
    .line 4
    sget v0, Lm31/d;->cellLeft:I

    .line 5
    .line 6
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 7
    .line 8
    .line 9
    move-result-object v2

    .line 10
    move-object v3, v2

    .line 11
    check-cast v3, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 12
    .line 13
    if-eqz v3, :cond_0

    .line 14
    .line 15
    sget v0, Lm31/d;->cellMiddle:I

    .line 16
    .line 17
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    move-object v4, v2

    .line 22
    check-cast v4, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 23
    .line 24
    if-eqz v4, :cond_0

    .line 25
    .line 26
    sget v0, Lm31/d;->cellRight:I

    .line 27
    .line 28
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    move-object v5, v2

    .line 33
    check-cast v5, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 34
    .line 35
    if-eqz v5, :cond_0

    .line 36
    .line 37
    new-instance v0, LC31/I;

    .line 38
    .line 39
    move-object v2, v1

    .line 40
    invoke-direct/range {v0 .. v5}, LC31/I;-><init>(Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipMediumClear;Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipMediumClear;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)V

    .line 41
    .line 42
    .line 43
    return-object v0

    .line 44
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    invoke-virtual {p0, v0}, Landroid/content/res/Resources;->getResourceName(I)Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object p0

    .line 52
    new-instance v0, Ljava/lang/NullPointerException;

    .line 53
    .line 54
    const-string v1, "Missing required view with ID: "

    .line 55
    .line 56
    invoke-virtual {v1, p0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object p0

    .line 60
    invoke-direct {v0, p0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 61
    .line 62
    .line 63
    throw v0
.end method

.method public static c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LC31/I;
    .locals 2
    .param p0    # Landroid/view/LayoutInflater;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    sget v0, Lm31/e;->item_sport_feeds_cell_championship_medium_clear:I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {p0, v0, p1, v1}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;Z)Landroid/view/View;

    .line 5
    .line 6
    .line 7
    move-result-object p0

    .line 8
    if-eqz p2, :cond_0

    .line 9
    .line 10
    invoke-virtual {p1, p0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 11
    .line 12
    .line 13
    :cond_0
    invoke-static {p0}, LC31/I;->a(Landroid/view/View;)LC31/I;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    return-object p0
.end method


# virtual methods
.method public b()Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipMediumClear;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    iget-object v0, p0, LC31/I;->a:Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipMediumClear;

    .line 2
    .line 3
    return-object v0
.end method

.method public bridge synthetic getRoot()Landroid/view/View;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LC31/I;->b()Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipMediumClear;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
