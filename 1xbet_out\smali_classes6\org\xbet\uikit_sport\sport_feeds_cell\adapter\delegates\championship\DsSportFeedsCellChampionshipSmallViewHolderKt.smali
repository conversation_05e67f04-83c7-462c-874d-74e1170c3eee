.class public final Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipSmallViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a!\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "Ld41/a;",
        "clickListener",
        "LA4/c;",
        "",
        "Le41/a;",
        "i",
        "(Ld41/a;)LA4/c;",
        "uikit_sport_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipSmallViewHolderKt;->o(Ld41/a;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic b(Ld41/a;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipSmallViewHolderKt;->k(Ld41/a;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipSmallViewHolderKt;->l(Ld41/a;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic d(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/J;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipSmallViewHolderKt;->j(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/J;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipSmallViewHolderKt;->q(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipSmallViewHolderKt;->n(Ld41/a;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic g(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipSmallViewHolderKt;->p(Ld41/a;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic h(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipSmallViewHolderKt;->m(Ld41/a;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static final i(Ld41/a;)LA4/c;
    .locals 4
    .param p0    # Ld41/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ld41/a;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "Le41/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lb41/v;

    .line 2
    .line 3
    invoke-direct {v0}, Lb41/v;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lb41/w;

    .line 7
    .line 8
    invoke-direct {v1, p0}, Lb41/w;-><init>(Ld41/a;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipSmallViewHolderKt$sportFeedsCellChampionshipSmallViewHolder$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipSmallViewHolderKt$sportFeedsCellChampionshipSmallViewHolder$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipSmallViewHolderKt$sportFeedsCellChampionshipSmallViewHolder$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipSmallViewHolderKt$sportFeedsCellChampionshipSmallViewHolder$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final j(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/J;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LC31/J;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LC31/J;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final k(Ld41/a;LB4/a;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LC31/J;

    .line 6
    .line 7
    iget-object v0, v0, LC31/J;->b:Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipSmall;

    .line 8
    .line 9
    new-instance v1, Lb41/x;

    .line 10
    .line 11
    invoke-direct {v1, p0, p1}, Lb41/x;-><init>(Ld41/a;LB4/a;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, LC31/J;

    .line 22
    .line 23
    iget-object v0, v0, LC31/J;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 24
    .line 25
    new-instance v1, Lb41/y;

    .line 26
    .line 27
    invoke-direct {v1, p0, p1}, Lb41/y;-><init>(Ld41/a;LB4/a;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setAccordionClickListener(Landroid/view/View$OnClickListener;)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    check-cast v0, LC31/J;

    .line 38
    .line 39
    iget-object v0, v0, LC31/J;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 40
    .line 41
    new-instance v1, Lb41/z;

    .line 42
    .line 43
    invoke-direct {v1, p0, p1}, Lb41/z;-><init>(Ld41/a;LB4/a;)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setListCheckBoxClickListener(Landroid/view/View$OnClickListener;)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    check-cast v0, LC31/J;

    .line 54
    .line 55
    iget-object v0, v0, LC31/J;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 56
    .line 57
    new-instance v1, Lb41/A;

    .line 58
    .line 59
    invoke-direct {v1, p0, p1}, Lb41/A;-><init>(Ld41/a;LB4/a;)V

    .line 60
    .line 61
    .line 62
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setActionIconClickListener(Landroid/view/View$OnClickListener;)V

    .line 63
    .line 64
    .line 65
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    check-cast v0, LC31/J;

    .line 70
    .line 71
    iget-object v0, v0, LC31/J;->c:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 72
    .line 73
    new-instance v1, Lb41/B;

    .line 74
    .line 75
    invoke-direct {v1, p0, p1}, Lb41/B;-><init>(Ld41/a;LB4/a;)V

    .line 76
    .line 77
    .line 78
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setActionIconClickListener(Landroid/view/View$OnClickListener;)V

    .line 79
    .line 80
    .line 81
    new-instance p0, Lb41/C;

    .line 82
    .line 83
    invoke-direct {p0, p1}, Lb41/C;-><init>(LB4/a;)V

    .line 84
    .line 85
    .line 86
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 87
    .line 88
    .line 89
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 90
    .line 91
    return-object p0
.end method

.method public static final l(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lf41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/a;->c(Lf41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final m(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lf41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/a;->a(Lf41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final n(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lf41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/a;->d(Lf41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final o(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lf41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/a;->b(Lf41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final p(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lf41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/a;->b(Lf41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final q(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lf41/a;

    .line 6
    .line 7
    invoke-virtual {p1}, Lf41/a;->h()Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    sget-object v0, Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;->CHAMPIONSHIP_COUNTER_WITH_ACTION_ICON:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;

    .line 12
    .line 13
    if-ne p1, v0, :cond_0

    .line 14
    .line 15
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    check-cast p1, LC31/J;

    .line 20
    .line 21
    iget-object p1, p1, LC31/J;->c:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 22
    .line 23
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    check-cast v0, Lf41/a;

    .line 28
    .line 29
    invoke-virtual {v0}, Lf41/a;->i()Z

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setFavoriteIcon(Z)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    check-cast p1, LC31/J;

    .line 41
    .line 42
    iget-object p1, p1, LC31/J;->c:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 43
    .line 44
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;->NONE:Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;

    .line 45
    .line 46
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setBadge(Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    check-cast p1, LC31/J;

    .line 54
    .line 55
    iget-object p1, p1, LC31/J;->c:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 56
    .line 57
    const/4 v0, 0x0

    .line 58
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setCounterNumber(Ljava/lang/Integer;)V

    .line 59
    .line 60
    .line 61
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    check-cast p1, LC31/J;

    .line 66
    .line 67
    iget-object p1, p1, LC31/J;->c:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 68
    .line 69
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    check-cast v0, Lf41/a;

    .line 74
    .line 75
    invoke-virtual {v0}, Lf41/a;->e()Ljava/lang/Integer;

    .line 76
    .line 77
    .line 78
    move-result-object v0

    .line 79
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setIconTintByColorAttr(Ljava/lang/Integer;)V

    .line 80
    .line 81
    .line 82
    goto :goto_0

    .line 83
    :cond_0
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 84
    .line 85
    .line 86
    move-result-object p1

    .line 87
    check-cast p1, LC31/J;

    .line 88
    .line 89
    iget-object p1, p1, LC31/J;->c:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 90
    .line 91
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 92
    .line 93
    .line 94
    move-result-object v0

    .line 95
    check-cast v0, Lf41/a;

    .line 96
    .line 97
    invoke-virtual {v0}, Lf41/a;->j()I

    .line 98
    .line 99
    .line 100
    move-result v0

    .line 101
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setIcon(I)V

    .line 102
    .line 103
    .line 104
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 105
    .line 106
    .line 107
    move-result-object p1

    .line 108
    check-cast p1, LC31/J;

    .line 109
    .line 110
    iget-object p1, p1, LC31/J;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 111
    .line 112
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 113
    .line 114
    .line 115
    move-result-object v0

    .line 116
    check-cast v0, Lf41/a;

    .line 117
    .line 118
    invoke-virtual {v0}, Lf41/a;->f()Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;

    .line 119
    .line 120
    .line 121
    move-result-object v0

    .line 122
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setCustomBadgeType(Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;)V

    .line 123
    .line 124
    .line 125
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 126
    .line 127
    .line 128
    move-result-object p1

    .line 129
    check-cast p1, LC31/J;

    .line 130
    .line 131
    iget-object p1, p1, LC31/J;->c:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 132
    .line 133
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 134
    .line 135
    .line 136
    move-result-object v0

    .line 137
    check-cast v0, Lf41/a;

    .line 138
    .line 139
    invoke-virtual {v0}, Lf41/a;->l()Ljava/lang/Integer;

    .line 140
    .line 141
    .line 142
    move-result-object v0

    .line 143
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setCounterNumber(Ljava/lang/Integer;)V

    .line 144
    .line 145
    .line 146
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 147
    .line 148
    .line 149
    move-result-object p1

    .line 150
    check-cast p1, LC31/J;

    .line 151
    .line 152
    iget-object p1, p1, LC31/J;->c:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 153
    .line 154
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 155
    .line 156
    .line 157
    move-result-object v0

    .line 158
    check-cast v0, Lf41/a;

    .line 159
    .line 160
    invoke-virtual {v0}, Lf41/a;->k()Ljava/lang/Integer;

    .line 161
    .line 162
    .line 163
    move-result-object v0

    .line 164
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setIconTintByColorAttr(Ljava/lang/Integer;)V

    .line 165
    .line 166
    .line 167
    :goto_0
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 168
    .line 169
    .line 170
    move-result-object p1

    .line 171
    check-cast p1, LC31/J;

    .line 172
    .line 173
    iget-object p1, p1, LC31/J;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 174
    .line 175
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 176
    .line 177
    .line 178
    move-result-object v0

    .line 179
    check-cast v0, Lf41/a;

    .line 180
    .line 181
    invoke-virtual {v0}, Lf41/a;->e()Ljava/lang/Integer;

    .line 182
    .line 183
    .line 184
    move-result-object v0

    .line 185
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setActionIconTintByColorAttr(Ljava/lang/Integer;)V

    .line 186
    .line 187
    .line 188
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 189
    .line 190
    .line 191
    move-result-object p1

    .line 192
    check-cast p1, LC31/J;

    .line 193
    .line 194
    iget-object p1, p1, LC31/J;->b:Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipSmall;

    .line 195
    .line 196
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 197
    .line 198
    .line 199
    move-result-object v0

    .line 200
    check-cast v0, Lf41/a;

    .line 201
    .line 202
    invoke-virtual {v0}, Lf41/a;->h()Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;

    .line 203
    .line 204
    .line 205
    move-result-object v0

    .line 206
    const/4 v1, 0x1

    .line 207
    invoke-virtual {p1, v0, v1}, Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipSmall;->setComponentStyle(Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;Z)V

    .line 208
    .line 209
    .line 210
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 211
    .line 212
    .line 213
    move-result-object p1

    .line 214
    check-cast p1, LC31/J;

    .line 215
    .line 216
    iget-object p1, p1, LC31/J;->d:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 217
    .line 218
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 219
    .line 220
    .line 221
    move-result-object v0

    .line 222
    check-cast v0, Lf41/a;

    .line 223
    .line 224
    invoke-virtual {v0}, Lf41/a;->o()Ljava/lang/String;

    .line 225
    .line 226
    .line 227
    move-result-object v0

    .line 228
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->setTitleText(Ljava/lang/String;)V

    .line 229
    .line 230
    .line 231
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 232
    .line 233
    .line 234
    move-result-object p1

    .line 235
    check-cast p1, LC31/J;

    .line 236
    .line 237
    iget-object p1, p1, LC31/J;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 238
    .line 239
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 240
    .line 241
    .line 242
    move-result-object v0

    .line 243
    check-cast v0, Lf41/a;

    .line 244
    .line 245
    invoke-virtual {v0}, Lf41/a;->g()Z

    .line 246
    .line 247
    .line 248
    move-result v0

    .line 249
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setListCheckboxChecked(Z)V

    .line 250
    .line 251
    .line 252
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 253
    .line 254
    .line 255
    move-result-object p1

    .line 256
    check-cast p1, LC31/J;

    .line 257
    .line 258
    iget-object p1, p1, LC31/J;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 259
    .line 260
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 261
    .line 262
    .line 263
    move-result-object v0

    .line 264
    check-cast v0, Lf41/a;

    .line 265
    .line 266
    invoke-virtual {v0}, Lf41/a;->m()Ljava/lang/Integer;

    .line 267
    .line 268
    .line 269
    move-result-object v0

    .line 270
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setCounterNumber(Ljava/lang/Integer;)V

    .line 271
    .line 272
    .line 273
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 274
    .line 275
    .line 276
    move-result-object p1

    .line 277
    check-cast p1, LC31/J;

    .line 278
    .line 279
    iget-object p1, p1, LC31/J;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 280
    .line 281
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 282
    .line 283
    .line 284
    move-result-object v0

    .line 285
    check-cast v0, Lf41/a;

    .line 286
    .line 287
    invoke-virtual {v0}, Lf41/a;->d()Z

    .line 288
    .line 289
    .line 290
    move-result v0

    .line 291
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setAccordionExpanded(Z)V

    .line 292
    .line 293
    .line 294
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 295
    .line 296
    .line 297
    move-result-object p0

    .line 298
    check-cast p0, LC31/J;

    .line 299
    .line 300
    iget-object p0, p0, LC31/J;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 301
    .line 302
    const/4 p1, 0x0

    .line 303
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setTagVisible(Z)V

    .line 304
    .line 305
    .line 306
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 307
    .line 308
    return-object p0
.end method
