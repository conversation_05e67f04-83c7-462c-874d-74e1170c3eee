.class public final Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/c;
.source "SourceFile"

# interfaces
.implements LVo/d;
.implements Lbl0/f;
.implements Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;
.implements Lcx0/a;
.implements LCx0/a;
.implements LAx0/a;
.implements LVw0/a;
.implements Lox0/a;
.implements Lxx0/a;
.implements LMx0/a;
.implements LFx0/a;
.implements Ltx0/a;
.implements LEx0/b;
.implements Lgx0/a;
.implements Lfx0/a;
.implements Llx0/a;
.implements LHy0/a;
.implements LHy0/b;
.implements Lmp0/a;
.implements Lmp0/d;
.implements Lmp0/c;
.implements Lmp0/e;
.implements Lmp0/b;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$a;,
        Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0094\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0018\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0010\t\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0018\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008b\n\u0002\u0018\u0002\n\u0002\u0008+\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0001\u0018\u0000 \u00c5\u00032\u00020\u00012\u00020\u00022\u00020\u00032\u00020\u00042\u00020\u00052\u00020\u00062\u00020\u00072\u00020\u00082\u00020\t2\u00020\n2\u00020\u000b2\u00020\u000c2\u00020\r2\u00020\u000e2\u00020\u000f2\u00020\u00102\u00020\u00112\u00020\u00122\u00020\u00132\u00020\u00142\u00020\u00152\u00020\u00162\u00020\u00172\u00020\u0018:\u0002\u00cd\u0001B\u00a6\u0003\u0008\u0007\u0012\u0008\u0008\u0001\u0010\u001a\u001a\u00020\u0019\u0012\u0006\u0010\u001c\u001a\u00020\u001b\u0012\u0006\u0010\u001e\u001a\u00020\u001d\u0012\u0008\u0008\u0001\u0010 \u001a\u00020\u001f\u0012\u0006\u0010\"\u001a\u00020!\u0012\u0006\u0010$\u001a\u00020#\u0012\u0006\u0010&\u001a\u00020%\u0012\u0006\u0010(\u001a\u00020\'\u0012\u0006\u0010*\u001a\u00020)\u0012\u0006\u0010,\u001a\u00020+\u0012\u0006\u0010.\u001a\u00020-\u0012\u0006\u00100\u001a\u00020/\u0012\u0006\u00102\u001a\u000201\u0012\u0006\u00104\u001a\u000203\u0012\u0006\u00106\u001a\u000205\u0012\u0006\u00108\u001a\u000207\u0012\u0006\u0010:\u001a\u000209\u0012\u0006\u0010<\u001a\u00020;\u0012\u0006\u0010>\u001a\u00020=\u0012\u0006\u0010@\u001a\u00020?\u0012\u0006\u0010B\u001a\u00020A\u0012\u0006\u0010D\u001a\u00020C\u0012\u0006\u0010F\u001a\u00020E\u0012\u0006\u0010H\u001a\u00020G\u0012\u0006\u0010J\u001a\u00020I\u0012\u0006\u0010L\u001a\u00020K\u0012\u0006\u0010N\u001a\u00020M\u0012\u0006\u0010P\u001a\u00020O\u0012\u0006\u0010R\u001a\u00020Q\u0012\u0006\u0010T\u001a\u00020S\u0012\u0006\u0010V\u001a\u00020U\u0012\u0006\u0010X\u001a\u00020W\u0012\u0006\u0010Z\u001a\u00020Y\u0012\u0006\u0010\\\u001a\u00020[\u0012\u0006\u0010^\u001a\u00020]\u0012\u0006\u0010`\u001a\u00020_\u0012\u0006\u0010b\u001a\u00020a\u0012\u0006\u0010d\u001a\u00020c\u0012\u0006\u0010f\u001a\u00020e\u0012\u0006\u0010h\u001a\u00020g\u0012\u0006\u0010j\u001a\u00020i\u0012\u0006\u0010l\u001a\u00020k\u0012\u0006\u0010n\u001a\u00020m\u0012\u0006\u0010p\u001a\u00020o\u0012\u0006\u0010r\u001a\u00020q\u0012\u0006\u0010t\u001a\u00020s\u0012\u0006\u0010v\u001a\u00020u\u0012\u0006\u0010x\u001a\u00020w\u0012\u0006\u0010z\u001a\u00020y\u0012\u0006\u0010|\u001a\u00020{\u0012\u0006\u0010~\u001a\u00020}\u00a2\u0006\u0005\u0008\u007f\u0010\u0080\u0001J\u0013\u0010\u0082\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0082\u0001\u0010\u0083\u0001J\u0013\u0010\u0084\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0084\u0001\u0010\u0083\u0001J\u0013\u0010\u0085\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0085\u0001\u0010\u0083\u0001J\u0013\u0010\u0086\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0086\u0001\u0010\u0083\u0001J\u0013\u0010\u0087\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0087\u0001\u0010\u0083\u0001J\u0013\u0010\u0088\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0088\u0001\u0010\u0083\u0001J\u0013\u0010\u0089\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0089\u0001\u0010\u0083\u0001J\u0013\u0010\u008a\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u008a\u0001\u0010\u0083\u0001J\u0013\u0010\u008b\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u008b\u0001\u0010\u0083\u0001J\u0013\u0010\u008c\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u008c\u0001\u0010\u0083\u0001J\u0013\u0010\u008d\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u008d\u0001\u0010\u0083\u0001J\u0013\u0010\u008e\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u008e\u0001\u0010\u0083\u0001J\u0013\u0010\u008f\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u008f\u0001\u0010\u0083\u0001J\u0013\u0010\u0090\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0090\u0001\u0010\u0083\u0001J\u0013\u0010\u0091\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0091\u0001\u0010\u0083\u0001J\u0013\u0010\u0092\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0092\u0001\u0010\u0083\u0001J\u0013\u0010\u0093\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0093\u0001\u0010\u0083\u0001J\u0013\u0010\u0094\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0094\u0001\u0010\u0083\u0001J\u0013\u0010\u0095\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0095\u0001\u0010\u0083\u0001J\u0013\u0010\u0096\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0096\u0001\u0010\u0083\u0001J\u0013\u0010\u0097\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0097\u0001\u0010\u0083\u0001J\u0013\u0010\u0098\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0098\u0001\u0010\u0083\u0001J\u0013\u0010\u0099\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u0099\u0001\u0010\u0083\u0001J\u001d\u0010\u009c\u0001\u001a\u00030\u0081\u00012\u0008\u0010\u009b\u0001\u001a\u00030\u009a\u0001H\u0002\u00a2\u0006\u0006\u0008\u009c\u0001\u0010\u009d\u0001J$\u0010\u00a1\u0001\u001a\u00030\u0081\u00012\u000f\u0010\u00a0\u0001\u001a\n\u0012\u0005\u0012\u00030\u009f\u00010\u009e\u0001H\u0002\u00a2\u0006\u0006\u0008\u00a1\u0001\u0010\u00a2\u0001J\u0013\u0010\u00a3\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u00a3\u0001\u0010\u0083\u0001J\u001c\u0010\u00a5\u0001\u001a\u00030\u0081\u00012\u0007\u0010\u00a4\u0001\u001a\u00020\u001fH\u0002\u00a2\u0006\u0006\u0008\u00a5\u0001\u0010\u00a6\u0001J&\u0010\u00aa\u0001\u001a\u00030\u0081\u00012\u0008\u0010\u00a8\u0001\u001a\u00030\u00a7\u00012\u0007\u0010\u00a9\u0001\u001a\u00020\u001bH\u0002\u00a2\u0006\u0006\u0008\u00aa\u0001\u0010\u00ab\u0001J\u001d\u0010\u00ae\u0001\u001a\u00030\u0081\u00012\u0008\u0010\u00ad\u0001\u001a\u00030\u00ac\u0001H\u0002\u00a2\u0006\u0006\u0008\u00ae\u0001\u0010\u00af\u0001J0\u0010\u00b3\u0001\u001a\u00030\u0081\u00012\u0008\u0010\u00b0\u0001\u001a\u00030\u009f\u00012\u0008\u0010\u00b1\u0001\u001a\u00030\u009f\u00012\u0007\u0010\u00b2\u0001\u001a\u00020\u001fH\u0002\u00a2\u0006\u0006\u0008\u00b3\u0001\u0010\u00b4\u0001J\u0013\u0010\u00b5\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u00b5\u0001\u0010\u0083\u0001J\u0013\u0010\u00b6\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u00b6\u0001\u0010\u0083\u0001J\u0013\u0010\u00b8\u0001\u001a\u00030\u00b7\u0001H\u0002\u00a2\u0006\u0006\u0008\u00b8\u0001\u0010\u00b9\u0001J\u0013\u0010\u00ba\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u00ba\u0001\u0010\u0083\u0001J\u001d\u0010\u00bd\u0001\u001a\u00030\u0081\u00012\u0008\u0010\u00bc\u0001\u001a\u00030\u00bb\u0001H\u0002\u00a2\u0006\u0006\u0008\u00bd\u0001\u0010\u00be\u0001J\u0012\u0010\u00bf\u0001\u001a\u00020\u001bH\u0002\u00a2\u0006\u0006\u0008\u00bf\u0001\u0010\u00c0\u0001J\u0013\u0010\u00c1\u0001\u001a\u00030\u0081\u0001H\u0002\u00a2\u0006\u0006\u0008\u00c1\u0001\u0010\u0083\u0001J\u0013\u0010\u00c2\u0001\u001a\u00030\u0081\u0001H\u0014\u00a2\u0006\u0006\u0008\u00c2\u0001\u0010\u0083\u0001J\u0013\u0010\u00c3\u0001\u001a\u00030\u0081\u0001H\u0016\u00a2\u0006\u0006\u0008\u00c3\u0001\u0010\u0083\u0001J\u0013\u0010\u00c4\u0001\u001a\u00030\u0081\u0001H\u0016\u00a2\u0006\u0006\u0008\u00c4\u0001\u0010\u0083\u0001J\u0013\u0010\u00c5\u0001\u001a\u00030\u0081\u0001H\u0016\u00a2\u0006\u0006\u0008\u00c5\u0001\u0010\u0083\u0001J\u001c\u0010\u00c7\u0001\u001a\u00030\u0081\u00012\u0007\u0010\u00c6\u0001\u001a\u00020\u001fH\u0016\u00a2\u0006\u0006\u0008\u00c7\u0001\u0010\u00a6\u0001J\u0013\u0010\u00c8\u0001\u001a\u00030\u0081\u0001H\u0016\u00a2\u0006\u0006\u0008\u00c8\u0001\u0010\u0083\u0001J&\u0010\u00cb\u0001\u001a\u00030\u0081\u00012\u0008\u0010\u00c9\u0001\u001a\u00030\u009f\u00012\u0007\u0010\u00ca\u0001\u001a\u00020\u001fH\u0016\u00a2\u0006\u0006\u0008\u00cb\u0001\u0010\u00cc\u0001J&\u0010\u00cd\u0001\u001a\u00030\u0081\u00012\u0008\u0010\u00c9\u0001\u001a\u00030\u009f\u00012\u0007\u0010\u00ca\u0001\u001a\u00020\u001fH\u0016\u00a2\u0006\u0006\u0008\u00cd\u0001\u0010\u00cc\u0001J\u0013\u0010\u00ce\u0001\u001a\u00030\u0081\u0001H\u0016\u00a2\u0006\u0006\u0008\u00ce\u0001\u0010\u0083\u0001J\u0013\u0010\u00cf\u0001\u001a\u00030\u0081\u0001H\u0016\u00a2\u0006\u0006\u0008\u00cf\u0001\u0010\u0083\u0001J\u0013\u0010\u00d0\u0001\u001a\u00030\u0081\u0001H\u0016\u00a2\u0006\u0006\u0008\u00d0\u0001\u0010\u0083\u0001J\u001c\u0010\u00d2\u0001\u001a\u00030\u0081\u00012\u0007\u0010\u00d1\u0001\u001a\u00020\u001bH\u0016\u00a2\u0006\u0006\u0008\u00d2\u0001\u0010\u00d3\u0001J\u001d\u0010\u00d6\u0001\u001a\u00030\u0081\u00012\u0008\u0010\u00d5\u0001\u001a\u00030\u00d4\u0001H\u0016\u00a2\u0006\u0006\u0008\u00d6\u0001\u0010\u00d7\u0001J\u001d\u0010\u00da\u0001\u001a\u00030\u0081\u00012\u0008\u0010\u00d9\u0001\u001a\u00030\u00d8\u0001H\u0016\u00a2\u0006\u0006\u0008\u00da\u0001\u0010\u00db\u0001J\u0013\u0010\u00dc\u0001\u001a\u00030\u0081\u0001H\u0016\u00a2\u0006\u0006\u0008\u00dc\u0001\u0010\u0083\u0001J&\u0010\u00df\u0001\u001a\u00030\u0081\u00012\u0008\u0010\u00de\u0001\u001a\u00030\u00dd\u00012\u0007\u0010\u00a9\u0001\u001a\u00020\u001bH\u0016\u00a2\u0006\u0006\u0008\u00df\u0001\u0010\u00e0\u0001J\u0013\u0010\u00e1\u0001\u001a\u00030\u0081\u0001H\u0016\u00a2\u0006\u0006\u0008\u00e1\u0001\u0010\u0083\u0001J\'\u0010\u00e4\u0001\u001a\u00030\u0081\u00012\u0007\u0010\u00e2\u0001\u001a\u00020\u001b2\t\u0010\u00e3\u0001\u001a\u0004\u0018\u00010\u001bH\u0016\u00a2\u0006\u0006\u0008\u00e4\u0001\u0010\u00e5\u0001J\'\u0010\u00e6\u0001\u001a\u00030\u0081\u00012\u0007\u0010\u00e2\u0001\u001a\u00020\u001b2\t\u0010\u00e3\u0001\u001a\u0004\u0018\u00010\u001bH\u0016\u00a2\u0006\u0006\u0008\u00e6\u0001\u0010\u00e5\u0001J\u0013\u0010\u00e7\u0001\u001a\u00030\u0081\u0001H\u0016\u00a2\u0006\u0006\u0008\u00e7\u0001\u0010\u0083\u0001J\u0013\u0010\u00e8\u0001\u001a\u00030\u0081\u0001H\u0016\u00a2\u0006\u0006\u0008\u00e8\u0001\u0010\u0083\u0001J0\u0010\u00e9\u0001\u001a\u00030\u0081\u00012\u0008\u0010\u00b0\u0001\u001a\u00030\u009f\u00012\u0008\u0010\u00b1\u0001\u001a\u00030\u009f\u00012\u0007\u0010\u00b2\u0001\u001a\u00020\u001fH\u0016\u00a2\u0006\u0006\u0008\u00e9\u0001\u0010\u00b4\u0001J\u001d\u0010\u00ec\u0001\u001a\u00030\u0081\u00012\u0008\u0010\u00eb\u0001\u001a\u00030\u00ea\u0001H\u0016\u00a2\u0006\u0006\u0008\u00ec\u0001\u0010\u00ed\u0001J\u001c\u0010\u00ef\u0001\u001a\u00030\u0081\u00012\u0007\u0010\u00ee\u0001\u001a\u00020\u001bH\u0016\u00a2\u0006\u0006\u0008\u00ef\u0001\u0010\u00d3\u0001J\u0018\u0010\u00f1\u0001\u001a\n\u0012\u0005\u0012\u00030\u00bb\u00010\u00f0\u0001\u00a2\u0006\u0006\u0008\u00f1\u0001\u0010\u00f2\u0001J\u0018\u0010\u00f4\u0001\u001a\n\u0012\u0005\u0012\u00030\u00f3\u00010\u00f0\u0001\u00a2\u0006\u0006\u0008\u00f4\u0001\u0010\u00f2\u0001J\u0011\u0010\u00f5\u0001\u001a\u00030\u0081\u0001\u00a2\u0006\u0006\u0008\u00f5\u0001\u0010\u0083\u0001J\u0011\u0010\u00f6\u0001\u001a\u00030\u0081\u0001\u00a2\u0006\u0006\u0008\u00f6\u0001\u0010\u0083\u0001J\u0011\u0010\u00f7\u0001\u001a\u00030\u0081\u0001\u00a2\u0006\u0006\u0008\u00f7\u0001\u0010\u0083\u0001J\u001b\u0010\u00f9\u0001\u001a\n\u0012\u0005\u0012\u00030\u00f8\u00010\u00f0\u0001H\u0096\u0001\u00a2\u0006\u0006\u0008\u00f9\u0001\u0010\u00f2\u0001J\u001b\u0010\u00fb\u0001\u001a\n\u0012\u0005\u0012\u00030\u00fa\u00010\u00f0\u0001H\u0096\u0001\u00a2\u0006\u0006\u0008\u00fb\u0001\u0010\u00f2\u0001J2\u0010\u0082\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00fd\u0001\u001a\u00030\u00fc\u00012\u0008\u0010\u00ff\u0001\u001a\u00030\u00fe\u00012\u0008\u0010\u0081\u0002\u001a\u00030\u0080\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u0082\u0002\u0010\u0083\u0002J(\u0010\u0086\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00fd\u0001\u001a\u00030\u00fc\u00012\u0008\u0010\u0085\u0002\u001a\u00030\u0084\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u0086\u0002\u0010\u0087\u0002J\u001e\u0010\u0089\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00d5\u0001\u001a\u00030\u0088\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u0089\u0002\u0010\u008a\u0002J\u001e\u0010\u008c\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00d5\u0001\u001a\u00030\u008b\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u008c\u0002\u0010\u008d\u0002J\u001e\u0010\u008f\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00d5\u0001\u001a\u00030\u008e\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u008f\u0002\u0010\u0090\u0002J\u001e\u0010\u0091\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00d5\u0001\u001a\u00030\u008b\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u0091\u0002\u0010\u008d\u0002J\u001e\u0010\u0093\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00d5\u0001\u001a\u00030\u0092\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u0093\u0002\u0010\u0094\u0002J\u001e\u0010\u0095\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00d5\u0001\u001a\u00030\u008b\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u0095\u0002\u0010\u008d\u0002J\u001e\u0010\u0096\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00d5\u0001\u001a\u00030\u0092\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u0096\u0002\u0010\u0094\u0002J\u001e\u0010\u0097\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00d5\u0001\u001a\u00030\u008b\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u0097\u0002\u0010\u008d\u0002J\u001e\u0010\u0099\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00d5\u0001\u001a\u00030\u0098\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u0099\u0002\u0010\u009a\u0002J\u001e\u0010\u009b\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00d5\u0001\u001a\u00030\u0098\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u009b\u0002\u0010\u009a\u0002J\u001e\u0010\u009d\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00d5\u0001\u001a\u00030\u009c\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u009d\u0002\u0010\u009e\u0002J\u001b\u0010\u00a0\u0002\u001a\n\u0012\u0005\u0012\u00030\u009f\u00020\u00f0\u0001H\u0096\u0001\u00a2\u0006\u0006\u0008\u00a0\u0002\u0010\u00f2\u0001J\u001e\u0010\u00a1\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00d5\u0001\u001a\u00030\u008b\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u00a1\u0002\u0010\u008d\u0002J\u001e\u0010\u00a2\u0002\u001a\u00030\u0080\u00022\u0008\u0010\u00e3\u0001\u001a\u00030\u009f\u0001H\u0096\u0001\u00a2\u0006\u0006\u0008\u00a2\u0002\u0010\u00a3\u0002J\u001b\u0010\u00a5\u0002\u001a\n\u0012\u0005\u0012\u00030\u00a4\u00020\u00f0\u0001H\u0096\u0001\u00a2\u0006\u0006\u0008\u00a5\u0002\u0010\u00f2\u0001J;\u0010\u00a8\u0002\u001a\u00030\u0081\u00012\u0007\u0010\u00e2\u0001\u001a\u00020\u001b2\t\u0010\u00e3\u0001\u001a\u0004\u0018\u00010\u001b2\u0008\u0010\u00a6\u0002\u001a\u00030\u009f\u00012\u0007\u0010\u00a7\u0002\u001a\u00020\u001bH\u0096\u0001\u00a2\u0006\u0006\u0008\u00a8\u0002\u0010\u00a9\u0002J;\u0010\u00aa\u0002\u001a\u00030\u0081\u00012\u0007\u0010\u00e2\u0001\u001a\u00020\u001b2\t\u0010\u00e3\u0001\u001a\u0004\u0018\u00010\u001b2\u0008\u0010\u00a6\u0002\u001a\u00030\u009f\u00012\u0007\u0010\u00a7\u0002\u001a\u00020\u001bH\u0096\u0001\u00a2\u0006\u0006\u0008\u00aa\u0002\u0010\u00a9\u0002J(\u0010\u00ab\u0002\u001a\u00030\u0081\u00012\u0008\u0010\u00fd\u0001\u001a\u00030\u00fc\u00012\u0008\u0010\u0085\u0002\u001a\u00030\u0084\u0002H\u0096\u0001\u00a2\u0006\u0006\u0008\u00ab\u0002\u0010\u0087\u0002J\u0014\u0010\u00ac\u0002\u001a\u00030\u0081\u0001H\u0096\u0001\u00a2\u0006\u0006\u0008\u00ac\u0002\u0010\u0083\u0001R\u0016\u0010\u001a\u001a\u00020\u00198\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ad\u0002\u0010\u00ae\u0002R\u0016\u0010\u001c\u001a\u00020\u001b8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00af\u0002\u0010\u00b0\u0002R\u0016\u0010\u001e\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b1\u0002\u0010\u00b2\u0002R\u0016\u0010 \u001a\u00020\u001f8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b3\u0002\u0010\u00b4\u0002R\u0016\u0010$\u001a\u00020#8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b5\u0002\u0010\u00b6\u0002R\u0016\u0010&\u001a\u00020%8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b7\u0002\u0010\u00b8\u0002R\u0016\u0010(\u001a\u00020\'8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b9\u0002\u0010\u00ba\u0002R\u0016\u0010*\u001a\u00020)8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bb\u0002\u0010\u00bc\u0002R\u0016\u0010,\u001a\u00020+8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bd\u0002\u0010\u00be\u0002R\u0016\u0010.\u001a\u00020-8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bf\u0002\u0010\u00c0\u0002R\u0016\u00100\u001a\u00020/8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c1\u0002\u0010\u00c2\u0002R\u0016\u00102\u001a\u0002018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a0\u0002\u0010\u00c3\u0002R\u0016\u00104\u001a\u0002038\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c4\u0002\u0010\u00c5\u0002R\u0016\u00106\u001a\u0002058\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c6\u0002\u0010\u00c7\u0002R\u0016\u00108\u001a\u0002078\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e8\u0001\u0010\u00c8\u0002R\u0016\u0010>\u001a\u00020=8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c9\u0002\u0010\u00ca\u0002R\u0016\u0010@\u001a\u00020?8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00cb\u0002\u0010\u00cc\u0002R\u0016\u0010B\u001a\u00020A8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00cd\u0002\u0010\u00ce\u0002R\u0016\u0010D\u001a\u00020C8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00cf\u0002\u0010\u00d0\u0002R\u0016\u0010F\u001a\u00020E8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d1\u0002\u0010\u00d2\u0002R\u0016\u0010H\u001a\u00020G8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d3\u0002\u0010\u00d4\u0002R\u0016\u0010J\u001a\u00020I8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d5\u0002\u0010\u00d6\u0002R\u0016\u0010L\u001a\u00020K8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d7\u0002\u0010\u00d8\u0002R\u0016\u0010N\u001a\u00020M8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008d\u0001\u0010\u00d9\u0002R\u0016\u0010P\u001a\u00020O8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0092\u0001\u0010\u00da\u0002R\u0016\u0010R\u001a\u00020Q8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ae\u0001\u0010\u00db\u0002R\u0016\u0010T\u001a\u00020S8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009c\u0001\u0010\u00dc\u0002R\u0016\u0010V\u001a\u00020U8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00dd\u0002\u0010\u00de\u0002R\u0016\u0010X\u001a\u00020W8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00df\u0002\u0010\u00e0\u0002R\u0016\u0010Z\u001a\u00020Y8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e1\u0002\u0010\u00e2\u0002R\u0016\u0010\\\u001a\u00020[8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e3\u0002\u0010\u00e4\u0002R\u0016\u0010^\u001a\u00020]8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e5\u0002\u0010\u00e6\u0002R\u0016\u0010`\u001a\u00020_8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e7\u0002\u0010\u00e8\u0002R\u0016\u0010b\u001a\u00020a8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e9\u0002\u0010\u00ea\u0002R\u0016\u0010d\u001a\u00020c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00eb\u0002\u0010\u00ec\u0002R\u0016\u0010f\u001a\u00020e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ed\u0002\u0010\u00ee\u0002R\u0016\u0010h\u001a\u00020g8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ef\u0002\u0010\u00f0\u0002R\u0016\u0010j\u001a\u00020i8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00f1\u0002\u0010\u00f2\u0002R\u0016\u0010l\u001a\u00020k8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00f3\u0002\u0010\u00f4\u0002R\u0016\u0010n\u001a\u00020m8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00f5\u0002\u0010\u00f6\u0002R\u0016\u0010p\u001a\u00020o8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00f7\u0002\u0010\u00f8\u0002R\u0016\u0010r\u001a\u00020q8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00f9\u0002\u0010\u00fa\u0002R\u0016\u0010t\u001a\u00020s8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00fb\u0002\u0010\u00fc\u0002R\u0016\u0010v\u001a\u00020u8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00fd\u0002\u0010\u00fe\u0002R\u0016\u0010x\u001a\u00020w8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ff\u0002\u0010\u0080\u0003R\u0016\u0010z\u001a\u00020y8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0081\u0003\u0010\u0082\u0003R\u0016\u0010|\u001a\u00020{8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0083\u0003\u0010\u0084\u0003R\u0016\u0010~\u001a\u00020}8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0085\u0003\u0010\u0086\u0003R\u001c\u0010\u008a\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0088\u0003\u0010\u0089\u0003R\u001c\u0010\u008c\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u008b\u0003\u0010\u0089\u0003R\u001c\u0010\u008e\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u008d\u0003\u0010\u0089\u0003R\u001c\u0010\u0090\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u008f\u0003\u0010\u0089\u0003R\u001c\u0010\u0092\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0091\u0003\u0010\u0089\u0003R\u001c\u0010\u0094\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0093\u0003\u0010\u0089\u0003R\u001c\u0010\u0096\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0095\u0003\u0010\u0089\u0003R\u001c\u0010\u0098\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0097\u0003\u0010\u0089\u0003R\u001c\u0010\u009a\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0099\u0003\u0010\u0089\u0003R\u001c\u0010\u009c\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u009b\u0003\u0010\u0089\u0003R\u001c\u0010\u009e\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u009d\u0003\u0010\u0089\u0003R\u001c\u0010\u00a0\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u009f\u0003\u0010\u0089\u0003R\u001c\u0010\u00a2\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a1\u0003\u0010\u0089\u0003R\u001c\u0010\u00a4\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a3\u0003\u0010\u0089\u0003R\u001c\u0010\u00a6\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a5\u0003\u0010\u0089\u0003R\u001c\u0010\u00a8\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a7\u0003\u0010\u0089\u0003R\u001c\u0010\u00aa\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a9\u0003\u0010\u0089\u0003R\u001c\u0010\u00ac\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00ab\u0003\u0010\u0089\u0003R\u001c\u0010\u00ae\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00ad\u0003\u0010\u0089\u0003R\u001c\u0010\u00b0\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00af\u0003\u0010\u0089\u0003R\u001c\u0010\u00b2\u0003\u001a\u0005\u0018\u00010\u0087\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00b1\u0003\u0010\u0089\u0003R\u001c\u0010\u00b6\u0003\u001a\u0005\u0018\u00010\u00b3\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00b4\u0003\u0010\u00b5\u0003R\u0018\u0010\u00ba\u0003\u001a\u00030\u00b7\u00038\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b8\u0003\u0010\u00b9\u0003R\u001f\u0010\u00be\u0003\u001a\n\u0012\u0005\u0012\u00030\u00b7\u00010\u00bb\u00038\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bc\u0003\u0010\u00bd\u0003R\u001f\u0010\u00c0\u0003\u001a\n\u0012\u0005\u0012\u00030\u00bb\u00010\u00bb\u00038\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bf\u0003\u0010\u00bd\u0003R\u001a\u0010\u00c4\u0003\u001a\u00030\u00c1\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00c2\u0003\u0010\u00c3\u0003\u00a8\u0006\u00c6\u0003"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/c;",
        "LVo/d;",
        "Lbl0/f;",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;",
        "Lcx0/a;",
        "LCx0/a;",
        "LAx0/a;",
        "LVw0/a;",
        "Lox0/a;",
        "Lxx0/a;",
        "LMx0/a;",
        "LFx0/a;",
        "Ltx0/a;",
        "LEx0/b;",
        "Lgx0/a;",
        "Lfx0/a;",
        "Llx0/a;",
        "LHy0/a;",
        "LHy0/b;",
        "Lmp0/a;",
        "Lmp0/d;",
        "Lmp0/c;",
        "Lmp0/e;",
        "Lmp0/b;",
        "Landroidx/lifecycle/Q;",
        "savedStateHandle",
        "",
        "eventId",
        "LwX0/c;",
        "router",
        "",
        "tournamentTitleEvent",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "LHX0/e;",
        "resourceManager",
        "LSX0/a;",
        "lottieConfigurator",
        "LVo/e;",
        "gameCardViewModelDelegate",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;",
        "getTeamsStreamUseCase",
        "Lkc1/p;",
        "getSpecialEventBannerListScenario",
        "Ltn/a;",
        "betHistoryScreenFactory",
        "Lvw0/a;",
        "teamSelectorScreenFactory",
        "LWo0/a;",
        "allEventGamesScreenFactory",
        "LRl0/b;",
        "rulesWebScreenFactory",
        "LVg0/a;",
        "promotionsNewsScreenFactory",
        "LIP/a;",
        "gameUtilsProvider",
        "Lorg/xbet/remoteconfig/domain/usecases/k;",
        "isBettingDisabledUseCase",
        "LJZ/a;",
        "getChampImagesHolderModelUseCase",
        "Lbl0/g;",
        "resultGameCardViewModelDelegate",
        "LNo0/b;",
        "getSpecialEventInfoUseCase",
        "Lss0/a;",
        "getLocalGamesLiveStreamResultUseCase",
        "Lqs0/a;",
        "getLocalGamesLineStreamResultUseCase",
        "Los0/a;",
        "getLocalGamesHistoryStreamResultUseCase",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LCw0/a;",
        "getTop10PlayersUseCase",
        "LDH0/a;",
        "statisticScreenFactory",
        "LIu0/b;",
        "getStadiumsUseCase",
        "Lry0/a;",
        "venuesScreenFactory",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "LPx0/a;",
        "tournamentAnalyticsTracker",
        "Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;",
        "getChampTop10MedalsUseCase",
        "LKs0/a;",
        "medalStatisticScreenFactory",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;",
        "whoWinViewModelDelegate",
        "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
        "getStageTableWithExtrasScenario",
        "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
        "getStageTableUseCase",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
        "getProfileUseCase",
        "Lorg/xbet/special_event/impl/teams/domain/usecase/d;",
        "getTeamsUseCase",
        "LFy0/a;",
        "whoWinScreenFactory",
        "Llp0/a;",
        "getAboutTournamentStreamScenario",
        "Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;",
        "getDotaPopularHeroesStreamUseCase",
        "Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;",
        "getLolPopularChampionsStreamUseCase",
        "Llp0/i;",
        "getPrizeDistributionStreamScenario",
        "Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;",
        "getCSMapStatisticStreamUseCase",
        "LuX0/a;",
        "getTabletFlagUseCase",
        "Llp0/g;",
        "getCyberTopTeamsStreamScenario",
        "LFI/c;",
        "cyberGamesNavigator",
        "Llp0/e;",
        "getCyberTopPlayersStreamScenario",
        "LNo0/a;",
        "getSocialNetsUseCase",
        "Llp0/c;",
        "getCyberGroupsStreamScenario",
        "<init>",
        "(Landroidx/lifecycle/Q;ILwX0/c;Ljava/lang/String;Lorg/xbet/remoteconfig/domain/usecases/i;LHX0/e;LSX0/a;LVo/e;Lm8/a;Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;Lkc1/p;Ltn/a;Lvw0/a;LWo0/a;LRl0/b;LVg0/a;LIP/a;Lorg/xbet/remoteconfig/domain/usecases/k;LJZ/a;Lbl0/g;LNo0/b;Lss0/a;Lqs0/a;Los0/a;Lorg/xbet/ui_common/utils/M;LCw0/a;LDH0/a;LIu0/b;Lry0/a;Lorg/xbet/ui_common/utils/internet/a;LPx0/a;Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;LKs0/a;Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lp9/c;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lorg/xbet/special_event/impl/teams/domain/usecase/d;LFy0/a;Llp0/a;Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;Llp0/i;Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;LuX0/a;Llp0/g;LFI/c;Llp0/e;LNo0/a;Llp0/c;)V",
        "",
        "P4",
        "()V",
        "d5",
        "V4",
        "J4",
        "S4",
        "D4",
        "U4",
        "w4",
        "A4",
        "L4",
        "H4",
        "z4",
        "Q4",
        "y4",
        "m5",
        "X4",
        "q5",
        "N4",
        "F4",
        "B4",
        "p5",
        "o5",
        "n5",
        "LZx0/a;",
        "contentMediator",
        "w5",
        "(LZx0/a;)V",
        "",
        "",
        "teamsIds",
        "u5",
        "(Ljava/util/List;)V",
        "t5",
        "siteLink",
        "i5",
        "(Ljava/lang/String;)V",
        "Lorg/xplatform/banners/api/domain/models/BannerModel;",
        "banner",
        "position",
        "g5",
        "(Lorg/xplatform/banners/api/domain/models/BannerModel;I)V",
        "Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;",
        "lottieErrorType",
        "v5",
        "(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;)V",
        "subSportId",
        "teamId",
        "teamName",
        "r5",
        "(JJLjava/lang/String;)V",
        "s5",
        "b5",
        "LZx0/k;",
        "s4",
        "()LZx0/k;",
        "q4",
        "Lorg/xbet/special_event/impl/tournament/presentation/a;",
        "uiEvent",
        "j5",
        "(Lorg/xbet/special_event/impl/tournament/presentation/a;)V",
        "v4",
        "()I",
        "p4",
        "onCleared",
        "R",
        "Y",
        "S",
        "playerId",
        "g",
        "X",
        "stadiumId",
        "stadiumTitle",
        "b",
        "(JLjava/lang/String;)V",
        "a",
        "W",
        "T",
        "c0",
        "teamClId",
        "c",
        "(I)V",
        "LAp/b;",
        "item",
        "f3",
        "(LAp/b;)V",
        "Lel0/b;",
        "historyGame",
        "s",
        "(Lel0/b;)V",
        "U",
        "Lpx0/a;",
        "selectedPromotion",
        "g3",
        "(Lpx0/a;I)V",
        "O1",
        "opponentId",
        "gameId",
        "g1",
        "(ILjava/lang/Integer;)V",
        "n3",
        "l0",
        "I2",
        "x",
        "Lyp0/a;",
        "uiModel",
        "N2",
        "(Lyp0/a;)V",
        "index",
        "r1",
        "Lkotlinx/coroutines/flow/e;",
        "t4",
        "()Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/special_event/impl/tournament/presentation/e;",
        "u4",
        "l5",
        "a5",
        "o4",
        "LYo/a;",
        "Q0",
        "LYo/b;",
        "k2",
        "Lorg/xbet/betting/core/coupon/models/SingleBetGame;",
        "singleBetGame",
        "Lorg/xbet/betting/core/zip/model/bet/BetInfo;",
        "betInfo",
        "",
        "resetToExpress",
        "m",
        "(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/zip/model/bet/BetInfo;Z)V",
        "Lorg/xbet/betting/core/coupon/models/SimpleBetZip;",
        "simpleBetZip",
        "B1",
        "(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;)V",
        "LAp/e;",
        "P",
        "(LAp/e;)V",
        "Lfl0/c;",
        "g2",
        "(Lfl0/c;)V",
        "LAp/c;",
        "o1",
        "(LAp/c;)V",
        "B2",
        "LAp/f;",
        "n2",
        "(LAp/f;)V",
        "D",
        "y",
        "T2",
        "LAp/a;",
        "A",
        "(LAp/a;)V",
        "n1",
        "LAp/d;",
        "r",
        "(LAp/d;)V",
        "Lbl0/a;",
        "y2",
        "c2",
        "M2",
        "(J)Z",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;",
        "A1",
        "sportId",
        "champId",
        "c3",
        "(ILjava/lang/Integer;JI)V",
        "J",
        "f2",
        "j1",
        "x1",
        "Landroidx/lifecycle/Q;",
        "y1",
        "I",
        "F1",
        "LwX0/c;",
        "H1",
        "Ljava/lang/String;",
        "I1",
        "LHX0/e;",
        "P1",
        "LSX0/a;",
        "S1",
        "LVo/e;",
        "V1",
        "Lm8/a;",
        "b2",
        "Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;",
        "v2",
        "Lkc1/p;",
        "x2",
        "Ltn/a;",
        "Lvw0/a;",
        "F2",
        "LWo0/a;",
        "H2",
        "LRl0/b;",
        "LVg0/a;",
        "P2",
        "LJZ/a;",
        "S2",
        "Lbl0/g;",
        "V2",
        "LNo0/b;",
        "X2",
        "Lss0/a;",
        "F3",
        "Lqs0/a;",
        "H3",
        "Los0/a;",
        "I3",
        "Lorg/xbet/ui_common/utils/M;",
        "S3",
        "LCw0/a;",
        "LDH0/a;",
        "LIu0/b;",
        "Lry0/a;",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "x5",
        "LPx0/a;",
        "y5",
        "Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;",
        "z5",
        "LKs0/a;",
        "A5",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;",
        "B5",
        "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
        "C5",
        "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
        "D5",
        "Lp9/c;",
        "E5",
        "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
        "F5",
        "Lorg/xbet/special_event/impl/teams/domain/usecase/d;",
        "G5",
        "LFy0/a;",
        "H5",
        "Llp0/a;",
        "I5",
        "Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;",
        "J5",
        "Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;",
        "K5",
        "Llp0/i;",
        "L5",
        "Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;",
        "M5",
        "LuX0/a;",
        "N5",
        "Llp0/g;",
        "O5",
        "LFI/c;",
        "P5",
        "Llp0/e;",
        "Q5",
        "LNo0/a;",
        "R5",
        "Llp0/c;",
        "Lkotlinx/coroutines/x0;",
        "S5",
        "Lkotlinx/coroutines/x0;",
        "networkConnectionJob",
        "T5",
        "loadTeamsJob",
        "U5",
        "loadLocationsJob",
        "V5",
        "loadPromotionsJob",
        "W5",
        "loadStadiumsJob",
        "X5",
        "loadTopPlayersJob",
        "Y5",
        "loadMedalsTableJob",
        "Z5",
        "whoWinJob",
        "a6",
        "aboutTournamentJob",
        "b6",
        "loadDotaPopularHeroesJob",
        "c6",
        "loadLolPopularChampionsJob",
        "d6",
        "prizeDistributionJob",
        "e6",
        "mapStatisticJob",
        "f6",
        "cyberTeamsStreamJob",
        "g6",
        "socialNetsJob",
        "h6",
        "cyberGroupsStreamJob",
        "i6",
        "gamesLiveResultStreamJob",
        "j6",
        "gamesLineResultStreamJob",
        "k6",
        "gamesHistoryResultStreamJob",
        "l6",
        "whoWinResultStreamJob",
        "m6",
        "cyberTopPlayersStreamJob",
        "Lq4/m;",
        "n6",
        "Lq4/m;",
        "resultHandler",
        "Lek0/o;",
        "o6",
        "Lek0/o;",
        "remoteConfig",
        "Lkotlinx/coroutines/flow/V;",
        "p6",
        "Lkotlinx/coroutines/flow/V;",
        "tournamentStateModel",
        "q6",
        "tournamentEvent",
        "LZx0/h;",
        "r6",
        "LZx0/h;",
        "tournamentScreenSettingsModel",
        "s6",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final s6:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final t6:I


# instance fields
.field public final A5:Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B5:Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C5:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final D5:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final E5:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:LWo0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F3:Lqs0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F5:Lorg/xbet/special_event/impl/teams/domain/usecase/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final G5:LFy0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:LRl0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H3:Los0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H4:LDH0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H5:Llp0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:LVg0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I3:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I5:Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final J5:Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final K5:Llp0/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final L5:Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final M5:LuX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final N5:Llp0/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final O5:LFI/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:LJZ/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P5:Llp0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final Q5:LNo0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final R5:Llp0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:LVo/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:Lbl0/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S3:LCw0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public S5:Lkotlinx/coroutines/x0;

.field public T5:Lkotlinx/coroutines/x0;

.field public U5:Lkotlinx/coroutines/x0;

.field public final V1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:LNo0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public V5:Lkotlinx/coroutines/x0;

.field public W5:Lkotlinx/coroutines/x0;

.field public final X2:Lss0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X4:LIu0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public X5:Lkotlinx/coroutines/x0;

.field public Y5:Lkotlinx/coroutines/x0;

.field public Z5:Lkotlinx/coroutines/x0;

.field public a6:Lkotlinx/coroutines/x0;

.field public final b2:Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b6:Lkotlinx/coroutines/x0;

.field public c6:Lkotlinx/coroutines/x0;

.field public d6:Lkotlinx/coroutines/x0;

.field public e6:Lkotlinx/coroutines/x0;

.field public f6:Lkotlinx/coroutines/x0;

.field public g6:Lkotlinx/coroutines/x0;

.field public h6:Lkotlinx/coroutines/x0;

.field public i6:Lkotlinx/coroutines/x0;

.field public j6:Lkotlinx/coroutines/x0;

.field public k6:Lkotlinx/coroutines/x0;

.field public l6:Lkotlinx/coroutines/x0;

.field public m6:Lkotlinx/coroutines/x0;

.field public n6:Lq4/m;

.field public final o6:Lek0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LZx0/k;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/special_event/impl/tournament/presentation/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public r6:LZx0/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lkc1/p;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v5:Lry0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w5:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Landroidx/lifecycle/Q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Ltn/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x5:LPx0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:I

.field public final y2:Lvw0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y5:Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z5:LKs0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->s6:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->t6:I

    return-void
.end method

.method public constructor <init>(Landroidx/lifecycle/Q;ILwX0/c;Ljava/lang/String;Lorg/xbet/remoteconfig/domain/usecases/i;LHX0/e;LSX0/a;LVo/e;Lm8/a;Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;Lkc1/p;Ltn/a;Lvw0/a;LWo0/a;LRl0/b;LVg0/a;LIP/a;Lorg/xbet/remoteconfig/domain/usecases/k;LJZ/a;Lbl0/g;LNo0/b;Lss0/a;Lqs0/a;Los0/a;Lorg/xbet/ui_common/utils/M;LCw0/a;LDH0/a;LIu0/b;Lry0/a;Lorg/xbet/ui_common/utils/internet/a;LPx0/a;Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;LKs0/a;Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lp9/c;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lorg/xbet/special_event/impl/teams/domain/usecase/d;LFy0/a;Llp0/a;Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;Llp0/i;Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;LuX0/a;Llp0/g;LFI/c;Llp0/e;LNo0/a;Llp0/c;)V
    .locals 17
    .param p1    # Landroidx/lifecycle/Q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LVo/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lkc1/p;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Ltn/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lvw0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LWo0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # LRl0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # LVg0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # LIP/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lorg/xbet/remoteconfig/domain/usecases/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # LJZ/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Lbl0/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # LNo0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # Lss0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Lqs0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # Los0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p26    # LCw0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p27    # LDH0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p28    # LIu0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p29    # Lry0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p30    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p31    # LPx0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p32    # Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p33    # LKs0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p34    # Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p35    # Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p36    # Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p37    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p38    # Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p39    # Lorg/xbet/special_event/impl/teams/domain/usecase/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p40    # LFy0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p41    # Llp0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p42    # Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p43    # Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p44    # Llp0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p45    # Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p46    # LuX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p47    # Llp0/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p48    # LFI/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p49    # Llp0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p50    # LNo0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p51    # Llp0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    move-object/from16 v2, p8

    .line 6
    .line 7
    move-object/from16 v3, p20

    .line 8
    .line 9
    move-object/from16 v4, p34

    .line 10
    .line 11
    const/4 v5, 0x3

    .line 12
    new-array v5, v5, [Lorg/xbet/ui_common/viewmodel/core/k;

    .line 13
    .line 14
    const/4 v6, 0x0

    .line 15
    aput-object v2, v5, v6

    .line 16
    .line 17
    const/4 v6, 0x1

    .line 18
    aput-object v3, v5, v6

    .line 19
    .line 20
    const/4 v6, 0x2

    .line 21
    aput-object v4, v5, v6

    .line 22
    .line 23
    invoke-static {v5}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 24
    .line 25
    .line 26
    move-result-object v5

    .line 27
    invoke-direct {v0, v1, v5}, Lorg/xbet/ui_common/viewmodel/core/c;-><init>(Landroidx/lifecycle/Q;Ljava/util/List;)V

    .line 28
    .line 29
    .line 30
    iput-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x1:Landroidx/lifecycle/Q;

    .line 31
    .line 32
    move/from16 v1, p2

    .line 33
    .line 34
    iput v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y1:I

    .line 35
    .line 36
    move-object/from16 v5, p3

    .line 37
    .line 38
    iput-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 39
    .line 40
    move-object/from16 v5, p4

    .line 41
    .line 42
    iput-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->H1:Ljava/lang/String;

    .line 43
    .line 44
    move-object/from16 v5, p6

    .line 45
    .line 46
    iput-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 47
    .line 48
    move-object/from16 v5, p7

    .line 49
    .line 50
    iput-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->P1:LSX0/a;

    .line 51
    .line 52
    iput-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S1:LVo/e;

    .line 53
    .line 54
    move-object/from16 v2, p9

    .line 55
    .line 56
    iput-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 57
    .line 58
    move-object/from16 v2, p10

    .line 59
    .line 60
    iput-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->b2:Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;

    .line 61
    .line 62
    move-object/from16 v2, p11

    .line 63
    .line 64
    iput-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->v2:Lkc1/p;

    .line 65
    .line 66
    move-object/from16 v2, p12

    .line 67
    .line 68
    iput-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x2:Ltn/a;

    .line 69
    .line 70
    move-object/from16 v2, p13

    .line 71
    .line 72
    iput-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y2:Lvw0/a;

    .line 73
    .line 74
    move-object/from16 v2, p14

    .line 75
    .line 76
    iput-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F2:LWo0/a;

    .line 77
    .line 78
    move-object/from16 v2, p15

    .line 79
    .line 80
    iput-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->H2:LRl0/b;

    .line 81
    .line 82
    move-object/from16 v2, p16

    .line 83
    .line 84
    iput-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I2:LVg0/a;

    .line 85
    .line 86
    move-object/from16 v2, p19

    .line 87
    .line 88
    iput-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->P2:LJZ/a;

    .line 89
    .line 90
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S2:Lbl0/g;

    .line 91
    .line 92
    move-object/from16 v2, p21

    .line 93
    .line 94
    iput-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V2:LNo0/b;

    .line 95
    .line 96
    move-object/from16 v3, p22

    .line 97
    .line 98
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->X2:Lss0/a;

    .line 99
    .line 100
    move-object/from16 v3, p23

    .line 101
    .line 102
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F3:Lqs0/a;

    .line 103
    .line 104
    move-object/from16 v3, p24

    .line 105
    .line 106
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->H3:Los0/a;

    .line 107
    .line 108
    move-object/from16 v3, p25

    .line 109
    .line 110
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I3:Lorg/xbet/ui_common/utils/M;

    .line 111
    .line 112
    move-object/from16 v3, p26

    .line 113
    .line 114
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S3:LCw0/a;

    .line 115
    .line 116
    move-object/from16 v3, p27

    .line 117
    .line 118
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->H4:LDH0/a;

    .line 119
    .line 120
    move-object/from16 v3, p28

    .line 121
    .line 122
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->X4:LIu0/b;

    .line 123
    .line 124
    move-object/from16 v3, p29

    .line 125
    .line 126
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->v5:Lry0/a;

    .line 127
    .line 128
    move-object/from16 v3, p30

    .line 129
    .line 130
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5:Lorg/xbet/ui_common/utils/internet/a;

    .line 131
    .line 132
    move-object/from16 v3, p31

    .line 133
    .line 134
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 135
    .line 136
    move-object/from16 v3, p32

    .line 137
    .line 138
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y5:Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;

    .line 139
    .line 140
    move-object/from16 v3, p33

    .line 141
    .line 142
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->z5:LKs0/a;

    .line 143
    .line 144
    iput-object v4, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->A5:Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;

    .line 145
    .line 146
    move-object/from16 v3, p35

    .line 147
    .line 148
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->B5:Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;

    .line 149
    .line 150
    move-object/from16 v3, p36

    .line 151
    .line 152
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->C5:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 153
    .line 154
    move-object/from16 v3, p37

    .line 155
    .line 156
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->D5:Lp9/c;

    .line 157
    .line 158
    move-object/from16 v3, p38

    .line 159
    .line 160
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->E5:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 161
    .line 162
    move-object/from16 v3, p39

    .line 163
    .line 164
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F5:Lorg/xbet/special_event/impl/teams/domain/usecase/d;

    .line 165
    .line 166
    move-object/from16 v3, p40

    .line 167
    .line 168
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->G5:LFy0/a;

    .line 169
    .line 170
    move-object/from16 v3, p41

    .line 171
    .line 172
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->H5:Llp0/a;

    .line 173
    .line 174
    move-object/from16 v3, p42

    .line 175
    .line 176
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I5:Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;

    .line 177
    .line 178
    move-object/from16 v3, p43

    .line 179
    .line 180
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->J5:Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;

    .line 181
    .line 182
    move-object/from16 v3, p44

    .line 183
    .line 184
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->K5:Llp0/i;

    .line 185
    .line 186
    move-object/from16 v3, p45

    .line 187
    .line 188
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->L5:Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;

    .line 189
    .line 190
    move-object/from16 v3, p46

    .line 191
    .line 192
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->M5:LuX0/a;

    .line 193
    .line 194
    move-object/from16 v3, p47

    .line 195
    .line 196
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->N5:Llp0/g;

    .line 197
    .line 198
    move-object/from16 v3, p48

    .line 199
    .line 200
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->O5:LFI/c;

    .line 201
    .line 202
    move-object/from16 v3, p49

    .line 203
    .line 204
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->P5:Llp0/e;

    .line 205
    .line 206
    move-object/from16 v3, p50

    .line 207
    .line 208
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->Q5:LNo0/a;

    .line 209
    .line 210
    move-object/from16 v3, p51

    .line 211
    .line 212
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->R5:Llp0/c;

    .line 213
    .line 214
    invoke-interface/range {p5 .. p5}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 215
    .line 216
    .line 217
    move-result-object v3

    .line 218
    iput-object v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->o6:Lek0/o;

    .line 219
    .line 220
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->s4()LZx0/k;

    .line 221
    .line 222
    .line 223
    move-result-object v4

    .line 224
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 225
    .line 226
    .line 227
    move-result-object v4

    .line 228
    iput-object v4, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 229
    .line 230
    sget-object v4, Lorg/xbet/special_event/impl/tournament/presentation/a$a;->a:Lorg/xbet/special_event/impl/tournament/presentation/a$a;

    .line 231
    .line 232
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 233
    .line 234
    .line 235
    move-result-object v4

    .line 236
    iput-object v4, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->q6:Lkotlinx/coroutines/flow/V;

    .line 237
    .line 238
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 239
    .line 240
    .line 241
    move-result-object v4

    .line 242
    invoke-interface/range {p18 .. p18}, Lorg/xbet/remoteconfig/domain/usecases/k;->invoke()Z

    .line 243
    .line 244
    .line 245
    move-result v5

    .line 246
    invoke-virtual {v3}, Lek0/o;->D1()Z

    .line 247
    .line 248
    .line 249
    move-result v6

    .line 250
    invoke-virtual {v3}, Lek0/o;->J1()Z

    .line 251
    .line 252
    .line 253
    move-result v3

    .line 254
    new-instance v7, LZx0/h;

    .line 255
    .line 256
    const/4 v8, 0x0

    .line 257
    const/4 v9, 0x0

    .line 258
    const/4 v10, 0x0

    .line 259
    const-wide/16 v11, 0x0

    .line 260
    .line 261
    const-wide/16 v13, 0x0

    .line 262
    .line 263
    move-object/from16 p16, p17

    .line 264
    .line 265
    move/from16 p4, v1

    .line 266
    .line 267
    move/from16 p15, v3

    .line 268
    .line 269
    move-object/from16 p10, v4

    .line 270
    .line 271
    move/from16 p13, v5

    .line 272
    .line 273
    move/from16 p14, v6

    .line 274
    .line 275
    move-object/from16 p3, v7

    .line 276
    .line 277
    move-wide/from16 p6, v11

    .line 278
    .line 279
    move-wide/from16 p8, v13

    .line 280
    .line 281
    const/16 p5, 0x0

    .line 282
    .line 283
    const/16 p11, 0x0

    .line 284
    .line 285
    const/16 p12, 0x0

    .line 286
    .line 287
    invoke-direct/range {p3 .. p16}, LZx0/h;-><init>(IIJJLjava/util/List;ZZZZZLIP/a;)V

    .line 288
    .line 289
    .line 290
    move-object/from16 v1, p3

    .line 291
    .line 292
    iput-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 293
    .line 294
    invoke-interface {v2}, LNo0/b;->invoke()Ljava/util/List;

    .line 295
    .line 296
    .line 297
    move-result-object v1

    .line 298
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 299
    .line 300
    .line 301
    move-result-object v1

    .line 302
    :cond_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 303
    .line 304
    .line 305
    move-result v2

    .line 306
    if-eqz v2, :cond_1

    .line 307
    .line 308
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 309
    .line 310
    .line 311
    move-result-object v2

    .line 312
    move-object v3, v2

    .line 313
    check-cast v3, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;

    .line 314
    .line 315
    invoke-virtual {v3}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getId()I

    .line 316
    .line 317
    .line 318
    move-result v3

    .line 319
    iget-object v4, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 320
    .line 321
    invoke-virtual {v4}, LZx0/h;->d()I

    .line 322
    .line 323
    .line 324
    move-result v4

    .line 325
    if-ne v3, v4, :cond_0

    .line 326
    .line 327
    goto :goto_0

    .line 328
    :cond_1
    const/4 v2, 0x0

    .line 329
    :goto_0
    check-cast v2, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;

    .line 330
    .line 331
    if-nez v2, :cond_2

    .line 332
    .line 333
    sget-object v1, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;->CONTENT_ERROR:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    .line 334
    .line 335
    invoke-virtual {v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->v5(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;)V

    .line 336
    .line 337
    .line 338
    invoke-direct {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p4()V

    .line 339
    .line 340
    .line 341
    return-void

    .line 342
    :cond_2
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 343
    .line 344
    invoke-virtual {v2}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getId()I

    .line 345
    .line 346
    .line 347
    move-result v3

    .line 348
    invoke-virtual {v2}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getSportId()J

    .line 349
    .line 350
    .line 351
    move-result-wide v4

    .line 352
    invoke-virtual {v2}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getSubSportId()J

    .line 353
    .line 354
    .line 355
    move-result-wide v6

    .line 356
    invoke-virtual {v2}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getTournamentContentOrderIds()Ljava/util/List;

    .line 357
    .line 358
    .line 359
    move-result-object v8

    .line 360
    invoke-virtual {v2}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getCustomSportIcon()Z

    .line 361
    .line 362
    .line 363
    move-result v9

    .line 364
    invoke-virtual {v2}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getTopIcon()Z

    .line 365
    .line 366
    .line 367
    move-result v2

    .line 368
    const/16 v10, 0x781

    .line 369
    .line 370
    const/4 v11, 0x0

    .line 371
    const/4 v12, 0x0

    .line 372
    const/4 v13, 0x0

    .line 373
    const/4 v14, 0x0

    .line 374
    const/4 v15, 0x0

    .line 375
    const/16 v16, 0x0

    .line 376
    .line 377
    move-object/from16 p1, v1

    .line 378
    .line 379
    move/from16 p10, v2

    .line 380
    .line 381
    move/from16 p3, v3

    .line 382
    .line 383
    move-wide/from16 p4, v4

    .line 384
    .line 385
    move-wide/from16 p6, v6

    .line 386
    .line 387
    move-object/from16 p8, v8

    .line 388
    .line 389
    move/from16 p9, v9

    .line 390
    .line 391
    move-object/from16 p16, v11

    .line 392
    .line 393
    move-object/from16 p14, v16

    .line 394
    .line 395
    const/16 p2, 0x0

    .line 396
    .line 397
    const/16 p11, 0x0

    .line 398
    .line 399
    const/16 p12, 0x0

    .line 400
    .line 401
    const/16 p13, 0x0

    .line 402
    .line 403
    const/16 p15, 0x781

    .line 404
    .line 405
    invoke-static/range {p1 .. p16}, LZx0/h;->b(LZx0/h;IIJJLjava/util/List;ZZZZZLIP/a;ILjava/lang/Object;)LZx0/h;

    .line 406
    .line 407
    .line 408
    move-result-object v1

    .line 409
    iput-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 410
    .line 411
    invoke-direct {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->s5()V

    .line 412
    .line 413
    .line 414
    invoke-direct {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->q4()V

    .line 415
    .line 416
    .line 417
    return-void
.end method

.method public static synthetic A3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->K4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final A4()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->m6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 7
    .line 8
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LZx0/k;

    .line 13
    .line 14
    invoke-virtual {v0}, LZx0/k;->h()Lay0/b;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v0}, Lay0/b;->c()LZx0/g;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {v0}, LQx0/b;->i(LZx0/g;)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-nez v0, :cond_0

    .line 27
    .line 28
    new-instance v0, LZx0/a$d;

    .line 29
    .line 30
    sget-object v1, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 31
    .line 32
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-direct {v0, v1, v2}, LZx0/a$d;-><init>(LZx0/g;Ljava/util/List;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 40
    .line 41
    .line 42
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->P5:Llp0/e;

    .line 43
    .line 44
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 45
    .line 46
    invoke-virtual {v1}, LZx0/h;->d()I

    .line 47
    .line 48
    .line 49
    move-result v1

    .line 50
    iget-object v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 51
    .line 52
    invoke-virtual {v2}, LZx0/h;->j()J

    .line 53
    .line 54
    .line 55
    move-result-wide v2

    .line 56
    invoke-virtual {v0, v1, v2, v3}, Llp0/e;->a(IJ)Lkotlinx/coroutines/flow/e;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadCyberTopPlayers$1;

    .line 61
    .line 62
    const/4 v2, 0x0

    .line 63
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadCyberTopPlayers$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 64
    .line 65
    .line 66
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    iget-object v3, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 75
    .line 76
    invoke-interface {v3}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 77
    .line 78
    .line 79
    move-result-object v3

    .line 80
    invoke-static {v1, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    new-instance v3, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadCyberTopPlayers$2;

    .line 85
    .line 86
    invoke-direct {v3, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadCyberTopPlayers$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 87
    .line 88
    .line 89
    invoke-static {v0, v1, v3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 90
    .line 91
    .line 92
    move-result-object v0

    .line 93
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->m6:Lkotlinx/coroutines/x0;

    .line 94
    .line 95
    return-void
.end method

.method public static synthetic B3(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->f5(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final B4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->b6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 7
    .line 8
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LZx0/k;

    .line 13
    .line 14
    invoke-virtual {v0}, LZx0/k;->i()Ldy0/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v0}, Ldy0/a;->b()LZx0/g;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {v0}, LQx0/b;->i(LZx0/g;)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-nez v0, :cond_0

    .line 27
    .line 28
    new-instance v0, LZx0/a$e;

    .line 29
    .line 30
    sget-object v1, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 31
    .line 32
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-direct {v0, v1, v2}, LZx0/a$e;-><init>(LZx0/g;Ljava/util/List;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 40
    .line 41
    .line 42
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I5:Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;

    .line 43
    .line 44
    iget v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y1:I

    .line 45
    .line 46
    invoke-virtual {v0, v1}, Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;->a(I)Lkotlinx/coroutines/flow/e;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadDotaPopularHeroes$1;

    .line 51
    .line 52
    const/4 v2, 0x0

    .line 53
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadDotaPopularHeroes$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 54
    .line 55
    .line 56
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 61
    .line 62
    .line 63
    move-result-object v1

    .line 64
    iget-object v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 65
    .line 66
    invoke-interface {v2}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadDotaPopularHeroes$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadDotaPopularHeroes$2;

    .line 75
    .line 76
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->b6:Lkotlinx/coroutines/x0;

    .line 81
    .line 82
    return-void
.end method

.method public static synthetic C3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->h5(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic C4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final D4()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->U5:Lkotlinx/coroutines/x0;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    invoke-static {v1}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 11
    .line 12
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    check-cast v1, LZx0/k;

    .line 17
    .line 18
    invoke-virtual {v1}, LZx0/k;->j()Ley0/a;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    invoke-virtual {v1}, Ley0/a;->b()LZx0/g;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    invoke-static {v1}, LQx0/b;->i(LZx0/g;)Z

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    if-nez v1, :cond_1

    .line 31
    .line 32
    new-instance v1, LZx0/a$f;

    .line 33
    .line 34
    sget-object v2, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 35
    .line 36
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object v3

    .line 40
    invoke-direct {v1, v2, v3}, LZx0/a$f;-><init>(LZx0/g;Ljava/util/List;)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 44
    .line 45
    .line 46
    :cond_1
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 47
    .line 48
    .line 49
    move-result-object v4

    .line 50
    const-class v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 51
    .line 52
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    new-instance v2, Ljava/lang/StringBuilder;

    .line 57
    .line 58
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 59
    .line 60
    .line 61
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 62
    .line 63
    .line 64
    const-string v1, ".loadLocations"

    .line 65
    .line 66
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 67
    .line 68
    .line 69
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v5

    .line 73
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 74
    .line 75
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 76
    .line 77
    .line 78
    move-result-object v12

    .line 79
    new-instance v10, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLocations$1;

    .line 80
    .line 81
    const/4 v1, 0x0

    .line 82
    invoke-direct {v10, v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLocations$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 83
    .line 84
    .line 85
    new-instance v13, Lorg/xbet/special_event/impl/tournament/presentation/r;

    .line 86
    .line 87
    invoke-direct {v13, v0}, Lorg/xbet/special_event/impl/tournament/presentation/r;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 88
    .line 89
    .line 90
    const/16 v15, 0x128

    .line 91
    .line 92
    const/16 v16, 0x0

    .line 93
    .line 94
    const/4 v6, 0x3

    .line 95
    const-wide/16 v7, 0x3

    .line 96
    .line 97
    const/4 v9, 0x0

    .line 98
    const/4 v11, 0x0

    .line 99
    const/4 v14, 0x0

    .line 100
    invoke-static/range {v4 .. v16}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->Y(Lkotlinx/coroutines/N;Ljava/lang/String;IJLjava/util/List;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 101
    .line 102
    .line 103
    move-result-object v1

    .line 104
    iput-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->U5:Lkotlinx/coroutines/x0;

    .line 105
    .line 106
    return-void
.end method

.method public static final synthetic E3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->a6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final E4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->G3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$c;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$c;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 8
    .line 9
    .line 10
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 11
    .line 12
    .line 13
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 14
    .line 15
    return-object p0
.end method

.method public static final synthetic F3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LWo0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F2:LWo0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method private final F4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->c6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 7
    .line 8
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LZx0/k;

    .line 13
    .line 14
    invoke-virtual {v0}, LZx0/k;->k()Lfy0/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v0}, Lfy0/a;->c()LZx0/g;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {v0}, LQx0/b;->i(LZx0/g;)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-nez v0, :cond_0

    .line 27
    .line 28
    new-instance v0, LZx0/a$g;

    .line 29
    .line 30
    sget-object v1, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 31
    .line 32
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-direct {v0, v1, v2}, LZx0/a$g;-><init>(LZx0/g;Ljava/util/List;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 40
    .line 41
    .line 42
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->J5:Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;

    .line 43
    .line 44
    iget v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y1:I

    .line 45
    .line 46
    invoke-virtual {v0, v1}, Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;->a(I)Lkotlinx/coroutines/flow/e;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;

    .line 51
    .line 52
    const/4 v2, 0x0

    .line 53
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 54
    .line 55
    .line 56
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 61
    .line 62
    .line 63
    move-result-object v1

    .line 64
    iget-object v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 65
    .line 66
    invoke-interface {v2}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$2;

    .line 75
    .line 76
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->c6:Lkotlinx/coroutines/x0;

    .line 81
    .line 82
    return-void
.end method

.method public static final synthetic G3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lorg/xbet/ui_common/utils/M;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I3:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic G4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic H3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lp9/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->D5:Lp9/c;

    .line 2
    .line 3
    return-object p0
.end method

.method private final H4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->e6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 7
    .line 8
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LZx0/k;

    .line 13
    .line 14
    invoke-virtual {v0}, LZx0/k;->m()Lgy0/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v0}, Lgy0/a;->b()LZx0/g;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {v0}, LQx0/b;->i(LZx0/g;)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-nez v0, :cond_0

    .line 27
    .line 28
    new-instance v0, LZx0/a$h;

    .line 29
    .line 30
    sget-object v1, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 31
    .line 32
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-direct {v0, v1, v2}, LZx0/a$h;-><init>(LZx0/g;Ljava/util/List;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 40
    .line 41
    .line 42
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->L5:Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;

    .line 43
    .line 44
    iget v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y1:I

    .line 45
    .line 46
    invoke-virtual {v0, v1}, Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;->a(I)Lkotlinx/coroutines/flow/e;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMapStatistic$1;

    .line 51
    .line 52
    const/4 v2, 0x0

    .line 53
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMapStatistic$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 54
    .line 55
    .line 56
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 61
    .line 62
    .line 63
    move-result-object v1

    .line 64
    iget-object v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 65
    .line 66
    invoke-interface {v2}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMapStatistic$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMapStatistic$2;

    .line 75
    .line 76
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->e6:Lkotlinx/coroutines/x0;

    .line 81
    .line 82
    return-void
.end method

.method public static final synthetic I3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LJZ/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->P2:LJZ/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic I4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic J3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y5:Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method private final J4()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->Y5:Lkotlinx/coroutines/x0;

    .line 4
    .line 5
    invoke-static {v1}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 6
    .line 7
    .line 8
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 9
    .line 10
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    check-cast v1, LZx0/k;

    .line 15
    .line 16
    invoke-virtual {v1}, LZx0/k;->n()Lhy0/a;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-virtual {v1}, Lhy0/a;->b()LZx0/g;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-static {v1}, LQx0/b;->i(LZx0/g;)Z

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    if-nez v1, :cond_0

    .line 29
    .line 30
    new-instance v1, LZx0/a$i;

    .line 31
    .line 32
    sget-object v2, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 33
    .line 34
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 35
    .line 36
    .line 37
    move-result-object v3

    .line 38
    invoke-direct {v1, v2, v3}, LZx0/a$i;-><init>(LZx0/g;Ljava/util/List;)V

    .line 39
    .line 40
    .line 41
    invoke-virtual {v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 42
    .line 43
    .line 44
    :cond_0
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 45
    .line 46
    .line 47
    move-result-object v4

    .line 48
    const-class v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 49
    .line 50
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    new-instance v2, Ljava/lang/StringBuilder;

    .line 55
    .line 56
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 57
    .line 58
    .line 59
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 60
    .line 61
    .line 62
    const-string v1, ".loadMedalsTable"

    .line 63
    .line 64
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 65
    .line 66
    .line 67
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v5

    .line 71
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 72
    .line 73
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 74
    .line 75
    .line 76
    move-result-object v12

    .line 77
    new-instance v10, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;

    .line 78
    .line 79
    const/4 v1, 0x0

    .line 80
    invoke-direct {v10, v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 81
    .line 82
    .line 83
    new-instance v13, Lorg/xbet/special_event/impl/tournament/presentation/p;

    .line 84
    .line 85
    invoke-direct {v13, v0}, Lorg/xbet/special_event/impl/tournament/presentation/p;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 86
    .line 87
    .line 88
    const/16 v15, 0x128

    .line 89
    .line 90
    const/16 v16, 0x0

    .line 91
    .line 92
    const/4 v6, 0x3

    .line 93
    const-wide/16 v7, 0x3

    .line 94
    .line 95
    const/4 v9, 0x0

    .line 96
    const/4 v11, 0x0

    .line 97
    const/4 v14, 0x0

    .line 98
    invoke-static/range {v4 .. v16}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->Y(Lkotlinx/coroutines/N;Ljava/lang/String;IJLjava/util/List;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    iput-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->Y5:Lkotlinx/coroutines/x0;

    .line 103
    .line 104
    return-void
.end method

.method public static final synthetic K3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->E5:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final K4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->G3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$d;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$d;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 8
    .line 9
    .line 10
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 11
    .line 12
    .line 13
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 14
    .line 15
    return-object p0
.end method

.method public static final synthetic L3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LNo0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->Q5:LNo0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method private final L4()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->d6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 7
    .line 8
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LZx0/k;

    .line 13
    .line 14
    invoke-virtual {v0}, LZx0/k;->p()Liy0/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v0}, Liy0/a;->d()LZx0/g;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {v0}, LQx0/b;->i(LZx0/g;)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-nez v0, :cond_0

    .line 27
    .line 28
    new-instance v0, LZx0/a$k;

    .line 29
    .line 30
    sget-object v1, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 31
    .line 32
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-direct {v0, v1, v2}, LZx0/a$k;-><init>(LZx0/g;Ljava/util/List;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 40
    .line 41
    .line 42
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->K5:Llp0/i;

    .line 43
    .line 44
    iget v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y1:I

    .line 45
    .line 46
    iget-object v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 47
    .line 48
    invoke-virtual {v2}, LZx0/h;->j()J

    .line 49
    .line 50
    .line 51
    move-result-wide v2

    .line 52
    invoke-virtual {v0, v1, v2, v3}, Llp0/i;->a(IJ)Lkotlinx/coroutines/flow/e;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadPrizeDistribution$1;

    .line 57
    .line 58
    const/4 v2, 0x0

    .line 59
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadPrizeDistribution$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 60
    .line 61
    .line 62
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 63
    .line 64
    .line 65
    move-result-object v0

    .line 66
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 67
    .line 68
    .line 69
    move-result-object v1

    .line 70
    iget-object v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 71
    .line 72
    invoke-interface {v2}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 73
    .line 74
    .line 75
    move-result-object v2

    .line 76
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 77
    .line 78
    .line 79
    move-result-object v1

    .line 80
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadPrizeDistribution$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadPrizeDistribution$2;

    .line 81
    .line 82
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 83
    .line 84
    .line 85
    move-result-object v0

    .line 86
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->d6:Lkotlinx/coroutines/x0;

    .line 87
    .line 88
    return-void
.end method

.method public static final synthetic M3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lkc1/p;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->v2:Lkc1/p;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic M4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic N3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LIu0/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->X4:LIu0/b;

    .line 2
    .line 3
    return-object p0
.end method

.method private final N4()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V5:Lkotlinx/coroutines/x0;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    invoke-interface {v1}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    const/4 v2, 0x1

    .line 12
    if-ne v1, v2, :cond_0

    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 16
    .line 17
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    check-cast v1, LZx0/k;

    .line 22
    .line 23
    invoke-virtual {v1}, LZx0/k;->q()Ljy0/a;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-virtual {v1}, Ljy0/a;->c()LZx0/g;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    invoke-static {v1}, LQx0/b;->i(LZx0/g;)Z

    .line 32
    .line 33
    .line 34
    move-result v1

    .line 35
    if-nez v1, :cond_1

    .line 36
    .line 37
    new-instance v1, LZx0/a$l;

    .line 38
    .line 39
    sget-object v2, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 40
    .line 41
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object v3

    .line 45
    invoke-direct {v1, v2, v3}, LZx0/a$l;-><init>(LZx0/g;Ljava/util/List;)V

    .line 46
    .line 47
    .line 48
    invoke-virtual {v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 49
    .line 50
    .line 51
    :cond_1
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 52
    .line 53
    .line 54
    move-result-object v4

    .line 55
    const-class v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 56
    .line 57
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    new-instance v2, Ljava/lang/StringBuilder;

    .line 62
    .line 63
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 64
    .line 65
    .line 66
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 67
    .line 68
    .line 69
    const-string v1, ".loadPromotions"

    .line 70
    .line 71
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 72
    .line 73
    .line 74
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 75
    .line 76
    .line 77
    move-result-object v5

    .line 78
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 79
    .line 80
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 81
    .line 82
    .line 83
    move-result-object v12

    .line 84
    new-instance v10, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadPromotions$1;

    .line 85
    .line 86
    const/4 v1, 0x0

    .line 87
    invoke-direct {v10, v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadPromotions$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 88
    .line 89
    .line 90
    new-instance v13, Lorg/xbet/special_event/impl/tournament/presentation/m;

    .line 91
    .line 92
    invoke-direct {v13, v0}, Lorg/xbet/special_event/impl/tournament/presentation/m;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 93
    .line 94
    .line 95
    const/16 v15, 0x128

    .line 96
    .line 97
    const/16 v16, 0x0

    .line 98
    .line 99
    const/4 v6, 0x3

    .line 100
    const-wide/16 v7, 0x3

    .line 101
    .line 102
    const/4 v9, 0x0

    .line 103
    const/4 v11, 0x0

    .line 104
    const/4 v14, 0x0

    .line 105
    invoke-static/range {v4 .. v16}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->Y(Lkotlinx/coroutines/N;Ljava/lang/String;IJLjava/util/List;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 106
    .line 107
    .line 108
    move-result-object v1

    .line 109
    iput-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V5:Lkotlinx/coroutines/x0;

    .line 110
    .line 111
    return-void
.end method

.method public static final synthetic O3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->C5:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final O4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->G3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$e;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$e;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 8
    .line 9
    .line 10
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 11
    .line 12
    .line 13
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 14
    .line 15
    return-object p0
.end method

.method public static final synthetic P3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lorg/xbet/special_event/impl/teams/domain/usecase/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F5:Lorg/xbet/special_event/impl/teams/domain/usecase/d;

    .line 2
    .line 3
    return-object p0
.end method

.method private final P4()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LZx0/k;

    .line 8
    .line 9
    invoke-static {v0}, LQx0/b;->b(LZx0/k;)Ljava/util/List;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    new-instance v1, Ljava/util/ArrayList;

    .line 14
    .line 15
    const/16 v2, 0xa

    .line 16
    .line 17
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 22
    .line 23
    .line 24
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 29
    .line 30
    .line 31
    move-result v2

    .line 32
    if-eqz v2, :cond_13

    .line 33
    .line 34
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    check-cast v2, LZx0/i;

    .line 39
    .line 40
    instance-of v3, v2, LZx0/i$a$o;

    .line 41
    .line 42
    if-eqz v3, :cond_0

    .line 43
    .line 44
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V4()V

    .line 45
    .line 46
    .line 47
    goto/16 :goto_1

    .line 48
    .line 49
    :cond_0
    instance-of v3, v2, LZx0/i$a$m;

    .line 50
    .line 51
    if-eqz v3, :cond_1

    .line 52
    .line 53
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S4()V

    .line 54
    .line 55
    .line 56
    goto/16 :goto_1

    .line 57
    .line 58
    :cond_1
    instance-of v3, v2, LZx0/i$a$i;

    .line 59
    .line 60
    if-eqz v3, :cond_2

    .line 61
    .line 62
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->J4()V

    .line 63
    .line 64
    .line 65
    goto/16 :goto_1

    .line 66
    .line 67
    :cond_2
    instance-of v3, v2, LZx0/i$a$f;

    .line 68
    .line 69
    if-eqz v3, :cond_3

    .line 70
    .line 71
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->D4()V

    .line 72
    .line 73
    .line 74
    goto/16 :goto_1

    .line 75
    .line 76
    :cond_3
    instance-of v3, v2, LZx0/i$a$n;

    .line 77
    .line 78
    if-eqz v3, :cond_4

    .line 79
    .line 80
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->U4()V

    .line 81
    .line 82
    .line 83
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->m5()V

    .line 84
    .line 85
    .line 86
    goto/16 :goto_1

    .line 87
    .line 88
    :cond_4
    instance-of v3, v2, LZx0/i$a$p;

    .line 89
    .line 90
    if-eqz v3, :cond_5

    .line 91
    .line 92
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->X4()V

    .line 93
    .line 94
    .line 95
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->q5()V

    .line 96
    .line 97
    .line 98
    goto/16 :goto_1

    .line 99
    .line 100
    :cond_5
    instance-of v3, v2, LZx0/i$a$k;

    .line 101
    .line 102
    if-eqz v3, :cond_6

    .line 103
    .line 104
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->N4()V

    .line 105
    .line 106
    .line 107
    goto/16 :goto_1

    .line 108
    .line 109
    :cond_6
    instance-of v3, v2, LZx0/i$b$a;

    .line 110
    .line 111
    if-eqz v3, :cond_7

    .line 112
    .line 113
    sget-object v2, LZx0/a$j;->a:LZx0/a$j;

    .line 114
    .line 115
    invoke-virtual {p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 116
    .line 117
    .line 118
    goto :goto_1

    .line 119
    :cond_7
    instance-of v3, v2, LZx0/i$b$b;

    .line 120
    .line 121
    if-eqz v3, :cond_8

    .line 122
    .line 123
    sget-object v2, LZx0/a$o;->a:LZx0/a$o;

    .line 124
    .line 125
    invoke-virtual {p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 126
    .line 127
    .line 128
    goto :goto_1

    .line 129
    :cond_8
    instance-of v3, v2, LZx0/i$b$c;

    .line 130
    .line 131
    if-eqz v3, :cond_9

    .line 132
    .line 133
    sget-object v2, LZx0/a$r;->a:LZx0/a$r;

    .line 134
    .line 135
    invoke-virtual {p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 136
    .line 137
    .line 138
    goto :goto_1

    .line 139
    :cond_9
    instance-of v3, v2, LZx0/i$a$a;

    .line 140
    .line 141
    if-eqz v3, :cond_a

    .line 142
    .line 143
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w4()V

    .line 144
    .line 145
    .line 146
    goto :goto_1

    .line 147
    :cond_a
    instance-of v3, v2, LZx0/i$a$e;

    .line 148
    .line 149
    if-eqz v3, :cond_b

    .line 150
    .line 151
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->B4()V

    .line 152
    .line 153
    .line 154
    goto :goto_1

    .line 155
    :cond_b
    instance-of v3, v2, LZx0/i$a$g;

    .line 156
    .line 157
    if-eqz v3, :cond_c

    .line 158
    .line 159
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F4()V

    .line 160
    .line 161
    .line 162
    goto :goto_1

    .line 163
    :cond_c
    instance-of v3, v2, LZx0/i$a$d;

    .line 164
    .line 165
    if-eqz v3, :cond_d

    .line 166
    .line 167
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->A4()V

    .line 168
    .line 169
    .line 170
    goto :goto_1

    .line 171
    :cond_d
    instance-of v3, v2, LZx0/i$a$j;

    .line 172
    .line 173
    if-eqz v3, :cond_e

    .line 174
    .line 175
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->L4()V

    .line 176
    .line 177
    .line 178
    goto :goto_1

    .line 179
    :cond_e
    instance-of v3, v2, LZx0/i$a$h;

    .line 180
    .line 181
    if-eqz v3, :cond_f

    .line 182
    .line 183
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->H4()V

    .line 184
    .line 185
    .line 186
    goto :goto_1

    .line 187
    :cond_f
    instance-of v3, v2, LZx0/i$a$c;

    .line 188
    .line 189
    if-eqz v3, :cond_10

    .line 190
    .line 191
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->z4()V

    .line 192
    .line 193
    .line 194
    goto :goto_1

    .line 195
    :cond_10
    instance-of v3, v2, LZx0/i$a$l;

    .line 196
    .line 197
    if-eqz v3, :cond_11

    .line 198
    .line 199
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->Q4()V

    .line 200
    .line 201
    .line 202
    goto :goto_1

    .line 203
    :cond_11
    instance-of v2, v2, LZx0/i$a$b;

    .line 204
    .line 205
    if-eqz v2, :cond_12

    .line 206
    .line 207
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y4()V

    .line 208
    .line 209
    .line 210
    :goto_1
    sget-object v2, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 211
    .line 212
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 213
    .line 214
    .line 215
    goto/16 :goto_0

    .line 216
    .line 217
    :cond_12
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 218
    .line 219
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 220
    .line 221
    .line 222
    throw v0

    .line 223
    :cond_13
    return-void
.end method

.method public static final synthetic Q3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LCw0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S3:LCw0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method private final Q4()V
    .locals 11

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->g6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 7
    .line 8
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LZx0/k;

    .line 13
    .line 14
    invoke-virtual {v0}, LZx0/k;->r()Lky0/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v0}, Lky0/a;->b()LZx0/g;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {v0}, LQx0/b;->i(LZx0/g;)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-nez v0, :cond_0

    .line 27
    .line 28
    new-instance v0, LZx0/a$m;

    .line 29
    .line 30
    sget-object v1, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 31
    .line 32
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-direct {v0, v1, v2}, LZx0/a$m;-><init>(LZx0/g;Ljava/util/List;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 40
    .line 41
    .line 42
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 43
    .line 44
    .line 45
    move-result-object v3

    .line 46
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 47
    .line 48
    invoke-interface {v0}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 49
    .line 50
    .line 51
    move-result-object v6

    .line 52
    new-instance v4, Lorg/xbet/special_event/impl/tournament/presentation/l;

    .line 53
    .line 54
    invoke-direct {v4, p0}, Lorg/xbet/special_event/impl/tournament/presentation/l;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 55
    .line 56
    .line 57
    new-instance v8, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;

    .line 58
    .line 59
    const/4 v0, 0x0

    .line 60
    invoke-direct {v8, p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 61
    .line 62
    .line 63
    const/16 v9, 0xa

    .line 64
    .line 65
    const/4 v10, 0x0

    .line 66
    const/4 v5, 0x0

    .line 67
    const/4 v7, 0x0

    .line 68
    invoke-static/range {v3 .. v10}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->g6:Lkotlinx/coroutines/x0;

    .line 73
    .line 74
    return-void
.end method

.method public static final synthetic R3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->b6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final R4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    new-instance p1, LZx0/a$m;

    .line 2
    .line 3
    sget-object v0, LZx0/f;->a:LZx0/f;

    .line 4
    .line 5
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-direct {p1, v0, v1}, LZx0/a$m;-><init>(LZx0/g;Ljava/util/List;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final synthetic S3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->c6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method private final S4()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->W5:Lkotlinx/coroutines/x0;

    .line 4
    .line 5
    invoke-static {v1}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 6
    .line 7
    .line 8
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 9
    .line 10
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    check-cast v1, LZx0/k;

    .line 15
    .line 16
    invoke-virtual {v1}, LZx0/k;->s()Lly0/a;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-virtual {v1}, Lly0/a;->b()LZx0/g;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-static {v1}, LQx0/b;->i(LZx0/g;)Z

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    if-nez v1, :cond_0

    .line 29
    .line 30
    new-instance v1, LZx0/a$n;

    .line 31
    .line 32
    sget-object v2, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 33
    .line 34
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 35
    .line 36
    .line 37
    move-result-object v3

    .line 38
    invoke-direct {v1, v2, v3}, LZx0/a$n;-><init>(LZx0/g;Ljava/util/List;)V

    .line 39
    .line 40
    .line 41
    invoke-virtual {v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 42
    .line 43
    .line 44
    :cond_0
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 45
    .line 46
    .line 47
    move-result-object v4

    .line 48
    const-class v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 49
    .line 50
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    new-instance v2, Ljava/lang/StringBuilder;

    .line 55
    .line 56
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 57
    .line 58
    .line 59
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 60
    .line 61
    .line 62
    const-string v1, ".loadStadiums"

    .line 63
    .line 64
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 65
    .line 66
    .line 67
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v5

    .line 71
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 72
    .line 73
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 74
    .line 75
    .line 76
    move-result-object v12

    .line 77
    new-instance v10, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadStadiums$1;

    .line 78
    .line 79
    const/4 v1, 0x0

    .line 80
    invoke-direct {v10, v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadStadiums$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 81
    .line 82
    .line 83
    new-instance v13, Lorg/xbet/special_event/impl/tournament/presentation/q;

    .line 84
    .line 85
    invoke-direct {v13, v0}, Lorg/xbet/special_event/impl/tournament/presentation/q;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 86
    .line 87
    .line 88
    const/16 v15, 0x128

    .line 89
    .line 90
    const/16 v16, 0x0

    .line 91
    .line 92
    const/4 v6, 0x3

    .line 93
    const-wide/16 v7, 0x3

    .line 94
    .line 95
    const/4 v9, 0x0

    .line 96
    const/4 v11, 0x0

    .line 97
    const/4 v14, 0x0

    .line 98
    invoke-static/range {v4 .. v16}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->Y(Lkotlinx/coroutines/N;Ljava/lang/String;IJLjava/util/List;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    iput-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->W5:Lkotlinx/coroutines/x0;

    .line 103
    .line 104
    return-void
.end method

.method public static final synthetic T3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->e6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final T4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->G3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$f;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$f;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 8
    .line 9
    .line 10
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 11
    .line 12
    .line 13
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 14
    .line 15
    return-object p0
.end method

.method public static final synthetic U3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->d6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method private final U4()V
    .locals 12

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->T5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    check-cast v0, LZx0/k;

    .line 20
    .line 21
    invoke-virtual {v0}, LZx0/k;->u()Lmy0/a;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-virtual {v0}, Lmy0/a;->c()LZx0/g;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-static {v0}, LQx0/b;->i(LZx0/g;)Z

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    if-nez v0, :cond_1

    .line 34
    .line 35
    new-instance v0, LZx0/a$p;

    .line 36
    .line 37
    sget-object v1, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 38
    .line 39
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    invoke-direct {v0, v1, v2}, LZx0/a$p;-><init>(LZx0/g;Ljava/util/List;)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 47
    .line 48
    .line 49
    :cond_1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->b2:Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;

    .line 50
    .line 51
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 52
    .line 53
    invoke-virtual {v1}, LZx0/h;->d()I

    .line 54
    .line 55
    .line 56
    move-result v1

    .line 57
    invoke-virtual {v0, v1}, Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;->b(I)Lkotlinx/coroutines/flow/e;

    .line 58
    .line 59
    .line 60
    move-result-object v2

    .line 61
    const-class v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 62
    .line 63
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    new-instance v1, Ljava/lang/StringBuilder;

    .line 68
    .line 69
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 70
    .line 71
    .line 72
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 73
    .line 74
    .line 75
    const-string v0, ".loadTeams"

    .line 76
    .line 77
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 78
    .line 79
    .line 80
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 81
    .line 82
    .line 83
    move-result-object v3

    .line 84
    const/16 v10, 0x38

    .line 85
    .line 86
    const/4 v11, 0x0

    .line 87
    const/4 v4, 0x3

    .line 88
    const-wide/16 v5, 0x3

    .line 89
    .line 90
    const/4 v7, 0x0

    .line 91
    const/4 v8, 0x0

    .line 92
    const/4 v9, 0x0

    .line 93
    invoke-static/range {v2 .. v11}, Lcom/xbet/onexcore/utils/flows/FlowBuilderKt;->e(Lkotlinx/coroutines/flow/e;Ljava/lang/String;IJLjava/util/List;Ljava/util/List;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadTeams$1;

    .line 98
    .line 99
    const/4 v2, 0x0

    .line 100
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadTeams$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 101
    .line 102
    .line 103
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 104
    .line 105
    .line 106
    move-result-object v0

    .line 107
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 108
    .line 109
    .line 110
    move-result-object v1

    .line 111
    iget-object v3, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 112
    .line 113
    invoke-interface {v3}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 114
    .line 115
    .line 116
    move-result-object v3

    .line 117
    invoke-static {v1, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 118
    .line 119
    .line 120
    move-result-object v1

    .line 121
    new-instance v3, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadTeams$2;

    .line 122
    .line 123
    invoke-direct {v3, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadTeams$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 124
    .line 125
    .line 126
    invoke-static {v0, v1, v3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 127
    .line 128
    .line 129
    move-result-object v0

    .line 130
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->T5:Lkotlinx/coroutines/x0;

    .line 131
    .line 132
    return-void
.end method

.method public static final synthetic V3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LVg0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I2:LVg0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method private final V4()V
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->X5:Lkotlinx/coroutines/x0;

    .line 4
    .line 5
    invoke-static {v1}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 6
    .line 7
    .line 8
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 9
    .line 10
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    check-cast v1, LZx0/k;

    .line 15
    .line 16
    invoke-virtual {v1}, LZx0/k;->v()Loy0/a;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-virtual {v1}, Loy0/a;->b()LZx0/g;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-static {v1}, LQx0/b;->i(LZx0/g;)Z

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    if-nez v1, :cond_0

    .line 29
    .line 30
    new-instance v1, LZx0/a$q;

    .line 31
    .line 32
    sget-object v2, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 33
    .line 34
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 35
    .line 36
    .line 37
    move-result-object v3

    .line 38
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 39
    .line 40
    .line 41
    move-result-object v4

    .line 42
    invoke-direct {v1, v2, v3, v4}, LZx0/a$q;-><init>(LZx0/g;Ljava/util/List;Ljava/util/List;)V

    .line 43
    .line 44
    .line 45
    invoke-virtual {v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 46
    .line 47
    .line 48
    :cond_0
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 49
    .line 50
    .line 51
    move-result-object v5

    .line 52
    const-class v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 53
    .line 54
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 55
    .line 56
    .line 57
    move-result-object v1

    .line 58
    new-instance v2, Ljava/lang/StringBuilder;

    .line 59
    .line 60
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 61
    .line 62
    .line 63
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 64
    .line 65
    .line 66
    const-string v1, ".loadTopPlayers"

    .line 67
    .line 68
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 69
    .line 70
    .line 71
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 72
    .line 73
    .line 74
    move-result-object v6

    .line 75
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 76
    .line 77
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 78
    .line 79
    .line 80
    move-result-object v13

    .line 81
    new-instance v11, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadTopPlayers$1;

    .line 82
    .line 83
    const/4 v1, 0x0

    .line 84
    invoke-direct {v11, v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadTopPlayers$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 85
    .line 86
    .line 87
    new-instance v14, Lorg/xbet/special_event/impl/tournament/presentation/f;

    .line 88
    .line 89
    invoke-direct {v14, v0}, Lorg/xbet/special_event/impl/tournament/presentation/f;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 90
    .line 91
    .line 92
    const/16 v16, 0x128

    .line 93
    .line 94
    const/16 v17, 0x0

    .line 95
    .line 96
    const/4 v7, 0x3

    .line 97
    const-wide/16 v8, 0x3

    .line 98
    .line 99
    const/4 v10, 0x0

    .line 100
    const/4 v12, 0x0

    .line 101
    const/4 v15, 0x0

    .line 102
    invoke-static/range {v5 .. v17}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->Y(Lkotlinx/coroutines/N;Ljava/lang/String;IJLjava/util/List;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 103
    .line 104
    .line 105
    move-result-object v1

    .line 106
    iput-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->X5:Lkotlinx/coroutines/x0;

    .line 107
    .line 108
    return-void
.end method

.method public static final synthetic W3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final W4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->G3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$g;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$g;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 8
    .line 9
    .line 10
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 11
    .line 12
    .line 13
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 14
    .line 15
    return-object p0
.end method

.method public static final synthetic X3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LwX0/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 2
    .line 3
    return-object p0
.end method

.method private final X4()V
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->Z5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    new-instance v2, Lorg/xbet/special_event/impl/tournament/presentation/k;

    .line 11
    .line 12
    invoke-direct {v2, p0}, Lorg/xbet/special_event/impl/tournament/presentation/k;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 13
    .line 14
    .line 15
    new-instance v6, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadWhoWinSection$2;

    .line 16
    .line 17
    const/4 v0, 0x0

    .line 18
    invoke-direct {v6, p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadWhoWinSection$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 19
    .line 20
    .line 21
    const/16 v7, 0xe

    .line 22
    .line 23
    const/4 v8, 0x0

    .line 24
    const/4 v3, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    const/4 v5, 0x0

    .line 27
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->Z5:Lkotlinx/coroutines/x0;

    .line 32
    .line 33
    return-void
.end method

.method public static final synthetic Y3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LPx0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final Y4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I3:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/s;

    .line 4
    .line 5
    invoke-direct {v0}, Lorg/xbet/special_event/impl/tournament/presentation/s;-><init>()V

    .line 6
    .line 7
    .line 8
    invoke-interface {p0, p1, v0}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final synthetic Z3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->q6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final Z4(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic a4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LZx0/h;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic b4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method private final b5()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->s4()LZx0/k;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final synthetic c4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)I
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->v4()I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final c5(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Object;)V
    .locals 1

    .line 1
    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    sget-object p1, Lorg/xbet/special_event/impl/tournament/presentation/a$d;->a:Lorg/xbet/special_event/impl/tournament/presentation/a$d;

    .line 10
    .line 11
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->j5(Lorg/xbet/special_event/impl/tournament/presentation/a;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    return-void
.end method

.method public static final synthetic d4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final d5()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->b5()V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->q4()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->o4()V

    .line 8
    .line 9
    .line 10
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->s5()V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->a5()V

    .line 14
    .line 15
    .line 16
    sget-object v0, Lorg/xbet/special_event/impl/tournament/presentation/a$f;->a:Lorg/xbet/special_event/impl/tournament/presentation/a$f;

    .line 17
    .line 18
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->j5(Lorg/xbet/special_event/impl/tournament/presentation/a;)V

    .line 19
    .line 20
    .line 21
    return-void
.end method

.method public static final synthetic e4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->C4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final e5(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I3:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/j;

    .line 4
    .line 5
    invoke-direct {v0}, Lorg/xbet/special_event/impl/tournament/presentation/j;-><init>()V

    .line 6
    .line 7
    .line 8
    invoke-interface {p0, p1, v0}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final synthetic f4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->G4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private static final f5(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic g4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final g5(Lorg/xplatform/banners/api/domain/models/BannerModel;I)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/g;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/tournament/presentation/g;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$openPromotionInfo$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, p2, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$openPromotionInfo$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public static final synthetic h4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->M4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final h5(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I3:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    invoke-interface {p0, p1}, Lorg/xbet/ui_common/utils/M;->i(Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p0
.end method

.method public static final synthetic i4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->P4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final i5(Ljava/lang/String;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->H2:LRl0/b;

    .line 4
    .line 5
    invoke-interface {v1, p1}, LRl0/b;->a(Ljava/lang/String;)Lq4/q;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {v0, p1}, LwX0/c;->m(Lq4/q;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public static final synthetic j4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->d5()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic k4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->t5()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private static final k5(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic l4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->u5(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic m4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->v5(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final m5()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p5()V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->o5()V

    .line 5
    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->n5()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final synthetic n4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;LZx0/a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final n5()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->k6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    check-cast v0, LZx0/k;

    .line 20
    .line 21
    invoke-virtual {v0}, LZx0/k;->u()Lmy0/a;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-static {v0}, LQx0/b;->m(Lmy0/a;)Z

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    if-eqz v0, :cond_1

    .line 30
    .line 31
    new-instance v0, LZx0/b;

    .line 32
    .line 33
    sget-object v1, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 34
    .line 35
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 36
    .line 37
    .line 38
    move-result-object v2

    .line 39
    invoke-static {}, Lkotlin/collections/Z;->e()Ljava/util/Set;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    invoke-direct {v0, v1, v2, v3}, LZx0/b;-><init>(LZx0/g;Ljava/util/List;Ljava/util/Set;)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 47
    .line 48
    .line 49
    :cond_1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->H3:Los0/a;

    .line 50
    .line 51
    invoke-virtual {v0}, Los0/a;->a()Lkotlinx/coroutines/flow/e;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S2:Lbl0/g;

    .line 56
    .line 57
    invoke-interface {v1}, Lbl0/f;->l2()Lkotlinx/coroutines/flow/e;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    new-instance v2, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;

    .line 62
    .line 63
    const/4 v3, 0x0

    .line 64
    invoke-direct {v2, p0, v3}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 65
    .line 66
    .line 67
    invoke-static {v0, v1, v2}, Lkotlinx/coroutines/flow/g;->o(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    iget-object v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 76
    .line 77
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 78
    .line 79
    .line 80
    move-result-object v2

    .line 81
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 82
    .line 83
    .line 84
    move-result-object v1

    .line 85
    new-instance v2, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$2;

    .line 86
    .line 87
    invoke-direct {v2, p0, v3}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 88
    .line 89
    .line 90
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->k6:Lkotlinx/coroutines/x0;

    .line 95
    .line 96
    return-void
.end method

.method private final o5()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->j6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    check-cast v0, LZx0/k;

    .line 20
    .line 21
    invoke-virtual {v0}, LZx0/k;->u()Lmy0/a;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-static {v0}, LQx0/b;->n(Lmy0/a;)Z

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    if-eqz v0, :cond_1

    .line 30
    .line 31
    new-instance v0, LZx0/c;

    .line 32
    .line 33
    sget-object v1, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 34
    .line 35
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 36
    .line 37
    .line 38
    move-result-object v2

    .line 39
    invoke-direct {v0, v1, v2}, LZx0/c;-><init>(LZx0/g;Ljava/util/List;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 43
    .line 44
    .line 45
    :cond_1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F3:Lqs0/a;

    .line 46
    .line 47
    invoke-virtual {v0}, Lqs0/a;->a()Lkotlinx/coroutines/flow/e;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesLineResult$1;

    .line 52
    .line 53
    const/4 v2, 0x0

    .line 54
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesLineResult$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 55
    .line 56
    .line 57
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    iget-object v3, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 66
    .line 67
    invoke-interface {v3}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 68
    .line 69
    .line 70
    move-result-object v3

    .line 71
    invoke-static {v1, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    new-instance v3, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesLineResult$2;

    .line 76
    .line 77
    invoke-direct {v3, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesLineResult$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 78
    .line 79
    .line 80
    invoke-static {v0, v1, v3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->j6:Lkotlinx/coroutines/x0;

    .line 85
    .line 86
    return-void
.end method

.method public static synthetic p3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->E4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final p4()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->T5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->X5:Lkotlinx/coroutines/x0;

    .line 7
    .line 8
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->U5:Lkotlinx/coroutines/x0;

    .line 12
    .line 13
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->W5:Lkotlinx/coroutines/x0;

    .line 17
    .line 18
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 19
    .line 20
    .line 21
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->b6:Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 24
    .line 25
    .line 26
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->i6:Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 29
    .line 30
    .line 31
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->j6:Lkotlinx/coroutines/x0;

    .line 32
    .line 33
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 34
    .line 35
    .line 36
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->k6:Lkotlinx/coroutines/x0;

    .line 37
    .line 38
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 39
    .line 40
    .line 41
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V5:Lkotlinx/coroutines/x0;

    .line 42
    .line 43
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 44
    .line 45
    .line 46
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->Y5:Lkotlinx/coroutines/x0;

    .line 47
    .line 48
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 49
    .line 50
    .line 51
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->Z5:Lkotlinx/coroutines/x0;

    .line 52
    .line 53
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 54
    .line 55
    .line 56
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->l6:Lkotlinx/coroutines/x0;

    .line 57
    .line 58
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 59
    .line 60
    .line 61
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->m6:Lkotlinx/coroutines/x0;

    .line 62
    .line 63
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 64
    .line 65
    .line 66
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->a6:Lkotlinx/coroutines/x0;

    .line 67
    .line 68
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 69
    .line 70
    .line 71
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->d6:Lkotlinx/coroutines/x0;

    .line 72
    .line 73
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 74
    .line 75
    .line 76
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->e6:Lkotlinx/coroutines/x0;

    .line 77
    .line 78
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 79
    .line 80
    .line 81
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->f6:Lkotlinx/coroutines/x0;

    .line 82
    .line 83
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 84
    .line 85
    .line 86
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->g6:Lkotlinx/coroutines/x0;

    .line 87
    .line 88
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 89
    .line 90
    .line 91
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->h6:Lkotlinx/coroutines/x0;

    .line 92
    .line 93
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 94
    .line 95
    .line 96
    return-void
.end method

.method private final p5()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->i6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    check-cast v0, LZx0/k;

    .line 20
    .line 21
    invoke-virtual {v0}, LZx0/k;->u()Lmy0/a;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-static {v0}, LQx0/b;->o(Lmy0/a;)Z

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    if-eqz v0, :cond_1

    .line 30
    .line 31
    new-instance v0, LZx0/d;

    .line 32
    .line 33
    sget-object v1, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 34
    .line 35
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 36
    .line 37
    .line 38
    move-result-object v2

    .line 39
    invoke-direct {v0, v1, v2}, LZx0/d;-><init>(LZx0/g;Ljava/util/List;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 43
    .line 44
    .line 45
    :cond_1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->X2:Lss0/a;

    .line 46
    .line 47
    invoke-virtual {v0}, Lss0/a;->a()Lkotlinx/coroutines/flow/e;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesLiveResult$1;

    .line 52
    .line 53
    const/4 v2, 0x0

    .line 54
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesLiveResult$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 55
    .line 56
    .line 57
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    iget-object v3, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 66
    .line 67
    invoke-interface {v3}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 68
    .line 69
    .line 70
    move-result-object v3

    .line 71
    invoke-static {v1, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    new-instance v3, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesLiveResult$2;

    .line 76
    .line 77
    invoke-direct {v3, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesLiveResult$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 78
    .line 79
    .line 80
    invoke-static {v0, v1, v3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->i6:Lkotlinx/coroutines/x0;

    .line 85
    .line 86
    return-void
.end method

.method public static synthetic q3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->T4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final q4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/o;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/special_event/impl/tournament/presentation/o;-><init>()V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method private final q5()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->l6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 7
    .line 8
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LZx0/k;

    .line 13
    .line 14
    invoke-virtual {v0}, LZx0/k;->x()Lpy0/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v0}, Lpy0/a;->c()LZx0/g;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {v0}, LQx0/b;->i(LZx0/g;)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-nez v0, :cond_0

    .line 27
    .line 28
    new-instance v0, LZx0/a$s;

    .line 29
    .line 30
    sget-object v1, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 31
    .line 32
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-direct {v0, v1, v2}, LZx0/a$s;-><init>(LZx0/g;Ljava/util/List;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 40
    .line 41
    .line 42
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->B5:Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;

    .line 43
    .line 44
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->d()Lkotlinx/coroutines/flow/e;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveWhoWinResult$1;

    .line 49
    .line 50
    const/4 v2, 0x0

    .line 51
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveWhoWinResult$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 52
    .line 53
    .line 54
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 59
    .line 60
    .line 61
    move-result-object v1

    .line 62
    iget-object v3, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 63
    .line 64
    invoke-interface {v3}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 65
    .line 66
    .line 67
    move-result-object v3

    .line 68
    invoke-static {v1, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    new-instance v3, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveWhoWinResult$2;

    .line 73
    .line 74
    invoke-direct {v3, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveWhoWinResult$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 75
    .line 76
    .line 77
    invoke-static {v0, v1, v3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->l6:Lkotlinx/coroutines/x0;

    .line 82
    .line 83
    return-void
.end method

.method public static synthetic r3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Object;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->c5(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Object;)V

    return-void
.end method

.method private static final r4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final r5(JJLjava/lang/String;)V
    .locals 7

    .line 1
    sget-object v0, Ll8/m;->a:Ll8/m;

    .line 2
    .line 3
    invoke-virtual {v0}, Ll8/m;->d()Ljava/util/List;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-interface {v0, v1}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->O5:LFI/c;

    .line 18
    .line 19
    move-wide v2, p1

    .line 20
    move-wide v4, p3

    .line 21
    move-object v6, p5

    .line 22
    invoke-interface/range {v1 .. v6}, LFI/c;->p(JJLjava/lang/String;)V

    .line 23
    .line 24
    .line 25
    :cond_0
    return-void
.end method

.method public static synthetic s3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->k5(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final s5()V
    .locals 28

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 4
    .line 5
    invoke-virtual {v1}, LZx0/h;->l()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-static {v1}, LRx0/a;->a(Ljava/util/List;)Ljava/util/List;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    :goto_0
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    move-object v4, v2

    .line 20
    move-object v2, v4

    .line 21
    check-cast v2, LZx0/k;

    .line 22
    .line 23
    const v25, 0x3ffffe

    .line 24
    .line 25
    .line 26
    const/16 v26, 0x0

    .line 27
    .line 28
    move-object v5, v4

    .line 29
    const/4 v4, 0x0

    .line 30
    move-object v6, v5

    .line 31
    const/4 v5, 0x0

    .line 32
    move-object v7, v6

    .line 33
    const/4 v6, 0x0

    .line 34
    move-object v8, v7

    .line 35
    const/4 v7, 0x0

    .line 36
    move-object v9, v8

    .line 37
    const/4 v8, 0x0

    .line 38
    move-object v10, v9

    .line 39
    const/4 v9, 0x0

    .line 40
    move-object v11, v10

    .line 41
    const/4 v10, 0x0

    .line 42
    move-object v12, v11

    .line 43
    const/4 v11, 0x0

    .line 44
    move-object v13, v12

    .line 45
    const/4 v12, 0x0

    .line 46
    move-object v14, v13

    .line 47
    const/4 v13, 0x0

    .line 48
    move-object v15, v14

    .line 49
    const/4 v14, 0x0

    .line 50
    move-object/from16 v16, v15

    .line 51
    .line 52
    const/4 v15, 0x0

    .line 53
    move-object/from16 v17, v16

    .line 54
    .line 55
    const/16 v16, 0x0

    .line 56
    .line 57
    move-object/from16 v18, v17

    .line 58
    .line 59
    const/16 v17, 0x0

    .line 60
    .line 61
    move-object/from16 v19, v18

    .line 62
    .line 63
    const/16 v18, 0x0

    .line 64
    .line 65
    move-object/from16 v20, v19

    .line 66
    .line 67
    const/16 v19, 0x0

    .line 68
    .line 69
    move-object/from16 v21, v20

    .line 70
    .line 71
    const/16 v20, 0x0

    .line 72
    .line 73
    move-object/from16 v22, v21

    .line 74
    .line 75
    const/16 v21, 0x0

    .line 76
    .line 77
    move-object/from16 v23, v22

    .line 78
    .line 79
    const/16 v22, 0x0

    .line 80
    .line 81
    move-object/from16 v24, v23

    .line 82
    .line 83
    const/16 v23, 0x0

    .line 84
    .line 85
    move-object/from16 v27, v24

    .line 86
    .line 87
    const/16 v24, 0x0

    .line 88
    .line 89
    move-object/from16 v0, v27

    .line 90
    .line 91
    invoke-static/range {v2 .. v26}, LZx0/k;->b(LZx0/k;Ljava/util/List;LUw0/a;LUw0/a;LUw0/a;Lmy0/a;Ley0/a;Ljy0/a;Loy0/a;Lly0/a;Lhy0/a;Lpy0/a;Ldy0/a;Lfy0/a;Lay0/a;Lay0/b;Liy0/a;Lgy0/a;Lcy0/a;Lky0/a;Lby0/a;LNZ/a;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;ILjava/lang/Object;)LZx0/k;

    .line 92
    .line 93
    .line 94
    move-result-object v2

    .line 95
    invoke-interface {v1, v0, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 96
    .line 97
    .line 98
    move-result v0

    .line 99
    if-eqz v0, :cond_0

    .line 100
    .line 101
    return-void

    .line 102
    :cond_0
    move-object/from16 v0, p0

    .line 103
    .line 104
    goto :goto_0
.end method

.method public static synthetic t3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->O4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final t5()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LZx0/k;

    .line 8
    .line 9
    invoke-virtual {v0}, LZx0/k;->u()Lmy0/a;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S1:LVo/e;

    .line 14
    .line 15
    invoke-virtual {v0}, Lmy0/a;->f()Lny0/c;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    invoke-virtual {v2}, Lny0/c;->c()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    invoke-virtual {v0}, Lmy0/a;->e()Lny0/b;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    invoke-virtual {v0}, Lny0/b;->c()Ljava/util/List;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-static {v2, v0}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    invoke-interface {v1, v0}, LVo/d;->U0(Ljava/util/List;)V

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method public static synthetic u3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->Y4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final u5(Ljava/util/List;)V
    .locals 29
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/Long;",
            ">;)V"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    iget-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 6
    .line 7
    :cond_0
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    move-object v4, v3

    .line 12
    check-cast v4, LZx0/k;

    .line 13
    .line 14
    invoke-virtual {v4}, LZx0/k;->u()Lmy0/a;

    .line 15
    .line 16
    .line 17
    move-result-object v5

    .line 18
    invoke-virtual {v4}, LZx0/k;->u()Lmy0/a;

    .line 19
    .line 20
    .line 21
    move-result-object v6

    .line 22
    invoke-virtual {v6}, Lmy0/a;->f()Lny0/c;

    .line 23
    .line 24
    .line 25
    move-result-object v7

    .line 26
    invoke-virtual {v4}, LZx0/k;->u()Lmy0/a;

    .line 27
    .line 28
    .line 29
    move-result-object v6

    .line 30
    invoke-virtual {v6}, Lmy0/a;->f()Lny0/c;

    .line 31
    .line 32
    .line 33
    move-result-object v6

    .line 34
    invoke-virtual {v6}, Lny0/c;->c()Ljava/util/List;

    .line 35
    .line 36
    .line 37
    move-result-object v6

    .line 38
    invoke-static {v6, v1}, LXx0/a;->a(Ljava/util/List;Ljava/util/List;)Ljava/util/List;

    .line 39
    .line 40
    .line 41
    move-result-object v8

    .line 42
    iget-object v9, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 43
    .line 44
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 45
    .line 46
    invoke-virtual {v6}, LZx0/h;->e()LIP/a;

    .line 47
    .line 48
    .line 49
    move-result-object v10

    .line 50
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 51
    .line 52
    invoke-virtual {v6}, LZx0/h;->m()Z

    .line 53
    .line 54
    .line 55
    move-result v11

    .line 56
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 57
    .line 58
    invoke-virtual {v6}, LZx0/h;->g()Z

    .line 59
    .line 60
    .line 61
    move-result v12

    .line 62
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 63
    .line 64
    invoke-virtual {v6}, LZx0/h;->h()Z

    .line 65
    .line 66
    .line 67
    move-result v13

    .line 68
    invoke-virtual {v4}, LZx0/k;->e()LNZ/a;

    .line 69
    .line 70
    .line 71
    move-result-object v14

    .line 72
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 73
    .line 74
    invoke-virtual {v6}, LZx0/h;->d()I

    .line 75
    .line 76
    .line 77
    move-result v15

    .line 78
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 79
    .line 80
    invoke-virtual {v6}, LZx0/h;->c()Z

    .line 81
    .line 82
    .line 83
    move-result v16

    .line 84
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 85
    .line 86
    invoke-virtual {v6}, LZx0/h;->k()Z

    .line 87
    .line 88
    .line 89
    move-result v17

    .line 90
    invoke-static/range {v8 .. v17}, LXx0/a;->c(Ljava/util/List;LHX0/e;LIP/a;ZZZLNZ/a;IZZ)Ljava/util/List;

    .line 91
    .line 92
    .line 93
    move-result-object v10

    .line 94
    const/4 v11, 0x3

    .line 95
    const/4 v12, 0x0

    .line 96
    const/4 v8, 0x0

    .line 97
    const/4 v9, 0x0

    .line 98
    invoke-static/range {v7 .. v12}, Lny0/c;->b(Lny0/c;LZx0/g;Ljava/util/List;Ljava/util/List;ILjava/lang/Object;)Lny0/c;

    .line 99
    .line 100
    .line 101
    move-result-object v9

    .line 102
    invoke-virtual {v4}, LZx0/k;->u()Lmy0/a;

    .line 103
    .line 104
    .line 105
    move-result-object v6

    .line 106
    invoke-virtual {v6}, Lmy0/a;->e()Lny0/b;

    .line 107
    .line 108
    .line 109
    move-result-object v10

    .line 110
    invoke-virtual {v4}, LZx0/k;->u()Lmy0/a;

    .line 111
    .line 112
    .line 113
    move-result-object v6

    .line 114
    invoke-virtual {v6}, Lmy0/a;->e()Lny0/b;

    .line 115
    .line 116
    .line 117
    move-result-object v6

    .line 118
    invoke-virtual {v6}, Lny0/b;->c()Ljava/util/List;

    .line 119
    .line 120
    .line 121
    move-result-object v6

    .line 122
    invoke-static {v6, v1}, LXx0/a;->a(Ljava/util/List;Ljava/util/List;)Ljava/util/List;

    .line 123
    .line 124
    .line 125
    move-result-object v11

    .line 126
    iget-object v12, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 127
    .line 128
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 129
    .line 130
    invoke-virtual {v6}, LZx0/h;->e()LIP/a;

    .line 131
    .line 132
    .line 133
    move-result-object v13

    .line 134
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 135
    .line 136
    invoke-virtual {v6}, LZx0/h;->m()Z

    .line 137
    .line 138
    .line 139
    move-result v14

    .line 140
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 141
    .line 142
    invoke-virtual {v6}, LZx0/h;->g()Z

    .line 143
    .line 144
    .line 145
    move-result v15

    .line 146
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 147
    .line 148
    invoke-virtual {v6}, LZx0/h;->h()Z

    .line 149
    .line 150
    .line 151
    move-result v16

    .line 152
    invoke-virtual {v4}, LZx0/k;->e()LNZ/a;

    .line 153
    .line 154
    .line 155
    move-result-object v17

    .line 156
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 157
    .line 158
    invoke-virtual {v6}, LZx0/h;->d()I

    .line 159
    .line 160
    .line 161
    move-result v18

    .line 162
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 163
    .line 164
    invoke-virtual {v6}, LZx0/h;->c()Z

    .line 165
    .line 166
    .line 167
    move-result v19

    .line 168
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 169
    .line 170
    invoke-virtual {v6}, LZx0/h;->k()Z

    .line 171
    .line 172
    .line 173
    move-result v20

    .line 174
    invoke-static/range {v11 .. v20}, LXx0/a;->c(Ljava/util/List;LHX0/e;LIP/a;ZZZLNZ/a;IZZ)Ljava/util/List;

    .line 175
    .line 176
    .line 177
    move-result-object v13

    .line 178
    const/4 v14, 0x3

    .line 179
    const/4 v15, 0x0

    .line 180
    const/4 v11, 0x0

    .line 181
    const/4 v12, 0x0

    .line 182
    invoke-static/range {v10 .. v15}, Lny0/b;->b(Lny0/b;LZx0/g;Ljava/util/List;Ljava/util/List;ILjava/lang/Object;)Lny0/b;

    .line 183
    .line 184
    .line 185
    move-result-object v10

    .line 186
    invoke-virtual {v4}, LZx0/k;->u()Lmy0/a;

    .line 187
    .line 188
    .line 189
    move-result-object v6

    .line 190
    invoke-virtual {v6}, Lmy0/a;->d()Lny0/a;

    .line 191
    .line 192
    .line 193
    move-result-object v11

    .line 194
    invoke-virtual {v4}, LZx0/k;->u()Lmy0/a;

    .line 195
    .line 196
    .line 197
    move-result-object v6

    .line 198
    invoke-virtual {v6}, Lmy0/a;->d()Lny0/a;

    .line 199
    .line 200
    .line 201
    move-result-object v6

    .line 202
    invoke-virtual {v6}, Lny0/a;->c()Ljava/util/List;

    .line 203
    .line 204
    .line 205
    move-result-object v6

    .line 206
    new-instance v12, Ljava/util/ArrayList;

    .line 207
    .line 208
    invoke-direct {v12}, Ljava/util/ArrayList;-><init>()V

    .line 209
    .line 210
    .line 211
    invoke-interface {v6}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 212
    .line 213
    .line 214
    move-result-object v6

    .line 215
    :cond_1
    :goto_0
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    .line 216
    .line 217
    .line 218
    move-result v7

    .line 219
    if-eqz v7, :cond_2

    .line 220
    .line 221
    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 222
    .line 223
    .line 224
    move-result-object v7

    .line 225
    instance-of v8, v7, Lorg/xbet/domain/betting/api/models/result/HistoryGameItem$g;

    .line 226
    .line 227
    if-eqz v8, :cond_1

    .line 228
    .line 229
    invoke-interface {v12, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 230
    .line 231
    .line 232
    goto :goto_0

    .line 233
    :cond_2
    iget-object v13, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 234
    .line 235
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 236
    .line 237
    invoke-virtual {v6}, LZx0/h;->d()I

    .line 238
    .line 239
    .line 240
    move-result v14

    .line 241
    invoke-virtual {v4}, LZx0/k;->u()Lmy0/a;

    .line 242
    .line 243
    .line 244
    move-result-object v6

    .line 245
    invoke-virtual {v6}, Lmy0/a;->d()Lny0/a;

    .line 246
    .line 247
    .line 248
    move-result-object v6

    .line 249
    invoke-virtual {v6}, Lny0/a;->e()Ljava/util/Set;

    .line 250
    .line 251
    .line 252
    move-result-object v15

    .line 253
    invoke-virtual {v4}, LZx0/k;->u()Lmy0/a;

    .line 254
    .line 255
    .line 256
    move-result-object v6

    .line 257
    invoke-virtual {v6}, Lmy0/a;->g()Ljava/util/List;

    .line 258
    .line 259
    .line 260
    move-result-object v16

    .line 261
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 262
    .line 263
    invoke-virtual {v6}, LZx0/h;->c()Z

    .line 264
    .line 265
    .line 266
    move-result v17

    .line 267
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 268
    .line 269
    invoke-virtual {v6}, LZx0/h;->k()Z

    .line 270
    .line 271
    .line 272
    move-result v18

    .line 273
    invoke-static/range {v12 .. v18}, LXx0/a;->b(Ljava/util/List;LHX0/e;ILjava/util/Set;Ljava/util/List;ZZ)Ljava/util/List;

    .line 274
    .line 275
    .line 276
    move-result-object v14

    .line 277
    const/16 v16, 0xb

    .line 278
    .line 279
    const/16 v17, 0x0

    .line 280
    .line 281
    const/4 v12, 0x0

    .line 282
    const/4 v13, 0x0

    .line 283
    const/4 v15, 0x0

    .line 284
    invoke-static/range {v11 .. v17}, Lny0/a;->b(Lny0/a;LZx0/g;Ljava/util/List;Ljava/util/List;Ljava/util/Set;ILjava/lang/Object;)Lny0/a;

    .line 285
    .line 286
    .line 287
    move-result-object v11

    .line 288
    const/4 v12, 0x7

    .line 289
    const/4 v6, 0x0

    .line 290
    const/4 v7, 0x0

    .line 291
    const/4 v8, 0x0

    .line 292
    invoke-static/range {v5 .. v13}, Lmy0/a;->b(Lmy0/a;LZx0/g;Ljava/util/List;Ljava/util/List;Lny0/c;Lny0/b;Lny0/a;ILjava/lang/Object;)Lmy0/a;

    .line 293
    .line 294
    .line 295
    move-result-object v9

    .line 296
    const v27, 0x3fffef

    .line 297
    .line 298
    .line 299
    const/16 v28, 0x0

    .line 300
    .line 301
    const/4 v5, 0x0

    .line 302
    const/4 v10, 0x0

    .line 303
    const/4 v11, 0x0

    .line 304
    const/4 v12, 0x0

    .line 305
    const/4 v14, 0x0

    .line 306
    const/16 v16, 0x0

    .line 307
    .line 308
    const/16 v18, 0x0

    .line 309
    .line 310
    const/16 v19, 0x0

    .line 311
    .line 312
    const/16 v20, 0x0

    .line 313
    .line 314
    const/16 v21, 0x0

    .line 315
    .line 316
    const/16 v22, 0x0

    .line 317
    .line 318
    const/16 v23, 0x0

    .line 319
    .line 320
    const/16 v24, 0x0

    .line 321
    .line 322
    const/16 v25, 0x0

    .line 323
    .line 324
    const/16 v26, 0x0

    .line 325
    .line 326
    invoke-static/range {v4 .. v28}, LZx0/k;->b(LZx0/k;Ljava/util/List;LUw0/a;LUw0/a;LUw0/a;Lmy0/a;Ley0/a;Ljy0/a;Loy0/a;Lly0/a;Lhy0/a;Lpy0/a;Ldy0/a;Lfy0/a;Lay0/a;Lay0/b;Liy0/a;Lgy0/a;Lcy0/a;Lky0/a;Lby0/a;LNZ/a;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;ILjava/lang/Object;)LZx0/k;

    .line 327
    .line 328
    .line 329
    move-result-object v4

    .line 330
    invoke-interface {v2, v3, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 331
    .line 332
    .line 333
    move-result v3

    .line 334
    if-eqz v3, :cond_0

    .line 335
    .line 336
    return-void
.end method

.method public static synthetic v3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->W4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final v4()I
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V2:LNo0/b;

    .line 2
    .line 3
    invoke-interface {v0}, LNo0/b;->invoke()Ljava/util/List;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Ljava/util/ArrayList;

    .line 8
    .line 9
    const/16 v2, 0xa

    .line 10
    .line 11
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 12
    .line 13
    .line 14
    move-result v3

    .line 15
    invoke-direct {v1, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 16
    .line 17
    .line 18
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object v3

    .line 22
    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v4

    .line 26
    if-eqz v4, :cond_0

    .line 27
    .line 28
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v4

    .line 32
    check-cast v4, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;

    .line 33
    .line 34
    invoke-virtual {v4}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getId()I

    .line 35
    .line 36
    .line 37
    move-result v4

    .line 38
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 39
    .line 40
    .line 41
    move-result-object v4

    .line 42
    invoke-interface {v1, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 43
    .line 44
    .line 45
    goto :goto_0

    .line 46
    :cond_0
    iget-object v3, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 47
    .line 48
    invoke-virtual {v3}, LZx0/h;->d()I

    .line 49
    .line 50
    .line 51
    move-result v3

    .line 52
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 53
    .line 54
    .line 55
    move-result-object v3

    .line 56
    invoke-interface {v1, v3}, Ljava/util/List;->indexOf(Ljava/lang/Object;)I

    .line 57
    .line 58
    .line 59
    move-result v1

    .line 60
    new-instance v3, Ljava/util/ArrayList;

    .line 61
    .line 62
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 63
    .line 64
    .line 65
    move-result v2

    .line 66
    invoke-direct {v3, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 67
    .line 68
    .line 69
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 74
    .line 75
    .line 76
    move-result v2

    .line 77
    if-eqz v2, :cond_1

    .line 78
    .line 79
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object v2

    .line 83
    check-cast v2, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;

    .line 84
    .line 85
    invoke-virtual {v2}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getBannerCategoryId()I

    .line 86
    .line 87
    .line 88
    move-result v2

    .line 89
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 90
    .line 91
    .line 92
    move-result-object v2

    .line 93
    invoke-interface {v3, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 94
    .line 95
    .line 96
    goto :goto_1

    .line 97
    :cond_1
    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 98
    .line 99
    .line 100
    move-result-object v0

    .line 101
    check-cast v0, Ljava/lang/Number;

    .line 102
    .line 103
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 104
    .line 105
    .line 106
    move-result v0

    .line 107
    return v0
.end method

.method public static synthetic w3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->R4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final w4()V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->a6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 7
    .line 8
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LZx0/k;

    .line 13
    .line 14
    invoke-virtual {v0}, LZx0/k;->c()Lay0/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v0}, Lay0/a;->d()LZx0/g;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {v0}, LQx0/b;->i(LZx0/g;)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    const/4 v1, 0x0

    .line 27
    if-nez v0, :cond_0

    .line 28
    .line 29
    new-instance v0, LZx0/a$a;

    .line 30
    .line 31
    sget-object v2, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 32
    .line 33
    invoke-direct {v0, v2, v1}, LZx0/a$a;-><init>(LZx0/g;Lkp0/a;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 37
    .line 38
    .line 39
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->H5:Llp0/a;

    .line 40
    .line 41
    iget v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y1:I

    .line 42
    .line 43
    iget-object v3, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 44
    .line 45
    invoke-virtual {v3}, LZx0/h;->j()J

    .line 46
    .line 47
    .line 48
    move-result-wide v3

    .line 49
    invoke-virtual {v0, v2, v3, v4}, Llp0/a;->a(IJ)Lkotlinx/coroutines/flow/e;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    new-instance v2, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadAboutTournament$1;

    .line 54
    .line 55
    invoke-direct {v2, p0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadAboutTournament$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 56
    .line 57
    .line 58
    invoke-static {v0, v2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    iget-object v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 67
    .line 68
    invoke-interface {v2}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 69
    .line 70
    .line 71
    move-result-object v2

    .line 72
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 73
    .line 74
    .line 75
    move-result-object v1

    .line 76
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadAboutTournament$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadAboutTournament$2;

    .line 77
    .line 78
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 79
    .line 80
    .line 81
    move-result-object v0

    .line 82
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->a6:Lkotlinx/coroutines/x0;

    .line 83
    .line 84
    return-void
.end method

.method public static synthetic x3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->e5(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private static final synthetic x4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic y3(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->Z4(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final y4()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->h6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 7
    .line 8
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LZx0/k;

    .line 13
    .line 14
    invoke-virtual {v0}, LZx0/k;->f()Lby0/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v0}, Lby0/a;->d()LZx0/g;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {v0}, LQx0/b;->i(LZx0/g;)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-nez v0, :cond_0

    .line 27
    .line 28
    new-instance v0, LZx0/a$b;

    .line 29
    .line 30
    sget-object v1, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 31
    .line 32
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-direct {v0, v1, v2}, LZx0/a$b;-><init>(LZx0/g;Ljava/util/List;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 40
    .line 41
    .line 42
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->R5:Llp0/c;

    .line 43
    .line 44
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 45
    .line 46
    invoke-virtual {v1}, LZx0/h;->d()I

    .line 47
    .line 48
    .line 49
    move-result v1

    .line 50
    iget-object v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 51
    .line 52
    invoke-virtual {v2}, LZx0/h;->j()J

    .line 53
    .line 54
    .line 55
    move-result-wide v2

    .line 56
    invoke-virtual {v0, v1, v2, v3}, Llp0/c;->a(IJ)Lkotlinx/coroutines/flow/e;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadCyberGroups$1;

    .line 61
    .line 62
    const/4 v2, 0x0

    .line 63
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadCyberGroups$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 64
    .line 65
    .line 66
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    iget-object v3, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 75
    .line 76
    invoke-interface {v3}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 77
    .line 78
    .line 79
    move-result-object v3

    .line 80
    invoke-static {v1, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    new-instance v3, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadCyberGroups$2;

    .line 85
    .line 86
    invoke-direct {v3, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadCyberGroups$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 87
    .line 88
    .line 89
    invoke-static {v0, v1, v3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 90
    .line 91
    .line 92
    move-result-object v0

    .line 93
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->h6:Lkotlinx/coroutines/x0;

    .line 94
    .line 95
    return-void
.end method

.method public static synthetic z3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final z4()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->f6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 7
    .line 8
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LZx0/k;

    .line 13
    .line 14
    invoke-virtual {v0}, LZx0/k;->g()Lcy0/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v0}, Lcy0/a;->c()LZx0/g;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {v0}, LQx0/b;->i(LZx0/g;)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-nez v0, :cond_0

    .line 27
    .line 28
    new-instance v0, LZx0/a$c;

    .line 29
    .line 30
    sget-object v1, LZx0/g$a$a;->a:LZx0/g$a$a;

    .line 31
    .line 32
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-direct {v0, v1, v2}, LZx0/a$c;-><init>(LZx0/g;Ljava/util/List;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5(LZx0/a;)V

    .line 40
    .line 41
    .line 42
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->N5:Llp0/g;

    .line 43
    .line 44
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 45
    .line 46
    invoke-virtual {v1}, LZx0/h;->d()I

    .line 47
    .line 48
    .line 49
    move-result v1

    .line 50
    iget-object v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 51
    .line 52
    invoke-virtual {v2}, LZx0/h;->j()J

    .line 53
    .line 54
    .line 55
    move-result-wide v2

    .line 56
    invoke-virtual {v0, v1, v2, v3}, Llp0/g;->a(IJ)Lkotlinx/coroutines/flow/e;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadCyberTeams$1;

    .line 61
    .line 62
    const/4 v2, 0x0

    .line 63
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadCyberTeams$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 64
    .line 65
    .line 66
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    iget-object v3, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 75
    .line 76
    invoke-interface {v3}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 77
    .line 78
    .line 79
    move-result-object v3

    .line 80
    invoke-static {v1, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    new-instance v3, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadCyberTeams$2;

    .line 85
    .line 86
    invoke-direct {v3, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadCyberTeams$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 87
    .line 88
    .line 89
    invoke-static {v0, v1, v3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 90
    .line 91
    .line 92
    move-result-object v0

    .line 93
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->f6:Lkotlinx/coroutines/x0;

    .line 94
    .line 95
    return-void
.end method


# virtual methods
.method public A(LAp/a;)V
    .locals 1
    .param p1    # LAp/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S1:LVo/e;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lorg/xbet/betting/event_card/presentation/delegates/a;->A(LAp/a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public A1()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->A5:Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;

    invoke-interface {v0}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;->A1()Lkotlinx/coroutines/flow/e;

    move-result-object v0

    return-object v0
.end method

.method public B1(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;)V
    .locals 1
    .param p1    # Lorg/xbet/betting/core/coupon/models/SingleBetGame;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/betting/core/coupon/models/SimpleBetZip;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S1:LVo/e;

    invoke-interface {v0, p1, p2}, LVo/d;->B1(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;)V

    return-void
.end method

.method public B2(Lfl0/c;)V
    .locals 1
    .param p1    # Lfl0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S2:Lbl0/g;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lbl0/c;->B2(Lfl0/c;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public D(Lfl0/c;)V
    .locals 1
    .param p1    # Lfl0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S2:Lbl0/g;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lbl0/c;->D(Lfl0/c;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public I2()V
    .locals 28

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 4
    .line 5
    :cond_0
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    move-object v3, v2

    .line 10
    check-cast v3, LZx0/k;

    .line 11
    .line 12
    invoke-virtual {v3}, LZx0/k;->p()Liy0/a;

    .line 13
    .line 14
    .line 15
    move-result-object v4

    .line 16
    invoke-virtual {v3}, LZx0/k;->p()Liy0/a;

    .line 17
    .line 18
    .line 19
    move-result-object v5

    .line 20
    invoke-virtual {v5}, Liy0/a;->c()Lxp0/b;

    .line 21
    .line 22
    .line 23
    move-result-object v5

    .line 24
    const/4 v6, 0x1

    .line 25
    const/4 v7, 0x0

    .line 26
    if-eqz v5, :cond_1

    .line 27
    .line 28
    invoke-virtual {v3}, LZx0/k;->p()Liy0/a;

    .line 29
    .line 30
    .line 31
    move-result-object v8

    .line 32
    invoke-virtual {v8}, Liy0/a;->c()Lxp0/b;

    .line 33
    .line 34
    .line 35
    move-result-object v8

    .line 36
    invoke-virtual {v8}, Lxp0/b;->d()Z

    .line 37
    .line 38
    .line 39
    move-result v8

    .line 40
    xor-int/2addr v8, v6

    .line 41
    invoke-static {v5, v7, v8, v6, v7}, Lxp0/b;->b(Lxp0/b;Ljava/util/List;ZILjava/lang/Object;)Lxp0/b;

    .line 42
    .line 43
    .line 44
    move-result-object v5

    .line 45
    goto :goto_0

    .line 46
    :cond_1
    move-object v5, v7

    .line 47
    :goto_0
    invoke-static {v4, v7, v5, v6, v7}, Liy0/a;->b(Liy0/a;LZx0/g;Lxp0/b;ILjava/lang/Object;)Liy0/a;

    .line 48
    .line 49
    .line 50
    move-result-object v19

    .line 51
    const v26, 0x3f7fff

    .line 52
    .line 53
    .line 54
    const/16 v27, 0x0

    .line 55
    .line 56
    const/4 v4, 0x0

    .line 57
    const/4 v5, 0x0

    .line 58
    const/4 v6, 0x0

    .line 59
    const/4 v7, 0x0

    .line 60
    const/4 v8, 0x0

    .line 61
    const/4 v9, 0x0

    .line 62
    const/4 v10, 0x0

    .line 63
    const/4 v11, 0x0

    .line 64
    const/4 v12, 0x0

    .line 65
    const/4 v13, 0x0

    .line 66
    const/4 v14, 0x0

    .line 67
    const/4 v15, 0x0

    .line 68
    const/16 v16, 0x0

    .line 69
    .line 70
    const/16 v17, 0x0

    .line 71
    .line 72
    const/16 v18, 0x0

    .line 73
    .line 74
    const/16 v20, 0x0

    .line 75
    .line 76
    const/16 v21, 0x0

    .line 77
    .line 78
    const/16 v22, 0x0

    .line 79
    .line 80
    const/16 v23, 0x0

    .line 81
    .line 82
    const/16 v24, 0x0

    .line 83
    .line 84
    const/16 v25, 0x0

    .line 85
    .line 86
    invoke-static/range {v3 .. v27}, LZx0/k;->b(LZx0/k;Ljava/util/List;LUw0/a;LUw0/a;LUw0/a;Lmy0/a;Ley0/a;Ljy0/a;Loy0/a;Lly0/a;Lhy0/a;Lpy0/a;Ldy0/a;Lfy0/a;Lay0/a;Lay0/b;Liy0/a;Lgy0/a;Lcy0/a;Lky0/a;Lby0/a;LNZ/a;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;ILjava/lang/Object;)LZx0/k;

    .line 87
    .line 88
    .line 89
    move-result-object v3

    .line 90
    invoke-interface {v1, v2, v3}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 91
    .line 92
    .line 93
    move-result v2

    .line 94
    if-eqz v2, :cond_0

    .line 95
    .line 96
    return-void
.end method

.method public J(ILjava/lang/Integer;JI)V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->A5:Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;

    move v1, p1

    move-object v2, p2

    move-wide v3, p3

    move v5, p5

    invoke-interface/range {v0 .. v5}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;->J(ILjava/lang/Integer;JI)V

    return-void
.end method

.method public M2(J)Z
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S2:Lbl0/g;

    invoke-interface {v0, p1, p2}, Lbl0/c;->M2(J)Z

    move-result p1

    return p1
.end method

.method public N2(Lyp0/a;)V
    .locals 2
    .param p1    # Lyp0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->q6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/a$e;

    .line 4
    .line 5
    invoke-direct {v1, p1}, Lorg/xbet/special_event/impl/tournament/presentation/a$e;-><init>(Lyp0/a;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public O1()V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->G5:LFy0/a;

    .line 4
    .line 5
    new-instance v2, Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;

    .line 6
    .line 7
    iget v3, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y1:I

    .line 8
    .line 9
    iget-object v4, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 10
    .line 11
    invoke-virtual {v4}, LZx0/h;->i()J

    .line 12
    .line 13
    .line 14
    move-result-wide v4

    .line 15
    iget-object v6, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 16
    .line 17
    invoke-virtual {v6}, LZx0/h;->f()I

    .line 18
    .line 19
    .line 20
    move-result v6

    .line 21
    invoke-direct {v2, v3, v4, v5, v6}, Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;-><init>(IJI)V

    .line 22
    .line 23
    .line 24
    invoke-interface {v1, v2}, LFy0/a;->a(Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;)Lq4/q;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public P(LAp/e;)V
    .locals 1
    .param p1    # LAp/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S1:LVo/e;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lorg/xbet/betting/event_card/presentation/delegates/a;->P(LAp/e;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public Q0()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LYo/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S1:LVo/e;

    invoke-interface {v0}, LVo/d;->Q0()Lkotlinx/coroutines/flow/e;

    move-result-object v0

    return-object v0
.end method

.method public R()V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->H4:LDH0/a;

    .line 4
    .line 5
    invoke-virtual {v0}, LZx0/h;->d()I

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    invoke-static {v2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-virtual {v0}, LZx0/h;->i()J

    .line 14
    .line 15
    .line 16
    move-result-wide v3

    .line 17
    invoke-virtual {v0}, LZx0/h;->f()I

    .line 18
    .line 19
    .line 20
    move-result v5

    .line 21
    int-to-long v5, v5

    .line 22
    invoke-interface/range {v1 .. v6}, LDH0/a;->c(Ljava/lang/String;JJ)Lq4/q;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    iget-object v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 27
    .line 28
    invoke-virtual {v0}, LZx0/h;->d()I

    .line 29
    .line 30
    .line 31
    move-result v0

    .line 32
    invoke-virtual {v2, v0}, LPx0/a;->b(I)V

    .line 33
    .line 34
    .line 35
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 36
    .line 37
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public S()V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->H4:LDH0/a;

    .line 4
    .line 5
    invoke-virtual {v0}, LZx0/h;->d()I

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    invoke-static {v2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-virtual {v0}, LZx0/h;->i()J

    .line 14
    .line 15
    .line 16
    move-result-wide v3

    .line 17
    invoke-virtual {v0}, LZx0/h;->f()I

    .line 18
    .line 19
    .line 20
    move-result v5

    .line 21
    int-to-long v5, v5

    .line 22
    invoke-interface/range {v1 .. v6}, LDH0/a;->g(Ljava/lang/String;JJ)Lq4/q;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    iget-object v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 27
    .line 28
    invoke-virtual {v0}, LZx0/h;->d()I

    .line 29
    .line 30
    .line 31
    move-result v0

    .line 32
    invoke-virtual {v2, v0}, LPx0/a;->c(I)V

    .line 33
    .line 34
    .line 35
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 36
    .line 37
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public T()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 4
    .line 5
    invoke-virtual {v1}, LZx0/h;->d()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-virtual {v0, v1}, LPx0/a;->h(I)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->n6:Lq4/m;

    .line 13
    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    invoke-interface {v0}, Lq4/m;->dispose()V

    .line 17
    .line 18
    .line 19
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 20
    .line 21
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/h;

    .line 22
    .line 23
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/tournament/presentation/h;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 24
    .line 25
    .line 26
    const-string v2, "MAKE_BET_TAG"

    .line 27
    .line 28
    invoke-virtual {v0, v2, v1}, Lq4/c;->d(Ljava/lang/String;Lq4/l;)Lq4/m;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->n6:Lq4/m;

    .line 33
    .line 34
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 35
    .line 36
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x2:Ltn/a;

    .line 37
    .line 38
    new-instance v2, Ltn/b;

    .line 39
    .line 40
    sget-object v3, Lorg/xbet/bethistory_champ/domain/model/BetHistoryTypeModel;->EVENTS:Lorg/xbet/bethistory_champ/domain/model/BetHistoryTypeModel;

    .line 41
    .line 42
    invoke-virtual {v3}, Lorg/xbet/bethistory_champ/domain/model/BetHistoryTypeModel;->getId()I

    .line 43
    .line 44
    .line 45
    move-result v3

    .line 46
    iget-object v4, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 47
    .line 48
    invoke-virtual {v4}, LZx0/h;->d()I

    .line 49
    .line 50
    .line 51
    move-result v4

    .line 52
    invoke-static {v4}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v8

    .line 56
    iget-object v9, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->H1:Ljava/lang/String;

    .line 57
    .line 58
    const-wide/16 v4, 0x0

    .line 59
    .line 60
    const-wide/16 v6, 0x0

    .line 61
    .line 62
    invoke-direct/range {v2 .. v9}, Ltn/b;-><init>(IJJLjava/lang/String;Ljava/lang/String;)V

    .line 63
    .line 64
    .line 65
    invoke-interface {v1, v2}, Ltn/a;->a(Ltn/b;)Lr4/d;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    const/4 v2, 0x1

    .line 70
    invoke-virtual {v0, v2, v1}, LwX0/c;->o(ZLq4/q;)V

    .line 71
    .line 72
    .line 73
    return-void
.end method

.method public T2(Lfl0/c;)V
    .locals 1
    .param p1    # Lfl0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S2:Lbl0/g;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lbl0/c;->T2(Lfl0/c;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public U()V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 4
    .line 5
    invoke-virtual {v1}, LZx0/h;->d()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-virtual {v0, v1}, LPx0/a;->m(I)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 13
    .line 14
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F2:LWo0/a;

    .line 15
    .line 16
    new-instance v2, Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams$AllGames;

    .line 17
    .line 18
    iget-object v3, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 19
    .line 20
    invoke-virtual {v3}, LZx0/h;->d()I

    .line 21
    .line 22
    .line 23
    move-result v3

    .line 24
    iget-object v4, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 25
    .line 26
    invoke-interface {v4}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v4

    .line 30
    check-cast v4, LZx0/k;

    .line 31
    .line 32
    invoke-virtual {v4}, LZx0/k;->u()Lmy0/a;

    .line 33
    .line 34
    .line 35
    move-result-object v4

    .line 36
    invoke-virtual {v4}, Lmy0/a;->g()Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object v4

    .line 40
    invoke-direct {v2, v3, v4}, Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams$AllGames;-><init>(ILjava/util/List;)V

    .line 41
    .line 42
    .line 43
    invoke-interface {v1, v2}, LWo0/a;->a(Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams;)Lq4/q;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 48
    .line 49
    .line 50
    return-void
.end method

.method public W()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y1:I

    .line 4
    .line 5
    invoke-virtual {v0, v1}, LPx0/a;->a(I)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 9
    .line 10
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->v5:Lry0/a;

    .line 11
    .line 12
    iget v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y1:I

    .line 13
    .line 14
    invoke-interface {v1, v2}, Lry0/a;->a(I)Lq4/q;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 19
    .line 20
    .line 21
    return-void
.end method

.method public X()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 4
    .line 5
    invoke-virtual {v0}, LZx0/h;->d()I

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    invoke-virtual {v1, v2}, LPx0/a;->j(I)V

    .line 10
    .line 11
    .line 12
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 13
    .line 14
    new-instance v2, Lorg/xbet/special_event/impl/top_players/presentation/i;

    .line 15
    .line 16
    invoke-virtual {v0}, LZx0/h;->d()I

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    invoke-direct {v2, v0}, Lorg/xbet/special_event/impl/top_players/presentation/i;-><init>(I)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v1, v2}, LwX0/c;->m(Lq4/q;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public Y()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 4
    .line 5
    invoke-virtual {v0}, LZx0/h;->d()I

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    invoke-virtual {v1, v2}, LPx0/a;->f(I)V

    .line 10
    .line 11
    .line 12
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 13
    .line 14
    iget-object v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->z5:LKs0/a;

    .line 15
    .line 16
    invoke-virtual {v0}, LZx0/h;->d()I

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    invoke-interface {v2, v0}, LKs0/a;->a(I)Lq4/q;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-virtual {v1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public a(JLjava/lang/String;)V
    .locals 4
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F2:LWo0/a;

    .line 4
    .line 5
    new-instance v2, Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams$StadiumGames;

    .line 6
    .line 7
    invoke-virtual {v0}, LZx0/h;->d()I

    .line 8
    .line 9
    .line 10
    move-result v3

    .line 11
    invoke-direct {v2, v3, p1, p2, p3}, Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams$StadiumGames;-><init>(IJLjava/lang/String;)V

    .line 12
    .line 13
    .line 14
    invoke-interface {v1, v2}, LWo0/a;->a(Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams;)Lq4/q;

    .line 15
    .line 16
    .line 17
    move-result-object p3

    .line 18
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 19
    .line 20
    invoke-virtual {v0}, LZx0/h;->d()I

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    invoke-virtual {v1, v0, p1, p2}, LPx0/a;->n(IJ)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 28
    .line 29
    invoke-virtual {p1, p3}, LwX0/c;->m(Lq4/q;)V

    .line 30
    .line 31
    .line 32
    return-void
.end method

.method public final a5()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->w5:Lorg/xbet/ui_common/utils/internet/a;

    .line 14
    .line 15
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;

    .line 20
    .line 21
    const/4 v2, 0x0

    .line 22
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    iget-object v3, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 34
    .line 35
    invoke-interface {v3}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 36
    .line 37
    .line 38
    move-result-object v3

    .line 39
    invoke-static {v1, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    new-instance v3, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$2;

    .line 44
    .line 45
    invoke-direct {v3, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$2;-><init>(Lkotlin/coroutines/e;)V

    .line 46
    .line 47
    .line 48
    invoke-static {v0, v1, v3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S5:Lkotlinx/coroutines/x0;

    .line 53
    .line 54
    return-void
.end method

.method public b(JLjava/lang/String;)V
    .locals 3
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F2:LWo0/a;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams$StadiumGames;

    .line 4
    .line 5
    iget v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y1:I

    .line 6
    .line 7
    invoke-direct {v1, v2, p1, p2, p3}, Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams$StadiumGames;-><init>(IJLjava/lang/String;)V

    .line 8
    .line 9
    .line 10
    invoke-interface {v0, v1}, LWo0/a;->a(Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams;)Lq4/q;

    .line 11
    .line 12
    .line 13
    move-result-object p3

    .line 14
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 15
    .line 16
    iget v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y1:I

    .line 17
    .line 18
    invoke-virtual {v0, v1, p1, p2}, LPx0/a;->n(IJ)V

    .line 19
    .line 20
    .line 21
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 22
    .line 23
    invoke-virtual {p1, p3}, LwX0/c;->m(Lq4/q;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public c(I)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/i;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/tournament/presentation/i;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;ILkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public c0()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 4
    .line 5
    invoke-virtual {v1}, LZx0/h;->d()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-virtual {v0, v1}, LPx0/a;->d(I)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->y2:Lvw0/a;

    .line 13
    .line 14
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 15
    .line 16
    invoke-virtual {v1}, LZx0/h;->d()I

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    invoke-interface {v0, v1}, Lvw0/a;->a(I)Lq4/q;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 25
    .line 26
    invoke-virtual {v1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public c2(Lfl0/c;)V
    .locals 1
    .param p1    # Lfl0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S2:Lbl0/g;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lbl0/c;->c2(Lfl0/c;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public c3(ILjava/lang/Integer;JI)V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->A5:Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;

    move v1, p1

    move-object v2, p2

    move-wide v3, p3

    move v5, p5

    invoke-interface/range {v0 .. v5}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;->c3(ILjava/lang/Integer;JI)V

    return-void
.end method

.method public f2(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;)V
    .locals 1
    .param p1    # Lorg/xbet/betting/core/coupon/models/SingleBetGame;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/betting/core/coupon/models/SimpleBetZip;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->A5:Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;

    invoke-interface {v0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;->f2(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;)V

    return-void
.end method

.method public f3(LAp/b;)V
    .locals 5
    .param p1    # LAp/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 2
    .line 3
    invoke-virtual {p1}, LAp/b;->e()J

    .line 4
    .line 5
    .line 6
    move-result-wide v1

    .line 7
    iget-object v3, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 8
    .line 9
    invoke-virtual {v3}, LZx0/h;->d()I

    .line 10
    .line 11
    .line 12
    move-result v3

    .line 13
    invoke-virtual {p1}, LAp/b;->d()Z

    .line 14
    .line 15
    .line 16
    move-result v4

    .line 17
    invoke-virtual {v0, v1, v2, v3, v4}, LPx0/a;->i(JIZ)V

    .line 18
    .line 19
    .line 20
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S1:LVo/e;

    .line 21
    .line 22
    invoke-interface {v0, p1}, Lorg/xbet/betting/event_card/presentation/delegates/a;->f3(LAp/b;)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public g(Ljava/lang/String;)V
    .locals 5
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 4
    .line 5
    invoke-virtual {v0}, LZx0/h;->d()I

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    invoke-virtual {v1, v2}, LPx0/a;->k(I)V

    .line 10
    .line 11
    .line 12
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->H4:LDH0/a;

    .line 13
    .line 14
    const-string v2, ""

    .line 15
    .line 16
    invoke-virtual {v0}, LZx0/h;->i()J

    .line 17
    .line 18
    .line 19
    move-result-wide v3

    .line 20
    invoke-interface {v1, v2, v3, v4, p1}, LDH0/a;->h(Ljava/lang/String;JLjava/lang/String;)Lq4/q;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F1:LwX0/c;

    .line 25
    .line 26
    invoke-virtual {v0, p1}, LwX0/c;->m(Lq4/q;)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public g1(ILjava/lang/Integer;)V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 2
    .line 3
    invoke-virtual {v0}, LZx0/h;->i()J

    .line 4
    .line 5
    .line 6
    move-result-wide v4

    .line 7
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 8
    .line 9
    invoke-virtual {v0}, LZx0/h;->f()I

    .line 10
    .line 11
    .line 12
    move-result v6

    .line 13
    move-object v1, p0

    .line 14
    move v2, p1

    .line 15
    move-object v3, p2

    .line 16
    invoke-virtual/range {v1 .. v6}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->c3(ILjava/lang/Integer;JI)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public g2(Lfl0/c;)V
    .locals 1
    .param p1    # Lfl0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S2:Lbl0/g;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lbl0/c;->g2(Lfl0/c;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public g3(Lpx0/a;I)V
    .locals 4
    .param p1    # Lpx0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LZx0/k;

    .line 8
    .line 9
    invoke-virtual {v0}, LZx0/k;->q()Ljy0/a;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0}, Ljy0/a;->b()Ljava/util/List;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    if-eqz v1, :cond_1

    .line 26
    .line 27
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    move-object v2, v1

    .line 32
    check-cast v2, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 33
    .line 34
    invoke-virtual {v2}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 35
    .line 36
    .line 37
    move-result v2

    .line 38
    invoke-virtual {p1}, Lpx0/a;->d()I

    .line 39
    .line 40
    .line 41
    move-result v3

    .line 42
    if-ne v2, v3, :cond_0

    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_1
    const/4 v1, 0x0

    .line 46
    :goto_0
    check-cast v1, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 47
    .line 48
    if-nez v1, :cond_2

    .line 49
    .line 50
    return-void

    .line 51
    :cond_2
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 52
    .line 53
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 54
    .line 55
    invoke-virtual {v0}, LZx0/h;->d()I

    .line 56
    .line 57
    .line 58
    move-result v0

    .line 59
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 60
    .line 61
    .line 62
    move-result v2

    .line 63
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getTranslateId()Ljava/lang/String;

    .line 64
    .line 65
    .line 66
    move-result-object v3

    .line 67
    invoke-virtual {p1, v0, v2, v3}, LPx0/a;->l(IILjava/lang/String;)V

    .line 68
    .line 69
    .line 70
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getAction()Z

    .line 71
    .line 72
    .line 73
    move-result p1

    .line 74
    if-eqz p1, :cond_3

    .line 75
    .line 76
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getDeeplink()Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 81
    .line 82
    .line 83
    move-result p1

    .line 84
    if-lez p1, :cond_3

    .line 85
    .line 86
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getDeeplink()Ljava/lang/String;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/a$b;->b(Ljava/lang/String;)Ljava/lang/String;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/a$b;->a(Ljava/lang/String;)Lorg/xbet/special_event/impl/tournament/presentation/a$b;

    .line 95
    .line 96
    .line 97
    move-result-object p1

    .line 98
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->j5(Lorg/xbet/special_event/impl/tournament/presentation/a;)V

    .line 99
    .line 100
    .line 101
    return-void

    .line 102
    :cond_3
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getAction()Z

    .line 103
    .line 104
    .line 105
    move-result p1

    .line 106
    if-eqz p1, :cond_4

    .line 107
    .line 108
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getSiteLink()Ljava/lang/String;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 113
    .line 114
    .line 115
    move-result p1

    .line 116
    if-lez p1, :cond_4

    .line 117
    .line 118
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getSiteLink()Ljava/lang/String;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    invoke-direct {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->i5(Ljava/lang/String;)V

    .line 123
    .line 124
    .line 125
    return-void

    .line 126
    :cond_4
    invoke-direct {p0, v1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->g5(Lorg/xplatform/banners/api/domain/models/BannerModel;I)V

    .line 127
    .line 128
    .line 129
    return-void
.end method

.method public j1()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->A5:Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;

    invoke-interface {v0}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;->j1()V

    return-void
.end method

.method public final j5(Lorg/xbet/special_event/impl/tournament/presentation/a;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/n;

    .line 12
    .line 13
    invoke-direct {v1}, Lorg/xbet/special_event/impl/tournament/presentation/n;-><init>()V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$sendEvent$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$sendEvent$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lorg/xbet/special_event/impl/tournament/presentation/a;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public k2()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LYo/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S1:LVo/e;

    invoke-interface {v0}, LVo/d;->k2()Lkotlinx/coroutines/flow/e;

    move-result-object v0

    return-object v0
.end method

.method public l0()V
    .locals 28

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 4
    .line 5
    :cond_0
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    move-object v3, v2

    .line 10
    check-cast v3, LZx0/k;

    .line 11
    .line 12
    invoke-virtual {v3}, LZx0/k;->c()Lay0/a;

    .line 13
    .line 14
    .line 15
    move-result-object v4

    .line 16
    invoke-virtual {v4}, Lay0/a;->c()Lkp0/a;

    .line 17
    .line 18
    .line 19
    move-result-object v4

    .line 20
    instance-of v4, v4, Lkp0/a$b;

    .line 21
    .line 22
    if-eqz v4, :cond_1

    .line 23
    .line 24
    invoke-virtual {v3}, LZx0/k;->c()Lay0/a;

    .line 25
    .line 26
    .line 27
    move-result-object v4

    .line 28
    invoke-virtual {v3}, LZx0/k;->c()Lay0/a;

    .line 29
    .line 30
    .line 31
    move-result-object v5

    .line 32
    invoke-virtual {v5}, Lay0/a;->c()Lkp0/a;

    .line 33
    .line 34
    .line 35
    move-result-object v5

    .line 36
    move-object v6, v5

    .line 37
    check-cast v6, Lkp0/a$b;

    .line 38
    .line 39
    invoke-virtual {v3}, LZx0/k;->c()Lay0/a;

    .line 40
    .line 41
    .line 42
    move-result-object v5

    .line 43
    invoke-virtual {v5}, Lay0/a;->c()Lkp0/a;

    .line 44
    .line 45
    .line 46
    move-result-object v5

    .line 47
    check-cast v5, Lkp0/a$b;

    .line 48
    .line 49
    invoke-virtual {v5}, Lkp0/a$b;->i()Z

    .line 50
    .line 51
    .line 52
    move-result v5

    .line 53
    const/4 v7, 0x1

    .line 54
    xor-int/lit8 v13, v5, 0x1

    .line 55
    .line 56
    const/16 v14, 0x3f

    .line 57
    .line 58
    const/4 v15, 0x0

    .line 59
    const/4 v5, 0x1

    .line 60
    const/4 v7, 0x0

    .line 61
    const/4 v8, 0x0

    .line 62
    const/4 v9, 0x0

    .line 63
    const/4 v10, 0x0

    .line 64
    const/4 v11, 0x0

    .line 65
    const/4 v12, 0x0

    .line 66
    invoke-static/range {v6 .. v15}, Lkp0/a$b;->b(Lkp0/a$b;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)Lkp0/a$b;

    .line 67
    .line 68
    .line 69
    move-result-object v6

    .line 70
    invoke-static {v4, v7, v6, v5, v7}, Lay0/a;->b(Lay0/a;LZx0/g;Lkp0/a;ILjava/lang/Object;)Lay0/a;

    .line 71
    .line 72
    .line 73
    move-result-object v17

    .line 74
    const v26, 0x3fdfff

    .line 75
    .line 76
    .line 77
    const/16 v27, 0x0

    .line 78
    .line 79
    const/4 v4, 0x0

    .line 80
    const/4 v5, 0x0

    .line 81
    const/4 v6, 0x0

    .line 82
    const/4 v13, 0x0

    .line 83
    const/4 v14, 0x0

    .line 84
    const/16 v16, 0x0

    .line 85
    .line 86
    const/16 v18, 0x0

    .line 87
    .line 88
    const/16 v19, 0x0

    .line 89
    .line 90
    const/16 v20, 0x0

    .line 91
    .line 92
    const/16 v21, 0x0

    .line 93
    .line 94
    const/16 v22, 0x0

    .line 95
    .line 96
    const/16 v23, 0x0

    .line 97
    .line 98
    const/16 v24, 0x0

    .line 99
    .line 100
    const/16 v25, 0x0

    .line 101
    .line 102
    invoke-static/range {v3 .. v27}, LZx0/k;->b(LZx0/k;Ljava/util/List;LUw0/a;LUw0/a;LUw0/a;Lmy0/a;Ley0/a;Ljy0/a;Loy0/a;Lly0/a;Lhy0/a;Lpy0/a;Ldy0/a;Lfy0/a;Lay0/a;Lay0/b;Liy0/a;Lgy0/a;Lcy0/a;Lky0/a;Lby0/a;LNZ/a;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;ILjava/lang/Object;)LZx0/k;

    .line 103
    .line 104
    .line 105
    move-result-object v3

    .line 106
    :cond_1
    invoke-interface {v1, v2, v3}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 107
    .line 108
    .line 109
    move-result v2

    .line 110
    if-eqz v2, :cond_0

    .line 111
    .line 112
    return-void
.end method

.method public final l5()V
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/special_event/impl/tournament/presentation/a$a;->a:Lorg/xbet/special_event/impl/tournament/presentation/a$a;

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->j5(Lorg/xbet/special_event/impl/tournament/presentation/a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public m(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/zip/model/bet/BetInfo;Z)V
    .locals 1
    .param p1    # Lorg/xbet/betting/core/coupon/models/SingleBetGame;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/betting/core/zip/model/bet/BetInfo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S1:LVo/e;

    invoke-interface {v0, p1, p2, p3}, LVo/d;->m(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/zip/model/bet/BetInfo;Z)V

    return-void
.end method

.method public n1(LAp/a;)V
    .locals 1
    .param p1    # LAp/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S1:LVo/e;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lorg/xbet/betting/event_card/presentation/delegates/a;->n1(LAp/a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public n2(LAp/f;)V
    .locals 1
    .param p1    # LAp/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S1:LVo/e;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lorg/xbet/betting/event_card/presentation/delegates/a;->n2(LAp/f;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public n3(ILjava/lang/Integer;)V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 2
    .line 3
    invoke-virtual {v0}, LZx0/h;->i()J

    .line 4
    .line 5
    .line 6
    move-result-wide v4

    .line 7
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 8
    .line 9
    invoke-virtual {v0}, LZx0/h;->f()I

    .line 10
    .line 11
    .line 12
    move-result v6

    .line 13
    move-object v1, p0

    .line 14
    move v2, p1

    .line 15
    move-object v3, p2

    .line 16
    invoke-virtual/range {v1 .. v6}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->J(ILjava/lang/Integer;JI)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public o1(LAp/c;)V
    .locals 1
    .param p1    # LAp/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S1:LVo/e;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lorg/xbet/betting/event_card/presentation/delegates/a;->o1(LAp/c;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final o4()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p4()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public onCleared()V
    .locals 1

    .line 1
    invoke-super {p0}, Lorg/xbet/ui_common/viewmodel/core/c;->onCleared()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->n6:Lq4/m;

    .line 5
    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-interface {v0}, Lq4/m;->dispose()V

    .line 9
    .line 10
    .line 11
    :cond_0
    return-void
.end method

.method public r(LAp/d;)V
    .locals 1
    .param p1    # LAp/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S1:LVo/e;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lorg/xbet/betting/event_card/presentation/delegates/a;->r(LAp/d;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public r1(I)V
    .locals 28

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 4
    .line 5
    :cond_0
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    move-object v3, v2

    .line 10
    check-cast v3, LZx0/k;

    .line 11
    .line 12
    invoke-virtual {v3}, LZx0/k;->f()Lby0/a;

    .line 13
    .line 14
    .line 15
    move-result-object v4

    .line 16
    invoke-virtual {v4}, Lby0/a;->c()Lwp0/f;

    .line 17
    .line 18
    .line 19
    move-result-object v4

    .line 20
    if-eqz v4, :cond_1

    .line 21
    .line 22
    invoke-virtual {v3}, LZx0/k;->f()Lby0/a;

    .line 23
    .line 24
    .line 25
    move-result-object v4

    .line 26
    invoke-virtual {v3}, LZx0/k;->f()Lby0/a;

    .line 27
    .line 28
    .line 29
    move-result-object v5

    .line 30
    invoke-virtual {v5}, Lby0/a;->c()Lwp0/f;

    .line 31
    .line 32
    .line 33
    move-result-object v6

    .line 34
    const/4 v11, 0x6

    .line 35
    const/4 v12, 0x0

    .line 36
    const-wide/16 v8, 0x0

    .line 37
    .line 38
    const/4 v10, 0x0

    .line 39
    move/from16 v7, p1

    .line 40
    .line 41
    invoke-static/range {v6 .. v12}, Lwp0/f;->b(Lwp0/f;IJLjava/util/List;ILjava/lang/Object;)Lwp0/f;

    .line 42
    .line 43
    .line 44
    move-result-object v5

    .line 45
    const/4 v6, 0x1

    .line 46
    const/4 v7, 0x0

    .line 47
    invoke-static {v4, v7, v5, v6, v7}, Lby0/a;->b(Lby0/a;LZx0/g;Lwp0/f;ILjava/lang/Object;)Lby0/a;

    .line 48
    .line 49
    .line 50
    move-result-object v23

    .line 51
    const v26, 0x37ffff

    .line 52
    .line 53
    .line 54
    const/16 v27, 0x0

    .line 55
    .line 56
    const/4 v4, 0x0

    .line 57
    const/4 v5, 0x0

    .line 58
    const/4 v6, 0x0

    .line 59
    const/4 v8, 0x0

    .line 60
    const/4 v9, 0x0

    .line 61
    const/4 v11, 0x0

    .line 62
    const/4 v13, 0x0

    .line 63
    const/4 v14, 0x0

    .line 64
    const/4 v15, 0x0

    .line 65
    const/16 v16, 0x0

    .line 66
    .line 67
    const/16 v17, 0x0

    .line 68
    .line 69
    const/16 v18, 0x0

    .line 70
    .line 71
    const/16 v19, 0x0

    .line 72
    .line 73
    const/16 v20, 0x0

    .line 74
    .line 75
    const/16 v21, 0x0

    .line 76
    .line 77
    const/16 v22, 0x0

    .line 78
    .line 79
    const/16 v24, 0x0

    .line 80
    .line 81
    const/16 v25, 0x0

    .line 82
    .line 83
    invoke-static/range {v3 .. v27}, LZx0/k;->b(LZx0/k;Ljava/util/List;LUw0/a;LUw0/a;LUw0/a;Lmy0/a;Ley0/a;Ljy0/a;Loy0/a;Lly0/a;Lhy0/a;Lpy0/a;Ldy0/a;Lfy0/a;Lay0/a;Lay0/b;Liy0/a;Lgy0/a;Lcy0/a;Lky0/a;Lby0/a;LNZ/a;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;ILjava/lang/Object;)LZx0/k;

    .line 84
    .line 85
    .line 86
    move-result-object v3

    .line 87
    :cond_1
    invoke-interface {v1, v2, v3}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 88
    .line 89
    .line 90
    move-result v2

    .line 91
    if-eqz v2, :cond_0

    .line 92
    .line 93
    return-void
.end method

.method public s(Lel0/b;)V
    .locals 4
    .param p1    # Lel0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->x5:LPx0/a;

    .line 2
    .line 3
    invoke-virtual {p1}, Lel0/b;->c()J

    .line 4
    .line 5
    .line 6
    move-result-wide v1

    .line 7
    iget-object v3, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 8
    .line 9
    invoke-virtual {v3}, LZx0/h;->d()I

    .line 10
    .line 11
    .line 12
    move-result v3

    .line 13
    invoke-virtual {v0, v1, v2, v3}, LPx0/a;->g(JI)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S2:Lbl0/g;

    .line 17
    .line 18
    invoke-interface {v0, p1}, Lbl0/c;->s(Lel0/b;)V

    .line 19
    .line 20
    .line 21
    return-void
.end method

.method public final s4()LZx0/k;
    .locals 26

    .line 1
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    new-instance v8, Loy0/a;

    .line 6
    .line 7
    sget-object v10, LZx0/g$b;->a:LZx0/g$b;

    .line 8
    .line 9
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-direct {v8, v10, v0}, Loy0/a;-><init>(LZx0/g;Ljava/util/List;)V

    .line 14
    .line 15
    .line 16
    new-instance v0, Lly0/a;

    .line 17
    .line 18
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    invoke-direct {v0, v10, v2}, Lly0/a;-><init>(LZx0/g;Ljava/util/List;)V

    .line 23
    .line 24
    .line 25
    new-instance v5, Lmy0/a;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 28
    .line 29
    .line 30
    move-result-object v11

    .line 31
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 32
    .line 33
    .line 34
    move-result-object v12

    .line 35
    new-instance v13, Lny0/c;

    .line 36
    .line 37
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object v3

    .line 45
    invoke-direct {v13, v10, v2, v3}, Lny0/c;-><init>(LZx0/g;Ljava/util/List;Ljava/util/List;)V

    .line 46
    .line 47
    .line 48
    new-instance v14, Lny0/b;

    .line 49
    .line 50
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 51
    .line 52
    .line 53
    move-result-object v2

    .line 54
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 55
    .line 56
    .line 57
    move-result-object v3

    .line 58
    invoke-direct {v14, v10, v2, v3}, Lny0/b;-><init>(LZx0/g;Ljava/util/List;Ljava/util/List;)V

    .line 59
    .line 60
    .line 61
    new-instance v15, Lny0/a;

    .line 62
    .line 63
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 64
    .line 65
    .line 66
    move-result-object v2

    .line 67
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 68
    .line 69
    .line 70
    move-result-object v3

    .line 71
    invoke-static {}, Lkotlin/collections/Z;->e()Ljava/util/Set;

    .line 72
    .line 73
    .line 74
    move-result-object v4

    .line 75
    invoke-direct {v15, v10, v2, v3, v4}, Lny0/a;-><init>(LZx0/g;Ljava/util/List;Ljava/util/List;Ljava/util/Set;)V

    .line 76
    .line 77
    .line 78
    move-object v9, v5

    .line 79
    invoke-direct/range {v9 .. v15}, Lmy0/a;-><init>(LZx0/g;Ljava/util/List;Ljava/util/List;Lny0/c;Lny0/b;Lny0/a;)V

    .line 80
    .line 81
    .line 82
    new-instance v7, Ljy0/a;

    .line 83
    .line 84
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 85
    .line 86
    .line 87
    move-result-object v2

    .line 88
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 89
    .line 90
    .line 91
    move-result-object v3

    .line 92
    invoke-direct {v7, v10, v2, v3}, Ljy0/a;-><init>(LZx0/g;Ljava/util/List;Ljava/util/List;)V

    .line 93
    .line 94
    .line 95
    new-instance v6, Ley0/a;

    .line 96
    .line 97
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 98
    .line 99
    .line 100
    move-result-object v2

    .line 101
    invoke-direct {v6, v10, v2}, Ley0/a;-><init>(LZx0/g;Ljava/util/List;)V

    .line 102
    .line 103
    .line 104
    new-instance v2, Lhy0/a;

    .line 105
    .line 106
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 107
    .line 108
    .line 109
    move-result-object v3

    .line 110
    invoke-direct {v2, v10, v3}, Lhy0/a;-><init>(LZx0/g;Ljava/util/List;)V

    .line 111
    .line 112
    .line 113
    new-instance v11, Lpy0/a;

    .line 114
    .line 115
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 116
    .line 117
    .line 118
    move-result-object v3

    .line 119
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 120
    .line 121
    .line 122
    move-result-object v4

    .line 123
    invoke-direct {v11, v10, v3, v4}, Lpy0/a;-><init>(LZx0/g;Ljava/util/List;Ljava/util/List;)V

    .line 124
    .line 125
    .line 126
    new-instance v14, Lay0/a;

    .line 127
    .line 128
    sget-object v3, Lkp0/a$a;->a:Lkp0/a$a;

    .line 129
    .line 130
    invoke-direct {v14, v10, v3}, Lay0/a;-><init>(LZx0/g;Lkp0/a;)V

    .line 131
    .line 132
    .line 133
    new-instance v15, Lay0/b;

    .line 134
    .line 135
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 136
    .line 137
    .line 138
    move-result-object v3

    .line 139
    invoke-direct {v15, v10, v3}, Lay0/b;-><init>(LZx0/g;Ljava/util/List;)V

    .line 140
    .line 141
    .line 142
    new-instance v3, Liy0/a;

    .line 143
    .line 144
    const/4 v4, 0x0

    .line 145
    invoke-direct {v3, v10, v4}, Liy0/a;-><init>(LZx0/g;Lxp0/b;)V

    .line 146
    .line 147
    .line 148
    new-instance v9, Lcy0/a;

    .line 149
    .line 150
    invoke-direct {v9, v10, v4}, Lcy0/a;-><init>(LZx0/g;LAp0/b;)V

    .line 151
    .line 152
    .line 153
    new-instance v12, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;

    .line 154
    .line 155
    sget-object v13, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;->NO_ERROR:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    .line 156
    .line 157
    move-object/from16 v16, v2

    .line 158
    .line 159
    move-object/from16 v2, p0

    .line 160
    .line 161
    iget-object v4, v2, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->P1:LSX0/a;

    .line 162
    .line 163
    sget-object v18, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->NOTHING:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 164
    .line 165
    const/16 v24, 0x1e

    .line 166
    .line 167
    const/16 v25, 0x0

    .line 168
    .line 169
    const/16 v19, 0x0

    .line 170
    .line 171
    const/16 v20, 0x0

    .line 172
    .line 173
    const/16 v21, 0x0

    .line 174
    .line 175
    const-wide/16 v22, 0x0

    .line 176
    .line 177
    move-object/from16 v17, v4

    .line 178
    .line 179
    invoke-static/range {v17 .. v25}, LSX0/a$a;->a(LSX0/a;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;JILjava/lang/Object;)Lorg/xbet/uikit/components/lottie/a;

    .line 180
    .line 181
    .line 182
    move-result-object v4

    .line 183
    invoke-direct {v12, v13, v4}, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;Lorg/xbet/uikit/components/lottie/a;)V

    .line 184
    .line 185
    .line 186
    move-object/from16 v22, v12

    .line 187
    .line 188
    new-instance v12, Ldy0/a;

    .line 189
    .line 190
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 191
    .line 192
    .line 193
    move-result-object v4

    .line 194
    invoke-direct {v12, v10, v4}, Ldy0/a;-><init>(LZx0/g;Ljava/util/List;)V

    .line 195
    .line 196
    .line 197
    new-instance v13, Lfy0/a;

    .line 198
    .line 199
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 200
    .line 201
    .line 202
    move-result-object v4

    .line 203
    move-object/from16 v17, v0

    .line 204
    .line 205
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 206
    .line 207
    .line 208
    move-result-object v0

    .line 209
    invoke-direct {v13, v10, v4, v0}, Lfy0/a;-><init>(LZx0/g;Ljava/util/List;Ljava/util/List;)V

    .line 210
    .line 211
    .line 212
    new-instance v0, Lby0/a;

    .line 213
    .line 214
    const/4 v4, 0x0

    .line 215
    invoke-direct {v0, v10, v4}, Lby0/a;-><init>(LZx0/g;Lwp0/f;)V

    .line 216
    .line 217
    .line 218
    new-instance v4, Lgy0/a;

    .line 219
    .line 220
    move-object/from16 v20, v0

    .line 221
    .line 222
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 223
    .line 224
    .line 225
    move-result-object v0

    .line 226
    invoke-direct {v4, v10, v0}, Lgy0/a;-><init>(LZx0/g;Ljava/util/List;)V

    .line 227
    .line 228
    .line 229
    new-instance v0, Lky0/a;

    .line 230
    .line 231
    move-object/from16 v18, v1

    .line 232
    .line 233
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 234
    .line 235
    .line 236
    move-result-object v1

    .line 237
    invoke-direct {v0, v10, v1}, Lky0/a;-><init>(LZx0/g;Ljava/util/List;)V

    .line 238
    .line 239
    .line 240
    move-object/from16 v19, v0

    .line 241
    .line 242
    new-instance v0, LZx0/k;

    .line 243
    .line 244
    move-object/from16 v1, v18

    .line 245
    .line 246
    move-object/from16 v18, v9

    .line 247
    .line 248
    move-object/from16 v9, v17

    .line 249
    .line 250
    move-object/from16 v17, v4

    .line 251
    .line 252
    const/4 v4, 0x0

    .line 253
    const/4 v2, 0x0

    .line 254
    move-object/from16 v10, v16

    .line 255
    .line 256
    move-object/from16 v16, v3

    .line 257
    .line 258
    const/4 v3, 0x0

    .line 259
    invoke-direct/range {v0 .. v22}, LZx0/k;-><init>(Ljava/util/List;LUw0/a;LUw0/a;LUw0/a;Lmy0/a;Ley0/a;Ljy0/a;Loy0/a;Lly0/a;Lhy0/a;Lpy0/a;Ldy0/a;Lfy0/a;Lay0/a;Lay0/b;Liy0/a;Lgy0/a;Lcy0/a;Lky0/a;Lby0/a;LNZ/a;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;)V

    .line 260
    .line 261
    .line 262
    return-object v0
.end method

.method public final t4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/special_event/impl/tournament/presentation/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->q6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final u4()Lkotlinx/coroutines/flow/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/special_event/impl/tournament/presentation/e;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getTournamentUiState$1;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getTournamentUiState$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->V1:Lm8/a;

    .line 14
    .line 15
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->Z(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/flow/e;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getTournamentUiState$$inlined$map$1;

    .line 24
    .line 25
    invoke-direct {v1, v0, p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getTournamentUiState$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 26
    .line 27
    .line 28
    invoke-static {v1}, Lkotlinx/coroutines/flow/g;->B(Lkotlinx/coroutines/flow/e;)Lkotlinx/coroutines/flow/e;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    return-object v0
.end method

.method public final v5(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;)V
    .locals 29

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    iget-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 6
    .line 7
    :cond_0
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    move-object v4, v3

    .line 12
    check-cast v4, LZx0/k;

    .line 13
    .line 14
    sget-object v5, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$b;->a:[I

    .line 15
    .line 16
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 17
    .line 18
    .line 19
    move-result v6

    .line 20
    aget v5, v5, v6

    .line 21
    .line 22
    const/4 v6, 0x1

    .line 23
    if-eq v5, v6, :cond_3

    .line 24
    .line 25
    const/4 v6, 0x2

    .line 26
    if-eq v5, v6, :cond_2

    .line 27
    .line 28
    const/4 v6, 0x3

    .line 29
    if-ne v5, v6, :cond_1

    .line 30
    .line 31
    invoke-virtual {v4}, LZx0/k;->l()Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;

    .line 32
    .line 33
    .line 34
    move-result-object v5

    .line 35
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->P1:LSX0/a;

    .line 36
    .line 37
    sget-object v7, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->NOTHING:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 38
    .line 39
    const/16 v13, 0x1e

    .line 40
    .line 41
    const/4 v14, 0x0

    .line 42
    const/4 v8, 0x0

    .line 43
    const/4 v9, 0x0

    .line 44
    const/4 v10, 0x0

    .line 45
    const-wide/16 v11, 0x0

    .line 46
    .line 47
    invoke-static/range {v6 .. v14}, LSX0/a$a;->a(LSX0/a;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;JILjava/lang/Object;)Lorg/xbet/uikit/components/lottie/a;

    .line 48
    .line 49
    .line 50
    move-result-object v6

    .line 51
    invoke-virtual {v5, v1, v6}, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->a(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;Lorg/xbet/uikit/components/lottie/a;)Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;

    .line 52
    .line 53
    .line 54
    move-result-object v26

    .line 55
    const v27, 0x1fffff

    .line 56
    .line 57
    .line 58
    const/16 v28, 0x0

    .line 59
    .line 60
    const/4 v5, 0x0

    .line 61
    const/4 v6, 0x0

    .line 62
    const/4 v7, 0x0

    .line 63
    const/4 v8, 0x0

    .line 64
    const/4 v9, 0x0

    .line 65
    const/4 v11, 0x0

    .line 66
    const/4 v12, 0x0

    .line 67
    const/4 v13, 0x0

    .line 68
    const/4 v15, 0x0

    .line 69
    const/16 v16, 0x0

    .line 70
    .line 71
    const/16 v17, 0x0

    .line 72
    .line 73
    const/16 v18, 0x0

    .line 74
    .line 75
    const/16 v19, 0x0

    .line 76
    .line 77
    const/16 v20, 0x0

    .line 78
    .line 79
    const/16 v21, 0x0

    .line 80
    .line 81
    const/16 v22, 0x0

    .line 82
    .line 83
    const/16 v23, 0x0

    .line 84
    .line 85
    const/16 v24, 0x0

    .line 86
    .line 87
    const/16 v25, 0x0

    .line 88
    .line 89
    invoke-static/range {v4 .. v28}, LZx0/k;->b(LZx0/k;Ljava/util/List;LUw0/a;LUw0/a;LUw0/a;Lmy0/a;Ley0/a;Ljy0/a;Loy0/a;Lly0/a;Lhy0/a;Lpy0/a;Ldy0/a;Lfy0/a;Lay0/a;Lay0/b;Liy0/a;Lgy0/a;Lcy0/a;Lky0/a;Lby0/a;LNZ/a;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;ILjava/lang/Object;)LZx0/k;

    .line 90
    .line 91
    .line 92
    move-result-object v4

    .line 93
    goto/16 :goto_0

    .line 94
    .line 95
    :cond_1
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    .line 96
    .line 97
    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 98
    .line 99
    .line 100
    throw v1

    .line 101
    :cond_2
    invoke-virtual {v4}, LZx0/k;->l()Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;

    .line 102
    .line 103
    .line 104
    move-result-object v5

    .line 105
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->P1:LSX0/a;

    .line 106
    .line 107
    sget-object v7, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 108
    .line 109
    sget v8, Lpb/k;->data_retrieval_error:I

    .line 110
    .line 111
    sget v9, Lpb/k;->try_again_text:I

    .line 112
    .line 113
    new-instance v10, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$updateScreenLottieErrorState$1$2;

    .line 114
    .line 115
    invoke-direct {v10, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$updateScreenLottieErrorState$1$2;-><init>(Ljava/lang/Object;)V

    .line 116
    .line 117
    .line 118
    const-wide/16 v11, 0x2710

    .line 119
    .line 120
    invoke-interface/range {v6 .. v12}, LSX0/a;->a(Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;J)Lorg/xbet/uikit/components/lottie/a;

    .line 121
    .line 122
    .line 123
    move-result-object v6

    .line 124
    invoke-virtual {v5, v1, v6}, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->a(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;Lorg/xbet/uikit/components/lottie/a;)Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;

    .line 125
    .line 126
    .line 127
    move-result-object v26

    .line 128
    const v27, 0x1fffff

    .line 129
    .line 130
    .line 131
    const/16 v28, 0x0

    .line 132
    .line 133
    const/4 v5, 0x0

    .line 134
    const/4 v6, 0x0

    .line 135
    const/4 v7, 0x0

    .line 136
    const/4 v8, 0x0

    .line 137
    const/4 v9, 0x0

    .line 138
    const/4 v10, 0x0

    .line 139
    const/4 v11, 0x0

    .line 140
    const/4 v12, 0x0

    .line 141
    const/4 v13, 0x0

    .line 142
    const/4 v14, 0x0

    .line 143
    const/4 v15, 0x0

    .line 144
    const/16 v16, 0x0

    .line 145
    .line 146
    const/16 v17, 0x0

    .line 147
    .line 148
    const/16 v18, 0x0

    .line 149
    .line 150
    const/16 v19, 0x0

    .line 151
    .line 152
    const/16 v20, 0x0

    .line 153
    .line 154
    const/16 v21, 0x0

    .line 155
    .line 156
    const/16 v22, 0x0

    .line 157
    .line 158
    const/16 v23, 0x0

    .line 159
    .line 160
    const/16 v24, 0x0

    .line 161
    .line 162
    const/16 v25, 0x0

    .line 163
    .line 164
    invoke-static/range {v4 .. v28}, LZx0/k;->b(LZx0/k;Ljava/util/List;LUw0/a;LUw0/a;LUw0/a;Lmy0/a;Ley0/a;Ljy0/a;Loy0/a;Lly0/a;Lhy0/a;Lpy0/a;Ldy0/a;Lfy0/a;Lay0/a;Lay0/b;Liy0/a;Lgy0/a;Lcy0/a;Lky0/a;Lby0/a;LNZ/a;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;ILjava/lang/Object;)LZx0/k;

    .line 165
    .line 166
    .line 167
    move-result-object v4

    .line 168
    goto :goto_0

    .line 169
    :cond_3
    invoke-virtual {v4}, LZx0/k;->l()Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;

    .line 170
    .line 171
    .line 172
    move-result-object v5

    .line 173
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->P1:LSX0/a;

    .line 174
    .line 175
    sget-object v7, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 176
    .line 177
    sget v8, Lpb/k;->currently_no_events:I

    .line 178
    .line 179
    sget v9, Lpb/k;->refresh_data:I

    .line 180
    .line 181
    new-instance v10, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$updateScreenLottieErrorState$1$1;

    .line 182
    .line 183
    invoke-direct {v10, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$updateScreenLottieErrorState$1$1;-><init>(Ljava/lang/Object;)V

    .line 184
    .line 185
    .line 186
    const-wide/16 v11, 0x2710

    .line 187
    .line 188
    invoke-interface/range {v6 .. v12}, LSX0/a;->a(Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;J)Lorg/xbet/uikit/components/lottie/a;

    .line 189
    .line 190
    .line 191
    move-result-object v6

    .line 192
    invoke-virtual {v5, v1, v6}, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->a(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;Lorg/xbet/uikit/components/lottie/a;)Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;

    .line 193
    .line 194
    .line 195
    move-result-object v26

    .line 196
    const v27, 0x1fffff

    .line 197
    .line 198
    .line 199
    const/16 v28, 0x0

    .line 200
    .line 201
    const/4 v5, 0x0

    .line 202
    const/4 v6, 0x0

    .line 203
    const/4 v7, 0x0

    .line 204
    const/4 v8, 0x0

    .line 205
    const/4 v9, 0x0

    .line 206
    const/4 v10, 0x0

    .line 207
    const/4 v11, 0x0

    .line 208
    const/4 v12, 0x0

    .line 209
    const/4 v13, 0x0

    .line 210
    const/4 v14, 0x0

    .line 211
    const/4 v15, 0x0

    .line 212
    const/16 v16, 0x0

    .line 213
    .line 214
    const/16 v17, 0x0

    .line 215
    .line 216
    const/16 v18, 0x0

    .line 217
    .line 218
    const/16 v19, 0x0

    .line 219
    .line 220
    const/16 v20, 0x0

    .line 221
    .line 222
    const/16 v21, 0x0

    .line 223
    .line 224
    const/16 v22, 0x0

    .line 225
    .line 226
    const/16 v23, 0x0

    .line 227
    .line 228
    const/16 v24, 0x0

    .line 229
    .line 230
    const/16 v25, 0x0

    .line 231
    .line 232
    invoke-static/range {v4 .. v28}, LZx0/k;->b(LZx0/k;Ljava/util/List;LUw0/a;LUw0/a;LUw0/a;Lmy0/a;Ley0/a;Ljy0/a;Loy0/a;Lly0/a;Lhy0/a;Lpy0/a;Ldy0/a;Lfy0/a;Lay0/a;Lay0/b;Liy0/a;Lgy0/a;Lcy0/a;Lky0/a;Lby0/a;LNZ/a;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;ILjava/lang/Object;)LZx0/k;

    .line 233
    .line 234
    .line 235
    move-result-object v4

    .line 236
    :goto_0
    invoke-interface {v2, v3, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 237
    .line 238
    .line 239
    move-result v3

    .line 240
    if-eqz v3, :cond_0

    .line 241
    .line 242
    return-void
.end method

.method public final w5(LZx0/a;)V
    .locals 29

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    iget-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 6
    .line 7
    :cond_0
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    move-object v4, v3

    .line 12
    check-cast v4, LZx0/k;

    .line 13
    .line 14
    sget-object v5, LZx0/a$j;->a:LZx0/a$j;

    .line 15
    .line 16
    invoke-static {v1, v5}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 17
    .line 18
    .line 19
    move-result v5

    .line 20
    if-eqz v5, :cond_1

    .line 21
    .line 22
    sget-object v6, Ldx0/a;->a:Ldx0/a;

    .line 23
    .line 24
    const v27, 0x3ffffd

    .line 25
    .line 26
    .line 27
    const/16 v28, 0x0

    .line 28
    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v7, 0x0

    .line 31
    const/4 v8, 0x0

    .line 32
    const/4 v9, 0x0

    .line 33
    const/4 v10, 0x0

    .line 34
    const/4 v11, 0x0

    .line 35
    const/4 v12, 0x0

    .line 36
    const/4 v13, 0x0

    .line 37
    const/4 v14, 0x0

    .line 38
    const/4 v15, 0x0

    .line 39
    const/16 v16, 0x0

    .line 40
    .line 41
    const/16 v17, 0x0

    .line 42
    .line 43
    const/16 v18, 0x0

    .line 44
    .line 45
    const/16 v19, 0x0

    .line 46
    .line 47
    const/16 v20, 0x0

    .line 48
    .line 49
    const/16 v21, 0x0

    .line 50
    .line 51
    const/16 v22, 0x0

    .line 52
    .line 53
    const/16 v23, 0x0

    .line 54
    .line 55
    const/16 v24, 0x0

    .line 56
    .line 57
    const/16 v25, 0x0

    .line 58
    .line 59
    const/16 v26, 0x0

    .line 60
    .line 61
    invoke-static/range {v4 .. v28}, LZx0/k;->b(LZx0/k;Ljava/util/List;LUw0/a;LUw0/a;LUw0/a;Lmy0/a;Ley0/a;Ljy0/a;Loy0/a;Lly0/a;Lhy0/a;Lpy0/a;Ldy0/a;Lfy0/a;Lay0/a;Lay0/b;Liy0/a;Lgy0/a;Lcy0/a;Lky0/a;Lby0/a;LNZ/a;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;ILjava/lang/Object;)LZx0/k;

    .line 62
    .line 63
    .line 64
    move-result-object v4

    .line 65
    goto/16 :goto_1

    .line 66
    .line 67
    :cond_1
    sget-object v5, LZx0/a$o;->a:LZx0/a$o;

    .line 68
    .line 69
    invoke-static {v1, v5}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 70
    .line 71
    .line 72
    move-result v5

    .line 73
    if-eqz v5, :cond_2

    .line 74
    .line 75
    sget-object v7, Lyx0/a;->a:Lyx0/a;

    .line 76
    .line 77
    const v27, 0x3ffffb

    .line 78
    .line 79
    .line 80
    const/16 v28, 0x0

    .line 81
    .line 82
    const/4 v5, 0x0

    .line 83
    const/4 v6, 0x0

    .line 84
    const/4 v8, 0x0

    .line 85
    const/4 v9, 0x0

    .line 86
    const/4 v10, 0x0

    .line 87
    const/4 v11, 0x0

    .line 88
    const/4 v12, 0x0

    .line 89
    const/4 v13, 0x0

    .line 90
    const/4 v14, 0x0

    .line 91
    const/4 v15, 0x0

    .line 92
    const/16 v16, 0x0

    .line 93
    .line 94
    const/16 v17, 0x0

    .line 95
    .line 96
    const/16 v18, 0x0

    .line 97
    .line 98
    const/16 v19, 0x0

    .line 99
    .line 100
    const/16 v20, 0x0

    .line 101
    .line 102
    const/16 v21, 0x0

    .line 103
    .line 104
    const/16 v22, 0x0

    .line 105
    .line 106
    const/16 v23, 0x0

    .line 107
    .line 108
    const/16 v24, 0x0

    .line 109
    .line 110
    const/16 v25, 0x0

    .line 111
    .line 112
    const/16 v26, 0x0

    .line 113
    .line 114
    invoke-static/range {v4 .. v28}, LZx0/k;->b(LZx0/k;Ljava/util/List;LUw0/a;LUw0/a;LUw0/a;Lmy0/a;Ley0/a;Ljy0/a;Loy0/a;Lly0/a;Lhy0/a;Lpy0/a;Ldy0/a;Lfy0/a;Lay0/a;Lay0/b;Liy0/a;Lgy0/a;Lcy0/a;Lky0/a;Lby0/a;LNZ/a;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;ILjava/lang/Object;)LZx0/k;

    .line 115
    .line 116
    .line 117
    move-result-object v4

    .line 118
    goto/16 :goto_1

    .line 119
    .line 120
    :cond_2
    sget-object v5, LZx0/a$r;->a:LZx0/a$r;

    .line 121
    .line 122
    invoke-static {v1, v5}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 123
    .line 124
    .line 125
    move-result v5

    .line 126
    if-eqz v5, :cond_3

    .line 127
    .line 128
    sget-object v8, LNx0/a;->a:LNx0/a;

    .line 129
    .line 130
    const v27, 0x3ffff7

    .line 131
    .line 132
    .line 133
    const/16 v28, 0x0

    .line 134
    .line 135
    const/4 v5, 0x0

    .line 136
    const/4 v6, 0x0

    .line 137
    const/4 v7, 0x0

    .line 138
    const/4 v9, 0x0

    .line 139
    const/4 v10, 0x0

    .line 140
    const/4 v11, 0x0

    .line 141
    const/4 v12, 0x0

    .line 142
    const/4 v13, 0x0

    .line 143
    const/4 v14, 0x0

    .line 144
    const/4 v15, 0x0

    .line 145
    const/16 v16, 0x0

    .line 146
    .line 147
    const/16 v17, 0x0

    .line 148
    .line 149
    const/16 v18, 0x0

    .line 150
    .line 151
    const/16 v19, 0x0

    .line 152
    .line 153
    const/16 v20, 0x0

    .line 154
    .line 155
    const/16 v21, 0x0

    .line 156
    .line 157
    const/16 v22, 0x0

    .line 158
    .line 159
    const/16 v23, 0x0

    .line 160
    .line 161
    const/16 v24, 0x0

    .line 162
    .line 163
    const/16 v25, 0x0

    .line 164
    .line 165
    const/16 v26, 0x0

    .line 166
    .line 167
    invoke-static/range {v4 .. v28}, LZx0/k;->b(LZx0/k;Ljava/util/List;LUw0/a;LUw0/a;LUw0/a;Lmy0/a;Ley0/a;Ljy0/a;Loy0/a;Lly0/a;Lhy0/a;Lpy0/a;Ldy0/a;Lfy0/a;Lay0/a;Lay0/b;Liy0/a;Lgy0/a;Lcy0/a;Lky0/a;Lby0/a;LNZ/a;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;ILjava/lang/Object;)LZx0/k;

    .line 168
    .line 169
    .line 170
    move-result-object v4

    .line 171
    goto/16 :goto_1

    .line 172
    .line 173
    :cond_3
    instance-of v5, v1, LZx0/a$p;

    .line 174
    .line 175
    if-eqz v5, :cond_4

    .line 176
    .line 177
    iget-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 178
    .line 179
    move-object v6, v1

    .line 180
    check-cast v6, LZx0/a$p;

    .line 181
    .line 182
    invoke-static {v4, v5, v6}, LQx0/b;->E(LZx0/k;LHX0/e;LZx0/a$p;)LZx0/k;

    .line 183
    .line 184
    .line 185
    move-result-object v4

    .line 186
    goto/16 :goto_1

    .line 187
    .line 188
    :cond_4
    instance-of v5, v1, LZx0/a$l;

    .line 189
    .line 190
    if-eqz v5, :cond_5

    .line 191
    .line 192
    iget-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 193
    .line 194
    move-object v6, v1

    .line 195
    check-cast v6, LZx0/a$l;

    .line 196
    .line 197
    invoke-static {v4, v5, v6}, LQx0/b;->B(LZx0/k;LHX0/e;LZx0/a$l;)LZx0/k;

    .line 198
    .line 199
    .line 200
    move-result-object v4

    .line 201
    goto/16 :goto_1

    .line 202
    .line 203
    :cond_5
    instance-of v5, v1, LZx0/d;

    .line 204
    .line 205
    if-eqz v5, :cond_6

    .line 206
    .line 207
    move-object v5, v1

    .line 208
    check-cast v5, LZx0/d;

    .line 209
    .line 210
    invoke-static {v4, v5}, LQx0/b;->v(LZx0/k;LZx0/d;)LZx0/k;

    .line 211
    .line 212
    .line 213
    move-result-object v4

    .line 214
    goto/16 :goto_1

    .line 215
    .line 216
    :cond_6
    instance-of v5, v1, LZx0/c;

    .line 217
    .line 218
    if-eqz v5, :cond_7

    .line 219
    .line 220
    move-object v5, v1

    .line 221
    check-cast v5, LZx0/c;

    .line 222
    .line 223
    invoke-static {v4, v5}, LQx0/b;->u(LZx0/k;LZx0/c;)LZx0/k;

    .line 224
    .line 225
    .line 226
    move-result-object v4

    .line 227
    goto/16 :goto_1

    .line 228
    .line 229
    :cond_7
    instance-of v5, v1, LZx0/b;

    .line 230
    .line 231
    if-eqz v5, :cond_8

    .line 232
    .line 233
    move-object v5, v1

    .line 234
    check-cast v5, LZx0/b;

    .line 235
    .line 236
    invoke-static {v4, v5}, LQx0/b;->t(LZx0/k;LZx0/b;)LZx0/k;

    .line 237
    .line 238
    .line 239
    move-result-object v4

    .line 240
    goto/16 :goto_1

    .line 241
    .line 242
    :cond_8
    instance-of v5, v1, LZx0/a$n;

    .line 243
    .line 244
    if-eqz v5, :cond_9

    .line 245
    .line 246
    iget-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 247
    .line 248
    move-object v6, v1

    .line 249
    check-cast v6, LZx0/a$n;

    .line 250
    .line 251
    iget-object v7, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 252
    .line 253
    invoke-virtual {v7}, LZx0/h;->d()I

    .line 254
    .line 255
    .line 256
    move-result v7

    .line 257
    invoke-static {v4, v5, v6, v7}, LQx0/b;->D(LZx0/k;LHX0/e;LZx0/a$n;I)LZx0/k;

    .line 258
    .line 259
    .line 260
    move-result-object v4

    .line 261
    goto/16 :goto_1

    .line 262
    .line 263
    :cond_9
    instance-of v5, v1, LZx0/a$q;

    .line 264
    .line 265
    if-eqz v5, :cond_a

    .line 266
    .line 267
    iget-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 268
    .line 269
    move-object v6, v1

    .line 270
    check-cast v6, LZx0/a$q;

    .line 271
    .line 272
    invoke-static {v4, v5, v6}, LQx0/b;->G(LZx0/k;LHX0/e;LZx0/a$q;)LZx0/k;

    .line 273
    .line 274
    .line 275
    move-result-object v4

    .line 276
    goto/16 :goto_1

    .line 277
    .line 278
    :cond_a
    instance-of v5, v1, LZx0/a$f;

    .line 279
    .line 280
    if-eqz v5, :cond_b

    .line 281
    .line 282
    iget-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 283
    .line 284
    move-object v6, v1

    .line 285
    check-cast v6, LZx0/a$f;

    .line 286
    .line 287
    invoke-static {v4, v5, v6}, LQx0/b;->w(LZx0/k;LHX0/e;LZx0/a$f;)LZx0/k;

    .line 288
    .line 289
    .line 290
    move-result-object v4

    .line 291
    goto/16 :goto_1

    .line 292
    .line 293
    :cond_b
    instance-of v5, v1, LZx0/a$i;

    .line 294
    .line 295
    if-eqz v5, :cond_c

    .line 296
    .line 297
    iget-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 298
    .line 299
    move-object v6, v1

    .line 300
    check-cast v6, LZx0/a$i;

    .line 301
    .line 302
    invoke-static {v4, v5, v6}, LQx0/b;->z(LZx0/k;LHX0/e;LZx0/a$i;)LZx0/k;

    .line 303
    .line 304
    .line 305
    move-result-object v4

    .line 306
    goto/16 :goto_1

    .line 307
    .line 308
    :cond_c
    instance-of v5, v1, LZx0/a$s;

    .line 309
    .line 310
    if-eqz v5, :cond_d

    .line 311
    .line 312
    iget-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 313
    .line 314
    move-object v6, v1

    .line 315
    check-cast v6, LZx0/a$s;

    .line 316
    .line 317
    invoke-static {v4, v5, v6}, LQx0/b;->H(LZx0/k;LHX0/e;LZx0/a$s;)LZx0/k;

    .line 318
    .line 319
    .line 320
    move-result-object v4

    .line 321
    goto/16 :goto_1

    .line 322
    .line 323
    :cond_d
    instance-of v5, v1, LZx0/a$a;

    .line 324
    .line 325
    if-eqz v5, :cond_e

    .line 326
    .line 327
    move-object v5, v1

    .line 328
    check-cast v5, LZx0/a$a;

    .line 329
    .line 330
    invoke-static {v4, v5}, LQx0/b;->p(LZx0/k;LZx0/a$a;)LZx0/k;

    .line 331
    .line 332
    .line 333
    move-result-object v4

    .line 334
    goto/16 :goto_1

    .line 335
    .line 336
    :cond_e
    instance-of v5, v1, LZx0/a$k;

    .line 337
    .line 338
    if-eqz v5, :cond_f

    .line 339
    .line 340
    move-object v5, v1

    .line 341
    check-cast v5, LZx0/a$k;

    .line 342
    .line 343
    invoke-static {v4, v5}, LQx0/b;->A(LZx0/k;LZx0/a$k;)LZx0/k;

    .line 344
    .line 345
    .line 346
    move-result-object v4

    .line 347
    goto/16 :goto_1

    .line 348
    .line 349
    :cond_f
    instance-of v5, v1, LZx0/a$d;

    .line 350
    .line 351
    if-eqz v5, :cond_10

    .line 352
    .line 353
    iget-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 354
    .line 355
    invoke-virtual {v5}, LZx0/h;->j()J

    .line 356
    .line 357
    .line 358
    move-result-wide v5

    .line 359
    iget-object v7, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 360
    .line 361
    move-object v8, v1

    .line 362
    check-cast v8, LZx0/a$d;

    .line 363
    .line 364
    invoke-static {v4, v5, v6, v7, v8}, LQx0/b;->r(LZx0/k;JLHX0/e;LZx0/a$d;)LZx0/k;

    .line 365
    .line 366
    .line 367
    move-result-object v4

    .line 368
    goto/16 :goto_1

    .line 369
    .line 370
    :cond_10
    instance-of v5, v1, LZx0/a$e;

    .line 371
    .line 372
    if-eqz v5, :cond_11

    .line 373
    .line 374
    move-object v5, v1

    .line 375
    check-cast v5, LZx0/a$e;

    .line 376
    .line 377
    invoke-static {v4, v5}, LQx0/b;->s(LZx0/k;LZx0/a$e;)LZx0/k;

    .line 378
    .line 379
    .line 380
    move-result-object v4

    .line 381
    goto :goto_1

    .line 382
    :cond_11
    instance-of v5, v1, LZx0/a$g;

    .line 383
    .line 384
    if-eqz v5, :cond_12

    .line 385
    .line 386
    move-object v5, v1

    .line 387
    check-cast v5, LZx0/a$g;

    .line 388
    .line 389
    invoke-static {v4, v5}, LQx0/b;->x(LZx0/k;LZx0/a$g;)LZx0/k;

    .line 390
    .line 391
    .line 392
    move-result-object v4

    .line 393
    goto :goto_1

    .line 394
    :cond_12
    instance-of v5, v1, LZx0/a$h;

    .line 395
    .line 396
    if-eqz v5, :cond_13

    .line 397
    .line 398
    iget-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 399
    .line 400
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->M5:LuX0/a;

    .line 401
    .line 402
    invoke-interface {v6}, LuX0/a;->invoke()Z

    .line 403
    .line 404
    .line 405
    move-result v6

    .line 406
    move-object v7, v1

    .line 407
    check-cast v7, LZx0/a$h;

    .line 408
    .line 409
    invoke-static {v4, v5, v6, v7}, LQx0/b;->y(LZx0/k;LHX0/e;ZLZx0/a$h;)LZx0/k;

    .line 410
    .line 411
    .line 412
    move-result-object v4

    .line 413
    goto :goto_1

    .line 414
    :cond_13
    instance-of v5, v1, LZx0/a$c;

    .line 415
    .line 416
    if-eqz v5, :cond_14

    .line 417
    .line 418
    iget-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 419
    .line 420
    invoke-virtual {v5}, LZx0/h;->j()J

    .line 421
    .line 422
    .line 423
    move-result-wide v5

    .line 424
    move-object v7, v1

    .line 425
    check-cast v7, LZx0/a$c;

    .line 426
    .line 427
    invoke-static {v4, v5, v6, v7}, LQx0/b;->q(LZx0/k;JLZx0/a$c;)LZx0/k;

    .line 428
    .line 429
    .line 430
    move-result-object v4

    .line 431
    goto :goto_1

    .line 432
    :cond_14
    instance-of v5, v1, LZx0/a$m;

    .line 433
    .line 434
    if-eqz v5, :cond_15

    .line 435
    .line 436
    move-object v5, v1

    .line 437
    check-cast v5, LZx0/a$m;

    .line 438
    .line 439
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I1:LHX0/e;

    .line 440
    .line 441
    invoke-static {v4, v5, v6}, LQx0/b;->C(LZx0/k;LZx0/a$m;LHX0/e;)LZx0/k;

    .line 442
    .line 443
    .line 444
    move-result-object v4

    .line 445
    goto :goto_1

    .line 446
    :cond_15
    instance-of v5, v1, LZx0/a$b;

    .line 447
    .line 448
    if-eqz v5, :cond_19

    .line 449
    .line 450
    iget-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r6:LZx0/h;

    .line 451
    .line 452
    invoke-virtual {v5}, LZx0/h;->j()J

    .line 453
    .line 454
    .line 455
    move-result-wide v5

    .line 456
    iget-object v7, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 457
    .line 458
    invoke-interface {v7}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 459
    .line 460
    .line 461
    move-result-object v7

    .line 462
    check-cast v7, LZx0/k;

    .line 463
    .line 464
    invoke-virtual {v7}, LZx0/k;->f()Lby0/a;

    .line 465
    .line 466
    .line 467
    move-result-object v7

    .line 468
    invoke-virtual {v7}, Lby0/a;->c()Lwp0/f;

    .line 469
    .line 470
    .line 471
    move-result-object v7

    .line 472
    if-eqz v7, :cond_16

    .line 473
    .line 474
    invoke-virtual {v7}, Lwp0/f;->c()I

    .line 475
    .line 476
    .line 477
    move-result v7

    .line 478
    goto :goto_0

    .line 479
    :cond_16
    const/4 v7, 0x0

    .line 480
    :goto_0
    move-object v8, v1

    .line 481
    check-cast v8, LZx0/a$b;

    .line 482
    .line 483
    invoke-static {v4, v5, v6, v7, v8}, LQx0/b;->F(LZx0/k;JILZx0/a$b;)LZx0/k;

    .line 484
    .line 485
    .line 486
    move-result-object v4

    .line 487
    :goto_1
    invoke-interface {v2, v3, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 488
    .line 489
    .line 490
    move-result v3

    .line 491
    if-eqz v3, :cond_0

    .line 492
    .line 493
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 494
    .line 495
    .line 496
    move-result-object v1

    .line 497
    check-cast v1, LZx0/k;

    .line 498
    .line 499
    invoke-static {v1}, LQx0/b;->h(LZx0/k;)Z

    .line 500
    .line 501
    .line 502
    move-result v1

    .line 503
    if-eqz v1, :cond_17

    .line 504
    .line 505
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 506
    .line 507
    .line 508
    move-result-object v1

    .line 509
    check-cast v1, LZx0/k;

    .line 510
    .line 511
    invoke-static {v1}, LQx0/b;->f(LZx0/k;)Z

    .line 512
    .line 513
    .line 514
    move-result v1

    .line 515
    if-eqz v1, :cond_17

    .line 516
    .line 517
    sget-object v1, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;->CONTENT_ERROR:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    .line 518
    .line 519
    goto :goto_2

    .line 520
    :cond_17
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 521
    .line 522
    .line 523
    move-result-object v1

    .line 524
    check-cast v1, LZx0/k;

    .line 525
    .line 526
    invoke-static {v1}, LQx0/b;->h(LZx0/k;)Z

    .line 527
    .line 528
    .line 529
    move-result v1

    .line 530
    if-eqz v1, :cond_18

    .line 531
    .line 532
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 533
    .line 534
    .line 535
    move-result-object v1

    .line 536
    check-cast v1, LZx0/k;

    .line 537
    .line 538
    invoke-static {v1}, LQx0/b;->e(LZx0/k;)Z

    .line 539
    .line 540
    .line 541
    move-result v1

    .line 542
    if-eqz v1, :cond_18

    .line 543
    .line 544
    sget-object v1, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;->CONTENT_EMPTY_ERROR:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    .line 545
    .line 546
    goto :goto_2

    .line 547
    :cond_18
    sget-object v1, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;->NO_ERROR:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    .line 548
    .line 549
    :goto_2
    invoke-virtual {v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->v5(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;)V

    .line 550
    .line 551
    .line 552
    return-void

    .line 553
    :cond_19
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    .line 554
    .line 555
    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 556
    .line 557
    .line 558
    throw v1
.end method

.method public x(JJLjava/lang/String;)V
    .locals 0
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct/range {p0 .. p5}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r5(JJLjava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public y(LAp/f;)V
    .locals 1
    .param p1    # LAp/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S1:LVo/e;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lorg/xbet/betting/event_card/presentation/delegates/a;->y(LAp/f;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public y2()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lbl0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S2:Lbl0/g;

    invoke-interface {v0}, Lbl0/f;->y2()Lkotlinx/coroutines/flow/e;

    move-result-object v0

    return-object v0
.end method
