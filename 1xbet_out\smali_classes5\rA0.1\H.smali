.class public final synthetic LrA0/H;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function2;

.field public final synthetic b:I

.field public final synthetic c:LB4/a;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function2;ILB4/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LrA0/H;->a:Lkotlin/jvm/functions/Function2;

    iput p2, p0, LrA0/H;->b:I

    iput-object p3, p0, LrA0/H;->c:LB4/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, LrA0/H;->a:L<PERSON>lin/jvm/functions/Function2;

    iget v1, p0, LrA0/H;->b:I

    iget-object v2, p0, LrA0/H;->c:LB4/a;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, v2, p1}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/gameinfo/withtimer/viewholders/StadiumInfoAdapterDelegateKt;->e(Lkotlin/jvm/functions/Function2;ILB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
