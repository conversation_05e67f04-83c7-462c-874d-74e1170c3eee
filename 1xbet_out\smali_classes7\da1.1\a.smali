.class public final Lda1/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0006\u001a\u00020\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "Ly81/a;",
        "",
        "service",
        "",
        "hasAggregatorBrands",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "a",
        "(Ly81/a;Ljava/lang/String;Z)Lorg/xplatform/aggregator/api/model/Game;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ly81/a;Ljava/lang/String;Z)Lorg/xplatform/aggregator/api/model/Game;
    .locals 25
    .param p0    # Ly81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p0}, Ly81/a;->e()Ljava/lang/Long;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const-wide/16 v1, 0x0

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 10
    .line 11
    .line 12
    move-result-wide v3

    .line 13
    move-wide v6, v3

    .line 14
    goto :goto_0

    .line 15
    :cond_0
    move-wide v6, v1

    .line 16
    :goto_0
    invoke-virtual/range {p0 .. p0}, Ly81/a;->i()Ljava/lang/Long;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    if-eqz v0, :cond_1

    .line 21
    .line 22
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 23
    .line 24
    .line 25
    move-result-wide v3

    .line 26
    move-wide v8, v3

    .line 27
    goto :goto_1

    .line 28
    :cond_1
    move-wide v8, v1

    .line 29
    :goto_1
    const/4 v0, -0x1

    .line 30
    if-eqz p2, :cond_2

    .line 31
    .line 32
    invoke-virtual/range {p0 .. p0}, Ly81/a;->b()Ljava/lang/Integer;

    .line 33
    .line 34
    .line 35
    move-result-object v3

    .line 36
    if-eqz v3, :cond_3

    .line 37
    .line 38
    :goto_2
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 39
    .line 40
    .line 41
    move-result v0

    .line 42
    goto :goto_3

    .line 43
    :cond_2
    invoke-virtual/range {p0 .. p0}, Ly81/a;->k()Ljava/lang/Integer;

    .line 44
    .line 45
    .line 46
    move-result-object v3

    .line 47
    if-eqz v3, :cond_3

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_3
    :goto_3
    int-to-long v10, v0

    .line 51
    invoke-virtual/range {p0 .. p0}, Ly81/a;->b()Ljava/lang/Integer;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    if-eqz v0, :cond_4

    .line 56
    .line 57
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 58
    .line 59
    .line 60
    move-result v0

    .line 61
    int-to-long v1, v0

    .line 62
    :cond_4
    move-wide v12, v1

    .line 63
    const-string v0, ""

    .line 64
    .line 65
    if-eqz p2, :cond_6

    .line 66
    .line 67
    invoke-virtual/range {p0 .. p0}, Ly81/a;->c()Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    if-nez v1, :cond_5

    .line 72
    .line 73
    :goto_4
    move-object v14, v0

    .line 74
    goto :goto_5

    .line 75
    :cond_5
    move-object v14, v1

    .line 76
    goto :goto_5

    .line 77
    :cond_6
    invoke-virtual/range {p0 .. p0}, Ly81/a;->j()Ljava/lang/String;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    if-nez v1, :cond_5

    .line 82
    .line 83
    goto :goto_4

    .line 84
    :goto_5
    invoke-virtual/range {p0 .. p0}, Ly81/a;->g()Ljava/lang/String;

    .line 85
    .line 86
    .line 87
    move-result-object v1

    .line 88
    if-nez v1, :cond_7

    .line 89
    .line 90
    move-object v15, v0

    .line 91
    goto :goto_6

    .line 92
    :cond_7
    move-object v15, v1

    .line 93
    :goto_6
    invoke-virtual/range {p0 .. p0}, Ly81/a;->f()Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object v1

    .line 97
    if-nez v1, :cond_8

    .line 98
    .line 99
    goto :goto_7

    .line 100
    :cond_8
    move-object v0, v1

    .line 101
    :goto_7
    new-instance v1, Ljava/lang/StringBuilder;

    .line 102
    .line 103
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 104
    .line 105
    .line 106
    move-object/from16 v2, p1

    .line 107
    .line 108
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 109
    .line 110
    .line 111
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 112
    .line 113
    .line 114
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 115
    .line 116
    .line 117
    move-result-object v16

    .line 118
    invoke-virtual/range {p0 .. p0}, Ly81/a;->l()Ljava/util/List;

    .line 119
    .line 120
    .line 121
    move-result-object v0

    .line 122
    const/4 v1, 0x0

    .line 123
    const/4 v2, 0x1

    .line 124
    if-eqz v0, :cond_9

    .line 125
    .line 126
    const-string v3, "popular"

    .line 127
    .line 128
    invoke-interface {v0, v3}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 129
    .line 130
    .line 131
    move-result v0

    .line 132
    if-ne v0, v2, :cond_9

    .line 133
    .line 134
    const/16 v17, 0x1

    .line 135
    .line 136
    goto :goto_8

    .line 137
    :cond_9
    const/16 v17, 0x0

    .line 138
    .line 139
    :goto_8
    invoke-virtual/range {p0 .. p0}, Ly81/a;->l()Ljava/util/List;

    .line 140
    .line 141
    .line 142
    move-result-object v0

    .line 143
    if-eqz v0, :cond_a

    .line 144
    .line 145
    const-string v3, "new"

    .line 146
    .line 147
    invoke-interface {v0, v3}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 148
    .line 149
    .line 150
    move-result v0

    .line 151
    if-ne v0, v2, :cond_a

    .line 152
    .line 153
    const/16 v18, 0x1

    .line 154
    .line 155
    goto :goto_9

    .line 156
    :cond_a
    const/16 v18, 0x0

    .line 157
    .line 158
    :goto_9
    invoke-virtual/range {p0 .. p0}, Ly81/a;->l()Ljava/util/List;

    .line 159
    .line 160
    .line 161
    move-result-object v0

    .line 162
    if-eqz v0, :cond_b

    .line 163
    .line 164
    const-string v3, "promo"

    .line 165
    .line 166
    invoke-interface {v0, v3}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 167
    .line 168
    .line 169
    move-result v0

    .line 170
    if-ne v0, v2, :cond_b

    .line 171
    .line 172
    const/16 v19, 0x1

    .line 173
    .line 174
    goto :goto_a

    .line 175
    :cond_b
    const/16 v19, 0x0

    .line 176
    .line 177
    :goto_a
    invoke-virtual/range {p0 .. p0}, Ly81/a;->l()Ljava/util/List;

    .line 178
    .line 179
    .line 180
    move-result-object v0

    .line 181
    if-eqz v0, :cond_c

    .line 182
    .line 183
    const-string v3, "hot"

    .line 184
    .line 185
    invoke-interface {v0, v3}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 186
    .line 187
    .line 188
    move-result v0

    .line 189
    if-ne v0, v2, :cond_c

    .line 190
    .line 191
    const/16 v23, 0x1

    .line 192
    .line 193
    goto :goto_b

    .line 194
    :cond_c
    const/16 v23, 0x0

    .line 195
    .line 196
    :goto_b
    invoke-virtual/range {p0 .. p0}, Ly81/a;->h()Ljava/lang/Boolean;

    .line 197
    .line 198
    .line 199
    move-result-object v0

    .line 200
    sget-object v1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 201
    .line 202
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 203
    .line 204
    .line 205
    move-result v20

    .line 206
    invoke-virtual/range {p0 .. p0}, Ly81/a;->a()Ljava/lang/Boolean;

    .line 207
    .line 208
    .line 209
    move-result-object v0

    .line 210
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 211
    .line 212
    .line 213
    move-result v21

    .line 214
    invoke-virtual/range {p0 .. p0}, Ly81/a;->m()Ljava/lang/Boolean;

    .line 215
    .line 216
    .line 217
    move-result-object v0

    .line 218
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 219
    .line 220
    .line 221
    move-result v22

    .line 222
    invoke-virtual/range {p0 .. p0}, Ly81/a;->d()Ljava/util/List;

    .line 223
    .line 224
    .line 225
    move-result-object v0

    .line 226
    if-nez v0, :cond_d

    .line 227
    .line 228
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 229
    .line 230
    .line 231
    move-result-object v0

    .line 232
    :cond_d
    move-object/from16 v24, v0

    .line 233
    .line 234
    new-instance v5, Lorg/xplatform/aggregator/api/model/Game;

    .line 235
    .line 236
    invoke-direct/range {v5 .. v24}, Lorg/xplatform/aggregator/api/model/Game;-><init>(JJJJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZZZZZZLjava/util/List;)V

    .line 237
    .line 238
    .line 239
    return-object v5
.end method
