.class public final synthetic LRS0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;

.field public final synthetic b:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LRS0/a;->a:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;

    iput-object p2, p0, LRS0/a;->b:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, LRS0/a;->a:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;

    iget-object v1, p0, LRS0/a;->b:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;

    invoke-static {v0, v1}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;->k(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;)V

    return-void
.end method
