.class public final LRQ0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LRQ0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LRQ0/a;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IILkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    if-nez p8, :cond_1

    .line 2
    .line 3
    and-int/lit8 p7, p7, 0x1

    .line 4
    .line 5
    if-eqz p7, :cond_0

    .line 6
    .line 7
    const-string p1, "application/vnd.xenvelop+json"

    .line 8
    .line 9
    :cond_0
    move p7, p5

    .line 10
    move-object p8, p6

    .line 11
    move-object p5, p3

    .line 12
    move p6, p4

    .line 13
    move-object p3, p1

    .line 14
    move-object p4, p2

    .line 15
    move-object p2, p0

    .line 16
    invoke-interface/range {p2 .. p8}, LRQ0/a;->a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object p0

    .line 20
    return-object p0

    .line 21
    :cond_1
    new-instance p0, Ljava/lang/UnsupportedOperationException;

    .line 22
    .line 23
    const-string p1, "Super calls with default arguments not supported in this target, function: getTextBroadcast"

    .line 24
    .line 25
    invoke-direct {p0, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    .line 26
    .line 27
    .line 28
    throw p0
.end method
