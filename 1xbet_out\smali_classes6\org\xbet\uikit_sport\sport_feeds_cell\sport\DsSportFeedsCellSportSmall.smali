.class public final Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportSmall;
.super Lorg/xbet/uikit_sport/sport_cell/DsSportCell;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportSmall$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0007\u0018\u00002\u00020\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u001d\u0010\u0010\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u001d\u0010\u0014\u001a\u00020\u000c2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u0014\u0010\u0015R\u0018\u0010\u0013\u001a\u0004\u0018\u00010\u00128\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0017\u00a8\u0006\u0018"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportSmall;",
        "Lorg/xbet/uikit_sport/sport_cell/DsSportCell;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "showBadge",
        "",
        "j",
        "(Z)V",
        "showListCheckBox",
        "setSportStyle",
        "(ZZ)V",
        "Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;",
        "componentStyle",
        "setComponentStyle",
        "(Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;Z)V",
        "l",
        "Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public l:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportSmall;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportSmall;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportSmall;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method private final j(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportSmall;->l:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    goto/16 :goto_0

    .line 6
    .line 7
    :cond_0
    sget-object v1, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportSmall$a;->a:[I

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    aget v0, v1, v0

    .line 14
    .line 15
    const/4 v1, 0x1

    .line 16
    if-eq v0, v1, :cond_7

    .line 17
    .line 18
    const/4 v1, 0x2

    .line 19
    if-eq v0, v1, :cond_5

    .line 20
    .line 21
    const/4 v1, 0x3

    .line 22
    if-eq v0, v1, :cond_3

    .line 23
    .line 24
    const/4 v1, 0x4

    .line 25
    if-ne v0, v1, :cond_2

    .line 26
    .line 27
    if-eqz p1, :cond_1

    .line 28
    .line 29
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    if-eqz p1, :cond_8

    .line 34
    .line 35
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->BADGE_WITH_COUNTER_WITH_LIST_CHECKBOX:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 36
    .line 37
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 38
    .line 39
    .line 40
    return-void

    .line 41
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    if-eqz p1, :cond_8

    .line 46
    .line 47
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->COUNTER_WITH_LIST_CHECKBOX:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 48
    .line 49
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 50
    .line 51
    .line 52
    return-void

    .line 53
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 54
    .line 55
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 56
    .line 57
    .line 58
    throw p1

    .line 59
    :cond_3
    if-eqz p1, :cond_4

    .line 60
    .line 61
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    if-eqz p1, :cond_8

    .line 66
    .line 67
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->BADGE_WITH_COUNTER_WITH_ACCORDION:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 68
    .line 69
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 70
    .line 71
    .line 72
    return-void

    .line 73
    :cond_4
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    if-eqz p1, :cond_8

    .line 78
    .line 79
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->COUNTER_WITH_ACCORDION:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 80
    .line 81
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 82
    .line 83
    .line 84
    return-void

    .line 85
    :cond_5
    if-eqz p1, :cond_6

    .line 86
    .line 87
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    if-eqz p1, :cond_8

    .line 92
    .line 93
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->BADGE_WITH_COUNTER:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 94
    .line 95
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 96
    .line 97
    .line 98
    return-void

    .line 99
    :cond_6
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 100
    .line 101
    .line 102
    move-result-object p1

    .line 103
    if-eqz p1, :cond_8

    .line 104
    .line 105
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->COUNTER:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 106
    .line 107
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 108
    .line 109
    .line 110
    return-void

    .line 111
    :cond_7
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 112
    .line 113
    .line 114
    move-result-object p1

    .line 115
    if-eqz p1, :cond_8

    .line 116
    .line 117
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->LIST_CHECKBOX:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 118
    .line 119
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 120
    .line 121
    .line 122
    :cond_8
    :goto_0
    return-void
.end method


# virtual methods
.method public final setComponentStyle(Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;Z)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportSmall;->l:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;

    .line 2
    .line 3
    invoke-direct {p0, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportSmall;->j(Z)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setSportStyle(ZZ)V
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    sget-object p1, Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;->SPORT_COUNTER_WITH_LIST_CHECKBOX:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    sget-object p1, Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;->SPORT_COUNTER:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;

    .line 7
    .line 8
    :goto_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportSmall;->setComponentStyle(Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;Z)V

    .line 9
    .line 10
    .line 11
    return-void
.end method
