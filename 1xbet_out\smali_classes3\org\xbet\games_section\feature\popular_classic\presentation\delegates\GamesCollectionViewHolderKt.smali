.class public final Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCollectionViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a3\u0010\t\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00080\u00070\u00062\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0004H\u0000\u00a2\u0006\u0004\u0008\t\u0010\n\u00a8\u0006\u000b"
    }
    d2 = {
        "Li50/a;",
        "clickListener",
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "",
        "screenName",
        "LA4/c;",
        "",
        "LVX0/i;",
        "h",
        "(Li50/a;LUX0/k;Ljava/lang/String;)LA4/c;",
        "popular_classic_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCollectionViewHolderKt;->n(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Li50/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCollectionViewHolderKt;->m(Li50/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Ld50/b;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCollectionViewHolderKt;->i(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Ld50/b;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCollectionViewHolderKt;->o(LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Li50/a;Ljava/lang/String;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCollectionViewHolderKt;->j(Li50/a;Ljava/lang/String;LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(LB4/a;Li50/a;Ljava/lang/String;Ln41/m;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCollectionViewHolderKt;->k(LB4/a;Li50/a;Ljava/lang/String;Ln41/m;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Li50/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCollectionViewHolderKt;->l(Li50/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final h(Li50/a;LUX0/k;Ljava/lang/String;)LA4/c;
    .locals 2
    .param p0    # Li50/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LUX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li50/a;",
            "LUX0/k;",
            "Ljava/lang/String;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lh50/l;

    .line 2
    .line 3
    invoke-direct {v0}, Lh50/l;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lh50/m;

    .line 7
    .line 8
    invoke-direct {v1, p0, p2, p1}, Lh50/m;-><init>(Li50/a;Ljava/lang/String;LUX0/k;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCollectionViewHolderKt$getGamesCollectionAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCollectionViewHolderKt$getGamesCollectionAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCollectionViewHolderKt$getGamesCollectionAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCollectionViewHolderKt$getGamesCollectionAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance p2, LB4/b;

    .line 19
    .line 20
    invoke-direct {p2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object p2
.end method

.method public static final i(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Ld50/b;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, Ld50/b;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)Ld50/b;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final j(Li50/a;Ljava/lang/String;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 4

    .line 1
    invoke-virtual {p3}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Ld50/b;

    .line 6
    .line 7
    iget-object v0, v0, Ld50/b;->b:Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    .line 8
    .line 9
    new-instance v1, Lh50/n;

    .line 10
    .line 11
    invoke-direct {v1, p3, p0, p1}, Lh50/n;-><init>(LB4/a;Li50/a;Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function2;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p3}, LB4/a;->e()LL2/a;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, Ld50/b;

    .line 22
    .line 23
    iget-object v0, v0, Ld50/b;->b:Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    .line 24
    .line 25
    new-instance v1, Lh50/o;

    .line 26
    .line 27
    invoke-direct {v1, p0, p1, p3}, Lh50/o;-><init>(Li50/a;Ljava/lang/String;LB4/a;)V

    .line 28
    .line 29
    .line 30
    const/4 v2, 0x0

    .line 31
    const/4 v3, 0x1

    .line 32
    invoke-static {v0, v2, v1, v3, v2}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 33
    .line 34
    .line 35
    invoke-virtual {p3}, LB4/a;->e()LL2/a;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    check-cast v0, Ld50/b;

    .line 40
    .line 41
    iget-object v0, v0, Ld50/b;->c:Lorg/xbet/uikit/components/header/DSHeader;

    .line 42
    .line 43
    new-instance v1, Lh50/p;

    .line 44
    .line 45
    invoke-direct {v1, p0, p1, p3}, Lh50/p;-><init>(Li50/a;Ljava/lang/String;LB4/a;)V

    .line 46
    .line 47
    .line 48
    invoke-static {v2, v1, v3, v2}, LN11/f;->k(Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 49
    .line 50
    .line 51
    move-result-object p0

    .line 52
    invoke-virtual {v0, p0}, Lorg/xbet/uikit/components/header/DSHeader;->setButtonClickListener(Landroid/view/View$OnClickListener;)V

    .line 53
    .line 54
    .line 55
    new-instance p0, Lh50/q;

    .line 56
    .line 57
    invoke-direct {p0, p3}, Lh50/q;-><init>(LB4/a;)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {p3, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 61
    .line 62
    .line 63
    new-instance p0, Lh50/r;

    .line 64
    .line 65
    invoke-direct {p0, p2, p3}, Lh50/r;-><init>(LUX0/k;LB4/a;)V

    .line 66
    .line 67
    .line 68
    invoke-virtual {p3, p0}, LB4/a;->t(Lkotlin/jvm/functions/Function0;)V

    .line 69
    .line 70
    .line 71
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 72
    .line 73
    return-object p0
.end method

.method public static final k(LB4/a;Li50/a;Ljava/lang/String;Ln41/m;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    check-cast p3, Lk50/g$a;

    .line 6
    .line 7
    invoke-virtual {p3}, Lk50/g$a;->f()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    invoke-interface {p3, p4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p3

    .line 15
    check-cast p3, Lk50/f;

    .line 16
    .line 17
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    check-cast p0, Lk50/g$a;

    .line 22
    .line 23
    invoke-virtual {p0}, Lk50/g$a;->d()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    const/4 p4, 0x0

    .line 28
    invoke-interface {p1, p2, p3, p0, p4}, Li50/a;->Q1(Ljava/lang/String;Lk50/f;Ljava/lang/String;Z)V

    .line 29
    .line 30
    .line 31
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 32
    .line 33
    return-object p0
.end method

.method public static final l(Li50/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p2}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, Lk50/g$a;

    .line 6
    .line 7
    invoke-virtual {p2}, Lk50/g$a;->d()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    invoke-interface {p0, p1, p2}, Li50/a;->k(Ljava/lang/String;Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final m(Li50/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p2}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, Lk50/g$a;

    .line 6
    .line 7
    invoke-virtual {p2}, Lk50/g$a;->d()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    invoke-interface {p0, p1, p2}, Li50/a;->k(Ljava/lang/String;Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final n(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Ld50/b;

    .line 6
    .line 7
    iget-object p1, p1, Ld50/b;->c:Lorg/xbet/uikit/components/header/DSHeader;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, Lk50/g$a;

    .line 14
    .line 15
    invoke-virtual {v0}, Lk50/g$a;->j()Lorg/xbet/uikit/components/header/a$a;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/header/DSHeader;->setModel(Lorg/xbet/uikit/components/header/a;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    check-cast p1, Ld50/b;

    .line 27
    .line 28
    iget-object p1, p1, Ld50/b;->b:Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    .line 29
    .line 30
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p0

    .line 34
    check-cast p0, Lk50/g$a;

    .line 35
    .line 36
    invoke-virtual {p0}, Lk50/g$a;->e()Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    const/4 v0, 0x0

    .line 41
    const/4 v1, 0x2

    .line 42
    invoke-static {p1, p0, v0, v1, v0}, Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;->setItems$default(Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;Ljava/util/List;Ljava/lang/Runnable;ILjava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 46
    .line 47
    return-object p0
.end method

.method public static final o(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, Ld50/b;

    .line 14
    .line 15
    iget-object p1, p1, Ld50/b;->b:Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    .line 16
    .line 17
    invoke-virtual {p0, v0, p1}, LUX0/k;->e(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method
