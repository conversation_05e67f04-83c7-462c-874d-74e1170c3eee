.class public final LTN0/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LL2/a;


# instance fields
.field public final a:Landroidx/constraintlayout/widget/ConstraintLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final b:Landroid/view/View;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final c:Landroid/widget/ImageView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/uikit/components/lottie/LottieView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final e:LDN0/i;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final f:Landroidx/constraintlayout/widget/Group;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final g:Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final h:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final i:Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamScoreView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final j:Landroidx/viewpager2/widget/ViewPager2;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final k:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final l:Landroid/widget/FrameLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroidx/constraintlayout/widget/ConstraintLayout;Landroid/view/View;Landroid/widget/ImageView;Lorg/xbet/uikit/components/lottie/LottieView;LDN0/i;Landroidx/constraintlayout/widget/Group;Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;Lorg/xbet/uikit/components/shimmer/ShimmerView;Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamScoreView;Landroidx/viewpager2/widget/ViewPager2;Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Landroid/widget/FrameLayout;)V
    .locals 0
    .param p1    # Landroidx/constraintlayout/widget/ConstraintLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Landroid/widget/ImageView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/uikit/components/lottie/LottieView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p5    # LDN0/i;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p6    # Landroidx/constraintlayout/widget/Group;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/uikit/components/shimmer/ShimmerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamScoreView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p10    # Landroidx/viewpager2/widget/ViewPager2;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p12    # Landroid/widget/FrameLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LTN0/k;->a:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 5
    .line 6
    iput-object p2, p0, LTN0/k;->b:Landroid/view/View;

    .line 7
    .line 8
    iput-object p3, p0, LTN0/k;->c:Landroid/widget/ImageView;

    .line 9
    .line 10
    iput-object p4, p0, LTN0/k;->d:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 11
    .line 12
    iput-object p5, p0, LTN0/k;->e:LDN0/i;

    .line 13
    .line 14
    iput-object p6, p0, LTN0/k;->f:Landroidx/constraintlayout/widget/Group;

    .line 15
    .line 16
    iput-object p7, p0, LTN0/k;->g:Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;

    .line 17
    .line 18
    iput-object p8, p0, LTN0/k;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 19
    .line 20
    iput-object p9, p0, LTN0/k;->i:Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamScoreView;

    .line 21
    .line 22
    iput-object p10, p0, LTN0/k;->j:Landroidx/viewpager2/widget/ViewPager2;

    .line 23
    .line 24
    iput-object p11, p0, LTN0/k;->k:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 25
    .line 26
    iput-object p12, p0, LTN0/k;->l:Landroid/widget/FrameLayout;

    .line 27
    .line 28
    return-void
.end method

.method public static a(Landroid/view/View;)LTN0/k;
    .locals 14
    .param p0    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    sget v0, LRN0/a;->contentBackground:I

    .line 2
    .line 3
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object v3

    .line 7
    if-eqz v3, :cond_0

    .line 8
    .line 9
    sget v0, LRN0/a;->ivGameBackground:I

    .line 10
    .line 11
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    move-object v4, v1

    .line 16
    check-cast v4, Landroid/widget/ImageView;

    .line 17
    .line 18
    if-eqz v4, :cond_0

    .line 19
    .line 20
    sget v0, LRN0/a;->lottie:I

    .line 21
    .line 22
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    move-object v5, v1

    .line 27
    check-cast v5, Lorg/xbet/uikit/components/lottie/LottieView;

    .line 28
    .line 29
    if-eqz v5, :cond_0

    .line 30
    .line 31
    sget v0, LRN0/a;->menuShimmer:I

    .line 32
    .line 33
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    if-eqz v1, :cond_0

    .line 38
    .line 39
    invoke-static {v1}, LDN0/i;->a(Landroid/view/View;)LDN0/i;

    .line 40
    .line 41
    .line 42
    move-result-object v6

    .line 43
    sget v0, LRN0/a;->shimmerGroup:I

    .line 44
    .line 45
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    move-object v7, v1

    .line 50
    check-cast v7, Landroidx/constraintlayout/widget/Group;

    .line 51
    .line 52
    if-eqz v7, :cond_0

    .line 53
    .line 54
    sget v0, LRN0/a;->staticNavigationBar:I

    .line 55
    .line 56
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    move-object v8, v1

    .line 61
    check-cast v8, Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;

    .line 62
    .line 63
    if-eqz v8, :cond_0

    .line 64
    .line 65
    sget v0, LRN0/a;->tabsShimmer:I

    .line 66
    .line 67
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    move-object v9, v1

    .line 72
    check-cast v9, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 73
    .line 74
    if-eqz v9, :cond_0

    .line 75
    .line 76
    sget v0, LRN0/a;->teamCardView:I

    .line 77
    .line 78
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 79
    .line 80
    .line 81
    move-result-object v1

    .line 82
    move-object v10, v1

    .line 83
    check-cast v10, Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamScoreView;

    .line 84
    .line 85
    if-eqz v10, :cond_0

    .line 86
    .line 87
    sget v0, LRN0/a;->teamMenuViewPager:I

    .line 88
    .line 89
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 90
    .line 91
    .line 92
    move-result-object v1

    .line 93
    move-object v11, v1

    .line 94
    check-cast v11, Landroidx/viewpager2/widget/ViewPager2;

    .line 95
    .line 96
    if-eqz v11, :cond_0

    .line 97
    .line 98
    sget v0, LRN0/a;->viewPagerTabs:I

    .line 99
    .line 100
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 101
    .line 102
    .line 103
    move-result-object v1

    .line 104
    move-object v12, v1

    .line 105
    check-cast v12, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 106
    .line 107
    if-eqz v12, :cond_0

    .line 108
    .line 109
    sget v0, LRN0/a;->viewPagerTabsContainer:I

    .line 110
    .line 111
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 112
    .line 113
    .line 114
    move-result-object v1

    .line 115
    move-object v13, v1

    .line 116
    check-cast v13, Landroid/widget/FrameLayout;

    .line 117
    .line 118
    if-eqz v13, :cond_0

    .line 119
    .line 120
    new-instance v1, LTN0/k;

    .line 121
    .line 122
    move-object v2, p0

    .line 123
    check-cast v2, Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 124
    .line 125
    invoke-direct/range {v1 .. v13}, LTN0/k;-><init>(Landroidx/constraintlayout/widget/ConstraintLayout;Landroid/view/View;Landroid/widget/ImageView;Lorg/xbet/uikit/components/lottie/LottieView;LDN0/i;Landroidx/constraintlayout/widget/Group;Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;Lorg/xbet/uikit/components/shimmer/ShimmerView;Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamScoreView;Landroidx/viewpager2/widget/ViewPager2;Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Landroid/widget/FrameLayout;)V

    .line 126
    .line 127
    .line 128
    return-object v1

    .line 129
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 130
    .line 131
    .line 132
    move-result-object p0

    .line 133
    invoke-virtual {p0, v0}, Landroid/content/res/Resources;->getResourceName(I)Ljava/lang/String;

    .line 134
    .line 135
    .line 136
    move-result-object p0

    .line 137
    new-instance v0, Ljava/lang/NullPointerException;

    .line 138
    .line 139
    const-string v1, "Missing required view with ID: "

    .line 140
    .line 141
    invoke-virtual {v1, p0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 142
    .line 143
    .line 144
    move-result-object p0

    .line 145
    invoke-direct {v0, p0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 146
    .line 147
    .line 148
    throw v0
.end method


# virtual methods
.method public b()Landroidx/constraintlayout/widget/ConstraintLayout;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    iget-object v0, p0, LTN0/k;->a:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 2
    .line 3
    return-object v0
.end method

.method public bridge synthetic getRoot()Landroid/view/View;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LTN0/k;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
