.class public final synthetic Lorg/xbet/games_section/feature/popular/presentation/o;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

.field public final synthetic b:Z


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Z)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/o;->a:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    iput-boolean p2, p0, Lorg/xbet/games_section/feature/popular/presentation/o;->b:Z

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/o;->a:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    iget-boolean v1, p0, Lorg/xbet/games_section/feature/popular/presentation/o;->b:Z

    invoke-static {v0, v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->t3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Z)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
