.class public final synthetic Leb1/K;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:L<PERSON><PERSON>/jvm/functions/Function0;


# direct methods
.method public synthetic constructor <init>(LB4/a;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Leb1/K;->a:LB4/a;

    iput-object p2, p0, Leb1/K;->b:L<PERSON><PERSON>/jvm/functions/Function0;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Leb1/K;->a:LB4/a;

    iget-object v1, p0, Leb1/K;->b:L<PERSON>lin/jvm/functions/Function0;

    invoke-static {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentTimerAltDesignDelegateKt;->d(LB4/a;Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
