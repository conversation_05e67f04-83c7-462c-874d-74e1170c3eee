.class public final Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000V\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0007\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0018\u0000 <2\u00020\u0001:\u0001+B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u001f\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\n\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u001f\u0010\u0011\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u000eH\u0000\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u001d\u0010\u0017\u001a\u00020\u000b2\u000c\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\u0013H\u0000\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u000f\u0010\u0018\u001a\u00020\u000bH\u0000\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u000f\u0010\u001a\u001a\u00020\u000bH\u0000\u00a2\u0006\u0004\u0008\u001a\u0010\u0019J\u000f\u0010\u001b\u001a\u00020\u000bH\u0000\u00a2\u0006\u0004\u0008\u001b\u0010\u0019J\u001f\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\u001c\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u000eH\u0000\u00a2\u0006\u0004\u0008\u001d\u0010\u0012J\u0017\u0010\u001f\u001a\u00020\u000b2\u0006\u0010\u001e\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0017\u0010!\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008!\u0010 J\u000f\u0010#\u001a\u00020\"H\u0002\u00a2\u0006\u0004\u0008#\u0010$J\u000f\u0010%\u001a\u00020\"H\u0002\u00a2\u0006\u0004\u0008%\u0010$J\u0017\u0010&\u001a\u00020\"2\u0006\u0010\u000f\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008&\u0010\'J\u0017\u0010)\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008)\u0010 R\u0014\u0010-\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008+\u0010,R\u0016\u00100\u001a\u00020\u000e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0018\u00104\u001a\u0004\u0018\u0001018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0018\u00108\u001a\u0004\u0018\u0001058\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00086\u00107R\u001c\u0010;\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\u00138\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00089\u0010:\u00a8\u0006="
    }
    d2 = {
        "Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "",
        "degreeResult",
        "sectorOffset",
        "u",
        "(FF)V",
        "Lkotlin/Function0;",
        "action",
        "setAnimationEndListener$african_roulette_release",
        "(Lkotlin/jvm/functions/Function0;)V",
        "setAnimationEndListener",
        "i",
        "()V",
        "r",
        "q",
        "degree",
        "s",
        "randomDegree",
        "t",
        "(F)V",
        "h",
        "Landroid/animation/ValueAnimator;",
        "j",
        "()Landroid/animation/ValueAnimator;",
        "l",
        "n",
        "(F)Landroid/animation/ValueAnimator;",
        "newRadius",
        "v",
        "Lgg/e;",
        "a",
        "Lgg/e;",
        "viewBinding",
        "b",
        "F",
        "circleRadiusCoef",
        "Landroid/animation/AnimatorSet;",
        "c",
        "Landroid/animation/AnimatorSet;",
        "animatorSet",
        "Landroid/animation/ObjectAnimator;",
        "d",
        "Landroid/animation/ObjectAnimator;",
        "rotateAnim",
        "e",
        "Lkotlin/jvm/functions/Function0;",
        "onStop",
        "f",
        "african_roulette_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final f:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lgg/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:F

.field public c:Landroid/animation/AnimatorSet;

.field public d:Landroid/animation/ObjectAnimator;

.field public e:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->f:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel$a;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroid/util/AttributeSet;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 2
    .line 3
    .line 4
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    const/4 p2, 0x1

    .line 9
    invoke-static {p1, p0, p2}, Lgg/e;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)Lgg/e;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->a:Lgg/e;

    .line 14
    .line 15
    const p1, 0x4019999a

    .line 16
    .line 17
    .line 18
    iput p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->b:F

    .line 19
    .line 20
    new-instance p1, Lkg/f;

    .line 21
    .line 22
    invoke-direct {p1}, Lkg/f;-><init>()V

    .line 23
    .line 24
    .line 25
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->e:Lkotlin/jvm/functions/Function0;

    .line 26
    .line 27
    return-void
.end method

.method public static synthetic a(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;FLandroid/animation/ValueAnimator;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->m(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;FLandroid/animation/ValueAnimator;)V

    return-void
.end method

.method public static synthetic b(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;Landroid/animation/ValueAnimator;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->o(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;Landroid/animation/ValueAnimator;)V

    return-void
.end method

.method public static synthetic c(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;Landroid/animation/ValueAnimator;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->k(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;Landroid/animation/ValueAnimator;)V

    return-void
.end method

.method public static synthetic d()Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->p()Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method

.method public static final synthetic e(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;)Lkotlin/jvm/functions/Function0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->e:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic f(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;Landroid/animation/AnimatorSet;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->c:Landroid/animation/AnimatorSet;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic g(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;Landroid/animation/ObjectAnimator;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->d:Landroid/animation/ObjectAnimator;

    .line 2
    .line 3
    return-void
.end method

.method public static final k(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;Landroid/animation/ValueAnimator;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->a:Lgg/e;

    .line 2
    .line 3
    iget-object v0, v0, Lgg/e;->b:Landroid/widget/ImageView;

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    instance-of v1, v0, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    check-cast v0, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    move-object v0, v2

    .line 18
    :goto_0
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->getAnimatedValue()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    instance-of v1, p1, Ljava/lang/Float;

    .line 23
    .line 24
    if-eqz v1, :cond_1

    .line 25
    .line 26
    move-object v2, p1

    .line 27
    check-cast v2, Ljava/lang/Float;

    .line 28
    .line 29
    :cond_1
    if-eqz v2, :cond_3

    .line 30
    .line 31
    invoke-virtual {v2}, Ljava/lang/Number;->floatValue()F

    .line 32
    .line 33
    .line 34
    move-result p1

    .line 35
    if-eqz v0, :cond_2

    .line 36
    .line 37
    iput p1, v0, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;->r:F

    .line 38
    .line 39
    :cond_2
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->a:Lgg/e;

    .line 40
    .line 41
    iget-object p0, p0, Lgg/e;->b:Landroid/widget/ImageView;

    .line 42
    .line 43
    invoke-virtual {p0, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 44
    .line 45
    .line 46
    :cond_3
    return-void
.end method

.method public static final m(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;FLandroid/animation/ValueAnimator;)V
    .locals 1

    .line 1
    invoke-virtual {p2}, Landroid/animation/ValueAnimator;->getAnimatedValue()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    instance-of v0, p2, Ljava/lang/Float;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    check-cast p2, Ljava/lang/Float;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 p2, 0x0

    .line 13
    :goto_0
    if-eqz p2, :cond_1

    .line 14
    .line 15
    invoke-virtual {p2}, Ljava/lang/Number;->floatValue()F

    .line 16
    .line 17
    .line 18
    move-result p2

    .line 19
    add-float/2addr p1, p2

    .line 20
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->v(F)V

    .line 21
    .line 22
    .line 23
    :cond_1
    return-void
.end method

.method public static final o(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;Landroid/animation/ValueAnimator;)V
    .locals 3

    .line 1
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->getAnimatedValue()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    instance-of v0, p1, Ljava/lang/Float;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    check-cast p1, Ljava/lang/Float;

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object p1, v1

    .line 14
    :goto_0
    if-eqz p1, :cond_3

    .line 15
    .line 16
    invoke-virtual {p1}, Ljava/lang/Number;->floatValue()F

    .line 17
    .line 18
    .line 19
    move-result p1

    .line 20
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->a:Lgg/e;

    .line 21
    .line 22
    iget-object v0, v0, Lgg/e;->b:Landroid/widget/ImageView;

    .line 23
    .line 24
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    instance-of v2, v0, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;

    .line 29
    .line 30
    if-eqz v2, :cond_1

    .line 31
    .line 32
    move-object v1, v0

    .line 33
    check-cast v1, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;

    .line 34
    .line 35
    :cond_1
    if-eqz v1, :cond_2

    .line 36
    .line 37
    iput p1, v1, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;->r:F

    .line 38
    .line 39
    :cond_2
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->a:Lgg/e;

    .line 40
    .line 41
    iget-object p0, p0, Lgg/e;->b:Landroid/widget/ImageView;

    .line 42
    .line 43
    invoke-virtual {p0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 44
    .line 45
    .line 46
    :cond_3
    return-void
.end method

.method public static final p()Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object v0
.end method


# virtual methods
.method public final h(F)V
    .locals 6

    .line 1
    const v0, 0x4019999a

    .line 2
    .line 3
    .line 4
    iput v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->b:F

    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->j()Landroid/animation/ValueAnimator;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->l()Landroid/animation/ValueAnimator;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    new-instance v2, Landroid/animation/AnimatorSet;

    .line 15
    .line 16
    invoke-direct {v2}, Landroid/animation/AnimatorSet;-><init>()V

    .line 17
    .line 18
    .line 19
    const/4 v3, 0x2

    .line 20
    new-array v4, v3, [Landroid/animation/Animator;

    .line 21
    .line 22
    const/4 v5, 0x0

    .line 23
    aput-object v0, v4, v5

    .line 24
    .line 25
    const/4 v0, 0x1

    .line 26
    aput-object v1, v4, v0

    .line 27
    .line 28
    invoke-virtual {v2, v4}, Landroid/animation/AnimatorSet;->playTogether([Landroid/animation/Animator;)V

    .line 29
    .line 30
    .line 31
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->n(F)Landroid/animation/ValueAnimator;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    new-instance v1, Landroid/animation/AnimatorSet;

    .line 36
    .line 37
    invoke-direct {v1}, Landroid/animation/AnimatorSet;-><init>()V

    .line 38
    .line 39
    .line 40
    iput-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->c:Landroid/animation/AnimatorSet;

    .line 41
    .line 42
    new-array v3, v3, [Landroid/animation/Animator;

    .line 43
    .line 44
    aput-object v2, v3, v5

    .line 45
    .line 46
    aput-object p1, v3, v0

    .line 47
    .line 48
    invoke-virtual {v1, v3}, Landroid/animation/AnimatorSet;->playSequentially([Landroid/animation/Animator;)V

    .line 49
    .line 50
    .line 51
    invoke-virtual {v1}, Landroid/animation/Animator;->removeAllListeners()V

    .line 52
    .line 53
    .line 54
    new-instance p1, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel$b;

    .line 55
    .line 56
    invoke-direct {p1, p0}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel$b;-><init>(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;)V

    .line 57
    .line 58
    .line 59
    invoke-virtual {v1, p1}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 60
    .line 61
    .line 62
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->c:Landroid/animation/AnimatorSet;

    .line 63
    .line 64
    if-eqz p1, :cond_0

    .line 65
    .line 66
    invoke-virtual {p1}, Landroid/animation/AnimatorSet;->start()V

    .line 67
    .line 68
    .line 69
    :cond_0
    return-void
.end method

.method public final i()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->c:Landroid/animation/AnimatorSet;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/animation/Animator;->removeAllListeners()V

    .line 6
    .line 7
    .line 8
    :cond_0
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->c:Landroid/animation/AnimatorSet;

    .line 9
    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/animation/AnimatorSet;->cancel()V

    .line 13
    .line 14
    .line 15
    :cond_1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->d:Landroid/animation/ObjectAnimator;

    .line 16
    .line 17
    if-eqz v0, :cond_2

    .line 18
    .line 19
    invoke-virtual {v0}, Landroid/animation/Animator;->cancel()V

    .line 20
    .line 21
    .line 22
    :cond_2
    return-void
.end method

.method public final j()Landroid/animation/ValueAnimator;
    .locals 3

    .line 1
    const/4 v0, 0x2

    .line 2
    new-array v0, v0, [F

    .line 3
    .line 4
    fill-array-data v0, :array_0

    .line 5
    .line 6
    .line 7
    invoke-static {v0}, Landroid/animation/ValueAnimator;->ofFloat([F)Landroid/animation/ValueAnimator;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    const-wide/16 v1, 0x834

    .line 12
    .line 13
    invoke-virtual {v0, v1, v2}, Landroid/animation/ValueAnimator;->setDuration(J)Landroid/animation/ValueAnimator;

    .line 14
    .line 15
    .line 16
    new-instance v1, Landroid/view/animation/DecelerateInterpolator;

    .line 17
    .line 18
    invoke-direct {v1}, Landroid/view/animation/DecelerateInterpolator;-><init>()V

    .line 19
    .line 20
    .line 21
    invoke-virtual {v0, v1}, Landroid/animation/ValueAnimator;->setInterpolator(Landroid/animation/TimeInterpolator;)V

    .line 22
    .line 23
    .line 24
    new-instance v1, Lkg/d;

    .line 25
    .line 26
    invoke-direct {v1, p0}, Lkg/d;-><init>(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {v0, v1}, Landroid/animation/ValueAnimator;->addUpdateListener(Landroid/animation/ValueAnimator$AnimatorUpdateListener;)V

    .line 30
    .line 31
    .line 32
    return-object v0

    .line 33
    :array_0
    .array-data 4
        -0x3e600000    # -20.0f
        -0x3ced0000    # -147.0f
    .end array-data
.end method

.method public final l()Landroid/animation/ValueAnimator;
    .locals 3

    .line 1
    const/4 v0, 0x2

    .line 2
    new-array v0, v0, [F

    .line 3
    .line 4
    fill-array-data v0, :array_0

    .line 5
    .line 6
    .line 7
    invoke-static {v0}, Landroid/animation/ValueAnimator;->ofFloat([F)Landroid/animation/ValueAnimator;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    const-wide/16 v1, 0x320

    .line 12
    .line 13
    invoke-virtual {v0, v1, v2}, Landroid/animation/ValueAnimator;->setStartDelay(J)V

    .line 14
    .line 15
    .line 16
    const-wide/16 v1, 0x514

    .line 17
    .line 18
    invoke-virtual {v0, v1, v2}, Landroid/animation/ValueAnimator;->setDuration(J)Landroid/animation/ValueAnimator;

    .line 19
    .line 20
    .line 21
    iget v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->b:F

    .line 22
    .line 23
    new-instance v2, Lkg/c;

    .line 24
    .line 25
    invoke-direct {v2, p0, v1}, Lkg/c;-><init>(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;F)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {v0, v2}, Landroid/animation/ValueAnimator;->addUpdateListener(Landroid/animation/ValueAnimator$AnimatorUpdateListener;)V

    .line 29
    .line 30
    .line 31
    return-object v0

    .line 32
    nop

    .line 33
    :array_0
    .array-data 4
        0x0
        0x3f866666
    .end array-data
.end method

.method public final n(F)Landroid/animation/ValueAnimator;
    .locals 3

    .line 1
    const/16 v0, 0x438

    .line 2
    .line 3
    int-to-float v0, v0

    .line 4
    add-float/2addr v0, p1

    .line 5
    const/4 p1, 0x2

    .line 6
    new-array p1, p1, [F

    .line 7
    .line 8
    const/high16 v1, -0x3ced0000    # -147.0f

    .line 9
    .line 10
    const/4 v2, 0x0

    .line 11
    aput v1, p1, v2

    .line 12
    .line 13
    const/4 v1, 0x1

    .line 14
    aput v0, p1, v1

    .line 15
    .line 16
    invoke-static {p1}, Landroid/animation/ValueAnimator;->ofFloat([F)Landroid/animation/ValueAnimator;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    const-wide/16 v0, 0x2710

    .line 21
    .line 22
    invoke-virtual {p1, v0, v1}, Landroid/animation/ValueAnimator;->setDuration(J)Landroid/animation/ValueAnimator;

    .line 23
    .line 24
    .line 25
    new-instance v0, Lkg/e;

    .line 26
    .line 27
    invoke-direct {v0, p0}, Lkg/e;-><init>(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {p1, v0}, Landroid/animation/ValueAnimator;->addUpdateListener(Landroid/animation/ValueAnimator$AnimatorUpdateListener;)V

    .line 31
    .line 32
    .line 33
    return-object p1
.end method

.method public onMeasure(II)V
    .locals 1

    .line 1
    invoke-super {p0, p1, p2}, Landroid/widget/FrameLayout;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->a:Lgg/e;

    .line 5
    .line 6
    iget-object p1, p1, Lgg/e;->b:Landroid/widget/ImageView;

    .line 7
    .line 8
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    instance-of p2, p1, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;

    .line 13
    .line 14
    if-eqz p2, :cond_0

    .line 15
    .line 16
    check-cast p1, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    const/4 p1, 0x0

    .line 20
    :goto_0
    if-eqz p1, :cond_1

    .line 21
    .line 22
    iget-object p2, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->a:Lgg/e;

    .line 23
    .line 24
    iget-object p2, p2, Lgg/e;->c:Landroid/widget/ImageView;

    .line 25
    .line 26
    invoke-virtual {p2}, Landroid/view/View;->getHeight()I

    .line 27
    .line 28
    .line 29
    move-result p2

    .line 30
    div-int/lit8 p2, p2, 0xf

    .line 31
    .line 32
    iput p2, p1, Landroid/view/ViewGroup$MarginLayoutParams;->height:I

    .line 33
    .line 34
    iput p2, p1, Landroid/view/ViewGroup$MarginLayoutParams;->width:I

    .line 35
    .line 36
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 37
    .line 38
    .line 39
    move-result p2

    .line 40
    int-to-float p2, p2

    .line 41
    iget v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->b:F

    .line 42
    .line 43
    div-float/2addr p2, v0

    .line 44
    float-to-int p2, p2

    .line 45
    iput p2, p1, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;->q:I

    .line 46
    .line 47
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 48
    .line 49
    .line 50
    :cond_1
    return-void
.end method

.method public final q()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->c:Landroid/animation/AnimatorSet;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/animation/AnimatorSet;->pause()V

    .line 6
    .line 7
    .line 8
    :cond_0
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->d:Landroid/animation/ObjectAnimator;

    .line 9
    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/animation/Animator;->pause()V

    .line 13
    .line 14
    .line 15
    :cond_1
    return-void
.end method

.method public final r()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->c:Landroid/animation/AnimatorSet;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    invoke-virtual {v0}, Landroid/animation/Animator;->isPaused()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->c:Landroid/animation/AnimatorSet;

    .line 13
    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    invoke-virtual {v0}, Landroid/animation/AnimatorSet;->resume()V

    .line 17
    .line 18
    .line 19
    :cond_0
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->d:Landroid/animation/ObjectAnimator;

    .line 20
    .line 21
    if-eqz v0, :cond_1

    .line 22
    .line 23
    invoke-virtual {v0}, Landroid/animation/Animator;->isPaused()Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-ne v0, v1, :cond_1

    .line 28
    .line 29
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->d:Landroid/animation/ObjectAnimator;

    .line 30
    .line 31
    if-eqz v0, :cond_1

    .line 32
    .line 33
    invoke-virtual {v0}, Landroid/animation/Animator;->resume()V

    .line 34
    .line 35
    .line 36
    :cond_1
    return-void
.end method

.method public final s(FF)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->a:Lgg/e;

    .line 2
    .line 3
    iget-object v0, v0, Lgg/e;->d:Landroid/widget/ImageView;

    .line 4
    .line 5
    add-float/2addr p1, p2

    .line 6
    const/16 v1, 0x14

    .line 7
    .line 8
    int-to-float v1, v1

    .line 9
    sub-float/2addr p1, v1

    .line 10
    invoke-virtual {v0, p1}, Landroid/view/View;->setRotation(F)V

    .line 11
    .line 12
    .line 13
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->a:Lgg/e;

    .line 14
    .line 15
    iget-object p1, p1, Lgg/e;->b:Landroid/widget/ImageView;

    .line 16
    .line 17
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    instance-of v0, p1, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;

    .line 22
    .line 23
    if-eqz v0, :cond_0

    .line 24
    .line 25
    check-cast p1, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;

    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_0
    const/4 p1, 0x0

    .line 29
    :goto_0
    if-eqz p1, :cond_1

    .line 30
    .line 31
    iput p2, p1, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;->r:F

    .line 32
    .line 33
    :cond_1
    iget-object p2, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->a:Lgg/e;

    .line 34
    .line 35
    iget-object p2, p2, Lgg/e;->b:Landroid/widget/ImageView;

    .line 36
    .line 37
    invoke-virtual {p2, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 38
    .line 39
    .line 40
    const p1, 0x405ccccd

    .line 41
    .line 42
    .line 43
    iput p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->b:F

    .line 44
    .line 45
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 46
    .line 47
    .line 48
    return-void
.end method

.method public final setAnimationEndListener$african_roulette_release(Lkotlin/jvm/functions/Function0;)V
    .locals 0
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->e:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    return-void
.end method

.method public final t(F)V
    .locals 4

    .line 1
    const/16 v0, 0x870

    .line 2
    .line 3
    int-to-float v0, v0

    .line 4
    add-float/2addr p1, v0

    .line 5
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->a:Lgg/e;

    .line 6
    .line 7
    iget-object v0, v0, Lgg/e;->d:Landroid/widget/ImageView;

    .line 8
    .line 9
    const/4 v1, 0x2

    .line 10
    new-array v1, v1, [F

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    const/4 v3, 0x0

    .line 14
    aput v2, v1, v3

    .line 15
    .line 16
    const/4 v2, 0x1

    .line 17
    aput p1, v1, v2

    .line 18
    .line 19
    const-string p1, "rotation"

    .line 20
    .line 21
    invoke-static {v0, p1, v1}, Landroid/animation/ObjectAnimator;->ofFloat(Ljava/lang/Object;Ljava/lang/String;[F)Landroid/animation/ObjectAnimator;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    const-wide/16 v0, 0x2ee0

    .line 26
    .line 27
    invoke-virtual {p1, v0, v1}, Landroid/animation/ObjectAnimator;->setDuration(J)Landroid/animation/ObjectAnimator;

    .line 28
    .line 29
    .line 30
    new-instance v0, Landroid/view/animation/DecelerateInterpolator;

    .line 31
    .line 32
    invoke-direct {v0}, Landroid/view/animation/DecelerateInterpolator;-><init>()V

    .line 33
    .line 34
    .line 35
    invoke-virtual {p1, v0}, Landroid/animation/Animator;->setInterpolator(Landroid/animation/TimeInterpolator;)V

    .line 36
    .line 37
    .line 38
    new-instance v0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel$c;

    .line 39
    .line 40
    invoke-direct {v0, p0}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel$c;-><init>(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p1, v0}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 44
    .line 45
    .line 46
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->d:Landroid/animation/ObjectAnimator;

    .line 47
    .line 48
    invoke-virtual {p1}, Landroid/animation/ObjectAnimator;->start()V

    .line 49
    .line 50
    .line 51
    return-void
.end method

.method public final u(FF)V
    .locals 1

    .line 1
    add-float/2addr p1, p2

    .line 2
    const/16 v0, 0x14

    .line 3
    .line 4
    int-to-float v0, v0

    .line 5
    sub-float/2addr p1, v0

    .line 6
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->t(F)V

    .line 7
    .line 8
    .line 9
    invoke-virtual {p0, p2}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->h(F)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final v(F)V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    cmpg-float v0, p1, v0

    .line 3
    .line 4
    if-nez v0, :cond_0

    .line 5
    .line 6
    const p1, 0x4019999a

    .line 7
    .line 8
    .line 9
    :cond_0
    iput p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->b:F

    .line 10
    .line 11
    return-void
.end method
