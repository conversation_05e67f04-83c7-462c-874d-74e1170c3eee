.class public final LSH0/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LL2/a;


# instance fields
.field public final a:Landroidx/constraintlayout/widget/ConstraintLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final b:Lcom/google/android/material/button/MaterialButton;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final c:Landroidx/constraintlayout/widget/ConstraintLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final d:Landroid/widget/FrameLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final e:Landroid/widget/FrameLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final f:Landroid/widget/FrameLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final g:Landroid/widget/ImageView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final h:Landroid/widget/ImageView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final i:Landroid/widget/ImageView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final j:Lorg/xbet/uikit/components/lottie/LottieView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final k:Landroidx/recyclerview/widget/RecyclerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final l:Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/ScrollablePanel;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final m:Lorg/xbet/ui_common/viewcomponents/layouts/linear/ShimmerLinearLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final n:Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final o:Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamCardView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final p:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final q:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroidx/constraintlayout/widget/ConstraintLayout;Lcom/google/android/material/button/MaterialButton;Landroidx/constraintlayout/widget/ConstraintLayout;Landroid/widget/FrameLayout;Landroid/widget/FrameLayout;Landroid/widget/FrameLayout;Landroid/widget/ImageView;Landroid/widget/ImageView;Landroid/widget/ImageView;Lorg/xbet/uikit/components/lottie/LottieView;Landroidx/recyclerview/widget/RecyclerView;Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/ScrollablePanel;Lorg/xbet/ui_common/viewcomponents/layouts/linear/ShimmerLinearLayout;Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamCardView;Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Landroid/widget/TextView;)V
    .locals 0
    .param p1    # Landroidx/constraintlayout/widget/ConstraintLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Lcom/google/android/material/button/MaterialButton;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Landroidx/constraintlayout/widget/ConstraintLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Landroid/widget/FrameLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p5    # Landroid/widget/FrameLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p6    # Landroid/widget/FrameLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p7    # Landroid/widget/ImageView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p8    # Landroid/widget/ImageView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p9    # Landroid/widget/ImageView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/uikit/components/lottie/LottieView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p11    # Landroidx/recyclerview/widget/RecyclerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p12    # Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/ScrollablePanel;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p13    # Lorg/xbet/ui_common/viewcomponents/layouts/linear/ShimmerLinearLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p14    # Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p15    # Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamCardView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p16    # Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p17    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LSH0/k;->a:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 5
    .line 6
    iput-object p2, p0, LSH0/k;->b:Lcom/google/android/material/button/MaterialButton;

    .line 7
    .line 8
    iput-object p3, p0, LSH0/k;->c:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 9
    .line 10
    iput-object p4, p0, LSH0/k;->d:Landroid/widget/FrameLayout;

    .line 11
    .line 12
    iput-object p5, p0, LSH0/k;->e:Landroid/widget/FrameLayout;

    .line 13
    .line 14
    iput-object p6, p0, LSH0/k;->f:Landroid/widget/FrameLayout;

    .line 15
    .line 16
    iput-object p7, p0, LSH0/k;->g:Landroid/widget/ImageView;

    .line 17
    .line 18
    iput-object p8, p0, LSH0/k;->h:Landroid/widget/ImageView;

    .line 19
    .line 20
    iput-object p9, p0, LSH0/k;->i:Landroid/widget/ImageView;

    .line 21
    .line 22
    iput-object p10, p0, LSH0/k;->j:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 23
    .line 24
    iput-object p11, p0, LSH0/k;->k:Landroidx/recyclerview/widget/RecyclerView;

    .line 25
    .line 26
    iput-object p12, p0, LSH0/k;->l:Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/ScrollablePanel;

    .line 27
    .line 28
    iput-object p13, p0, LSH0/k;->m:Lorg/xbet/ui_common/viewcomponents/layouts/linear/ShimmerLinearLayout;

    .line 29
    .line 30
    iput-object p14, p0, LSH0/k;->n:Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;

    .line 31
    .line 32
    iput-object p15, p0, LSH0/k;->o:Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamCardView;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, LSH0/k;->p:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, LSH0/k;->q:Landroid/widget/TextView;

    .line 41
    .line 42
    return-void
.end method

.method public static a(Landroid/view/View;)LSH0/k;
    .locals 21
    .param p0    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    sget v1, LQH0/a;->btnResult:I

    .line 4
    .line 5
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    move-object v5, v2

    .line 10
    check-cast v5, Lcom/google/android/material/button/MaterialButton;

    .line 11
    .line 12
    if-eqz v5, :cond_0

    .line 13
    .line 14
    sget v1, LQH0/a;->content:I

    .line 15
    .line 16
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    move-object v6, v2

    .line 21
    check-cast v6, Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 22
    .line 23
    if-eqz v6, :cond_0

    .line 24
    .line 25
    sget v1, LQH0/a;->flResultContainer:I

    .line 26
    .line 27
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 28
    .line 29
    .line 30
    move-result-object v2

    .line 31
    move-object v7, v2

    .line 32
    check-cast v7, Landroid/widget/FrameLayout;

    .line 33
    .line 34
    if-eqz v7, :cond_0

    .line 35
    .line 36
    sget v1, LQH0/a;->flToolbarInfo:I

    .line 37
    .line 38
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    move-object v8, v2

    .line 43
    check-cast v8, Landroid/widget/FrameLayout;

    .line 44
    .line 45
    if-eqz v8, :cond_0

    .line 46
    .line 47
    sget v1, LQH0/a;->flToolbarSelectors:I

    .line 48
    .line 49
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    move-object v9, v2

    .line 54
    check-cast v9, Landroid/widget/FrameLayout;

    .line 55
    .line 56
    if-eqz v9, :cond_0

    .line 57
    .line 58
    sget v1, LQH0/a;->ivGameBackground:I

    .line 59
    .line 60
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 61
    .line 62
    .line 63
    move-result-object v2

    .line 64
    move-object v10, v2

    .line 65
    check-cast v10, Landroid/widget/ImageView;

    .line 66
    .line 67
    if-eqz v10, :cond_0

    .line 68
    .line 69
    sget v1, LQH0/a;->ivToolbarInfo:I

    .line 70
    .line 71
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 72
    .line 73
    .line 74
    move-result-object v2

    .line 75
    move-object v11, v2

    .line 76
    check-cast v11, Landroid/widget/ImageView;

    .line 77
    .line 78
    if-eqz v11, :cond_0

    .line 79
    .line 80
    sget v1, LQH0/a;->ivToolbarSelectors:I

    .line 81
    .line 82
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 83
    .line 84
    .line 85
    move-result-object v2

    .line 86
    move-object v12, v2

    .line 87
    check-cast v12, Landroid/widget/ImageView;

    .line 88
    .line 89
    if-eqz v12, :cond_0

    .line 90
    .line 91
    sget v1, LQH0/a;->lottieEmptyView:I

    .line 92
    .line 93
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 94
    .line 95
    .line 96
    move-result-object v2

    .line 97
    move-object v13, v2

    .line 98
    check-cast v13, Lorg/xbet/uikit/components/lottie/LottieView;

    .line 99
    .line 100
    if-eqz v13, :cond_0

    .line 101
    .line 102
    sget v1, LQH0/a;->rvInnings:I

    .line 103
    .line 104
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 105
    .line 106
    .line 107
    move-result-object v2

    .line 108
    move-object v14, v2

    .line 109
    check-cast v14, Landroidx/recyclerview/widget/RecyclerView;

    .line 110
    .line 111
    if-eqz v14, :cond_0

    .line 112
    .line 113
    sget v1, LQH0/a;->scrollablePanel:I

    .line 114
    .line 115
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 116
    .line 117
    .line 118
    move-result-object v2

    .line 119
    move-object v15, v2

    .line 120
    check-cast v15, Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/ScrollablePanel;

    .line 121
    .line 122
    if-eqz v15, :cond_0

    .line 123
    .line 124
    sget v1, LQH0/a;->shimmers:I

    .line 125
    .line 126
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 127
    .line 128
    .line 129
    move-result-object v2

    .line 130
    move-object/from16 v16, v2

    .line 131
    .line 132
    check-cast v16, Lorg/xbet/ui_common/viewcomponents/layouts/linear/ShimmerLinearLayout;

    .line 133
    .line 134
    if-eqz v16, :cond_0

    .line 135
    .line 136
    sget v1, LQH0/a;->staticNavigationBar:I

    .line 137
    .line 138
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 139
    .line 140
    .line 141
    move-result-object v2

    .line 142
    move-object/from16 v17, v2

    .line 143
    .line 144
    check-cast v17, Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;

    .line 145
    .line 146
    if-eqz v17, :cond_0

    .line 147
    .line 148
    sget v1, LQH0/a;->teamCardView:I

    .line 149
    .line 150
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 151
    .line 152
    .line 153
    move-result-object v2

    .line 154
    move-object/from16 v18, v2

    .line 155
    .line 156
    check-cast v18, Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamCardView;

    .line 157
    .line 158
    if-eqz v18, :cond_0

    .line 159
    .line 160
    sget v1, LQH0/a;->teamTabs:I

    .line 161
    .line 162
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 163
    .line 164
    .line 165
    move-result-object v2

    .line 166
    move-object/from16 v19, v2

    .line 167
    .line 168
    check-cast v19, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 169
    .line 170
    if-eqz v19, :cond_0

    .line 171
    .line 172
    sget v1, LQH0/a;->tvTitle:I

    .line 173
    .line 174
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 175
    .line 176
    .line 177
    move-result-object v2

    .line 178
    move-object/from16 v20, v2

    .line 179
    .line 180
    check-cast v20, Landroid/widget/TextView;

    .line 181
    .line 182
    if-eqz v20, :cond_0

    .line 183
    .line 184
    new-instance v3, LSH0/k;

    .line 185
    .line 186
    move-object v4, v0

    .line 187
    check-cast v4, Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 188
    .line 189
    invoke-direct/range {v3 .. v20}, LSH0/k;-><init>(Landroidx/constraintlayout/widget/ConstraintLayout;Lcom/google/android/material/button/MaterialButton;Landroidx/constraintlayout/widget/ConstraintLayout;Landroid/widget/FrameLayout;Landroid/widget/FrameLayout;Landroid/widget/FrameLayout;Landroid/widget/ImageView;Landroid/widget/ImageView;Landroid/widget/ImageView;Lorg/xbet/uikit/components/lottie/LottieView;Landroidx/recyclerview/widget/RecyclerView;Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/ScrollablePanel;Lorg/xbet/ui_common/viewcomponents/layouts/linear/ShimmerLinearLayout;Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamCardView;Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Landroid/widget/TextView;)V

    .line 190
    .line 191
    .line 192
    return-object v3

    .line 193
    :cond_0
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 194
    .line 195
    .line 196
    move-result-object v0

    .line 197
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getResourceName(I)Ljava/lang/String;

    .line 198
    .line 199
    .line 200
    move-result-object v0

    .line 201
    new-instance v1, Ljava/lang/NullPointerException;

    .line 202
    .line 203
    const-string v2, "Missing required view with ID: "

    .line 204
    .line 205
    invoke-virtual {v2, v0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 206
    .line 207
    .line 208
    move-result-object v0

    .line 209
    invoke-direct {v1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 210
    .line 211
    .line 212
    throw v1
.end method


# virtual methods
.method public b()Landroidx/constraintlayout/widget/ConstraintLayout;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    iget-object v0, p0, LSH0/k;->a:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 2
    .line 3
    return-object v0
.end method

.method public bridge synthetic getRoot()Landroid/view/View;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LSH0/k;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
