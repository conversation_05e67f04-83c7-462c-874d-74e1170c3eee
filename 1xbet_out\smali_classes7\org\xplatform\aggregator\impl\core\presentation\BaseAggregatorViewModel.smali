.class public abstract Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u008e\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008.\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008 \u0018\u00002\u00020\u0001:\u0002\u00a9\u0001B\u009f\u0001\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u00a2\u0006\u0004\u0008(\u0010)J\u000f\u0010+\u001a\u00020*H\u0002\u00a2\u0006\u0004\u0008+\u0010,J\u001f\u00101\u001a\u00020*2\u0006\u0010.\u001a\u00020-2\u0006\u00100\u001a\u00020/H\u0002\u00a2\u0006\u0004\u00081\u00102J\u0017\u00105\u001a\u00020-2\u0006\u00104\u001a\u000203H\u0002\u00a2\u0006\u0004\u00085\u00106J\u000f\u00107\u001a\u00020*H\u0016\u00a2\u0006\u0004\u00087\u0010,J\u000f\u00108\u001a\u00020*H\u0016\u00a2\u0006\u0004\u00088\u0010,J\u000f\u00109\u001a\u00020*H&\u00a2\u0006\u0004\u00089\u0010,J\u0017\u0010<\u001a\u00020*2\u0006\u0010;\u001a\u00020:H&\u00a2\u0006\u0004\u0008<\u0010=J\u0013\u0010@\u001a\u0008\u0012\u0004\u0012\u00020?0>\u00a2\u0006\u0004\u0008@\u0010AJ\u0013\u0010C\u001a\u0008\u0012\u0004\u0012\u00020-0B\u00a2\u0006\u0004\u0008C\u0010DJ\u0013\u0010F\u001a\u0008\u0012\u0004\u0012\u00020-0E\u00a2\u0006\u0004\u0008F\u0010GJ\u0015\u0010I\u001a\u0008\u0012\u0004\u0012\u00020H0BH\u0016\u00a2\u0006\u0004\u0008I\u0010DJ\r\u0010J\u001a\u00020*\u00a2\u0006\u0004\u0008J\u0010,J\r\u0010K\u001a\u00020*\u00a2\u0006\u0004\u0008K\u0010,J\u001f\u0010M\u001a\u00020*2\u0006\u0010;\u001a\u00020:2\u0006\u0010L\u001a\u00020-H\u0004\u00a2\u0006\u0004\u0008M\u0010NJ\r\u0010O\u001a\u00020*\u00a2\u0006\u0004\u0008O\u0010,J\u001d\u0010R\u001a\u00020*2\u0006\u0010.\u001a\u00020-2\u0006\u0010Q\u001a\u00020P\u00a2\u0006\u0004\u0008R\u0010SJ\r\u0010T\u001a\u00020*\u00a2\u0006\u0004\u0008T\u0010,J\r\u0010U\u001a\u00020*\u00a2\u0006\u0004\u0008U\u0010,J\u001d\u0010V\u001a\u00020*2\u0006\u0010.\u001a\u00020-2\u0006\u00100\u001a\u00020/\u00a2\u0006\u0004\u0008V\u00102J\r\u0010W\u001a\u00020*\u00a2\u0006\u0004\u0008W\u0010,J\u0017\u0010X\u001a\u00020*2\u0006\u0010;\u001a\u00020:H\u0004\u00a2\u0006\u0004\u0008X\u0010=R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Y\u0010ZR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008[\u0010\\R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008]\u0010^R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008_\u0010`R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008a\u0010bR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008c\u0010dR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008e\u0010fR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008g\u0010hR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008i\u0010jR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008k\u0010lR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008m\u0010nR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008o\u0010pR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008q\u0010rR\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008s\u0010tR\u0014\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008u\u0010vR\u0014\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008w\u0010xR\u0014\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008y\u0010zR\u0014\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008{\u0010|R\u0014\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008}\u0010~R\u0019\u0010\u0082\u0001\u001a\u00020\u007f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0080\u0001\u0010\u0081\u0001R\u001c\u0010\u0086\u0001\u001a\u0005\u0018\u00010\u0083\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0084\u0001\u0010\u0085\u0001R\u001c\u0010\u0088\u0001\u001a\u0005\u0018\u00010\u0083\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0087\u0001\u0010\u0085\u0001R\u0017\u0010\u008b\u0001\u001a\u00030\u0089\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008I\u0010\u008a\u0001R\u001d\u0010\u008e\u0001\u001a\t\u0012\u0004\u0012\u00020-0\u008c\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u00088\u0010\u008d\u0001R\u001e\u0010\u0092\u0001\u001a\t\u0012\u0004\u0012\u00020-0\u008f\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0090\u0001\u0010\u0091\u0001R\u001e\u0010\u0096\u0001\u001a\t\u0012\u0004\u0012\u00020?0\u0093\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0094\u0001\u0010\u0095\u0001R*\u0010\u009e\u0001\u001a\u00030\u0097\u00018\u0004@\u0004X\u0084\u000e\u00a2\u0006\u0018\n\u0006\u0008\u0098\u0001\u0010\u0099\u0001\u001a\u0006\u0008\u009a\u0001\u0010\u009b\u0001\"\u0006\u0008\u009c\u0001\u0010\u009d\u0001R*\u0010\u00a2\u0001\u001a\u00030\u0097\u00018\u0004@\u0004X\u0084\u000e\u00a2\u0006\u0018\n\u0006\u0008\u009f\u0001\u0010\u0099\u0001\u001a\u0006\u0008\u00a0\u0001\u0010\u009b\u0001\"\u0006\u0008\u00a1\u0001\u0010\u009d\u0001R \u0010\u00a8\u0001\u001a\u00030\u00a3\u00018\u0004X\u0084\u0004\u00a2\u0006\u0010\n\u0006\u0008\u00a4\u0001\u0010\u00a5\u0001\u001a\u0006\u0008\u00a6\u0001\u0010\u00a7\u0001\u00a8\u0006\u00aa\u0001"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "LP91/b;",
        "aggregatorNavigator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LxX0/a;",
        "blockPaymentNavigator",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "LGg/a;",
        "searchAnalytics",
        "Lorg/xbet/analytics/domain/scope/I;",
        "depositAnalytics",
        "LwX0/C;",
        "routerHolder",
        "Lm8/a;",
        "dispatchers",
        "Lek/f;",
        "updateWithCheckGamesAggregatorScenario",
        "Lfk/l;",
        "getLastBalanceUseCase",
        "Lek/d;",
        "getScreenBalanceByTypeScenario",
        "LHX0/e;",
        "resourceManager",
        "LAR/a;",
        "depositFatmanLogger",
        "LZR/a;",
        "searchFatmanLogger",
        "Lgk0/a;",
        "getAccountSelectionStyleConfigTypeScenario",
        "Lfk/s;",
        "hasUserScreenBalanceUseCase",
        "Lfk/o;",
        "observeScreenBalanceUseCase",
        "LC81/f;",
        "setDailyTaskRefreshScenario",
        "<init>",
        "(LP91/b;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LxX0/a;Lp9/c;LGg/a;Lorg/xbet/analytics/domain/scope/I;LwX0/C;Lm8/a;Lek/f;Lfk/l;Lek/d;LHX0/e;LAR/a;LZR/a;Lgk0/a;Lfk/s;Lfk/o;LC81/f;)V",
        "",
        "h4",
        "()V",
        "",
        "screenName",
        "Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "depositCallScreenType",
        "X3",
        "(Ljava/lang/String;Lorg/xbet/analytics/domain/scope/DepositCallScreenType;)V",
        "Lorg/xbet/balance/model/BalanceModel;",
        "balance",
        "L3",
        "(Lorg/xbet/balance/model/BalanceModel;)Ljava/lang/String;",
        "R3",
        "S3",
        "d4",
        "",
        "throwable",
        "e4",
        "(Ljava/lang/Throwable;)V",
        "Lkotlinx/coroutines/flow/f0;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;",
        "c4",
        "()Lkotlinx/coroutines/flow/f0;",
        "Lkotlinx/coroutines/flow/e;",
        "g4",
        "()Lkotlinx/coroutines/flow/e;",
        "Lkotlinx/coroutines/flow/Z;",
        "P3",
        "()Lkotlinx/coroutines/flow/Z;",
        "",
        "I3",
        "Y3",
        "J3",
        "defaultMessage",
        "Q3",
        "(Ljava/lang/Throwable;Ljava/lang/String;)V",
        "i4",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "searchScreenType",
        "T3",
        "(Ljava/lang/String;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;)V",
        "p0",
        "j4",
        "U3",
        "K3",
        "f4",
        "v1",
        "LP91/b;",
        "x1",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "y1",
        "Lorg/xbet/ui_common/utils/M;",
        "F1",
        "LxX0/a;",
        "H1",
        "Lp9/c;",
        "I1",
        "LGg/a;",
        "P1",
        "Lorg/xbet/analytics/domain/scope/I;",
        "S1",
        "LwX0/C;",
        "V1",
        "Lm8/a;",
        "b2",
        "Lek/f;",
        "v2",
        "Lfk/l;",
        "x2",
        "Lek/d;",
        "y2",
        "LHX0/e;",
        "F2",
        "LAR/a;",
        "H2",
        "LZR/a;",
        "I2",
        "Lgk0/a;",
        "P2",
        "Lfk/s;",
        "S2",
        "Lfk/o;",
        "V2",
        "LC81/f;",
        "",
        "X2",
        "J",
        "currentLastBalanceIdValue",
        "Lkotlinx/coroutines/x0;",
        "F3",
        "Lkotlinx/coroutines/x0;",
        "networkConnectionJob",
        "H3",
        "loadLastBalanceIdJob",
        "Lorg/xbet/balance/model/BalanceScreenType;",
        "Lorg/xbet/balance/model/BalanceScreenType;",
        "balanceScreenType",
        "Lkotlinx/coroutines/flow/U;",
        "Lkotlinx/coroutines/flow/U;",
        "showErrorMutableSharedFlow",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "H4",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "showFavoritesErrorMutableSharedFlow",
        "Lkotlinx/coroutines/flow/V;",
        "X4",
        "Lkotlinx/coroutines/flow/V;",
        "accountBalanceMutableStateFlow",
        "",
        "v5",
        "Z",
        "N3",
        "()Z",
        "a4",
        "(Z)V",
        "dataLoaded",
        "w5",
        "O3",
        "b4",
        "lastConnection",
        "Lkotlinx/coroutines/CoroutineExceptionHandler;",
        "x5",
        "Lkotlinx/coroutines/CoroutineExceptionHandler;",
        "M3",
        "()Lkotlinx/coroutines/CoroutineExceptionHandler;",
        "coroutineErrorHandler",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final F1:LxX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:LAR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public F3:Lkotlinx/coroutines/x0;

.field public final H1:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:LZR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public H3:Lkotlinx/coroutines/x0;

.field public final H4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:LGg/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lgk0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I3:Lorg/xbet/balance/model/BalanceScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lorg/xbet/analytics/domain/scope/I;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lfk/s;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:Lfk/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S3:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:LC81/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public X2:J

.field public final X4:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:Lek/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:LP91/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lfk/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public volatile v5:Z

.field public volatile w5:Z

.field public final x1:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lek/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x5:Lkotlinx/coroutines/CoroutineExceptionHandler;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LP91/b;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LxX0/a;Lp9/c;LGg/a;Lorg/xbet/analytics/domain/scope/I;LwX0/C;Lm8/a;Lek/f;Lfk/l;Lek/d;LHX0/e;LAR/a;LZR/a;Lgk0/a;Lfk/s;Lfk/o;LC81/f;)V
    .locals 0
    .param p1    # LP91/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LGg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/analytics/domain/scope/I;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lek/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lfk/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lek/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LAR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # LZR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lgk0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lfk/s;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lfk/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # LC81/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->v1:LP91/b;

    .line 3
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->x1:Lorg/xbet/ui_common/utils/internet/a;

    .line 4
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->y1:Lorg/xbet/ui_common/utils/M;

    .line 5
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->F1:LxX0/a;

    .line 6
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->H1:Lp9/c;

    .line 7
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->I1:LGg/a;

    .line 8
    iput-object p7, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->P1:Lorg/xbet/analytics/domain/scope/I;

    .line 9
    iput-object p8, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->S1:LwX0/C;

    .line 10
    iput-object p9, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->V1:Lm8/a;

    .line 11
    iput-object p10, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->b2:Lek/f;

    .line 12
    iput-object p11, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->v2:Lfk/l;

    .line 13
    iput-object p12, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->x2:Lek/d;

    .line 14
    iput-object p13, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->y2:LHX0/e;

    .line 15
    iput-object p14, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->F2:LAR/a;

    .line 16
    iput-object p15, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->H2:LZR/a;

    move-object/from16 p1, p16

    .line 17
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->I2:Lgk0/a;

    move-object/from16 p1, p17

    .line 18
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->P2:Lfk/s;

    move-object/from16 p1, p18

    .line 19
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->S2:Lfk/o;

    move-object/from16 p1, p19

    .line 20
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->V2:LC81/f;

    .line 21
    sget-object p1, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->I3:Lorg/xbet/balance/model/BalanceScreenType;

    const/4 p1, 0x0

    const/4 p2, 0x7

    const/4 p3, 0x0

    .line 22
    invoke-static {p3, p3, p1, p2, p1}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    move-result-object p1

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->S3:Lkotlinx/coroutines/flow/U;

    .line 23
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 24
    sget-object p2, Lkotlinx/coroutines/channels/BufferOverflow;->DROP_OLDEST:Lkotlinx/coroutines/channels/BufferOverflow;

    const/4 p3, 0x1

    .line 25
    invoke-direct {p1, p3, p2}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;)V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->H4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 26
    sget-object p1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$a;->a:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$a;

    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object p1

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 27
    iput-boolean p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->w5:Z

    .line 28
    sget-object p1, Lkotlinx/coroutines/CoroutineExceptionHandler;->Y3:Lkotlinx/coroutines/CoroutineExceptionHandler$a;

    new-instance p2, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$b;

    invoke-direct {p2, p1, p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$b;-><init>(Lkotlinx/coroutines/CoroutineExceptionHandler$a;Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)V

    .line 29
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->x5:Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 30
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->h4()V

    return-void
.end method

.method public static final synthetic A3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lfk/s;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->P2:Lfk/s;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->y2:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)LC81/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->V2:LC81/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->S3:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->H4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic F3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lek/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->b2:Lek/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic G3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Ljava/lang/String;Lorg/xbet/analytics/domain/scope/DepositCallScreenType;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->X3(Ljava/lang/String;Lorg/xbet/analytics/domain/scope/DepositCallScreenType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic H3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;J)V
    .locals 0

    .line 1
    iput-wide p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->X2:J

    .line 2
    .line 3
    return-void
.end method

.method public static final V3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->y1:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/x;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/core/presentation/x;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final W3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object p1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$b;->a:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$b;

    .line 4
    .line 5
    invoke-interface {p0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final Z3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$b;->a:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$b;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->y1:Lorg/xbet/ui_common/utils/M;

    .line 9
    .line 10
    invoke-interface {p0, p1}, Lorg/xbet/ui_common/utils/M;->i(Ljava/lang/Throwable;)V

    .line 11
    .line 12
    .line 13
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 14
    .line 15
    return-object p0
.end method

.method public static final k4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic p3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->W3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->V3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->Z3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->k4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic t3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lorg/xbet/balance/model/BalanceScreenType;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->I3:Lorg/xbet/balance/model/BalanceScreenType;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Lorg/xbet/balance/model/BalanceModel;)Ljava/lang/String;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->L3(Lorg/xbet/balance/model/BalanceModel;)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lorg/xbet/ui_common/utils/M;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->y1:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lp9/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->H1:Lp9/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lfk/l;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->v2:Lfk/l;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lek/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->x2:Lek/d;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public I3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->I2:Lgk0/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lgk0/a;->invoke()Lorg/xbet/remoteconfig/domain/models/AccountSelectionStyleConfigType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, LQ91/a;->a(Lorg/xbet/remoteconfig/domain/models/AccountSelectionStyleConfigType;)I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->X(Ljava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    return-object v0
.end method

.method public final J3()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->H1:Lp9/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Lp9/c;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 10
    .line 11
    sget-object v1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$b;->a:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$b;

    .line 12
    .line 13
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    :cond_0
    return-void
.end method

.method public final K3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->F3:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const/4 v1, 0x1

    .line 6
    const/4 v2, 0x0

    .line 7
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public final L3(Lorg/xbet/balance/model/BalanceModel;)Ljava/lang/String;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->I2:Lgk0/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lgk0/a;->invoke()Lorg/xbet/remoteconfig/domain/models/AccountSelectionStyleConfigType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sget-object v1, Lorg/xbet/remoteconfig/domain/models/AccountSelectionStyleConfigType;->SECONDARY:Lorg/xbet/remoteconfig/domain/models/AccountSelectionStyleConfigType;

    .line 8
    .line 9
    if-ne v0, v1, :cond_0

    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->y2:LHX0/e;

    .line 12
    .line 13
    sget v0, Lpb/k;->all_balances:I

    .line 14
    .line 15
    const/4 v1, 0x0

    .line 16
    new-array v1, v1, [Ljava/lang/Object;

    .line 17
    .line 18
    invoke-interface {p1, v0, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    return-object p1

    .line 23
    :cond_0
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getAlias()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 28
    .line 29
    .line 30
    move-result v0

    .line 31
    if-lez v0, :cond_1

    .line 32
    .line 33
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getAlias()Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    return-object p1

    .line 38
    :cond_1
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getAccountName()Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    return-object p1
.end method

.method public final M3()Lkotlinx/coroutines/CoroutineExceptionHandler;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->x5:Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 2
    .line 3
    return-object v0
.end method

.method public final N3()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->v5:Z

    .line 2
    .line 3
    return v0
.end method

.method public final O3()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->w5:Z

    .line 2
    .line 3
    return v0
.end method

.method public final P3()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->S3:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->d(Lkotlinx/coroutines/flow/U;)Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final Q3(Ljava/lang/Throwable;Ljava/lang/String;)V
    .locals 6
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 p2, 0x0

    .line 2
    iput-boolean p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->v5:Z

    .line 3
    .line 4
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    new-instance v3, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$handleCustomError$1;

    .line 9
    .line 10
    const/4 p2, 0x0

    .line 11
    invoke-direct {v3, p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$handleCustomError$1;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)V

    .line 12
    .line 13
    .line 14
    const/4 v4, 0x3

    .line 15
    const/4 v5, 0x0

    .line 16
    const/4 v1, 0x0

    .line 17
    const/4 v2, 0x0

    .line 18
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 19
    .line 20
    .line 21
    return-void
.end method

.method public R3()V
    .locals 0

    .line 1
    return-void
.end method

.method public S3()V
    .locals 0

    .line 1
    return-void
.end method

.method public final T3(Ljava/lang/String;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;)V
    .locals 16
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v15, v0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->v1:LP91/b;

    .line 4
    .line 5
    new-instance v1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 6
    .line 7
    sget-object v6, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorSearch;->INSTANCE:Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorSearch;

    .line 8
    .line 9
    const/16 v13, 0xe7

    .line 10
    .line 11
    const/4 v14, 0x0

    .line 12
    const/4 v2, 0x0

    .line 13
    const/4 v3, 0x0

    .line 14
    const-wide/16 v4, 0x0

    .line 15
    .line 16
    const-wide/16 v8, 0x0

    .line 17
    .line 18
    const-wide/16 v10, 0x0

    .line 19
    .line 20
    const/4 v12, 0x0

    .line 21
    move-object/from16 v7, p2

    .line 22
    .line 23
    invoke-direct/range {v1 .. v14}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {v15, v1}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 27
    .line 28
    .line 29
    sget-object v1, Lorg/xbet/analytics/domain/scope/search/SearchScreenType;->UNKNOWN:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 30
    .line 31
    if-eq v7, v1, :cond_0

    .line 32
    .line 33
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->I1:LGg/a;

    .line 34
    .line 35
    invoke-virtual {v1, v7}, LGg/a;->b(Lorg/xbet/analytics/domain/scope/search/SearchScreenType;)V

    .line 36
    .line 37
    .line 38
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->H2:LZR/a;

    .line 39
    .line 40
    invoke-virtual {v7}, Lorg/xbet/analytics/domain/scope/search/SearchScreenType;->getSearchScreenValue()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object v2

    .line 44
    move-object/from16 v3, p1

    .line 45
    .line 46
    invoke-interface {v1, v3, v2}, LZR/a;->a(Ljava/lang/String;Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    :cond_0
    return-void
.end method

.method public final U3(Ljava/lang/String;Lorg/xbet/analytics/domain/scope/DepositCallScreenType;)V
    .locals 11
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/analytics/domain/scope/DepositCallScreenType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->H3:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    const/4 v2, 0x1

    .line 7
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->V1:Lm8/a;

    .line 15
    .line 16
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 17
    .line 18
    .line 19
    move-result-object v6

    .line 20
    new-instance v4, Lorg/xplatform/aggregator/impl/core/presentation/u;

    .line 21
    .line 22
    invoke-direct {v4, p0}, Lorg/xplatform/aggregator/impl/core/presentation/u;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)V

    .line 23
    .line 24
    .line 25
    new-instance v8, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$openPaymentScreen$2;

    .line 26
    .line 27
    invoke-direct {v8, p0, p1, p2, v1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$openPaymentScreen$2;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Ljava/lang/String;Lorg/xbet/analytics/domain/scope/DepositCallScreenType;Lkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    const/16 v9, 0xa

    .line 31
    .line 32
    const/4 v10, 0x0

    .line 33
    const/4 v5, 0x0

    .line 34
    const/4 v7, 0x0

    .line 35
    invoke-static/range {v3 .. v10}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->H3:Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    return-void
.end method

.method public final X3(Ljava/lang/String;Lorg/xbet/analytics/domain/scope/DepositCallScreenType;)V
    .locals 3

    .line 1
    sget-object v0, Lorg/xbet/analytics/domain/scope/DepositCallScreenType;->UNKNOWN:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 2
    .line 3
    if-eq p2, v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->P1:Lorg/xbet/analytics/domain/scope/I;

    .line 6
    .line 7
    invoke-virtual {v0, p2}, Lorg/xbet/analytics/domain/scope/I;->d(Lorg/xbet/analytics/domain/scope/DepositCallScreenType;)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->F2:LAR/a;

    .line 11
    .line 12
    invoke-virtual {p2}, Lorg/xbet/analytics/domain/scope/DepositCallScreenType;->getValue()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object p2

    .line 16
    invoke-interface {v0, p1, p2}, LAR/a;->e(Ljava/lang/String;Ljava/lang/String;)V

    .line 17
    .line 18
    .line 19
    :cond_0
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->S1:LwX0/C;

    .line 20
    .line 21
    invoke-virtual {p1}, LwX0/D;->a()LwX0/c;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    if-eqz p1, :cond_1

    .line 26
    .line 27
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->F1:LxX0/a;

    .line 28
    .line 29
    const/4 v0, 0x1

    .line 30
    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->X2:J

    .line 31
    .line 32
    invoke-interface {p2, p1, v0, v1, v2}, LxX0/a;->b(LwX0/c;ZJ)V

    .line 33
    .line 34
    .line 35
    :cond_1
    return-void
.end method

.method public final Y3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->V1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/w;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/core/presentation/w;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$refreshUserBalanceClick$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$refreshUserBalanceClick$2;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final a4(Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->v5:Z

    .line 2
    .line 3
    return-void
.end method

.method public final b4(Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->w5:Z

    .line 2
    .line 3
    return-void
.end method

.method public final c4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public abstract d4()V
.end method

.method public abstract e4(Ljava/lang/Throwable;)V
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public final f4(Ljava/lang/Throwable;)V
    .locals 7
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    .line 2
    iput-boolean v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->v5:Z

    .line 3
    .line 4
    instance-of v0, p1, Ljava/net/SocketTimeoutException;

    .line 5
    .line 6
    if-nez v0, :cond_2

    .line 7
    .line 8
    instance-of v0, p1, Ljava/net/ConnectException;

    .line 9
    .line 10
    if-nez v0, :cond_2

    .line 11
    .line 12
    instance-of v0, p1, Ljava/net/UnknownHostException;

    .line 13
    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    instance-of v0, p1, Lorg/xplatform/aggregator/api/domain/exceptions/FavoritesLimitException;

    .line 18
    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    new-instance v4, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$showError$1;

    .line 26
    .line 27
    const/4 p1, 0x0

    .line 28
    invoke-direct {v4, p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$showError$1;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v5, 0x3

    .line 32
    const/4 v6, 0x0

    .line 33
    const/4 v2, 0x0

    .line 34
    const/4 v3, 0x0

    .line 35
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 36
    .line 37
    .line 38
    return-void

    .line 39
    :cond_1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->e4(Ljava/lang/Throwable;)V

    .line 40
    .line 41
    .line 42
    return-void

    .line 43
    :cond_2
    :goto_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->d4()V

    .line 44
    .line 45
    .line 46
    return-void
.end method

.method public final g4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->H4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->S2:Lfk/o;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->I3:Lorg/xbet/balance/model/BalanceScreenType;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lfk/o;->a(Lorg/xbet/balance/model/BalanceScreenType;)Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$2;

    .line 20
    .line 21
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$2;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 22
    .line 23
    .line 24
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$3;

    .line 29
    .line 30
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$3;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 31
    .line 32
    .line 33
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->V1:Lm8/a;

    .line 42
    .line 43
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 52
    .line 53
    .line 54
    return-void
.end method

.method public final i4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->F3:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->x1:Lorg/xbet/ui_common/utils/internet/a;

    .line 14
    .line 15
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToConnectionState$1;

    .line 20
    .line 21
    const/4 v2, 0x0

    .line 22
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToConnectionState$1;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->V1:Lm8/a;

    .line 34
    .line 35
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 36
    .line 37
    .line 38
    move-result-object v2

    .line 39
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->F3:Lkotlinx/coroutines/x0;

    .line 48
    .line 49
    return-void
.end method

.method public final j4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->V1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/v;

    .line 12
    .line 13
    invoke-direct {v1}, Lorg/xplatform/aggregator/impl/core/presentation/v;-><init>()V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateDailyTasks$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateDailyTasks$2;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final p0()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$1;

    .line 6
    .line 7
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->y1:Lorg/xbet/ui_common/utils/M;

    .line 8
    .line 9
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    new-instance v5, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    invoke-direct {v5, p0, v2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    const/16 v6, 0xe

    .line 19
    .line 20
    const/4 v7, 0x0

    .line 21
    const/4 v3, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method
