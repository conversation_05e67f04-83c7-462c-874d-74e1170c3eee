.class public final Lorg/xbet/coupon/impl/promocode/presentation/adapter/SelectPromoCodeViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a/\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lkotlin/Function1;",
        "",
        "",
        "itemClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "(Lkotlin/jvm/functions/Function1;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/coupon/impl/promocode/presentation/adapter/SelectPromoCodeViewHolderKt;->g(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/coupon/impl/promocode/presentation/adapter/SelectPromoCodeViewHolderKt;->i(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/coupon/impl/promocode/presentation/adapter/SelectPromoCodeViewHolderKt;->h(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC7/y;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/coupon/impl/promocode/presentation/adapter/SelectPromoCodeViewHolderKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC7/y;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lkotlin/jvm/functions/Function1;)LA4/c;
    .locals 4
    .param p0    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LOx/b;

    .line 2
    .line 3
    invoke-direct {v0}, LOx/b;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LOx/c;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LOx/c;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/coupon/impl/promocode/presentation/adapter/SelectPromoCodeViewHolderKt$getSelectPromoCodeAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/coupon/impl/promocode/presentation/adapter/SelectPromoCodeViewHolderKt$getSelectPromoCodeAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/coupon/impl/promocode/presentation/adapter/SelectPromoCodeViewHolderKt$getSelectPromoCodeAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/coupon/impl/promocode/presentation/adapter/SelectPromoCodeViewHolderKt$getSelectPromoCodeAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC7/y;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LC7/y;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LC7/y;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LC7/y;

    .line 6
    .line 7
    invoke-virtual {v0}, LC7/y;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    new-instance v1, LOx/d;

    .line 12
    .line 13
    invoke-direct {v1, p0, p1}, LOx/d;-><init>(Lkotlin/jvm/functions/Function1;LB4/a;)V

    .line 14
    .line 15
    .line 16
    const/4 p0, 0x1

    .line 17
    const/4 v2, 0x0

    .line 18
    invoke-static {v0, v2, v1, p0, v2}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 19
    .line 20
    .line 21
    new-instance p0, LOx/e;

    .line 22
    .line 23
    invoke-direct {p0, p1}, LOx/e;-><init>(LB4/a;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 27
    .line 28
    .line 29
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 30
    .line 31
    return-object p0
.end method

.method public static final h(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LRx/a;

    .line 6
    .line 7
    invoke-virtual {p1}, LRx/a;->f()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final i(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 10

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LC7/y;

    .line 6
    .line 7
    iget-object p1, p1, LC7/y;->f:Landroid/widget/TextView;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, LRx/a;

    .line 14
    .line 15
    invoke-virtual {v0}, LRx/a;->f()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    check-cast p1, LC7/y;

    .line 27
    .line 28
    iget-object p1, p1, LC7/y;->g:Landroid/widget/TextView;

    .line 29
    .line 30
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    check-cast v0, LRx/a;

    .line 35
    .line 36
    invoke-virtual {v0}, LRx/a;->j()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    check-cast p1, LC7/y;

    .line 48
    .line 49
    iget-object p1, p1, LC7/y;->g:Landroid/widget/TextView;

    .line 50
    .line 51
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    check-cast v0, LRx/a;

    .line 56
    .line 57
    invoke-virtual {v0}, LRx/a;->j()Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    invoke-static {v0}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    .line 62
    .line 63
    .line 64
    move-result v0

    .line 65
    const/16 v1, 0x8

    .line 66
    .line 67
    const/4 v2, 0x0

    .line 68
    if-nez v0, :cond_0

    .line 69
    .line 70
    const/4 v0, 0x0

    .line 71
    goto :goto_0

    .line 72
    :cond_0
    const/16 v0, 0x8

    .line 73
    .line 74
    :goto_0
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 75
    .line 76
    .line 77
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 78
    .line 79
    .line 80
    move-result-object p1

    .line 81
    check-cast p1, LC7/y;

    .line 82
    .line 83
    iget-object p1, p1, LC7/y;->e:Landroid/widget/TextView;

    .line 84
    .line 85
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    move-result-object v0

    .line 89
    check-cast v0, LRx/a;

    .line 90
    .line 91
    invoke-virtual {v0}, LRx/a;->e()Ljava/lang/String;

    .line 92
    .line 93
    .line 94
    move-result-object v0

    .line 95
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 96
    .line 97
    .line 98
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 99
    .line 100
    .line 101
    move-result-object p1

    .line 102
    check-cast p1, LC7/y;

    .line 103
    .line 104
    iget-object p1, p1, LC7/y;->e:Landroid/widget/TextView;

    .line 105
    .line 106
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 107
    .line 108
    .line 109
    move-result-object v0

    .line 110
    check-cast v0, LRx/a;

    .line 111
    .line 112
    invoke-virtual {v0}, LRx/a;->e()Ljava/lang/String;

    .line 113
    .line 114
    .line 115
    move-result-object v0

    .line 116
    invoke-static {v0}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    .line 117
    .line 118
    .line 119
    move-result v0

    .line 120
    if-nez v0, :cond_1

    .line 121
    .line 122
    const/4 v0, 0x0

    .line 123
    goto :goto_1

    .line 124
    :cond_1
    const/16 v0, 0x8

    .line 125
    .line 126
    :goto_1
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 127
    .line 128
    .line 129
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 130
    .line 131
    .line 132
    move-result-object p1

    .line 133
    check-cast p1, LC7/y;

    .line 134
    .line 135
    iget-object p1, p1, LC7/y;->i:Landroid/widget/TextView;

    .line 136
    .line 137
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 138
    .line 139
    .line 140
    move-result-object v0

    .line 141
    check-cast v0, LRx/a;

    .line 142
    .line 143
    invoke-virtual {v0}, LRx/a;->A()Ljava/lang/String;

    .line 144
    .line 145
    .line 146
    move-result-object v0

    .line 147
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 148
    .line 149
    .line 150
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 151
    .line 152
    .line 153
    move-result-object p1

    .line 154
    check-cast p1, LC7/y;

    .line 155
    .line 156
    iget-object p1, p1, LC7/y;->i:Landroid/widget/TextView;

    .line 157
    .line 158
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 159
    .line 160
    .line 161
    move-result-object v0

    .line 162
    check-cast v0, LRx/a;

    .line 163
    .line 164
    invoke-virtual {v0}, LRx/a;->A()Ljava/lang/String;

    .line 165
    .line 166
    .line 167
    move-result-object v0

    .line 168
    invoke-static {v0}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    .line 169
    .line 170
    .line 171
    move-result v0

    .line 172
    if-nez v0, :cond_2

    .line 173
    .line 174
    const/4 v0, 0x0

    .line 175
    goto :goto_2

    .line 176
    :cond_2
    const/16 v0, 0x8

    .line 177
    .line 178
    :goto_2
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 179
    .line 180
    .line 181
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 182
    .line 183
    .line 184
    move-result-object p1

    .line 185
    check-cast p1, LC7/y;

    .line 186
    .line 187
    iget-object p1, p1, LC7/y;->d:Landroid/widget/TextView;

    .line 188
    .line 189
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 190
    .line 191
    .line 192
    move-result-object v0

    .line 193
    check-cast v0, LRx/a;

    .line 194
    .line 195
    invoke-virtual {v0}, LRx/a;->d()Ljava/lang/String;

    .line 196
    .line 197
    .line 198
    move-result-object v0

    .line 199
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 200
    .line 201
    .line 202
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 203
    .line 204
    .line 205
    move-result-object p1

    .line 206
    check-cast p1, LC7/y;

    .line 207
    .line 208
    iget-object p1, p1, LC7/y;->d:Landroid/widget/TextView;

    .line 209
    .line 210
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 211
    .line 212
    .line 213
    move-result-object v0

    .line 214
    check-cast v0, LRx/a;

    .line 215
    .line 216
    invoke-virtual {v0}, LRx/a;->d()Ljava/lang/String;

    .line 217
    .line 218
    .line 219
    move-result-object v0

    .line 220
    invoke-static {v0}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    .line 221
    .line 222
    .line 223
    move-result v0

    .line 224
    if-nez v0, :cond_3

    .line 225
    .line 226
    const/4 v0, 0x0

    .line 227
    goto :goto_3

    .line 228
    :cond_3
    const/16 v0, 0x8

    .line 229
    .line 230
    :goto_3
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 231
    .line 232
    .line 233
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 234
    .line 235
    .line 236
    move-result-object p1

    .line 237
    check-cast p1, LC7/y;

    .line 238
    .line 239
    iget-object p1, p1, LC7/y;->h:Landroid/widget/TextView;

    .line 240
    .line 241
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 242
    .line 243
    .line 244
    move-result-object v0

    .line 245
    check-cast v0, LRx/a;

    .line 246
    .line 247
    invoke-virtual {v0}, LRx/a;->s()Ljava/lang/String;

    .line 248
    .line 249
    .line 250
    move-result-object v0

    .line 251
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 252
    .line 253
    .line 254
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 255
    .line 256
    .line 257
    move-result-object p1

    .line 258
    check-cast p1, LC7/y;

    .line 259
    .line 260
    iget-object p1, p1, LC7/y;->h:Landroid/widget/TextView;

    .line 261
    .line 262
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 263
    .line 264
    .line 265
    move-result-object v0

    .line 266
    check-cast v0, LRx/a;

    .line 267
    .line 268
    invoke-virtual {v0}, LRx/a;->s()Ljava/lang/String;

    .line 269
    .line 270
    .line 271
    move-result-object v0

    .line 272
    invoke-static {v0}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    .line 273
    .line 274
    .line 275
    move-result v0

    .line 276
    if-nez v0, :cond_4

    .line 277
    .line 278
    const/4 v0, 0x0

    .line 279
    goto :goto_4

    .line 280
    :cond_4
    const/16 v0, 0x8

    .line 281
    .line 282
    :goto_4
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 283
    .line 284
    .line 285
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 286
    .line 287
    .line 288
    move-result-object p1

    .line 289
    check-cast p1, LRx/a;

    .line 290
    .line 291
    invoke-virtual {p1}, LRx/a;->s()Ljava/lang/String;

    .line 292
    .line 293
    .line 294
    move-result-object p1

    .line 295
    sget-object v3, Ll8/b;->a:Ll8/b;

    .line 296
    .line 297
    iget-object v0, p0, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 298
    .line 299
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 300
    .line 301
    .line 302
    move-result-object v0

    .line 303
    invoke-static {v0}, Landroid/text/format/DateFormat;->is24HourFormat(Landroid/content/Context;)Z

    .line 304
    .line 305
    .line 306
    move-result v4

    .line 307
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 308
    .line 309
    .line 310
    move-result-object v0

    .line 311
    check-cast v0, LRx/a;

    .line 312
    .line 313
    invoke-virtual {v0}, LRx/a;->o()J

    .line 314
    .line 315
    .line 316
    move-result-wide v5

    .line 317
    const/4 v8, 0x4

    .line 318
    const/4 v9, 0x0

    .line 319
    const/4 v7, 0x0

    .line 320
    invoke-static/range {v3 .. v9}, Ll8/b;->z(Ll8/b;ZJLjava/lang/String;ILjava/lang/Object;)Ljava/lang/String;

    .line 321
    .line 322
    .line 323
    move-result-object v0

    .line 324
    new-instance v3, Ljava/lang/StringBuilder;

    .line 325
    .line 326
    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 327
    .line 328
    .line 329
    invoke-virtual {v3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 330
    .line 331
    .line 332
    const-string p1, " "

    .line 333
    .line 334
    invoke-virtual {v3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 335
    .line 336
    .line 337
    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 338
    .line 339
    .line 340
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 341
    .line 342
    .line 343
    move-result-object p1

    .line 344
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 345
    .line 346
    .line 347
    move-result-object v0

    .line 348
    check-cast v0, LC7/y;

    .line 349
    .line 350
    iget-object v0, v0, LC7/y;->h:Landroid/widget/TextView;

    .line 351
    .line 352
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 353
    .line 354
    .line 355
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 356
    .line 357
    .line 358
    move-result-object p1

    .line 359
    check-cast p1, LC7/y;

    .line 360
    .line 361
    iget-object p1, p1, LC7/y;->h:Landroid/widget/TextView;

    .line 362
    .line 363
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 364
    .line 365
    .line 366
    move-result-object p0

    .line 367
    check-cast p0, LRx/a;

    .line 368
    .line 369
    invoke-virtual {p0}, LRx/a;->o()J

    .line 370
    .line 371
    .line 372
    move-result-wide v3

    .line 373
    const-wide/16 v5, 0x0

    .line 374
    .line 375
    cmp-long p0, v3, v5

    .line 376
    .line 377
    if-eqz p0, :cond_5

    .line 378
    .line 379
    const/4 p0, 0x1

    .line 380
    goto :goto_5

    .line 381
    :cond_5
    const/4 p0, 0x0

    .line 382
    :goto_5
    if-eqz p0, :cond_6

    .line 383
    .line 384
    const/4 v1, 0x0

    .line 385
    :cond_6
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 386
    .line 387
    .line 388
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 389
    .line 390
    return-object p0
.end method
