.class public final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0007\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J-\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00042\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u000cR\u0014\u0010\u000e\u001a\u00020\r8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0012R\u0014\u0010\u0013\u001a\u00020\u00108\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0012R\u0014\u0010\u0014\u001a\u00020\u00108\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0012R\u0014\u0010\u0015\u001a\u00020\u00108\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0012R\u0014\u0010\u0016\u001a\u00020\u00108\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0012\u00a8\u0006\u0017"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$a;",
        "",
        "<init>",
        "()V",
        "",
        "bonusesCount",
        "freeSpinsCount",
        "giftTypeId",
        "",
        "afterAuth",
        "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;",
        "a",
        "(IIIZ)Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;",
        "",
        "LOTTIE_RETRY_COUNT_DOWN_TIME_MILLIS",
        "J",
        "",
        "REQUEST_REFUSE_BONUS",
        "Ljava/lang/String;",
        "BUNDLE_AFTER_AUTH",
        "BUNDLE_GIFT_TYPE_ID",
        "BUNDLE_FREE_SPINS_COUNT",
        "BUNDLE_BONUSES_COUNT",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(IIIZ)Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->G3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;I)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->H3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;I)V

    .line 10
    .line 11
    .line 12
    invoke-static {v0, p3}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->I3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;I)V

    .line 13
    .line 14
    .line 15
    invoke-static {v0, p4}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->F3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Z)V

    .line 16
    .line 17
    .line 18
    return-object v0
.end method
