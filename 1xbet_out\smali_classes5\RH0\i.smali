.class public final LRH0/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LPH0/b;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0008\u0000\u0018\u00002\u00020\u0001B\t\u0008\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J-\u0010\u000c\u001a\u00020\u000b2\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u001f\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u001f\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u0010\u0010\u000fJ\'\u0010\u0012\u001a\u00020\u000b2\u0006\u0010\u0011\u001a\u00020\u00072\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u001f\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u0014\u0010\u000fJ\u001f\u0010\u0015\u001a\u00020\u000b2\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u0015\u0010\u000f\u00a8\u0006\u0016"
    }
    d2 = {
        "LRH0/i;",
        "LPH0/b;",
        "<init>",
        "()V",
        "",
        "LLK0/a;",
        "referees",
        "",
        "gameId",
        "",
        "sportId",
        "Lq4/q;",
        "a",
        "(Ljava/util/List;Ljava/lang/String;J)Lq4/q;",
        "b",
        "(Ljava/lang/String;J)Lq4/q;",
        "c",
        "playerId",
        "f",
        "(Ljava/lang/String;Ljava/lang/String;J)Lq4/q;",
        "e",
        "d",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(Ljava/util/List;Ljava/lang/String;J)Lq4/q;
    .locals 8
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LLK0/a;",
            ">;",
            "Ljava/lang/String;",
            "J)",
            "Lq4/q;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x1

    .line 6
    if-ne v0, v1, :cond_0

    .line 7
    .line 8
    new-instance v2, LeJ0/a;

    .line 9
    .line 10
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->x0(Ljava/util/List;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    check-cast p1, LLK0/a;

    .line 15
    .line 16
    invoke-virtual {p1}, LLK0/a;->a()Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v3

    .line 20
    sget-object v7, Lorg/xbet/statistic/player/model/PlayerScreenType;->REFEREE:Lorg/xbet/statistic/player/model/PlayerScreenType;

    .line 21
    .line 22
    move-object v6, p2

    .line 23
    move-wide v4, p3

    .line 24
    invoke-direct/range {v2 .. v7}, LeJ0/a;-><init>(Ljava/lang/String;JLjava/lang/String;Lorg/xbet/statistic/player/model/PlayerScreenType;)V

    .line 25
    .line 26
    .line 27
    return-object v2

    .line 28
    :cond_0
    move-object v6, p2

    .line 29
    move-wide v4, p3

    .line 30
    new-instance p1, LeJ0/b;

    .line 31
    .line 32
    invoke-direct {p1, v6, v4, v5}, LeJ0/b;-><init>(Ljava/lang/String;J)V

    .line 33
    .line 34
    .line 35
    return-object p1
.end method

.method public b(Ljava/lang/String;J)Lq4/q;
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LdK0/a;

    .line 2
    .line 3
    invoke-direct {v0, p1, p2, p3}, LdK0/a;-><init>(Ljava/lang/String;J)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public c(Ljava/lang/String;J)Lq4/q;
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LlI0/a;

    .line 2
    .line 3
    invoke-direct {v0, p1, p2, p3}, LlI0/a;-><init>(Ljava/lang/String;J)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public d(Ljava/lang/String;J)Lq4/q;
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LAJ0/a;

    .line 2
    .line 3
    invoke-direct {v0, p1, p2, p3}, LAJ0/a;-><init>(Ljava/lang/String;J)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public e(Ljava/lang/String;J)Lq4/q;
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/j;

    .line 2
    .line 3
    invoke-direct {v0, p1, p2, p3}, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/j;-><init>(Ljava/lang/String;J)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public f(Ljava/lang/String;Ljava/lang/String;J)Lq4/q;
    .locals 6
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LeJ0/a;

    .line 2
    .line 3
    sget-object v5, Lorg/xbet/statistic/player/model/PlayerScreenType;->PLAYER:Lorg/xbet/statistic/player/model/PlayerScreenType;

    .line 4
    .line 5
    move-object v1, p1

    .line 6
    move-object v4, p2

    .line 7
    move-wide v2, p3

    .line 8
    invoke-direct/range {v0 .. v5}, LeJ0/a;-><init>(Ljava/lang/String;JLjava/lang/String;Lorg/xbet/statistic/player/model/PlayerScreenType;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method
