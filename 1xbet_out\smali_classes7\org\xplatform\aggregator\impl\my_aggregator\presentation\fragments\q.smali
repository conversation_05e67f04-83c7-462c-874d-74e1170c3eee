.class public final synthetic Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/q;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;

.field public final synthetic b:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/q;->a:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/q;->b:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/q;->a:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/q;->b:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;

    check-cast p1, Landroidx/paging/f;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->n3(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;Landroidx/paging/f;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
