.class public final synthetic LL1/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/util/concurrent/Executor;


# instance fields
.field public final synthetic a:Lt1/n;


# direct methods
.method public synthetic constructor <init>(Lt1/n;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LL1/k;->a:Lt1/n;

    return-void
.end method


# virtual methods
.method public final execute(Ljava/lang/Runnable;)V
    .locals 1

    .line 1
    iget-object v0, p0, LL1/k;->a:Lt1/n;

    invoke-interface {v0, p1}, Lt1/n;->i(Ljava/lang/Runnable;)Z

    return-void
.end method
