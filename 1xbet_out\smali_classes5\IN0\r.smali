.class public final synthetic LIN0/r;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:I


# direct methods
.method public synthetic constructor <init>(I)V
    .locals 0

    .line 1
    invoke-direct {p0}, <PERSON>java/lang/Object;-><init>()V

    iput p1, p0, LIN0/r;->a:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget v0, p0, LIN0/r;->a:I

    check-cast p1, Landroidx/compose/foundation/lazy/t;

    invoke-static {v0, p1}, LIN0/t;->e(ILandroidx/compose/foundation/lazy/t;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
