.class public final synthetic Lc21/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilled;

.field public final synthetic b:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabsFilledGroup;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilled;Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabsFilledGroup;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lc21/d;->a:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilled;

    iput-object p2, p0, Lc21/d;->b:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabsFilledGroup;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lc21/d;->a:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilled;

    iget-object v1, p0, Lc21/d;->b:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabsFilledGroup;

    invoke-static {v0, v1, p1}, Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabsFilledGroup;->c(Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilled;Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabsFilledGroup;Landroid/view/View;)V

    return-void
.end method
