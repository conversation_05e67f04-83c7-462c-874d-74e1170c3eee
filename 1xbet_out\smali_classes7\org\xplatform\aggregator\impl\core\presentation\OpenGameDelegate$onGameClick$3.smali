.class final Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.core.presentation.OpenGameDelegate$onGameClick$3"
    f = "OpenGameDelegate.kt"
    l = {
        0x3d
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->t(JILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $callOnError:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic $game:J

.field final synthetic $subCategoryId:I

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;


# direct methods
.method public constructor <init>(JLorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;ILkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
            "I",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;",
            ">;)V"
        }
    .end annotation

    iput-wide p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->$game:J

    iput-object p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    iput p4, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->$subCategoryId:I

    iput-object p5, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->$callOnError:Lkotlin/jvm/functions/Function1;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p6}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;

    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->$game:J

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    iget v4, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->$subCategoryId:I

    iget-object v5, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->$callOnError:Lkotlin/jvm/functions/Function1;

    move-object v6, p2

    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;-><init>(JLorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;ILkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->$game:J

    .line 28
    .line 29
    const-wide/high16 v5, -0x8000000000000000L

    .line 30
    .line 31
    cmp-long p1, v3, v5

    .line 32
    .line 33
    if-eqz p1, :cond_3

    .line 34
    .line 35
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 36
    .line 37
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->i(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)Le81/d;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->$game:J

    .line 42
    .line 43
    iput v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->label:I

    .line 44
    .line 45
    invoke-interface {p1, v3, v4, p0}, Le81/d;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    if-ne p1, v0, :cond_2

    .line 50
    .line 51
    return-object v0

    .line 52
    :cond_2
    :goto_0
    check-cast p1, Lorg/xplatform/aggregator/api/model/Game;

    .line 53
    .line 54
    if-eqz p1, :cond_3

    .line 55
    .line 56
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 57
    .line 58
    iget v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->$subCategoryId:I

    .line 59
    .line 60
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;->$callOnError:Lkotlin/jvm/functions/Function1;

    .line 61
    .line 62
    invoke-virtual {v0, p1, v1, v2}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->u(Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;)V

    .line 63
    .line 64
    .line 65
    :cond_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 66
    .line 67
    return-object p1
.end method
