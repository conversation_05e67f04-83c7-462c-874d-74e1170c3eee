.class final Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.coupon.impl.make_bet.presentation.viewmodel.MakeBetAutoViewModel$initBetSumStepInputUiModel$3"
    f = "MakeBetAutoViewModel.kt"
    l = {
        0x3d4
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->m5(Lorg/xbet/balance/model/BalanceModel;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $balance:Lorg/xbet/balance/model/BalanceModel;

.field label:I

.field final synthetic this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/balance/model/BalanceModel;Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/balance/model/BalanceModel;",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;->$balance:Lorg/xbet/balance/model/BalanceModel;

    iput-object p2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;

    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;->$balance:Lorg/xbet/balance/model/BalanceModel;

    iget-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;-><init>(Lorg/xbet/balance/model/BalanceModel;Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 28

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;->label:I

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    if-eqz v2, :cond_1

    .line 11
    .line 12
    if-ne v2, v3, :cond_0

    .line 13
    .line 14
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    move-object/from16 v2, p1

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw v1

    .line 28
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    iget-object v2, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;->$balance:Lorg/xbet/balance/model/BalanceModel;

    .line 32
    .line 33
    invoke-virtual {v2}, Lorg/xbet/balance/model/BalanceModel;->getCurrencyId()J

    .line 34
    .line 35
    .line 36
    move-result-wide v4

    .line 37
    iget-object v2, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 38
    .line 39
    invoke-static {v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->a4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/GetLimitsScenario;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    iput v3, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;->label:I

    .line 44
    .line 45
    invoke-virtual {v2, v4, v5, v0}, Lorg/xbet/coupon/impl/coupon/domain/usecases/GetLimitsScenario;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 46
    .line 47
    .line 48
    move-result-object v2

    .line 49
    if-ne v2, v1, :cond_2

    .line 50
    .line 51
    return-object v1

    .line 52
    :cond_2
    :goto_0
    check-cast v2, LSw/c;

    .line 53
    .line 54
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initBetSumStepInputUiModel$3;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 55
    .line 56
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->J3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lkotlinx/coroutines/flow/V;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    :cond_3
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object v3

    .line 64
    move-object v4, v3

    .line 65
    check-cast v4, Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    .line 66
    .line 67
    invoke-virtual {v4}, Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;->n()Z

    .line 68
    .line 69
    .line 70
    move-result v5

    .line 71
    if-eqz v5, :cond_4

    .line 72
    .line 73
    const v26, 0xbfff

    .line 74
    .line 75
    .line 76
    const/16 v27, 0x0

    .line 77
    .line 78
    const-wide/16 v5, 0x0

    .line 79
    .line 80
    const-wide/16 v7, 0x0

    .line 81
    .line 82
    const-wide/16 v9, 0x0

    .line 83
    .line 84
    const-wide/16 v11, 0x0

    .line 85
    .line 86
    const-wide/16 v13, 0x0

    .line 87
    .line 88
    const/4 v15, 0x0

    .line 89
    const/16 v16, 0x0

    .line 90
    .line 91
    const/16 v17, 0x0

    .line 92
    .line 93
    const/16 v18, 0x0

    .line 94
    .line 95
    const/16 v19, 0x0

    .line 96
    .line 97
    const/16 v20, 0x0

    .line 98
    .line 99
    const/16 v21, 0x0

    .line 100
    .line 101
    const/16 v22, 0x0

    .line 102
    .line 103
    const/16 v23, 0x0

    .line 104
    .line 105
    const/16 v24, 0x0

    .line 106
    .line 107
    const/16 v25, 0x0

    .line 108
    .line 109
    invoke-static/range {v4 .. v27}, Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;->b(Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;DDDDDLjava/lang/String;ZZLorg/xbet/coupon/impl/make_bet/presentation/model/AutoMaxUiModel;ZZZZZZZILjava/lang/Object;)Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    .line 110
    .line 111
    .line 112
    move-result-object v4

    .line 113
    goto :goto_1

    .line 114
    :cond_4
    invoke-virtual {v2}, LSw/c;->c()Ljava/lang/String;

    .line 115
    .line 116
    .line 117
    move-result-object v15

    .line 118
    const v26, 0xafde

    .line 119
    .line 120
    .line 121
    const/16 v27, 0x0

    .line 122
    .line 123
    const-wide/high16 v5, -0x4010000000000000L    # -1.0

    .line 124
    .line 125
    const-wide/16 v7, 0x0

    .line 126
    .line 127
    const-wide/16 v9, 0x0

    .line 128
    .line 129
    const-wide/16 v11, 0x0

    .line 130
    .line 131
    const-wide/16 v13, 0x0

    .line 132
    .line 133
    const/16 v16, 0x0

    .line 134
    .line 135
    const/16 v17, 0x0

    .line 136
    .line 137
    const/16 v18, 0x0

    .line 138
    .line 139
    const/16 v19, 0x0

    .line 140
    .line 141
    const/16 v20, 0x0

    .line 142
    .line 143
    const/16 v21, 0x0

    .line 144
    .line 145
    const/16 v22, 0x0

    .line 146
    .line 147
    const/16 v23, 0x0

    .line 148
    .line 149
    const/16 v24, 0x0

    .line 150
    .line 151
    const/16 v25, 0x0

    .line 152
    .line 153
    invoke-static/range {v4 .. v27}, Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;->b(Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;DDDDDLjava/lang/String;ZZLorg/xbet/coupon/impl/make_bet/presentation/model/AutoMaxUiModel;ZZZZZZZILjava/lang/Object;)Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    .line 154
    .line 155
    .line 156
    move-result-object v4

    .line 157
    :goto_1
    invoke-interface {v1, v3, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 158
    .line 159
    .line 160
    move-result v3

    .line 161
    if-eqz v3, :cond_3

    .line 162
    .line 163
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 164
    .line 165
    return-object v1
.end method
