.class public final Lorg/xbet/crystal/presentation/game/e;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lay/e;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lay/f;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LWv/b;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/u;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_info/q;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lay/a;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/d;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lay/e;",
            ">;",
            "LBc/a<",
            "Lay/f;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
            ">;",
            "LBc/a<",
            "LWv/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/u;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_info/q;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lay/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/d;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/crystal/presentation/game/e;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/crystal/presentation/game/e;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/crystal/presentation/game/e;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/crystal/presentation/game/e;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/crystal/presentation/game/e;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/crystal/presentation/game/e;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/crystal/presentation/game/e;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/crystal/presentation/game/e;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/crystal/presentation/game/e;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/crystal/presentation/game/e;->j:LBc/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/crystal/presentation/game/e;->k:LBc/a;

    .line 25
    .line 26
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/crystal/presentation/game/e;
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lay/e;",
            ">;",
            "LBc/a<",
            "Lay/f;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
            ">;",
            "LBc/a<",
            "LWv/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/u;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_info/q;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lay/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/d;",
            ">;)",
            "Lorg/xbet/crystal/presentation/game/e;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/crystal/presentation/game/e;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object/from16 v5, p4

    .line 8
    .line 9
    move-object/from16 v6, p5

    .line 10
    .line 11
    move-object/from16 v7, p6

    .line 12
    .line 13
    move-object/from16 v8, p7

    .line 14
    .line 15
    move-object/from16 v9, p8

    .line 16
    .line 17
    move-object/from16 v10, p9

    .line 18
    .line 19
    move-object/from16 v11, p10

    .line 20
    .line 21
    invoke-direct/range {v0 .. v11}, Lorg/xbet/crystal/presentation/game/e;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 22
    .line 23
    .line 24
    return-object v0
.end method

.method public static c(Lay/e;Lay/f;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;LWv/b;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/game_info/q;Lm8/a;Lay/a;LwX0/c;Lorg/xbet/core/domain/usecases/d;)Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;
    .locals 13

    .line 1
    new-instance v0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object/from16 v4, p3

    .line 7
    .line 8
    move-object/from16 v5, p4

    .line 9
    .line 10
    move-object/from16 v6, p5

    .line 11
    .line 12
    move-object/from16 v7, p6

    .line 13
    .line 14
    move-object/from16 v8, p7

    .line 15
    .line 16
    move-object/from16 v9, p8

    .line 17
    .line 18
    move-object/from16 v10, p9

    .line 19
    .line 20
    move-object/from16 v11, p10

    .line 21
    .line 22
    move-object/from16 v12, p11

    .line 23
    .line 24
    invoke-direct/range {v0 .. v12}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;-><init>(Lay/e;Lay/f;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;LWv/b;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/game_info/q;Lm8/a;Lay/a;LwX0/c;Lorg/xbet/core/domain/usecases/d;)V

    .line 25
    .line 26
    .line 27
    return-object v0
.end method


# virtual methods
.method public b(LwX0/c;)Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;
    .locals 13

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/e;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, Lay/e;

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/e;->b:LBc/a;

    .line 11
    .line 12
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    move-object v2, v0

    .line 17
    check-cast v2, Lay/f;

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/e;->c:LBc/a;

    .line 20
    .line 21
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    move-object v3, v0

    .line 26
    check-cast v3, Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;

    .line 27
    .line 28
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/e;->d:LBc/a;

    .line 29
    .line 30
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    move-object v4, v0

    .line 35
    check-cast v4, Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 36
    .line 37
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/e;->e:LBc/a;

    .line 38
    .line 39
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    move-object v5, v0

    .line 44
    check-cast v5, Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 45
    .line 46
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/e;->f:LBc/a;

    .line 47
    .line 48
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    move-object v6, v0

    .line 53
    check-cast v6, LWv/b;

    .line 54
    .line 55
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/e;->g:LBc/a;

    .line 56
    .line 57
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    move-object v7, v0

    .line 62
    check-cast v7, Lorg/xbet/core/domain/usecases/u;

    .line 63
    .line 64
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/e;->h:LBc/a;

    .line 65
    .line 66
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    move-object v8, v0

    .line 71
    check-cast v8, Lorg/xbet/core/domain/usecases/game_info/q;

    .line 72
    .line 73
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/e;->i:LBc/a;

    .line 74
    .line 75
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object v0

    .line 79
    move-object v9, v0

    .line 80
    check-cast v9, Lm8/a;

    .line 81
    .line 82
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/e;->j:LBc/a;

    .line 83
    .line 84
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    move-object v10, v0

    .line 89
    check-cast v10, Lay/a;

    .line 90
    .line 91
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/e;->k:LBc/a;

    .line 92
    .line 93
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    move-object v12, v0

    .line 98
    check-cast v12, Lorg/xbet/core/domain/usecases/d;

    .line 99
    .line 100
    move-object v11, p1

    .line 101
    invoke-static/range {v1 .. v12}, Lorg/xbet/crystal/presentation/game/e;->c(Lay/e;Lay/f;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;LWv/b;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/game_info/q;Lm8/a;Lay/a;LwX0/c;Lorg/xbet/core/domain/usecases/d;)Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    return-object p1
.end method
