.class public final Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "LHy0/b;",
        "whoWinCardClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "d",
        "(LHy0/b;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LHy0/b;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupViewHolderKt;->f(LHy0/b;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/r1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupViewHolderKt;->e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/r1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupViewHolderKt;->g(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final d(LHy0/b;)LA4/c;
    .locals 4
    .param p0    # LHy0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LHy0/b;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LJy0/k;

    .line 2
    .line 3
    invoke-direct {v0}, LJy0/k;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LJy0/l;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LJy0/l;-><init>(LHy0/b;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupViewHolderKt$groupAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupViewHolderKt$groupAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupViewHolderKt$groupAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupViewHolderKt$groupAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/r1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/r1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/r1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final f(LHy0/b;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, LGy0/a;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LGy0/a;-><init>(LHy0/b;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    check-cast p0, LGq0/r1;

    .line 11
    .line 12
    iget-object p0, p0, LGq0/r1;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 13
    .line 14
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 15
    .line 16
    .line 17
    new-instance p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupViewHolderKt$a;

    .line 18
    .line 19
    invoke-direct {p0, v0, p1, v0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupViewHolderKt$a;-><init>(LGy0/a;LB4/a;LGy0/a;LB4/a;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 23
    .line 24
    .line 25
    new-instance p0, LJy0/m;

    .line 26
    .line 27
    invoke-direct {p0, p1}, LJy0/m;-><init>(LB4/a;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {p1, p0}, LB4/a;->r(Lkotlin/jvm/functions/Function0;)V

    .line 31
    .line 32
    .line 33
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 34
    .line 35
    return-object p0
.end method

.method public static final g(LB4/a;)Lkotlin/Unit;
    .locals 7

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LIy0/e;

    .line 6
    .line 7
    invoke-virtual {v0}, LIy0/e;->e()Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    const-string v1, "null cannot be cast to non-null type android.view.ViewGroup.LayoutParams"

    .line 12
    .line 13
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    check-cast v0, LGq0/r1;

    .line 20
    .line 21
    invoke-virtual {v0}, LGq0/r1;->b()Landroidx/recyclerview/widget/RecyclerView;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-virtual {v0}, Landroid/view/View;->getRootView()Landroid/view/View;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-virtual {v0}, Landroid/view/View;->getWidth()I

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 34
    .line 35
    .line 36
    move-result-object p0

    .line 37
    check-cast p0, LGq0/r1;

    .line 38
    .line 39
    invoke-virtual {p0}, LGq0/r1;->b()Landroidx/recyclerview/widget/RecyclerView;

    .line 40
    .line 41
    .line 42
    move-result-object p0

    .line 43
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    if-eqz v2, :cond_0

    .line 48
    .line 49
    const/16 v1, 0x190

    .line 50
    .line 51
    invoke-static {v1}, Lorg/xbet/ui_common/utils/ExtensionsKt;->q(I)I

    .line 52
    .line 53
    .line 54
    move-result v1

    .line 55
    int-to-double v3, v0

    .line 56
    const-wide v5, 0x3feccccccccccccdL    # 0.9

    .line 57
    .line 58
    .line 59
    .line 60
    .line 61
    mul-double v3, v3, v5

    .line 62
    .line 63
    double-to-int v0, v3

    .line 64
    invoke-static {v1, v0}, Ljava/lang/Math;->min(II)I

    .line 65
    .line 66
    .line 67
    move-result v0

    .line 68
    iput v0, v2, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 69
    .line 70
    invoke-virtual {p0, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 71
    .line 72
    .line 73
    goto :goto_0

    .line 74
    :cond_0
    new-instance p0, Ljava/lang/NullPointerException;

    .line 75
    .line 76
    invoke-direct {p0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 77
    .line 78
    .line 79
    throw p0

    .line 80
    :cond_1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 81
    .line 82
    .line 83
    move-result-object p0

    .line 84
    check-cast p0, LGq0/r1;

    .line 85
    .line 86
    invoke-virtual {p0}, LGq0/r1;->b()Landroidx/recyclerview/widget/RecyclerView;

    .line 87
    .line 88
    .line 89
    move-result-object p0

    .line 90
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    if-eqz v0, :cond_2

    .line 95
    .line 96
    const/4 v1, -0x1

    .line 97
    iput v1, v0, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 98
    .line 99
    invoke-virtual {p0, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 100
    .line 101
    .line 102
    :goto_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 103
    .line 104
    return-object p0

    .line 105
    :cond_2
    new-instance p0, Ljava/lang/NullPointerException;

    .line 106
    .line 107
    invoke-direct {p0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 108
    .line 109
    .line 110
    throw p0
.end method
