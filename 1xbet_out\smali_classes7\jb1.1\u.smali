.class public final Ljb1/u;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00008\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001aQ\u0010\u0011\u001a\u00020\u00102\u0008\u0008\u0001\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000eH\u0000\u00a2\u0006\u0004\u0008\u0011\u0010\u0012\u00a8\u0006\u0013"
    }
    d2 = {
        "",
        "aggregatorGameCardCollectionStyle",
        "LO21/b;",
        "aggregatorProviderCardCollectionAppearanceModel",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;",
        "dSTournamentStagesCellType",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;",
        "aggregatorTournamentTimerType",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;",
        "aggregatorTournamentPrizesStyle",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;",
        "aggregatorTournamentPrizePoolStyle",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;",
        "aggregatorTournamentProgressDSStyleType",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;",
        "aggregatorTournamentRulesDSStyleType",
        "Lkb1/n;",
        "a",
        "(ILO21/b;Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;)Lkb1/n;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(ILO21/b;Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;)Lkb1/n;
    .locals 7
    .param p1    # LO21/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Llb1/b;

    .line 2
    .line 3
    new-instance v1, Ln21/c;

    .line 4
    .line 5
    invoke-direct {v1, p5}, Ln21/c;-><init>(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {v0, v1}, Llb1/b;-><init>(Ln21/b;)V

    .line 9
    .line 10
    .line 11
    new-instance p5, Llb1/m;

    .line 12
    .line 13
    new-instance v1, Le31/d;

    .line 14
    .line 15
    invoke-direct {v1, p3}, Le31/d;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)V

    .line 16
    .line 17
    .line 18
    invoke-direct {p5, v1}, Llb1/m;-><init>(Le31/b;)V

    .line 19
    .line 20
    .line 21
    new-instance p3, Llb1/e;

    .line 22
    .line 23
    new-instance v1, Lq21/e;

    .line 24
    .line 25
    invoke-direct {v1, p6}, Lq21/e;-><init>(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;)V

    .line 26
    .line 27
    .line 28
    invoke-direct {p3, v1}, Llb1/e;-><init>(Lq21/b;)V

    .line 29
    .line 30
    .line 31
    new-instance p6, Llb1/c;

    .line 32
    .line 33
    new-instance v1, LV21/e;

    .line 34
    .line 35
    invoke-direct {v1, p4}, LV21/e;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;)V

    .line 36
    .line 37
    .line 38
    invoke-direct {p6, v1}, Llb1/c;-><init>(LV21/d;)V

    .line 39
    .line 40
    .line 41
    new-instance p4, Llb1/o;

    .line 42
    .line 43
    invoke-direct {p4}, Llb1/o;-><init>()V

    .line 44
    .line 45
    .line 46
    new-instance v1, Llb1/r;

    .line 47
    .line 48
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    const/4 v3, 0x1

    .line 53
    invoke-direct {v1, p0, v2, v3}, Llb1/r;-><init>(ILjava/util/List;Z)V

    .line 54
    .line 55
    .line 56
    new-instance p0, Llb1/k;

    .line 57
    .line 58
    new-instance v2, Lb31/c;

    .line 59
    .line 60
    invoke-direct {v2, p2}, Lb31/c;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;)V

    .line 61
    .line 62
    .line 63
    invoke-direct {p0, v2}, Llb1/k;-><init>(Lb31/b;)V

    .line 64
    .line 65
    .line 66
    new-instance p2, Llb1/i;

    .line 67
    .line 68
    new-instance v2, LZ21/c;

    .line 69
    .line 70
    invoke-direct {v2, p7}, LZ21/c;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;)V

    .line 71
    .line 72
    .line 73
    invoke-direct {p2, v2}, Llb1/i;-><init>(LZ21/b;)V

    .line 74
    .line 75
    .line 76
    new-instance p7, Llb1/o;

    .line 77
    .line 78
    invoke-direct {p7}, Llb1/o;-><init>()V

    .line 79
    .line 80
    .line 81
    new-instance v2, Llb1/g;

    .line 82
    .line 83
    new-instance v4, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a$c;

    .line 84
    .line 85
    const/4 v5, 0x0

    .line 86
    const/4 v6, 0x2

    .line 87
    invoke-direct {v4, p1, v5, v6, v5}, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a$c;-><init>(LO21/b;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 88
    .line 89
    .line 90
    invoke-direct {v2, v4}, Llb1/g;-><init>(Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a;)V

    .line 91
    .line 92
    .line 93
    const/16 p1, 0xa

    .line 94
    .line 95
    new-array p1, p1, [LVX0/i;

    .line 96
    .line 97
    const/4 v4, 0x0

    .line 98
    aput-object v0, p1, v4

    .line 99
    .line 100
    aput-object p5, p1, v3

    .line 101
    .line 102
    aput-object p3, p1, v6

    .line 103
    .line 104
    const/4 p3, 0x3

    .line 105
    aput-object p6, p1, p3

    .line 106
    .line 107
    const/4 p3, 0x4

    .line 108
    aput-object p4, p1, p3

    .line 109
    .line 110
    const/4 p3, 0x5

    .line 111
    aput-object v1, p1, p3

    .line 112
    .line 113
    const/4 p3, 0x6

    .line 114
    aput-object p0, p1, p3

    .line 115
    .line 116
    const/4 p0, 0x7

    .line 117
    aput-object p2, p1, p0

    .line 118
    .line 119
    const/16 p0, 0x8

    .line 120
    .line 121
    aput-object p7, p1, p0

    .line 122
    .line 123
    const/16 p0, 0x9

    .line 124
    .line 125
    aput-object v2, p1, p0

    .line 126
    .line 127
    invoke-static {p1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 128
    .line 129
    .line 130
    move-result-object p0

    .line 131
    new-instance p1, Lkb1/n;

    .line 132
    .line 133
    invoke-direct {p1, p0, v4}, Lkb1/n;-><init>(Ljava/util/List;Z)V

    .line 134
    .line 135
    .line 136
    return-object p1
.end method
