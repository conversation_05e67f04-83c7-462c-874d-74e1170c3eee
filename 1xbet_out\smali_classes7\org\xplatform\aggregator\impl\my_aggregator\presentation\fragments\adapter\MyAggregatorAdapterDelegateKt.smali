.class public final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001ai\u0010\r\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000c0\u000b0\n2\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u00002\u001e\u0010\u0007\u001a\u001a\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00030\u00052\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00030\u0008H\u0000\u00a2\u0006\u0004\u0008\r\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "Lkotlin/Function2;",
        "Lra1/c;",
        "",
        "",
        "onGameClicked",
        "Lkotlin/Function3;",
        "",
        "onFavoriteClicked",
        "Lkotlin/Function1;",
        "clickAction",
        "LA4/c;",
        "",
        "LVX0/i;",
        "j",
        "(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;)LA4/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt;->s(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt;->m(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LOc/n;LB4/a;LN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt;->r(LOc/n;LB4/a;LN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/m1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt;->k(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/m1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Lkotlin/jvm/functions/Function2;LB4/a;LN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt;->q(Lkotlin/jvm/functions/Function2;LB4/a;LN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(LVX0/i;Ljava/util/List;I)Z
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt;->l(LVX0/i;Ljava/util/List;I)Z

    move-result p0

    return p0
.end method

.method public static final synthetic g(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt;->n(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic h(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt;->o(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic i(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt;->p(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final j(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;)LA4/c;
    .locals 3
    .param p0    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LOc/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Lra1/c;",
            "-",
            "Ljava/lang/Long;",
            "Lkotlin/Unit;",
            ">;",
            "LOc/n<",
            "-",
            "Lra1/c;",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lra1/c;",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lpa1/k;

    .line 2
    .line 3
    invoke-direct {v0}, Lpa1/k;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lpa1/l;

    .line 7
    .line 8
    invoke-direct {v1}, Lpa1/l;-><init>()V

    .line 9
    .line 10
    .line 11
    new-instance v2, Lpa1/m;

    .line 12
    .line 13
    invoke-direct {v2, p0, p1, p2}, Lpa1/m;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt$gamesCategoryAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;->INSTANCE:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt$gamesCategoryAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 17
    .line 18
    new-instance p1, LB4/b;

    .line 19
    .line 20
    invoke-direct {p1, v0, v1, v2, p0}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object p1
.end method

.method public static final k(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/m1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/m1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/m1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final l(LVX0/i;Ljava/util/List;I)Z
    .locals 1

    .line 1
    :try_start_0
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 2
    .line 3
    invoke-static {p1, p2}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    check-cast p1, LVX0/i;

    .line 8
    .line 9
    if-nez p1, :cond_0

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    move-object p0, p1

    .line 13
    :goto_0
    nop

    .line 14
    instance-of p1, p0, Lra1/b;

    .line 15
    .line 16
    if-eqz p1, :cond_1

    .line 17
    .line 18
    check-cast p0, Lra1/b;

    .line 19
    .line 20
    invoke-virtual {p0}, Lra1/b;->s()I

    .line 21
    .line 22
    .line 23
    const/4 p0, 0x1

    .line 24
    goto :goto_1

    .line 25
    :cond_1
    const/4 p0, 0x0

    .line 26
    :goto_1
    invoke-static {p0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 27
    .line 28
    .line 29
    move-result-object p0

    .line 30
    invoke-static {p0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 34
    goto :goto_2

    .line 35
    :catchall_0
    move-exception p0

    .line 36
    sget-object p1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 37
    .line 38
    invoke-static {p0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object p0

    .line 42
    invoke-static {p0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    move-result-object p0

    .line 46
    :goto_2
    sget-object p1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 47
    .line 48
    invoke-static {p0}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 49
    .line 50
    .line 51
    move-result p2

    .line 52
    if-eqz p2, :cond_2

    .line 53
    .line 54
    move-object p0, p1

    .line 55
    :cond_2
    check-cast p0, Ljava/lang/Boolean;

    .line 56
    .line 57
    invoke-virtual {p0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 58
    .line 59
    .line 60
    move-result p0

    .line 61
    return p0
.end method

.method public static final m(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p3}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LS91/m1;

    .line 6
    .line 7
    iget-object v0, v0, LS91/m1;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 8
    .line 9
    new-instance v1, Lpa1/n;

    .line 10
    .line 11
    invoke-direct {v1, p0, p3}, Lpa1/n;-><init>(Lkotlin/jvm/functions/Function2;LB4/a;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p3}, LB4/a;->e()LL2/a;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    check-cast p0, LS91/m1;

    .line 22
    .line 23
    iget-object p0, p0, LS91/m1;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 24
    .line 25
    new-instance v0, Lpa1/o;

    .line 26
    .line 27
    invoke-direct {v0, p1, p3}, Lpa1/o;-><init>(LOc/n;LB4/a;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnActionIconClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p3}, LB4/a;->e()LL2/a;

    .line 34
    .line 35
    .line 36
    move-result-object p0

    .line 37
    check-cast p0, LS91/m1;

    .line 38
    .line 39
    iget-object p0, p0, LS91/m1;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 40
    .line 41
    new-instance p1, Lpa1/p;

    .line 42
    .line 43
    invoke-direct {p1, p2, p3}, Lpa1/p;-><init>(Lkotlin/jvm/functions/Function1;LB4/a;)V

    .line 44
    .line 45
    .line 46
    const/4 p2, 0x1

    .line 47
    const/4 v0, 0x0

    .line 48
    invoke-static {v0, p1, p2, v0}, LN11/f;->k(Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    invoke-virtual {p0, p1}, Lorg/xbet/uikit/components/header/DSHeader;->setButtonClickListener(Landroid/view/View$OnClickListener;)V

    .line 53
    .line 54
    .line 55
    new-instance p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt$a;

    .line 56
    .line 57
    invoke-direct {p0, p3, p3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt$a;-><init>(LB4/a;LB4/a;)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {p3, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 61
    .line 62
    .line 63
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 64
    .line 65
    return-object p0
.end method

.method public static final n(LB4/a;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lra1/b;",
            "LS91/m1;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LS91/m1;

    .line 6
    .line 7
    iget-object v0, v0, LS91/m1;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    check-cast v1, Lra1/b;

    .line 14
    .line 15
    invoke-virtual {v1}, Lra1/b;->s()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setStyle(I)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    check-cast v0, LS91/m1;

    .line 27
    .line 28
    iget-object v0, v0, LS91/m1;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 29
    .line 30
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p0

    .line 34
    check-cast p0, Lra1/b;

    .line 35
    .line 36
    invoke-virtual {p0}, Lra1/b;->f()Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-virtual {v0, p0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setItems(Ljava/util/List;)V

    .line 41
    .line 42
    .line 43
    return-void
.end method

.method public static final o(LB4/a;)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lra1/b;",
            "LS91/m1;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lra1/b;

    .line 6
    .line 7
    invoke-virtual {v0}, Lra1/b;->o()Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-eqz v0, :cond_0

    .line 12
    .line 13
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    check-cast v0, LS91/m1;

    .line 18
    .line 19
    iget-object v0, v0, LS91/m1;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 20
    .line 21
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    check-cast v1, Lra1/b;

    .line 26
    .line 27
    invoke-virtual {v1}, Lra1/b;->s()I

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setStyle(I)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, LS91/m1;

    .line 39
    .line 40
    iget-object v0, v0, LS91/m1;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 41
    .line 42
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->o()V

    .line 43
    .line 44
    .line 45
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    check-cast v0, LS91/m1;

    .line 50
    .line 51
    invoke-virtual {v0}, LS91/m1;->b()Landroid/widget/LinearLayout;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    invoke-static {v0}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 56
    .line 57
    .line 58
    goto :goto_2

    .line 59
    :cond_0
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    check-cast v0, LS91/m1;

    .line 64
    .line 65
    iget-object v0, v0, LS91/m1;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 66
    .line 67
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    check-cast v1, Lra1/b;

    .line 72
    .line 73
    invoke-virtual {v1}, Lra1/b;->e()Lra1/c;

    .line 74
    .line 75
    .line 76
    move-result-object v1

    .line 77
    sget-object v2, Lra1/c$b;->c:Lra1/c$b;

    .line 78
    .line 79
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 80
    .line 81
    .line 82
    move-result v1

    .line 83
    if-nez v1, :cond_2

    .line 84
    .line 85
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    move-result-object v1

    .line 89
    check-cast v1, Lra1/b;

    .line 90
    .line 91
    invoke-virtual {v1}, Lra1/b;->e()Lra1/c;

    .line 92
    .line 93
    .line 94
    move-result-object v1

    .line 95
    sget-object v2, Lra1/c$d;->c:Lra1/c$d;

    .line 96
    .line 97
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 98
    .line 99
    .line 100
    move-result v1

    .line 101
    if-eqz v1, :cond_1

    .line 102
    .line 103
    goto :goto_0

    .line 104
    :cond_1
    const/4 v1, 0x0

    .line 105
    goto :goto_1

    .line 106
    :cond_2
    :goto_0
    const/4 v1, 0x1

    .line 107
    :goto_1
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setIgnoreDiffUtil(Z)V

    .line 108
    .line 109
    .line 110
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/MyAggregatorAdapterDelegateKt;->n(LB4/a;)V

    .line 111
    .line 112
    .line 113
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 114
    .line 115
    .line 116
    move-result-object v0

    .line 117
    check-cast v0, LS91/m1;

    .line 118
    .line 119
    iget-object v0, v0, LS91/m1;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 120
    .line 121
    new-instance v1, Lorg/xbet/uikit/components/header/a$a;

    .line 122
    .line 123
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 124
    .line 125
    .line 126
    move-result-object v2

    .line 127
    check-cast v2, Lra1/b;

    .line 128
    .line 129
    invoke-virtual {v2}, Lra1/b;->d()Ljava/lang/String;

    .line 130
    .line 131
    .line 132
    move-result-object v2

    .line 133
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 134
    .line 135
    .line 136
    move-result-object v3

    .line 137
    sget v4, Lpb/k;->all:I

    .line 138
    .line 139
    invoke-virtual {v3, v4}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 140
    .line 141
    .line 142
    move-result-object v5

    .line 143
    const/16 v11, 0x1f6

    .line 144
    .line 145
    const/4 v12, 0x0

    .line 146
    const/4 v3, 0x0

    .line 147
    const/4 v4, 0x0

    .line 148
    const/4 v6, 0x0

    .line 149
    const/4 v7, 0x0

    .line 150
    const/4 v8, 0x0

    .line 151
    const/4 v9, 0x0

    .line 152
    const/4 v10, 0x0

    .line 153
    invoke-direct/range {v1 .. v12}, Lorg/xbet/uikit/components/header/a$a;-><init>(Ljava/lang/CharSequence;ZLjava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/Integer;Landroid/graphics/drawable/Drawable;LL11/c;LL11/c;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 154
    .line 155
    .line 156
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/header/DSHeader;->setModel(Lorg/xbet/uikit/components/header/a;)V

    .line 157
    .line 158
    .line 159
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 160
    .line 161
    .line 162
    move-result-object v0

    .line 163
    check-cast v0, LS91/m1;

    .line 164
    .line 165
    invoke-virtual {v0}, LS91/m1;->b()Landroid/widget/LinearLayout;

    .line 166
    .line 167
    .line 168
    move-result-object v0

    .line 169
    invoke-static {v0}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 170
    .line 171
    .line 172
    :goto_2
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 173
    .line 174
    .line 175
    move-result-object v0

    .line 176
    check-cast v0, LS91/m1;

    .line 177
    .line 178
    iget-object v0, v0, LS91/m1;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 179
    .line 180
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 181
    .line 182
    .line 183
    move-result-object p0

    .line 184
    check-cast p0, Lra1/b;

    .line 185
    .line 186
    invoke-virtual {p0}, Lra1/b;->o()Z

    .line 187
    .line 188
    .line 189
    move-result p0

    .line 190
    invoke-virtual {v0, p0}, Lorg/xbet/uikit/components/header/DSHeader;->c(Z)V

    .line 191
    .line 192
    .line 193
    return-void
.end method

.method public static final p(LB4/a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lra1/b;",
            "LS91/m1;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LS91/m1;

    .line 6
    .line 7
    iget-object v0, v0, LS91/m1;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, Lra1/b;

    .line 14
    .line 15
    invoke-virtual {p0}, Lra1/b;->d()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {v0, p0}, Lorg/xbet/uikit/components/header/DSHeader;->setLabel(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final q(Lkotlin/jvm/functions/Function2;LB4/a;LN21/k;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lra1/b;

    .line 6
    .line 7
    invoke-virtual {p1}, Lra1/b;->e()Lra1/c;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-virtual {p2}, LN21/k;->e()J

    .line 12
    .line 13
    .line 14
    move-result-wide v0

    .line 15
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 16
    .line 17
    .line 18
    move-result-object p2

    .line 19
    invoke-interface {p0, p1, p2}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 23
    .line 24
    return-object p0
.end method

.method public static final r(LOc/n;LB4/a;LN21/k;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lra1/b;

    .line 6
    .line 7
    invoke-virtual {p1}, Lra1/b;->e()Lra1/c;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-virtual {p2}, LN21/k;->e()J

    .line 12
    .line 13
    .line 14
    move-result-wide v0

    .line 15
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p2}, LN21/k;->c()LN21/m;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    invoke-virtual {p2}, LN21/m;->b()Z

    .line 24
    .line 25
    .line 26
    move-result p2

    .line 27
    invoke-static {p2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 28
    .line 29
    .line 30
    move-result-object p2

    .line 31
    invoke-interface {p0, p1, v0, p2}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 35
    .line 36
    return-object p0
.end method

.method public static final s(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lra1/b;

    .line 6
    .line 7
    invoke-virtual {p1}, Lra1/b;->e()Lra1/c;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method
