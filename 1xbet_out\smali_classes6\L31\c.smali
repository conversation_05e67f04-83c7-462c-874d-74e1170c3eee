.class public final synthetic LL31/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

.field public final synthetic b:Landroid/content/Context;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;Landroid/content/Context;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LL31/c;->a:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    iput-object p2, p0, LL31/c;->b:Landroid/content/Context;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LL31/c;->a:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    iget-object v1, p0, LL31/c;->b:Landroid/content/Context;

    check-cast p1, Landroid/content/res/TypedArray;

    invoke-static {v0, v1, p1}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->b(Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;Landroid/content/Context;Landroid/content/res/TypedArray;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
