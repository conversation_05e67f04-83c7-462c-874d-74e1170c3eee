.class final Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.tile_matching.data.repositories.TileMatchingRepositoryImpl$makeBetGame$2"
    f = "TileMatchingRepositoryImpl.kt"
    l = {
        0x28,
        0x30
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->f(Lorg/xbet/games_section/api/models/GameBonus;DJLcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Ljava/lang/String;",
        "Ljava/lang/Long;",
        "Lkotlin/coroutines/e<",
        "-",
        "LzT0/e;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\n\u00a2\u0006\u0004\u0008\u0005\u0010\u0006"
    }
    d2 = {
        "",
        "token",
        "",
        "userId",
        "LzT0/e;",
        "<anonymous>",
        "(Ljava/lang/String;J)LzT0/e;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $accountId:J

.field final synthetic $betSum:D

.field final synthetic $bonus:Lorg/xbet/games_section/api/models/GameBonus;

.field final synthetic $gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

.field synthetic J$0:J

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;


# direct methods
.method public constructor <init>(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lorg/xbet/games_section/api/models/GameBonus;Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;JDLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
            "Lorg/xbet/games_section/api/models/GameBonus;",
            "Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;",
            "JD",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    iput-object p2, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$bonus:Lorg/xbet/games_section/api/models/GameBonus;

    iput-object p3, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    iput-wide p4, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$accountId:J

    iput-wide p6, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$betSum:D

    const/4 p1, 0x3

    invoke-direct {p0, p1, p8}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Ljava/lang/Number;

    invoke-virtual {p2}, Ljava/lang/Number;->longValue()J

    move-result-wide v0

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, v0, v1, p3}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->invoke(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "J",
            "Lkotlin/coroutines/e<",
            "-",
            "LzT0/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;

    iget-object v1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    iget-object v2, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$bonus:Lorg/xbet/games_section/api/models/GameBonus;

    iget-object v3, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    iget-wide v4, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$accountId:J

    iget-wide v6, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$betSum:D

    move-object v8, p4

    invoke-direct/range {v0 .. v8}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;-><init>(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lorg/xbet/games_section/api/models/GameBonus;Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;JDLkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->L$0:Ljava/lang/Object;

    iput-wide p2, v0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->J$0:J

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 13

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v11

    .line 5
    iget v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->label:I

    .line 6
    .line 7
    const/4 v1, 0x2

    .line 8
    const/4 v2, 0x1

    .line 9
    if-eqz v0, :cond_2

    .line 10
    .line 11
    if-eq v0, v2, :cond_1

    .line 12
    .line 13
    if-ne v0, v1, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    move-object v0, p1

    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw v0

    .line 28
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    move-object v0, p1

    .line 32
    goto/16 :goto_2

    .line 33
    .line 34
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 35
    .line 36
    .line 37
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->L$0:Ljava/lang/Object;

    .line 38
    .line 39
    check-cast v0, Ljava/lang/String;

    .line 40
    .line 41
    iget-wide v3, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->J$0:J

    .line 42
    .line 43
    iget-object v5, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 44
    .line 45
    sget-object v6, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2$a;->a:[I

    .line 46
    .line 47
    invoke-virtual {v5}, Ljava/lang/Enum;->ordinal()I

    .line 48
    .line 49
    .line 50
    move-result v5

    .line 51
    aget v5, v6, v5

    .line 52
    .line 53
    if-eq v5, v2, :cond_5

    .line 54
    .line 55
    if-ne v5, v1, :cond_4

    .line 56
    .line 57
    iget-object v2, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$bonus:Lorg/xbet/games_section/api/models/GameBonus;

    .line 58
    .line 59
    invoke-virtual {v2}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusId()J

    .line 60
    .line 61
    .line 62
    move-result-wide v5

    .line 63
    move-object v2, v0

    .line 64
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    .line 65
    .line 66
    move-wide v9, v5

    .line 67
    move-object v6, v2

    .line 68
    move-wide v2, v3

    .line 69
    iget-wide v4, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$accountId:J

    .line 70
    .line 71
    move-object v12, v6

    .line 72
    iget-wide v6, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$betSum:D

    .line 73
    .line 74
    iput v1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->label:I

    .line 75
    .line 76
    move-wide v8, v9

    .line 77
    move-object v1, v12

    .line 78
    move-object v10, p0

    .line 79
    invoke-static/range {v0 .. v10}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->n(Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Ljava/lang/String;JJDJLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    if-ne v0, v11, :cond_3

    .line 84
    .line 85
    goto :goto_1

    .line 86
    :cond_3
    :goto_0
    check-cast v0, LwT0/d;

    .line 87
    .line 88
    invoke-static {v0}, LtT0/h;->b(LwT0/d;)LzT0/e;

    .line 89
    .line 90
    .line 91
    move-result-object v0

    .line 92
    return-object v0

    .line 93
    :cond_4
    new-instance v0, Ljava/lang/EnumConstantNotPresentException;

    .line 94
    .line 95
    const-class v1, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 96
    .line 97
    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 98
    .line 99
    .line 100
    move-result-object v2

    .line 101
    invoke-direct {v0, v1, v2}, Ljava/lang/EnumConstantNotPresentException;-><init>(Ljava/lang/Class;Ljava/lang/String;)V

    .line 102
    .line 103
    .line 104
    throw v0

    .line 105
    :cond_5
    move-object v1, v0

    .line 106
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$bonus:Lorg/xbet/games_section/api/models/GameBonus;

    .line 107
    .line 108
    invoke-virtual {v0}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusId()J

    .line 109
    .line 110
    .line 111
    move-result-wide v6

    .line 112
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    .line 113
    .line 114
    iget-wide v3, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$accountId:J

    .line 115
    .line 116
    move-wide v9, v3

    .line 117
    iget-wide v4, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->$betSum:D

    .line 118
    .line 119
    iput v2, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;->label:I

    .line 120
    .line 121
    move-object v8, p0

    .line 122
    move-wide v2, v9

    .line 123
    invoke-static/range {v0 .. v8}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->m(Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Ljava/lang/String;JDJLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 124
    .line 125
    .line 126
    move-result-object v0

    .line 127
    if-ne v0, v11, :cond_6

    .line 128
    .line 129
    :goto_1
    return-object v11

    .line 130
    :cond_6
    :goto_2
    check-cast v0, LvT0/d;

    .line 131
    .line 132
    invoke-static {v0}, LtT0/h;->a(LvT0/d;)LzT0/e;

    .line 133
    .line 134
    .line 135
    move-result-object v0

    .line 136
    return-object v0
.end method
