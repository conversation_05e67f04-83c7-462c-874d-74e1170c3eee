.class public final Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;-><init>()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0011\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u000f\u0010\u0003\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "org/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$b",
        "Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;",
        "",
        "onGlobalLayout",
        "()V",
        "african_roulette_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$b;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public static synthetic a(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$b;->b(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final b(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->G2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->e4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method


# virtual methods
.method public onGlobalLayout()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$b;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->F2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lgg/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v0, v0, Lgg/b;->l:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;

    .line 8
    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0, p0}, Landroid/view/ViewTreeObserver;->removeOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$b;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 17
    .line 18
    invoke-static {v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->F2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lgg/b;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iget-object v0, v0, Lgg/b;->l:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;

    .line 23
    .line 24
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$b;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 25
    .line 26
    new-instance v2, Lorg/xbet/african_roulette/presentation/game/f;

    .line 27
    .line 28
    invoke-direct {v2, v1}, Lorg/xbet/african_roulette/presentation/game/f;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)V

    .line 29
    .line 30
    .line 31
    invoke-virtual {v0, v2}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->setAnimationEndListener$african_roulette_release(Lkotlin/jvm/functions/Function0;)V

    .line 32
    .line 33
    .line 34
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$b;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 35
    .line 36
    invoke-static {v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->E2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Z

    .line 37
    .line 38
    .line 39
    move-result v0

    .line 40
    if-nez v0, :cond_0

    .line 41
    .line 42
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$b;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 43
    .line 44
    invoke-static {v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->F2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lgg/b;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iget-object v0, v0, Lgg/b;->l:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;

    .line 49
    .line 50
    invoke-virtual {v0}, Landroid/view/View;->getHeight()I

    .line 51
    .line 52
    .line 53
    move-result v0

    .line 54
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$b;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 55
    .line 56
    invoke-static {v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->F2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lgg/b;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    iget-object v1, v1, Lgg/b;->l:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;

    .line 61
    .line 62
    invoke-virtual {v1}, Landroid/view/View;->getWidth()I

    .line 63
    .line 64
    .line 65
    move-result v1

    .line 66
    sub-int/2addr v0, v1

    .line 67
    neg-int v0, v0

    .line 68
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$b;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 69
    .line 70
    invoke-static {v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->G2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    invoke-virtual {v1, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->j4(I)V

    .line 75
    .line 76
    .line 77
    :cond_0
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$b;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 78
    .line 79
    invoke-static {v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->G2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    const/4 v1, 0x1

    .line 84
    invoke-virtual {v0, v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->R3(Z)V

    .line 85
    .line 86
    .line 87
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$b;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 88
    .line 89
    invoke-static {v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->G2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 90
    .line 91
    .line 92
    move-result-object v0

    .line 93
    invoke-virtual {v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->n4()V

    .line 94
    .line 95
    .line 96
    return-void
.end method
