.class public interface abstract LJ91/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation build LNc/c;
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010$\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0008a\u0018\u00002\u00020\u0001Jj\u0010\u0010\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u000e2\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0001\u0010\u0005\u001a\u00020\u00042\n\u0008\u0001\u0010\u0006\u001a\u0004\u0018\u00010\u00042\u0008\u0008\u0001\u0010\u0007\u001a\u00020\u00042\u0008\u0008\u0001\u0010\u0008\u001a\u00020\u00042\u0008\u0008\u0001\u0010\n\u001a\u00020\t2\u0008\u0008\u0001\u0010\u000b\u001a\u00020\u00042\n\u0008\u0001\u0010\r\u001a\u0004\u0018\u00010\u000cH\u00a7@\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J,\u0010\u0015\u001a\u0008\u0012\u0004\u0012\u00020\u00140\u000e2\u0014\u0008\u0001\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u0012H\u00a7@\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J,\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00180\u000e2\u0014\u0008\u0001\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u0012H\u00a7@\u00a2\u0006\u0004\u0008\u0019\u0010\u0016\u00a8\u0006\u001a"
    }
    d2 = {
        "LJ91/b;",
        "",
        "",
        "partitionId",
        "",
        "whence",
        "countryId",
        "countryIdBlocking",
        "ref",
        "",
        "language",
        "groupId",
        "",
        "test",
        "Le8/b;",
        "LB91/i;",
        "b",
        "(JILjava/lang/Integer;IILjava/lang/String;ILjava/lang/Boolean;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "params",
        "LB91/d;",
        "a",
        "(Ljava/util/Map;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "paramsMap",
        "LB91/g;",
        "c",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Ljava/util/Map;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/util/Map;
        .annotation runtime Lbd1/u;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "/Aggregator_v3/v2/GetLobbyMobile"
    .end annotation

    .annotation runtime Lbd1/k;
        value = {
            "Accept: application/vnd.xenvelop+json"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;",
            "Lkotlin/coroutines/e<",
            "Le8/b<",
            "LB91/d;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract b(JILjava/lang/Integer;IILjava/lang/String;ILjava/lang/Boolean;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # J
        .annotation runtime Lbd1/t;
            value = "partId"
        .end annotation
    .end param
    .param p3    # I
        .annotation runtime Lbd1/t;
            value = "whence"
        .end annotation
    .end param
    .param p4    # Ljava/lang/Integer;
        .annotation runtime Lbd1/t;
            value = "country"
        .end annotation
    .end param
    .param p5    # I
        .annotation runtime Lbd1/t;
            value = "fcountry"
        .end annotation
    .end param
    .param p6    # I
        .annotation runtime Lbd1/t;
            value = "ref"
        .end annotation
    .end param
    .param p7    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "lng"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # I
        .annotation runtime Lbd1/t;
            value = "gr"
        .end annotation
    .end param
    .param p9    # Ljava/lang/Boolean;
        .annotation runtime Lbd1/t;
            value = "test"
        .end annotation
    .end param
    .param p10    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "/Aggregator_v3/v2/Categories/GetPromotedCategories"
    .end annotation

    .annotation runtime Lbd1/k;
        value = {
            "Accept: application/vnd.xenvelop+json"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JI",
            "Ljava/lang/Integer;",
            "II",
            "Ljava/lang/String;",
            "I",
            "Ljava/lang/Boolean;",
            "Lkotlin/coroutines/e<",
            "Le8/b<",
            "LB91/i;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract c(Ljava/util/Map;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/util/Map;
        .annotation runtime Lbd1/u;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "/Aggregator_v3/v2/GetFilterGroupsForPartition"
    .end annotation

    .annotation runtime Lbd1/k;
        value = {
            "Accept: application/vnd.xenvelop+json"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;",
            "Lkotlin/coroutines/e<",
            "Le8/b<",
            "LB91/g;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method
