.class public final synthetic LD01/C;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Landroid/content/Context;

.field public final synthetic b:Lorg/xbet/uikit/components/successbetalert/success_bet_info/SuccessBetInfoSecondaryView;


# direct methods
.method public synthetic constructor <init>(Landroid/content/Context;Lorg/xbet/uikit/components/successbetalert/success_bet_info/SuccessBetInfoSecondaryView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LD01/C;->a:Landroid/content/Context;

    iput-object p2, p0, LD01/C;->b:Lorg/xbet/uikit/components/successbetalert/success_bet_info/SuccessBetInfoSecondaryView;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LD01/C;->a:Landroid/content/Context;

    iget-object v1, p0, LD01/C;->b:Lorg/xbet/uikit/components/successbetalert/success_bet_info/SuccessBetInfoSecondaryView;

    invoke-static {v0, v1}, Lorg/xbet/uikit/components/successbetalert/success_bet_info/SuccessBetInfoSecondaryView;->i(Landroid/content/Context;Lorg/xbet/uikit/components/successbetalert/success_bet_info/SuccessBetInfoSecondaryView;)Landroidx/appcompat/widget/AppCompatTextView;

    move-result-object v0

    return-object v0
.end method
