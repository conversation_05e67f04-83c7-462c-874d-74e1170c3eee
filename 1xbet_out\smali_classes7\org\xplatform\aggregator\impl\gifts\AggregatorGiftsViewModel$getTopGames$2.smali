.class final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.AggregatorGiftsViewModel$getTopGames$2"
    f = "AggregatorGiftsViewModel.kt"
    l = {
        0x2bb,
        0x2ba
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->u5()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $activeChipType:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

.field L$0:Ljava/lang/Object;

.field Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lorg/xplatform/aggregator/api/navigation/GiftsChipType;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;",
            "Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->$activeChipType:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->$activeChipType:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    invoke-direct {p1, v0, v1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lorg/xplatform/aggregator/api/navigation/GiftsChipType;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 11

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    move-object v10, p0

    .line 19
    goto/16 :goto_2

    .line 20
    .line 21
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 22
    .line 23
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 24
    .line 25
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 26
    .line 27
    .line 28
    throw p1

    .line 29
    :cond_1
    iget-boolean v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->Z$0:Z

    .line 30
    .line 31
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->L$0:Ljava/lang/Object;

    .line 32
    .line 33
    check-cast v3, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 34
    .line 35
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    check-cast p1, Lkotlin/Result;

    .line 39
    .line 40
    invoke-virtual {p1}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    move-object v10, p0

    .line 45
    goto :goto_0

    .line 46
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 47
    .line 48
    .line 49
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 50
    .line 51
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->R4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 56
    .line 57
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->d5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lma1/f;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    invoke-static {v1}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 66
    .line 67
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->y4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Ljava/util/List;

    .line 68
    .line 69
    .line 70
    move-result-object v4

    .line 71
    invoke-static {v1, v4}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    invoke-interface {p1, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 76
    .line 77
    .line 78
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 79
    .line 80
    invoke-static {p1, v3}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->c5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Z)V

    .line 81
    .line 82
    .line 83
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->$activeChipType:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 84
    .line 85
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 86
    .line 87
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->v4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    if-ne p1, v1, :cond_3

    .line 92
    .line 93
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 94
    .line 95
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->O4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    invoke-static {v3}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 100
    .line 101
    .line 102
    move-result-object v1

    .line 103
    invoke-interface {p1, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 104
    .line 105
    .line 106
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 107
    .line 108
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->N4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$a;->a:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$a;

    .line 113
    .line 114
    invoke-interface {p1, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 115
    .line 116
    .line 117
    :cond_3
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 118
    .line 119
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->A4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lp9/c;

    .line 120
    .line 121
    .line 122
    move-result-object p1

    .line 123
    invoke-virtual {p1}, Lp9/c;->a()Z

    .line 124
    .line 125
    .line 126
    move-result v6

    .line 127
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 128
    .line 129
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->C4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lf81/c;

    .line 130
    .line 131
    .line 132
    move-result-object v4

    .line 133
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->L$0:Ljava/lang/Object;

    .line 134
    .line 135
    iput-boolean v6, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->Z$0:Z

    .line 136
    .line 137
    iput v3, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->label:I

    .line 138
    .line 139
    const/4 v5, 0x0

    .line 140
    const/16 v7, 0x8

    .line 141
    .line 142
    const/4 v8, 0x0

    .line 143
    const/4 v9, 0x0

    .line 144
    move-object v10, p0

    .line 145
    invoke-interface/range {v4 .. v10}, Lf81/c;->a(ZZIZZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 146
    .line 147
    .line 148
    move-result-object v1

    .line 149
    if-ne v1, v0, :cond_4

    .line 150
    .line 151
    goto :goto_1

    .line 152
    :cond_4
    move-object v3, p1

    .line 153
    move-object p1, v1

    .line 154
    move v1, v6

    .line 155
    :goto_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 156
    .line 157
    .line 158
    move-result-object v4

    .line 159
    invoke-static {p1}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 160
    .line 161
    .line 162
    move-result v5

    .line 163
    if-eqz v5, :cond_5

    .line 164
    .line 165
    move-object p1, v4

    .line 166
    :cond_5
    check-cast p1, Ljava/util/List;

    .line 167
    .line 168
    iget-object v4, v10, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 169
    .line 170
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->K4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lek0/o;

    .line 171
    .line 172
    .line 173
    move-result-object v4

    .line 174
    const/4 v5, 0x0

    .line 175
    iput-object v5, v10, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->L$0:Ljava/lang/Object;

    .line 176
    .line 177
    iput v2, v10, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->label:I

    .line 178
    .line 179
    invoke-static {v3, p1, v1, v4, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->U4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Ljava/util/List;ZLek0/o;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 180
    .line 181
    .line 182
    move-result-object p1

    .line 183
    if-ne p1, v0, :cond_6

    .line 184
    .line 185
    :goto_1
    return-object v0

    .line 186
    :cond_6
    :goto_2
    check-cast p1, Ljava/util/List;

    .line 187
    .line 188
    iget-object v0, v10, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 189
    .line 190
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->R4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 191
    .line 192
    .line 193
    move-result-object v0

    .line 194
    iget-object v1, v10, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 195
    .line 196
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->d5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lma1/f;

    .line 197
    .line 198
    .line 199
    move-result-object v1

    .line 200
    invoke-static {v1}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 201
    .line 202
    .line 203
    move-result-object v1

    .line 204
    invoke-static {v1, p1}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 205
    .line 206
    .line 207
    move-result-object p1

    .line 208
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 209
    .line 210
    .line 211
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 212
    .line 213
    return-object p1
.end method
