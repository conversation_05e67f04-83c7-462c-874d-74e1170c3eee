.class public final LfP0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0098\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008$\u0018\u00002\u00020\u0001B\u0091\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u00a2\u0006\u0004\u0008$\u0010%J?\u00102\u001a\u0002012\u0006\u0010\'\u001a\u00020&2\u0006\u0010)\u001a\u00020(2\u0006\u0010+\u001a\u00020*2\u0006\u0010-\u001a\u00020,2\u0006\u0010/\u001a\u00020.2\u0006\u00100\u001a\u00020.H\u0000\u00a2\u0006\u0004\u00082\u00103R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00104R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00085\u00106R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00087\u00108R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u0010:R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008;\u0010<R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u0010>R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008?\u0010@R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008A\u0010BR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008C\u0010DR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008E\u0010FR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010HR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008I\u0010JR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008K\u0010LR\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008M\u0010NR\u0014\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008O\u0010PR\u0014\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Q\u0010RR\u0014\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008S\u0010T\u00a8\u0006U"
    }
    d2 = {
        "LfP0/d;",
        "LQW0/a;",
        "LQW0/c;",
        "coroutinesLib",
        "Lf8/g;",
        "serviceGenerator",
        "LTn/a;",
        "sportRepository",
        "Lorg/xbet/onexdatabase/OnexDatabase;",
        "onexDatabase",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Li8/l;",
        "getThemeStreamUseCase",
        "Lorg/xbet/analytics/domain/scope/StatisticAnalytics;",
        "statisticAnalytics",
        "LSX0/a;",
        "lottieConfigurator",
        "Lc8/h;",
        "requestParamsDataSource",
        "LHX0/e;",
        "resourceManager",
        "LiR/a;",
        "fatmanFeature",
        "LJo0/a;",
        "specialEventFeature",
        "LHg/d;",
        "specialEventAnalytics",
        "LEN0/f;",
        "statisticCoreFeature",
        "LGL0/a;",
        "stadiumFeature",
        "LLD0/a;",
        "statisticFeature",
        "<init>",
        "(LQW0/c;Lf8/g;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/ui_common/utils/M;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;LSX0/a;Lc8/h;LHX0/e;LiR/a;LJo0/a;LHg/d;LEN0/f;LGL0/a;LLD0/a;)V",
        "LwX0/c;",
        "router",
        "Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;",
        "param",
        "",
        "sportId",
        "",
        "screenName",
        "",
        "eventId",
        "teamClId",
        "LfP0/c;",
        "a",
        "(LwX0/c;Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;JLjava/lang/String;II)LfP0/c;",
        "LQW0/c;",
        "b",
        "Lf8/g;",
        "c",
        "LTn/a;",
        "d",
        "Lorg/xbet/onexdatabase/OnexDatabase;",
        "e",
        "Lorg/xbet/ui_common/utils/M;",
        "f",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "g",
        "Li8/l;",
        "h",
        "Lorg/xbet/analytics/domain/scope/StatisticAnalytics;",
        "i",
        "LSX0/a;",
        "j",
        "Lc8/h;",
        "k",
        "LHX0/e;",
        "l",
        "LiR/a;",
        "m",
        "LJo0/a;",
        "n",
        "LHg/d;",
        "o",
        "LEN0/f;",
        "p",
        "LGL0/a;",
        "q",
        "LLD0/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LQW0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LTn/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/onexdatabase/OnexDatabase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Li8/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lorg/xbet/analytics/domain/scope/StatisticAnalytics;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:LiR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:LJo0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:LHg/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:LEN0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:LGL0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q:LLD0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lf8/g;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/ui_common/utils/M;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;LSX0/a;Lc8/h;LHX0/e;LiR/a;LJo0/a;LHg/d;LEN0/f;LGL0/a;LLD0/a;)V
    .locals 0
    .param p1    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LTn/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/onexdatabase/OnexDatabase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Li8/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/analytics/domain/scope/StatisticAnalytics;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LiR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # LJo0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LHg/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # LEN0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # LGL0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # LLD0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LfP0/d;->a:LQW0/c;

    .line 5
    .line 6
    iput-object p2, p0, LfP0/d;->b:Lf8/g;

    .line 7
    .line 8
    iput-object p3, p0, LfP0/d;->c:LTn/a;

    .line 9
    .line 10
    iput-object p4, p0, LfP0/d;->d:Lorg/xbet/onexdatabase/OnexDatabase;

    .line 11
    .line 12
    iput-object p5, p0, LfP0/d;->e:Lorg/xbet/ui_common/utils/M;

    .line 13
    .line 14
    iput-object p6, p0, LfP0/d;->f:Lorg/xbet/ui_common/utils/internet/a;

    .line 15
    .line 16
    iput-object p7, p0, LfP0/d;->g:Li8/l;

    .line 17
    .line 18
    iput-object p8, p0, LfP0/d;->h:Lorg/xbet/analytics/domain/scope/StatisticAnalytics;

    .line 19
    .line 20
    iput-object p9, p0, LfP0/d;->i:LSX0/a;

    .line 21
    .line 22
    iput-object p10, p0, LfP0/d;->j:Lc8/h;

    .line 23
    .line 24
    iput-object p11, p0, LfP0/d;->k:LHX0/e;

    .line 25
    .line 26
    iput-object p12, p0, LfP0/d;->l:LiR/a;

    .line 27
    .line 28
    iput-object p13, p0, LfP0/d;->m:LJo0/a;

    .line 29
    .line 30
    iput-object p14, p0, LfP0/d;->n:LHg/d;

    .line 31
    .line 32
    iput-object p15, p0, LfP0/d;->o:LEN0/f;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, LfP0/d;->p:LGL0/a;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, LfP0/d;->q:LLD0/a;

    .line 41
    .line 42
    return-void
.end method


# virtual methods
.method public final a(LwX0/c;Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;JLjava/lang/String;II)LfP0/c;
    .locals 27
    .param p1    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, LfP0/a;->a()LfP0/c$a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, v0, LfP0/d;->a:LQW0/c;

    .line 8
    .line 9
    iget-object v11, v0, LfP0/d;->b:Lf8/g;

    .line 10
    .line 11
    iget-object v12, v0, LfP0/d;->e:Lorg/xbet/ui_common/utils/M;

    .line 12
    .line 13
    iget-object v13, v0, LfP0/d;->c:LTn/a;

    .line 14
    .line 15
    iget-object v14, v0, LfP0/d;->d:Lorg/xbet/onexdatabase/OnexDatabase;

    .line 16
    .line 17
    invoke-static/range {p2 .. p2}, Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/a;->a(Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v15

    .line 21
    iget-object v3, v0, LfP0/d;->f:Lorg/xbet/ui_common/utils/internet/a;

    .line 22
    .line 23
    iget-object v4, v0, LfP0/d;->g:Li8/l;

    .line 24
    .line 25
    iget-object v5, v0, LfP0/d;->h:Lorg/xbet/analytics/domain/scope/StatisticAnalytics;

    .line 26
    .line 27
    iget-object v9, v0, LfP0/d;->k:LHX0/e;

    .line 28
    .line 29
    iget-object v6, v0, LfP0/d;->i:LSX0/a;

    .line 30
    .line 31
    iget-object v7, v0, LfP0/d;->j:Lc8/h;

    .line 32
    .line 33
    iget-object v8, v0, LfP0/d;->n:LHg/d;

    .line 34
    .line 35
    move-object/from16 v19, v3

    .line 36
    .line 37
    iget-object v3, v0, LfP0/d;->l:LiR/a;

    .line 38
    .line 39
    move-object/from16 v20, v4

    .line 40
    .line 41
    iget-object v4, v0, LfP0/d;->m:LJo0/a;

    .line 42
    .line 43
    move-object/from16 v21, v5

    .line 44
    .line 45
    iget-object v5, v0, LfP0/d;->o:LEN0/f;

    .line 46
    .line 47
    move-object/from16 v24, v6

    .line 48
    .line 49
    iget-object v6, v0, LfP0/d;->p:LGL0/a;

    .line 50
    .line 51
    move-object/from16 v25, v7

    .line 52
    .line 53
    iget-object v7, v0, LfP0/d;->q:LLD0/a;

    .line 54
    .line 55
    move-object/from16 v10, p1

    .line 56
    .line 57
    move-object/from16 v16, p2

    .line 58
    .line 59
    move-wide/from16 v22, p3

    .line 60
    .line 61
    move/from16 v17, p6

    .line 62
    .line 63
    move/from16 v18, p7

    .line 64
    .line 65
    move-object/from16 v26, v8

    .line 66
    .line 67
    move-object/from16 v8, p5

    .line 68
    .line 69
    invoke-interface/range {v1 .. v26}, LfP0/c$a;->a(LQW0/c;LiR/a;LJo0/a;LEN0/f;LGL0/a;LLD0/a;Ljava/lang/String;LHX0/e;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;IILorg/xbet/ui_common/utils/internet/a;Li8/l;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;JLSX0/a;Lc8/h;LHg/d;)LfP0/c;

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    return-object v1
.end method
