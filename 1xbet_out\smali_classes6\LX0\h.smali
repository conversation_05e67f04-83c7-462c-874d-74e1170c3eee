.class public final synthetic LLX0/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:LLX0/i;


# direct methods
.method public synthetic constructor <init>(LLX0/i;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LLX0/h;->a:LLX0/i;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    .line 1
    iget-object v0, p0, LLX0/h;->a:LLX0/i;

    invoke-static {v0}, LLX0/i;->b(LLX0/i;)V

    return-void
.end method
