.class public final synthetic Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/e;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/e;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;

    invoke-static {v0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->B2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Landroid/view/View;)V

    return-void
.end method
