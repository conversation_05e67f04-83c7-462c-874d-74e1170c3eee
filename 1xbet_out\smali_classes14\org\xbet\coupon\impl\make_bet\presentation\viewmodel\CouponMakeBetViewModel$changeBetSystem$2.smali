.class final Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.coupon.impl.make_bet.presentation.viewmodel.CouponMakeBetViewModel$changeBetSystem$2"
    f = "CouponMakeBetViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->U3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;

    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_3

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 12
    .line 13
    invoke-static {p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->x3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/W;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/coupon/domain/usecases/W;->d()Ljava/util/List;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 22
    .line 23
    invoke-static {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->D3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    iget-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 28
    .line 29
    new-instance v2, Ljava/util/ArrayList;

    .line 30
    .line 31
    const/16 v3, 0xa

    .line 32
    .line 33
    invoke-static {p1, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 34
    .line 35
    .line 36
    move-result v3

    .line 37
    invoke-direct {v2, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 38
    .line 39
    .line 40
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 41
    .line 42
    .line 43
    move-result-object v3

    .line 44
    const/4 v4, 0x0

    .line 45
    const/4 v5, 0x0

    .line 46
    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 47
    .line 48
    .line 49
    move-result v6

    .line 50
    if-eqz v6, :cond_2

    .line 51
    .line 52
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 53
    .line 54
    .line 55
    move-result-object v6

    .line 56
    add-int/lit8 v7, v5, 0x1

    .line 57
    .line 58
    if-gez v5, :cond_0

    .line 59
    .line 60
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 61
    .line 62
    .line 63
    :cond_0
    check-cast v6, Luw/a;

    .line 64
    .line 65
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 66
    .line 67
    .line 68
    move-result v8

    .line 69
    const/4 v9, 0x1

    .line 70
    sub-int/2addr v8, v9

    .line 71
    if-ne v5, v8, :cond_1

    .line 72
    .line 73
    goto :goto_1

    .line 74
    :cond_1
    const/4 v9, 0x0

    .line 75
    :goto_1
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->t3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/flow/V;

    .line 76
    .line 77
    .line 78
    move-result-object v5

    .line 79
    invoke-interface {v5}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object v5

    .line 83
    check-cast v5, LAx/d;

    .line 84
    .line 85
    invoke-virtual {v5}, LAx/d;->f()Luw/a;

    .line 86
    .line 87
    .line 88
    move-result-object v5

    .line 89
    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 90
    .line 91
    .line 92
    move-result v5

    .line 93
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->F3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)LHX0/e;

    .line 94
    .line 95
    .line 96
    move-result-object v8

    .line 97
    invoke-static {v6, v9, v5, v8}, LVw/f;->b(Luw/a;ZZLHX0/e;)Lorg/xbet/coupon/impl/coupon/presentation/models/CouponTypeUiModel;

    .line 98
    .line 99
    .line 100
    move-result-object v5

    .line 101
    invoke-interface {v2, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 102
    .line 103
    .line 104
    move v5, v7

    .line 105
    goto :goto_0

    .line 106
    :cond_2
    new-instance p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$b$c;

    .line 107
    .line 108
    invoke-direct {p1, v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$b$c;-><init>(Ljava/util/List;)V

    .line 109
    .line 110
    .line 111
    invoke-virtual {v0, p1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 112
    .line 113
    .line 114
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 115
    .line 116
    return-object p1

    .line 117
    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 118
    .line 119
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 120
    .line 121
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 122
    .line 123
    .line 124
    throw p1
.end method
