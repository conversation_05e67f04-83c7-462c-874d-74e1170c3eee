.class public final Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0000\n\u0002\u0008\r\u0008\u0087\u0008\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0010\u0010\u000b\u001a\u00020\nH\u00d6\u0001\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0010\u0010\u000e\u001a\u00020\rH\u00d6\u0001\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u001a\u0010\u0012\u001a\u00020\u00062\u0008\u0010\u0011\u001a\u0004\u0018\u00010\u0010H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0012\u0010\u0013R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0014\u0010\u0015\u001a\u0004\u0008\u0016\u0010\u0017R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0016\u0010\u0018\u001a\u0004\u0008\u0014\u0010\u0019R\u0017\u0010\u0007\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u001b\u001a\u0004\u0008\u001a\u0010\u001c\u00a8\u0006\u001d"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;",
        "Lorg/xbet/betting/core/coupon/models/SingleBetGame;",
        "singleBetGame",
        "Lorg/xbet/betting/core/zip/model/bet/BetInfo;",
        "betInfo",
        "",
        "isMakeBetFeatureEnabled",
        "<init>",
        "(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/zip/model/bet/BetInfo;Z)V",
        "",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "",
        "other",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "Lorg/xbet/betting/core/coupon/models/SingleBetGame;",
        "b",
        "()Lorg/xbet/betting/core/coupon/models/SingleBetGame;",
        "Lorg/xbet/betting/core/zip/model/bet/BetInfo;",
        "()Lorg/xbet/betting/core/zip/model/bet/BetInfo;",
        "c",
        "Z",
        "()Z",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/betting/core/coupon/models/SingleBetGame;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/betting/core/zip/model/bet/BetInfo;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Z


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/zip/model/bet/BetInfo;Z)V
    .locals 0
    .param p1    # Lorg/xbet/betting/core/coupon/models/SingleBetGame;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/betting/core/zip/model/bet/BetInfo;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->a:Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->b:Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    .line 7
    .line 8
    iput-boolean p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->c:Z

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final a()Lorg/xbet/betting/core/zip/model/bet/BetInfo;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->b:Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Lorg/xbet/betting/core/coupon/models/SingleBetGame;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->a:Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->c:Z

    .line 2
    .line 3
    return v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->a:Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    iget-object v3, p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->a:Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->b:Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    iget-object v3, p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->b:Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-boolean v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->c:Z

    iget-boolean p1, p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->c:Z

    if-eq v1, p1, :cond_4

    return v2

    :cond_4
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->a:Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    invoke-virtual {v0}, Lorg/xbet/betting/core/coupon/models/SingleBetGame;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->b:Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    invoke-virtual {v1}, Lorg/xbet/betting/core/zip/model/bet/BetInfo;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->c:Z

    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 5
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->a:Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->b:Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    iget-boolean v2, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->c:Z

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "ShowMakeBetDialog(singleBetGame="

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", betInfo="

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", isMakeBetFeatureEnabled="

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
