.class public final synthetic Lh2/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/google/common/base/Function;


# instance fields
.field public final synthetic a:Lh2/h;


# direct methods
.method public synthetic constructor <init>(Lh2/h;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lh2/e;->a:Lh2/h;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lh2/e;->a:Lh2/h;

    check-cast p1, Lh2/t;

    invoke-virtual {v0, p1}, Lh2/h;->s(Lh2/t;)Lh2/t;

    move-result-object p1

    return-object p1
.end method
