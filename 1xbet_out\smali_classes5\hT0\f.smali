.class public final LhT0/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "LhT0/e;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LRf0/o;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LRf0/o;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LhT0/f;->a:LBc/a;

    .line 5
    .line 6
    return-void
.end method

.method public static a(LBc/a;)LhT0/f;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LRf0/o;",
            ">;)",
            "LhT0/f;"
        }
    .end annotation

    .line 1
    new-instance v0, LhT0/f;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LhT0/f;-><init>(LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(LRf0/o;)LhT0/e;
    .locals 1

    .line 1
    new-instance v0, LhT0/e;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LhT0/e;-><init>(LRf0/o;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()LhT0/e;
    .locals 1

    .line 1
    iget-object v0, p0, LhT0/f;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LRf0/o;

    .line 8
    .line 9
    invoke-static {v0}, LhT0/f;->c(LRf0/o;)LhT0/e;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LhT0/f;->b()LhT0/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
