.class public final LNB0/g$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LNB0/g;->b(Landroidx/compose/ui/l;Landroidx/compose/runtime/r1;ZLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function2<",
        "Landroidx/compose/runtime/j;",
        "Ljava/lang/Integer;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Landroidx/compose/runtime/r1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/compose/runtime/r1<",
            "LNB0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final synthetic b:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Lorg/xbet/sportgame/markets/impl/presentation/base/i;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public final synthetic c:Z

.field public final synthetic d:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Lorg/xbet/sportgame/markets/impl/presentation/base/i;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroidx/compose/runtime/r1;Lkotlin/jvm/functions/Function1;ZLkotlin/jvm/functions/Function1;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/runtime/r1<",
            "LNB0/a;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/sportgame/markets/impl/presentation/base/i;",
            "Lkotlin/Unit;",
            ">;Z",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/sportgame/markets/impl/presentation/base/i;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LNB0/g$a;->a:Landroidx/compose/runtime/r1;

    .line 2
    .line 3
    iput-object p2, p0, LNB0/g$a;->b:Lkotlin/jvm/functions/Function1;

    .line 4
    .line 5
    iput-boolean p3, p0, LNB0/g$a;->c:Z

    .line 6
    .line 7
    iput-object p4, p0, LNB0/g$a;->d:Lkotlin/jvm/functions/Function1;

    .line 8
    .line 9
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public static synthetic a(ZLkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LNB0/g$a;->k(ZLkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LNB0/g$a;->g(Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LNB0/g$a;->f(Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(ZLkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LNB0/g$a;->j(ZLkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final f(Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, LNB0/e;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, LNB0/e;-><init>(Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)V

    .line 4
    .line 5
    .line 6
    const/4 p0, 0x1

    .line 7
    const/4 p1, 0x0

    .line 8
    invoke-static {p1, v0, p0, p1}, LG11/c;->b(Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)LG11/a;

    .line 9
    .line 10
    .line 11
    move-result-object p0

    .line 12
    invoke-interface {p0}, LG11/a;->invoke()V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)Lkotlin/Unit;
    .locals 9

    .line 1
    new-instance v0, Lorg/xbet/sportgame/markets/impl/presentation/base/i;

    .line 2
    .line 3
    invoke-interface {p1}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    check-cast v1, LPB0/a$a;

    .line 8
    .line 9
    invoke-virtual {v1}, LPB0/a$a;->a()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    invoke-interface {p1}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    check-cast v2, LPB0/a$a;

    .line 18
    .line 19
    invoke-virtual {v2}, LPB0/a$a;->d()J

    .line 20
    .line 21
    .line 22
    move-result-wide v2

    .line 23
    invoke-interface {p1}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    check-cast v4, LPB0/a$a;

    .line 28
    .line 29
    invoke-virtual {v4}, LPB0/a$a;->c()J

    .line 30
    .line 31
    .line 32
    move-result-wide v4

    .line 33
    invoke-interface {p1}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v6

    .line 37
    check-cast v6, LPB0/a$a;

    .line 38
    .line 39
    invoke-virtual {v6}, LPB0/a$a;->e()D

    .line 40
    .line 41
    .line 42
    move-result-wide v6

    .line 43
    invoke-interface {p1}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    check-cast p1, LPB0/a$a;

    .line 48
    .line 49
    invoke-virtual {p1}, LPB0/a$a;->b()Lorg/xbet/betting/core/zip/model/bet/KindEnumModel;

    .line 50
    .line 51
    .line 52
    move-result-object v8

    .line 53
    invoke-direct/range {v0 .. v8}, Lorg/xbet/sportgame/markets/impl/presentation/base/i;-><init>(IJJDLorg/xbet/betting/core/zip/model/bet/KindEnumModel;)V

    .line 54
    .line 55
    .line 56
    invoke-interface {p0, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 57
    .line 58
    .line 59
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 60
    .line 61
    return-object p0
.end method

.method public static final j(ZLkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, LNB0/f;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1, p2}, LNB0/f;-><init>(ZLkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)V

    .line 4
    .line 5
    .line 6
    const/4 p0, 0x1

    .line 7
    const/4 p1, 0x0

    .line 8
    invoke-static {p1, v0, p0, p1}, LG11/c;->b(Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)LG11/a;

    .line 9
    .line 10
    .line 11
    move-result-object p0

    .line 12
    invoke-interface {p0}, LG11/a;->invoke()V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final k(ZLkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)Lkotlin/Unit;
    .locals 9

    .line 1
    if-nez p0, :cond_0

    .line 2
    .line 3
    new-instance v0, Lorg/xbet/sportgame/markets/impl/presentation/base/i;

    .line 4
    .line 5
    invoke-interface {p2}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    check-cast p0, LPB0/a$a;

    .line 10
    .line 11
    invoke-virtual {p0}, LPB0/a$a;->a()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    invoke-interface {p2}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    check-cast p0, LPB0/a$a;

    .line 20
    .line 21
    invoke-virtual {p0}, LPB0/a$a;->d()J

    .line 22
    .line 23
    .line 24
    move-result-wide v2

    .line 25
    invoke-interface {p2}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object p0

    .line 29
    check-cast p0, LPB0/a$a;

    .line 30
    .line 31
    invoke-virtual {p0}, LPB0/a$a;->c()J

    .line 32
    .line 33
    .line 34
    move-result-wide v4

    .line 35
    invoke-interface {p2}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object p0

    .line 39
    check-cast p0, LPB0/a$a;

    .line 40
    .line 41
    invoke-virtual {p0}, LPB0/a$a;->e()D

    .line 42
    .line 43
    .line 44
    move-result-wide v6

    .line 45
    invoke-interface {p2}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 46
    .line 47
    .line 48
    move-result-object p0

    .line 49
    check-cast p0, LPB0/a$a;

    .line 50
    .line 51
    invoke-virtual {p0}, LPB0/a$a;->b()Lorg/xbet/betting/core/zip/model/bet/KindEnumModel;

    .line 52
    .line 53
    .line 54
    move-result-object v8

    .line 55
    invoke-direct/range {v0 .. v8}, Lorg/xbet/sportgame/markets/impl/presentation/base/i;-><init>(IJJDLorg/xbet/betting/core/zip/model/bet/KindEnumModel;)V

    .line 56
    .line 57
    .line 58
    invoke-interface {p1, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 62
    .line 63
    return-object p0
.end method


# virtual methods
.method public final e(Landroidx/compose/runtime/j;I)V
    .locals 12

    .line 1
    and-int/lit8 v1, p2, 0x3

    .line 2
    .line 3
    const/4 v2, 0x2

    .line 4
    if-ne v1, v2, :cond_1

    .line 5
    .line 6
    invoke-interface {p1}, Landroidx/compose/runtime/j;->c()Z

    .line 7
    .line 8
    .line 9
    move-result v1

    .line 10
    if-nez v1, :cond_0

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    invoke-interface {p1}, Landroidx/compose/runtime/j;->n()V

    .line 14
    .line 15
    .line 16
    return-void

    .line 17
    :cond_1
    :goto_0
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    if-eqz v1, :cond_2

    .line 22
    .line 23
    const/4 v1, -0x1

    .line 24
    const-string v2, "org.xbet.sportgame.markets.impl.presentation.base.compose_nodes.EventRows.<anonymous> (EventRows.kt:38)"

    .line 25
    .line 26
    const v3, 0x11a5e662

    .line 27
    .line 28
    .line 29
    invoke-static {v3, p2, v1, v2}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 30
    .line 31
    .line 32
    :cond_2
    iget-object v0, p0, LNB0/g$a;->a:Landroidx/compose/runtime/r1;

    .line 33
    .line 34
    invoke-interface {v0}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, LNB0/a;

    .line 39
    .line 40
    invoke-virtual {v0}, LNB0/a;->a()LHd/c;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    iget-object v8, p0, LNB0/g$a;->b:Lkotlin/jvm/functions/Function1;

    .line 45
    .line 46
    iget-boolean v9, p0, LNB0/g$a;->c:Z

    .line 47
    .line 48
    iget-object v10, p0, LNB0/g$a;->d:Lkotlin/jvm/functions/Function1;

    .line 49
    .line 50
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 51
    .line 52
    .line 53
    move-result-object v11

    .line 54
    :goto_1
    invoke-interface {v11}, Ljava/util/Iterator;->hasNext()Z

    .line 55
    .line 56
    .line 57
    move-result v0

    .line 58
    if-eqz v0, :cond_d

    .line 59
    .line 60
    invoke-interface {v11}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    check-cast v0, LPB0/a;

    .line 65
    .line 66
    instance-of v1, v0, LPB0/a$a;

    .line 67
    .line 68
    const/4 v2, 0x0

    .line 69
    if-eqz v1, :cond_7

    .line 70
    .line 71
    const v1, -0xc50c5b

    .line 72
    .line 73
    .line 74
    invoke-interface {p1, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 75
    .line 76
    .line 77
    move-object v1, v0

    .line 78
    check-cast v1, LPB0/a$a;

    .line 79
    .line 80
    invoke-virtual {v1}, LPB0/a$a;->c()J

    .line 81
    .line 82
    .line 83
    move-result-wide v3

    .line 84
    invoke-static {v3, v4}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 85
    .line 86
    .line 87
    move-result-object v1

    .line 88
    const v3, 0x73968f0d

    .line 89
    .line 90
    .line 91
    invoke-interface {p1, v3, v1}, Landroidx/compose/runtime/j;->T(ILjava/lang/Object;)V

    .line 92
    .line 93
    .line 94
    invoke-static {v0, p1, v2}, Landroidx/compose/runtime/i1;->p(Ljava/lang/Object;Landroidx/compose/runtime/j;I)Landroidx/compose/runtime/r1;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    invoke-interface {v0}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    check-cast v1, LPB0/a$a;

    .line 103
    .line 104
    invoke-virtual {v1}, LPB0/a$a;->f()Lu31/a;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    invoke-static {v1, p1, v2}, Landroidx/compose/runtime/i1;->p(Ljava/lang/Object;Landroidx/compose/runtime/j;I)Landroidx/compose/runtime/r1;

    .line 109
    .line 110
    .line 111
    move-result-object v1

    .line 112
    new-instance v3, Lu31/b$b;

    .line 113
    .line 114
    const/4 v4, 0x1

    .line 115
    const/4 v6, 0x0

    .line 116
    invoke-direct {v3, v2, v4, v6}, Lu31/b$b;-><init>(IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 117
    .line 118
    .line 119
    const v2, -0x615d173a

    .line 120
    .line 121
    .line 122
    invoke-interface {p1, v2}, Landroidx/compose/runtime/j;->t(I)V

    .line 123
    .line 124
    .line 125
    invoke-interface {p1, v8}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 126
    .line 127
    .line 128
    move-result v2

    .line 129
    invoke-interface {p1, v0}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 130
    .line 131
    .line 132
    move-result v4

    .line 133
    or-int/2addr v2, v4

    .line 134
    invoke-interface {p1}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 135
    .line 136
    .line 137
    move-result-object v4

    .line 138
    if-nez v2, :cond_3

    .line 139
    .line 140
    sget-object v2, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 141
    .line 142
    invoke-virtual {v2}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 143
    .line 144
    .line 145
    move-result-object v2

    .line 146
    if-ne v4, v2, :cond_4

    .line 147
    .line 148
    :cond_3
    new-instance v4, LNB0/c;

    .line 149
    .line 150
    invoke-direct {v4, v8, v0}, LNB0/c;-><init>(Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)V

    .line 151
    .line 152
    .line 153
    invoke-interface {p1, v4}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 154
    .line 155
    .line 156
    :cond_4
    check-cast v4, Lkotlin/jvm/functions/Function0;

    .line 157
    .line 158
    invoke-interface {p1}, Landroidx/compose/runtime/j;->q()V

    .line 159
    .line 160
    .line 161
    const v2, -0x6815fd56

    .line 162
    .line 163
    .line 164
    invoke-interface {p1, v2}, Landroidx/compose/runtime/j;->t(I)V

    .line 165
    .line 166
    .line 167
    invoke-interface {p1, v9}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 168
    .line 169
    .line 170
    move-result v2

    .line 171
    invoke-interface {p1, v10}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 172
    .line 173
    .line 174
    move-result v6

    .line 175
    or-int/2addr v2, v6

    .line 176
    invoke-interface {p1, v0}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 177
    .line 178
    .line 179
    move-result v6

    .line 180
    or-int/2addr v2, v6

    .line 181
    invoke-interface {p1}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 182
    .line 183
    .line 184
    move-result-object v6

    .line 185
    if-nez v2, :cond_5

    .line 186
    .line 187
    sget-object v2, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 188
    .line 189
    invoke-virtual {v2}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 190
    .line 191
    .line 192
    move-result-object v2

    .line 193
    if-ne v6, v2, :cond_6

    .line 194
    .line 195
    :cond_5
    new-instance v6, LNB0/d;

    .line 196
    .line 197
    invoke-direct {v6, v9, v10, v0}, LNB0/d;-><init>(ZLkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)V

    .line 198
    .line 199
    .line 200
    invoke-interface {p1, v6}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 201
    .line 202
    .line 203
    :cond_6
    check-cast v6, Lkotlin/jvm/functions/Function0;

    .line 204
    .line 205
    invoke-interface {p1}, Landroidx/compose/runtime/j;->q()V

    .line 206
    .line 207
    .line 208
    sget v0, Lu31/b$b;->b:I

    .line 209
    .line 210
    shl-int/lit8 v0, v0, 0x6

    .line 211
    .line 212
    const/4 v7, 0x1

    .line 213
    move-object v2, v3

    .line 214
    move-object v3, v4

    .line 215
    move-object v4, v6

    .line 216
    move v6, v0

    .line 217
    const/4 v0, 0x0

    .line 218
    move-object v5, p1

    .line 219
    invoke-static/range {v0 .. v7}, Lt31/a;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/r1;Lu31/b;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V

    .line 220
    .line 221
    .line 222
    invoke-interface {p1}, Landroidx/compose/runtime/j;->Y()V

    .line 223
    .line 224
    .line 225
    invoke-interface {p1}, Landroidx/compose/runtime/j;->q()V

    .line 226
    .line 227
    .line 228
    goto/16 :goto_1

    .line 229
    .line 230
    :cond_7
    sget-object v1, LPB0/a$b;->a:LPB0/a$b;

    .line 231
    .line 232
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 233
    .line 234
    .line 235
    move-result v0

    .line 236
    if-eqz v0, :cond_c

    .line 237
    .line 238
    const v0, 0x73979f90

    .line 239
    .line 240
    .line 241
    invoke-interface {p1, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 242
    .line 243
    .line 244
    sget-object v0, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 245
    .line 246
    sget-object v1, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 247
    .line 248
    invoke-virtual {v1}, Landroidx/compose/ui/e$a;->o()Landroidx/compose/ui/e;

    .line 249
    .line 250
    .line 251
    move-result-object v1

    .line 252
    invoke-static {v1, v2}, Landroidx/compose/foundation/layout/BoxKt;->g(Landroidx/compose/ui/e;Z)Landroidx/compose/ui/layout/J;

    .line 253
    .line 254
    .line 255
    move-result-object v1

    .line 256
    invoke-static {p1, v2}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 257
    .line 258
    .line 259
    move-result v2

    .line 260
    invoke-interface {p1}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 261
    .line 262
    .line 263
    move-result-object v3

    .line 264
    invoke-static {p1, v0}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 265
    .line 266
    .line 267
    move-result-object v0

    .line 268
    sget-object v4, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 269
    .line 270
    invoke-virtual {v4}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 271
    .line 272
    .line 273
    move-result-object v6

    .line 274
    invoke-interface {p1}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 275
    .line 276
    .line 277
    move-result-object v7

    .line 278
    invoke-static {v7}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 279
    .line 280
    .line 281
    move-result v7

    .line 282
    if-nez v7, :cond_8

    .line 283
    .line 284
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 285
    .line 286
    .line 287
    :cond_8
    invoke-interface {p1}, Landroidx/compose/runtime/j;->l()V

    .line 288
    .line 289
    .line 290
    invoke-interface {p1}, Landroidx/compose/runtime/j;->B()Z

    .line 291
    .line 292
    .line 293
    move-result v7

    .line 294
    if-eqz v7, :cond_9

    .line 295
    .line 296
    invoke-interface {p1, v6}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 297
    .line 298
    .line 299
    goto :goto_2

    .line 300
    :cond_9
    invoke-interface {p1}, Landroidx/compose/runtime/j;->h()V

    .line 301
    .line 302
    .line 303
    :goto_2
    invoke-static {p1}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 304
    .line 305
    .line 306
    move-result-object v6

    .line 307
    invoke-virtual {v4}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 308
    .line 309
    .line 310
    move-result-object v7

    .line 311
    invoke-static {v6, v1, v7}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 312
    .line 313
    .line 314
    invoke-virtual {v4}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 315
    .line 316
    .line 317
    move-result-object v1

    .line 318
    invoke-static {v6, v3, v1}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 319
    .line 320
    .line 321
    invoke-virtual {v4}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 322
    .line 323
    .line 324
    move-result-object v1

    .line 325
    invoke-interface {v6}, Landroidx/compose/runtime/j;->B()Z

    .line 326
    .line 327
    .line 328
    move-result v3

    .line 329
    if-nez v3, :cond_a

    .line 330
    .line 331
    invoke-interface {v6}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 332
    .line 333
    .line 334
    move-result-object v3

    .line 335
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 336
    .line 337
    .line 338
    move-result-object v7

    .line 339
    invoke-static {v3, v7}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 340
    .line 341
    .line 342
    move-result v3

    .line 343
    if-nez v3, :cond_b

    .line 344
    .line 345
    :cond_a
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 346
    .line 347
    .line 348
    move-result-object v3

    .line 349
    invoke-interface {v6, v3}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 350
    .line 351
    .line 352
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 353
    .line 354
    .line 355
    move-result-object v2

    .line 356
    invoke-interface {v6, v2, v1}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 357
    .line 358
    .line 359
    :cond_b
    invoke-virtual {v4}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 360
    .line 361
    .line 362
    move-result-object v1

    .line 363
    invoke-static {v6, v0, v1}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 364
    .line 365
    .line 366
    sget-object v0, Landroidx/compose/foundation/layout/BoxScopeInstance;->a:Landroidx/compose/foundation/layout/BoxScopeInstance;

    .line 367
    .line 368
    invoke-interface {p1}, Landroidx/compose/runtime/j;->j()V

    .line 369
    .line 370
    .line 371
    invoke-interface {p1}, Landroidx/compose/runtime/j;->q()V

    .line 372
    .line 373
    .line 374
    goto/16 :goto_1

    .line 375
    .line 376
    :cond_c
    const v0, 0x73968427

    .line 377
    .line 378
    .line 379
    invoke-interface {p1, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 380
    .line 381
    .line 382
    invoke-interface {p1}, Landroidx/compose/runtime/j;->q()V

    .line 383
    .line 384
    .line 385
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 386
    .line 387
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 388
    .line 389
    .line 390
    throw v0

    .line 391
    :cond_d
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 392
    .line 393
    .line 394
    move-result v0

    .line 395
    if-eqz v0, :cond_e

    .line 396
    .line 397
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 398
    .line 399
    .line 400
    :cond_e
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/runtime/j;

    .line 2
    .line 3
    check-cast p2, Ljava/lang/Number;

    .line 4
    .line 5
    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    invoke-virtual {p0, p1, p2}, LNB0/g$a;->e(Landroidx/compose/runtime/j;I)V

    .line 10
    .line 11
    .line 12
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p1
.end method
