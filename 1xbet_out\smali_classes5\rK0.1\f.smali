.class public final LrK0/f;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LsK0/f;",
        "LuK0/e;",
        "a",
        "(LsK0/f;)LuK0/e;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LsK0/f;)LuK0/e;
    .locals 14
    .param p0    # LsK0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LuK0/e;

    .line 2
    .line 3
    invoke-virtual {p0}, LsK0/f;->a()Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    :goto_0
    const/4 v3, 0x0

    .line 15
    goto :goto_1

    .line 16
    :cond_0
    const/4 v1, 0x0

    .line 17
    goto :goto_0

    .line 18
    :goto_1
    invoke-virtual {p0}, LsK0/f;->b()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    const/4 v11, 0x1

    .line 23
    const/4 v12, 0x0

    .line 24
    if-eqz v2, :cond_b

    .line 25
    .line 26
    invoke-virtual {p0}, LsK0/f;->c()Ljava/lang/Integer;

    .line 27
    .line 28
    .line 29
    move-result-object v4

    .line 30
    if-eqz v4, :cond_1

    .line 31
    .line 32
    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    .line 33
    .line 34
    .line 35
    move-result v4

    .line 36
    goto :goto_2

    .line 37
    :cond_1
    const/4 v4, 0x0

    .line 38
    :goto_2
    invoke-virtual {p0}, LsK0/f;->d()Ljava/lang/Integer;

    .line 39
    .line 40
    .line 41
    move-result-object v5

    .line 42
    if-eqz v5, :cond_2

    .line 43
    .line 44
    invoke-virtual {v5}, Ljava/lang/Integer;->intValue()I

    .line 45
    .line 46
    .line 47
    move-result v5

    .line 48
    goto :goto_3

    .line 49
    :cond_2
    const/4 v5, 0x0

    .line 50
    :goto_3
    invoke-virtual {p0}, LsK0/f;->e()Ljava/lang/Integer;

    .line 51
    .line 52
    .line 53
    move-result-object v6

    .line 54
    if-eqz v6, :cond_3

    .line 55
    .line 56
    invoke-virtual {v6}, Ljava/lang/Integer;->intValue()I

    .line 57
    .line 58
    .line 59
    move-result v3

    .line 60
    :cond_3
    invoke-virtual {p0}, LsK0/f;->f()Ljava/lang/String;

    .line 61
    .line 62
    .line 63
    move-result-object v6

    .line 64
    if-nez v6, :cond_4

    .line 65
    .line 66
    const-string v6, ""

    .line 67
    .line 68
    :cond_4
    invoke-virtual {p0}, LsK0/f;->g()LsK0/d;

    .line 69
    .line 70
    .line 71
    move-result-object v7

    .line 72
    if-eqz v7, :cond_5

    .line 73
    .line 74
    invoke-static {v7}, LrK0/g;->a(LsK0/d;)LuK0/f;

    .line 75
    .line 76
    .line 77
    move-result-object v7

    .line 78
    if-nez v7, :cond_6

    .line 79
    .line 80
    :cond_5
    sget-object v7, LuK0/f;->d:LuK0/f$a;

    .line 81
    .line 82
    invoke-virtual {v7}, LuK0/f$a;->a()LuK0/f;

    .line 83
    .line 84
    .line 85
    move-result-object v7

    .line 86
    :cond_6
    invoke-virtual {p0}, LsK0/f;->h()Ljava/lang/String;

    .line 87
    .line 88
    .line 89
    move-result-object v8

    .line 90
    if-eqz v8, :cond_a

    .line 91
    .line 92
    invoke-virtual {p0}, LsK0/f;->i()LsK0/d;

    .line 93
    .line 94
    .line 95
    move-result-object v9

    .line 96
    if-eqz v9, :cond_7

    .line 97
    .line 98
    invoke-static {v9}, LrK0/g;->a(LsK0/d;)LuK0/f;

    .line 99
    .line 100
    .line 101
    move-result-object v9

    .line 102
    if-nez v9, :cond_8

    .line 103
    .line 104
    :cond_7
    sget-object v9, LuK0/f;->d:LuK0/f$a;

    .line 105
    .line 106
    invoke-virtual {v9}, LuK0/f$a;->a()LuK0/f;

    .line 107
    .line 108
    .line 109
    move-result-object v9

    .line 110
    :cond_8
    invoke-virtual {p0}, LsK0/f;->j()Ljava/lang/String;

    .line 111
    .line 112
    .line 113
    move-result-object v10

    .line 114
    if-eqz v10, :cond_9

    .line 115
    .line 116
    move v13, v5

    .line 117
    move v5, v3

    .line 118
    move v3, v4

    .line 119
    move v4, v13

    .line 120
    invoke-direct/range {v0 .. v10}, LuK0/e;-><init>(ILjava/lang/String;IIILjava/lang/String;LuK0/f;Ljava/lang/String;LuK0/f;Ljava/lang/String;)V

    .line 121
    .line 122
    .line 123
    return-object v0

    .line 124
    :cond_9
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 125
    .line 126
    invoke-direct {p0, v12, v11, v12}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 127
    .line 128
    .line 129
    throw p0

    .line 130
    :cond_a
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 131
    .line 132
    invoke-direct {p0, v12, v11, v12}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 133
    .line 134
    .line 135
    throw p0

    .line 136
    :cond_b
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 137
    .line 138
    invoke-direct {p0, v12, v11, v12}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 139
    .line 140
    .line 141
    throw p0
.end method
