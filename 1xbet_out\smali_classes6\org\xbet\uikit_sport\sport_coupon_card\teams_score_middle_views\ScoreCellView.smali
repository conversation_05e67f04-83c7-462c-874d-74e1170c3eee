.class public final Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000t\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0007\n\u0002\u0008\u000e\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0007\u0018\u00002\u00020\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\n\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0011\u0010\r\u001a\u0004\u0018\u00010\u000cH\u0016\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u001f\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0017\u0010\u0016\u001a\u00020\u00112\u0006\u0010\u0015\u001a\u00020\u0014H\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0015\u0010\u001a\u001a\u00020\u00112\u0006\u0010\u0019\u001a\u00020\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0015\u0010\u001e\u001a\u00020\u00112\u0006\u0010\u001d\u001a\u00020\u001c\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u000f\u0010 \u001a\u00020\u0011H\u0002\u00a2\u0006\u0004\u0008 \u0010!J\u0017\u0010#\u001a\u00020\u00112\u0006\u0010\"\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008#\u0010$R\u0014\u0010(\u001a\u00020%8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u001b\u0010.\u001a\u00020)8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008*\u0010+\u001a\u0004\u0008,\u0010-R\u0014\u00101\u001a\u00020/8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u00100R\u0014\u00102\u001a\u00020/8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u00100R\u0014\u00105\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u00104R\u0014\u00107\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00104R\u0014\u00109\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00104R\u0016\u0010;\u001a\u00020/8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008:\u00100R\u0016\u0010=\u001a\u00020/8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008<\u00100R\u0016\u0010A\u001a\u00020>8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008?\u0010@R\u0014\u0010E\u001a\u00020B8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008C\u0010DR\u0014\u0010I\u001a\u00020F8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010HR\u0016\u0010L\u001a\u00020\u00188\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008J\u0010K\u00a8\u0006M"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "getViewWidth",
        "()I",
        "Landroid/content/res/ColorStateList;",
        "getBackgroundTintList",
        "()Landroid/content/res/ColorStateList;",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "",
        "score",
        "setTeamScore",
        "(Ljava/lang/String;)V",
        "Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;",
        "scoreState",
        "setTeamScoreState",
        "(Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;)V",
        "d",
        "()V",
        "totalWidth",
        "c",
        "(I)V",
        "Landroid/graphics/RectF;",
        "a",
        "Landroid/graphics/RectF;",
        "rectF",
        "Lorg/xbet/uikit/utils/e;",
        "b",
        "Lkotlin/j;",
        "getBackgroundTintHelper",
        "()Lorg/xbet/uikit/utils/e;",
        "backgroundTintHelper",
        "",
        "F",
        "borderWidth",
        "borderCornerRadius",
        "e",
        "I",
        "minimalCellWidth",
        "f",
        "cellHeight",
        "g",
        "widthIncreaseStepPerDigit",
        "h",
        "scoreTextLeftBound",
        "i",
        "scoreTextBottomBound",
        "",
        "j",
        "Z",
        "hasBorder",
        "Landroid/graphics/Paint;",
        "k",
        "Landroid/graphics/Paint;",
        "borderPaint",
        "Landroid/text/TextPaint;",
        "l",
        "Landroid/text/TextPaint;",
        "teamScoreTextPaint",
        "m",
        "Ljava/lang/String;",
        "teamScore",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Landroid/graphics/RectF;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:F

.field public final d:F

.field public final e:I

.field public final f:I

.field public final g:I

.field public h:F

.field public i:F

.field public j:Z

.field public final k:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public m:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 5
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance v0, Landroid/graphics/RectF;

    invoke-direct {v0}, Landroid/graphics/RectF;-><init>()V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->a:Landroid/graphics/RectF;

    .line 6
    new-instance v0, LZ31/b;

    invoke-direct {v0, p0}, LZ31/b;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->b:Lkotlin/j;

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_1:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    int-to-float v0, v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->c:F

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->radius_6:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    int-to-float v1, v1

    iput v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->d:F

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->size_24:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->e:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->size_24:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->f:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->space_8:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->g:I

    const/4 v1, 0x1

    .line 12
    iput-boolean v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->j:Z

    .line 13
    new-instance v2, Landroid/graphics/Paint;

    invoke-direct {v2}, Landroid/graphics/Paint;-><init>()V

    .line 14
    invoke-virtual {v2, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 15
    sget-object v3, Landroid/graphics/Paint$Style;->STROKE:Landroid/graphics/Paint$Style;

    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 16
    invoke-virtual {v2, v0}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 17
    sget v0, LlZ0/d;->uikitSeparator:I

    const/4 v3, 0x0

    const/4 v4, 0x2

    invoke-static {p1, v0, v3, v4, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v0

    invoke-virtual {v2, v0}, Landroid/graphics/Paint;->setColor(I)V

    .line 18
    iput-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->k:Landroid/graphics/Paint;

    .line 19
    new-instance v0, Landroid/text/TextPaint;

    invoke-direct {v0}, Landroid/text/TextPaint;-><init>()V

    .line 20
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 21
    sget v2, LlZ0/n;->TextStyle_Caption_Bold_L_TextPrimary:I

    invoke-static {v0, p1, v2}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    .line 22
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->l:Landroid/text/TextPaint;

    .line 23
    const-string v0, ""

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->m:Ljava/lang/String;

    const/4 v0, 0x0

    .line 24
    invoke-virtual {p0, v0}, Landroid/view/View;->setWillNotDraw(Z)V

    .line 25
    sget-object v2, Lm31/g;->ScoreCellView:[I

    .line 26
    invoke-virtual {p1, p2, v2, v0, v0}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object v0

    .line 27
    sget v2, Lm31/g;->ScoreCellView_hasBorder:I

    invoke-virtual {v0, v2, v1}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    move-result v1

    iput-boolean v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->j:Z

    .line 28
    invoke-virtual {v0}, Landroid/content/res/TypedArray;->recycle()V

    .line 29
    sget v0, LlZ0/h;->rounded_background_6:I

    invoke-static {p1, v0}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 30
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    move-result-object p1

    invoke-virtual {p1, p2, p3}, Lorg/xbet/uikit/utils/e;->a(Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic a(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;)Lorg/xbet/uikit/utils/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->b(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;)Lorg/xbet/uikit/utils/e;

    move-result-object p0

    return-object p0
.end method

.method public static final b(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;)Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/uikit/utils/e;-><init>(Landroid/view/View;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method private final getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->b:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/utils/e;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getViewWidth()I
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->m:Ljava/lang/String;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x2

    .line 8
    if-gt v0, v1, :cond_0

    .line 9
    .line 10
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->e:I

    .line 11
    .line 12
    return v0

    .line 13
    :cond_0
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->e:I

    .line 14
    .line 15
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->m:Ljava/lang/String;

    .line 16
    .line 17
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    sub-int/2addr v2, v1

    .line 22
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->g:I

    .line 23
    .line 24
    mul-int v2, v2, v1

    .line 25
    .line 26
    add-int/2addr v0, v2

    .line 27
    return v0
.end method


# virtual methods
.method public final c(I)V
    .locals 3

    .line 1
    const/4 v0, 0x2

    .line 2
    div-int/2addr p1, v0

    .line 3
    int-to-float p1, p1

    .line 4
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->l:Landroid/text/TextPaint;

    .line 5
    .line 6
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->m:Ljava/lang/String;

    .line 7
    .line 8
    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 9
    .line 10
    .line 11
    move-result v1

    .line 12
    int-to-float v2, v0

    .line 13
    div-float/2addr v1, v2

    .line 14
    sub-float/2addr p1, v1

    .line 15
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->h:F

    .line 16
    .line 17
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->f:I

    .line 18
    .line 19
    div-int/2addr p1, v0

    .line 20
    int-to-float p1, p1

    .line 21
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->l:Landroid/text/TextPaint;

    .line 22
    .line 23
    invoke-virtual {v0}, Landroid/graphics/Paint;->getTextSize()F

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    div-float/2addr v0, v2

    .line 28
    add-float/2addr p1, v0

    .line 29
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->c:F

    .line 30
    .line 31
    sub-float/2addr p1, v0

    .line 32
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->i:F

    .line 33
    .line 34
    return-void
.end method

.method public final d()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->a:Landroid/graphics/RectF;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    iput v1, v0, Landroid/graphics/RectF;->top:F

    .line 5
    .line 6
    iput v1, v0, Landroid/graphics/RectF;->left:F

    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 9
    .line 10
    .line 11
    move-result v1

    .line 12
    int-to-float v1, v1

    .line 13
    iput v1, v0, Landroid/graphics/RectF;->right:F

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->a:Landroid/graphics/RectF;

    .line 16
    .line 17
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    int-to-float v1, v1

    .line 22
    iput v1, v0, Landroid/graphics/RectF;->bottom:F

    .line 23
    .line 24
    return-void
.end method

.method public getBackgroundTintList()Landroid/content/res/ColorStateList;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/e;->b()Landroid/content/res/ColorStateList;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 4
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->m:Ljava/lang/String;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-lez v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->m:Ljava/lang/String;

    .line 10
    .line 11
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->h:F

    .line 12
    .line 13
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->i:F

    .line 14
    .line 15
    iget-object v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->l:Landroid/text/TextPaint;

    .line 16
    .line 17
    invoke-virtual {p1, v0, v1, v2, v3}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    .line 18
    .line 19
    .line 20
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->j:Z

    .line 21
    .line 22
    if-eqz v0, :cond_0

    .line 23
    .line 24
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->d()V

    .line 25
    .line 26
    .line 27
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->a:Landroid/graphics/RectF;

    .line 28
    .line 29
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->d:F

    .line 30
    .line 31
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->k:Landroid/graphics/Paint;

    .line 32
    .line 33
    invoke-virtual {p1, v0, v1, v1, v2}, Landroid/graphics/Canvas;->drawRoundRect(Landroid/graphics/RectF;FFLandroid/graphics/Paint;)V

    .line 34
    .line 35
    .line 36
    :cond_0
    return-void
.end method

.method public onMeasure(II)V
    .locals 4

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getMode(I)I

    .line 2
    .line 3
    .line 4
    move-result p2

    .line 5
    const/high16 v0, 0x40000000    # 2.0f

    .line 6
    .line 7
    if-ne p2, v0, :cond_0

    .line 8
    .line 9
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->getViewWidth()I

    .line 15
    .line 16
    .line 17
    move-result p1

    .line 18
    :goto_0
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->m:Ljava/lang/String;

    .line 23
    .line 24
    invoke-interface {p2}, Ljava/lang/CharSequence;->length()I

    .line 25
    .line 26
    .line 27
    move-result p2

    .line 28
    const/4 v1, 0x0

    .line 29
    if-lez p2, :cond_1

    .line 30
    .line 31
    goto :goto_1

    .line 32
    :cond_1
    move-object p1, v1

    .line 33
    :goto_1
    const/4 p2, 0x0

    .line 34
    if-eqz p1, :cond_2

    .line 35
    .line 36
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 37
    .line 38
    .line 39
    move-result p1

    .line 40
    goto :goto_2

    .line 41
    :cond_2
    const/4 p1, 0x0

    .line 42
    :goto_2
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->f:I

    .line 43
    .line 44
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 45
    .line 46
    .line 47
    move-result-object v2

    .line 48
    iget-object v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->m:Ljava/lang/String;

    .line 49
    .line 50
    invoke-interface {v3}, Ljava/lang/CharSequence;->length()I

    .line 51
    .line 52
    .line 53
    move-result v3

    .line 54
    if-lez v3, :cond_3

    .line 55
    .line 56
    move-object v1, v2

    .line 57
    :cond_3
    if-eqz v1, :cond_4

    .line 58
    .line 59
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 60
    .line 61
    .line 62
    move-result p2

    .line 63
    :cond_4
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->c(I)V

    .line 64
    .line 65
    .line 66
    invoke-static {p1, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 67
    .line 68
    .line 69
    move-result p1

    .line 70
    invoke-static {p2, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 71
    .line 72
    .line 73
    move-result p2

    .line 74
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 75
    .line 76
    .line 77
    return-void
.end method

.method public final setTeamScore(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->m:Ljava/lang/String;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final setTeamScoreState(Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;)V
    .locals 5
    .param p1    # Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->l:Landroid/text/TextPaint;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->getColorResAttr()I

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    const/4 v3, 0x0

    .line 12
    const/4 v4, 0x2

    .line 13
    invoke-static {v1, v2, v3, v4, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setColor(I)V

    .line 18
    .line 19
    .line 20
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->l:Landroid/text/TextPaint;

    .line 21
    .line 22
    sget-object v1, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->INACTIVE:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 23
    .line 24
    if-ne p1, v1, :cond_0

    .line 25
    .line 26
    const/16 p1, 0x7f

    .line 27
    .line 28
    goto :goto_0

    .line 29
    :cond_0
    const/16 p1, 0xff

    .line 30
    .line 31
    :goto_0
    invoke-virtual {v0, p1}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 35
    .line 36
    .line 37
    return-void
.end method
