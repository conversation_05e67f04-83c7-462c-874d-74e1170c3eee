.class public final Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u009a\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\"\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u000e\u0018\u0000 Z2\u00020\u0001:\u0001[B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003B\u0011\u0008\u0016\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0002\u0010\u0006J\u000f\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\u0003J\u0017\u0010\u000b\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u000f\u001a\u00020\u00072\u0006\u0010\u000e\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u001d\u0010\u0014\u001a\u00020\u00072\u000c\u0010\u0013\u001a\u0008\u0012\u0004\u0012\u00020\u00120\u0011H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u001d\u0010\u0019\u001a\u00020\u00072\u000c\u0010\u0018\u001a\u0008\u0012\u0004\u0012\u00020\u00170\u0016H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u0017\u0010\u001d\u001a\u00020\u00072\u0006\u0010\u001c\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0017\u0010!\u001a\u00020\u00072\u0006\u0010 \u001a\u00020\u001fH\u0002\u00a2\u0006\u0004\u0008!\u0010\"J\u0017\u0010%\u001a\u00020\u00072\u0006\u0010$\u001a\u00020#H\u0002\u00a2\u0006\u0004\u0008%\u0010&J\u0019\u0010)\u001a\u00020\u00072\u0008\u0010(\u001a\u0004\u0018\u00010\'H\u0014\u00a2\u0006\u0004\u0008)\u0010*R\"\u00102\u001a\u00020+8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008,\u0010-\u001a\u0004\u0008.\u0010/\"\u0004\u00080\u00101R\u001a\u00108\u001a\u0002038\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u00084\u00105\u001a\u0004\u00086\u00107R\u001b\u0010>\u001a\u0002098BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008:\u0010;\u001a\u0004\u0008<\u0010=R\u001b\u0010C\u001a\u00020?8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008@\u0010;\u001a\u0004\u0008A\u0010BR\u001b\u0010H\u001a\u00020D8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008E\u0010;\u001a\u0004\u0008F\u0010GR\u001b\u0010M\u001a\u00020I8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008J\u0010;\u001a\u0004\u0008K\u0010LR\u001b\u0010S\u001a\u00020N8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008O\u0010P\u001a\u0004\u0008Q\u0010RR+\u0010\u0005\u001a\u00020\u00042\u0006\u0010T\u001a\u00020\u00048B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008U\u0010V\u001a\u0004\u0008W\u0010X\"\u0004\u0008Y\u0010\u0006\u00a8\u0006\\"
    }
    d2 = {
        "Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;",
        "screenType",
        "(Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;)V",
        "",
        "d3",
        "Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$c;",
        "action",
        "R2",
        "(Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$c;)V",
        "",
        "message",
        "b3",
        "(Ljava/lang/String;)V",
        "",
        "Lwl0/d;",
        "items",
        "W2",
        "(Ljava/util/List;)V",
        "",
        "",
        "ids",
        "V2",
        "(Ljava/util/Set;)V",
        "",
        "count",
        "Y2",
        "(I)V",
        "Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b;",
        "state",
        "S2",
        "(Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b;)V",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "lottieConfig",
        "a3",
        "(Lorg/xbet/uikit/components/lottie_empty/n;)V",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "LzX0/k;",
        "i0",
        "LzX0/k;",
        "N2",
        "()LzX0/k;",
        "setSnackbarManager",
        "(LzX0/k;)V",
        "snackbarManager",
        "",
        "j0",
        "Z",
        "r2",
        "()Z",
        "showNavBar",
        "Lul0/h;",
        "k0",
        "Lkotlin/j;",
        "O2",
        "()Lul0/h;",
        "sportsResultsComponent",
        "Lorg/xbet/results/impl/presentation/sports/a;",
        "l0",
        "K2",
        "()Lorg/xbet/results/impl/presentation/sports/a;",
        "adapter",
        "Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;",
        "m0",
        "Q2",
        "()Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;",
        "viewModel",
        "Lorg/xbet/results/impl/presentation/screen/h;",
        "n0",
        "M2",
        "()Lorg/xbet/results/impl/presentation/screen/h;",
        "shareViewModel",
        "Lml0/l;",
        "o0",
        "LRc/c;",
        "P2",
        "()Lml0/l;",
        "viewBinding",
        "<set-?>",
        "b1",
        "Lorg/xbet/results/impl/presentation/utils/a;",
        "L2",
        "()Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;",
        "X2",
        "k1",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final k1:Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic v1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final b1:Lorg/xbet/results/impl/presentation/utils/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public i0:LzX0/k;

.field public final j0:Z

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;

    .line 4
    .line 5
    const-string v2, "viewBinding"

    .line 6
    .line 7
    const-string v3, "getViewBinding()Lorg/xbet/results/impl/databinding/FragmentSportsResultsBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "screenType"

    .line 20
    .line 21
    const-string v5, "getScreenType()Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    const/4 v2, 0x2

    .line 31
    new-array v2, v2, [Lkotlin/reflect/m;

    .line 32
    .line 33
    aput-object v0, v2, v4

    .line 34
    .line 35
    const/4 v0, 0x1

    .line 36
    aput-object v1, v2, v0

    .line 37
    .line 38
    sput-object v2, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->v1:[Lkotlin/reflect/m;

    .line 39
    .line 40
    new-instance v0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$a;

    .line 41
    .line 42
    const/4 v1, 0x0

    .line 43
    invoke-direct {v0, v1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    sput-object v0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->k1:Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$a;

    .line 47
    .line 48
    return-void
.end method

.method public constructor <init>()V
    .locals 7

    .line 1
    sget v0, Lhl0/c;->fragment_sports_results:I

    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    const/4 v0, 0x1

    .line 2
    iput-boolean v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->j0:Z

    .line 3
    new-instance v0, Lorg/xbet/results/impl/presentation/sports/d;

    invoke-direct {v0, p0}, Lorg/xbet/results/impl/presentation/sports/d;-><init>(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)V

    .line 4
    sget-object v1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 5
    iput-object v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->k0:Lkotlin/j;

    .line 6
    new-instance v0, Lorg/xbet/results/impl/presentation/sports/e;

    invoke-direct {v0, p0}, Lorg/xbet/results/impl/presentation/sports/e;-><init>(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)V

    .line 7
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 8
    iput-object v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->l0:Lkotlin/j;

    .line 9
    new-instance v0, Lorg/xbet/results/impl/presentation/sports/f;

    invoke-direct {v0, p0}, Lorg/xbet/results/impl/presentation/sports/f;-><init>(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)V

    .line 10
    new-instance v2, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$b;

    invoke-direct {v2, p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$b;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 11
    new-instance v3, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$c;

    invoke-direct {v3, v0, v2}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$c;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V

    .line 12
    new-instance v0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$savedStateViewModels$default$3;

    invoke-direct {v0, p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$savedStateViewModels$default$3;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 13
    new-instance v2, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$savedStateViewModels$default$4;

    invoke-direct {v2, v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$savedStateViewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;)V

    invoke-static {v1, v2}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 14
    const-class v2, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;

    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    move-result-object v2

    new-instance v4, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$savedStateViewModels$default$5;

    invoke-direct {v4, v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$savedStateViewModels$default$5;-><init>(Lkotlin/j;)V

    new-instance v5, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$savedStateViewModels$default$6;

    const/4 v6, 0x0

    invoke-direct {v5, v6, v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$savedStateViewModels$default$6;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 15
    invoke-static {p0, v2, v4, v5, v3}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 16
    iput-object v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->m0:Lkotlin/j;

    .line 17
    new-instance v0, Lorg/xbet/results/impl/presentation/sports/g;

    invoke-direct {v0, p0}, Lorg/xbet/results/impl/presentation/sports/g;-><init>(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)V

    .line 18
    new-instance v2, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$viewModels$default$1;

    invoke-direct {v2, v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$viewModels$default$1;-><init>(Lkotlin/jvm/functions/Function0;)V

    invoke-static {v1, v2}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 19
    const-class v1, Lorg/xbet/results/impl/presentation/screen/h;

    invoke-static {v1}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    move-result-object v1

    new-instance v2, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$viewModels$default$2;

    invoke-direct {v2, v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/j;)V

    new-instance v3, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$viewModels$default$3;

    invoke-direct {v3, v6, v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    new-instance v4, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$viewModels$default$4;

    invoke-direct {v4, p0, v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$special$$inlined$viewModels$default$4;-><init>(Landroidx/fragment/app/Fragment;Lkotlin/j;)V

    invoke-static {p0, v1, v2, v3, v4}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 20
    iput-object v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->n0:Lkotlin/j;

    .line 21
    sget-object v0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$viewBinding$2;->INSTANCE:Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$viewBinding$2;

    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->o0:LRc/c;

    .line 22
    new-instance v0, Lorg/xbet/results/impl/presentation/utils/a;

    const-string v1, "KEY_SCREEN_TYPE"

    invoke-direct {v0, v1}, Lorg/xbet/results/impl/presentation/utils/a;-><init>(Ljava/lang/String;)V

    iput-object v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->b1:Lorg/xbet/results/impl/presentation/utils/a;

    return-void
.end method

.method public constructor <init>(Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;)V
    .locals 0
    .param p1    # Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 23
    invoke-direct {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;-><init>()V

    .line 24
    invoke-virtual {p0, p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->X2(Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;)V

    return-void
.end method

.method public static synthetic A2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)Landroidx/lifecycle/h0;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->Z2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)Landroidx/lifecycle/h0;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->T2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic C2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->U2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic D2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)Lul0/h;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->c3(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)Lul0/h;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic E2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$c;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->e3(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$c;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic F2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->f3(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic G2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Ljava/util/Set;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->g3(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Ljava/util/Set;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic H2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->h3(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic I2(Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->i3(Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final J2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)Lorg/xbet/results/impl/presentation/sports/a;
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/results/impl/presentation/sports/a;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$adapter$2$1;

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->Q2()Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-direct {v1, v2}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$adapter$2$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    new-instance v2, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$adapter$2$2;

    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->Q2()Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;

    .line 15
    .line 16
    .line 17
    move-result-object p0

    .line 18
    invoke-direct {v2, p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$adapter$2$2;-><init>(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    invoke-direct {v0, v1, v2}, Lorg/xbet/results/impl/presentation/sports/a;-><init>(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;)V

    .line 22
    .line 23
    .line 24
    return-object v0
.end method

.method private final M2()Lorg/xbet/results/impl/presentation/screen/h;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->n0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/results/impl/presentation/screen/h;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final T2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->Q2()Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;->c1()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final U2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->Q2()Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;->V3()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method private final V2(Ljava/util/Set;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Set<",
            "Ljava/lang/Long;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->K2()Lorg/xbet/results/impl/presentation/sports/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {p1}, Ljava/util/Set;->size()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    const/16 v2, 0xa

    .line 10
    .line 11
    if-ne v1, v2, :cond_0

    .line 12
    .line 13
    const/4 v1, 0x1

    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/4 v1, 0x0

    .line 16
    :goto_0
    invoke-virtual {v0, p1, v1}, Lorg/xbet/results/impl/presentation/sports/a;->r(Ljava/util/Set;Z)V

    .line 17
    .line 18
    .line 19
    check-cast p1, Ljava/util/Collection;

    .line 20
    .line 21
    invoke-interface {p1}, Ljava/util/Collection;->size()I

    .line 22
    .line 23
    .line 24
    move-result p1

    .line 25
    invoke-direct {p0, p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->Y2(I)V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method private final Y2(I)V
    .locals 6

    .line 1
    const/4 v0, 0x1

    .line 2
    const/4 v1, 0x0

    .line 3
    if-lez p1, :cond_0

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    goto :goto_0

    .line 7
    :cond_0
    const/4 v2, 0x0

    .line 8
    :goto_0
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->P2()Lml0/l;

    .line 9
    .line 10
    .line 11
    move-result-object v3

    .line 12
    iget-object v3, v3, Lml0/l;->d:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 13
    .line 14
    sget v4, Lpb/k;->seleceted_amount:I

    .line 15
    .line 16
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    const/4 v5, 0x2

    .line 21
    new-array v5, v5, [Ljava/lang/Object;

    .line 22
    .line 23
    aput-object p1, v5, v1

    .line 24
    .line 25
    const-string p1, "10"

    .line 26
    .line 27
    aput-object p1, v5, v0

    .line 28
    .line 29
    invoke-virtual {p0, v4, v5}, Landroidx/fragment/app/Fragment;->getString(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-virtual {v3, p1}, Lorg/xbet/uikit/components/buttons/DSButton;->v(Ljava/lang/CharSequence;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->P2()Lml0/l;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    iget-object p1, p1, Lml0/l;->h:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 41
    .line 42
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 43
    .line 44
    .line 45
    move-result p1

    .line 46
    if-nez p1, :cond_1

    .line 47
    .line 48
    goto :goto_1

    .line 49
    :cond_1
    const/4 v0, 0x0

    .line 50
    :goto_1
    if-eq v0, v2, :cond_4

    .line 51
    .line 52
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->P2()Lml0/l;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    iget-object p1, p1, Lml0/l;->h:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 57
    .line 58
    const/16 v0, 0x8

    .line 59
    .line 60
    if-eqz v2, :cond_2

    .line 61
    .line 62
    const/4 v3, 0x0

    .line 63
    goto :goto_2

    .line 64
    :cond_2
    const/16 v3, 0x8

    .line 65
    .line 66
    :goto_2
    invoke-virtual {p1, v3}, Landroid/view/View;->setVisibility(I)V

    .line 67
    .line 68
    .line 69
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->P2()Lml0/l;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    iget-object p1, p1, Lml0/l;->i:Landroid/widget/Space;

    .line 74
    .line 75
    if-eqz v2, :cond_3

    .line 76
    .line 77
    goto :goto_3

    .line 78
    :cond_3
    const/16 v1, 0x8

    .line 79
    .line 80
    :goto_3
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 81
    .line 82
    .line 83
    :cond_4
    return-void
.end method

.method public static final Z2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)Landroidx/lifecycle/h0;
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final a3(Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->P2()Lml0/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lml0/l;->e:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 8
    .line 9
    .line 10
    const/4 p1, 0x0

    .line 11
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method private final b3(Ljava/lang/String;)V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->N2()LzX0/k;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Ly01/g;

    .line 6
    .line 7
    sget-object v2, Ly01/i$c;->a:Ly01/i$c;

    .line 8
    .line 9
    const/16 v8, 0x3c

    .line 10
    .line 11
    const/4 v9, 0x0

    .line 12
    const/4 v4, 0x0

    .line 13
    const/4 v5, 0x0

    .line 14
    const/4 v6, 0x0

    .line 15
    const/4 v7, 0x0

    .line 16
    move-object v3, p1

    .line 17
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 18
    .line 19
    .line 20
    const/16 v10, 0x1fc

    .line 21
    .line 22
    const/4 v11, 0x0

    .line 23
    const/4 v3, 0x0

    .line 24
    const/4 v5, 0x0

    .line 25
    const/4 v6, 0x0

    .line 26
    const/4 v8, 0x0

    .line 27
    move-object v2, p0

    .line 28
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public static final c3(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)Lul0/h;
    .locals 4

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    instance-of v1, v0, LQW0/b;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    check-cast v0, LQW0/b;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    move-object v0, v2

    .line 18
    :goto_0
    const-class v1, Lul0/i;

    .line 19
    .line 20
    if-eqz v0, :cond_3

    .line 21
    .line 22
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, LBc/a;

    .line 31
    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, LQW0/a;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-object v0, v2

    .line 42
    :goto_1
    instance-of v3, v0, Lul0/i;

    .line 43
    .line 44
    if-nez v3, :cond_2

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_2
    move-object v2, v0

    .line 48
    :goto_2
    check-cast v2, Lul0/i;

    .line 49
    .line 50
    if-eqz v2, :cond_3

    .line 51
    .line 52
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->L2()Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;

    .line 57
    .line 58
    .line 59
    move-result-object p0

    .line 60
    invoke-virtual {v2, v0, p0}, Lul0/i;->a(LwX0/c;Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;)Lul0/h;

    .line 61
    .line 62
    .line 63
    move-result-object p0

    .line 64
    return-object p0

    .line 65
    :cond_3
    new-instance p0, Ljava/lang/IllegalStateException;

    .line 66
    .line 67
    new-instance v0, Ljava/lang/StringBuilder;

    .line 68
    .line 69
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 70
    .line 71
    .line 72
    const-string v2, "Cannot create dependency "

    .line 73
    .line 74
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 75
    .line 76
    .line 77
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 78
    .line 79
    .line 80
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    invoke-direct {p0, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 89
    .line 90
    .line 91
    throw p0
.end method

.method private final d3()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->Q2()Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;->O3()Lkotlinx/coroutines/flow/e;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v6, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$1;

    .line 12
    .line 13
    invoke-direct {v6, v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$1;-><init>(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 17
    .line 18
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 19
    .line 20
    .line 21
    move-result-object v4

    .line 22
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 23
    .line 24
    .line 25
    move-result-object v11

    .line 26
    new-instance v2, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$$inlined$observeWithLifecycle$default$1;

    .line 27
    .line 28
    const/4 v7, 0x0

    .line 29
    move-object v5, v10

    .line 30
    invoke-direct/range {v2 .. v7}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 31
    .line 32
    .line 33
    const/4 v15, 0x3

    .line 34
    const/16 v16, 0x0

    .line 35
    .line 36
    const/4 v12, 0x0

    .line 37
    const/4 v13, 0x0

    .line 38
    move-object v14, v2

    .line 39
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    invoke-virtual {v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->Q2()Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    invoke-virtual {v1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;->N3()Lkotlinx/coroutines/flow/e;

    .line 47
    .line 48
    .line 49
    move-result-object v8

    .line 50
    new-instance v11, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$2;

    .line 51
    .line 52
    invoke-direct {v11, v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$2;-><init>(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 56
    .line 57
    .line 58
    move-result-object v9

    .line 59
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    new-instance v4, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$$inlined$observeWithLifecycle$default$2;

    .line 64
    .line 65
    move-object v7, v4

    .line 66
    invoke-direct/range {v7 .. v12}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 67
    .line 68
    .line 69
    const/4 v5, 0x3

    .line 70
    const/4 v6, 0x0

    .line 71
    const/4 v2, 0x0

    .line 72
    const/4 v3, 0x0

    .line 73
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 74
    .line 75
    .line 76
    invoke-virtual {v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->Q2()Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;

    .line 77
    .line 78
    .line 79
    move-result-object v1

    .line 80
    invoke-virtual {v1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;->L3()Lkotlinx/coroutines/flow/e;

    .line 81
    .line 82
    .line 83
    move-result-object v8

    .line 84
    new-instance v11, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$3;

    .line 85
    .line 86
    invoke-direct {v11, v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$3;-><init>(Ljava/lang/Object;)V

    .line 87
    .line 88
    .line 89
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 90
    .line 91
    .line 92
    move-result-object v9

    .line 93
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 94
    .line 95
    .line 96
    move-result-object v1

    .line 97
    new-instance v4, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$$inlined$observeWithLifecycle$default$3;

    .line 98
    .line 99
    move-object v7, v4

    .line 100
    invoke-direct/range {v7 .. v12}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 101
    .line 102
    .line 103
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 104
    .line 105
    .line 106
    invoke-virtual {v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->Q2()Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;

    .line 107
    .line 108
    .line 109
    move-result-object v1

    .line 110
    invoke-virtual {v1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;->M3()Lkotlinx/coroutines/flow/e;

    .line 111
    .line 112
    .line 113
    move-result-object v8

    .line 114
    new-instance v11, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$4;

    .line 115
    .line 116
    invoke-direct {v11, v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$4;-><init>(Ljava/lang/Object;)V

    .line 117
    .line 118
    .line 119
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 120
    .line 121
    .line 122
    move-result-object v9

    .line 123
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 124
    .line 125
    .line 126
    move-result-object v1

    .line 127
    new-instance v4, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$$inlined$observeWithLifecycle$default$4;

    .line 128
    .line 129
    move-object v7, v4

    .line 130
    invoke-direct/range {v7 .. v12}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$$inlined$observeWithLifecycle$default$4;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 131
    .line 132
    .line 133
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 134
    .line 135
    .line 136
    invoke-direct {v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->M2()Lorg/xbet/results/impl/presentation/screen/h;

    .line 137
    .line 138
    .line 139
    move-result-object v1

    .line 140
    invoke-virtual {v1}, Lorg/xbet/results/impl/presentation/screen/h;->o3()Lkotlinx/coroutines/flow/f0;

    .line 141
    .line 142
    .line 143
    move-result-object v8

    .line 144
    new-instance v11, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$5;

    .line 145
    .line 146
    invoke-virtual {v0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->Q2()Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;

    .line 147
    .line 148
    .line 149
    move-result-object v1

    .line 150
    invoke-direct {v11, v1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$5;-><init>(Ljava/lang/Object;)V

    .line 151
    .line 152
    .line 153
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 154
    .line 155
    .line 156
    move-result-object v9

    .line 157
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 158
    .line 159
    .line 160
    move-result-object v1

    .line 161
    new-instance v4, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$$inlined$observeWithLifecycle$default$5;

    .line 162
    .line 163
    move-object v7, v4

    .line 164
    invoke-direct/range {v7 .. v12}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment$subscribeEvents$$inlined$observeWithLifecycle$default$5;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 165
    .line 166
    .line 167
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 168
    .line 169
    .line 170
    return-void
.end method

.method public static final synthetic e3(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$c;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->R2(Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$c;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic f3(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->S2(Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic g3(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Ljava/util/Set;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->V2(Ljava/util/Set;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic h3(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->W2(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic i3(Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;->k4(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final j3(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)Lorg/xbet/ui_common/viewmodel/core/e;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->O2()Lul0/h;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-interface {p0}, Lul0/h;->a()Lul0/k;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    return-object p0
.end method

.method public static synthetic y2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)Lorg/xbet/results/impl/presentation/sports/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->J2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)Lorg/xbet/results/impl/presentation/sports/a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)Lorg/xbet/ui_common/viewmodel/core/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->j3(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)Lorg/xbet/ui_common/viewmodel/core/e;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final K2()Lorg/xbet/results/impl/presentation/sports/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->l0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/results/impl/presentation/sports/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public final L2()Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->b1:Lorg/xbet/results/impl/presentation/utils/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, Lorg/xbet/results/impl/presentation/utils/a;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    return-object v0
.end method

.method public final N2()LzX0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->i0:LzX0/k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final O2()Lul0/h;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lul0/h;

    .line 8
    .line 9
    return-object v0
.end method

.method public final P2()Lml0/l;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->o0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lml0/l;

    .line 13
    .line 14
    return-object v0
.end method

.method public final Q2()Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->m0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final R2(Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$c;)V
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$c$c;->a:Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$c$c;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->P2()Lml0/l;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    iget-object p1, p1, Lml0/l;->g:Lorg/xbet/ui_common/viewcomponents/swiperefreshlayout/SwipeRefreshLayout;

    .line 14
    .line 15
    const/4 v0, 0x1

    .line 16
    invoke-virtual {p1, v0}, Landroidx/swiperefreshlayout/widget/SwipeRefreshLayout;->setRefreshing(Z)V

    .line 17
    .line 18
    .line 19
    return-void

    .line 20
    :cond_0
    sget-object v0, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$c$a;->a:Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$c$a;

    .line 21
    .line 22
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-eqz v0, :cond_1

    .line 27
    .line 28
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->P2()Lml0/l;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    iget-object p1, p1, Lml0/l;->g:Lorg/xbet/ui_common/viewcomponents/swiperefreshlayout/SwipeRefreshLayout;

    .line 33
    .line 34
    const/4 v0, 0x0

    .line 35
    invoke-virtual {p1, v0}, Landroidx/swiperefreshlayout/widget/SwipeRefreshLayout;->setRefreshing(Z)V

    .line 36
    .line 37
    .line 38
    return-void

    .line 39
    :cond_1
    instance-of v0, p1, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$c$b;

    .line 40
    .line 41
    if-eqz v0, :cond_2

    .line 42
    .line 43
    check-cast p1, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$c$b;

    .line 44
    .line 45
    invoke-virtual {p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$c$b;->a()Ljava/lang/String;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    invoke-direct {p0, p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->b3(Ljava/lang/String;)V

    .line 50
    .line 51
    .line 52
    return-void

    .line 53
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 54
    .line 55
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 56
    .line 57
    .line 58
    throw p1
.end method

.method public final S2(Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b;)V
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b$c;->a:Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b$c;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->P2()Lml0/l;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    iget-object p1, p1, Lml0/l;->e:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 14
    .line 15
    const/16 v0, 0x8

    .line 16
    .line 17
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 18
    .line 19
    .line 20
    return-void

    .line 21
    :cond_0
    instance-of v0, p1, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b$a;

    .line 22
    .line 23
    if-eqz v0, :cond_1

    .line 24
    .line 25
    check-cast p1, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b$a;

    .line 26
    .line 27
    invoke-virtual {p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b$a;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-direct {p0, p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->a3(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 32
    .line 33
    .line 34
    return-void

    .line 35
    :cond_1
    instance-of v0, p1, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b$b;

    .line 36
    .line 37
    if-eqz v0, :cond_2

    .line 38
    .line 39
    check-cast p1, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b$b;

    .line 40
    .line 41
    invoke-virtual {p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel$b$b;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    invoke-direct {p0, p1}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->a3(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 46
    .line 47
    .line 48
    return-void

    .line 49
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 50
    .line 51
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 52
    .line 53
    .line 54
    throw p1
.end method

.method public final W2(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lwl0/d;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->K2()Lorg/xbet/results/impl/presentation/sports/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/results/impl/presentation/sports/a;->s(Ljava/util/List;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final X2(Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->b1:Lorg/xbet/results/impl/presentation/utils/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, Lorg/xbet/results/impl/presentation/utils/a;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public r2()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->j0:Z

    .line 2
    .line 3
    return v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 4

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->P2()Lml0/l;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object v0, p1, Lml0/l;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 9
    .line 10
    new-instance v1, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 11
    .line 12
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getContext()Landroid/content/Context;

    .line 13
    .line 14
    .line 15
    move-result-object v2

    .line 16
    invoke-direct {v1, v2}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p1, Lml0/l;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 23
    .line 24
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->K2()Lorg/xbet/results/impl/presentation/sports/a;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 29
    .line 30
    .line 31
    iget-object v0, p1, Lml0/l;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 32
    .line 33
    invoke-static {v0}, Lorg/xbet/ui_common/utils/k0;->b(Landroidx/recyclerview/widget/RecyclerView;)V

    .line 34
    .line 35
    .line 36
    iget-object v0, p1, Lml0/l;->g:Lorg/xbet/ui_common/viewcomponents/swiperefreshlayout/SwipeRefreshLayout;

    .line 37
    .line 38
    invoke-virtual {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->Q2()Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    new-instance v2, Lorg/xbet/results/impl/presentation/sports/h;

    .line 43
    .line 44
    invoke-direct {v2, v1}, Lorg/xbet/results/impl/presentation/sports/h;-><init>(Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {v0, v2}, Landroidx/swiperefreshlayout/widget/SwipeRefreshLayout;->setOnRefreshListener(Landroidx/swiperefreshlayout/widget/SwipeRefreshLayout$j;)V

    .line 48
    .line 49
    .line 50
    iget-object v0, p1, Lml0/l;->d:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 51
    .line 52
    new-instance v1, Lorg/xbet/results/impl/presentation/sports/i;

    .line 53
    .line 54
    invoke-direct {v1, p0}, Lorg/xbet/results/impl/presentation/sports/i;-><init>(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)V

    .line 55
    .line 56
    .line 57
    const/4 v2, 0x0

    .line 58
    const/4 v3, 0x1

    .line 59
    invoke-static {v0, v2, v1, v3, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 60
    .line 61
    .line 62
    iget-object p1, p1, Lml0/l;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 63
    .line 64
    new-instance v0, Lorg/xbet/results/impl/presentation/sports/j;

    .line 65
    .line 66
    invoke-direct {v0, p0}, Lorg/xbet/results/impl/presentation/sports/j;-><init>(Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;)V

    .line 67
    .line 68
    .line 69
    invoke-static {p1, v2, v0, v3, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 70
    .line 71
    .line 72
    invoke-direct {p0}, Lorg/xbet/results/impl/presentation/sports/SportsResultsFragment;->d3()V

    .line 73
    .line 74
    .line 75
    return-void
.end method
