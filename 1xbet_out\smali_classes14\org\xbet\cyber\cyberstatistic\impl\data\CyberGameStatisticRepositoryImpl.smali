.class public final Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/cyber/cyberstatistic/impl/domain/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0001\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J(\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0006H\u0096@\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ(\u0010\u000e\u001a\u00020\r2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0006H\u0096@\u00a2\u0006\u0004\u0008\u000e\u0010\u000cR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u000f\u00a8\u0006\u0010"
    }
    d2 = {
        "Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl;",
        "Lorg/xbet/cyber/cyberstatistic/impl/domain/a;",
        "Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;",
        "cyberGameStatisticRemoteDataSource",
        "<init>",
        "(Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;)V",
        "",
        "teamOne",
        "teamTwo",
        "startTime",
        "Lpy/c;",
        "b",
        "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lzy/e;",
        "a",
        "Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;)V
    .locals 0
    .param p1    # Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl;->a:Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 20
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lzy/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    move-object/from16 v0, p4

    .line 4
    .line 5
    instance-of v2, v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    move-object v2, v0

    .line 10
    check-cast v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;

    .line 11
    .line 12
    iget v3, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->label:I

    .line 13
    .line 14
    const/high16 v4, -0x80000000

    .line 15
    .line 16
    and-int v5, v3, v4

    .line 17
    .line 18
    if-eqz v5, :cond_0

    .line 19
    .line 20
    sub-int/2addr v3, v4

    .line 21
    iput v3, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->label:I

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;

    .line 25
    .line 26
    invoke-direct {v2, v1, v0}, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;-><init>(Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    :goto_0
    iget-object v0, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->result:Ljava/lang/Object;

    .line 30
    .line 31
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    iget v4, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->label:I

    .line 36
    .line 37
    const/4 v5, 0x2

    .line 38
    const/4 v7, 0x1

    .line 39
    if-eqz v4, :cond_3

    .line 40
    .line 41
    if-eq v4, v7, :cond_2

    .line 42
    .line 43
    if-ne v4, v5, :cond_1

    .line 44
    .line 45
    iget v4, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->I$1:I

    .line 46
    .line 47
    iget v8, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->I$0:I

    .line 48
    .line 49
    iget-wide v9, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->J$1:J

    .line 50
    .line 51
    iget-wide v11, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->J$0:J

    .line 52
    .line 53
    iget-object v13, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$3:Ljava/lang/Object;

    .line 54
    .line 55
    check-cast v13, Ljava/lang/Throwable;

    .line 56
    .line 57
    iget-object v14, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$2:Ljava/lang/Object;

    .line 58
    .line 59
    check-cast v14, Ljava/lang/String;

    .line 60
    .line 61
    iget-object v15, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$1:Ljava/lang/Object;

    .line 62
    .line 63
    check-cast v15, Ljava/lang/String;

    .line 64
    .line 65
    iget-object v6, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$0:Ljava/lang/Object;

    .line 66
    .line 67
    check-cast v6, Ljava/lang/String;

    .line 68
    .line 69
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 70
    .line 71
    .line 72
    move v0, v4

    .line 73
    move-object v4, v15

    .line 74
    const/4 v15, 0x2

    .line 75
    move/from16 v17, v8

    .line 76
    .line 77
    move-object v8, v2

    .line 78
    move-object v2, v6

    .line 79
    move-object v6, v14

    .line 80
    move-wide/from16 v18, v9

    .line 81
    .line 82
    move/from16 v9, v17

    .line 83
    .line 84
    move-object v10, v13

    .line 85
    move-wide v13, v11

    .line 86
    move-wide/from16 v11, v18

    .line 87
    .line 88
    goto/16 :goto_7

    .line 89
    .line 90
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 91
    .line 92
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 93
    .line 94
    invoke-direct {v0, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 95
    .line 96
    .line 97
    throw v0

    .line 98
    :cond_2
    iget v4, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->I$0:I

    .line 99
    .line 100
    iget-wide v8, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->J$1:J

    .line 101
    .line 102
    iget-wide v10, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->J$0:J

    .line 103
    .line 104
    iget-object v6, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$3:Ljava/lang/Object;

    .line 105
    .line 106
    check-cast v6, Ljava/lang/Throwable;

    .line 107
    .line 108
    iget-object v12, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$2:Ljava/lang/Object;

    .line 109
    .line 110
    check-cast v12, Ljava/lang/String;

    .line 111
    .line 112
    iget-object v13, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$1:Ljava/lang/Object;

    .line 113
    .line 114
    check-cast v13, Ljava/lang/String;

    .line 115
    .line 116
    iget-object v14, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$0:Ljava/lang/Object;

    .line 117
    .line 118
    check-cast v14, Ljava/lang/String;

    .line 119
    .line 120
    :try_start_0
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 121
    .line 122
    .line 123
    goto/16 :goto_2

    .line 124
    .line 125
    :catchall_0
    move-exception v0

    .line 126
    move-wide/from16 v17, v10

    .line 127
    .line 128
    move-object v10, v0

    .line 129
    move-object v0, v6

    .line 130
    move-object v6, v12

    .line 131
    move-wide v11, v8

    .line 132
    move-object v8, v2

    .line 133
    move v9, v4

    .line 134
    move-object v4, v13

    .line 135
    move-object v2, v14

    .line 136
    move-wide/from16 v13, v17

    .line 137
    .line 138
    goto/16 :goto_4

    .line 139
    .line 140
    :cond_3
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 141
    .line 142
    .line 143
    invoke-static {}, Lorg/xbet/ui_common/utils/retry/a;->a()J

    .line 144
    .line 145
    .line 146
    move-result-wide v8

    .line 147
    const-wide/16 v10, 0x3

    .line 148
    .line 149
    const/4 v0, 0x0

    .line 150
    move-object/from16 v4, p2

    .line 151
    .line 152
    move-object/from16 v6, p3

    .line 153
    .line 154
    move-wide v13, v10

    .line 155
    move-object v10, v0

    .line 156
    move-wide v11, v8

    .line 157
    const/4 v0, 0x1

    .line 158
    const/4 v9, 0x0

    .line 159
    move-object v8, v2

    .line 160
    move-object/from16 v2, p1

    .line 161
    .line 162
    :goto_1
    if-eqz v0, :cond_c

    .line 163
    .line 164
    :try_start_1
    iget-object v0, v1, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl;->a:Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;

    .line 165
    .line 166
    iput-object v2, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$0:Ljava/lang/Object;

    .line 167
    .line 168
    iput-object v4, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$1:Ljava/lang/Object;

    .line 169
    .line 170
    iput-object v6, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$2:Ljava/lang/Object;

    .line 171
    .line 172
    iput-object v10, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$3:Ljava/lang/Object;

    .line 173
    .line 174
    iput-wide v13, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->J$0:J

    .line 175
    .line 176
    iput-wide v11, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->J$1:J

    .line 177
    .line 178
    iput v9, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->I$0:I

    .line 179
    .line 180
    iput v7, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->label:I

    .line 181
    .line 182
    invoke-virtual {v0, v2, v4, v6, v8}, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;->c(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 183
    .line 184
    .line 185
    move-result-object v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 186
    if-ne v0, v3, :cond_4

    .line 187
    .line 188
    goto/16 :goto_6

    .line 189
    .line 190
    :cond_4
    move-wide/from16 v17, v13

    .line 191
    .line 192
    move-object v14, v2

    .line 193
    move-object v13, v4

    .line 194
    move-object v2, v8

    .line 195
    move v4, v9

    .line 196
    move-wide v8, v11

    .line 197
    move-object v12, v6

    .line 198
    move-object v6, v10

    .line 199
    move-wide/from16 v10, v17

    .line 200
    .line 201
    :goto_2
    :try_start_2
    check-cast v0, Ljava/util/List;

    .line 202
    .line 203
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 204
    .line 205
    .line 206
    move-result-object v0

    .line 207
    check-cast v0, Lvy/e;

    .line 208
    .line 209
    if-eqz v0, :cond_5

    .line 210
    .line 211
    invoke-static {v0}, Lty/e;->a(Lvy/e;)Lzy/e;

    .line 212
    .line 213
    .line 214
    move-result-object v0

    .line 215
    if-eqz v0, :cond_5

    .line 216
    .line 217
    goto :goto_3

    .line 218
    :cond_5
    sget-object v0, Lzy/e;->f:Lzy/e$a;

    .line 219
    .line 220
    invoke-virtual {v0}, Lzy/e$a;->a()Lzy/e;

    .line 221
    .line 222
    .line 223
    move-result-object v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 224
    :goto_3
    return-object v0

    .line 225
    :catchall_1
    move-exception v0

    .line 226
    move-object/from16 v17, v10

    .line 227
    .line 228
    move-object v10, v0

    .line 229
    move-object/from16 v0, v17

    .line 230
    .line 231
    :goto_4
    invoke-static {v10}, LJX0/a;->b(Ljava/lang/Throwable;)Z

    .line 232
    .line 233
    .line 234
    move-result v15

    .line 235
    if-eqz v15, :cond_6

    .line 236
    .line 237
    new-instance v0, Lorg/xbet/ui_common/utils/retry/exception/NetworkNotAvailableException;

    .line 238
    .line 239
    invoke-direct {v0, v10}, Lorg/xbet/ui_common/utils/retry/exception/NetworkNotAvailableException;-><init>(Ljava/lang/Throwable;)V

    .line 240
    .line 241
    .line 242
    move-object v10, v0

    .line 243
    const/4 v0, 0x0

    .line 244
    goto :goto_1

    .line 245
    :cond_6
    invoke-static {v10}, LJX0/a;->c(Ljava/lang/Throwable;)Z

    .line 246
    .line 247
    .line 248
    move-result v15

    .line 249
    if-eqz v15, :cond_a

    .line 250
    .line 251
    move-object/from16 p1, v6

    .line 252
    .line 253
    int-to-long v5, v9

    .line 254
    cmp-long v16, v5, v13

    .line 255
    .line 256
    if-gez v16, :cond_7

    .line 257
    .line 258
    const/4 v5, 0x1

    .line 259
    goto :goto_5

    .line 260
    :cond_7
    const/4 v5, 0x0

    .line 261
    :goto_5
    add-int/lit8 v9, v9, 0x1

    .line 262
    .line 263
    if-eqz v5, :cond_8

    .line 264
    .line 265
    iput-object v2, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$0:Ljava/lang/Object;

    .line 266
    .line 267
    iput-object v4, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$1:Ljava/lang/Object;

    .line 268
    .line 269
    move-object/from16 v6, p1

    .line 270
    .line 271
    iput-object v6, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$2:Ljava/lang/Object;

    .line 272
    .line 273
    iput-object v0, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->L$3:Ljava/lang/Object;

    .line 274
    .line 275
    iput-wide v13, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->J$0:J

    .line 276
    .line 277
    iput-wide v11, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->J$1:J

    .line 278
    .line 279
    iput v9, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->I$0:I

    .line 280
    .line 281
    iput v5, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->I$1:I

    .line 282
    .line 283
    const/4 v15, 0x2

    .line 284
    iput v15, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberCSMatchesStatistic$1;->label:I

    .line 285
    .line 286
    invoke-static {v11, v12, v8}, Lkotlinx/coroutines/DelayKt;->c(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 287
    .line 288
    .line 289
    move-result-object v10

    .line 290
    if-ne v10, v3, :cond_9

    .line 291
    .line 292
    :goto_6
    return-object v3

    .line 293
    :goto_7
    const/4 v5, 0x2

    .line 294
    goto/16 :goto_1

    .line 295
    .line 296
    :cond_8
    move-object/from16 v6, p1

    .line 297
    .line 298
    const/4 v15, 0x2

    .line 299
    new-instance v0, Lorg/xbet/ui_common/utils/retry/exception/RetryOnErrorException;

    .line 300
    .line 301
    invoke-direct {v0, v10}, Lorg/xbet/ui_common/utils/retry/exception/RetryOnErrorException;-><init>(Ljava/lang/Throwable;)V

    .line 302
    .line 303
    .line 304
    :cond_9
    move-object v10, v0

    .line 305
    move v0, v5

    .line 306
    goto :goto_7

    .line 307
    :cond_a
    const/4 v15, 0x2

    .line 308
    invoke-static {v10}, LJX0/a;->a(Ljava/lang/Throwable;)Z

    .line 309
    .line 310
    .line 311
    move-result v0

    .line 312
    if-eqz v0, :cond_b

    .line 313
    .line 314
    new-instance v0, Lorg/xbet/ui_common/utils/retry/exception/ClientThrowable;

    .line 315
    .line 316
    invoke-direct {v0, v10}, Lorg/xbet/ui_common/utils/retry/exception/ClientThrowable;-><init>(Ljava/lang/Throwable;)V

    .line 317
    .line 318
    .line 319
    move-object v10, v0

    .line 320
    :cond_b
    const/4 v0, 0x0

    .line 321
    goto :goto_7

    .line 322
    :cond_c
    if-nez v10, :cond_d

    .line 323
    .line 324
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 325
    .line 326
    const-string v2, "Unexpected error"

    .line 327
    .line 328
    invoke-direct {v0, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 329
    .line 330
    .line 331
    throw v0

    .line 332
    :cond_d
    throw v10
.end method

.method public b(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 20
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lpy/c;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    move-object/from16 v0, p4

    .line 4
    .line 5
    instance-of v2, v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    move-object v2, v0

    .line 10
    check-cast v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;

    .line 11
    .line 12
    iget v3, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->label:I

    .line 13
    .line 14
    const/high16 v4, -0x80000000

    .line 15
    .line 16
    and-int v5, v3, v4

    .line 17
    .line 18
    if-eqz v5, :cond_0

    .line 19
    .line 20
    sub-int/2addr v3, v4

    .line 21
    iput v3, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->label:I

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;

    .line 25
    .line 26
    invoke-direct {v2, v1, v0}, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;-><init>(Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    :goto_0
    iget-object v0, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->result:Ljava/lang/Object;

    .line 30
    .line 31
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    iget v4, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->label:I

    .line 36
    .line 37
    const/4 v5, 0x2

    .line 38
    const/4 v7, 0x1

    .line 39
    if-eqz v4, :cond_3

    .line 40
    .line 41
    if-eq v4, v7, :cond_2

    .line 42
    .line 43
    if-ne v4, v5, :cond_1

    .line 44
    .line 45
    iget v4, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->I$1:I

    .line 46
    .line 47
    iget v8, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->I$0:I

    .line 48
    .line 49
    iget-wide v9, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->J$1:J

    .line 50
    .line 51
    iget-wide v11, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->J$0:J

    .line 52
    .line 53
    iget-object v13, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$3:Ljava/lang/Object;

    .line 54
    .line 55
    check-cast v13, Ljava/lang/Throwable;

    .line 56
    .line 57
    iget-object v14, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$2:Ljava/lang/Object;

    .line 58
    .line 59
    check-cast v14, Ljava/lang/String;

    .line 60
    .line 61
    iget-object v15, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$1:Ljava/lang/Object;

    .line 62
    .line 63
    check-cast v15, Ljava/lang/String;

    .line 64
    .line 65
    iget-object v6, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$0:Ljava/lang/Object;

    .line 66
    .line 67
    check-cast v6, Ljava/lang/String;

    .line 68
    .line 69
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 70
    .line 71
    .line 72
    move v0, v4

    .line 73
    move-object v4, v15

    .line 74
    const/4 v15, 0x2

    .line 75
    move/from16 v17, v8

    .line 76
    .line 77
    move-object v8, v2

    .line 78
    move-object v2, v6

    .line 79
    move-object v6, v14

    .line 80
    move-wide/from16 v18, v9

    .line 81
    .line 82
    move/from16 v9, v17

    .line 83
    .line 84
    move-object v10, v13

    .line 85
    move-wide v13, v11

    .line 86
    move-wide/from16 v11, v18

    .line 87
    .line 88
    goto/16 :goto_7

    .line 89
    .line 90
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 91
    .line 92
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 93
    .line 94
    invoke-direct {v0, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 95
    .line 96
    .line 97
    throw v0

    .line 98
    :cond_2
    iget v4, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->I$0:I

    .line 99
    .line 100
    iget-wide v8, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->J$1:J

    .line 101
    .line 102
    iget-wide v10, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->J$0:J

    .line 103
    .line 104
    iget-object v6, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$3:Ljava/lang/Object;

    .line 105
    .line 106
    check-cast v6, Ljava/lang/Throwable;

    .line 107
    .line 108
    iget-object v12, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$2:Ljava/lang/Object;

    .line 109
    .line 110
    check-cast v12, Ljava/lang/String;

    .line 111
    .line 112
    iget-object v13, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$1:Ljava/lang/Object;

    .line 113
    .line 114
    check-cast v13, Ljava/lang/String;

    .line 115
    .line 116
    iget-object v14, v2, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$0:Ljava/lang/Object;

    .line 117
    .line 118
    check-cast v14, Ljava/lang/String;

    .line 119
    .line 120
    :try_start_0
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 121
    .line 122
    .line 123
    goto/16 :goto_2

    .line 124
    .line 125
    :catchall_0
    move-exception v0

    .line 126
    move-wide/from16 v17, v10

    .line 127
    .line 128
    move-object v10, v0

    .line 129
    move-object v0, v6

    .line 130
    move-object v6, v12

    .line 131
    move-wide v11, v8

    .line 132
    move-object v8, v2

    .line 133
    move v9, v4

    .line 134
    move-object v4, v13

    .line 135
    move-object v2, v14

    .line 136
    move-wide/from16 v13, v17

    .line 137
    .line 138
    goto/16 :goto_4

    .line 139
    .line 140
    :cond_3
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 141
    .line 142
    .line 143
    invoke-static {}, Lorg/xbet/ui_common/utils/retry/a;->a()J

    .line 144
    .line 145
    .line 146
    move-result-wide v8

    .line 147
    const-wide/16 v10, 0x3

    .line 148
    .line 149
    const/4 v0, 0x0

    .line 150
    move-object/from16 v4, p2

    .line 151
    .line 152
    move-object/from16 v6, p3

    .line 153
    .line 154
    move-wide v13, v10

    .line 155
    move-object v10, v0

    .line 156
    move-wide v11, v8

    .line 157
    const/4 v0, 0x1

    .line 158
    const/4 v9, 0x0

    .line 159
    move-object v8, v2

    .line 160
    move-object/from16 v2, p1

    .line 161
    .line 162
    :goto_1
    if-eqz v0, :cond_c

    .line 163
    .line 164
    :try_start_1
    iget-object v0, v1, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl;->a:Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;

    .line 165
    .line 166
    iput-object v2, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$0:Ljava/lang/Object;

    .line 167
    .line 168
    iput-object v4, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$1:Ljava/lang/Object;

    .line 169
    .line 170
    iput-object v6, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$2:Ljava/lang/Object;

    .line 171
    .line 172
    iput-object v10, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$3:Ljava/lang/Object;

    .line 173
    .line 174
    iput-wide v13, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->J$0:J

    .line 175
    .line 176
    iput-wide v11, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->J$1:J

    .line 177
    .line 178
    iput v9, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->I$0:I

    .line 179
    .line 180
    iput v7, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->label:I

    .line 181
    .line 182
    invoke-virtual {v0, v2, v4, v6, v8}, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;->d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 183
    .line 184
    .line 185
    move-result-object v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 186
    if-ne v0, v3, :cond_4

    .line 187
    .line 188
    goto/16 :goto_6

    .line 189
    .line 190
    :cond_4
    move-wide/from16 v17, v13

    .line 191
    .line 192
    move-object v14, v2

    .line 193
    move-object v13, v4

    .line 194
    move-object v2, v8

    .line 195
    move v4, v9

    .line 196
    move-wide v8, v11

    .line 197
    move-object v12, v6

    .line 198
    move-object v6, v10

    .line 199
    move-wide/from16 v10, v17

    .line 200
    .line 201
    :goto_2
    :try_start_2
    check-cast v0, Ljava/util/List;

    .line 202
    .line 203
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 204
    .line 205
    .line 206
    move-result-object v0

    .line 207
    check-cast v0, Luy/b;

    .line 208
    .line 209
    if-eqz v0, :cond_5

    .line 210
    .line 211
    invoke-static {v0}, Lsy/b;->a(Luy/b;)Lpy/c;

    .line 212
    .line 213
    .line 214
    move-result-object v0

    .line 215
    if-eqz v0, :cond_5

    .line 216
    .line 217
    goto :goto_3

    .line 218
    :cond_5
    sget-object v0, Lpy/c;->j:Lpy/c$a;

    .line 219
    .line 220
    invoke-virtual {v0}, Lpy/c$a;->a()Lpy/c;

    .line 221
    .line 222
    .line 223
    move-result-object v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 224
    :goto_3
    return-object v0

    .line 225
    :catchall_1
    move-exception v0

    .line 226
    move-object/from16 v17, v10

    .line 227
    .line 228
    move-object v10, v0

    .line 229
    move-object/from16 v0, v17

    .line 230
    .line 231
    :goto_4
    invoke-static {v10}, LJX0/a;->b(Ljava/lang/Throwable;)Z

    .line 232
    .line 233
    .line 234
    move-result v15

    .line 235
    if-eqz v15, :cond_6

    .line 236
    .line 237
    new-instance v0, Lorg/xbet/ui_common/utils/retry/exception/NetworkNotAvailableException;

    .line 238
    .line 239
    invoke-direct {v0, v10}, Lorg/xbet/ui_common/utils/retry/exception/NetworkNotAvailableException;-><init>(Ljava/lang/Throwable;)V

    .line 240
    .line 241
    .line 242
    move-object v10, v0

    .line 243
    const/4 v0, 0x0

    .line 244
    goto :goto_1

    .line 245
    :cond_6
    invoke-static {v10}, LJX0/a;->c(Ljava/lang/Throwable;)Z

    .line 246
    .line 247
    .line 248
    move-result v15

    .line 249
    if-eqz v15, :cond_a

    .line 250
    .line 251
    move-object/from16 p1, v6

    .line 252
    .line 253
    int-to-long v5, v9

    .line 254
    cmp-long v16, v5, v13

    .line 255
    .line 256
    if-gez v16, :cond_7

    .line 257
    .line 258
    const/4 v5, 0x1

    .line 259
    goto :goto_5

    .line 260
    :cond_7
    const/4 v5, 0x0

    .line 261
    :goto_5
    add-int/lit8 v9, v9, 0x1

    .line 262
    .line 263
    if-eqz v5, :cond_8

    .line 264
    .line 265
    iput-object v2, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$0:Ljava/lang/Object;

    .line 266
    .line 267
    iput-object v4, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$1:Ljava/lang/Object;

    .line 268
    .line 269
    move-object/from16 v6, p1

    .line 270
    .line 271
    iput-object v6, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$2:Ljava/lang/Object;

    .line 272
    .line 273
    iput-object v0, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->L$3:Ljava/lang/Object;

    .line 274
    .line 275
    iput-wide v13, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->J$0:J

    .line 276
    .line 277
    iput-wide v11, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->J$1:J

    .line 278
    .line 279
    iput v9, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->I$0:I

    .line 280
    .line 281
    iput v5, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->I$1:I

    .line 282
    .line 283
    const/4 v15, 0x2

    .line 284
    iput v15, v8, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRepositoryImpl$getCyberDotaMatchesStatistic$1;->label:I

    .line 285
    .line 286
    invoke-static {v11, v12, v8}, Lkotlinx/coroutines/DelayKt;->c(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 287
    .line 288
    .line 289
    move-result-object v10

    .line 290
    if-ne v10, v3, :cond_9

    .line 291
    .line 292
    :goto_6
    return-object v3

    .line 293
    :goto_7
    const/4 v5, 0x2

    .line 294
    goto/16 :goto_1

    .line 295
    .line 296
    :cond_8
    move-object/from16 v6, p1

    .line 297
    .line 298
    const/4 v15, 0x2

    .line 299
    new-instance v0, Lorg/xbet/ui_common/utils/retry/exception/RetryOnErrorException;

    .line 300
    .line 301
    invoke-direct {v0, v10}, Lorg/xbet/ui_common/utils/retry/exception/RetryOnErrorException;-><init>(Ljava/lang/Throwable;)V

    .line 302
    .line 303
    .line 304
    :cond_9
    move-object v10, v0

    .line 305
    move v0, v5

    .line 306
    goto :goto_7

    .line 307
    :cond_a
    const/4 v15, 0x2

    .line 308
    invoke-static {v10}, LJX0/a;->a(Ljava/lang/Throwable;)Z

    .line 309
    .line 310
    .line 311
    move-result v0

    .line 312
    if-eqz v0, :cond_b

    .line 313
    .line 314
    new-instance v0, Lorg/xbet/ui_common/utils/retry/exception/ClientThrowable;

    .line 315
    .line 316
    invoke-direct {v0, v10}, Lorg/xbet/ui_common/utils/retry/exception/ClientThrowable;-><init>(Ljava/lang/Throwable;)V

    .line 317
    .line 318
    .line 319
    move-object v10, v0

    .line 320
    :cond_b
    const/4 v0, 0x0

    .line 321
    goto :goto_7

    .line 322
    :cond_c
    if-nez v10, :cond_d

    .line 323
    .line 324
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 325
    .line 326
    const-string v2, "Unexpected error"

    .line 327
    .line 328
    invoke-direct {v0, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 329
    .line 330
    .line 331
    throw v0

    .line 332
    :cond_d
    throw v10
.end method
