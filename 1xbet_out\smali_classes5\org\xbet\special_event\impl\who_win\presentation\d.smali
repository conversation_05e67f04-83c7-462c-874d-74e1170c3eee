.class public final synthetic Lorg/xbet/special_event/impl/who_win/presentation/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, <PERSON>ja<PERSON>/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, LKo0/a;

    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->o3(LKo0/a;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
