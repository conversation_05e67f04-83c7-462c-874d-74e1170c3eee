.class public final LrG0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LsG0/b;",
        "LvG0/b;",
        "a",
        "(LsG0/b;)LvG0/b;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LsG0/b;)LvG0/b;
    .locals 7
    .param p0    # LsG0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LsG0/b;->a()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    :cond_0
    move-object v2, v0

    .line 12
    invoke-virtual {p0}, LsG0/b;->c()LsG0/c;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    if-eqz v0, :cond_2

    .line 17
    .line 18
    invoke-static {v0}, LrG0/e;->a(LsG0/c;)LvG0/e;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    if-nez v0, :cond_1

    .line 23
    .line 24
    goto :goto_1

    .line 25
    :cond_1
    :goto_0
    move-object v3, v0

    .line 26
    goto :goto_2

    .line 27
    :cond_2
    :goto_1
    sget-object v0, LvG0/e;->k:LvG0/e$a;

    .line 28
    .line 29
    invoke-virtual {v0}, LvG0/e$a;->a()LvG0/e;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    goto :goto_0

    .line 34
    :goto_2
    invoke-virtual {p0}, LsG0/b;->b()Ljava/lang/Integer;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    if-eqz v0, :cond_3

    .line 39
    .line 40
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 41
    .line 42
    .line 43
    move-result v0

    .line 44
    move v4, v0

    .line 45
    goto :goto_3

    .line 46
    :cond_3
    const/4 v0, 0x0

    .line 47
    const/4 v4, 0x0

    .line 48
    :goto_3
    invoke-virtual {p0}, LsG0/b;->d()Ljava/util/List;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    if-eqz v0, :cond_4

    .line 53
    .line 54
    new-instance v1, Ljava/util/ArrayList;

    .line 55
    .line 56
    const/16 v5, 0xa

    .line 57
    .line 58
    invoke-static {v0, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 59
    .line 60
    .line 61
    move-result v5

    .line 62
    invoke-direct {v1, v5}, Ljava/util/ArrayList;-><init>(I)V

    .line 63
    .line 64
    .line 65
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    :goto_4
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 70
    .line 71
    .line 72
    move-result v5

    .line 73
    if-eqz v5, :cond_5

    .line 74
    .line 75
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object v5

    .line 79
    check-cast v5, LCN0/p;

    .line 80
    .line 81
    invoke-static {v5}, LBN0/n;->a(LCN0/p;)LND0/k;

    .line 82
    .line 83
    .line 84
    move-result-object v5

    .line 85
    invoke-interface {v1, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 86
    .line 87
    .line 88
    goto :goto_4

    .line 89
    :cond_4
    const/4 v1, 0x0

    .line 90
    :cond_5
    if-nez v1, :cond_6

    .line 91
    .line 92
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 93
    .line 94
    .line 95
    move-result-object v1

    .line 96
    :cond_6
    move-object v5, v1

    .line 97
    invoke-virtual {p0}, LsG0/b;->c()LsG0/c;

    .line 98
    .line 99
    .line 100
    move-result-object p0

    .line 101
    if-eqz p0, :cond_8

    .line 102
    .line 103
    invoke-static {p0}, LrG0/a;->d(LsG0/c;)LvG0/h;

    .line 104
    .line 105
    .line 106
    move-result-object p0

    .line 107
    if-nez p0, :cond_7

    .line 108
    .line 109
    goto :goto_6

    .line 110
    :cond_7
    :goto_5
    move-object v6, p0

    .line 111
    goto :goto_7

    .line 112
    :cond_8
    :goto_6
    sget-object p0, LvG0/h;->d:LvG0/h$a;

    .line 113
    .line 114
    invoke-virtual {p0}, LvG0/h$a;->a()LvG0/h;

    .line 115
    .line 116
    .line 117
    move-result-object p0

    .line 118
    goto :goto_5

    .line 119
    :goto_7
    new-instance v1, LvG0/b;

    .line 120
    .line 121
    invoke-direct/range {v1 .. v6}, LvG0/b;-><init>(Ljava/util/List;LvG0/e;ILjava/util/List;LvG0/h;)V

    .line 122
    .line 123
    .line 124
    return-object v1
.end method
