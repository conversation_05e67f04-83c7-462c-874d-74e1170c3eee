.class public final Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt;->e(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)LA4/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function1<",
        "Ljava/util/List<",
        "+",
        "Ljava/lang/Object;",
        ">;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:LB4/a;


# direct methods
.method public constructor <init>(LB4/a;LB4/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;->a:LB4/a;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;->b:LB4/a;

    .line 4
    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;->a:LB4/a;

    .line 8
    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, Lv80/F;

    .line 14
    .line 15
    iget-object p1, p1, Lv80/F;->b:Lorg/xbet/uikit/components/cells/MenuCell;

    .line 16
    .line 17
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;->a:LB4/a;

    .line 18
    .line 19
    invoke-virtual {v0}, LB4/a;->g()Landroid/content/Context;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;->a:LB4/a;

    .line 24
    .line 25
    invoke-virtual {v1}, LB4/a;->i()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    check-cast v1, LN80/c$c;

    .line 30
    .line 31
    invoke-virtual {v1}, LN80/c$c;->f()I

    .line 32
    .line 33
    .line 34
    move-result v1

    .line 35
    const/4 v2, 0x2

    .line 36
    const/4 v3, 0x0

    .line 37
    invoke-static {v0, v1, v3, v2, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 38
    .line 39
    .line 40
    move-result v0

    .line 41
    invoke-static {v0}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    invoke-static {p1, v0}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 46
    .line 47
    .line 48
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;->a:LB4/a;

    .line 49
    .line 50
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    check-cast p1, Lv80/F;

    .line 55
    .line 56
    iget-object p1, p1, Lv80/F;->d:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 57
    .line 58
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;->a:LB4/a;

    .line 59
    .line 60
    invoke-virtual {v0}, LB4/a;->i()Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    check-cast v0, LN80/c$c;

    .line 65
    .line 66
    invoke-virtual {v0}, LN80/c$c;->getIcon()I

    .line 67
    .line 68
    .line 69
    move-result v0

    .line 70
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->setIconResource(I)V

    .line 71
    .line 72
    .line 73
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;->a:LB4/a;

    .line 74
    .line 75
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    check-cast p1, Lv80/F;

    .line 80
    .line 81
    iget-object p1, p1, Lv80/F;->e:Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;

    .line 82
    .line 83
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;->a:LB4/a;

    .line 84
    .line 85
    invoke-virtual {v0}, LB4/a;->i()Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    move-result-object v0

    .line 89
    check-cast v0, LN80/c$c;

    .line 90
    .line 91
    invoke-virtual {v0}, LN80/c$c;->getTitle()Ljava/lang/String;

    .line 92
    .line 93
    .line 94
    move-result-object v0

    .line 95
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;->setTitle(Ljava/lang/CharSequence;)V

    .line 96
    .line 97
    .line 98
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;->a:LB4/a;

    .line 99
    .line 100
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    check-cast p1, Lv80/F;

    .line 105
    .line 106
    iget-object p1, p1, Lv80/F;->e:Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;

    .line 107
    .line 108
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;->a:LB4/a;

    .line 109
    .line 110
    invoke-virtual {v0}, LB4/a;->i()Ljava/lang/Object;

    .line 111
    .line 112
    .line 113
    move-result-object v0

    .line 114
    check-cast v0, LN80/c$c;

    .line 115
    .line 116
    invoke-virtual {v0}, LN80/c$c;->j()Ljava/lang/String;

    .line 117
    .line 118
    .line 119
    move-result-object v0

    .line 120
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;->setSubtitle(Ljava/lang/CharSequence;)V

    .line 121
    .line 122
    .line 123
    return-void

    .line 124
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    .line 125
    .line 126
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 127
    .line 128
    .line 129
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 130
    .line 131
    .line 132
    move-result-object p1

    .line 133
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 134
    .line 135
    .line 136
    move-result v1

    .line 137
    if-eqz v1, :cond_1

    .line 138
    .line 139
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 140
    .line 141
    .line 142
    move-result-object v1

    .line 143
    check-cast v1, Ljava/util/Collection;

    .line 144
    .line 145
    check-cast v1, Ljava/lang/Iterable;

    .line 146
    .line 147
    invoke-static {v0, v1}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 148
    .line 149
    .line 150
    goto :goto_0

    .line 151
    :cond_1
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 152
    .line 153
    .line 154
    move-result-object p1

    .line 155
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 156
    .line 157
    .line 158
    move-result v0

    .line 159
    if-eqz v0, :cond_2

    .line 160
    .line 161
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 162
    .line 163
    .line 164
    move-result-object v0

    .line 165
    check-cast v0, LN80/c$c$a;

    .line 166
    .line 167
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;->b:LB4/a;

    .line 168
    .line 169
    invoke-virtual {v0}, LB4/a;->e()LL2/a;

    .line 170
    .line 171
    .line 172
    move-result-object v0

    .line 173
    check-cast v0, Lv80/F;

    .line 174
    .line 175
    iget-object v0, v0, Lv80/F;->e:Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;

    .line 176
    .line 177
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;->b:LB4/a;

    .line 178
    .line 179
    invoke-virtual {v1}, LB4/a;->i()Ljava/lang/Object;

    .line 180
    .line 181
    .line 182
    move-result-object v1

    .line 183
    check-cast v1, LN80/c$c;

    .line 184
    .line 185
    invoke-virtual {v1}, LN80/c$c;->j()Ljava/lang/String;

    .line 186
    .line 187
    .line 188
    move-result-object v1

    .line 189
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;->setSubtitle(Ljava/lang/CharSequence;)V

    .line 190
    .line 191
    .line 192
    goto :goto_1

    .line 193
    :cond_2
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/CallMenuItemViewHolderKt$a;->a(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p1
.end method
