.class public final LnT0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LnT0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LnT0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;

.field public final b:LRf0/o;

.field public final c:Li8/m;

.field public final d:LnT0/a$b;


# direct methods
.method public constructor <init>(LRf0/o;Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;Li8/m;Lm8/a;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LnT0/a$b;->d:LnT0/a$b;

    .line 4
    iput-object p2, p0, LnT0/a$b;->a:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;

    .line 5
    iput-object p1, p0, LnT0/a$b;->b:LRf0/o;

    .line 6
    iput-object p3, p0, LnT0/a$b;->c:Li8/m;

    return-void
.end method

.method public synthetic constructor <init>(LRf0/o;Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;Li8/m;Lm8/a;LnT0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3, p4}, LnT0/a$b;-><init>(LRf0/o;Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;Li8/m;Lm8/a;)V

    return-void
.end method


# virtual methods
.method public a()LmT0/c;
    .locals 1

    .line 1
    invoke-virtual {p0}, LnT0/a$b;->j()LpT0/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public b()LmT0/a;
    .locals 1

    .line 1
    invoke-virtual {p0}, LnT0/a$b;->f()LqT0/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public c()LmT0/b;
    .locals 1

    .line 1
    invoke-virtual {p0}, LnT0/a$b;->i()LqT0/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final d()LqT0/a;
    .locals 2

    .line 1
    new-instance v0, LqT0/a;

    .line 2
    .line 3
    iget-object v1, p0, LnT0/a$b;->b:LRf0/o;

    .line 4
    .line 5
    invoke-direct {v0, v1}, LqT0/a;-><init>(LRf0/o;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final e()LqT0/b;
    .locals 2

    .line 1
    new-instance v0, LqT0/b;

    .line 2
    .line 3
    iget-object v1, p0, LnT0/a$b;->b:LRf0/o;

    .line 4
    .line 5
    invoke-direct {v0, v1}, LqT0/b;-><init>(LRf0/o;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final f()LqT0/c;
    .locals 2

    .line 1
    new-instance v0, LqT0/c;

    .line 2
    .line 3
    invoke-virtual {p0}, LnT0/a$b;->h()Lorg/xbet/themeswitch/impl/data/b;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LqT0/c;-><init>(LpT0/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final g()LqT0/d;
    .locals 2

    .line 1
    new-instance v0, LqT0/d;

    .line 2
    .line 3
    iget-object v1, p0, LnT0/a$b;->b:LRf0/o;

    .line 4
    .line 5
    invoke-direct {v0, v1}, LqT0/d;-><init>(LRf0/o;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final h()Lorg/xbet/themeswitch/impl/data/b;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/themeswitch/impl/data/b;

    .line 2
    .line 3
    iget-object v1, p0, LnT0/a$b;->a:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;

    .line 4
    .line 5
    invoke-direct {v0, v1}, Lorg/xbet/themeswitch/impl/data/b;-><init>(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final i()LqT0/e;
    .locals 2

    .line 1
    new-instance v0, LqT0/e;

    .line 2
    .line 3
    invoke-virtual {p0}, LnT0/a$b;->h()Lorg/xbet/themeswitch/impl/data/b;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LqT0/e;-><init>(LpT0/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final j()LpT0/b;
    .locals 6

    .line 1
    new-instance v0, LpT0/b;

    .line 2
    .line 3
    invoke-virtual {p0}, LnT0/a$b;->k()LqT0/f;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, LnT0/a$b;->g()LqT0/d;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-virtual {p0}, LnT0/a$b;->e()LqT0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    invoke-virtual {p0}, LnT0/a$b;->d()LqT0/a;

    .line 16
    .line 17
    .line 18
    move-result-object v4

    .line 19
    iget-object v5, p0, LnT0/a$b;->c:Li8/m;

    .line 20
    .line 21
    invoke-direct/range {v0 .. v5}, LpT0/b;-><init>(LqT0/f;LqT0/d;LqT0/b;LqT0/a;Li8/m;)V

    .line 22
    .line 23
    .line 24
    return-object v0
.end method

.method public final k()LqT0/f;
    .locals 2

    .line 1
    new-instance v0, LqT0/f;

    .line 2
    .line 3
    invoke-virtual {p0}, LnT0/a$b;->h()Lorg/xbet/themeswitch/impl/data/b;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LqT0/f;-><init>(LpT0/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
