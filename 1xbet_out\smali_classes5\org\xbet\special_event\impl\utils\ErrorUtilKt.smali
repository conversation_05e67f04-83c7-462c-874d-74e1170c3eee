.class public final Lorg/xbet/special_event/impl/utils/ErrorUtilKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\u001a4\u0010\u0005\u001a\u00028\u0000\"\u0004\u0008\u0000\u0010\u00002\u001c\u0010\u0004\u001a\u0018\u0008\u0001\u0012\n\u0012\u0008\u0012\u0004\u0012\u00028\u00000\u0002\u0012\u0006\u0012\u0004\u0018\u00010\u00030\u0001H\u0080@\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\t\u00b2\u0006\u000c\u0010\u0008\u001a\u00020\u00078\nX\u008a\u0084\u0002"
    }
    d2 = {
        "T",
        "Lkotlin/Function1;",
        "Lkotlin/coroutines/e;",
        "",
        "block",
        "b",
        "(Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "networkAvailable",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Ljava/lang/Throwable;)Z
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/utils/ErrorUtilKt;->c(Ljava/lang/Throwable;)Z

    move-result p0

    return p0
.end method

.method public static final b(Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .param p0    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lkotlin/coroutines/e<",
            "-TT;>;+",
            "Ljava/lang/Object;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-TT;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;

    .line 21
    .line 22
    invoke-direct {v0, p1}, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;-><init>(Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x0

    .line 34
    const/4 v4, 0x2

    .line 35
    const/4 v5, 0x1

    .line 36
    if-eqz v2, :cond_4

    .line 37
    .line 38
    if-eq v2, v5, :cond_3

    .line 39
    .line 40
    if-ne v2, v4, :cond_2

    .line 41
    .line 42
    iget p0, v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;->I$0:I

    .line 43
    .line 44
    iget-object v2, v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;->L$0:Ljava/lang/Object;

    .line 45
    .line 46
    check-cast v2, Lkotlin/jvm/functions/Function1;

    .line 47
    .line 48
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 49
    .line 50
    .line 51
    :cond_1
    move p1, p0

    .line 52
    move-object p0, v2

    .line 53
    goto :goto_1

    .line 54
    :cond_2
    new-instance p0, Ljava/lang/IllegalStateException;

    .line 55
    .line 56
    const-string p1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 57
    .line 58
    invoke-direct {p0, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 59
    .line 60
    .line 61
    throw p0

    .line 62
    :cond_3
    iget p0, v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;->I$0:I

    .line 63
    .line 64
    iget-object v2, v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;->L$0:Ljava/lang/Object;

    .line 65
    .line 66
    check-cast v2, Lkotlin/jvm/functions/Function1;

    .line 67
    .line 68
    :try_start_0
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 69
    .line 70
    .line 71
    return-object p1

    .line 72
    :catchall_0
    move-exception p1

    .line 73
    goto :goto_2

    .line 74
    :cond_4
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 75
    .line 76
    .line 77
    const/4 p1, 0x0

    .line 78
    :goto_1
    :try_start_1
    iput-object p0, v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;->L$0:Ljava/lang/Object;

    .line 79
    .line 80
    iput p1, v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;->I$0:I

    .line 81
    .line 82
    iput v5, v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;->label:I

    .line 83
    .line 84
    invoke-interface {p0, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 85
    .line 86
    .line 87
    move-result-object p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 88
    if-ne p0, v1, :cond_5

    .line 89
    .line 90
    goto :goto_4

    .line 91
    :cond_5
    return-object p0

    .line 92
    :catchall_1
    move-exception v2

    .line 93
    move-object v8, v2

    .line 94
    move-object v2, p0

    .line 95
    move p0, p1

    .line 96
    move-object p1, v8

    .line 97
    :goto_2
    new-instance v6, Lorg/xbet/special_event/impl/utils/a;

    .line 98
    .line 99
    invoke-direct {v6, p1}, Lorg/xbet/special_event/impl/utils/a;-><init>(Ljava/lang/Throwable;)V

    .line 100
    .line 101
    .line 102
    invoke-static {v6}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 103
    .line 104
    .line 105
    move-result-object v6

    .line 106
    invoke-static {v6}, Lorg/xbet/special_event/impl/utils/ErrorUtilKt;->d(Lkotlin/j;)Z

    .line 107
    .line 108
    .line 109
    move-result v6

    .line 110
    if-eqz v6, :cond_6

    .line 111
    .line 112
    instance-of v6, p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 113
    .line 114
    if-eqz v6, :cond_8

    .line 115
    .line 116
    move-object v6, p1

    .line 117
    check-cast v6, Lcom/xbet/onexcore/data/model/ServerException;

    .line 118
    .line 119
    invoke-virtual {v6}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 120
    .line 121
    .line 122
    move-result v6

    .line 123
    if-nez v6, :cond_8

    .line 124
    .line 125
    :cond_6
    add-int/lit8 v6, p0, 0x1

    .line 126
    .line 127
    const/4 v7, 0x3

    .line 128
    if-ge p0, v7, :cond_7

    .line 129
    .line 130
    move p0, v6

    .line 131
    const/4 v6, 0x1

    .line 132
    goto :goto_3

    .line 133
    :cond_7
    move p0, v6

    .line 134
    :cond_8
    const/4 v6, 0x0

    .line 135
    :goto_3
    if-eqz v6, :cond_9

    .line 136
    .line 137
    iput-object v2, v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;->L$0:Ljava/lang/Object;

    .line 138
    .line 139
    iput p0, v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;->I$0:I

    .line 140
    .line 141
    iput v4, v0, Lorg/xbet/special_event/impl/utils/ErrorUtilKt$runWithRetryWhenInternalServerError$1;->label:I

    .line 142
    .line 143
    const-wide/16 v6, 0xbb8

    .line 144
    .line 145
    invoke-static {v6, v7, v0}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 146
    .line 147
    .line 148
    move-result-object p1

    .line 149
    if-ne p1, v1, :cond_1

    .line 150
    .line 151
    :goto_4
    return-object v1

    .line 152
    :cond_9
    throw p1
.end method

.method public static final c(Ljava/lang/Throwable;)Z
    .locals 5

    .line 1
    const/4 v0, 0x2

    .line 2
    new-array v0, v0, [Ljava/lang/Class;

    .line 3
    .line 4
    const-class v1, Ljava/net/SocketTimeoutException;

    .line 5
    .line 6
    const/4 v2, 0x0

    .line 7
    aput-object v1, v0, v2

    .line 8
    .line 9
    const-class v1, Ljava/net/UnknownHostException;

    .line 10
    .line 11
    const/4 v3, 0x1

    .line 12
    aput-object v1, v0, v3

    .line 13
    .line 14
    invoke-static {v0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v1

    .line 26
    if-eqz v1, :cond_1

    .line 27
    .line 28
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    move-object v4, v1

    .line 33
    check-cast v4, Ljava/lang/Class;

    .line 34
    .line 35
    invoke-virtual {v4, p0}, Ljava/lang/Class;->isInstance(Ljava/lang/Object;)Z

    .line 36
    .line 37
    .line 38
    move-result v4

    .line 39
    if-eqz v4, :cond_0

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_1
    const/4 v1, 0x0

    .line 43
    :goto_0
    check-cast v1, Ljava/lang/Class;

    .line 44
    .line 45
    if-eqz v1, :cond_2

    .line 46
    .line 47
    invoke-virtual {v1, p0}, Ljava/lang/Class;->isInstance(Ljava/lang/Object;)Z

    .line 48
    .line 49
    .line 50
    move-result v2

    .line 51
    :cond_2
    xor-int/lit8 p0, v2, 0x1

    .line 52
    .line 53
    return p0
.end method

.method public static final d(Lkotlin/j;)Z
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/j<",
            "Ljava/lang/Boolean;",
            ">;)Z"
        }
    .end annotation

    .line 1
    invoke-interface {p0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Ljava/lang/Boolean;

    .line 6
    .line 7
    invoke-virtual {p0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 8
    .line 9
    .line 10
    move-result p0

    .line 11
    return p0
.end method
