.class public final synthetic LM1/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/graphics/SurfaceTexture$OnFrameAvailableListener;


# instance fields
.field public final synthetic a:LM1/h;


# direct methods
.method public synthetic constructor <init>(LM1/h;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LM1/g;->a:LM1/h;

    return-void
.end method


# virtual methods
.method public final onFrameAvailable(Landroid/graphics/SurfaceTexture;)V
    .locals 1

    .line 1
    iget-object v0, p0, LM1/g;->a:LM1/h;

    invoke-static {v0, p1}, LM1/h;->b(LM1/h;Landroid/graphics/SurfaceTexture;)V

    return-void
.end method
