.class public final enum Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\u000e\n\u0002\u0010\u000e\n\u0002\u0008\u0002\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0006\u0010\u000f\u001a\u00020\u0010J\u0006\u0010\u0011\u001a\u00020\u0010j\u0002\u0008\u0004j\u0002\u0008\u0005j\u0002\u0008\u0006j\u0002\u0008\u0007j\u0002\u0008\u0008j\u0002\u0008\tj\u0002\u0008\nj\u0002\u0008\u000bj\u0002\u0008\u000cj\u0002\u0008\rj\u0002\u0008\u000e\u00a8\u0006\u0012"
    }
    d2 = {
        "Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "ONEXGAMES_ALL_GAMES_CLICKED",
        "ONEXGAMES_PROMO_CLICKED",
        "ONEXGAMES_CASHBACK_CLICKED",
        "ONEXGAMES_FAVORITE_CLICKED",
        "ONEXGAMES_PROMO_LUCKY_WHEEL_CLICKED",
        "ONEXGAMES_PROMO_BONUS_CLICKED",
        "ONEXGAMES_PROMO_BONUS_INFO_CLICKED",
        "ONEXGAMES_PROMO_QUEST_CLICKED",
        "ONEXGAMES_PROMO_WEEKLY_REWARD_CLICKED",
        "ONEXGAMES_PROMO_TOURNAMENT_CLICKED",
        "ONEXGAMES_PROMO_JACKPOT_CLICKED",
        "getKey",
        "",
        "getValue",
        "analytics_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

.field public static final enum ONEXGAMES_ALL_GAMES_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

.field public static final enum ONEXGAMES_CASHBACK_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

.field public static final enum ONEXGAMES_FAVORITE_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

.field public static final enum ONEXGAMES_PROMO_BONUS_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

.field public static final enum ONEXGAMES_PROMO_BONUS_INFO_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

.field public static final enum ONEXGAMES_PROMO_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

.field public static final enum ONEXGAMES_PROMO_JACKPOT_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

.field public static final enum ONEXGAMES_PROMO_LUCKY_WHEEL_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

.field public static final enum ONEXGAMES_PROMO_QUEST_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

.field public static final enum ONEXGAMES_PROMO_TOURNAMENT_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

.field public static final enum ONEXGAMES_PROMO_WEEKLY_REWARD_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 2
    .line 3
    const-string v1, "ONEXGAMES_ALL_GAMES_CLICKED"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_ALL_GAMES_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 12
    .line 13
    const-string v1, "ONEXGAMES_PROMO_CLICKED"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 20
    .line 21
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 22
    .line 23
    const-string v1, "ONEXGAMES_CASHBACK_CLICKED"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;-><init>(Ljava/lang/String;I)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_CASHBACK_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 30
    .line 31
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 32
    .line 33
    const-string v1, "ONEXGAMES_FAVORITE_CLICKED"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;-><init>(Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_FAVORITE_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 42
    .line 43
    const-string v1, "ONEXGAMES_PROMO_LUCKY_WHEEL_CLICKED"

    .line 44
    .line 45
    const/4 v2, 0x4

    .line 46
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;-><init>(Ljava/lang/String;I)V

    .line 47
    .line 48
    .line 49
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_LUCKY_WHEEL_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 50
    .line 51
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 52
    .line 53
    const-string v1, "ONEXGAMES_PROMO_BONUS_CLICKED"

    .line 54
    .line 55
    const/4 v2, 0x5

    .line 56
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;-><init>(Ljava/lang/String;I)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_BONUS_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 60
    .line 61
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 62
    .line 63
    const-string v1, "ONEXGAMES_PROMO_BONUS_INFO_CLICKED"

    .line 64
    .line 65
    const/4 v2, 0x6

    .line 66
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;-><init>(Ljava/lang/String;I)V

    .line 67
    .line 68
    .line 69
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_BONUS_INFO_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 70
    .line 71
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 72
    .line 73
    const-string v1, "ONEXGAMES_PROMO_QUEST_CLICKED"

    .line 74
    .line 75
    const/4 v2, 0x7

    .line 76
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;-><init>(Ljava/lang/String;I)V

    .line 77
    .line 78
    .line 79
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_QUEST_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 80
    .line 81
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 82
    .line 83
    const-string v1, "ONEXGAMES_PROMO_WEEKLY_REWARD_CLICKED"

    .line 84
    .line 85
    const/16 v2, 0x8

    .line 86
    .line 87
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;-><init>(Ljava/lang/String;I)V

    .line 88
    .line 89
    .line 90
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_WEEKLY_REWARD_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 91
    .line 92
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 93
    .line 94
    const-string v1, "ONEXGAMES_PROMO_TOURNAMENT_CLICKED"

    .line 95
    .line 96
    const/16 v2, 0x9

    .line 97
    .line 98
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;-><init>(Ljava/lang/String;I)V

    .line 99
    .line 100
    .line 101
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_TOURNAMENT_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 102
    .line 103
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 104
    .line 105
    const-string v1, "ONEXGAMES_PROMO_JACKPOT_CLICKED"

    .line 106
    .line 107
    const/16 v2, 0xa

    .line 108
    .line 109
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;-><init>(Ljava/lang/String;I)V

    .line 110
    .line 111
    .line 112
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_JACKPOT_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 113
    .line 114
    invoke-static {}, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->a()[Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 115
    .line 116
    .line 117
    move-result-object v0

    .line 118
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->$VALUES:[Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 119
    .line 120
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 121
    .line 122
    .line 123
    move-result-object v0

    .line 124
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->$ENTRIES:Lkotlin/enums/a;

    .line 125
    .line 126
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;
    .locals 3

    .line 1
    const/16 v0, 0xb

    new-array v0, v0, [Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_ALL_GAMES_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_CASHBACK_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_FAVORITE_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_LUCKY_WHEEL_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_BONUS_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_BONUS_INFO_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_QUEST_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_WEEKLY_REWARD_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    const/16 v2, 0x8

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_TOURNAMENT_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    const/16 v2, 0x9

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->ONEXGAMES_PROMO_JACKPOT_CLICKED:Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    const/16 v2, 0xa

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;->$VALUES:[Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getKey()Ljava/lang/String;
    .locals 2
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    packed-switch v0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw v0

    .line 18
    :pswitch_0
    const-string v0, "point"

    .line 19
    .line 20
    return-object v0

    .line 21
    :pswitch_1
    const-string v0, ""

    .line 22
    .line 23
    return-object v0

    .line 24
    nop

    .line 25
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method

.method public final getValue()Ljava/lang/String;
    .locals 2
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamesEventType$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    packed-switch v0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw v0

    .line 18
    :pswitch_0
    const-string v0, "jackpot"

    .line 19
    .line 20
    return-object v0

    .line 21
    :pswitch_1
    const-string v0, "daily_tournament"

    .line 22
    .line 23
    return-object v0

    .line 24
    :pswitch_2
    const-string v0, "weekly_reward"

    .line 25
    .line 26
    return-object v0

    .line 27
    :pswitch_3
    const-string v0, "daily_quest"

    .line 28
    .line 29
    return-object v0

    .line 30
    :pswitch_4
    const-string v0, "bonus_info"

    .line 31
    .line 32
    return-object v0

    .line 33
    :pswitch_5
    const-string v0, "bonus"

    .line 34
    .line 35
    return-object v0

    .line 36
    :pswitch_6
    const-string v0, "lucky_wheel"

    .line 37
    .line 38
    return-object v0

    .line 39
    :pswitch_7
    const-string v0, "games_favor"

    .line 40
    .line 41
    return-object v0

    .line 42
    :pswitch_8
    const-string v0, "games_cashback"

    .line 43
    .line 44
    return-object v0

    .line 45
    :pswitch_9
    const-string v0, "games_bonuses"

    .line 46
    .line 47
    return-object v0

    .line 48
    :pswitch_a
    const-string v0, "games_all"

    .line 49
    .line 50
    return-object v0

    .line 51
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
