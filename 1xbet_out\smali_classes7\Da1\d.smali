.class public final synthetic LDa1/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function0;


# direct methods
.method public synthetic constructor <init>(L<PERSON>lin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LDa1/d;->a:Lkotlin/jvm/functions/Function0;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LDa1/d;->a:Lkotlin/jvm/functions/Function0;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoPlainTournamentViewHolderKt;->a(<PERSON><PERSON><PERSON>/jvm/functions/Function0;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
