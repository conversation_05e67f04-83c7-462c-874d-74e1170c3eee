.class public final LEc1/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lzc1/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0000\u0018\u00002\u00020\u0001B\t\u0008\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J/\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u000f\u0010\r\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008\r\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "LEc1/a;",
        "Lzc1/a;",
        "<init>",
        "()V",
        "Lorg/xbet/uikit/components/dialog/DialogFields;",
        "dialogFields",
        "",
        "isInvisibleDialog",
        "showDefaultMessage",
        "reverseNeutralButton",
        "LTZ0/h;",
        "b",
        "(Lorg/xbet/uikit/components/dialog/DialogFields;ZZZ)LTZ0/h;",
        "a",
        "()LTZ0/h;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a()LTZ0/h;
    .locals 16
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xplatform/logout/impl/presentation/LogoutDialog;->x1:Lorg/xplatform/logout/impl/presentation/LogoutDialog$a;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 4
    .line 5
    sget-object v12, Lorg/xbet/uikit/components/dialog/AlertType;->INFO:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 6
    .line 7
    const/16 v14, 0xbfb

    .line 8
    .line 9
    const/4 v15, 0x0

    .line 10
    const/4 v2, 0x0

    .line 11
    const/4 v3, 0x0

    .line 12
    const-string v4, ""

    .line 13
    .line 14
    const/4 v5, 0x0

    .line 15
    const/4 v6, 0x0

    .line 16
    const/4 v7, 0x0

    .line 17
    const/4 v8, 0x0

    .line 18
    const/4 v9, 0x0

    .line 19
    const/4 v10, 0x0

    .line 20
    const/4 v11, 0x0

    .line 21
    const/4 v13, 0x0

    .line 22
    invoke-direct/range {v1 .. v15}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 23
    .line 24
    .line 25
    const/4 v2, 0x1

    .line 26
    const/4 v3, 0x0

    .line 27
    invoke-virtual {v0, v1, v2, v3, v3}, Lorg/xplatform/logout/impl/presentation/LogoutDialog$a;->a(Lorg/xbet/uikit/components/dialog/DialogFields;ZZZ)Lorg/xplatform/logout/impl/presentation/LogoutDialog;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    return-object v0
.end method

.method public b(Lorg/xbet/uikit/components/dialog/DialogFields;ZZZ)LTZ0/h;
    .locals 1
    .param p1    # Lorg/xbet/uikit/components/dialog/DialogFields;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xplatform/logout/impl/presentation/LogoutDialog;->x1:Lorg/xplatform/logout/impl/presentation/LogoutDialog$a;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2, p3, p4}, Lorg/xplatform/logout/impl/presentation/LogoutDialog$a;->a(Lorg/xbet/uikit/components/dialog/DialogFields;ZZZ)Lorg/xplatform/logout/impl/presentation/LogoutDialog;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method
