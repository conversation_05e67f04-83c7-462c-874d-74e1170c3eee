.class public final LiD0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0006\u001a\u00020\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "LjD0/f;",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "",
        "sportId",
        "LjD0/i;",
        "a",
        "(LjD0/f;LSX0/c;J)LjD0/i;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LjD0/f;LSX0/c;J)LjD0/i;
    .locals 17
    .param p0    # LjD0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LjD0/i;

    .line 2
    .line 3
    new-instance v1, LjD0/h;

    .line 4
    .line 5
    invoke-virtual/range {p0 .. p0}, LjD0/f;->m()Z

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    invoke-virtual/range {p0 .. p0}, LjD0/f;->j()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    invoke-virtual/range {p0 .. p0}, LjD0/f;->h()Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object v4

    .line 17
    invoke-virtual/range {p0 .. p0}, LjD0/f;->c()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    invoke-virtual/range {p0 .. p0}, LjD0/f;->i()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v6

    .line 25
    invoke-direct/range {v1 .. v6}, LjD0/h;-><init>(ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 26
    .line 27
    .line 28
    new-instance v2, LNN0/a;

    .line 29
    .line 30
    invoke-virtual/range {p0 .. p0}, LjD0/f;->f()Z

    .line 31
    .line 32
    .line 33
    move-result v5

    .line 34
    const/4 v7, 0x4

    .line 35
    const/4 v8, 0x0

    .line 36
    const/4 v6, 0x0

    .line 37
    move-wide/from16 v3, p2

    .line 38
    .line 39
    invoke-direct/range {v2 .. v8}, LNN0/a;-><init>(JZLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual/range {p0 .. p0}, LjD0/f;->n()Z

    .line 43
    .line 44
    .line 45
    move-result v3

    .line 46
    if-eqz v3, :cond_0

    .line 47
    .line 48
    sget-object v3, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$d;->a:Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$d;

    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_0
    invoke-virtual/range {p0 .. p0}, LjD0/f;->k()Z

    .line 52
    .line 53
    .line 54
    move-result v3

    .line 55
    if-eqz v3, :cond_1

    .line 56
    .line 57
    new-instance v3, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$b;

    .line 58
    .line 59
    sget-object v5, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 60
    .line 61
    sget v10, Lpb/k;->data_is_missing:I

    .line 62
    .line 63
    const/16 v14, 0x1de

    .line 64
    .line 65
    const/4 v15, 0x0

    .line 66
    const/4 v6, 0x0

    .line 67
    const/4 v7, 0x0

    .line 68
    const/4 v8, 0x0

    .line 69
    const/4 v9, 0x0

    .line 70
    const/4 v11, 0x0

    .line 71
    const/4 v12, 0x0

    .line 72
    const/4 v13, 0x0

    .line 73
    move-object/from16 v4, p1

    .line 74
    .line 75
    invoke-static/range {v4 .. v15}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 76
    .line 77
    .line 78
    move-result-object v4

    .line 79
    invoke-direct {v3, v4}, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 80
    .line 81
    .line 82
    goto :goto_0

    .line 83
    :cond_1
    invoke-virtual/range {p0 .. p0}, LjD0/f;->l()Z

    .line 84
    .line 85
    .line 86
    move-result v3

    .line 87
    if-eqz v3, :cond_2

    .line 88
    .line 89
    new-instance v3, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$c;

    .line 90
    .line 91
    sget-object v6, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 92
    .line 93
    sget v11, Lpb/k;->data_retrieval_error:I

    .line 94
    .line 95
    const/16 v15, 0x1de

    .line 96
    .line 97
    const/16 v16, 0x0

    .line 98
    .line 99
    const/4 v7, 0x0

    .line 100
    const/4 v8, 0x0

    .line 101
    const/4 v9, 0x0

    .line 102
    const/4 v10, 0x0

    .line 103
    const/4 v12, 0x0

    .line 104
    const/4 v13, 0x0

    .line 105
    const/4 v14, 0x0

    .line 106
    move-object/from16 v5, p1

    .line 107
    .line 108
    invoke-static/range {v5 .. v16}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 109
    .line 110
    .line 111
    move-result-object v4

    .line 112
    invoke-direct {v3, v4}, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$c;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 113
    .line 114
    .line 115
    goto :goto_0

    .line 116
    :cond_2
    new-instance v3, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$a;

    .line 117
    .line 118
    invoke-virtual/range {p0 .. p0}, LjD0/f;->e()Ljava/util/List;

    .line 119
    .line 120
    .line 121
    move-result-object v4

    .line 122
    invoke-static {v4}, LHd/a;->g(Ljava/lang/Iterable;)LHd/c;

    .line 123
    .line 124
    .line 125
    move-result-object v4

    .line 126
    invoke-direct {v3, v4}, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$a;-><init>(LHd/c;)V

    .line 127
    .line 128
    .line 129
    :goto_0
    invoke-direct {v0, v1, v2, v3}, LjD0/i;-><init>(LjD0/h;LNN0/a;Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l;)V

    .line 130
    .line 131
    .line 132
    return-object v0
.end method
