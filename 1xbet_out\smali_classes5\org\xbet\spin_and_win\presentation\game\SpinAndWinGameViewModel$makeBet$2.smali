.class final Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.spin_and_win.presentation.game.SpinAndWinGameViewModel$makeBet$2"
    f = "SpinAndWinGameViewModel.kt"
    l = {
        0xf5
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->p4(D)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $betSum:D

.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;DLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;",
            "D",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    iput-wide p2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->$betSum:D

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;

    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    iget-wide v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->$betSum:D

    invoke-direct {p1, v0, v1, v2, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;DLkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->L$1:Ljava/lang/Object;

    .line 13
    .line 14
    check-cast v0, Lorg/xbet/games_section/api/models/GameBonusType;

    .line 15
    .line 16
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->L$0:Ljava/lang/Object;

    .line 17
    .line 18
    check-cast v1, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 19
    .line 20
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 21
    .line 22
    .line 23
    move-object v5, v0

    .line 24
    :goto_0
    move-object v3, v1

    .line 25
    goto :goto_1

    .line 26
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 27
    .line 28
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 29
    .line 30
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 31
    .line 32
    .line 33
    throw p1

    .line 34
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 35
    .line 36
    .line 37
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 38
    .line 39
    invoke-static {p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/e;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    invoke-virtual {p1}, Lez0/e;->a()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    sget-object p1, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->EMPTY:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 48
    .line 49
    if-eq v1, p1, :cond_4

    .line 50
    .line 51
    iget-object v3, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 52
    .line 53
    invoke-static {v3}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->C3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lorg/xbet/core/domain/usecases/bonus/e;

    .line 54
    .line 55
    .line 56
    move-result-object v3

    .line 57
    invoke-virtual {v3}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 58
    .line 59
    .line 60
    move-result-object v3

    .line 61
    invoke-virtual {v3}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 62
    .line 63
    .line 64
    move-result-object v3

    .line 65
    invoke-virtual {v3}, Lorg/xbet/games_section/api/models/GameBonusType;->isGameBonus()Z

    .line 66
    .line 67
    .line 68
    move-result v4

    .line 69
    if-eqz v4, :cond_2

    .line 70
    .line 71
    iget-object v4, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 72
    .line 73
    invoke-static {v4}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->L3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/h;

    .line 74
    .line 75
    .line 76
    move-result-object v4

    .line 77
    invoke-virtual {v4}, Lez0/h;->a()V

    .line 78
    .line 79
    .line 80
    iget-object v4, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 81
    .line 82
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/b$b;

    .line 83
    .line 84
    invoke-direct {v5, p1}, Lorg/xbet/spin_and_win/presentation/game/b$b;-><init>(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V

    .line 85
    .line 86
    .line 87
    invoke-static {v4, v5}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->X3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/b;)V

    .line 88
    .line 89
    .line 90
    :cond_2
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 91
    .line 92
    invoke-static {p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->D3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    iput-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->L$0:Ljava/lang/Object;

    .line 97
    .line 98
    iput-object v3, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->L$1:Ljava/lang/Object;

    .line 99
    .line 100
    iput v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->label:I

    .line 101
    .line 102
    invoke-virtual {p1, p0}, Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 103
    .line 104
    .line 105
    move-result-object p1

    .line 106
    if-ne p1, v0, :cond_3

    .line 107
    .line 108
    return-object v0

    .line 109
    :cond_3
    move-object v5, v3

    .line 110
    goto :goto_0

    .line 111
    :goto_1
    move-object v4, p1

    .line 112
    check-cast v4, Ljava/lang/String;

    .line 113
    .line 114
    new-instance v0, Ldz0/a;

    .line 115
    .line 116
    iget-wide v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->$betSum:D

    .line 117
    .line 118
    const/16 v7, 0x10

    .line 119
    .line 120
    const/4 v8, 0x0

    .line 121
    const/4 v6, 0x0

    .line 122
    invoke-direct/range {v0 .. v8}, Ldz0/a;-><init>(DLorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;Ljava/lang/String;Lorg/xbet/games_section/api/models/GameBonusType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 123
    .line 124
    .line 125
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 126
    .line 127
    invoke-static {p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->x3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/a;

    .line 128
    .line 129
    .line 130
    move-result-object p1

    .line 131
    invoke-virtual {p1, v0}, Lez0/a;->a(Ldz0/a;)V

    .line 132
    .line 133
    .line 134
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 135
    .line 136
    invoke-static {p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->O3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lorg/xbet/core/domain/usecases/game_info/H;

    .line 137
    .line 138
    .line 139
    move-result-object p1

    .line 140
    iget-wide v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->$betSum:D

    .line 141
    .line 142
    invoke-virtual {p1, v0, v1}, Lorg/xbet/core/domain/usecases/game_info/H;->a(D)V

    .line 143
    .line 144
    .line 145
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 146
    .line 147
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/b$a;

    .line 148
    .line 149
    iget-wide v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$makeBet$2;->$betSum:D

    .line 150
    .line 151
    invoke-static {v1, v2}, LHc/a;->c(D)Ljava/lang/Double;

    .line 152
    .line 153
    .line 154
    move-result-object v1

    .line 155
    invoke-direct {v0, v3, v1}, Lorg/xbet/spin_and_win/presentation/game/b$a;-><init>(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;Ljava/lang/Double;)V

    .line 156
    .line 157
    .line 158
    invoke-static {p1, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->X3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/b;)V

    .line 159
    .line 160
    .line 161
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 162
    .line 163
    return-object p1
.end method
