.class final Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.my_aggregator.data.datasource.remote.RecommendedGamesPagingDataSource$loadNextPage$games$1"
    f = "RecommendedGamesPagingDataSource.kt"
    l = {
        0x26
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;->l(Landroidx/paging/PagingSource$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/String;",
        "Lkotlin/coroutines/e<",
        "-",
        "Ljava/util/List<",
        "+",
        "LL91/d;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "",
        "token",
        "",
        "LL91/d;",
        "<anonymous>",
        "(Ljava/lang/String;)Ljava/util/List;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $pageNumber:I

.field final synthetic $params:Landroidx/paging/PagingSource$a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/paging/PagingSource$a<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;",
            ">;"
        }
    .end annotation
.end field

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;Landroidx/paging/PagingSource$a;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;",
            "Landroidx/paging/PagingSource$a<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;",
            ">;I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->$params:Landroidx/paging/PagingSource$a;

    iput p3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->$pageNumber:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->$params:Landroidx/paging/PagingSource$a;

    iget v3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->$pageNumber:I

    invoke-direct {v0, v1, v2, v3, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;Landroidx/paging/PagingSource$a;ILkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "LL91/d;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_3

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->L$0:Ljava/lang/Object;

    .line 28
    .line 29
    move-object v5, p1

    .line 30
    check-cast v5, Ljava/lang/String;

    .line 31
    .line 32
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;

    .line 33
    .line 34
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;->m(Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;)Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 35
    .line 36
    .line 37
    move-result-object v3

    .line 38
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->$params:Landroidx/paging/PagingSource$a;

    .line 39
    .line 40
    invoke-virtual {p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    check-cast p1, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;

    .line 45
    .line 46
    if-eqz p1, :cond_3

    .line 47
    .line 48
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;->a()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    if-nez p1, :cond_2

    .line 53
    .line 54
    goto :goto_1

    .line 55
    :cond_2
    :goto_0
    move-object v7, p1

    .line 56
    goto :goto_2

    .line 57
    :cond_3
    :goto_1
    const-string p1, ""

    .line 58
    .line 59
    goto :goto_0

    .line 60
    :goto_2
    iget v6, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->$pageNumber:I

    .line 61
    .line 62
    iput v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;->label:I

    .line 63
    .line 64
    const/16 v4, 0x1e

    .line 65
    .line 66
    move-object v8, p0

    .line 67
    invoke-virtual/range {v3 .. v8}, Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;->w(ILjava/lang/String;ILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    if-ne p1, v0, :cond_4

    .line 72
    .line 73
    return-object v0

    .line 74
    :cond_4
    :goto_3
    check-cast p1, Le8/b;

    .line 75
    .line 76
    invoke-virtual {p1}, Le8/b;->a()Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    check-cast p1, LL91/e;

    .line 81
    .line 82
    invoke-virtual {p1}, LL91/e;->b()Ljava/util/List;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    return-object p1
.end method
