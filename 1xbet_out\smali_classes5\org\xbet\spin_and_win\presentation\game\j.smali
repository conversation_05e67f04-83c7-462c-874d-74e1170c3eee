.class public final Lorg/xbet/spin_and_win/presentation/game/j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;LzX0/k;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->j0:LzX0/k;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lcz0/c$b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->i0:Lcz0/c$b;

    .line 2
    .line 3
    return-void
.end method
