.class final Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.who_win.domain.usecase.GetStageTableUseCase$invoke$1$6"
    f = "GetStageTableUseCase.kt"
    l = {
        0x3b
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->g(ILjava/lang/Integer;)Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/util/List<",
        "+",
        "LDy0/a;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u00020\u00032\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "",
        "LDy0/a;",
        "data",
        "",
        "<anonymous>",
        "(Ljava/util/List;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $initRequestInternal:Lkotlin/jvm/internal/Ref$BooleanRef;

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;


# direct methods
.method public constructor <init>(Lkotlin/jvm/internal/Ref$BooleanRef;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/internal/Ref$BooleanRef;",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;->$initRequestInternal:Lkotlin/jvm/internal/Ref$BooleanRef;

    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;->$initRequestInternal:Lkotlin/jvm/internal/Ref$BooleanRef;

    iget-object v2, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    invoke-direct {v0, v1, v2, p2}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;-><init>(Lkotlin/jvm/internal/Ref$BooleanRef;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;->invoke(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LDy0/a;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;->L$0:Ljava/lang/Object;

    .line 28
    .line 29
    check-cast p1, Ljava/util/List;

    .line 30
    .line 31
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;->$initRequestInternal:Lkotlin/jvm/internal/Ref$BooleanRef;

    .line 32
    .line 33
    const/4 v3, 0x0

    .line 34
    iput-boolean v3, v1, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    .line 35
    .line 36
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 37
    .line 38
    invoke-static {v1}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->b(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;)LEy0/a;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    new-instance v3, LKo0/a$b;

    .line 43
    .line 44
    invoke-direct {v3, p1}, LKo0/a$b;-><init>(Ljava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    iput v2, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;->label:I

    .line 48
    .line 49
    invoke-interface {v1, v3, p0}, LEy0/a;->c(LKo0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    if-ne p1, v0, :cond_2

    .line 54
    .line 55
    return-object v0

    .line 56
    :cond_2
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 57
    .line 58
    return-object p1
.end method
