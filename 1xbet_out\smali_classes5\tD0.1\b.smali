.class public final LtD0/b;
.super LVX0/a;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0001\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LtD0/b;",
        "LVX0/a;",
        "<init>",
        "()V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final h:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget v0, LVX0/a;->g:I

    .line 2
    .line 3
    sput v0, LtD0/b;->h:I

    .line 4
    .line 5
    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-direct {p0, v0, v1, v0}, LVX0/a;-><init>(Landroidx/recyclerview/widget/i$f;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 7
    .line 8
    invoke-static {}, Lorg/xbet/statistic/cycling/impl/cycling_player/presentation/adapter/delegate/CyclingAdapterDelegateKt;->d()LA4/c;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 13
    .line 14
    .line 15
    return-void
.end method
