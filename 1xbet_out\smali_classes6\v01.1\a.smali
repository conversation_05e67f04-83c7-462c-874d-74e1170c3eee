.class public final synthetic Lv01/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/widget/CompoundButton$OnCheckedChangeListener;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lv01/a;->a:Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;

    return-void
.end method


# virtual methods
.method public final onCheckedChanged(Landroid/widget/CompoundButton;Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lv01/a;->a:Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;

    invoke-static {v0, p1, p2}, Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;->a(Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;Landroid/widget/CompoundButton;Z)V

    return-void
.end method
