.class public final Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u000e\u0018\u00002\u00020\u0001B9\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0018\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0011\u001a\u00020\u0010H\u0080B\u00a2\u0006\u0004\u0008\u0013\u0010\u0014R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0015R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0017R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010\u0019R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010\u001bR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010\u001dR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001f\u00a8\u0006 "
    }
    d2 = {
        "Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;",
        "",
        "Lorg/xbet/core/domain/usecases/game_state/c;",
        "gameFinishStatusChangedUseCase",
        "Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;",
        "unfinishedGameLoadedScenario",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "addCommandScenario",
        "Lorg/xbet/tile_matching/domain/usecases/d;",
        "isTileMatchingGameActiveUseCase",
        "Lorg/xbet/core/domain/usecases/bet/p;",
        "setBetSumUseCase",
        "LAT0/a;",
        "tileMatchingRepository",
        "<init>",
        "(Lorg/xbet/core/domain/usecases/game_state/c;Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/tile_matching/domain/usecases/d;Lorg/xbet/core/domain/usecases/bet/p;LAT0/a;)V",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
        "gameType",
        "",
        "a",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xbet/core/domain/usecases/game_state/c;",
        "b",
        "Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;",
        "c",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "d",
        "Lorg/xbet/tile_matching/domain/usecases/d;",
        "e",
        "Lorg/xbet/core/domain/usecases/bet/p;",
        "f",
        "LAT0/a;",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/core/domain/usecases/game_state/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/core/domain/usecases/AddCommandScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/tile_matching/domain/usecases/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xbet/core/domain/usecases/bet/p;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:LAT0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/core/domain/usecases/game_state/c;Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/tile_matching/domain/usecases/d;Lorg/xbet/core/domain/usecases/bet/p;LAT0/a;)V
    .locals 0
    .param p1    # Lorg/xbet/core/domain/usecases/game_state/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/core/domain/usecases/AddCommandScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/tile_matching/domain/usecases/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/core/domain/usecases/bet/p;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LAT0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->a:Lorg/xbet/core/domain/usecases/game_state/c;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->b:Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->c:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->d:Lorg/xbet/tile_matching/domain/usecases/d;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->e:Lorg/xbet/core/domain/usecases/bet/p;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->f:LAT0/a;

    .line 15
    .line 16
    return-void
.end method


# virtual methods
.method public final a(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 11
    .param p1    # Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;-><init>(Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x0

    .line 34
    const/4 v4, 0x5

    .line 35
    const/4 v5, 0x4

    .line 36
    const/4 v6, 0x3

    .line 37
    const/4 v7, 0x2

    .line 38
    const/4 v8, 0x1

    .line 39
    if-eqz v2, :cond_6

    .line 40
    .line 41
    if-eq v2, v8, :cond_5

    .line 42
    .line 43
    if-eq v2, v7, :cond_4

    .line 44
    .line 45
    if-eq v2, v6, :cond_3

    .line 46
    .line 47
    if-eq v2, v5, :cond_2

    .line 48
    .line 49
    if-ne v2, v4, :cond_1

    .line 50
    .line 51
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 52
    .line 53
    .line 54
    goto/16 :goto_6

    .line 55
    .line 56
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 57
    .line 58
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 59
    .line 60
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 61
    .line 62
    .line 63
    throw p1

    .line 64
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 65
    .line 66
    .line 67
    goto/16 :goto_4

    .line 68
    .line 69
    :cond_3
    iget-object p1, v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 70
    .line 71
    check-cast p1, LzT0/e;

    .line 72
    .line 73
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 74
    .line 75
    .line 76
    goto :goto_3

    .line 77
    :cond_4
    iget-object p1, v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 78
    .line 79
    check-cast p1, LzT0/e;

    .line 80
    .line 81
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 82
    .line 83
    .line 84
    goto :goto_2

    .line 85
    :cond_5
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 86
    .line 87
    .line 88
    goto :goto_1

    .line 89
    :cond_6
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 90
    .line 91
    .line 92
    iget-object p2, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->f:LAT0/a;

    .line 93
    .line 94
    iput v8, v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;->label:I

    .line 95
    .line 96
    invoke-interface {p2, p1, v0}, LAT0/a;->d(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 97
    .line 98
    .line 99
    move-result-object p2

    .line 100
    if-ne p2, v1, :cond_7

    .line 101
    .line 102
    goto/16 :goto_5

    .line 103
    .line 104
    :cond_7
    :goto_1
    check-cast p2, LzT0/e;

    .line 105
    .line 106
    iget-object p1, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->f:LAT0/a;

    .line 107
    .line 108
    invoke-interface {p1, p2}, LAT0/a;->i(LzT0/e;)V

    .line 109
    .line 110
    .line 111
    iget-object p1, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->f:LAT0/a;

    .line 112
    .line 113
    invoke-virtual {p2}, LzT0/e;->f()LzT0/d;

    .line 114
    .line 115
    .line 116
    move-result-object v2

    .line 117
    invoke-virtual {v2}, LzT0/d;->a()Ljava/util/List;

    .line 118
    .line 119
    .line 120
    move-result-object v2

    .line 121
    invoke-interface {p1, v2}, LAT0/a;->e(Ljava/util/List;)V

    .line 122
    .line 123
    .line 124
    iget-object p1, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->d:Lorg/xbet/tile_matching/domain/usecases/d;

    .line 125
    .line 126
    invoke-virtual {p1}, Lorg/xbet/tile_matching/domain/usecases/d;->a()Z

    .line 127
    .line 128
    .line 129
    move-result p1

    .line 130
    if-eqz p1, :cond_c

    .line 131
    .line 132
    iget-object p1, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->a:Lorg/xbet/core/domain/usecases/game_state/c;

    .line 133
    .line 134
    const/4 v2, 0x0

    .line 135
    invoke-virtual {p1, v2}, Lorg/xbet/core/domain/usecases/game_state/c;->a(Z)V

    .line 136
    .line 137
    .line 138
    iget-object p1, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->b:Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;

    .line 139
    .line 140
    iput-object p2, v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 141
    .line 142
    iput v7, v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;->label:I

    .line 143
    .line 144
    invoke-static {p1, v2, v0, v8, v3}, Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;->b(Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;ZLkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 145
    .line 146
    .line 147
    move-result-object p1

    .line 148
    if-ne p1, v1, :cond_8

    .line 149
    .line 150
    goto :goto_5

    .line 151
    :cond_8
    move-object p1, p2

    .line 152
    :goto_2
    iget-object p2, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->e:Lorg/xbet/core/domain/usecases/bet/p;

    .line 153
    .line 154
    invoke-virtual {p1}, LzT0/e;->d()D

    .line 155
    .line 156
    .line 157
    move-result-wide v9

    .line 158
    invoke-virtual {p2, v9, v10}, Lorg/xbet/core/domain/usecases/bet/p;->a(D)V

    .line 159
    .line 160
    .line 161
    iget-object p2, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->c:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 162
    .line 163
    new-instance v2, LTv/a$m;

    .line 164
    .line 165
    invoke-virtual {p1}, LzT0/e;->a()J

    .line 166
    .line 167
    .line 168
    move-result-wide v9

    .line 169
    invoke-direct {v2, v9, v10}, LTv/a$m;-><init>(J)V

    .line 170
    .line 171
    .line 172
    iput-object p1, v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 173
    .line 174
    iput v6, v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;->label:I

    .line 175
    .line 176
    invoke-virtual {p2, v2, v0}, Lorg/xbet/core/domain/usecases/AddCommandScenario;->l(LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 177
    .line 178
    .line 179
    move-result-object p2

    .line 180
    if-ne p2, v1, :cond_9

    .line 181
    .line 182
    goto :goto_5

    .line 183
    :cond_9
    :goto_3
    iget-object p2, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->c:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 184
    .line 185
    new-instance v2, LTv/a$g;

    .line 186
    .line 187
    invoke-virtual {p1}, LzT0/e;->e()Lorg/xbet/games_section/api/models/GameBonus;

    .line 188
    .line 189
    .line 190
    move-result-object p1

    .line 191
    invoke-direct {v2, p1}, LTv/a$g;-><init>(Lorg/xbet/games_section/api/models/GameBonus;)V

    .line 192
    .line 193
    .line 194
    iput-object v3, v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 195
    .line 196
    iput v5, v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;->label:I

    .line 197
    .line 198
    invoke-virtual {p2, v2, v0}, Lorg/xbet/core/domain/usecases/AddCommandScenario;->l(LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 199
    .line 200
    .line 201
    move-result-object p1

    .line 202
    if-ne p1, v1, :cond_a

    .line 203
    .line 204
    goto :goto_5

    .line 205
    :cond_a
    :goto_4
    iget-object p1, p0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;->c:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 206
    .line 207
    new-instance p2, LTv/a$v;

    .line 208
    .line 209
    invoke-direct {p2, v8}, LTv/a$v;-><init>(Z)V

    .line 210
    .line 211
    .line 212
    iput v4, v0, Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario$invoke$1;->label:I

    .line 213
    .line 214
    invoke-virtual {p1, p2, v0}, Lorg/xbet/core/domain/usecases/AddCommandScenario;->l(LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 215
    .line 216
    .line 217
    move-result-object p1

    .line 218
    if-ne p1, v1, :cond_b

    .line 219
    .line 220
    :goto_5
    return-object v1

    .line 221
    :cond_b
    :goto_6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 222
    .line 223
    return-object p1

    .line 224
    :cond_c
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 225
    .line 226
    return-object p1
.end method
