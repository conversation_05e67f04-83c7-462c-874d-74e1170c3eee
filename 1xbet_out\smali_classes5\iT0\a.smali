.class public final LiT0/a;
.super LwX0/B;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0003\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u000f\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "LiT0/a;",
        "LwX0/B;",
        "<init>",
        "()V",
        "Landroidx/fragment/app/u;",
        "factory",
        "Landroidx/fragment/app/Fragment;",
        "createFragment",
        "(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;",
        "",
        "needAuth",
        "()Z",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, LwX0/B;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public createFragment(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;
    .locals 0
    .param p1    # Landroidx/fragment/app/u;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object p1, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->m0:Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$a;

    .line 2
    .line 3
    invoke-virtual {p1}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$a;->a()Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public needAuth()Z
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    return v0
.end method
