.class public final LqN0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000x\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u001e\u0018\u00002\u00020\u0001By\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\'\u0010\'\u001a\u00020&2\u0006\u0010!\u001a\u00020 2\u0006\u0010#\u001a\u00020\"2\u0006\u0010%\u001a\u00020$H\u0000\u00a2\u0006\u0004\u0008\'\u0010(R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010)R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00105R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00109R\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u0010;R\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=R\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010?R\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010AR\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010C\u00a8\u0006D"
    }
    d2 = {
        "LqN0/d;",
        "LQW0/a;",
        "LQW0/c;",
        "coroutinesLib",
        "Lf8/g;",
        "serviceGenerator",
        "LHX0/e;",
        "resourceManager",
        "LSX0/a;",
        "lottieConfigurator",
        "Li8/l;",
        "getThemeStreamUseCase",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "LnN0/a;",
        "resultsGridLocalDataSource",
        "LTn/a;",
        "sportRepository",
        "Lorg/xbet/onexdatabase/OnexDatabase;",
        "onexDatabase",
        "LDH0/a;",
        "statisticScreenFactory",
        "LkC0/a;",
        "gameScreenFactory",
        "Lc8/h;",
        "requestParamsDataSource",
        "LEN0/f;",
        "statisticCoreFeature",
        "<init>",
        "(LQW0/c;Lf8/g;LHX0/e;LSX0/a;Li8/l;Lorg/xbet/ui_common/utils/M;Lorg/xbet/ui_common/utils/internet/a;LnN0/a;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LDH0/a;LkC0/a;Lc8/h;LEN0/f;)V",
        "",
        "gameId",
        "",
        "sportId",
        "LwX0/c;",
        "router",
        "LqN0/c;",
        "a",
        "(Ljava/lang/String;JLwX0/c;)LqN0/c;",
        "LQW0/c;",
        "b",
        "Lf8/g;",
        "c",
        "LHX0/e;",
        "d",
        "LSX0/a;",
        "e",
        "Li8/l;",
        "f",
        "Lorg/xbet/ui_common/utils/M;",
        "g",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "h",
        "LnN0/a;",
        "i",
        "LTn/a;",
        "j",
        "Lorg/xbet/onexdatabase/OnexDatabase;",
        "k",
        "LDH0/a;",
        "l",
        "LkC0/a;",
        "m",
        "Lc8/h;",
        "n",
        "LEN0/f;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LQW0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Li8/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:LnN0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:LTn/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lorg/xbet/onexdatabase/OnexDatabase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:LDH0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:LkC0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:LEN0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lf8/g;LHX0/e;LSX0/a;Li8/l;Lorg/xbet/ui_common/utils/M;Lorg/xbet/ui_common/utils/internet/a;LnN0/a;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LDH0/a;LkC0/a;Lc8/h;LEN0/f;)V
    .locals 0
    .param p1    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Li8/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LnN0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LTn/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/onexdatabase/OnexDatabase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LDH0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LkC0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LEN0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LqN0/d;->a:LQW0/c;

    .line 5
    .line 6
    iput-object p2, p0, LqN0/d;->b:Lf8/g;

    .line 7
    .line 8
    iput-object p3, p0, LqN0/d;->c:LHX0/e;

    .line 9
    .line 10
    iput-object p4, p0, LqN0/d;->d:LSX0/a;

    .line 11
    .line 12
    iput-object p5, p0, LqN0/d;->e:Li8/l;

    .line 13
    .line 14
    iput-object p6, p0, LqN0/d;->f:Lorg/xbet/ui_common/utils/M;

    .line 15
    .line 16
    iput-object p7, p0, LqN0/d;->g:Lorg/xbet/ui_common/utils/internet/a;

    .line 17
    .line 18
    iput-object p8, p0, LqN0/d;->h:LnN0/a;

    .line 19
    .line 20
    iput-object p9, p0, LqN0/d;->i:LTn/a;

    .line 21
    .line 22
    iput-object p10, p0, LqN0/d;->j:Lorg/xbet/onexdatabase/OnexDatabase;

    .line 23
    .line 24
    iput-object p11, p0, LqN0/d;->k:LDH0/a;

    .line 25
    .line 26
    iput-object p12, p0, LqN0/d;->l:LkC0/a;

    .line 27
    .line 28
    iput-object p13, p0, LqN0/d;->m:Lc8/h;

    .line 29
    .line 30
    iput-object p14, p0, LqN0/d;->n:LEN0/f;

    .line 31
    .line 32
    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/String;JLwX0/c;)LqN0/c;
    .locals 20
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, LqN0/a;->a()LqN0/c$a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, v0, LqN0/d;->a:LQW0/c;

    .line 8
    .line 9
    iget-object v4, v0, LqN0/d;->b:Lf8/g;

    .line 10
    .line 11
    iget-object v8, v0, LqN0/d;->c:LHX0/e;

    .line 12
    .line 13
    iget-object v9, v0, LqN0/d;->d:LSX0/a;

    .line 14
    .line 15
    iget-object v10, v0, LqN0/d;->e:Li8/l;

    .line 16
    .line 17
    iget-object v11, v0, LqN0/d;->f:Lorg/xbet/ui_common/utils/M;

    .line 18
    .line 19
    iget-object v12, v0, LqN0/d;->g:Lorg/xbet/ui_common/utils/internet/a;

    .line 20
    .line 21
    iget-object v13, v0, LqN0/d;->h:LnN0/a;

    .line 22
    .line 23
    iget-object v14, v0, LqN0/d;->i:LTn/a;

    .line 24
    .line 25
    iget-object v15, v0, LqN0/d;->j:Lorg/xbet/onexdatabase/OnexDatabase;

    .line 26
    .line 27
    iget-object v3, v0, LqN0/d;->k:LDH0/a;

    .line 28
    .line 29
    iget-object v5, v0, LqN0/d;->l:LkC0/a;

    .line 30
    .line 31
    iget-object v6, v0, LqN0/d;->m:Lc8/h;

    .line 32
    .line 33
    move-object/from16 v16, v3

    .line 34
    .line 35
    iget-object v3, v0, LqN0/d;->n:LEN0/f;

    .line 36
    .line 37
    move-object/from16 v17, p4

    .line 38
    .line 39
    move-object/from16 v18, v5

    .line 40
    .line 41
    move-object/from16 v19, v6

    .line 42
    .line 43
    move-object/from16 v5, p1

    .line 44
    .line 45
    move-wide/from16 v6, p2

    .line 46
    .line 47
    invoke-interface/range {v1 .. v19}, LqN0/c$a;->a(LQW0/c;LEN0/f;Lf8/g;Ljava/lang/String;JLHX0/e;LSX0/a;Li8/l;Lorg/xbet/ui_common/utils/M;Lorg/xbet/ui_common/utils/internet/a;LnN0/a;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LDH0/a;LwX0/c;LkC0/a;Lc8/h;)LqN0/c;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    return-object v1
.end method
