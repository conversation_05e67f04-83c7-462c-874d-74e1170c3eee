.class public final synthetic LG91/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:Lkotlin/jvm/functions/Function1;

.field public final synthetic c:Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;


# direct methods
.method public synthetic constructor <init>(LB4/a;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LG91/b;->a:LB4/a;

    iput-object p2, p0, LG91/b;->b:Lkotlin/jvm/functions/Function1;

    iput-object p3, p0, LG91/b;->c:Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, LG91/b;->a:LB4/a;

    iget-object v1, p0, LG91/b;->b:Lkotlin/jvm/functions/Function1;

    iget-object v2, p0, LG91/b;->c:Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    check-cast p1, Lorg/xbet/uikit/components/chips/Chip;

    check-cast p2, Ljava/lang/Boolean;

    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p2

    invoke-static {v0, v1, v2, p1, p2}, LG91/c;->a(LB4/a;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;Lorg/xbet/uikit/components/chips/Chip;Z)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
