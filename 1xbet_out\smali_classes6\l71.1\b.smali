.class public final Ll71/b;
.super Ljava/lang/Object;


# static fields
.field public static app_widget_favorites:I = 0x7f0d007d

.field public static app_widget_quick_available:I = 0x7f0d007e

.field public static app_widget_top_live:I = 0x7f0d007f

.field public static fragment_quick_available:I = 0x7f0d044d

.field public static header_category_item:I = 0x7f0d0523

.field public static item_quick_grid_part:I = 0x7f0d0682

.field public static widget_item_favorites_empty:I = 0x7f0d0bbd

.field public static widget_item_game:I = 0x7f0d0bbe

.field public static widget_item_game_doubles:I = 0x7f0d0bbf

.field public static widget_item_game_loading:I = 0x7f0d0bc0

.field public static widget_item_game_one_team:I = 0x7f0d0bc1

.field public static widget_item_game_preview:I = 0x7f0d0bc2

.field public static widget_item_game_quarter:I = 0x7f0d0bc3

.field public static widget_item_notification:I = 0x7f0d0bc4

.field public static widget_item_recomend_title:I = 0x7f0d0bc5

.field public static widget_scaled_small_item_game:I = 0x7f0d0bc6

.field public static widget_scaled_small_item_game_doubles:I = 0x7f0d0bc7

.field public static widget_scaled_small_item_game_quarter:I = 0x7f0d0bc8

.field public static widget_sections_item:I = 0x7f0d0bc9

.field public static widget_small_item_game:I = 0x7f0d0bca

.field public static widget_small_item_game_doubles:I = 0x7f0d0bcb

.field public static widget_small_item_game_quarter:I = 0x7f0d0bcc


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
