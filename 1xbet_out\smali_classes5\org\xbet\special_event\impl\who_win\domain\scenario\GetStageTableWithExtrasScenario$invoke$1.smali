.class final Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/o;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.who_win.domain.scenario.GetStageTableWithExtrasScenario$invoke$1"
    f = "GetStageTableWithExtrasScenario.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->d()Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/o<",
        "LKo0/a<",
        "Ljava/util/List<",
        "+",
        "LDy0/a;",
        ">;>;",
        "Ljava/util/List<",
        "+",
        "LDP/a;",
        ">;",
        "Ljava/util/List<",
        "+",
        "LWn/a;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "LKo0/a<",
        "Ljava/util/List<",
        "+",
        "LDy0/a;",
        ">;>;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0010\u0008\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u00002\u0012\u0010\u0003\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u00002\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00012\u000c\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u0001H\n\u00a2\u0006\u0004\u0008\u0008\u0010\t"
    }
    d2 = {
        "LKo0/a;",
        "",
        "LDy0/a;",
        "stageTableResult",
        "LDP/a;",
        "trackedEvents",
        "LWn/a;",
        "addedToCouponEvents",
        "<anonymous>",
        "(LKo0/a;Ljava/util/List;Ljava/util/List;)LKo0/a;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field synthetic L$2:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;->this$0:Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;

    const/4 p1, 0x4

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final invoke(LKo0/a;Ljava/util/List;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LKo0/a<",
            "Ljava/util/List<",
            "LDy0/a;",
            ">;>;",
            "Ljava/util/List<",
            "LDP/a;",
            ">;",
            "Ljava/util/List<",
            "LWn/a;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "LKo0/a<",
            "Ljava/util/List<",
            "LDy0/a;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;->this$0:Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;

    invoke-direct {v0, v1, p4}, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;-><init>(Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;->L$1:Ljava/lang/Object;

    iput-object p3, v0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;->L$2:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, LKo0/a;

    check-cast p2, Ljava/util/List;

    check-cast p3, Ljava/util/List;

    check-cast p4, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;->invoke(LKo0/a;Ljava/util/List;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_3

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, LKo0/a;

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, Ljava/util/List;

    .line 18
    .line 19
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;->L$2:Ljava/lang/Object;

    .line 20
    .line 21
    check-cast v1, Ljava/util/List;

    .line 22
    .line 23
    instance-of v2, p1, LKo0/a$a;

    .line 24
    .line 25
    if-eqz v2, :cond_0

    .line 26
    .line 27
    return-object p1

    .line 28
    :cond_0
    instance-of v2, p1, LKo0/a$b;

    .line 29
    .line 30
    if-eqz v2, :cond_2

    .line 31
    .line 32
    check-cast p1, LKo0/a$b;

    .line 33
    .line 34
    invoke-virtual {p1}, LKo0/a$b;->b()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    check-cast v2, Ljava/lang/Iterable;

    .line 39
    .line 40
    iget-object v3, p0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;->this$0:Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;

    .line 41
    .line 42
    new-instance v4, Ljava/util/ArrayList;

    .line 43
    .line 44
    const/16 v5, 0xa

    .line 45
    .line 46
    invoke-static {v2, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 47
    .line 48
    .line 49
    move-result v5

    .line 50
    invoke-direct {v4, v5}, Ljava/util/ArrayList;-><init>(I)V

    .line 51
    .line 52
    .line 53
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 54
    .line 55
    .line 56
    move-result-object v2

    .line 57
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 58
    .line 59
    .line 60
    move-result v5

    .line 61
    if-eqz v5, :cond_1

    .line 62
    .line 63
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object v5

    .line 67
    check-cast v5, LDy0/a;

    .line 68
    .line 69
    invoke-static {v3, v5, v1, v0}, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->a(Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;LDy0/a;Ljava/util/List;Ljava/util/List;)LDy0/a;

    .line 70
    .line 71
    .line 72
    move-result-object v5

    .line 73
    invoke-interface {v4, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 74
    .line 75
    .line 76
    goto :goto_0

    .line 77
    :cond_1
    invoke-virtual {p1, v4}, LKo0/a$b;->a(Ljava/lang/Object;)LKo0/a$b;

    .line 78
    .line 79
    .line 80
    move-result-object p1

    .line 81
    return-object p1

    .line 82
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 83
    .line 84
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 85
    .line 86
    .line 87
    throw p1

    .line 88
    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 89
    .line 90
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 91
    .line 92
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 93
    .line 94
    .line 95
    throw p1
.end method
