.class public final LOJ0/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LIJ0/g;",
        "LaZ0/g;",
        "a",
        "(LIJ0/g;)LaZ0/g;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LIJ0/g;)LaZ0/g;
    .locals 15
    .param p0    # LIJ0/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v2, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v4, Ljava/util/ArrayList;

    .line 7
    .line 8
    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LIJ0/g;->d()Ljava/util/List;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 20
    .line 21
    .line 22
    move-result v1

    .line 23
    const/16 v3, 0xa

    .line 24
    .line 25
    if-eqz v1, :cond_3

    .line 26
    .line 27
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    check-cast v1, LIJ0/h;

    .line 32
    .line 33
    invoke-static {v1}, LOJ0/f;->a(LIJ0/h;)LaZ0/f;

    .line 34
    .line 35
    .line 36
    move-result-object v5

    .line 37
    invoke-interface {v2, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 38
    .line 39
    .line 40
    invoke-virtual {v1}, LIJ0/h;->a()Ljava/util/List;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    new-instance v5, Ljava/util/ArrayList;

    .line 45
    .line 46
    invoke-static {v1, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 47
    .line 48
    .line 49
    move-result v3

    .line 50
    invoke-direct {v5, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 51
    .line 52
    .line 53
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    const/4 v3, 0x0

    .line 58
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 59
    .line 60
    .line 61
    move-result v6

    .line 62
    if-eqz v6, :cond_2

    .line 63
    .line 64
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object v6

    .line 68
    add-int/lit8 v7, v3, 0x1

    .line 69
    .line 70
    if-gez v3, :cond_0

    .line 71
    .line 72
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 73
    .line 74
    .line 75
    :cond_0
    move-object v9, v6

    .line 76
    check-cast v9, Ljava/lang/String;

    .line 77
    .line 78
    if-nez v3, :cond_1

    .line 79
    .line 80
    sget-object v3, Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;->START:Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;

    .line 81
    .line 82
    :goto_2
    move-object v11, v3

    .line 83
    goto :goto_3

    .line 84
    :cond_1
    sget-object v3, Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;->CENTER_HORIZONTAL:Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;

    .line 85
    .line 86
    goto :goto_2

    .line 87
    :goto_3
    new-instance v8, LaZ0/b;

    .line 88
    .line 89
    const/16 v13, 0xa

    .line 90
    .line 91
    const/4 v14, 0x0

    .line 92
    const/4 v10, 0x0

    .line 93
    const/4 v12, 0x0

    .line 94
    invoke-direct/range {v8 .. v14}, LaZ0/b;-><init>(Ljava/lang/String;Ljava/lang/String;Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;Ljava/util/List;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 95
    .line 96
    .line 97
    invoke-interface {v5, v8}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 98
    .line 99
    .line 100
    move v3, v7

    .line 101
    goto :goto_1

    .line 102
    :cond_2
    invoke-interface {v4, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 103
    .line 104
    .line 105
    goto :goto_0

    .line 106
    :cond_3
    invoke-virtual {p0}, LIJ0/g;->c()Ljava/util/List;

    .line 107
    .line 108
    .line 109
    move-result-object v0

    .line 110
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 111
    .line 112
    .line 113
    move-result-object v0

    .line 114
    check-cast v0, LIJ0/e;

    .line 115
    .line 116
    invoke-static {v0}, LOJ0/b;->a(LIJ0/e;)LaZ0/c;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    invoke-virtual {p0}, LIJ0/g;->c()Ljava/util/List;

    .line 121
    .line 122
    .line 123
    move-result-object p0

    .line 124
    const/4 v0, 0x1

    .line 125
    invoke-static {p0, v0}, Lkotlin/collections/CollectionsKt;->n0(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 126
    .line 127
    .line 128
    move-result-object p0

    .line 129
    const/16 v0, 0xa

    .line 130
    .line 131
    new-instance v3, Ljava/util/ArrayList;

    .line 132
    .line 133
    invoke-static {p0, v0}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 134
    .line 135
    .line 136
    move-result v0

    .line 137
    invoke-direct {v3, v0}, Ljava/util/ArrayList;-><init>(I)V

    .line 138
    .line 139
    .line 140
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 141
    .line 142
    .line 143
    move-result-object p0

    .line 144
    :goto_4
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 145
    .line 146
    .line 147
    move-result v0

    .line 148
    if-eqz v0, :cond_4

    .line 149
    .line 150
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 151
    .line 152
    .line 153
    move-result-object v0

    .line 154
    check-cast v0, LIJ0/e;

    .line 155
    .line 156
    invoke-static {v0}, LOJ0/a;->a(LIJ0/e;)LaZ0/a$c;

    .line 157
    .line 158
    .line 159
    move-result-object v0

    .line 160
    invoke-interface {v3, v0}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 161
    .line 162
    .line 163
    goto :goto_4

    .line 164
    :cond_4
    sget-object v5, Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/UiPanelBackgroundType;->ZEBRA:Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/UiPanelBackgroundType;

    .line 165
    .line 166
    new-instance v0, LaZ0/g;

    .line 167
    .line 168
    invoke-direct/range {v0 .. v5}, LaZ0/g;-><init>(LaZ0/c;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/UiPanelBackgroundType;)V

    .line 169
    .line 170
    .line 171
    return-object v0
.end method
