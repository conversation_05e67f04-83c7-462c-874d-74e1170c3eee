.class public final Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;
.super Landroid/widget/LinearLayout;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0007\u0018\u00002\u00020\u0001B\u001b\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0017\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u001d\u0010\u0010\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000f\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u0010\u0010\u0011JQ\u0010\u001c\u001a\u00020\n2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0014\u001a\u00020\u00082\u001a\u0008\u0002\u0010\u0018\u001a\u0014\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u00170\u00152\u0016\u0008\u0002\u0010\u001b\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u001a\u0012\u0004\u0012\u00020\u00170\u0019\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u0015\u0010 \u001a\u00020\n2\u0006\u0010\u001f\u001a\u00020\u001e\u00a2\u0006\u0004\u0008 \u0010!JS\u0010%\u001a\u00020\n2\u0006\u0010#\u001a\u00020\"2\u0006\u0010$\u001a\u00020\u00082\u001a\u0008\u0002\u0010\u0018\u001a\u0014\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u00170\u00152\u0016\u0008\u0002\u0010\u001b\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u001a\u0012\u0004\u0012\u00020\u00170\u0019H\u0002\u00a2\u0006\u0004\u0008%\u0010&R\u0014\u0010*\u001a\u00020\'8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010)\u00a8\u0006+"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;",
        "Landroid/widget/LinearLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "LL11/c$b;",
        "actionIcon",
        "",
        "setActionIcon",
        "(LL11/c$b;)V",
        "Lq31/c;",
        "model",
        "iconDrawable",
        "setModel",
        "(Lq31/c;LL11/c$b;)V",
        "Lq31/e;",
        "pictureType",
        "drawable",
        "Lkotlin/Function2;",
        "Landroid/graphics/drawable/Drawable;",
        "",
        "onLoaded",
        "Lkotlin/Function1;",
        "Lcom/bumptech/glide/load/engine/GlideException;",
        "onError",
        "setPictureByType",
        "(Lq31/e;LL11/c$b;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;)V",
        "",
        "label",
        "setLabel",
        "(Ljava/lang/String;)V",
        "Lq31/e$b;",
        "picture",
        "placeholder",
        "d",
        "(Lq31/e$b;LL11/c$b;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;)V",
        "LC31/d;",
        "a",
        "LC31/d;",
        "binding",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LC31/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0, p1, p2}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 3
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    invoke-static {p1, p0}, LC31/d;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/d;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->a:LC31/d;

    const/4 p1, 0x1

    .line 4
    invoke-virtual {p0, p1}, Landroid/widget/LinearLayout;->setOrientation(I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 1
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic a(Lcom/bumptech/glide/load/engine/GlideException;)Z
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->g(Lcom/bumptech/glide/load/engine/GlideException;)Z

    move-result p0

    return p0
.end method

.method public static synthetic b(Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;Lkotlin/jvm/functions/Function2;Landroid/graphics/drawable/Drawable;)Z
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->e(Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;Lkotlin/jvm/functions/Function2;Landroid/graphics/drawable/Drawable;)Z

    move-result p0

    return p0
.end method

.method public static synthetic c(Landroid/graphics/drawable/Drawable;Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;)Z
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->f(Landroid/graphics/drawable/Drawable;Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;)Z

    move-result p0

    return p0
.end method

.method public static final e(Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;Lkotlin/jvm/functions/Function2;Landroid/graphics/drawable/Drawable;)Z
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->a:LC31/d;

    .line 2
    .line 3
    iget-object v0, v0, LC31/d;->c:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    invoke-virtual {v0, v1}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 7
    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->a:LC31/d;

    .line 10
    .line 11
    iget-object v0, v0, LC31/d;->c:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 12
    .line 13
    invoke-virtual {v0, v1}, Landroid/view/View;->setBackgroundTintList(Landroid/content/res/ColorStateList;)V

    .line 14
    .line 15
    .line 16
    invoke-interface {p1, p2, p0}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object p0

    .line 20
    check-cast p0, Ljava/lang/Boolean;

    .line 21
    .line 22
    invoke-virtual {p0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 23
    .line 24
    .line 25
    move-result p0

    .line 26
    return p0
.end method

.method public static final f(Landroid/graphics/drawable/Drawable;Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;)Z
    .locals 0

    .line 1
    const/4 p0, 0x0

    return p0
.end method

.method public static final g(Lcom/bumptech/glide/load/engine/GlideException;)Z
    .locals 0

    .line 1
    const/4 p0, 0x0

    return p0
.end method

.method private final setActionIcon(LL11/c$b;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->a:LC31/d;

    .line 2
    .line 3
    iget-object v0, v0, LC31/d;->c:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 4
    .line 5
    invoke-virtual {p1}, LL11/c$b;->c()Landroid/graphics/drawable/Drawable;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {v0, p1}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 10
    .line 11
    .line 12
    iget-object p1, p0, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->a:LC31/d;

    .line 13
    .line 14
    iget-object p1, p1, LC31/d;->c:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 15
    .line 16
    const/4 v0, 0x0

    .line 17
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 18
    .line 19
    .line 20
    iget-object p1, p0, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->a:LC31/d;

    .line 21
    .line 22
    iget-object p1, p1, LC31/d;->c:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 23
    .line 24
    invoke-virtual {p1, v0}, Landroid/view/View;->setBackgroundTintList(Landroid/content/res/ColorStateList;)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public static synthetic setPictureByType$default(Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;Lq31/e;LL11/c$b;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p6, p5, 0x4

    .line 2
    .line 3
    if-eqz p6, :cond_0

    .line 4
    .line 5
    new-instance p3, Lp31/b;

    .line 6
    .line 7
    invoke-direct {p3}, Lp31/b;-><init>()V

    .line 8
    .line 9
    .line 10
    :cond_0
    and-int/lit8 p5, p5, 0x8

    .line 11
    .line 12
    if-eqz p5, :cond_1

    .line 13
    .line 14
    new-instance p4, Lp31/c;

    .line 15
    .line 16
    invoke-direct {p4}, Lp31/c;-><init>()V

    .line 17
    .line 18
    .line 19
    :cond_1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->setPictureByType(Lq31/e;LL11/c$b;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method


# virtual methods
.method public final d(Lq31/e$b;LL11/c$b;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lq31/e$b;",
            "LL11/c$b;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Landroid/graphics/drawable/Drawable;",
            "-",
            "Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;",
            "Ljava/lang/Boolean;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lcom/bumptech/glide/load/engine/GlideException;",
            "Ljava/lang/Boolean;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->a:LC31/d;

    .line 2
    .line 3
    iget-object v0, v0, LC31/d;->c:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    invoke-virtual {v0, v1}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 7
    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->a:LC31/d;

    .line 10
    .line 11
    iget-object v0, v0, LC31/d;->c:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 12
    .line 13
    invoke-virtual {p1}, Lq31/e$b;->a()LL11/c;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    new-instance v1, LL11/c$b;

    .line 18
    .line 19
    invoke-virtual {p2}, LL11/c$b;->c()Landroid/graphics/drawable/Drawable;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    invoke-direct {v1, p2}, LL11/c$b;-><init>(Landroid/graphics/drawable/Drawable;)V

    .line 24
    .line 25
    .line 26
    new-instance p2, Lp31/a;

    .line 27
    .line 28
    invoke-direct {p2, p0, p3}, Lp31/a;-><init>(Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;Lkotlin/jvm/functions/Function2;)V

    .line 29
    .line 30
    .line 31
    invoke-virtual {v0, p1, v1, p2, p4}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->G(LL11/c;LL11/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public final setLabel(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->a:LC31/d;

    .line 2
    .line 3
    iget-object v0, v0, LC31/d;->b:Landroid/widget/TextView;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setModel(Lq31/c;LL11/c$b;)V
    .locals 3
    .param p1    # Lq31/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LL11/c$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Lq31/c;->i()Lq31/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p1}, Lq31/c;->h()Lkotlin/jvm/functions/Function2;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {p1}, Lq31/c;->g()Lkotlin/jvm/functions/Function1;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-virtual {p0, v0, p2, v1, v2}, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->setPictureByType(Lq31/e;LL11/c$b;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p1}, Lq31/c;->f()Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->setLabel(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public final setPictureByType(Lq31/e;LL11/c$b;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .param p1    # Lq31/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LL11/c$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lq31/e;",
            "LL11/c$b;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Landroid/graphics/drawable/Drawable;",
            "-",
            "Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;",
            "Ljava/lang/Boolean;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lcom/bumptech/glide/load/engine/GlideException;",
            "Ljava/lang/Boolean;",
            ">;)V"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lq31/e$a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-direct {p0, p2}, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->setActionIcon(LL11/c$b;)V

    .line 6
    .line 7
    .line 8
    return-void

    .line 9
    :cond_0
    instance-of v0, p1, Lq31/e$b;

    .line 10
    .line 11
    if-eqz v0, :cond_1

    .line 12
    .line 13
    check-cast p1, Lq31/e$b;

    .line 14
    .line 15
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;->d(Lq31/e$b;LL11/c$b;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;)V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :cond_1
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 20
    .line 21
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 22
    .line 23
    .line 24
    throw p1
.end method
