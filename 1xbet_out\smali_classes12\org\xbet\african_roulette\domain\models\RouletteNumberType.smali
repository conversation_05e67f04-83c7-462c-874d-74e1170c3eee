.class public final enum Lorg/xbet/african_roulette/domain/models/RouletteNumberType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/african_roulette/domain/models/RouletteNumberType$a;,
        Lorg/xbet/african_roulette/domain/models/RouletteNumberType$b;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/african_roulette/domain/models/RouletteNumberType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\u0002\n\u0002\u0010\u0007\n\u0002\u0008\u0012\u0008\u0086\u0081\u0002\u0018\u0000 \u00072\u0008\u0012\u0004\u0012\u00020\u00000\u0001:\u0001\u0008B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\r\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0005\u0010\u0006j\u0002\u0008\tj\u0002\u0008\nj\u0002\u0008\u000bj\u0002\u0008\u000cj\u0002\u0008\rj\u0002\u0008\u000ej\u0002\u0008\u000fj\u0002\u0008\u0010j\u0002\u0008\u0011j\u0002\u0008\u0012j\u0002\u0008\u0013j\u0002\u0008\u0014j\u0002\u0008\u0015\u00a8\u0006\u0016"
    }
    d2 = {
        "Lorg/xbet/african_roulette/domain/models/RouletteNumberType;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "",
        "getDegree",
        "()F",
        "Companion",
        "a",
        "ZERO",
        "ONE",
        "TWO",
        "THREE",
        "FOUR",
        "FIVE",
        "SIX",
        "SEVEN",
        "EIGHT",
        "NINE",
        "TEN",
        "ELEVEN",
        "TWELVE",
        "african_roulette_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

.field public static final Companion:Lorg/xbet/african_roulette/domain/models/RouletteNumberType$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final enum EIGHT:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

.field public static final enum ELEVEN:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

.field public static final enum FIVE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

.field public static final enum FOUR:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

.field public static final enum NINE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

.field public static final enum ONE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

.field public static final enum SEVEN:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

.field public static final enum SIX:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

.field public static final enum TEN:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

.field public static final enum THREE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

.field public static final enum TWELVE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

.field public static final enum TWO:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

.field public static final enum ZERO:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 2
    .line 3
    const-string v1, "ZERO"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->ZERO:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 12
    .line 13
    const-string v1, "ONE"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->ONE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 20
    .line 21
    new-instance v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 22
    .line 23
    const-string v1, "TWO"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;-><init>(Ljava/lang/String;I)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->TWO:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 30
    .line 31
    new-instance v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 32
    .line 33
    const-string v1, "THREE"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;-><init>(Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->THREE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 42
    .line 43
    const-string v1, "FOUR"

    .line 44
    .line 45
    const/4 v2, 0x4

    .line 46
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;-><init>(Ljava/lang/String;I)V

    .line 47
    .line 48
    .line 49
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->FOUR:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 50
    .line 51
    new-instance v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 52
    .line 53
    const-string v1, "FIVE"

    .line 54
    .line 55
    const/4 v2, 0x5

    .line 56
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;-><init>(Ljava/lang/String;I)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->FIVE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 60
    .line 61
    new-instance v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 62
    .line 63
    const-string v1, "SIX"

    .line 64
    .line 65
    const/4 v2, 0x6

    .line 66
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;-><init>(Ljava/lang/String;I)V

    .line 67
    .line 68
    .line 69
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->SIX:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 70
    .line 71
    new-instance v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 72
    .line 73
    const-string v1, "SEVEN"

    .line 74
    .line 75
    const/4 v2, 0x7

    .line 76
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;-><init>(Ljava/lang/String;I)V

    .line 77
    .line 78
    .line 79
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->SEVEN:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 80
    .line 81
    new-instance v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 82
    .line 83
    const-string v1, "EIGHT"

    .line 84
    .line 85
    const/16 v2, 0x8

    .line 86
    .line 87
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;-><init>(Ljava/lang/String;I)V

    .line 88
    .line 89
    .line 90
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->EIGHT:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 91
    .line 92
    new-instance v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 93
    .line 94
    const-string v1, "NINE"

    .line 95
    .line 96
    const/16 v2, 0x9

    .line 97
    .line 98
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;-><init>(Ljava/lang/String;I)V

    .line 99
    .line 100
    .line 101
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->NINE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 102
    .line 103
    new-instance v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 104
    .line 105
    const-string v1, "TEN"

    .line 106
    .line 107
    const/16 v2, 0xa

    .line 108
    .line 109
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;-><init>(Ljava/lang/String;I)V

    .line 110
    .line 111
    .line 112
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->TEN:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 113
    .line 114
    new-instance v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 115
    .line 116
    const-string v1, "ELEVEN"

    .line 117
    .line 118
    const/16 v2, 0xb

    .line 119
    .line 120
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;-><init>(Ljava/lang/String;I)V

    .line 121
    .line 122
    .line 123
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->ELEVEN:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 124
    .line 125
    new-instance v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 126
    .line 127
    const-string v1, "TWELVE"

    .line 128
    .line 129
    const/16 v2, 0xc

    .line 130
    .line 131
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;-><init>(Ljava/lang/String;I)V

    .line 132
    .line 133
    .line 134
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->TWELVE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 135
    .line 136
    invoke-static {}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->a()[Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 137
    .line 138
    .line 139
    move-result-object v0

    .line 140
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->$VALUES:[Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 141
    .line 142
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 143
    .line 144
    .line 145
    move-result-object v0

    .line 146
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->$ENTRIES:Lkotlin/enums/a;

    .line 147
    .line 148
    new-instance v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType$a;

    .line 149
    .line 150
    const/4 v1, 0x0

    .line 151
    invoke-direct {v0, v1}, Lorg/xbet/african_roulette/domain/models/RouletteNumberType$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 152
    .line 153
    .line 154
    sput-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->Companion:Lorg/xbet/african_roulette/domain/models/RouletteNumberType$a;

    .line 155
    .line 156
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/african_roulette/domain/models/RouletteNumberType;
    .locals 3

    .line 1
    const/16 v0, 0xd

    new-array v0, v0, [Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    sget-object v1, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->ZERO:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->ONE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->TWO:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->THREE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->FOUR:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->FIVE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->SIX:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->SEVEN:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->EIGHT:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    const/16 v2, 0x8

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->NINE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    const/16 v2, 0x9

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->TEN:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    const/16 v2, 0xa

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->ELEVEN:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    const/16 v2, 0xb

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->TWELVE:Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    const/16 v2, 0xc

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/african_roulette/domain/models/RouletteNumberType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/african_roulette/domain/models/RouletteNumberType;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/african_roulette/domain/models/RouletteNumberType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType;->$VALUES:[Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/african_roulette/domain/models/RouletteNumberType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getDegree()F
    .locals 2

    .line 1
    sget-object v0, Lorg/xbet/african_roulette/domain/models/RouletteNumberType$b;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    packed-switch v0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw v0

    .line 18
    :pswitch_0
    const/high16 v0, 0x42c80000    # 100.0f

    .line 19
    .line 20
    return v0

    .line 21
    :pswitch_1
    const/high16 v0, 0x43520000    # 210.0f

    .line 22
    .line 23
    return v0

    .line 24
    :pswitch_2
    const/high16 v0, 0x42340000    # 45.0f

    .line 25
    .line 26
    return v0

    .line 27
    :pswitch_3
    const v0, 0x43a28000    # 325.0f

    .line 28
    .line 29
    .line 30
    return v0

    .line 31
    :pswitch_4
    const v0, 0x43b18000    # 355.0f

    .line 32
    .line 33
    .line 34
    return v0

    .line 35
    :pswitch_5
    const/high16 v0, 0x43870000    # 270.0f

    .line 36
    .line 37
    return v0

    .line 38
    :pswitch_6
    const/high16 v0, 0x43700000    # 240.0f

    .line 39
    .line 40
    return v0

    .line 41
    :pswitch_7
    const/high16 v0, 0x43020000    # 130.0f

    .line 42
    .line 43
    return v0

    .line 44
    :pswitch_8
    const/high16 v0, 0x43390000    # 185.0f

    .line 45
    .line 46
    return v0

    .line 47
    :pswitch_9
    const/high16 v0, 0x428c0000    # 70.0f

    .line 48
    .line 49
    return v0

    .line 50
    :pswitch_a
    const/high16 v0, 0x43960000    # 300.0f

    .line 51
    .line 52
    return v0

    .line 53
    :pswitch_b
    const/high16 v0, 0x41a00000    # 20.0f

    .line 54
    .line 55
    return v0

    .line 56
    :pswitch_c
    const/high16 v0, 0x431b0000    # 155.0f

    .line 57
    .line 58
    return v0

    .line 59
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
