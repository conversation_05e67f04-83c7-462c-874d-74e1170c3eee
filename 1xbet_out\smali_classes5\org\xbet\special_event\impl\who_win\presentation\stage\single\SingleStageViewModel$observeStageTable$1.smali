.class final Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.who_win.presentation.stage.single.SingleStageViewModel$observeStageTable$1"
    f = "SingleStageViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->t3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "LKo0/a<",
        "Ljava/util/List<",
        "+",
        "LDy0/a;",
        ">;>;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0012\u0010\u0003\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u0000H\n\u00a2\u0006\u0004\u0008\u0005\u0010\u0006"
    }
    d2 = {
        "LKo0/a;",
        "",
        "LDy0/a;",
        "result",
        "",
        "<anonymous>",
        "(LKo0/a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $type:Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/Class<",
            "+",
            "LDy0/a$b;",
            ">;"
        }
    .end annotation
.end field

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;Ljava/lang/Class;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;",
            "Ljava/lang/Class<",
            "+",
            "LDy0/a$b;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;

    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;->$type:Ljava/lang/Class;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;

    iget-object v2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;->$type:Ljava/lang/Class;

    invoke-direct {v0, v1, v2, p2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;Ljava/lang/Class;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public final invoke(LKo0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LKo0/a<",
            "Ljava/util/List<",
            "LDy0/a;",
            ">;>;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, LKo0/a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;->invoke(LKo0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_5

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, LKo0/a;

    .line 14
    .line 15
    instance-of v0, p1, LKo0/a$a;

    .line 16
    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;

    .line 20
    .line 21
    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->r3(Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;)Lkotlinx/coroutines/flow/V;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    move-object v1, p1

    .line 30
    check-cast v1, LVy0/a;

    .line 31
    .line 32
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object v3

    .line 40
    invoke-virtual {v1, v2, v3}, LVy0/a;->a(Ljava/util/List;Ljava/util/List;)LVy0/a;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    invoke-interface {v0, p1, v1}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 45
    .line 46
    .line 47
    move-result p1

    .line 48
    if-eqz p1, :cond_0

    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_1
    instance-of v0, p1, LKo0/a$b;

    .line 52
    .line 53
    if-eqz v0, :cond_4

    .line 54
    .line 55
    check-cast p1, LKo0/a$b;

    .line 56
    .line 57
    invoke-virtual {p1}, LKo0/a$b;->b()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    check-cast p1, Ljava/lang/Iterable;

    .line 62
    .line 63
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;->$type:Ljava/lang/Class;

    .line 64
    .line 65
    invoke-static {p1, v0}, Lkotlin/collections/C;->b0(Ljava/lang/Iterable;Ljava/lang/Class;)Ljava/util/List;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    check-cast p1, LDy0/a$b;

    .line 74
    .line 75
    if-eqz p1, :cond_3

    .line 76
    .line 77
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;

    .line 78
    .line 79
    invoke-static {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->r3(Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;)Lkotlinx/coroutines/flow/V;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    :cond_2
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    move-object v2, v1

    .line 88
    check-cast v2, LVy0/a;

    .line 89
    .line 90
    invoke-interface {p1}, LDy0/a$b;->b()Ljava/util/List;

    .line 91
    .line 92
    .line 93
    move-result-object v3

    .line 94
    invoke-interface {p1}, LDy0/a;->a()Ljava/util/List;

    .line 95
    .line 96
    .line 97
    move-result-object v4

    .line 98
    invoke-virtual {v2, v3, v4}, LVy0/a;->a(Ljava/util/List;Ljava/util/List;)LVy0/a;

    .line 99
    .line 100
    .line 101
    move-result-object v2

    .line 102
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 103
    .line 104
    .line 105
    move-result v1

    .line 106
    if-eqz v1, :cond_2

    .line 107
    .line 108
    :cond_3
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 109
    .line 110
    return-object p1

    .line 111
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 112
    .line 113
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 114
    .line 115
    .line 116
    throw p1

    .line 117
    :cond_5
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 118
    .line 119
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 120
    .line 121
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 122
    .line 123
    .line 124
    throw p1
.end method
