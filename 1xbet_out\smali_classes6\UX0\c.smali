.class public final synthetic LUX0/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LUX0/e;

.field public final synthetic b:Ljava/lang/Object;


# direct methods
.method public synthetic constructor <init>(LUX0/e;Ljava/lang/Object;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LUX0/c;->a:LUX0/e;

    iput-object p2, p0, LUX0/c;->b:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LUX0/c;->a:LUX0/e;

    iget-object v1, p0, LUX0/c;->b:Ljava/lang/Object;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, p1}, LUX0/e;->n(LUX0/e;Ljava/lang/Object;Landroid/view/View;)L<PERSON><PERSON>/Unit;

    move-result-object p1

    return-object p1
.end method
