.class public final synthetic LLX0/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:LLX0/f;


# direct methods
.method public synthetic constructor <init>(LLX0/f;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LLX0/e;->a:LLX0/f;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    .line 1
    iget-object v0, p0, LLX0/e;->a:LLX0/f;

    invoke-static {v0}, LLX0/f;->b(LLX0/f;)V

    return-void
.end method
