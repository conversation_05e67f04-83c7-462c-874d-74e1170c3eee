.class public final Lorg/xplatform/aggregator/impl/gifts/usecases/o;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/n;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lya1/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lya1/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/o;->a:LBc/a;

    .line 5
    .line 6
    return-void
.end method

.method public static a(LBc/a;)Lorg/xplatform/aggregator/impl/gifts/usecases/o;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lya1/a;",
            ">;)",
            "Lorg/xplatform/aggregator/impl/gifts/usecases/o;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/usecases/o;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/gifts/usecases/o;-><init>(LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(Lya1/a;)Lorg/xplatform/aggregator/impl/gifts/usecases/n;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/usecases/n;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/gifts/usecases/n;-><init>(Lya1/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xplatform/aggregator/impl/gifts/usecases/n;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/o;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lya1/a;

    .line 8
    .line 9
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/gifts/usecases/o;->c(Lya1/a;)Lorg/xplatform/aggregator/impl/gifts/usecases/n;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/usecases/o;->b()Lorg/xplatform/aggregator/impl/gifts/usecases/n;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
