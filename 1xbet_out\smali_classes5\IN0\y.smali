.class public final synthetic LIN0/y;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Landroidx/compose/ui/l;

.field public final synthetic b:I

.field public final synthetic c:Ljava/util/List;

.field public final synthetic d:Lkotlin/jvm/functions/Function1;

.field public final synthetic e:I

.field public final synthetic f:I


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/ui/l;ILjava/util/List;Lkotlin/jvm/functions/Function1;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LIN0/y;->a:Landroidx/compose/ui/l;

    iput p2, p0, LIN0/y;->b:I

    iput-object p3, p0, LIN0/y;->c:Ljava/util/List;

    iput-object p4, p0, LIN0/y;->d:<PERSON><PERSON><PERSON>/jvm/functions/Function1;

    iput p5, p0, LIN0/y;->e:I

    iput p6, p0, LIN0/y;->f:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    iget-object v0, p0, LIN0/y;->a:Landroidx/compose/ui/l;

    iget v1, p0, LIN0/y;->b:I

    iget-object v2, p0, LIN0/y;->c:Ljava/util/List;

    iget-object v3, p0, LIN0/y;->d:Lkotlin/jvm/functions/Function1;

    iget v4, p0, LIN0/y;->e:I

    iget v5, p0, LIN0/y;->f:I

    move-object v6, p1

    check-cast v6, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v7

    invoke-static/range {v0 .. v7}, LIN0/z;->c(Landroidx/compose/ui/l;ILjava/util/List;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
