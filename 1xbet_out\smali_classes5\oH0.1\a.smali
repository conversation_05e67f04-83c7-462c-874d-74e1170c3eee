.class public final LoH0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001f\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0000*\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "",
        "LlH0/a;",
        "LpH0/a;",
        "a",
        "(Ljava/util/List;)Ljava/util/List;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ljava/util/List;)Ljava/util/List;
    .locals 13
    .param p0    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LlH0/a;",
            ">;)",
            "Ljava/util/List<",
            "LpH0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    const/16 v1, 0xa

    .line 4
    .line 5
    invoke-static {p0, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    invoke-direct {v0, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 10
    .line 11
    .line 12
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v2

    .line 20
    if-eqz v2, :cond_4

    .line 21
    .line 22
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v2

    .line 26
    check-cast v2, LlH0/a;

    .line 27
    .line 28
    invoke-virtual {v2}, LlH0/a;->c()Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object v3

    .line 32
    invoke-virtual {v2}, LlH0/a;->a()LlH0/b;

    .line 33
    .line 34
    .line 35
    move-result-object v4

    .line 36
    invoke-virtual {v4}, LlH0/b;->b()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v4

    .line 40
    invoke-virtual {v2}, LlH0/a;->a()LlH0/b;

    .line 41
    .line 42
    .line 43
    move-result-object v5

    .line 44
    invoke-virtual {v5}, LlH0/b;->a()Ljava/util/List;

    .line 45
    .line 46
    .line 47
    move-result-object v5

    .line 48
    new-instance v6, Ljava/util/ArrayList;

    .line 49
    .line 50
    invoke-static {v5, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 51
    .line 52
    .line 53
    move-result v7

    .line 54
    invoke-direct {v6, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 55
    .line 56
    .line 57
    invoke-interface {v5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 58
    .line 59
    .line 60
    move-result-object v5

    .line 61
    :goto_1
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    .line 62
    .line 63
    .line 64
    move-result v7

    .line 65
    if-eqz v7, :cond_3

    .line 66
    .line 67
    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    move-result-object v7

    .line 71
    check-cast v7, Ljava/lang/String;

    .line 72
    .line 73
    invoke-static {}, Lorg/xbet/statistic/match_progress/match_progress_cricket/presentation/mappers/CricketImageBallEnum;->values()[Lorg/xbet/statistic/match_progress/match_progress_cricket/presentation/mappers/CricketImageBallEnum;

    .line 74
    .line 75
    .line 76
    move-result-object v8

    .line 77
    array-length v9, v8

    .line 78
    const/4 v10, 0x0

    .line 79
    :goto_2
    if-ge v10, v9, :cond_1

    .line 80
    .line 81
    aget-object v11, v8, v10

    .line 82
    .line 83
    invoke-virtual {v11}, Lorg/xbet/statistic/match_progress/match_progress_cricket/presentation/mappers/CricketImageBallEnum;->getValue()Ljava/lang/String;

    .line 84
    .line 85
    .line 86
    move-result-object v12

    .line 87
    invoke-static {v12, v7}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 88
    .line 89
    .line 90
    move-result v12

    .line 91
    if-eqz v12, :cond_0

    .line 92
    .line 93
    goto :goto_3

    .line 94
    :cond_0
    add-int/lit8 v10, v10, 0x1

    .line 95
    .line 96
    goto :goto_2

    .line 97
    :cond_1
    const/4 v11, 0x0

    .line 98
    :goto_3
    if-eqz v11, :cond_2

    .line 99
    .line 100
    new-instance v7, LpH0/b;

    .line 101
    .line 102
    invoke-virtual {v11}, Lorg/xbet/statistic/match_progress/match_progress_cricket/presentation/mappers/CricketImageBallEnum;->getIconRes()I

    .line 103
    .line 104
    .line 105
    move-result v8

    .line 106
    invoke-direct {v7, v8}, LpH0/b;-><init>(I)V

    .line 107
    .line 108
    .line 109
    goto :goto_4

    .line 110
    :cond_2
    new-instance v8, LpH0/c;

    .line 111
    .line 112
    invoke-direct {v8, v7}, LpH0/c;-><init>(Ljava/lang/String;)V

    .line 113
    .line 114
    .line 115
    move-object v7, v8

    .line 116
    :goto_4
    invoke-interface {v6, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 117
    .line 118
    .line 119
    goto :goto_1

    .line 120
    :cond_3
    new-instance v5, LpH0/d;

    .line 121
    .line 122
    invoke-direct {v5, v4, v6}, LpH0/d;-><init>(Ljava/lang/String;Ljava/util/List;)V

    .line 123
    .line 124
    .line 125
    new-instance v4, LpH0/e;

    .line 126
    .line 127
    invoke-virtual {v2}, LlH0/a;->b()LlH0/c;

    .line 128
    .line 129
    .line 130
    move-result-object v6

    .line 131
    invoke-virtual {v6}, LlH0/c;->b()Ljava/lang/String;

    .line 132
    .line 133
    .line 134
    move-result-object v6

    .line 135
    invoke-virtual {v2}, LlH0/a;->b()LlH0/c;

    .line 136
    .line 137
    .line 138
    move-result-object v2

    .line 139
    invoke-virtual {v2}, LlH0/c;->a()Ljava/lang/String;

    .line 140
    .line 141
    .line 142
    move-result-object v2

    .line 143
    invoke-direct {v4, v6, v2}, LpH0/e;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 144
    .line 145
    .line 146
    new-instance v2, LpH0/a;

    .line 147
    .line 148
    invoke-direct {v2, v3, v5, v4}, LpH0/a;-><init>(Ljava/lang/String;LpH0/d;LpH0/e;)V

    .line 149
    .line 150
    .line 151
    invoke-interface {v0, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 152
    .line 153
    .line 154
    goto/16 :goto_0

    .line 155
    .line 156
    :cond_4
    return-object v0
.end method
