.class public final LSG0/i;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0004\u001a;\u0010\t\u001a\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u00002\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007H\u0001\u00a2\u0006\u0004\u0008\t\u0010\n\u001a?\u0010\u0012\u001a\u00020\u00022\u000c\u0010\r\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000b2\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u000e2\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00020\u0000H\u0003\u00a2\u0006\u0004\u0008\u0012\u0010\u0013\u00a8\u0006\u0014"
    }
    d2 = {
        "Lkotlin/Function1;",
        "Lorg/xbet/statistic/main/common/presentation/viewmodel/g0;",
        "",
        "onClick",
        "LWG0/d;",
        "LSG0/j;",
        "gameEventUiModel",
        "Landroidx/compose/ui/l;",
        "modifier",
        "e",
        "(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V",
        "LHd/c;",
        "Ls31/a;",
        "dsGameEventAdapterListUiModel",
        "Lkotlin/Function0;",
        "onDetailClick",
        "",
        "onPlayerClick",
        "h",
        "(LHd/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;I)V",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, LSG0/i;->i(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p6}, LSG0/i;->g(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, LSG0/i;->f(Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LHd/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, LSG0/i;->j(LHd/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V
    .locals 10
    .param p0    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LWG0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/statistic/main/common/presentation/viewmodel/g0;",
            "Lkotlin/Unit;",
            ">;",
            "LWG0/d<",
            "LSG0/j;",
            ">;",
            "Landroidx/compose/ui/l;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    const v0, 0x51035ccd

    .line 2
    .line 3
    .line 4
    invoke-interface {p3, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 5
    .line 6
    .line 7
    move-result-object v7

    .line 8
    and-int/lit8 p3, p5, 0x1

    .line 9
    .line 10
    const/4 v1, 0x4

    .line 11
    if-eqz p3, :cond_0

    .line 12
    .line 13
    or-int/lit8 p3, p4, 0x6

    .line 14
    .line 15
    goto :goto_1

    .line 16
    :cond_0
    and-int/lit8 p3, p4, 0x6

    .line 17
    .line 18
    if-nez p3, :cond_2

    .line 19
    .line 20
    invoke-interface {v7, p0}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 21
    .line 22
    .line 23
    move-result p3

    .line 24
    if-eqz p3, :cond_1

    .line 25
    .line 26
    const/4 p3, 0x4

    .line 27
    goto :goto_0

    .line 28
    :cond_1
    const/4 p3, 0x2

    .line 29
    :goto_0
    or-int/2addr p3, p4

    .line 30
    goto :goto_1

    .line 31
    :cond_2
    move p3, p4

    .line 32
    :goto_1
    and-int/lit8 v2, p5, 0x2

    .line 33
    .line 34
    if-eqz v2, :cond_3

    .line 35
    .line 36
    or-int/lit8 p3, p3, 0x30

    .line 37
    .line 38
    goto :goto_3

    .line 39
    :cond_3
    and-int/lit8 v2, p4, 0x30

    .line 40
    .line 41
    if-nez v2, :cond_5

    .line 42
    .line 43
    invoke-interface {v7, p1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 44
    .line 45
    .line 46
    move-result v2

    .line 47
    if-eqz v2, :cond_4

    .line 48
    .line 49
    const/16 v2, 0x20

    .line 50
    .line 51
    goto :goto_2

    .line 52
    :cond_4
    const/16 v2, 0x10

    .line 53
    .line 54
    :goto_2
    or-int/2addr p3, v2

    .line 55
    :cond_5
    :goto_3
    and-int/lit8 v2, p5, 0x4

    .line 56
    .line 57
    if-eqz v2, :cond_6

    .line 58
    .line 59
    or-int/lit16 p3, p3, 0x180

    .line 60
    .line 61
    goto :goto_5

    .line 62
    :cond_6
    and-int/lit16 v3, p4, 0x180

    .line 63
    .line 64
    if-nez v3, :cond_8

    .line 65
    .line 66
    invoke-interface {v7, p2}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 67
    .line 68
    .line 69
    move-result v3

    .line 70
    if-eqz v3, :cond_7

    .line 71
    .line 72
    const/16 v3, 0x100

    .line 73
    .line 74
    goto :goto_4

    .line 75
    :cond_7
    const/16 v3, 0x80

    .line 76
    .line 77
    :goto_4
    or-int/2addr p3, v3

    .line 78
    :cond_8
    :goto_5
    and-int/lit16 v3, p3, 0x93

    .line 79
    .line 80
    const/16 v4, 0x92

    .line 81
    .line 82
    if-ne v3, v4, :cond_b

    .line 83
    .line 84
    invoke-interface {v7}, Landroidx/compose/runtime/j;->c()Z

    .line 85
    .line 86
    .line 87
    move-result v3

    .line 88
    if-nez v3, :cond_9

    .line 89
    .line 90
    goto :goto_7

    .line 91
    :cond_9
    invoke-interface {v7}, Landroidx/compose/runtime/j;->n()V

    .line 92
    .line 93
    .line 94
    :cond_a
    :goto_6
    move-object v3, p2

    .line 95
    goto :goto_9

    .line 96
    :cond_b
    :goto_7
    if-eqz v2, :cond_c

    .line 97
    .line 98
    sget-object p2, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 99
    .line 100
    :cond_c
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 101
    .line 102
    .line 103
    move-result v2

    .line 104
    if-eqz v2, :cond_d

    .line 105
    .line 106
    const/4 v2, -0x1

    .line 107
    const-string v3, "org.xbet.statistic.main.common.presentation.game_event.GameEventComponent (GameEventComponent.kt:33)"

    .line 108
    .line 109
    invoke-static {v0, p3, v2, v3}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 110
    .line 111
    .line 112
    :cond_d
    const/4 v0, 0x4

    .line 113
    sget v1, Lpb/k;->statistic_info_events:I

    .line 114
    .line 115
    sget v2, LlZ0/h;->ic_glyph_info_circle_primary:I

    .line 116
    .line 117
    invoke-virtual {p1}, LWG0/d;->e()Z

    .line 118
    .line 119
    .line 120
    move-result v3

    .line 121
    const v4, 0x4c5de2

    .line 122
    .line 123
    .line 124
    invoke-interface {v7, v4}, Landroidx/compose/runtime/j;->t(I)V

    .line 125
    .line 126
    .line 127
    and-int/lit8 p3, p3, 0xe

    .line 128
    .line 129
    const/4 v4, 0x0

    .line 130
    const/4 v5, 0x1

    .line 131
    if-ne p3, v0, :cond_e

    .line 132
    .line 133
    const/4 p3, 0x1

    .line 134
    goto :goto_8

    .line 135
    :cond_e
    const/4 p3, 0x0

    .line 136
    :goto_8
    invoke-interface {v7}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 137
    .line 138
    .line 139
    move-result-object v0

    .line 140
    if-nez p3, :cond_f

    .line 141
    .line 142
    sget-object p3, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 143
    .line 144
    invoke-virtual {p3}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 145
    .line 146
    .line 147
    move-result-object p3

    .line 148
    if-ne v0, p3, :cond_10

    .line 149
    .line 150
    :cond_f
    new-instance v0, LSG0/a;

    .line 151
    .line 152
    invoke-direct {v0, p0}, LSG0/a;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 153
    .line 154
    .line 155
    invoke-interface {v7, v0}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 156
    .line 157
    .line 158
    :cond_10
    check-cast v0, Lkotlin/jvm/functions/Function0;

    .line 159
    .line 160
    invoke-interface {v7}, Landroidx/compose/runtime/j;->q()V

    .line 161
    .line 162
    .line 163
    const/4 p3, 0x3

    .line 164
    const/4 v6, 0x0

    .line 165
    invoke-static {p2, v6, v4, p3, v6}, Landroidx/compose/foundation/layout/SizeKt;->G(Landroidx/compose/ui/l;Landroidx/compose/ui/e;ZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 166
    .line 167
    .line 168
    move-result-object p3

    .line 169
    new-instance v4, LSG0/i$a;

    .line 170
    .line 171
    invoke-direct {v4, p1, p0}, LSG0/i$a;-><init>(LWG0/d;Lkotlin/jvm/functions/Function1;)V

    .line 172
    .line 173
    .line 174
    const/16 v6, 0x36

    .line 175
    .line 176
    const v8, 0x1519521f

    .line 177
    .line 178
    .line 179
    invoke-static {v8, v5, v4, v7, v6}, Landroidx/compose/runtime/internal/b;->d(IZLjava/lang/Object;Landroidx/compose/runtime/j;I)Landroidx/compose/runtime/internal/a;

    .line 180
    .line 181
    .line 182
    move-result-object v6

    .line 183
    const/high16 v8, 0x30000

    .line 184
    .line 185
    const/4 v9, 0x0

    .line 186
    move-object v5, p3

    .line 187
    move-object v4, v0

    .line 188
    invoke-static/range {v1 .. v9}, LIN0/v;->b(IIZLkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;LOc/n;Landroidx/compose/runtime/j;II)V

    .line 189
    .line 190
    .line 191
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 192
    .line 193
    .line 194
    move-result p3

    .line 195
    if-eqz p3, :cond_a

    .line 196
    .line 197
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 198
    .line 199
    .line 200
    goto :goto_6

    .line 201
    :goto_9
    invoke-interface {v7}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 202
    .line 203
    .line 204
    move-result-object p2

    .line 205
    if-eqz p2, :cond_11

    .line 206
    .line 207
    new-instance v0, LSG0/b;

    .line 208
    .line 209
    move-object v1, p0

    .line 210
    move-object v2, p1

    .line 211
    move v4, p4

    .line 212
    move v5, p5

    .line 213
    invoke-direct/range {v0 .. v5}, LSG0/b;-><init>(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;II)V

    .line 214
    .line 215
    .line 216
    invoke-interface {p2, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 217
    .line 218
    .line 219
    :cond_11
    return-void
.end method

.method public static final f(Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;->GAME_EVENT:Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a$a;->b(Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;)Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a$a;->a(Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;)Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a$a;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-interface {p0, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 6

    .line 1
    or-int/lit8 p3, p3, 0x1

    .line 2
    .line 3
    invoke-static {p3}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result v4

    .line 7
    move-object v0, p0

    .line 8
    move-object v1, p1

    .line 9
    move-object v2, p2

    .line 10
    move v5, p4

    .line 11
    move-object v3, p5

    .line 12
    invoke-static/range {v0 .. v5}, LSG0/i;->e(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final h(LHd/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;I)V
    .locals 24
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LHd/c<",
            "+",
            "Ls31/a;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "I)V"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v6, p1

    .line 4
    .line 5
    move-object/from16 v2, p2

    .line 6
    .line 7
    move/from16 v7, p4

    .line 8
    .line 9
    const v1, -0xc4bfd9a

    .line 10
    .line 11
    .line 12
    move-object/from16 v3, p3

    .line 13
    .line 14
    invoke-interface {v3, v1}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 15
    .line 16
    .line 17
    move-result-object v3

    .line 18
    and-int/lit8 v4, v7, 0x6

    .line 19
    .line 20
    if-nez v4, :cond_2

    .line 21
    .line 22
    and-int/lit8 v4, v7, 0x8

    .line 23
    .line 24
    if-nez v4, :cond_0

    .line 25
    .line 26
    invoke-interface {v3, v0}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 27
    .line 28
    .line 29
    move-result v4

    .line 30
    goto :goto_0

    .line 31
    :cond_0
    invoke-interface {v3, v0}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 32
    .line 33
    .line 34
    move-result v4

    .line 35
    :goto_0
    if-eqz v4, :cond_1

    .line 36
    .line 37
    const/4 v4, 0x4

    .line 38
    goto :goto_1

    .line 39
    :cond_1
    const/4 v4, 0x2

    .line 40
    :goto_1
    or-int/2addr v4, v7

    .line 41
    goto :goto_2

    .line 42
    :cond_2
    move v4, v7

    .line 43
    :goto_2
    and-int/lit8 v5, v7, 0x30

    .line 44
    .line 45
    const/16 v8, 0x20

    .line 46
    .line 47
    if-nez v5, :cond_4

    .line 48
    .line 49
    invoke-interface {v3, v6}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 50
    .line 51
    .line 52
    move-result v5

    .line 53
    if-eqz v5, :cond_3

    .line 54
    .line 55
    const/16 v5, 0x20

    .line 56
    .line 57
    goto :goto_3

    .line 58
    :cond_3
    const/16 v5, 0x10

    .line 59
    .line 60
    :goto_3
    or-int/2addr v4, v5

    .line 61
    :cond_4
    and-int/lit16 v5, v7, 0x180

    .line 62
    .line 63
    if-nez v5, :cond_6

    .line 64
    .line 65
    invoke-interface {v3, v2}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 66
    .line 67
    .line 68
    move-result v5

    .line 69
    if-eqz v5, :cond_5

    .line 70
    .line 71
    const/16 v5, 0x100

    .line 72
    .line 73
    goto :goto_4

    .line 74
    :cond_5
    const/16 v5, 0x80

    .line 75
    .line 76
    :goto_4
    or-int/2addr v4, v5

    .line 77
    :cond_6
    move v9, v4

    .line 78
    and-int/lit16 v4, v9, 0x93

    .line 79
    .line 80
    const/16 v5, 0x92

    .line 81
    .line 82
    if-ne v4, v5, :cond_8

    .line 83
    .line 84
    invoke-interface {v3}, Landroidx/compose/runtime/j;->c()Z

    .line 85
    .line 86
    .line 87
    move-result v4

    .line 88
    if-nez v4, :cond_7

    .line 89
    .line 90
    goto :goto_5

    .line 91
    :cond_7
    invoke-interface {v3}, Landroidx/compose/runtime/j;->n()V

    .line 92
    .line 93
    .line 94
    goto/16 :goto_8

    .line 95
    .line 96
    :cond_8
    :goto_5
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 97
    .line 98
    .line 99
    move-result v4

    .line 100
    if-eqz v4, :cond_9

    .line 101
    .line 102
    const/4 v4, -0x1

    .line 103
    const-string v5, "org.xbet.statistic.main.common.presentation.game_event.SuccessState (GameEventComponent.kt:105)"

    .line 104
    .line 105
    invoke-static {v1, v9, v4, v5}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 106
    .line 107
    .line 108
    :cond_9
    sget-object v11, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 109
    .line 110
    const/4 v1, 0x0

    .line 111
    const/4 v4, 0x0

    .line 112
    const/4 v5, 0x1

    .line 113
    invoke-static {v11, v1, v5, v4}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 114
    .line 115
    .line 116
    move-result-object v1

    .line 117
    sget-object v16, LA11/a;->a:LA11/a;

    .line 118
    .line 119
    invoke-virtual/range {v16 .. v16}, LA11/a;->z0()F

    .line 120
    .line 121
    .line 122
    move-result v4

    .line 123
    invoke-static {v1, v4}, Landroidx/compose/foundation/layout/SizeKt;->i(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 124
    .line 125
    .line 126
    move-result-object v1

    .line 127
    sget-object v4, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 128
    .line 129
    invoke-virtual {v4}, Landroidx/compose/foundation/layout/Arrangement;->h()Landroidx/compose/foundation/layout/Arrangement$m;

    .line 130
    .line 131
    .line 132
    move-result-object v4

    .line 133
    sget-object v10, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 134
    .line 135
    invoke-virtual {v10}, Landroidx/compose/ui/e$a;->k()Landroidx/compose/ui/e$b;

    .line 136
    .line 137
    .line 138
    move-result-object v10

    .line 139
    const/4 v12, 0x0

    .line 140
    invoke-static {v4, v10, v3, v12}, Landroidx/compose/foundation/layout/k;->a(Landroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 141
    .line 142
    .line 143
    move-result-object v4

    .line 144
    invoke-static {v3, v12}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 145
    .line 146
    .line 147
    move-result v10

    .line 148
    invoke-interface {v3}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 149
    .line 150
    .line 151
    move-result-object v13

    .line 152
    invoke-static {v3, v1}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 153
    .line 154
    .line 155
    move-result-object v1

    .line 156
    sget-object v14, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 157
    .line 158
    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 159
    .line 160
    .line 161
    move-result-object v15

    .line 162
    invoke-interface {v3}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 163
    .line 164
    .line 165
    move-result-object v17

    .line 166
    invoke-static/range {v17 .. v17}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 167
    .line 168
    .line 169
    move-result v17

    .line 170
    if-nez v17, :cond_a

    .line 171
    .line 172
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 173
    .line 174
    .line 175
    :cond_a
    invoke-interface {v3}, Landroidx/compose/runtime/j;->l()V

    .line 176
    .line 177
    .line 178
    invoke-interface {v3}, Landroidx/compose/runtime/j;->B()Z

    .line 179
    .line 180
    .line 181
    move-result v17

    .line 182
    if-eqz v17, :cond_b

    .line 183
    .line 184
    invoke-interface {v3, v15}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 185
    .line 186
    .line 187
    goto :goto_6

    .line 188
    :cond_b
    invoke-interface {v3}, Landroidx/compose/runtime/j;->h()V

    .line 189
    .line 190
    .line 191
    :goto_6
    invoke-static {v3}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 192
    .line 193
    .line 194
    move-result-object v15

    .line 195
    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 196
    .line 197
    .line 198
    move-result-object v5

    .line 199
    invoke-static {v15, v4, v5}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 200
    .line 201
    .line 202
    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 203
    .line 204
    .line 205
    move-result-object v4

    .line 206
    invoke-static {v15, v13, v4}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 207
    .line 208
    .line 209
    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 210
    .line 211
    .line 212
    move-result-object v4

    .line 213
    invoke-interface {v15}, Landroidx/compose/runtime/j;->B()Z

    .line 214
    .line 215
    .line 216
    move-result v5

    .line 217
    if-nez v5, :cond_c

    .line 218
    .line 219
    invoke-interface {v15}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 220
    .line 221
    .line 222
    move-result-object v5

    .line 223
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 224
    .line 225
    .line 226
    move-result-object v13

    .line 227
    invoke-static {v5, v13}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 228
    .line 229
    .line 230
    move-result v5

    .line 231
    if-nez v5, :cond_d

    .line 232
    .line 233
    :cond_c
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 234
    .line 235
    .line 236
    move-result-object v5

    .line 237
    invoke-interface {v15, v5}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 238
    .line 239
    .line 240
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 241
    .line 242
    .line 243
    move-result-object v5

    .line 244
    invoke-interface {v15, v5, v4}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 245
    .line 246
    .line 247
    :cond_d
    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 248
    .line 249
    .line 250
    move-result-object v4

    .line 251
    invoke-static {v15, v1, v4}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 252
    .line 253
    .line 254
    sget-object v10, Landroidx/compose/foundation/layout/n;->a:Landroidx/compose/foundation/layout/n;

    .line 255
    .line 256
    const/4 v14, 0x2

    .line 257
    const/4 v15, 0x0

    .line 258
    const/4 v1, 0x0

    .line 259
    const/high16 v12, 0x3f800000    # 1.0f

    .line 260
    .line 261
    const/4 v13, 0x0

    .line 262
    invoke-static/range {v10 .. v15}, Landroidx/compose/foundation/layout/l;->a(Landroidx/compose/foundation/layout/m;Landroidx/compose/ui/l;FZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 263
    .line 264
    .line 265
    move-result-object v17

    .line 266
    invoke-virtual/range {v16 .. v16}, LA11/a;->i1()F

    .line 267
    .line 268
    .line 269
    move-result v19

    .line 270
    invoke-virtual/range {v16 .. v16}, LA11/a;->l1()F

    .line 271
    .line 272
    .line 273
    move-result v18

    .line 274
    invoke-virtual/range {v16 .. v16}, LA11/a;->l1()F

    .line 275
    .line 276
    .line 277
    move-result v20

    .line 278
    const/16 v22, 0x8

    .line 279
    .line 280
    const/16 v23, 0x0

    .line 281
    .line 282
    const/16 v21, 0x0

    .line 283
    .line 284
    invoke-static/range {v17 .. v23}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 285
    .line 286
    .line 287
    move-result-object v4

    .line 288
    sget v5, Ls31/a;->a:I

    .line 289
    .line 290
    and-int/lit8 v10, v9, 0xe

    .line 291
    .line 292
    or-int/2addr v5, v10

    .line 293
    and-int/lit16 v10, v9, 0x380

    .line 294
    .line 295
    or-int/2addr v5, v10

    .line 296
    move-object v1, v4

    .line 297
    move v4, v5

    .line 298
    const/4 v10, 0x0

    .line 299
    const/4 v5, 0x0

    .line 300
    const/4 v10, 0x1

    .line 301
    const/4 v12, 0x0

    .line 302
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt;->c(LHd/c;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 303
    .line 304
    .line 305
    const v1, 0x4c5de2

    .line 306
    .line 307
    .line 308
    invoke-interface {v3, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 309
    .line 310
    .line 311
    and-int/lit8 v1, v9, 0x70

    .line 312
    .line 313
    if-ne v1, v8, :cond_e

    .line 314
    .line 315
    const/4 v5, 0x1

    .line 316
    goto :goto_7

    .line 317
    :cond_e
    const/4 v5, 0x0

    .line 318
    :goto_7
    invoke-interface {v3}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 319
    .line 320
    .line 321
    move-result-object v1

    .line 322
    if-nez v5, :cond_f

    .line 323
    .line 324
    sget-object v4, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 325
    .line 326
    invoke-virtual {v4}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 327
    .line 328
    .line 329
    move-result-object v4

    .line 330
    if-ne v1, v4, :cond_10

    .line 331
    .line 332
    :cond_f
    new-instance v1, LSG0/c;

    .line 333
    .line 334
    invoke-direct {v1, v6}, LSG0/c;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 335
    .line 336
    .line 337
    invoke-interface {v3, v1}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 338
    .line 339
    .line 340
    :cond_10
    check-cast v1, Lkotlin/jvm/functions/Function0;

    .line 341
    .line 342
    invoke-interface {v3}, Landroidx/compose/runtime/j;->q()V

    .line 343
    .line 344
    .line 345
    invoke-virtual/range {v16 .. v16}, LA11/a;->l1()F

    .line 346
    .line 347
    .line 348
    move-result v4

    .line 349
    invoke-virtual/range {v16 .. v16}, LA11/a;->l1()F

    .line 350
    .line 351
    .line 352
    move-result v5

    .line 353
    invoke-virtual/range {v16 .. v16}, LA11/a;->u1()F

    .line 354
    .line 355
    .line 356
    move-result v8

    .line 357
    invoke-virtual/range {v16 .. v16}, LA11/a;->l1()F

    .line 358
    .line 359
    .line 360
    move-result v9

    .line 361
    invoke-static {v11, v4, v8, v5, v9}, Landroidx/compose/foundation/layout/PaddingKt;->l(Landroidx/compose/ui/l;FFFF)Landroidx/compose/ui/l;

    .line 362
    .line 363
    .line 364
    move-result-object v4

    .line 365
    invoke-static {v1, v4, v3, v12, v12}, LQG0/b;->b(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 366
    .line 367
    .line 368
    invoke-interface {v3}, Landroidx/compose/runtime/j;->j()V

    .line 369
    .line 370
    .line 371
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 372
    .line 373
    .line 374
    move-result v1

    .line 375
    if-eqz v1, :cond_11

    .line 376
    .line 377
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 378
    .line 379
    .line 380
    :cond_11
    :goto_8
    invoke-interface {v3}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 381
    .line 382
    .line 383
    move-result-object v1

    .line 384
    if-eqz v1, :cond_12

    .line 385
    .line 386
    new-instance v3, LSG0/d;

    .line 387
    .line 388
    invoke-direct {v3, v0, v6, v2, v7}, LSG0/d;-><init>(LHd/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;I)V

    .line 389
    .line 390
    .line 391
    invoke-interface {v1, v3}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 392
    .line 393
    .line 394
    :cond_12
    return-void
.end method

.method public static final i(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final j(LHd/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    or-int/lit8 p3, p3, 0x1

    .line 2
    .line 3
    invoke-static {p3}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result p3

    .line 7
    invoke-static {p0, p1, p2, p4, p3}, LSG0/i;->h(LHd/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;I)V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method

.method public static final synthetic k(LHd/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;I)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, LSG0/i;->h(LHd/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method
