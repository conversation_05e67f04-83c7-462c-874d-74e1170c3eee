.class public final LmG0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001d\u0010\u0004\u001a\u0004\u0018\u00010\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "LhG0/a;",
        "LHX0/e;",
        "resourceManager",
        "LnG0/a;",
        "a",
        "(LhG0/a;LHX0/e;)LnG0/a;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LhG0/a;LHX0/e;)LnG0/a;
    .locals 16
    .param p0    # LhG0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p1

    .line 2
    .line 3
    invoke-virtual/range {p0 .. p0}, LhG0/a;->c()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    const/4 v3, 0x0

    .line 12
    packed-switch v2, :pswitch_data_0

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :pswitch_0
    const-string v2, "3"

    .line 17
    .line 18
    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 19
    .line 20
    .line 21
    move-result v1

    .line 22
    if-nez v1, :cond_0

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_0
    sget v1, Lpb/k;->statistic_horses_race_gelding:I

    .line 26
    .line 27
    new-array v2, v3, [Ljava/lang/Object;

    .line 28
    .line 29
    invoke-interface {v0, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    goto :goto_1

    .line 34
    :pswitch_1
    const-string v2, "2"

    .line 35
    .line 36
    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 37
    .line 38
    .line 39
    move-result v1

    .line 40
    if-nez v1, :cond_1

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_1
    sget v1, Lpb/k;->man:I

    .line 44
    .line 45
    new-array v2, v3, [Ljava/lang/Object;

    .line 46
    .line 47
    invoke-interface {v0, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    goto :goto_1

    .line 52
    :pswitch_2
    const-string v2, "1"

    .line 53
    .line 54
    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 55
    .line 56
    .line 57
    move-result v1

    .line 58
    if-nez v1, :cond_2

    .line 59
    .line 60
    :goto_0
    const-string v1, "-"

    .line 61
    .line 62
    goto :goto_1

    .line 63
    :cond_2
    sget v1, Lpb/k;->woman:I

    .line 64
    .line 65
    new-array v2, v3, [Ljava/lang/Object;

    .line 66
    .line 67
    invoke-interface {v0, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    :goto_1
    new-instance v2, LnG0/a;

    .line 72
    .line 73
    invoke-virtual/range {p0 .. p0}, LhG0/a;->d()Ljava/lang/String;

    .line 74
    .line 75
    .line 76
    move-result-object v4

    .line 77
    invoke-virtual/range {p0 .. p0}, LhG0/a;->f()Ljava/lang/String;

    .line 78
    .line 79
    .line 80
    move-result-object v5

    .line 81
    invoke-virtual/range {p0 .. p0}, LhG0/a;->i()Z

    .line 82
    .line 83
    .line 84
    move-result v6

    .line 85
    new-instance v7, LnG0/b;

    .line 86
    .line 87
    sget v8, Lpb/k;->statistic_horses_race_start_position:I

    .line 88
    .line 89
    new-array v9, v3, [Ljava/lang/Object;

    .line 90
    .line 91
    invoke-interface {v0, v8, v9}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 92
    .line 93
    .line 94
    move-result-object v8

    .line 95
    invoke-virtual/range {p0 .. p0}, LhG0/a;->h()Ljava/lang/String;

    .line 96
    .line 97
    .line 98
    move-result-object v9

    .line 99
    invoke-direct {v7, v8, v9}, LnG0/b;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 100
    .line 101
    .line 102
    new-instance v8, LnG0/b;

    .line 103
    .line 104
    sget v9, Lpb/k;->statistic_horses_race_jockey_and_trainer:I

    .line 105
    .line 106
    new-array v10, v3, [Ljava/lang/Object;

    .line 107
    .line 108
    invoke-interface {v0, v9, v10}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 109
    .line 110
    .line 111
    move-result-object v9

    .line 112
    invoke-virtual/range {p0 .. p0}, LhG0/a;->e()Ljava/lang/String;

    .line 113
    .line 114
    .line 115
    move-result-object v10

    .line 116
    invoke-direct {v8, v9, v10}, LnG0/b;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 117
    .line 118
    .line 119
    new-instance v9, LnG0/c;

    .line 120
    .line 121
    sget v10, Lpb/k;->statistic_horses_race_gender:I

    .line 122
    .line 123
    new-array v11, v3, [Ljava/lang/Object;

    .line 124
    .line 125
    invoke-interface {v0, v10, v11}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 126
    .line 127
    .line 128
    move-result-object v10

    .line 129
    invoke-direct {v9, v10, v1}, LnG0/c;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 130
    .line 131
    .line 132
    new-instance v1, LnG0/b;

    .line 133
    .line 134
    sget v10, Lpb/k;->statistic_horses_race_year_and_age:I

    .line 135
    .line 136
    new-array v11, v3, [Ljava/lang/Object;

    .line 137
    .line 138
    invoke-interface {v0, v10, v11}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 139
    .line 140
    .line 141
    move-result-object v10

    .line 142
    invoke-virtual/range {p0 .. p0}, LhG0/a;->k()Ljava/lang/String;

    .line 143
    .line 144
    .line 145
    move-result-object v11

    .line 146
    invoke-direct {v1, v10, v11}, LnG0/b;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 147
    .line 148
    .line 149
    new-instance v10, LnG0/b;

    .line 150
    .line 151
    sget v11, Lpb/k;->statistic_horses_race_jockey_weight:I

    .line 152
    .line 153
    new-array v12, v3, [Ljava/lang/Object;

    .line 154
    .line 155
    invoke-interface {v0, v11, v12}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 156
    .line 157
    .line 158
    move-result-object v11

    .line 159
    invoke-virtual/range {p0 .. p0}, LhG0/a;->j()Ljava/lang/String;

    .line 160
    .line 161
    .line 162
    move-result-object v12

    .line 163
    invoke-direct {v10, v11, v12}, LnG0/b;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 164
    .line 165
    .line 166
    new-instance v11, LnG0/b;

    .line 167
    .line 168
    sget v12, Lpb/k;->statistic_horses_race_dam_and_sire:I

    .line 169
    .line 170
    new-array v13, v3, [Ljava/lang/Object;

    .line 171
    .line 172
    invoke-interface {v0, v12, v13}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 173
    .line 174
    .line 175
    move-result-object v12

    .line 176
    invoke-virtual/range {p0 .. p0}, LhG0/a;->a()Ljava/lang/String;

    .line 177
    .line 178
    .line 179
    move-result-object v13

    .line 180
    invoke-direct {v11, v12, v13}, LnG0/b;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 181
    .line 182
    .line 183
    new-instance v12, LnG0/b;

    .line 184
    .line 185
    sget v13, Lpb/k;->statistic_horses_race_distance:I

    .line 186
    .line 187
    new-array v14, v3, [Ljava/lang/Object;

    .line 188
    .line 189
    invoke-interface {v0, v13, v14}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 190
    .line 191
    .line 192
    move-result-object v13

    .line 193
    invoke-virtual/range {p0 .. p0}, LhG0/a;->b()Ljava/lang/String;

    .line 194
    .line 195
    .line 196
    move-result-object v14

    .line 197
    invoke-direct {v12, v13, v14}, LnG0/b;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 198
    .line 199
    .line 200
    new-instance v13, LnG0/b;

    .line 201
    .line 202
    sget v14, Lpb/k;->statistic_horses_race_place:I

    .line 203
    .line 204
    new-array v15, v3, [Ljava/lang/Object;

    .line 205
    .line 206
    invoke-interface {v0, v14, v15}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 207
    .line 208
    .line 209
    move-result-object v0

    .line 210
    invoke-virtual/range {p0 .. p0}, LhG0/a;->g()Ljava/lang/String;

    .line 211
    .line 212
    .line 213
    move-result-object v14

    .line 214
    invoke-direct {v13, v0, v14}, LnG0/b;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 215
    .line 216
    .line 217
    const/16 v0, 0x8

    .line 218
    .line 219
    new-array v0, v0, [LnG0/d;

    .line 220
    .line 221
    aput-object v7, v0, v3

    .line 222
    .line 223
    const/4 v3, 0x1

    .line 224
    aput-object v8, v0, v3

    .line 225
    .line 226
    const/4 v3, 0x2

    .line 227
    aput-object v9, v0, v3

    .line 228
    .line 229
    const/4 v3, 0x3

    .line 230
    aput-object v1, v0, v3

    .line 231
    .line 232
    const/4 v1, 0x4

    .line 233
    aput-object v10, v0, v1

    .line 234
    .line 235
    const/4 v1, 0x5

    .line 236
    aput-object v11, v0, v1

    .line 237
    .line 238
    const/4 v1, 0x6

    .line 239
    aput-object v12, v0, v1

    .line 240
    .line 241
    const/4 v1, 0x7

    .line 242
    aput-object v13, v0, v1

    .line 243
    .line 244
    invoke-static {v0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 245
    .line 246
    .line 247
    move-result-object v0

    .line 248
    invoke-direct {v2, v4, v5, v6, v0}, LnG0/a;-><init>(Ljava/lang/String;Ljava/lang/String;ZLjava/util/List;)V

    .line 249
    .line 250
    .line 251
    return-object v2

    .line 252
    nop

    .line 253
    :pswitch_data_0
    .packed-switch 0x31
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
