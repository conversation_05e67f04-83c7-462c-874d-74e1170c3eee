.class public final synthetic LUX0/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/recyclerview/widget/RecyclerView;

.field public final synthetic b:I


# direct methods
.method public synthetic constructor <init>(Landroidx/recyclerview/widget/RecyclerView;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LUX0/l;->a:Landroidx/recyclerview/widget/RecyclerView;

    iput p2, p0, LUX0/l;->b:I

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, LUX0/l;->a:Landroidx/recyclerview/widget/RecyclerView;

    iget v1, p0, LUX0/l;->b:I

    invoke-static {v0, v1}, LUX0/o;->b(Landroidx/recyclerview/widget/RecyclerView;I)V

    return-void
.end method
