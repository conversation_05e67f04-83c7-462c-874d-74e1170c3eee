.class public final synthetic Ll11/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Landroidx/compose/ui/l;

.field public final synthetic b:Ljava/lang/String;

.field public final synthetic c:Landroidx/compose/ui/graphics/v0;

.field public final synthetic d:I

.field public final synthetic e:I


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/ui/l;Ljava/lang/String;Landroidx/compose/ui/graphics/v0;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ll11/e;->a:Landroidx/compose/ui/l;

    iput-object p2, p0, Ll11/e;->b:Ljava/lang/String;

    iput-object p3, p0, Ll11/e;->c:Landroidx/compose/ui/graphics/v0;

    iput p4, p0, Ll11/e;->d:I

    iput p5, p0, Ll11/e;->e:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    iget-object v0, p0, Ll11/e;->a:Landroidx/compose/ui/l;

    iget-object v1, p0, Ll11/e;->b:Ljava/lang/String;

    iget-object v2, p0, Ll11/e;->c:Landroidx/compose/ui/graphics/v0;

    iget v3, p0, Ll11/e;->d:I

    iget v4, p0, Ll11/e;->e:I

    move-object v5, p1

    check-cast v5, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v6

    invoke-static/range {v0 .. v6}, Ll11/g;->a(Landroidx/compose/ui/l;Ljava/lang/String;Landroidx/compose/ui/graphics/v0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
