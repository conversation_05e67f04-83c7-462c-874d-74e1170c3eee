.class public final LNz0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LNz0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003R\u001d\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0006\u0010\u0007\u001a\u0004\u0008\u0008\u0010\t\u00a8\u0006\u000b"
    }
    d2 = {
        "LNz0/a$a;",
        "",
        "<init>",
        "()V",
        "Landroidx/recyclerview/widget/i$f;",
        "LNz0/a;",
        "b",
        "Landroidx/recyclerview/widget/i$f;",
        "a",
        "()Landroidx/recyclerview/widget/i$f;",
        "DIFF_CALLBACK",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final synthetic a:LNz0/a$a;

.field public static final b:Landroidx/recyclerview/widget/i$f;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/recyclerview/widget/i$f<",
            "LNz0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LNz0/a$a;

    .line 2
    .line 3
    invoke-direct {v0}, LNz0/a$a;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LNz0/a$a;->a:LNz0/a$a;

    .line 7
    .line 8
    new-instance v0, LNz0/a$a$a;

    .line 9
    .line 10
    invoke-direct {v0}, LNz0/a$a$a;-><init>()V

    .line 11
    .line 12
    .line 13
    sput-object v0, LNz0/a$a;->b:Landroidx/recyclerview/widget/i$f;

    .line 14
    .line 15
    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final a()Landroidx/recyclerview/widget/i$f;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Landroidx/recyclerview/widget/i$f<",
            "LNz0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, LNz0/a$a;->b:Landroidx/recyclerview/widget/i$f;

    .line 2
    .line 3
    return-object v0
.end method
