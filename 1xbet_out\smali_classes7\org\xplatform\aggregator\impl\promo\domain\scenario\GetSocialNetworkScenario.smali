.class public final Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J$\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\t0\u00062\u000c\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u0006H\u0086B\u00a2\u0006\u0004\u0008\n\u0010\u000bR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\n\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario;",
        "",
        "Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;",
        "localDataSource",
        "<init>",
        "(Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;)V",
        "",
        "Lorg/xbet/rules/api/domain/models/RuleModel;",
        "socials",
        "Lg81/i;",
        "a",
        "(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario;->a:Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/rules/api/domain/models/RuleModel;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "+",
            "Lg81/i;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario$invoke$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario$invoke$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario$invoke$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario$invoke$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario$invoke$1;-><init>(Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario$invoke$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario$invoke$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-object p1, v0, Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 39
    .line 40
    check-cast p1, Ljava/util/List;

    .line 41
    .line 42
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    goto :goto_1

    .line 46
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 47
    .line 48
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 49
    .line 50
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    throw p1

    .line 54
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario;->a:Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;

    .line 58
    .line 59
    iput-object p1, v0, Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 60
    .line 61
    iput v3, v0, Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario$invoke$1;->label:I

    .line 62
    .line 63
    invoke-virtual {p2, v0}, Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p2

    .line 67
    if-ne p2, v1, :cond_3

    .line 68
    .line 69
    return-object v1

    .line 70
    :cond_3
    :goto_1
    check-cast p2, Ljava/util/List;

    .line 71
    .line 72
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 73
    .line 74
    .line 75
    move-result v0

    .line 76
    if-nez v0, :cond_4

    .line 77
    .line 78
    return-object p2

    .line 79
    :cond_4
    new-instance p2, Ljava/util/ArrayList;

    .line 80
    .line 81
    invoke-direct {p2}, Ljava/util/ArrayList;-><init>()V

    .line 82
    .line 83
    .line 84
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    const/4 v1, 0x0

    .line 89
    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 90
    .line 91
    .line 92
    move-result v2

    .line 93
    if-eqz v2, :cond_a

    .line 94
    .line 95
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object v2

    .line 99
    add-int/lit8 v3, v1, 0x1

    .line 100
    .line 101
    if-gez v1, :cond_5

    .line 102
    .line 103
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 104
    .line 105
    .line 106
    :cond_5
    check-cast v2, Lorg/xbet/rules/api/domain/models/RuleModel;

    .line 107
    .line 108
    invoke-virtual {v2}, Lorg/xbet/rules/api/domain/models/RuleModel;->getHref()Lorg/xbet/rules/api/domain/models/HrefModel;

    .line 109
    .line 110
    .line 111
    move-result-object v4

    .line 112
    invoke-virtual {v4}, Lorg/xbet/rules/api/domain/models/HrefModel;->getLink()Ljava/lang/String;

    .line 113
    .line 114
    .line 115
    move-result-object v4

    .line 116
    invoke-static {v4}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    .line 117
    .line 118
    .line 119
    move-result v4

    .line 120
    const/4 v5, 0x0

    .line 121
    if-nez v4, :cond_8

    .line 122
    .line 123
    new-instance v4, Lg81/i$a;

    .line 124
    .line 125
    invoke-virtual {v2}, Lorg/xbet/rules/api/domain/models/RuleModel;->getHref()Lorg/xbet/rules/api/domain/models/HrefModel;

    .line 126
    .line 127
    .line 128
    move-result-object v6

    .line 129
    invoke-virtual {v6}, Lorg/xbet/rules/api/domain/models/HrefModel;->getLink()Ljava/lang/String;

    .line 130
    .line 131
    .line 132
    move-result-object v6

    .line 133
    add-int/lit8 v1, v1, -0x1

    .line 134
    .line 135
    invoke-static {p1, v1}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 136
    .line 137
    .line 138
    move-result-object v1

    .line 139
    check-cast v1, Lorg/xbet/rules/api/domain/models/RuleModel;

    .line 140
    .line 141
    if-eqz v1, :cond_6

    .line 142
    .line 143
    invoke-virtual {v1}, Lorg/xbet/rules/api/domain/models/RuleModel;->getRulePoint()Ljava/lang/String;

    .line 144
    .line 145
    .line 146
    move-result-object v5

    .line 147
    :cond_6
    if-nez v5, :cond_7

    .line 148
    .line 149
    const-string v5, ""

    .line 150
    .line 151
    :cond_7
    invoke-virtual {v2}, Lorg/xbet/rules/api/domain/models/RuleModel;->getHref()Lorg/xbet/rules/api/domain/models/HrefModel;

    .line 152
    .line 153
    .line 154
    move-result-object v1

    .line 155
    invoke-virtual {v1}, Lorg/xbet/rules/api/domain/models/HrefModel;->getImg()Ljava/lang/String;

    .line 156
    .line 157
    .line 158
    move-result-object v1

    .line 159
    invoke-direct {v4, v6, v5, v1}, Lg81/i$a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 160
    .line 161
    .line 162
    move-object v5, v4

    .line 163
    :cond_8
    if-eqz v5, :cond_9

    .line 164
    .line 165
    invoke-interface {p2, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 166
    .line 167
    .line 168
    :cond_9
    move v1, v3

    .line 169
    goto :goto_2

    .line 170
    :cond_a
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/promo/domain/scenario/GetSocialNetworkScenario;->a:Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;

    .line 171
    .line 172
    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;->b(Ljava/util/List;)V

    .line 173
    .line 174
    .line 175
    return-object p2
.end method
