.class public final LRw0/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LRw0/f;


# instance fields
.field public final a:Lorg/xbet/special_event/impl/tournament/presentation/x;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/tournament/presentation/x;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LRw0/g;->a:Lorg/xbet/special_event/impl/tournament/presentation/x;

    .line 5
    .line 6
    return-void
.end method

.method public static c(Lorg/xbet/special_event/impl/tournament/presentation/x;)Ldagger/internal/h;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/tournament/presentation/x;",
            ")",
            "Ldagger/internal/h<",
            "LRw0/f;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, LRw0/g;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LRw0/g;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/x;)V

    .line 4
    .line 5
    .line 6
    invoke-static {v0}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    return-object p0
.end method


# virtual methods
.method public bridge synthetic a(Landroidx/lifecycle/Q;)Landroidx/lifecycle/b0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LRw0/g;->b(Landroidx/lifecycle/Q;)Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public b(Landroidx/lifecycle/Q;)Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, LRw0/g;->a:Lorg/xbet/special_event/impl/tournament/presentation/x;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/x;->b(Landroidx/lifecycle/Q;)Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method
