.class public final synthetic LU01/m;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/views/LoadableImageView;

.field public final synthetic b:Ljava/lang/String;

.field public final synthetic c:Landroid/graphics/drawable/Drawable;

.field public final synthetic d:Lkotlin/jvm/functions/Function1;

.field public final synthetic e:Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/views/LoadableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LU01/m;->a:Lorg/xbet/uikit/components/views/LoadableImageView;

    iput-object p2, p0, LU01/m;->b:Ljava/lang/String;

    iput-object p3, p0, LU01/m;->c:Landroid/graphics/drawable/Drawable;

    iput-object p4, p0, LU01/m;->d:Lkotlin/jvm/functions/Function1;

    iput-object p5, p0, LU01/m;->e:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    iget-object v0, p0, LU01/m;->a:Lorg/xbet/uikit/components/views/LoadableImageView;

    iget-object v1, p0, LU01/m;->b:Ljava/lang/String;

    iget-object v2, p0, LU01/m;->c:Landroid/graphics/drawable/Drawable;

    iget-object v3, p0, LU01/m;->d:Lkotlin/jvm/functions/Function1;

    iget-object v4, p0, LU01/m;->e:Lkotlin/jvm/functions/Function1;

    move-object v5, p1

    check-cast v5, Lcom/bumptech/glide/load/engine/GlideException;

    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/components/views/LoadableImageView;->t(Lorg/xbet/uikit/components/views/LoadableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lcom/bumptech/glide/load/engine/GlideException;)Z

    move-result p1

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method
