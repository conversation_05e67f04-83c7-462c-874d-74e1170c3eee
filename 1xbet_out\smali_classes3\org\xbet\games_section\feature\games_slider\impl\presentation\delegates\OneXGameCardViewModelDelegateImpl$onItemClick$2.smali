.class final Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.games_slider.impl.presentation.delegates.OneXGameCardViewModelDelegateImpl$onItemClick$2"
    f = "OneXGameCardViewModelDelegateImpl.kt"
    l = {
        0x41,
        0x4e
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->J2(Ln41/m;Ljava/lang/String;Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $model:Ln41/m;

.field final synthetic $screenName:Ljava/lang/String;

.field final synthetic $screenType:Ljava/lang/String;

.field J$0:J

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;


# direct methods
.method public constructor <init>(Ln41/m;Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ln41/m;",
            "Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->$model:Ln41/m;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    .line 4
    .line 5
    iput-object p3, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->$screenName:Ljava/lang/String;

    .line 6
    .line 7
    iput-object p4, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->$screenType:Ljava/lang/String;

    .line 8
    .line 9
    const/4 p1, 0x2

    .line 10
    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;

    iget-object v1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->$model:Ln41/m;

    iget-object v2, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    iget-object v3, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->$screenName:Ljava/lang/String;

    iget-object v4, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->$screenType:Ljava/lang/String;

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;-><init>(Ln41/m;Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 12

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    move-object v9, p0

    .line 19
    goto/16 :goto_3

    .line 20
    .line 21
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 22
    .line 23
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 24
    .line 25
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 26
    .line 27
    .line 28
    throw p1

    .line 29
    :cond_1
    iget-wide v4, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->J$0:J

    .line 30
    .line 31
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    move-object v9, p0

    .line 35
    goto :goto_0

    .line 36
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 37
    .line 38
    .line 39
    iget-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->$model:Ln41/m;

    .line 40
    .line 41
    invoke-virtual {p1}, Ln41/m;->e()J

    .line 42
    .line 43
    .line 44
    move-result-wide v4

    .line 45
    iget-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    .line 46
    .line 47
    invoke-static {p1}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->t(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)Lw30/q;

    .line 48
    .line 49
    .line 50
    move-result-object v6

    .line 51
    iput-wide v4, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->J$0:J

    .line 52
    .line 53
    iput v3, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->label:I

    .line 54
    .line 55
    const/4 v7, 0x0

    .line 56
    const/4 v8, 0x0

    .line 57
    const/4 v10, 0x3

    .line 58
    const/4 v11, 0x0

    .line 59
    move-object v9, p0

    .line 60
    invoke-static/range {v6 .. v11}, Lw30/q$a;->a(Lw30/q;ZILkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    if-ne p1, v0, :cond_3

    .line 65
    .line 66
    goto :goto_2

    .line 67
    :cond_3
    :goto_0
    check-cast p1, Ljava/util/List;

    .line 68
    .line 69
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    :cond_4
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 74
    .line 75
    .line 76
    move-result v1

    .line 77
    if-eqz v1, :cond_5

    .line 78
    .line 79
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    move-object v6, v1

    .line 84
    check-cast v6, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;

    .line 85
    .line 86
    invoke-virtual {v6}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getId()J

    .line 87
    .line 88
    .line 89
    move-result-wide v6

    .line 90
    cmp-long v8, v6, v4

    .line 91
    .line 92
    if-nez v8, :cond_4

    .line 93
    .line 94
    goto :goto_1

    .line 95
    :cond_5
    const/4 v1, 0x0

    .line 96
    :goto_1
    check-cast v1, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;

    .line 97
    .line 98
    if-eqz v1, :cond_6

    .line 99
    .line 100
    invoke-virtual {v1}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getGameType()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    if-eqz p1, :cond_6

    .line 105
    .line 106
    invoke-static {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->c(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)Z

    .line 107
    .line 108
    .line 109
    move-result p1

    .line 110
    if-ne p1, v3, :cond_6

    .line 111
    .line 112
    if-eqz v1, :cond_6

    .line 113
    .line 114
    invoke-virtual {v1}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getUnderMaintenance()Z

    .line 115
    .line 116
    .line 117
    move-result p1

    .line 118
    if-nez p1, :cond_6

    .line 119
    .line 120
    iget-object p1, v9, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    .line 121
    .line 122
    invoke-static {p1}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->u(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)LDg/c;

    .line 123
    .line 124
    .line 125
    move-result-object p1

    .line 126
    sget-object v3, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->PopularNewTop:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 127
    .line 128
    invoke-virtual {p1, v4, v5, v3}, LDg/c;->p(JLorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;)V

    .line 129
    .line 130
    .line 131
    iget-object p1, v9, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    .line 132
    .line 133
    invoke-static {p1}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->v(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)LpS/b;

    .line 134
    .line 135
    .line 136
    move-result-object p1

    .line 137
    iget-object v3, v9, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->$screenName:Ljava/lang/String;

    .line 138
    .line 139
    long-to-int v6, v4

    .line 140
    sget-object v7, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_NEW_TOP:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 141
    .line 142
    invoke-interface {p1, v3, v6, v7}, LpS/b;->e(Ljava/lang/String;ILorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;)V

    .line 143
    .line 144
    .line 145
    iget-object p1, v9, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    .line 146
    .line 147
    iput v2, v9, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->label:I

    .line 148
    .line 149
    invoke-static {p1, v4, v5, v1, p0}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->F(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;JLcom/xbet/onexuser/domain/entity/onexgame/GpResult;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 150
    .line 151
    .line 152
    move-result-object p1

    .line 153
    if-ne p1, v0, :cond_8

    .line 154
    .line 155
    :goto_2
    return-object v0

    .line 156
    :cond_6
    if-eqz v1, :cond_7

    .line 157
    .line 158
    invoke-virtual {v1}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getGameType()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 159
    .line 160
    .line 161
    move-result-object p1

    .line 162
    if-eqz p1, :cond_7

    .line 163
    .line 164
    invoke-static {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->c(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)Z

    .line 165
    .line 166
    .line 167
    move-result p1

    .line 168
    if-ne p1, v3, :cond_7

    .line 169
    .line 170
    goto :goto_3

    .line 171
    :cond_7
    iget-object p1, v9, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    .line 172
    .line 173
    iget-object v0, v9, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->$screenName:Ljava/lang/String;

    .line 174
    .line 175
    iget-object v1, v9, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;->$screenType:Ljava/lang/String;

    .line 176
    .line 177
    invoke-virtual {p1, v0, v1}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->B(Ljava/lang/String;Ljava/lang/String;)V

    .line 178
    .line 179
    .line 180
    :cond_8
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 181
    .line 182
    return-object p1
.end method
