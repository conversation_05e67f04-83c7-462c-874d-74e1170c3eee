.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/u;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;LTZ0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;->b1:LTZ0/a;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;Lck/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;->k1:Lck/a;

    .line 2
    .line 3
    return-void
.end method

.method public static c(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;LzX0/k;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;->v1:LzX0/k;

    .line 2
    .line 3
    return-void
.end method

.method public static d(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;->o0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    return-void
.end method
