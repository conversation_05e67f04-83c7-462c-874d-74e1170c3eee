.class public final LN1/p;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LN1/p$b;
    }
.end annotation


# static fields
.field public static final a:[I

.field public static final b:[I

.field public static final c:[I

.field public static final d:[I

.field public static final e:[I

.field public static final f:[I

.field public static final g:[I

.field public static final h:[I

.field public static final i:[I


# direct methods
.method static constructor <clinit>()V
    .locals 10

    .line 1
    const/16 v0, 0x8

    .line 2
    .line 3
    const/4 v1, 0x2

    .line 4
    const/4 v2, 0x4

    .line 5
    const/4 v3, 0x5

    .line 6
    const/4 v4, 0x6

    .line 7
    const/16 v5, 0x10

    .line 8
    .line 9
    new-array v6, v5, [I

    .line 10
    .line 11
    fill-array-data v6, :array_0

    .line 12
    .line 13
    .line 14
    sput-object v6, LN1/p;->a:[I

    .line 15
    .line 16
    new-array v6, v5, [I

    .line 17
    .line 18
    fill-array-data v6, :array_1

    .line 19
    .line 20
    .line 21
    sput-object v6, LN1/p;->b:[I

    .line 22
    .line 23
    const/16 v6, 0x1d

    .line 24
    .line 25
    new-array v6, v6, [I

    .line 26
    .line 27
    fill-array-data v6, :array_2

    .line 28
    .line 29
    .line 30
    sput-object v6, LN1/p;->c:[I

    .line 31
    .line 32
    new-array v6, v5, [I

    .line 33
    .line 34
    fill-array-data v6, :array_3

    .line 35
    .line 36
    .line 37
    sput-object v6, LN1/p;->d:[I

    .line 38
    .line 39
    const/16 v6, 0xa

    .line 40
    .line 41
    const/16 v7, 0xc

    .line 42
    .line 43
    filled-new-array {v3, v0, v6, v7}, [I

    .line 44
    .line 45
    .line 46
    move-result-object v8

    .line 47
    sput-object v8, LN1/p;->e:[I

    .line 48
    .line 49
    const/16 v8, 0xf

    .line 50
    .line 51
    const/16 v9, 0x9

    .line 52
    .line 53
    filled-new-array {v4, v9, v7, v8}, [I

    .line 54
    .line 55
    .line 56
    move-result-object v8

    .line 57
    sput-object v8, LN1/p;->f:[I

    .line 58
    .line 59
    filled-new-array {v1, v2, v4, v0}, [I

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    sput-object v1, LN1/p;->g:[I

    .line 64
    .line 65
    const/16 v1, 0xd

    .line 66
    .line 67
    const/16 v2, 0xb

    .line 68
    .line 69
    filled-new-array {v9, v2, v1, v5}, [I

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    sput-object v1, LN1/p;->h:[I

    .line 74
    .line 75
    filled-new-array {v3, v0, v6, v7}, [I

    .line 76
    .line 77
    .line 78
    move-result-object v0

    .line 79
    sput-object v0, LN1/p;->i:[I

    .line 80
    .line 81
    return-void

    .line 82
    nop

    .line 83
    :array_0
    .array-data 4
        0x1
        0x2
        0x2
        0x2
        0x2
        0x3
        0x3
        0x4
        0x4
        0x5
        0x6
        0x6
        0x6
        0x7
        0x8
        0x8
    .end array-data

    .line 84
    .line 85
    .line 86
    .line 87
    .line 88
    .line 89
    .line 90
    .line 91
    .line 92
    .line 93
    .line 94
    .line 95
    .line 96
    .line 97
    .line 98
    .line 99
    .line 100
    .line 101
    .line 102
    .line 103
    .line 104
    .line 105
    .line 106
    .line 107
    .line 108
    .line 109
    .line 110
    .line 111
    .line 112
    .line 113
    .line 114
    .line 115
    .line 116
    .line 117
    .line 118
    .line 119
    :array_1
    .array-data 4
        -0x1
        0x1f40
        0x3e80
        0x7d00
        -0x1
        -0x1
        0x2b11
        0x5622
        0xac44
        -0x1
        -0x1
        0x2ee0
        0x5dc0
        0xbb80
        -0x1
        -0x1
    .end array-data

    .line 120
    .line 121
    .line 122
    .line 123
    .line 124
    .line 125
    .line 126
    .line 127
    .line 128
    .line 129
    .line 130
    .line 131
    .line 132
    .line 133
    .line 134
    .line 135
    .line 136
    .line 137
    .line 138
    .line 139
    .line 140
    .line 141
    .line 142
    .line 143
    .line 144
    .line 145
    .line 146
    .line 147
    .line 148
    .line 149
    .line 150
    .line 151
    .line 152
    .line 153
    .line 154
    .line 155
    :array_2
    .array-data 4
        0x40
        0x70
        0x80
        0xc0
        0xe0
        0x100
        0x180
        0x1c0
        0x200
        0x280
        0x300
        0x380
        0x400
        0x480
        0x500
        0x600
        0x780
        0x800
        0x900
        0xa00
        0xa80
        0xb00
        0xb07
        0xb80
        0xc00
        0xf00
        0x1000
        0x1800
        0x1e00
    .end array-data

    .line 156
    .line 157
    .line 158
    .line 159
    .line 160
    .line 161
    .line 162
    .line 163
    .line 164
    .line 165
    .line 166
    .line 167
    .line 168
    .line 169
    .line 170
    .line 171
    .line 172
    .line 173
    .line 174
    .line 175
    .line 176
    .line 177
    .line 178
    .line 179
    .line 180
    .line 181
    .line 182
    .line 183
    .line 184
    .line 185
    .line 186
    .line 187
    .line 188
    .line 189
    .line 190
    .line 191
    .line 192
    .line 193
    .line 194
    .line 195
    .line 196
    .line 197
    .line 198
    .line 199
    .line 200
    .line 201
    .line 202
    .line 203
    .line 204
    .line 205
    .line 206
    .line 207
    .line 208
    .line 209
    .line 210
    .line 211
    .line 212
    .line 213
    .line 214
    .line 215
    .line 216
    .line 217
    :array_3
    .array-data 4
        0x1f40
        0x3e80
        0x7d00
        0xfa00
        0x1f400
        0x5622
        0xac44
        0x15888
        0x2b110
        0x56220
        0x2ee0
        0x5dc0
        0xbb80
        0x17700
        0x2ee00
        0x5dc00
    .end array-data
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static a([BI)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    .line 1
    add-int/lit8 v0, p1, -0x2

    .line 2
    .line 3
    aget-byte v1, p0, v0

    .line 4
    .line 5
    shl-int/lit8 v1, v1, 0x8

    .line 6
    .line 7
    const v2, 0xffff

    .line 8
    .line 9
    .line 10
    and-int/2addr v1, v2

    .line 11
    add-int/lit8 p1, p1, -0x1

    .line 12
    .line 13
    aget-byte p1, p0, p1

    .line 14
    .line 15
    and-int/lit16 p1, p1, 0xff

    .line 16
    .line 17
    or-int/2addr p1, v1

    .line 18
    const/4 v1, 0x0

    .line 19
    invoke-static {p0, v1, v0, v2}, Lt1/a0;->v([BIII)I

    .line 20
    .line 21
    .line 22
    move-result p0

    .line 23
    if-ne p1, p0, :cond_0

    .line 24
    .line 25
    return-void

    .line 26
    :cond_0
    const-string p0, "CRC check failed"

    .line 27
    .line 28
    const/4 p1, 0x0

    .line 29
    invoke-static {p0, p1}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    throw p0
.end method

.method public static b([B)I
    .locals 7

    .line 1
    const/4 v0, 0x0

    .line 2
    aget-byte v1, p0, v0

    .line 3
    .line 4
    const/4 v2, -0x2

    .line 5
    const/4 v3, 0x7

    .line 6
    const/4 v4, 0x6

    .line 7
    const/4 v5, 0x1

    .line 8
    const/4 v6, 0x4

    .line 9
    if-eq v1, v2, :cond_2

    .line 10
    .line 11
    const/4 v2, -0x1

    .line 12
    if-eq v1, v2, :cond_1

    .line 13
    .line 14
    const/16 v2, 0x1f

    .line 15
    .line 16
    if-eq v1, v2, :cond_0

    .line 17
    .line 18
    const/4 v1, 0x5

    .line 19
    aget-byte v1, p0, v1

    .line 20
    .line 21
    and-int/lit8 v1, v1, 0x3

    .line 22
    .line 23
    shl-int/lit8 v1, v1, 0xc

    .line 24
    .line 25
    aget-byte v2, p0, v4

    .line 26
    .line 27
    and-int/lit16 v2, v2, 0xff

    .line 28
    .line 29
    shl-int/2addr v2, v6

    .line 30
    or-int/2addr v1, v2

    .line 31
    aget-byte p0, p0, v3

    .line 32
    .line 33
    :goto_0
    and-int/lit16 p0, p0, 0xf0

    .line 34
    .line 35
    shr-int/2addr p0, v6

    .line 36
    or-int/2addr p0, v1

    .line 37
    add-int/2addr p0, v5

    .line 38
    goto :goto_2

    .line 39
    :cond_0
    aget-byte v0, p0, v4

    .line 40
    .line 41
    and-int/lit8 v0, v0, 0x3

    .line 42
    .line 43
    shl-int/lit8 v0, v0, 0xc

    .line 44
    .line 45
    aget-byte v1, p0, v3

    .line 46
    .line 47
    and-int/lit16 v1, v1, 0xff

    .line 48
    .line 49
    shl-int/2addr v1, v6

    .line 50
    or-int/2addr v0, v1

    .line 51
    const/16 v1, 0x8

    .line 52
    .line 53
    aget-byte p0, p0, v1

    .line 54
    .line 55
    :goto_1
    and-int/lit8 p0, p0, 0x3c

    .line 56
    .line 57
    shr-int/lit8 p0, p0, 0x2

    .line 58
    .line 59
    or-int/2addr p0, v0

    .line 60
    add-int/2addr p0, v5

    .line 61
    const/4 v0, 0x1

    .line 62
    goto :goto_2

    .line 63
    :cond_1
    aget-byte v0, p0, v3

    .line 64
    .line 65
    and-int/lit8 v0, v0, 0x3

    .line 66
    .line 67
    shl-int/lit8 v0, v0, 0xc

    .line 68
    .line 69
    aget-byte v1, p0, v4

    .line 70
    .line 71
    and-int/lit16 v1, v1, 0xff

    .line 72
    .line 73
    shl-int/2addr v1, v6

    .line 74
    or-int/2addr v0, v1

    .line 75
    const/16 v1, 0x9

    .line 76
    .line 77
    aget-byte p0, p0, v1

    .line 78
    .line 79
    goto :goto_1

    .line 80
    :cond_2
    aget-byte v1, p0, v6

    .line 81
    .line 82
    and-int/lit8 v1, v1, 0x3

    .line 83
    .line 84
    shl-int/lit8 v1, v1, 0xc

    .line 85
    .line 86
    aget-byte v2, p0, v3

    .line 87
    .line 88
    and-int/lit16 v2, v2, 0xff

    .line 89
    .line 90
    shl-int/2addr v2, v6

    .line 91
    or-int/2addr v1, v2

    .line 92
    aget-byte p0, p0, v4

    .line 93
    .line 94
    goto :goto_0

    .line 95
    :goto_2
    if-eqz v0, :cond_3

    .line 96
    .line 97
    mul-int/lit8 p0, p0, 0x10

    .line 98
    .line 99
    div-int/lit8 p0, p0, 0xe

    .line 100
    .line 101
    :cond_3
    return p0
.end method

.method public static c(I)I
    .locals 1

    .line 1
    const v0, 0x7ffe8001

    .line 2
    .line 3
    .line 4
    if-eq p0, v0, :cond_7

    .line 5
    .line 6
    const v0, -0x180fe80

    .line 7
    .line 8
    .line 9
    if-eq p0, v0, :cond_7

    .line 10
    .line 11
    const v0, 0x1fffe800

    .line 12
    .line 13
    .line 14
    if-eq p0, v0, :cond_7

    .line 15
    .line 16
    const v0, -0xe0ff18

    .line 17
    .line 18
    .line 19
    if-ne p0, v0, :cond_0

    .line 20
    .line 21
    goto :goto_3

    .line 22
    :cond_0
    const v0, 0x64582025

    .line 23
    .line 24
    .line 25
    if-eq p0, v0, :cond_6

    .line 26
    .line 27
    const v0, 0x25205864

    .line 28
    .line 29
    .line 30
    if-ne p0, v0, :cond_1

    .line 31
    .line 32
    goto :goto_2

    .line 33
    :cond_1
    const v0, 0x40411bf2

    .line 34
    .line 35
    .line 36
    if-eq p0, v0, :cond_5

    .line 37
    .line 38
    const v0, -0xde4bec0

    .line 39
    .line 40
    .line 41
    if-ne p0, v0, :cond_2

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_2
    const v0, 0x71c442e8

    .line 45
    .line 46
    .line 47
    if-eq p0, v0, :cond_4

    .line 48
    .line 49
    const v0, -0x17bd3b8f

    .line 50
    .line 51
    .line 52
    if-ne p0, v0, :cond_3

    .line 53
    .line 54
    goto :goto_0

    .line 55
    :cond_3
    const/4 p0, 0x0

    .line 56
    return p0

    .line 57
    :cond_4
    :goto_0
    const/4 p0, 0x4

    .line 58
    return p0

    .line 59
    :cond_5
    :goto_1
    const/4 p0, 0x3

    .line 60
    return p0

    .line 61
    :cond_6
    :goto_2
    const/4 p0, 0x2

    .line 62
    return p0

    .line 63
    :cond_7
    :goto_3
    const/4 p0, 0x1

    .line 64
    return p0
.end method

.method public static d([B)Lt1/F;
    .locals 5

    .line 1
    const/4 v0, 0x0

    .line 2
    aget-byte v1, p0, v0

    .line 3
    .line 4
    const/16 v2, 0x7f

    .line 5
    .line 6
    if-eq v1, v2, :cond_3

    .line 7
    .line 8
    const/16 v2, 0x64

    .line 9
    .line 10
    if-eq v1, v2, :cond_3

    .line 11
    .line 12
    const/16 v2, 0x40

    .line 13
    .line 14
    if-eq v1, v2, :cond_3

    .line 15
    .line 16
    const/16 v2, 0x71

    .line 17
    .line 18
    if-ne v1, v2, :cond_0

    .line 19
    .line 20
    goto :goto_2

    .line 21
    :cond_0
    array-length v1, p0

    .line 22
    invoke-static {p0, v1}, Ljava/util/Arrays;->copyOf([BI)[B

    .line 23
    .line 24
    .line 25
    move-result-object p0

    .line 26
    invoke-static {p0}, LN1/p;->e([B)Z

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    if-eqz v1, :cond_1

    .line 31
    .line 32
    const/4 v1, 0x0

    .line 33
    :goto_0
    array-length v2, p0

    .line 34
    add-int/lit8 v2, v2, -0x1

    .line 35
    .line 36
    if-ge v1, v2, :cond_1

    .line 37
    .line 38
    aget-byte v2, p0, v1

    .line 39
    .line 40
    add-int/lit8 v3, v1, 0x1

    .line 41
    .line 42
    aget-byte v4, p0, v3

    .line 43
    .line 44
    aput-byte v4, p0, v1

    .line 45
    .line 46
    aput-byte v2, p0, v3

    .line 47
    .line 48
    add-int/lit8 v1, v1, 0x2

    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_1
    new-instance v1, Lt1/F;

    .line 52
    .line 53
    invoke-direct {v1, p0}, Lt1/F;-><init>([B)V

    .line 54
    .line 55
    .line 56
    aget-byte v0, p0, v0

    .line 57
    .line 58
    const/16 v2, 0x1f

    .line 59
    .line 60
    if-ne v0, v2, :cond_2

    .line 61
    .line 62
    new-instance v0, Lt1/F;

    .line 63
    .line 64
    invoke-direct {v0, p0}, Lt1/F;-><init>([B)V

    .line 65
    .line 66
    .line 67
    :goto_1
    invoke-virtual {v0}, Lt1/F;->b()I

    .line 68
    .line 69
    .line 70
    move-result v2

    .line 71
    const/16 v3, 0x10

    .line 72
    .line 73
    if-lt v2, v3, :cond_2

    .line 74
    .line 75
    const/4 v2, 0x2

    .line 76
    invoke-virtual {v0, v2}, Lt1/F;->r(I)V

    .line 77
    .line 78
    .line 79
    const/16 v2, 0xe

    .line 80
    .line 81
    invoke-virtual {v0, v2}, Lt1/F;->h(I)I

    .line 82
    .line 83
    .line 84
    move-result v3

    .line 85
    invoke-virtual {v1, v3, v2}, Lt1/F;->f(II)V

    .line 86
    .line 87
    .line 88
    goto :goto_1

    .line 89
    :cond_2
    invoke-virtual {v1, p0}, Lt1/F;->n([B)V

    .line 90
    .line 91
    .line 92
    return-object v1

    .line 93
    :cond_3
    :goto_2
    new-instance v0, Lt1/F;

    .line 94
    .line 95
    invoke-direct {v0, p0}, Lt1/F;-><init>([B)V

    .line 96
    .line 97
    .line 98
    return-object v0
.end method

.method public static e([B)Z
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    aget-byte p0, p0, v0

    .line 3
    .line 4
    const/4 v1, -0x2

    .line 5
    if-eq p0, v1, :cond_1

    .line 6
    .line 7
    const/4 v1, -0x1

    .line 8
    if-eq p0, v1, :cond_1

    .line 9
    .line 10
    const/16 v1, 0x25

    .line 11
    .line 12
    if-eq p0, v1, :cond_1

    .line 13
    .line 14
    const/16 v1, -0xe

    .line 15
    .line 16
    if-eq p0, v1, :cond_1

    .line 17
    .line 18
    const/16 v1, -0x18

    .line 19
    .line 20
    if-ne p0, v1, :cond_0

    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    return v0

    .line 24
    :cond_1
    :goto_0
    const/4 p0, 0x1

    .line 25
    return p0
.end method

.method public static f(Ljava/nio/ByteBuffer;)I
    .locals 3

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-virtual {p0, v0}, Ljava/nio/ByteBuffer;->getInt(I)I

    .line 3
    .line 4
    .line 5
    move-result v1

    .line 6
    const v2, -0xde4bec0

    .line 7
    .line 8
    .line 9
    if-eq v1, v2, :cond_5

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Ljava/nio/ByteBuffer;->getInt(I)I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    const v2, -0x17bd3b8f

    .line 16
    .line 17
    .line 18
    if-ne v1, v2, :cond_0

    .line 19
    .line 20
    goto :goto_4

    .line 21
    :cond_0
    invoke-virtual {p0, v0}, Ljava/nio/ByteBuffer;->getInt(I)I

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    const v1, 0x25205864

    .line 26
    .line 27
    .line 28
    if-ne v0, v1, :cond_1

    .line 29
    .line 30
    const/16 p0, 0x1000

    .line 31
    .line 32
    return p0

    .line 33
    :cond_1
    invoke-virtual {p0}, Ljava/nio/Buffer;->position()I

    .line 34
    .line 35
    .line 36
    move-result v0

    .line 37
    invoke-virtual {p0, v0}, Ljava/nio/ByteBuffer;->get(I)B

    .line 38
    .line 39
    .line 40
    move-result v1

    .line 41
    const/4 v2, -0x2

    .line 42
    if-eq v1, v2, :cond_4

    .line 43
    .line 44
    const/4 v2, -0x1

    .line 45
    if-eq v1, v2, :cond_3

    .line 46
    .line 47
    const/16 v2, 0x1f

    .line 48
    .line 49
    if-eq v1, v2, :cond_2

    .line 50
    .line 51
    add-int/lit8 v1, v0, 0x4

    .line 52
    .line 53
    invoke-virtual {p0, v1}, Ljava/nio/ByteBuffer;->get(I)B

    .line 54
    .line 55
    .line 56
    move-result v1

    .line 57
    and-int/lit8 v1, v1, 0x1

    .line 58
    .line 59
    shl-int/lit8 v1, v1, 0x6

    .line 60
    .line 61
    add-int/lit8 v0, v0, 0x5

    .line 62
    .line 63
    invoke-virtual {p0, v0}, Ljava/nio/ByteBuffer;->get(I)B

    .line 64
    .line 65
    .line 66
    move-result p0

    .line 67
    :goto_0
    and-int/lit16 p0, p0, 0xfc

    .line 68
    .line 69
    :goto_1
    shr-int/lit8 p0, p0, 0x2

    .line 70
    .line 71
    or-int/2addr p0, v1

    .line 72
    goto :goto_3

    .line 73
    :cond_2
    add-int/lit8 v1, v0, 0x5

    .line 74
    .line 75
    invoke-virtual {p0, v1}, Ljava/nio/ByteBuffer;->get(I)B

    .line 76
    .line 77
    .line 78
    move-result v1

    .line 79
    and-int/lit8 v1, v1, 0x7

    .line 80
    .line 81
    shl-int/lit8 v1, v1, 0x4

    .line 82
    .line 83
    add-int/lit8 v0, v0, 0x6

    .line 84
    .line 85
    invoke-virtual {p0, v0}, Ljava/nio/ByteBuffer;->get(I)B

    .line 86
    .line 87
    .line 88
    move-result p0

    .line 89
    :goto_2
    and-int/lit8 p0, p0, 0x3c

    .line 90
    .line 91
    goto :goto_1

    .line 92
    :cond_3
    add-int/lit8 v1, v0, 0x4

    .line 93
    .line 94
    invoke-virtual {p0, v1}, Ljava/nio/ByteBuffer;->get(I)B

    .line 95
    .line 96
    .line 97
    move-result v1

    .line 98
    and-int/lit8 v1, v1, 0x7

    .line 99
    .line 100
    shl-int/lit8 v1, v1, 0x4

    .line 101
    .line 102
    add-int/lit8 v0, v0, 0x7

    .line 103
    .line 104
    invoke-virtual {p0, v0}, Ljava/nio/ByteBuffer;->get(I)B

    .line 105
    .line 106
    .line 107
    move-result p0

    .line 108
    goto :goto_2

    .line 109
    :cond_4
    add-int/lit8 v1, v0, 0x5

    .line 110
    .line 111
    invoke-virtual {p0, v1}, Ljava/nio/ByteBuffer;->get(I)B

    .line 112
    .line 113
    .line 114
    move-result v1

    .line 115
    and-int/lit8 v1, v1, 0x1

    .line 116
    .line 117
    shl-int/lit8 v1, v1, 0x6

    .line 118
    .line 119
    add-int/lit8 v0, v0, 0x4

    .line 120
    .line 121
    invoke-virtual {p0, v0}, Ljava/nio/ByteBuffer;->get(I)B

    .line 122
    .line 123
    .line 124
    move-result p0

    .line 125
    goto :goto_0

    .line 126
    :goto_3
    add-int/lit8 p0, p0, 0x1

    .line 127
    .line 128
    mul-int/lit8 p0, p0, 0x20

    .line 129
    .line 130
    return p0

    .line 131
    :cond_5
    :goto_4
    const/16 p0, 0x400

    .line 132
    .line 133
    return p0
.end method

.method public static g([B)I
    .locals 6

    .line 1
    const/4 v0, 0x0

    .line 2
    aget-byte v0, p0, v0

    .line 3
    .line 4
    const/4 v1, -0x2

    .line 5
    const/4 v2, 0x5

    .line 6
    const/4 v3, 0x6

    .line 7
    const/4 v4, 0x4

    .line 8
    if-eq v0, v1, :cond_2

    .line 9
    .line 10
    const/4 v1, -0x1

    .line 11
    const/4 v5, 0x7

    .line 12
    if-eq v0, v1, :cond_1

    .line 13
    .line 14
    const/16 v1, 0x1f

    .line 15
    .line 16
    if-eq v0, v1, :cond_0

    .line 17
    .line 18
    aget-byte v0, p0, v4

    .line 19
    .line 20
    and-int/lit8 v0, v0, 0x1

    .line 21
    .line 22
    shl-int/2addr v0, v3

    .line 23
    aget-byte p0, p0, v2

    .line 24
    .line 25
    :goto_0
    and-int/lit16 p0, p0, 0xfc

    .line 26
    .line 27
    :goto_1
    shr-int/lit8 p0, p0, 0x2

    .line 28
    .line 29
    or-int/2addr p0, v0

    .line 30
    goto :goto_3

    .line 31
    :cond_0
    aget-byte v0, p0, v2

    .line 32
    .line 33
    and-int/2addr v0, v5

    .line 34
    shl-int/2addr v0, v4

    .line 35
    aget-byte p0, p0, v3

    .line 36
    .line 37
    :goto_2
    and-int/lit8 p0, p0, 0x3c

    .line 38
    .line 39
    goto :goto_1

    .line 40
    :cond_1
    aget-byte v0, p0, v4

    .line 41
    .line 42
    and-int/2addr v0, v5

    .line 43
    shl-int/2addr v0, v4

    .line 44
    aget-byte p0, p0, v5

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_2
    aget-byte v0, p0, v2

    .line 48
    .line 49
    and-int/lit8 v0, v0, 0x1

    .line 50
    .line 51
    shl-int/2addr v0, v3

    .line 52
    aget-byte p0, p0, v4

    .line 53
    .line 54
    goto :goto_0

    .line 55
    :goto_3
    add-int/lit8 p0, p0, 0x1

    .line 56
    .line 57
    mul-int/lit8 p0, p0, 0x20

    .line 58
    .line 59
    return p0
.end method

.method public static h([BLjava/lang/String;Ljava/lang/String;ILjava/lang/String;Landroidx/media3/common/DrmInitData;)Landroidx/media3/common/r;
    .locals 6

    .line 1
    invoke-static {p0}, LN1/p;->d([B)Lt1/F;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const/16 v0, 0x3c

    .line 6
    .line 7
    invoke-virtual {p0, v0}, Lt1/F;->r(I)V

    .line 8
    .line 9
    .line 10
    const/4 v0, 0x6

    .line 11
    invoke-virtual {p0, v0}, Lt1/F;->h(I)I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    sget-object v1, LN1/p;->a:[I

    .line 16
    .line 17
    aget v0, v1, v0

    .line 18
    .line 19
    const/4 v1, 0x4

    .line 20
    invoke-virtual {p0, v1}, Lt1/F;->h(I)I

    .line 21
    .line 22
    .line 23
    move-result v1

    .line 24
    sget-object v2, LN1/p;->b:[I

    .line 25
    .line 26
    aget v1, v2, v1

    .line 27
    .line 28
    const/4 v2, 0x5

    .line 29
    invoke-virtual {p0, v2}, Lt1/F;->h(I)I

    .line 30
    .line 31
    .line 32
    move-result v2

    .line 33
    sget-object v3, LN1/p;->c:[I

    .line 34
    .line 35
    array-length v4, v3

    .line 36
    const/4 v5, 0x2

    .line 37
    if-lt v2, v4, :cond_0

    .line 38
    .line 39
    const/4 v2, -0x1

    .line 40
    goto :goto_0

    .line 41
    :cond_0
    aget v2, v3, v2

    .line 42
    .line 43
    mul-int/lit16 v2, v2, 0x3e8

    .line 44
    .line 45
    div-int/2addr v2, v5

    .line 46
    :goto_0
    const/16 v3, 0xa

    .line 47
    .line 48
    invoke-virtual {p0, v3}, Lt1/F;->r(I)V

    .line 49
    .line 50
    .line 51
    invoke-virtual {p0, v5}, Lt1/F;->h(I)I

    .line 52
    .line 53
    .line 54
    move-result p0

    .line 55
    if-lez p0, :cond_1

    .line 56
    .line 57
    const/4 p0, 0x1

    .line 58
    goto :goto_1

    .line 59
    :cond_1
    const/4 p0, 0x0

    .line 60
    :goto_1
    add-int/2addr v0, p0

    .line 61
    new-instance p0, Landroidx/media3/common/r$b;

    .line 62
    .line 63
    invoke-direct {p0}, Landroidx/media3/common/r$b;-><init>()V

    .line 64
    .line 65
    .line 66
    invoke-virtual {p0, p1}, Landroidx/media3/common/r$b;->f0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 67
    .line 68
    .line 69
    move-result-object p0

    .line 70
    invoke-virtual {p0, p4}, Landroidx/media3/common/r$b;->U(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 71
    .line 72
    .line 73
    move-result-object p0

    .line 74
    const-string p1, "audio/vnd.dts"

    .line 75
    .line 76
    invoke-virtual {p0, p1}, Landroidx/media3/common/r$b;->u0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 77
    .line 78
    .line 79
    move-result-object p0

    .line 80
    invoke-virtual {p0, v2}, Landroidx/media3/common/r$b;->Q(I)Landroidx/media3/common/r$b;

    .line 81
    .line 82
    .line 83
    move-result-object p0

    .line 84
    invoke-virtual {p0, v0}, Landroidx/media3/common/r$b;->R(I)Landroidx/media3/common/r$b;

    .line 85
    .line 86
    .line 87
    move-result-object p0

    .line 88
    invoke-virtual {p0, v1}, Landroidx/media3/common/r$b;->v0(I)Landroidx/media3/common/r$b;

    .line 89
    .line 90
    .line 91
    move-result-object p0

    .line 92
    invoke-virtual {p0, p5}, Landroidx/media3/common/r$b;->Y(Landroidx/media3/common/DrmInitData;)Landroidx/media3/common/r$b;

    .line 93
    .line 94
    .line 95
    move-result-object p0

    .line 96
    invoke-virtual {p0, p2}, Landroidx/media3/common/r$b;->j0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 97
    .line 98
    .line 99
    move-result-object p0

    .line 100
    invoke-virtual {p0, p3}, Landroidx/media3/common/r$b;->s0(I)Landroidx/media3/common/r$b;

    .line 101
    .line 102
    .line 103
    move-result-object p0

    .line 104
    invoke-virtual {p0}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    .line 105
    .line 106
    .line 107
    move-result-object p0

    .line 108
    return-object p0
.end method

.method public static i([B)LN1/p$b;
    .locals 17
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    .line 1
    invoke-static/range {p0 .. p0}, LN1/p;->d([B)Lt1/F;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/16 v1, 0x28

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Lt1/F;->r(I)V

    .line 8
    .line 9
    .line 10
    const/4 v1, 0x2

    .line 11
    invoke-virtual {v0, v1}, Lt1/F;->h(I)I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    invoke-virtual {v0}, Lt1/F;->g()Z

    .line 16
    .line 17
    .line 18
    move-result v3

    .line 19
    const/16 v4, 0xc

    .line 20
    .line 21
    const/16 v5, 0x8

    .line 22
    .line 23
    if-nez v3, :cond_0

    .line 24
    .line 25
    const/16 v3, 0x10

    .line 26
    .line 27
    const/16 v6, 0x8

    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_0
    const/16 v3, 0x14

    .line 31
    .line 32
    const/16 v6, 0xc

    .line 33
    .line 34
    :goto_0
    invoke-virtual {v0, v6}, Lt1/F;->r(I)V

    .line 35
    .line 36
    .line 37
    invoke-virtual {v0, v3}, Lt1/F;->h(I)I

    .line 38
    .line 39
    .line 40
    move-result v6

    .line 41
    const/4 v7, 0x1

    .line 42
    add-int/lit8 v12, v6, 0x1

    .line 43
    .line 44
    invoke-virtual {v0}, Lt1/F;->g()Z

    .line 45
    .line 46
    .line 47
    move-result v6

    .line 48
    const/4 v8, -0x1

    .line 49
    const/4 v9, 0x0

    .line 50
    if-eqz v6, :cond_6

    .line 51
    .line 52
    invoke-virtual {v0, v1}, Lt1/F;->h(I)I

    .line 53
    .line 54
    .line 55
    move-result v10

    .line 56
    const/4 v11, 0x3

    .line 57
    invoke-virtual {v0, v11}, Lt1/F;->h(I)I

    .line 58
    .line 59
    .line 60
    move-result v13

    .line 61
    add-int/2addr v13, v7

    .line 62
    mul-int/lit16 v13, v13, 0x200

    .line 63
    .line 64
    invoke-virtual {v0}, Lt1/F;->g()Z

    .line 65
    .line 66
    .line 67
    move-result v14

    .line 68
    if-eqz v14, :cond_1

    .line 69
    .line 70
    const/16 v14, 0x24

    .line 71
    .line 72
    invoke-virtual {v0, v14}, Lt1/F;->r(I)V

    .line 73
    .line 74
    .line 75
    :cond_1
    invoke-virtual {v0, v11}, Lt1/F;->h(I)I

    .line 76
    .line 77
    .line 78
    move-result v14

    .line 79
    add-int/2addr v14, v7

    .line 80
    invoke-virtual {v0, v11}, Lt1/F;->h(I)I

    .line 81
    .line 82
    .line 83
    move-result v11

    .line 84
    add-int/2addr v11, v7

    .line 85
    if-ne v14, v7, :cond_5

    .line 86
    .line 87
    if-ne v11, v7, :cond_5

    .line 88
    .line 89
    add-int/2addr v2, v7

    .line 90
    invoke-virtual {v0, v2}, Lt1/F;->h(I)I

    .line 91
    .line 92
    .line 93
    move-result v11

    .line 94
    const/4 v14, 0x0

    .line 95
    :goto_1
    if-ge v14, v2, :cond_3

    .line 96
    .line 97
    shr-int v15, v11, v14

    .line 98
    .line 99
    and-int/2addr v15, v7

    .line 100
    if-ne v15, v7, :cond_2

    .line 101
    .line 102
    invoke-virtual {v0, v5}, Lt1/F;->r(I)V

    .line 103
    .line 104
    .line 105
    :cond_2
    add-int/lit8 v14, v14, 0x1

    .line 106
    .line 107
    goto :goto_1

    .line 108
    :cond_3
    invoke-virtual {v0}, Lt1/F;->g()Z

    .line 109
    .line 110
    .line 111
    move-result v2

    .line 112
    if-eqz v2, :cond_4

    .line 113
    .line 114
    invoke-virtual {v0, v1}, Lt1/F;->r(I)V

    .line 115
    .line 116
    .line 117
    invoke-virtual {v0, v1}, Lt1/F;->h(I)I

    .line 118
    .line 119
    .line 120
    move-result v2

    .line 121
    add-int/2addr v2, v7

    .line 122
    shl-int/2addr v2, v1

    .line 123
    invoke-virtual {v0, v1}, Lt1/F;->h(I)I

    .line 124
    .line 125
    .line 126
    move-result v11

    .line 127
    add-int/2addr v11, v7

    .line 128
    :goto_2
    if-ge v9, v11, :cond_4

    .line 129
    .line 130
    invoke-virtual {v0, v2}, Lt1/F;->r(I)V

    .line 131
    .line 132
    .line 133
    add-int/lit8 v9, v9, 0x1

    .line 134
    .line 135
    goto :goto_2

    .line 136
    :cond_4
    move v9, v13

    .line 137
    goto :goto_3

    .line 138
    :cond_5
    const-string v0, "Multiple audio presentations or assets not supported"

    .line 139
    .line 140
    invoke-static {v0}, Landroidx/media3/common/ParserException;->createForUnsupportedContainerFeature(Ljava/lang/String;)Landroidx/media3/common/ParserException;

    .line 141
    .line 142
    .line 143
    move-result-object v0

    .line 144
    throw v0

    .line 145
    :cond_6
    const/4 v10, -0x1

    .line 146
    :goto_3
    invoke-virtual {v0, v3}, Lt1/F;->r(I)V

    .line 147
    .line 148
    .line 149
    invoke-virtual {v0, v4}, Lt1/F;->r(I)V

    .line 150
    .line 151
    .line 152
    if-eqz v6, :cond_a

    .line 153
    .line 154
    invoke-virtual {v0}, Lt1/F;->g()Z

    .line 155
    .line 156
    .line 157
    move-result v2

    .line 158
    const/4 v3, 0x4

    .line 159
    if-eqz v2, :cond_7

    .line 160
    .line 161
    invoke-virtual {v0, v3}, Lt1/F;->r(I)V

    .line 162
    .line 163
    .line 164
    :cond_7
    invoke-virtual {v0}, Lt1/F;->g()Z

    .line 165
    .line 166
    .line 167
    move-result v2

    .line 168
    if-eqz v2, :cond_8

    .line 169
    .line 170
    const/16 v2, 0x18

    .line 171
    .line 172
    invoke-virtual {v0, v2}, Lt1/F;->r(I)V

    .line 173
    .line 174
    .line 175
    :cond_8
    invoke-virtual {v0}, Lt1/F;->g()Z

    .line 176
    .line 177
    .line 178
    move-result v2

    .line 179
    if-eqz v2, :cond_9

    .line 180
    .line 181
    const/16 v2, 0xa

    .line 182
    .line 183
    invoke-virtual {v0, v2}, Lt1/F;->h(I)I

    .line 184
    .line 185
    .line 186
    move-result v2

    .line 187
    add-int/2addr v2, v7

    .line 188
    invoke-virtual {v0, v2}, Lt1/F;->s(I)V

    .line 189
    .line 190
    .line 191
    :cond_9
    const/4 v2, 0x5

    .line 192
    invoke-virtual {v0, v2}, Lt1/F;->r(I)V

    .line 193
    .line 194
    .line 195
    sget-object v2, LN1/p;->d:[I

    .line 196
    .line 197
    invoke-virtual {v0, v3}, Lt1/F;->h(I)I

    .line 198
    .line 199
    .line 200
    move-result v3

    .line 201
    aget v2, v2, v3

    .line 202
    .line 203
    invoke-virtual {v0, v5}, Lt1/F;->h(I)I

    .line 204
    .line 205
    .line 206
    move-result v0

    .line 207
    add-int/lit8 v8, v0, 0x1

    .line 208
    .line 209
    move v11, v2

    .line 210
    goto :goto_4

    .line 211
    :cond_a
    const v2, -0x7fffffff

    .line 212
    .line 213
    .line 214
    const v11, -0x7fffffff

    .line 215
    .line 216
    .line 217
    :goto_4
    if-eqz v6, :cond_e

    .line 218
    .line 219
    if-eqz v10, :cond_d

    .line 220
    .line 221
    if-eq v10, v7, :cond_c

    .line 222
    .line 223
    if-ne v10, v1, :cond_b

    .line 224
    .line 225
    const v0, 0xbb80

    .line 226
    .line 227
    .line 228
    goto :goto_5

    .line 229
    :cond_b
    new-instance v0, Ljava/lang/StringBuilder;

    .line 230
    .line 231
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 232
    .line 233
    .line 234
    const-string v1, "Unsupported reference clock code in DTS HD header: "

    .line 235
    .line 236
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 237
    .line 238
    .line 239
    invoke-virtual {v0, v10}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 240
    .line 241
    .line 242
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 243
    .line 244
    .line 245
    move-result-object v0

    .line 246
    const/4 v1, 0x0

    .line 247
    invoke-static {v0, v1}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    .line 248
    .line 249
    .line 250
    move-result-object v0

    .line 251
    throw v0

    .line 252
    :cond_c
    const v0, 0xac44

    .line 253
    .line 254
    .line 255
    goto :goto_5

    .line 256
    :cond_d
    const/16 v0, 0x7d00

    .line 257
    .line 258
    :goto_5
    int-to-long v1, v9

    .line 259
    const-wide/32 v3, 0xf4240

    .line 260
    .line 261
    .line 262
    int-to-long v5, v0

    .line 263
    invoke-static/range {v1 .. v6}, Lt1/a0;->c1(JJJ)J

    .line 264
    .line 265
    .line 266
    move-result-wide v0

    .line 267
    :goto_6
    move-wide v13, v0

    .line 268
    move v10, v8

    .line 269
    goto :goto_7

    .line 270
    :cond_e
    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    .line 271
    .line 272
    .line 273
    .line 274
    .line 275
    goto :goto_6

    .line 276
    :goto_7
    new-instance v8, LN1/p$b;

    .line 277
    .line 278
    const/4 v15, 0x0

    .line 279
    const/16 v16, 0x0

    .line 280
    .line 281
    const-string v9, "audio/vnd.dts.hd;profile=lbr"

    .line 282
    .line 283
    invoke-direct/range {v8 .. v16}, LN1/p$b;-><init>(Ljava/lang/String;IIIJILN1/p$a;)V

    .line 284
    .line 285
    .line 286
    return-object v8
.end method

.method public static j([B)I
    .locals 1

    .line 1
    invoke-static {p0}, LN1/p;->d([B)Lt1/F;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const/16 v0, 0x2a

    .line 6
    .line 7
    invoke-virtual {p0, v0}, Lt1/F;->r(I)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lt1/F;->g()Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    const/16 v0, 0xc

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    const/16 v0, 0x8

    .line 20
    .line 21
    :goto_0
    invoke-virtual {p0, v0}, Lt1/F;->h(I)I

    .line 22
    .line 23
    .line 24
    move-result p0

    .line 25
    add-int/lit8 p0, p0, 0x1

    .line 26
    .line 27
    return p0
.end method

.method public static k([BLjava/util/concurrent/atomic/AtomicInteger;)LN1/p$b;
    .locals 17
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    .line 1
    invoke-static/range {p0 .. p0}, LN1/p;->d([B)Lt1/F;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/16 v1, 0x20

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Lt1/F;->h(I)I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    const v2, 0x40411bf2

    .line 12
    .line 13
    .line 14
    const/4 v3, 0x0

    .line 15
    const/4 v4, 0x1

    .line 16
    if-ne v1, v2, :cond_0

    .line 17
    .line 18
    const/4 v1, 0x1

    .line 19
    goto :goto_0

    .line 20
    :cond_0
    const/4 v1, 0x0

    .line 21
    :goto_0
    sget-object v2, LN1/p;->e:[I

    .line 22
    .line 23
    invoke-static {v0, v2, v4}, LN1/p;->m(Lt1/F;[IZ)I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    add-int/2addr v2, v4

    .line 28
    if-eqz v1, :cond_9

    .line 29
    .line 30
    invoke-virtual {v0}, Lt1/F;->g()Z

    .line 31
    .line 32
    .line 33
    move-result v5

    .line 34
    if-eqz v5, :cond_8

    .line 35
    .line 36
    move-object/from16 v5, p0

    .line 37
    .line 38
    invoke-static {v5, v2}, LN1/p;->a([BI)V

    .line 39
    .line 40
    .line 41
    const/4 v5, 0x2

    .line 42
    invoke-virtual {v0, v5}, Lt1/F;->h(I)I

    .line 43
    .line 44
    .line 45
    move-result v6

    .line 46
    const/4 v7, 0x0

    .line 47
    if-eqz v6, :cond_3

    .line 48
    .line 49
    if-eq v6, v4, :cond_2

    .line 50
    .line 51
    if-ne v6, v5, :cond_1

    .line 52
    .line 53
    const/16 v6, 0x180

    .line 54
    .line 55
    goto :goto_1

    .line 56
    :cond_1
    new-instance v0, Ljava/lang/StringBuilder;

    .line 57
    .line 58
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 59
    .line 60
    .line 61
    const-string v1, "Unsupported base duration index in DTS UHD header: "

    .line 62
    .line 63
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 64
    .line 65
    .line 66
    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 67
    .line 68
    .line 69
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    invoke-static {v0, v7}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    throw v0

    .line 78
    :cond_2
    const/16 v6, 0x1e0

    .line 79
    .line 80
    goto :goto_1

    .line 81
    :cond_3
    const/16 v6, 0x200

    .line 82
    .line 83
    :goto_1
    const/4 v8, 0x3

    .line 84
    invoke-virtual {v0, v8}, Lt1/F;->h(I)I

    .line 85
    .line 86
    .line 87
    move-result v8

    .line 88
    add-int/2addr v8, v4

    .line 89
    mul-int v6, v6, v8

    .line 90
    .line 91
    invoke-virtual {v0, v5}, Lt1/F;->h(I)I

    .line 92
    .line 93
    .line 94
    move-result v8

    .line 95
    if-eqz v8, :cond_6

    .line 96
    .line 97
    if-eq v8, v4, :cond_5

    .line 98
    .line 99
    if-ne v8, v5, :cond_4

    .line 100
    .line 101
    const v7, 0xbb80

    .line 102
    .line 103
    .line 104
    goto :goto_2

    .line 105
    :cond_4
    new-instance v0, Ljava/lang/StringBuilder;

    .line 106
    .line 107
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 108
    .line 109
    .line 110
    const-string v1, "Unsupported clock rate index in DTS UHD header: "

    .line 111
    .line 112
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 113
    .line 114
    .line 115
    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 116
    .line 117
    .line 118
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 119
    .line 120
    .line 121
    move-result-object v0

    .line 122
    invoke-static {v0, v7}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    .line 123
    .line 124
    .line 125
    move-result-object v0

    .line 126
    throw v0

    .line 127
    :cond_5
    const v7, 0xac44

    .line 128
    .line 129
    .line 130
    goto :goto_2

    .line 131
    :cond_6
    const/16 v7, 0x7d00

    .line 132
    .line 133
    :goto_2
    invoke-virtual {v0}, Lt1/F;->g()Z

    .line 134
    .line 135
    .line 136
    move-result v8

    .line 137
    if-eqz v8, :cond_7

    .line 138
    .line 139
    const/16 v8, 0x24

    .line 140
    .line 141
    invoke-virtual {v0, v8}, Lt1/F;->r(I)V

    .line 142
    .line 143
    .line 144
    :cond_7
    invoke-virtual {v0, v5}, Lt1/F;->h(I)I

    .line 145
    .line 146
    .line 147
    move-result v5

    .line 148
    shl-int v5, v4, v5

    .line 149
    .line 150
    mul-int v5, v5, v7

    .line 151
    .line 152
    int-to-long v8, v6

    .line 153
    const-wide/32 v10, 0xf4240

    .line 154
    .line 155
    .line 156
    int-to-long v12, v7

    .line 157
    invoke-static/range {v8 .. v13}, Lt1/a0;->c1(JJJ)J

    .line 158
    .line 159
    .line 160
    move-result-wide v6

    .line 161
    move v11, v5

    .line 162
    :goto_3
    move-wide v13, v6

    .line 163
    goto :goto_4

    .line 164
    :cond_8
    const-string v0, "Only supports full channel mask-based audio presentation"

    .line 165
    .line 166
    invoke-static {v0}, Landroidx/media3/common/ParserException;->createForUnsupportedContainerFeature(Ljava/lang/String;)Landroidx/media3/common/ParserException;

    .line 167
    .line 168
    .line 169
    move-result-object v0

    .line 170
    throw v0

    .line 171
    :cond_9
    const v5, -0x7fffffff

    .line 172
    .line 173
    .line 174
    const-wide v6, -0x7fffffffffffffffL    # -4.9E-324

    .line 175
    .line 176
    .line 177
    .line 178
    .line 179
    const v11, -0x7fffffff

    .line 180
    .line 181
    .line 182
    goto :goto_3

    .line 183
    :goto_4
    const/4 v5, 0x0

    .line 184
    const/4 v6, 0x0

    .line 185
    :goto_5
    if-ge v5, v1, :cond_a

    .line 186
    .line 187
    sget-object v7, LN1/p;->f:[I

    .line 188
    .line 189
    invoke-static {v0, v7, v4}, LN1/p;->m(Lt1/F;[IZ)I

    .line 190
    .line 191
    .line 192
    move-result v7

    .line 193
    add-int/2addr v6, v7

    .line 194
    add-int/lit8 v5, v5, 0x1

    .line 195
    .line 196
    goto :goto_5

    .line 197
    :cond_a
    if-eqz v1, :cond_b

    .line 198
    .line 199
    sget-object v1, LN1/p;->g:[I

    .line 200
    .line 201
    invoke-static {v0, v1, v4}, LN1/p;->m(Lt1/F;[IZ)I

    .line 202
    .line 203
    .line 204
    move-result v1

    .line 205
    move-object/from16 v5, p1

    .line 206
    .line 207
    invoke-virtual {v5, v1}, Ljava/util/concurrent/atomic/AtomicInteger;->set(I)V

    .line 208
    .line 209
    .line 210
    goto :goto_6

    .line 211
    :cond_b
    move-object/from16 v5, p1

    .line 212
    .line 213
    :goto_6
    invoke-virtual {v5}, Ljava/util/concurrent/atomic/AtomicInteger;->get()I

    .line 214
    .line 215
    .line 216
    move-result v1

    .line 217
    if-eqz v1, :cond_c

    .line 218
    .line 219
    sget-object v1, LN1/p;->h:[I

    .line 220
    .line 221
    invoke-static {v0, v1, v4}, LN1/p;->m(Lt1/F;[IZ)I

    .line 222
    .line 223
    .line 224
    move-result v3

    .line 225
    :cond_c
    add-int/2addr v6, v3

    .line 226
    add-int v12, v2, v6

    .line 227
    .line 228
    new-instance v8, LN1/p$b;

    .line 229
    .line 230
    const/4 v15, 0x0

    .line 231
    const/16 v16, 0x0

    .line 232
    .line 233
    const-string v9, "audio/vnd.dts.uhd;profile=p2"

    .line 234
    .line 235
    const/4 v10, 0x2

    .line 236
    invoke-direct/range {v8 .. v16}, LN1/p$b;-><init>(Ljava/lang/String;IIIJILN1/p$a;)V

    .line 237
    .line 238
    .line 239
    return-object v8
.end method

.method public static l([B)I
    .locals 2

    .line 1
    invoke-static {p0}, LN1/p;->d([B)Lt1/F;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const/16 v0, 0x20

    .line 6
    .line 7
    invoke-virtual {p0, v0}, Lt1/F;->r(I)V

    .line 8
    .line 9
    .line 10
    sget-object v0, LN1/p;->i:[I

    .line 11
    .line 12
    const/4 v1, 0x1

    .line 13
    invoke-static {p0, v0, v1}, LN1/p;->m(Lt1/F;[IZ)I

    .line 14
    .line 15
    .line 16
    move-result p0

    .line 17
    add-int/2addr p0, v1

    .line 18
    return p0
.end method

.method public static m(Lt1/F;[IZ)I
    .locals 4

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x0

    .line 3
    const/4 v2, 0x0

    .line 4
    :goto_0
    const/4 v3, 0x3

    .line 5
    if-ge v1, v3, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Lt1/F;->g()Z

    .line 8
    .line 9
    .line 10
    move-result v3

    .line 11
    if-eqz v3, :cond_0

    .line 12
    .line 13
    add-int/lit8 v2, v2, 0x1

    .line 14
    .line 15
    add-int/lit8 v1, v1, 0x1

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    if-eqz p2, :cond_2

    .line 19
    .line 20
    const/4 p2, 0x0

    .line 21
    :goto_1
    if-ge v0, v2, :cond_1

    .line 22
    .line 23
    aget v1, p1, v0

    .line 24
    .line 25
    const/4 v3, 0x1

    .line 26
    shl-int v1, v3, v1

    .line 27
    .line 28
    add-int/2addr p2, v1

    .line 29
    add-int/lit8 v0, v0, 0x1

    .line 30
    .line 31
    goto :goto_1

    .line 32
    :cond_1
    move v0, p2

    .line 33
    :cond_2
    aget p1, p1, v2

    .line 34
    .line 35
    invoke-virtual {p0, p1}, Lt1/F;->h(I)I

    .line 36
    .line 37
    .line 38
    move-result p0

    .line 39
    add-int/2addr v0, p0

    .line 40
    return v0
.end method
