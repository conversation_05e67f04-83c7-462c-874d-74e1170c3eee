.class public interface abstract Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lk31/b;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u0008`\u0018\u00002\u00020\u0001J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;",
        "Lk31/b;",
        "Lj31/a;",
        "data",
        "",
        "setData",
        "(Lj31/a;)V",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract setData(Lj31/a;)V
    .param p1    # Lj31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method
