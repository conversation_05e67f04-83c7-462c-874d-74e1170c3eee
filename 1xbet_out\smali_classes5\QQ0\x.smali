.class public final LQQ0/x;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "LQQ0/w;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LQQ0/y;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LQQ0/A;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LQQ0/C;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LQQ0/y;",
            ">;",
            "LBc/a<",
            "LQQ0/A;",
            ">;",
            "LBc/a<",
            "LQQ0/C;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LQQ0/x;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, LQQ0/x;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, LQQ0/x;->c:LBc/a;

    .line 9
    .line 10
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;)LQQ0/x;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LQQ0/y;",
            ">;",
            "LBc/a<",
            "LQQ0/A;",
            ">;",
            "LBc/a<",
            "LQQ0/C;",
            ">;)",
            "LQQ0/x;"
        }
    .end annotation

    .line 1
    new-instance v0, LQQ0/x;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1, p2}, LQQ0/x;-><init>(LBc/a;LBc/a;LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(LQQ0/y;LQQ0/A;LQQ0/C;)LQQ0/w;
    .locals 1

    .line 1
    new-instance v0, LQQ0/w;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1, p2}, LQQ0/w;-><init>(LQQ0/y;LQQ0/A;LQQ0/C;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()LQQ0/w;
    .locals 3

    .line 1
    iget-object v0, p0, LQQ0/x;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LQQ0/y;

    .line 8
    .line 9
    iget-object v1, p0, LQQ0/x;->b:LBc/a;

    .line 10
    .line 11
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    check-cast v1, LQQ0/A;

    .line 16
    .line 17
    iget-object v2, p0, LQQ0/x;->c:LBc/a;

    .line 18
    .line 19
    invoke-interface {v2}, LBc/a;->get()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    check-cast v2, LQQ0/C;

    .line 24
    .line 25
    invoke-static {v0, v1, v2}, LQQ0/x;->c(LQQ0/y;LQQ0/A;LQQ0/C;)LQQ0/w;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LQQ0/x;->b()LQQ0/w;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
