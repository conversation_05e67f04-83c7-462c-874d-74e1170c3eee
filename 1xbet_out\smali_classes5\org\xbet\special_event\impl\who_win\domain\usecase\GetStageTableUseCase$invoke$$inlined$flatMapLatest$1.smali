.class public final Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.who_win.domain.usecase.GetStageTableUseCase$invoke$$inlined$flatMapLatest$1"
    f = "GetStageTableUseCase.kt"
    l = {
        0xbd
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->g(ILjava/lang/Integer;)Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lkotlinx/coroutines/flow/f<",
        "-",
        "Ljava/util/List<",
        "+",
        "LDy0/a;",
        ">;>;",
        "Lorg/xbet/coef_type/api/domain/models/EnCoefView;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0006\u001a\u00020\u0004\"\u0004\u0008\u0000\u0010\u0000\"\u0004\u0008\u0001\u0010\u0001*\u0008\u0012\u0004\u0012\u00028\u00000\u00022\u0006\u0010\u0003\u001a\u00028\u0001H\n\u00a8\u0006\u0005"
    }
    d2 = {
        "R",
        "T",
        "Lkotlinx/coroutines/flow/f;",
        "it",
        "",
        "kotlinx/coroutines/flow/FlowKt__MergeKt$flatMapLatest$1",
        "<anonymous>"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $eventId$inlined:I

.field final synthetic $initRequestInternal$inlined:Lkotlin/jvm/internal/Ref$BooleanRef;

.field final synthetic $userRegistrationCountryId$inlined:Ljava/lang/Integer;

.field private synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;


# direct methods
.method public constructor <init>(Lkotlin/coroutines/e;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;ILjava/lang/Integer;Lkotlin/jvm/internal/Ref$BooleanRef;)V
    .locals 0

    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    iput p3, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->$eventId$inlined:I

    iput-object p4, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->$userRegistrationCountryId$inlined:Ljava/lang/Integer;

    iput-object p5, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->$initRequestInternal$inlined:Lkotlin/jvm/internal/Ref$BooleanRef;

    const/4 p2, 0x3

    invoke-direct {p0, p2, p1}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/flow/f;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->invoke(Lkotlinx/coroutines/flow/f;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/flow/f;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/f<",
            "-",
            "Ljava/util/List<",
            "+",
            "LDy0/a;",
            ">;>;",
            "Lorg/xbet/coef_type/api/domain/models/EnCoefView;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;

    iget-object v2, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    iget v3, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->$eventId$inlined:I

    iget-object v4, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->$userRegistrationCountryId$inlined:Ljava/lang/Integer;

    iget-object v5, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->$initRequestInternal$inlined:Lkotlin/jvm/internal/Ref$BooleanRef;

    move-object v1, p3

    invoke-direct/range {v0 .. v5}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;-><init>(Lkotlin/coroutines/e;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;ILjava/lang/Integer;Lkotlin/jvm/internal/Ref$BooleanRef;)V

    iput-object p1, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->label:I

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    if-eqz v2, :cond_1

    .line 11
    .line 12
    if-ne v2, v3, :cond_0

    .line 13
    .line 14
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 19
    .line 20
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 21
    .line 22
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 23
    .line 24
    .line 25
    throw v1

    .line 26
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    iget-object v2, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    .line 30
    .line 31
    check-cast v2, Lkotlinx/coroutines/flow/f;

    .line 32
    .line 33
    iget-object v4, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    .line 34
    .line 35
    move-object v9, v4

    .line 36
    check-cast v9, Lorg/xbet/coef_type/api/domain/models/EnCoefView;

    .line 37
    .line 38
    sget-object v4, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    .line 39
    .line 40
    new-instance v5, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;

    .line 41
    .line 42
    iget-object v6, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 43
    .line 44
    iget v7, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->$eventId$inlined:I

    .line 45
    .line 46
    iget-object v8, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->$userRegistrationCountryId$inlined:Ljava/lang/Integer;

    .line 47
    .line 48
    iget-object v10, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->$initRequestInternal$inlined:Lkotlin/jvm/internal/Ref$BooleanRef;

    .line 49
    .line 50
    const/4 v11, 0x0

    .line 51
    invoke-direct/range {v5 .. v11}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;-><init>(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;ILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;Lkotlin/jvm/internal/Ref$BooleanRef;Lkotlin/coroutines/e;)V

    .line 52
    .line 53
    .line 54
    const-wide/16 v6, 0x1e

    .line 55
    .line 56
    invoke-static {v6, v7, v4, v5}, Lcom/xbet/onexcore/utils/flows/FlowBuilderKt;->b(JLjava/util/concurrent/TimeUnit;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 57
    .line 58
    .line 59
    move-result-object v8

    .line 60
    new-instance v9, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$b;

    .line 61
    .line 62
    iget-object v4, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->$initRequestInternal$inlined:Lkotlin/jvm/internal/Ref$BooleanRef;

    .line 63
    .line 64
    invoke-direct {v9, v4}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$b;-><init>(Lkotlin/jvm/internal/Ref$BooleanRef;)V

    .line 65
    .line 66
    .line 67
    new-instance v12, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$3;

    .line 68
    .line 69
    iget-object v4, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 70
    .line 71
    const/4 v5, 0x0

    .line 72
    invoke-direct {v12, v4, v5}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$3;-><init>(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lkotlin/coroutines/e;)V

    .line 73
    .line 74
    .line 75
    new-instance v15, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$c;

    .line 76
    .line 77
    iget-object v4, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->$initRequestInternal$inlined:Lkotlin/jvm/internal/Ref$BooleanRef;

    .line 78
    .line 79
    iget-object v6, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 80
    .line 81
    invoke-direct {v15, v4, v6}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$c;-><init>(Lkotlin/jvm/internal/Ref$BooleanRef;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;)V

    .line 82
    .line 83
    .line 84
    const/16 v16, 0x18

    .line 85
    .line 86
    const/16 v17, 0x0

    .line 87
    .line 88
    const-wide/16 v10, 0x1e

    .line 89
    .line 90
    const/4 v13, 0x0

    .line 91
    const/4 v14, 0x0

    .line 92
    invoke-static/range {v8 .. v17}, Lcom/xbet/onexcore/utils/flows/ScreenRetryStrategiesExtentionsKt;->d(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;JLkotlin/jvm/functions/Function1;ZLOc/n;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 93
    .line 94
    .line 95
    move-result-object v4

    .line 96
    new-instance v6, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$5;

    .line 97
    .line 98
    iget-object v7, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 99
    .line 100
    invoke-direct {v6, v7, v5}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$5;-><init>(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lkotlin/coroutines/e;)V

    .line 101
    .line 102
    .line 103
    invoke-static {v4, v6}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 104
    .line 105
    .line 106
    move-result-object v4

    .line 107
    new-instance v6, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;

    .line 108
    .line 109
    iget-object v7, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->$initRequestInternal$inlined:Lkotlin/jvm/internal/Ref$BooleanRef;

    .line 110
    .line 111
    iget-object v8, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 112
    .line 113
    invoke-direct {v6, v7, v8, v5}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$6;-><init>(Lkotlin/jvm/internal/Ref$BooleanRef;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lkotlin/coroutines/e;)V

    .line 114
    .line 115
    .line 116
    invoke-static {v4, v6}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 117
    .line 118
    .line 119
    move-result-object v4

    .line 120
    iput v3, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;->label:I

    .line 121
    .line 122
    invoke-static {v2, v4, v0}, Lkotlinx/coroutines/flow/g;->H(Lkotlinx/coroutines/flow/f;Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v2

    .line 126
    if-ne v2, v1, :cond_2

    .line 127
    .line 128
    return-object v1

    .line 129
    :cond_2
    :goto_0
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 130
    .line 131
    return-object v1
.end method
