.class final Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.tile_matching.data.repositories.TileMatchingRepositoryImpl$makeAction$2"
    f = "TileMatchingRepositoryImpl.kt"
    l = {
        0x47,
        0x50
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->b(JIIILcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Ljava/lang/String;",
        "Ljava/lang/Long;",
        "Lkotlin/coroutines/e<",
        "-",
        "LzT0/e;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\n\u00a2\u0006\u0004\u0008\u0005\u0010\u0006"
    }
    d2 = {
        "",
        "token",
        "",
        "userId",
        "LzT0/e;",
        "<anonymous>",
        "(Ljava/lang/String;J)LzT0/e;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $accountId:J

.field final synthetic $actionStep:I

.field final synthetic $column:I

.field final synthetic $gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

.field final synthetic $row:I

.field synthetic J$0:J

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;


# direct methods
.method public constructor <init>(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;JIIILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
            "Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;",
            "JIII",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    iput-object p2, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    iput-wide p3, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$accountId:J

    iput p5, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$actionStep:I

    iput p6, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$row:I

    iput p7, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$column:I

    const/4 p1, 0x3

    invoke-direct {p0, p1, p8}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Ljava/lang/Number;

    invoke-virtual {p2}, Ljava/lang/Number;->longValue()J

    move-result-wide v0

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, v0, v1, p3}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->invoke(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "J",
            "Lkotlin/coroutines/e<",
            "-",
            "LzT0/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;

    iget-object v1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    iget-object v2, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    iget-wide v3, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$accountId:J

    iget v5, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$actionStep:I

    iget v6, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$row:I

    iget v7, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$column:I

    move-object v8, p4

    invoke-direct/range {v0 .. v8}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;-><init>(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;JIIILkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->L$0:Ljava/lang/Object;

    iput-wide p2, v0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->J$0:J

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 11

    .line 1
    const/4 v0, 0x1

    .line 2
    const/4 v1, 0x2

    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v2

    .line 7
    iget v3, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->label:I

    .line 8
    .line 9
    if-eqz v3, :cond_2

    .line 10
    .line 11
    if-eq v3, v0, :cond_1

    .line 12
    .line 13
    if-ne v3, v1, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    move-object v9, p0

    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    goto :goto_2

    .line 32
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 33
    .line 34
    .line 35
    iget-object p1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->L$0:Ljava/lang/Object;

    .line 36
    .line 37
    move-object v4, p1

    .line 38
    check-cast v4, Ljava/lang/String;

    .line 39
    .line 40
    iget-wide v5, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->J$0:J

    .line 41
    .line 42
    iget-object p1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 43
    .line 44
    sget-object v3, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2$a;->a:[I

    .line 45
    .line 46
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 47
    .line 48
    .line 49
    move-result p1

    .line 50
    aget p1, v3, p1

    .line 51
    .line 52
    if-eq p1, v0, :cond_5

    .line 53
    .line 54
    if-ne p1, v1, :cond_4

    .line 55
    .line 56
    iget-object v3, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    .line 57
    .line 58
    iget v7, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$actionStep:I

    .line 59
    .line 60
    iget p1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$row:I

    .line 61
    .line 62
    invoke-static {p1}, LHc/a;->e(I)Ljava/lang/Integer;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    iget v8, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$column:I

    .line 67
    .line 68
    invoke-static {v8}, LHc/a;->e(I)Ljava/lang/Integer;

    .line 69
    .line 70
    .line 71
    move-result-object v8

    .line 72
    new-array v9, v1, [Ljava/lang/Integer;

    .line 73
    .line 74
    const/4 v10, 0x0

    .line 75
    aput-object p1, v9, v10

    .line 76
    .line 77
    aput-object v8, v9, v0

    .line 78
    .line 79
    invoke-static {v9}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 80
    .line 81
    .line 82
    move-result-object v8

    .line 83
    iput v1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->label:I

    .line 84
    .line 85
    move-object v9, p0

    .line 86
    invoke-static/range {v3 .. v9}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->p(Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Ljava/lang/String;JILjava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    if-ne p1, v2, :cond_3

    .line 91
    .line 92
    goto :goto_1

    .line 93
    :cond_3
    :goto_0
    check-cast p1, LwT0/d;

    .line 94
    .line 95
    invoke-static {p1}, LtT0/h;->b(LwT0/d;)LzT0/e;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    return-object p1

    .line 100
    :cond_4
    move-object v9, p0

    .line 101
    new-instance p1, Ljava/lang/EnumConstantNotPresentException;

    .line 102
    .line 103
    const-class v0, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 104
    .line 105
    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 106
    .line 107
    .line 108
    move-result-object v1

    .line 109
    invoke-direct {p1, v0, v1}, Ljava/lang/EnumConstantNotPresentException;-><init>(Ljava/lang/Class;Ljava/lang/String;)V

    .line 110
    .line 111
    .line 112
    throw p1

    .line 113
    :cond_5
    move-object v9, p0

    .line 114
    iget-object v3, v9, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    .line 115
    .line 116
    iget-wide v5, v9, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$accountId:J

    .line 117
    .line 118
    iget v7, v9, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$actionStep:I

    .line 119
    .line 120
    iget v8, v9, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$row:I

    .line 121
    .line 122
    move-object v10, v9

    .line 123
    iget v9, v10, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->$column:I

    .line 124
    .line 125
    iput v0, v10, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;->label:I

    .line 126
    .line 127
    invoke-static/range {v3 .. v10}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->o(Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Ljava/lang/String;JIIILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 128
    .line 129
    .line 130
    move-result-object p1

    .line 131
    if-ne p1, v2, :cond_6

    .line 132
    .line 133
    :goto_1
    return-object v2

    .line 134
    :cond_6
    :goto_2
    check-cast p1, LvT0/d;

    .line 135
    .line 136
    invoke-static {p1}, LtT0/h;->a(LvT0/d;)LzT0/e;

    .line 137
    .line 138
    .line 139
    move-result-object p1

    .line 140
    return-object p1
.end method
