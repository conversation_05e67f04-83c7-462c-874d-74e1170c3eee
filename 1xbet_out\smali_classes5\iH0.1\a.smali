.class public final LiH0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0019\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u001a\u001f\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u0001*\u0008\u0012\u0004\u0012\u00020\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "LjH0/e;",
        "",
        "LlH0/d;",
        "a",
        "(LjH0/e;)Ljava/util/List;",
        "LlH0/e;",
        "b",
        "(Ljava/util/List;)Ljava/util/List;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LjH0/e;)Ljava/util/List;
    .locals 17
    .param p0    # LjH0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LjH0/e;",
            ")",
            "Ljava/util/List<",
            "LlH0/d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p0}, LjH0/e;->a()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_10

    .line 6
    .line 7
    new-instance v2, Ljava/util/ArrayList;

    .line 8
    .line 9
    const/16 v3, 0xa

    .line 10
    .line 11
    invoke-static {v0, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 12
    .line 13
    .line 14
    move-result v4

    .line 15
    invoke-direct {v2, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 16
    .line 17
    .line 18
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    const/4 v4, 0x0

    .line 23
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v5

    .line 27
    if-eqz v5, :cond_f

    .line 28
    .line 29
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v5

    .line 33
    add-int/lit8 v6, v4, 0x1

    .line 34
    .line 35
    if-gez v4, :cond_0

    .line 36
    .line 37
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 38
    .line 39
    .line 40
    :cond_0
    check-cast v5, LjH0/d;

    .line 41
    .line 42
    invoke-virtual {v5}, LjH0/d;->c()Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v7

    .line 46
    const-string v8, ""

    .line 47
    .line 48
    if-nez v7, :cond_1

    .line 49
    .line 50
    move-object v7, v8

    .line 51
    :cond_1
    invoke-virtual {v5}, LjH0/d;->b()Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object v9

    .line 55
    if-nez v9, :cond_2

    .line 56
    .line 57
    move-object v9, v8

    .line 58
    :cond_2
    invoke-virtual {v5}, LjH0/d;->a()Ljava/util/List;

    .line 59
    .line 60
    .line 61
    move-result-object v5

    .line 62
    if-eqz v5, :cond_c

    .line 63
    .line 64
    new-instance v10, Ljava/util/ArrayList;

    .line 65
    .line 66
    invoke-static {v5, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 67
    .line 68
    .line 69
    move-result v11

    .line 70
    invoke-direct {v10, v11}, Ljava/util/ArrayList;-><init>(I)V

    .line 71
    .line 72
    .line 73
    invoke-interface {v5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 74
    .line 75
    .line 76
    move-result-object v5

    .line 77
    :goto_1
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    .line 78
    .line 79
    .line 80
    move-result v11

    .line 81
    if-eqz v11, :cond_d

    .line 82
    .line 83
    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 84
    .line 85
    .line 86
    move-result-object v11

    .line 87
    check-cast v11, LjH0/a;

    .line 88
    .line 89
    new-instance v12, LlH0/a;

    .line 90
    .line 91
    invoke-virtual {v11}, LjH0/a;->c()Ljava/lang/String;

    .line 92
    .line 93
    .line 94
    move-result-object v13

    .line 95
    if-nez v13, :cond_3

    .line 96
    .line 97
    move-object v13, v8

    .line 98
    :cond_3
    new-instance v14, LlH0/b;

    .line 99
    .line 100
    invoke-virtual {v11}, LjH0/a;->a()LjH0/b;

    .line 101
    .line 102
    .line 103
    move-result-object v15

    .line 104
    if-eqz v15, :cond_4

    .line 105
    .line 106
    invoke-virtual {v15}, LjH0/b;->b()Ljava/lang/String;

    .line 107
    .line 108
    .line 109
    move-result-object v15

    .line 110
    if-nez v15, :cond_5

    .line 111
    .line 112
    :cond_4
    move-object v15, v8

    .line 113
    :cond_5
    invoke-virtual {v11}, LjH0/a;->a()LjH0/b;

    .line 114
    .line 115
    .line 116
    move-result-object v16

    .line 117
    if-eqz v16, :cond_7

    .line 118
    .line 119
    invoke-virtual/range {v16 .. v16}, LjH0/b;->a()Ljava/util/List;

    .line 120
    .line 121
    .line 122
    move-result-object v16

    .line 123
    if-nez v16, :cond_6

    .line 124
    .line 125
    goto :goto_3

    .line 126
    :cond_6
    :goto_2
    move-object/from16 v1, v16

    .line 127
    .line 128
    goto :goto_4

    .line 129
    :cond_7
    :goto_3
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 130
    .line 131
    .line 132
    move-result-object v16

    .line 133
    goto :goto_2

    .line 134
    :goto_4
    invoke-direct {v14, v15, v1}, LlH0/b;-><init>(Ljava/lang/String;Ljava/util/List;)V

    .line 135
    .line 136
    .line 137
    new-instance v1, LlH0/c;

    .line 138
    .line 139
    invoke-virtual {v11}, LjH0/a;->b()LjH0/c;

    .line 140
    .line 141
    .line 142
    move-result-object v15

    .line 143
    if-eqz v15, :cond_8

    .line 144
    .line 145
    invoke-virtual {v15}, LjH0/c;->b()Ljava/lang/String;

    .line 146
    .line 147
    .line 148
    move-result-object v15

    .line 149
    if-nez v15, :cond_9

    .line 150
    .line 151
    :cond_8
    move-object v15, v8

    .line 152
    :cond_9
    invoke-virtual {v11}, LjH0/a;->b()LjH0/c;

    .line 153
    .line 154
    .line 155
    move-result-object v11

    .line 156
    if-eqz v11, :cond_a

    .line 157
    .line 158
    invoke-virtual {v11}, LjH0/c;->a()Ljava/lang/String;

    .line 159
    .line 160
    .line 161
    move-result-object v11

    .line 162
    if-nez v11, :cond_b

    .line 163
    .line 164
    :cond_a
    move-object v11, v8

    .line 165
    :cond_b
    invoke-direct {v1, v15, v11}, LlH0/c;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 166
    .line 167
    .line 168
    invoke-direct {v12, v13, v14, v1}, LlH0/a;-><init>(Ljava/lang/String;LlH0/b;LlH0/c;)V

    .line 169
    .line 170
    .line 171
    invoke-interface {v10, v12}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 172
    .line 173
    .line 174
    goto :goto_1

    .line 175
    :cond_c
    const/4 v10, 0x0

    .line 176
    :cond_d
    if-nez v10, :cond_e

    .line 177
    .line 178
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 179
    .line 180
    .line 181
    move-result-object v10

    .line 182
    :cond_e
    new-instance v1, LlH0/d;

    .line 183
    .line 184
    invoke-direct {v1, v4, v7, v9, v10}, LlH0/d;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/util/List;)V

    .line 185
    .line 186
    .line 187
    invoke-interface {v2, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 188
    .line 189
    .line 190
    move v4, v6

    .line 191
    goto/16 :goto_0

    .line 192
    .line 193
    :cond_f
    move-object v1, v2

    .line 194
    goto :goto_5

    .line 195
    :cond_10
    const/4 v1, 0x0

    .line 196
    :goto_5
    if-nez v1, :cond_11

    .line 197
    .line 198
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 199
    .line 200
    .line 201
    move-result-object v0

    .line 202
    return-object v0

    .line 203
    :cond_11
    return-object v1
.end method

.method public static final b(Ljava/util/List;)Ljava/util/List;
    .locals 5
    .param p0    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LlH0/d;",
            ">;)",
            "Ljava/util/List<",
            "LlH0/e;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    const/16 v1, 0xa

    .line 4
    .line 5
    invoke-static {p0, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 10
    .line 11
    .line 12
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    if-eqz v1, :cond_0

    .line 21
    .line 22
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    check-cast v1, LlH0/d;

    .line 27
    .line 28
    new-instance v2, LlH0/e;

    .line 29
    .line 30
    invoke-virtual {v1}, LlH0/d;->b()I

    .line 31
    .line 32
    .line 33
    move-result v3

    .line 34
    invoke-virtual {v1}, LlH0/d;->c()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object v4

    .line 38
    invoke-virtual {v1}, LlH0/d;->d()Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-direct {v2, v3, v4, v1}, LlH0/e;-><init>(ILjava/lang/String;Ljava/lang/String;)V

    .line 43
    .line 44
    .line 45
    invoke-interface {v0, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 46
    .line 47
    .line 48
    goto :goto_0

    .line 49
    :cond_0
    return-object v0
.end method
