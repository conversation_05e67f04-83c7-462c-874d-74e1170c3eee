.class final synthetic Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$updateScreenLottieErrorState$1$2;
.super Lkotlin/jvm/internal/FunctionReferenceImpl;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->v5(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1001
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/FunctionReferenceImpl;",
        "Lkotlin/jvm/functions/Function0<",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkot<PERSON>/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>(Ljava/lang/Object;)V
    .locals 7

    const-string v5, "onReloadScreenSections()V"

    const/4 v6, 0x0

    const/4 v1, 0x0

    const-class v3, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    const-string v4, "onReloadScreenSections"

    move-object v0, p0

    move-object v2, p1

    invoke-direct/range {v0 .. v6}, Lkotlin/jvm/internal/FunctionReferenceImpl;-><init>(ILjava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$updateScreenLottieErrorState$1$2;->invoke()V

    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object v0
.end method

.method public final invoke()V
    .locals 1

    .line 2
    iget-object v0, p0, Lkotlin/jvm/internal/CallableReference;->receiver:Ljava/lang/Object;

    check-cast v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->j4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    return-void
.end method
