.class final Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.alerts_pipe_impl.presentation.AlertsPipeReceiverImpl$requestToShowDialogByPriority$2"
    f = "AlertsPipeReceiverImpl.kt"
    l = {
        0x33
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->b(Lmg/a;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;


# direct methods
.method public constructor <init>(Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;->this$0:Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;

    iget-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;->this$0:Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;

    invoke-direct {p1, v0, p2}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;-><init>(Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iput v2, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;->label:I

    .line 28
    .line 29
    const-wide/16 v3, 0x3e8

    .line 30
    .line 31
    invoke-static {v3, v4, p0}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    if-ne p1, v0, :cond_2

    .line 36
    .line 37
    return-object v0

    .line 38
    :cond_2
    :goto_0
    iget-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;->this$0:Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;

    .line 39
    .line 40
    invoke-static {p1}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->e(Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;)Ljava/util/List;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 45
    .line 46
    .line 47
    move-result v0

    .line 48
    if-le v0, v2, :cond_3

    .line 49
    .line 50
    new-instance v0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2$a;

    .line 51
    .line 52
    invoke-direct {v0}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2$a;-><init>()V

    .line 53
    .line 54
    .line 55
    invoke-static {p1, v0}, Lkotlin/collections/z;->C(Ljava/util/List;Ljava/util/Comparator;)V

    .line 56
    .line 57
    .line 58
    :cond_3
    iget-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;->this$0:Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;

    .line 59
    .line 60
    invoke-static {p1}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->e(Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;)Ljava/util/List;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    check-cast p1, Lmg/a;

    .line 69
    .line 70
    iget-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;->this$0:Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;

    .line 71
    .line 72
    invoke-static {v0}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->e(Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;)Ljava/util/List;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    invoke-interface {v0}, Ljava/util/List;->clear()V

    .line 77
    .line 78
    .line 79
    if-eqz p1, :cond_4

    .line 80
    .line 81
    iget-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl$requestToShowDialogByPriority$2;->this$0:Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;

    .line 82
    .line 83
    invoke-static {v0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;->f(Lorg/xbet/alerts_pipe_impl/presentation/AlertsPipeReceiverImpl;Lmg/a;)V

    .line 84
    .line 85
    .line 86
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 87
    .line 88
    return-object p1
.end method
