.class public final Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements Lw21/y;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0098\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0013\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\u001d\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0015\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\n\u001a\u00020\t\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0015\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u000f\u0010\u0013\u001a\u00020\u0012H\u0016\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0017\u0010\u0017\u001a\u00020\u000b2\u0006\u0010\u0016\u001a\u00020\u0015H\u0016\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J!\u0010\u001c\u001a\u00020\u000b2\u0006\u0010\u001a\u001a\u00020\u00192\u0008\u0010\u001b\u001a\u0004\u0018\u00010\u0019H\u0016\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u0017\u0010\u001f\u001a\u00020\u000b2\u0006\u0010\u001e\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u001f\u0010\rJ\u0017\u0010!\u001a\u00020\u000b2\u0006\u0010 \u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008!\u0010\rJ\u0017\u0010#\u001a\u00020\u000b2\u0006\u0010\"\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008#\u0010\rJ\u0019\u0010&\u001a\u00020\u000b2\u0008\u0010%\u001a\u0004\u0018\u00010$H\u0016\u00a2\u0006\u0004\u0008&\u0010\'J\u0019\u0010)\u001a\u00020\u000b2\u0008\u0010%\u001a\u0004\u0018\u00010(H\u0016\u00a2\u0006\u0004\u0008)\u0010*J\u0019\u0010-\u001a\u00020\u000b2\u0008\u0010,\u001a\u0004\u0018\u00010+H\u0016\u00a2\u0006\u0004\u0008-\u0010.J\u0019\u00101\u001a\u00020\u000b2\u0008\u00100\u001a\u0004\u0018\u00010/H\u0016\u00a2\u0006\u0004\u00081\u00102J\u001d\u00105\u001a\u00020\u000b2\u000c\u00104\u001a\u0008\u0012\u0004\u0012\u00020\u000b03H\u0016\u00a2\u0006\u0004\u00085\u00106J\u0019\u00109\u001a\u00020\u000b2\u0008\u00108\u001a\u0004\u0018\u000107H\u0016\u00a2\u0006\u0004\u00089\u0010:J\u0019\u0010<\u001a\u00020\u000b2\u0008\u00108\u001a\u0004\u0018\u00010;H\u0016\u00a2\u0006\u0004\u0008<\u0010=J#\u0010A\u001a\u00020\u000b2\u0012\u0010@\u001a\u000e\u0012\u0004\u0012\u00020?\u0012\u0004\u0012\u00020\u000b0>H\u0016\u00a2\u0006\u0004\u0008A\u0010BJ)\u0010E\u001a\u00020\u000b2\u0018\u0010@\u001a\u0014\u0012\u0004\u0012\u00020?\u0012\u0004\u0012\u00020D\u0012\u0004\u0012\u00020\u000b0CH\u0016\u00a2\u0006\u0004\u0008E\u0010FJ\u000f\u0010G\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008G\u0010HR\u0016\u0010K\u001a\u00020\u000e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008I\u0010JR\u0016\u0010N\u001a\u00020\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR(\u0010Q\u001a\u0014\u0012\u0004\u0012\u00020?\u0012\u0004\u0012\u00020D\u0012\u0004\u0012\u00020\u000b0C8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008O\u0010PR\"\u0010T\u001a\u000e\u0012\u0004\u0012\u00020?\u0012\u0004\u0012\u00020\u000b0>8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008R\u0010SR\u001c\u0010V\u001a\u0008\u0012\u0004\u0012\u00020\u000b038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008G\u0010U\u00a8\u0006W"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;",
        "Landroid/widget/FrameLayout;",
        "Lw21/y;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "style",
        "",
        "setStyleName",
        "(Ljava/lang/String;)V",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;",
        "styleType",
        "setStyleType",
        "(Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;)V",
        "Landroid/view/View;",
        "getView",
        "()Landroid/view/View;",
        "Lv21/e;",
        "tournamentCardModel",
        "setModel",
        "(Lv21/e;)V",
        "LL11/c;",
        "image",
        "placeHolder",
        "setBannerImage",
        "(LL11/c;LL11/c;)V",
        "label",
        "setPrizeLabel",
        "value",
        "setPrizeValue",
        "title",
        "setTitle",
        "Lk21/m;",
        "tag",
        "setMainTag",
        "(Lk21/m;)V",
        "Lk21/a;",
        "setAdditionalTag",
        "(Lk21/a;)V",
        "Lv21/g;",
        "period",
        "setPeriodDates",
        "(Lv21/g;)V",
        "Lv21/t;",
        "timerModel",
        "setTimer",
        "(Lv21/t;)V",
        "Lkotlin/Function0;",
        "callback",
        "setOnTimerExpiredListener",
        "(Lkotlin/jvm/functions/Function0;)V",
        "Lv21/a;",
        "type",
        "setActionButton",
        "(Lv21/a;)V",
        "Lv21/h;",
        "setInfoButton",
        "(Lv21/h;)V",
        "Lkotlin/Function1;",
        "",
        "listener",
        "setInfoButtonClickListener",
        "(Lkotlin/jvm/functions/Function1;)V",
        "Lkotlin/Function2;",
        "",
        "setActionButtonClickListener",
        "(Lkotlin/jvm/functions/Function2;)V",
        "e",
        "()V",
        "a",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;",
        "currentStyle",
        "b",
        "Lw21/y;",
        "currentStyledView",
        "c",
        "Lkotlin/jvm/functions/Function2;",
        "actionClickListener",
        "d",
        "Lkotlin/jvm/functions/Function1;",
        "infoClickListener",
        "Lkotlin/jvm/functions/Function0;",
        "timeOutCallback",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public a:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:Lw21/y;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:Lkotlin/jvm/functions/Function2;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Long;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    sget-object p2, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;->PRIZE:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;

    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;

    .line 5
    new-instance p2, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativePrize;

    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p2, p1, v0, v1, v0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativePrize;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 6
    new-instance p1, Lt21/a;

    invoke-direct {p1}, Lt21/a;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->c:Lkotlin/jvm/functions/Function2;

    .line 7
    new-instance p1, Lt21/b;

    invoke-direct {p1}, Lt21/b;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->d:Lkotlin/jvm/functions/Function1;

    .line 8
    new-instance p1, Lt21/c;

    invoke-direct {p1}, Lt21/c;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->e:Lkotlin/jvm/functions/Function0;

    .line 9
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->e()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic a(J)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->f(J)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b()Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->g()Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic c(JZ)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->d(JZ)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final d(JZ)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final f(J)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final g()Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object v0
.end method


# virtual methods
.method public final e()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative$a;->a:[I

    .line 4
    .line 5
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    aget v0, v1, v0

    .line 10
    .line 11
    const/4 v1, 0x1

    .line 12
    const/4 v2, 0x2

    .line 13
    const/4 v3, 0x0

    .line 14
    if-eq v0, v1, :cond_3

    .line 15
    .line 16
    if-eq v0, v2, :cond_2

    .line 17
    .line 18
    const/4 v1, 0x3

    .line 19
    if-eq v0, v1, :cond_1

    .line 20
    .line 21
    const/4 v1, 0x4

    .line 22
    if-ne v0, v1, :cond_0

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeTime;

    .line 25
    .line 26
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-direct {v0, v1, v3, v2, v3}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeTime;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 31
    .line 32
    .line 33
    goto :goto_0

    .line 34
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 35
    .line 36
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;

    .line 37
    .line 38
    new-instance v2, Ljava/lang/StringBuilder;

    .line 39
    .line 40
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 41
    .line 42
    .line 43
    const-string v3, "Unknown view type "

    .line 44
    .line 45
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 46
    .line 47
    .line 48
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 49
    .line 50
    .line 51
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 56
    .line 57
    .line 58
    throw v0

    .line 59
    :cond_1
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeTags;

    .line 60
    .line 61
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    invoke-direct {v0, v1, v3, v2, v3}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeTags;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 66
    .line 67
    .line 68
    goto :goto_0

    .line 69
    :cond_2
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates;

    .line 70
    .line 71
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    invoke-direct {v0, v1, v3, v2, v3}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 76
    .line 77
    .line 78
    goto :goto_0

    .line 79
    :cond_3
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativePrize;

    .line 80
    .line 81
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 82
    .line 83
    .line 84
    move-result-object v1

    .line 85
    invoke-direct {v0, v1, v3, v2, v3}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativePrize;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 86
    .line 87
    .line 88
    :goto_0
    invoke-virtual {p0}, Landroid/view/ViewGroup;->removeAllViews()V

    .line 89
    .line 90
    .line 91
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 92
    .line 93
    .line 94
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 95
    .line 96
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->d:Lkotlin/jvm/functions/Function1;

    .line 97
    .line 98
    invoke-interface {v0, v1}, Lw21/y;->setInfoButtonClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 99
    .line 100
    .line 101
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 102
    .line 103
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->c:Lkotlin/jvm/functions/Function2;

    .line 104
    .line 105
    invoke-interface {v0, v1}, Lw21/y;->setActionButtonClickListener(Lkotlin/jvm/functions/Function2;)V

    .line 106
    .line 107
    .line 108
    return-void
.end method

.method public getView()Landroid/view/View;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 2
    .line 3
    invoke-interface {v0}, Lw21/y;->getView()Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public setActionButton(Lv21/a;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lw21/y;->setActionButton(Lv21/a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setActionButtonClickListener(Lkotlin/jvm/functions/Function2;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->c:Lkotlin/jvm/functions/Function2;

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 4
    .line 5
    invoke-interface {v0, p1}, Lw21/y;->setActionButtonClickListener(Lkotlin/jvm/functions/Function2;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public setAdditionalTag(Lk21/a;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lw21/y;->setAdditionalTag(Lk21/a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setBannerImage(LL11/c;LL11/c;)V
    .locals 1
    .param p1    # LL11/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 2
    .line 3
    invoke-interface {v0, p1, p2}, Lw21/y;->setBannerImage(LL11/c;LL11/c;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setInfoButton(Lv21/h;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lw21/y;->setInfoButton(Lv21/h;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setInfoButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Long;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->d:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 4
    .line 5
    invoke-interface {v0, p1}, Lw21/y;->setInfoButtonClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public setMainTag(Lk21/m;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lw21/y;->setMainTag(Lk21/m;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setModel(Lv21/e;)V
    .locals 1
    .param p1    # Lv21/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lw21/y;->setModel(Lv21/e;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setOnTimerExpiredListener(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->e:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 4
    .line 5
    invoke-interface {v0, p1}, Lw21/y;->setOnTimerExpiredListener(Lkotlin/jvm/functions/Function0;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public setPeriodDates(Lv21/g;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lw21/y;->setPeriodDates(Lv21/g;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setPrizeLabel(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lw21/y;->setPrizeLabel(Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setPrizeValue(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lw21/y;->setPrizeValue(Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setStyleName(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;->Companion:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType$a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType$a;->a(Ljava/lang/String;)Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;

    .line 8
    .line 9
    if-ne v0, p1, :cond_0

    .line 10
    .line 11
    return-void

    .line 12
    :cond_0
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;

    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->e()V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public final setStyleType(Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;

    .line 2
    .line 3
    if-ne v0, p1, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;

    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->e()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public setTimer(Lv21/t;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lw21/y;->setTimer(Lv21/t;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setTitle(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/DSAggregatorTournamentCardsNative;->b:Lw21/y;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lw21/y;->setTitle(Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
