.class public final LGE0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LGE0/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LGE0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LGE0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LGE0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LQW0/c;LEN0/f;Lf8/g;LwX0/c;Ljava/lang/String;JLorg/xbet/ui_common/utils/M;LHX0/e;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Li8/j;Lc8/h;LiS/a;)LGE0/c;
    .locals 20

    .line 1
    invoke-static/range {p1 .. p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static/range {p2 .. p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p7}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 21
    .line 22
    .line 23
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    invoke-static/range {p10 .. p10}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    invoke-static/range {p12 .. p12}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    invoke-static/range {p13 .. p13}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    invoke-static/range {p14 .. p14}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    invoke-static/range {p15 .. p15}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    invoke-static/range {p16 .. p16}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    invoke-static/range {p17 .. p17}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    invoke-static/range {p18 .. p18}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    new-instance v1, LGE0/a$b;

    .line 57
    .line 58
    invoke-static/range {p6 .. p7}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 59
    .line 60
    .line 61
    move-result-object v7

    .line 62
    const/16 v19, 0x0

    .line 63
    .line 64
    move-object/from16 v2, p1

    .line 65
    .line 66
    move-object/from16 v3, p2

    .line 67
    .line 68
    move-object/from16 v4, p3

    .line 69
    .line 70
    move-object/from16 v5, p4

    .line 71
    .line 72
    move-object/from16 v6, p5

    .line 73
    .line 74
    move-object/from16 v8, p8

    .line 75
    .line 76
    move-object/from16 v9, p9

    .line 77
    .line 78
    move-object/from16 v10, p10

    .line 79
    .line 80
    move-object/from16 v11, p11

    .line 81
    .line 82
    move-object/from16 v12, p12

    .line 83
    .line 84
    move-object/from16 v13, p13

    .line 85
    .line 86
    move-object/from16 v14, p14

    .line 87
    .line 88
    move-object/from16 v15, p15

    .line 89
    .line 90
    move-object/from16 v16, p16

    .line 91
    .line 92
    move-object/from16 v17, p17

    .line 93
    .line 94
    move-object/from16 v18, p18

    .line 95
    .line 96
    invoke-direct/range {v1 .. v19}, LGE0/a$b;-><init>(LQW0/c;LEN0/f;Lf8/g;LwX0/c;Ljava/lang/String;Ljava/lang/Long;Lorg/xbet/ui_common/utils/M;LHX0/e;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Li8/j;Lc8/h;LiS/a;LGE0/b;)V

    .line 97
    .line 98
    .line 99
    return-object v1
.end method
