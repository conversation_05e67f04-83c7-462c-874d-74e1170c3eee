.class public final LtD0/a;
.super LkY0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "LkY0/a<",
        "Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0001\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B%\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u000c\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000c\u001a\u00020\u000bH\u0016\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u00a8\u0006\u0010"
    }
    d2 = {
        "LtD0/a;",
        "LkY0/a;",
        "Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;",
        "Landroidx/fragment/app/FragmentManager;",
        "childFragmentManager",
        "Landroidx/lifecycle/Lifecycle;",
        "lifecycle",
        "",
        "pages",
        "<init>",
        "(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;)V",
        "",
        "position",
        "Landroidx/fragment/app/Fragment;",
        "p",
        "(I)Landroidx/fragment/app/Fragment;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final o:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget v0, LkY0/a;->n:I

    .line 2
    .line 3
    sput v0, LtD0/a;->o:I

    .line 4
    .line 5
    return-void
.end method

.method public constructor <init>(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;)V
    .locals 0
    .param p1    # Landroidx/fragment/app/FragmentManager;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroidx/lifecycle/Lifecycle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/fragment/app/FragmentManager;",
            "Landroidx/lifecycle/Lifecycle;",
            "Ljava/util/List<",
            "+",
            "Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2, p3}, LkY0/a;-><init>(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public p(I)Landroidx/fragment/app/Fragment;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    if-eqz p1, :cond_2

    .line 2
    .line 3
    const/4 v0, 0x1

    .line 4
    if-eq p1, v0, :cond_1

    .line 5
    .line 6
    const/4 v0, 0x2

    .line 7
    if-ne p1, v0, :cond_0

    .line 8
    .line 9
    sget-object p1, Lorg/xbet/statistic/cycling/impl/cycling_player/presentation/fragment/CyclingPlayerStatisticViewPagerFragment;->n0:Lorg/xbet/statistic/cycling/impl/cycling_player/presentation/fragment/CyclingPlayerStatisticViewPagerFragment$a;

    .line 10
    .line 11
    sget-object v0, Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;->THIRD:Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;

    .line 12
    .line 13
    invoke-virtual {p1, v0}, Lorg/xbet/statistic/cycling/impl/cycling_player/presentation/fragment/CyclingPlayerStatisticViewPagerFragment$a;->a(Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;)Lorg/xbet/statistic/cycling/impl/cycling_player/presentation/fragment/CyclingPlayerStatisticViewPagerFragment;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    return-object p1

    .line 18
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    .line 19
    .line 20
    const-string v0, "undefined team position"

    .line 21
    .line 22
    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 23
    .line 24
    .line 25
    throw p1

    .line 26
    :cond_1
    sget-object p1, Lorg/xbet/statistic/cycling/impl/cycling_player/presentation/fragment/CyclingPlayerStatisticViewPagerFragment;->n0:Lorg/xbet/statistic/cycling/impl/cycling_player/presentation/fragment/CyclingPlayerStatisticViewPagerFragment$a;

    .line 27
    .line 28
    sget-object v0, Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;->SECOND:Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;

    .line 29
    .line 30
    invoke-virtual {p1, v0}, Lorg/xbet/statistic/cycling/impl/cycling_player/presentation/fragment/CyclingPlayerStatisticViewPagerFragment$a;->a(Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;)Lorg/xbet/statistic/cycling/impl/cycling_player/presentation/fragment/CyclingPlayerStatisticViewPagerFragment;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    return-object p1

    .line 35
    :cond_2
    sget-object p1, Lorg/xbet/statistic/cycling/impl/cycling_player/presentation/fragment/CyclingPlayerStatisticViewPagerFragment;->n0:Lorg/xbet/statistic/cycling/impl/cycling_player/presentation/fragment/CyclingPlayerStatisticViewPagerFragment$a;

    .line 36
    .line 37
    sget-object v0, Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;->FIRST:Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;

    .line 38
    .line 39
    invoke-virtual {p1, v0}, Lorg/xbet/statistic/cycling/impl/cycling_player/presentation/fragment/CyclingPlayerStatisticViewPagerFragment$a;->a(Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;)Lorg/xbet/statistic/cycling/impl/cycling_player/presentation/fragment/CyclingPlayerStatisticViewPagerFragment;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    return-object p1
.end method
