.class public final LoR0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LoR0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LoR0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LoR0/a$b$a;
    }
.end annotation


# instance fields
.field public final a:LoR0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LlR0/b;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/winter_games/impl/personal_statistic/data/repository/PersonalStatisticRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LrR0/a;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/winter_games/impl/personal_statistic/presentation/viewmodel/PersonalStatisticViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Li8/m;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LoR0/a$b;->a:LoR0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p8}, LoR0/a$b;->b(LQW0/c;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Li8/m;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Li8/m;Lc8/h;LoR0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p8}, LoR0/a$b;-><init>(LQW0/c;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Li8/m;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/winter_games/impl/personal_statistic/presentation/fragment/PersonalStatisticFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LoR0/a$b;->c(Lorg/xbet/statistic/winter_games/impl/personal_statistic/presentation/fragment/PersonalStatisticFragment;)Lorg/xbet/statistic/winter_games/impl/personal_statistic/presentation/fragment/PersonalStatisticFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Li8/m;Lc8/h;)V
    .locals 0

    .line 1
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p5

    .line 5
    iput-object p5, p0, LoR0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p5}, LlR0/c;->a(LBc/a;)LlR0/c;

    .line 8
    .line 9
    .line 10
    move-result-object p5

    .line 11
    iput-object p5, p0, LoR0/a$b;->c:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 14
    .line 15
    .line 16
    move-result-object p5

    .line 17
    iput-object p5, p0, LoR0/a$b;->d:Ldagger/internal/h;

    .line 18
    .line 19
    new-instance p5, LoR0/a$b$a;

    .line 20
    .line 21
    invoke-direct {p5, p1}, LoR0/a$b$a;-><init>(LQW0/c;)V

    .line 22
    .line 23
    .line 24
    iput-object p5, p0, LoR0/a$b;->e:Ldagger/internal/h;

    .line 25
    .line 26
    iget-object p1, p0, LoR0/a$b;->c:Ldagger/internal/h;

    .line 27
    .line 28
    iget-object p7, p0, LoR0/a$b;->d:Ldagger/internal/h;

    .line 29
    .line 30
    invoke-static {p1, p7, p5}, Lorg/xbet/statistic/winter_games/impl/personal_statistic/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/winter_games/impl/personal_statistic/data/repository/a;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    iput-object p1, p0, LoR0/a$b;->f:Ldagger/internal/h;

    .line 35
    .line 36
    invoke-static {p1}, LrR0/b;->a(LBc/a;)LrR0/b;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    iput-object p1, p0, LoR0/a$b;->g:Ldagger/internal/h;

    .line 41
    .line 42
    invoke-static {p2}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    iput-object p1, p0, LoR0/a$b;->h:Ldagger/internal/h;

    .line 47
    .line 48
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iput-object p1, p0, LoR0/a$b;->i:Ldagger/internal/h;

    .line 53
    .line 54
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    iput-object p1, p0, LoR0/a$b;->j:Ldagger/internal/h;

    .line 59
    .line 60
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    iput-object p1, p0, LoR0/a$b;->k:Ldagger/internal/h;

    .line 65
    .line 66
    iget-object p2, p0, LoR0/a$b;->g:Ldagger/internal/h;

    .line 67
    .line 68
    iget-object p3, p0, LoR0/a$b;->h:Ldagger/internal/h;

    .line 69
    .line 70
    iget-object p4, p0, LoR0/a$b;->i:Ldagger/internal/h;

    .line 71
    .line 72
    iget-object p5, p0, LoR0/a$b;->j:Ldagger/internal/h;

    .line 73
    .line 74
    invoke-static {p2, p3, p4, p5, p1}, Lorg/xbet/statistic/winter_games/impl/personal_statistic/presentation/viewmodel/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/winter_games/impl/personal_statistic/presentation/viewmodel/a;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    iput-object p1, p0, LoR0/a$b;->l:Ldagger/internal/h;

    .line 79
    .line 80
    return-void
.end method

.method public final c(Lorg/xbet/statistic/winter_games/impl/personal_statistic/presentation/fragment/PersonalStatisticFragment;)Lorg/xbet/statistic/winter_games/impl/personal_statistic/presentation/fragment/PersonalStatisticFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LoR0/a$b;->e()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/winter_games/impl/personal_statistic/presentation/fragment/d;->a(Lorg/xbet/statistic/winter_games/impl/personal_statistic/presentation/fragment/PersonalStatisticFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final d()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/statistic/winter_games/impl/personal_statistic/presentation/viewmodel/PersonalStatisticViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LoR0/a$b;->l:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final e()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LoR0/a$b;->d()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
