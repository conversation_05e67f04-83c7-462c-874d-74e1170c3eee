.class final Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.games_slider.impl.presentation.delegates.OneXGameCardViewModelDelegateImpl$openGame$3"
    f = "OneXGameCardViewModelDelegateImpl.kt"
    l = {
        0x70
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->T(JLcom/xbet/onexuser/domain/entity/onexgame/GpResult;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $typeValue:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    iput-object p2, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->$typeValue:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method

.method public static synthetic a(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->c(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)Lkotlin/Unit;
    .locals 0

    .line 1
    check-cast p1, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

    .line 2
    .line 3
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->E(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V

    .line 4
    .line 5
    .line 6
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p0
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;

    iget-object v0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    iget-object v1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->$typeValue:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;-><init>(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->o(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)Lw30/i;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    iget-object v1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->$typeValue:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 34
    .line 35
    invoke-static {v1}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 36
    .line 37
    .line 38
    move-result-wide v3

    .line 39
    iput v2, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->label:I

    .line 40
    .line 41
    invoke-interface {p1, v3, v4, p0}, Lw30/i;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    if-ne p1, v0, :cond_2

    .line 46
    .line 47
    return-object v0

    .line 48
    :cond_2
    :goto_0
    check-cast p1, Ljava/lang/Boolean;

    .line 49
    .line 50
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 51
    .line 52
    .line 53
    move-result p1

    .line 54
    xor-int/2addr p1, v2

    .line 55
    iget-object v0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    .line 56
    .line 57
    invoke-static {v0}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->x(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)LwX0/C;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    invoke-virtual {v0}, LwX0/D;->a()LwX0/c;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    if-eqz v0, :cond_3

    .line 66
    .line 67
    iget-object v1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;

    .line 68
    .line 69
    iget-object v2, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;->$typeValue:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 70
    .line 71
    new-instance v3, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/a;

    .line 72
    .line 73
    invoke-direct {v3, v1, v2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/a;-><init>(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)V

    .line 74
    .line 75
    .line 76
    invoke-virtual {v0, p1, v3}, LwX0/c;->n(ZLkotlin/jvm/functions/Function0;)V

    .line 77
    .line 78
    .line 79
    :cond_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 80
    .line 81
    return-object p1
.end method
