.class public final Lorg/xbet/spin_and_win/presentation/game/p;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/u;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_info/H;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_info/q;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bonus/e;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/d;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/l;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/o;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lez0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lez0/b;",
            ">;"
        }
    .end annotation
.end field

.field public final l:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lez0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final m:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lez0/d;",
            ">;"
        }
    .end annotation
.end field

.field public final n:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lez0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final o:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lez0/f;",
            ">;"
        }
    .end annotation
.end field

.field public final p:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lez0/g;",
            ">;"
        }
    .end annotation
.end field

.field public final q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lez0/h;",
            ">;"
        }
    .end annotation
.end field

.field public final r:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lez0/i;",
            ">;"
        }
    .end annotation
.end field

.field public final s:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lez0/j;",
            ">;"
        }
    .end annotation
.end field

.field public final t:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/h;",
            ">;"
        }
    .end annotation
.end field

.field public final u:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lez0/k;",
            ">;"
        }
    .end annotation
.end field

.field public final v:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final w:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LWv/b;",
            ">;"
        }
    .end annotation
.end field

.field public final x:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/d;",
            ">;"
        }
    .end annotation
.end field

.field public final y:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final z:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/l;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/u;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_info/H;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_info/q;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bonus/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/d;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/l;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/o;",
            ">;",
            "LBc/a<",
            "Lez0/a;",
            ">;",
            "LBc/a<",
            "Lez0/b;",
            ">;",
            "LBc/a<",
            "Lez0/c;",
            ">;",
            "LBc/a<",
            "Lez0/d;",
            ">;",
            "LBc/a<",
            "Lez0/e;",
            ">;",
            "LBc/a<",
            "Lez0/f;",
            ">;",
            "LBc/a<",
            "Lez0/g;",
            ">;",
            "LBc/a<",
            "Lez0/h;",
            ">;",
            "LBc/a<",
            "Lez0/i;",
            ">;",
            "LBc/a<",
            "Lez0/j;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/h;",
            ">;",
            "LBc/a<",
            "Lez0/k;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "LWv/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/d;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/l;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/p;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/spin_and_win/presentation/game/p;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/spin_and_win/presentation/game/p;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/spin_and_win/presentation/game/p;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/spin_and_win/presentation/game/p;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/spin_and_win/presentation/game/p;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/spin_and_win/presentation/game/p;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/spin_and_win/presentation/game/p;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/spin_and_win/presentation/game/p;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/spin_and_win/presentation/game/p;->j:LBc/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/spin_and_win/presentation/game/p;->k:LBc/a;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/spin_and_win/presentation/game/p;->l:LBc/a;

    .line 27
    .line 28
    iput-object p13, p0, Lorg/xbet/spin_and_win/presentation/game/p;->m:LBc/a;

    .line 29
    .line 30
    iput-object p14, p0, Lorg/xbet/spin_and_win/presentation/game/p;->n:LBc/a;

    .line 31
    .line 32
    iput-object p15, p0, Lorg/xbet/spin_and_win/presentation/game/p;->o:LBc/a;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/p;->p:LBc/a;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/p;->q:LBc/a;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/p;->r:LBc/a;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/p;->s:LBc/a;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/p;->t:LBc/a;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/p;->u:LBc/a;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/p;->v:LBc/a;

    .line 61
    .line 62
    move-object/from16 p1, p23

    .line 63
    .line 64
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/p;->w:LBc/a;

    .line 65
    .line 66
    move-object/from16 p1, p24

    .line 67
    .line 68
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/p;->x:LBc/a;

    .line 69
    .line 70
    move-object/from16 p1, p25

    .line 71
    .line 72
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/p;->y:LBc/a;

    .line 73
    .line 74
    move-object/from16 p1, p26

    .line 75
    .line 76
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/p;->z:LBc/a;

    .line 77
    .line 78
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/spin_and_win/presentation/game/p;
    .locals 27
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/u;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_info/H;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_info/q;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bonus/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/d;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/l;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/o;",
            ">;",
            "LBc/a<",
            "Lez0/a;",
            ">;",
            "LBc/a<",
            "Lez0/b;",
            ">;",
            "LBc/a<",
            "Lez0/c;",
            ">;",
            "LBc/a<",
            "Lez0/d;",
            ">;",
            "LBc/a<",
            "Lez0/e;",
            ">;",
            "LBc/a<",
            "Lez0/f;",
            ">;",
            "LBc/a<",
            "Lez0/g;",
            ">;",
            "LBc/a<",
            "Lez0/h;",
            ">;",
            "LBc/a<",
            "Lez0/i;",
            ">;",
            "LBc/a<",
            "Lez0/j;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/h;",
            ">;",
            "LBc/a<",
            "Lez0/k;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "LWv/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/d;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/l;",
            ">;)",
            "Lorg/xbet/spin_and_win/presentation/game/p;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/p;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    invoke-direct/range {v0 .. v26}, Lorg/xbet/spin_and_win/presentation/game/p;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 56
    .line 57
    .line 58
    return-object v0
.end method

.method public static c(LwX0/c;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/game_info/H;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/core/domain/usecases/game_info/q;Lorg/xbet/core/domain/usecases/bonus/e;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/bet/o;Lez0/a;Lez0/b;Lez0/c;Lez0/d;Lez0/e;Lez0/f;Lez0/g;Lez0/h;Lez0/i;Lez0/j;Lorg/xbet/core/domain/usecases/game_state/h;Lez0/k;Lm8/a;LWv/b;Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;Lorg/xbet/core/domain/usecases/bet/l;)Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;
    .locals 28

    .line 1
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    invoke-direct/range {v0 .. v27}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;-><init>(LwX0/c;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/game_info/H;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/core/domain/usecases/game_info/q;Lorg/xbet/core/domain/usecases/bonus/e;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/bet/o;Lez0/a;Lez0/b;Lez0/c;Lez0/d;Lez0/e;Lez0/f;Lez0/g;Lez0/h;Lez0/i;Lez0/j;Lorg/xbet/core/domain/usecases/game_state/h;Lez0/k;Lm8/a;LWv/b;Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;Lorg/xbet/core/domain/usecases/bet/l;)V

    .line 58
    .line 59
    .line 60
    return-object v0
.end method


# virtual methods
.method public b(LwX0/c;)Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;
    .locals 29

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->a:LBc/a;

    .line 4
    .line 5
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    move-object v3, v1

    .line 10
    check-cast v3, Lorg/xbet/core/domain/usecases/u;

    .line 11
    .line 12
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->b:LBc/a;

    .line 13
    .line 14
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    move-object v4, v1

    .line 19
    check-cast v4, Lorg/xbet/core/domain/usecases/game_info/H;

    .line 20
    .line 21
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->c:LBc/a;

    .line 22
    .line 23
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    move-object v5, v1

    .line 28
    check-cast v5, Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;

    .line 29
    .line 30
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->d:LBc/a;

    .line 31
    .line 32
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    move-object v6, v1

    .line 37
    check-cast v6, Lorg/xbet/core/domain/usecases/game_info/q;

    .line 38
    .line 39
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->e:LBc/a;

    .line 40
    .line 41
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    move-object v7, v1

    .line 46
    check-cast v7, Lorg/xbet/core/domain/usecases/bonus/e;

    .line 47
    .line 48
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->f:LBc/a;

    .line 49
    .line 50
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    move-object v8, v1

    .line 55
    check-cast v8, Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 56
    .line 57
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->g:LBc/a;

    .line 58
    .line 59
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    move-object v9, v1

    .line 64
    check-cast v9, Lorg/xbet/core/domain/usecases/d;

    .line 65
    .line 66
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->h:LBc/a;

    .line 67
    .line 68
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    move-object v10, v1

    .line 73
    check-cast v10, Lorg/xbet/core/domain/usecases/game_state/l;

    .line 74
    .line 75
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->i:LBc/a;

    .line 76
    .line 77
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    move-object v11, v1

    .line 82
    check-cast v11, Lorg/xbet/core/domain/usecases/bet/o;

    .line 83
    .line 84
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->j:LBc/a;

    .line 85
    .line 86
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    move-object v12, v1

    .line 91
    check-cast v12, Lez0/a;

    .line 92
    .line 93
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->k:LBc/a;

    .line 94
    .line 95
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object v1

    .line 99
    move-object v13, v1

    .line 100
    check-cast v13, Lez0/b;

    .line 101
    .line 102
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->l:LBc/a;

    .line 103
    .line 104
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    move-object v14, v1

    .line 109
    check-cast v14, Lez0/c;

    .line 110
    .line 111
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->m:LBc/a;

    .line 112
    .line 113
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object v1

    .line 117
    move-object v15, v1

    .line 118
    check-cast v15, Lez0/d;

    .line 119
    .line 120
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->n:LBc/a;

    .line 121
    .line 122
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v1

    .line 126
    move-object/from16 v16, v1

    .line 127
    .line 128
    check-cast v16, Lez0/e;

    .line 129
    .line 130
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->o:LBc/a;

    .line 131
    .line 132
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 133
    .line 134
    .line 135
    move-result-object v1

    .line 136
    move-object/from16 v17, v1

    .line 137
    .line 138
    check-cast v17, Lez0/f;

    .line 139
    .line 140
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->p:LBc/a;

    .line 141
    .line 142
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 143
    .line 144
    .line 145
    move-result-object v1

    .line 146
    move-object/from16 v18, v1

    .line 147
    .line 148
    check-cast v18, Lez0/g;

    .line 149
    .line 150
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->q:LBc/a;

    .line 151
    .line 152
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 153
    .line 154
    .line 155
    move-result-object v1

    .line 156
    move-object/from16 v19, v1

    .line 157
    .line 158
    check-cast v19, Lez0/h;

    .line 159
    .line 160
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->r:LBc/a;

    .line 161
    .line 162
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 163
    .line 164
    .line 165
    move-result-object v1

    .line 166
    move-object/from16 v20, v1

    .line 167
    .line 168
    check-cast v20, Lez0/i;

    .line 169
    .line 170
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->s:LBc/a;

    .line 171
    .line 172
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 173
    .line 174
    .line 175
    move-result-object v1

    .line 176
    move-object/from16 v21, v1

    .line 177
    .line 178
    check-cast v21, Lez0/j;

    .line 179
    .line 180
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->t:LBc/a;

    .line 181
    .line 182
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 183
    .line 184
    .line 185
    move-result-object v1

    .line 186
    move-object/from16 v22, v1

    .line 187
    .line 188
    check-cast v22, Lorg/xbet/core/domain/usecases/game_state/h;

    .line 189
    .line 190
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->u:LBc/a;

    .line 191
    .line 192
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 193
    .line 194
    .line 195
    move-result-object v1

    .line 196
    move-object/from16 v23, v1

    .line 197
    .line 198
    check-cast v23, Lez0/k;

    .line 199
    .line 200
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->v:LBc/a;

    .line 201
    .line 202
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 203
    .line 204
    .line 205
    move-result-object v1

    .line 206
    move-object/from16 v24, v1

    .line 207
    .line 208
    check-cast v24, Lm8/a;

    .line 209
    .line 210
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->w:LBc/a;

    .line 211
    .line 212
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 213
    .line 214
    .line 215
    move-result-object v1

    .line 216
    move-object/from16 v25, v1

    .line 217
    .line 218
    check-cast v25, LWv/b;

    .line 219
    .line 220
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->x:LBc/a;

    .line 221
    .line 222
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 223
    .line 224
    .line 225
    move-result-object v1

    .line 226
    move-object/from16 v26, v1

    .line 227
    .line 228
    check-cast v26, Lorg/xbet/core/domain/usecases/bet/d;

    .line 229
    .line 230
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->y:LBc/a;

    .line 231
    .line 232
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 233
    .line 234
    .line 235
    move-result-object v1

    .line 236
    move-object/from16 v27, v1

    .line 237
    .line 238
    check-cast v27, Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 239
    .line 240
    iget-object v1, v0, Lorg/xbet/spin_and_win/presentation/game/p;->z:LBc/a;

    .line 241
    .line 242
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 243
    .line 244
    .line 245
    move-result-object v1

    .line 246
    move-object/from16 v28, v1

    .line 247
    .line 248
    check-cast v28, Lorg/xbet/core/domain/usecases/bet/l;

    .line 249
    .line 250
    move-object/from16 v2, p1

    .line 251
    .line 252
    invoke-static/range {v2 .. v28}, Lorg/xbet/spin_and_win/presentation/game/p;->c(LwX0/c;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/game_info/H;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/core/domain/usecases/game_info/q;Lorg/xbet/core/domain/usecases/bonus/e;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/bet/o;Lez0/a;Lez0/b;Lez0/c;Lez0/d;Lez0/e;Lez0/f;Lez0/g;Lez0/h;Lez0/i;Lez0/j;Lorg/xbet/core/domain/usecases/game_state/h;Lez0/k;Lm8/a;LWv/b;Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;Lorg/xbet/core/domain/usecases/bet/l;)Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 253
    .line 254
    .line 255
    move-result-object v1

    .line 256
    return-object v1
.end method
