.class public final Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt;->d(LB4/a;LHy0/b;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function2<",
        "Landroidx/compose/runtime/j;",
        "Ljava/lang/Integer;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LB4/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LB4/a<",
            "LIy0/f;",
            "LGq0/u1;",
            ">;"
        }
    .end annotation
.end field

.field public final synthetic b:LHy0/b;


# direct methods
.method public constructor <init>(LB4/a;LHy0/b;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "LIy0/f;",
            "LGq0/u1;",
            ">;",
            "LHy0/b;",
            ")V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;->a:LB4/a;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;->b:LHy0/b;

    .line 4
    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public static synthetic a(LHy0/b;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;->d(LHy0/b;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LHy0/b;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;->e(LHy0/b;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final d(LHy0/b;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LIy0/f;

    .line 6
    .line 7
    invoke-virtual {v0}, LIy0/f;->getId()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    check-cast p1, LIy0/f;

    .line 16
    .line 17
    invoke-virtual {p1}, LIy0/f;->e()Ljava/lang/Integer;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-interface {p0, v0, p1}, LHy0/b;->g1(ILjava/lang/Integer;)V

    .line 22
    .line 23
    .line 24
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 25
    .line 26
    return-object p0
.end method

.method public static final e(LHy0/b;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LIy0/f;

    .line 6
    .line 7
    invoke-virtual {v0}, LIy0/f;->getId()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    check-cast p1, LIy0/f;

    .line 16
    .line 17
    invoke-virtual {p1}, LIy0/f;->e()Ljava/lang/Integer;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-interface {p0, v0, p1}, LHy0/b;->n3(ILjava/lang/Integer;)V

    .line 22
    .line 23
    .line 24
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 25
    .line 26
    return-object p0
.end method


# virtual methods
.method public final c(Landroidx/compose/runtime/j;I)V
    .locals 9

    .line 1
    and-int/lit8 v0, p2, 0x3

    .line 2
    .line 3
    const/4 v1, 0x2

    .line 4
    if-ne v0, v1, :cond_1

    .line 5
    .line 6
    invoke-interface {p1}, Landroidx/compose/runtime/j;->c()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-nez v0, :cond_0

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    invoke-interface {p1}, Landroidx/compose/runtime/j;->n()V

    .line 14
    .line 15
    .line 16
    return-void

    .line 17
    :cond_1
    :goto_0
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-eqz v0, :cond_2

    .line 22
    .line 23
    const/4 v0, -0x1

    .line 24
    const-string v1, "org.xbet.special_event.impl.who_win.presentation.adapter.viewholder.bindMarket.<anonymous> (OpponentViewHolder.kt:52)"

    .line 25
    .line 26
    const v2, 0x4f5aaba2

    .line 27
    .line 28
    .line 29
    invoke-static {v2, p2, v0, v1}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 30
    .line 31
    .line 32
    :cond_2
    iget-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;->a:LB4/a;

    .line 33
    .line 34
    invoke-virtual {p2}, LB4/a;->i()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object p2

    .line 38
    check-cast p2, LIy0/f;

    .line 39
    .line 40
    invoke-virtual {p2}, LIy0/f;->j()LIy0/f$b$a;

    .line 41
    .line 42
    .line 43
    move-result-object p2

    .line 44
    invoke-virtual {p2}, LIy0/f$b$a;->a()Lu31/a$b;

    .line 45
    .line 46
    .line 47
    move-result-object p2

    .line 48
    const/4 v0, 0x0

    .line 49
    invoke-static {p2, p1, v0}, Landroidx/compose/runtime/i1;->p(Ljava/lang/Object;Landroidx/compose/runtime/j;I)Landroidx/compose/runtime/r1;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    sget-object p2, LIy0/f;->f:LIy0/f$a;

    .line 54
    .line 55
    invoke-virtual {p2}, LIy0/f$a;->a()Lu31/b$b;

    .line 56
    .line 57
    .line 58
    move-result-object v3

    .line 59
    const p2, -0x615d173a

    .line 60
    .line 61
    .line 62
    invoke-interface {p1, p2}, Landroidx/compose/runtime/j;->t(I)V

    .line 63
    .line 64
    .line 65
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;->b:LHy0/b;

    .line 66
    .line 67
    invoke-interface {p1, v0}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 68
    .line 69
    .line 70
    move-result v0

    .line 71
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;->a:LB4/a;

    .line 72
    .line 73
    invoke-interface {p1, v1}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 74
    .line 75
    .line 76
    move-result v1

    .line 77
    or-int/2addr v0, v1

    .line 78
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;->b:LHy0/b;

    .line 79
    .line 80
    iget-object v4, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;->a:LB4/a;

    .line 81
    .line 82
    invoke-interface {p1}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 83
    .line 84
    .line 85
    move-result-object v5

    .line 86
    if-nez v0, :cond_3

    .line 87
    .line 88
    sget-object v0, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 89
    .line 90
    invoke-virtual {v0}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    if-ne v5, v0, :cond_4

    .line 95
    .line 96
    :cond_3
    new-instance v5, LJy0/p;

    .line 97
    .line 98
    invoke-direct {v5, v1, v4}, LJy0/p;-><init>(LHy0/b;LB4/a;)V

    .line 99
    .line 100
    .line 101
    invoke-interface {p1, v5}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 102
    .line 103
    .line 104
    :cond_4
    move-object v4, v5

    .line 105
    check-cast v4, Lkotlin/jvm/functions/Function0;

    .line 106
    .line 107
    invoke-interface {p1}, Landroidx/compose/runtime/j;->q()V

    .line 108
    .line 109
    .line 110
    invoke-interface {p1, p2}, Landroidx/compose/runtime/j;->t(I)V

    .line 111
    .line 112
    .line 113
    iget-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;->b:LHy0/b;

    .line 114
    .line 115
    invoke-interface {p1, p2}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 116
    .line 117
    .line 118
    move-result p2

    .line 119
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;->a:LB4/a;

    .line 120
    .line 121
    invoke-interface {p1, v0}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 122
    .line 123
    .line 124
    move-result v0

    .line 125
    or-int/2addr p2, v0

    .line 126
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;->b:LHy0/b;

    .line 127
    .line 128
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;->a:LB4/a;

    .line 129
    .line 130
    invoke-interface {p1}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    move-result-object v5

    .line 134
    if-nez p2, :cond_5

    .line 135
    .line 136
    sget-object p2, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 137
    .line 138
    invoke-virtual {p2}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 139
    .line 140
    .line 141
    move-result-object p2

    .line 142
    if-ne v5, p2, :cond_6

    .line 143
    .line 144
    :cond_5
    new-instance v5, LJy0/q;

    .line 145
    .line 146
    invoke-direct {v5, v0, v1}, LJy0/q;-><init>(LHy0/b;LB4/a;)V

    .line 147
    .line 148
    .line 149
    invoke-interface {p1, v5}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 150
    .line 151
    .line 152
    :cond_6
    check-cast v5, Lkotlin/jvm/functions/Function0;

    .line 153
    .line 154
    invoke-interface {p1}, Landroidx/compose/runtime/j;->q()V

    .line 155
    .line 156
    .line 157
    sget p2, Lu31/b$b;->b:I

    .line 158
    .line 159
    shl-int/lit8 v7, p2, 0x6

    .line 160
    .line 161
    const/4 v8, 0x1

    .line 162
    const/4 v1, 0x0

    .line 163
    move-object v6, p1

    .line 164
    invoke-static/range {v1 .. v8}, Lt31/a;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/r1;Lu31/b;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V

    .line 165
    .line 166
    .line 167
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 168
    .line 169
    .line 170
    move-result p1

    .line 171
    if-eqz p1, :cond_7

    .line 172
    .line 173
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 174
    .line 175
    .line 176
    :cond_7
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/runtime/j;

    .line 2
    .line 3
    check-cast p2, Ljava/lang/Number;

    .line 4
    .line 5
    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;->c(Landroidx/compose/runtime/j;I)V

    .line 10
    .line 11
    .line 12
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p1
.end method
