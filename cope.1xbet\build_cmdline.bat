@echo off
REM سكريبت تجميع Apple Fortune Helper باستخدام Android Command Line Tools
REM Build script for Apple Fortune Helper using Android Command Line Tools

echo 🔧 Building Apple Fortune Helper with Android Command Line Tools...
echo.

REM تحديد المتغيرات
set PROJECT_DIR=%cd%
set CMDLINE_TOOLS=C:\Users\<USER>\Downloads\commandlinetools-win-13114758_latest\cmdline-tools\bin
set BUILD_DIR=%PROJECT_DIR%\build_output

REM التحقق من وجود cmdline-tools
if not exist "%CMDLINE_TOOLS%" (
    echo ❌ Command line tools not found at: %CMDLINE_TOOLS%
    echo Please check the path and try again.
    pause
    exit /b 1
)

echo ✅ Command line tools found at: %CMDLINE_TOOLS%

REM إضافة cmdline-tools إلى PATH مؤقتاً
set PATH=%CMDLINE_TOOLS%;%PATH%

REM التحقق من متغيرات البيئة المطلوبة
echo 📋 Checking environment variables...

if "%ANDROID_HOME%"=="" (
    echo ⚠️ ANDROID_HOME not set. Trying to detect...
    for /d %%i in ("C:\Users\<USER>\AppData\Local\Android\Sdk") do set ANDROID_HOME=%%i
    if "%ANDROID_HOME%"=="" (
        echo ❌ ANDROID_HOME not found. Please set it manually.
        pause
        exit /b 1
    )
)

echo ✅ ANDROID_HOME: %ANDROID_HOME%

REM التحقق من وجود Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java not found. Please install Java JDK 8 or higher.
    pause
    exit /b 1
)

echo ✅ Java found

REM إنشاء مجلد البناء
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"

echo.
echo 🏗️ Starting build process...
echo.

REM الخطوة 1: تنظيف المشروع
echo 📦 Step 1: Cleaning project...
if exist "app\build" rmdir /s /q "app\build"
if exist "build" rmdir /s /q "build"

REM الخطوة 2: التحقق من SDK وتحديث المكونات
echo 📱 Step 2: Checking Android SDK components...

REM قائمة بالمكونات المطلوبة
set REQUIRED_COMPONENTS=platforms;android-34 build-tools;34.0.0 platform-tools

echo Installing required SDK components...
for %%c in (%REQUIRED_COMPONENTS%) do (
    echo Installing %%c...
    sdkmanager "%%c" --sdk_root="%ANDROID_HOME%"
)

REM الخطوة 3: قبول التراخيص
echo 📜 Step 3: Accepting licenses...
echo y | sdkmanager --licenses --sdk_root="%ANDROID_HOME%"

REM الخطوة 4: تجميع المشروع
echo 🔨 Step 4: Building project...

REM التحقق من وجود gradlew
if not exist "gradlew.bat" (
    echo ⚠️ gradlew.bat not found. Creating gradle wrapper...
    gradle wrapper
)

REM تجميع المشروع
echo Building debug APK...
call gradlew.bat assembleDebug

if %errorlevel% equ 0 (
    echo ✅ Build successful!
    
    REM البحث عن APK المبني
    for /r "app\build\outputs\apk" %%f in (*.apk) do (
        echo 📱 APK found: %%f
        copy "%%f" "%BUILD_DIR%\apple_fortune_helper.apk"
        echo 📁 APK copied to: %BUILD_DIR%\apple_fortune_helper.apk
    )
    
    REM عرض معلومات APK
    echo.
    echo 📊 APK Information:
    aapt dump badging "%BUILD_DIR%\apple_fortune_helper.apk" 2>nul | findstr "package:"
    aapt dump badging "%BUILD_DIR%\apple_fortune_helper.apk" 2>nul | findstr "versionCode:"
    aapt dump badging "%BUILD_DIR%\apple_fortune_helper.apk" 2>nul | findstr "versionName:"
    
    echo.
    echo 🎉 Build completed successfully!
    echo 📍 Output APK: %BUILD_DIR%\apple_fortune_helper.apk
    
    REM اختبار التثبيت (اختياري)
    echo.
    set /p INSTALL_TEST="Do you want to install the APK for testing? (y/n): "
    if /i "%INSTALL_TEST%"=="y" (
        echo 📲 Installing APK...
        adb install "%BUILD_DIR%\apple_fortune_helper.apk"
        if %errorlevel% equ 0 (
            echo ✅ APK installed successfully!
        ) else (
            echo ⚠️ Installation failed. Make sure USB debugging is enabled.
        )
    )
    
) else (
    echo ❌ Build failed!
    echo.
    echo 🔍 Common solutions:
    echo 1. Make sure ANDROID_HOME is set correctly
    echo 2. Check internet connection for downloading dependencies
    echo 3. Ensure Java JDK is installed
    echo 4. Try running: gradlew clean
    echo.
    pause
    exit /b 1
)

echo.
echo ✨ Done!
pause
