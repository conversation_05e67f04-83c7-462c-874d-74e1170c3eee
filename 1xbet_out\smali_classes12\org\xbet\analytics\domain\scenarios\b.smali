.class public final Lorg/xbet/analytics/domain/scenarios/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xbet/analytics/domain/scenarios/LogAppsFlyerScenarioImpl;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LIg/i;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lqn0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LCn0/b;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LIg/i;",
            ">;",
            "LBc/a<",
            "Lqn0/a;",
            ">;",
            "LBc/a<",
            "LCn0/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/analytics/domain/scenarios/b;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/analytics/domain/scenarios/b;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/analytics/domain/scenarios/b;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/analytics/domain/scenarios/b;->d:LBc/a;

    .line 11
    .line 12
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/analytics/domain/scenarios/b;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LIg/i;",
            ">;",
            "LBc/a<",
            "Lqn0/a;",
            ">;",
            "LBc/a<",
            "LCn0/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;)",
            "Lorg/xbet/analytics/domain/scenarios/b;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/analytics/domain/scenarios/b;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1, p2, p3}, Lorg/xbet/analytics/domain/scenarios/b;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(LIg/i;Lqn0/a;LCn0/b;Lorg/xbet/remoteconfig/domain/usecases/i;)Lorg/xbet/analytics/domain/scenarios/LogAppsFlyerScenarioImpl;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/analytics/domain/scenarios/LogAppsFlyerScenarioImpl;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1, p2, p3}, Lorg/xbet/analytics/domain/scenarios/LogAppsFlyerScenarioImpl;-><init>(LIg/i;Lqn0/a;LCn0/b;Lorg/xbet/remoteconfig/domain/usecases/i;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xbet/analytics/domain/scenarios/LogAppsFlyerScenarioImpl;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/domain/scenarios/b;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LIg/i;

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xbet/analytics/domain/scenarios/b;->b:LBc/a;

    .line 10
    .line 11
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    check-cast v1, Lqn0/a;

    .line 16
    .line 17
    iget-object v2, p0, Lorg/xbet/analytics/domain/scenarios/b;->c:LBc/a;

    .line 18
    .line 19
    invoke-interface {v2}, LBc/a;->get()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    check-cast v2, LCn0/b;

    .line 24
    .line 25
    iget-object v3, p0, Lorg/xbet/analytics/domain/scenarios/b;->d:LBc/a;

    .line 26
    .line 27
    invoke-interface {v3}, LBc/a;->get()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v3

    .line 31
    check-cast v3, Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 32
    .line 33
    invoke-static {v0, v1, v2, v3}, Lorg/xbet/analytics/domain/scenarios/b;->c(LIg/i;Lqn0/a;LCn0/b;Lorg/xbet/remoteconfig/domain/usecases/i;)Lorg/xbet/analytics/domain/scenarios/LogAppsFlyerScenarioImpl;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/analytics/domain/scenarios/b;->b()Lorg/xbet/analytics/domain/scenarios/LogAppsFlyerScenarioImpl;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
