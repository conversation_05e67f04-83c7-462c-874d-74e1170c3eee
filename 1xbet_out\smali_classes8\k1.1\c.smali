.class public Lk1/c;
.super Lk1/d;
.source "SourceFile"


# static fields
.field public static final c:[F


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const/16 v0, 0xc9

    .line 2
    .line 3
    new-array v0, v0, [F

    .line 4
    .line 5
    fill-array-data v0, :array_0

    .line 6
    .line 7
    .line 8
    sput-object v0, Lk1/c;->c:[F

    .line 9
    .line 10
    return-void

    .line 11
    :array_0
    .array-data 4
        0x0
        0x3cb5dcc6
        0x3d2dab9f
        0x3d7b15b5
        0x3da2680a
        0x3dc5d639
        0x3de7d567
        0x3e046738
        0x3e1460aa
        0x3e23d70a
        0x3e32e48f
        0x3e418937
        0x3e4fc505
        0x3e5db22d
        0x3e6b50b1
        0x3e78a090
        0x3e82d0e5
        0x3e89374c
        0x3e8f7660
        0x3e958e22
        0x3e9b8bac
        0x3ea16f00
        0x3ea7381d
        0x3eace704
        0x3eb27bb3
        0x3eb7e910
        0x3ebd4952
        0x3ec29c78
        0x3ec7c84b
        0x3ecce704
        0x3ed1eb85
        0x3ed6d5d0
        0x3edbb2ff
        0x3ee08312
        0x3ee538ef
        0x3ee9d495
        0x3eee6320
        0x3ef2e48f
        0x3ef758e2
        0x3efbb2ff
        0x3f000000    # 0.5f
        0x3f021ff3
        0x3f0432ca
        0x3f0645a2
        0x3f084b5e
        0x3f0a4a8c
        0x3f0c432d
        0x3f0e353f
        0x3f101a37
        0x3f11ff2e
        0x3f13dd98
        0x3f15aee6
        0x3f178034
        0x3f194af5
        0x3f1b0f28
        0x3f1cc63f
        0x3f1e7d56
        0x3f202de0
        0x3f21d7dc
        0x3f2381d8
        0x3f251eb8
        0x3f26b50b
        0x3f284b5e
        0x3f29db23
        0x3f2b645a
        0x3f2ce704
        0x3f2e6320
        0x3f2fdf3b
        0x3f3154ca
        0x3f32c3ca
        0x3f342c3d
        0x3f3594af
        0x3f36f694
        0x3f3851ec
        0x3f39a6b5
        0x3f3afb7f
        0x3f3c49ba
        0x3f3d97f6
        0x3f3ed917
        0x3f401a37
        0x3f415b57
        0x3f428f5c
        0x3f43c361
        0x3f44f766
        0x3f4624dd
        0x3f474bc7
        0x3f486c22
        0x3f498c7e
        0x3f4aacda
        0x3f4bc01a
        0x3f4cd9e8
        0x3f4de69b
        0x3f4ef34d
        0x3f500000    # 0.8125f
        0x3f510625
        0x3f5205bc
        0x3f530553
        0x3f53fe5d
        0x3f54f766
        0x3f55e9e2
        0x3f56d5d0
        0x3f57c84b
        0x3f58adac
        0x3f59930c
        0x3f5a786c
        0x3f5b573f
        0x3f5c2f83
        0x3f5d07c8
        0x3f5de00d
        0x3f5eb1c4
        0x3f5f837b
        0x3f604ea5
        0x3f611340
        0x3f61de6a
        0x3f629c78
        0x3f636113
        0x3f641893
        0x3f64d6a1
        0x3f658e22
        0x3f663f14
        0x3f66f007
        0x3f67a0f9
        0x3f684b5e
        0x3f68f5c3
        0x3f69999a
        0x3f6a3d71
        0x3f6adaba
        0x3f6b7803
        0x3f6c154d
        0x3f6cac08
        0x3f6d42c4
        0x3f6dd2f2
        0x3f6e6320
        0x3f6ef34d
        0x3f6f7cee
        0x3f70068e
        0x3f7089a0
        0x3f710cb3
        0x3f718fc5
        0x3f720c4a
        0x3f7288ce
        0x3f72fec5
        0x3f7374bc
        0x3f73eab3
        0x3f745a1d
        0x3f74c986
        0x3f7538ef
        0x3f75a1cb
        0x3f760aa6
        0x3f767382
        0x3f76d5d0
        0x3f77381d
        0x3f779a6b
        0x3f77f62b
        0x3f7851ec
        0x3f78a71e
        0x3f78fc50
        0x3f795183
        0x3f79a6b5
        0x3f79f55a
        0x3f7a43fe
        0x3f7a8c15
        0x3f7ad42c
        0x3f7b1c43
        0x3f7b645a
        0x3f7ba5e3
        0x3f7be76d
        0x3f7c2268
        0x3f7c63f1
        0x3f7c9eed
        0x3f7cd35b
        0x3f7d0e56
        0x3f7d42c4
        0x3f7d70a4
        0x3f7da512
        0x3f7dd2f2
        0x3f7e00d2
        0x3f7e2824
        0x3f7e4f76
        0x3f7e76c9
        0x3f7e9e1b
        0x3f7ebee0
        0x3f7edfa4
        0x3f7f0069
        0x3f7f212d
        0x3f7f3b64
        0x3f7f559b
        0x3f7f6944
        0x3f7f837b
        0x3f7f9724
        0x3f7faace
        0x3f7fb7e9
        0x3f7fcb92
        0x3f7fd8ae
        0x3f7fdf3b
        0x3f7fec57
        0x3f7ff2e5
        0x3f7ff972
        0x3f7ff972
        0x3f800000    # 1.0f
        0x3f800000    # 1.0f
    .end array-data
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    sget-object v0, Lk1/c;->c:[F

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lk1/d;-><init>([F)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public bridge synthetic getInterpolation(F)F
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lk1/d;->getInterpolation(F)F

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method
