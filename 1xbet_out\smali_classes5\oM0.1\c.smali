.class public final LoM0/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LoM0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u00020\u0001B#\u0012\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00040\u0002\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\u000b\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u0003H\u0017\u00a2\u0006\u0004\u0008\u000b\u0010\u000cR \u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00040\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\rR\u0014\u0010\u0011\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0010\u00a8\u0006\u0012"
    }
    d2 = {
        "LoM0/c;",
        "LoM0/a;",
        "Lkotlin/Function1;",
        "LzM0/a;",
        "",
        "listener",
        "Landroid/view/View;",
        "view",
        "<init>",
        "(Lkotlin/jvm/functions/Function1;Landroid/view/View;)V",
        "netCell",
        "a",
        "(LzM0/a;)V",
        "Lkotlin/jvm/functions/Function1;",
        "LpM0/j;",
        "b",
        "LpM0/j;",
        "binding",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "LzM0/a;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LpM0/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lkotlin/jvm/functions/Function1;Landroid/view/View;)V
    .locals 0
    .param p1    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LzM0/a;",
            "Lkotlin/Unit;",
            ">;",
            "Landroid/view/View;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LoM0/c;->a:Lkotlin/jvm/functions/Function1;

    .line 5
    .line 6
    invoke-static {p2}, LpM0/j;->a(Landroid/view/View;)LpM0/j;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    iput-object p1, p0, LoM0/c;->b:LpM0/j;

    .line 11
    .line 12
    return-void
.end method

.method public static synthetic b(LoM0/c;LzM0/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LoM0/c;->c(LoM0/c;LzM0/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(LoM0/c;LzM0/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, LoM0/c;->a:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p0
.end method


# virtual methods
.method public a(LzM0/a;)V
    .locals 4
    .param p1    # LzM0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "ClickableViewAccessibility"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LoM0/c;->b:LpM0/j;

    .line 2
    .line 3
    invoke-virtual {v0}, LpM0/j;->b()Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, LoM0/b;

    .line 8
    .line 9
    invoke-direct {v1, p0, p1}, LoM0/b;-><init>(LoM0/c;LzM0/a;)V

    .line 10
    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    const/4 v3, 0x1

    .line 14
    invoke-static {v0, v2, v1, v3, v2}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, LoM0/c;->b:LpM0/j;

    .line 18
    .line 19
    iget-object v0, v0, LpM0/j;->b:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;

    .line 20
    .line 21
    invoke-virtual {p1}, LzM0/a;->h()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->setTopLogo(Ljava/lang/String;)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p1}, LzM0/a;->m()Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->setBotLogo(Ljava/lang/String;)V

    .line 33
    .line 34
    .line 35
    invoke-virtual {p1}, LzM0/a;->i()Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->setTopTeamName(Ljava/lang/CharSequence;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {p1}, LzM0/a;->n()Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->setBotTeamName(Ljava/lang/CharSequence;)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {v0, v3}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->setScroll(Z)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {p1}, LzM0/a;->c()LuM0/f;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    invoke-virtual {v1}, LuM0/f;->a()Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->setTopSeekScore(Ljava/lang/CharSequence;)V

    .line 61
    .line 62
    .line 63
    invoke-virtual {p1}, LzM0/a;->c()LuM0/f;

    .line 64
    .line 65
    .line 66
    move-result-object v1

    .line 67
    invoke-virtual {v1}, LuM0/f;->c()Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->setBotSeekScore(Ljava/lang/CharSequence;)V

    .line 72
    .line 73
    .line 74
    invoke-virtual {p1}, LzM0/a;->b()Ljava/util/List;

    .line 75
    .line 76
    .line 77
    move-result-object v1

    .line 78
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;->setScores(Ljava/util/List;)V

    .line 79
    .line 80
    .line 81
    iget-object v0, p0, LoM0/c;->b:LpM0/j;

    .line 82
    .line 83
    iget-object v0, v0, LpM0/j;->c:Lorg/xbet/uikit_sport/eventcard/top/StatisticsHeader;

    .line 84
    .line 85
    invoke-virtual {p1}, LzM0/a;->r()Z

    .line 86
    .line 87
    .line 88
    move-result v1

    .line 89
    if-eqz v1, :cond_0

    .line 90
    .line 91
    const/4 v1, 0x0

    .line 92
    goto :goto_0

    .line 93
    :cond_0
    const/16 v1, 0x8

    .line 94
    .line 95
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 96
    .line 97
    .line 98
    invoke-virtual {p1}, LzM0/a;->q()Ljava/lang/String;

    .line 99
    .line 100
    .line 101
    move-result-object v0

    .line 102
    invoke-virtual {p1}, LzM0/a;->g()Ljava/lang/String;

    .line 103
    .line 104
    .line 105
    move-result-object v1

    .line 106
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 107
    .line 108
    .line 109
    move-result v1

    .line 110
    if-eqz v1, :cond_1

    .line 111
    .line 112
    iget-object p1, p0, LoM0/c;->b:LpM0/j;

    .line 113
    .line 114
    invoke-virtual {p1}, LpM0/j;->b()Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;

    .line 115
    .line 116
    .line 117
    move-result-object p1

    .line 118
    invoke-static {p1}, LNN0/k;->c(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;)V

    .line 119
    .line 120
    .line 121
    return-void

    .line 122
    :cond_1
    invoke-virtual {p1}, LzM0/a;->l()Ljava/lang/String;

    .line 123
    .line 124
    .line 125
    move-result-object p1

    .line 126
    invoke-static {v0, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 127
    .line 128
    .line 129
    move-result p1

    .line 130
    if-eqz p1, :cond_2

    .line 131
    .line 132
    iget-object p1, p0, LoM0/c;->b:LpM0/j;

    .line 133
    .line 134
    invoke-virtual {p1}, LpM0/j;->b()Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;

    .line 135
    .line 136
    .line 137
    move-result-object p1

    .line 138
    invoke-static {p1}, LNN0/k;->e(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;)V

    .line 139
    .line 140
    .line 141
    return-void

    .line 142
    :cond_2
    iget-object p1, p0, LoM0/c;->b:LpM0/j;

    .line 143
    .line 144
    invoke-virtual {p1}, LpM0/j;->b()Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;

    .line 145
    .line 146
    .line 147
    move-result-object p1

    .line 148
    invoke-static {p1}, LNN0/k;->a(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;)V

    .line 149
    .line 150
    .line 151
    return-void
.end method
