.class public final Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\n\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0008\u0010\t\u001a\u0004\u0008\u0008\u0010\nR\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u000b\u0010\u000c\u001a\u0004\u0008\u000b\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$c;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;",
        "Lorg/xbet/balance/model/BalanceModel;",
        "balance",
        "",
        "balanceTitle",
        "<init>",
        "(Lorg/xbet/balance/model/BalanceModel;Ljava/lang/String;)V",
        "a",
        "Lorg/xbet/balance/model/BalanceModel;",
        "()Lorg/xbet/balance/model/BalanceModel;",
        "b",
        "Ljava/lang/String;",
        "()Ljava/lang/String;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/balance/model/BalanceModel;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/balance/model/BalanceModel;Ljava/lang/String;)V
    .locals 0
    .param p1    # Lorg/xbet/balance/model/BalanceModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$c;->a:Lorg/xbet/balance/model/BalanceModel;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$c;->b:Ljava/lang/String;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a()Lorg/xbet/balance/model/BalanceModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$c;->a:Lorg/xbet/balance/model/BalanceModel;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$c;->b:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method
