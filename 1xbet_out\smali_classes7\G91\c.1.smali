.class public final LG91/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u0003\u001aG\u0010\n\u001a\u00020\u0006*\u000c\u0012\u0004\u0012\u00020\u0001\u0012\u0002\u0008\u00030\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00042\u000c\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u0008H\u0000\u00a2\u0006\u0004\u0008\n\u0010\u000b\u001a9\u0010\u000c\u001a\u00020\u0006*\u000c\u0012\u0004\u0012\u00020\u0001\u0012\u0002\u0008\u00030\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004H\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\r\u001aG\u0010\u0012\u001a\u00020\u0006*\u000c\u0012\u0004\u0012\u00020\u0001\u0012\u0002\u0008\u00030\u00002\u000c\u0010\u0010\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u000e2\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00042\u0006\u0010\u0011\u001a\u00020\u0002H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013\u001a\u001f\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0014\u001a\u00020\u00052\u0006\u0010\u0015\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018\u00a8\u0006\u0019"
    }
    d2 = {
        "LB4/a;",
        "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;",
        "Lorg/xbet/uikit/components/chips/ChipGroup;",
        "chipGroup",
        "Lkotlin/Function1;",
        "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;",
        "",
        "changeCheckedState",
        "Lkotlin/Function0;",
        "bindAll",
        "e",
        "(LB4/a;Lorg/xbet/uikit/components/chips/ChipGroup;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V",
        "f",
        "(LB4/a;Lorg/xbet/uikit/components/chips/ChipGroup;Lkotlin/jvm/functions/Function1;)V",
        "",
        "",
        "changedIds",
        "chipsGroup",
        "i",
        "(LB4/a;Ljava/util/Set;Lkotlin/jvm/functions/Function1;Lorg/xbet/uikit/components/chips/ChipGroup;)V",
        "filterItem",
        "filterCategory",
        "",
        "h",
        "(Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;)Z",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LB4/a;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;Lorg/xbet/uikit/components/chips/Chip;Z)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, LG91/c;->j(LB4/a;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;Lorg/xbet/uikit/components/chips/Chip;Z)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;Lorg/xbet/uikit/components/chips/Chip;Z)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, LG91/c;->g(Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;Lorg/xbet/uikit/components/chips/Chip;Z)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic c(LB4/a;Lorg/xbet/uikit/components/chips/ChipGroup;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LG91/c;->f(LB4/a;Lorg/xbet/uikit/components/chips/ChipGroup;Lkotlin/jvm/functions/Function1;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic d(LB4/a;Ljava/util/Set;Lkotlin/jvm/functions/Function1;Lorg/xbet/uikit/components/chips/ChipGroup;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, LG91/c;->i(LB4/a;Ljava/util/Set;Lkotlin/jvm/functions/Function1;Lorg/xbet/uikit/components/chips/ChipGroup;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final e(LB4/a;Lorg/xbet/uikit/components/chips/ChipGroup;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V
    .locals 8
    .param p0    # LB4/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/uikit/components/chips/ChipGroup;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;",
            "*>;",
            "Lorg/xbet/uikit/components/chips/ChipGroup;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, LG91/c$a;

    .line 2
    .line 3
    move-object v5, p0

    .line 4
    move-object v6, p2

    .line 5
    move-object v7, p1

    .line 6
    move-object v3, p0

    .line 7
    move-object v2, p1

    .line 8
    move-object v4, p2

    .line 9
    move-object v1, p3

    .line 10
    invoke-direct/range {v0 .. v7}, LG91/c$a;-><init>(Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/chips/ChipGroup;LB4/a;Lkotlin/jvm/functions/Function1;LB4/a;Lkotlin/jvm/functions/Function1;Lorg/xbet/uikit/components/chips/ChipGroup;)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {v3, v0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public static final f(LB4/a;Lorg/xbet/uikit/components/chips/ChipGroup;Lkotlin/jvm/functions/Function1;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;",
            "*>;",
            "Lorg/xbet/uikit/components/chips/ChipGroup;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;

    .line 6
    .line 7
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;->d()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    if-eqz v1, :cond_3

    .line 20
    .line 21
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    check-cast v1, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    .line 26
    .line 27
    new-instance v2, Lorg/xbet/uikit/components/chips/Chip;

    .line 28
    .line 29
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 30
    .line 31
    .line 32
    move-result-object v3

    .line 33
    const/4 v4, 0x2

    .line 34
    const/4 v5, 0x0

    .line 35
    invoke-direct {v2, v3, v5, v4, v5}, Lorg/xbet/uikit/components/chips/Chip;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 36
    .line 37
    .line 38
    sget v3, LlZ0/n;->Widget_Chips_Secondary:I

    .line 39
    .line 40
    invoke-virtual {v2, v3}, Lorg/xbet/uikit/components/chips/Chip;->setStyle(I)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {v2}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 44
    .line 45
    .line 46
    move-result-object v3

    .line 47
    sget v4, LlZ0/g;->space_8:I

    .line 48
    .line 49
    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 50
    .line 51
    .line 52
    move-result v3

    .line 53
    invoke-virtual {v2}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 54
    .line 55
    .line 56
    move-result-object v4

    .line 57
    sget v6, LlZ0/g;->space_8:I

    .line 58
    .line 59
    invoke-virtual {v4, v6}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 60
    .line 61
    .line 62
    move-result v4

    .line 63
    invoke-virtual {v2}, Landroid/view/View;->getPaddingLeft()I

    .line 64
    .line 65
    .line 66
    move-result v6

    .line 67
    invoke-virtual {v2}, Landroid/view/View;->getPaddingRight()I

    .line 68
    .line 69
    .line 70
    move-result v7

    .line 71
    invoke-virtual {v2, v6, v3, v7, v4}, Landroid/view/View;->setPadding(IIII)V

    .line 72
    .line 73
    .line 74
    invoke-interface {v1}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getId()Ljava/lang/String;

    .line 75
    .line 76
    .line 77
    move-result-object v3

    .line 78
    invoke-virtual {v2, v3}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 79
    .line 80
    .line 81
    invoke-interface {v1}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getId()Ljava/lang/String;

    .line 82
    .line 83
    .line 84
    move-result-object v3

    .line 85
    const-string v4, "0"

    .line 86
    .line 87
    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 88
    .line 89
    .line 90
    move-result v3

    .line 91
    if-eqz v3, :cond_0

    .line 92
    .line 93
    sget v3, Lpb/k;->filter_all:I

    .line 94
    .line 95
    invoke-virtual {p0, v3}, LB4/a;->j(I)Ljava/lang/String;

    .line 96
    .line 97
    .line 98
    move-result-object v3

    .line 99
    goto :goto_1

    .line 100
    :cond_0
    invoke-interface {v1}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getName()Ljava/lang/String;

    .line 101
    .line 102
    .line 103
    move-result-object v3

    .line 104
    :goto_1
    invoke-virtual {v2, v3}, Lorg/xbet/uikit/components/chips/Chip;->setText(Ljava/lang/CharSequence;)V

    .line 105
    .line 106
    .line 107
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 108
    .line 109
    .line 110
    move-result-object v3

    .line 111
    check-cast v3, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;

    .line 112
    .line 113
    invoke-static {v1, v3}, LG91/c;->h(Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;)Z

    .line 114
    .line 115
    .line 116
    move-result v3

    .line 117
    invoke-virtual {v2, v3}, Lorg/xbet/uikit/components/chips/Chip;->setSelected(Z)V

    .line 118
    .line 119
    .line 120
    new-instance v3, LG91/a;

    .line 121
    .line 122
    invoke-direct {v3, p2, v1}, LG91/a;-><init>(Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;)V

    .line 123
    .line 124
    .line 125
    invoke-virtual {v2, v3}, Lorg/xbet/uikit/components/chips/Chip;->setOnSelectListener(Lkotlin/jvm/functions/Function2;)V

    .line 126
    .line 127
    .line 128
    instance-of v3, v1, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterUiModel;

    .line 129
    .line 130
    if-eqz v3, :cond_1

    .line 131
    .line 132
    check-cast v1, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterUiModel;

    .line 133
    .line 134
    goto :goto_2

    .line 135
    :cond_1
    move-object v1, v5

    .line 136
    :goto_2
    if-eqz v1, :cond_2

    .line 137
    .line 138
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterUiModel;->e0()Ljava/lang/String;

    .line 139
    .line 140
    .line 141
    move-result-object v5

    .line 142
    :cond_2
    sget v1, LlZ0/h;->ic_glyph_category_new:I

    .line 143
    .line 144
    invoke-virtual {p0, v1}, LB4/a;->h(I)Landroid/graphics/drawable/Drawable;

    .line 145
    .line 146
    .line 147
    move-result-object v1

    .line 148
    invoke-virtual {v2, v5, v1}, Lorg/xbet/uikit/components/chips/Chip;->setIconByUrl(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V

    .line 149
    .line 150
    .line 151
    invoke-virtual {p1, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 152
    .line 153
    .line 154
    goto/16 :goto_0

    .line 155
    .line 156
    :cond_3
    return-void
.end method

.method public static final g(Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;Lorg/xbet/uikit/components/chips/Chip;Z)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final h(Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;)Z
    .locals 1

    .line 1
    invoke-interface {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->P()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_4

    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;->d()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-static {p1}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-eqz v0, :cond_0

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    :cond_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 29
    .line 30
    .line 31
    move-result v0

    .line 32
    if-eqz v0, :cond_2

    .line 33
    .line 34
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    .line 39
    .line 40
    invoke-interface {v0}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->P()Z

    .line 41
    .line 42
    .line 43
    move-result v0

    .line 44
    if-eqz v0, :cond_1

    .line 45
    .line 46
    goto :goto_1

    .line 47
    :cond_2
    :goto_0
    invoke-interface {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getId()Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object p0

    .line 51
    const-string p1, "0"

    .line 52
    .line 53
    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 54
    .line 55
    .line 56
    move-result p0

    .line 57
    if-eqz p0, :cond_3

    .line 58
    .line 59
    goto :goto_2

    .line 60
    :cond_3
    :goto_1
    const/4 p0, 0x0

    .line 61
    return p0

    .line 62
    :cond_4
    :goto_2
    const/4 p0, 0x1

    .line 63
    return p0
.end method

.method public static final i(LB4/a;Ljava/util/Set;Lkotlin/jvm/functions/Function1;Lorg/xbet/uikit/components/chips/ChipGroup;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;",
            "*>;",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;",
            "Lkotlin/Unit;",
            ">;",
            "Lorg/xbet/uikit/components/chips/ChipGroup;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;

    .line 6
    .line 7
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;->d()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    new-instance v1, Ljava/util/ArrayList;

    .line 12
    .line 13
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 14
    .line 15
    .line 16
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 21
    .line 22
    .line 23
    move-result v2

    .line 24
    if-eqz v2, :cond_1

    .line 25
    .line 26
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    move-object v3, v2

    .line 31
    check-cast v3, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    .line 32
    .line 33
    invoke-interface {v3}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getId()Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object v3

    .line 37
    invoke-interface {p1, v3}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    .line 38
    .line 39
    .line 40
    move-result v3

    .line 41
    if-eqz v3, :cond_0

    .line 42
    .line 43
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 44
    .line 45
    .line 46
    goto :goto_0

    .line 47
    :cond_1
    new-instance p1, Ljava/util/LinkedHashMap;

    .line 48
    .line 49
    const/16 v0, 0xa

    .line 50
    .line 51
    invoke-static {v1, v0}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 52
    .line 53
    .line 54
    move-result v0

    .line 55
    invoke-static {v0}, Lkotlin/collections/P;->e(I)I

    .line 56
    .line 57
    .line 58
    move-result v0

    .line 59
    const/16 v2, 0x10

    .line 60
    .line 61
    invoke-static {v0, v2}, Lkotlin/ranges/f;->g(II)I

    .line 62
    .line 63
    .line 64
    move-result v0

    .line 65
    invoke-direct {p1, v0}, Ljava/util/LinkedHashMap;-><init>(I)V

    .line 66
    .line 67
    .line 68
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 73
    .line 74
    .line 75
    move-result v1

    .line 76
    const/4 v2, 0x0

    .line 77
    if-eqz v1, :cond_5

    .line 78
    .line 79
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    move-object v3, v1

    .line 84
    check-cast v3, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    .line 85
    .line 86
    invoke-static {p3}, Landroidx/core/view/ViewGroupKt;->b(Landroid/view/ViewGroup;)Lkotlin/sequences/Sequence;

    .line 87
    .line 88
    .line 89
    move-result-object v4

    .line 90
    invoke-interface {v4}, Lkotlin/sequences/Sequence;->iterator()Ljava/util/Iterator;

    .line 91
    .line 92
    .line 93
    move-result-object v4

    .line 94
    :cond_2
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 95
    .line 96
    .line 97
    move-result v5

    .line 98
    if-eqz v5, :cond_3

    .line 99
    .line 100
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 101
    .line 102
    .line 103
    move-result-object v5

    .line 104
    move-object v6, v5

    .line 105
    check-cast v6, Landroid/view/View;

    .line 106
    .line 107
    invoke-virtual {v6}, Landroid/view/View;->getTag()Ljava/lang/Object;

    .line 108
    .line 109
    .line 110
    move-result-object v6

    .line 111
    invoke-interface {v3}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getId()Ljava/lang/String;

    .line 112
    .line 113
    .line 114
    move-result-object v7

    .line 115
    invoke-static {v6, v7}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 116
    .line 117
    .line 118
    move-result v6

    .line 119
    if-eqz v6, :cond_2

    .line 120
    .line 121
    goto :goto_2

    .line 122
    :cond_3
    move-object v5, v2

    .line 123
    :goto_2
    instance-of v3, v5, Lorg/xbet/uikit/components/chips/Chip;

    .line 124
    .line 125
    if-eqz v3, :cond_4

    .line 126
    .line 127
    move-object v2, v5

    .line 128
    check-cast v2, Lorg/xbet/uikit/components/chips/Chip;

    .line 129
    .line 130
    :cond_4
    invoke-interface {p1, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    goto :goto_1

    .line 134
    :cond_5
    invoke-interface {p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    .line 135
    .line 136
    .line 137
    move-result-object p1

    .line 138
    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    .line 139
    .line 140
    .line 141
    move-result-object p1

    .line 142
    :cond_6
    :goto_3
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 143
    .line 144
    .line 145
    move-result p3

    .line 146
    if-eqz p3, :cond_8

    .line 147
    .line 148
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 149
    .line 150
    .line 151
    move-result-object p3

    .line 152
    check-cast p3, Ljava/util/Map$Entry;

    .line 153
    .line 154
    invoke-interface {p3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    .line 155
    .line 156
    .line 157
    move-result-object v0

    .line 158
    check-cast v0, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    .line 159
    .line 160
    invoke-interface {p3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    .line 161
    .line 162
    .line 163
    move-result-object p3

    .line 164
    check-cast p3, Lorg/xbet/uikit/components/chips/Chip;

    .line 165
    .line 166
    if-eqz p3, :cond_6

    .line 167
    .line 168
    invoke-interface {v0}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->P()Z

    .line 169
    .line 170
    .line 171
    move-result v1

    .line 172
    invoke-virtual {p3}, Lorg/xbet/uikit/components/chips/Chip;->isSelected()Z

    .line 173
    .line 174
    .line 175
    move-result v3

    .line 176
    if-ne v1, v3, :cond_7

    .line 177
    .line 178
    goto :goto_3

    .line 179
    :cond_7
    invoke-virtual {p3, v2}, Lorg/xbet/uikit/components/chips/Chip;->setOnSelectListener(Lkotlin/jvm/functions/Function2;)V

    .line 180
    .line 181
    .line 182
    invoke-interface {v0}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->P()Z

    .line 183
    .line 184
    .line 185
    move-result v1

    .line 186
    invoke-virtual {p3, v1}, Lorg/xbet/uikit/components/chips/Chip;->setSelected(Z)V

    .line 187
    .line 188
    .line 189
    new-instance v1, LG91/b;

    .line 190
    .line 191
    invoke-direct {v1, p0, p2, v0}, LG91/b;-><init>(LB4/a;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;)V

    .line 192
    .line 193
    .line 194
    invoke-virtual {p3, v1}, Lorg/xbet/uikit/components/chips/Chip;->setOnSelectListener(Lkotlin/jvm/functions/Function2;)V

    .line 195
    .line 196
    .line 197
    goto :goto_3

    .line 198
    :cond_8
    return-void
.end method

.method public static final j(LB4/a;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;Lorg/xbet/uikit/components/chips/Chip;Z)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;->d()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 16
    .line 17
    .line 18
    move-result p3

    .line 19
    if-eqz p3, :cond_1

    .line 20
    .line 21
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object p3

    .line 25
    move-object p4, p3

    .line 26
    check-cast p4, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    .line 27
    .line 28
    invoke-interface {p4}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getId()Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object p4

    .line 32
    invoke-interface {p2}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getId()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    invoke-static {p4, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 37
    .line 38
    .line 39
    move-result p4

    .line 40
    if-eqz p4, :cond_0

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_1
    const/4 p3, 0x0

    .line 44
    :goto_0
    check-cast p3, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    .line 45
    .line 46
    if-eqz p3, :cond_2

    .line 47
    .line 48
    invoke-interface {p1, p3}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    :cond_2
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 52
    .line 53
    return-object p0
.end method
