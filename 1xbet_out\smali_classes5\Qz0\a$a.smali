.class public final LQz0/a$a;
.super Landroidx/recyclerview/widget/i$f;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LQz0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroidx/recyclerview/widget/i$f<",
        "LQz0/a;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000)\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\"\n\u0002\u0010 \n\u0002\u0010\u0000\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001J\u001f\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u001f\u0010\u0008\u001a\u00020\u00052\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\u0007J+\u0010\u000c\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000b0\n0\t2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Qz0/a$a",
        "Landroidx/recyclerview/widget/i$f;",
        "LQz0/a;",
        "oldItem",
        "newItem",
        "",
        "e",
        "(LQz0/a;LQz0/a;)Z",
        "d",
        "",
        "",
        "",
        "f",
        "(LQz0/a;LQz0/a;)Ljava/util/Set;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Landroidx/recyclerview/widget/i$f;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, LQz0/a;

    .line 2
    .line 3
    check-cast p2, LQz0/a;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LQz0/a$a;->d(LQz0/a;LQz0/a;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public bridge synthetic b(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, LQz0/a;

    .line 2
    .line 3
    check-cast p2, LQz0/a;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LQz0/a$a;->e(LQz0/a;LQz0/a;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public bridge synthetic c(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, LQz0/a;

    .line 2
    .line 3
    check-cast p2, LQz0/a;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LQz0/a$a;->f(LQz0/a;LQz0/a;)Ljava/util/Set;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    return-object p1
.end method

.method public d(LQz0/a;LQz0/a;)Z
    .locals 0

    .line 1
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method

.method public e(LQz0/a;LQz0/a;)Z
    .locals 0

    .line 1
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    if-ne p2, p1, :cond_0

    .line 10
    .line 11
    const/4 p1, 0x1

    .line 12
    return p1

    .line 13
    :cond_0
    const/4 p1, 0x0

    .line 14
    return p1
.end method

.method public f(LQz0/a;LQz0/a;)Ljava/util/Set;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LQz0/a;",
            "LQz0/a;",
            ")",
            "Ljava/util/Set<",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;>;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/LinkedHashSet;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    .line 4
    .line 5
    .line 6
    instance-of v1, p2, LQz0/i;

    .line 7
    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    instance-of v1, p1, LQz0/i;

    .line 11
    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    move-object v1, p1

    .line 15
    check-cast v1, LQz0/i;

    .line 16
    .line 17
    move-object v2, p2

    .line 18
    check-cast v2, LQz0/i;

    .line 19
    .line 20
    invoke-static {v1, v2}, LQz0/j;->a(LQz0/i;LQz0/i;)Ljava/util/List;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 25
    .line 26
    .line 27
    :cond_0
    instance-of v1, p2, LQz0/r;

    .line 28
    .line 29
    if-eqz v1, :cond_1

    .line 30
    .line 31
    instance-of v1, p1, LQz0/r;

    .line 32
    .line 33
    if-eqz v1, :cond_1

    .line 34
    .line 35
    move-object v1, p1

    .line 36
    check-cast v1, LQz0/r;

    .line 37
    .line 38
    move-object v2, p2

    .line 39
    check-cast v2, LQz0/r;

    .line 40
    .line 41
    invoke-static {v1, v2}, LQz0/s;->a(LQz0/r;LQz0/r;)Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 46
    .line 47
    .line 48
    :cond_1
    instance-of v1, p2, LQz0/n;

    .line 49
    .line 50
    if-eqz v1, :cond_2

    .line 51
    .line 52
    instance-of v1, p1, LQz0/n;

    .line 53
    .line 54
    if-eqz v1, :cond_2

    .line 55
    .line 56
    move-object v1, p1

    .line 57
    check-cast v1, LQz0/n;

    .line 58
    .line 59
    move-object v2, p2

    .line 60
    check-cast v2, LQz0/n;

    .line 61
    .line 62
    invoke-static {v1, v2}, LQz0/o;->a(LQz0/n;LQz0/n;)Ljava/util/List;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 67
    .line 68
    .line 69
    :cond_2
    instance-of v1, p2, LQz0/p;

    .line 70
    .line 71
    if-eqz v1, :cond_3

    .line 72
    .line 73
    instance-of v1, p1, LQz0/p;

    .line 74
    .line 75
    if-eqz v1, :cond_3

    .line 76
    .line 77
    move-object v1, p1

    .line 78
    check-cast v1, LQz0/p;

    .line 79
    .line 80
    move-object v2, p2

    .line 81
    check-cast v2, LQz0/p;

    .line 82
    .line 83
    invoke-static {v1, v2}, LQz0/q;->a(LQz0/p;LQz0/p;)Ljava/util/List;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 88
    .line 89
    .line 90
    :cond_3
    instance-of v1, p2, LQz0/l;

    .line 91
    .line 92
    if-eqz v1, :cond_4

    .line 93
    .line 94
    instance-of v1, p1, LQz0/l;

    .line 95
    .line 96
    if-eqz v1, :cond_4

    .line 97
    .line 98
    move-object v1, p1

    .line 99
    check-cast v1, LQz0/l;

    .line 100
    .line 101
    move-object v2, p2

    .line 102
    check-cast v2, LQz0/l;

    .line 103
    .line 104
    invoke-static {v1, v2}, LQz0/m;->a(LQz0/l;LQz0/l;)Ljava/util/List;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 109
    .line 110
    .line 111
    :cond_4
    instance-of v1, p1, LQz0/t;

    .line 112
    .line 113
    if-eqz v1, :cond_5

    .line 114
    .line 115
    instance-of v1, p2, LQz0/t;

    .line 116
    .line 117
    if-eqz v1, :cond_5

    .line 118
    .line 119
    check-cast p1, LQz0/t;

    .line 120
    .line 121
    check-cast p2, LQz0/t;

    .line 122
    .line 123
    invoke-static {p1, p2}, LQz0/u;->a(LQz0/t;LQz0/t;)Ljava/util/List;

    .line 124
    .line 125
    .line 126
    move-result-object p1

    .line 127
    invoke-interface {v0, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 128
    .line 129
    .line 130
    :cond_5
    return-object v0
.end method
