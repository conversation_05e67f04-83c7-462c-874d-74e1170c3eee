.class public final LrT0/a;
.super Ljava/lang/Object;


# static fields
.field public static background_odyssey_field:I = 0x7f080200

.field public static fruit_blast_blueberries_coeff_icon:I = 0x7f0805f4

.field public static fruit_blast_blueberries_field_highlighted_icon:I = 0x7f0805f5

.field public static fruit_blast_blueberries_field_icon:I = 0x7f0805f6

.field public static fruit_blast_bonus_coeff_icon:I = 0x7f0805f7

.field public static fruit_blast_bonus_field_icon:I = 0x7f0805f8

.field public static fruit_blast_bonus_free_bet:I = 0x7f0805f9

.field public static fruit_blast_bonus_free_spin:I = 0x7f0805fa

.field public static fruit_blast_bonus_highlighted_field_icon:I = 0x7f0805fb

.field public static fruit_blast_bonus_money:I = 0x7f0805fc

.field public static fruit_blast_bonus_money_x2:I = 0x7f0805fd

.field public static fruit_blast_cherry_coeff_icon:I = 0x7f0805fe

.field public static fruit_blast_cherry_field_highlighted_icon:I = 0x7f0805ff

.field public static fruit_blast_cherry_field_icon:I = 0x7f080600

.field public static fruit_blast_coeff_count_back:I = 0x7f080601

.field public static fruit_blast_coeff_image_back:I = 0x7f080602

.field public static fruit_blast_coeff_value_back:I = 0x7f080603

.field public static fruit_blast_field_frame:I = 0x7f080604

.field public static fruit_blast_grape_coeff_icon:I = 0x7f080605

.field public static fruit_blast_grape_field_highlighted_icon:I = 0x7f080606

.field public static fruit_blast_grape_field_icon:I = 0x7f080607

.field public static fruit_blast_lemon_coeff_icon:I = 0x7f080608

.field public static fruit_blast_lemon_field_icon:I = 0x7f080609

.field public static fruit_blast_lemon_highlighted_field_icon:I = 0x7f08060a

.field public static fruit_blast_progress_back:I = 0x7f08060b

.field public static fruit_blast_strawberry_coeff_icon:I = 0x7f08060c

.field public static fruit_blast_strawberry_field_highlighted_icon:I = 0x7f08060d

.field public static fruit_blast_strawberry_field_icon:I = 0x7f08060e

.field public static game_ended_separator:I = 0x7f080629

.field public static ic_odyssey_crystal_blue:I = 0x7f080b73

.field public static ic_odyssey_crystal_blue_selected:I = 0x7f080b74

.field public static ic_odyssey_crystal_green:I = 0x7f080b75

.field public static ic_odyssey_crystal_green_selected:I = 0x7f080b76

.field public static ic_odyssey_crystal_pink:I = 0x7f080b77

.field public static ic_odyssey_crystal_pink_selected:I = 0x7f080b78

.field public static ic_odyssey_crystal_purple:I = 0x7f080b79

.field public static ic_odyssey_crystal_purple_selected:I = 0x7f080b7a

.field public static ic_odyssey_crystal_red:I = 0x7f080b7b

.field public static ic_odyssey_crystal_red_selected:I = 0x7f080b7c

.field public static ic_odyssey_crystal_yellow:I = 0x7f080b7d

.field public static ic_odyssey_crystal_yellow_selected:I = 0x7f080b7e

.field public static odyssey_coeff_count_back:I = 0x7f080e97

.field public static odyssey_coeff_image_back:I = 0x7f080e98

.field public static odyssey_coeff_value_back:I = 0x7f080e99

.field public static odyssey_progress_back:I = 0x7f080e9a

.field public static progress_background:I = 0x7f080efd


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
