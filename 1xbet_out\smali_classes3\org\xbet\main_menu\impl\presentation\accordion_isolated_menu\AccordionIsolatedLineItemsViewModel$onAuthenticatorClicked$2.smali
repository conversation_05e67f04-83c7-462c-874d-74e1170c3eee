.class final Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.main_menu.impl.presentation.accordion_isolated_menu.AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2"
    f = "AccordionIsolatedLineItemsViewModel.kt"
    l = {
        0x221,
        0x223
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->y4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;

    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;-><init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x2

    .line 9
    const/4 v4, 0x1

    .line 10
    if-eqz v1, :cond_2

    .line 11
    .line 12
    if-eq v1, v4, :cond_1

    .line 13
    .line 14
    if-ne v1, v3, :cond_0

    .line 15
    .line 16
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    goto :goto_2

    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    goto :goto_0

    .line 32
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 33
    .line 34
    .line 35
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 36
    .line 37
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->J3(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;)Lp9/c;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    invoke-virtual {p1}, Lp9/c;->a()Z

    .line 42
    .line 43
    .line 44
    move-result p1

    .line 45
    if-eqz p1, :cond_5

    .line 46
    .line 47
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 48
    .line 49
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->O3(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;)Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    iput v4, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->label:I

    .line 54
    .line 55
    invoke-virtual {p1, v2, p0}, Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;->c(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    if-ne p1, v0, :cond_3

    .line 60
    .line 61
    goto :goto_1

    .line 62
    :cond_3
    :goto_0
    check-cast p1, Le9/a;

    .line 63
    .line 64
    invoke-virtual {p1}, Le9/a;->s()Z

    .line 65
    .line 66
    .line 67
    move-result p1

    .line 68
    if-eqz p1, :cond_5

    .line 69
    .line 70
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 71
    .line 72
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->X3(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;)LFi/b;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    iput v3, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->label:I

    .line 77
    .line 78
    invoke-interface {p1, p0}, LFi/b;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    if-ne p1, v0, :cond_4

    .line 83
    .line 84
    :goto_1
    return-object v0

    .line 85
    :cond_4
    :goto_2
    check-cast p1, Ljava/lang/Boolean;

    .line 86
    .line 87
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 88
    .line 89
    .line 90
    move-result p1

    .line 91
    if-eqz p1, :cond_5

    .line 92
    .line 93
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 94
    .line 95
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->S3(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;)LwX0/c;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 100
    .line 101
    invoke-static {v0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->E3(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;)LwX0/a;

    .line 102
    .line 103
    .line 104
    move-result-object v0

    .line 105
    invoke-interface {v0}, LwX0/a;->e()Lq4/q;

    .line 106
    .line 107
    .line 108
    move-result-object v0

    .line 109
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 110
    .line 111
    .line 112
    goto :goto_3

    .line 113
    :cond_5
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 114
    .line 115
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->S3(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;)LwX0/c;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$onAuthenticatorClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 120
    .line 121
    invoke-static {v0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->E3(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;)LwX0/a;

    .line 122
    .line 123
    .line 124
    move-result-object v0

    .line 125
    invoke-interface {v0, v2}, LwX0/a;->z(Z)Lq4/q;

    .line 126
    .line 127
    .line 128
    move-result-object v0

    .line 129
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 130
    .line 131
    .line 132
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 133
    .line 134
    return-object p1
.end method
