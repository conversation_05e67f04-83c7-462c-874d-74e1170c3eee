.class public final LTG0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010 \n\u0002\u0008\u0004\u001aO\u0010\u000c\u001a\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u00002\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u000c\u0010\u000b\u001a\u0008\u0012\u0004\u0012\u00020\u00010\nH\u0001\u00a2\u0006\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Lkotlin/Function1;",
        "LUG0/a;",
        "",
        "onGameClickAction",
        "Lkotlin/Function0;",
        "onMoreButtonClickAction",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "emptyLottie",
        "",
        "maxGamesCount",
        "",
        "gamesList",
        "c",
        "(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/lottie_empty/n;ILjava/util/List;Landroidx/compose/runtime/j;I)V",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lkotlin/jvm/functions/Function1;LUG0/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LTG0/c;->d(Lkotlin/jvm/functions/Function1;LUG0/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/lottie_empty/n;ILjava/util/List;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p7}, LTG0/c;->e(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/lottie_empty/n;ILjava/util/List;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/lottie_empty/n;ILjava/util/List;Landroidx/compose/runtime/j;I)V
    .locals 27
    .param p0    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit/components/lottie_empty/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LUG0/a;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Lorg/xbet/uikit/components/lottie_empty/n;",
            "I",
            "Ljava/util/List<",
            "LUG0/a;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "I)V"
        }
    .end annotation

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    move-object/from16 v2, p1

    .line 4
    .line 5
    move-object/from16 v3, p2

    .line 6
    .line 7
    move/from16 v4, p3

    .line 8
    .line 9
    move-object/from16 v5, p4

    .line 10
    .line 11
    move/from16 v6, p6

    .line 12
    .line 13
    const/4 v0, 0x1

    .line 14
    const v7, 0x17faa842

    .line 15
    .line 16
    .line 17
    move-object/from16 v8, p5

    .line 18
    .line 19
    invoke-interface {v8, v7}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 20
    .line 21
    .line 22
    move-result-object v11

    .line 23
    and-int/lit8 v8, v6, 0x6

    .line 24
    .line 25
    const/4 v15, 0x2

    .line 26
    if-nez v8, :cond_1

    .line 27
    .line 28
    invoke-interface {v11, v1}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 29
    .line 30
    .line 31
    move-result v8

    .line 32
    if-eqz v8, :cond_0

    .line 33
    .line 34
    const/4 v8, 0x4

    .line 35
    goto :goto_0

    .line 36
    :cond_0
    const/4 v8, 0x2

    .line 37
    :goto_0
    or-int/2addr v8, v6

    .line 38
    goto :goto_1

    .line 39
    :cond_1
    move v8, v6

    .line 40
    :goto_1
    and-int/lit8 v9, v6, 0x30

    .line 41
    .line 42
    if-nez v9, :cond_3

    .line 43
    .line 44
    invoke-interface {v11, v2}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 45
    .line 46
    .line 47
    move-result v9

    .line 48
    if-eqz v9, :cond_2

    .line 49
    .line 50
    const/16 v9, 0x20

    .line 51
    .line 52
    goto :goto_2

    .line 53
    :cond_2
    const/16 v9, 0x10

    .line 54
    .line 55
    :goto_2
    or-int/2addr v8, v9

    .line 56
    :cond_3
    and-int/lit16 v9, v6, 0x180

    .line 57
    .line 58
    if-nez v9, :cond_6

    .line 59
    .line 60
    and-int/lit16 v9, v6, 0x200

    .line 61
    .line 62
    if-nez v9, :cond_4

    .line 63
    .line 64
    invoke-interface {v11, v3}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 65
    .line 66
    .line 67
    move-result v9

    .line 68
    goto :goto_3

    .line 69
    :cond_4
    invoke-interface {v11, v3}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 70
    .line 71
    .line 72
    move-result v9

    .line 73
    :goto_3
    if-eqz v9, :cond_5

    .line 74
    .line 75
    const/16 v9, 0x100

    .line 76
    .line 77
    goto :goto_4

    .line 78
    :cond_5
    const/16 v9, 0x80

    .line 79
    .line 80
    :goto_4
    or-int/2addr v8, v9

    .line 81
    :cond_6
    and-int/lit16 v9, v6, 0xc00

    .line 82
    .line 83
    if-nez v9, :cond_8

    .line 84
    .line 85
    invoke-interface {v11, v4}, Landroidx/compose/runtime/j;->x(I)Z

    .line 86
    .line 87
    .line 88
    move-result v9

    .line 89
    if-eqz v9, :cond_7

    .line 90
    .line 91
    const/16 v9, 0x800

    .line 92
    .line 93
    goto :goto_5

    .line 94
    :cond_7
    const/16 v9, 0x400

    .line 95
    .line 96
    :goto_5
    or-int/2addr v8, v9

    .line 97
    :cond_8
    and-int/lit16 v9, v6, 0x6000

    .line 98
    .line 99
    if-nez v9, :cond_a

    .line 100
    .line 101
    invoke-interface {v11, v5}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 102
    .line 103
    .line 104
    move-result v9

    .line 105
    if-eqz v9, :cond_9

    .line 106
    .line 107
    const/16 v9, 0x4000

    .line 108
    .line 109
    goto :goto_6

    .line 110
    :cond_9
    const/16 v9, 0x2000

    .line 111
    .line 112
    :goto_6
    or-int/2addr v8, v9

    .line 113
    :cond_a
    and-int/lit16 v9, v8, 0x2493

    .line 114
    .line 115
    const/16 v10, 0x2492

    .line 116
    .line 117
    if-ne v9, v10, :cond_c

    .line 118
    .line 119
    invoke-interface {v11}, Landroidx/compose/runtime/j;->c()Z

    .line 120
    .line 121
    .line 122
    move-result v9

    .line 123
    if-nez v9, :cond_b

    .line 124
    .line 125
    goto :goto_7

    .line 126
    :cond_b
    invoke-interface {v11}, Landroidx/compose/runtime/j;->n()V

    .line 127
    .line 128
    .line 129
    goto/16 :goto_e

    .line 130
    .line 131
    :cond_c
    :goto_7
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 132
    .line 133
    .line 134
    move-result v9

    .line 135
    if-eqz v9, :cond_d

    .line 136
    .line 137
    const/4 v9, -0x1

    .line 138
    const-string v10, "org.xbet.statistic.main.common.presentation.last_games.components.LastGamesPage (LastGamesPage.kt:24)"

    .line 139
    .line 140
    invoke-static {v7, v8, v9, v10}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 141
    .line 142
    .line 143
    :cond_d
    invoke-interface {v5}, Ljava/util/Collection;->isEmpty()Z

    .line 144
    .line 145
    .line 146
    move-result v7

    .line 147
    const/4 v9, 0x0

    .line 148
    if-nez v7, :cond_1d

    .line 149
    .line 150
    const v7, 0x7a474b24

    .line 151
    .line 152
    .line 153
    invoke-interface {v11, v7}, Landroidx/compose/runtime/j;->t(I)V

    .line 154
    .line 155
    .line 156
    sget-object v7, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 157
    .line 158
    const/4 v10, 0x0

    .line 159
    invoke-static {v7, v10, v0, v9}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 160
    .line 161
    .line 162
    move-result-object v7

    .line 163
    const/4 v12, 0x0

    .line 164
    const/4 v13, 0x3

    .line 165
    invoke-static {v7, v9, v12, v13, v9}, Landroidx/compose/foundation/layout/SizeKt;->E(Landroidx/compose/ui/l;Landroidx/compose/ui/e$c;ZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 166
    .line 167
    .line 168
    move-result-object v7

    .line 169
    sget-object v16, LA11/a;->a:LA11/a;

    .line 170
    .line 171
    invoke-virtual/range {v16 .. v16}, LA11/a;->l1()F

    .line 172
    .line 173
    .line 174
    move-result v14

    .line 175
    invoke-static {v7, v14, v10, v15, v9}, Landroidx/compose/foundation/layout/PaddingKt;->k(Landroidx/compose/ui/l;FFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 176
    .line 177
    .line 178
    move-result-object v7

    .line 179
    sget-object v14, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 180
    .line 181
    invoke-virtual {v14}, Landroidx/compose/foundation/layout/Arrangement;->h()Landroidx/compose/foundation/layout/Arrangement$m;

    .line 182
    .line 183
    .line 184
    move-result-object v14

    .line 185
    sget-object v16, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 186
    .line 187
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/ui/e$a;->k()Landroidx/compose/ui/e$b;

    .line 188
    .line 189
    .line 190
    move-result-object v15

    .line 191
    invoke-static {v14, v15, v11, v12}, Landroidx/compose/foundation/layout/k;->a(Landroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 192
    .line 193
    .line 194
    move-result-object v14

    .line 195
    invoke-static {v11, v12}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 196
    .line 197
    .line 198
    move-result v15

    .line 199
    invoke-interface {v11}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 200
    .line 201
    .line 202
    move-result-object v12

    .line 203
    invoke-static {v11, v7}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 204
    .line 205
    .line 206
    move-result-object v7

    .line 207
    sget-object v17, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 208
    .line 209
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 210
    .line 211
    .line 212
    move-result-object v13

    .line 213
    invoke-interface {v11}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 214
    .line 215
    .line 216
    move-result-object v18

    .line 217
    invoke-static/range {v18 .. v18}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 218
    .line 219
    .line 220
    move-result v18

    .line 221
    if-nez v18, :cond_e

    .line 222
    .line 223
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 224
    .line 225
    .line 226
    :cond_e
    invoke-interface {v11}, Landroidx/compose/runtime/j;->l()V

    .line 227
    .line 228
    .line 229
    invoke-interface {v11}, Landroidx/compose/runtime/j;->B()Z

    .line 230
    .line 231
    .line 232
    move-result v18

    .line 233
    if-eqz v18, :cond_f

    .line 234
    .line 235
    invoke-interface {v11, v13}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 236
    .line 237
    .line 238
    goto :goto_8

    .line 239
    :cond_f
    invoke-interface {v11}, Landroidx/compose/runtime/j;->h()V

    .line 240
    .line 241
    .line 242
    :goto_8
    invoke-static {v11}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 243
    .line 244
    .line 245
    move-result-object v13

    .line 246
    const/16 v18, 0x1

    .line 247
    .line 248
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 249
    .line 250
    .line 251
    move-result-object v0

    .line 252
    invoke-static {v13, v14, v0}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 253
    .line 254
    .line 255
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 256
    .line 257
    .line 258
    move-result-object v0

    .line 259
    invoke-static {v13, v12, v0}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 260
    .line 261
    .line 262
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 263
    .line 264
    .line 265
    move-result-object v0

    .line 266
    invoke-interface {v13}, Landroidx/compose/runtime/j;->B()Z

    .line 267
    .line 268
    .line 269
    move-result v12

    .line 270
    if-nez v12, :cond_10

    .line 271
    .line 272
    invoke-interface {v13}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 273
    .line 274
    .line 275
    move-result-object v12

    .line 276
    invoke-static {v15}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 277
    .line 278
    .line 279
    move-result-object v14

    .line 280
    invoke-static {v12, v14}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 281
    .line 282
    .line 283
    move-result v12

    .line 284
    if-nez v12, :cond_11

    .line 285
    .line 286
    :cond_10
    invoke-static {v15}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 287
    .line 288
    .line 289
    move-result-object v12

    .line 290
    invoke-interface {v13, v12}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 291
    .line 292
    .line 293
    invoke-static {v15}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 294
    .line 295
    .line 296
    move-result-object v12

    .line 297
    invoke-interface {v13, v12, v0}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 298
    .line 299
    .line 300
    :cond_11
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 301
    .line 302
    .line 303
    move-result-object v0

    .line 304
    invoke-static {v13, v7, v0}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 305
    .line 306
    .line 307
    sget-object v0, Landroidx/compose/foundation/layout/n;->a:Landroidx/compose/foundation/layout/n;

    .line 308
    .line 309
    const v0, 0x6c2bf8b

    .line 310
    .line 311
    .line 312
    invoke-interface {v11, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 313
    .line 314
    .line 315
    add-int/lit8 v0, v4, -0x1

    .line 316
    .line 317
    if-ltz v0, :cond_12

    .line 318
    .line 319
    const/4 v7, 0x0

    .line 320
    :goto_9
    invoke-interface {v5}, Ljava/util/List;->size()I

    .line 321
    .line 322
    .line 323
    move-result v12

    .line 324
    if-lt v7, v12, :cond_13

    .line 325
    .line 326
    :cond_12
    move/from16 v19, v8

    .line 327
    .line 328
    move-object v15, v9

    .line 329
    const/4 v1, 0x0

    .line 330
    const/4 v13, 0x1

    .line 331
    goto/16 :goto_b

    .line 332
    .line 333
    :cond_13
    invoke-interface {v5, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 334
    .line 335
    .line 336
    move-result-object v12

    .line 337
    move-object v14, v12

    .line 338
    check-cast v14, LUG0/a;

    .line 339
    .line 340
    sget-object v12, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 341
    .line 342
    const/4 v13, 0x1

    .line 343
    invoke-static {v12, v10, v13, v9}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 344
    .line 345
    .line 346
    move-result-object v12

    .line 347
    const/4 v13, 0x0

    .line 348
    const/4 v15, 0x3

    .line 349
    invoke-static {v12, v9, v13, v15, v9}, Landroidx/compose/foundation/layout/SizeKt;->E(Landroidx/compose/ui/l;Landroidx/compose/ui/e$c;ZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 350
    .line 351
    .line 352
    move-result-object v12

    .line 353
    sget-object v16, LA11/a;->a:LA11/a;

    .line 354
    .line 355
    invoke-virtual/range {v16 .. v16}, LA11/a;->h1()F

    .line 356
    .line 357
    .line 358
    move-result v9

    .line 359
    invoke-virtual/range {v16 .. v16}, LA11/a;->A1()F

    .line 360
    .line 361
    .line 362
    move-result v10

    .line 363
    invoke-static {v12, v9, v10}, Landroidx/compose/foundation/layout/PaddingKt;->j(Landroidx/compose/ui/l;FF)Landroidx/compose/ui/l;

    .line 364
    .line 365
    .line 366
    move-result-object v9

    .line 367
    const v10, -0x615d173a

    .line 368
    .line 369
    .line 370
    invoke-interface {v11, v10}, Landroidx/compose/runtime/j;->t(I)V

    .line 371
    .line 372
    .line 373
    and-int/lit8 v10, v8, 0xe

    .line 374
    .line 375
    const/4 v12, 0x4

    .line 376
    if-ne v10, v12, :cond_14

    .line 377
    .line 378
    const/4 v10, 0x1

    .line 379
    goto :goto_a

    .line 380
    :cond_14
    const/4 v10, 0x0

    .line 381
    :goto_a
    invoke-interface {v11, v14}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 382
    .line 383
    .line 384
    move-result v16

    .line 385
    or-int v10, v10, v16

    .line 386
    .line 387
    invoke-interface {v11}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 388
    .line 389
    .line 390
    move-result-object v12

    .line 391
    if-nez v10, :cond_15

    .line 392
    .line 393
    sget-object v10, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 394
    .line 395
    invoke-virtual {v10}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 396
    .line 397
    .line 398
    move-result-object v10

    .line 399
    if-ne v12, v10, :cond_16

    .line 400
    .line 401
    :cond_15
    new-instance v12, LTG0/a;

    .line 402
    .line 403
    invoke-direct {v12, v1, v14}, LTG0/a;-><init>(Lkotlin/jvm/functions/Function1;LUG0/a;)V

    .line 404
    .line 405
    .line 406
    invoke-interface {v11, v12}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 407
    .line 408
    .line 409
    :cond_16
    move-object v10, v12

    .line 410
    check-cast v10, Lkotlin/jvm/functions/Function0;

    .line 411
    .line 412
    invoke-interface {v11}, Landroidx/compose/runtime/j;->q()V

    .line 413
    .line 414
    .line 415
    const/4 v12, 0x0

    .line 416
    const/16 v16, 0x0

    .line 417
    .line 418
    const/4 v13, 0x1

    .line 419
    move/from16 v19, v8

    .line 420
    .line 421
    move-object v8, v9

    .line 422
    const/4 v9, 0x0

    .line 423
    const/4 v1, 0x0

    .line 424
    const/4 v15, 0x0

    .line 425
    invoke-static/range {v8 .. v13}, LF11/m;->c(Landroidx/compose/ui/l;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)Landroidx/compose/ui/l;

    .line 426
    .line 427
    .line 428
    move-result-object v8

    .line 429
    invoke-static {v14, v8, v11, v1, v1}, LTG0/f;->c(LUG0/a;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 430
    .line 431
    .line 432
    const/4 v13, 0x1

    .line 433
    if-eq v7, v0, :cond_17

    .line 434
    .line 435
    add-int/2addr v7, v13

    .line 436
    move-object/from16 v1, p0

    .line 437
    .line 438
    move-object v9, v15

    .line 439
    move/from16 v8, v19

    .line 440
    .line 441
    const/4 v10, 0x0

    .line 442
    const/16 v18, 0x1

    .line 443
    .line 444
    goto :goto_9

    .line 445
    :cond_17
    :goto_b
    invoke-interface {v11}, Landroidx/compose/runtime/j;->q()V

    .line 446
    .line 447
    .line 448
    const v0, 0x6c3170e

    .line 449
    .line 450
    .line 451
    invoke-interface {v11, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 452
    .line 453
    .line 454
    invoke-interface {v5}, Ljava/util/List;->size()I

    .line 455
    .line 456
    .line 457
    move-result v0

    .line 458
    if-le v0, v4, :cond_1c

    .line 459
    .line 460
    sget-object v0, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 461
    .line 462
    const/4 v7, 0x0

    .line 463
    invoke-static {v0, v7, v13, v15}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 464
    .line 465
    .line 466
    move-result-object v0

    .line 467
    const/4 v7, 0x3

    .line 468
    invoke-static {v0, v15, v1, v7, v15}, Landroidx/compose/foundation/layout/SizeKt;->E(Landroidx/compose/ui/l;Landroidx/compose/ui/e$c;ZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 469
    .line 470
    .line 471
    move-result-object v20

    .line 472
    sget-object v0, LA11/a;->a:LA11/a;

    .line 473
    .line 474
    invoke-virtual {v0}, LA11/a;->l1()F

    .line 475
    .line 476
    .line 477
    move-result v22

    .line 478
    const/16 v25, 0xd

    .line 479
    .line 480
    const/16 v26, 0x0

    .line 481
    .line 482
    const/16 v21, 0x0

    .line 483
    .line 484
    const/16 v23, 0x0

    .line 485
    .line 486
    const/16 v24, 0x0

    .line 487
    .line 488
    invoke-static/range {v20 .. v26}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 489
    .line 490
    .line 491
    move-result-object v0

    .line 492
    sget-object v8, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 493
    .line 494
    invoke-virtual {v8}, Landroidx/compose/ui/e$a;->o()Landroidx/compose/ui/e;

    .line 495
    .line 496
    .line 497
    move-result-object v8

    .line 498
    invoke-static {v8, v1}, Landroidx/compose/foundation/layout/BoxKt;->g(Landroidx/compose/ui/e;Z)Landroidx/compose/ui/layout/J;

    .line 499
    .line 500
    .line 501
    move-result-object v8

    .line 502
    invoke-static {v11, v1}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 503
    .line 504
    .line 505
    move-result v1

    .line 506
    invoke-interface {v11}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 507
    .line 508
    .line 509
    move-result-object v9

    .line 510
    invoke-static {v11, v0}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 511
    .line 512
    .line 513
    move-result-object v0

    .line 514
    sget-object v10, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 515
    .line 516
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 517
    .line 518
    .line 519
    move-result-object v12

    .line 520
    invoke-interface {v11}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 521
    .line 522
    .line 523
    move-result-object v13

    .line 524
    invoke-static {v13}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 525
    .line 526
    .line 527
    move-result v13

    .line 528
    if-nez v13, :cond_18

    .line 529
    .line 530
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 531
    .line 532
    .line 533
    :cond_18
    invoke-interface {v11}, Landroidx/compose/runtime/j;->l()V

    .line 534
    .line 535
    .line 536
    invoke-interface {v11}, Landroidx/compose/runtime/j;->B()Z

    .line 537
    .line 538
    .line 539
    move-result v13

    .line 540
    if-eqz v13, :cond_19

    .line 541
    .line 542
    invoke-interface {v11, v12}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 543
    .line 544
    .line 545
    goto :goto_c

    .line 546
    :cond_19
    invoke-interface {v11}, Landroidx/compose/runtime/j;->h()V

    .line 547
    .line 548
    .line 549
    :goto_c
    invoke-static {v11}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 550
    .line 551
    .line 552
    move-result-object v12

    .line 553
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 554
    .line 555
    .line 556
    move-result-object v13

    .line 557
    invoke-static {v12, v8, v13}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 558
    .line 559
    .line 560
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 561
    .line 562
    .line 563
    move-result-object v8

    .line 564
    invoke-static {v12, v9, v8}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 565
    .line 566
    .line 567
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 568
    .line 569
    .line 570
    move-result-object v8

    .line 571
    invoke-interface {v12}, Landroidx/compose/runtime/j;->B()Z

    .line 572
    .line 573
    .line 574
    move-result v9

    .line 575
    if-nez v9, :cond_1a

    .line 576
    .line 577
    invoke-interface {v12}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 578
    .line 579
    .line 580
    move-result-object v9

    .line 581
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 582
    .line 583
    .line 584
    move-result-object v13

    .line 585
    invoke-static {v9, v13}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 586
    .line 587
    .line 588
    move-result v9

    .line 589
    if-nez v9, :cond_1b

    .line 590
    .line 591
    :cond_1a
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 592
    .line 593
    .line 594
    move-result-object v9

    .line 595
    invoke-interface {v12, v9}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 596
    .line 597
    .line 598
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 599
    .line 600
    .line 601
    move-result-object v1

    .line 602
    invoke-interface {v12, v1, v8}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 603
    .line 604
    .line 605
    :cond_1b
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 606
    .line 607
    .line 608
    move-result-object v1

    .line 609
    invoke-static {v12, v0, v1}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 610
    .line 611
    .line 612
    sget-object v0, Landroidx/compose/foundation/layout/BoxScopeInstance;->a:Landroidx/compose/foundation/layout/BoxScopeInstance;

    .line 613
    .line 614
    shr-int/lit8 v0, v19, 0x3

    .line 615
    .line 616
    and-int/lit8 v0, v0, 0xe

    .line 617
    .line 618
    const/4 v1, 0x2

    .line 619
    invoke-static {v2, v15, v11, v0, v1}, LQG0/b;->b(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 620
    .line 621
    .line 622
    invoke-interface {v11}, Landroidx/compose/runtime/j;->j()V

    .line 623
    .line 624
    .line 625
    :cond_1c
    invoke-interface {v11}, Landroidx/compose/runtime/j;->q()V

    .line 626
    .line 627
    .line 628
    invoke-interface {v11}, Landroidx/compose/runtime/j;->j()V

    .line 629
    .line 630
    .line 631
    invoke-interface {v11}, Landroidx/compose/runtime/j;->q()V

    .line 632
    .line 633
    .line 634
    goto :goto_d

    .line 635
    :cond_1d
    move/from16 v19, v8

    .line 636
    .line 637
    move-object v15, v9

    .line 638
    const v0, 0x7a5b8ba6

    .line 639
    .line 640
    .line 641
    invoke-interface {v11, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 642
    .line 643
    .line 644
    sget v0, Lorg/xbet/uikit/components/lottie_empty/n;->j:I

    .line 645
    .line 646
    shr-int/lit8 v1, v19, 0x6

    .line 647
    .line 648
    and-int/lit8 v1, v1, 0xe

    .line 649
    .line 650
    or-int/2addr v0, v1

    .line 651
    const/4 v1, 0x2

    .line 652
    invoke-static {v3, v15, v11, v0, v1}, LJN0/i;->j(Lorg/xbet/uikit/components/lottie_empty/n;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 653
    .line 654
    .line 655
    invoke-interface {v11}, Landroidx/compose/runtime/j;->q()V

    .line 656
    .line 657
    .line 658
    :goto_d
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 659
    .line 660
    .line 661
    move-result v0

    .line 662
    if-eqz v0, :cond_1e

    .line 663
    .line 664
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 665
    .line 666
    .line 667
    :cond_1e
    :goto_e
    invoke-interface {v11}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 668
    .line 669
    .line 670
    move-result-object v7

    .line 671
    if-eqz v7, :cond_1f

    .line 672
    .line 673
    new-instance v0, LTG0/b;

    .line 674
    .line 675
    move-object/from16 v1, p0

    .line 676
    .line 677
    invoke-direct/range {v0 .. v6}, LTG0/b;-><init>(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/lottie_empty/n;ILjava/util/List;I)V

    .line 678
    .line 679
    .line 680
    invoke-interface {v7, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 681
    .line 682
    .line 683
    :cond_1f
    return-void
.end method

.method public static final d(Lkotlin/jvm/functions/Function1;LUG0/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final e(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/lottie_empty/n;ILjava/util/List;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 7

    .line 1
    or-int/lit8 p5, p5, 0x1

    .line 2
    .line 3
    invoke-static {p5}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result v6

    .line 7
    move-object v0, p0

    .line 8
    move-object v1, p1

    .line 9
    move-object v2, p2

    .line 10
    move v3, p3

    .line 11
    move-object v4, p4

    .line 12
    move-object v5, p6

    .line 13
    invoke-static/range {v0 .. v6}, LTG0/c;->c(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/lottie_empty/n;ILjava/util/List;Landroidx/compose/runtime/j;I)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method
