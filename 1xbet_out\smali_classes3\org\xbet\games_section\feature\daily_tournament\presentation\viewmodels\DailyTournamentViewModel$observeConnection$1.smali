.class final Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.daily_tournament.presentation.viewmodels.DailyTournamentViewModel$observeConnection$1"
    f = "DailyTournamentViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;->E3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/Boolean;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "connected",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;

    iget-object v1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;Lkotlin/coroutines/e;)V

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    iput-boolean p1, v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;->Z$0:Z

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;->invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_2

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-boolean p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;->Z$0:Z

    .line 12
    .line 13
    if-eqz p1, :cond_0

    .line 14
    .line 15
    iget-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;

    .line 16
    .line 17
    invoke-static {p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;->x3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;)V

    .line 18
    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    iget-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;

    .line 22
    .line 23
    invoke-static {p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;->r3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;)Z

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    if-nez p1, :cond_1

    .line 28
    .line 29
    iget-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;

    .line 30
    .line 31
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$a$c;

    .line 32
    .line 33
    invoke-static {p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;->s3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;)Lorg/xbet/uikit/components/lottie/a;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$a$c;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 38
    .line 39
    .line 40
    invoke-static {p1, v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;->y3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$a;)V

    .line 41
    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_1
    iget-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;

    .line 45
    .line 46
    invoke-static {p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;->q3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;)Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/a;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/a;->a()V

    .line 51
    .line 52
    .line 53
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 54
    .line 55
    return-object p1

    .line 56
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 57
    .line 58
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 59
    .line 60
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 61
    .line 62
    .line 63
    throw p1
.end method
