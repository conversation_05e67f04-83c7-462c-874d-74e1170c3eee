.class public interface abstract Lk2/s$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lk2/s;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# static fields
.field public static final a:Lk2/s$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lk2/s$a$a;

    .line 2
    .line 3
    invoke-direct {v0}, Lk2/s$a$a;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Lk2/s$a;->a:Lk2/s$a;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public abstract a(Landroidx/media3/common/r;)Z
.end method

.method public abstract b(Landroidx/media3/common/r;)I
.end method

.method public abstract c(Landroidx/media3/common/r;)Lk2/s;
.end method
