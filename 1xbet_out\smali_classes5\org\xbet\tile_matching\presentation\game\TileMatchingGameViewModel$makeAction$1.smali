.class final Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.tile_matching.presentation.game.TileMatchingGameViewModel$makeAction$1"
    f = "TileMatchingGameViewModel.kt"
    l = {
        0x82,
        0x88
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->c4(II)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $column:I

.field final synthetic $row:I

.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;IILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;",
            "II",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    iput p2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->$row:I

    iput p3, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->$column:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;

    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    iget v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->$row:I

    iget v2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->$column:I

    invoke-direct {p1, v0, v1, v2, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;IILkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->L$0:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, LzT0/d;

    .line 18
    .line 19
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    goto :goto_2

    .line 23
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 24
    .line 25
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 26
    .line 27
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    throw p1

    .line 31
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 39
    .line 40
    invoke-static {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->v3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)V

    .line 41
    .line 42
    .line 43
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 44
    .line 45
    invoke-static {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->F3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    iget v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->$row:I

    .line 50
    .line 51
    iget v4, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->$column:I

    .line 52
    .line 53
    iget-object v5, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 54
    .line 55
    invoke-static {v5}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->z3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)LTv/e;

    .line 56
    .line 57
    .line 58
    move-result-object v5

    .line 59
    invoke-virtual {v5}, LTv/e;->j()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 60
    .line 61
    .line 62
    move-result-object v5

    .line 63
    iput v3, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->label:I

    .line 64
    .line 65
    invoke-virtual {p1, v1, v4, v5, p0}, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;->a(IILcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    if-ne p1, v0, :cond_3

    .line 70
    .line 71
    goto :goto_1

    .line 72
    :cond_3
    :goto_0
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 73
    .line 74
    invoke-static {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->D3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lorg/xbet/tile_matching/domain/usecases/b;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    invoke-virtual {p1}, Lorg/xbet/tile_matching/domain/usecases/b;->a()LzT0/d;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 83
    .line 84
    invoke-static {v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->x3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 85
    .line 86
    .line 87
    move-result-object v1

    .line 88
    sget-object v3, LTv/a$a;->a:LTv/a$a;

    .line 89
    .line 90
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->L$0:Ljava/lang/Object;

    .line 91
    .line 92
    iput v2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->label:I

    .line 93
    .line 94
    invoke-virtual {v1, v3, p0}, Lorg/xbet/core/domain/usecases/AddCommandScenario;->l(LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 95
    .line 96
    .line 97
    move-result-object v1

    .line 98
    if-ne v1, v0, :cond_4

    .line 99
    .line 100
    :goto_1
    return-object v0

    .line 101
    :cond_4
    move-object v0, p1

    .line 102
    :goto_2
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 103
    .line 104
    invoke-static {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->B3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lkotlinx/coroutines/flow/V;

    .line 105
    .line 106
    .line 107
    move-result-object p1

    .line 108
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$d;

    .line 109
    .line 110
    invoke-virtual {v0}, LzT0/d;->c()Ljava/util/List;

    .line 111
    .line 112
    .line 113
    move-result-object v2

    .line 114
    invoke-virtual {v0}, LzT0/d;->e()Ljava/util/List;

    .line 115
    .line 116
    .line 117
    move-result-object v3

    .line 118
    invoke-virtual {v0}, LzT0/d;->d()Ljava/util/List;

    .line 119
    .line 120
    .line 121
    move-result-object v4

    .line 122
    invoke-direct {v1, v2, v3, v4}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$d;-><init>(Ljava/util/List;Ljava/util/List;Ljava/util/List;)V

    .line 123
    .line 124
    .line 125
    invoke-interface {p1, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 126
    .line 127
    .line 128
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 129
    .line 130
    invoke-virtual {v0}, LzT0/d;->a()Ljava/util/List;

    .line 131
    .line 132
    .line 133
    move-result-object v0

    .line 134
    invoke-static {p1, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->u3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Ljava/util/List;)V

    .line 135
    .line 136
    .line 137
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 138
    .line 139
    return-object p1
.end method
