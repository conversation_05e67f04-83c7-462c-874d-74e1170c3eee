.class public final LnM0/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00aa\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008.\u0018\u00002\u00020\u0001B\u00b9\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u00a2\u0006\u0004\u0008.\u0010/J7\u00109\u001a\u0002082\u0006\u00101\u001a\u0002002\u0006\u00103\u001a\u0002022\u0006\u00105\u001a\u0002042\u0006\u00106\u001a\u0002042\u0006\u00107\u001a\u000204H\u0000\u00a2\u0006\u0004\u00089\u0010:R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u0010;R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010?R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010AR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010CR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010ER\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010GR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008N\u0010OR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010QR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008R\u0010SR\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008T\u0010UR\u0014\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008V\u0010WR\u0014\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008X\u0010YR\u0014\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Z\u0010[R\u0014\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\\\u0010]R\u0014\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008^\u0010_R\u0014\u0010)\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008`\u0010aR\u0014\u0010+\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008b\u0010cR\u0014\u0010-\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008d\u0010e\u00a8\u0006f"
    }
    d2 = {
        "LnM0/i;",
        "LQW0/a;",
        "LQW0/c;",
        "coroutinesLib",
        "Lf8/g;",
        "serviceGenerator",
        "LTn/a;",
        "sportRepository",
        "LrM0/a;",
        "stageNetBottomSheetLocalDataSource",
        "Lorg/xbet/onexdatabase/OnexDatabase;",
        "onexDatabase",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Li8/m;",
        "getThemeUseCase",
        "LkC0/a;",
        "gameScreenGeneralFactory",
        "Lqy/b;",
        "cyberGameStatisticScreenFactory",
        "LSX0/a;",
        "lottieConfigurator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "LHX0/e;",
        "resourceManager",
        "Lc8/h;",
        "requestParamsDataSource",
        "LJo0/a;",
        "specialEventMainFeature",
        "LfR0/a;",
        "winterGamesFeature",
        "LGL0/a;",
        "stadiumFeature",
        "LVC0/a;",
        "cyclingFeature",
        "LbL0/a;",
        "statisticRatingScreenFactory",
        "LQN0/b;",
        "teamStatisticFeature",
        "LNF0/a;",
        "horsesMenuScreenFactory",
        "LAP0/a;",
        "tennisScreenFactory",
        "LLD0/a;",
        "statisticFeature",
        "<init>",
        "(LQW0/c;Lf8/g;LTn/a;LrM0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/ui_common/utils/M;Li8/m;LkC0/a;Lqy/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LHX0/e;Lc8/h;LJo0/a;LfR0/a;LGL0/a;LVC0/a;LbL0/a;LQN0/b;LNF0/a;LAP0/a;LLD0/a;)V",
        "LwX0/c;",
        "router",
        "Lorg/xbet/statistic/stage/api/domain/TypeStageId;",
        "stageId",
        "",
        "sportId",
        "subSportId",
        "globalChampId",
        "LnM0/h;",
        "a",
        "(LwX0/c;Lorg/xbet/statistic/stage/api/domain/TypeStageId;JJJ)LnM0/h;",
        "LQW0/c;",
        "b",
        "Lf8/g;",
        "c",
        "LTn/a;",
        "d",
        "LrM0/a;",
        "e",
        "Lorg/xbet/onexdatabase/OnexDatabase;",
        "f",
        "Lorg/xbet/ui_common/utils/M;",
        "g",
        "Li8/m;",
        "h",
        "LkC0/a;",
        "i",
        "Lqy/b;",
        "j",
        "LSX0/a;",
        "k",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "l",
        "LHX0/e;",
        "m",
        "Lc8/h;",
        "n",
        "LJo0/a;",
        "o",
        "LfR0/a;",
        "p",
        "LGL0/a;",
        "q",
        "LVC0/a;",
        "r",
        "LbL0/a;",
        "s",
        "LQN0/b;",
        "t",
        "LNF0/a;",
        "u",
        "LAP0/a;",
        "v",
        "LLD0/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LQW0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LTn/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:LrM0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xbet/onexdatabase/OnexDatabase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Li8/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:LkC0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lqy/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:LJo0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:LfR0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:LGL0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q:LVC0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final r:LbL0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final s:LQN0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final t:LNF0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final u:LAP0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v:LLD0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lf8/g;LTn/a;LrM0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/ui_common/utils/M;Li8/m;LkC0/a;Lqy/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LHX0/e;Lc8/h;LJo0/a;LfR0/a;LGL0/a;LVC0/a;LbL0/a;LQN0/b;LNF0/a;LAP0/a;LLD0/a;)V
    .locals 0
    .param p1    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LTn/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LrM0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/onexdatabase/OnexDatabase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Li8/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LkC0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lqy/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LJo0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # LfR0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # LGL0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # LVC0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # LbL0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # LQN0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # LNF0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # LAP0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # LLD0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LnM0/i;->a:LQW0/c;

    .line 5
    .line 6
    iput-object p2, p0, LnM0/i;->b:Lf8/g;

    .line 7
    .line 8
    iput-object p3, p0, LnM0/i;->c:LTn/a;

    .line 9
    .line 10
    iput-object p4, p0, LnM0/i;->d:LrM0/a;

    .line 11
    .line 12
    iput-object p5, p0, LnM0/i;->e:Lorg/xbet/onexdatabase/OnexDatabase;

    .line 13
    .line 14
    iput-object p6, p0, LnM0/i;->f:Lorg/xbet/ui_common/utils/M;

    .line 15
    .line 16
    iput-object p7, p0, LnM0/i;->g:Li8/m;

    .line 17
    .line 18
    iput-object p8, p0, LnM0/i;->h:LkC0/a;

    .line 19
    .line 20
    iput-object p9, p0, LnM0/i;->i:Lqy/b;

    .line 21
    .line 22
    iput-object p10, p0, LnM0/i;->j:LSX0/a;

    .line 23
    .line 24
    iput-object p11, p0, LnM0/i;->k:Lorg/xbet/ui_common/utils/internet/a;

    .line 25
    .line 26
    iput-object p12, p0, LnM0/i;->l:LHX0/e;

    .line 27
    .line 28
    iput-object p13, p0, LnM0/i;->m:Lc8/h;

    .line 29
    .line 30
    iput-object p14, p0, LnM0/i;->n:LJo0/a;

    .line 31
    .line 32
    iput-object p15, p0, LnM0/i;->o:LfR0/a;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, LnM0/i;->p:LGL0/a;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, LnM0/i;->q:LVC0/a;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, LnM0/i;->r:LbL0/a;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, LnM0/i;->s:LQN0/b;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, LnM0/i;->t:LNF0/a;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, LnM0/i;->u:LAP0/a;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, LnM0/i;->v:LLD0/a;

    .line 61
    .line 62
    return-void
.end method


# virtual methods
.method public final a(LwX0/c;Lorg/xbet/statistic/stage/api/domain/TypeStageId;JJJ)LnM0/h;
    .locals 32
    .param p1    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/statistic/stage/api/domain/TypeStageId;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, LnM0/a;->a()LnM0/h$a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, v0, LnM0/i;->a:LQW0/c;

    .line 8
    .line 9
    iget-object v12, v0, LnM0/i;->b:Lf8/g;

    .line 10
    .line 11
    iget-object v13, v0, LnM0/i;->f:Lorg/xbet/ui_common/utils/M;

    .line 12
    .line 13
    iget-object v14, v0, LnM0/i;->c:LTn/a;

    .line 14
    .line 15
    iget-object v15, v0, LnM0/i;->d:LrM0/a;

    .line 16
    .line 17
    iget-object v3, v0, LnM0/i;->e:Lorg/xbet/onexdatabase/OnexDatabase;

    .line 18
    .line 19
    iget-object v4, v0, LnM0/i;->g:Li8/m;

    .line 20
    .line 21
    iget-object v5, v0, LnM0/i;->h:LkC0/a;

    .line 22
    .line 23
    iget-object v10, v0, LnM0/i;->r:LbL0/a;

    .line 24
    .line 25
    iget-object v6, v0, LnM0/i;->i:Lqy/b;

    .line 26
    .line 27
    iget-object v7, v0, LnM0/i;->j:LSX0/a;

    .line 28
    .line 29
    iget-object v8, v0, LnM0/i;->k:Lorg/xbet/ui_common/utils/internet/a;

    .line 30
    .line 31
    iget-object v9, v0, LnM0/i;->m:Lc8/h;

    .line 32
    .line 33
    iget-object v11, v0, LnM0/i;->l:LHX0/e;

    .line 34
    .line 35
    move-object/from16 v16, v3

    .line 36
    .line 37
    iget-object v3, v0, LnM0/i;->n:LJo0/a;

    .line 38
    .line 39
    move-object/from16 v19, v4

    .line 40
    .line 41
    iget-object v4, v0, LnM0/i;->o:LfR0/a;

    .line 42
    .line 43
    move-object/from16 v26, v5

    .line 44
    .line 45
    iget-object v5, v0, LnM0/i;->q:LVC0/a;

    .line 46
    .line 47
    move-object/from16 v27, v6

    .line 48
    .line 49
    iget-object v6, v0, LnM0/i;->p:LGL0/a;

    .line 50
    .line 51
    move-object/from16 v28, v7

    .line 52
    .line 53
    iget-object v7, v0, LnM0/i;->s:LQN0/b;

    .line 54
    .line 55
    move-object/from16 v17, v1

    .line 56
    .line 57
    iget-object v1, v0, LnM0/i;->t:LNF0/a;

    .line 58
    .line 59
    move-object/from16 v30, v9

    .line 60
    .line 61
    iget-object v9, v0, LnM0/i;->u:LAP0/a;

    .line 62
    .line 63
    move-object/from16 v29, v8

    .line 64
    .line 65
    iget-object v8, v0, LnM0/i;->v:LLD0/a;

    .line 66
    .line 67
    move-object/from16 v18, p2

    .line 68
    .line 69
    move-wide/from16 v20, p3

    .line 70
    .line 71
    move-wide/from16 v22, p5

    .line 72
    .line 73
    move-wide/from16 v24, p7

    .line 74
    .line 75
    move-object/from16 v31, v1

    .line 76
    .line 77
    move-object/from16 v1, v17

    .line 78
    .line 79
    move-object/from16 v17, v11

    .line 80
    .line 81
    move-object/from16 v11, p1

    .line 82
    .line 83
    invoke-interface/range {v1 .. v31}, LnM0/h$a;->a(LQW0/c;LJo0/a;LfR0/a;LVC0/a;LGL0/a;LQN0/b;LLD0/a;LAP0/a;LbL0/a;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LrM0/a;Lorg/xbet/onexdatabase/OnexDatabase;LHX0/e;Lorg/xbet/statistic/stage/api/domain/TypeStageId;Li8/m;JJJLkC0/a;Lqy/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LNF0/a;)LnM0/h;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    return-object v1
.end method
