.class public final Lorg/xbet/ui_common/moxy/activities/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0007\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u000f\u0010\u0007\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0007\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "Lorg/xbet/ui_common/moxy/activities/i;",
        "LQW0/a;",
        "Lorg/xbet/onexlocalization/k;",
        "languageRepository",
        "<init>",
        "(Lorg/xbet/onexlocalization/k;)V",
        "Lorg/xbet/ui_common/moxy/activities/h;",
        "a",
        "()Lorg/xbet/ui_common/moxy/activities/h;",
        "Lorg/xbet/onexlocalization/k;",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/onexlocalization/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lorg/xbet/onexlocalization/k;)V
    .locals 0
    .param p1    # Lorg/xbet/onexlocalization/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/ui_common/moxy/activities/i;->a:Lorg/xbet/onexlocalization/k;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a()Lorg/xbet/ui_common/moxy/activities/h;
    .locals 2
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, Lorg/xbet/ui_common/moxy/activities/a;->a()Lorg/xbet/ui_common/moxy/activities/h$a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/ui_common/moxy/activities/i;->a:Lorg/xbet/onexlocalization/k;

    .line 6
    .line 7
    invoke-interface {v0, v1}, Lorg/xbet/ui_common/moxy/activities/h$a;->a(Lorg/xbet/onexlocalization/k;)Lorg/xbet/ui_common/moxy/activities/h;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0
.end method
