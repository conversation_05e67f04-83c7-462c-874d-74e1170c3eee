.class public final Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;
.super Landroid/view/View;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;,
        Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$b;,
        Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;,
        Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$c;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u009e\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u001b\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0001\u0018\u0000 62\u00020\u0001:\u0003B>{B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\n\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0017\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\n\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\rJ\u0017\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\n\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u0017\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\'\u0010\u001c\u001a\u00020\u00182\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001aH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u0017\u0010\u001e\u001a\u00020\u000b2\u0006\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u0015J?\u0010#\u001a\u00020\u00182\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010 \u001a\u00020\u001f2\u0006\u0010!\u001a\u00020\u001f2\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\"\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001aH\u0002\u00a2\u0006\u0004\u0008#\u0010$J/\u0010&\u001a\u00020\u00182\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010%\u001a\u00020\u001f2\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001aH\u0002\u00a2\u0006\u0004\u0008&\u0010\'J\u001f\u0010*\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020\u00062\u0006\u0010)\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008*\u0010+J\u0017\u0010,\u001a\u00020\u000b2\u0006\u0010\u0017\u001a\u00020\u0016H\u0014\u00a2\u0006\u0004\u0008,\u0010-J\u0015\u00100\u001a\u00020\u000b2\u0006\u0010/\u001a\u00020.\u00a2\u0006\u0004\u00080\u00101J)\u00106\u001a\u00020\u000b2\u000c\u00103\u001a\u0008\u0012\u0004\u0012\u00020\u000f022\u000c\u00105\u001a\u0008\u0012\u0004\u0012\u00020\u000b04\u00a2\u0006\u0004\u00086\u00107J\r\u00108\u001a\u00020\u000b\u00a2\u0006\u0004\u00088\u00109J\u001b\u0010;\u001a\u00020\u000b2\u000c\u0010:\u001a\u0008\u0012\u0004\u0012\u00020\u000b04\u00a2\u0006\u0004\u0008;\u0010<R\u0014\u0010@\u001a\u00020=8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010?R\u001b\u0010F\u001a\u00020A8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008B\u0010C\u001a\u0004\u0008D\u0010ER\u0014\u0010I\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010HR\u0014\u0010M\u001a\u00020J8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008K\u0010LR\u0014\u0010O\u001a\u00020J8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008N\u0010LR\u0014\u0010S\u001a\u00020P8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Q\u0010RR\u0014\u0010W\u001a\u00020T8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008U\u0010VR\u0014\u0010Y\u001a\u00020T8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008X\u0010VR\u0014\u0010]\u001a\u00020Z8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008[\u0010\\R\u0016\u0010`\u001a\u00020\u001f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008^\u0010_R\u0016\u0010b\u001a\u00020\u001f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008a\u0010_R\u0016\u0010d\u001a\u00020\u001f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008c\u0010_R\u0016\u0010f\u001a\u00020\u001f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008e\u0010_R\u0016\u0010h\u001a\u00020\u001f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008g\u0010_R\u0016\u0010i\u001a\u00020\u001f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00088\u0010_R\u0016\u0010k\u001a\u00020\u001f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008j\u0010_R\u0016\u0010l\u001a\u00020\u001f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010_R\u0016\u0010m\u001a\u00020\u001f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008#\u0010_R\u0016\u0010n\u001a\u00020\u001f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008&\u0010_R\u0016\u0010p\u001a\u00020\u00128\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010oR\u0016\u0010r\u001a\u00020\u00188\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010qR\u0016\u0010u\u001a\u00020\u000f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008s\u0010tR\u0018\u0010y\u001a\u0004\u0018\u00010v8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008w\u0010xR\u0016\u0010z\u001a\u00020\u00188\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010q\u00a8\u0006|"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;",
        "Landroid/view/View;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "parentWidth",
        "",
        "x",
        "(I)V",
        "t",
        "",
        "z",
        "(I)Z",
        "",
        "millisUntilFinished",
        "u",
        "(J)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "",
        "startX",
        "Landroid/graphics/Paint;",
        "paint",
        "q",
        "(Landroid/graphics/Canvas;FLandroid/graphics/Paint;)F",
        "B",
        "",
        "original",
        "modified",
        "verticalProgress",
        "r",
        "(Landroid/graphics/Canvas;Ljava/lang/String;Ljava/lang/String;FFLandroid/graphics/Paint;)F",
        "timeUnit",
        "s",
        "(Landroid/graphics/Canvas;Ljava/lang/String;FLandroid/graphics/Paint;)F",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "Le31/c;",
        "model",
        "setModel",
        "(Le31/c;)V",
        "Lkotlinx/coroutines/flow/e;",
        "stopTimerFlow",
        "Lkotlin/Function0;",
        "timeOutCallback",
        "y",
        "(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;)V",
        "o",
        "()V",
        "callback",
        "setOnTimerExpiredListener",
        "(Lkotlin/jvm/functions/Function0;)V",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;",
        "a",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;",
        "viewStyle",
        "Lkotlinx/coroutines/N;",
        "b",
        "Lkotlin/j;",
        "getScope",
        "()Lkotlinx/coroutines/N;",
        "scope",
        "c",
        "Landroid/graphics/Paint;",
        "separatorPaint",
        "Landroid/text/TextPaint;",
        "d",
        "Landroid/text/TextPaint;",
        "timeTextPaint",
        "e",
        "timeUnitPaint",
        "Lorg/xbet/uikit/utils/timer/FlowTimer;",
        "f",
        "Lorg/xbet/uikit/utils/timer/FlowTimer;",
        "timer",
        "Landroid/graphics/Rect;",
        "g",
        "Landroid/graphics/Rect;",
        "timeUnitBounds",
        "h",
        "timeCharacterBounds",
        "Landroid/graphics/RectF;",
        "i",
        "Landroid/graphics/RectF;",
        "timeCellBackgroundBound",
        "j",
        "Ljava/lang/String;",
        "daysTitle",
        "k",
        "visibleDaysTitle",
        "l",
        "currentDaysLeft",
        "m",
        "currentHoursLeft",
        "n",
        "currentMinutesLeft",
        "currentSecondsLeft",
        "p",
        "targetDaysLeft",
        "targetHoursLeft",
        "targetMinutesLeft",
        "targetSecondsLeft",
        "J",
        "timeExpiredMillis",
        "F",
        "timeTextBaseline",
        "v",
        "Z",
        "sizeChangeNeed",
        "Landroid/animation/ValueAnimator;",
        "w",
        "Landroid/animation/ValueAnimator;",
        "currentAnimation",
        "animationProgress",
        "TimeSize",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final y:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final z:I


# instance fields
.field public final a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lorg/xbet/uikit/utils/timer/FlowTimer;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Landroid/graphics/Rect;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Landroid/graphics/Rect;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Landroid/graphics/RectF;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public j:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public k:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public l:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public m:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public n:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public o:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public p:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public q:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public r:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public s:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public t:J

.field public u:F

.field public v:Z

.field public w:Landroid/animation/ValueAnimator;

.field public x:F


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->y:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$b;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->z:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 8
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/view/View;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance p2, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    invoke-direct {p2, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;-><init>(Landroid/content/Context;)V

    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 6
    new-instance p1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/i;

    invoke-direct {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/i;-><init>()V

    .line 7
    sget-object p3, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    invoke-static {p3, p1}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    .line 8
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->b:Lkotlin/j;

    .line 9
    sget v1, LlZ0/d;->uikitSeparator60:I

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lf31/a;->b(Landroid/view/View;ILandroid/graphics/Paint$Style;IILjava/lang/Object;)Landroid/graphics/Paint;

    move-result-object p1

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->c:Landroid/graphics/Paint;

    .line 10
    sget v1, LlZ0/n;->TextStyle_Title_Medium_XL_TextPrimary:I

    sget v2, LlZ0/d;->uikitTextPrimary:I

    const/4 v4, 0x4

    const/4 v3, 0x0

    invoke-static/range {v0 .. v5}, Lf31/a;->f(Landroid/view/View;IILandroid/graphics/Paint$Style;ILjava/lang/Object;)Landroid/text/TextPaint;

    move-result-object p1

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->d:Landroid/text/TextPaint;

    .line 11
    sget v1, LlZ0/n;->TextStyle_Caption_Bold_Caps_M_TextSecondary:I

    sget v2, LlZ0/d;->uikitSecondary:I

    invoke-static/range {v0 .. v5}, Lf31/a;->f(Landroid/view/View;IILandroid/graphics/Paint$Style;ILjava/lang/Object;)Landroid/text/TextPaint;

    move-result-object p1

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->e:Landroid/text/TextPaint;

    .line 12
    new-instance v1, Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 13
    new-instance v5, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/j;

    invoke-direct {v5, p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/j;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;)V

    const/4 v6, 0x1

    const/4 v7, 0x0

    const-wide/16 v2, 0x0

    const/4 v4, 0x0

    .line 14
    invoke-direct/range {v1 .. v7}, Lorg/xbet/uikit/utils/timer/FlowTimer;-><init>(JZLkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->f:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 15
    new-instance p1, Landroid/graphics/Rect;

    invoke-direct {p1}, Landroid/graphics/Rect;-><init>()V

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->g:Landroid/graphics/Rect;

    .line 16
    new-instance p1, Landroid/graphics/Rect;

    invoke-direct {p1}, Landroid/graphics/Rect;-><init>()V

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->h:Landroid/graphics/Rect;

    .line 17
    new-instance p1, Landroid/graphics/RectF;

    .line 18
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->i()I

    move-result p3

    int-to-float p3, p3

    .line 19
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->h()I

    move-result p2

    int-to-float p2, p2

    const/4 v1, 0x0

    .line 20
    invoke-direct {p1, v1, v1, p3, p2}, Landroid/graphics/RectF;-><init>(FFFF)V

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->i:Landroid/graphics/RectF;

    .line 21
    const-string p1, ""

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->j:Ljava/lang/String;

    .line 22
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->k:Ljava/lang/String;

    .line 23
    const-string p1, "00"

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->l:Ljava/lang/String;

    .line 24
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->m:Ljava/lang/String;

    .line 25
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->n:Ljava/lang/String;

    .line 26
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->o:Ljava/lang/String;

    .line 27
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->p:Ljava/lang/String;

    .line 28
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->q:Ljava/lang/String;

    .line 29
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->r:Ljava/lang/String;

    .line 30
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->s:Ljava/lang/String;

    const/high16 p1, 0x3f800000    # 1.0f

    .line 31
    iput p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->x:F

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static final A(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;J)Lkotlin/Unit;
    .locals 6

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->getScope()Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v3, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$timer$1$1;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-direct {v3, p0, p1, p2, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$timer$1$1;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;JLkotlin/coroutines/e;)V

    .line 9
    .line 10
    .line 11
    const/4 v4, 0x3

    .line 12
    const/4 v5, 0x0

    .line 13
    const/4 v2, 0x0

    .line 14
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 15
    .line 16
    .line 17
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 18
    .line 19
    return-object p0
.end method

.method public static synthetic a()Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->p()Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic b(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;Landroid/animation/ValueAnimator;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->v(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;Landroid/animation/ValueAnimator;)V

    return-void
.end method

.method public static synthetic c()Lkotlinx/coroutines/N;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->w()Lkotlinx/coroutines/N;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic d(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;J)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->A(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;J)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic e(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->p:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic f(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->q:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic g(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->r:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method

.method private final getScope()Lkotlinx/coroutines/N;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->b:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lkotlinx/coroutines/N;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final synthetic h(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->s:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic i(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;)Lorg/xbet/uikit/utils/timer/FlowTimer;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->f:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic j(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;J)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->u(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic k(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->l:Ljava/lang/String;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic l(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->m:Ljava/lang/String;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic m(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->n:Ljava/lang/String;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic n(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->o:Ljava/lang/String;

    .line 2
    .line 3
    return-void
.end method

.method public static final p()Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final v(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;Landroid/animation/ValueAnimator;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->getAnimatedValue()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Ljava/lang/Float;

    .line 6
    .line 7
    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->x:F

    .line 12
    .line 13
    iget-boolean p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->v:Z

    .line 14
    .line 15
    if-eqz p1, :cond_0

    .line 16
    .line 17
    const/4 p1, 0x0

    .line 18
    iput-boolean p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->v:Z

    .line 19
    .line 20
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 24
    .line 25
    .line 26
    return-void

    .line 27
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public static final w()Lkotlinx/coroutines/N;
    .locals 1

    .line 1
    invoke-static {}, Lkotlinx/coroutines/b0;->c()Lkotlinx/coroutines/E0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lkotlinx/coroutines/E0;->getImmediate()Lkotlinx/coroutines/E0;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-static {v0}, Lkotlinx/coroutines/O;->a(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method


# virtual methods
.method public final B(J)V
    .locals 7

    .line 1
    iput-wide p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->t:J

    .line 2
    .line 3
    sget-object v0, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toDays(J)J

    .line 6
    .line 7
    .line 8
    move-result-wide v1

    .line 9
    const-wide/16 v3, 0x64

    .line 10
    .line 11
    const/4 v5, 0x2

    .line 12
    cmp-long v6, v1, v3

    .line 13
    .line 14
    if-ltz v6, :cond_0

    .line 15
    .line 16
    const/4 v3, 0x3

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 v3, 0x2

    .line 19
    :goto_0
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->l:Ljava/lang/String;

    .line 20
    .line 21
    const/16 v6, 0x30

    .line 22
    .line 23
    invoke-static {v4, v3, v6}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    iput-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->l:Ljava/lang/String;

    .line 28
    .line 29
    invoke-static {v1, v2}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    invoke-static {v1, v3, v6}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->p:Ljava/lang/String;

    .line 38
    .line 39
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toHours(J)J

    .line 40
    .line 41
    .line 42
    move-result-wide v1

    .line 43
    const/16 v3, 0x18

    .line 44
    .line 45
    int-to-long v3, v3

    .line 46
    rem-long/2addr v1, v3

    .line 47
    invoke-static {v1, v2}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    invoke-static {v1, v5, v6}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->q:Ljava/lang/String;

    .line 56
    .line 57
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toMinutes(J)J

    .line 58
    .line 59
    .line 60
    move-result-wide v1

    .line 61
    const/16 v3, 0x3c

    .line 62
    .line 63
    int-to-long v3, v3

    .line 64
    rem-long/2addr v1, v3

    .line 65
    invoke-static {v1, v2}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    invoke-static {v1, v5, v6}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->r:Ljava/lang/String;

    .line 74
    .line 75
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toSeconds(J)J

    .line 76
    .line 77
    .line 78
    move-result-wide p1

    .line 79
    rem-long/2addr p1, v3

    .line 80
    invoke-static {p1, p2}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    invoke-static {p1, v5, v6}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->s:Ljava/lang/String;

    .line 89
    .line 90
    return-void
.end method

.method public final o()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->f:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/timer/FlowTimer;->o()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->f:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 7
    .line 8
    new-instance v1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/h;

    .line 9
    .line 10
    invoke-direct {v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/h;-><init>()V

    .line 11
    .line 12
    .line 13
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/utils/timer/FlowTimer;->l(Lkotlin/jvm/functions/Function0;)V

    .line 14
    .line 15
    .line 16
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->getScope()Lkotlinx/coroutines/N;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    invoke-interface {v0}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    const/4 v1, 0x0

    .line 25
    const/4 v2, 0x1

    .line 26
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/z0;->j(Lkotlin/coroutines/CoroutineContext;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->w:Landroid/animation/ValueAnimator;

    .line 30
    .line 31
    if-eqz v0, :cond_0

    .line 32
    .line 33
    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->cancel()V

    .line 34
    .line 35
    .line 36
    :cond_0
    return-void
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 10
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    int-to-float v0, v0

    .line 6
    const/high16 v1, 0x40000000    # 2.0f

    .line 7
    .line 8
    div-float/2addr v0, v1

    .line 9
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 10
    .line 11
    invoke-virtual {v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->a()I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    int-to-float v2, v2

    .line 16
    div-float/2addr v2, v1

    .line 17
    sub-float v7, v0, v2

    .line 18
    .line 19
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->i:Landroid/graphics/RectF;

    .line 23
    .line 24
    invoke-virtual {p1, v0}, Landroid/graphics/Canvas;->clipRect(Landroid/graphics/RectF;)Z

    .line 25
    .line 26
    .line 27
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 28
    .line 29
    .line 30
    move-result v0

    .line 31
    if-nez v0, :cond_0

    .line 32
    .line 33
    iget-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->l:Ljava/lang/String;

    .line 34
    .line 35
    iget-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->p:Ljava/lang/String;

    .line 36
    .line 37
    iget v8, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->x:F

    .line 38
    .line 39
    iget-object v9, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->d:Landroid/text/TextPaint;

    .line 40
    .line 41
    move-object v3, p0

    .line 42
    move-object v4, p1

    .line 43
    invoke-virtual/range {v3 .. v9}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->r(Landroid/graphics/Canvas;Ljava/lang/String;Ljava/lang/String;FFLandroid/graphics/Paint;)F

    .line 44
    .line 45
    .line 46
    move-result p1

    .line 47
    move-object v0, v3

    .line 48
    move-object v1, v4

    .line 49
    iget-object v2, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->k:Ljava/lang/String;

    .line 50
    .line 51
    iget-object v3, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->e:Landroid/text/TextPaint;

    .line 52
    .line 53
    invoke-virtual {p0, v1, v2, p1, v3}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->s(Landroid/graphics/Canvas;Ljava/lang/String;FLandroid/graphics/Paint;)F

    .line 54
    .line 55
    .line 56
    move-result p1

    .line 57
    goto :goto_0

    .line 58
    :cond_0
    move-object v0, p0

    .line 59
    move-object v1, p1

    .line 60
    iget-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->k:Ljava/lang/String;

    .line 61
    .line 62
    iget-object v2, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->e:Landroid/text/TextPaint;

    .line 63
    .line 64
    invoke-virtual {p0, v1, p1, v7, v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->s(Landroid/graphics/Canvas;Ljava/lang/String;FLandroid/graphics/Paint;)F

    .line 65
    .line 66
    .line 67
    move-result v4

    .line 68
    iget-object v2, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->l:Ljava/lang/String;

    .line 69
    .line 70
    iget-object v3, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->p:Ljava/lang/String;

    .line 71
    .line 72
    iget v5, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->x:F

    .line 73
    .line 74
    iget-object v6, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->d:Landroid/text/TextPaint;

    .line 75
    .line 76
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->r(Landroid/graphics/Canvas;Ljava/lang/String;Ljava/lang/String;FFLandroid/graphics/Paint;)F

    .line 77
    .line 78
    .line 79
    move-result p1

    .line 80
    :goto_0
    iget-object v2, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->c:Landroid/graphics/Paint;

    .line 81
    .line 82
    invoke-virtual {p0, v1, p1, v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->q(Landroid/graphics/Canvas;FLandroid/graphics/Paint;)F

    .line 83
    .line 84
    .line 85
    move-result v4

    .line 86
    iget-object v2, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->m:Ljava/lang/String;

    .line 87
    .line 88
    iget-object v3, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->q:Ljava/lang/String;

    .line 89
    .line 90
    iget v5, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->x:F

    .line 91
    .line 92
    iget-object v6, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->d:Landroid/text/TextPaint;

    .line 93
    .line 94
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->r(Landroid/graphics/Canvas;Ljava/lang/String;Ljava/lang/String;FFLandroid/graphics/Paint;)F

    .line 95
    .line 96
    .line 97
    move-result p1

    .line 98
    iget-object v2, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->c:Landroid/graphics/Paint;

    .line 99
    .line 100
    invoke-virtual {p0, v1, p1, v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->q(Landroid/graphics/Canvas;FLandroid/graphics/Paint;)F

    .line 101
    .line 102
    .line 103
    move-result v4

    .line 104
    iget-object v2, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->n:Ljava/lang/String;

    .line 105
    .line 106
    iget-object v3, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->r:Ljava/lang/String;

    .line 107
    .line 108
    iget v5, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->x:F

    .line 109
    .line 110
    iget-object v6, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->d:Landroid/text/TextPaint;

    .line 111
    .line 112
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->r(Landroid/graphics/Canvas;Ljava/lang/String;Ljava/lang/String;FFLandroid/graphics/Paint;)F

    .line 113
    .line 114
    .line 115
    move-result p1

    .line 116
    iget-object v2, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->c:Landroid/graphics/Paint;

    .line 117
    .line 118
    invoke-virtual {p0, v1, p1, v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->q(Landroid/graphics/Canvas;FLandroid/graphics/Paint;)F

    .line 119
    .line 120
    .line 121
    move-result v4

    .line 122
    iget-object v2, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->o:Ljava/lang/String;

    .line 123
    .line 124
    iget-object v3, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->s:Ljava/lang/String;

    .line 125
    .line 126
    iget v5, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->x:F

    .line 127
    .line 128
    iget-object v6, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->d:Landroid/text/TextPaint;

    .line 129
    .line 130
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->r(Landroid/graphics/Canvas;Ljava/lang/String;Ljava/lang/String;FFLandroid/graphics/Paint;)F

    .line 131
    .line 132
    .line 133
    invoke-virtual {v1}, Landroid/graphics/Canvas;->restore()V

    .line 134
    .line 135
    .line 136
    return-void
.end method

.method public onMeasure(II)V
    .locals 4

    .line 1
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 2
    .line 3
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->m()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 8
    .line 9
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->e()F

    .line 10
    .line 11
    .line 12
    move-result p2

    .line 13
    float-to-int p2, p2

    .line 14
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->z(I)Z

    .line 15
    .line 16
    .line 17
    move-result p2

    .line 18
    if-eqz p2, :cond_0

    .line 19
    .line 20
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 21
    .line 22
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->e()F

    .line 23
    .line 24
    .line 25
    move-result p2

    .line 26
    goto :goto_0

    .line 27
    :cond_0
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 28
    .line 29
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->c()F

    .line 30
    .line 31
    .line 32
    move-result p2

    .line 33
    :goto_0
    float-to-int p2, p2

    .line 34
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->x(I)V

    .line 35
    .line 36
    .line 37
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->t(I)V

    .line 38
    .line 39
    .line 40
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 41
    .line 42
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->k()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;->SHORT:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;

    .line 47
    .line 48
    if-ne v0, v1, :cond_1

    .line 49
    .line 50
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 51
    .line 52
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->f()I

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    goto :goto_1

    .line 57
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 58
    .line 59
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->b()I

    .line 60
    .line 61
    .line 62
    move-result v0

    .line 63
    :goto_1
    invoke-virtual {p2, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->n(I)V

    .line 64
    .line 65
    .line 66
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->e:Landroid/text/TextPaint;

    .line 67
    .line 68
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->j:Ljava/lang/String;

    .line 69
    .line 70
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 71
    .line 72
    .line 73
    move-result v1

    .line 74
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->g:Landroid/graphics/Rect;

    .line 75
    .line 76
    const/4 v3, 0x0

    .line 77
    invoke-virtual {p2, v0, v3, v1, v2}, Landroid/graphics/Paint;->getTextBounds(Ljava/lang/String;IILandroid/graphics/Rect;)V

    .line 78
    .line 79
    .line 80
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->d:Landroid/text/TextPaint;

    .line 81
    .line 82
    const/4 v0, 0x2

    .line 83
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->h:Landroid/graphics/Rect;

    .line 84
    .line 85
    const-string v2, "00"

    .line 86
    .line 87
    invoke-virtual {p2, v2, v3, v0, v1}, Landroid/graphics/Paint;->getTextBounds(Ljava/lang/String;IILandroid/graphics/Rect;)V

    .line 88
    .line 89
    .line 90
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->h:Landroid/graphics/Rect;

    .line 91
    .line 92
    invoke-virtual {p2}, Landroid/graphics/Rect;->height()I

    .line 93
    .line 94
    .line 95
    move-result p2

    .line 96
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->i:Landroid/graphics/RectF;

    .line 97
    .line 98
    invoke-virtual {v0}, Landroid/graphics/RectF;->centerY()F

    .line 99
    .line 100
    .line 101
    move-result v0

    .line 102
    int-to-float p2, p2

    .line 103
    const/high16 v1, 0x40000000    # 2.0f

    .line 104
    .line 105
    div-float/2addr p2, v1

    .line 106
    add-float/2addr v0, p2

    .line 107
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->u:F

    .line 108
    .line 109
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->i:Landroid/graphics/RectF;

    .line 110
    .line 111
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 112
    .line 113
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->a()I

    .line 114
    .line 115
    .line 116
    move-result v0

    .line 117
    int-to-float v0, v0

    .line 118
    iput v0, p2, Landroid/graphics/RectF;->right:F

    .line 119
    .line 120
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 121
    .line 122
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->a()I

    .line 123
    .line 124
    .line 125
    move-result p2

    .line 126
    invoke-virtual {p0, p2, p1}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 127
    .line 128
    .line 129
    return-void
.end method

.method public final q(Landroid/graphics/Canvas;FLandroid/graphics/Paint;)F
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->j()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    int-to-float v0, v0

    .line 8
    add-float v2, v0, p2

    .line 9
    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 11
    .line 12
    .line 13
    move-result p2

    .line 14
    int-to-float v5, p2

    .line 15
    const/4 v3, 0x0

    .line 16
    move v4, v2

    .line 17
    move-object v1, p1

    .line 18
    move-object v6, p3

    .line 19
    invoke-virtual/range {v1 .. v6}, Landroid/graphics/Canvas;->drawLine(FFFFLandroid/graphics/Paint;)V

    .line 20
    .line 21
    .line 22
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 23
    .line 24
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->j()I

    .line 25
    .line 26
    .line 27
    move-result p1

    .line 28
    int-to-float p1, p1

    .line 29
    add-float/2addr v2, p1

    .line 30
    return v2
.end method

.method public final r(Landroid/graphics/Canvas;Ljava/lang/String;Ljava/lang/String;FFLandroid/graphics/Paint;)F
    .locals 8

    .line 1
    invoke-virtual {p2}, Ljava/lang/String;->length()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-virtual {p3}, Ljava/lang/String;->length()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    const/4 v1, 0x0

    .line 14
    :goto_0
    if-ge v1, v0, :cond_8

    .line 15
    .line 16
    invoke-static {p2, v1}, Lkotlin/text/A;->T1(Ljava/lang/CharSequence;I)Ljava/lang/Character;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    const-string v3, ""

    .line 21
    .line 22
    if-eqz v2, :cond_0

    .line 23
    .line 24
    invoke-virtual {v2}, Ljava/lang/Character;->toString()Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object v2

    .line 28
    if-nez v2, :cond_1

    .line 29
    .line 30
    :cond_0
    move-object v2, v3

    .line 31
    :cond_1
    invoke-static {p3, v1}, Lkotlin/text/A;->T1(Ljava/lang/CharSequence;I)Ljava/lang/Character;

    .line 32
    .line 33
    .line 34
    move-result-object v4

    .line 35
    if-eqz v4, :cond_3

    .line 36
    .line 37
    invoke-virtual {v4}, Ljava/lang/Character;->toString()Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object v4

    .line 41
    if-nez v4, :cond_2

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_2
    move-object v3, v4

    .line 45
    :cond_3
    :goto_1
    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 46
    .line 47
    .line 48
    move-result v4

    .line 49
    iget v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->u:F

    .line 50
    .line 51
    if-nez v4, :cond_4

    .line 52
    .line 53
    const/4 v6, 0x1

    .line 54
    int-to-float v6, v6

    .line 55
    sub-float/2addr v6, p5

    .line 56
    mul-float v5, v5, v6

    .line 57
    .line 58
    :cond_4
    iget v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->u:F

    .line 59
    .line 60
    if-nez v4, :cond_5

    .line 61
    .line 62
    const/4 v7, 0x2

    .line 63
    int-to-float v7, v7

    .line 64
    sub-float/2addr v7, p5

    .line 65
    mul-float v6, v6, v7

    .line 66
    .line 67
    :cond_5
    invoke-interface {v2}, Ljava/lang/CharSequence;->length()I

    .line 68
    .line 69
    .line 70
    move-result v7

    .line 71
    if-lez v7, :cond_6

    .line 72
    .line 73
    if-nez v4, :cond_6

    .line 74
    .line 75
    invoke-virtual {p1, v2, p4, v5, p6}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    .line 76
    .line 77
    .line 78
    :cond_6
    invoke-interface {v3}, Ljava/lang/CharSequence;->length()I

    .line 79
    .line 80
    .line 81
    move-result v4

    .line 82
    if-lez v4, :cond_7

    .line 83
    .line 84
    invoke-virtual {p1, v3, p4, v6, p6}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    .line 85
    .line 86
    .line 87
    :cond_7
    invoke-virtual {p6, v2}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 88
    .line 89
    .line 90
    move-result v2

    .line 91
    invoke-virtual {p6, v3}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 92
    .line 93
    .line 94
    move-result v3

    .line 95
    invoke-static {v2, v3}, Ljava/lang/Math;->max(FF)F

    .line 96
    .line 97
    .line 98
    move-result v2

    .line 99
    add-float/2addr p4, v2

    .line 100
    add-int/lit8 v1, v1, 0x1

    .line 101
    .line 102
    goto :goto_0

    .line 103
    :cond_8
    return p4
.end method

.method public final s(Landroid/graphics/Canvas;Ljava/lang/String;FLandroid/graphics/Paint;)F
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 8
    .line 9
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->g()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    int-to-float v0, v0

    .line 14
    add-float/2addr v0, p3

    .line 15
    goto :goto_0

    .line 16
    :cond_0
    move v0, p3

    .line 17
    :goto_0
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->u:F

    .line 18
    .line 19
    invoke-virtual {p1, p2, v0, v1, p4}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p4, p2}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 27
    .line 28
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->g()I

    .line 29
    .line 30
    .line 31
    move-result p2

    .line 32
    int-to-float p2, p2

    .line 33
    add-float/2addr p3, p2

    .line 34
    add-float/2addr p3, p1

    .line 35
    return p3
.end method

.method public final setModel(Le31/c;)V
    .locals 7
    .param p1    # Le31/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Le31/c;->b()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p1}, Le31/c;->b()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    invoke-static {v0, v1}, LF0/b;->getString(Landroid/content/Context;I)Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    sget-object v1, Ljava/util/Locale;->ROOT:Ljava/util/Locale;

    .line 20
    .line 21
    invoke-virtual {v0, v1}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->j:Ljava/lang/String;

    .line 26
    .line 27
    :cond_0
    invoke-virtual {p1}, Le31/c;->e()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerMode;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$c;->a:[I

    .line 32
    .line 33
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 34
    .line 35
    .line 36
    move-result v0

    .line 37
    aget v0, v1, v0

    .line 38
    .line 39
    const/4 v1, 0x1

    .line 40
    const/4 v2, 0x2

    .line 41
    if-eq v0, v1, :cond_2

    .line 42
    .line 43
    if-ne v0, v2, :cond_1

    .line 44
    .line 45
    invoke-virtual {p1}, Le31/c;->a()Ljava/util/Date;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    invoke-virtual {p1}, Ljava/util/Date;->getTime()J

    .line 50
    .line 51
    .line 52
    move-result-wide v0

    .line 53
    goto :goto_0

    .line 54
    :cond_1
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 55
    .line 56
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 57
    .line 58
    .line 59
    throw p1

    .line 60
    :cond_2
    invoke-virtual {p1}, Le31/c;->a()Ljava/util/Date;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    invoke-virtual {p1}, Ljava/util/Date;->getTime()J

    .line 65
    .line 66
    .line 67
    move-result-wide v0

    .line 68
    new-instance p1, Ljava/util/Date;

    .line 69
    .line 70
    invoke-direct {p1}, Ljava/util/Date;-><init>()V

    .line 71
    .line 72
    .line 73
    invoke-virtual {p1}, Ljava/util/Date;->getTime()J

    .line 74
    .line 75
    .line 76
    move-result-wide v3

    .line 77
    sub-long/2addr v0, v3

    .line 78
    :goto_0
    iput-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->t:J

    .line 79
    .line 80
    sget-object p1, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 81
    .line 82
    invoke-virtual {p1, v0, v1}, Ljava/util/concurrent/TimeUnit;->toDays(J)J

    .line 83
    .line 84
    .line 85
    move-result-wide v0

    .line 86
    const-wide/16 v3, 0x64

    .line 87
    .line 88
    cmp-long v5, v0, v3

    .line 89
    .line 90
    if-ltz v5, :cond_3

    .line 91
    .line 92
    const/4 v3, 0x3

    .line 93
    goto :goto_1

    .line 94
    :cond_3
    const/4 v3, 0x2

    .line 95
    :goto_1
    invoke-static {v0, v1}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 96
    .line 97
    .line 98
    move-result-object v0

    .line 99
    const/16 v1, 0x30

    .line 100
    .line 101
    invoke-static {v0, v3, v1}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 102
    .line 103
    .line 104
    move-result-object v0

    .line 105
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->l:Ljava/lang/String;

    .line 106
    .line 107
    iget-wide v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->t:J

    .line 108
    .line 109
    invoke-virtual {p1, v3, v4}, Ljava/util/concurrent/TimeUnit;->toHours(J)J

    .line 110
    .line 111
    .line 112
    move-result-wide v3

    .line 113
    const/16 v0, 0x18

    .line 114
    .line 115
    int-to-long v5, v0

    .line 116
    rem-long/2addr v3, v5

    .line 117
    invoke-static {v3, v4}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 118
    .line 119
    .line 120
    move-result-object v0

    .line 121
    invoke-static {v0, v2, v1}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 122
    .line 123
    .line 124
    move-result-object v0

    .line 125
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->m:Ljava/lang/String;

    .line 126
    .line 127
    iget-wide v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->t:J

    .line 128
    .line 129
    invoke-virtual {p1, v3, v4}, Ljava/util/concurrent/TimeUnit;->toMinutes(J)J

    .line 130
    .line 131
    .line 132
    move-result-wide v3

    .line 133
    const/16 v0, 0x3c

    .line 134
    .line 135
    int-to-long v5, v0

    .line 136
    rem-long/2addr v3, v5

    .line 137
    invoke-static {v3, v4}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 138
    .line 139
    .line 140
    move-result-object v0

    .line 141
    invoke-static {v0, v2, v1}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 142
    .line 143
    .line 144
    move-result-object v0

    .line 145
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->n:Ljava/lang/String;

    .line 146
    .line 147
    iget-wide v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->t:J

    .line 148
    .line 149
    invoke-virtual {p1, v3, v4}, Ljava/util/concurrent/TimeUnit;->toSeconds(J)J

    .line 150
    .line 151
    .line 152
    move-result-wide v3

    .line 153
    rem-long/2addr v3, v5

    .line 154
    invoke-static {v3, v4}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 155
    .line 156
    .line 157
    move-result-object p1

    .line 158
    invoke-static {p1, v2, v1}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 159
    .line 160
    .line 161
    move-result-object p1

    .line 162
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->o:Ljava/lang/String;

    .line 163
    .line 164
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 165
    .line 166
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->l:Ljava/lang/String;

    .line 167
    .line 168
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 169
    .line 170
    .line 171
    move-result v0

    .line 172
    if-gt v0, v2, :cond_4

    .line 173
    .line 174
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;->SHORT:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;

    .line 175
    .line 176
    goto :goto_2

    .line 177
    :cond_4
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;->LONG:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;

    .line 178
    .line 179
    :goto_2
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->o(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;)V

    .line 180
    .line 181
    .line 182
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 183
    .line 184
    .line 185
    return-void
.end method

.method public final setOnTimerExpiredListener(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->f:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/utils/timer/FlowTimer;->l(Lkotlin/jvm/functions/Function0;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final t(I)V
    .locals 3

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->z(I)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->j:Ljava/lang/String;

    .line 8
    .line 9
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->k:Ljava/lang/String;

    .line 10
    .line 11
    return-void

    .line 12
    :cond_0
    int-to-float p1, p1

    .line 13
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->e:Landroid/text/TextPaint;

    .line 14
    .line 15
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->j:Ljava/lang/String;

    .line 16
    .line 17
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    sub-float/2addr p1, v0

    .line 22
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 23
    .line 24
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->c()F

    .line 25
    .line 26
    .line 27
    move-result v0

    .line 28
    invoke-static {p1, v0}, Ljava/lang/Math;->max(FF)F

    .line 29
    .line 30
    .line 31
    move-result p1

    .line 32
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->j:Ljava/lang/String;

    .line 33
    .line 34
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->e:Landroid/text/TextPaint;

    .line 35
    .line 36
    sget-object v2, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    .line 37
    .line 38
    invoke-static {v0, v1, p1, v2}, Landroid/text/TextUtils;->ellipsize(Ljava/lang/CharSequence;Landroid/text/TextPaint;FLandroid/text/TextUtils$TruncateAt;)Ljava/lang/CharSequence;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->k:Ljava/lang/String;

    .line 47
    .line 48
    return-void
.end method

.method public final u(J)V
    .locals 2

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->B(J)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 5
    .line 6
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->k()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->l:Ljava/lang/String;

    .line 13
    .line 14
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    const/4 v1, 0x2

    .line 19
    if-gt v0, v1, :cond_0

    .line 20
    .line 21
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;->SHORT:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;->LONG:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;

    .line 25
    .line 26
    :goto_0
    invoke-virtual {p2, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->o(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;)V

    .line 27
    .line 28
    .line 29
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 30
    .line 31
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->k()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;

    .line 32
    .line 33
    .line 34
    move-result-object p2

    .line 35
    if-ne p1, p2, :cond_1

    .line 36
    .line 37
    const/4 p1, 0x1

    .line 38
    goto :goto_1

    .line 39
    :cond_1
    const/4 p1, 0x0

    .line 40
    :goto_1
    iput-boolean p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->v:Z

    .line 41
    .line 42
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->w:Landroid/animation/ValueAnimator;

    .line 43
    .line 44
    if-eqz p1, :cond_2

    .line 45
    .line 46
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->cancel()V

    .line 47
    .line 48
    .line 49
    :cond_2
    new-array p1, v1, [F

    .line 50
    .line 51
    fill-array-data p1, :array_0

    .line 52
    .line 53
    .line 54
    invoke-static {p1}, Landroid/animation/ValueAnimator;->ofFloat([F)Landroid/animation/ValueAnimator;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    new-instance p2, Lk1/b;

    .line 59
    .line 60
    invoke-direct {p2}, Lk1/b;-><init>()V

    .line 61
    .line 62
    .line 63
    invoke-virtual {p1, p2}, Landroid/animation/ValueAnimator;->setInterpolator(Landroid/animation/TimeInterpolator;)V

    .line 64
    .line 65
    .line 66
    new-instance p2, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/g;

    .line 67
    .line 68
    invoke-direct {p2, p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/g;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;)V

    .line 69
    .line 70
    .line 71
    invoke-virtual {p1, p2}, Landroid/animation/ValueAnimator;->addUpdateListener(Landroid/animation/ValueAnimator$AnimatorUpdateListener;)V

    .line 72
    .line 73
    .line 74
    new-instance p2, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$d;

    .line 75
    .line 76
    invoke-direct {p2, p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$d;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {p1, p2}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 80
    .line 81
    .line 82
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->start()V

    .line 83
    .line 84
    .line 85
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->w:Landroid/animation/ValueAnimator;

    .line 86
    .line 87
    return-void

    .line 88
    nop

    .line 89
    :array_0
    .array-data 4
        0x0
        0x3f800000    # 1.0f
    .end array-data
.end method

.method public final x(I)V
    .locals 4

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->z(I)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->e:Landroid/text/TextPaint;

    .line 9
    .line 10
    invoke-virtual {v0}, Landroid/graphics/Paint;->getTextSize()F

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 15
    .line 16
    invoke-virtual {v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->d()F

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    cmpg-float v0, v0, v1

    .line 21
    .line 22
    if-gtz v0, :cond_1

    .line 23
    .line 24
    :goto_0
    return-void

    .line 25
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->e:Landroid/text/TextPaint;

    .line 26
    .line 27
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 28
    .line 29
    invoke-virtual {v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->d()F

    .line 30
    .line 31
    .line 32
    move-result v1

    .line 33
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->e:Landroid/text/TextPaint;

    .line 34
    .line 35
    invoke-virtual {v2}, Landroid/graphics/Paint;->getTextSize()F

    .line 36
    .line 37
    .line 38
    move-result v2

    .line 39
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;

    .line 40
    .line 41
    invoke-virtual {v3}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->l()F

    .line 42
    .line 43
    .line 44
    move-result v3

    .line 45
    sub-float/2addr v2, v3

    .line 46
    invoke-static {v1, v2}, Ljava/lang/Math;->max(FF)F

    .line 47
    .line 48
    .line 49
    move-result v1

    .line 50
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 51
    .line 52
    .line 53
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->x(I)V

    .line 54
    .line 55
    .line 56
    return-void
.end method

.method public final y(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;)V
    .locals 5
    .param p1    # Lkotlinx/coroutines/flow/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->f:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/timer/FlowTimer;->o()V

    .line 4
    .line 5
    .line 6
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->t:J

    .line 7
    .line 8
    const-wide/16 v2, 0x3e8

    .line 9
    .line 10
    cmp-long v4, v0, v2

    .line 11
    .line 12
    if-lez v4, :cond_0

    .line 13
    .line 14
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->f:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 15
    .line 16
    invoke-virtual {v2, v0, v1}, Lorg/xbet/uikit/utils/timer/FlowTimer;->m(J)V

    .line 17
    .line 18
    .line 19
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->t:J

    .line 20
    .line 21
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->u(J)V

    .line 22
    .line 23
    .line 24
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->f:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 25
    .line 26
    invoke-virtual {v0, p2}, Lorg/xbet/uikit/utils/timer/FlowTimer;->l(Lkotlin/jvm/functions/Function0;)V

    .line 27
    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_0
    const-string p2, "00"

    .line 31
    .line 32
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->l:Ljava/lang/String;

    .line 33
    .line 34
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->m:Ljava/lang/String;

    .line 35
    .line 36
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->n:Ljava/lang/String;

    .line 37
    .line 38
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->o:Ljava/lang/String;

    .line 39
    .line 40
    :goto_0
    new-instance p2, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$startTimer$1;

    .line 41
    .line 42
    const/4 v0, 0x0

    .line 43
    invoke-direct {p2, p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$startTimer$1;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;Lkotlin/coroutines/e;)V

    .line 44
    .line 45
    .line 46
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->getScope()Lkotlinx/coroutines/N;

    .line 51
    .line 52
    .line 53
    move-result-object p2

    .line 54
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 55
    .line 56
    .line 57
    return-void
.end method

.method public final z(I)Z
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->e:Landroid/text/TextPaint;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->j:Ljava/lang/String;

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    int-to-float p1, p1

    .line 10
    cmpg-float p1, v0, p1

    .line 11
    .line 12
    if-gtz p1, :cond_0

    .line 13
    .line 14
    const/4 p1, 0x1

    .line 15
    return p1

    .line 16
    :cond_0
    const/4 p1, 0x0

    .line 17
    return p1
.end method
