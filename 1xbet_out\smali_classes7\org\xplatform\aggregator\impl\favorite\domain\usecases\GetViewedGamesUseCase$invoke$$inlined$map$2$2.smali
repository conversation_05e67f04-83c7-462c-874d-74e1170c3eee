.class public final Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlinx/coroutines/flow/f;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2;->collect(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lkotlinx/coroutines/flow/f;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lkotlinx/coroutines/flow/f;

.field public final synthetic b:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;

.field public final synthetic c:Z

.field public final synthetic d:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lkotlinx/coroutines/flow/f;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;ZLjava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2;->a:Lkotlinx/coroutines/flow/f;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2;->b:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;

    iput-boolean p3, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2;->c:Z

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2;->d:Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8

    .line 1
    instance-of v0, p2, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x1

    .line 35
    if-eqz v2, :cond_3

    .line 36
    .line 37
    if-eq v2, v4, :cond_2

    .line 38
    .line 39
    if-ne v2, v3, :cond_1

    .line 40
    .line 41
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    goto :goto_3

    .line 45
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 46
    .line 47
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 48
    .line 49
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 50
    .line 51
    .line 52
    throw p1

    .line 53
    :cond_2
    iget-object p1, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2$1;->L$0:Ljava/lang/Object;

    .line 54
    .line 55
    check-cast p1, Lkotlinx/coroutines/flow/f;

    .line 56
    .line 57
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 58
    .line 59
    .line 60
    goto :goto_1

    .line 61
    :cond_3
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2;->a:Lkotlinx/coroutines/flow/f;

    .line 65
    .line 66
    check-cast p1, Ljava/util/List;

    .line 67
    .line 68
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2;->b:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;

    .line 69
    .line 70
    iget-boolean v5, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2;->c:Z

    .line 71
    .line 72
    iget-object v6, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2;->d:Ljava/lang/String;

    .line 73
    .line 74
    iput-object p2, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2$1;->L$0:Ljava/lang/Object;

    .line 75
    .line 76
    iput v4, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2$1;->label:I

    .line 77
    .line 78
    invoke-static {v2, p1, v5, v6, v0}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->a(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;Ljava/util/List;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    if-ne p1, v1, :cond_4

    .line 83
    .line 84
    goto :goto_2

    .line 85
    :cond_4
    move-object v7, p2

    .line 86
    move-object p2, p1

    .line 87
    move-object p1, v7

    .line 88
    :goto_1
    const/4 v2, 0x0

    .line 89
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2$1;->L$0:Ljava/lang/Object;

    .line 90
    .line 91
    iput v3, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2$2$1;->label:I

    .line 92
    .line 93
    invoke-interface {p1, p2, v0}, Lkotlinx/coroutines/flow/f;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    if-ne p1, v1, :cond_5

    .line 98
    .line 99
    :goto_2
    return-object v1

    .line 100
    :cond_5
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 101
    .line 102
    return-object p1
.end method
