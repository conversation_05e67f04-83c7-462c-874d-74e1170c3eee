.class final Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.spin_and_win.presentation.game.SpinAndWinGameFragment$onObserveData$2"
    f = "SpinAndWinGameFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/spin_and_win/presentation/game/b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/spin_and_win/presentation/game/b;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xbet/spin_and_win/presentation/game/b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;

    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;->invoke(Lorg/xbet/spin_and_win/presentation/game/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/spin_and_win/presentation/game/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/spin_and_win/presentation/game/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_5

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/b;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xbet/spin_and_win/presentation/game/b$b;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 20
    .line 21
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/b$b;

    .line 22
    .line 23
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/b$b;->a()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-static {v0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->I2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V

    .line 28
    .line 29
    .line 30
    goto :goto_1

    .line 31
    :cond_0
    instance-of v0, p1, Lorg/xbet/spin_and_win/presentation/game/b$d;

    .line 32
    .line 33
    if-eqz v0, :cond_2

    .line 34
    .line 35
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 36
    .line 37
    invoke-static {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->F2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lbz0/c;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    iget-object v0, v0, Lbz0/c;->c:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;

    .line 42
    .line 43
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/b$d;

    .line 44
    .line 45
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/b$d;->a()Z

    .line 46
    .line 47
    .line 48
    move-result p1

    .line 49
    if-nez p1, :cond_1

    .line 50
    .line 51
    const/4 p1, 0x4

    .line 52
    goto :goto_0

    .line 53
    :cond_1
    const/4 p1, 0x0

    .line 54
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 55
    .line 56
    .line 57
    goto :goto_1

    .line 58
    :cond_2
    instance-of v0, p1, Lorg/xbet/spin_and_win/presentation/game/b$a;

    .line 59
    .line 60
    if-eqz v0, :cond_3

    .line 61
    .line 62
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 63
    .line 64
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/b$a;

    .line 65
    .line 66
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/b$a;->b()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 67
    .line 68
    .line 69
    move-result-object v1

    .line 70
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/b$a;->a()Ljava/lang/Double;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    invoke-static {v0, v1, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->H2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;Ljava/lang/Double;)V

    .line 75
    .line 76
    .line 77
    goto :goto_1

    .line 78
    :cond_3
    instance-of v0, p1, Lorg/xbet/spin_and_win/presentation/game/b$c;

    .line 79
    .line 80
    if-eqz v0, :cond_4

    .line 81
    .line 82
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 83
    .line 84
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/b$c;

    .line 85
    .line 86
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/b$c;->a()Ljava/util/List;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    invoke-static {v0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->J2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Ljava/util/List;)V

    .line 91
    .line 92
    .line 93
    :goto_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 94
    .line 95
    return-object p1

    .line 96
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 97
    .line 98
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 99
    .line 100
    .line 101
    throw p1

    .line 102
    :cond_5
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 103
    .line 104
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 105
    .line 106
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 107
    .line 108
    .line 109
    throw p1
.end method
