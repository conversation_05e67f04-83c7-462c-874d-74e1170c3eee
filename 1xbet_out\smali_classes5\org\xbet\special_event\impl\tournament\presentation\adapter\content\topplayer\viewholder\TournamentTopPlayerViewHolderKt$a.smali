.class public final Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->m(LEx0/b;)LA4/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function1<",
        "Ljava/util/List<",
        "+",
        "Ljava/lang/Object;",
        ">;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:LEx0/a;

.field public final synthetic c:LB4/a;

.field public final synthetic d:LEx0/a;


# direct methods
.method public constructor <init>(LB4/a;LEx0/a;LB4/a;LEx0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->a:LB4/a;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->b:LEx0/a;

    .line 4
    .line 5
    iput-object p3, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->c:LB4/a;

    .line 6
    .line 7
    iput-object p4, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->d:LEx0/a;

    .line 8
    .line 9
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 10
    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->a:LB4/a;

    .line 8
    .line 9
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->e(LB4/a;)V

    .line 10
    .line 11
    .line 12
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->a:LB4/a;

    .line 13
    .line 14
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->f(LB4/a;)V

    .line 15
    .line 16
    .line 17
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->a:LB4/a;

    .line 18
    .line 19
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->g(LB4/a;)V

    .line 20
    .line 21
    .line 22
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->a:LB4/a;

    .line 23
    .line 24
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->h(LB4/a;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->b:LEx0/a;

    .line 28
    .line 29
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->a:LB4/a;

    .line 30
    .line 31
    invoke-virtual {v0}, LB4/a;->i()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    check-cast v0, LIx0/a;

    .line 36
    .line 37
    invoke-virtual {v0}, LIx0/a;->A()LIx0/a$a$e;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    invoke-virtual {v0}, LIx0/a$a$e;->a()Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    invoke-virtual {p1, v0}, LA4/e;->setItems(Ljava/util/List;)V

    .line 46
    .line 47
    .line 48
    return-void

    .line 49
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    .line 50
    .line 51
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 52
    .line 53
    .line 54
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 59
    .line 60
    .line 61
    move-result v1

    .line 62
    if-eqz v1, :cond_1

    .line 63
    .line 64
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object v1

    .line 68
    check-cast v1, Ljava/util/Collection;

    .line 69
    .line 70
    check-cast v1, Ljava/lang/Iterable;

    .line 71
    .line 72
    invoke-static {v0, v1}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 73
    .line 74
    .line 75
    goto :goto_0

    .line 76
    :cond_1
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 81
    .line 82
    .line 83
    move-result v0

    .line 84
    if-eqz v0, :cond_7

    .line 85
    .line 86
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object v0

    .line 90
    check-cast v0, LIx0/a$a;

    .line 91
    .line 92
    instance-of v1, v0, LIx0/a$a$a;

    .line 93
    .line 94
    if-eqz v1, :cond_2

    .line 95
    .line 96
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->c:LB4/a;

    .line 97
    .line 98
    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->e(LB4/a;)V

    .line 99
    .line 100
    .line 101
    goto :goto_1

    .line 102
    :cond_2
    instance-of v1, v0, LIx0/a$a$b;

    .line 103
    .line 104
    if-eqz v1, :cond_3

    .line 105
    .line 106
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->c:LB4/a;

    .line 107
    .line 108
    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->f(LB4/a;)V

    .line 109
    .line 110
    .line 111
    goto :goto_1

    .line 112
    :cond_3
    instance-of v1, v0, LIx0/a$a$c;

    .line 113
    .line 114
    if-eqz v1, :cond_4

    .line 115
    .line 116
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->c:LB4/a;

    .line 117
    .line 118
    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->g(LB4/a;)V

    .line 119
    .line 120
    .line 121
    goto :goto_1

    .line 122
    :cond_4
    instance-of v1, v0, LIx0/a$a$d;

    .line 123
    .line 124
    if-eqz v1, :cond_5

    .line 125
    .line 126
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->c:LB4/a;

    .line 127
    .line 128
    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->h(LB4/a;)V

    .line 129
    .line 130
    .line 131
    goto :goto_1

    .line 132
    :cond_5
    instance-of v0, v0, LIx0/a$a$e;

    .line 133
    .line 134
    if-eqz v0, :cond_6

    .line 135
    .line 136
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->d:LEx0/a;

    .line 137
    .line 138
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->c:LB4/a;

    .line 139
    .line 140
    invoke-virtual {v1}, LB4/a;->i()Ljava/lang/Object;

    .line 141
    .line 142
    .line 143
    move-result-object v1

    .line 144
    check-cast v1, LIx0/a;

    .line 145
    .line 146
    invoke-virtual {v1}, LIx0/a;->A()LIx0/a$a$e;

    .line 147
    .line 148
    .line 149
    move-result-object v1

    .line 150
    invoke-virtual {v1}, LIx0/a$a$e;->a()Ljava/util/List;

    .line 151
    .line 152
    .line 153
    move-result-object v1

    .line 154
    invoke-virtual {v0, v1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 155
    .line 156
    .line 157
    goto :goto_1

    .line 158
    :cond_6
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 159
    .line 160
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 161
    .line 162
    .line 163
    throw p1

    .line 164
    :cond_7
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;->a(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p1
.end method
