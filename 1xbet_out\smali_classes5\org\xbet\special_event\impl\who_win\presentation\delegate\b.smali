.class public interface abstract Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u0008`\u0018\u00002\u00020\u0001J\'\u0010\t\u001a\u00020\u00082\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0006H&\u00a2\u0006\u0004\u0008\t\u0010\n\u00a8\u0006\u000b"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;",
        "",
        "Landroidx/fragment/app/Fragment;",
        "fragment",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;",
        "whoWinCardViewModel",
        "Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;",
        "entryPointType",
        "",
        "a",
        "(Landroidx/fragment/app/Fragment;Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Landroidx/fragment/app/Fragment;Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V
    .param p1    # Landroidx/fragment/app/Fragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method
