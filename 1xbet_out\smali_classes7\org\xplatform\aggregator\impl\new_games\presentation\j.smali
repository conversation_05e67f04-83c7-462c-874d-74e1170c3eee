.class public final synthetic Lorg/xplatform/aggregator/impl/new_games/presentation/j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/j;->a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/j;->a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    check-cast p1, LrZ0/b;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result p2

    invoke-static {v0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->r3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;LrZ0/b;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
