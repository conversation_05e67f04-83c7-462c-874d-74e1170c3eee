.class public final LG91/c$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LG91/c;->e(LB4/a;Lorg/xbet/uikit/components/chips/ChipGroup;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function1<",
        "Ljava/util/List<",
        "+",
        "Ljava/lang/Object;",
        ">;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function0;

.field public final synthetic b:Lorg/xbet/uikit/components/chips/ChipGroup;

.field public final synthetic c:LB4/a;

.field public final synthetic d:Lkotlin/jvm/functions/Function1;

.field public final synthetic e:LB4/a;

.field public final synthetic f:Lkotlin/jvm/functions/Function1;

.field public final synthetic g:Lorg/xbet/uikit/components/chips/ChipGroup;


# direct methods
.method public constructor <init>(Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/chips/ChipGroup;LB4/a;Lkotlin/jvm/functions/Function1;LB4/a;Lkotlin/jvm/functions/Function1;Lorg/xbet/uikit/components/chips/ChipGroup;)V
    .locals 0

    .line 1
    iput-object p1, p0, LG91/c$a;->a:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    iput-object p2, p0, LG91/c$a;->b:Lorg/xbet/uikit/components/chips/ChipGroup;

    .line 4
    .line 5
    iput-object p3, p0, LG91/c$a;->c:LB4/a;

    .line 6
    .line 7
    iput-object p4, p0, LG91/c$a;->d:Lkotlin/jvm/functions/Function1;

    .line 8
    .line 9
    iput-object p5, p0, LG91/c$a;->e:LB4/a;

    .line 10
    .line 11
    iput-object p6, p0, LG91/c$a;->f:Lkotlin/jvm/functions/Function1;

    .line 12
    .line 13
    iput-object p7, p0, LG91/c$a;->g:Lorg/xbet/uikit/components/chips/ChipGroup;

    .line 14
    .line 15
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 16
    .line 17
    .line 18
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    iget-object p1, p0, LG91/c$a;->a:Lkotlin/jvm/functions/Function0;

    .line 8
    .line 9
    invoke-interface {p1}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    iget-object p1, p0, LG91/c$a;->b:Lorg/xbet/uikit/components/chips/ChipGroup;

    .line 13
    .line 14
    invoke-virtual {p1}, Landroid/view/ViewGroup;->removeAllViews()V

    .line 15
    .line 16
    .line 17
    iget-object p1, p0, LG91/c$a;->b:Lorg/xbet/uikit/components/chips/ChipGroup;

    .line 18
    .line 19
    invoke-virtual {p1}, Lcom/google/android/material/chip/ChipGroup;->clearCheck()V

    .line 20
    .line 21
    .line 22
    iget-object p1, p0, LG91/c$a;->c:LB4/a;

    .line 23
    .line 24
    iget-object v0, p0, LG91/c$a;->b:Lorg/xbet/uikit/components/chips/ChipGroup;

    .line 25
    .line 26
    iget-object v1, p0, LG91/c$a;->d:Lkotlin/jvm/functions/Function1;

    .line 27
    .line 28
    invoke-static {p1, v0, v1}, LG91/c;->c(LB4/a;Lorg/xbet/uikit/components/chips/ChipGroup;Lkotlin/jvm/functions/Function1;)V

    .line 29
    .line 30
    .line 31
    return-void

    .line 32
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    .line 33
    .line 34
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 35
    .line 36
    .line 37
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 42
    .line 43
    .line 44
    move-result v1

    .line 45
    if-eqz v1, :cond_1

    .line 46
    .line 47
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    check-cast v1, Ljava/util/Collection;

    .line 52
    .line 53
    check-cast v1, Ljava/lang/Iterable;

    .line 54
    .line 55
    invoke-static {v0, v1}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 56
    .line 57
    .line 58
    goto :goto_0

    .line 59
    :cond_1
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    :cond_2
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 64
    .line 65
    .line 66
    move-result v0

    .line 67
    if-eqz v0, :cond_3

    .line 68
    .line 69
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    check-cast v0, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi$a;

    .line 74
    .line 75
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi$a$a;

    .line 76
    .line 77
    if-eqz v1, :cond_2

    .line 78
    .line 79
    iget-object v1, p0, LG91/c$a;->e:LB4/a;

    .line 80
    .line 81
    check-cast v0, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi$a$a;

    .line 82
    .line 83
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi$a$a;->a()Ljava/util/List;

    .line 84
    .line 85
    .line 86
    move-result-object v0

    .line 87
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->E1(Ljava/lang/Iterable;)Ljava/util/Set;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    iget-object v2, p0, LG91/c$a;->f:Lkotlin/jvm/functions/Function1;

    .line 92
    .line 93
    iget-object v3, p0, LG91/c$a;->g:Lorg/xbet/uikit/components/chips/ChipGroup;

    .line 94
    .line 95
    invoke-static {v1, v0, v2, v3}, LG91/c;->d(LB4/a;Ljava/util/Set;Lkotlin/jvm/functions/Function1;Lorg/xbet/uikit/components/chips/ChipGroup;)V

    .line 96
    .line 97
    .line 98
    goto :goto_1

    .line 99
    :cond_3
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LG91/c$a;->a(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p1
.end method
