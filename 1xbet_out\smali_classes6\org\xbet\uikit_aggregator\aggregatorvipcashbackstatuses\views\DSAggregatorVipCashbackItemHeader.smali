.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0010\r\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0001\u0018\u00002\u00020\u0001B\u001b\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u001f\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\n\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u000c\u0010\rJ7\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u00082\u0006\u0010\u0011\u001a\u00020\u00082\u0006\u0010\u0012\u001a\u00020\u00082\u0006\u0010\u0013\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0015\u0010\u0018\u001a\u00020\u000b2\u0006\u0010\u0017\u001a\u00020\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\r\u0010\u001a\u001a\u00020\u000b\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\r\u0010\u001c\u001a\u00020\u000b\u00a2\u0006\u0004\u0008\u001c\u0010\u001bJ\u001b\u0010\u001f\u001a\u00020\u000b2\u000c\u0010\u001e\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\u001d\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0017\u0010!\u001a\u00020\u000b2\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008!\u0010\"J\u000f\u0010#\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008#\u0010\u001bJ\u000f\u0010$\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008$\u0010\u001bJ\u000f\u0010%\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008%\u0010\u001bR\u0014\u0010(\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u0017\u0010-\u001a\u00020)8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010*\u001a\u0004\u0008+\u0010,R\u0017\u00102\u001a\u00020.8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010/\u001a\u0004\u00080\u00101\u00a8\u00063"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "",
        "text",
        "setTitle",
        "(Ljava/lang/CharSequence;)V",
        "b",
        "()V",
        "c",
        "Lkotlin/Function0;",
        "listener",
        "setOnHelpClickListener",
        "(Lkotlin/jvm/functions/Function0;)V",
        "g",
        "(I)V",
        "f",
        "e",
        "d",
        "a",
        "I",
        "space8",
        "Lorg/xbet/uikit/components/header/DSHeader;",
        "Lorg/xbet/uikit/components/header/DSHeader;",
        "getHeaderCashbackStatuses",
        "()Lorg/xbet/uikit/components/header/DSHeader;",
        "headerCashbackStatuses",
        "Lorg/xbet/uikit/components/buttons/DSButton;",
        "Lorg/xbet/uikit/components/buttons/DSButton;",
        "getBtnHelp",
        "()Lorg/xbet/uikit/components/buttons/DSButton;",
        "btnHelp",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:I

.field public final b:Lorg/xbet/uikit/components/header/DSHeader;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/uikit/components/buttons/DSButton;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 12
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->space_8:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->a:I

    .line 4
    new-instance v0, Lorg/xbet/uikit/components/header/DSHeader;

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/header/DSHeader;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    const/4 p1, 0x0

    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/header/DSHeader;->d(I)V

    .line 6
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 7
    new-instance v6, Lorg/xbet/uikit/components/buttons/DSButton;

    const/4 v10, 0x6

    const/4 v11, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    move-object v7, v1

    invoke-direct/range {v6 .. v11}, Lorg/xbet/uikit/components/buttons/DSButton;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 8
    new-instance p1, Landroid/widget/FrameLayout$LayoutParams;

    const/4 p2, -0x2

    invoke-direct {p1, p2, p2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v6, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 9
    sget-object p1, Lorg/xbet/uikit/components/buttons/DSButton$Style;->TERTIARY:Lorg/xbet/uikit/components/buttons/DSButton$Style;

    invoke-virtual {v6, p1}, Lorg/xbet/uikit/components/buttons/DSButton;->setButtonStyle(Lorg/xbet/uikit/components/buttons/DSButton$Style;)V

    .line 10
    sget-object p1, Lorg/xbet/uikit/components/buttons/DSButton$Type;->ICON_CIRCLE:Lorg/xbet/uikit/components/buttons/DSButton$Type;

    invoke-virtual {v6, p1}, Lorg/xbet/uikit/components/buttons/DSButton;->setButtonType(Lorg/xbet/uikit/components/buttons/DSButton$Type;)V

    .line 11
    sget-object p1, Lorg/xbet/uikit/components/buttons/DSButton$Size;->LARGE:Lorg/xbet/uikit/components/buttons/DSButton$Size;

    invoke-virtual {v6, p1}, Lorg/xbet/uikit/components/buttons/DSButton;->setButtonSize(Lorg/xbet/uikit/components/buttons/DSButton$Size;)V

    .line 12
    sget p1, LlZ0/h;->ic_glyph_question_circle:I

    invoke-virtual {v6, p1}, Lorg/xbet/uikit/components/buttons/DSButton;->setIconRes(I)V

    .line 13
    invoke-virtual {v6}, Lorg/xbet/uikit/components/buttons/DSButton;->s()V

    .line 14
    iput-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 15
    new-instance p1, Landroid/widget/FrameLayout$LayoutParams;

    const/4 v1, -0x1

    invoke-direct {p1, v1, p2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 16
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 17
    invoke-virtual {p0, v6}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 1
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic a(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->h(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V

    return-void
.end method

.method public static final h(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final b()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/header/DSHeader;->c(Z)V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 8
    .line 9
    const/16 v1, 0x8

    .line 10
    .line 11
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final c()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/header/DSHeader;->c(Z)V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final d()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 8
    .line 9
    invoke-virtual {v2}, Landroid/view/View;->getWidth()I

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    sub-int v2, v0, v2

    .line 14
    .line 15
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 16
    .line 17
    .line 18
    move-result v4

    .line 19
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 20
    .line 21
    invoke-virtual {v0}, Landroid/view/View;->getHeight()I

    .line 22
    .line 23
    .line 24
    move-result v5

    .line 25
    const/4 v3, 0x0

    .line 26
    move-object v0, p0

    .line 27
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public final e()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 8
    .line 9
    invoke-virtual {v2}, Landroid/view/View;->getWidth()I

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    sub-int/2addr v0, v2

    .line 14
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->a:I

    .line 15
    .line 16
    sub-int v4, v0, v2

    .line 17
    .line 18
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 19
    .line 20
    invoke-virtual {v0}, Landroid/view/View;->getHeight()I

    .line 21
    .line 22
    .line 23
    move-result v5

    .line 24
    const/4 v2, 0x0

    .line 25
    const/4 v3, 0x0

    .line 26
    move-object v0, p0

    .line 27
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public final f()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 5
    .line 6
    .line 7
    move-result v2

    .line 8
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 9
    .line 10
    .line 11
    move-result v1

    .line 12
    invoke-virtual {v0, v2, v1}, Landroid/view/View;->measure(II)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final g(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 4
    .line 5
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    sub-int/2addr p1, v1

    .line 10
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->a:I

    .line 11
    .line 12
    sub-int/2addr p1, v1

    .line 13
    const/high16 v1, 0x40000000    # 2.0f

    .line 14
    .line 15
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 16
    .line 17
    .line 18
    move-result p1

    .line 19
    const/4 v1, 0x0

    .line 20
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 21
    .line 22
    .line 23
    move-result v1

    .line 24
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public final getBtnHelp()Lorg/xbet/uikit/components/buttons/DSButton;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 2
    .line 3
    return-object v0
.end method

.method public final getHeaderCashbackStatuses()Lorg/xbet/uikit/components/header/DSHeader;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 2
    .line 3
    return-object v0
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-super/range {p0 .. p5}, Landroid/widget/FrameLayout;->onLayout(ZIIII)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->d()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->e()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public onMeasure(II)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->f()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->g(I)V

    .line 5
    .line 6
    .line 7
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 8
    .line 9
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 10
    .line 11
    .line 12
    move-result p2

    .line 13
    const/high16 v0, 0x40000000    # 2.0f

    .line 14
    .line 15
    invoke-static {p2, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 16
    .line 17
    .line 18
    move-result p2

    .line 19
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public final setOnHelpClickListener(Lkotlin/jvm/functions/Function0;)V
    .locals 2
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 2
    .line 3
    new-instance v1, Lk31/a;

    .line 4
    .line 5
    invoke-direct {v1, p1}, Lk31/a;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final setTitle(Ljava/lang/CharSequence;)V
    .locals 13
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/uikit/components/header/a$a;

    .line 4
    .line 5
    const/16 v11, 0x1fe

    .line 6
    .line 7
    const/4 v12, 0x0

    .line 8
    const/4 v3, 0x0

    .line 9
    const/4 v4, 0x0

    .line 10
    const/4 v5, 0x0

    .line 11
    const/4 v6, 0x0

    .line 12
    const/4 v7, 0x0

    .line 13
    const/4 v8, 0x0

    .line 14
    const/4 v9, 0x0

    .line 15
    const/4 v10, 0x0

    .line 16
    move-object v2, p1

    .line 17
    invoke-direct/range {v1 .. v12}, Lorg/xbet/uikit/components/header/a$a;-><init>(Ljava/lang/CharSequence;ZLjava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/Integer;Landroid/graphics/drawable/Drawable;LL11/c;LL11/c;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/header/DSHeader;->setModel(Lorg/xbet/uikit/components/header/a;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method
