.class public final Lc71/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lc71/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lc71/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lc71/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lc71/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LQv/v;LwX0/c;)Lc71/c;
    .locals 6

    .line 1
    invoke-static {p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static {p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    new-instance v0, Lc71/a$d;

    .line 8
    .line 9
    new-instance v1, Lc71/d;

    .line 10
    .line 11
    invoke-direct {v1}, Lc71/d;-><init>()V

    .line 12
    .line 13
    .line 14
    new-instance v2, LQv/w;

    .line 15
    .line 16
    invoke-direct {v2}, LQv/w;-><init>()V

    .line 17
    .line 18
    .line 19
    const/4 v5, 0x0

    .line 20
    move-object v3, p1

    .line 21
    move-object v4, p2

    .line 22
    invoke-direct/range {v0 .. v5}, Lc71/a$d;-><init>(Lc71/d;LQv/w;LQv/v;LwX0/c;Lc71/b;)V

    .line 23
    .line 24
    .line 25
    return-object v0
.end method
