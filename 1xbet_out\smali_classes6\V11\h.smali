.class public final LV11/h;
.super Landroidx/recyclerview/widget/RecyclerView$Adapter;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LV11/h$a;,
        LV11/h$b;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroidx/recyclerview/widget/RecyclerView$Adapter<",
        "LV11/h$a;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u000e\u0008\u0001\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\'J\u0017\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0004\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0017\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u000f\u0010\u000e\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u001f\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0010\u001a\u00020\u00022\u0006\u0010\u0011\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u001f\u0010\u0018\u001a\u00020\u00022\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0017\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019R\u0014\u0010\u001d\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010\u001cR\u0014\u0010 \u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001fR*\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00038\u0006@FX\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008!\u0010\"\u001a\u0004\u0008#\u0010$\"\u0004\u0008%\u0010&\u00a8\u0006("
    }
    d2 = {
        "LV11/h;",
        "Landroidx/recyclerview/widget/RecyclerView$Adapter;",
        "LV11/h$a;",
        "Landroidx/paging/s;",
        "loadState",
        "",
        "n",
        "(Landroidx/paging/s;)Z",
        "Landroid/content/Context;",
        "context",
        "LV11/l;",
        "o",
        "(Landroid/content/Context;)LV11/l;",
        "",
        "getItemCount",
        "()I",
        "holder",
        "position",
        "",
        "p",
        "(LV11/h$a;I)V",
        "Landroid/view/ViewGroup;",
        "parent",
        "viewType",
        "q",
        "(Landroid/view/ViewGroup;I)LV11/h$a;",
        "Lorg/xbet/uikit_aggregator/aggregatorCategory/AggregatorCategoryType;",
        "d",
        "Lorg/xbet/uikit_aggregator/aggregatorCategory/AggregatorCategoryType;",
        "type",
        "e",
        "I",
        "shimmerCount",
        "f",
        "Landroidx/paging/s;",
        "getLoadState",
        "()Landroidx/paging/s;",
        "r",
        "(Landroidx/paging/s;)V",
        "a",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final d:Lorg/xbet/uikit_aggregator/aggregatorCategory/AggregatorCategoryType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:I

.field public f:Landroidx/paging/s;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method private final n(Landroidx/paging/s;)Z
    .locals 0

    .line 1
    instance-of p1, p1, Landroidx/paging/s$b;

    .line 2
    .line 3
    return p1
.end method

.method private final o(Landroid/content/Context;)LV11/l;
    .locals 2

    .line 1
    iget-object v0, p0, LV11/h;->d:Lorg/xbet/uikit_aggregator/aggregatorCategory/AggregatorCategoryType;

    .line 2
    .line 3
    sget-object v1, LV11/h$b;->a:[I

    .line 4
    .line 5
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    aget v0, v1, v0

    .line 10
    .line 11
    const/4 v1, 0x1

    .line 12
    if-eq v0, v1, :cond_3

    .line 13
    .line 14
    const/4 v1, 0x2

    .line 15
    if-eq v0, v1, :cond_2

    .line 16
    .line 17
    const/4 v1, 0x3

    .line 18
    if-eq v0, v1, :cond_2

    .line 19
    .line 20
    const/4 v1, 0x4

    .line 21
    if-eq v0, v1, :cond_1

    .line 22
    .line 23
    const/4 v1, 0x5

    .line 24
    if-ne v0, v1, :cond_0

    .line 25
    .line 26
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorCategory/view/AggregatorCategoryRectangleView;

    .line 27
    .line 28
    invoke-direct {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatorCategory/view/AggregatorCategoryRectangleView;-><init>(Landroid/content/Context;)V

    .line 29
    .line 30
    .line 31
    return-object v0

    .line 32
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 33
    .line 34
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 35
    .line 36
    .line 37
    throw p1

    .line 38
    :cond_1
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorCategory/view/AggregatorCategoryIconRightView;

    .line 39
    .line 40
    invoke-direct {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatorCategory/view/AggregatorCategoryIconRightView;-><init>(Landroid/content/Context;)V

    .line 41
    .line 42
    .line 43
    return-object v0

    .line 44
    :cond_2
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorCategory/view/AggregatorCategoryIconLeftView;

    .line 45
    .line 46
    invoke-direct {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatorCategory/view/AggregatorCategoryIconLeftView;-><init>(Landroid/content/Context;)V

    .line 47
    .line 48
    .line 49
    return-object v0

    .line 50
    :cond_3
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorCategory/view/AggregatorCategoryFullView;

    .line 51
    .line 52
    invoke-direct {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatorCategory/view/AggregatorCategoryFullView;-><init>(Landroid/content/Context;)V

    .line 53
    .line 54
    .line 55
    return-object v0
.end method


# virtual methods
.method public getItemCount()I
    .locals 1

    .line 1
    iget-object v0, p0, LV11/h;->f:Landroidx/paging/s;

    .line 2
    .line 3
    invoke-direct {p0, v0}, LV11/h;->n(Landroidx/paging/s;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget v0, p0, LV11/h;->e:I

    .line 10
    .line 11
    return v0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    return v0
.end method

.method public bridge synthetic onBindViewHolder(Landroidx/recyclerview/widget/RecyclerView$D;I)V
    .locals 0

    .line 1
    check-cast p1, LV11/h$a;

    .line 2
    .line 3
    invoke-virtual {p0, p1, p2}, LV11/h;->p(LV11/h$a;I)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public bridge synthetic onCreateViewHolder(Landroid/view/ViewGroup;I)Landroidx/recyclerview/widget/RecyclerView$D;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, LV11/h;->q(Landroid/view/ViewGroup;I)LV11/h$a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public p(LV11/h$a;I)V
    .locals 0
    .param p1    # LV11/h$a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LV11/h$a;->e()LV11/l;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    sget-object p2, LV11/i;->a:LV11/i;

    .line 6
    .line 7
    invoke-interface {p1, p2}, LV11/l;->b(LV11/m;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public q(Landroid/view/ViewGroup;I)LV11/h$a;
    .locals 0
    .param p1    # Landroid/view/ViewGroup;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance p2, LV11/h$a;

    .line 2
    .line 3
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-direct {p0, p1}, LV11/h;->o(Landroid/content/Context;)LV11/l;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-direct {p2, p1}, LV11/h$a;-><init>(LV11/l;)V

    .line 12
    .line 13
    .line 14
    return-object p2
.end method

.method public final r(Landroidx/paging/s;)V
    .locals 3
    .param p1    # Landroidx/paging/s;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LV11/h;->f:Landroidx/paging/s;

    .line 2
    .line 3
    invoke-static {v0, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_3

    .line 8
    .line 9
    iget-object v0, p0, LV11/h;->f:Landroidx/paging/s;

    .line 10
    .line 11
    invoke-direct {p0, v0}, LV11/h;->n(Landroidx/paging/s;)Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    invoke-direct {p0, p1}, LV11/h;->n(Landroidx/paging/s;)Z

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    const/4 v2, 0x0

    .line 20
    if-eqz v0, :cond_0

    .line 21
    .line 22
    if-nez v1, :cond_0

    .line 23
    .line 24
    iget v0, p0, LV11/h;->e:I

    .line 25
    .line 26
    invoke-virtual {p0, v2, v0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->notifyItemRangeRemoved(II)V

    .line 27
    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_0
    if-eqz v1, :cond_1

    .line 31
    .line 32
    if-nez v0, :cond_1

    .line 33
    .line 34
    iget v0, p0, LV11/h;->e:I

    .line 35
    .line 36
    invoke-virtual {p0, v2, v0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->notifyItemRangeInserted(II)V

    .line 37
    .line 38
    .line 39
    goto :goto_0

    .line 40
    :cond_1
    if-eqz v0, :cond_2

    .line 41
    .line 42
    iget v0, p0, LV11/h;->e:I

    .line 43
    .line 44
    invoke-virtual {p0, v2, v0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->notifyItemRangeChanged(II)V

    .line 45
    .line 46
    .line 47
    :cond_2
    :goto_0
    iput-object p1, p0, LV11/h;->f:Landroidx/paging/s;

    .line 48
    .line 49
    :cond_3
    return-void
.end method
