.class public final LU61/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LP61/a;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u001f\u0010\t\u001a\u00020\u00082\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\t\u0010\n\u00a8\u0006\u000b"
    }
    d2 = {
        "LU61/b;",
        "LP61/a;",
        "<init>",
        "()V",
        "",
        "titleRes",
        "",
        "endPoint",
        "LwX0/B;",
        "a",
        "(ILjava/lang/String;)LwX0/B;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(ILjava/lang/String;)LwX0/B;
    .locals 2
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LU61/a;

    .line 2
    .line 3
    new-instance v1, LT61/a;

    .line 4
    .line 5
    invoke-direct {v1, p1, p2}, LT61/a;-><init>(ILjava/lang/String;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {v0, v1}, LU61/a;-><init>(LT61/a;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method
