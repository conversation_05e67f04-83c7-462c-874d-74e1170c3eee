.class public final LSM0/w;
.super LeZ0/a;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0005\u0008\u0000\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0015\u0010\t\u001a\u00020\u00082\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\t\u0010\nR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "LSM0/w;",
        "LeZ0/a;",
        "LpM0/r;",
        "binding",
        "<init>",
        "(LpM0/r;)V",
        "LRM0/h;",
        "item",
        "",
        "h",
        "(LRM0/h;)V",
        "e",
        "LpM0/r;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final e:LpM0/r;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LpM0/r;)V
    .locals 1
    .param p1    # LpM0/r;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LpM0/r;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-direct {p0, v0}, LeZ0/a;-><init>(Landroid/view/View;)V

    .line 6
    .line 7
    .line 8
    iput-object p1, p0, LSM0/w;->e:LpM0/r;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final h(LRM0/h;)V
    .locals 7
    .param p1    # LRM0/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LSM0/w;->e:LpM0/r;

    .line 2
    .line 3
    iget-object v0, v0, LpM0/r;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 4
    .line 5
    sget v1, Lpb/g;->no_photo_statistic:I

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageResource(I)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p1}, LRM0/h;->f()Ljava/lang/String;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    if-lez v0, :cond_0

    .line 19
    .line 20
    sget-object v1, LCX0/l;->a:LCX0/l;

    .line 21
    .line 22
    iget-object v0, p0, LSM0/w;->e:LpM0/r;

    .line 23
    .line 24
    iget-object v2, v0, LpM0/r;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 25
    .line 26
    sget-object v3, Lorg/xbet/ui_common/utils/image/ImageCropType;->CIRCLE_IMAGE:Lorg/xbet/ui_common/utils/image/ImageCropType;

    .line 27
    .line 28
    sget-object v0, LDX0/e;->a:LDX0/e;

    .line 29
    .line 30
    invoke-virtual {p1}, LRM0/h;->f()Ljava/lang/String;

    .line 31
    .line 32
    .line 33
    move-result-object v4

    .line 34
    const-wide/16 v5, 0x0

    .line 35
    .line 36
    invoke-virtual {v0, v4, v5, v6}, LDX0/e;->b(Ljava/lang/String;J)Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v5

    .line 40
    sget v6, Lpb/g;->no_photo_statistic:I

    .line 41
    .line 42
    const/4 v4, 0x1

    .line 43
    invoke-virtual/range {v1 .. v6}, LCX0/l;->E(Landroid/widget/ImageView;Lorg/xbet/ui_common/utils/image/ImageCropType;ZLjava/lang/String;I)V

    .line 44
    .line 45
    .line 46
    :cond_0
    iget-object v0, p0, LSM0/w;->e:LpM0/r;

    .line 47
    .line 48
    iget-object v0, v0, LpM0/r;->e:Landroid/widget/TextView;

    .line 49
    .line 50
    invoke-virtual {p1}, LRM0/h;->i()Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 55
    .line 56
    .line 57
    iget-object v0, p0, LSM0/w;->e:LpM0/r;

    .line 58
    .line 59
    iget-object v0, v0, LpM0/r;->d:Landroid/widget/TextView;

    .line 60
    .line 61
    invoke-virtual {p1}, LRM0/h;->g()Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 66
    .line 67
    .line 68
    iget-object p1, p0, LSM0/w;->e:LpM0/r;

    .line 69
    .line 70
    invoke-virtual {p1}, LpM0/r;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    if-eqz v0, :cond_1

    .line 79
    .line 80
    const/4 v1, -0x1

    .line 81
    iput v1, v0, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 82
    .line 83
    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 84
    .line 85
    .line 86
    return-void

    .line 87
    :cond_1
    new-instance p1, Ljava/lang/NullPointerException;

    .line 88
    .line 89
    const-string v0, "null cannot be cast to non-null type android.view.ViewGroup.LayoutParams"

    .line 90
    .line 91
    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 92
    .line 93
    .line 94
    throw p1
.end method
