.class public final synthetic LGD0/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:LGD0/b$e;

.field public final synthetic b:LID0/c;


# direct methods
.method public synthetic constructor <init>(LGD0/b$e;LID0/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LGD0/c;->a:LGD0/b$e;

    iput-object p2, p0, LGD0/c;->b:LID0/c;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, LGD0/c;->a:LGD0/b$e;

    iget-object v1, p0, LGD0/c;->b:LID0/c;

    invoke-static {v0, v1, p1}, LGD0/b$e;->d(LGD0/b$e;LID0/c;Landroid/view/View;)V

    return-void
.end method
