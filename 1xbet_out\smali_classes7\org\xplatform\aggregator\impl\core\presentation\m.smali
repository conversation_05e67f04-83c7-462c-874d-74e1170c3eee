.class public final synthetic Lorg/xplatform/aggregator/impl/core/presentation/m;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/m;->a:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/m;->a:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;

    invoke-static {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->E2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
