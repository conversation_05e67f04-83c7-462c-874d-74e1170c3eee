.class public final synthetic LN1/S;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public static a(LN1/T;J)V
    .locals 0

    .line 1
    return-void
.end method

.method public static b(LN1/T;Landroidx/media3/common/j;IZ)I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-interface {p0, p1, p2, p3, v0}, LN1/T;->c(Landroidx/media3/common/j;IZI)I

    .line 3
    .line 4
    .line 5
    move-result p0

    .line 6
    return p0
.end method

.method public static c(LN1/T;Lt1/G;I)V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-interface {p0, p1, p2, v0}, LN1/T;->f(Lt1/G;II)V

    .line 3
    .line 4
    .line 5
    return-void
.end method
