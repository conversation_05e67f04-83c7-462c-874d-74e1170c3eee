.class public final LL3/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LL3/b;


# instance fields
.field public final a:Landroid/content/Context;

.field public final b:LL3/b$a;


# direct methods
.method public constructor <init>(Landroid/content/Context;LL3/b$a;)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # LL3/b$a;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iput-object p1, p0, LL3/d;->a:Landroid/content/Context;

    .line 9
    .line 10
    iput-object p2, p0, LL3/d;->b:LL3/b$a;

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public b()V
    .locals 0

    .line 1
    invoke-virtual {p0}, LL3/d;->i()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public c()V
    .locals 0

    .line 1
    invoke-virtual {p0}, LL3/d;->f()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final f()V
    .locals 2

    .line 1
    iget-object v0, p0, LL3/d;->a:Landroid/content/Context;

    .line 2
    .line 3
    invoke-static {v0}, LL3/r;->a(Landroid/content/Context;)LL3/r;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, LL3/d;->b:LL3/b$a;

    .line 8
    .line 9
    invoke-virtual {v0, v1}, LL3/r;->d(LL3/b$a;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final i()V
    .locals 2

    .line 1
    iget-object v0, p0, LL3/d;->a:Landroid/content/Context;

    .line 2
    .line 3
    invoke-static {v0}, LL3/r;->a(Landroid/content/Context;)LL3/r;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, LL3/d;->b:LL3/b$a;

    .line 8
    .line 9
    invoke-virtual {v0, v1}, LL3/r;->e(LL3/b$a;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public onDestroy()V
    .locals 0

    .line 1
    return-void
.end method
