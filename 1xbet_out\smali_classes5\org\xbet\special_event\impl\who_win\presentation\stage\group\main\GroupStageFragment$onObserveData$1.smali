.class final Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.who_win.presentation.stage.group.main.GroupStageFragment$onObserveData$1"
    f = "GroupStageFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "LRy0/b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "LRy0/b;",
        "uiState",
        "",
        "<anonymous>",
        "(LRy0/b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public final invoke(LRy0/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LRy0/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, LRy0/b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;->invoke(LRy0/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_3

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, LRy0/b;

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    .line 16
    .line 17
    invoke-static {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->I2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)LGq0/t;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    iget-object v0, v0, LGq0/t;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 22
    .line 23
    invoke-virtual {p1}, LRy0/b;->d()Z

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    if-eqz v1, :cond_0

    .line 28
    .line 29
    const/4 v1, 0x0

    .line 30
    goto :goto_0

    .line 31
    :cond_0
    const/16 v1, 0x8

    .line 32
    .line 33
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 34
    .line 35
    .line 36
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    .line 37
    .line 38
    invoke-static {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->E2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)LNy0/b;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    invoke-virtual {p1}, LRy0/b;->a()Ljava/util/List;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    invoke-virtual {v0, v1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {p1}, LRy0/b;->b()Ljava/util/List;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    .line 54
    .line 55
    invoke-static {v1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->D2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)LNy0/a;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    if-eqz v1, :cond_1

    .line 60
    .line 61
    invoke-virtual {v1}, LkY0/a;->getItems()Ljava/util/List;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    goto :goto_1

    .line 66
    :cond_1
    const/4 v1, 0x0

    .line 67
    :goto_1
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 68
    .line 69
    .line 70
    move-result v0

    .line 71
    if-nez v0, :cond_2

    .line 72
    .line 73
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    .line 74
    .line 75
    invoke-static {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->D2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)LNy0/a;

    .line 76
    .line 77
    .line 78
    move-result-object v0

    .line 79
    if-eqz v0, :cond_2

    .line 80
    .line 81
    invoke-virtual {p1}, LRy0/b;->b()Ljava/util/List;

    .line 82
    .line 83
    .line 84
    move-result-object v1

    .line 85
    invoke-virtual {v0, v1}, LkY0/a;->K(Ljava/util/List;)V

    .line 86
    .line 87
    .line 88
    :cond_2
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    .line 89
    .line 90
    invoke-static {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->I2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)LGq0/t;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    iget-object v0, v0, LGq0/t;->c:Landroidx/viewpager2/widget/ViewPager2;

    .line 95
    .line 96
    invoke-virtual {p1}, LRy0/b;->c()I

    .line 97
    .line 98
    .line 99
    move-result p1

    .line 100
    invoke-virtual {v0, p1}, Landroidx/viewpager2/widget/ViewPager2;->setCurrentItem(I)V

    .line 101
    .line 102
    .line 103
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 104
    .line 105
    return-object p1

    .line 106
    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 107
    .line 108
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 109
    .line 110
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 111
    .line 112
    .line 113
    throw p1
.end method
