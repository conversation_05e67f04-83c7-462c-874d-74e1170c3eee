.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/q;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/q;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/q;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;

    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;->A2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoContainerFragment;)LVa1/r;

    move-result-object v0

    return-object v0
.end method
