.class public final Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/social/PromoSocialViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a5\u0010\t\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00080\u00070\u00062\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u00002\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\t\u0010\n\u00a8\u0006\u000b"
    }
    d2 = {
        "Lkotlin/Function1;",
        "Lh21/q;",
        "",
        "onClick",
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "LA4/c;",
        "",
        "LVX0/i;",
        "g",
        "(Lkotlin/jvm/functions/Function1;LUX0/k;)LA4/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/social/PromoSocialViewHolderKt;->m(LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/social/PromoSocialViewHolderKt;->l(LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LB4/a;Lkotlin/jvm/functions/Function1;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/social/PromoSocialViewHolderKt;->j(LB4/a;Lkotlin/jvm/functions/Function1;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lkotlin/jvm/functions/Function1;Lh21/q;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/social/PromoSocialViewHolderKt;->k(Lkotlin/jvm/functions/Function1;Lh21/q;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/z0;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/social/PromoSocialViewHolderKt;->h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/z0;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Lkotlin/jvm/functions/Function1;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/social/PromoSocialViewHolderKt;->i(Lkotlin/jvm/functions/Function1;LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function1;LUX0/k;)LA4/c;
    .locals 3
    .param p0    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LUX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lh21/q;",
            "Lkotlin/Unit;",
            ">;",
            "LUX0/k;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LCa1/a;

    .line 2
    .line 3
    invoke-direct {v0}, LCa1/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LCa1/b;

    .line 7
    .line 8
    invoke-direct {v1, p0, p1}, LCa1/b;-><init>(Lkotlin/jvm/functions/Function1;LUX0/k;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/social/PromoSocialViewHolderKt$promoSocialViewHolder$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/social/PromoSocialViewHolderKt$promoSocialViewHolder$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/social/PromoSocialViewHolderKt$promoSocialViewHolder$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/social/PromoSocialViewHolderKt$promoSocialViewHolder$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v2, LB4/b;

    .line 19
    .line 20
    invoke-direct {v2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v2
.end method

.method public static final h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/z0;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/z0;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/z0;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final i(Lkotlin/jvm/functions/Function1;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, LCa1/c;

    .line 2
    .line 3
    invoke-direct {v0, p2, p0}, LCa1/c;-><init>(LB4/a;Lkotlin/jvm/functions/Function1;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p2, v0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 7
    .line 8
    .line 9
    new-instance p0, LCa1/d;

    .line 10
    .line 11
    invoke-direct {p0, p1, p2}, LCa1/d;-><init>(LUX0/k;LB4/a;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p2, p0}, LB4/a;->r(Lkotlin/jvm/functions/Function0;)V

    .line 15
    .line 16
    .line 17
    new-instance p0, LCa1/e;

    .line 18
    .line 19
    invoke-direct {p0, p1, p2}, LCa1/e;-><init>(LUX0/k;LB4/a;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p2, p0}, LB4/a;->s(Lkotlin/jvm/functions/Function0;)V

    .line 23
    .line 24
    .line 25
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 26
    .line 27
    return-object p0
.end method

.method public static final j(LB4/a;Lkotlin/jvm/functions/Function1;Ljava/util/List;)Lkotlin/Unit;
    .locals 8

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, Lza1/a$g;

    .line 6
    .line 7
    invoke-interface {p2}, Lza1/a$g;->t()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    .line 12
    .line 13
    .line 14
    move-result p2

    .line 15
    const/16 v0, 0x8

    .line 16
    .line 17
    if-eqz p2, :cond_0

    .line 18
    .line 19
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    check-cast p1, LS91/z0;

    .line 24
    .line 25
    iget-object p1, p1, LS91/z0;->b:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;

    .line 26
    .line 27
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    check-cast p1, LS91/z0;

    .line 35
    .line 36
    iget-object p1, p1, LS91/z0;->c:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworksShimmer;

    .line 37
    .line 38
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object p0

    .line 42
    check-cast p0, Lza1/a$g;

    .line 43
    .line 44
    invoke-interface {p0}, Lza1/a$g;->getType()Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/SocialNetworkStyle;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    invoke-virtual {p1, p0}, Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworksShimmer;->t(Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/SocialNetworkStyle;)V

    .line 49
    .line 50
    .line 51
    goto/16 :goto_0

    .line 52
    .line 53
    :cond_0
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 54
    .line 55
    .line 56
    move-result-object p2

    .line 57
    check-cast p2, LS91/z0;

    .line 58
    .line 59
    iget-object p2, p2, LS91/z0;->b:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;

    .line 60
    .line 61
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    check-cast v1, Lza1/a$g;

    .line 66
    .line 67
    invoke-interface {v1}, Lza1/a$g;->getType()Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/SocialNetworkStyle;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;->i(Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/SocialNetworkStyle;)V

    .line 72
    .line 73
    .line 74
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 75
    .line 76
    .line 77
    move-result-object p2

    .line 78
    check-cast p2, LS91/z0;

    .line 79
    .line 80
    iget-object p2, p2, LS91/z0;->b:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;

    .line 81
    .line 82
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 83
    .line 84
    .line 85
    move-result-object v1

    .line 86
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    sget v2, Lpb/k;->social:I

    .line 91
    .line 92
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 93
    .line 94
    .line 95
    move-result-object v1

    .line 96
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;->setTitle(Ljava/lang/String;)V

    .line 97
    .line 98
    .line 99
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 100
    .line 101
    .line 102
    move-result-object p2

    .line 103
    check-cast p2, LS91/z0;

    .line 104
    .line 105
    iget-object p2, p2, LS91/z0;->b:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;

    .line 106
    .line 107
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 108
    .line 109
    .line 110
    move-result-object v1

    .line 111
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 112
    .line 113
    .line 114
    move-result-object v1

    .line 115
    sget v2, Lpb/k;->read_aggregator_social_networks:I

    .line 116
    .line 117
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 118
    .line 119
    .line 120
    move-result-object v1

    .line 121
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;->setSubtitle(Ljava/lang/String;)V

    .line 122
    .line 123
    .line 124
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 125
    .line 126
    .line 127
    move-result-object p2

    .line 128
    check-cast p2, LS91/z0;

    .line 129
    .line 130
    iget-object p2, p2, LS91/z0;->b:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;

    .line 131
    .line 132
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 133
    .line 134
    .line 135
    move-result-object v1

    .line 136
    check-cast v1, Lza1/a$g;

    .line 137
    .line 138
    invoke-interface {v1}, Lza1/a$g;->t()Ljava/util/List;

    .line 139
    .line 140
    .line 141
    move-result-object v1

    .line 142
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;->setItems(Ljava/util/List;)V

    .line 143
    .line 144
    .line 145
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 146
    .line 147
    .line 148
    move-result-object p2

    .line 149
    check-cast p2, LS91/z0;

    .line 150
    .line 151
    iget-object p2, p2, LS91/z0;->b:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;

    .line 152
    .line 153
    new-instance v1, LCa1/f;

    .line 154
    .line 155
    invoke-direct {v1, p1}, LCa1/f;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 156
    .line 157
    .line 158
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;->setOnItemClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 159
    .line 160
    .line 161
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 162
    .line 163
    .line 164
    move-result-object p1

    .line 165
    check-cast p1, Lza1/a$g;

    .line 166
    .line 167
    invoke-interface {p1}, Lza1/a$g;->getType()Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/SocialNetworkStyle;

    .line 168
    .line 169
    .line 170
    move-result-object p1

    .line 171
    sget-object p2, Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/SocialNetworkStyle;->CELLS:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/SocialNetworkStyle;

    .line 172
    .line 173
    if-eq p1, p2, :cond_1

    .line 174
    .line 175
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 176
    .line 177
    .line 178
    move-result-object p1

    .line 179
    check-cast p1, Lza1/a$g;

    .line 180
    .line 181
    invoke-interface {p1}, Lza1/a$g;->getType()Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/SocialNetworkStyle;

    .line 182
    .line 183
    .line 184
    move-result-object p1

    .line 185
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/SocialNetworkStyle;->RECTANGLE_HORIZONTAL:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/SocialNetworkStyle;

    .line 186
    .line 187
    if-eq p1, v1, :cond_1

    .line 188
    .line 189
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 190
    .line 191
    .line 192
    move-result-object p1

    .line 193
    check-cast p1, LS91/z0;

    .line 194
    .line 195
    iget-object v1, p1, LS91/z0;->b:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;

    .line 196
    .line 197
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 198
    .line 199
    .line 200
    move-result-object p1

    .line 201
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 202
    .line 203
    .line 204
    move-result-object p1

    .line 205
    sget v2, LlZ0/g;->space_12:I

    .line 206
    .line 207
    invoke-virtual {p1, v2}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 208
    .line 209
    .line 210
    move-result v5

    .line 211
    const/4 v6, 0x7

    .line 212
    const/4 v7, 0x0

    .line 213
    const/4 v2, 0x0

    .line 214
    const/4 v3, 0x0

    .line 215
    const/4 v4, 0x0

    .line 216
    invoke-static/range {v1 .. v7}, Lorg/xbet/ui_common/utils/ExtensionsKt;->o0(Landroid/view/View;IIIIILjava/lang/Object;)V

    .line 217
    .line 218
    .line 219
    :cond_1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 220
    .line 221
    .line 222
    move-result-object p1

    .line 223
    check-cast p1, Lza1/a$g;

    .line 224
    .line 225
    invoke-interface {p1}, Lza1/a$g;->getType()Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/SocialNetworkStyle;

    .line 226
    .line 227
    .line 228
    move-result-object p1

    .line 229
    if-eq p1, p2, :cond_2

    .line 230
    .line 231
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 232
    .line 233
    .line 234
    move-result-object p1

    .line 235
    check-cast p1, LS91/z0;

    .line 236
    .line 237
    iget-object v1, p1, LS91/z0;->b:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;

    .line 238
    .line 239
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 240
    .line 241
    .line 242
    move-result-object p1

    .line 243
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 244
    .line 245
    .line 246
    move-result-object p1

    .line 247
    sget p2, LlZ0/g;->space_8:I

    .line 248
    .line 249
    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 250
    .line 251
    .line 252
    move-result v3

    .line 253
    const/16 v6, 0xd

    .line 254
    .line 255
    const/4 v7, 0x0

    .line 256
    const/4 v2, 0x0

    .line 257
    const/4 v4, 0x0

    .line 258
    const/4 v5, 0x0

    .line 259
    invoke-static/range {v1 .. v7}, Lorg/xbet/ui_common/utils/ExtensionsKt;->o0(Landroid/view/View;IIIIILjava/lang/Object;)V

    .line 260
    .line 261
    .line 262
    :cond_2
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 263
    .line 264
    .line 265
    move-result-object p1

    .line 266
    check-cast p1, LS91/z0;

    .line 267
    .line 268
    iget-object p1, p1, LS91/z0;->b:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;

    .line 269
    .line 270
    const/4 p2, 0x0

    .line 271
    invoke-virtual {p1, p2}, Landroid/view/View;->setVisibility(I)V

    .line 272
    .line 273
    .line 274
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 275
    .line 276
    .line 277
    move-result-object p1

    .line 278
    check-cast p1, LS91/z0;

    .line 279
    .line 280
    iget-object p1, p1, LS91/z0;->c:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworksShimmer;

    .line 281
    .line 282
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 283
    .line 284
    .line 285
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 286
    .line 287
    .line 288
    move-result-object p0

    .line 289
    check-cast p0, LS91/z0;

    .line 290
    .line 291
    iget-object p0, p0, LS91/z0;->c:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworksShimmer;

    .line 292
    .line 293
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworksShimmer;->x()V

    .line 294
    .line 295
    .line 296
    :goto_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 297
    .line 298
    return-object p0
.end method

.method public static final k(Lkotlin/jvm/functions/Function1;Lh21/q;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final l(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getAbsoluteAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, LS91/z0;

    .line 14
    .line 15
    iget-object p1, p1, LS91/z0;->b:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;

    .line 16
    .line 17
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;->getSocialNetworksRecycler()Landroidx/recyclerview/widget/RecyclerView;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-virtual {p0, v0, p1}, LUX0/k;->c(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 22
    .line 23
    .line 24
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 25
    .line 26
    return-object p0
.end method

.method public static final m(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getAbsoluteAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, LS91/z0;

    .line 14
    .line 15
    iget-object p1, p1, LS91/z0;->b:Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;

    .line 16
    .line 17
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorSocialNetworks/AggregatorSocialNetworks;->getSocialNetworksRecycler()Landroidx/recyclerview/widget/RecyclerView;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-virtual {p0, v0, p1}, LUX0/k;->e(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 22
    .line 23
    .line 24
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 25
    .line 26
    return-object p0
.end method
