.class public final Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/b;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/b;->b:LBc/a;

    .line 7
    .line 8
    return-void
.end method

.method public static a(LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/b;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;",
            ">;)",
            "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/b;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/b;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/b;-><init>(LBc/a;LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;)Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;-><init>(Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/b;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/b;->b:LBc/a;

    .line 10
    .line 11
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    check-cast v1, Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 16
    .line 17
    invoke-static {v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/b;->c(Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;)Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/b;->b()Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
