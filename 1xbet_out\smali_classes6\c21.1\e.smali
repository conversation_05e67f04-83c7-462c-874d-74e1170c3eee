.class public final synthetic Lc21/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabsFilledGroup;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabsFilledGroup;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lc21/e;->a:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabsFilledGroup;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lc21/e;->a:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabsFilledGroup;

    check-cast p1, Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilled;

    check-cast p2, Ljava/lang/Boolean;

    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p2

    invoke-static {v0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabsFilledGroup;->b(Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabsFilledGroup;Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilled;Z)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
