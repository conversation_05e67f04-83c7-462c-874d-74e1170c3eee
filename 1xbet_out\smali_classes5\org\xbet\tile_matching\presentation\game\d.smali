.class public final synthetic Lorg/xbet/tile_matching/presentation/game/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/d;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/d;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;

    invoke-static {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->A2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
