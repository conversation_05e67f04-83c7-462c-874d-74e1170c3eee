.class public final Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_sport/eventcard/middle/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000l\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0010\r\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\u0019\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u001f\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\u000e\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u000f\u0010\u0012\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0013J\u000f\u0010\u0015\u001a\u00020\u000fH\u0014\u00a2\u0006\u0004\u0008\u0015\u0010\u0013J\u0017\u0010\u0018\u001a\u00020\u000f2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0015\u0010\u001c\u001a\u00020\u000f2\u0006\u0010\u001b\u001a\u00020\u001a\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u0015\u0010 \u001a\u00020\u000f2\u0006\u0010\u001f\u001a\u00020\u001e\u00a2\u0006\u0004\u0008 \u0010!J\u0017\u0010\u0018\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0018\u0010\"J\u0017\u0010%\u001a\u00020\u000f2\u0008\u0010$\u001a\u0004\u0018\u00010#\u00a2\u0006\u0004\u0008%\u0010&J\u0017\u0010%\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\'\u001a\u00020\u0007\u00a2\u0006\u0004\u0008%\u0010\"J\u0015\u0010%\u001a\u00020\u000f2\u0006\u0010)\u001a\u00020(\u00a2\u0006\u0004\u0008%\u0010*J\u001f\u0010%\u001a\u00020\u000f2\u0006\u0010)\u001a\u00020(2\u0008\u0010+\u001a\u0004\u0018\u00010#\u00a2\u0006\u0004\u0008%\u0010,J\u0017\u0010-\u001a\u00020\u000f2\u0008\u0010$\u001a\u0004\u0018\u00010#\u00a2\u0006\u0004\u0008-\u0010&J\u0017\u0010-\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\'\u001a\u00020\u0007\u00a2\u0006\u0004\u0008-\u0010\"J\u0015\u0010-\u001a\u00020\u000f2\u0006\u0010)\u001a\u00020(\u00a2\u0006\u0004\u0008-\u0010*J\u001f\u0010-\u001a\u00020\u000f2\u0006\u0010)\u001a\u00020(2\u0008\u0010+\u001a\u0004\u0018\u00010#\u00a2\u0006\u0004\u0008-\u0010,J\u0017\u0010.\u001a\u00020\u000f2\u0008\u0010$\u001a\u0004\u0018\u00010#\u00a2\u0006\u0004\u0008.\u0010&J\u0017\u0010.\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\'\u001a\u00020\u0007\u00a2\u0006\u0004\u0008.\u0010\"J\u0015\u0010.\u001a\u00020\u000f2\u0006\u0010)\u001a\u00020(\u00a2\u0006\u0004\u0008.\u0010*J\u001f\u0010.\u001a\u00020\u000f2\u0006\u0010)\u001a\u00020(2\u0008\u0010+\u001a\u0004\u0018\u00010#\u00a2\u0006\u0004\u0008.\u0010,J\u0017\u0010/\u001a\u00020\u000f2\u0008\u0010$\u001a\u0004\u0018\u00010#\u00a2\u0006\u0004\u0008/\u0010&J\u0017\u0010/\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\'\u001a\u00020\u0007\u00a2\u0006\u0004\u0008/\u0010\"J\u0015\u0010/\u001a\u00020\u000f2\u0006\u0010)\u001a\u00020(\u00a2\u0006\u0004\u0008/\u0010*J\u001f\u0010/\u001a\u00020\u000f2\u0006\u0010)\u001a\u00020(2\u0008\u0010+\u001a\u0004\u0018\u00010#\u00a2\u0006\u0004\u0008/\u0010,J\u0017\u00100\u001a\u00020\u000f2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u00080\u0010\u0019J\u0017\u00100\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u0007\u00a2\u0006\u0004\u00080\u0010\"J\u0017\u00101\u001a\u00020\u000f2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u00081\u0010\u0019J\u0017\u00101\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u0007\u00a2\u0006\u0004\u00081\u0010\"J\u0017\u00102\u001a\u00020\u000f2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u00082\u0010\u0019J\u0017\u00102\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u0007\u00a2\u0006\u0004\u00082\u0010\"J\u0017\u00103\u001a\u00020\u000f2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u00083\u0010\u0019J\u0017\u00103\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u0007\u00a2\u0006\u0004\u00083\u0010\"J\u0015\u00105\u001a\u00020\u000f2\u0006\u00104\u001a\u00020\u001e\u00a2\u0006\u0004\u00085\u0010!J\u0015\u00106\u001a\u00020\u000f2\u0006\u00104\u001a\u00020\u001e\u00a2\u0006\u0004\u00086\u0010!J\u0017\u00107\u001a\u00020\u000f2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u00087\u0010\u0019J\u0017\u00107\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u0007\u00a2\u0006\u0004\u00087\u0010\"J!\u00108\u001a\u00020\u000f2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u00162\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u00088\u00109J!\u00108\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u00088\u0010:J!\u0010;\u001a\u00020\u000f2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u00162\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008;\u00109J!\u0010;\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008;\u0010:J\u0017\u0010<\u001a\u00020\u000f2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u0008<\u0010\u0019J\u0017\u0010<\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u0007\u00a2\u0006\u0004\u0008<\u0010\"J!\u0010=\u001a\u00020\u000f2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u00162\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008=\u00109J!\u0010=\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008=\u0010:J!\u0010>\u001a\u00020\u000f2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u00162\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008>\u00109J!\u0010>\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008>\u0010:J\u0017\u0010?\u001a\u00020\u000f2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u0008?\u0010\u0019J\u0017\u0010?\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u0007\u00a2\u0006\u0004\u0008?\u0010\"J!\u0010@\u001a\u00020\u000f2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u00162\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008@\u00109J!\u0010@\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008@\u0010:J!\u0010A\u001a\u00020\u000f2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u00162\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008A\u00109J!\u0010A\u001a\u00020\u000f2\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008A\u0010:J\r\u0010C\u001a\u00020B\u00a2\u0006\u0004\u0008C\u0010DR\u0014\u0010G\u001a\u00020B8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008E\u0010FR#\u0010N\u001a\n I*\u0004\u0018\u00010H0H8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008J\u0010K\u001a\u0004\u0008L\u0010M\u00a8\u0006O"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Lorg/xbet/uikit_sport/eventcard/middle/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "Landroid/widget/TextView;",
        "textView",
        "Lorg/xbet/uikit/core/eventcard/ScoreState;",
        "scoreState",
        "",
        "u",
        "(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V",
        "t",
        "()V",
        "v",
        "onDetachedFromWindow",
        "",
        "text",
        "setInfoText",
        "(Ljava/lang/CharSequence;)V",
        "",
        "time",
        "setGameStartTime",
        "(J)V",
        "",
        "visible",
        "setTimerVisible",
        "(Z)V",
        "(I)V",
        "Landroid/graphics/drawable/Drawable;",
        "drawable",
        "setTopFirstLogo",
        "(Landroid/graphics/drawable/Drawable;)V",
        "resId",
        "",
        "url",
        "(Ljava/lang/String;)V",
        "placeholder",
        "(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V",
        "setBotFirstLogo",
        "setTopSecondLogo",
        "setBotSecondLogo",
        "setTopTeamName",
        "setBotTeamName",
        "setTopScore",
        "setBotScore",
        "show",
        "setTopGameIndicator",
        "setBotGameIndicator",
        "setGameText",
        "setTopGameScore",
        "(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V",
        "(ILorg/xbet/uikit/core/eventcard/ScoreState;)V",
        "setBotGameScore",
        "setSetText",
        "setTopSetScore",
        "setBotSetScore",
        "setResultText",
        "setTopResultScore",
        "setBotResultScore",
        "LC31/t;",
        "getBinding",
        "()LC31/t;",
        "a",
        "LC31/t;",
        "binding",
        "Landroid/view/animation/Animation;",
        "kotlin.jvm.PlatformType",
        "b",
        "Lkotlin/j;",
        "getRotateAnimation",
        "()Landroid/view/animation/Animation;",
        "rotateAnimation",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:LC31/t;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p2

    invoke-static {p2, p0}, LC31/t;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/t;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    .line 7
    sget-object p2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    new-instance p3, Lorg/xbet/uikit_sport/eventcard/middle/b;

    invoke-direct {p3, p1}, Lorg/xbet/uikit_sport/eventcard/middle/b;-><init>(Landroid/content/Context;)V

    invoke-static {p2, p3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->b:Lkotlin/j;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->eventCardMiddleCricketStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method private final getRotateAnimation()Landroid/view/animation/Animation;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->b:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/view/animation/Animation;

    .line 8
    .line 9
    return-object v0
.end method

.method public static synthetic s(Landroid/content/Context;)Landroid/view/animation/Animation;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->w(Landroid/content/Context;)Landroid/view/animation/Animation;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic setBotGameScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;ILorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 2
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setBotGameScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setBotGameScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 1
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setBotGameScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setBotResultScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;ILorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 2
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setBotResultScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setBotResultScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 1
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setBotResultScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setBotSetScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;ILorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 2
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setBotSetScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setBotSetScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 1
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setBotSetScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setTopGameScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;ILorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 2
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setTopGameScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setTopGameScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 1
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setTopGameScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setTopResultScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;ILorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 2
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setTopResultScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setTopResultScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 1
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setTopResultScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setTopSetScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;ILorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 2
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setTopSetScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setTopSetScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 1
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setTopSetScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static final w(Landroid/content/Context;)Landroid/view/animation/Animation;
    .locals 1

    .line 1
    sget v0, Lm31/a;->game_indicator_rotate_animation:I

    .line 2
    .line 3
    invoke-static {p0, v0}, Landroid/view/animation/AnimationUtils;->loadAnimation(Landroid/content/Context;I)Landroid/view/animation/Animation;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    return-object p0
.end method


# virtual methods
.method public final getBinding()LC31/t;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    .line 2
    .line 3
    return-object v0
.end method

.method public onDetachedFromWindow()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroid/view/ViewGroup;->onDetachedFromWindow()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    .line 5
    .line 6
    iget-object v0, v0, LC31/t;->q:Landroid/widget/ImageView;

    .line 7
    .line 8
    invoke-virtual {v0}, Landroid/view/View;->clearAnimation()V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    .line 12
    .line 13
    iget-object v0, v0, LC31/t;->c:Landroid/widget/ImageView;

    .line 14
    .line 15
    invoke-virtual {v0}, Landroid/view/View;->clearAnimation()V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public final setBotFirstLogo(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setBotFirstLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setBotFirstLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/4 v1, 0x0

    if-nez p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    const/16 v1, 0x8

    .line 2
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setBotFirstLogo(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v1, v0, LC31/t;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xe

    const/4 v7, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setBotFirstLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v1, v0, LC31/t;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xc

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    move-object v3, p2

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setBotGameIndicator(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    .line 2
    .line 3
    iget-object v0, v0, LC31/t;->c:Landroid/widget/ImageView;

    .line 4
    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    goto :goto_0

    .line 9
    :cond_0
    const/16 v1, 0x8

    .line 10
    .line 11
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    if-eqz p1, :cond_1

    .line 15
    .line 16
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    .line 17
    .line 18
    iget-object p1, p1, LC31/t;->c:Landroid/widget/ImageView;

    .line 19
    .line 20
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->getRotateAnimation()Landroid/view/animation/Animation;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-virtual {p1, v0}, Landroid/view/View;->startAnimation(Landroid/view/animation/Animation;)V

    .line 25
    .line 26
    .line 27
    return-void

    .line 28
    :cond_1
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    .line 29
    .line 30
    iget-object p1, p1, LC31/t;->c:Landroid/widget/ImageView;

    .line 31
    .line 32
    invoke-virtual {p1}, Landroid/view/View;->clearAnimation()V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public final setBotGameScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setBotGameScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setBotGameScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 3
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->d:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->d:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object p1, p1, LC31/t;->d:Landroid/widget/TextView;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->u(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setBotResultScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setBotResultScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setBotResultScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 3
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->e:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->e:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object p1, p1, LC31/t;->e:Landroid/widget/TextView;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->u(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setBotScore(I)V
    .locals 1

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setBotScore(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setBotScore(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->f:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->f:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->v()V

    return-void
.end method

.method public final setBotSecondLogo(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setBotSecondLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setBotSecondLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->g:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/4 v1, 0x0

    if-nez p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    const/16 v1, 0x8

    .line 2
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->g:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setBotSecondLogo(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v1, v0, LC31/t;->g:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xe

    const/4 v7, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setBotSecondLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v1, v0, LC31/t;->g:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xc

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    move-object v3, p2

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setBotSetScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setBotSetScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setBotSetScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 3
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->h:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->h:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object p1, p1, LC31/t;->h:Landroid/widget/TextView;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->u(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setBotTeamName(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setBotTeamName(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setBotTeamName(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->i:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->i:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setGameStartTime(J)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    .line 2
    .line 3
    iget-object v0, v0, LC31/t;->l:Lorg/xbet/uikit/components/timer/TimerWithDescription;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit/components/timer/TimerWithDescription;->setGameStartTime(J)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setGameText(I)V
    .locals 1

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setGameText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setGameText(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->j:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->j:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->t()V

    return-void
.end method

.method public final setInfoText(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setInfoText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setInfoText(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->l:Lorg/xbet/uikit/components/timer/TimerWithDescription;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/4 v1, 0x4

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->l:Lorg/xbet/uikit/components/timer/TimerWithDescription;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/timer/TimerWithDescription;->setSubTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setResultText(I)V
    .locals 1

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setResultText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setResultText(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->m:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->m:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->t()V

    return-void
.end method

.method public final setSetText(I)V
    .locals 1

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setSetText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSetText(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->n:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->n:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->t()V

    return-void
.end method

.method public final setTimerVisible(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    .line 2
    .line 3
    iget-object v0, v0, LC31/t;->l:Lorg/xbet/uikit/components/timer/TimerWithDescription;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/timer/TimerWithDescription;->setTimerVisible(Z)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setTopFirstLogo(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setTopFirstLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTopFirstLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->p:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/4 v1, 0x0

    if-nez p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    const/16 v1, 0x8

    .line 2
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->p:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTopFirstLogo(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v1, v0, LC31/t;->p:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xe

    const/4 v7, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setTopFirstLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v1, v0, LC31/t;->p:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xc

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    move-object v3, p2

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setTopGameIndicator(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    .line 2
    .line 3
    iget-object v0, v0, LC31/t;->q:Landroid/widget/ImageView;

    .line 4
    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    goto :goto_0

    .line 9
    :cond_0
    const/16 v1, 0x8

    .line 10
    .line 11
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    if-eqz p1, :cond_1

    .line 15
    .line 16
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    .line 17
    .line 18
    iget-object p1, p1, LC31/t;->q:Landroid/widget/ImageView;

    .line 19
    .line 20
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->getRotateAnimation()Landroid/view/animation/Animation;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-virtual {p1, v0}, Landroid/view/View;->startAnimation(Landroid/view/animation/Animation;)V

    .line 25
    .line 26
    .line 27
    return-void

    .line 28
    :cond_1
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    .line 29
    .line 30
    iget-object p1, p1, LC31/t;->q:Landroid/widget/ImageView;

    .line 31
    .line 32
    invoke-virtual {p1}, Landroid/view/View;->clearAnimation()V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public final setTopGameScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setTopGameScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setTopGameScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 3
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->r:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->r:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object p1, p1, LC31/t;->r:Landroid/widget/TextView;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->u(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setTopResultScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setTopResultScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setTopResultScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 3
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->s:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->s:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object p1, p1, LC31/t;->s:Landroid/widget/TextView;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->u(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setTopScore(I)V
    .locals 1

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setTopScore(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTopScore(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->t:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->t:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->v()V

    return-void
.end method

.method public final setTopSecondLogo(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setTopSecondLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTopSecondLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->u:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/4 v1, 0x0

    if-nez p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    const/16 v1, 0x8

    .line 2
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->u:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTopSecondLogo(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v1, v0, LC31/t;->u:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xe

    const/4 v7, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setTopSecondLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v1, v0, LC31/t;->u:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xc

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    move-object v3, p2

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setTopSetScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setTopSetScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setTopSetScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 3
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->v:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->v:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object p1, p1, LC31/t;->v:Landroid/widget/TextView;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->u(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setTopTeamName(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->setTopTeamName(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTopTeamName(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->w:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    iget-object v0, v0, LC31/t;->w:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final t()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    .line 2
    .line 3
    iget-object v0, v0, LC31/t;->l:Lorg/xbet/uikit/components/timer/TimerWithDescription;

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    check-cast v1, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;

    .line 12
    .line 13
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    sget v3, LlZ0/g;->space_8:I

    .line 18
    .line 19
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    invoke-virtual {v1, v2}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginEnd(I)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 27
    .line 28
    .line 29
    return-void

    .line 30
    :cond_0
    new-instance v0, Ljava/lang/NullPointerException;

    .line 31
    .line 32
    const-string v1, "null cannot be cast to non-null type androidx.constraintlayout.widget.ConstraintLayout.LayoutParams"

    .line 33
    .line 34
    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 35
    .line 36
    .line 37
    throw v0
.end method

.method public final u(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p2}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    aget p2, v0, p2

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p2, v0, :cond_3

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p2, v0, :cond_2

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-eq p2, v0, :cond_1

    .line 17
    .line 18
    const/4 v0, 0x4

    .line 19
    if-ne p2, v0, :cond_0

    .line 20
    .line 21
    sget p2, LlZ0/n;->TextStyle_Caption_Regular_L_StaticGreen:I

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 25
    .line 26
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 27
    .line 28
    .line 29
    throw p1

    .line 30
    :cond_1
    sget p2, LlZ0/n;->TextStyle_Caption_Medium_L_StaticGreen:I

    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_2
    sget p2, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_3
    sget p2, LlZ0/n;->TextStyle_Caption_Medium_L_TextPrimary:I

    .line 37
    .line 38
    :goto_0
    invoke-static {p1, p2}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 39
    .line 40
    .line 41
    return-void
.end method

.method public final v()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;->a:LC31/t;

    .line 2
    .line 3
    iget-object v0, v0, LC31/t;->l:Lorg/xbet/uikit/components/timer/TimerWithDescription;

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    check-cast v1, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;

    .line 12
    .line 13
    const/4 v2, 0x0

    .line 14
    invoke-virtual {v1, v2}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginEnd(I)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 18
    .line 19
    .line 20
    return-void

    .line 21
    :cond_0
    new-instance v0, Ljava/lang/NullPointerException;

    .line 22
    .line 23
    const-string v1, "null cannot be cast to non-null type androidx.constraintlayout.widget.ConstraintLayout.LayoutParams"

    .line 24
    .line 25
    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 26
    .line 27
    .line 28
    throw v0
.end method
