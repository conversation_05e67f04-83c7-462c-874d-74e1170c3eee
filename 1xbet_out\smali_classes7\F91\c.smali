.class public final synthetic LF91/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:L<PERSON><PERSON>/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(ZLkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-boolean p1, p0, LF91/c;->a:Z

    iput-object p2, p0, LF91/c;->b:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-boolean v0, p0, LF91/c;->a:Z

    iget-object v1, p0, LF91/c;->b:Lkotlin/jvm/functions/Function1;

    check-cast p1, LB4/a;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/category/presentation/adapters/ProviderItemDelegateKt;->d(ZLkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
