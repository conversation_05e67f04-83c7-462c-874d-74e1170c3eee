.class public final synthetic LIG0/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:I


# direct methods
.method public synthetic constructor <init>(I)V
    .locals 0

    .line 1
    invoke-direct {p0}, <PERSON>java/lang/Object;-><init>()V

    iput p1, p0, LIG0/e;->a:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget v0, p0, LIG0/e;->a:I

    check-cast p1, LB4/a;

    invoke-static {v0, p1}, Lorg/xbet/statistic/lineup/presentation/adapters/delegate/MultiFieldAdapterDelegateKt;->c(ILB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
