.class final Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.tile_matching.presentation.game.TileMatchingEndGameFragment$subscribeOnViewActions$2"
    f = "TileMatchingEndGameFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->P2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;",
        "action",
        "",
        "<anonymous>",
        "(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;

    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;->invoke(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;

    .line 16
    .line 17
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->d()Lorg/xbet/core/data/LuckyWheelBonusType;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->e()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->i()D

    .line 26
    .line 27
    .line 28
    move-result-wide v3

    .line 29
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->f()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v5

    .line 33
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->g()Z

    .line 34
    .line 35
    .line 36
    move-result v6

    .line 37
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->h()Z

    .line 38
    .line 39
    .line 40
    move-result v7

    .line 41
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->c()D

    .line 42
    .line 43
    .line 44
    move-result-wide v8

    .line 45
    invoke-static/range {v0 .. v9}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->D2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZZD)V

    .line 46
    .line 47
    .line 48
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 49
    .line 50
    return-object p1

    .line 51
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 52
    .line 53
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 54
    .line 55
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 56
    .line 57
    .line 58
    throw p1
.end method
