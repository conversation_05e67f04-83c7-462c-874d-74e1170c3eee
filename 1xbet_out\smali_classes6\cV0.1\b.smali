.class public final synthetic LcV0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/toto_bet/outcomes/TotoBetAccurateChipsView;

.field public final synthetic b:LgV0/d;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/toto_bet/outcomes/TotoBetAccurateChipsView;LgV0/d;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LcV0/b;->a:Lorg/xbet/toto_bet/outcomes/TotoBetAccurateChipsView;

    iput-object p2, p0, LcV0/b;->b:LgV0/d;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, LcV0/b;->a:Lorg/xbet/toto_bet/outcomes/TotoBetAccurateChipsView;

    iget-object v1, p0, LcV0/b;->b:LgV0/d;

    invoke-static {v0, v1, p1}, Lorg/xbet/toto_bet/outcomes/TotoBetAccurateChipsView;->a(Lorg/xbet/toto_bet/outcomes/TotoBetAccurateChipsView;LgV0/d;Landroid/view/View;)V

    return-void
.end method
