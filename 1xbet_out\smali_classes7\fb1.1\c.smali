.class public final synthetic Lfb1/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:LB4/a;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;LB4/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lfb1/c;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, Lfb1/c;->b:LB4/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lfb1/c;->a:Lkotlin/jvm/functions/Function1;

    iget-object v1, p0, Lfb1/c;->b:LB4/a;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/prize/AggregatorTournamentCardItemDelegateKt;->d(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
