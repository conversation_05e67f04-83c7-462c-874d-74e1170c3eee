.class public final LQK0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQK0/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LQK0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LQK0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LQK0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LQW0/c;LEN0/f;LGL0/a;LwX0/c;LSX0/c;Lorg/xbet/ui_common/utils/M;Lc8/b;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LHX0/e;JLSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;LTn/a;LaN0/a;)LQK0/c;
    .locals 21

    .line 1
    invoke-static/range {p1 .. p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static/range {p2 .. p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    invoke-static/range {p10 .. p10}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    invoke-static/range {p12 .. p12}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    invoke-static/range {p13 .. p14}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    invoke-static/range {p15 .. p15}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    invoke-static/range {p16 .. p16}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    invoke-static/range {p17 .. p17}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    invoke-static/range {p18 .. p18}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    invoke-static/range {p19 .. p19}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 57
    .line 58
    .line 59
    new-instance v1, LQK0/a$b;

    .line 60
    .line 61
    invoke-static/range {p13 .. p14}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 62
    .line 63
    .line 64
    move-result-object v14

    .line 65
    const/16 v20, 0x0

    .line 66
    .line 67
    move-object/from16 v2, p1

    .line 68
    .line 69
    move-object/from16 v3, p2

    .line 70
    .line 71
    move-object/from16 v4, p3

    .line 72
    .line 73
    move-object/from16 v5, p4

    .line 74
    .line 75
    move-object/from16 v6, p5

    .line 76
    .line 77
    move-object/from16 v7, p6

    .line 78
    .line 79
    move-object/from16 v8, p7

    .line 80
    .line 81
    move-object/from16 v9, p8

    .line 82
    .line 83
    move-object/from16 v10, p9

    .line 84
    .line 85
    move-object/from16 v11, p10

    .line 86
    .line 87
    move-object/from16 v12, p11

    .line 88
    .line 89
    move-object/from16 v13, p12

    .line 90
    .line 91
    move-object/from16 v15, p15

    .line 92
    .line 93
    move-object/from16 v16, p16

    .line 94
    .line 95
    move-object/from16 v17, p17

    .line 96
    .line 97
    move-object/from16 v18, p18

    .line 98
    .line 99
    move-object/from16 v19, p19

    .line 100
    .line 101
    invoke-direct/range {v1 .. v20}, LQK0/a$b;-><init>(LQW0/c;LEN0/f;LGL0/a;LwX0/c;LSX0/c;Lorg/xbet/ui_common/utils/M;Lc8/b;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LHX0/e;Ljava/lang/Long;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;LTn/a;LaN0/a;LQK0/b;)V

    .line 102
    .line 103
    .line 104
    return-object v1
.end method
