.class public interface abstract LTU0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LTU0/c$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008a\u0018\u00002\u00020\u0001:\u0001\u000eJ\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0017\u0010\u0008\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0007H&\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\u000b\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\nH&\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u000e\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\rH&\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u00a8\u0006\u0010"
    }
    d2 = {
        "LTU0/c;",
        "",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;",
        "fragment",
        "",
        "d",
        "(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;",
        "c",
        "(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)V",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;",
        "b",
        "(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)V",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;",
        "a",
        "(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)V
    .param p1    # Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract b(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)V
    .param p1    # Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract c(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)V
    .param p1    # Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract d(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V
    .param p1    # Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method
