.class public final Lgc1/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LVb1/b;",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "a",
        "(LVb1/b;)Lorg/xplatform/aggregator/api/model/Game;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LVb1/b;)Lorg/xplatform/aggregator/api/model/Game;
    .locals 20
    .param p0    # LVb1/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p0}, LVb1/b;->b()J

    .line 2
    .line 3
    .line 4
    move-result-wide v1

    .line 5
    invoke-virtual/range {p0 .. p0}, LVb1/b;->s()J

    .line 6
    .line 7
    .line 8
    move-result-wide v3

    .line 9
    invoke-virtual/range {p0 .. p0}, LVb1/b;->A()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    int-to-long v5, v0

    .line 14
    invoke-virtual/range {p0 .. p0}, LVb1/b;->e()Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v10

    .line 18
    invoke-virtual/range {p0 .. p0}, LVb1/b;->f()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object v11

    .line 22
    invoke-virtual/range {p0 .. p0}, LVb1/b;->j()Z

    .line 23
    .line 24
    .line 25
    move-result v15

    .line 26
    invoke-virtual/range {p0 .. p0}, LVb1/b;->d()Z

    .line 27
    .line 28
    .line 29
    move-result v16

    .line 30
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 31
    .line 32
    .line 33
    move-result-object v19

    .line 34
    new-instance v0, Lorg/xplatform/aggregator/api/model/Game;

    .line 35
    .line 36
    const/16 v17, 0x0

    .line 37
    .line 38
    const/16 v18, 0x0

    .line 39
    .line 40
    const-wide/16 v7, 0x0

    .line 41
    .line 42
    const-string v9, ""

    .line 43
    .line 44
    const/4 v12, 0x0

    .line 45
    const/4 v13, 0x0

    .line 46
    const/4 v14, 0x0

    .line 47
    invoke-direct/range {v0 .. v19}, Lorg/xplatform/aggregator/api/model/Game;-><init>(JJJJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZZZZZZLjava/util/List;)V

    .line 48
    .line 49
    .line 50
    return-object v0
.end method
