.class public final synthetic LIN0/u;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:I

.field public final synthetic b:I

.field public final synthetic c:Z

.field public final synthetic d:Lkotlin/jvm/functions/Function0;

.field public final synthetic e:Landroidx/compose/ui/l;

.field public final synthetic f:LOc/n;

.field public final synthetic g:I

.field public final synthetic h:I


# direct methods
.method public synthetic constructor <init>(IIZLkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;LOc/n;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, LIN0/u;->a:I

    iput p2, p0, LIN0/u;->b:I

    iput-boolean p3, p0, LIN0/u;->c:Z

    iput-object p4, p0, LIN0/u;->d:Lkot<PERSON>/jvm/functions/Function0;

    iput-object p5, p0, LIN0/u;->e:Landroidx/compose/ui/l;

    iput-object p6, p0, LIN0/u;->f:LOc/n;

    iput p7, p0, LIN0/u;->g:I

    iput p8, p0, LIN0/u;->h:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    iget v0, p0, LIN0/u;->a:I

    iget v1, p0, LIN0/u;->b:I

    iget-boolean v2, p0, LIN0/u;->c:Z

    iget-object v3, p0, LIN0/u;->d:Lkotlin/jvm/functions/Function0;

    iget-object v4, p0, LIN0/u;->e:Landroidx/compose/ui/l;

    iget-object v5, p0, LIN0/u;->f:LOc/n;

    iget v6, p0, LIN0/u;->g:I

    iget v7, p0, LIN0/u;->h:I

    move-object v8, p1

    check-cast v8, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v9

    invoke-static/range {v0 .. v9}, LIN0/v;->a(IIZLkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;LOc/n;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
