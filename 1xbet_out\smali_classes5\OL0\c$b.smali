.class public final LOL0/c$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LOL0/h;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LOL0/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LOL0/c$b$a;
    }
.end annotation


# instance fields
.field public final a:LSX0/a;

.field public final b:LOL0/c$b;

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LLL0/b;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stadium/impl/core/data/repository/StadiumRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LRL0/c;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/a;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LRL0/a;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stadium/impl/core/presentation/viewmodel/BaseStadiumViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Li8/a;Lorg/xbet/onexdatabase/OnexDatabase;LwX0/c;LHX0/e;Ljava/lang/String;Ljava/lang/Long;Lorg/xbet/remoteconfig/domain/usecases/i;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LOL0/c$b;->b:LOL0/c$b;

    .line 4
    iput-object p4, p0, LOL0/c$b;->a:LSX0/a;

    .line 5
    invoke-virtual/range {p0 .. p13}, LOL0/c$b;->e(LQW0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Li8/a;Lorg/xbet/onexdatabase/OnexDatabase;LwX0/c;LHX0/e;Ljava/lang/String;Ljava/lang/Long;Lorg/xbet/remoteconfig/domain/usecases/i;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Li8/a;Lorg/xbet/onexdatabase/OnexDatabase;LwX0/c;LHX0/e;Ljava/lang/String;Ljava/lang/Long;Lorg/xbet/remoteconfig/domain/usecases/i;Lc8/h;LOL0/d;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p13}, LOL0/c$b;-><init>(LQW0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Li8/a;Lorg/xbet/onexdatabase/OnexDatabase;LwX0/c;LHX0/e;Ljava/lang/String;Ljava/lang/Long;Lorg/xbet/remoteconfig/domain/usecases/i;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/stadium/impl/track/fragment/TrackFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LOL0/c$b;->i(Lorg/xbet/statistic/stadium/impl/track/fragment/TrackFragment;)Lorg/xbet/statistic/stadium/impl/track/fragment/TrackFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/statistic/stadium/impl/arena/fragment/ArenaFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LOL0/c$b;->f(Lorg/xbet/statistic/stadium/impl/arena/fragment/ArenaFragment;)Lorg/xbet/statistic/stadium/impl/arena/fragment/ArenaFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public c(Lorg/xbet/statistic/stadium/impl/statium/fragment/StadiumFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LOL0/c$b;->h(Lorg/xbet/statistic/stadium/impl/statium/fragment/StadiumFragment;)Lorg/xbet/statistic/stadium/impl/statium/fragment/StadiumFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public d(Lorg/xbet/statistic/stadium/impl/route/fragment/RouteFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LOL0/c$b;->g(Lorg/xbet/statistic/stadium/impl/route/fragment/RouteFragment;)Lorg/xbet/statistic/stadium/impl/route/fragment/RouteFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final e(LQW0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Li8/a;Lorg/xbet/onexdatabase/OnexDatabase;LwX0/c;LHX0/e;Ljava/lang/String;Ljava/lang/Long;Lorg/xbet/remoteconfig/domain/usecases/i;Lc8/h;)V
    .locals 0

    .line 1
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p7

    .line 5
    iput-object p7, p0, LOL0/c$b;->c:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p7

    .line 11
    iput-object p7, p0, LOL0/c$b;->d:Ldagger/internal/h;

    .line 12
    .line 13
    new-instance p7, LOL0/c$b$a;

    .line 14
    .line 15
    invoke-direct {p7, p1}, LOL0/c$b$a;-><init>(LQW0/c;)V

    .line 16
    .line 17
    .line 18
    iput-object p7, p0, LOL0/c$b;->e:Ldagger/internal/h;

    .line 19
    .line 20
    invoke-static {p2}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iput-object p1, p0, LOL0/c$b;->f:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static {p1}, LLL0/c;->a(LBc/a;)LLL0/c;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iput-object p1, p0, LOL0/c$b;->g:Ldagger/internal/h;

    .line 31
    .line 32
    invoke-static {p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iput-object p1, p0, LOL0/c$b;->h:Ldagger/internal/h;

    .line 37
    .line 38
    iget-object p2, p0, LOL0/c$b;->e:Ldagger/internal/h;

    .line 39
    .line 40
    iget-object p7, p0, LOL0/c$b;->g:Ldagger/internal/h;

    .line 41
    .line 42
    invoke-static {p2, p7, p1}, Lorg/xbet/statistic/stadium/impl/core/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/stadium/impl/core/data/repository/a;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    iput-object p1, p0, LOL0/c$b;->i:Ldagger/internal/h;

    .line 47
    .line 48
    invoke-static {p1}, LRL0/d;->a(LBc/a;)LRL0/d;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iput-object p1, p0, LOL0/c$b;->j:Ldagger/internal/h;

    .line 53
    .line 54
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    iput-object p1, p0, LOL0/c$b;->k:Ldagger/internal/h;

    .line 59
    .line 60
    invoke-static {p1}, LRL0/b;->a(LBc/a;)LRL0/b;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    iput-object p1, p0, LOL0/c$b;->l:Ldagger/internal/h;

    .line 65
    .line 66
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    iput-object p1, p0, LOL0/c$b;->m:Ldagger/internal/h;

    .line 71
    .line 72
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    iput-object p1, p0, LOL0/c$b;->n:Ldagger/internal/h;

    .line 77
    .line 78
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    iput-object p1, p0, LOL0/c$b;->o:Ldagger/internal/h;

    .line 83
    .line 84
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    iput-object p1, p0, LOL0/c$b;->p:Ldagger/internal/h;

    .line 89
    .line 90
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    iput-object p1, p0, LOL0/c$b;->q:Ldagger/internal/h;

    .line 95
    .line 96
    invoke-static {p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 97
    .line 98
    .line 99
    move-result-object p12

    .line 100
    iput-object p12, p0, LOL0/c$b;->r:Ldagger/internal/h;

    .line 101
    .line 102
    iget-object p2, p0, LOL0/c$b;->c:Ldagger/internal/h;

    .line 103
    .line 104
    iget-object p3, p0, LOL0/c$b;->d:Ldagger/internal/h;

    .line 105
    .line 106
    iget-object p4, p0, LOL0/c$b;->j:Ldagger/internal/h;

    .line 107
    .line 108
    iget-object p5, p0, LOL0/c$b;->l:Ldagger/internal/h;

    .line 109
    .line 110
    iget-object p6, p0, LOL0/c$b;->m:Ldagger/internal/h;

    .line 111
    .line 112
    iget-object p7, p0, LOL0/c$b;->n:Ldagger/internal/h;

    .line 113
    .line 114
    iget-object p8, p0, LOL0/c$b;->o:Ldagger/internal/h;

    .line 115
    .line 116
    iget-object p9, p0, LOL0/c$b;->e:Ldagger/internal/h;

    .line 117
    .line 118
    iget-object p10, p0, LOL0/c$b;->p:Ldagger/internal/h;

    .line 119
    .line 120
    iget-object p11, p0, LOL0/c$b;->q:Ldagger/internal/h;

    .line 121
    .line 122
    invoke-static/range {p2 .. p12}, Lorg/xbet/statistic/stadium/impl/core/presentation/viewmodel/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/stadium/impl/core/presentation/viewmodel/a;

    .line 123
    .line 124
    .line 125
    move-result-object p1

    .line 126
    iput-object p1, p0, LOL0/c$b;->s:Ldagger/internal/h;

    .line 127
    .line 128
    return-void
.end method

.method public final f(Lorg/xbet/statistic/stadium/impl/arena/fragment/ArenaFragment;)Lorg/xbet/statistic/stadium/impl/arena/fragment/ArenaFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LOL0/c$b;->a:LSX0/a;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lorg/xbet/statistic/stadium/impl/arena/fragment/d;->a(Lorg/xbet/statistic/stadium/impl/arena/fragment/ArenaFragment;LSX0/a;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, LOL0/c$b;->k()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-static {p1, v0}, Lorg/xbet/statistic/stadium/impl/arena/fragment/d;->b(Lorg/xbet/statistic/stadium/impl/arena/fragment/ArenaFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 11
    .line 12
    .line 13
    return-object p1
.end method

.method public final g(Lorg/xbet/statistic/stadium/impl/route/fragment/RouteFragment;)Lorg/xbet/statistic/stadium/impl/route/fragment/RouteFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LOL0/c$b;->a:LSX0/a;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lorg/xbet/statistic/stadium/impl/route/fragment/d;->a(Lorg/xbet/statistic/stadium/impl/route/fragment/RouteFragment;LSX0/a;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, LOL0/c$b;->k()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-static {p1, v0}, Lorg/xbet/statistic/stadium/impl/route/fragment/d;->b(Lorg/xbet/statistic/stadium/impl/route/fragment/RouteFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 11
    .line 12
    .line 13
    return-object p1
.end method

.method public final h(Lorg/xbet/statistic/stadium/impl/statium/fragment/StadiumFragment;)Lorg/xbet/statistic/stadium/impl/statium/fragment/StadiumFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LOL0/c$b;->a:LSX0/a;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lorg/xbet/statistic/stadium/impl/statium/fragment/d;->a(Lorg/xbet/statistic/stadium/impl/statium/fragment/StadiumFragment;LSX0/a;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, LOL0/c$b;->k()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-static {p1, v0}, Lorg/xbet/statistic/stadium/impl/statium/fragment/d;->b(Lorg/xbet/statistic/stadium/impl/statium/fragment/StadiumFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 11
    .line 12
    .line 13
    return-object p1
.end method

.method public final i(Lorg/xbet/statistic/stadium/impl/track/fragment/TrackFragment;)Lorg/xbet/statistic/stadium/impl/track/fragment/TrackFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LOL0/c$b;->a:LSX0/a;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lorg/xbet/statistic/stadium/impl/track/fragment/d;->a(Lorg/xbet/statistic/stadium/impl/track/fragment/TrackFragment;LSX0/a;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, LOL0/c$b;->k()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-static {p1, v0}, Lorg/xbet/statistic/stadium/impl/track/fragment/d;->b(Lorg/xbet/statistic/stadium/impl/track/fragment/TrackFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 11
    .line 12
    .line 13
    return-object p1
.end method

.method public final j()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/statistic/stadium/impl/core/presentation/viewmodel/BaseStadiumViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LOL0/c$b;->s:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final k()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LOL0/c$b;->j()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
