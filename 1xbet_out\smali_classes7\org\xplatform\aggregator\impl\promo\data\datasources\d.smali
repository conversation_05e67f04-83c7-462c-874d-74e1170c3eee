.class public final synthetic Lorg/xplatform/aggregator/impl/promo/data/datasources/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/promo/data/datasources/e;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/promo/data/datasources/d;->a:Lorg/xplatform/aggregator/impl/promo/data/datasources/e;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/promo/data/datasources/d;->a:Lorg/xplatform/aggregator/impl/promo/data/datasources/e;

    invoke-static {v0}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->b(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;)Landroidx/paging/PagingSource;

    move-result-object v0

    return-object v0
.end method
