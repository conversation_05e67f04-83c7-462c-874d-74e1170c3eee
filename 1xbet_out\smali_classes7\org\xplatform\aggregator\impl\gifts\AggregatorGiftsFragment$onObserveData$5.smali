.class final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.AggregatorGiftsFragment$onObserveData$5"
    f = "AggregatorGiftsFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/Boolean;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "show",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lkotlin/coroutines/e;)V

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    iput-boolean p1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;->Z$0:Z

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;->invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_2

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-boolean p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;->Z$0:Z

    .line 12
    .line 13
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 14
    .line 15
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->x3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Laa1/a;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {v0}, LA4/e;->getItems()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-nez v0, :cond_0

    .line 28
    .line 29
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 30
    .line 31
    const/4 v0, 0x0

    .line 32
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->O3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Z)V

    .line 33
    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_0
    if-eqz p1, :cond_1

    .line 37
    .line 38
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 39
    .line 40
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->z3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)LS91/G;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    iget-object p1, p1, LS91/G;->f:Lorg/xbet/ui_common/viewcomponents/layouts/linear/ShimmerLinearLayout;

    .line 45
    .line 46
    sget v0, Lu91/c;->shimmer_gift_item:I

    .line 47
    .line 48
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/viewcomponents/layouts/linear/ShimmerLinearLayout;->setShimmerItems(I)V

    .line 49
    .line 50
    .line 51
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 52
    .line 53
    const/4 v0, 0x1

    .line 54
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->O3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Z)V

    .line 55
    .line 56
    .line 57
    goto :goto_0

    .line 58
    :cond_1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 59
    .line 60
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->y3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lorg/xplatform/aggregator/impl/gifts/f;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/f;->k()V

    .line 65
    .line 66
    .line 67
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 68
    .line 69
    return-object p1

    .line 70
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 71
    .line 72
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 73
    .line 74
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 75
    .line 76
    .line 77
    throw p1
.end method
