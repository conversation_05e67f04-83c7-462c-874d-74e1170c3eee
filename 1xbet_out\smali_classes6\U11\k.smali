.class public final synthetic LU11/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatorBonuses/HeaderStyleBonus;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorBonuses/HeaderStyleBonus;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LU11/k;->a:Lorg/xbet/uikit_aggregator/aggregatorBonuses/HeaderStyleBonus;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LU11/k;->a:Lorg/xbet/uikit_aggregator/aggregatorBonuses/HeaderStyleBonus;

    invoke-static {v0}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/HeaderStyleBonus;->d(Lorg/xbet/uikit_aggregator/aggregatorBonuses/HeaderStyleBonus;)Lorg/xbet/uikit/utils/z;

    move-result-object v0

    return-object v0
.end method
