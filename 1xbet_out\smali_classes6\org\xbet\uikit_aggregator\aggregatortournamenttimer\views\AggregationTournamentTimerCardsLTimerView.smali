.class public final Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;
.super Landroid/view/View;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;,
        Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$b;,
        Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;,
        Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$c;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0094\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0010\t\n\u0002\u0008\t\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0010\u0008\u0001\u0018\u0000 c2\u00020\u0001:\u0003>:dB\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\r\u001a\u00020\u000c2\u0006\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u0011\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000fH\u0014\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0015\u0010\u0015\u001a\u00020\u000c2\u0006\u0010\u0014\u001a\u00020\u0013\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J)\u0010\u001c\u001a\u00020\u000c2\u000c\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00180\u00172\u000c\u0010\u001b\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u001a\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\r\u0010\u001e\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u001b\u0010!\u001a\u00020\u000c2\u000c\u0010 \u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u001a\u00a2\u0006\u0004\u0008!\u0010\"J\u0017\u0010%\u001a\u00020\u000c2\u0006\u0010$\u001a\u00020#H\u0002\u00a2\u0006\u0004\u0008%\u0010&J\u0017\u0010(\u001a\u00020\u000c2\u0006\u0010\'\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008(\u0010)J\u0017\u0010*\u001a\u00020\u000c2\u0006\u0010\'\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008*\u0010)J\u0017\u0010+\u001a\u00020\u00182\u0006\u0010\'\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008+\u0010,J\'\u00101\u001a\u00020-2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010.\u001a\u00020-2\u0006\u00100\u001a\u00020/H\u0002\u00a2\u0006\u0004\u00081\u00102J/\u00106\u001a\u00020-2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010.\u001a\u00020-2\u0006\u00103\u001a\u00020/2\u0006\u00105\u001a\u000204H\u0002\u00a2\u0006\u0004\u00086\u00107J\u0017\u00108\u001a\u00020\u000c2\u0006\u0010$\u001a\u00020#H\u0002\u00a2\u0006\u0004\u00088\u0010&R\u0014\u0010<\u001a\u0002098\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u0010;R\u001b\u0010B\u001a\u00020=8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008>\u0010?\u001a\u0004\u0008@\u0010AR\u0014\u0010F\u001a\u00020C8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010ER\u0014\u0010H\u001a\u00020C8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010ER\u0014\u0010J\u001a\u00020C8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008I\u0010ER\u0014\u0010L\u001a\u0002048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010KR\u0014\u0010N\u001a\u0002048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008M\u0010KR\u0014\u0010Q\u001a\u00020O8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010PR\u0014\u0010T\u001a\u00020R8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00081\u0010SR\u0014\u0010W\u001a\u00020U8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u0010VR\u0016\u0010Y\u001a\u00020/8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008%\u0010XR\u0016\u0010[\u001a\u00020/8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008Z\u0010XR\u0016\u0010\\\u001a\u00020/8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008(\u0010XR\u0016\u0010]\u001a\u00020/8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010XR\u0016\u0010^\u001a\u00020/8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008+\u0010XR\u0016\u0010`\u001a\u00020/8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008_\u0010XR\u0016\u0010b\u001a\u00020#8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00088\u0010a\u00a8\u0006e"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;",
        "Landroid/view/View;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "Le31/c;",
        "model",
        "setModel",
        "(Le31/c;)V",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "stopTimerFlow",
        "Lkotlin/Function0;",
        "timeOutCallback",
        "n",
        "(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;)V",
        "f",
        "()V",
        "callback",
        "setOnTimerExpiredListener",
        "(Lkotlin/jvm/functions/Function0;)V",
        "",
        "millisUntilFinished",
        "k",
        "(J)V",
        "parentWidth",
        "m",
        "(I)V",
        "h",
        "o",
        "(I)Z",
        "",
        "startX",
        "",
        "time",
        "i",
        "(Landroid/graphics/Canvas;FLjava/lang/String;)F",
        "separator",
        "Landroid/graphics/Paint;",
        "paint",
        "j",
        "(Landroid/graphics/Canvas;FLjava/lang/String;Landroid/graphics/Paint;)F",
        "q",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;",
        "a",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;",
        "viewStyle",
        "Lkotlinx/coroutines/N;",
        "b",
        "Lkotlin/j;",
        "getScope",
        "()Lkotlinx/coroutines/N;",
        "scope",
        "Landroid/text/TextPaint;",
        "c",
        "Landroid/text/TextPaint;",
        "dayTitlePaint",
        "d",
        "separatorPaint",
        "e",
        "timeTextPaint",
        "Landroid/graphics/Paint;",
        "timeCellBackgroundPaint",
        "g",
        "linePaint",
        "Lorg/xbet/uikit/utils/timer/FlowTimer;",
        "Lorg/xbet/uikit/utils/timer/FlowTimer;",
        "timer",
        "Landroid/graphics/Rect;",
        "Landroid/graphics/Rect;",
        "timeCharacterBounds",
        "Landroid/graphics/RectF;",
        "Landroid/graphics/RectF;",
        "timeCellBackgroundBound",
        "Ljava/lang/String;",
        "daysTitle",
        "l",
        "visibleDaysTitle",
        "daysLeft",
        "hoursLeft",
        "minutesLeft",
        "p",
        "secondsLeft",
        "J",
        "timeExpiredMillis",
        "r",
        "TimeSize",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final r:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final s:I


# instance fields
.field public final a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lorg/xbet/uikit/utils/timer/FlowTimer;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Landroid/graphics/Rect;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Landroid/graphics/RectF;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public k:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public l:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public m:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public n:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public o:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public p:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public q:J


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->r:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$b;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->s:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 8
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/view/View;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance p2, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    invoke-direct {p2, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;-><init>(Landroid/content/Context;)V

    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 6
    new-instance p1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/a;

    invoke-direct {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/a;-><init>()V

    .line 7
    sget-object p3, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    invoke-static {p3, p1}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    .line 8
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->b:Lkotlin/j;

    .line 9
    sget v1, LlZ0/n;->TextStyle_Title_Medium_M:I

    sget v2, LlZ0/d;->uikitSecondary:I

    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lf31/a;->f(Landroid/view/View;IILandroid/graphics/Paint$Style;ILjava/lang/Object;)Landroid/text/TextPaint;

    move-result-object p1

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->c:Landroid/text/TextPaint;

    .line 10
    sget v1, LlZ0/n;->TextStyle_Headline_Bold_Secondary:I

    sget v2, LlZ0/d;->uikitSecondary:I

    invoke-static/range {v0 .. v5}, Lf31/a;->f(Landroid/view/View;IILandroid/graphics/Paint$Style;ILjava/lang/Object;)Landroid/text/TextPaint;

    move-result-object p1

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->d:Landroid/text/TextPaint;

    .line 11
    sget v1, LlZ0/n;->TextStyle_Title_Medium_XL_Secondary:I

    sget v2, LlZ0/d;->uikitSecondary:I

    invoke-static/range {v0 .. v5}, Lf31/a;->f(Landroid/view/View;IILandroid/graphics/Paint$Style;ILjava/lang/Object;)Landroid/text/TextPaint;

    move-result-object p1

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->e:Landroid/text/TextPaint;

    .line 12
    sget p1, LlZ0/d;->uikitBackgroundGroup:I

    const/4 p3, 0x0

    const/4 v1, 0x2

    invoke-static {p0, p1, p3, v1, p3}, Lf31/a;->d(Landroid/view/View;ILandroid/graphics/Paint$Style;ILjava/lang/Object;)Landroid/graphics/Paint;

    move-result-object p1

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->f:Landroid/graphics/Paint;

    .line 13
    sget v1, LlZ0/d;->uikitBackground:I

    const/4 v4, 0x6

    const/4 v2, 0x0

    const/4 v3, 0x0

    invoke-static/range {v0 .. v5}, Lf31/a;->b(Landroid/view/View;ILandroid/graphics/Paint$Style;IILjava/lang/Object;)Landroid/graphics/Paint;

    move-result-object p1

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->g:Landroid/graphics/Paint;

    .line 14
    new-instance v1, Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 15
    new-instance v5, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/b;

    invoke-direct {v5, p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/b;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;)V

    const/4 v6, 0x1

    const/4 v7, 0x0

    const-wide/16 v2, 0x0

    const/4 v4, 0x0

    .line 16
    invoke-direct/range {v1 .. v7}, Lorg/xbet/uikit/utils/timer/FlowTimer;-><init>(JZLkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->h:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 17
    new-instance p1, Landroid/graphics/Rect;

    invoke-direct {p1}, Landroid/graphics/Rect;-><init>()V

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->i:Landroid/graphics/Rect;

    .line 18
    new-instance p1, Landroid/graphics/RectF;

    .line 19
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->l()I

    move-result p3

    int-to-float p3, p3

    .line 20
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->j()I

    move-result p2

    int-to-float p2, p2

    const/4 v1, 0x0

    .line 21
    invoke-direct {p1, v1, v1, p3, p2}, Landroid/graphics/RectF;-><init>(FFFF)V

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->j:Landroid/graphics/RectF;

    .line 22
    const-string p1, ""

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->k:Ljava/lang/String;

    .line 23
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->l:Ljava/lang/String;

    .line 24
    const-string p1, "00"

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->m:Ljava/lang/String;

    .line 25
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->n:Ljava/lang/String;

    .line 26
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->o:Ljava/lang/String;

    .line 27
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->p:Ljava/lang/String;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic a(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;J)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->p(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;J)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b()Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->g()Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic c()Lkotlinx/coroutines/N;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->l()Lkotlinx/coroutines/N;

    move-result-object v0

    return-object v0
.end method

.method public static final synthetic d(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;)Lorg/xbet/uikit/utils/timer/FlowTimer;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->h:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic e(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;J)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->k(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final g()Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object v0
.end method

.method private final getScope()Lkotlinx/coroutines/N;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->b:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lkotlinx/coroutines/N;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final l()Lkotlinx/coroutines/N;
    .locals 1

    .line 1
    invoke-static {}, Lkotlinx/coroutines/b0;->c()Lkotlinx/coroutines/E0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lkotlinx/coroutines/E0;->getImmediate()Lkotlinx/coroutines/E0;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-static {v0}, Lkotlinx/coroutines/O;->a(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method public static final p(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;J)Lkotlin/Unit;
    .locals 6

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->getScope()Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v3, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$timer$1$1;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-direct {v3, p0, p1, p2, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$timer$1$1;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;JLkotlin/coroutines/e;)V

    .line 9
    .line 10
    .line 11
    const/4 v4, 0x3

    .line 12
    const/4 v5, 0x0

    .line 13
    const/4 v2, 0x0

    .line 14
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 15
    .line 16
    .line 17
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 18
    .line 19
    return-object p0
.end method


# virtual methods
.method public final f()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->h:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/timer/FlowTimer;->o()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->h:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 7
    .line 8
    new-instance v1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/c;

    .line 9
    .line 10
    invoke-direct {v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/c;-><init>()V

    .line 11
    .line 12
    .line 13
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/utils/timer/FlowTimer;->l(Lkotlin/jvm/functions/Function0;)V

    .line 14
    .line 15
    .line 16
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->getScope()Lkotlinx/coroutines/N;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    invoke-interface {v0}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    const/4 v1, 0x0

    .line 25
    const/4 v2, 0x1

    .line 26
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/z0;->j(Lkotlin/coroutines/CoroutineContext;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final h(I)V
    .locals 4

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->o(I)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->k:Ljava/lang/String;

    .line 8
    .line 9
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->l:Ljava/lang/String;

    .line 10
    .line 11
    return-void

    .line 12
    :cond_0
    int-to-float p1, p1

    .line 13
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->c:Landroid/text/TextPaint;

    .line 14
    .line 15
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->k:Ljava/lang/String;

    .line 16
    .line 17
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    sub-float/2addr p1, v0

    .line 22
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 23
    .line 24
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->e()I

    .line 25
    .line 26
    .line 27
    move-result v0

    .line 28
    int-to-float v0, v0

    .line 29
    invoke-static {p1, v0}, Ljava/lang/Math;->max(FF)F

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->k:Ljava/lang/String;

    .line 34
    .line 35
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 36
    .line 37
    .line 38
    move-result v0

    .line 39
    :goto_0
    const/4 v1, 0x0

    .line 40
    if-lez v0, :cond_1

    .line 41
    .line 42
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->c:Landroid/text/TextPaint;

    .line 43
    .line 44
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->k:Ljava/lang/String;

    .line 45
    .line 46
    invoke-virtual {v3, v1, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 51
    .line 52
    .line 53
    move-result v2

    .line 54
    cmpl-float v2, v2, p1

    .line 55
    .line 56
    if-lez v2, :cond_1

    .line 57
    .line 58
    add-int/lit8 v0, v0, -0x1

    .line 59
    .line 60
    goto :goto_0

    .line 61
    :cond_1
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->k:Ljava/lang/String;

    .line 62
    .line 63
    invoke-virtual {p1, v1, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->l:Ljava/lang/String;

    .line 68
    .line 69
    return-void
.end method

.method public final i(Landroid/graphics/Canvas;FLjava/lang/String;)F
    .locals 13

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->j:Landroid/graphics/RectF;

    .line 2
    .line 3
    iget v2, v1, Landroid/graphics/RectF;->top:F

    .line 4
    .line 5
    move v3, p2

    .line 6
    invoke-virtual {v1, p2, v2}, Landroid/graphics/RectF;->offsetTo(FF)V

    .line 7
    .line 8
    .line 9
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 10
    .line 11
    invoke-virtual {v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->l()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    int-to-float v1, v1

    .line 16
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 17
    .line 18
    invoke-virtual {v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->k()F

    .line 19
    .line 20
    .line 21
    move-result v2

    .line 22
    add-float v6, v1, v2

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v8, 0x0

    .line 26
    const/4 v9, 0x0

    .line 27
    :goto_0
    invoke-interface/range {p3 .. p3}, Ljava/lang/CharSequence;->length()I

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    if-ge v8, v1, :cond_1

    .line 32
    .line 33
    move-object/from16 v10, p3

    .line 34
    .line 35
    invoke-interface {v10, v8}, Ljava/lang/CharSequence;->charAt(I)C

    .line 36
    .line 37
    .line 38
    move-result v1

    .line 39
    add-int/lit8 v11, v9, 0x1

    .line 40
    .line 41
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->j:Landroid/graphics/RectF;

    .line 42
    .line 43
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 44
    .line 45
    invoke-virtual {v3}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->i()F

    .line 46
    .line 47
    .line 48
    move-result v3

    .line 49
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 50
    .line 51
    invoke-virtual {v4}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->i()F

    .line 52
    .line 53
    .line 54
    move-result v4

    .line 55
    iget-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->f:Landroid/graphics/Paint;

    .line 56
    .line 57
    invoke-virtual {p1, v2, v3, v4, v5}, Landroid/graphics/Canvas;->drawRoundRect(Landroid/graphics/RectF;FFLandroid/graphics/Paint;)V

    .line 58
    .line 59
    .line 60
    invoke-static {v1}, Ljava/lang/String;->valueOf(C)Ljava/lang/String;

    .line 61
    .line 62
    .line 63
    move-result-object v1

    .line 64
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->e:Landroid/text/TextPaint;

    .line 65
    .line 66
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->i:Landroid/graphics/Rect;

    .line 67
    .line 68
    const/4 v12, 0x1

    .line 69
    invoke-virtual {v2, v1, v7, v12, v3}, Landroid/graphics/Paint;->getTextBounds(Ljava/lang/String;IILandroid/graphics/Rect;)V

    .line 70
    .line 71
    .line 72
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->i:Landroid/graphics/Rect;

    .line 73
    .line 74
    invoke-virtual {v2}, Landroid/graphics/Rect;->width()I

    .line 75
    .line 76
    .line 77
    move-result v2

    .line 78
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->i:Landroid/graphics/Rect;

    .line 79
    .line 80
    invoke-virtual {v3}, Landroid/graphics/Rect;->height()I

    .line 81
    .line 82
    .line 83
    move-result v3

    .line 84
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->j:Landroid/graphics/RectF;

    .line 85
    .line 86
    invoke-virtual {v4}, Landroid/graphics/RectF;->centerX()F

    .line 87
    .line 88
    .line 89
    move-result v4

    .line 90
    int-to-float v2, v2

    .line 91
    const/high16 v5, 0x40000000    # 2.0f

    .line 92
    .line 93
    div-float/2addr v2, v5

    .line 94
    sub-float/2addr v4, v2

    .line 95
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->j:Landroid/graphics/RectF;

    .line 96
    .line 97
    invoke-virtual {v2}, Landroid/graphics/RectF;->centerY()F

    .line 98
    .line 99
    .line 100
    move-result v2

    .line 101
    int-to-float v3, v3

    .line 102
    div-float/2addr v3, v5

    .line 103
    add-float/2addr v2, v3

    .line 104
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->e:Landroid/text/TextPaint;

    .line 105
    .line 106
    invoke-virtual {p1, v1, v4, v2, v3}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    .line 107
    .line 108
    .line 109
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->j:Landroid/graphics/RectF;

    .line 110
    .line 111
    move-object v2, v1

    .line 112
    iget v1, v2, Landroid/graphics/RectF;->left:F

    .line 113
    .line 114
    invoke-virtual {v2}, Landroid/graphics/RectF;->centerY()F

    .line 115
    .line 116
    .line 117
    move-result v2

    .line 118
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->j:Landroid/graphics/RectF;

    .line 119
    .line 120
    move-object v4, v3

    .line 121
    iget v3, v4, Landroid/graphics/RectF;->right:F

    .line 122
    .line 123
    invoke-virtual {v4}, Landroid/graphics/RectF;->centerY()F

    .line 124
    .line 125
    .line 126
    move-result v4

    .line 127
    iget-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->g:Landroid/graphics/Paint;

    .line 128
    .line 129
    move-object v0, p1

    .line 130
    invoke-virtual/range {v0 .. v5}, Landroid/graphics/Canvas;->drawLine(FFFFLandroid/graphics/Paint;)V

    .line 131
    .line 132
    .line 133
    invoke-virtual {v10}, Ljava/lang/String;->length()I

    .line 134
    .line 135
    .line 136
    move-result v0

    .line 137
    sub-int/2addr v0, v12

    .line 138
    if-ge v9, v0, :cond_0

    .line 139
    .line 140
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->j:Landroid/graphics/RectF;

    .line 141
    .line 142
    const/4 v1, 0x0

    .line 143
    invoke-virtual {v0, v6, v1}, Landroid/graphics/RectF;->offset(FF)V

    .line 144
    .line 145
    .line 146
    :cond_0
    add-int/lit8 v8, v8, 0x1

    .line 147
    .line 148
    move v9, v11

    .line 149
    goto :goto_0

    .line 150
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->j:Landroid/graphics/RectF;

    .line 151
    .line 152
    iget v0, v0, Landroid/graphics/RectF;->right:F

    .line 153
    .line 154
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 155
    .line 156
    invoke-virtual {v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->m()I

    .line 157
    .line 158
    .line 159
    move-result v1

    .line 160
    int-to-float v1, v1

    .line 161
    add-float/2addr v0, v1

    .line 162
    return v0
.end method

.method public final j(Landroid/graphics/Canvas;FLjava/lang/String;Landroid/graphics/Paint;)F
    .locals 9

    .line 1
    invoke-virtual {p3}, Ljava/lang/String;->length()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->i:Landroid/graphics/Rect;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    invoke-virtual {p4, p3, v2, v0, v1}, Landroid/graphics/Paint;->getTextBounds(Ljava/lang/String;IILandroid/graphics/Rect;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->i:Landroid/graphics/Rect;

    .line 12
    .line 13
    invoke-virtual {v0}, Landroid/graphics/Rect;->height()I

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->j:Landroid/graphics/RectF;

    .line 18
    .line 19
    invoke-virtual {v1}, Landroid/graphics/RectF;->centerY()F

    .line 20
    .line 21
    .line 22
    move-result v1

    .line 23
    int-to-float v0, v0

    .line 24
    const/high16 v2, 0x40000000    # 2.0f

    .line 25
    .line 26
    div-float/2addr v0, v2

    .line 27
    add-float v7, v1, v0

    .line 28
    .line 29
    const/4 v4, 0x0

    .line 30
    invoke-virtual {p3}, Ljava/lang/String;->length()I

    .line 31
    .line 32
    .line 33
    move-result v5

    .line 34
    move-object v2, p1

    .line 35
    move v6, p2

    .line 36
    move-object v3, p3

    .line 37
    move-object v8, p4

    .line 38
    invoke-virtual/range {v2 .. v8}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;IIFFLandroid/graphics/Paint;)V

    .line 39
    .line 40
    .line 41
    invoke-virtual {v8, v3}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 42
    .line 43
    .line 44
    move-result p1

    .line 45
    add-float p2, v6, p1

    .line 46
    .line 47
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 48
    .line 49
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->m()I

    .line 50
    .line 51
    .line 52
    move-result p1

    .line 53
    int-to-float p1, p1

    .line 54
    add-float/2addr p2, p1

    .line 55
    return p2
.end method

.method public final k(J)V
    .locals 2

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->q(J)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 5
    .line 6
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->n()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->m:Ljava/lang/String;

    .line 13
    .line 14
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    const/4 v1, 0x2

    .line 19
    if-gt v0, v1, :cond_0

    .line 20
    .line 21
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;->SHORT:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;->LONG:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;

    .line 25
    .line 26
    :goto_0
    invoke-virtual {p2, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->p(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;)V

    .line 27
    .line 28
    .line 29
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 30
    .line 31
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->n()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;

    .line 32
    .line 33
    .line 34
    move-result-object p2

    .line 35
    if-ne p1, p2, :cond_1

    .line 36
    .line 37
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 38
    .line 39
    .line 40
    return-void

    .line 41
    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method public final m(I)V
    .locals 4

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->o(I)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->c:Landroid/text/TextPaint;

    .line 9
    .line 10
    invoke-virtual {v0}, Landroid/graphics/Paint;->getTextSize()F

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 15
    .line 16
    invoke-virtual {v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->f()F

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    cmpg-float v0, v0, v1

    .line 21
    .line 22
    if-gtz v0, :cond_1

    .line 23
    .line 24
    :goto_0
    return-void

    .line 25
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->c:Landroid/text/TextPaint;

    .line 26
    .line 27
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 28
    .line 29
    invoke-virtual {v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->f()F

    .line 30
    .line 31
    .line 32
    move-result v1

    .line 33
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->c:Landroid/text/TextPaint;

    .line 34
    .line 35
    invoke-virtual {v2}, Landroid/graphics/Paint;->getTextSize()F

    .line 36
    .line 37
    .line 38
    move-result v2

    .line 39
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 40
    .line 41
    invoke-virtual {v3}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->b()F

    .line 42
    .line 43
    .line 44
    move-result v3

    .line 45
    sub-float/2addr v2, v3

    .line 46
    invoke-static {v1, v2}, Ljava/lang/Math;->max(FF)F

    .line 47
    .line 48
    .line 49
    move-result v1

    .line 50
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 51
    .line 52
    .line 53
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->m(I)V

    .line 54
    .line 55
    .line 56
    return-void
.end method

.method public final n(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;)V
    .locals 5
    .param p1    # Lkotlinx/coroutines/flow/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->h:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/timer/FlowTimer;->o()V

    .line 4
    .line 5
    .line 6
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->q:J

    .line 7
    .line 8
    const-wide/16 v2, 0x3e8

    .line 9
    .line 10
    cmp-long v4, v0, v2

    .line 11
    .line 12
    if-lez v4, :cond_0

    .line 13
    .line 14
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->h:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 15
    .line 16
    invoke-virtual {v2, v0, v1}, Lorg/xbet/uikit/utils/timer/FlowTimer;->m(J)V

    .line 17
    .line 18
    .line 19
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->q:J

    .line 20
    .line 21
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->k(J)V

    .line 22
    .line 23
    .line 24
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->h:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 25
    .line 26
    invoke-virtual {v0, p2}, Lorg/xbet/uikit/utils/timer/FlowTimer;->l(Lkotlin/jvm/functions/Function0;)V

    .line 27
    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_0
    const-string p2, "00"

    .line 31
    .line 32
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->m:Ljava/lang/String;

    .line 33
    .line 34
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->n:Ljava/lang/String;

    .line 35
    .line 36
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->o:Ljava/lang/String;

    .line 37
    .line 38
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->p:Ljava/lang/String;

    .line 39
    .line 40
    :goto_0
    new-instance p2, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$startTimer$1;

    .line 41
    .line 42
    const/4 v0, 0x0

    .line 43
    invoke-direct {p2, p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$startTimer$1;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;Lkotlin/coroutines/e;)V

    .line 44
    .line 45
    .line 46
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->getScope()Lkotlinx/coroutines/N;

    .line 51
    .line 52
    .line 53
    move-result-object p2

    .line 54
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 55
    .line 56
    .line 57
    return-void
.end method

.method public final o(I)Z
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->c:Landroid/text/TextPaint;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->k:Ljava/lang/String;

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    int-to-float p1, p1

    .line 10
    cmpg-float p1, v0, p1

    .line 11
    .line 12
    if-gtz p1, :cond_0

    .line 13
    .line 14
    const/4 p1, 0x1

    .line 15
    return p1

    .line 16
    :cond_0
    const/4 p1, 0x0

    .line 17
    return p1
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 3
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    int-to-float v0, v0

    .line 6
    const/high16 v1, 0x40000000    # 2.0f

    .line 7
    .line 8
    div-float/2addr v0, v1

    .line 9
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 10
    .line 11
    invoke-virtual {v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->a()I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    int-to-float v2, v2

    .line 16
    div-float/2addr v2, v1

    .line 17
    sub-float/2addr v0, v2

    .line 18
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->m:Ljava/lang/String;

    .line 19
    .line 20
    invoke-virtual {p0, p1, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->i(Landroid/graphics/Canvas;FLjava/lang/String;)F

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->l:Ljava/lang/String;

    .line 25
    .line 26
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->c:Landroid/text/TextPaint;

    .line 27
    .line 28
    invoke-virtual {p0, p1, v0, v1, v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->j(Landroid/graphics/Canvas;FLjava/lang/String;Landroid/graphics/Paint;)F

    .line 29
    .line 30
    .line 31
    move-result v0

    .line 32
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->n:Ljava/lang/String;

    .line 33
    .line 34
    invoke-virtual {p0, p1, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->i(Landroid/graphics/Canvas;FLjava/lang/String;)F

    .line 35
    .line 36
    .line 37
    move-result v0

    .line 38
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->d:Landroid/text/TextPaint;

    .line 39
    .line 40
    const-string v2, ":"

    .line 41
    .line 42
    invoke-virtual {p0, p1, v0, v2, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->j(Landroid/graphics/Canvas;FLjava/lang/String;Landroid/graphics/Paint;)F

    .line 43
    .line 44
    .line 45
    move-result v0

    .line 46
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->o:Ljava/lang/String;

    .line 47
    .line 48
    invoke-virtual {p0, p1, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->i(Landroid/graphics/Canvas;FLjava/lang/String;)F

    .line 49
    .line 50
    .line 51
    move-result v0

    .line 52
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->d:Landroid/text/TextPaint;

    .line 53
    .line 54
    invoke-virtual {p0, p1, v0, v2, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->j(Landroid/graphics/Canvas;FLjava/lang/String;Landroid/graphics/Paint;)F

    .line 55
    .line 56
    .line 57
    move-result v0

    .line 58
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->p:Ljava/lang/String;

    .line 59
    .line 60
    invoke-virtual {p0, p1, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->i(Landroid/graphics/Canvas;FLjava/lang/String;)F

    .line 61
    .line 62
    .line 63
    return-void
.end method

.method public onMeasure(II)V
    .locals 3

    .line 1
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 2
    .line 3
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->j()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->c:Landroid/text/TextPaint;

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 10
    .line 11
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->d()F

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    invoke-virtual {p2, v0}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 16
    .line 17
    .line 18
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->d:Landroid/text/TextPaint;

    .line 19
    .line 20
    const-string v0, ":"

    .line 21
    .line 22
    invoke-virtual {p2, v0}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 23
    .line 24
    .line 25
    move-result p2

    .line 26
    float-to-int p2, p2

    .line 27
    mul-int/lit8 p2, p2, 0x2

    .line 28
    .line 29
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 30
    .line 31
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->g()I

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->o(I)Z

    .line 36
    .line 37
    .line 38
    move-result v0

    .line 39
    if-eqz v0, :cond_0

    .line 40
    .line 41
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 42
    .line 43
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->g()I

    .line 44
    .line 45
    .line 46
    move-result v0

    .line 47
    goto :goto_0

    .line 48
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 49
    .line 50
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->e()I

    .line 51
    .line 52
    .line 53
    move-result v0

    .line 54
    :goto_0
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->m(I)V

    .line 55
    .line 56
    .line 57
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->h(I)V

    .line 58
    .line 59
    .line 60
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 61
    .line 62
    invoke-virtual {v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->n()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    sget-object v2, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;->SHORT:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;

    .line 67
    .line 68
    if-ne v1, v2, :cond_1

    .line 69
    .line 70
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 71
    .line 72
    invoke-virtual {v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->h()I

    .line 73
    .line 74
    .line 75
    move-result v1

    .line 76
    goto :goto_1

    .line 77
    :cond_1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 78
    .line 79
    invoke-virtual {v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->c()I

    .line 80
    .line 81
    .line 82
    move-result v1

    .line 83
    :goto_1
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 84
    .line 85
    invoke-virtual {v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->m()I

    .line 86
    .line 87
    .line 88
    move-result v2

    .line 89
    mul-int/lit8 v2, v2, 0x6

    .line 90
    .line 91
    add-int/2addr p2, v2

    .line 92
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 93
    .line 94
    add-int/2addr v1, v0

    .line 95
    add-int/2addr v1, p2

    .line 96
    invoke-virtual {v2, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->o(I)V

    .line 97
    .line 98
    .line 99
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 100
    .line 101
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->a()I

    .line 102
    .line 103
    .line 104
    move-result p2

    .line 105
    invoke-virtual {p0, p2, p1}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 106
    .line 107
    .line 108
    return-void
.end method

.method public final q(J)V
    .locals 8

    .line 1
    iput-wide p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->q:J

    .line 2
    .line 3
    sget-object v0, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toDays(J)J

    .line 6
    .line 7
    .line 8
    move-result-wide v1

    .line 9
    const-wide/16 v3, 0x64

    .line 10
    .line 11
    const/4 v5, 0x2

    .line 12
    cmp-long v6, v1, v3

    .line 13
    .line 14
    if-ltz v6, :cond_0

    .line 15
    .line 16
    const/4 v3, 0x3

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 v3, 0x2

    .line 19
    :goto_0
    invoke-static {v1, v2}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    const/16 v2, 0x30

    .line 24
    .line 25
    invoke-static {v1, v3, v2}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->m:Ljava/lang/String;

    .line 30
    .line 31
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toHours(J)J

    .line 32
    .line 33
    .line 34
    move-result-wide v3

    .line 35
    const/16 v1, 0x18

    .line 36
    .line 37
    int-to-long v6, v1

    .line 38
    rem-long/2addr v3, v6

    .line 39
    invoke-static {v3, v4}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    invoke-static {v1, v5, v2}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->n:Ljava/lang/String;

    .line 48
    .line 49
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toMinutes(J)J

    .line 50
    .line 51
    .line 52
    move-result-wide v3

    .line 53
    const/16 v1, 0x3c

    .line 54
    .line 55
    int-to-long v6, v1

    .line 56
    rem-long/2addr v3, v6

    .line 57
    invoke-static {v3, v4}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    invoke-static {v1, v5, v2}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->o:Ljava/lang/String;

    .line 66
    .line 67
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toSeconds(J)J

    .line 68
    .line 69
    .line 70
    move-result-wide p1

    .line 71
    rem-long/2addr p1, v6

    .line 72
    invoke-static {p1, p2}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    invoke-static {p1, v5, v2}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->p:Ljava/lang/String;

    .line 81
    .line 82
    return-void
.end method

.method public final setModel(Le31/c;)V
    .locals 5
    .param p1    # Le31/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Le31/c;->b()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p1}, Le31/c;->b()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    invoke-static {v0, v1}, LF0/b;->getString(Landroid/content/Context;I)Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->k:Ljava/lang/String;

    .line 20
    .line 21
    :cond_0
    invoke-virtual {p1}, Le31/c;->e()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerMode;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$c;->a:[I

    .line 26
    .line 27
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 28
    .line 29
    .line 30
    move-result v0

    .line 31
    aget v0, v1, v0

    .line 32
    .line 33
    const/4 v1, 0x1

    .line 34
    const/4 v2, 0x2

    .line 35
    if-eq v0, v1, :cond_2

    .line 36
    .line 37
    if-ne v0, v2, :cond_1

    .line 38
    .line 39
    invoke-virtual {p1}, Le31/c;->a()Ljava/util/Date;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    invoke-virtual {p1}, Ljava/util/Date;->getTime()J

    .line 44
    .line 45
    .line 46
    move-result-wide v0

    .line 47
    goto :goto_0

    .line 48
    :cond_1
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 49
    .line 50
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 51
    .line 52
    .line 53
    throw p1

    .line 54
    :cond_2
    invoke-virtual {p1}, Le31/c;->a()Ljava/util/Date;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    invoke-virtual {p1}, Ljava/util/Date;->getTime()J

    .line 59
    .line 60
    .line 61
    move-result-wide v0

    .line 62
    new-instance p1, Ljava/util/Date;

    .line 63
    .line 64
    invoke-direct {p1}, Ljava/util/Date;-><init>()V

    .line 65
    .line 66
    .line 67
    invoke-virtual {p1}, Ljava/util/Date;->getTime()J

    .line 68
    .line 69
    .line 70
    move-result-wide v3

    .line 71
    sub-long/2addr v0, v3

    .line 72
    :goto_0
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->q(J)V

    .line 73
    .line 74
    .line 75
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;

    .line 76
    .line 77
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->m:Ljava/lang/String;

    .line 78
    .line 79
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 80
    .line 81
    .line 82
    move-result v0

    .line 83
    if-gt v0, v2, :cond_3

    .line 84
    .line 85
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;->SHORT:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;

    .line 86
    .line 87
    goto :goto_1

    .line 88
    :cond_3
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;->LONG:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;

    .line 89
    .line 90
    :goto_1
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$a;->p(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView$TimeSize;)V

    .line 91
    .line 92
    .line 93
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 94
    .line 95
    .line 96
    return-void
.end method

.method public final setOnTimerExpiredListener(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsLTimerView;->h:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/utils/timer/FlowTimer;->l(Lkotlin/jvm/functions/Function0;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
