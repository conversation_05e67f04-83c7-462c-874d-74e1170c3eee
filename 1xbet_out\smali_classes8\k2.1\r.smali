.class public final synthetic Lk2/r;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public static a(Lk2/s;[BII)Lk2/k;
    .locals 7

    .line 1
    invoke-static {}, Lcom/google/common/collect/ImmutableList;->builder()Lcom/google/common/collect/ImmutableList$Builder;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {}, Lk2/s$b;->a()Lk2/s$b;

    .line 6
    .line 7
    .line 8
    move-result-object v5

    .line 9
    invoke-static {v0}, Lj$/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    new-instance v6, Lk2/q;

    .line 13
    .line 14
    invoke-direct {v6, v0}, Lk2/q;-><init>(Lcom/google/common/collect/ImmutableList$Builder;)V

    .line 15
    .line 16
    .line 17
    move-object v1, p0

    .line 18
    move-object v2, p1

    .line 19
    move v3, p2

    .line 20
    move v4, p3

    .line 21
    invoke-interface/range {v1 .. v6}, Lk2/s;->c([BIILk2/s$b;Lt1/l;)V

    .line 22
    .line 23
    .line 24
    new-instance p0, Lk2/g;

    .line 25
    .line 26
    invoke-virtual {v0}, Lcom/google/common/collect/ImmutableList$Builder;->n()Lcom/google/common/collect/ImmutableList;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    invoke-direct {p0, p1}, Lk2/g;-><init>(Ljava/util/List;)V

    .line 31
    .line 32
    .line 33
    return-object p0
.end method

.method public static b(Lk2/s;)V
    .locals 0

    .line 1
    return-void
.end method
