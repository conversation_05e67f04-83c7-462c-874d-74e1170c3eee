.class public final Lorg/xplatform/aggregator/impl/core/domain/usecases/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lv81/e;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0005\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006H\u0096\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\n\u00a8\u0006\u000b"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/domain/usecases/c;",
        "Lv81/e;",
        "LO91/a;",
        "aggregatorWarningRepository",
        "<init>",
        "(LO91/a;)V",
        "",
        "invoke",
        "()V",
        "a",
        "LO91/a;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LO91/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LO91/a;)V
    .locals 0
    .param p1    # LO91/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/c;->a:LO91/a;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public invoke()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/c;->a:LO91/a;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-interface {v0, v1}, LO91/a;->b(Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method
