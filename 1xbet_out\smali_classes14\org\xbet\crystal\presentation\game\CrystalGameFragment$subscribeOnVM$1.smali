.class final Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.crystal.presentation.game.CrystalGameFragment$subscribeOnVM$1"
    f = "CrystalGameFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->O2()Lkotlinx/coroutines/x0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;",
        "command",
        "",
        "<anonymous>",
        "(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/crystal/presentation/game/CrystalGameFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/crystal/presentation/game/CrystalGameFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;

    iget-object v1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;-><init>(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;->invoke(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_5

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$e;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameFragment;

    .line 20
    .line 21
    check-cast p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$e;

    .line 22
    .line 23
    invoke-virtual {p1}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$e;->b()LZx/b;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-virtual {p1}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$e;->a()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-static {v0, v1, p1}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->B2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;LZx/b;Ljava/lang/String;)V

    .line 32
    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_0
    instance-of v0, p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$d;

    .line 36
    .line 37
    if-eqz v0, :cond_1

    .line 38
    .line 39
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameFragment;

    .line 40
    .line 41
    invoke-static {v0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->A2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)LXx/a;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    iget-object v0, v0, LXx/a;->d:Lorg/xbet/crystal/presentation/views/CrystalStatusView;

    .line 46
    .line 47
    check-cast p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$d;

    .line 48
    .line 49
    invoke-virtual {p1}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$d;->a()D

    .line 50
    .line 51
    .line 52
    move-result-wide v1

    .line 53
    invoke-virtual {v0, v1, v2}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->setFinalSum(D)V

    .line 54
    .line 55
    .line 56
    goto :goto_0

    .line 57
    :cond_1
    instance-of v0, p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$b;

    .line 58
    .line 59
    if-eqz v0, :cond_2

    .line 60
    .line 61
    iget-object p1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameFragment;

    .line 62
    .line 63
    invoke-static {p1}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->D2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)V

    .line 64
    .line 65
    .line 66
    goto :goto_0

    .line 67
    :cond_2
    instance-of v0, p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$c;

    .line 68
    .line 69
    if-eqz v0, :cond_3

    .line 70
    .line 71
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameFragment;

    .line 72
    .line 73
    check-cast p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$c;

    .line 74
    .line 75
    invoke-virtual {p1}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$c;->b()LZx/b;

    .line 76
    .line 77
    .line 78
    move-result-object v1

    .line 79
    invoke-virtual {p1}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$c;->a()Z

    .line 80
    .line 81
    .line 82
    move-result p1

    .line 83
    invoke-static {v0, v1, p1}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->E2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;LZx/b;Z)V

    .line 84
    .line 85
    .line 86
    goto :goto_0

    .line 87
    :cond_3
    instance-of p1, p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$a;

    .line 88
    .line 89
    if-eqz p1, :cond_4

    .line 90
    .line 91
    iget-object p1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameFragment;

    .line 92
    .line 93
    invoke-static {p1}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->C2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)V

    .line 94
    .line 95
    .line 96
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 97
    .line 98
    return-object p1

    .line 99
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 100
    .line 101
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 102
    .line 103
    .line 104
    throw p1

    .line 105
    :cond_5
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 106
    .line 107
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 108
    .line 109
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 110
    .line 111
    .line 112
    throw p1
.end method
