.class public Lorg/spongycastle/pqc/crypto/xmss/m$b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/spongycastle/pqc/crypto/xmss/m;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "b"
.end annotation


# instance fields
.field public final a:Lorg/spongycastle/pqc/crypto/xmss/l;

.field public b:J

.field public c:[B

.field public d:[B

.field public e:[B

.field public f:[B

.field public g:Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;

.field public h:[B

.field public i:Lorg/spongycastle/pqc/crypto/xmss/q;


# direct methods
.method public constructor <init>(Lorg/spongycastle/pqc/crypto/xmss/l;)V
    .locals 2

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    const-wide/16 v0, 0x0

    .line 5
    .line 6
    iput-wide v0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->b:J

    .line 7
    .line 8
    const/4 v0, 0x0

    .line 9
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->c:[B

    .line 10
    .line 11
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->d:[B

    .line 12
    .line 13
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->e:[B

    .line 14
    .line 15
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->f:[B

    .line 16
    .line 17
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->g:Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;

    .line 18
    .line 19
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->h:[B

    .line 20
    .line 21
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->i:Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 22
    .line 23
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->a:Lorg/spongycastle/pqc/crypto/xmss/l;

    .line 24
    .line 25
    return-void
.end method

.method public static synthetic a(Lorg/spongycastle/pqc/crypto/xmss/m$b;)Lorg/spongycastle/pqc/crypto/xmss/l;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->a:Lorg/spongycastle/pqc/crypto/xmss/l;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic b(Lorg/spongycastle/pqc/crypto/xmss/m$b;)[B
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->h:[B

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic c(Lorg/spongycastle/pqc/crypto/xmss/m$b;)Lorg/spongycastle/pqc/crypto/xmss/q;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->i:Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic d(Lorg/spongycastle/pqc/crypto/xmss/m$b;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->b:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public static synthetic e(Lorg/spongycastle/pqc/crypto/xmss/m$b;)[B
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->c:[B

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic f(Lorg/spongycastle/pqc/crypto/xmss/m$b;)[B
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->d:[B

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic g(Lorg/spongycastle/pqc/crypto/xmss/m$b;)[B
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->e:[B

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic h(Lorg/spongycastle/pqc/crypto/xmss/m$b;)[B
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->f:[B

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic i(Lorg/spongycastle/pqc/crypto/xmss/m$b;)Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->g:Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public j()Lorg/spongycastle/pqc/crypto/xmss/m;
    .locals 2

    .line 1
    new-instance v0, Lorg/spongycastle/pqc/crypto/xmss/m;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, v1}, Lorg/spongycastle/pqc/crypto/xmss/m;-><init>(Lorg/spongycastle/pqc/crypto/xmss/m$b;Lorg/spongycastle/pqc/crypto/xmss/m$a;)V

    .line 5
    .line 6
    .line 7
    return-object v0
.end method

.method public k(Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;)Lorg/spongycastle/pqc/crypto/xmss/m$b;
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->g:Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;

    .line 2
    .line 3
    return-object p0
.end method

.method public l(J)Lorg/spongycastle/pqc/crypto/xmss/m$b;
    .locals 0

    .line 1
    iput-wide p1, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->b:J

    .line 2
    .line 3
    return-object p0
.end method

.method public m([B)Lorg/spongycastle/pqc/crypto/xmss/m$b;
    .locals 0

    .line 1
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/t;->c([B)[B

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->e:[B

    .line 6
    .line 7
    return-object p0
.end method

.method public n([B)Lorg/spongycastle/pqc/crypto/xmss/m$b;
    .locals 0

    .line 1
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/t;->c([B)[B

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->f:[B

    .line 6
    .line 7
    return-object p0
.end method

.method public o([B)Lorg/spongycastle/pqc/crypto/xmss/m$b;
    .locals 0

    .line 1
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/t;->c([B)[B

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->d:[B

    .line 6
    .line 7
    return-object p0
.end method

.method public p([B)Lorg/spongycastle/pqc/crypto/xmss/m$b;
    .locals 0

    .line 1
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/t;->c([B)[B

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/m$b;->c:[B

    .line 6
    .line 7
    return-object p0
.end method
