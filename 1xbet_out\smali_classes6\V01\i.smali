.class public final LV01/i;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\"\u001d\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u00008\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0002\u0010\u0003\u001a\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0007"
    }
    d2 = {
        "Landroidx/compose/runtime/x0;",
        "Lorg/xbet/uikit/compose/color/CustomColors;",
        "a",
        "Landroidx/compose/runtime/x0;",
        "c",
        "()Landroidx/compose/runtime/x0;",
        "LocalCustomColors",
        "uikit_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:Landroidx/compose/runtime/x0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/compose/runtime/x0<",
            "Lorg/xbet/uikit/compose/color/CustomColors;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LV01/h;

    .line 2
    .line 3
    invoke-direct {v0}, LV01/h;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0}, Landroidx/compose/runtime/CompositionLocalKt;->g(Lkotlin/jvm/functions/Function0;)Landroidx/compose/runtime/x0;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    sput-object v0, LV01/i;->a:Landroidx/compose/runtime/x0;

    .line 11
    .line 12
    return-void
.end method

.method public static synthetic a()Lorg/xbet/uikit/compose/color/CustomColors;
    .locals 1

    .line 1
    invoke-static {}, LV01/i;->b()Lorg/xbet/uikit/compose/color/CustomColors;

    move-result-object v0

    return-object v0
.end method

.method public static final b()Lorg/xbet/uikit/compose/color/CustomColors;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit/compose/color/CustomColors;->INSTANCE:Lorg/xbet/uikit/compose/color/CustomColors;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final c()Landroidx/compose/runtime/x0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Landroidx/compose/runtime/x0<",
            "Lorg/xbet/uikit/compose/color/CustomColors;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, LV01/i;->a:Landroidx/compose/runtime/x0;

    .line 2
    .line 3
    return-object v0
.end method
