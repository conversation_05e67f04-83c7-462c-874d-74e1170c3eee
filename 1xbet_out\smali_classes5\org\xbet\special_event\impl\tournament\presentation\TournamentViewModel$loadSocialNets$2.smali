.class final Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.tournament.presentation.TournamentViewModel$loadSocialNets$2"
    f = "TournamentViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->Q4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;

    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_1

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 12
    .line 13
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->L3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LNo0/a;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 18
    .line 19
    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->a4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LZx0/h;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-virtual {v0}, LZx0/h;->d()I

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    invoke-interface {p1, v0}, LNo0/a;->a(I)Ljava/util/List;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    if-eqz v0, :cond_0

    .line 36
    .line 37
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 38
    .line 39
    new-instance v0, LZx0/a$m;

    .line 40
    .line 41
    sget-object v1, LZx0/e;->a:LZx0/e;

    .line 42
    .line 43
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    invoke-direct {v0, v1, v2}, LZx0/a$m;-><init>(LZx0/g;Ljava/util/List;)V

    .line 48
    .line 49
    .line 50
    invoke-static {p1, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->n4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;LZx0/a;)V

    .line 51
    .line 52
    .line 53
    goto :goto_0

    .line 54
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadSocialNets$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 55
    .line 56
    new-instance v1, LZx0/a$m;

    .line 57
    .line 58
    sget-object v2, LZx0/g$a$b;->a:LZx0/g$a$b;

    .line 59
    .line 60
    invoke-direct {v1, v2, p1}, LZx0/a$m;-><init>(LZx0/g;Ljava/util/List;)V

    .line 61
    .line 62
    .line 63
    invoke-static {v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->n4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;LZx0/a;)V

    .line 64
    .line 65
    .line 66
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 67
    .line 68
    return-object p1

    .line 69
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 70
    .line 71
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 72
    .line 73
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 74
    .line 75
    .line 76
    throw p1
.end method
