.class final Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.favorite.presentation.AggregatorFavoritesSharedViewModel$observeFavoriteGames$2"
    f = "AggregatorFavoritesSharedViewModel.kt"
    l = {
        0xe4
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->W4(ZLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/util/List<",
        "+",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\u0010\u0000\u001a\u00020\u00012\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "favorite",
        "",
        "Lorg/xplatform/aggregator/api/model/Game;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field final synthetic $loggedIn:Z

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;ZLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    iput-boolean p2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->$loggedIn:Z

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->$loggedIn:Z

    invoke-direct {v0, v1, v2, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;ZLkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->invoke(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->L$0:Ljava/lang/Object;

    .line 28
    .line 29
    check-cast p1, Ljava/util/List;

    .line 30
    .line 31
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x0

    .line 35
    invoke-static {v1, p1, v4, v3, v4}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->j5(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ljava/util/List;Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;ILjava/lang/Object;)Z

    .line 36
    .line 37
    .line 38
    move-result v1

    .line 39
    if-eqz v1, :cond_2

    .line 40
    .line 41
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 42
    .line 43
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->C4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;)V

    .line 44
    .line 45
    .line 46
    :cond_2
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 47
    .line 48
    iget-boolean v3, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->$loggedIn:Z

    .line 49
    .line 50
    sget-object v4, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;->FAVORITES:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 51
    .line 52
    iput v2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$observeFavoriteGames$2;->label:I

    .line 53
    .line 54
    invoke-static {v1, p1, v3, v4, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->G4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ljava/util/List;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    if-ne p1, v0, :cond_3

    .line 59
    .line 60
    return-object v0

    .line 61
    :cond_3
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 62
    .line 63
    return-object p1
.end method
