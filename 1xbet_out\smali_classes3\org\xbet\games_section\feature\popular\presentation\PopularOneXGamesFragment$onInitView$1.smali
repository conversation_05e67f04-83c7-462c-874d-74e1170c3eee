.class public final Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onInitView$1;
.super Landroidx/recyclerview/widget/LinearLayoutManager;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->t2(Landroid/os/Bundle;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000%\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0015\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0006*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u001f\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0007\u0010\u0008R\u001b\u0010\u000e\u001a\u00020\t8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\n\u0010\u000b\u001a\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000f"
    }
    d2 = {
        "org/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onInitView$1",
        "Landroidx/recyclerview/widget/LinearLayoutManager;",
        "Landroidx/recyclerview/widget/RecyclerView$z;",
        "state",
        "",
        "extraLayoutSpace",
        "",
        "calculateExtraLayoutSpace",
        "(Landroidx/recyclerview/widget/RecyclerView$z;[I)V",
        "",
        "a",
        "Lkotlin/j;",
        "m",
        "()I",
        "extraSpace",
        "popular_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lkotlin/j;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;Landroid/content/Context;)V
    .locals 0

    .line 1
    invoke-direct {p0, p2}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;)V

    .line 2
    .line 3
    .line 4
    new-instance p2, Lorg/xbet/games_section/feature/popular/presentation/f;

    .line 5
    .line 6
    invoke-direct {p2, p1}, Lorg/xbet/games_section/feature/popular/presentation/f;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;)V

    .line 7
    .line 8
    .line 9
    sget-object p1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 10
    .line 11
    invoke-static {p1, p2}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onInitView$1;->a:Lkotlin/j;

    .line 16
    .line 17
    return-void
.end method

.method public static synthetic k(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;)I
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onInitView$1;->l(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;)I

    move-result p0

    return p0
.end method

.method public static final l(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;)I
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/ui_common/utils/g;->a:Lorg/xbet/ui_common/utils/g;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    invoke-virtual {v0, p0}, Lorg/xbet/ui_common/utils/g;->M(Landroid/content/Context;)I

    .line 8
    .line 9
    .line 10
    move-result p0

    .line 11
    int-to-float p0, p0

    .line 12
    const v0, 0x3fcccccd

    .line 13
    .line 14
    .line 15
    mul-float p0, p0, v0

    .line 16
    .line 17
    float-to-int p0, p0

    .line 18
    return p0
.end method

.method private final m()I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onInitView$1;->a:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/lang/Number;

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    return v0
.end method


# virtual methods
.method public calculateExtraLayoutSpace(Landroidx/recyclerview/widget/RecyclerView$z;[I)V
    .locals 1

    .line 1
    array-length p1, p2

    .line 2
    const/4 v0, 0x1

    .line 3
    if-le p1, v0, :cond_0

    .line 4
    .line 5
    invoke-direct {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onInitView$1;->m()I

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    aput p1, p2, v0

    .line 10
    .line 11
    :cond_0
    return-void
.end method
