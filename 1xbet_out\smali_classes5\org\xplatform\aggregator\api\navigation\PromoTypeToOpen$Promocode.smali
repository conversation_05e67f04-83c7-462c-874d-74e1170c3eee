.class public final Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Promocode"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0007\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\u0008\u0086\u0008\u0018\u00002\u00020\u0001B\u0011\u0012\u0008\u0008\u0002\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\t\u0010\u0008\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\t\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\n\u001a\u00020\u000b2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\rH\u00d6\u0003J\t\u0010\u000e\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0011"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;",
        "Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen;",
        "promoId",
        "",
        "<init>",
        "(I)V",
        "getPromoId",
        "()I",
        "component1",
        "copy",
        "equals",
        "",
        "other",
        "",
        "hashCode",
        "toString",
        "",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field private final promoId:I


# direct methods
.method public constructor <init>()V
    .locals 3

    .line 1
    const/4 v0, 0x1

    const/4 v1, 0x0

    const/4 v2, 0x0

    invoke-direct {p0, v2, v0, v1}, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;-><init>(IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(I)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;->promoId:I

    return-void
.end method

.method public synthetic constructor <init>(IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p2, p2, 0x1

    if-eqz p2, :cond_0

    const/4 p1, 0x0

    .line 3
    :cond_0
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;-><init>(I)V

    return-void
.end method

.method public static synthetic copy$default(Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;IILjava/lang/Object;)Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;
    .locals 0

    and-int/lit8 p2, p2, 0x1

    if-eqz p2, :cond_0

    iget p1, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;->promoId:I

    :cond_0
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;->copy(I)Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final component1()I
    .locals 1

    iget v0, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;->promoId:I

    return v0
.end method

.method public final copy(I)Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;

    invoke-direct {v0, p1}, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;-><init>(I)V

    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 3

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;

    iget v1, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;->promoId:I

    iget p1, p1, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;->promoId:I

    if-eq v1, p1, :cond_2

    return v2

    :cond_2
    return v0
.end method

.method public final getPromoId()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;->promoId:I

    .line 2
    .line 3
    return v0
.end method

.method public hashCode()I
    .locals 1

    iget v0, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;->promoId:I

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget v0, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;->promoId:I

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Promocode(promoId="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
