.class final Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.tournament.presentation.TournamentViewModel$observeConnection$1"
    f = "TournamentViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->a5()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lja<PERSON>/lang/Boolean;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "isNetworkAvailable",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;

    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    iput-boolean p1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;->Z$0:Z

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;->invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_2

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-boolean p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;->Z$0:Z

    .line 12
    .line 13
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 14
    .line 15
    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->b4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lkotlinx/coroutines/flow/V;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$observeConnection$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 24
    .line 25
    check-cast v0, LZx0/k;

    .line 26
    .line 27
    if-eqz p1, :cond_0

    .line 28
    .line 29
    sget-object p1, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;->NO_ERROR:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    .line 30
    .line 31
    invoke-static {v1, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->m4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;)V

    .line 32
    .line 33
    .line 34
    invoke-static {v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->i4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 35
    .line 36
    .line 37
    goto :goto_0

    .line 38
    :cond_0
    invoke-static {v0}, LQx0/b;->h(LZx0/k;)Z

    .line 39
    .line 40
    .line 41
    move-result p1

    .line 42
    if-eqz p1, :cond_1

    .line 43
    .line 44
    invoke-static {v0}, LQx0/b;->g(LZx0/k;)Z

    .line 45
    .line 46
    .line 47
    move-result p1

    .line 48
    if-nez p1, :cond_1

    .line 49
    .line 50
    invoke-static {v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->D3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 51
    .line 52
    .line 53
    sget-object p1, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;->CONTENT_ERROR:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    .line 54
    .line 55
    invoke-static {v1, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->m4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;)V

    .line 56
    .line 57
    .line 58
    :cond_1
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 59
    .line 60
    return-object p1

    .line 61
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 62
    .line 63
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 64
    .line 65
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 66
    .line 67
    .line 68
    throw p1
.end method
