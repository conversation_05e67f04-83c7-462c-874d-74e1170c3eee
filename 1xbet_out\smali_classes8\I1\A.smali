.class public final LI1/A;
.super LI1/c;
.source "SourceFile"


# instance fields
.field public final i:I

.field public final j:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Landroidx/media3/common/G;II)V
    .locals 6

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move v3, p3

    .line 1
    invoke-direct/range {v0 .. v5}, LI1/A;-><init>(Landroidx/media3/common/G;IIILjava/lang/Object;)V

    return-void
.end method

.method public constructor <init>(Landroidx/media3/common/G;IIILjava/lang/Object;)V
    .locals 0

    .line 2
    filled-new-array {p2}, [I

    move-result-object p2

    invoke-direct {p0, p1, p2, p3}, LI1/c;-><init>(Landroidx/media3/common/G;[II)V

    .line 3
    iput p4, p0, LI1/A;->i:I

    .line 4
    iput-object p5, p0, LI1/A;->j:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public e()I
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    return v0
.end method

.method public i(JJJLjava/util/List;[LG1/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JJJ",
            "Ljava/util/List<",
            "+",
            "LG1/d;",
            ">;[",
            "LG1/e;",
            ")V"
        }
    .end annotation

    .line 1
    return-void
.end method

.method public q()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LI1/A;->j:Ljava/lang/Object;

    .line 2
    .line 3
    return-object v0
.end method

.method public s()I
    .locals 1

    .line 1
    iget v0, p0, LI1/A;->i:I

    .line 2
    .line 3
    return v0
.end method
