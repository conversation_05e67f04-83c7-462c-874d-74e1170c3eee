.class public final synthetic LhC0/m;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(LB4/a;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LhC0/m;->a:LB4/a;

    iput-object p2, p0, LhC0/m;->b:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LhC0/m;->a:LB4/a;

    iget-object v1, p0, LhC0/m;->b:L<PERSON>lin/jvm/functions/Function1;

    check-cast p1, <PERSON>ja<PERSON>/util/List;

    invoke-static {v0, v1, p1}, Lorg/xbet/sportgame/markets_settings/impl/presentation/adapters/MarketSettingDelegateKt;->a(LB4/a;Lkotlin/jvm/functions/Function1;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
