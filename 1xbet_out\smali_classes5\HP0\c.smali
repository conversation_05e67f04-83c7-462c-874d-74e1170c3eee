.class public final LHP0/c;
.super LUX0/i;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "LUX0/i<",
        "LJP0/d;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B\u000f\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0017\u0010\t\u001a\u00020\u00082\u0006\u0010\u0007\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\t\u0010\nR\u0014\u0010\u000e\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000f"
    }
    d2 = {
        "LHP0/c;",
        "LUX0/i;",
        "LJP0/d;",
        "Landroid/view/View;",
        "itemView",
        "<init>",
        "(Landroid/view/View;)V",
        "item",
        "",
        "g",
        "(LJP0/d;)V",
        "LLP0/l;",
        "f",
        "LLP0/l;",
        "viewBinding",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final f:LLP0/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/view/View;)V
    .locals 0
    .param p1    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0, p1}, LUX0/i;-><init>(Landroid/view/View;)V

    .line 2
    .line 3
    .line 4
    invoke-static {p1}, LLP0/l;->a(Landroid/view/View;)LLP0/l;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iput-object p1, p0, LHP0/c;->f:LLP0/l;

    .line 9
    .line 10
    return-void
.end method

.method public static synthetic e(LHP0/c;Landroid/graphics/Bitmap;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LHP0/c;->h(LHP0/c;Landroid/graphics/Bitmap;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(LHP0/c;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LHP0/c;->i(LHP0/c;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final h(LHP0/c;Landroid/graphics/Bitmap;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p0, LHP0/c;->f:LLP0/l;

    .line 2
    .line 3
    iget-object v0, v0, LLP0/l;->b:Landroid/widget/ImageView;

    .line 4
    .line 5
    const/16 v1, 0x8

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 8
    .line 9
    .line 10
    iget-object p0, p0, LHP0/c;->f:LLP0/l;

    .line 11
    .line 12
    iget-object p0, p0, LLP0/l;->c:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 13
    .line 14
    invoke-virtual {p0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageBitmap(Landroid/graphics/Bitmap;)V

    .line 15
    .line 16
    .line 17
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 18
    .line 19
    return-object p0
.end method

.method public static final i(LHP0/c;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, LHP0/c;->f:LLP0/l;

    .line 2
    .line 3
    iget-object p0, p0, LLP0/l;->b:Landroid/widget/ImageView;

    .line 4
    .line 5
    const/4 p1, 0x0

    .line 6
    invoke-virtual {p0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 7
    .line 8
    .line 9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    return-object p0
.end method


# virtual methods
.method public bridge synthetic d(Ljava/lang/Object;)V
    .locals 0

    .line 1
    check-cast p1, LJP0/d;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LHP0/c;->g(LJP0/d;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public g(LJP0/d;)V
    .locals 4
    .param p1    # LJP0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 2
    .line 3
    iget-object v1, p0, LHP0/c;->f:LLP0/l;

    .line 4
    .line 5
    iget-object v1, v1, LLP0/l;->c:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 6
    .line 7
    invoke-virtual {p1}, LJP0/d;->a()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    new-instance v2, LHP0/a;

    .line 12
    .line 13
    invoke-direct {v2, p0}, LHP0/a;-><init>(LHP0/c;)V

    .line 14
    .line 15
    .line 16
    new-instance v3, LHP0/b;

    .line 17
    .line 18
    invoke-direct {v3, p0}, LHP0/b;-><init>(LHP0/c;)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {v0, v1, p1, v2, v3}, LCX0/l;->q(Landroid/widget/ImageView;Ljava/lang/String;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method
