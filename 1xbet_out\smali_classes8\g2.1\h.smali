.class public final Lg2/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lg2/g;


# instance fields
.field public final a:[J

.field public final b:[J

.field public final c:J

.field public final d:J

.field public final e:I


# direct methods
.method public constructor <init>([J[JJJI)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lg2/h;->a:[J

    .line 5
    .line 6
    iput-object p2, p0, Lg2/h;->b:[J

    .line 7
    .line 8
    iput-wide p3, p0, Lg2/h;->c:J

    .line 9
    .line 10
    iput-wide p5, p0, Lg2/h;->d:J

    .line 11
    .line 12
    iput p7, p0, Lg2/h;->e:I

    .line 13
    .line 14
    return-void
.end method

.method public static a(JJLN1/I$a;Lt1/G;)Lg2/h;
    .locals 23

    .line 1
    move-wide/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v2, p4

    .line 4
    .line 5
    move-object/from16 v3, p5

    .line 6
    .line 7
    const/4 v4, 0x6

    .line 8
    invoke-virtual {v3, v4}, Lt1/G;->X(I)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {v3}, Lt1/G;->q()I

    .line 12
    .line 13
    .line 14
    move-result v4

    .line 15
    iget v5, v2, LN1/I$a;->c:I

    .line 16
    .line 17
    int-to-long v5, v5

    .line 18
    add-long v5, p2, v5

    .line 19
    .line 20
    int-to-long v7, v4

    .line 21
    add-long/2addr v5, v7

    .line 22
    invoke-virtual {v3}, Lt1/G;->q()I

    .line 23
    .line 24
    .line 25
    move-result v4

    .line 26
    const/4 v7, 0x0

    .line 27
    if-gtz v4, :cond_0

    .line 28
    .line 29
    return-object v7

    .line 30
    :cond_0
    iget v8, v2, LN1/I$a;->d:I

    .line 31
    .line 32
    int-to-long v9, v4

    .line 33
    iget v4, v2, LN1/I$a;->g:I

    .line 34
    .line 35
    int-to-long v11, v4

    .line 36
    mul-long v9, v9, v11

    .line 37
    .line 38
    const-wide/16 v11, 0x1

    .line 39
    .line 40
    sub-long/2addr v9, v11

    .line 41
    invoke-static {v9, v10, v8}, Lt1/a0;->b1(JI)J

    .line 42
    .line 43
    .line 44
    move-result-wide v14

    .line 45
    invoke-virtual {v3}, Lt1/G;->P()I

    .line 46
    .line 47
    .line 48
    move-result v4

    .line 49
    invoke-virtual {v3}, Lt1/G;->P()I

    .line 50
    .line 51
    .line 52
    move-result v8

    .line 53
    invoke-virtual {v3}, Lt1/G;->P()I

    .line 54
    .line 55
    .line 56
    move-result v9

    .line 57
    const/4 v10, 0x2

    .line 58
    invoke-virtual {v3, v10}, Lt1/G;->X(I)V

    .line 59
    .line 60
    .line 61
    iget v11, v2, LN1/I$a;->c:I

    .line 62
    .line 63
    int-to-long v11, v11

    .line 64
    add-long v11, p2, v11

    .line 65
    .line 66
    move-wide/from16 v16, v11

    .line 67
    .line 68
    new-array v12, v4, [J

    .line 69
    .line 70
    new-array v13, v4, [J

    .line 71
    .line 72
    const/4 v11, 0x0

    .line 73
    move-wide/from16 v21, v16

    .line 74
    .line 75
    move-object/from16 v16, v7

    .line 76
    .line 77
    move/from16 v17, v8

    .line 78
    .line 79
    move-wide/from16 v7, v21

    .line 80
    .line 81
    :goto_0
    if-ge v11, v4, :cond_5

    .line 82
    .line 83
    move-object/from16 v18, v12

    .line 84
    .line 85
    move-object/from16 p2, v13

    .line 86
    .line 87
    int-to-long v12, v11

    .line 88
    mul-long v12, v12, v14

    .line 89
    .line 90
    move/from16 p3, v11

    .line 91
    .line 92
    int-to-long v10, v4

    .line 93
    div-long/2addr v12, v10

    .line 94
    aput-wide v12, v18, p3

    .line 95
    .line 96
    aput-wide v7, p2, p3

    .line 97
    .line 98
    const/4 v10, 0x1

    .line 99
    if-eq v9, v10, :cond_4

    .line 100
    .line 101
    const/4 v10, 0x2

    .line 102
    if-eq v9, v10, :cond_3

    .line 103
    .line 104
    const/4 v11, 0x3

    .line 105
    if-eq v9, v11, :cond_2

    .line 106
    .line 107
    const/4 v11, 0x4

    .line 108
    if-eq v9, v11, :cond_1

    .line 109
    .line 110
    return-object v16

    .line 111
    :cond_1
    invoke-virtual {v3}, Lt1/G;->L()I

    .line 112
    .line 113
    .line 114
    move-result v11

    .line 115
    goto :goto_1

    .line 116
    :cond_2
    invoke-virtual {v3}, Lt1/G;->K()I

    .line 117
    .line 118
    .line 119
    move-result v11

    .line 120
    goto :goto_1

    .line 121
    :cond_3
    invoke-virtual {v3}, Lt1/G;->P()I

    .line 122
    .line 123
    .line 124
    move-result v11

    .line 125
    goto :goto_1

    .line 126
    :cond_4
    const/4 v10, 0x2

    .line 127
    invoke-virtual {v3}, Lt1/G;->H()I

    .line 128
    .line 129
    .line 130
    move-result v11

    .line 131
    :goto_1
    int-to-long v11, v11

    .line 132
    move-wide/from16 v19, v11

    .line 133
    .line 134
    move/from16 v13, v17

    .line 135
    .line 136
    int-to-long v10, v13

    .line 137
    mul-long v11, v19, v10

    .line 138
    .line 139
    add-long/2addr v7, v11

    .line 140
    add-int/lit8 v11, p3, 0x1

    .line 141
    .line 142
    move-object/from16 v12, v18

    .line 143
    .line 144
    const/4 v10, 0x2

    .line 145
    move-object/from16 v13, p2

    .line 146
    .line 147
    goto :goto_0

    .line 148
    :cond_5
    move-object/from16 v18, v12

    .line 149
    .line 150
    move-object/from16 p2, v13

    .line 151
    .line 152
    const-wide/16 v3, -0x1

    .line 153
    .line 154
    const-string v9, ", "

    .line 155
    .line 156
    const-string v10, "VbriSeeker"

    .line 157
    .line 158
    cmp-long v11, v0, v3

    .line 159
    .line 160
    if-eqz v11, :cond_6

    .line 161
    .line 162
    cmp-long v3, v0, v5

    .line 163
    .line 164
    if-eqz v3, :cond_6

    .line 165
    .line 166
    new-instance v3, Ljava/lang/StringBuilder;

    .line 167
    .line 168
    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 169
    .line 170
    .line 171
    const-string v4, "VBRI data size mismatch: "

    .line 172
    .line 173
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 174
    .line 175
    .line 176
    invoke-virtual {v3, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 177
    .line 178
    .line 179
    invoke-virtual {v3, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 180
    .line 181
    .line 182
    invoke-virtual {v3, v5, v6}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 183
    .line 184
    .line 185
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 186
    .line 187
    .line 188
    move-result-object v0

    .line 189
    invoke-static {v10, v0}, Lt1/r;->h(Ljava/lang/String;Ljava/lang/String;)V

    .line 190
    .line 191
    .line 192
    :cond_6
    cmp-long v0, v5, v7

    .line 193
    .line 194
    if-eqz v0, :cond_7

    .line 195
    .line 196
    new-instance v0, Ljava/lang/StringBuilder;

    .line 197
    .line 198
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 199
    .line 200
    .line 201
    const-string v1, "VBRI bytes and ToC mismatch (using max): "

    .line 202
    .line 203
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 204
    .line 205
    .line 206
    invoke-virtual {v0, v5, v6}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 207
    .line 208
    .line 209
    invoke-virtual {v0, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 210
    .line 211
    .line 212
    invoke-virtual {v0, v7, v8}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 213
    .line 214
    .line 215
    const-string v1, "\nSeeking will be inaccurate."

    .line 216
    .line 217
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 218
    .line 219
    .line 220
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 221
    .line 222
    .line 223
    move-result-object v0

    .line 224
    invoke-static {v10, v0}, Lt1/r;->h(Ljava/lang/String;Ljava/lang/String;)V

    .line 225
    .line 226
    .line 227
    invoke-static {v5, v6, v7, v8}, Ljava/lang/Math;->max(JJ)J

    .line 228
    .line 229
    .line 230
    move-result-wide v5

    .line 231
    :cond_7
    move-wide/from16 v16, v5

    .line 232
    .line 233
    new-instance v11, Lg2/h;

    .line 234
    .line 235
    iget v0, v2, LN1/I$a;->f:I

    .line 236
    .line 237
    move-object/from16 v13, p2

    .line 238
    .line 239
    move-object/from16 v12, v18

    .line 240
    .line 241
    move/from16 v18, v0

    .line 242
    .line 243
    invoke-direct/range {v11 .. v18}, Lg2/h;-><init>([J[JJJI)V

    .line 244
    .line 245
    .line 246
    return-object v11
.end method


# virtual methods
.method public c(J)LN1/M$a;
    .locals 8

    .line 1
    iget-object v0, p0, Lg2/h;->a:[J

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-static {v0, p1, p2, v1, v1}, Lt1/a0;->h([JJZZ)I

    .line 5
    .line 6
    .line 7
    move-result v0

    .line 8
    new-instance v2, LN1/N;

    .line 9
    .line 10
    iget-object v3, p0, Lg2/h;->a:[J

    .line 11
    .line 12
    aget-wide v4, v3, v0

    .line 13
    .line 14
    iget-object v3, p0, Lg2/h;->b:[J

    .line 15
    .line 16
    aget-wide v6, v3, v0

    .line 17
    .line 18
    invoke-direct {v2, v4, v5, v6, v7}, LN1/N;-><init>(JJ)V

    .line 19
    .line 20
    .line 21
    iget-wide v3, v2, LN1/N;->a:J

    .line 22
    .line 23
    cmp-long v5, v3, p1

    .line 24
    .line 25
    if-gez v5, :cond_1

    .line 26
    .line 27
    iget-object p1, p0, Lg2/h;->a:[J

    .line 28
    .line 29
    array-length p1, p1

    .line 30
    sub-int/2addr p1, v1

    .line 31
    if-ne v0, p1, :cond_0

    .line 32
    .line 33
    goto :goto_0

    .line 34
    :cond_0
    new-instance p1, LN1/N;

    .line 35
    .line 36
    iget-object p2, p0, Lg2/h;->a:[J

    .line 37
    .line 38
    add-int/2addr v0, v1

    .line 39
    aget-wide v3, p2, v0

    .line 40
    .line 41
    iget-object p2, p0, Lg2/h;->b:[J

    .line 42
    .line 43
    aget-wide v0, p2, v0

    .line 44
    .line 45
    invoke-direct {p1, v3, v4, v0, v1}, LN1/N;-><init>(JJ)V

    .line 46
    .line 47
    .line 48
    new-instance p2, LN1/M$a;

    .line 49
    .line 50
    invoke-direct {p2, v2, p1}, LN1/M$a;-><init>(LN1/N;LN1/N;)V

    .line 51
    .line 52
    .line 53
    return-object p2

    .line 54
    :cond_1
    :goto_0
    new-instance p1, LN1/M$a;

    .line 55
    .line 56
    invoke-direct {p1, v2}, LN1/M$a;-><init>(LN1/N;)V

    .line 57
    .line 58
    .line 59
    return-object p1
.end method

.method public e()Z
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    return v0
.end method

.method public g()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lg2/h;->d:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public i(J)J
    .locals 3

    .line 1
    iget-object v0, p0, Lg2/h;->a:[J

    .line 2
    .line 3
    iget-object v1, p0, Lg2/h;->b:[J

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    invoke-static {v1, p1, p2, v2, v2}, Lt1/a0;->h([JJZZ)I

    .line 7
    .line 8
    .line 9
    move-result p1

    .line 10
    aget-wide p1, v0, p1

    .line 11
    .line 12
    return-wide p1
.end method

.method public k()I
    .locals 1

    .line 1
    iget v0, p0, Lg2/h;->e:I

    .line 2
    .line 3
    return v0
.end method

.method public l()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lg2/h;->c:J

    .line 2
    .line 3
    return-wide v0
.end method
