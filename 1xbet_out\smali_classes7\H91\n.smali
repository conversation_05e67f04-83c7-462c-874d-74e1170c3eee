.class public final LH91/n;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LH91/n$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001d\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0008\u0010\u0002\u001a\u0004\u0018\u00010\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "LD91/e;",
        "Lorg/xbet/uikit_aggregator/aggregatorFilter/AggregatorFilterType;",
        "categoryFilterType",
        "Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;",
        "a",
        "(LD91/e;Lorg/xbet/uikit_aggregator/aggregatorFilter/AggregatorFilterType;)Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LD91/e;Lorg/xbet/uikit_aggregator/aggregatorFilter/AggregatorFilterType;)Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;
    .locals 9
    .param p0    # LD91/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    new-instance v2, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;

    .line 4
    .line 5
    invoke-virtual {p0}, LD91/e;->b()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v3

    .line 9
    invoke-virtual {p0}, LD91/e;->c()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v4

    .line 13
    invoke-virtual {p0}, LD91/e;->a()Z

    .line 14
    .line 15
    .line 16
    move-result v5

    .line 17
    if-nez p1, :cond_0

    .line 18
    .line 19
    const/4 p1, -0x1

    .line 20
    goto :goto_0

    .line 21
    :cond_0
    sget-object v6, LH91/n$a;->a:[I

    .line 22
    .line 23
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    aget p1, v6, p1

    .line 28
    .line 29
    :goto_0
    const-string v6, "0"

    .line 30
    .line 31
    if-eq p1, v1, :cond_3

    .line 32
    .line 33
    const/4 v7, 0x2

    .line 34
    if-eq p1, v7, :cond_3

    .line 35
    .line 36
    const/4 v7, 0x3

    .line 37
    if-eq p1, v7, :cond_1

    .line 38
    .line 39
    const/4 p0, 0x0

    .line 40
    goto :goto_3

    .line 41
    :cond_1
    sget-object p1, LCX0/l;->a:LCX0/l;

    .line 42
    .line 43
    sget-object v7, Lkotlin/jvm/internal/D;->a:Lkotlin/jvm/internal/D;

    .line 44
    .line 45
    sget-object v7, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    .line 46
    .line 47
    invoke-virtual {p0}, LD91/e;->b()Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object v8

    .line 51
    invoke-static {v8, v6}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 52
    .line 53
    .line 54
    move-result v8

    .line 55
    if-eqz v8, :cond_2

    .line 56
    .line 57
    goto :goto_1

    .line 58
    :cond_2
    invoke-virtual {p0}, LD91/e;->b()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v6

    .line 62
    :goto_1
    new-array p0, v1, [Ljava/lang/Object;

    .line 63
    .line 64
    aput-object v6, p0, v0

    .line 65
    .line 66
    invoke-static {p0, v1}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object p0

    .line 70
    const-string v0, "/static/img/android/casino/alt_design/aggregator_filter/tabs_line/%s.webp"

    .line 71
    .line 72
    invoke-static {v7, v0, p0}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object p0

    .line 76
    invoke-virtual {p1, p0}, LCX0/l;->K(Ljava/lang/String;)Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object p0

    .line 80
    goto :goto_3

    .line 81
    :cond_3
    sget-object p1, LCX0/l;->a:LCX0/l;

    .line 82
    .line 83
    sget-object v7, Lkotlin/jvm/internal/D;->a:Lkotlin/jvm/internal/D;

    .line 84
    .line 85
    sget-object v7, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    .line 86
    .line 87
    invoke-virtual {p0}, LD91/e;->b()Ljava/lang/String;

    .line 88
    .line 89
    .line 90
    move-result-object v8

    .line 91
    invoke-static {v8, v6}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 92
    .line 93
    .line 94
    move-result v8

    .line 95
    if-eqz v8, :cond_4

    .line 96
    .line 97
    goto :goto_2

    .line 98
    :cond_4
    invoke-virtual {p0}, LD91/e;->b()Ljava/lang/String;

    .line 99
    .line 100
    .line 101
    move-result-object v6

    .line 102
    :goto_2
    new-array p0, v1, [Ljava/lang/Object;

    .line 103
    .line 104
    aput-object v6, p0, v0

    .line 105
    .line 106
    invoke-static {p0, v1}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 107
    .line 108
    .line 109
    move-result-object p0

    .line 110
    const-string v0, "/static/img/android/casino/alt_design/aggregator_filter/chips_l&_tabs_filled/%s.svg"

    .line 111
    .line 112
    invoke-static {v7, v0, p0}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 113
    .line 114
    .line 115
    move-result-object p0

    .line 116
    invoke-virtual {p1, p0}, LCX0/l;->K(Ljava/lang/String;)Ljava/lang/String;

    .line 117
    .line 118
    .line 119
    move-result-object p0

    .line 120
    :goto_3
    invoke-direct {v2, v3, v4, v5, p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;-><init>(Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;)V

    .line 121
    .line 122
    .line 123
    return-object v2
.end method
