.class public final LtC0/a;
.super LkY0/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LtC0/a$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "LkY0/b<",
        "LwC0/b;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\t\u0008\u0000\u0018\u0000 \u00102\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\u001cB\'\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0008\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\t\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u000e\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u0017\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0013\u001a\u00020\u0012H\u0016\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0017\u0010\u0017\u001a\u00020\u00122\u0006\u0010\u000e\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008\u0017\u0010\u0018R\u0014\u0010\u0008\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u001aR\u0014\u0010\n\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u001b\u00a8\u0006\u001d"
    }
    d2 = {
        "LtC0/a;",
        "LkY0/b;",
        "LwC0/b;",
        "Landroidx/fragment/app/FragmentManager;",
        "fragmentManager",
        "Landroidx/lifecycle/Lifecycle;",
        "lifecycle",
        "LuB0/a;",
        "marketsFragmentFactory",
        "Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;",
        "entryPointType",
        "<init>",
        "(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;LuB0/a;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V",
        "",
        "position",
        "Landroidx/fragment/app/Fragment;",
        "p",
        "(I)Landroidx/fragment/app/Fragment;",
        "",
        "itemId",
        "",
        "o",
        "(J)Z",
        "getItemId",
        "(I)J",
        "n",
        "LuB0/a;",
        "Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final p:LtC0/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final n:LuB0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LtC0/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LtC0/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LtC0/a;->p:LtC0/a$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;LuB0/a;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V
    .locals 1
    .param p1    # Landroidx/fragment/app/FragmentManager;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroidx/lifecycle/Lifecycle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LuB0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, LtC0/a;->p:LtC0/a$a;

    .line 2
    .line 3
    invoke-direct {p0, p1, p2, v0}, LkY0/b;-><init>(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Landroidx/recyclerview/widget/i$f;)V

    .line 4
    .line 5
    .line 6
    iput-object p3, p0, LtC0/a;->n:LuB0/a;

    .line 7
    .line 8
    iput-object p4, p0, LtC0/a;->o:Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public getItemId(I)J
    .locals 2

    .line 1
    invoke-virtual {p0, p1}, LkY0/b;->I(I)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LwC0/b;

    .line 6
    .line 7
    invoke-virtual {p1}, LwC0/b;->b()J

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    return-wide v0
.end method

.method public o(J)Z
    .locals 5

    .line 1
    invoke-virtual {p0}, LkY0/b;->H()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz v1, :cond_0

    .line 11
    .line 12
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    if-eqz v1, :cond_0

    .line 17
    .line 18
    return v2

    .line 19
    :cond_0
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    :cond_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    if-eqz v1, :cond_2

    .line 28
    .line 29
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    check-cast v1, LwC0/b;

    .line 34
    .line 35
    invoke-virtual {v1}, LwC0/b;->b()J

    .line 36
    .line 37
    .line 38
    move-result-wide v3

    .line 39
    cmp-long v1, v3, p1

    .line 40
    .line 41
    if-nez v1, :cond_1

    .line 42
    .line 43
    const/4 p1, 0x1

    .line 44
    return p1

    .line 45
    :cond_2
    return v2
.end method

.method public p(I)Landroidx/fragment/app/Fragment;
    .locals 12
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LtC0/a;->n:LuB0/a;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LkY0/b;->I(I)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    check-cast p1, LwC0/b;

    .line 8
    .line 9
    instance-of v1, p1, LwC0/b$a;

    .line 10
    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    check-cast p1, LwC0/b$a;

    .line 14
    .line 15
    invoke-virtual {p1}, LwC0/b$a;->f()J

    .line 16
    .line 17
    .line 18
    move-result-wide v2

    .line 19
    invoke-virtual {p1}, LwC0/b$a;->d()J

    .line 20
    .line 21
    .line 22
    move-result-wide v4

    .line 23
    invoke-virtual {p1}, LwC0/b$a;->b()J

    .line 24
    .line 25
    .line 26
    move-result-wide v6

    .line 27
    invoke-virtual {p1}, LwC0/b$a;->e()Z

    .line 28
    .line 29
    .line 30
    move-result v8

    .line 31
    invoke-virtual {p1}, LwC0/b$a;->a()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v9

    .line 35
    iget-object v11, p0, LtC0/a;->o:Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;

    .line 36
    .line 37
    invoke-virtual {p1}, LwC0/b$a;->c()Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object v10

    .line 41
    new-instance v1, Lorg/xbet/sportgame/markets/api/navigation/MarketsParams$InsightsSubGame;

    .line 42
    .line 43
    invoke-direct/range {v1 .. v11}, Lorg/xbet/sportgame/markets/api/navigation/MarketsParams$InsightsSubGame;-><init>(JJJZLjava/lang/String;Ljava/lang/String;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V

    .line 44
    .line 45
    .line 46
    goto :goto_0

    .line 47
    :cond_0
    instance-of v1, p1, LwC0/b$b;

    .line 48
    .line 49
    if-eqz v1, :cond_1

    .line 50
    .line 51
    check-cast p1, LwC0/b$b;

    .line 52
    .line 53
    invoke-virtual {p1}, LwC0/b$b;->f()J

    .line 54
    .line 55
    .line 56
    move-result-wide v2

    .line 57
    invoke-virtual {p1}, LwC0/b$b;->d()J

    .line 58
    .line 59
    .line 60
    move-result-wide v4

    .line 61
    invoke-virtual {p1}, LwC0/b$b;->b()J

    .line 62
    .line 63
    .line 64
    move-result-wide v6

    .line 65
    invoke-virtual {p1}, LwC0/b$b;->e()Z

    .line 66
    .line 67
    .line 68
    move-result v8

    .line 69
    invoke-virtual {p1}, LwC0/b$b;->a()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v9

    .line 73
    iget-object v11, p0, LtC0/a;->o:Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;

    .line 74
    .line 75
    invoke-virtual {p1}, LwC0/b$b;->c()Ljava/lang/String;

    .line 76
    .line 77
    .line 78
    move-result-object v10

    .line 79
    new-instance v1, Lorg/xbet/sportgame/markets/api/navigation/MarketsParams$StandardSubGame;

    .line 80
    .line 81
    invoke-direct/range {v1 .. v11}, Lorg/xbet/sportgame/markets/api/navigation/MarketsParams$StandardSubGame;-><init>(JJJZLjava/lang/String;Ljava/lang/String;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V

    .line 82
    .line 83
    .line 84
    :goto_0
    invoke-interface {v0, v1}, LuB0/a;->b(Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;)Landroidx/fragment/app/Fragment;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    return-object p1

    .line 89
    :cond_1
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 90
    .line 91
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 92
    .line 93
    .line 94
    throw p1
.end method
