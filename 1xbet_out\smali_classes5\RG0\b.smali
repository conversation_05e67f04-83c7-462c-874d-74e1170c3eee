.class public final synthetic LRG0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:LWG0/d;

.field public final synthetic c:Landroidx/compose/ui/l;

.field public final synthetic d:I

.field public final synthetic e:I


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LRG0/b;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, LRG0/b;->b:LWG0/d;

    iput-object p3, p0, LRG0/b;->c:Landroidx/compose/ui/l;

    iput p4, p0, LRG0/b;->d:I

    iput p5, p0, LRG0/b;->e:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    iget-object v0, p0, LRG0/b;->a:Lkotlin/jvm/functions/Function1;

    iget-object v1, p0, LRG0/b;->b:LWG0/d;

    iget-object v2, p0, LRG0/b;->c:Landroidx/compose/ui/l;

    iget v3, p0, LRG0/b;->d:I

    iget v4, p0, LRG0/b;->e:I

    move-object v5, p1

    check-cast v5, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v6

    invoke-static/range {v0 .. v6}, LRG0/i;->c(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
