.class public final synthetic Lorg/xplatform/aggregator/impl/favorite/presentation/q;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/recyclerview/widget/RecyclerView;


# direct methods
.method public synthetic constructor <init>(Landroidx/recyclerview/widget/RecyclerView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/q;->a:Landroidx/recyclerview/widget/RecyclerView;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/q;->a:Landroidx/recyclerview/widget/RecyclerView;

    invoke-static {v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->E2(Landroidx/recyclerview/widget/RecyclerView;)V

    return-void
.end method
