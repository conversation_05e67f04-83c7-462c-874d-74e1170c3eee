.class public final Lt91/a$a;
.super LwX0/B;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lt91/a;->a(JJZJZJZI)Lr4/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001f\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u000f\u0010\u0003\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u0017\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0006\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "t91/a$a",
        "LwX0/B;",
        "",
        "needAuth",
        "()Z",
        "Landroidx/fragment/app/u;",
        "factory",
        "Landroidx/fragment/app/Fragment;",
        "createFragment",
        "(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:J

.field public final synthetic b:J

.field public final synthetic c:J

.field public final synthetic d:Z

.field public final synthetic e:Z

.field public final synthetic f:J

.field public final synthetic g:Z

.field public final synthetic h:I


# direct methods
.method public constructor <init>(JJJZZJZI)V
    .locals 0

    .line 1
    iput-wide p1, p0, Lt91/a$a;->a:J

    .line 2
    .line 3
    iput-wide p3, p0, Lt91/a$a;->b:J

    .line 4
    .line 5
    iput-wide p5, p0, Lt91/a$a;->c:J

    .line 6
    .line 7
    iput-boolean p7, p0, Lt91/a$a;->d:Z

    .line 8
    .line 9
    iput-boolean p8, p0, Lt91/a$a;->e:Z

    .line 10
    .line 11
    iput-wide p9, p0, Lt91/a$a;->f:J

    .line 12
    .line 13
    iput-boolean p11, p0, Lt91/a$a;->g:Z

    .line 14
    .line 15
    iput p12, p0, Lt91/a$a;->h:I

    .line 16
    .line 17
    invoke-direct {p0}, LwX0/B;-><init>()V

    .line 18
    .line 19
    .line 20
    return-void
.end method


# virtual methods
.method public createFragment(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;
    .locals 13

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/game/impl/gameslist/presentation/ChromeTabsLoadingFragment;->H1:Lorg/xplatform/aggregator/game/impl/gameslist/presentation/ChromeTabsLoadingFragment$a;

    .line 2
    .line 3
    iget-wide v1, p0, Lt91/a$a;->a:J

    .line 4
    .line 5
    iget-wide v3, p0, Lt91/a$a;->b:J

    .line 6
    .line 7
    iget-wide v5, p0, Lt91/a$a;->c:J

    .line 8
    .line 9
    iget-boolean v7, p0, Lt91/a$a;->d:Z

    .line 10
    .line 11
    iget-boolean v8, p0, Lt91/a$a;->e:Z

    .line 12
    .line 13
    iget-wide v9, p0, Lt91/a$a;->f:J

    .line 14
    .line 15
    iget-boolean v11, p0, Lt91/a$a;->g:Z

    .line 16
    .line 17
    iget v12, p0, Lt91/a$a;->h:I

    .line 18
    .line 19
    invoke-virtual/range {v0 .. v12}, Lorg/xplatform/aggregator/game/impl/gameslist/presentation/ChromeTabsLoadingFragment$a;->a(JJJZZJZI)Lorg/xplatform/aggregator/game/impl/gameslist/presentation/ChromeTabsLoadingFragment;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    return-object p1
.end method

.method public needAuth()Z
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    return v0
.end method
