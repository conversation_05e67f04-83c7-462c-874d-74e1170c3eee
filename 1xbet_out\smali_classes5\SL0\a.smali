.class public final LSL0/a;
.super LA4/e;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LSL0/a$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "LA4/e<",
        "LYL0/d;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0000\u0018\u0000 \u00072\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\u0008B\u000f\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\t"
    }
    d2 = {
        "LSL0/a;",
        "LA4/e;",
        "LYL0/d;",
        "LYL0/b;",
        "iStadiumUiModel",
        "<init>",
        "(LYL0/b;)V",
        "f",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final f:LSL0/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LSL0/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LSL0/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LSL0/a;->f:LSL0/a$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>(LYL0/b;)V
    .locals 3
    .param p1    # LYL0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, LSL0/a;->f:LSL0/a$a;

    .line 2
    .line 3
    invoke-direct {p0, v0}, LA4/e;-><init>(Landroidx/recyclerview/widget/i$f;)V

    .line 4
    .line 5
    .line 6
    instance-of v0, p1, LYL0/f;

    .line 7
    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 11
    .line 12
    check-cast p1, LYL0/f;

    .line 13
    .line 14
    invoke-virtual {p1}, LYL0/f;->a()LSX0/a;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    invoke-virtual {p1}, LYL0/f;->c()Landroidx/recyclerview/widget/x;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    invoke-virtual {p1}, LYL0/f;->b()Lkotlin/jvm/functions/Function1;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    invoke-static {v1, v2, p1}, Lorg/xbet/statistic/stadium/impl/core/presentation/adapter/delegate/TrackAdapterDelegateKt;->j(LSX0/a;Landroidx/recyclerview/widget/x;Lkotlin/jvm/functions/Function1;)LA4/c;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 31
    .line 32
    .line 33
    return-void

    .line 34
    :cond_0
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 35
    .line 36
    check-cast p1, LYL0/e;

    .line 37
    .line 38
    invoke-virtual {p1}, LYL0/e;->a()LSX0/a;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-virtual {p1}, LYL0/e;->b()Landroidx/recyclerview/widget/x;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    invoke-static {v1, p1}, Lorg/xbet/statistic/stadium/impl/core/presentation/adapter/delegate/StadiumAdapterDelegateKt;->i(LSX0/a;Landroidx/recyclerview/widget/x;)LA4/c;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 51
    .line 52
    .line 53
    return-void
.end method
