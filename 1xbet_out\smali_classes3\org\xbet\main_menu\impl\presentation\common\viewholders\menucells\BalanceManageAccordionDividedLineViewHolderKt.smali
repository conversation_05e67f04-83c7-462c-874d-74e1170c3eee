.class public final Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/BalanceManageAccordionDividedLineViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a/\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lkotlin/Function1;",
        "LN80/c;",
        "",
        "onItemClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "(Lkotlin/jvm/functions/Function1;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/BalanceManageAccordionDividedLineViewHolderKt;->h(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/BalanceManageAccordionDividedLineViewHolderKt;->g(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/BalanceManageAccordionDividedLineViewHolderKt;->i(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/C;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/BalanceManageAccordionDividedLineViewHolderKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/C;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lkotlin/jvm/functions/Function1;)LA4/c;
    .locals 4
    .param p0    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LN80/c;",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LT80/m;

    .line 2
    .line 3
    invoke-direct {v0}, LT80/m;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LT80/n;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LT80/n;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/BalanceManageAccordionDividedLineViewHolderKt$getBalanceManageAccordionDividedLineAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/BalanceManageAccordionDividedLineViewHolderKt$getBalanceManageAccordionDividedLineAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/BalanceManageAccordionDividedLineViewHolderKt$getBalanceManageAccordionDividedLineAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/BalanceManageAccordionDividedLineViewHolderKt$getBalanceManageAccordionDividedLineAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/C;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, Lv80/C;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)Lv80/C;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lv80/C;

    .line 6
    .line 7
    invoke-virtual {v0}, Lv80/C;->b()Lorg/xbet/uikit/components/cells/MenuCell;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    new-instance v1, LT80/o;

    .line 12
    .line 13
    invoke-direct {v1, p0, p1}, LT80/o;-><init>(Lkotlin/jvm/functions/Function1;LB4/a;)V

    .line 14
    .line 15
    .line 16
    const/4 p0, 0x1

    .line 17
    const/4 v2, 0x0

    .line 18
    invoke-static {v0, v2, v1, p0, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 19
    .line 20
    .line 21
    new-instance p0, LT80/p;

    .line 22
    .line 23
    invoke-direct {p0, p1}, LT80/p;-><init>(LB4/a;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 27
    .line 28
    .line 29
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 30
    .line 31
    return-object p0
.end method

.method public static final h(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final i(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 7

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lv80/C;

    .line 6
    .line 7
    iget-object p1, p1, Lv80/C;->b:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, LN80/c$b;

    .line 14
    .line 15
    invoke-virtual {v0}, LN80/c$b;->u()Z

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->setBadgeVisible(Z)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    check-cast p1, Lv80/C;

    .line 27
    .line 28
    iget-object v0, p1, Lv80/C;->b:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 29
    .line 30
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    check-cast p1, LN80/c$b;

    .line 35
    .line 36
    invoke-virtual {p1}, LN80/c$b;->f()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v1

    .line 40
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    check-cast p1, LN80/c$b;

    .line 45
    .line 46
    invoke-virtual {p1}, LN80/c$b;->j()I

    .line 47
    .line 48
    .line 49
    move-result v2

    .line 50
    const/16 v5, 0xc

    .line 51
    .line 52
    const/4 v6, 0x0

    .line 53
    const/4 v3, 0x0

    .line 54
    const/4 v4, 0x0

    .line 55
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit/components/views/LoadableImageView;->W(Lorg/xbet/uikit/components/views/LoadableImageView;Ljava/lang/String;ILkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    check-cast p1, Lv80/C;

    .line 63
    .line 64
    iget-object p1, p1, Lv80/C;->c:Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;

    .line 65
    .line 66
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    check-cast v0, LN80/c$b;

    .line 71
    .line 72
    invoke-virtual {v0}, LN80/c$b;->getTitle()Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;->setTitle(Ljava/lang/CharSequence;)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    check-cast p1, Lv80/C;

    .line 84
    .line 85
    iget-object p1, p1, Lv80/C;->c:Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;

    .line 86
    .line 87
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 88
    .line 89
    .line 90
    move-result-object p0

    .line 91
    check-cast p0, LN80/c$b;

    .line 92
    .line 93
    invoke-virtual {p0}, LN80/c$b;->o()Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object p0

    .line 97
    invoke-virtual {p1, p0}, Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;->setSubtitle(Ljava/lang/CharSequence;)V

    .line 98
    .line 99
    .line 100
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 101
    .line 102
    return-object p0
.end method
