.class public final Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lf81/d;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0008\u0018\u00002\u00020\u0001B!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ(\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000eH\u0096B\u00a2\u0006\u0004\u0008\u0011\u0010\u0012R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0013R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0015R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0017\u00a8\u0006\u0018"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;",
        "Lf81/d;",
        "Lcom/xbet/onexcore/utils/ext/c;",
        "networkConnectionUtil",
        "Lu81/b;",
        "repository",
        "Lm8/a;",
        "dispatchers",
        "<init>",
        "(Lcom/xbet/onexcore/utils/ext/c;Lu81/b;Lm8/a;)V",
        "",
        "gameId",
        "",
        "brandsApi",
        "",
        "subcategoryId",
        "",
        "a",
        "(JZILkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lcom/xbet/onexcore/utils/ext/c;",
        "b",
        "Lu81/b;",
        "c",
        "Lm8/a;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lcom/xbet/onexcore/utils/ext/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lu81/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lcom/xbet/onexcore/utils/ext/c;Lu81/b;Lm8/a;)V
    .locals 0
    .param p1    # Lcom/xbet/onexcore/utils/ext/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lu81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;->a:Lcom/xbet/onexcore/utils/ext/c;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;->b:Lu81/b;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;->c:Lm8/a;

    .line 9
    .line 10
    return-void
.end method

.method public static final synthetic b(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;)Lu81/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;->b:Lu81/b;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public a(JZILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JZI",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;->a:Lcom/xbet/onexcore/utils/ext/c;

    .line 2
    .line 3
    invoke-interface {v0}, Lcom/xbet/onexcore/utils/ext/c;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    return-object p1

    .line 12
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;->c:Lm8/a;

    .line 13
    .line 14
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;

    .line 19
    .line 20
    const/4 v7, 0x0

    .line 21
    move-object v2, p0

    .line 22
    move-wide v3, p1

    .line 23
    move v5, p3

    .line 24
    move v6, p4

    .line 25
    invoke-direct/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;-><init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;JZILkotlin/coroutines/e;)V

    .line 26
    .line 27
    .line 28
    invoke-static {v0, v1, p5}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object p2

    .line 36
    if-ne p1, p2, :cond_1

    .line 37
    .line 38
    return-object p1

    .line 39
    :cond_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 40
    .line 41
    return-object p1
.end method
