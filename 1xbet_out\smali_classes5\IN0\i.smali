.class public final synthetic LIN0/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;

.field public final synthetic b:Landroidx/compose/ui/l;

.field public final synthetic c:I

.field public final synthetic d:I


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;Landroidx/compose/ui/l;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LIN0/i;->a:Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;

    iput-object p2, p0, LIN0/i;->b:Landroidx/compose/ui/l;

    iput p3, p0, LIN0/i;->c:I

    iput p4, p0, LIN0/i;->d:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    iget-object v0, p0, LIN0/i;->a:Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;

    iget-object v1, p0, LIN0/i;->b:Landroidx/compose/ui/l;

    iget v2, p0, LIN0/i;->c:I

    iget v3, p0, LIN0/i;->d:I

    move-object v4, p1

    check-cast v4, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v5

    invoke-static/range {v0 .. v5}, LIN0/k;->a(Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
