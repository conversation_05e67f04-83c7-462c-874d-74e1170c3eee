.class public final LlY0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\t\n\u0002\u0008\u0013\u0008\u00c7\u0002\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003R\u001a\u0010\t\u001a\u00020\u00048\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008\u0005\u0010\u0006\u001a\u0004\u0008\u0007\u0010\u0008R\u001a\u0010\u000e\u001a\u00020\n8\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008\u0007\u0010\u000b\u001a\u0004\u0008\u000c\u0010\rR\u001a\u0010\u0011\u001a\u00020\n8\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008\u000f\u0010\u000b\u001a\u0004\u0008\u0010\u0010\rR\u001a\u0010\u0013\u001a\u00020\n8\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008\u0012\u0010\u000b\u001a\u0004\u0008\u0005\u0010\rR\u001a\u0010\u0015\u001a\u00020\n8\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008\u0014\u0010\u000b\u001a\u0004\u0008\u000f\u0010\rR\u001a\u0010\u0017\u001a\u00020\n8\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008\u0016\u0010\u000b\u001a\u0004\u0008\u0012\u0010\rR\u001a\u0010\u0019\u001a\u00020\n8\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008\u0018\u0010\u000b\u001a\u0004\u0008\u0014\u0010\rR\u001a\u0010\u001c\u001a\u00020\n8\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u000b\u001a\u0004\u0008\u001b\u0010\r\u00a8\u0006\u001d"
    }
    d2 = {
        "LlY0/b;",
        "",
        "<init>",
        "()V",
        "",
        "b",
        "I",
        "c",
        "()I",
        "elevationOverlayColor",
        "",
        "J",
        "a",
        "()J",
        "axisLabelColor",
        "d",
        "getAxisGuidelineColor",
        "axisGuidelineColor",
        "e",
        "axisLineColor",
        "f",
        "entity1Color",
        "g",
        "entity2Color",
        "h",
        "entity3Color",
        "i",
        "getLineColor",
        "lineColor",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:LlY0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final b:I

.field public static final c:J

.field public static final d:J

.field public static final e:J

.field public static final f:J

.field public static final g:J

.field public static final h:J

.field public static final i:J


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LlY0/b;

    .line 2
    .line 3
    invoke-direct {v0}, LlY0/b;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LlY0/b;->a:LlY0/b;

    .line 7
    .line 8
    const/high16 v0, -0x80000000

    .line 9
    .line 10
    sput v0, LlY0/b;->b:I

    .line 11
    .line 12
    const-wide v0, 0xde000000L

    .line 13
    .line 14
    .line 15
    .line 16
    .line 17
    sput-wide v0, LlY0/b;->c:J

    .line 18
    .line 19
    const-wide/32 v0, 0x47000000

    .line 20
    .line 21
    .line 22
    sput-wide v0, LlY0/b;->d:J

    .line 23
    .line 24
    sput-wide v0, LlY0/b;->e:J

    .line 25
    .line 26
    const-wide v0, 0xff787878L

    .line 27
    .line 28
    .line 29
    .line 30
    .line 31
    sput-wide v0, LlY0/b;->f:J

    .line 32
    .line 33
    const-wide v0, 0xff5a5a5aL

    .line 34
    .line 35
    .line 36
    .line 37
    .line 38
    sput-wide v0, LlY0/b;->g:J

    .line 39
    .line 40
    const-wide v0, 0xff383838L

    .line 41
    .line 42
    .line 43
    .line 44
    .line 45
    sput-wide v0, LlY0/b;->h:J

    .line 46
    .line 47
    const-wide v0, 0xff1a1a1aL

    .line 48
    .line 49
    .line 50
    .line 51
    .line 52
    sput-wide v0, LlY0/b;->i:J

    .line 53
    .line 54
    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a()J
    .locals 2

    .line 1
    sget-wide v0, LlY0/b;->c:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public b()J
    .locals 2

    .line 1
    sget-wide v0, LlY0/b;->e:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public c()I
    .locals 1

    .line 1
    sget v0, LlY0/b;->b:I

    .line 2
    .line 3
    return v0
.end method

.method public d()J
    .locals 2

    .line 1
    sget-wide v0, LlY0/b;->f:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public e()J
    .locals 2

    .line 1
    sget-wide v0, LlY0/b;->g:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public f()J
    .locals 2

    .line 1
    sget-wide v0, LlY0/b;->h:J

    .line 2
    .line 3
    return-wide v0
.end method
