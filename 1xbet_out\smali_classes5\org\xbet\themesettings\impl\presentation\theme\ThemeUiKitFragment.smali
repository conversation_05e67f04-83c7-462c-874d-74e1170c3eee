.class public final Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\n\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u0000 >2\u00020\u0001:\u0001?B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u0019\u0010\u0008\u001a\u00020\u00042\u0008\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u0014\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\n\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\n\u0010\u0003J\u0017\u0010\r\u001a\u00020\u00042\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u0011\u001a\u00020\u00042\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J%\u0010\u0017\u001a\u00020\u00042\u000c\u0010\u0015\u001a\u0008\u0012\u0004\u0012\u00020\u00140\u00132\u0006\u0010\u0016\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\'\u0010\u001d\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u001b\u001a\u00020\u000b2\u0006\u0010\u001c\u001a\u00020\u000bH\u0003\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0017\u0010\u001f\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0017\u0010!\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008!\u0010 J\u000f\u0010\"\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\"\u0010\u0003J\u000f\u0010#\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008#\u0010\u0003R\u001a\u0010)\u001a\u00020$8\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008%\u0010&\u001a\u0004\u0008\'\u0010(R\"\u00101\u001a\u00020*8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.\"\u0004\u0008/\u00100R\u001b\u00107\u001a\u0002028BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00083\u00104\u001a\u0004\u00085\u00106R\u001b\u0010=\u001a\u0002088BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00089\u0010:\u001a\u0004\u0008;\u0010<\u00a8\u0006@"
    }
    d2 = {
        "Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "",
        "key",
        "Z2",
        "(Ljava/lang/String;)V",
        "LjT0/a;",
        "state",
        "T2",
        "(LjT0/a;)V",
        "",
        "Lcom/xbet/onexcore/themes/Theme;",
        "availableThemes",
        "theme",
        "J2",
        "(Ljava/util/List;Lcom/xbet/onexcore/themes/Theme;)V",
        "",
        "enable",
        "turnOnTime",
        "turnOffTime",
        "K2",
        "(ZLjava/lang/String;Ljava/lang/String;)V",
        "P2",
        "(Z)V",
        "O2",
        "c3",
        "b3",
        "",
        "i0",
        "I",
        "p2",
        "()I",
        "colorRes",
        "LeT0/h;",
        "j0",
        "LeT0/h;",
        "S2",
        "()LeT0/h;",
        "setViewModelFactory",
        "(LeT0/h;)V",
        "viewModelFactory",
        "Lorg/xbet/themesettings/impl/presentation/theme/m;",
        "k0",
        "Lkotlin/j;",
        "R2",
        "()Lorg/xbet/themesettings/impl/presentation/theme/m;",
        "viewModel",
        "LcT0/c;",
        "l0",
        "LRc/c;",
        "Q2",
        "()LcT0/c;",
        "binding",
        "m0",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final m0:Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic n0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final i0:I

.field public j0:LeT0/h;

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getBinding()Lorg/xbet/themesettings/impl/databinding/FragmentThemeSettingsUiKitBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;

    .line 7
    .line 8
    const-string v4, "binding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->n0:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->m0:Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$a;

    .line 31
    .line 32
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LbT0/b;->fragment_theme_settings_ui_kit:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget v0, Lpb/c;->statusBarColor:I

    .line 7
    .line 8
    iput v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->i0:I

    .line 9
    .line 10
    new-instance v0, Lorg/xbet/themesettings/impl/presentation/theme/a;

    .line 11
    .line 12
    invoke-direct {v0, p0}, Lorg/xbet/themesettings/impl/presentation/theme/a;-><init>(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)V

    .line 13
    .line 14
    .line 15
    new-instance v1, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$special$$inlined$viewModels$default$1;

    .line 16
    .line 17
    invoke-direct {v1, p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 18
    .line 19
    .line 20
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 21
    .line 22
    new-instance v3, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$special$$inlined$viewModels$default$2;

    .line 23
    .line 24
    invoke-direct {v3, v1}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 25
    .line 26
    .line 27
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    const-class v2, Lorg/xbet/themesettings/impl/presentation/theme/m;

    .line 32
    .line 33
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    new-instance v3, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$special$$inlined$viewModels$default$3;

    .line 38
    .line 39
    invoke-direct {v3, v1}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 40
    .line 41
    .line 42
    new-instance v4, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$special$$inlined$viewModels$default$4;

    .line 43
    .line 44
    const/4 v5, 0x0

    .line 45
    invoke-direct {v4, v5, v1}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 46
    .line 47
    .line 48
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    iput-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->k0:Lkotlin/j;

    .line 53
    .line 54
    sget-object v0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$binding$2;->INSTANCE:Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$binding$2;

    .line 55
    .line 56
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    iput-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->l0:LRc/c;

    .line 61
    .line 62
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->M2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/widget/CompoundButton;Z)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->L2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/widget/CompoundButton;Z)V

    return-void
.end method

.method public static synthetic C2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->U2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic D2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->N2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic E2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/widget/CompoundButton;Z)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->W2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/widget/CompoundButton;Z)V

    return-void
.end method

.method public static synthetic F2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/widget/CompoundButton;Z)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->V2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/widget/CompoundButton;Z)V

    return-void
.end method

.method public static synthetic G2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Ljava/lang/String;Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment$BottomSheetResult;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->a3(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Ljava/lang/String;Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment$BottomSheetResult;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic H2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)Lorg/xbet/themesettings/impl/presentation/theme/m;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->R2()Lorg/xbet/themesettings/impl/presentation/theme/m;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic I2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;LjT0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Y2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;LjT0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final L2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/widget/CompoundButton;Z)V
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$b;

    .line 2
    .line 3
    invoke-direct {v0, p0, p2}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$b;-><init>(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Z)V

    .line 4
    .line 5
    .line 6
    const-wide/16 v1, 0xfa

    .line 7
    .line 8
    invoke-virtual {p1, v0, v1, v2}, Landroid/view/View;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static final M2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->c3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final N2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->b3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final U2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->R2()Lorg/xbet/themesettings/impl/presentation/theme/m;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/m;->onBackPressed()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final V2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/widget/CompoundButton;Z)V
    .locals 0

    .line 1
    if-eqz p2, :cond_0

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->R2()Lorg/xbet/themesettings/impl/presentation/theme/m;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/m;->v3()V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public static final W2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/widget/CompoundButton;Z)V
    .locals 0

    .line 1
    if-eqz p2, :cond_0

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->R2()Lorg/xbet/themesettings/impl/presentation/theme/m;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/m;->t3()V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public static final X2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/widget/CompoundButton;Z)V
    .locals 0

    .line 1
    if-eqz p2, :cond_0

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->R2()Lorg/xbet/themesettings/impl/presentation/theme/m;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/m;->w3()V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public static final synthetic Y2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;LjT0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->T2(LjT0/a;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final a3(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Ljava/lang/String;Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment$BottomSheetResult;I)Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment$BottomSheetResult;->ITEM_CLICKED:Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment$BottomSheetResult;

    .line 2
    .line 3
    if-ne p2, v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->R2()Lorg/xbet/themesettings/impl/presentation/theme/m;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    sget-object p1, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 10
    .line 11
    invoke-virtual {p1, p3}, Lcom/xbet/onexcore/themes/Theme$a;->a(I)Lcom/xbet/onexcore/themes/Theme;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    invoke-virtual {p0, p1}, Lorg/xbet/themesettings/impl/presentation/theme/m;->z3(Lcom/xbet/onexcore/themes/Theme;)V

    .line 16
    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->R2()Lorg/xbet/themesettings/impl/presentation/theme/m;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    invoke-virtual {p0}, LcT0/c;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 28
    .line 29
    .line 30
    move-result-object p0

    .line 31
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 32
    .line 33
    .line 34
    move-result-object p0

    .line 35
    invoke-static {p0}, Landroid/text/format/DateFormat;->is24HourFormat(Landroid/content/Context;)Z

    .line 36
    .line 37
    .line 38
    move-result p0

    .line 39
    const-string p3, "TIME_PICKER_ON_DIALOG_KEY"

    .line 40
    .line 41
    invoke-static {p1, p3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 42
    .line 43
    .line 44
    move-result p1

    .line 45
    invoke-virtual {p2, p0, p1}, Lorg/xbet/themesettings/impl/presentation/theme/m;->A3(ZZ)V

    .line 46
    .line 47
    .line 48
    :goto_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 49
    .line 50
    return-object p0
.end method

.method public static final d3(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)Landroidx/lifecycle/e0$c;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/a;

    .line 2
    .line 3
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->S2()LeT0/h;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, v1, p0}, Lorg/xbet/ui_common/viewmodel/core/a;-><init>(LwX0/c;LQW0/i;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->d3(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/widget/CompoundButton;Z)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->X2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/widget/CompoundButton;Z)V

    return-void
.end method


# virtual methods
.method public final J2(Ljava/util/List;Lcom/xbet/onexcore/themes/Theme;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/themes/Theme;",
            ">;",
            "Lcom/xbet/onexcore/themes/Theme;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LcT0/c;->f:Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;

    .line 6
    .line 7
    sget-object v1, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 8
    .line 9
    invoke-virtual {v1, p2}, Lcom/xbet/onexcore/themes/Theme$a;->c(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    invoke-virtual {v0, v2}, Landroid/widget/CompoundButton;->setChecked(Z)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    iget-object v0, v0, LcT0/c;->e:Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;

    .line 21
    .line 22
    invoke-virtual {v1, p2}, Lcom/xbet/onexcore/themes/Theme$a;->b(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    invoke-virtual {v0, v2}, Landroid/widget/CompoundButton;->setChecked(Z)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget-object v0, v0, LcT0/c;->g:Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;

    .line 34
    .line 35
    invoke-virtual {v1, p2}, Lcom/xbet/onexcore/themes/Theme$a;->d(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 36
    .line 37
    .line 38
    move-result p2

    .line 39
    invoke-virtual {v0, p2}, Landroid/widget/CompoundButton;->setChecked(Z)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 43
    .line 44
    .line 45
    move-result-object p2

    .line 46
    iget-object p2, p2, LcT0/c;->f:Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;

    .line 47
    .line 48
    invoke-static {p1}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 49
    .line 50
    .line 51
    move-result v0

    .line 52
    const/4 v1, 0x1

    .line 53
    const/4 v2, 0x0

    .line 54
    if-eqz v0, :cond_1

    .line 55
    .line 56
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 57
    .line 58
    .line 59
    move-result v0

    .line 60
    if-eqz v0, :cond_1

    .line 61
    .line 62
    :cond_0
    const/4 v0, 0x0

    .line 63
    goto :goto_0

    .line 64
    :cond_1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    :cond_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 69
    .line 70
    .line 71
    move-result v3

    .line 72
    if-eqz v3, :cond_0

    .line 73
    .line 74
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 75
    .line 76
    .line 77
    move-result-object v3

    .line 78
    check-cast v3, Lcom/xbet/onexcore/themes/Theme;

    .line 79
    .line 80
    sget-object v4, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 81
    .line 82
    invoke-virtual {v4, v3}, Lcom/xbet/onexcore/themes/Theme$a;->c(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 83
    .line 84
    .line 85
    move-result v3

    .line 86
    if-eqz v3, :cond_2

    .line 87
    .line 88
    const/4 v0, 0x1

    .line 89
    :goto_0
    const/16 v3, 0x8

    .line 90
    .line 91
    if-eqz v0, :cond_3

    .line 92
    .line 93
    const/4 v0, 0x0

    .line 94
    goto :goto_1

    .line 95
    :cond_3
    const/16 v0, 0x8

    .line 96
    .line 97
    :goto_1
    invoke-virtual {p2, v0}, Landroid/view/View;->setVisibility(I)V

    .line 98
    .line 99
    .line 100
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 101
    .line 102
    .line 103
    move-result-object p2

    .line 104
    iget-object p2, p2, LcT0/c;->e:Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;

    .line 105
    .line 106
    invoke-static {p1}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 107
    .line 108
    .line 109
    move-result v0

    .line 110
    if-eqz v0, :cond_5

    .line 111
    .line 112
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 113
    .line 114
    .line 115
    move-result v0

    .line 116
    if-eqz v0, :cond_5

    .line 117
    .line 118
    :cond_4
    const/4 v0, 0x0

    .line 119
    goto :goto_2

    .line 120
    :cond_5
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 121
    .line 122
    .line 123
    move-result-object v0

    .line 124
    :cond_6
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 125
    .line 126
    .line 127
    move-result v4

    .line 128
    if-eqz v4, :cond_4

    .line 129
    .line 130
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    move-result-object v4

    .line 134
    check-cast v4, Lcom/xbet/onexcore/themes/Theme;

    .line 135
    .line 136
    sget-object v5, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 137
    .line 138
    invoke-virtual {v5, v4}, Lcom/xbet/onexcore/themes/Theme$a;->b(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 139
    .line 140
    .line 141
    move-result v4

    .line 142
    if-eqz v4, :cond_6

    .line 143
    .line 144
    const/4 v0, 0x1

    .line 145
    :goto_2
    if-eqz v0, :cond_7

    .line 146
    .line 147
    const/4 v0, 0x0

    .line 148
    goto :goto_3

    .line 149
    :cond_7
    const/16 v0, 0x8

    .line 150
    .line 151
    :goto_3
    invoke-virtual {p2, v0}, Landroid/view/View;->setVisibility(I)V

    .line 152
    .line 153
    .line 154
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 155
    .line 156
    .line 157
    move-result-object p2

    .line 158
    iget-object p2, p2, LcT0/c;->g:Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;

    .line 159
    .line 160
    invoke-static {p1}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 161
    .line 162
    .line 163
    move-result v0

    .line 164
    if-eqz v0, :cond_9

    .line 165
    .line 166
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 167
    .line 168
    .line 169
    move-result v0

    .line 170
    if-eqz v0, :cond_9

    .line 171
    .line 172
    :cond_8
    const/4 v1, 0x0

    .line 173
    goto :goto_4

    .line 174
    :cond_9
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 175
    .line 176
    .line 177
    move-result-object p1

    .line 178
    :cond_a
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 179
    .line 180
    .line 181
    move-result v0

    .line 182
    if-eqz v0, :cond_8

    .line 183
    .line 184
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 185
    .line 186
    .line 187
    move-result-object v0

    .line 188
    check-cast v0, Lcom/xbet/onexcore/themes/Theme;

    .line 189
    .line 190
    sget-object v4, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 191
    .line 192
    invoke-virtual {v4, v0}, Lcom/xbet/onexcore/themes/Theme$a;->d(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 193
    .line 194
    .line 195
    move-result v0

    .line 196
    if-eqz v0, :cond_a

    .line 197
    .line 198
    :goto_4
    if-eqz v1, :cond_b

    .line 199
    .line 200
    goto :goto_5

    .line 201
    :cond_b
    const/16 v2, 0x8

    .line 202
    .line 203
    :goto_5
    invoke-virtual {p2, v2}, Landroid/view/View;->setVisibility(I)V

    .line 204
    .line 205
    .line 206
    return-void
.end method

.method public final K2(ZLjava/lang/String;Ljava/lang/String;)V
    .locals 1
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "ClickableViewAccessibility"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LcT0/c;->l:Lorg/xbet/uikit/components/cells/right/CellRightLabel;

    .line 6
    .line 7
    invoke-virtual {v0, p2}, Lorg/xbet/uikit/components/cells/right/CellRightLabel;->setText(Ljava/lang/CharSequence;)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 11
    .line 12
    .line 13
    move-result-object p2

    .line 14
    iget-object p2, p2, LcT0/c;->j:Lorg/xbet/uikit/components/cells/right/CellRightLabel;

    .line 15
    .line 16
    invoke-virtual {p2, p3}, Lorg/xbet/uikit/components/cells/right/CellRightLabel;->setText(Ljava/lang/CharSequence;)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    iget-object p2, p2, LcT0/c;->i:Lorg/xbet/uikit/components/cells/right/CellRightSwitch;

    .line 24
    .line 25
    invoke-virtual {p2, p1}, Landroidx/appcompat/widget/SwitchCompat;->setChecked(Z)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    iget-object p1, p1, LcT0/c;->i:Lorg/xbet/uikit/components/cells/right/CellRightSwitch;

    .line 33
    .line 34
    new-instance p2, Lorg/xbet/themesettings/impl/presentation/theme/b;

    .line 35
    .line 36
    invoke-direct {p2, p0}, Lorg/xbet/themesettings/impl/presentation/theme/b;-><init>(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p1, p2}, Landroid/widget/CompoundButton;->setOnCheckedChangeListener(Landroid/widget/CompoundButton$OnCheckedChangeListener;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    iget-object p1, p1, LcT0/c;->n:Lorg/xbet/uikit/components/cells/SettingsCell;

    .line 47
    .line 48
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 49
    .line 50
    .line 51
    move-result-object p2

    .line 52
    iget-object p2, p2, LcT0/c;->i:Lorg/xbet/uikit/components/cells/right/CellRightSwitch;

    .line 53
    .line 54
    invoke-static {p1, p2}, Lorg/xbet/ui_common/utils/ViewExtensionsKt;->e(Landroid/view/View;Landroidx/appcompat/widget/SwitchCompat;)V

    .line 55
    .line 56
    .line 57
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    iget-object p1, p1, LcT0/c;->l:Lorg/xbet/uikit/components/cells/right/CellRightLabel;

    .line 62
    .line 63
    new-instance p2, Lorg/xbet/themesettings/impl/presentation/theme/c;

    .line 64
    .line 65
    invoke-direct {p2, p0}, Lorg/xbet/themesettings/impl/presentation/theme/c;-><init>(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)V

    .line 66
    .line 67
    .line 68
    const/4 p3, 0x0

    .line 69
    const/4 v0, 0x1

    .line 70
    invoke-static {p1, p3, p2, v0, p3}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 71
    .line 72
    .line 73
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    iget-object p1, p1, LcT0/c;->j:Lorg/xbet/uikit/components/cells/right/CellRightLabel;

    .line 78
    .line 79
    new-instance p2, Lorg/xbet/themesettings/impl/presentation/theme/d;

    .line 80
    .line 81
    invoke-direct {p2, p0}, Lorg/xbet/themesettings/impl/presentation/theme/d;-><init>(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)V

    .line 82
    .line 83
    .line 84
    invoke-static {p1, p3, p2, v0, p3}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 85
    .line 86
    .line 87
    return-void
.end method

.method public final O2(Z)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LcT0/c;->m:Lorg/xbet/uikit/components/cells/SettingsCell;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/cells/BaseCell;->setEnabled(Z)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    iget-object v0, v0, LcT0/c;->k:Lorg/xbet/uikit/components/cells/SettingsCell;

    .line 15
    .line 16
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/cells/BaseCell;->setEnabled(Z)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public final P2(Z)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LcT0/c;->n:Lorg/xbet/uikit/components/cells/SettingsCell;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/cells/BaseCell;->setEnabled(Z)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    iget-object v0, v0, LcT0/c;->i:Lorg/xbet/uikit/components/cells/right/CellRightSwitch;

    .line 15
    .line 16
    invoke-virtual {v0, p1}, Landroid/view/View;->setEnabled(Z)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public final Q2()LcT0/c;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->l0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->n0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LcT0/c;

    .line 13
    .line 14
    return-object v0
.end method

.method public final R2()Lorg/xbet/themesettings/impl/presentation/theme/m;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/themesettings/impl/presentation/theme/m;

    .line 8
    .line 9
    return-object v0
.end method

.method public final S2()LeT0/h;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->j0:LeT0/h;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final T2(LjT0/a;)V
    .locals 3

    .line 1
    invoke-virtual {p1}, LjT0/a;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    return-void

    .line 12
    :cond_0
    invoke-virtual {p1}, LjT0/a;->c()Ljava/util/List;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-virtual {p1}, LjT0/a;->d()Lcom/xbet/onexcore/themes/Theme;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-virtual {p0, v0, v1}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->J2(Ljava/util/List;Lcom/xbet/onexcore/themes/Theme;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p1}, LjT0/a;->f()Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    invoke-virtual {p1}, LjT0/a;->h()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    invoke-virtual {p1}, LjT0/a;->g()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v2

    .line 35
    invoke-virtual {p0, v0, v1, v2}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->K2(ZLjava/lang/String;Ljava/lang/String;)V

    .line 36
    .line 37
    .line 38
    invoke-virtual {p1}, LjT0/a;->e()Z

    .line 39
    .line 40
    .line 41
    move-result v0

    .line 42
    invoke-virtual {p0, v0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->P2(Z)V

    .line 43
    .line 44
    .line 45
    invoke-virtual {p1}, LjT0/a;->f()Z

    .line 46
    .line 47
    .line 48
    move-result p1

    .line 49
    invoke-virtual {p0, p1}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->O2(Z)V

    .line 50
    .line 51
    .line 52
    return-void
.end method

.method public final Z2(Ljava/lang/String;)V
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/themesettings/impl/presentation/theme/i;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, Lorg/xbet/themesettings/impl/presentation/theme/i;-><init>(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-static {p0, p1, v0}, Lorg/xbet/ui_common/utils/ExtensionsKt;->F(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function2;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final b3()V
    .locals 4

    .line 1
    sget-object v0, Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomSheet;->b1:Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomSheet$a;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget v2, Lpb/k;->turn_off_theme:I

    .line 8
    .line 9
    invoke-virtual {p0, v2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    const-string v3, "TIME_PICKER_OFF_DIALOG_KEY"

    .line 14
    .line 15
    invoke-virtual {v0, v1, v3, v2}, Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomSheet$a;->a(Landroidx/fragment/app/FragmentManager;Ljava/lang/String;Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public final c3()V
    .locals 4

    .line 1
    sget-object v0, Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomSheet;->b1:Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomSheet$a;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget v2, Lpb/k;->turn_on_theme:I

    .line 8
    .line 9
    invoke-virtual {p0, v2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    const-string v3, "TIME_PICKER_ON_DIALOG_KEY"

    .line 14
    .line 15
    invoke-virtual {v0, v1, v3, v2}, Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomSheet$a;->a(Landroidx/fragment/app/FragmentManager;Ljava/lang/String;Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public p2()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->i0:I

    .line 2
    .line 3
    return v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->R2()Lorg/xbet/themesettings/impl/presentation/theme/m;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, LcT0/c;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {v0}, Landroid/text/format/DateFormat;->is24HourFormat(Landroid/content/Context;)Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    invoke-virtual {p1, v0}, Lorg/xbet/themesettings/impl/presentation/theme/m;->u3(Z)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    iget-object p1, p1, LcT0/c;->d:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 29
    .line 30
    new-instance v0, Lorg/xbet/themesettings/impl/presentation/theme/e;

    .line 31
    .line 32
    invoke-direct {v0, p0}, Lorg/xbet/themesettings/impl/presentation/theme/e;-><init>(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)V

    .line 33
    .line 34
    .line 35
    const/4 v1, 0x1

    .line 36
    const/4 v2, 0x0

    .line 37
    const/4 v3, 0x0

    .line 38
    invoke-static {p1, v3, v0, v1, v2}, LK01/d$a;->a(LK01/d;ZLkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    iget-object p1, p1, LcT0/c;->f:Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;

    .line 46
    .line 47
    new-instance v0, Lorg/xbet/themesettings/impl/presentation/theme/f;

    .line 48
    .line 49
    invoke-direct {v0, p0}, Lorg/xbet/themesettings/impl/presentation/theme/f;-><init>(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {p1, v0}, Landroid/widget/CompoundButton;->setOnCheckedChangeListener(Landroid/widget/CompoundButton$OnCheckedChangeListener;)V

    .line 53
    .line 54
    .line 55
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    iget-object p1, p1, LcT0/c;->e:Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;

    .line 60
    .line 61
    new-instance v0, Lorg/xbet/themesettings/impl/presentation/theme/g;

    .line 62
    .line 63
    invoke-direct {v0, p0}, Lorg/xbet/themesettings/impl/presentation/theme/g;-><init>(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)V

    .line 64
    .line 65
    .line 66
    invoke-virtual {p1, v0}, Landroid/widget/CompoundButton;->setOnCheckedChangeListener(Landroid/widget/CompoundButton$OnCheckedChangeListener;)V

    .line 67
    .line 68
    .line 69
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Q2()LcT0/c;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    iget-object p1, p1, LcT0/c;->g:Lorg/xbet/uikit/components/selectioncontrollers/RadioButton;

    .line 74
    .line 75
    new-instance v0, Lorg/xbet/themesettings/impl/presentation/theme/h;

    .line 76
    .line 77
    invoke-direct {v0, p0}, Lorg/xbet/themesettings/impl/presentation/theme/h;-><init>(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)V

    .line 78
    .line 79
    .line 80
    invoke-virtual {p1, v0}, Landroid/widget/CompoundButton;->setOnCheckedChangeListener(Landroid/widget/CompoundButton$OnCheckedChangeListener;)V

    .line 81
    .line 82
    .line 83
    const-string p1, "TIME_PICKER_ON_DIALOG_KEY"

    .line 84
    .line 85
    invoke-virtual {p0, p1}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Z2(Ljava/lang/String;)V

    .line 86
    .line 87
    .line 88
    const-string p1, "TIME_PICKER_OFF_DIALOG_KEY"

    .line 89
    .line 90
    invoke-virtual {p0, p1}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->Z2(Ljava/lang/String;)V

    .line 91
    .line 92
    .line 93
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    instance-of v1, v0, LQW0/b;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    check-cast v0, LQW0/b;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    move-object v0, v2

    .line 18
    :goto_0
    const-class v1, LeT0/f;

    .line 19
    .line 20
    if-eqz v0, :cond_3

    .line 21
    .line 22
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, LBc/a;

    .line 31
    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, LQW0/a;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-object v0, v2

    .line 42
    :goto_1
    instance-of v3, v0, LeT0/f;

    .line 43
    .line 44
    if-nez v3, :cond_2

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_2
    move-object v2, v0

    .line 48
    :goto_2
    check-cast v2, LeT0/f;

    .line 49
    .line 50
    if-eqz v2, :cond_3

    .line 51
    .line 52
    const-class v0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;

    .line 53
    .line 54
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    invoke-virtual {v2, v0}, LeT0/f;->a(Ljava/lang/String;)LeT0/e;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    invoke-interface {v0, p0}, LeT0/e;->a(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)V

    .line 63
    .line 64
    .line 65
    return-void

    .line 66
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 67
    .line 68
    new-instance v2, Ljava/lang/StringBuilder;

    .line 69
    .line 70
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 71
    .line 72
    .line 73
    const-string v3, "Cannot create dependency "

    .line 74
    .line 75
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 76
    .line 77
    .line 78
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 79
    .line 80
    .line 81
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 82
    .line 83
    .line 84
    move-result-object v1

    .line 85
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 86
    .line 87
    .line 88
    move-result-object v1

    .line 89
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 90
    .line 91
    .line 92
    throw v0
.end method

.method public v2()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->R2()Lorg/xbet/themesettings/impl/presentation/theme/m;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/themesettings/impl/presentation/theme/m;->r3()Lkotlinx/coroutines/flow/f0;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$onObserveData$1;

    .line 10
    .line 11
    invoke-direct {v5, p0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$onObserveData$1;-><init>(Ljava/lang/Object;)V

    .line 12
    .line 13
    .line 14
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 15
    .line 16
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 17
    .line 18
    .line 19
    move-result-object v3

    .line 20
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    new-instance v1, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 25
    .line 26
    const/4 v6, 0x0

    .line 27
    invoke-direct/range {v1 .. v6}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    const/4 v10, 0x3

    .line 31
    const/4 v11, 0x0

    .line 32
    const/4 v7, 0x0

    .line 33
    const/4 v8, 0x0

    .line 34
    move-object v6, v0

    .line 35
    move-object v9, v1

    .line 36
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 37
    .line 38
    .line 39
    return-void
.end method
