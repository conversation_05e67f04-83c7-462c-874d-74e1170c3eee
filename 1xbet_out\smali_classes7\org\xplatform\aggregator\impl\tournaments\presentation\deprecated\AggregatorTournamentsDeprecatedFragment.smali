.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;
.super Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0090\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\t\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u0000 ^2\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001_B\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u0017\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0006\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\u000c\u001a\u00020\u00072\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u000f\u0010\u000e\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u0004J\u001d\u0010\u0012\u001a\u00020\u00072\u000c\u0010\u0011\u001a\u0008\u0012\u0004\u0012\u00020\u00100\u000fH\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0017\u0010\u0016\u001a\u00020\u00072\u0006\u0010\u0015\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u000f\u0010\u0018\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0004J\u000f\u0010\u0019\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0019\u0010\u0004J\u000f\u0010\u001a\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u0008\u001a\u0010\u0004J\u000f\u0010\u001b\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u0008\u001b\u0010\u0004J\u000f\u0010\u001d\u001a\u00020\u001cH\u0010\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u000f\u0010 \u001a\u00020\u001fH\u0014\u00a2\u0006\u0004\u0008 \u0010!J!\u0010&\u001a\u00020\u00072\u0006\u0010#\u001a\u00020\"2\u0008\u0010%\u001a\u0004\u0018\u00010$H\u0016\u00a2\u0006\u0004\u0008&\u0010\'J\u0019\u0010(\u001a\u00020\u00072\u0008\u0010%\u001a\u0004\u0018\u00010$H\u0014\u00a2\u0006\u0004\u0008(\u0010)J\u000f\u0010*\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008*\u0010\u0004J\u000f\u0010+\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u0008+\u0010\u0004R\u001b\u00101\u001a\u00020,8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008-\u0010.\u001a\u0004\u0008/\u00100R+\u0010:\u001a\u0002022\u0006\u00103\u001a\u0002028B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u00084\u00105\u001a\u0004\u00086\u00107\"\u0004\u00088\u00109R\"\u0010B\u001a\u00020;8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008<\u0010=\u001a\u0004\u0008>\u0010?\"\u0004\u0008@\u0010AR\u001b\u0010G\u001a\u00020\u00028TX\u0094\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008C\u0010D\u001a\u0004\u0008E\u0010FR\u001b\u0010L\u001a\u00020H8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008I\u0010D\u001a\u0004\u0008J\u0010KR\u001b\u0010Q\u001a\u00020M8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008N\u0010D\u001a\u0004\u0008O\u0010PR\u001a\u0010W\u001a\u00020R8\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008S\u0010T\u001a\u0004\u0008U\u0010VR\u001a\u0010]\u001a\u00020X8\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008Y\u0010Z\u001a\u0004\u0008[\u0010\\\u00a8\u0006`"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;",
        "<init>",
        "()V",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "lottieConfig",
        "",
        "Z3",
        "(Lorg/xbet/uikit/components/lottie_empty/n;)V",
        "",
        "visible",
        "R3",
        "(Z)V",
        "W3",
        "",
        "Lkb1/b;",
        "bannerList",
        "U3",
        "(Ljava/util/List;)V",
        "",
        "deeplink",
        "V3",
        "(Ljava/lang/String;)V",
        "Y3",
        "u2",
        "onResume",
        "onPause",
        "Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "K2",
        "()Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "N2",
        "()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "Landroid/view/View;",
        "view",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onViewCreated",
        "(Landroid/view/View;Landroid/os/Bundle;)V",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "onDestroyView",
        "LS91/M;",
        "o0",
        "LRc/c;",
        "O3",
        "()LS91/M;",
        "viewBinding",
        "",
        "<set-?>",
        "b1",
        "LeX0/f;",
        "M3",
        "()J",
        "X3",
        "(J)V",
        "bannerIdToOpen",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "k1",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "Q3",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "v1",
        "Lkotlin/j;",
        "P3",
        "()Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;",
        "viewModel",
        "Ldb1/e;",
        "x1",
        "N3",
        "()Ldb1/e;",
        "bannersAdapter",
        "Lorg/xplatform/aggregator/impl/gifts/f;",
        "y1",
        "L3",
        "()Lorg/xplatform/aggregator/impl/gifts/f;",
        "appBarObserver",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "F1",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "O2",
        "()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "searchScreenType",
        "Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "H1",
        "Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "M2",
        "()Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "depositScreenType",
        "I1",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final I1:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic P1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final F1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b1:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public k1:Lorg/xbet/ui_common/viewmodel/core/l;

.field public final o0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    .line 4
    .line 5
    const-string v2, "viewBinding"

    .line 6
    .line 7
    const-string v3, "getViewBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentAggregatorTournamentsDeprecatedBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "bannerIdToOpen"

    .line 20
    .line 21
    const-string v5, "getBannerIdToOpen()J"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    const/4 v2, 0x2

    .line 31
    new-array v2, v2, [Lkotlin/reflect/m;

    .line 32
    .line 33
    aput-object v0, v2, v4

    .line 34
    .line 35
    const/4 v0, 0x1

    .line 36
    aput-object v1, v2, v0

    .line 37
    .line 38
    sput-object v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->P1:[Lkotlin/reflect/m;

    .line 39
    .line 40
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$a;

    .line 41
    .line 42
    const/4 v1, 0x0

    .line 43
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->I1:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$a;

    .line 47
    .line 48
    return-void
.end method

.method public constructor <init>()V
    .locals 7

    .line 1
    sget v0, Lu91/c;->fragment_aggregator_tournaments_deprecated:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$viewBinding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$viewBinding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->o0:LRc/c;

    .line 13
    .line 14
    new-instance v1, LeX0/f;

    .line 15
    .line 16
    const/4 v5, 0x2

    .line 17
    const/4 v6, 0x0

    .line 18
    const-string v2, "BANNER_ITEM"

    .line 19
    .line 20
    const-wide/16 v3, 0x0

    .line 21
    .line 22
    invoke-direct/range {v1 .. v6}, LeX0/f;-><init>(Ljava/lang/String;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 23
    .line 24
    .line 25
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->b1:LeX0/f;

    .line 26
    .line 27
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/c;

    .line 28
    .line 29
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/c;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)V

    .line 30
    .line 31
    .line 32
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$special$$inlined$viewModels$default$1;

    .line 33
    .line 34
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 35
    .line 36
    .line 37
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 38
    .line 39
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$special$$inlined$viewModels$default$2;

    .line 40
    .line 41
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 42
    .line 43
    .line 44
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    const-class v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 49
    .line 50
    invoke-static {v3}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$special$$inlined$viewModels$default$3;

    .line 55
    .line 56
    invoke-direct {v4, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 57
    .line 58
    .line 59
    new-instance v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$special$$inlined$viewModels$default$4;

    .line 60
    .line 61
    invoke-direct {v5, v6, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 62
    .line 63
    .line 64
    invoke-static {p0, v3, v4, v5, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->v1:Lkotlin/j;

    .line 69
    .line 70
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/d;

    .line 71
    .line 72
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/d;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)V

    .line 73
    .line 74
    .line 75
    invoke-static {v2, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 76
    .line 77
    .line 78
    move-result-object v0

    .line 79
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->x1:Lkotlin/j;

    .line 80
    .line 81
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/e;

    .line 82
    .line 83
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/e;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)V

    .line 84
    .line 85
    .line 86
    invoke-static {v2, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 87
    .line 88
    .line 89
    move-result-object v0

    .line 90
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->y1:Lkotlin/j;

    .line 91
    .line 92
    sget-object v0, Lorg/xbet/analytics/domain/scope/search/SearchScreenType;->AGGREGATOR_TOURNAMENTS:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 93
    .line 94
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->F1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 95
    .line 96
    sget-object v0, Lorg/xbet/analytics/domain/scope/DepositCallScreenType;->AggregatorTournaments:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 97
    .line 98
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->H1:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 99
    .line 100
    return-void
.end method

.method public static final synthetic A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->W3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic B3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;J)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->X3(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->Y3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->Z3(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final E3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)Lorg/xplatform/aggregator/impl/gifts/f;
    .locals 7

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/f;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->N3()Ldb1/e;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$appBarObserver$2$1;

    .line 8
    .line 9
    invoke-direct {v2, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$appBarObserver$2$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/f;

    .line 13
    .line 14
    invoke-direct {v3, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/f;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)V

    .line 15
    .line 16
    .line 17
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/g;

    .line 18
    .line 19
    invoke-direct {v4, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/g;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)V

    .line 20
    .line 21
    .line 22
    new-instance v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/h;

    .line 23
    .line 24
    invoke-direct {v5, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/h;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)V

    .line 25
    .line 26
    .line 27
    new-instance v6, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/i;

    .line 28
    .line 29
    invoke-direct {v6, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/i;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)V

    .line 30
    .line 31
    .line 32
    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/gifts/f;-><init>(Landroidx/recyclerview/widget/RecyclerView$Adapter;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;LOc/n;)V

    .line 33
    .line 34
    .line 35
    return-object v0
.end method

.method public static final F3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->W3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final G3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->W3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final H3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->W3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final I3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;III)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->W3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final J3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)Ldb1/e;
    .locals 2

    .line 1
    new-instance v0, Ldb1/e;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/j;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/j;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {v0, v1}, Ldb1/e;-><init>(Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method

.method public static final K3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lkb1/b;I)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->P3()Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, v0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->C4(Ljava/lang/String;Lkb1/b;I)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method private final L3()Lorg/xplatform/aggregator/impl/gifts/f;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->y1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/f;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final S3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->P3()Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->E4(Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method

.method public static final T3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->P3()Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->F4(Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method

.method private final V3(Ljava/lang/String;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0, p1}, Lorg/xbet/ui_common/utils/h;->l(Landroid/content/Context;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method private final W3()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->O3()LS91/M;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/M;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$b;

    .line 8
    .line 9
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$b;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0, v1}, Landroid/view/View;->addOnLayoutChangeListener(Landroid/view/View$OnLayoutChangeListener;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method private final Y3()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->P2()LzX0/k;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Ly01/g;

    .line 6
    .line 7
    sget-object v2, Ly01/i$c;->a:Ly01/i$c;

    .line 8
    .line 9
    sget v3, Lpb/k;->access_denied_with_bonus_currency_message:I

    .line 10
    .line 11
    invoke-virtual {p0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    const/16 v8, 0x3c

    .line 16
    .line 17
    const/4 v9, 0x0

    .line 18
    const/4 v4, 0x0

    .line 19
    const/4 v5, 0x0

    .line 20
    const/4 v6, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 23
    .line 24
    .line 25
    const/16 v10, 0x1fc

    .line 26
    .line 27
    const/4 v11, 0x0

    .line 28
    const/4 v3, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v8, 0x0

    .line 32
    move-object v2, p0

    .line 33
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method private final Z3(Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->O3()LS91/M;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/M;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->O3()LS91/M;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iget-object v0, v0, LS91/M;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 17
    .line 18
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->O3()LS91/M;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    iget-object p1, p1, LS91/M;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 26
    .line 27
    const/4 v0, 0x0

    .line 28
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 29
    .line 30
    .line 31
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->O3()LS91/M;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    iget-object p1, p1, LS91/M;->e:Lcom/google/android/material/appbar/CollapsingToolbarLayout;

    .line 36
    .line 37
    invoke-virtual {p0, p1, v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->I2(Lcom/google/android/material/appbar/CollapsingToolbarLayout;Z)V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public static final a4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->Q3()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic l3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)Lorg/xplatform/aggregator/impl/gifts/f;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->E3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)Lorg/xplatform/aggregator/impl/gifts/f;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic m3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->G3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;II)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic n3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->a4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic o3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->H3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;II)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic p3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)Ldb1/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->J3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)Ldb1/e;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->T3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->S3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->F3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;II)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic t3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;III)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->I3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;III)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic u3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lkb1/b;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->K3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lkb1/b;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic v3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)Ldb1/e;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->N3()Ldb1/e;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)LS91/M;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->O3()LS91/M;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->R3(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic y3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->U3(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic z3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->V3(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public K2()Lorg/xbet/uikit/components/accountselection/AccountSelection;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->O3()LS91/M;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/M;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 6
    .line 7
    return-object v0
.end method

.method public M2()Lorg/xbet/analytics/domain/scope/DepositCallScreenType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->H1:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final M3()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->b1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->P1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/f;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Long;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v0

    .line 16
    return-wide v0
.end method

.method public N2()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->O3()LS91/M;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/M;->h:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 6
    .line 7
    return-object v0
.end method

.method public final N3()Ldb1/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->x1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ldb1/e;

    .line 8
    .line 9
    return-object v0
.end method

.method public O2()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->F1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final O3()LS91/M;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->o0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->P1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/M;

    .line 13
    .line 14
    return-object v0
.end method

.method public P3()Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->v1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public bridge synthetic Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->P3()Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final Q3()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->k1:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final R3(Z)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->O3()LS91/M;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/M;->d:Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;

    .line 6
    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    const/4 p1, 0x0

    .line 10
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {v0}, Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;->getRegistrationButton()Lorg/xbet/uikit/components/buttons/DSButton;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/a;

    .line 18
    .line 19
    invoke-direct {v1, p0, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/a;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;)V

    .line 20
    .line 21
    .line 22
    const/4 v2, 0x0

    .line 23
    const/4 v3, 0x1

    .line 24
    invoke-static {p1, v2, v1, v3, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 25
    .line 26
    .line 27
    invoke-virtual {v0}, Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;->getAuthorizationButton()Lorg/xbet/uikit/components/buttons/DSButton;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/b;

    .line 32
    .line 33
    invoke-direct {v1, p0, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/b;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;)V

    .line 34
    .line 35
    .line 36
    invoke-static {p1, v2, v1, v3, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 37
    .line 38
    .line 39
    return-void

    .line 40
    :cond_0
    const/16 p1, 0x8

    .line 41
    .line 42
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public final U3(Ljava/util/List;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lkb1/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_3

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->M3()J

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    const-wide/16 v2, 0x0

    .line 12
    .line 13
    cmp-long v4, v0, v2

    .line 14
    .line 15
    if-eqz v4, :cond_3

    .line 16
    .line 17
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    if-eqz v1, :cond_1

    .line 26
    .line 27
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    move-object v4, v1

    .line 32
    check-cast v4, Lkb1/b;

    .line 33
    .line 34
    invoke-virtual {v4}, Lkb1/b;->f()I

    .line 35
    .line 36
    .line 37
    move-result v4

    .line 38
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->M3()J

    .line 39
    .line 40
    .line 41
    move-result-wide v5

    .line 42
    long-to-int v6, v5

    .line 43
    if-ne v4, v6, :cond_0

    .line 44
    .line 45
    goto :goto_0

    .line 46
    :cond_1
    const/4 v1, 0x0

    .line 47
    :goto_0
    check-cast v1, Lkb1/b;

    .line 48
    .line 49
    if-eqz v1, :cond_2

    .line 50
    .line 51
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->P3()Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    const-class v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    .line 56
    .line 57
    invoke-virtual {v4}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v4

    .line 61
    invoke-interface {p1, v1}, Ljava/util/List;->indexOf(Ljava/lang/Object;)I

    .line 62
    .line 63
    .line 64
    move-result p1

    .line 65
    invoke-virtual {v0, v4, v1, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->C4(Ljava/lang/String;Lkb1/b;I)V

    .line 66
    .line 67
    .line 68
    :cond_2
    invoke-virtual {p0, v2, v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->X3(J)V

    .line 69
    .line 70
    .line 71
    :cond_3
    return-void
.end method

.method public final X3(J)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->b1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->P1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1, p2}, LeX0/f;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;J)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onDestroyView()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->O3()LS91/M;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/M;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 9
    .line 10
    .line 11
    invoke-super {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onDestroyView()V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public onPause()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->L3()Lorg/xplatform/aggregator/impl/gifts/f;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/f;->l()V

    .line 6
    .line 7
    .line 8
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onPause()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onResume()V
    .locals 1

    .line 1
    invoke-super {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->L3()Lorg/xplatform/aggregator/impl/gifts/f;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/f;->k()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onViewCreated(Landroid/view/View;Landroid/os/Bundle;)V
    .locals 0
    .param p1    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1, p2}, LXW0/a;->onViewCreated(Landroid/view/View;Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->P3()Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->B4()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->P3()Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->A4()V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 14

    .line 1
    invoke-super {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->O3()LS91/M;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object p1, p1, LS91/M;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->N3()Ldb1/e;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p1}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    sget v1, Lpb/f;->space_16:I

    .line 22
    .line 23
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 24
    .line 25
    .line 26
    move-result v3

    .line 27
    invoke-virtual {p1}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    sget v1, LlZ0/g;->medium_horizontal_margin_dynamic:I

    .line 32
    .line 33
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 34
    .line 35
    .line 36
    move-result v4

    .line 37
    new-instance v2, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;

    .line 38
    .line 39
    const/16 v12, 0x1c4

    .line 40
    .line 41
    const/4 v13, 0x0

    .line 42
    const/4 v5, 0x0

    .line 43
    const/4 v8, 0x1

    .line 44
    const/4 v9, 0x0

    .line 45
    const/4 v10, 0x0

    .line 46
    const/4 v11, 0x0

    .line 47
    move v6, v4

    .line 48
    move v7, v3

    .line 49
    invoke-direct/range {v2 .. v13}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;-><init>(IIIIIILkotlin/jvm/functions/Function1;Lorg/xbet/ui_common/viewcomponents/recycler/decorators/SpacingItemDecorationBias;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {p1, v2}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 53
    .line 54
    .line 55
    return-void
.end method

.method public u2()V
    .locals 1

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/core/presentation/f;->a(Landroidx/fragment/app/Fragment;)LN91/k;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0, p0}, LN91/k;->e(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public v2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-super {v0}, LXW0/a;->v2()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->P3()Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->w4()Lkotlinx/coroutines/flow/f0;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    new-instance v6, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$1;

    .line 15
    .line 16
    const/4 v1, 0x0

    .line 17
    invoke-direct {v6, v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 21
    .line 22
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 23
    .line 24
    .line 25
    move-result-object v4

    .line 26
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 27
    .line 28
    .line 29
    move-result-object v11

    .line 30
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 31
    .line 32
    const/4 v7, 0x0

    .line 33
    move-object v5, v10

    .line 34
    invoke-direct/range {v2 .. v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 35
    .line 36
    .line 37
    const/4 v15, 0x3

    .line 38
    const/16 v16, 0x0

    .line 39
    .line 40
    const/4 v12, 0x0

    .line 41
    const/4 v13, 0x0

    .line 42
    move-object v14, v2

    .line 43
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->P3()Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->y4()Lkotlinx/coroutines/flow/f0;

    .line 51
    .line 52
    .line 53
    move-result-object v8

    .line 54
    new-instance v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;

    .line 55
    .line 56
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lkotlin/coroutines/e;)V

    .line 57
    .line 58
    .line 59
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 60
    .line 61
    .line 62
    move-result-object v9

    .line 63
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 64
    .line 65
    .line 66
    move-result-object v2

    .line 67
    new-instance v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 68
    .line 69
    move-object v7, v5

    .line 70
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 71
    .line 72
    .line 73
    const/4 v6, 0x3

    .line 74
    const/4 v7, 0x0

    .line 75
    const/4 v3, 0x0

    .line 76
    const/4 v4, 0x0

    .line 77
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 78
    .line 79
    .line 80
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->P3()Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->x4()Lkotlinx/coroutines/flow/Z;

    .line 85
    .line 86
    .line 87
    move-result-object v8

    .line 88
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/w;

    .line 89
    .line 90
    .line 91
    move-result-object v9

    .line 92
    new-instance v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$3;

    .line 93
    .line 94
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$3;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lkotlin/coroutines/e;)V

    .line 95
    .line 96
    .line 97
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 98
    .line 99
    .line 100
    move-result-object v2

    .line 101
    new-instance v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 102
    .line 103
    move-object v7, v5

    .line 104
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 105
    .line 106
    .line 107
    const/4 v7, 0x0

    .line 108
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 109
    .line 110
    .line 111
    return-void
.end method
