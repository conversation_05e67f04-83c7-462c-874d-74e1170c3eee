.class public final synthetic Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

.field public final synthetic b:Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;

.field public final synthetic c:J


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;J)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/a;->a:Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/a;->b:Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;

    iput-wide p3, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/a;->c:J

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/a;->a:Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/a;->b:Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;

    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/a;->c:J

    move-object v4, p1

    check-cast v4, Ljava/util/List;

    move-object v5, p2

    check-cast v5, Ljava/util/List;

    invoke-static/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->a(Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;JLjava/util/List;Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method
