.class public final Li81/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000e\n\u0002\u00088\u0008\u0086\u0008\u0018\u00002\u00020\u0001B\u00a3\u0001\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\u0008\u001a\u00020\u0006\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000c\u001a\u00020\u000b\u0012\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\r\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010\"\u001a\u00020\u0002\u0012\u000c\u0010$\u001a\u0008\u0012\u0004\u0012\u00020#0\r\u00a2\u0006\u0004\u0008%\u0010&J\u00d0\u0001\u0010\'\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u00062\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u00062\u0008\u0008\u0002\u0010\n\u001a\u00020\t2\u0008\u0008\u0002\u0010\u000c\u001a\u00020\u000b2\u000e\u0008\u0002\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\r2\u0008\u0008\u0002\u0010\u0011\u001a\u00020\u00102\u0008\u0008\u0002\u0010\u0013\u001a\u00020\u00122\u0008\u0008\u0002\u0010\u0015\u001a\u00020\u00142\u0008\u0008\u0002\u0010\u0017\u001a\u00020\u00162\u0008\u0008\u0002\u0010\u0019\u001a\u00020\u00182\u0008\u0008\u0002\u0010\u001b\u001a\u00020\u001a2\u0008\u0008\u0002\u0010\u001d\u001a\u00020\u001c2\u0008\u0008\u0002\u0010\u001f\u001a\u00020\u001e2\u0008\u0008\u0002\u0010!\u001a\u00020 2\u0008\u0008\u0002\u0010\"\u001a\u00020\u00022\u000e\u0008\u0002\u0010$\u001a\u0008\u0012\u0004\u0012\u00020#0\rH\u00c6\u0001\u00a2\u0006\u0004\u0008\'\u0010(J\u0010\u0010*\u001a\u00020)H\u00d6\u0001\u00a2\u0006\u0004\u0008*\u0010+J\u0010\u0010,\u001a\u00020\u001aH\u00d6\u0001\u00a2\u0006\u0004\u0008,\u0010-J\u001a\u0010/\u001a\u00020\u00062\u0008\u0010.\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008/\u00100R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\'\u00101\u001a\u0004\u00082\u00103R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u00084\u00105\u001a\u0004\u00086\u00107R\u0017\u0010\u0007\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u00088\u00109\u001a\u0004\u0008:\u0010;R\u0017\u0010\u0008\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008<\u00109\u001a\u0004\u0008=\u0010;R\u0017\u0010\n\u001a\u00020\t8\u0006\u00a2\u0006\u000c\n\u0004\u0008>\u0010?\u001a\u0004\u0008<\u0010@R\u0017\u0010\u000c\u001a\u00020\u000b8\u0006\u00a2\u0006\u000c\n\u0004\u0008A\u0010B\u001a\u0004\u0008>\u0010CR\u001d\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\r8\u0006\u00a2\u0006\u000c\n\u0004\u0008D\u0010E\u001a\u0004\u0008F\u0010GR\u0017\u0010\u0011\u001a\u00020\u00108\u0006\u00a2\u0006\u000c\n\u0004\u0008H\u0010I\u001a\u0004\u0008A\u0010JR\u0017\u0010\u0013\u001a\u00020\u00128\u0006\u00a2\u0006\u000c\n\u0004\u0008K\u0010L\u001a\u0004\u0008D\u0010MR\u0017\u0010\u0015\u001a\u00020\u00148\u0006\u00a2\u0006\u000c\n\u0004\u0008N\u0010O\u001a\u0004\u00088\u0010PR\u0017\u0010\u0017\u001a\u00020\u00168\u0006\u00a2\u0006\u000c\n\u0004\u0008Q\u0010R\u001a\u0004\u0008H\u0010SR\u0017\u0010\u0019\u001a\u00020\u00188\u0006\u00a2\u0006\u000c\n\u0004\u0008T\u0010U\u001a\u0004\u0008K\u0010VR\u0017\u0010\u001b\u001a\u00020\u001a8\u0006\u00a2\u0006\u000c\n\u0004\u0008W\u0010X\u001a\u0004\u0008Y\u0010-R\u0017\u0010\u001d\u001a\u00020\u001c8\u0006\u00a2\u0006\u000c\n\u0004\u00082\u0010Z\u001a\u0004\u0008[\u0010\\R\u0017\u0010\u001f\u001a\u00020\u001e8\u0006\u00a2\u0006\u000c\n\u0004\u00086\u0010]\u001a\u0004\u0008Q\u0010^R\u0017\u0010!\u001a\u00020 8\u0006\u00a2\u0006\u000c\n\u0004\u0008:\u0010_\u001a\u0004\u0008N\u0010`R\u0017\u0010\"\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008F\u00101\u001a\u0004\u0008T\u00103R\u001d\u0010$\u001a\u0008\u0012\u0004\u0012\u00020#0\r8\u0006\u00a2\u0006\u000c\n\u0004\u0008=\u0010E\u001a\u0004\u0008W\u0010G\u00a8\u0006a"
    }
    d2 = {
        "Li81/a;",
        "",
        "",
        "id",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "kind",
        "",
        "meParticipating",
        "providerTournamentWithStages",
        "Lj81/a;",
        "blockHeader",
        "Lk81/a;",
        "blockPrize",
        "",
        "Ll81/a;",
        "productsList",
        "Lm81/a;",
        "blockResult",
        "Ln81/c;",
        "blockRule",
        "Ln81/a;",
        "blockFullRule",
        "Ln81/b;",
        "blockRuleStage",
        "Lo81/a;",
        "blockStages",
        "",
        "type",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;",
        "userActionButtonType",
        "Lh81/a;",
        "buttons",
        "Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;",
        "buttonStatus",
        "crmParticipantCurrentStage",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "games",
        "<init>",
        "(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;ZZLj81/a;Lk81/a;Ljava/util/List;Lm81/a;Ln81/c;Ln81/a;Ln81/b;Lo81/a;ILorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;JLjava/util/List;)V",
        "a",
        "(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;ZZLj81/a;Lk81/a;Ljava/util/List;Lm81/a;Ln81/c;Ln81/a;Ln81/b;Lo81/a;ILorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;JLjava/util/List;)Li81/a;",
        "",
        "toString",
        "()Ljava/lang/String;",
        "hashCode",
        "()I",
        "other",
        "equals",
        "(Ljava/lang/Object;)Z",
        "J",
        "n",
        "()J",
        "b",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "o",
        "()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "c",
        "Z",
        "p",
        "()Z",
        "d",
        "r",
        "e",
        "Lj81/a;",
        "()Lj81/a;",
        "f",
        "Lk81/a;",
        "()Lk81/a;",
        "g",
        "Ljava/util/List;",
        "q",
        "()Ljava/util/List;",
        "h",
        "Lm81/a;",
        "()Lm81/a;",
        "i",
        "Ln81/c;",
        "()Ln81/c;",
        "j",
        "Ln81/a;",
        "()Ln81/a;",
        "k",
        "Ln81/b;",
        "()Ln81/b;",
        "l",
        "Lo81/a;",
        "()Lo81/a;",
        "m",
        "I",
        "s",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;",
        "t",
        "()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;",
        "Lh81/a;",
        "()Lh81/a;",
        "Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;",
        "()Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:J

.field public final b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Z

.field public final d:Z

.field public final e:Lj81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lk81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ll81/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lm81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Ln81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Ln81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Ln81/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lo81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:I

.field public final n:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:Lh81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q:J

.field public final r:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;ZZLj81/a;Lk81/a;Ljava/util/List;Lm81/a;Ln81/c;Ln81/a;Ln81/b;Lo81/a;ILorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;JLjava/util/List;)V
    .locals 0
    .param p3    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lj81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lk81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lm81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Ln81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Ln81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Ln81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lo81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lh81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
            "ZZ",
            "Lj81/a;",
            "Lk81/a;",
            "Ljava/util/List<",
            "Ll81/a;",
            ">;",
            "Lm81/a;",
            "Ln81/c;",
            "Ln81/a;",
            "Ln81/b;",
            "Lo81/a;",
            "I",
            "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;",
            "Lh81/a;",
            "Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;",
            "J",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-wide p1, p0, Li81/a;->a:J

    .line 5
    .line 6
    iput-object p3, p0, Li81/a;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 7
    .line 8
    iput-boolean p4, p0, Li81/a;->c:Z

    .line 9
    .line 10
    iput-boolean p5, p0, Li81/a;->d:Z

    .line 11
    .line 12
    iput-object p6, p0, Li81/a;->e:Lj81/a;

    .line 13
    .line 14
    iput-object p7, p0, Li81/a;->f:Lk81/a;

    .line 15
    .line 16
    iput-object p8, p0, Li81/a;->g:Ljava/util/List;

    .line 17
    .line 18
    iput-object p9, p0, Li81/a;->h:Lm81/a;

    .line 19
    .line 20
    iput-object p10, p0, Li81/a;->i:Ln81/c;

    .line 21
    .line 22
    iput-object p11, p0, Li81/a;->j:Ln81/a;

    .line 23
    .line 24
    iput-object p12, p0, Li81/a;->k:Ln81/b;

    .line 25
    .line 26
    iput-object p13, p0, Li81/a;->l:Lo81/a;

    .line 27
    .line 28
    iput p14, p0, Li81/a;->m:I

    .line 29
    .line 30
    iput-object p15, p0, Li81/a;->n:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 31
    .line 32
    move-object/from16 p1, p16

    .line 33
    .line 34
    iput-object p1, p0, Li81/a;->o:Lh81/a;

    .line 35
    .line 36
    move-object/from16 p1, p17

    .line 37
    .line 38
    iput-object p1, p0, Li81/a;->p:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    .line 39
    .line 40
    move-wide/from16 p1, p18

    .line 41
    .line 42
    iput-wide p1, p0, Li81/a;->q:J

    .line 43
    .line 44
    move-object/from16 p1, p20

    .line 45
    .line 46
    iput-object p1, p0, Li81/a;->r:Ljava/util/List;

    .line 47
    .line 48
    return-void
.end method

.method public static synthetic b(Li81/a;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;ZZLj81/a;Lk81/a;Ljava/util/List;Lm81/a;Ln81/c;Ln81/a;Ln81/b;Lo81/a;ILorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;JLjava/util/List;ILjava/lang/Object;)Li81/a;
    .locals 19

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move/from16 v1, p21

    .line 4
    .line 5
    and-int/lit8 v2, v1, 0x1

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    iget-wide v2, v0, Li81/a;->a:J

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    move-wide/from16 v2, p1

    .line 13
    .line 14
    :goto_0
    and-int/lit8 v4, v1, 0x2

    .line 15
    .line 16
    if-eqz v4, :cond_1

    .line 17
    .line 18
    iget-object v4, v0, Li81/a;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 19
    .line 20
    goto :goto_1

    .line 21
    :cond_1
    move-object/from16 v4, p3

    .line 22
    .line 23
    :goto_1
    and-int/lit8 v5, v1, 0x4

    .line 24
    .line 25
    if-eqz v5, :cond_2

    .line 26
    .line 27
    iget-boolean v5, v0, Li81/a;->c:Z

    .line 28
    .line 29
    goto :goto_2

    .line 30
    :cond_2
    move/from16 v5, p4

    .line 31
    .line 32
    :goto_2
    and-int/lit8 v6, v1, 0x8

    .line 33
    .line 34
    if-eqz v6, :cond_3

    .line 35
    .line 36
    iget-boolean v6, v0, Li81/a;->d:Z

    .line 37
    .line 38
    goto :goto_3

    .line 39
    :cond_3
    move/from16 v6, p5

    .line 40
    .line 41
    :goto_3
    and-int/lit8 v7, v1, 0x10

    .line 42
    .line 43
    if-eqz v7, :cond_4

    .line 44
    .line 45
    iget-object v7, v0, Li81/a;->e:Lj81/a;

    .line 46
    .line 47
    goto :goto_4

    .line 48
    :cond_4
    move-object/from16 v7, p6

    .line 49
    .line 50
    :goto_4
    and-int/lit8 v8, v1, 0x20

    .line 51
    .line 52
    if-eqz v8, :cond_5

    .line 53
    .line 54
    iget-object v8, v0, Li81/a;->f:Lk81/a;

    .line 55
    .line 56
    goto :goto_5

    .line 57
    :cond_5
    move-object/from16 v8, p7

    .line 58
    .line 59
    :goto_5
    and-int/lit8 v9, v1, 0x40

    .line 60
    .line 61
    if-eqz v9, :cond_6

    .line 62
    .line 63
    iget-object v9, v0, Li81/a;->g:Ljava/util/List;

    .line 64
    .line 65
    goto :goto_6

    .line 66
    :cond_6
    move-object/from16 v9, p8

    .line 67
    .line 68
    :goto_6
    and-int/lit16 v10, v1, 0x80

    .line 69
    .line 70
    if-eqz v10, :cond_7

    .line 71
    .line 72
    iget-object v10, v0, Li81/a;->h:Lm81/a;

    .line 73
    .line 74
    goto :goto_7

    .line 75
    :cond_7
    move-object/from16 v10, p9

    .line 76
    .line 77
    :goto_7
    and-int/lit16 v11, v1, 0x100

    .line 78
    .line 79
    if-eqz v11, :cond_8

    .line 80
    .line 81
    iget-object v11, v0, Li81/a;->i:Ln81/c;

    .line 82
    .line 83
    goto :goto_8

    .line 84
    :cond_8
    move-object/from16 v11, p10

    .line 85
    .line 86
    :goto_8
    and-int/lit16 v12, v1, 0x200

    .line 87
    .line 88
    if-eqz v12, :cond_9

    .line 89
    .line 90
    iget-object v12, v0, Li81/a;->j:Ln81/a;

    .line 91
    .line 92
    goto :goto_9

    .line 93
    :cond_9
    move-object/from16 v12, p11

    .line 94
    .line 95
    :goto_9
    and-int/lit16 v13, v1, 0x400

    .line 96
    .line 97
    if-eqz v13, :cond_a

    .line 98
    .line 99
    iget-object v13, v0, Li81/a;->k:Ln81/b;

    .line 100
    .line 101
    goto :goto_a

    .line 102
    :cond_a
    move-object/from16 v13, p12

    .line 103
    .line 104
    :goto_a
    and-int/lit16 v14, v1, 0x800

    .line 105
    .line 106
    if-eqz v14, :cond_b

    .line 107
    .line 108
    iget-object v14, v0, Li81/a;->l:Lo81/a;

    .line 109
    .line 110
    goto :goto_b

    .line 111
    :cond_b
    move-object/from16 v14, p13

    .line 112
    .line 113
    :goto_b
    and-int/lit16 v15, v1, 0x1000

    .line 114
    .line 115
    if-eqz v15, :cond_c

    .line 116
    .line 117
    iget v15, v0, Li81/a;->m:I

    .line 118
    .line 119
    goto :goto_c

    .line 120
    :cond_c
    move/from16 v15, p14

    .line 121
    .line 122
    :goto_c
    move-wide/from16 v16, v2

    .line 123
    .line 124
    and-int/lit16 v2, v1, 0x2000

    .line 125
    .line 126
    if-eqz v2, :cond_d

    .line 127
    .line 128
    iget-object v2, v0, Li81/a;->n:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 129
    .line 130
    goto :goto_d

    .line 131
    :cond_d
    move-object/from16 v2, p15

    .line 132
    .line 133
    :goto_d
    and-int/lit16 v3, v1, 0x4000

    .line 134
    .line 135
    if-eqz v3, :cond_e

    .line 136
    .line 137
    iget-object v3, v0, Li81/a;->o:Lh81/a;

    .line 138
    .line 139
    goto :goto_e

    .line 140
    :cond_e
    move-object/from16 v3, p16

    .line 141
    .line 142
    :goto_e
    const v18, 0x8000

    .line 143
    .line 144
    .line 145
    and-int v18, v1, v18

    .line 146
    .line 147
    if-eqz v18, :cond_f

    .line 148
    .line 149
    iget-object v1, v0, Li81/a;->p:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    .line 150
    .line 151
    goto :goto_f

    .line 152
    :cond_f
    move-object/from16 v1, p17

    .line 153
    .line 154
    :goto_f
    const/high16 v18, 0x10000

    .line 155
    .line 156
    and-int v18, p21, v18

    .line 157
    .line 158
    move-object/from16 p2, v1

    .line 159
    .line 160
    move-object/from16 p1, v2

    .line 161
    .line 162
    if-eqz v18, :cond_10

    .line 163
    .line 164
    iget-wide v1, v0, Li81/a;->q:J

    .line 165
    .line 166
    goto :goto_10

    .line 167
    :cond_10
    move-wide/from16 v1, p18

    .line 168
    .line 169
    :goto_10
    const/high16 v18, 0x20000

    .line 170
    .line 171
    and-int v18, p21, v18

    .line 172
    .line 173
    if-eqz v18, :cond_11

    .line 174
    .line 175
    move-wide/from16 p3, v1

    .line 176
    .line 177
    iget-object v1, v0, Li81/a;->r:Ljava/util/List;

    .line 178
    .line 179
    move-wide/from16 p19, p3

    .line 180
    .line 181
    move-object/from16 p21, v1

    .line 182
    .line 183
    :goto_11
    move-object/from16 p16, p1

    .line 184
    .line 185
    move-object/from16 p18, p2

    .line 186
    .line 187
    move-object/from16 p1, v0

    .line 188
    .line 189
    move-object/from16 p17, v3

    .line 190
    .line 191
    move-object/from16 p4, v4

    .line 192
    .line 193
    move/from16 p5, v5

    .line 194
    .line 195
    move/from16 p6, v6

    .line 196
    .line 197
    move-object/from16 p7, v7

    .line 198
    .line 199
    move-object/from16 p8, v8

    .line 200
    .line 201
    move-object/from16 p9, v9

    .line 202
    .line 203
    move-object/from16 p10, v10

    .line 204
    .line 205
    move-object/from16 p11, v11

    .line 206
    .line 207
    move-object/from16 p12, v12

    .line 208
    .line 209
    move-object/from16 p13, v13

    .line 210
    .line 211
    move-object/from16 p14, v14

    .line 212
    .line 213
    move/from16 p15, v15

    .line 214
    .line 215
    move-wide/from16 p2, v16

    .line 216
    .line 217
    goto :goto_12

    .line 218
    :cond_11
    move-object/from16 p21, p20

    .line 219
    .line 220
    move-wide/from16 p19, v1

    .line 221
    .line 222
    goto :goto_11

    .line 223
    :goto_12
    invoke-virtual/range {p1 .. p21}, Li81/a;->a(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;ZZLj81/a;Lk81/a;Ljava/util/List;Lm81/a;Ln81/c;Ln81/a;Ln81/b;Lo81/a;ILorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;JLjava/util/List;)Li81/a;

    .line 224
    .line 225
    .line 226
    move-result-object v0

    .line 227
    return-object v0
.end method


# virtual methods
.method public final a(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;ZZLj81/a;Lk81/a;Ljava/util/List;Lm81/a;Ln81/c;Ln81/a;Ln81/b;Lo81/a;ILorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;JLjava/util/List;)Li81/a;
    .locals 21
    .param p3    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lj81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lk81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lm81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Ln81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Ln81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Ln81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lo81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lh81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
            "ZZ",
            "Lj81/a;",
            "Lk81/a;",
            "Ljava/util/List<",
            "Ll81/a;",
            ">;",
            "Lm81/a;",
            "Ln81/c;",
            "Ln81/a;",
            "Ln81/b;",
            "Lo81/a;",
            "I",
            "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;",
            "Lh81/a;",
            "Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;",
            "J",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;)",
            "Li81/a;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Li81/a;

    .line 2
    .line 3
    move-wide/from16 v1, p1

    .line 4
    .line 5
    move-object/from16 v3, p3

    .line 6
    .line 7
    move/from16 v4, p4

    .line 8
    .line 9
    move/from16 v5, p5

    .line 10
    .line 11
    move-object/from16 v6, p6

    .line 12
    .line 13
    move-object/from16 v7, p7

    .line 14
    .line 15
    move-object/from16 v8, p8

    .line 16
    .line 17
    move-object/from16 v9, p9

    .line 18
    .line 19
    move-object/from16 v10, p10

    .line 20
    .line 21
    move-object/from16 v11, p11

    .line 22
    .line 23
    move-object/from16 v12, p12

    .line 24
    .line 25
    move-object/from16 v13, p13

    .line 26
    .line 27
    move/from16 v14, p14

    .line 28
    .line 29
    move-object/from16 v15, p15

    .line 30
    .line 31
    move-object/from16 v16, p16

    .line 32
    .line 33
    move-object/from16 v17, p17

    .line 34
    .line 35
    move-wide/from16 v18, p18

    .line 36
    .line 37
    move-object/from16 v20, p20

    .line 38
    .line 39
    invoke-direct/range {v0 .. v20}, Li81/a;-><init>(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;ZZLj81/a;Lk81/a;Ljava/util/List;Lm81/a;Ln81/c;Ln81/a;Ln81/b;Lo81/a;ILorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;JLjava/util/List;)V

    .line 40
    .line 41
    .line 42
    return-object v0
.end method

.method public final c()Ln81/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Li81/a;->j:Ln81/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()Lj81/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Li81/a;->e:Lj81/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e()Lk81/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Li81/a;->f:Lk81/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 7

    .line 1
    const/4 v0, 0x1

    .line 2
    if-ne p0, p1, :cond_0

    .line 3
    .line 4
    return v0

    .line 5
    :cond_0
    instance-of v1, p1, Li81/a;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-nez v1, :cond_1

    .line 9
    .line 10
    return v2

    .line 11
    :cond_1
    check-cast p1, Li81/a;

    .line 12
    .line 13
    iget-wide v3, p0, Li81/a;->a:J

    .line 14
    .line 15
    iget-wide v5, p1, Li81/a;->a:J

    .line 16
    .line 17
    cmp-long v1, v3, v5

    .line 18
    .line 19
    if-eqz v1, :cond_2

    .line 20
    .line 21
    return v2

    .line 22
    :cond_2
    iget-object v1, p0, Li81/a;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 23
    .line 24
    iget-object v3, p1, Li81/a;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 25
    .line 26
    if-eq v1, v3, :cond_3

    .line 27
    .line 28
    return v2

    .line 29
    :cond_3
    iget-boolean v1, p0, Li81/a;->c:Z

    .line 30
    .line 31
    iget-boolean v3, p1, Li81/a;->c:Z

    .line 32
    .line 33
    if-eq v1, v3, :cond_4

    .line 34
    .line 35
    return v2

    .line 36
    :cond_4
    iget-boolean v1, p0, Li81/a;->d:Z

    .line 37
    .line 38
    iget-boolean v3, p1, Li81/a;->d:Z

    .line 39
    .line 40
    if-eq v1, v3, :cond_5

    .line 41
    .line 42
    return v2

    .line 43
    :cond_5
    iget-object v1, p0, Li81/a;->e:Lj81/a;

    .line 44
    .line 45
    iget-object v3, p1, Li81/a;->e:Lj81/a;

    .line 46
    .line 47
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 48
    .line 49
    .line 50
    move-result v1

    .line 51
    if-nez v1, :cond_6

    .line 52
    .line 53
    return v2

    .line 54
    :cond_6
    iget-object v1, p0, Li81/a;->f:Lk81/a;

    .line 55
    .line 56
    iget-object v3, p1, Li81/a;->f:Lk81/a;

    .line 57
    .line 58
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 59
    .line 60
    .line 61
    move-result v1

    .line 62
    if-nez v1, :cond_7

    .line 63
    .line 64
    return v2

    .line 65
    :cond_7
    iget-object v1, p0, Li81/a;->g:Ljava/util/List;

    .line 66
    .line 67
    iget-object v3, p1, Li81/a;->g:Ljava/util/List;

    .line 68
    .line 69
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 70
    .line 71
    .line 72
    move-result v1

    .line 73
    if-nez v1, :cond_8

    .line 74
    .line 75
    return v2

    .line 76
    :cond_8
    iget-object v1, p0, Li81/a;->h:Lm81/a;

    .line 77
    .line 78
    iget-object v3, p1, Li81/a;->h:Lm81/a;

    .line 79
    .line 80
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 81
    .line 82
    .line 83
    move-result v1

    .line 84
    if-nez v1, :cond_9

    .line 85
    .line 86
    return v2

    .line 87
    :cond_9
    iget-object v1, p0, Li81/a;->i:Ln81/c;

    .line 88
    .line 89
    iget-object v3, p1, Li81/a;->i:Ln81/c;

    .line 90
    .line 91
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 92
    .line 93
    .line 94
    move-result v1

    .line 95
    if-nez v1, :cond_a

    .line 96
    .line 97
    return v2

    .line 98
    :cond_a
    iget-object v1, p0, Li81/a;->j:Ln81/a;

    .line 99
    .line 100
    iget-object v3, p1, Li81/a;->j:Ln81/a;

    .line 101
    .line 102
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 103
    .line 104
    .line 105
    move-result v1

    .line 106
    if-nez v1, :cond_b

    .line 107
    .line 108
    return v2

    .line 109
    :cond_b
    iget-object v1, p0, Li81/a;->k:Ln81/b;

    .line 110
    .line 111
    iget-object v3, p1, Li81/a;->k:Ln81/b;

    .line 112
    .line 113
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 114
    .line 115
    .line 116
    move-result v1

    .line 117
    if-nez v1, :cond_c

    .line 118
    .line 119
    return v2

    .line 120
    :cond_c
    iget-object v1, p0, Li81/a;->l:Lo81/a;

    .line 121
    .line 122
    iget-object v3, p1, Li81/a;->l:Lo81/a;

    .line 123
    .line 124
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 125
    .line 126
    .line 127
    move-result v1

    .line 128
    if-nez v1, :cond_d

    .line 129
    .line 130
    return v2

    .line 131
    :cond_d
    iget v1, p0, Li81/a;->m:I

    .line 132
    .line 133
    iget v3, p1, Li81/a;->m:I

    .line 134
    .line 135
    if-eq v1, v3, :cond_e

    .line 136
    .line 137
    return v2

    .line 138
    :cond_e
    iget-object v1, p0, Li81/a;->n:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 139
    .line 140
    iget-object v3, p1, Li81/a;->n:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 141
    .line 142
    if-eq v1, v3, :cond_f

    .line 143
    .line 144
    return v2

    .line 145
    :cond_f
    iget-object v1, p0, Li81/a;->o:Lh81/a;

    .line 146
    .line 147
    iget-object v3, p1, Li81/a;->o:Lh81/a;

    .line 148
    .line 149
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 150
    .line 151
    .line 152
    move-result v1

    .line 153
    if-nez v1, :cond_10

    .line 154
    .line 155
    return v2

    .line 156
    :cond_10
    iget-object v1, p0, Li81/a;->p:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    .line 157
    .line 158
    iget-object v3, p1, Li81/a;->p:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    .line 159
    .line 160
    if-eq v1, v3, :cond_11

    .line 161
    .line 162
    return v2

    .line 163
    :cond_11
    iget-wide v3, p0, Li81/a;->q:J

    .line 164
    .line 165
    iget-wide v5, p1, Li81/a;->q:J

    .line 166
    .line 167
    cmp-long v1, v3, v5

    .line 168
    .line 169
    if-eqz v1, :cond_12

    .line 170
    .line 171
    return v2

    .line 172
    :cond_12
    iget-object v1, p0, Li81/a;->r:Ljava/util/List;

    .line 173
    .line 174
    iget-object p1, p1, Li81/a;->r:Ljava/util/List;

    .line 175
    .line 176
    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 177
    .line 178
    .line 179
    move-result p1

    .line 180
    if-nez p1, :cond_13

    .line 181
    .line 182
    return v2

    .line 183
    :cond_13
    return v0
.end method

.method public final f()Lm81/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Li81/a;->h:Lm81/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g()Ln81/c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Li81/a;->i:Ln81/c;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h()Ln81/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Li81/a;->k:Ln81/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public hashCode()I
    .locals 3

    .line 1
    iget-wide v0, p0, Li81/a;->a:J

    .line 2
    .line 3
    invoke-static {v0, v1}, Lu/l;->a(J)I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    mul-int/lit8 v0, v0, 0x1f

    .line 8
    .line 9
    iget-object v1, p0, Li81/a;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 10
    .line 11
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    add-int/2addr v0, v1

    .line 16
    mul-int/lit8 v0, v0, 0x1f

    .line 17
    .line 18
    iget-boolean v1, p0, Li81/a;->c:Z

    .line 19
    .line 20
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 21
    .line 22
    .line 23
    move-result v1

    .line 24
    add-int/2addr v0, v1

    .line 25
    mul-int/lit8 v0, v0, 0x1f

    .line 26
    .line 27
    iget-boolean v1, p0, Li81/a;->d:Z

    .line 28
    .line 29
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 30
    .line 31
    .line 32
    move-result v1

    .line 33
    add-int/2addr v0, v1

    .line 34
    mul-int/lit8 v0, v0, 0x1f

    .line 35
    .line 36
    iget-object v1, p0, Li81/a;->e:Lj81/a;

    .line 37
    .line 38
    invoke-virtual {v1}, Lj81/a;->hashCode()I

    .line 39
    .line 40
    .line 41
    move-result v1

    .line 42
    add-int/2addr v0, v1

    .line 43
    mul-int/lit8 v0, v0, 0x1f

    .line 44
    .line 45
    iget-object v1, p0, Li81/a;->f:Lk81/a;

    .line 46
    .line 47
    invoke-virtual {v1}, Lk81/a;->hashCode()I

    .line 48
    .line 49
    .line 50
    move-result v1

    .line 51
    add-int/2addr v0, v1

    .line 52
    mul-int/lit8 v0, v0, 0x1f

    .line 53
    .line 54
    iget-object v1, p0, Li81/a;->g:Ljava/util/List;

    .line 55
    .line 56
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 57
    .line 58
    .line 59
    move-result v1

    .line 60
    add-int/2addr v0, v1

    .line 61
    mul-int/lit8 v0, v0, 0x1f

    .line 62
    .line 63
    iget-object v1, p0, Li81/a;->h:Lm81/a;

    .line 64
    .line 65
    invoke-virtual {v1}, Lm81/a;->hashCode()I

    .line 66
    .line 67
    .line 68
    move-result v1

    .line 69
    add-int/2addr v0, v1

    .line 70
    mul-int/lit8 v0, v0, 0x1f

    .line 71
    .line 72
    iget-object v1, p0, Li81/a;->i:Ln81/c;

    .line 73
    .line 74
    invoke-virtual {v1}, Ln81/c;->hashCode()I

    .line 75
    .line 76
    .line 77
    move-result v1

    .line 78
    add-int/2addr v0, v1

    .line 79
    mul-int/lit8 v0, v0, 0x1f

    .line 80
    .line 81
    iget-object v1, p0, Li81/a;->j:Ln81/a;

    .line 82
    .line 83
    invoke-virtual {v1}, Ln81/a;->hashCode()I

    .line 84
    .line 85
    .line 86
    move-result v1

    .line 87
    add-int/2addr v0, v1

    .line 88
    mul-int/lit8 v0, v0, 0x1f

    .line 89
    .line 90
    iget-object v1, p0, Li81/a;->k:Ln81/b;

    .line 91
    .line 92
    invoke-virtual {v1}, Ln81/b;->hashCode()I

    .line 93
    .line 94
    .line 95
    move-result v1

    .line 96
    add-int/2addr v0, v1

    .line 97
    mul-int/lit8 v0, v0, 0x1f

    .line 98
    .line 99
    iget-object v1, p0, Li81/a;->l:Lo81/a;

    .line 100
    .line 101
    invoke-virtual {v1}, Lo81/a;->hashCode()I

    .line 102
    .line 103
    .line 104
    move-result v1

    .line 105
    add-int/2addr v0, v1

    .line 106
    mul-int/lit8 v0, v0, 0x1f

    .line 107
    .line 108
    iget v1, p0, Li81/a;->m:I

    .line 109
    .line 110
    add-int/2addr v0, v1

    .line 111
    mul-int/lit8 v0, v0, 0x1f

    .line 112
    .line 113
    iget-object v1, p0, Li81/a;->n:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 114
    .line 115
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 116
    .line 117
    .line 118
    move-result v1

    .line 119
    add-int/2addr v0, v1

    .line 120
    mul-int/lit8 v0, v0, 0x1f

    .line 121
    .line 122
    iget-object v1, p0, Li81/a;->o:Lh81/a;

    .line 123
    .line 124
    invoke-virtual {v1}, Lh81/a;->hashCode()I

    .line 125
    .line 126
    .line 127
    move-result v1

    .line 128
    add-int/2addr v0, v1

    .line 129
    mul-int/lit8 v0, v0, 0x1f

    .line 130
    .line 131
    iget-object v1, p0, Li81/a;->p:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    .line 132
    .line 133
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 134
    .line 135
    .line 136
    move-result v1

    .line 137
    add-int/2addr v0, v1

    .line 138
    mul-int/lit8 v0, v0, 0x1f

    .line 139
    .line 140
    iget-wide v1, p0, Li81/a;->q:J

    .line 141
    .line 142
    invoke-static {v1, v2}, Lu/l;->a(J)I

    .line 143
    .line 144
    .line 145
    move-result v1

    .line 146
    add-int/2addr v0, v1

    .line 147
    mul-int/lit8 v0, v0, 0x1f

    .line 148
    .line 149
    iget-object v1, p0, Li81/a;->r:Ljava/util/List;

    .line 150
    .line 151
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 152
    .line 153
    .line 154
    move-result v1

    .line 155
    add-int/2addr v0, v1

    .line 156
    return v0
.end method

.method public final i()Lo81/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Li81/a;->l:Lo81/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final j()Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Li81/a;->p:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    .line 2
    .line 3
    return-object v0
.end method

.method public final k()Lh81/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Li81/a;->o:Lh81/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final l()J
    .locals 2

    .line 1
    iget-wide v0, p0, Li81/a;->q:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final m()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Li81/a;->r:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final n()J
    .locals 2

    .line 1
    iget-wide v0, p0, Li81/a;->a:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Li81/a;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 2
    .line 3
    return-object v0
.end method

.method public final p()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Li81/a;->c:Z

    .line 2
    .line 3
    return v0
.end method

.method public final q()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ll81/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Li81/a;->g:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final r()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Li81/a;->d:Z

    .line 2
    .line 3
    return v0
.end method

.method public final s()I
    .locals 1

    .line 1
    iget v0, p0, Li81/a;->m:I

    .line 2
    .line 3
    return v0
.end method

.method public final t()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Li81/a;->n:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 2
    .line 3
    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 22
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-wide v1, v0, Li81/a;->a:J

    .line 4
    .line 5
    iget-object v3, v0, Li81/a;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 6
    .line 7
    iget-boolean v4, v0, Li81/a;->c:Z

    .line 8
    .line 9
    iget-boolean v5, v0, Li81/a;->d:Z

    .line 10
    .line 11
    iget-object v6, v0, Li81/a;->e:Lj81/a;

    .line 12
    .line 13
    iget-object v7, v0, Li81/a;->f:Lk81/a;

    .line 14
    .line 15
    iget-object v8, v0, Li81/a;->g:Ljava/util/List;

    .line 16
    .line 17
    iget-object v9, v0, Li81/a;->h:Lm81/a;

    .line 18
    .line 19
    iget-object v10, v0, Li81/a;->i:Ln81/c;

    .line 20
    .line 21
    iget-object v11, v0, Li81/a;->j:Ln81/a;

    .line 22
    .line 23
    iget-object v12, v0, Li81/a;->k:Ln81/b;

    .line 24
    .line 25
    iget-object v13, v0, Li81/a;->l:Lo81/a;

    .line 26
    .line 27
    iget v14, v0, Li81/a;->m:I

    .line 28
    .line 29
    iget-object v15, v0, Li81/a;->n:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 30
    .line 31
    move-object/from16 v16, v15

    .line 32
    .line 33
    iget-object v15, v0, Li81/a;->o:Lh81/a;

    .line 34
    .line 35
    move-object/from16 v17, v15

    .line 36
    .line 37
    iget-object v15, v0, Li81/a;->p:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    .line 38
    .line 39
    move/from16 v18, v14

    .line 40
    .line 41
    move-object/from16 v19, v15

    .line 42
    .line 43
    iget-wide v14, v0, Li81/a;->q:J

    .line 44
    .line 45
    move-wide/from16 v20, v14

    .line 46
    .line 47
    iget-object v14, v0, Li81/a;->r:Ljava/util/List;

    .line 48
    .line 49
    new-instance v15, Ljava/lang/StringBuilder;

    .line 50
    .line 51
    invoke-direct {v15}, Ljava/lang/StringBuilder;-><init>()V

    .line 52
    .line 53
    .line 54
    const-string v0, "TournamentFullInfoModel(id="

    .line 55
    .line 56
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 57
    .line 58
    .line 59
    invoke-virtual {v15, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 60
    .line 61
    .line 62
    const-string v0, ", kind="

    .line 63
    .line 64
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 65
    .line 66
    .line 67
    invoke-virtual {v15, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 68
    .line 69
    .line 70
    const-string v0, ", meParticipating="

    .line 71
    .line 72
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 73
    .line 74
    .line 75
    invoke-virtual {v15, v4}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 76
    .line 77
    .line 78
    const-string v0, ", providerTournamentWithStages="

    .line 79
    .line 80
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 81
    .line 82
    .line 83
    invoke-virtual {v15, v5}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 84
    .line 85
    .line 86
    const-string v0, ", blockHeader="

    .line 87
    .line 88
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 89
    .line 90
    .line 91
    invoke-virtual {v15, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 92
    .line 93
    .line 94
    const-string v0, ", blockPrize="

    .line 95
    .line 96
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 97
    .line 98
    .line 99
    invoke-virtual {v15, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 100
    .line 101
    .line 102
    const-string v0, ", productsList="

    .line 103
    .line 104
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 105
    .line 106
    .line 107
    invoke-virtual {v15, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 108
    .line 109
    .line 110
    const-string v0, ", blockResult="

    .line 111
    .line 112
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 113
    .line 114
    .line 115
    invoke-virtual {v15, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 116
    .line 117
    .line 118
    const-string v0, ", blockRule="

    .line 119
    .line 120
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 121
    .line 122
    .line 123
    invoke-virtual {v15, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 124
    .line 125
    .line 126
    const-string v0, ", blockFullRule="

    .line 127
    .line 128
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 129
    .line 130
    .line 131
    invoke-virtual {v15, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 132
    .line 133
    .line 134
    const-string v0, ", blockRuleStage="

    .line 135
    .line 136
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 137
    .line 138
    .line 139
    invoke-virtual {v15, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 140
    .line 141
    .line 142
    const-string v0, ", blockStages="

    .line 143
    .line 144
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 145
    .line 146
    .line 147
    invoke-virtual {v15, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 148
    .line 149
    .line 150
    const-string v0, ", type="

    .line 151
    .line 152
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 153
    .line 154
    .line 155
    move/from16 v0, v18

    .line 156
    .line 157
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 158
    .line 159
    .line 160
    const-string v0, ", userActionButtonType="

    .line 161
    .line 162
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 163
    .line 164
    .line 165
    move-object/from16 v0, v16

    .line 166
    .line 167
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 168
    .line 169
    .line 170
    const-string v0, ", buttons="

    .line 171
    .line 172
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 173
    .line 174
    .line 175
    move-object/from16 v0, v17

    .line 176
    .line 177
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 178
    .line 179
    .line 180
    const-string v0, ", buttonStatus="

    .line 181
    .line 182
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 183
    .line 184
    .line 185
    move-object/from16 v0, v19

    .line 186
    .line 187
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 188
    .line 189
    .line 190
    const-string v0, ", crmParticipantCurrentStage="

    .line 191
    .line 192
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 193
    .line 194
    .line 195
    move-wide/from16 v0, v20

    .line 196
    .line 197
    invoke-virtual {v15, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 198
    .line 199
    .line 200
    const-string v0, ", games="

    .line 201
    .line 202
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 203
    .line 204
    .line 205
    invoke-virtual {v15, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 206
    .line 207
    .line 208
    const-string v0, ")"

    .line 209
    .line 210
    invoke-virtual {v15, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 211
    .line 212
    .line 213
    invoke-virtual {v15}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 214
    .line 215
    .line 216
    move-result-object v0

    .line 217
    return-object v0
.end method
