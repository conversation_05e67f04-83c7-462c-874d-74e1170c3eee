.class final Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.crystal.presentation.game.CrystalGameViewModel$playGame$2"
    f = "CrystalGameViewModel.kt"
    l = {
        0x7e,
        0x7f,
        0x80
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->H3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;

    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;-><init>(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x3

    .line 8
    const/4 v3, 0x2

    .line 9
    const/4 v4, 0x1

    .line 10
    if-eqz v1, :cond_3

    .line 11
    .line 12
    if-eq v1, v4, :cond_2

    .line 13
    .line 14
    if-eq v1, v3, :cond_1

    .line 15
    .line 16
    if-ne v1, v2, :cond_0

    .line 17
    .line 18
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->L$1:Ljava/lang/Object;

    .line 19
    .line 20
    check-cast v0, Ljava/lang/String;

    .line 21
    .line 22
    iget-object v1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->L$0:Ljava/lang/Object;

    .line 23
    .line 24
    check-cast v1, LZx/b;

    .line 25
    .line 26
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    goto :goto_3

    .line 30
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 31
    .line 32
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 33
    .line 34
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 35
    .line 36
    .line 37
    throw p1

    .line 38
    :cond_1
    iget-object v1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->L$0:Ljava/lang/Object;

    .line 39
    .line 40
    check-cast v1, LZx/b;

    .line 41
    .line 42
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    goto :goto_1

    .line 46
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 47
    .line 48
    .line 49
    goto :goto_0

    .line 50
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    .line 54
    .line 55
    invoke-static {p1}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->u3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;)Lay/e;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    iput v4, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->label:I

    .line 60
    .line 61
    invoke-virtual {p1, p0}, Lay/e;->b(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    if-ne p1, v0, :cond_4

    .line 66
    .line 67
    goto :goto_2

    .line 68
    :cond_4
    :goto_0
    check-cast p1, LZx/b;

    .line 69
    .line 70
    iget-object v1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    .line 71
    .line 72
    invoke-static {v1}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->t3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;)Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 73
    .line 74
    .line 75
    move-result-object v1

    .line 76
    iput-object p1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->L$0:Ljava/lang/Object;

    .line 77
    .line 78
    iput v3, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->label:I

    .line 79
    .line 80
    invoke-virtual {v1, p0}, Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    if-ne v1, v0, :cond_5

    .line 85
    .line 86
    goto :goto_2

    .line 87
    :cond_5
    move-object v5, v1

    .line 88
    move-object v1, p1

    .line 89
    move-object p1, v5

    .line 90
    :goto_1
    check-cast p1, Ljava/lang/String;

    .line 91
    .line 92
    iget-object v3, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    .line 93
    .line 94
    invoke-static {v3}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->r3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;)Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 95
    .line 96
    .line 97
    move-result-object v3

    .line 98
    sget-object v4, LTv/a$k;->a:LTv/a$k;

    .line 99
    .line 100
    iput-object v1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->L$0:Ljava/lang/Object;

    .line 101
    .line 102
    iput-object p1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->L$1:Ljava/lang/Object;

    .line 103
    .line 104
    iput v2, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->label:I

    .line 105
    .line 106
    invoke-virtual {v3, v4, p0}, Lorg/xbet/core/domain/usecases/AddCommandScenario;->l(LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 107
    .line 108
    .line 109
    move-result-object v2

    .line 110
    if-ne v2, v0, :cond_6

    .line 111
    .line 112
    :goto_2
    return-object v0

    .line 113
    :cond_6
    move-object v0, p1

    .line 114
    :goto_3
    iget-object p1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    .line 115
    .line 116
    new-instance v2, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$e;

    .line 117
    .line 118
    invoke-direct {v2, v1, v0}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$e;-><init>(LZx/b;Ljava/lang/String;)V

    .line 119
    .line 120
    .line 121
    invoke-static {p1, v2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->z3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;)V

    .line 122
    .line 123
    .line 124
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 125
    .line 126
    return-object p1
.end method
