.class public final synthetic LI5/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/vk/sdk/api/ApiResponseParser;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final parseResponse(Lcom/google/gson/stream/JsonReader;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p1}, Lcom/vk/sdk/api/apps/AppsService;->e(Lcom/google/gson/stream/JsonReader;)Lcom/vk/sdk/api/apps/dto/AppsGetMiniAppPoliciesResponseDto;

    move-result-object p1

    return-object p1
.end method
