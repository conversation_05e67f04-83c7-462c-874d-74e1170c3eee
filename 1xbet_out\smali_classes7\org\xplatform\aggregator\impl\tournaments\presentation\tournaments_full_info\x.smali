.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/x;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/x;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/x;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    check-cast p1, Ljava/lang/Throwable;

    check-cast p2, Ljava/lang/String;

    invoke-static {v0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->p3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
