.class public final synthetic Lorg/xbet/african_roulette/presentation/holder/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/holder/b;->a:Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/holder/b;->a:Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;

    invoke-static {v0}, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;->g4(Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;)Lhg/a;

    move-result-object v0

    return-object v0
.end method
