.class public final LqA0/a$a$a;
.super Landroidx/recyclerview/widget/i$f;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LqA0/a$a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroidx/recyclerview/widget/i$f<",
        "LqA0/a;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000)\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\"\n\u0002\u0010 \n\u0002\u0010\u0000\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001J\u001f\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u001f\u0010\u0008\u001a\u00020\u00052\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\u0007J+\u0010\u000c\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000b0\n0\t2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "qA0/a$a$a",
        "Landroidx/recyclerview/widget/i$f;",
        "LqA0/a;",
        "oldItem",
        "newItem",
        "",
        "e",
        "(LqA0/a;LqA0/a;)Z",
        "d",
        "",
        "",
        "",
        "f",
        "(LqA0/a;LqA0/a;)Ljava/util/Set;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Landroidx/recyclerview/widget/i$f;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, LqA0/a;

    .line 2
    .line 3
    check-cast p2, LqA0/a;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LqA0/a$a$a;->d(LqA0/a;LqA0/a;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public bridge synthetic b(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, LqA0/a;

    .line 2
    .line 3
    check-cast p2, LqA0/a;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LqA0/a$a$a;->e(LqA0/a;LqA0/a;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public bridge synthetic c(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, LqA0/a;

    .line 2
    .line 3
    check-cast p2, LqA0/a;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LqA0/a$a$a;->f(LqA0/a;LqA0/a;)Ljava/util/Set;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    return-object p1
.end method

.method public d(LqA0/a;LqA0/a;)Z
    .locals 0

    .line 1
    invoke-static {p2, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method

.method public e(LqA0/a;LqA0/a;)Z
    .locals 0

    .line 1
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    if-ne p2, p1, :cond_0

    .line 10
    .line 11
    const/4 p1, 0x1

    .line 12
    return p1

    .line 13
    :cond_0
    const/4 p1, 0x0

    .line 14
    return p1
.end method

.method public f(LqA0/a;LqA0/a;)Ljava/util/Set;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LqA0/a;",
            "LqA0/a;",
            ")",
            "Ljava/util/Set<",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;>;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/LinkedHashSet;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    .line 4
    .line 5
    .line 6
    instance-of v1, p2, LqA0/x;

    .line 7
    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    instance-of v1, p1, LqA0/x;

    .line 11
    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    move-object v1, p1

    .line 15
    check-cast v1, LqA0/x;

    .line 16
    .line 17
    move-object v2, p2

    .line 18
    check-cast v2, LqA0/x;

    .line 19
    .line 20
    invoke-static {v1, v2}, LqA0/y;->a(LqA0/x;LqA0/x;)Ljava/util/List;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 25
    .line 26
    .line 27
    :cond_0
    instance-of v1, p2, LqA0/m;

    .line 28
    .line 29
    if-eqz v1, :cond_1

    .line 30
    .line 31
    instance-of v1, p1, LqA0/m;

    .line 32
    .line 33
    if-eqz v1, :cond_1

    .line 34
    .line 35
    move-object v1, p1

    .line 36
    check-cast v1, LqA0/m;

    .line 37
    .line 38
    move-object v2, p2

    .line 39
    check-cast v2, LqA0/m;

    .line 40
    .line 41
    invoke-static {v1, v2}, LqA0/n;->a(LqA0/m;LqA0/m;)Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 46
    .line 47
    .line 48
    :cond_1
    instance-of v1, p2, LqA0/C;

    .line 49
    .line 50
    if-eqz v1, :cond_2

    .line 51
    .line 52
    instance-of v1, p1, LqA0/C;

    .line 53
    .line 54
    if-eqz v1, :cond_2

    .line 55
    .line 56
    move-object v1, p1

    .line 57
    check-cast v1, LqA0/C;

    .line 58
    .line 59
    move-object v2, p2

    .line 60
    check-cast v2, LqA0/C;

    .line 61
    .line 62
    invoke-static {v1, v2}, LqA0/D;->a(LqA0/C;LqA0/C;)Ljava/util/List;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 67
    .line 68
    .line 69
    :cond_2
    instance-of v1, p2, LqA0/z;

    .line 70
    .line 71
    if-eqz v1, :cond_3

    .line 72
    .line 73
    instance-of v1, p1, LqA0/z;

    .line 74
    .line 75
    if-eqz v1, :cond_3

    .line 76
    .line 77
    move-object v1, p1

    .line 78
    check-cast v1, LqA0/z;

    .line 79
    .line 80
    move-object v2, p2

    .line 81
    check-cast v2, LqA0/z;

    .line 82
    .line 83
    invoke-static {v1, v2}, LqA0/A;->a(LqA0/z;LqA0/z;)Ljava/util/List;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 88
    .line 89
    .line 90
    :cond_3
    instance-of v1, p2, LqA0/E;

    .line 91
    .line 92
    if-eqz v1, :cond_4

    .line 93
    .line 94
    instance-of v1, p1, LqA0/E;

    .line 95
    .line 96
    if-eqz v1, :cond_4

    .line 97
    .line 98
    move-object v1, p1

    .line 99
    check-cast v1, LqA0/E;

    .line 100
    .line 101
    move-object v2, p2

    .line 102
    check-cast v2, LqA0/E;

    .line 103
    .line 104
    invoke-static {v1, v2}, LqA0/F;->a(LqA0/E;LqA0/E;)Ljava/util/List;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 109
    .line 110
    .line 111
    :cond_4
    instance-of v1, p2, LqA0/q;

    .line 112
    .line 113
    if-eqz v1, :cond_5

    .line 114
    .line 115
    instance-of v1, p1, LqA0/q;

    .line 116
    .line 117
    if-eqz v1, :cond_5

    .line 118
    .line 119
    move-object v1, p1

    .line 120
    check-cast v1, LqA0/q;

    .line 121
    .line 122
    move-object v2, p2

    .line 123
    check-cast v2, LqA0/q;

    .line 124
    .line 125
    invoke-static {v1, v2}, LqA0/r;->a(LqA0/q;LqA0/q;)Ljava/util/List;

    .line 126
    .line 127
    .line 128
    move-result-object v1

    .line 129
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 130
    .line 131
    .line 132
    :cond_5
    instance-of v1, p2, LqA0/s;

    .line 133
    .line 134
    if-eqz v1, :cond_6

    .line 135
    .line 136
    instance-of v1, p1, LqA0/s;

    .line 137
    .line 138
    if-eqz v1, :cond_6

    .line 139
    .line 140
    move-object v1, p1

    .line 141
    check-cast v1, LqA0/s;

    .line 142
    .line 143
    move-object v2, p2

    .line 144
    check-cast v2, LqA0/s;

    .line 145
    .line 146
    invoke-static {v1, v2}, LqA0/t;->a(LqA0/s;LqA0/s;)Ljava/util/List;

    .line 147
    .line 148
    .line 149
    move-result-object v1

    .line 150
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 151
    .line 152
    .line 153
    :cond_6
    instance-of v1, p2, LqA0/u;

    .line 154
    .line 155
    if-eqz v1, :cond_7

    .line 156
    .line 157
    instance-of v1, p1, LqA0/u;

    .line 158
    .line 159
    if-eqz v1, :cond_7

    .line 160
    .line 161
    move-object v1, p1

    .line 162
    check-cast v1, LqA0/u;

    .line 163
    .line 164
    move-object v2, p2

    .line 165
    check-cast v2, LqA0/u;

    .line 166
    .line 167
    invoke-static {v1, v2}, LqA0/v;->a(LqA0/u;LqA0/u;)Ljava/util/List;

    .line 168
    .line 169
    .line 170
    move-result-object v1

    .line 171
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 172
    .line 173
    .line 174
    :cond_7
    instance-of v1, p2, LqA0/o;

    .line 175
    .line 176
    if-eqz v1, :cond_8

    .line 177
    .line 178
    instance-of v1, p1, LqA0/o;

    .line 179
    .line 180
    if-eqz v1, :cond_8

    .line 181
    .line 182
    check-cast p1, LqA0/o;

    .line 183
    .line 184
    check-cast p2, LqA0/o;

    .line 185
    .line 186
    invoke-static {p1, p2}, LqA0/p;->a(LqA0/o;LqA0/o;)Ljava/util/List;

    .line 187
    .line 188
    .line 189
    move-result-object p1

    .line 190
    invoke-interface {v0, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 191
    .line 192
    .line 193
    :cond_8
    return-object v0
.end method
