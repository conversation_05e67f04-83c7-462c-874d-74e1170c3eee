.class public final enum Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\u0005\n\u0002\u0010\u0008\n\u0000\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0006\u0010\u0006\u001a\u00020\u0007j\u0002\u0008\u0004j\u0002\u0008\u0005\u00a8\u0006\u0008"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "MAIN",
        "STAGE",
        "name",
        "",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

.field public static final enum MAIN:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

.field public static final enum STAGE:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 2
    .line 3
    const-string v1, "MAIN"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;->MAIN:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 10
    .line 11
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 12
    .line 13
    const-string v1, "STAGE"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;->STAGE:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 20
    .line 21
    invoke-static {}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;->a()[Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;->$VALUES:[Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 26
    .line 27
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;->$ENTRIES:Lkotlin/enums/a;

    .line 32
    .line 33
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;
    .locals 3

    .line 1
    const/4 v0, 0x2

    new-array v0, v0, [Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;->MAIN:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;->STAGE:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;
    .locals 1

    .line 1
    const-class v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;->$VALUES:[Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final name()I
    .locals 2

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    const/4 v1, 0x1

    .line 10
    if-eq v0, v1, :cond_1

    .line 11
    .line 12
    const/4 v1, 0x2

    .line 13
    if-ne v0, v1, :cond_0

    .line 14
    .line 15
    sget v0, Lpb/k;->stages:I

    .line 16
    .line 17
    return v0

    .line 18
    :cond_0
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 19
    .line 20
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 21
    .line 22
    .line 23
    throw v0

    .line 24
    :cond_1
    sget v0, Lpb/k;->common:I

    .line 25
    .line 26
    return v0
.end method
