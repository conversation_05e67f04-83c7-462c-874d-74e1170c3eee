.class public final LHz0/a;
.super Ljava/lang/Object;


# static fields
.field public static bg_event_time:I = 0x7f080274

.field public static bg_expand_btn:I = 0x7f080276

.field public static bg_football_field:I = 0x7f080279

.field public static bg_penalty_beating:I = 0x7f08028b

.field public static bg_penalty_expected:I = 0x7f08028c

.field public static bg_rounded_corner_4:I = 0x7f08029c

.field public static bg_rounded_corner_4_end:I = 0x7f08029d

.field public static bg_rounded_corner_4_start:I = 0x7f08029e

.field public static bg_rounded_corner_8:I = 0x7f0802a0

.field public static bg_timer_text:I = 0x7f0802cc

.field public static bg_winner_indicate:I = 0x7f0802d6

.field public static ic_arrow_collaps_8:I = 0x7f080714

.field public static ic_arrow_expand_8:I = 0x7f08071f

.field public static ic_favorite_star_checked:I = 0x7f080882

.field public static ic_favorite_star_unchecked:I = 0x7f080884

.field public static ic_guests_label:I = 0x7f080ac6

.field public static ic_hosts_label:I = 0x7f080ade

.field public static ic_inning_period:I = 0x7f080b0c

.field public static ic_penalty_football_goal_new:I = 0x7f080b96

.field public static ic_penalty_football_miss_new:I = 0x7f080b97

.field public static ic_penalty_hockey_goal_new:I = 0x7f080b98

.field public static ic_penalty_hockey_miss_new:I = 0x7f080b99

.field public static ic_red_card_16_new:I = 0x7f080bd5

.field public static ic_zoom:I = 0x7f080d6f


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
