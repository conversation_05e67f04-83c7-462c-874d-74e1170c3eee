.class final synthetic Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView$show$holder$1;
.super Lkotlin/jvm/internal/FunctionReferenceImpl;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/FunctionReferenceImpl;",
        "Lkotlin/jvm/functions/Function2<",
        "LL11/c;",
        "LL11/c;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>(Ljava/lang/Object;)V
    .locals 7

    const-string v5, "setBannerImage(Lorg/xbet/uikit/models/ImageLink;Lorg/xbet/uikit/models/ImageLink;)V"

    const/4 v6, 0x0

    const/4 v1, 0x2

    const-class v3, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    const-string v4, "setBannerImage"

    move-object v0, p0

    move-object v2, p1

    invoke-direct/range {v0 .. v6}, Lkotlin/jvm/internal/FunctionReferenceImpl;-><init>(ILjava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, LL11/c;

    check-cast p2, LL11/c;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView$show$holder$1;->invoke(LL11/c;LL11/c;)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1
.end method

.method public final invoke(LL11/c;LL11/c;)V
    .locals 1

    .line 2
    iget-object v0, p0, Lkotlin/jvm/internal/CallableReference;->receiver:Ljava/lang/Object;

    check-cast v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->setBannerImage(LL11/c;LL11/c;)V

    return-void
.end method
