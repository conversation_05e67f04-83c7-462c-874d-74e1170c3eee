.class public final Lh2/b$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lh2/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public final a:J

.field public final b:J


# direct methods
.method public constructor <init>(JJ)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-wide p1, p0, Lh2/b$a;->a:J

    .line 5
    .line 6
    iput-wide p3, p0, Lh2/b$a;->b:J

    .line 7
    .line 8
    return-void
.end method

.method public static synthetic a(Lh2/b$a;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lh2/b$a;->b:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public static synthetic b(Lh2/b$a;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lh2/b$a;->a:J

    .line 2
    .line 3
    return-wide v0
.end method
