.class final Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.swipex.impl.presentation.swipex.SwipexViewModel$loadCardsFromRemote$2"
    f = "SwipexViewModel.kt"
    l = {
        0x175,
        0x17a,
        0x180,
        0x185,
        0x186
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->q4(Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $updateCardStack:Z

.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;ZLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    iput-boolean p2, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->$updateCardStack:Z

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;

    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    iget-boolean v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->$updateCardStack:Z

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;ZLkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 11

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x5

    .line 9
    const/4 v4, 0x4

    .line 10
    const/4 v5, 0x3

    .line 11
    const/4 v6, 0x2

    .line 12
    const/4 v7, 0x0

    .line 13
    const/4 v8, 0x1

    .line 14
    if-eqz v1, :cond_5

    .line 15
    .line 16
    if-eq v1, v8, :cond_4

    .line 17
    .line 18
    if-eq v1, v6, :cond_3

    .line 19
    .line 20
    if-eq v1, v5, :cond_2

    .line 21
    .line 22
    if-eq v1, v4, :cond_1

    .line 23
    .line 24
    if-ne v1, v3, :cond_0

    .line 25
    .line 26
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    goto/16 :goto_6

    .line 30
    .line 31
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 32
    .line 33
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 34
    .line 35
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 36
    .line 37
    .line 38
    throw p1

    .line 39
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    goto/16 :goto_4

    .line 43
    .line 44
    :cond_2
    iget-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->L$0:Ljava/lang/Object;

    .line 45
    .line 46
    check-cast v1, LDS0/b;

    .line 47
    .line 48
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 49
    .line 50
    .line 51
    goto/16 :goto_3

    .line 52
    .line 53
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 54
    .line 55
    .line 56
    goto :goto_2

    .line 57
    :cond_4
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 58
    .line 59
    .line 60
    goto :goto_0

    .line 61
    :cond_5
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 65
    .line 66
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->A3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/swipex/impl/domain/usecases/h;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    iput v8, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->label:I

    .line 71
    .line 72
    invoke-virtual {p1, p0}, Lorg/xbet/swipex/impl/domain/usecases/h;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    if-ne p1, v0, :cond_6

    .line 77
    .line 78
    goto/16 :goto_5

    .line 79
    .line 80
    :cond_6
    :goto_0
    check-cast p1, Ljava/lang/Iterable;

    .line 81
    .line 82
    new-instance v1, Ljava/util/ArrayList;

    .line 83
    .line 84
    const/16 v9, 0xa

    .line 85
    .line 86
    invoke-static {p1, v9}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 87
    .line 88
    .line 89
    move-result v9

    .line 90
    invoke-direct {v1, v9}, Ljava/util/ArrayList;-><init>(I)V

    .line 91
    .line 92
    .line 93
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 98
    .line 99
    .line 100
    move-result v9

    .line 101
    if-eqz v9, :cond_7

    .line 102
    .line 103
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 104
    .line 105
    .line 106
    move-result-object v9

    .line 107
    check-cast v9, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;

    .line 108
    .line 109
    invoke-virtual {v9}, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;->getId()I

    .line 110
    .line 111
    .line 112
    move-result v9

    .line 113
    int-to-long v9, v9

    .line 114
    invoke-static {v9, v10}, LHc/a;->f(J)Ljava/lang/Long;

    .line 115
    .line 116
    .line 117
    move-result-object v9

    .line 118
    invoke-interface {v1, v9}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 119
    .line 120
    .line 121
    goto :goto_1

    .line 122
    :cond_7
    invoke-static {v1}, Lkotlin/collections/CollectionsKt;->j1(Ljava/lang/Iterable;)Ljava/util/List;

    .line 123
    .line 124
    .line 125
    move-result-object p1

    .line 126
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->E1(Ljava/lang/Iterable;)Ljava/util/Set;

    .line 127
    .line 128
    .line 129
    move-result-object p1

    .line 130
    invoke-interface {p1}, Ljava/util/Set;->isEmpty()Z

    .line 131
    .line 132
    .line 133
    move-result v1

    .line 134
    if-eqz v1, :cond_8

    .line 135
    .line 136
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 137
    .line 138
    return-object p1

    .line 139
    :cond_8
    iget-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 140
    .line 141
    invoke-static {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->H3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/swipex/impl/domain/scenario/LoadSwipexCardListScenario;

    .line 142
    .line 143
    .line 144
    move-result-object v1

    .line 145
    iput v6, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->label:I

    .line 146
    .line 147
    invoke-virtual {v1, p1, p0}, Lorg/xbet/swipex/impl/domain/scenario/LoadSwipexCardListScenario;->a(Ljava/util/Set;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 148
    .line 149
    .line 150
    move-result-object p1

    .line 151
    if-ne p1, v0, :cond_9

    .line 152
    .line 153
    goto/16 :goto_5

    .line 154
    .line 155
    :cond_9
    :goto_2
    move-object v1, p1

    .line 156
    check-cast v1, LDS0/b;

    .line 157
    .line 158
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 159
    .line 160
    invoke-static {p1, v7}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->Z3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Z)V

    .line 161
    .line 162
    .line 163
    invoke-virtual {v1}, LDS0/b;->d()Z

    .line 164
    .line 165
    .line 166
    move-result p1

    .line 167
    if-eqz p1, :cond_a

    .line 168
    .line 169
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 170
    .line 171
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->w3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Z

    .line 172
    .line 173
    .line 174
    move-result p1

    .line 175
    if-nez p1, :cond_a

    .line 176
    .line 177
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 178
    .line 179
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->R3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 180
    .line 181
    .line 182
    move-result-object p1

    .line 183
    sget-object v6, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$f;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$f;

    .line 184
    .line 185
    invoke-virtual {p1, v6}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 186
    .line 187
    .line 188
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 189
    .line 190
    invoke-static {p1, v8}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->Y3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Z)V

    .line 191
    .line 192
    .line 193
    :cond_a
    invoke-virtual {v1}, LDS0/b;->c()Ljava/util/List;

    .line 194
    .line 195
    .line 196
    move-result-object p1

    .line 197
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 198
    .line 199
    .line 200
    move-result p1

    .line 201
    if-eqz p1, :cond_c

    .line 202
    .line 203
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 204
    .line 205
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->O3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;

    .line 206
    .line 207
    .line 208
    move-result-object p1

    .line 209
    iput-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->L$0:Ljava/lang/Object;

    .line 210
    .line 211
    iput v5, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->label:I

    .line 212
    .line 213
    invoke-virtual {p1, p0}, Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;->g(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 214
    .line 215
    .line 216
    move-result-object p1

    .line 217
    if-ne p1, v0, :cond_b

    .line 218
    .line 219
    goto :goto_5

    .line 220
    :cond_b
    :goto_3
    check-cast p1, Ljava/lang/Boolean;

    .line 221
    .line 222
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 223
    .line 224
    .line 225
    move-result p1

    .line 226
    if-eqz p1, :cond_c

    .line 227
    .line 228
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 229
    .line 230
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->S3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lkotlinx/coroutines/flow/V;

    .line 231
    .line 232
    .line 233
    move-result-object p1

    .line 234
    sget-object v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$a;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$a;

    .line 235
    .line 236
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 237
    .line 238
    .line 239
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 240
    .line 241
    return-object p1

    .line 242
    :cond_c
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 243
    .line 244
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->M3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/swipex/impl/domain/usecases/N;

    .line 245
    .line 246
    .line 247
    move-result-object p1

    .line 248
    invoke-virtual {p1, v7}, Lorg/xbet/swipex/impl/domain/usecases/N;->a(Z)V

    .line 249
    .line 250
    .line 251
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 252
    .line 253
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->O3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;

    .line 254
    .line 255
    .line 256
    move-result-object p1

    .line 257
    invoke-virtual {v1}, LDS0/b;->c()Ljava/util/List;

    .line 258
    .line 259
    .line 260
    move-result-object v1

    .line 261
    iput-object v2, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->L$0:Ljava/lang/Object;

    .line 262
    .line 263
    iput v4, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->label:I

    .line 264
    .line 265
    invoke-virtual {p1, v1, p0}, Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;->f(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 266
    .line 267
    .line 268
    move-result-object p1

    .line 269
    if-ne p1, v0, :cond_d

    .line 270
    .line 271
    goto :goto_5

    .line 272
    :cond_d
    :goto_4
    iget-boolean p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->$updateCardStack:Z

    .line 273
    .line 274
    if-eqz p1, :cond_e

    .line 275
    .line 276
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 277
    .line 278
    iput v3, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$loadCardsFromRemote$2;->label:I

    .line 279
    .line 280
    invoke-static {p1, v7, p0, v8, v2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->Q4(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;ZLkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 281
    .line 282
    .line 283
    move-result-object p1

    .line 284
    if-ne p1, v0, :cond_e

    .line 285
    .line 286
    :goto_5
    return-object v0

    .line 287
    :cond_e
    :goto_6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 288
    .line 289
    return-object p1
.end method
