.class public final LtW0/e$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LtW0/q;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LtW0/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:LzX0/k;

.field public final b:LtW0/e$b;

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwW0/k;",
            ">;"
        }
    .end annotation
.end field

.field public d:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/e;

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LtW0/q$b;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LwW0/k;LzX0/k;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LtW0/e$b;->b:LtW0/e$b;

    .line 4
    iput-object p2, p0, LtW0/e$b;->a:LzX0/k;

    .line 5
    invoke-virtual {p0, p1, p2}, LtW0/e$b;->b(LwW0/k;LzX0/k;)V

    return-void
.end method

.method public synthetic constructor <init>(LwW0/k;LzX0/k;LtW0/f;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, LtW0/e$b;-><init>(LwW0/k;LzX0/k;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LtW0/e$b;->c(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LwW0/k;LzX0/k;)V
    .locals 0

    .line 1
    invoke-static {p1}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, LtW0/e$b;->c:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/e;->a(LBc/a;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/e;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    iput-object p1, p0, LtW0/e$b;->d:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/e;

    .line 12
    .line 13
    invoke-static {p1}, LtW0/t;->c(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/e;)Ldagger/internal/h;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iput-object p1, p0, LtW0/e$b;->e:Ldagger/internal/h;

    .line 18
    .line 19
    return-void
.end method

.method public final c(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LtW0/e$b;->e:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LtW0/q$b;

    .line 8
    .line 9
    invoke-static {p1, v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/c;->b(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;LtW0/q$b;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, LtW0/e$b;->a:LzX0/k;

    .line 13
    .line 14
    invoke-static {p1, v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/c;->a(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;LzX0/k;)V

    .line 15
    .line 16
    .line 17
    return-object p1
.end method
