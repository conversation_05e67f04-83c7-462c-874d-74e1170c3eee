.class public final LNA0/g;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0003\u001a\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u001a\u0013\u0010\u0005\u001a\u00020\u0004*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u001a#\u0010\u000c\u001a\u0004\u0018\u00010\u000b*\u0008\u0012\u0004\u0012\u00020\u00080\u00072\u0006\u0010\n\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "LYA0/a;",
        "LRA0/a;",
        "c",
        "(LYA0/a;)LRA0/a;",
        "",
        "b",
        "(LYA0/a;)Z",
        "",
        "Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;",
        "Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;",
        "statisticKey",
        "",
        "a",
        "(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;",
        "core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;",
            ">;",
            "Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;",
            ")",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 1
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x0

    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    move-object v2, v0

    .line 17
    check-cast v2, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;

    .line 18
    .line 19
    invoke-virtual {v2}, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;->a()Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    if-ne v2, p1, :cond_0

    .line 24
    .line 25
    goto :goto_0

    .line 26
    :cond_1
    move-object v0, v1

    .line 27
    :goto_0
    check-cast v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;

    .line 28
    .line 29
    if-eqz v0, :cond_2

    .line 30
    .line 31
    invoke-virtual {v0}, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;->b()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object p0

    .line 35
    return-object p0

    .line 36
    :cond_2
    return-object v1
.end method

.method public static final b(LYA0/a;)Z
    .locals 2

    .line 1
    invoke-virtual {p0}, LYA0/a;->z()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;->LEFT_ACTION_ZONES:Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;

    .line 6
    .line 7
    invoke-static {v0, v1}, LNA0/g;->a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    if-eqz v0, :cond_3

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    if-nez v0, :cond_0

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    invoke-virtual {p0}, LYA0/a;->z()Ljava/util/List;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    sget-object v1, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;->CENTRAL_ACTION_ZONES:Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;

    .line 25
    .line 26
    invoke-static {v0, v1}, LNA0/g;->a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    if-eqz v0, :cond_3

    .line 31
    .line 32
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 33
    .line 34
    .line 35
    move-result v0

    .line 36
    if-nez v0, :cond_1

    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_1
    invoke-virtual {p0}, LYA0/a;->z()Ljava/util/List;

    .line 40
    .line 41
    .line 42
    move-result-object p0

    .line 43
    sget-object v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;->RIGHT_ACTION_ZONES:Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;

    .line 44
    .line 45
    invoke-static {p0, v0}, LNA0/g;->a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;

    .line 46
    .line 47
    .line 48
    move-result-object p0

    .line 49
    if-eqz p0, :cond_3

    .line 50
    .line 51
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 52
    .line 53
    .line 54
    move-result p0

    .line 55
    if-nez p0, :cond_2

    .line 56
    .line 57
    goto :goto_0

    .line 58
    :cond_2
    const/4 p0, 0x1

    .line 59
    return p0

    .line 60
    :cond_3
    :goto_0
    const/4 p0, 0x0

    .line 61
    return p0
.end method

.method public static final c(LYA0/a;)LRA0/a;
    .locals 14
    .param p0    # LYA0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, LYA0/a;->v()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    const-wide/16 v2, 0x1

    .line 6
    .line 7
    cmp-long v4, v0, v2

    .line 8
    .line 9
    if-nez v4, :cond_3

    .line 10
    .line 11
    invoke-static {p0}, LNA0/g;->b(LYA0/a;)Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_3

    .line 16
    .line 17
    invoke-static {p0}, LNA0/p;->a(LYA0/a;)LRA0/g;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    invoke-virtual {p0}, LYA0/a;->D()Ljava/util/List;

    .line 22
    .line 23
    .line 24
    move-result-object v3

    .line 25
    invoke-virtual {p0}, LYA0/a;->G()Ljava/util/List;

    .line 26
    .line 27
    .line 28
    move-result-object v4

    .line 29
    invoke-virtual {p0}, LYA0/a;->z()Ljava/util/List;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    sget-object v1, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;->LEFT_ACTION_ZONES:Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;

    .line 34
    .line 35
    invoke-static {v0, v1}, LNA0/g;->a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    const/4 v1, 0x0

    .line 40
    if-eqz v0, :cond_0

    .line 41
    .line 42
    invoke-static {v0}, Lkotlin/text/u;->v(Ljava/lang/String;)Ljava/lang/Float;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    if-eqz v0, :cond_0

    .line 47
    .line 48
    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    .line 49
    .line 50
    .line 51
    move-result v0

    .line 52
    move v7, v0

    .line 53
    goto :goto_0

    .line 54
    :cond_0
    const/4 v7, 0x0

    .line 55
    :goto_0
    invoke-virtual {p0}, LYA0/a;->z()Ljava/util/List;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    sget-object v5, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;->CENTRAL_ACTION_ZONES:Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;

    .line 60
    .line 61
    invoke-static {v0, v5}, LNA0/g;->a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    if-eqz v0, :cond_1

    .line 66
    .line 67
    invoke-static {v0}, Lkotlin/text/u;->v(Ljava/lang/String;)Ljava/lang/Float;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    if-eqz v0, :cond_1

    .line 72
    .line 73
    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    .line 74
    .line 75
    .line 76
    move-result v0

    .line 77
    move v6, v0

    .line 78
    goto :goto_1

    .line 79
    :cond_1
    const/4 v6, 0x0

    .line 80
    :goto_1
    invoke-virtual {p0}, LYA0/a;->z()Ljava/util/List;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    sget-object v5, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;->RIGHT_ACTION_ZONES:Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;

    .line 85
    .line 86
    invoke-static {v0, v5}, LNA0/g;->a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;

    .line 87
    .line 88
    .line 89
    move-result-object v0

    .line 90
    if-eqz v0, :cond_2

    .line 91
    .line 92
    invoke-static {v0}, Lkotlin/text/u;->v(Ljava/lang/String;)Ljava/lang/Float;

    .line 93
    .line 94
    .line 95
    move-result-object v0

    .line 96
    if-eqz v0, :cond_2

    .line 97
    .line 98
    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    .line 99
    .line 100
    .line 101
    move-result v1

    .line 102
    move v5, v1

    .line 103
    goto :goto_2

    .line 104
    :cond_2
    const/4 v5, 0x0

    .line 105
    :goto_2
    invoke-virtual {p0}, LYA0/a;->h()Z

    .line 106
    .line 107
    .line 108
    move-result v8

    .line 109
    invoke-virtual {p0}, LYA0/a;->r()LYA0/e;

    .line 110
    .line 111
    .line 112
    move-result-object v0

    .line 113
    invoke-virtual {v0}, LYA0/e;->d()Ljava/lang/String;

    .line 114
    .line 115
    .line 116
    move-result-object v9

    .line 117
    invoke-virtual {p0}, LYA0/a;->O()Ljava/lang/String;

    .line 118
    .line 119
    .line 120
    move-result-object v10

    .line 121
    invoke-virtual {p0}, LYA0/a;->f()Ljava/lang/String;

    .line 122
    .line 123
    .line 124
    move-result-object v11

    .line 125
    invoke-virtual {p0}, LYA0/a;->v()J

    .line 126
    .line 127
    .line 128
    move-result-wide v12

    .line 129
    new-instance v1, LRA0/a;

    .line 130
    .line 131
    invoke-direct/range {v1 .. v13}, LRA0/a;-><init>(LRA0/g;Ljava/util/List;Ljava/util/List;FFFZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V

    .line 132
    .line 133
    .line 134
    return-object v1

    .line 135
    :cond_3
    const/4 p0, 0x0

    .line 136
    return-object p0
.end method
