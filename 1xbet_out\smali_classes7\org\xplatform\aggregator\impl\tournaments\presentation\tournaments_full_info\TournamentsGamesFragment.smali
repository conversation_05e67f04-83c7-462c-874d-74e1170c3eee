.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u0000 42\u00020\u0001:\u00015B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u000f\u0010\t\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\t\u0010\u0003J\u0017\u0010\u000c\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0017\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u0008J\u0017\u0010\u0010\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\rJ\u000f\u0010\u0011\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0011\u0010\u0003J\u000f\u0010\u0012\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0012\u0010\u0003J\u0019\u0010\u0015\u001a\u00020\u00062\u0008\u0010\u0014\u001a\u0004\u0018\u00010\u0013H\u0014\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u000f\u0010\u0017\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0017\u0010\u0003R\u001b\u0010\u001d\u001a\u00020\u00188BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0019\u0010\u001a\u001a\u0004\u0008\u001b\u0010\u001cR\"\u0010%\u001a\u00020\u001e8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u001f\u0010 \u001a\u0004\u0008!\u0010\"\"\u0004\u0008#\u0010$R\"\u0010-\u001a\u00020&8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\'\u0010(\u001a\u0004\u0008)\u0010*\"\u0004\u0008+\u0010,R\u001b\u00103\u001a\u00020.8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008/\u00100\u001a\u0004\u00081\u00102\u00a8\u00066"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "lottieConfig",
        "",
        "M2",
        "(Lorg/xbet/uikit/components/lottie_empty/n;)V",
        "L2",
        "",
        "loading",
        "d",
        "(Z)V",
        "N2",
        "buttonVisible",
        "F2",
        "x2",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "LS91/h0;",
        "i0",
        "LRc/c;",
        "H2",
        "()LS91/h0;",
        "viewBinding",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "j0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "getViewModelFactory",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "LSX0/c;",
        "k0",
        "LSX0/c;",
        "G2",
        "()LSX0/c;",
        "setLottieEmptyConfigurator",
        "(LSX0/c;)V",
        "lottieEmptyConfigurator",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;",
        "l0",
        "Lkotlin/j;",
        "I2",
        "()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;",
        "viewModel",
        "m0",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final m0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic n0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final i0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public j0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public k0:LSX0/c;

.field public final l0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getViewBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentTournamentsGamesBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;

    .line 7
    .line 8
    const-string v4, "viewBinding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->n0:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->m0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$a;

    .line 31
    .line 32
    return-void
.end method

.method public constructor <init>()V
    .locals 5

    .line 1
    sget v0, Lu91/c;->fragment_tournaments_games:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$viewBinding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$viewBinding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->i0:LRc/c;

    .line 13
    .line 14
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/C;

    .line 15
    .line 16
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/C;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;)V

    .line 17
    .line 18
    .line 19
    sget-object v1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 20
    .line 21
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$special$$inlined$viewModels$default$1;

    .line 22
    .line 23
    invoke-direct {v2, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$special$$inlined$viewModels$default$1;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 24
    .line 25
    .line 26
    invoke-static {v1, v2}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    const-class v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 31
    .line 32
    invoke-static {v1}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$special$$inlined$viewModels$default$2;

    .line 37
    .line 38
    invoke-direct {v2, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/j;)V

    .line 39
    .line 40
    .line 41
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$special$$inlined$viewModels$default$3;

    .line 42
    .line 43
    const/4 v4, 0x0

    .line 44
    invoke-direct {v3, v4, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 45
    .line 46
    .line 47
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$special$$inlined$viewModels$default$4;

    .line 48
    .line 49
    invoke-direct {v4, p0, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$special$$inlined$viewModels$default$4;-><init>(Landroidx/fragment/app/Fragment;Lkotlin/j;)V

    .line 50
    .line 51
    .line 52
    invoke-static {p0, v1, v2, v3, v4}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->l0:Lkotlin/j;

    .line 57
    .line 58
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;Landroidx/paging/f;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->J2(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;Landroidx/paging/f;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic B2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->F2(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;)LS91/h0;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->H2()LS91/h0;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic D2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->M2(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->d(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final F2(Z)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->H2()LS91/h0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/h0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    if-eqz p1, :cond_0

    .line 12
    .line 13
    sget p1, Lpb/f;->space_8:I

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    sget p1, Lpb/f;->space_48:I

    .line 17
    .line 18
    :goto_0
    invoke-virtual {v1, p1}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    invoke-virtual {v0}, Landroid/view/View;->getPaddingLeft()I

    .line 23
    .line 24
    .line 25
    move-result v1

    .line 26
    invoke-virtual {v0}, Landroid/view/View;->getPaddingTop()I

    .line 27
    .line 28
    .line 29
    move-result v2

    .line 30
    invoke-virtual {v0}, Landroid/view/View;->getPaddingRight()I

    .line 31
    .line 32
    .line 33
    move-result v3

    .line 34
    invoke-virtual {v0, v1, v2, v3, p1}, Landroid/view/View;->setPadding(IIII)V

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method private final I2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->l0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final J2(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;Landroidx/paging/f;)Lkotlin/Unit;
    .locals 18

    .line 1
    move-object/from16 v0, p1

    .line 2
    .line 3
    invoke-virtual/range {p2 .. p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    instance-of v1, v1, Landroidx/paging/s$b;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    const/4 v3, 0x1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-virtual/range {p0 .. p0}, Landroidx/paging/PagingDataAdapter;->getItemCount()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    if-nez v1, :cond_0

    .line 18
    .line 19
    const/4 v1, 0x1

    .line 20
    goto :goto_0

    .line 21
    :cond_0
    const/4 v1, 0x0

    .line 22
    :goto_0
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->d(Z)V

    .line 23
    .line 24
    .line 25
    invoke-virtual/range {p2 .. p2}, Landroidx/paging/f;->e()Landroidx/paging/u;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    invoke-virtual {v1}, Landroidx/paging/u;->d()Landroidx/paging/s;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    instance-of v4, v1, Landroidx/paging/s$a;

    .line 34
    .line 35
    const/4 v5, 0x0

    .line 36
    if-eqz v4, :cond_1

    .line 37
    .line 38
    check-cast v1, Landroidx/paging/s$a;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-object v1, v5

    .line 42
    :goto_1
    if-nez v1, :cond_6

    .line 43
    .line 44
    invoke-virtual/range {p2 .. p2}, Landroidx/paging/f;->e()Landroidx/paging/u;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    invoke-virtual {v1}, Landroidx/paging/u;->e()Landroidx/paging/s;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    instance-of v4, v1, Landroidx/paging/s$a;

    .line 53
    .line 54
    if-eqz v4, :cond_2

    .line 55
    .line 56
    check-cast v1, Landroidx/paging/s$a;

    .line 57
    .line 58
    goto :goto_2

    .line 59
    :cond_2
    move-object v1, v5

    .line 60
    :goto_2
    if-nez v1, :cond_6

    .line 61
    .line 62
    invoke-virtual/range {p2 .. p2}, Landroidx/paging/f;->e()Landroidx/paging/u;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    invoke-virtual {v1}, Landroidx/paging/u;->f()Landroidx/paging/s;

    .line 67
    .line 68
    .line 69
    move-result-object v1

    .line 70
    instance-of v4, v1, Landroidx/paging/s$a;

    .line 71
    .line 72
    if-eqz v4, :cond_3

    .line 73
    .line 74
    check-cast v1, Landroidx/paging/s$a;

    .line 75
    .line 76
    goto :goto_3

    .line 77
    :cond_3
    move-object v1, v5

    .line 78
    :goto_3
    if-nez v1, :cond_6

    .line 79
    .line 80
    invoke-virtual/range {p2 .. p2}, Landroidx/paging/f;->a()Landroidx/paging/s;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    instance-of v4, v1, Landroidx/paging/s$a;

    .line 85
    .line 86
    if-eqz v4, :cond_4

    .line 87
    .line 88
    check-cast v1, Landroidx/paging/s$a;

    .line 89
    .line 90
    goto :goto_4

    .line 91
    :cond_4
    move-object v1, v5

    .line 92
    :goto_4
    if-nez v1, :cond_6

    .line 93
    .line 94
    invoke-virtual/range {p2 .. p2}, Landroidx/paging/f;->c()Landroidx/paging/s;

    .line 95
    .line 96
    .line 97
    move-result-object v1

    .line 98
    instance-of v4, v1, Landroidx/paging/s$a;

    .line 99
    .line 100
    if-eqz v4, :cond_5

    .line 101
    .line 102
    check-cast v1, Landroidx/paging/s$a;

    .line 103
    .line 104
    goto :goto_5

    .line 105
    :cond_5
    move-object v1, v5

    .line 106
    :goto_5
    if-nez v1, :cond_6

    .line 107
    .line 108
    invoke-virtual/range {p2 .. p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 109
    .line 110
    .line 111
    move-result-object v1

    .line 112
    instance-of v4, v1, Landroidx/paging/s$a;

    .line 113
    .line 114
    if-eqz v4, :cond_7

    .line 115
    .line 116
    move-object v5, v1

    .line 117
    check-cast v5, Landroidx/paging/s$a;

    .line 118
    .line 119
    goto :goto_6

    .line 120
    :cond_6
    move-object v5, v1

    .line 121
    :cond_7
    :goto_6
    if-eqz v5, :cond_8

    .line 122
    .line 123
    invoke-virtual {v5}, Landroidx/paging/s$a;->b()Ljava/lang/Throwable;

    .line 124
    .line 125
    .line 126
    move-result-object v1

    .line 127
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->I2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 128
    .line 129
    .line 130
    move-result-object v4

    .line 131
    invoke-virtual {v4, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->m4(Ljava/lang/Throwable;)V

    .line 132
    .line 133
    .line 134
    :cond_8
    invoke-virtual/range {p2 .. p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 135
    .line 136
    .line 137
    move-result-object v1

    .line 138
    instance-of v1, v1, Landroidx/paging/s$b;

    .line 139
    .line 140
    if-nez v1, :cond_9

    .line 141
    .line 142
    invoke-virtual/range {p0 .. p0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 143
    .line 144
    .line 145
    move-result v1

    .line 146
    if-nez v1, :cond_9

    .line 147
    .line 148
    if-nez v5, :cond_9

    .line 149
    .line 150
    const/4 v2, 0x1

    .line 151
    :cond_9
    if-eqz v2, :cond_a

    .line 152
    .line 153
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->G2()LSX0/c;

    .line 154
    .line 155
    .line 156
    move-result-object v6

    .line 157
    sget-object v7, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 158
    .line 159
    sget v12, Lpb/k;->data_retrieval_error:I

    .line 160
    .line 161
    const/16 v16, 0x1de

    .line 162
    .line 163
    const/16 v17, 0x0

    .line 164
    .line 165
    const/4 v8, 0x0

    .line 166
    const/4 v9, 0x0

    .line 167
    const/4 v10, 0x0

    .line 168
    const/4 v11, 0x0

    .line 169
    const/4 v13, 0x0

    .line 170
    const/4 v14, 0x0

    .line 171
    const/4 v15, 0x0

    .line 172
    invoke-static/range {v6 .. v17}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 173
    .line 174
    .line 175
    move-result-object v1

    .line 176
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->N2(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 177
    .line 178
    .line 179
    :cond_a
    invoke-virtual/range {p2 .. p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 180
    .line 181
    .line 182
    move-result-object v0

    .line 183
    instance-of v0, v0, Landroidx/paging/s$b;

    .line 184
    .line 185
    if-nez v0, :cond_b

    .line 186
    .line 187
    if-nez v5, :cond_b

    .line 188
    .line 189
    invoke-virtual/range {p0 .. p0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 190
    .line 191
    .line 192
    :cond_b
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 193
    .line 194
    return-object v0
.end method

.method public static final K2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;LN21/k;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->I2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p1}, LN21/k;->e()J

    .line 12
    .line 13
    .line 14
    move-result-wide v1

    .line 15
    invoke-virtual {p0, v0, v1, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->u4(Ljava/lang/String;J)V

    .line 16
    .line 17
    .line 18
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 19
    .line 20
    return-object p0
.end method

.method private final L2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->H2()LS91/h0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/h0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-virtual {v0, v1}, Landroid/view/ViewGroup;->setMotionEventSplittingEnabled(Z)V

    .line 9
    .line 10
    .line 11
    const/4 v1, 0x0

    .line 12
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setItemAnimator(Landroidx/recyclerview/widget/RecyclerView$m;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method private final M2(Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->H2()LS91/h0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/h0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->N2(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method private final N2(Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->H2()LS91/h0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/h0;->b:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 8
    .line 9
    .line 10
    const/4 p1, 0x0

    .line 11
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->H2()LS91/h0;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    iget-object p1, p1, LS91/h0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 19
    .line 20
    const/16 v0, 0x8

    .line 21
    .line 22
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public static final O2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;)Landroidx/lifecycle/h0;
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final d(Z)V
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->H2()LS91/h0;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    iget-object p1, p1, LS91/h0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 8
    .line 9
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->o()V

    .line 10
    .line 11
    .line 12
    :cond_0
    return-void
.end method

.method public static synthetic y2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;LN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->K2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;LN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;)Landroidx/lifecycle/h0;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->O2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;)Landroidx/lifecycle/h0;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final G2()LSX0/c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->k0:LSX0/c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final H2()LS91/h0;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->i0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->n0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/h0;

    .line 13
    .line 14
    return-object v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 2

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->L2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->H2()LS91/h0;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object p1, p1, LS91/h0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 9
    .line 10
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->I2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->c4()I

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setStyle(I)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->H2()LS91/h0;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    iget-object p1, p1, LS91/h0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 26
    .line 27
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    sget v1, LlZ0/g;->space_8:I

    .line 32
    .line 33
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 34
    .line 35
    .line 36
    move-result v0

    .line 37
    const/4 v1, 0x0

    .line 38
    invoke-virtual {p1, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->q(II)V

    .line 39
    .line 40
    .line 41
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->H2()LS91/h0;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    iget-object p1, p1, LS91/h0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 46
    .line 47
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->getPagingAdapter()Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    if-eqz p1, :cond_0

    .line 52
    .line 53
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/D;

    .line 54
    .line 55
    invoke-direct {v0, p1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/D;-><init>(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;)V

    .line 56
    .line 57
    .line 58
    invoke-virtual {p1, v0}, Landroidx/paging/PagingDataAdapter;->p(Lkotlin/jvm/functions/Function1;)V

    .line 59
    .line 60
    .line 61
    :cond_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->H2()LS91/h0;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    iget-object p1, p1, LS91/h0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 66
    .line 67
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/E;

    .line 68
    .line 69
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/E;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;)V

    .line 70
    .line 71
    .line 72
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 73
    .line 74
    .line 75
    return-void
.end method

.method public u2()V
    .locals 1

    .line 1
    invoke-static {p0}, LVa1/w;->a(Landroidx/fragment/app/Fragment;)LVa1/r;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0, p0}, LVa1/r;->e(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public v2()V
    .locals 14

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->I2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->k4()Lkotlinx/coroutines/flow/f0;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->RESUMED:Landroidx/lifecycle/Lifecycle$State;

    .line 10
    .line 11
    new-instance v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$onObserveData$1;

    .line 12
    .line 13
    const/4 v0, 0x0

    .line 14
    invoke-direct {v5, p0, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;Lkotlin/coroutines/e;)V

    .line 15
    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v7

    .line 25
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$onObserveData$$inlined$observeWithLifecycle$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$onObserveData$$inlined$observeWithLifecycle$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    move-object v6, v7

    .line 34
    const/4 v7, 0x0

    .line 35
    const/4 v8, 0x0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->I2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->h4()Lkotlinx/coroutines/flow/e;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    new-instance v7, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$onObserveData$2;

    .line 49
    .line 50
    invoke-direct {v7, p0, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$onObserveData$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;Lkotlin/coroutines/e;)V

    .line 51
    .line 52
    .line 53
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 54
    .line 55
    .line 56
    move-result-object v5

    .line 57
    invoke-static {v5}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$onObserveData$$inlined$observeWithLifecycle$2;

    .line 62
    .line 63
    move-object v6, v4

    .line 64
    move-object v4, v1

    .line 65
    invoke-direct/range {v3 .. v8}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment$onObserveData$$inlined$observeWithLifecycle$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 66
    .line 67
    .line 68
    const/4 v12, 0x3

    .line 69
    const/4 v13, 0x0

    .line 70
    const/4 v9, 0x0

    .line 71
    const/4 v10, 0x0

    .line 72
    move-object v8, v0

    .line 73
    move-object v11, v3

    .line 74
    invoke-static/range {v8 .. v13}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 75
    .line 76
    .line 77
    return-void
.end method

.method public x2()V
    .locals 0

    .line 1
    return-void
.end method
