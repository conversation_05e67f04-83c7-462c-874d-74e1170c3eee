.class final Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.african_roulette.presentation.game.AfricanRouletteViewModel$makeBet$2"
    f = "AfricanRouletteViewModel.kt"
    l = {
        0xe0
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->c4(D)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $betSum:D

.field D$0:D

.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;DLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;",
            "D",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    iput-wide p2, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->$betSum:D

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;

    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    iget-wide v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->$betSum:D

    invoke-direct {p1, v0, v1, v2, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;DLkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    iget-wide v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->D$0:D

    .line 13
    .line 14
    iget-object v2, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->L$1:Ljava/lang/Object;

    .line 15
    .line 16
    check-cast v2, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 17
    .line 18
    iget-object v3, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->L$0:Ljava/lang/Object;

    .line 19
    .line 20
    check-cast v3, Lorg/xbet/games_section/api/models/GameBonusType;

    .line 21
    .line 22
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 23
    .line 24
    .line 25
    move-object v5, v3

    .line 26
    move-object v3, v2

    .line 27
    move-wide v1, v0

    .line 28
    goto :goto_0

    .line 29
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 30
    .line 31
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 32
    .line 33
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 34
    .line 35
    .line 36
    throw p1

    .line 37
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 38
    .line 39
    .line 40
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 41
    .line 42
    invoke-static {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    invoke-virtual {p1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->q()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    invoke-virtual {p1}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->isNotEmpty()Z

    .line 51
    .line 52
    .line 53
    move-result v1

    .line 54
    if-eqz v1, :cond_4

    .line 55
    .line 56
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 57
    .line 58
    invoke-static {v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->B3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/core/domain/usecases/bonus/e;

    .line 59
    .line 60
    .line 61
    move-result-object v1

    .line 62
    invoke-virtual {v1}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    invoke-virtual {v1}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 67
    .line 68
    .line 69
    move-result-object v3

    .line 70
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 71
    .line 72
    invoke-static {v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 73
    .line 74
    .line 75
    move-result-object v1

    .line 76
    invoke-virtual {v1, v3}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->v(Lorg/xbet/games_section/api/models/GameBonusType;)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {v3}, Lorg/xbet/games_section/api/models/GameBonusType;->isGameBonus()Z

    .line 80
    .line 81
    .line 82
    move-result v1

    .line 83
    if-eqz v1, :cond_2

    .line 84
    .line 85
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 86
    .line 87
    invoke-static {v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    invoke-virtual {v1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->s()V

    .line 92
    .line 93
    .line 94
    :cond_2
    iget-wide v4, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->$betSum:D

    .line 95
    .line 96
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 97
    .line 98
    invoke-static {v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->C3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    iput-object v3, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->L$0:Ljava/lang/Object;

    .line 103
    .line 104
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->L$1:Ljava/lang/Object;

    .line 105
    .line 106
    iput-wide v4, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->D$0:D

    .line 107
    .line 108
    iput v2, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->label:I

    .line 109
    .line 110
    invoke-virtual {v1, p0}, Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 111
    .line 112
    .line 113
    move-result-object v1

    .line 114
    if-ne v1, v0, :cond_3

    .line 115
    .line 116
    return-object v0

    .line 117
    :cond_3
    move-object v6, v3

    .line 118
    move-object v3, p1

    .line 119
    move-object p1, v1

    .line 120
    move-wide v1, v4

    .line 121
    move-object v5, v6

    .line 122
    :goto_0
    move-object v4, p1

    .line 123
    check-cast v4, Ljava/lang/String;

    .line 124
    .line 125
    new-instance v0, Lig/a;

    .line 126
    .line 127
    invoke-direct/range {v0 .. v5}, Lig/a;-><init>(DLorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;Ljava/lang/String;Lorg/xbet/games_section/api/models/GameBonusType;)V

    .line 128
    .line 129
    .line 130
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 131
    .line 132
    invoke-static {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 133
    .line 134
    .line 135
    move-result-object p1

    .line 136
    invoke-virtual {p1, v0}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e(Lig/a;)V

    .line 137
    .line 138
    .line 139
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 140
    .line 141
    invoke-static {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->H3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/core/domain/usecases/game_info/H;

    .line 142
    .line 143
    .line 144
    move-result-object p1

    .line 145
    iget-wide v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->$betSum:D

    .line 146
    .line 147
    invoke-virtual {p1, v0, v1}, Lorg/xbet/core/domain/usecases/game_info/H;->a(D)V

    .line 148
    .line 149
    .line 150
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 151
    .line 152
    invoke-static {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 153
    .line 154
    .line 155
    move-result-object p1

    .line 156
    sget-object v0, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->EMPTY:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 157
    .line 158
    invoke-virtual {p1, v0}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->x(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 159
    .line 160
    .line 161
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$makeBet$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 162
    .line 163
    sget-object v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$a;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$a;

    .line 164
    .line 165
    invoke-static {p1, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->N3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;)V

    .line 166
    .line 167
    .line 168
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 169
    .line 170
    return-object p1
.end method
