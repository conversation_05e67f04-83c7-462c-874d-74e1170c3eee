.class public final Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoBannersContainerViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a;\u0010\n\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\t0\u00080\u00072\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u00002\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "Lkotlin/Function2;",
        "LrZ0/b;",
        "",
        "",
        "onBannerClick",
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "LA4/c;",
        "",
        "LVX0/i;",
        "g",
        "(Lkotlin/jvm/functions/Function2;LUX0/k;)LA4/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoBannersContainerViewHolderKt;->l(LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function2;LrZ0/b;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoBannersContainerViewHolderKt;->j(Lkotlin/jvm/functions/Function2;LrZ0/b;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function2;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoBannersContainerViewHolderKt;->i(Lkotlin/jvm/functions/Function2;LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/w0;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoBannersContainerViewHolderKt;->h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/w0;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoBannersContainerViewHolderKt;->m(LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoBannersContainerViewHolderKt;->k(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function2;LUX0/k;)LA4/c;
    .locals 3
    .param p0    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LUX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "LrZ0/b;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;",
            "LUX0/k;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LBa1/a;

    .line 2
    .line 3
    invoke-direct {v0}, LBa1/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LBa1/b;

    .line 7
    .line 8
    invoke-direct {v1, p0, p1}, LBa1/b;-><init>(Lkotlin/jvm/functions/Function2;LUX0/k;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoBannersContainerViewHolderKt$promoBannerContainerViewHolder$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoBannersContainerViewHolderKt$promoBannerContainerViewHolder$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoBannersContainerViewHolderKt$promoBannerContainerViewHolder$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoBannersContainerViewHolderKt$promoBannerContainerViewHolder$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v2, LB4/b;

    .line 19
    .line 20
    invoke-direct {v2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v2
.end method

.method public static final h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/w0;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/w0;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/w0;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final i(Lkotlin/jvm/functions/Function2;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p2}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LS91/w0;

    .line 6
    .line 7
    iget-object v0, v0, LS91/w0;->b:Lorg/xbet/uikit/components/bannercollection/BannerCollection;

    .line 8
    .line 9
    new-instance v1, LBa1/c;

    .line 10
    .line 11
    invoke-direct {v1, p0}, LBa1/c;-><init>(Lkotlin/jvm/functions/Function2;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/bannercollection/BannerCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function2;)V

    .line 15
    .line 16
    .line 17
    new-instance p0, LBa1/d;

    .line 18
    .line 19
    invoke-direct {p0, p2}, LBa1/d;-><init>(LB4/a;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p2, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 23
    .line 24
    .line 25
    new-instance p0, LBa1/e;

    .line 26
    .line 27
    invoke-direct {p0, p1, p2}, LBa1/e;-><init>(LUX0/k;LB4/a;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {p2, p0}, LB4/a;->r(Lkotlin/jvm/functions/Function0;)V

    .line 31
    .line 32
    .line 33
    new-instance p0, LBa1/f;

    .line 34
    .line 35
    invoke-direct {p0, p1, p2}, LBa1/f;-><init>(LUX0/k;LB4/a;)V

    .line 36
    .line 37
    .line 38
    invoke-virtual {p2, p0}, LB4/a;->s(Lkotlin/jvm/functions/Function0;)V

    .line 39
    .line 40
    .line 41
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 42
    .line 43
    return-object p0
.end method

.method public static final j(Lkotlin/jvm/functions/Function2;LrZ0/b;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    invoke-interface {p0, p1, p2}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final k(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LS91/w0;

    .line 6
    .line 7
    iget-object p1, p1, LS91/w0;->b:Lorg/xbet/uikit/components/bannercollection/BannerCollection;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, Lza1/a$a$a;

    .line 14
    .line 15
    invoke-virtual {p0}, Lza1/a$a$a;->d()Lorg/xbet/uikit/components/bannercollection/a;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {p1, p0}, Lorg/xbet/uikit/components/bannercollection/BannerCollection;->setItems(Lorg/xbet/uikit/components/bannercollection/a;)V

    .line 20
    .line 21
    .line 22
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 23
    .line 24
    return-object p0
.end method

.method public static final l(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getAbsoluteAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, LS91/w0;

    .line 14
    .line 15
    iget-object p1, p1, LS91/w0;->b:Lorg/xbet/uikit/components/bannercollection/BannerCollection;

    .line 16
    .line 17
    invoke-virtual {p0, v0, p1}, LUX0/k;->c(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method

.method public static final m(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getAbsoluteAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, LS91/w0;

    .line 14
    .line 15
    iget-object p1, p1, LS91/w0;->b:Lorg/xbet/uikit/components/bannercollection/BannerCollection;

    .line 16
    .line 17
    invoke-virtual {p0, v0, p1}, LUX0/k;->e(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method
