.class public final Ll31/w;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LL2/a;


# instance fields
.field public final a:Landroid/view/View;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final b:Landroid/widget/ImageView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final c:Landroid/widget/ImageView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardImageView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final e:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/ActionCardTransparencyLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final f:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardShimmerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final g:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final h:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardRoundedTag;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final j:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final k:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final l:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final m:Landroid/view/View;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/view/View;Landroid/widget/ImageView;Landroid/widget/ImageView;Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardImageView;Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/ActionCardTransparencyLayout;Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardShimmerView;Lorg/xbet/uikit/components/shimmer/ShimmerView;Lorg/xbet/uikit/components/shimmer/ShimmerView;Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardRoundedTag;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/view/View;)V
    .locals 0
    .param p1    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Landroid/widget/ImageView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Landroid/widget/ImageView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardImageView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/ActionCardTransparencyLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardShimmerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/uikit/components/shimmer/ShimmerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/uikit/components/shimmer/ShimmerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardRoundedTag;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p10    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p11    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p12    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p13    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Ll31/w;->a:Landroid/view/View;

    .line 5
    .line 6
    iput-object p2, p0, Ll31/w;->b:Landroid/widget/ImageView;

    .line 7
    .line 8
    iput-object p3, p0, Ll31/w;->c:Landroid/widget/ImageView;

    .line 9
    .line 10
    iput-object p4, p0, Ll31/w;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardImageView;

    .line 11
    .line 12
    iput-object p5, p0, Ll31/w;->e:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/ActionCardTransparencyLayout;

    .line 13
    .line 14
    iput-object p6, p0, Ll31/w;->f:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardShimmerView;

    .line 15
    .line 16
    iput-object p7, p0, Ll31/w;->g:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 17
    .line 18
    iput-object p8, p0, Ll31/w;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 19
    .line 20
    iput-object p9, p0, Ll31/w;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardRoundedTag;

    .line 21
    .line 22
    iput-object p10, p0, Ll31/w;->j:Landroid/widget/TextView;

    .line 23
    .line 24
    iput-object p11, p0, Ll31/w;->k:Landroid/widget/TextView;

    .line 25
    .line 26
    iput-object p12, p0, Ll31/w;->l:Landroid/widget/TextView;

    .line 27
    .line 28
    iput-object p13, p0, Ll31/w;->m:Landroid/view/View;

    .line 29
    .line 30
    return-void
.end method

.method public static a(Landroid/view/View;)Ll31/w;
    .locals 14
    .param p0    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    sget v0, LS11/d;->ivAction:I

    .line 2
    .line 3
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object v2

    .line 7
    check-cast v2, Landroid/widget/ImageView;

    .line 8
    .line 9
    if-eqz v2, :cond_0

    .line 10
    .line 11
    sget v0, LS11/d;->ivActionCardIcon:I

    .line 12
    .line 13
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 14
    .line 15
    .line 16
    move-result-object v3

    .line 17
    check-cast v3, Landroid/widget/ImageView;

    .line 18
    .line 19
    if-eqz v3, :cond_0

    .line 20
    .line 21
    sget v0, LS11/d;->ivBanner:I

    .line 22
    .line 23
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    check-cast v4, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardImageView;

    .line 28
    .line 29
    if-eqz v4, :cond_0

    .line 30
    .line 31
    sget v0, LS11/d;->llActionCard:I

    .line 32
    .line 33
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 34
    .line 35
    .line 36
    move-result-object v5

    .line 37
    check-cast v5, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/ActionCardTransparencyLayout;

    .line 38
    .line 39
    if-eqz v5, :cond_0

    .line 40
    .line 41
    sget v0, LS11/d;->shimmerImageView:I

    .line 42
    .line 43
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 44
    .line 45
    .line 46
    move-result-object v6

    .line 47
    check-cast v6, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardShimmerView;

    .line 48
    .line 49
    if-eqz v6, :cond_0

    .line 50
    .line 51
    sget v0, LS11/d;->shimmerSubtitle:I

    .line 52
    .line 53
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 54
    .line 55
    .line 56
    move-result-object v7

    .line 57
    check-cast v7, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 58
    .line 59
    if-eqz v7, :cond_0

    .line 60
    .line 61
    sget v0, LS11/d;->shimmerTitle:I

    .line 62
    .line 63
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 64
    .line 65
    .line 66
    move-result-object v8

    .line 67
    check-cast v8, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 68
    .line 69
    if-eqz v8, :cond_0

    .line 70
    .line 71
    sget v0, LS11/d;->tag:I

    .line 72
    .line 73
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 74
    .line 75
    .line 76
    move-result-object v9

    .line 77
    check-cast v9, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardRoundedTag;

    .line 78
    .line 79
    if-eqz v9, :cond_0

    .line 80
    .line 81
    sget v0, LS11/d;->tvActionCardLabel:I

    .line 82
    .line 83
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 84
    .line 85
    .line 86
    move-result-object v10

    .line 87
    check-cast v10, Landroid/widget/TextView;

    .line 88
    .line 89
    if-eqz v10, :cond_0

    .line 90
    .line 91
    sget v0, LS11/d;->tvSubtitle:I

    .line 92
    .line 93
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 94
    .line 95
    .line 96
    move-result-object v11

    .line 97
    check-cast v11, Landroid/widget/TextView;

    .line 98
    .line 99
    if-eqz v11, :cond_0

    .line 100
    .line 101
    sget v0, LS11/d;->tvTitle:I

    .line 102
    .line 103
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 104
    .line 105
    .line 106
    move-result-object v12

    .line 107
    check-cast v12, Landroid/widget/TextView;

    .line 108
    .line 109
    if-eqz v12, :cond_0

    .line 110
    .line 111
    sget v0, LS11/d;->viewAction:I

    .line 112
    .line 113
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 114
    .line 115
    .line 116
    move-result-object v13

    .line 117
    if-eqz v13, :cond_0

    .line 118
    .line 119
    new-instance v0, Ll31/w;

    .line 120
    .line 121
    move-object v1, p0

    .line 122
    invoke-direct/range {v0 .. v13}, Ll31/w;-><init>(Landroid/view/View;Landroid/widget/ImageView;Landroid/widget/ImageView;Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardImageView;Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/ActionCardTransparencyLayout;Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardShimmerView;Lorg/xbet/uikit/components/shimmer/ShimmerView;Lorg/xbet/uikit/components/shimmer/ShimmerView;Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/view/GameCardRoundedTag;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/view/View;)V

    .line 123
    .line 124
    .line 125
    return-object v0

    .line 126
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 127
    .line 128
    .line 129
    move-result-object v1

    .line 130
    invoke-virtual {v1, v0}, Landroid/content/res/Resources;->getResourceName(I)Ljava/lang/String;

    .line 131
    .line 132
    .line 133
    move-result-object v0

    .line 134
    new-instance v1, Ljava/lang/NullPointerException;

    .line 135
    .line 136
    const-string v2, "Missing required view with ID: "

    .line 137
    .line 138
    invoke-virtual {v2, v0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 139
    .line 140
    .line 141
    move-result-object v0

    .line 142
    invoke-direct {v1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 143
    .line 144
    .line 145
    throw v1
.end method

.method public static b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Ll31/w;
    .locals 1
    .param p0    # Landroid/view/LayoutInflater;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p1    # Landroid/view/ViewGroup;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    sget v0, LS11/f;->aggregator_game_card_item_transparency:I

    .line 4
    .line 5
    invoke-virtual {p0, v0, p1}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;)Landroid/view/View;

    .line 6
    .line 7
    .line 8
    invoke-static {p1}, Ll31/w;->a(Landroid/view/View;)Ll31/w;

    .line 9
    .line 10
    .line 11
    move-result-object p0

    .line 12
    return-object p0

    .line 13
    :cond_0
    new-instance p0, Ljava/lang/NullPointerException;

    .line 14
    .line 15
    const-string p1, "parent"

    .line 16
    .line 17
    invoke-direct {p0, p1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    throw p0
.end method


# virtual methods
.method public getRoot()Landroid/view/View;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    iget-object v0, p0, Ll31/w;->a:Landroid/view/View;

    .line 2
    .line 3
    return-object v0
.end method
