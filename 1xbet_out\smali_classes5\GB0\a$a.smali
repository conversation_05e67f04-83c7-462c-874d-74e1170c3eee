.class public final LGB0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LGB0/d$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LGB0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LGB0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LGB0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lm8/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;Lorg/xbet/sportgame/core/data/datasource/local/l;Lorg/xbet/sportgame/core/data/datasource/local/j;LEP/b;LKB0/a;Lorg/xbet/sportgame/core/data/datasource/local/r;)LGB0/d;
    .locals 15

    .line 1
    invoke-static/range {p1 .. p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static/range {p2 .. p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    invoke-static/range {p10 .. p10}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    invoke-static/range {p12 .. p12}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    invoke-static/range {p13 .. p13}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    new-instance v0, LGB0/a$b;

    .line 41
    .line 42
    const/4 v14, 0x0

    .line 43
    move-object/from16 v1, p1

    .line 44
    .line 45
    move-object/from16 v2, p2

    .line 46
    .line 47
    move-object/from16 v3, p3

    .line 48
    .line 49
    move-object/from16 v4, p4

    .line 50
    .line 51
    move-object/from16 v5, p5

    .line 52
    .line 53
    move-object/from16 v6, p6

    .line 54
    .line 55
    move-object/from16 v7, p7

    .line 56
    .line 57
    move-object/from16 v8, p8

    .line 58
    .line 59
    move-object/from16 v9, p9

    .line 60
    .line 61
    move-object/from16 v10, p10

    .line 62
    .line 63
    move-object/from16 v11, p11

    .line 64
    .line 65
    move-object/from16 v12, p12

    .line 66
    .line 67
    move-object/from16 v13, p13

    .line 68
    .line 69
    invoke-direct/range {v0 .. v14}, LGB0/a$b;-><init>(Lm8/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;Lorg/xbet/sportgame/core/data/datasource/local/l;Lorg/xbet/sportgame/core/data/datasource/local/j;LEP/b;LKB0/a;Lorg/xbet/sportgame/core/data/datasource/local/r;LGB0/b;)V

    .line 70
    .line 71
    .line 72
    return-object v0
.end method
