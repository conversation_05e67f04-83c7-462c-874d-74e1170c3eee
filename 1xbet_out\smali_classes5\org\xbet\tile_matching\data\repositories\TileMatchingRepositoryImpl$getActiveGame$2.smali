.class final Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.tile_matching.data.repositories.TileMatchingRepositoryImpl$getActiveGame$2"
    f = "TileMatchingRepositoryImpl.kt"
    l = {
        0x61,
        0x66
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->d(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/String;",
        "Lkotlin/coroutines/e<",
        "-",
        "LzT0/e;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "",
        "token",
        "LzT0/e;",
        "<anonymous>",
        "(Ljava/lang/String;)LzT0/e;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;


# direct methods
.method public constructor <init>(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
            "Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    iput-object p2, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;

    iget-object v1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    iget-object v2, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    invoke-direct {v0, v1, v2, p2}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;-><init>(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "LzT0/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    goto :goto_2

    .line 31
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    iget-object p1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->L$0:Ljava/lang/Object;

    .line 35
    .line 36
    check-cast p1, Ljava/lang/String;

    .line 37
    .line 38
    iget-object v1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 39
    .line 40
    sget-object v4, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2$a;->a:[I

    .line 41
    .line 42
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    aget v1, v4, v1

    .line 47
    .line 48
    if-eq v1, v3, :cond_5

    .line 49
    .line 50
    if-ne v1, v2, :cond_4

    .line 51
    .line 52
    iget-object v1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    .line 53
    .line 54
    iput v2, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->label:I

    .line 55
    .line 56
    invoke-static {v1, p1, p0}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->k(Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    if-ne p1, v0, :cond_3

    .line 61
    .line 62
    goto :goto_1

    .line 63
    :cond_3
    :goto_0
    check-cast p1, LwT0/d;

    .line 64
    .line 65
    invoke-static {p1}, LtT0/h;->b(LwT0/d;)LzT0/e;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    return-object p1

    .line 70
    :cond_4
    new-instance p1, Ljava/lang/EnumConstantNotPresentException;

    .line 71
    .line 72
    const-class v0, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 73
    .line 74
    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 75
    .line 76
    .line 77
    move-result-object v1

    .line 78
    invoke-direct {p1, v0, v1}, Ljava/lang/EnumConstantNotPresentException;-><init>(Ljava/lang/Class;Ljava/lang/String;)V

    .line 79
    .line 80
    .line 81
    throw p1

    .line 82
    :cond_5
    iget-object v1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->this$0:Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    .line 83
    .line 84
    iput v3, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;->label:I

    .line 85
    .line 86
    invoke-static {v1, p1, p0}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->j(Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    if-ne p1, v0, :cond_6

    .line 91
    .line 92
    :goto_1
    return-object v0

    .line 93
    :cond_6
    :goto_2
    check-cast p1, LvT0/d;

    .line 94
    .line 95
    invoke-static {p1}, LtT0/h;->a(LvT0/d;)LzT0/e;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    return-object p1
.end method
