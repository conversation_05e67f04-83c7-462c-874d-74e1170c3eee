.class final Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.favorite.data.repositories.AggregatorFavoritesRepositoryImpl$removeFavorite$2$1"
    f = "AggregatorFavoritesRepositoryImpl.kt"
    l = {
        0x16a,
        0x1fa
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $brandsApi:Z

.field final synthetic $gameId:J

.field final synthetic $subcategoryId:I

.field I$0:I

.field I$1:I

.field I$2:I

.field J$0:J

.field private synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ZJILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;",
            "ZJI",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    iput-boolean p2, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->$brandsApi:Z

    iput-wide p3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->$gameId:J

    iput p5, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->$subcategoryId:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p6}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->$brandsApi:Z

    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->$gameId:J

    iget v5, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->$subcategoryId:I

    move-object v6, p2

    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ZJILkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 19

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v2

    .line 7
    iget v0, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->label:I

    .line 8
    .line 9
    const/4 v3, 0x2

    .line 10
    const/4 v4, 0x0

    .line 11
    const/4 v5, 0x1

    .line 12
    if-eqz v0, :cond_2

    .line 13
    .line 14
    if-eq v0, v5, :cond_1

    .line 15
    .line 16
    if-ne v0, v3, :cond_0

    .line 17
    .line 18
    iget v0, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->I$2:I

    .line 19
    .line 20
    iget v6, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->I$1:I

    .line 21
    .line 22
    iget-wide v7, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->J$0:J

    .line 23
    .line 24
    iget-boolean v9, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->Z$0:Z

    .line 25
    .line 26
    iget v10, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->I$0:I

    .line 27
    .line 28
    iget-object v11, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->L$1:Ljava/lang/Object;

    .line 29
    .line 30
    check-cast v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 31
    .line 32
    iget-object v12, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->L$0:Ljava/lang/Object;

    .line 33
    .line 34
    check-cast v12, Lkotlinx/coroutines/N;

    .line 35
    .line 36
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 37
    .line 38
    .line 39
    move/from16 v16, v6

    .line 40
    .line 41
    move-wide v14, v7

    .line 42
    move v7, v10

    .line 43
    move-object v13, v11

    .line 44
    move v6, v0

    .line 45
    :goto_0
    move-object v8, v12

    .line 46
    move v12, v9

    .line 47
    goto :goto_1

    .line 48
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 49
    .line 50
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 51
    .line 52
    invoke-direct {v0, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 53
    .line 54
    .line 55
    throw v0

    .line 56
    :cond_1
    iget v6, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->I$2:I

    .line 57
    .line 58
    iget v7, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->I$1:I

    .line 59
    .line 60
    iget-wide v8, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->J$0:J

    .line 61
    .line 62
    iget-boolean v10, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->Z$0:Z

    .line 63
    .line 64
    iget v11, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->I$0:I

    .line 65
    .line 66
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->L$1:Ljava/lang/Object;

    .line 67
    .line 68
    move-object v12, v0

    .line 69
    check-cast v12, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 70
    .line 71
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->L$0:Ljava/lang/Object;

    .line 72
    .line 73
    move-object v13, v0

    .line 74
    check-cast v13, Lkotlinx/coroutines/N;

    .line 75
    .line 76
    :try_start_0
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 77
    .line 78
    .line 79
    goto/16 :goto_2

    .line 80
    .line 81
    :catchall_0
    move-exception v0

    .line 82
    move/from16 v18, v10

    .line 83
    .line 84
    move v10, v7

    .line 85
    move-wide v7, v8

    .line 86
    move/from16 v9, v18

    .line 87
    .line 88
    move-object/from16 v18, v13

    .line 89
    .line 90
    move-object v13, v12

    .line 91
    move-object/from16 v12, v18

    .line 92
    .line 93
    goto/16 :goto_4

    .line 94
    .line 95
    :cond_2
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 96
    .line 97
    .line 98
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->L$0:Ljava/lang/Object;

    .line 99
    .line 100
    check-cast v0, Lkotlinx/coroutines/N;

    .line 101
    .line 102
    iget-object v6, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 103
    .line 104
    invoke-static {v6}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->s(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)LT91/a;

    .line 105
    .line 106
    .line 107
    move-result-object v6

    .line 108
    invoke-virtual {v6, v5}, LT91/a;->p(Z)V

    .line 109
    .line 110
    .line 111
    iget-object v6, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 112
    .line 113
    iget-boolean v7, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->$brandsApi:Z

    .line 114
    .line 115
    iget-wide v8, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->$gameId:J

    .line 116
    .line 117
    iget v10, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->$subcategoryId:I

    .line 118
    .line 119
    move-object v13, v6

    .line 120
    move v12, v7

    .line 121
    move-wide v14, v8

    .line 122
    move/from16 v16, v10

    .line 123
    .line 124
    const/4 v6, 0x0

    .line 125
    const/4 v7, 0x0

    .line 126
    move-object v8, v0

    .line 127
    :goto_1
    :try_start_1
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 128
    .line 129
    invoke-static {v13}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->v(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 130
    .line 131
    .line 132
    move-result-object v0

    .line 133
    new-instance v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1$1$1;

    .line 134
    .line 135
    const/16 v17, 0x0

    .line 136
    .line 137
    invoke-direct/range {v11 .. v17}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1$1$1;-><init>(ZLorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;JILkotlin/coroutines/e;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_2

    .line 138
    .line 139
    .line 140
    move/from16 v10, v16

    .line 141
    .line 142
    :try_start_2
    iput-object v8, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->L$0:Ljava/lang/Object;

    .line 143
    .line 144
    iput-object v13, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->L$1:Ljava/lang/Object;

    .line 145
    .line 146
    iput v7, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->I$0:I

    .line 147
    .line 148
    iput-boolean v12, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->Z$0:Z

    .line 149
    .line 150
    iput-wide v14, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->J$0:J

    .line 151
    .line 152
    iput v10, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->I$1:I

    .line 153
    .line 154
    iput v6, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->I$2:I

    .line 155
    .line 156
    iput v5, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->label:I

    .line 157
    .line 158
    invoke-virtual {v0, v11, v1}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 159
    .line 160
    .line 161
    move-result-object v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 162
    if-ne v0, v2, :cond_3

    .line 163
    .line 164
    goto/16 :goto_8

    .line 165
    .line 166
    :cond_3
    move v11, v7

    .line 167
    move v7, v10

    .line 168
    move v10, v12

    .line 169
    move-object v12, v13

    .line 170
    move-object v13, v8

    .line 171
    move-wide v8, v14

    .line 172
    :goto_2
    :try_start_3
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 173
    .line 174
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 175
    .line 176
    .line 177
    move-result-object v0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 178
    goto/16 :goto_a

    .line 179
    .line 180
    :catchall_1
    move-exception v0

    .line 181
    :goto_3
    move v11, v7

    .line 182
    move v9, v12

    .line 183
    move-object v12, v8

    .line 184
    move-wide v7, v14

    .line 185
    goto :goto_4

    .line 186
    :catchall_2
    move-exception v0

    .line 187
    move/from16 v10, v16

    .line 188
    .line 189
    goto :goto_3

    .line 190
    :goto_4
    if-eqz v11, :cond_4

    .line 191
    .line 192
    instance-of v14, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 193
    .line 194
    if-eqz v14, :cond_4

    .line 195
    .line 196
    move-object v14, v0

    .line 197
    check-cast v14, Lcom/xbet/onexcore/data/model/ServerException;

    .line 198
    .line 199
    invoke-virtual {v14}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 200
    .line 201
    .line 202
    move-result v14

    .line 203
    if-eqz v14, :cond_4

    .line 204
    .line 205
    const/4 v14, 0x1

    .line 206
    goto :goto_5

    .line 207
    :cond_4
    const/4 v14, 0x0

    .line 208
    :goto_5
    instance-of v15, v0, Ljava/util/concurrent/CancellationException;

    .line 209
    .line 210
    if-nez v15, :cond_c

    .line 211
    .line 212
    instance-of v15, v0, Ljava/net/ConnectException;

    .line 213
    .line 214
    if-nez v15, :cond_c

    .line 215
    .line 216
    if-nez v14, :cond_c

    .line 217
    .line 218
    instance-of v14, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 219
    .line 220
    if-eqz v14, :cond_7

    .line 221
    .line 222
    move-object v14, v0

    .line 223
    check-cast v14, Lcom/xbet/onexcore/data/model/ServerException;

    .line 224
    .line 225
    invoke-virtual {v14}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 226
    .line 227
    .line 228
    move-result v15

    .line 229
    if-nez v15, :cond_6

    .line 230
    .line 231
    invoke-virtual {v14}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 232
    .line 233
    .line 234
    move-result v14

    .line 235
    if-eqz v14, :cond_5

    .line 236
    .line 237
    goto :goto_6

    .line 238
    :cond_5
    const/4 v14, 0x0

    .line 239
    goto :goto_7

    .line 240
    :cond_6
    :goto_6
    const/4 v14, 0x1

    .line 241
    goto :goto_7

    .line 242
    :cond_7
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 243
    .line 244
    .line 245
    move-result v14

    .line 246
    if-nez v14, :cond_5

    .line 247
    .line 248
    goto :goto_6

    .line 249
    :goto_7
    add-int/2addr v6, v5

    .line 250
    const/4 v15, 0x3

    .line 251
    if-gt v6, v15, :cond_a

    .line 252
    .line 253
    if-eqz v14, :cond_8

    .line 254
    .line 255
    goto :goto_9

    .line 256
    :cond_8
    new-instance v14, Ljava/lang/StringBuilder;

    .line 257
    .line 258
    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    .line 259
    .line 260
    .line 261
    const-string v15, "error ("

    .line 262
    .line 263
    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 264
    .line 265
    .line 266
    invoke-virtual {v14, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 267
    .line 268
    .line 269
    const-string v15, "): "

    .line 270
    .line 271
    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 272
    .line 273
    .line 274
    invoke-virtual {v14, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 275
    .line 276
    .line 277
    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 278
    .line 279
    .line 280
    move-result-object v0

    .line 281
    sget-object v14, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 282
    .line 283
    invoke-virtual {v14, v0}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 284
    .line 285
    .line 286
    iput-object v12, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->L$0:Ljava/lang/Object;

    .line 287
    .line 288
    iput-object v13, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->L$1:Ljava/lang/Object;

    .line 289
    .line 290
    iput v11, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->I$0:I

    .line 291
    .line 292
    iput-boolean v9, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->Z$0:Z

    .line 293
    .line 294
    iput-wide v7, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->J$0:J

    .line 295
    .line 296
    iput v10, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->I$1:I

    .line 297
    .line 298
    iput v6, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->I$2:I

    .line 299
    .line 300
    iput v3, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->label:I

    .line 301
    .line 302
    const-wide/16 v14, 0xbb8

    .line 303
    .line 304
    invoke-static {v14, v15, v1}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 305
    .line 306
    .line 307
    move-result-object v0

    .line 308
    if-ne v0, v2, :cond_9

    .line 309
    .line 310
    :goto_8
    return-object v2

    .line 311
    :cond_9
    move-wide v14, v7

    .line 312
    move/from16 v16, v10

    .line 313
    .line 314
    move v7, v11

    .line 315
    goto/16 :goto_0

    .line 316
    .line 317
    :cond_a
    :goto_9
    sget-object v2, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 318
    .line 319
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 320
    .line 321
    .line 322
    move-result-object v0

    .line 323
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 324
    .line 325
    .line 326
    move-result-object v0

    .line 327
    :goto_a
    iget-object v2, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 328
    .line 329
    iget-wide v3, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;->$gameId:J

    .line 330
    .line 331
    invoke-static {v0}, Lkotlin/Result;->isSuccess-impl(Ljava/lang/Object;)Z

    .line 332
    .line 333
    .line 334
    move-result v5

    .line 335
    if-eqz v5, :cond_b

    .line 336
    .line 337
    move-object v5, v0

    .line 338
    check-cast v5, Lkotlin/Unit;

    .line 339
    .line 340
    invoke-static {v2, v3, v4}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->w(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;J)V

    .line 341
    .line 342
    .line 343
    :cond_b
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 344
    .line 345
    .line 346
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 347
    .line 348
    return-object v0

    .line 349
    :cond_c
    throw v0
.end method
