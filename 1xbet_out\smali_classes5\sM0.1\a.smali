.class public final LsM0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0005\u001a\u00020\u0004*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "LtM0/c;",
        "",
        "teamOneId",
        "teamTwoId",
        "LuM0/b;",
        "a",
        "(LtM0/c;Ljava/lang/String;Ljava/lang/String;)LuM0/b;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LtM0/c;Ljava/lang/String;Ljava/lang/String;)LuM0/b;
    .locals 12
    .param p0    # LtM0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LuM0/b;

    .line 2
    .line 3
    invoke-virtual {p0}, LtM0/c;->e()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    if-nez v1, :cond_0

    .line 8
    .line 9
    const-string v1, ""

    .line 10
    .line 11
    :cond_0
    invoke-virtual {p0}, LtM0/c;->c()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-static {v2, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 16
    .line 17
    .line 18
    move-result v2

    .line 19
    if-eqz v2, :cond_1

    .line 20
    .line 21
    move-object v2, p1

    .line 22
    goto :goto_0

    .line 23
    :cond_1
    move-object v2, p2

    .line 24
    :goto_0
    invoke-virtual {p0}, LtM0/c;->f()Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object v3

    .line 28
    invoke-static {v3, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 29
    .line 30
    .line 31
    move-result v3

    .line 32
    if-eqz v3, :cond_2

    .line 33
    .line 34
    move-object v3, p2

    .line 35
    goto :goto_1

    .line 36
    :cond_2
    move-object v3, p1

    .line 37
    :goto_1
    invoke-virtual {p0}, LtM0/c;->d()Ljava/lang/Integer;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    const/4 p2, -0x1

    .line 42
    if-eqz p1, :cond_3

    .line 43
    .line 44
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 45
    .line 46
    .line 47
    move-result p1

    .line 48
    move v4, p1

    .line 49
    goto :goto_2

    .line 50
    :cond_3
    const/4 v4, -0x1

    .line 51
    :goto_2
    invoke-virtual {p0}, LtM0/c;->g()Ljava/lang/Integer;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    if-eqz p1, :cond_4

    .line 56
    .line 57
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 58
    .line 59
    .line 60
    move-result p1

    .line 61
    move v5, p1

    .line 62
    goto :goto_3

    .line 63
    :cond_4
    const/4 v5, -0x1

    .line 64
    :goto_3
    invoke-virtual {p0}, LtM0/c;->a()Ljava/lang/Long;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    const-wide/16 v6, 0x0

    .line 69
    .line 70
    if-eqz p1, :cond_5

    .line 71
    .line 72
    invoke-virtual {p1}, Ljava/lang/Long;->longValue()J

    .line 73
    .line 74
    .line 75
    move-result-wide v8

    .line 76
    goto :goto_4

    .line 77
    :cond_5
    move-wide v8, v6

    .line 78
    :goto_4
    sget-object p1, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;->Companion:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType$a;

    .line 79
    .line 80
    invoke-virtual {p0}, LtM0/c;->h()Ljava/lang/Integer;

    .line 81
    .line 82
    .line 83
    move-result-object v10

    .line 84
    if-eqz v10, :cond_6

    .line 85
    .line 86
    invoke-virtual {v10}, Ljava/lang/Integer;->intValue()I

    .line 87
    .line 88
    .line 89
    move-result v10

    .line 90
    goto :goto_5

    .line 91
    :cond_6
    const/4 v10, 0x0

    .line 92
    :goto_5
    invoke-virtual {p1, v10}, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType$a;->a(I)Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    invoke-virtual {p0}, LtM0/c;->i()Ljava/lang/Integer;

    .line 97
    .line 98
    .line 99
    move-result-object v10

    .line 100
    if-eqz v10, :cond_7

    .line 101
    .line 102
    invoke-virtual {v10}, Ljava/lang/Integer;->intValue()I

    .line 103
    .line 104
    .line 105
    move-result p2

    .line 106
    :cond_7
    invoke-virtual {p0}, LtM0/c;->b()Ljava/lang/Long;

    .line 107
    .line 108
    .line 109
    move-result-object p0

    .line 110
    if-eqz p0, :cond_8

    .line 111
    .line 112
    invoke-virtual {p0}, Ljava/lang/Long;->longValue()J

    .line 113
    .line 114
    .line 115
    move-result-wide v6

    .line 116
    :cond_8
    move-wide v10, v6

    .line 117
    move-wide v6, v8

    .line 118
    move-object v8, p1

    .line 119
    move v9, p2

    .line 120
    invoke-direct/range {v0 .. v11}, LuM0/b;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIJLorg/xbet/statistic/domain/model/shortgame/EventStatusType;IJ)V

    .line 121
    .line 122
    .line 123
    return-object v0
.end method
