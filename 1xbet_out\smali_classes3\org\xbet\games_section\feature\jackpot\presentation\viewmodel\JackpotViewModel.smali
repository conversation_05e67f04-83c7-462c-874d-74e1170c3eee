.class public final Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$a;,
        Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b;,
        Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u009a\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0010\u000b\n\u0002\u0008!\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u00002\u00020\u0001:\u0003cdeBc\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0008\u0008\u0001\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u000f\u0010\u001b\u001a\u00020\u001aH\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0017\u0010\u001f\u001a\u00020\u001a2\u0006\u0010\u001e\u001a\u00020\u001dH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0015\u0010#\u001a\u0008\u0012\u0004\u0012\u00020\"0!H\u0000\u00a2\u0006\u0004\u0008#\u0010$J\u0015\u0010&\u001a\u0008\u0012\u0004\u0012\u00020%0!H\u0000\u00a2\u0006\u0004\u0008&\u0010$J\u0015\u0010)\u001a\u0008\u0012\u0004\u0012\u00020(0\'H\u0000\u00a2\u0006\u0004\u0008)\u0010*J\u0015\u0010,\u001a\u0008\u0012\u0004\u0012\u00020+0!H\u0000\u00a2\u0006\u0004\u0008,\u0010$J\r\u0010-\u001a\u00020\u001a\u00a2\u0006\u0004\u0008-\u0010\u001cJ\u000f\u0010.\u001a\u00020\u001aH\u0014\u00a2\u0006\u0004\u0008.\u0010\u001cJ\r\u0010/\u001a\u00020\u001a\u00a2\u0006\u0004\u0008/\u0010\u001cJ\r\u00100\u001a\u00020\u001a\u00a2\u0006\u0004\u00080\u0010\u001cJ\r\u00101\u001a\u00020\u001a\u00a2\u0006\u0004\u00081\u0010\u001cJ\r\u00102\u001a\u00020\u001a\u00a2\u0006\u0004\u00082\u0010\u001cJ\r\u00103\u001a\u00020\u001a\u00a2\u0006\u0004\u00083\u0010\u001cJ\r\u00104\u001a\u00020\u001a\u00a2\u0006\u0004\u00084\u0010\u001cJ\u0017\u00107\u001a\u00020\u001a2\u0006\u00106\u001a\u000205H\u0002\u00a2\u0006\u0004\u00087\u00108J\u000f\u00109\u001a\u00020\u001aH\u0002\u00a2\u0006\u0004\u00089\u0010\u001cJ\u0013\u0010:\u001a\u00020\u001a*\u00020\"H\u0002\u00a2\u0006\u0004\u0008:\u0010;J\u0013\u0010<\u001a\u00020\u001a*\u00020%H\u0002\u00a2\u0006\u0004\u0008<\u0010=J\u0013\u0010>\u001a\u00020\u001a*\u00020+H\u0002\u00a2\u0006\u0004\u0008>\u0010?R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010AR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010CR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010ER\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010GR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008N\u0010OR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010QR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008R\u0010SR\u0016\u0010V\u001a\u0002058\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008T\u0010UR\u001a\u0010Z\u001a\u0008\u0012\u0004\u0012\u00020\"0W8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008X\u0010YR\u001a\u0010\\\u001a\u0008\u0012\u0004\u0012\u00020%0W8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008[\u0010YR\u001a\u0010^\u001a\u0008\u0012\u0004\u0012\u00020(0W8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008]\u0010YR\u001a\u0010b\u001a\u0008\u0012\u0004\u0012\u00020+0_8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008`\u0010a\u00a8\u0006f"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "LR40/c;",
        "jackpotUseCase",
        "Lgk/b;",
        "getCurrencyByIdUseCase",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "LSX0/a;",
        "lottieConfigurator",
        "LXv/a;",
        "gamesSectionRulesScreenFactory",
        "LwX0/c;",
        "router",
        "Lm8/a;",
        "dispatchers",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;",
        "isBalanceForGamesSectionScenario",
        "Lak/a;",
        "balanceFeature",
        "LR40/a;",
        "getJackpotImageUrlsScenario",
        "<init>",
        "(LR40/c;Lgk/b;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;LXv/a;LwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;Lak/a;LR40/a;)V",
        "",
        "J3",
        "()V",
        "",
        "throwable",
        "G3",
        "(Ljava/lang/Throwable;)V",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c;",
        "E3",
        "()Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b;",
        "B3",
        "Lkotlinx/coroutines/flow/f0;",
        "LP40/a;",
        "C3",
        "()Lkotlinx/coroutines/flow/f0;",
        "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$a;",
        "A3",
        "L3",
        "onCleared",
        "z3",
        "K3",
        "D3",
        "M3",
        "N3",
        "onBackPressed",
        "",
        "connected",
        "F3",
        "(Z)V",
        "H3",
        "Q3",
        "(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c;)V",
        "P3",
        "(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b;)V",
        "O3",
        "(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$a;)V",
        "v1",
        "LR40/c;",
        "x1",
        "Lgk/b;",
        "y1",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "F1",
        "LSX0/a;",
        "H1",
        "LXv/a;",
        "I1",
        "LwX0/c;",
        "P1",
        "Lm8/a;",
        "S1",
        "Lorg/xbet/ui_common/utils/M;",
        "V1",
        "Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;",
        "b2",
        "Lak/a;",
        "v2",
        "Z",
        "lastConnection",
        "Lkotlinx/coroutines/flow/V;",
        "x2",
        "Lkotlinx/coroutines/flow/V;",
        "state",
        "y2",
        "oneExecutionEvent",
        "F2",
        "imagesState",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "H2",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "bonusBalanceState",
        "b",
        "c",
        "a",
        "jackpot_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final F1:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LP40/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:LXv/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:Lak/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:LR40/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public v2:Z

.field public final x1:Lgk/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LR40/c;Lgk/b;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;LXv/a;LwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;Lak/a;LR40/a;)V
    .locals 0
    .param p1    # LR40/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lgk/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LXv/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lak/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LR40/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->v1:LR40/c;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->x1:Lgk/b;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->y1:Lorg/xbet/ui_common/utils/internet/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->F1:LSX0/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->H1:LXv/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->I1:LwX0/c;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->P1:Lm8/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->S1:Lorg/xbet/ui_common/utils/M;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->V1:Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->b2:Lak/a;

    .line 23
    .line 24
    sget-object p1, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c$a;->a:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c$a;

    .line 25
    .line 26
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iput-object p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 31
    .line 32
    sget-object p1, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b$a;->a:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b$a;

    .line 33
    .line 34
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    iput-object p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->y2:Lkotlinx/coroutines/flow/V;

    .line 39
    .line 40
    invoke-virtual {p11}, LR40/a;->a()LP40/a;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput-object p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->F2:Lkotlinx/coroutines/flow/V;

    .line 49
    .line 50
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 51
    .line 52
    const/4 p2, 0x1

    .line 53
    sget-object p3, Lkotlinx/coroutines/channels/BufferOverflow;->DROP_OLDEST:Lkotlinx/coroutines/channels/BufferOverflow;

    .line 54
    .line 55
    invoke-direct {p1, p2, p3}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;)V

    .line 56
    .line 57
    .line 58
    iput-object p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->H2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 59
    .line 60
    return-void
.end method

.method private final G3(Ljava/lang/Throwable;)V
    .locals 10

    .line 1
    new-instance v0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b$b;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->F1:LSX0/a;

    .line 4
    .line 5
    sget-object v2, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 6
    .line 7
    sget v3, Lpb/k;->data_retrieval_error:I

    .line 8
    .line 9
    const/16 v8, 0x1c

    .line 10
    .line 11
    const/4 v9, 0x0

    .line 12
    const/4 v4, 0x0

    .line 13
    const/4 v5, 0x0

    .line 14
    const-wide/16 v6, 0x0

    .line 15
    .line 16
    invoke-static/range {v1 .. v9}, LSX0/a$a;->a(LSX0/a;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;JILjava/lang/Object;)Lorg/xbet/uikit/components/lottie/a;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b$b;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p0, v0}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->P3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b;)V

    .line 24
    .line 25
    .line 26
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->S1:Lorg/xbet/ui_common/utils/M;

    .line 27
    .line 28
    invoke-interface {v0, p1}, Lorg/xbet/ui_common/utils/M;->i(Ljava/lang/Throwable;)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public static final I3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final J3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->y1:Lorg/xbet/ui_common/utils/internet/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    iget-object v1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->P1:Lm8/a;

    .line 18
    .line 19
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->Z(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/flow/e;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public static synthetic p3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->I3(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic q3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;)Lak/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->b2:Lak/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic r3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;)Lm8/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->P1:Lm8/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;)Lgk/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->x1:Lgk/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;)LR40/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->v1:LR40/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->F3(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic v3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->G3(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic w3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;)Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->V1:Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->O3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic y3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->Q3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final A3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->H2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final B3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->y2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final C3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "LP40/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->F2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final D3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->P1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$1;-><init>(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final E3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final F3(Z)V
    .locals 10

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    iget-boolean v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->v2:Z

    .line 4
    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->D3()V

    .line 8
    .line 9
    .line 10
    sget-object v0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b$a;->a:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b$a;

    .line 11
    .line 12
    invoke-virtual {p0, v0}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->P3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    if-nez p1, :cond_1

    .line 17
    .line 18
    iget-boolean v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->v2:Z

    .line 19
    .line 20
    if-nez v0, :cond_1

    .line 21
    .line 22
    new-instance v0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b$b;

    .line 23
    .line 24
    iget-object v1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->F1:LSX0/a;

    .line 25
    .line 26
    sget-object v2, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 27
    .line 28
    sget v3, Lpb/k;->data_retrieval_error:I

    .line 29
    .line 30
    const/16 v8, 0x1c

    .line 31
    .line 32
    const/4 v9, 0x0

    .line 33
    const/4 v4, 0x0

    .line 34
    const/4 v5, 0x0

    .line 35
    const-wide/16 v6, 0x0

    .line 36
    .line 37
    invoke-static/range {v1 .. v9}, LSX0/a$a;->a(LSX0/a;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;JILjava/lang/Object;)Lorg/xbet/uikit/components/lottie/a;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b$b;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 42
    .line 43
    .line 44
    :cond_1
    :goto_0
    iput-boolean p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->v2:Z

    .line 45
    .line 46
    return-void
.end method

.method public final H3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/a;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/a;-><init>()V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->P1:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$loadBalance$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$loadBalance$2;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final K3()V
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$a$a;->a:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$a$a;

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->O3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$a;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->H3()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final L3()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->J3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final M3()V
    .locals 10

    .line 1
    iget-boolean v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->v2:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    new-instance v0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b$b;

    .line 6
    .line 7
    iget-object v1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->F1:LSX0/a;

    .line 8
    .line 9
    sget-object v2, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 10
    .line 11
    sget v3, Lpb/k;->data_retrieval_error:I

    .line 12
    .line 13
    const/16 v8, 0x1c

    .line 14
    .line 15
    const/4 v9, 0x0

    .line 16
    const/4 v4, 0x0

    .line 17
    const/4 v5, 0x0

    .line 18
    const-wide/16 v6, 0x0

    .line 19
    .line 20
    invoke-static/range {v1 .. v9}, LSX0/a$a;->a(LSX0/a;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;JILjava/lang/Object;)Lorg/xbet/uikit/components/lottie/a;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b$b;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 25
    .line 26
    .line 27
    invoke-virtual {p0, v0}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->P3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b;)V

    .line 28
    .line 29
    .line 30
    return-void

    .line 31
    :cond_0
    if-eqz v0, :cond_1

    .line 32
    .line 33
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 34
    .line 35
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    instance-of v0, v0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c$a;

    .line 40
    .line 41
    if-nez v0, :cond_1

    .line 42
    .line 43
    sget-object v0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b$a;->a:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b$a;

    .line 44
    .line 45
    invoke-virtual {p0, v0}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->P3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b;)V

    .line 46
    .line 47
    .line 48
    return-void

    .line 49
    :cond_1
    iget-boolean v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->v2:Z

    .line 50
    .line 51
    if-eqz v0, :cond_2

    .line 52
    .line 53
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 54
    .line 55
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    instance-of v0, v0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c$a;

    .line 60
    .line 61
    :cond_2
    return-void
.end method

.method public final N3()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->I1:LwX0/c;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->H1:LXv/a;

    .line 4
    .line 5
    invoke-virtual {v1}, LXv/a;->c()Lr4/d;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final O3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$a;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->H2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final P3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$b;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->y2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final Q3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final onBackPressed()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->I1:LwX0/c;

    .line 2
    .line 3
    invoke-virtual {v0}, LwX0/c;->h()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public onCleared()V
    .locals 3

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    const/4 v2, 0x1

    .line 7
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/O;->e(Lkotlinx/coroutines/N;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    invoke-super {p0}, Lorg/xbet/ui_common/viewmodel/core/b;->onCleared()V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public final z3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$checkBonusBalance$1;

    .line 6
    .line 7
    iget-object v2, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->S1:Lorg/xbet/ui_common/utils/M;

    .line 8
    .line 9
    invoke-direct {v1, v2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$checkBonusBalance$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    iget-object v2, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->P1:Lm8/a;

    .line 13
    .line 14
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 15
    .line 16
    .line 17
    move-result-object v3

    .line 18
    new-instance v5, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$checkBonusBalance$2;

    .line 19
    .line 20
    const/4 v2, 0x0

    .line 21
    invoke-direct {v5, p0, v2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$checkBonusBalance$2;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;Lkotlin/coroutines/e;)V

    .line 22
    .line 23
    .line 24
    const/16 v6, 0xa

    .line 25
    .line 26
    const/4 v7, 0x0

    .line 27
    const/4 v4, 0x0

    .line 28
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 29
    .line 30
    .line 31
    return-void
.end method
