.class public final Ll31/z;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LL2/a;


# instance fields
.field public final a:Landroid/view/View;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final b:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final c:Landroid/view/View;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/uikit/components/buttons/DSButton;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final e:Landroidx/constraintlayout/helper/widget/Flow;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final f:Landroidx/appcompat/widget/AppCompatImageView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final g:Landroidx/constraintlayout/helper/widget/Flow;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final h:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final i:Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final j:Landroidx/constraintlayout/helper/widget/Flow;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final k:Landroidx/appcompat/widget/AppCompatImageView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final l:Landroid/widget/ProgressBar;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final m:Landroidx/constraintlayout/helper/widget/Flow;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final n:Landroidx/constraintlayout/helper/widget/Flow;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final o:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final p:Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final q:Lorg/xbet/uikit/components/separator/Separator;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final r:Lorg/xbet/uikit/components/separator/Separator;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final s:Lorg/xbet/uikit/components/separator/Separator;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final t:Lorg/xbet/uikit/components/tag/Tag;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final u:Landroidx/compose/ui/platform/ComposeView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final v:Lorg/xbet/uikit/components/buttons/DSButton;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final w:Landroidx/constraintlayout/helper/widget/Flow;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final x:Landroidx/constraintlayout/helper/widget/Flow;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final y:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final z:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/view/View;Landroidx/appcompat/widget/AppCompatTextView;Landroid/view/View;Lorg/xbet/uikit/components/buttons/DSButton;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatImageView;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatTextView;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatImageView;Landroid/widget/ProgressBar;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatTextView;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;Lorg/xbet/uikit/components/separator/Separator;Lorg/xbet/uikit/components/separator/Separator;Lorg/xbet/uikit/components/separator/Separator;Lorg/xbet/uikit/components/tag/Tag;Landroidx/compose/ui/platform/ComposeView;Lorg/xbet/uikit/components/buttons/DSButton;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatTextView;Landroidx/appcompat/widget/AppCompatTextView;)V
    .locals 0
    .param p1    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Landroidx/appcompat/widget/AppCompatTextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/uikit/components/buttons/DSButton;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p5    # Landroidx/constraintlayout/helper/widget/Flow;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p6    # Landroidx/appcompat/widget/AppCompatImageView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p7    # Landroidx/constraintlayout/helper/widget/Flow;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p8    # Landroidx/appcompat/widget/AppCompatTextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p10    # Landroidx/constraintlayout/helper/widget/Flow;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p11    # Landroidx/appcompat/widget/AppCompatImageView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p12    # Landroid/widget/ProgressBar;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p13    # Landroidx/constraintlayout/helper/widget/Flow;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p14    # Landroidx/constraintlayout/helper/widget/Flow;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p15    # Landroidx/appcompat/widget/AppCompatTextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p16    # Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p17    # Lorg/xbet/uikit/components/separator/Separator;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p18    # Lorg/xbet/uikit/components/separator/Separator;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p19    # Lorg/xbet/uikit/components/separator/Separator;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p20    # Lorg/xbet/uikit/components/tag/Tag;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p21    # Landroidx/compose/ui/platform/ComposeView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p22    # Lorg/xbet/uikit/components/buttons/DSButton;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p23    # Landroidx/constraintlayout/helper/widget/Flow;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p24    # Landroidx/constraintlayout/helper/widget/Flow;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p25    # Landroidx/appcompat/widget/AppCompatTextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p26    # Landroidx/appcompat/widget/AppCompatTextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Ll31/z;->a:Landroid/view/View;

    .line 5
    .line 6
    iput-object p2, p0, Ll31/z;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 7
    .line 8
    iput-object p3, p0, Ll31/z;->c:Landroid/view/View;

    .line 9
    .line 10
    iput-object p4, p0, Ll31/z;->d:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 11
    .line 12
    iput-object p5, p0, Ll31/z;->e:Landroidx/constraintlayout/helper/widget/Flow;

    .line 13
    .line 14
    iput-object p6, p0, Ll31/z;->f:Landroidx/appcompat/widget/AppCompatImageView;

    .line 15
    .line 16
    iput-object p7, p0, Ll31/z;->g:Landroidx/constraintlayout/helper/widget/Flow;

    .line 17
    .line 18
    iput-object p8, p0, Ll31/z;->h:Landroidx/appcompat/widget/AppCompatTextView;

    .line 19
    .line 20
    iput-object p9, p0, Ll31/z;->i:Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;

    .line 21
    .line 22
    iput-object p10, p0, Ll31/z;->j:Landroidx/constraintlayout/helper/widget/Flow;

    .line 23
    .line 24
    iput-object p11, p0, Ll31/z;->k:Landroidx/appcompat/widget/AppCompatImageView;

    .line 25
    .line 26
    iput-object p12, p0, Ll31/z;->l:Landroid/widget/ProgressBar;

    .line 27
    .line 28
    iput-object p13, p0, Ll31/z;->m:Landroidx/constraintlayout/helper/widget/Flow;

    .line 29
    .line 30
    iput-object p14, p0, Ll31/z;->n:Landroidx/constraintlayout/helper/widget/Flow;

    .line 31
    .line 32
    iput-object p15, p0, Ll31/z;->o:Landroidx/appcompat/widget/AppCompatTextView;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, Ll31/z;->p:Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, Ll31/z;->q:Lorg/xbet/uikit/components/separator/Separator;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, Ll31/z;->r:Lorg/xbet/uikit/components/separator/Separator;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, Ll31/z;->s:Lorg/xbet/uikit/components/separator/Separator;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, Ll31/z;->t:Lorg/xbet/uikit/components/tag/Tag;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, Ll31/z;->u:Landroidx/compose/ui/platform/ComposeView;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, Ll31/z;->v:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 61
    .line 62
    move-object/from16 p1, p23

    .line 63
    .line 64
    iput-object p1, p0, Ll31/z;->w:Landroidx/constraintlayout/helper/widget/Flow;

    .line 65
    .line 66
    move-object/from16 p1, p24

    .line 67
    .line 68
    iput-object p1, p0, Ll31/z;->x:Landroidx/constraintlayout/helper/widget/Flow;

    .line 69
    .line 70
    move-object/from16 p1, p25

    .line 71
    .line 72
    iput-object p1, p0, Ll31/z;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 73
    .line 74
    move-object/from16 p1, p26

    .line 75
    .line 76
    iput-object p1, p0, Ll31/z;->z:Landroidx/appcompat/widget/AppCompatTextView;

    .line 77
    .line 78
    return-void
.end method

.method public static a(Landroid/view/View;)Ll31/z;
    .locals 27
    .param p0    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    sget v0, LS11/d;->amountTv:I

    .line 4
    .line 5
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    check-cast v2, Landroidx/appcompat/widget/AppCompatTextView;

    .line 10
    .line 11
    if-eqz v2, :cond_0

    .line 12
    .line 13
    sget v0, LS11/d;->background:I

    .line 14
    .line 15
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 16
    .line 17
    .line 18
    move-result-object v3

    .line 19
    if-eqz v3, :cond_0

    .line 20
    .line 21
    sget v0, LS11/d;->bottomButton:I

    .line 22
    .line 23
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    check-cast v4, Lorg/xbet/uikit/components/buttons/DSButton;

    .line 28
    .line 29
    if-eqz v4, :cond_0

    .line 30
    .line 31
    sget v0, LS11/d;->buttonContainer:I

    .line 32
    .line 33
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 34
    .line 35
    .line 36
    move-result-object v5

    .line 37
    check-cast v5, Landroidx/constraintlayout/helper/widget/Flow;

    .line 38
    .line 39
    if-eqz v5, :cond_0

    .line 40
    .line 41
    sget v0, LS11/d;->closeIv:I

    .line 42
    .line 43
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 44
    .line 45
    .line 46
    move-result-object v6

    .line 47
    check-cast v6, Landroidx/appcompat/widget/AppCompatImageView;

    .line 48
    .line 49
    if-eqz v6, :cond_0

    .line 50
    .line 51
    sget v0, LS11/d;->gamesContainer:I

    .line 52
    .line 53
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 54
    .line 55
    .line 56
    move-result-object v7

    .line 57
    check-cast v7, Landroidx/constraintlayout/helper/widget/Flow;

    .line 58
    .line 59
    if-eqz v7, :cond_0

    .line 60
    .line 61
    sget v0, LS11/d;->gamesTv:I

    .line 62
    .line 63
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 64
    .line 65
    .line 66
    move-result-object v8

    .line 67
    check-cast v8, Landroidx/appcompat/widget/AppCompatTextView;

    .line 68
    .line 69
    if-eqz v8, :cond_0

    .line 70
    .line 71
    sget v0, LS11/d;->gamesValue:I

    .line 72
    .line 73
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 74
    .line 75
    .line 76
    move-result-object v9

    .line 77
    check-cast v9, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;

    .line 78
    .line 79
    if-eqz v9, :cond_0

    .line 80
    .line 81
    sget v0, LS11/d;->infoContainer:I

    .line 82
    .line 83
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 84
    .line 85
    .line 86
    move-result-object v10

    .line 87
    check-cast v10, Landroidx/constraintlayout/helper/widget/Flow;

    .line 88
    .line 89
    if-eqz v10, :cond_0

    .line 90
    .line 91
    sget v0, LS11/d;->loadableImage:I

    .line 92
    .line 93
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 94
    .line 95
    .line 96
    move-result-object v11

    .line 97
    check-cast v11, Landroidx/appcompat/widget/AppCompatImageView;

    .line 98
    .line 99
    if-eqz v11, :cond_0

    .line 100
    .line 101
    sget v0, LS11/d;->loaderWager:I

    .line 102
    .line 103
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 104
    .line 105
    .line 106
    move-result-object v12

    .line 107
    check-cast v12, Landroid/widget/ProgressBar;

    .line 108
    .line 109
    if-eqz v12, :cond_0

    .line 110
    .line 111
    sget v0, LS11/d;->loaderWagerContainer:I

    .line 112
    .line 113
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 114
    .line 115
    .line 116
    move-result-object v13

    .line 117
    check-cast v13, Landroidx/constraintlayout/helper/widget/Flow;

    .line 118
    .line 119
    if-eqz v13, :cond_0

    .line 120
    .line 121
    sget v0, LS11/d;->providersContainer:I

    .line 122
    .line 123
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 124
    .line 125
    .line 126
    move-result-object v14

    .line 127
    check-cast v14, Landroidx/constraintlayout/helper/widget/Flow;

    .line 128
    .line 129
    if-eqz v14, :cond_0

    .line 130
    .line 131
    sget v0, LS11/d;->providersTv:I

    .line 132
    .line 133
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 134
    .line 135
    .line 136
    move-result-object v15

    .line 137
    check-cast v15, Landroidx/appcompat/widget/AppCompatTextView;

    .line 138
    .line 139
    if-eqz v15, :cond_0

    .line 140
    .line 141
    sget v0, LS11/d;->providersValue:I

    .line 142
    .line 143
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 144
    .line 145
    .line 146
    move-result-object v16

    .line 147
    check-cast v16, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;

    .line 148
    .line 149
    if-eqz v16, :cond_0

    .line 150
    .line 151
    sget v0, LS11/d;->sellSeparatorProviders:I

    .line 152
    .line 153
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 154
    .line 155
    .line 156
    move-result-object v17

    .line 157
    check-cast v17, Lorg/xbet/uikit/components/separator/Separator;

    .line 158
    .line 159
    if-eqz v17, :cond_0

    .line 160
    .line 161
    sget v0, LS11/d;->sellSeparatorTimer:I

    .line 162
    .line 163
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 164
    .line 165
    .line 166
    move-result-object v18

    .line 167
    check-cast v18, Lorg/xbet/uikit/components/separator/Separator;

    .line 168
    .line 169
    if-eqz v18, :cond_0

    .line 170
    .line 171
    sget v0, LS11/d;->sellSeparatorWager:I

    .line 172
    .line 173
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 174
    .line 175
    .line 176
    move-result-object v19

    .line 177
    check-cast v19, Lorg/xbet/uikit/components/separator/Separator;

    .line 178
    .line 179
    if-eqz v19, :cond_0

    .line 180
    .line 181
    sget v0, LS11/d;->tagTv:I

    .line 182
    .line 183
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 184
    .line 185
    .line 186
    move-result-object v20

    .line 187
    check-cast v20, Lorg/xbet/uikit/components/tag/Tag;

    .line 188
    .line 189
    if-eqz v20, :cond_0

    .line 190
    .line 191
    sget v0, LS11/d;->timerValue:I

    .line 192
    .line 193
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 194
    .line 195
    .line 196
    move-result-object v21

    .line 197
    check-cast v21, Landroidx/compose/ui/platform/ComposeView;

    .line 198
    .line 199
    if-eqz v21, :cond_0

    .line 200
    .line 201
    sget v0, LS11/d;->topButton:I

    .line 202
    .line 203
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 204
    .line 205
    .line 206
    move-result-object v22

    .line 207
    check-cast v22, Lorg/xbet/uikit/components/buttons/DSButton;

    .line 208
    .line 209
    if-eqz v22, :cond_0

    .line 210
    .line 211
    sget v0, LS11/d;->topTitleContainer:I

    .line 212
    .line 213
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 214
    .line 215
    .line 216
    move-result-object v23

    .line 217
    check-cast v23, Landroidx/constraintlayout/helper/widget/Flow;

    .line 218
    .line 219
    if-eqz v23, :cond_0

    .line 220
    .line 221
    sget v0, LS11/d;->wagerHorizontal:I

    .line 222
    .line 223
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 224
    .line 225
    .line 226
    move-result-object v24

    .line 227
    check-cast v24, Landroidx/constraintlayout/helper/widget/Flow;

    .line 228
    .line 229
    if-eqz v24, :cond_0

    .line 230
    .line 231
    sget v0, LS11/d;->wagerTv:I

    .line 232
    .line 233
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 234
    .line 235
    .line 236
    move-result-object v25

    .line 237
    check-cast v25, Landroidx/appcompat/widget/AppCompatTextView;

    .line 238
    .line 239
    if-eqz v25, :cond_0

    .line 240
    .line 241
    sget v0, LS11/d;->wagerValue:I

    .line 242
    .line 243
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 244
    .line 245
    .line 246
    move-result-object v26

    .line 247
    check-cast v26, Landroidx/appcompat/widget/AppCompatTextView;

    .line 248
    .line 249
    if-eqz v26, :cond_0

    .line 250
    .line 251
    new-instance v0, Ll31/z;

    .line 252
    .line 253
    invoke-direct/range {v0 .. v26}, Ll31/z;-><init>(Landroid/view/View;Landroidx/appcompat/widget/AppCompatTextView;Landroid/view/View;Lorg/xbet/uikit/components/buttons/DSButton;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatImageView;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatTextView;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatImageView;Landroid/widget/ProgressBar;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatTextView;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;Lorg/xbet/uikit/components/separator/Separator;Lorg/xbet/uikit/components/separator/Separator;Lorg/xbet/uikit/components/separator/Separator;Lorg/xbet/uikit/components/tag/Tag;Landroidx/compose/ui/platform/ComposeView;Lorg/xbet/uikit/components/buttons/DSButton;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatTextView;Landroidx/appcompat/widget/AppCompatTextView;)V

    .line 254
    .line 255
    .line 256
    return-object v0

    .line 257
    :cond_0
    invoke-virtual/range {p0 .. p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 258
    .line 259
    .line 260
    move-result-object v1

    .line 261
    invoke-virtual {v1, v0}, Landroid/content/res/Resources;->getResourceName(I)Ljava/lang/String;

    .line 262
    .line 263
    .line 264
    move-result-object v0

    .line 265
    new-instance v1, Ljava/lang/NullPointerException;

    .line 266
    .line 267
    const-string v2, "Missing required view with ID: "

    .line 268
    .line 269
    invoke-virtual {v2, v0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 270
    .line 271
    .line 272
    move-result-object v0

    .line 273
    invoke-direct {v1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 274
    .line 275
    .line 276
    throw v1
.end method

.method public static b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Ll31/z;
    .locals 1
    .param p0    # Landroid/view/LayoutInflater;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p1    # Landroid/view/ViewGroup;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    sget v0, LS11/f;->aggregator_gift_card_icon:I

    .line 4
    .line 5
    invoke-virtual {p0, v0, p1}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;)Landroid/view/View;

    .line 6
    .line 7
    .line 8
    invoke-static {p1}, Ll31/z;->a(Landroid/view/View;)Ll31/z;

    .line 9
    .line 10
    .line 11
    move-result-object p0

    .line 12
    return-object p0

    .line 13
    :cond_0
    new-instance p0, Ljava/lang/NullPointerException;

    .line 14
    .line 15
    const-string p1, "parent"

    .line 16
    .line 17
    invoke-direct {p0, p1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    throw p0
.end method


# virtual methods
.method public getRoot()Landroid/view/View;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    iget-object v0, p0, Ll31/z;->a:Landroid/view/View;

    .line 2
    .line 3
    return-object v0
.end method
