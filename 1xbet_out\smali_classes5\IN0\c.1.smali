.class public final LIN0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:LIN0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static b:LOc/o;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LOc/o<",
            "Landroidx/compose/foundation/lazy/c;",
            "Ljava/lang/Integer;",
            "Landroidx/compose/runtime/j;",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, LIN0/c;

    .line 2
    .line 3
    invoke-direct {v0}, LIN0/c;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LIN0/c;->a:LIN0/c;

    .line 7
    .line 8
    const/4 v0, 0x0

    .line 9
    sget-object v1, LIN0/c$a;->a:LIN0/c$a;

    .line 10
    .line 11
    const v2, 0x2579e4f4

    .line 12
    .line 13
    .line 14
    invoke-static {v2, v0, v1}, Landroidx/compose/runtime/internal/b;->b(IZLjava/lang/Object;)Landroidx/compose/runtime/internal/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    sput-object v0, LIN0/c;->b:LOc/o;

    .line 19
    .line 20
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final a()LOc/o;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LOc/o<",
            "Landroidx/compose/foundation/lazy/c;",
            "Ljava/lang/Integer;",
            "Landroidx/compose/runtime/j;",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, LIN0/c;->b:LOc/o;

    .line 2
    .line 3
    return-object v0
.end method
