.class final Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.core.presentation.OpenGameDelegate$onGameClick$4"
    f = "OpenGameDelegate.kt"
    l = {
        0x51
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->u(Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $callOnError:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic $game:Lorg/xplatform/aggregator/api/model/Game;

.field final synthetic $subCategoryId:I

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->$callOnError:Lkotlin/jvm/functions/Function1;

    iput-object p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->$game:Lorg/xplatform/aggregator/api/model/Game;

    iput p4, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->$subCategoryId:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->$callOnError:Lkotlin/jvm/functions/Function1;

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->$game:Lorg/xplatform/aggregator/api/model/Game;

    iget v4, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->$subCategoryId:I

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;ILkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->g(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)Lorg/xbet/ui_common/utils/internet/a;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-interface {p1}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    iput v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->label:I

    .line 38
    .line 39
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/g;->L(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    if-ne p1, v0, :cond_2

    .line 44
    .line 45
    return-object v0

    .line 46
    :cond_2
    :goto_0
    check-cast p1, Ljava/lang/Boolean;

    .line 47
    .line 48
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 49
    .line 50
    .line 51
    move-result p1

    .line 52
    if-nez p1, :cond_3

    .line 53
    .line 54
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 55
    .line 56
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->h(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    sget-object v0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$b;->a:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$b;

    .line 61
    .line 62
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 63
    .line 64
    .line 65
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 66
    .line 67
    return-object p1

    .line 68
    :cond_3
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 69
    .line 70
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->$callOnError:Lkotlin/jvm/functions/Function1;

    .line 71
    .line 72
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 73
    .line 74
    iget v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;->$subCategoryId:I

    .line 75
    .line 76
    invoke-static {p1, v0, v1, v2}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->m(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 77
    .line 78
    .line 79
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 80
    .line 81
    return-object p1
.end method
