.class public final synthetic LoM0/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LoM0/g;

.field public final synthetic b:LzM0/a;


# direct methods
.method public synthetic constructor <init>(LoM0/g;LzM0/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LoM0/f;->a:LoM0/g;

    iput-object p2, p0, LoM0/f;->b:LzM0/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LoM0/f;->a:LoM0/g;

    iget-object v1, p0, LoM0/f;->b:LzM0/a;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, p1}, LoM0/g;->b(LoM0/g;LzM0/a;Landroid/view/View;)L<PERSON>lin/Unit;

    move-result-object p1

    return-object p1
.end method
