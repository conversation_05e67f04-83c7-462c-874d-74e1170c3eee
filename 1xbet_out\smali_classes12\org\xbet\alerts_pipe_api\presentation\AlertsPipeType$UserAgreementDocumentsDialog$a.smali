.class public final Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/os/Parcelable$Creator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroid/os/Parcelable$Creator<",
        "Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroid/os/Parcel;)Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;

    invoke-virtual {p1}, Landroid/os/Parcel;->readInt()I

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    invoke-direct {v0, p1}, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;-><init>(Z)V

    return-object v0
.end method

.method public final b(I)[Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;
    .locals 0

    .line 1
    new-array p1, p1, [Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;

    return-object p1
.end method

.method public bridge synthetic createFromParcel(Landroid/os/Parcel;)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog$a;->a(Landroid/os/Parcel;)Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic newArray(I)[Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog$a;->b(I)[Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;

    move-result-object p1

    return-object p1
.end method
