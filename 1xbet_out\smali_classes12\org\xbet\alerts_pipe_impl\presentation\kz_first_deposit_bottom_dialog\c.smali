.class public final synthetic Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/c;->a:Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/c;->a:Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;

    invoke-static {v0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;->K2(Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;Landroid/view/View;)V

    return-void
.end method
