.class public final Lpz0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010!\n\u0002\u0010\u0002\n\u0002\u0008\u000e\u001a)\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u001a!\u0010\u000b\u001a\u00020\n*\u0008\u0012\u0004\u0012\u00020\u00060\t2\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u001a)\u0010\u000e\u001a\u00020\n*\u0008\u0012\u0004\u0012\u00020\u00060\t2\u0006\u0010\r\u001a\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u001a)\u0010\u0011\u001a\u00020\n*\u0008\u0012\u0004\u0012\u00020\u00060\t2\u0006\u0010\u0010\u001a\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u000f\u001a)\u0010\u0013\u001a\u00020\n*\u0008\u0012\u0004\u0012\u00020\u00060\t2\u0006\u0010\u0012\u001a\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u000f\u001a)\u0010\u0015\u001a\u00020\n*\u0008\u0012\u0004\u0012\u00020\u00060\t2\u0006\u0010\u0014\u001a\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u000f\u001a!\u0010\u0016\u001a\u00020\n*\u0008\u0012\u0004\u0012\u00020\u00060\t2\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u000c\u001a!\u0010\u0017\u001a\u00020\n*\u0008\u0012\u0004\u0012\u00020\u00060\t2\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u000c\u00a8\u0006\u0018"
    }
    d2 = {
        "Lorg/xbet/sportgame/action_menu/impl/presentation/d;",
        "LHX0/e;",
        "resourceManager",
        "",
        "hideBetting",
        "",
        "Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b;",
        "h",
        "(Lorg/xbet/sportgame/action_menu/impl/presentation/d;LHX0/e;Z)Ljava/util/List;",
        "",
        "",
        "g",
        "(Ljava/util/List;LHX0/e;)V",
        "filteredMarkets",
        "c",
        "(Ljava/util/List;ZLHX0/e;)V",
        "expandedMarkets",
        "a",
        "favorite",
        "b",
        "subscribed",
        "f",
        "d",
        "e",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ljava/util/List;ZLHX0/e;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b;",
            ">;Z",
            "LHX0/e;",
            ")V"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b$c;

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    sget v1, Lpb/g;->ic_action_expand:I

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    sget v1, Lpb/g;->ic_action_collapse:I

    .line 9
    .line 10
    :goto_0
    const/4 v2, 0x0

    .line 11
    if-eqz p1, :cond_1

    .line 12
    .line 13
    sget p1, Lpb/k;->expand_all_markets:I

    .line 14
    .line 15
    new-array v2, v2, [Ljava/lang/Object;

    .line 16
    .line 17
    invoke-interface {p2, p1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    goto :goto_1

    .line 22
    :cond_1
    sget p1, Lpb/k;->collapse_all_markets:I

    .line 23
    .line 24
    new-array v2, v2, [Ljava/lang/Object;

    .line 25
    .line 26
    invoke-interface {p2, p1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    :goto_1
    invoke-direct {v0, v1, p1}, Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b$c;-><init>(ILjava/lang/String;)V

    .line 31
    .line 32
    .line 33
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public static final b(Ljava/util/List;ZLHX0/e;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b;",
            ">;Z",
            "LHX0/e;",
            ")V"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b$d;

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    sget v1, Lpb/g;->ic_star_liked_new:I

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    sget v1, Lpb/g;->ic_star_unliked_new:I

    .line 9
    .line 10
    :goto_0
    const/4 v2, 0x0

    .line 11
    if-eqz p1, :cond_1

    .line 12
    .line 13
    sget p1, Lpb/k;->favorites_remove:I

    .line 14
    .line 15
    new-array v2, v2, [Ljava/lang/Object;

    .line 16
    .line 17
    invoke-interface {p2, p1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    goto :goto_1

    .line 22
    :cond_1
    sget p1, Lpb/k;->favorites_add:I

    .line 23
    .line 24
    new-array v2, v2, [Ljava/lang/Object;

    .line 25
    .line 26
    invoke-interface {p2, p1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    :goto_1
    invoke-direct {v0, v1, p1}, Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b$d;-><init>(ILjava/lang/String;)V

    .line 31
    .line 32
    .line 33
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public static final c(Ljava/util/List;ZLHX0/e;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b;",
            ">;Z",
            "LHX0/e;",
            ")V"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b$i;

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    sget p1, Ljz0/a;->ic_action_filter_applied:I

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    sget p1, Lpb/g;->ic_setting_gray:I

    .line 9
    .line 10
    :goto_0
    sget v1, Lpb/k;->markets_settings:I

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    new-array v2, v2, [Ljava/lang/Object;

    .line 14
    .line 15
    invoke-interface {p2, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p2

    .line 19
    invoke-direct {v0, p1, p2}, Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b$i;-><init>(ILjava/lang/String;)V

    .line 20
    .line 21
    .line 22
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public static final d(Ljava/util/List;LHX0/e;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b;",
            ">;",
            "LHX0/e;",
            ")V"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b$e;

    .line 2
    .line 3
    sget v1, Ljz0/a;->ic_action_market_changes:I

    .line 4
    .line 5
    sget v2, Lpb/k;->markets_move:I

    .line 6
    .line 7
    const/4 v3, 0x0

    .line 8
    new-array v3, v3, [Ljava/lang/Object;

    .line 9
    .line 10
    invoke-interface {p1, v2, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    invoke-direct {v0, v1, p1}, Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b$e;-><init>(ILjava/lang/String;)V

    .line 15
    .line 16
    .line 17
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public static final e(Ljava/util/List;LHX0/e;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b;",
            ">;",
            "LHX0/e;",
            ")V"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b$g;

    .line 2
    .line 3
    sget v1, Lpb/g;->ic_players_duel:I

    .line 4
    .line 5
    sget v2, Lpb/k;->players_duel_title:I

    .line 6
    .line 7
    const/4 v3, 0x0

    .line 8
    new-array v3, v3, [Ljava/lang/Object;

    .line 9
    .line 10
    invoke-interface {p1, v2, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    invoke-direct {v0, v1, p1}, Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b$g;-><init>(ILjava/lang/String;)V

    .line 15
    .line 16
    .line 17
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public static final f(Ljava/util/List;ZLHX0/e;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b;",
            ">;Z",
            "LHX0/e;",
            ")V"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b$h;

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    sget p1, Lpb/g;->ic_notification_on:I

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    sget p1, Lpb/g;->ic_notification_none_white:I

    .line 9
    .line 10
    :goto_0
    sget v1, Lpb/k;->subscriptions:I

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    new-array v2, v2, [Ljava/lang/Object;

    .line 14
    .line 15
    invoke-interface {p2, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p2

    .line 19
    invoke-direct {v0, p1, p2}, Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b$h;-><init>(ILjava/lang/String;)V

    .line 20
    .line 21
    .line 22
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public static final g(Ljava/util/List;LHX0/e;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b;",
            ">;",
            "LHX0/e;",
            ")V"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b$j;

    .line 2
    .line 3
    sget v1, Lpb/g;->ic_advanced_statistics:I

    .line 4
    .line 5
    sget v2, Lpb/k;->statistic:I

    .line 6
    .line 7
    const/4 v3, 0x0

    .line 8
    new-array v3, v3, [Ljava/lang/Object;

    .line 9
    .line 10
    invoke-interface {p1, v2, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    invoke-direct {v0, v1, p1}, Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b$j;-><init>(ILjava/lang/String;)V

    .line 15
    .line 16
    .line 17
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public static final h(Lorg/xbet/sportgame/action_menu/impl/presentation/d;LHX0/e;Z)Ljava/util/List;
    .locals 2
    .param p0    # Lorg/xbet/sportgame/action_menu/impl/presentation/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/sportgame/action_menu/impl/presentation/d;",
            "LHX0/e;",
            "Z)",
            "Ljava/util/List<",
            "Lorg/xbet/sportgame/action_menu/impl/presentation/adapter/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/sportgame/action_menu/impl/presentation/d;->l()Z

    .line 7
    .line 8
    .line 9
    move-result v1

    .line 10
    if-eqz v1, :cond_0

    .line 11
    .line 12
    invoke-static {v0, p1}, Lpz0/a;->g(Ljava/util/List;LHX0/e;)V

    .line 13
    .line 14
    .line 15
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/sportgame/action_menu/impl/presentation/d;->g()Z

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    if-eqz v1, :cond_1

    .line 20
    .line 21
    invoke-virtual {p0}, Lorg/xbet/sportgame/action_menu/impl/presentation/d;->h()Z

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    invoke-static {v0, v1, p1}, Lpz0/a;->c(Ljava/util/List;ZLHX0/e;)V

    .line 26
    .line 27
    .line 28
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/sportgame/action_menu/impl/presentation/d;->c()Z

    .line 29
    .line 30
    .line 31
    move-result v1

    .line 32
    if-eqz v1, :cond_2

    .line 33
    .line 34
    invoke-virtual {p0}, Lorg/xbet/sportgame/action_menu/impl/presentation/d;->d()Z

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    invoke-static {v0, v1, p1}, Lpz0/a;->a(Ljava/util/List;ZLHX0/e;)V

    .line 39
    .line 40
    .line 41
    :cond_2
    invoke-virtual {p0}, Lorg/xbet/sportgame/action_menu/impl/presentation/d;->f()Z

    .line 42
    .line 43
    .line 44
    move-result v1

    .line 45
    if-eqz v1, :cond_3

    .line 46
    .line 47
    if-nez p2, :cond_3

    .line 48
    .line 49
    invoke-virtual {p0}, Lorg/xbet/sportgame/action_menu/impl/presentation/d;->e()Z

    .line 50
    .line 51
    .line 52
    move-result v1

    .line 53
    invoke-static {v0, v1, p1}, Lpz0/a;->b(Ljava/util/List;ZLHX0/e;)V

    .line 54
    .line 55
    .line 56
    :cond_3
    invoke-virtual {p0}, Lorg/xbet/sportgame/action_menu/impl/presentation/d;->j()Z

    .line 57
    .line 58
    .line 59
    move-result v1

    .line 60
    if-eqz v1, :cond_4

    .line 61
    .line 62
    if-nez p2, :cond_4

    .line 63
    .line 64
    invoke-virtual {p0}, Lorg/xbet/sportgame/action_menu/impl/presentation/d;->m()Z

    .line 65
    .line 66
    .line 67
    move-result p2

    .line 68
    invoke-static {v0, p2, p1}, Lpz0/a;->f(Ljava/util/List;ZLHX0/e;)V

    .line 69
    .line 70
    .line 71
    :cond_4
    invoke-virtual {p0}, Lorg/xbet/sportgame/action_menu/impl/presentation/d;->i()Z

    .line 72
    .line 73
    .line 74
    move-result p2

    .line 75
    if-eqz p2, :cond_5

    .line 76
    .line 77
    invoke-static {v0, p1}, Lpz0/a;->d(Ljava/util/List;LHX0/e;)V

    .line 78
    .line 79
    .line 80
    :cond_5
    invoke-virtual {p0}, Lorg/xbet/sportgame/action_menu/impl/presentation/d;->k()Z

    .line 81
    .line 82
    .line 83
    move-result p0

    .line 84
    if-eqz p0, :cond_6

    .line 85
    .line 86
    invoke-static {v0, p1}, Lpz0/a;->e(Ljava/util/List;LHX0/e;)V

    .line 87
    .line 88
    .line 89
    :cond_6
    return-object v0
.end method
