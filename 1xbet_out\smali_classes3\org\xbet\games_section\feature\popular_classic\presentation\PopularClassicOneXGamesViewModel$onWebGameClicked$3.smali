.class final Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.popular_classic.presentation.PopularClassicOneXGamesViewModel$onWebGameClicked$3"
    f = "PopularClassicOneXGamesViewModel.kt"
    l = {
        0x10e,
        0x113
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->L4(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    iput-object p2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;

    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    goto :goto_2

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    goto :goto_0

    .line 31
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 35
    .line 36
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->b4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e$c;

    .line 41
    .line 42
    invoke-direct {v1, v3}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$e$c;-><init>(Z)V

    .line 43
    .line 44
    .line 45
    invoke-interface {p1, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 46
    .line 47
    .line 48
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 49
    .line 50
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->L3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lw30/i;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

    .line 55
    .line 56
    invoke-static {v1}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 57
    .line 58
    .line 59
    move-result-wide v4

    .line 60
    iput v3, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->label:I

    .line 61
    .line 62
    invoke-interface {p1, v4, v5, p0}, Lw30/i;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    if-ne p1, v0, :cond_3

    .line 67
    .line 68
    goto :goto_1

    .line 69
    :cond_3
    :goto_0
    check-cast p1, Ljava/lang/Boolean;

    .line 70
    .line 71
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 72
    .line 73
    .line 74
    move-result p1

    .line 75
    if-eqz p1, :cond_4

    .line 76
    .line 77
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 78
    .line 79
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->I3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lp9/c;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    invoke-virtual {p1}, Lp9/c;->a()Z

    .line 84
    .line 85
    .line 86
    move-result p1

    .line 87
    if-nez p1, :cond_4

    .line 88
    .line 89
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 90
    .line 91
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

    .line 92
    .line 93
    invoke-static {v0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 94
    .line 95
    .line 96
    move-result-wide v0

    .line 97
    invoke-static {p1, v0, v1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->j4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;J)V

    .line 98
    .line 99
    .line 100
    goto :goto_3

    .line 101
    :cond_4
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 102
    .line 103
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->N3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lw30/o;

    .line 104
    .line 105
    .line 106
    move-result-object p1

    .line 107
    iput v2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->label:I

    .line 108
    .line 109
    invoke-interface {p1, p0}, Lw30/o;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 110
    .line 111
    .line 112
    move-result-object p1

    .line 113
    if-ne p1, v0, :cond_5

    .line 114
    .line 115
    :goto_1
    return-object v0

    .line 116
    :cond_5
    :goto_2
    check-cast p1, Ljava/util/List;

    .line 117
    .line 118
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 119
    .line 120
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onWebGameClicked$3;->$gameType:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

    .line 121
    .line 122
    invoke-static {v0, p1, v1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->k4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V

    .line 123
    .line 124
    .line 125
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 126
    .line 127
    return-object p1
.end method
