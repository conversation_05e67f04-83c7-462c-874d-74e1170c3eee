.class public interface abstract Lj4/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Lcom/github/mikephil/charting/data/Entry;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract E()F
.end method

.method public abstract E0()Z
.end method

.method public abstract F0(FFLcom/github/mikephil/charting/data/DataSet$Rounding;)Lcom/github/mikephil/charting/data/Entry;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(FF",
            "Lcom/github/mikephil/charting/data/DataSet$Rounding;",
            ")TT;"
        }
    .end annotation
.end method

.method public abstract G()Landroid/graphics/DashPathEffect;
.end method

.method public abstract H()Z
.end method

.method public abstract K()F
.end method

.method public abstract N0()I
.end method

.method public abstract O()F
.end method

.method public abstract O0()Lp4/e;
.end method

.method public abstract P0(Lg4/e;)V
.end method

.method public abstract Q0(I)Lm4/a;
.end method

.method public abstract U()Z
.end method

.method public abstract Y()F
.end method

.method public abstract a()I
.end method

.method public abstract b(I)I
.end method

.method public abstract c0()F
.end method

.method public abstract d(Lcom/github/mikephil/charting/data/Entry;)I
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)I"
        }
    .end annotation
.end method

.method public abstract e()Lcom/github/mikephil/charting/components/Legend$LegendForm;
.end method

.method public abstract f0()Lg4/e;
.end method

.method public abstract g()Ljava/lang/String;
.end method

.method public abstract i(I)Lcom/github/mikephil/charting/data/Entry;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)TT;"
        }
    .end annotation
.end method

.method public abstract i0(Lcom/github/mikephil/charting/data/Entry;)Z
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)Z"
        }
    .end annotation
.end method

.method public abstract isVisible()Z
.end method

.method public abstract j()F
.end method

.method public abstract k0()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end method

.method public abstract l()Landroid/graphics/Typeface;
.end method

.method public abstract m0()Z
.end method

.method public abstract n(I)I
.end method

.method public abstract n0()Lcom/github/mikephil/charting/components/YAxis$AxisDependency;
.end method

.method public abstract q(FF)V
.end method

.method public abstract r(F)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(F)",
            "Ljava/util/List<",
            "TT;>;"
        }
    .end annotation
.end method

.method public abstract s()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lm4/a;",
            ">;"
        }
    .end annotation
.end method

.method public abstract t0(FF)Lcom/github/mikephil/charting/data/Entry;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(FF)TT;"
        }
    .end annotation
.end method

.method public abstract w0()Lm4/a;
.end method

.method public abstract y0()F
.end method
