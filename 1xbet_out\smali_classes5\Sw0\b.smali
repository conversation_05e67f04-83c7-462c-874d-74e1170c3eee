.class public final LSw0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LSw0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0001\u0018\u00002\u00020\u0001B\t\u0008\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u001f\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "LSw0/b;",
        "LSw0/a;",
        "<init>",
        "()V",
        "",
        "getTag",
        "()Ljava/lang/String;",
        "",
        "eventId",
        "title",
        "Landroidx/fragment/app/Fragment;",
        "a",
        "(ILjava/lang/String;)Landroidx/fragment/app/Fragment;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(ILjava/lang/String;)Landroidx/fragment/app/Fragment;
    .locals 1
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->I1:Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$a;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$a;->b(ILjava/lang/String;)Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public getTag()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->I1:Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$a;->a()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method
