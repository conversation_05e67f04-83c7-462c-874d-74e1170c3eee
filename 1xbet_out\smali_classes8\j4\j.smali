.class public interface abstract Lj4/j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lj4/g;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lj4/g<",
        "Lcom/github/mikephil/charting/data/RadarEntry;",
        ">;"
    }
.end annotation


# virtual methods
.method public abstract D()I
.end method

.method public abstract Z()I
.end method

.method public abstract c()I
.end method

.method public abstract d0()F
.end method

.method public abstract o0()F
.end method

.method public abstract r0()F
.end method

.method public abstract u0()Z
.end method
