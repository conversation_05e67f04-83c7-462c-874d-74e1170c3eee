.class public final enum Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0008\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\u0013\u0008\u0002\u0012\u0008\u0008\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0006\u0010\u0007j\u0002\u0008\u0008j\u0002\u0008\tj\u0002\u0008\n\u00a8\u0006\u000b"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;",
        "",
        "colorResAttr",
        "",
        "<init>",
        "(Ljava/lang/String;II)V",
        "getColorResAttr",
        "()I",
        "INACTIVE",
        "DEFAULT",
        "CHANGED",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

.field public static final enum CHANGED:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

.field public static final enum DEFAULT:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

.field public static final enum INACTIVE:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;


# instance fields
.field private final colorResAttr:I


# direct methods
.method static constructor <clinit>()V
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    sget v2, LlZ0/d;->uikitTextPrimary:I

    .line 5
    .line 6
    const-string v3, "INACTIVE"

    .line 7
    .line 8
    invoke-direct {v0, v3, v1, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;-><init>(Ljava/lang/String;II)V

    .line 9
    .line 10
    .line 11
    sput-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->INACTIVE:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 12
    .line 13
    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 14
    .line 15
    const/4 v1, 0x1

    .line 16
    sget v2, LlZ0/d;->uikitTextPrimary:I

    .line 17
    .line 18
    const-string v3, "DEFAULT"

    .line 19
    .line 20
    invoke-direct {v0, v3, v1, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;-><init>(Ljava/lang/String;II)V

    .line 21
    .line 22
    .line 23
    sput-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->DEFAULT:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 24
    .line 25
    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 26
    .line 27
    const/4 v1, 0x2

    .line 28
    sget v2, LlZ0/d;->uikitStaticGreen:I

    .line 29
    .line 30
    const-string v3, "CHANGED"

    .line 31
    .line 32
    invoke-direct {v0, v3, v1, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;-><init>(Ljava/lang/String;II)V

    .line 33
    .line 34
    .line 35
    sput-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->CHANGED:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 36
    .line 37
    invoke-static {}, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->a()[Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    sput-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->$VALUES:[Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 42
    .line 43
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    sput-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->$ENTRIES:Lkotlin/enums/a;

    .line 48
    .line 49
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;II)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    iput p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->colorResAttr:I

    .line 5
    .line 6
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;
    .locals 3

    .line 1
    const/4 v0, 0x3

    new-array v0, v0, [Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    sget-object v1, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->INACTIVE:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->DEFAULT:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->CHANGED:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->$VALUES:[Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getColorResAttr()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->colorResAttr:I

    .line 2
    .line 3
    return v0
.end method
