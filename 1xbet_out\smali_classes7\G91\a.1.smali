.class public final synthetic LG91/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LG91/a;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, LG91/a;->b:Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LG91/a;->a:L<PERSON><PERSON>/jvm/functions/Function1;

    iget-object v1, p0, LG91/a;->b:Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    check-cast p1, Lorg/xbet/uikit/components/chips/Chip;

    check-cast p2, Ljava/lang/Boolean;

    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p2

    invoke-static {v0, v1, p1, p2}, LG91/c;->b(Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;Lorg/xbet/uikit/components/chips/Chip;Z)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
