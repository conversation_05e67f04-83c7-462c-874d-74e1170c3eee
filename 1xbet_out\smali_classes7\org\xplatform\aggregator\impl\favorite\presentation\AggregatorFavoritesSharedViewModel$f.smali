.class public final Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$f;
.super Lkotlin/coroutines/a;
.source "SourceFile"

# interfaces
.implements Lkotlinx/coroutines/CoroutineExceptionHandler;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Y4(ZLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000!\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0003\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u00012\u00020\u0002J\u001f\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "org/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$f",
        "Lkotlin/coroutines/a;",
        "Lkotlinx/coroutines/CoroutineExceptionHandler;",
        "Lkotlin/coroutines/CoroutineContext;",
        "context",
        "",
        "exception",
        "",
        "handleException",
        "(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V",
        "kotlinx-coroutines-core"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic b:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;


# direct methods
.method public constructor <init>(Lkotlinx/coroutines/CoroutineExceptionHandler$a;Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;)V
    .locals 0

    .line 1
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$f;->b:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 2
    .line 3
    invoke-direct {p0, p1}, Lkotlin/coroutines/a;-><init>(Lkotlin/coroutines/CoroutineContext$b;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V
    .locals 1

    .line 1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$f;->b:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 2
    .line 3
    sget-object v0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;->VIEWED:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 4
    .line 5
    invoke-static {p1, v0, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->v4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;Ljava/lang/Throwable;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method
