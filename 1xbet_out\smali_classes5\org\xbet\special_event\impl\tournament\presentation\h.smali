.class public final synthetic Lorg/xbet/special_event/impl/tournament/presentation/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lq4/l;


# instance fields
.field public final synthetic a:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/h;->a:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    return-void
.end method


# virtual methods
.method public final onResult(Ljava/lang/Object;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/h;->a:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    invoke-static {v0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->r3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Ljava/lang/Object;)V

    return-void
.end method
