.class public final Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lf81/b;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0000\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J,\u0010\u000f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\r0\u000c2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\nH\u0096B\u00a2\u0006\u0004\u0008\u000f\u0010\u0010R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0011R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u0013\u00a8\u0006\u0014"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl;",
        "Lf81/b;",
        "Lu81/b;",
        "repository",
        "Lm8/a;",
        "dispatchers",
        "<init>",
        "(Lu81/b;Lm8/a;)V",
        "",
        "brandsApi",
        "",
        "endPoint",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "a",
        "(ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lu81/b;",
        "b",
        "Lm8/a;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lu81/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lu81/b;Lm8/a;)V
    .locals 0
    .param p1    # Lu81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl;->a:Lu81/b;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl;->b:Lm8/a;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public a(ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlinx/coroutines/flow/e<",
            "+",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl$invoke$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p3

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl$invoke$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl$invoke$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl$invoke$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p3}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl$invoke$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p3, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl$invoke$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl$invoke$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl;->a:Lu81/b;

    .line 54
    .line 55
    iput v3, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl$invoke$1;->label:I

    .line 56
    .line 57
    invoke-interface {p3, p1, p2, v0}, Lu81/b;->f(ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object p3

    .line 61
    if-ne p3, v1, :cond_3

    .line 62
    .line 63
    return-object v1

    .line 64
    :cond_3
    :goto_1
    check-cast p3, Lkotlinx/coroutines/flow/e;

    .line 65
    .line 66
    new-instance p1, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl$invoke$$inlined$map$1;

    .line 67
    .line 68
    invoke-direct {p1, p3}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl$invoke$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;)V

    .line 69
    .line 70
    .line 71
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetFavoriteGamesFlowWithoutCatchingUseCaseImpl;->b:Lm8/a;

    .line 72
    .line 73
    invoke-interface {p2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 74
    .line 75
    .line 76
    move-result-object p2

    .line 77
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->Z(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/flow/e;

    .line 78
    .line 79
    .line 80
    move-result-object p1

    .line 81
    return-object p1
.end method
