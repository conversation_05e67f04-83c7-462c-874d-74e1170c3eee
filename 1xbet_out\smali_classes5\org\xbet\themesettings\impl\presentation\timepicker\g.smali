.class public final synthetic Lorg/xbet/themesettings/impl/presentation/timepicker/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/g;->a:Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/g;->a:Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;

    invoke-static {v0}, Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;->P2(Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;)Landroidx/lifecycle/e0$c;

    move-result-object v0

    return-object v0
.end method
