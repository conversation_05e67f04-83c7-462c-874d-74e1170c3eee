.class public final synthetic LSM0/n;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:LbZ0/a;


# direct methods
.method public synthetic constructor <init>(LB4/a;LbZ0/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LSM0/n;->a:LB4/a;

    iput-object p2, p0, LSM0/n;->b:LbZ0/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LSM0/n;->a:LB4/a;

    iget-object v1, p0, LSM0/n;->b:LbZ0/a;

    check-cast p1, Ljava/util/List;

    invoke-static {v0, v1, p1}, Lorg/xbet/statistic/stage/impl/stagetable/presentation/common/viewholder/ShadowViewHolderKt;->c(LB4/a;LbZ0/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
