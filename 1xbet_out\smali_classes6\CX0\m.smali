.class public final LCX0/m;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LCX0/m$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0003\u001a\u0011\u0010\u0002\u001a\u00020\u0001*\u00020\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u001a\u0011\u0010\u0005\u001a\u00020\u0004*\u00020\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "Lcom/xbet/onexuser/domain/user/model/OsType;",
        "",
        "a",
        "(Lcom/xbet/onexuser/domain/user/model/OsType;)I",
        "",
        "b",
        "(Lcom/xbet/onexuser/domain/user/model/OsType;)Ljava/lang/String;",
        "ui_common_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lcom/xbet/onexuser/domain/user/model/OsType;)I
    .locals 1
    .param p0    # Lcom/xbet/onexuser/domain/user/model/OsType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, LCX0/m$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    packed-switch p0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw p0

    .line 18
    :pswitch_0
    sget p0, Lpb/g;->ic_os_apple:I

    .line 19
    .line 20
    return p0

    .line 21
    :pswitch_1
    sget p0, Lpb/g;->ic_os_android_new:I

    .line 22
    .line 23
    return p0

    .line 24
    :pswitch_2
    sget p0, Lpb/g;->ic_os_mac_os:I

    .line 25
    .line 26
    return p0

    .line 27
    :pswitch_3
    sget p0, Lpb/g;->ic_os_windows:I

    .line 28
    .line 29
    return p0

    .line 30
    :pswitch_4
    sget p0, Lpb/g;->ic_os_unknown_phone:I

    .line 31
    .line 32
    return p0

    .line 33
    :pswitch_5
    sget p0, Lpb/g;->ic_os_unknown_device:I

    .line 34
    .line 35
    return p0

    .line 36
    nop

    .line 37
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static final b(Lcom/xbet/onexuser/domain/user/model/OsType;)Ljava/lang/String;
    .locals 1
    .param p0    # Lcom/xbet/onexuser/domain/user/model/OsType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, LCX0/m$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    packed-switch p0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw p0

    .line 18
    :pswitch_0
    const-string p0, "iOS"

    .line 19
    .line 20
    return-object p0

    .line 21
    :pswitch_1
    const-string p0, "Android"

    .line 22
    .line 23
    return-object p0

    .line 24
    :pswitch_2
    const-string p0, "OS X"

    .line 25
    .line 26
    return-object p0

    .line 27
    :pswitch_3
    const-string p0, "Windows"

    .line 28
    .line 29
    return-object p0

    .line 30
    :pswitch_4
    const-string p0, "BREWOS"

    .line 31
    .line 32
    return-object p0

    .line 33
    :pswitch_5
    const-string p0, "badaOS"

    .line 34
    .line 35
    return-object p0

    .line 36
    :pswitch_6
    const-string p0, "webOS"

    .line 37
    .line 38
    return-object p0

    .line 39
    :pswitch_7
    const-string p0, "JavaOS"

    .line 40
    .line 41
    return-object p0

    .line 42
    :pswitch_8
    const-string p0, "MaemoOS"

    .line 43
    .line 44
    return-object p0

    .line 45
    :pswitch_9
    const-string p0, "MeeGoOS"

    .line 46
    .line 47
    return-object p0

    .line 48
    :pswitch_a
    const-string p0, "WindowsPhoneOS"

    .line 49
    .line 50
    return-object p0

    .line 51
    :pswitch_b
    const-string p0, "WindowsMobileOS"

    .line 52
    .line 53
    return-object p0

    .line 54
    :pswitch_c
    const-string p0, "SymbianOS"

    .line 55
    .line 56
    return-object p0

    .line 57
    :pswitch_d
    const-string p0, "BlackBerryOS"

    .line 58
    .line 59
    return-object p0

    .line 60
    :pswitch_e
    const-string p0, "Unknown"

    .line 61
    .line 62
    return-object p0

    .line 63
    :pswitch_f
    const-string p0, "ChromeOS"

    .line 64
    .line 65
    return-object p0

    .line 66
    :pswitch_10
    const-string p0, "Linux"

    .line 67
    .line 68
    return-object p0

    .line 69
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_e
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
