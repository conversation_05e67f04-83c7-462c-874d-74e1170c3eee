.class final Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.swipex.impl.presentation.swipex.SwipexFragment$observeUiState$1"
    f = "SwipexFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->o3(Lkotlinx/coroutines/flow/e;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;

    iget-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->invoke(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_a

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c;

    .line 14
    .line 15
    sget-object v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$c;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$c;

    .line 16
    .line 17
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    const/4 v1, 0x0

    .line 22
    const/16 v2, 0x8

    .line 23
    .line 24
    if-eqz v0, :cond_0

    .line 25
    .line 26
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 27
    .line 28
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    iget-object p1, p1, LwS0/q;->n:Landroid/widget/FrameLayout;

    .line 33
    .line 34
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 35
    .line 36
    .line 37
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 38
    .line 39
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    iget-object p1, p1, LwS0/q;->i:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;

    .line 44
    .line 45
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 46
    .line 47
    .line 48
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 49
    .line 50
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    iget-object p1, p1, LwS0/q;->b:Landroidx/constraintlayout/widget/Group;

    .line 55
    .line 56
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 57
    .line 58
    .line 59
    goto/16 :goto_0

    .line 60
    .line 61
    :cond_0
    instance-of v0, p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$b;

    .line 62
    .line 63
    const/4 v3, 0x1

    .line 64
    if-eqz v0, :cond_6

    .line 65
    .line 66
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 67
    .line 68
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    iget-object v0, v0, LwS0/q;->t:Landroidx/recyclerview/widget/RecyclerView;

    .line 73
    .line 74
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->suppressLayout(Z)V

    .line 75
    .line 76
    .line 77
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 78
    .line 79
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->M2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    invoke-virtual {v0}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;->n()Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackState;

    .line 84
    .line 85
    .line 86
    move-result-object v0

    .line 87
    invoke-virtual {v0}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackState;->i()Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackState$Status;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    invoke-virtual {v0}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackState$Status;->isBusy()Z

    .line 92
    .line 93
    .line 94
    move-result v0

    .line 95
    if-eqz v0, :cond_1

    .line 96
    .line 97
    move-object v0, p1

    .line 98
    check-cast v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$b;

    .line 99
    .line 100
    invoke-virtual {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$b;->b()Z

    .line 101
    .line 102
    .line 103
    move-result v0

    .line 104
    if-eqz v0, :cond_3

    .line 105
    .line 106
    :cond_1
    check-cast p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$b;

    .line 107
    .line 108
    invoke-virtual {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$b;->a()Ljava/util/List;

    .line 109
    .line 110
    .line 111
    move-result-object v0

    .line 112
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 113
    .line 114
    .line 115
    move-result v0

    .line 116
    iget-object v4, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 117
    .line 118
    invoke-static {v4}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->M2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;

    .line 119
    .line 120
    .line 121
    move-result-object v4

    .line 122
    invoke-virtual {v4}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;->p()I

    .line 123
    .line 124
    .line 125
    move-result v4

    .line 126
    add-int/2addr v0, v4

    .line 127
    iget-object v4, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 128
    .line 129
    invoke-static {v4}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->M2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;

    .line 130
    .line 131
    .line 132
    move-result-object v4

    .line 133
    invoke-virtual {v4}, Landroidx/recyclerview/widget/RecyclerView$LayoutManager;->getItemCount()I

    .line 134
    .line 135
    .line 136
    move-result v4

    .line 137
    sub-int/2addr v0, v4

    .line 138
    if-ne v0, v3, :cond_2

    .line 139
    .line 140
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 141
    .line 142
    return-object p1

    .line 143
    :cond_2
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 144
    .line 145
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->L2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LQS0/b;

    .line 146
    .line 147
    .line 148
    move-result-object v0

    .line 149
    invoke-virtual {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$b;->a()Ljava/util/List;

    .line 150
    .line 151
    .line 152
    move-result-object p1

    .line 153
    invoke-virtual {v0, p1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 154
    .line 155
    .line 156
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 157
    .line 158
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->M2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;

    .line 159
    .line 160
    .line 161
    move-result-object p1

    .line 162
    invoke-virtual {p1, v1}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;->x(I)V

    .line 163
    .line 164
    .line 165
    :cond_3
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 166
    .line 167
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 168
    .line 169
    .line 170
    move-result-object p1

    .line 171
    iget-object p1, p1, LwS0/q;->r:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 172
    .line 173
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 174
    .line 175
    .line 176
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 177
    .line 178
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 179
    .line 180
    .line 181
    move-result-object p1

    .line 182
    iget-object p1, p1, LwS0/q;->n:Landroid/widget/FrameLayout;

    .line 183
    .line 184
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 185
    .line 186
    .line 187
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 188
    .line 189
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 190
    .line 191
    .line 192
    move-result-object p1

    .line 193
    iget-object p1, p1, LwS0/q;->s:Landroid/view/View;

    .line 194
    .line 195
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 196
    .line 197
    .line 198
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 199
    .line 200
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 201
    .line 202
    .line 203
    move-result-object p1

    .line 204
    iget-object p1, p1, LwS0/q;->b:Landroidx/constraintlayout/widget/Group;

    .line 205
    .line 206
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 207
    .line 208
    .line 209
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 210
    .line 211
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 212
    .line 213
    .line 214
    move-result-object p1

    .line 215
    iget-object p1, p1, LwS0/q;->i:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;

    .line 216
    .line 217
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 218
    .line 219
    .line 220
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 221
    .line 222
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 223
    .line 224
    .line 225
    move-result-object p1

    .line 226
    iget-object p1, p1, LwS0/q;->m:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 227
    .line 228
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 229
    .line 230
    .line 231
    move-result p1

    .line 232
    if-nez p1, :cond_4

    .line 233
    .line 234
    goto/16 :goto_0

    .line 235
    .line 236
    :cond_4
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 237
    .line 238
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 239
    .line 240
    .line 241
    move-result-object p1

    .line 242
    iget-object p1, p1, LwS0/q;->m:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 243
    .line 244
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 245
    .line 246
    .line 247
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 248
    .line 249
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 250
    .line 251
    .line 252
    move-result-object p1

    .line 253
    iget-object p1, p1, LwS0/q;->m:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 254
    .line 255
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 256
    .line 257
    invoke-virtual {p1}, Landroid/view/View;->isLaidOut()Z

    .line 258
    .line 259
    .line 260
    move-result v1

    .line 261
    if-eqz v1, :cond_5

    .line 262
    .line 263
    invoke-virtual {p1}, Landroid/view/View;->isLayoutRequested()Z

    .line 264
    .line 265
    .line 266
    move-result v1

    .line 267
    if-nez v1, :cond_5

    .line 268
    .line 269
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->K2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 270
    .line 271
    .line 272
    goto/16 :goto_0

    .line 273
    .line 274
    :cond_5
    new-instance v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1$a;

    .line 275
    .line 276
    invoke-direct {v1, v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1$a;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 277
    .line 278
    .line 279
    invoke-virtual {p1, v1}, Landroid/view/View;->addOnLayoutChangeListener(Landroid/view/View$OnLayoutChangeListener;)V

    .line 280
    .line 281
    .line 282
    goto/16 :goto_0

    .line 283
    .line 284
    :cond_6
    sget-object v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$a;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$a;

    .line 285
    .line 286
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 287
    .line 288
    .line 289
    move-result v0

    .line 290
    if-eqz v0, :cond_7

    .line 291
    .line 292
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 293
    .line 294
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 295
    .line 296
    .line 297
    move-result-object p1

    .line 298
    iget-object p1, p1, LwS0/q;->n:Landroid/widget/FrameLayout;

    .line 299
    .line 300
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 301
    .line 302
    .line 303
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 304
    .line 305
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 306
    .line 307
    .line 308
    move-result-object p1

    .line 309
    iget-object p1, p1, LwS0/q;->b:Landroidx/constraintlayout/widget/Group;

    .line 310
    .line 311
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 312
    .line 313
    .line 314
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 315
    .line 316
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 317
    .line 318
    .line 319
    move-result-object p1

    .line 320
    iget-object p1, p1, LwS0/q;->m:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 321
    .line 322
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 323
    .line 324
    .line 325
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 326
    .line 327
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 328
    .line 329
    .line 330
    move-result-object p1

    .line 331
    iget-object p1, p1, LwS0/q;->i:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;

    .line 332
    .line 333
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 334
    .line 335
    .line 336
    goto/16 :goto_0

    .line 337
    .line 338
    :cond_7
    instance-of v0, p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$d;

    .line 339
    .line 340
    if-eqz v0, :cond_8

    .line 341
    .line 342
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 343
    .line 344
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 345
    .line 346
    .line 347
    move-result-object v0

    .line 348
    iget-object v0, v0, LwS0/q;->n:Landroid/widget/FrameLayout;

    .line 349
    .line 350
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 351
    .line 352
    .line 353
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 354
    .line 355
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 356
    .line 357
    .line 358
    move-result-object v0

    .line 359
    iget-object v0, v0, LwS0/q;->i:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;

    .line 360
    .line 361
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 362
    .line 363
    .line 364
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 365
    .line 366
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 367
    .line 368
    .line 369
    move-result-object v0

    .line 370
    iget-object v0, v0, LwS0/q;->b:Landroidx/constraintlayout/widget/Group;

    .line 371
    .line 372
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 373
    .line 374
    .line 375
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 376
    .line 377
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 378
    .line 379
    .line 380
    move-result-object v0

    .line 381
    iget-object v0, v0, LwS0/q;->m:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 382
    .line 383
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 384
    .line 385
    .line 386
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 387
    .line 388
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 389
    .line 390
    .line 391
    move-result-object v0

    .line 392
    iget-object v0, v0, LwS0/q;->r:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 393
    .line 394
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 395
    .line 396
    .line 397
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 398
    .line 399
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 400
    .line 401
    .line 402
    move-result-object v0

    .line 403
    iget-object v1, v0, LwS0/q;->r:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 404
    .line 405
    check-cast p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$d;

    .line 406
    .line 407
    invoke-virtual {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$d;->a()Lorg/xbet/uikit/components/lottie/a;

    .line 408
    .line 409
    .line 410
    move-result-object v2

    .line 411
    sget v4, Lpb/k;->update_again_after:I

    .line 412
    .line 413
    const/4 v5, 0x2

    .line 414
    const/4 v6, 0x0

    .line 415
    const/4 v3, 0x0

    .line 416
    invoke-static/range {v1 .. v6}, Lorg/xbet/uikit/components/lottie/LottieView;->N(Lorg/xbet/uikit/components/lottie/LottieView;Lorg/xbet/uikit/components/lottie/a;Lkotlin/jvm/functions/Function0;IILjava/lang/Object;)V

    .line 417
    .line 418
    .line 419
    goto :goto_0

    .line 420
    :cond_8
    sget-object v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$e;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$e;

    .line 421
    .line 422
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 423
    .line 424
    .line 425
    move-result p1

    .line 426
    if-eqz p1, :cond_9

    .line 427
    .line 428
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 429
    .line 430
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 431
    .line 432
    .line 433
    move-result-object p1

    .line 434
    iget-object p1, p1, LwS0/q;->i:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;

    .line 435
    .line 436
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 437
    .line 438
    .line 439
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 440
    .line 441
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 442
    .line 443
    .line 444
    move-result-object p1

    .line 445
    iget-object p1, p1, LwS0/q;->m:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 446
    .line 447
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 448
    .line 449
    .line 450
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 451
    .line 452
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 453
    .line 454
    .line 455
    move-result-object p1

    .line 456
    iget-object p1, p1, LwS0/q;->s:Landroid/view/View;

    .line 457
    .line 458
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 459
    .line 460
    .line 461
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 462
    .line 463
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 464
    .line 465
    .line 466
    move-result-object p1

    .line 467
    iget-object p1, p1, LwS0/q;->n:Landroid/widget/FrameLayout;

    .line 468
    .line 469
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 470
    .line 471
    .line 472
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 473
    .line 474
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 475
    .line 476
    .line 477
    move-result-object p1

    .line 478
    iget-object p1, p1, LwS0/q;->t:Landroidx/recyclerview/widget/RecyclerView;

    .line 479
    .line 480
    invoke-virtual {p1, v3}, Landroidx/recyclerview/widget/RecyclerView;->suppressLayout(Z)V

    .line 481
    .line 482
    .line 483
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 484
    .line 485
    return-object p1

    .line 486
    :cond_9
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 487
    .line 488
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 489
    .line 490
    .line 491
    throw p1

    .line 492
    :cond_a
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 493
    .line 494
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 495
    .line 496
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 497
    .line 498
    .line 499
    throw p1
.end method
