.class public final synthetic LSG0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:LHd/c;

.field public final synthetic b:Lkotlin/jvm/functions/Function0;

.field public final synthetic c:Lkotlin/jvm/functions/Function1;

.field public final synthetic d:I


# direct methods
.method public synthetic constructor <init>(LHd/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LSG0/d;->a:LHd/c;

    iput-object p2, p0, LSG0/d;->b:Lkotlin/jvm/functions/Function0;

    iput-object p3, p0, LSG0/d;->c:Lkotlin/jvm/functions/Function1;

    iput p4, p0, LSG0/d;->d:I

    return-void
.end method


# virtual methods
.method public final invoke(<PERSON>ja<PERSON>/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    iget-object v0, p0, LSG0/d;->a:LHd/c;

    iget-object v1, p0, LSG0/d;->b:Lkotlin/jvm/functions/Function0;

    iget-object v2, p0, LSG0/d;->c:Lkotlin/jvm/functions/Function1;

    iget v3, p0, LSG0/d;->d:I

    move-object v4, p1

    check-cast v4, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v5

    invoke-static/range {v0 .. v5}, LSG0/i;->d(LHd/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
