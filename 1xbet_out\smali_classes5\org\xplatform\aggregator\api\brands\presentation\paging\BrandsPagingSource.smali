.class public final Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;
.super Lorg/xbet/ui_common/paging/BasePagingSource;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/xbet/ui_common/paging/BasePagingSource<",
        "Lorg/xplatform/aggregator/api/brands/presentation/paging/a;",
        "LP21/c;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\u0008\u0014\u0018\u0000 \"2\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001:\u00018B?\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\u0008\u001a\u00020\u0006\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000c\u001a\u00020\u000b\u0012\u0006\u0010\u000e\u001a\u00020\r\u0012\u0006\u0010\u0010\u001a\u00020\u000f\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J%\u0010\u0015\u001a\u0004\u0018\u00010\u00022\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0013H\u0016\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J8\u0010\u001b\u001a\u001c\u0012\u0006\u0012\u0004\u0018\u00010\u0002\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u001a0\u00192\u000c\u0010\u0018\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0017H\u0096@\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u001f\u0010\u001e\u001a\u00020\u0006*\u0004\u0018\u00010\u00022\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u0002H\u0016\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ#\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u001a2\u0006\u0010!\u001a\u00020 H\u0016\u00a2\u0006\u0004\u0008\"\u0010#J\u0013\u0010&\u001a\u0008\u0012\u0004\u0012\u00020%0$\u00a2\u0006\u0004\u0008&\u0010\'R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010)R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u0010\u0008\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010+R\u0014\u0010\n\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u0010.R\u0014\u0010\u000c\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u00100R\u0014\u0010\u000e\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00081\u00102R\u0014\u0010\u0010\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u00104R\u001a\u00107\u001a\u0008\u0012\u0004\u0012\u00020%0$8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00085\u00106\u00a8\u00069"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;",
        "Lorg/xbet/ui_common/paging/BasePagingSource;",
        "Lorg/xplatform/aggregator/api/brands/presentation/paging/a;",
        "LP21/c;",
        "",
        "partitionId",
        "",
        "fromPopularSearch",
        "hasAggregatorBrandsFullInfo",
        "Lv81/h;",
        "getBrandsUseCase",
        "Li8/m;",
        "getThemeUseCase",
        "LHX0/e;",
        "resourceManager",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "<init>",
        "(JZZLv81/h;Li8/m;LHX0/e;Lorg/xbet/remoteconfig/domain/usecases/i;)V",
        "Landroidx/paging/M;",
        "state",
        "n",
        "(Landroidx/paging/M;)Lorg/xplatform/aggregator/api/brands/presentation/paging/a;",
        "Landroidx/paging/PagingSource$a;",
        "params",
        "Lkotlin/Pair;",
        "Landroidx/paging/PagingSource$b;",
        "l",
        "(Landroidx/paging/PagingSource$a;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "nextKey",
        "o",
        "(Lorg/xplatform/aggregator/api/brands/presentation/paging/a;Lorg/xplatform/aggregator/api/brands/presentation/paging/a;)Z",
        "",
        "throwable",
        "j",
        "(Ljava/lang/Throwable;)Landroidx/paging/PagingSource$b;",
        "",
        "Lg81/j;",
        "m",
        "()Ljava/util/List;",
        "b",
        "J",
        "c",
        "Z",
        "d",
        "e",
        "Lv81/h;",
        "f",
        "Li8/m;",
        "g",
        "LHX0/e;",
        "h",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "i",
        "Ljava/util/List;",
        "brandsList",
        "a",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final j:Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final b:J

.field public final c:Z

.field public final d:Z

.field public final e:Lv81/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Li8/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lg81/j;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->j:Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$a;

    return-void
.end method

.method public constructor <init>(JZZLv81/h;Li8/m;LHX0/e;Lorg/xbet/remoteconfig/domain/usecases/i;)V
    .locals 0
    .param p5    # Lv81/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Li8/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/paging/BasePagingSource;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-wide p1, p0, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->b:J

    .line 5
    .line 6
    iput-boolean p3, p0, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->c:Z

    .line 7
    .line 8
    iput-boolean p4, p0, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->d:Z

    .line 9
    .line 10
    iput-object p5, p0, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->e:Lv81/h;

    .line 11
    .line 12
    iput-object p6, p0, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->f:Li8/m;

    .line 13
    .line 14
    iput-object p7, p0, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->g:LHX0/e;

    .line 15
    .line 16
    iput-object p8, p0, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->h:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 17
    .line 18
    new-instance p1, Ljava/util/ArrayList;

    .line 19
    .line 20
    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    .line 21
    .line 22
    .line 23
    iput-object p1, p0, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->i:Ljava/util/List;

    .line 24
    .line 25
    return-void
.end method


# virtual methods
.method public bridge synthetic e(Landroidx/paging/M;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->n(Landroidx/paging/M;)Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public j(Ljava/lang/Throwable;)Landroidx/paging/PagingSource$b;
    .locals 1
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Throwable;",
            ")",
            "Landroidx/paging/PagingSource$b<",
            "Lorg/xplatform/aggregator/api/brands/presentation/paging/a;",
            "LP21/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Landroidx/paging/PagingSource$b$a;

    .line 2
    .line 3
    invoke-direct {v0, p1}, Landroidx/paging/PagingSource$b$a;-><init>(Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public bridge synthetic k(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 2
    .line 3
    check-cast p2, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->o(Lorg/xplatform/aggregator/api/brands/presentation/paging/a;Lorg/xplatform/aggregator/api/brands/presentation/paging/a;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public l(Landroidx/paging/PagingSource$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 34
    .param p1    # Landroidx/paging/PagingSource$a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/paging/PagingSource$a<",
            "Lorg/xplatform/aggregator/api/brands/presentation/paging/a;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Pair<",
            "Lorg/xplatform/aggregator/api/brands/presentation/paging/a;",
            "+",
            "Landroidx/paging/PagingSource$b<",
            "Lorg/xplatform/aggregator/api/brands/presentation/paging/a;",
            "LP21/c;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    move-object/from16 v0, p2

    .line 4
    .line 5
    instance-of v3, v0, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;

    .line 6
    .line 7
    if-eqz v3, :cond_0

    .line 8
    .line 9
    move-object v3, v0

    .line 10
    check-cast v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;

    .line 11
    .line 12
    iget v4, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->label:I

    .line 13
    .line 14
    const/high16 v5, -0x80000000

    .line 15
    .line 16
    and-int v6, v4, v5

    .line 17
    .line 18
    if-eqz v6, :cond_0

    .line 19
    .line 20
    sub-int/2addr v4, v5

    .line 21
    iput v4, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->label:I

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;

    .line 25
    .line 26
    invoke-direct {v3, v1, v0}, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;-><init>(Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    :goto_0
    iget-object v0, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->result:Ljava/lang/Object;

    .line 30
    .line 31
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v4

    .line 35
    iget v5, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->label:I

    .line 36
    .line 37
    const-string v6, ""

    .line 38
    .line 39
    const/4 v9, 0x4

    .line 40
    const/4 v10, 0x2

    .line 41
    const/4 v11, 0x3

    .line 42
    const/4 v12, 0x1

    .line 43
    if-eqz v5, :cond_5

    .line 44
    .line 45
    if-eq v5, v12, :cond_4

    .line 46
    .line 47
    if-eq v5, v10, :cond_3

    .line 48
    .line 49
    if-eq v5, v11, :cond_2

    .line 50
    .line 51
    if-eq v5, v9, :cond_1

    .line 52
    .line 53
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 54
    .line 55
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 56
    .line 57
    invoke-direct {v0, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 58
    .line 59
    .line 60
    throw v0

    .line 61
    :cond_1
    iget-object v2, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 62
    .line 63
    check-cast v2, Ljava/lang/Throwable;

    .line 64
    .line 65
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 66
    .line 67
    .line 68
    move-object v0, v2

    .line 69
    move-object v2, v1

    .line 70
    goto/16 :goto_2d

    .line 71
    .line 72
    :cond_2
    iget v4, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$1:I

    .line 73
    .line 74
    iget v5, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$0:I

    .line 75
    .line 76
    iget-object v9, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$1:Ljava/lang/Object;

    .line 77
    .line 78
    check-cast v9, Ljava/util/List;

    .line 79
    .line 80
    iget-object v3, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 81
    .line 82
    check-cast v3, Landroidx/paging/PagingSource$a;

    .line 83
    .line 84
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 85
    .line 86
    .line 87
    move-object/from16 v19, v6

    .line 88
    .line 89
    goto/16 :goto_1b

    .line 90
    .line 91
    :cond_3
    iget v5, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$3:I

    .line 92
    .line 93
    iget v15, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$2:I

    .line 94
    .line 95
    iget-wide v7, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->J$1:J

    .line 96
    .line 97
    iget-wide v13, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->J$0:J

    .line 98
    .line 99
    iget v9, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$1:I

    .line 100
    .line 101
    iget v2, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$0:I

    .line 102
    .line 103
    iget-object v10, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$3:Ljava/lang/Object;

    .line 104
    .line 105
    check-cast v10, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;

    .line 106
    .line 107
    iget-object v11, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$2:Ljava/lang/Object;

    .line 108
    .line 109
    check-cast v11, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;

    .line 110
    .line 111
    iget-object v12, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$1:Ljava/lang/Object;

    .line 112
    .line 113
    check-cast v12, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 114
    .line 115
    move-object/from16 v18, v0

    .line 116
    .line 117
    iget-object v0, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 118
    .line 119
    check-cast v0, Landroidx/paging/PagingSource$a;

    .line 120
    .line 121
    :try_start_0
    invoke-static/range {v18 .. v18}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 122
    .line 123
    .line 124
    move/from16 v18, v5

    .line 125
    .line 126
    move-object v5, v3

    .line 127
    move/from16 v3, v18

    .line 128
    .line 129
    move/from16 v24, v2

    .line 130
    .line 131
    move-object/from16 v19, v6

    .line 132
    .line 133
    const/16 v18, 0x1

    .line 134
    .line 135
    move-object v2, v0

    .line 136
    move-wide/from16 v30, v13

    .line 137
    .line 138
    move v13, v9

    .line 139
    move-object v14, v11

    .line 140
    move-wide/from16 v32, v7

    .line 141
    .line 142
    move-object v7, v10

    .line 143
    move-wide/from16 v9, v32

    .line 144
    .line 145
    move v8, v15

    .line 146
    move-object v15, v12

    .line 147
    move-wide/from16 v11, v30

    .line 148
    .line 149
    goto/16 :goto_18

    .line 150
    .line 151
    :catchall_0
    move-exception v0

    .line 152
    move-object v2, v1

    .line 153
    goto/16 :goto_2b

    .line 154
    .line 155
    :cond_4
    move-object/from16 v18, v0

    .line 156
    .line 157
    iget v2, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$3:I

    .line 158
    .line 159
    iget v5, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$2:I

    .line 160
    .line 161
    iget-wide v7, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->J$1:J

    .line 162
    .line 163
    iget-wide v9, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->J$0:J

    .line 164
    .line 165
    iget v11, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$1:I

    .line 166
    .line 167
    iget v12, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$0:I

    .line 168
    .line 169
    iget-object v0, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$3:Ljava/lang/Object;

    .line 170
    .line 171
    move-object v13, v0

    .line 172
    check-cast v13, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;

    .line 173
    .line 174
    iget-object v0, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$2:Ljava/lang/Object;

    .line 175
    .line 176
    move-object v14, v0

    .line 177
    check-cast v14, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;

    .line 178
    .line 179
    iget-object v0, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$1:Ljava/lang/Object;

    .line 180
    .line 181
    move-object v15, v0

    .line 182
    check-cast v15, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 183
    .line 184
    iget-object v0, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 185
    .line 186
    move-object/from16 v19, v0

    .line 187
    .line 188
    check-cast v19, Landroidx/paging/PagingSource$a;

    .line 189
    .line 190
    :try_start_1
    invoke-static/range {v18 .. v18}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 191
    .line 192
    .line 193
    move-object/from16 v0, v18

    .line 194
    .line 195
    goto/16 :goto_10

    .line 196
    .line 197
    :catchall_1
    move-exception v0

    .line 198
    move-object v1, v13

    .line 199
    move-wide/from16 v30, v9

    .line 200
    .line 201
    move v9, v11

    .line 202
    move-object v11, v14

    .line 203
    move-object/from16 v10, v19

    .line 204
    .line 205
    move-wide/from16 v13, v30

    .line 206
    .line 207
    goto/16 :goto_12

    .line 208
    .line 209
    :cond_5
    move-object/from16 v18, v0

    .line 210
    .line 211
    invoke-static/range {v18 .. v18}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 212
    .line 213
    .line 214
    invoke-virtual/range {p1 .. p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 215
    .line 216
    .line 217
    move-result-object v0

    .line 218
    check-cast v0, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 219
    .line 220
    if-eqz v0, :cond_6

    .line 221
    .line 222
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->b()I

    .line 223
    .line 224
    .line 225
    move-result v0

    .line 226
    goto :goto_1

    .line 227
    :cond_6
    const/4 v0, 0x0

    .line 228
    :goto_1
    invoke-virtual/range {p1 .. p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 229
    .line 230
    .line 231
    move-result-object v2

    .line 232
    check-cast v2, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 233
    .line 234
    if-eqz v2, :cond_7

    .line 235
    .line 236
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->e()I

    .line 237
    .line 238
    .line 239
    move-result v2

    .line 240
    goto :goto_2

    .line 241
    :cond_7
    const/4 v2, 0x0

    .line 242
    :goto_2
    sget-object v5, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;->Companion:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle$a;

    .line 243
    .line 244
    iget-object v7, v1, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->h:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 245
    .line 246
    invoke-interface {v7}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 247
    .line 248
    .line 249
    move-result-object v7

    .line 250
    invoke-virtual {v7}, Lek0/o;->w()Ljava/lang/String;

    .line 251
    .line 252
    .line 253
    move-result-object v7

    .line 254
    invoke-virtual {v5, v7}, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle$a;->a(Ljava/lang/String;)Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 255
    .line 256
    .line 257
    move-result-object v5

    .line 258
    invoke-virtual/range {p1 .. p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 259
    .line 260
    .line 261
    move-result-object v7

    .line 262
    check-cast v7, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 263
    .line 264
    if-eqz v7, :cond_8

    .line 265
    .line 266
    invoke-virtual {v7}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->a()J

    .line 267
    .line 268
    .line 269
    move-result-wide v7

    .line 270
    goto :goto_3

    .line 271
    :cond_8
    const-wide/16 v7, 0x0

    .line 272
    .line 273
    :goto_3
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 274
    .line 275
    .line 276
    move-result-wide v9

    .line 277
    move/from16 v24, v0

    .line 278
    .line 279
    move-object v14, v1

    .line 280
    move v13, v2

    .line 281
    move-object v15, v5

    .line 282
    move-wide v11, v7

    .line 283
    const/4 v8, 0x0

    .line 284
    move-object/from16 v2, p1

    .line 285
    .line 286
    move-object v5, v3

    .line 287
    move-object v7, v5

    .line 288
    const/4 v3, 0x0

    .line 289
    :goto_4
    :try_start_2
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 290
    .line 291
    iget-object v0, v14, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->e:Lv81/h;

    .line 292
    .line 293
    move-object/from16 v18, v0

    .line 294
    .line 295
    iget-wide v0, v14, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->b:J

    .line 296
    .line 297
    invoke-virtual {v2}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 298
    .line 299
    .line 300
    move-result-object v19

    .line 301
    check-cast v19, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 302
    .line 303
    if-eqz v19, :cond_a

    .line 304
    .line 305
    invoke-virtual/range {v19 .. v19}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->f()Ljava/lang/String;

    .line 306
    .line 307
    .line 308
    move-result-object v19

    .line 309
    if-nez v19, :cond_9

    .line 310
    .line 311
    goto :goto_7

    .line 312
    :cond_9
    :goto_5
    move-object/from16 v21, v19

    .line 313
    .line 314
    goto :goto_8

    .line 315
    :catchall_2
    move-exception v0

    .line 316
    :goto_6
    move-object v1, v7

    .line 317
    move-wide/from16 v30, v9

    .line 318
    .line 319
    move-object v10, v2

    .line 320
    move v2, v3

    .line 321
    move-object v3, v5

    .line 322
    move v5, v8

    .line 323
    move-wide/from16 v7, v30

    .line 324
    .line 325
    move v9, v13

    .line 326
    move-wide/from16 v30, v11

    .line 327
    .line 328
    move-object v11, v14

    .line 329
    move-wide/from16 v13, v30

    .line 330
    .line 331
    move/from16 v12, v24

    .line 332
    .line 333
    goto/16 :goto_12

    .line 334
    .line 335
    :cond_a
    :goto_7
    sget-object v19, Lorg/xplatform/aggregator/api/model/ProductSortType;->BY_POPULARITY:Lorg/xplatform/aggregator/api/model/ProductSortType;

    .line 336
    .line 337
    invoke-static/range {v19 .. v19}, Lg81/g;->c(Lorg/xplatform/aggregator/api/model/ProductSortType;)Ljava/lang/String;

    .line 338
    .line 339
    .line 340
    move-result-object v19

    .line 341
    goto :goto_5

    .line 342
    :goto_8
    invoke-virtual {v2}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 343
    .line 344
    .line 345
    move-result-object v19

    .line 346
    check-cast v19, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 347
    .line 348
    if-eqz v19, :cond_b

    .line 349
    .line 350
    invoke-virtual/range {v19 .. v19}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->d()Ljava/lang/String;

    .line 351
    .line 352
    .line 353
    move-result-object v19

    .line 354
    goto :goto_9

    .line 355
    :cond_b
    const/16 v19, 0x0

    .line 356
    .line 357
    :goto_9
    if-nez v19, :cond_c

    .line 358
    .line 359
    move-object/from16 v22, v6

    .line 360
    .line 361
    :goto_a
    move-wide/from16 v19, v0

    .line 362
    .line 363
    goto :goto_b

    .line 364
    :cond_c
    move-object/from16 v22, v19

    .line 365
    .line 366
    goto :goto_a

    .line 367
    :goto_b
    iget-boolean v0, v14, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->c:Z

    .line 368
    .line 369
    if-eqz v0, :cond_d

    .line 370
    .line 371
    const v0, 0x7fffffff

    .line 372
    .line 373
    .line 374
    const v23, 0x7fffffff

    .line 375
    .line 376
    .line 377
    goto :goto_c

    .line 378
    :cond_d
    const/16 v23, 0x3c

    .line 379
    .line 380
    :goto_c
    iget-boolean v0, v14, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->d:Z

    .line 381
    .line 382
    sget-object v1, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 383
    .line 384
    move/from16 v26, v0

    .line 385
    .line 386
    iget-object v0, v14, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->f:Li8/m;

    .line 387
    .line 388
    invoke-interface {v0}, Li8/m;->invoke()Lcom/xbet/onexcore/themes/Theme;

    .line 389
    .line 390
    .line 391
    move-result-object v0

    .line 392
    invoke-virtual {v1, v0}, Lcom/xbet/onexcore/themes/Theme$a;->e(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 393
    .line 394
    .line 395
    move-result v27

    .line 396
    iget-boolean v0, v14, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->c:Z

    .line 397
    .line 398
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;->BrandSExtended:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 399
    .line 400
    if-eq v15, v1, :cond_f

    .line 401
    .line 402
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;->BrandM:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 403
    .line 404
    if-eq v15, v1, :cond_f

    .line 405
    .line 406
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;->BrandMExtended:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 407
    .line 408
    if-ne v15, v1, :cond_e

    .line 409
    .line 410
    goto :goto_d

    .line 411
    :cond_e
    const/16 v28, 0x0

    .line 412
    .line 413
    goto :goto_e

    .line 414
    :cond_f
    :goto_d
    const/16 v28, 0x1

    .line 415
    .line 416
    :goto_e
    const-string v25, ""

    .line 417
    .line 418
    move/from16 v29, v0

    .line 419
    .line 420
    invoke-interface/range {v18 .. v29}, Lv81/h;->a(JLjava/lang/String;Ljava/lang/String;IILjava/lang/String;ZZZZ)Lkotlinx/coroutines/flow/e;

    .line 421
    .line 422
    .line 423
    move-result-object v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_2

    .line 424
    move/from16 v1, v24

    .line 425
    .line 426
    :try_start_3
    iput-object v2, v5, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 427
    .line 428
    iput-object v15, v5, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$1:Ljava/lang/Object;

    .line 429
    .line 430
    iput-object v14, v5, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$2:Ljava/lang/Object;

    .line 431
    .line 432
    iput-object v7, v5, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$3:Ljava/lang/Object;

    .line 433
    .line 434
    iput v1, v5, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$0:I

    .line 435
    .line 436
    iput v13, v5, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$1:I

    .line 437
    .line 438
    iput-wide v11, v5, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->J$0:J

    .line 439
    .line 440
    iput-wide v9, v5, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->J$1:J

    .line 441
    .line 442
    iput v8, v5, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$2:I

    .line 443
    .line 444
    iput v3, v5, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$3:I
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_3

    .line 445
    .line 446
    move/from16 v24, v1

    .line 447
    .line 448
    const/4 v1, 0x1

    .line 449
    :try_start_4
    iput v1, v5, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->label:I

    .line 450
    .line 451
    invoke-static {v0, v5}, Lkotlinx/coroutines/flow/g;->L(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 452
    .line 453
    .line 454
    move-result-object v0
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_2

    .line 455
    if-ne v0, v4, :cond_10

    .line 456
    .line 457
    :goto_f
    move-object/from16 v2, p0

    .line 458
    .line 459
    goto/16 :goto_2c

    .line 460
    .line 461
    :cond_10
    move-object/from16 v19, v2

    .line 462
    .line 463
    move v2, v3

    .line 464
    move-object v3, v5

    .line 465
    move v5, v8

    .line 466
    move/from16 v30, v13

    .line 467
    .line 468
    move-object v13, v7

    .line 469
    move-wide v7, v9

    .line 470
    move-wide v9, v11

    .line 471
    move/from16 v11, v30

    .line 472
    .line 473
    move/from16 v12, v24

    .line 474
    .line 475
    :goto_10
    :try_start_5
    check-cast v0, Ljava/util/List;

    .line 476
    .line 477
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 478
    .line 479
    .line 480
    move-result-object v0
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    .line 481
    move-object v1, v3

    .line 482
    move-wide v13, v9

    .line 483
    move v9, v11

    .line 484
    move-object/from16 v3, v19

    .line 485
    .line 486
    move-object/from16 v19, v6

    .line 487
    .line 488
    :goto_11
    move v5, v12

    .line 489
    goto/16 :goto_1a

    .line 490
    .line 491
    :catchall_3
    move-exception v0

    .line 492
    move/from16 v24, v1

    .line 493
    .line 494
    goto/16 :goto_6

    .line 495
    .line 496
    :goto_12
    if-eqz v5, :cond_11

    .line 497
    .line 498
    move/from16 p1, v2

    .line 499
    .line 500
    :try_start_6
    instance-of v2, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 501
    .line 502
    if-eqz v2, :cond_12

    .line 503
    .line 504
    move-object v2, v0

    .line 505
    check-cast v2, Lcom/xbet/onexcore/data/model/ServerException;

    .line 506
    .line 507
    invoke-virtual {v2}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 508
    .line 509
    .line 510
    move-result v2

    .line 511
    if-eqz v2, :cond_12

    .line 512
    .line 513
    const/16 v18, 0x1

    .line 514
    .line 515
    goto :goto_14

    .line 516
    :catchall_4
    move-exception v0

    .line 517
    :goto_13
    move-object/from16 v2, p0

    .line 518
    .line 519
    goto/16 :goto_2b

    .line 520
    .line 521
    :cond_11
    move/from16 p1, v2

    .line 522
    .line 523
    :cond_12
    const/16 v18, 0x0

    .line 524
    .line 525
    :goto_14
    instance-of v2, v0, Ljava/util/concurrent/CancellationException;

    .line 526
    .line 527
    if-nez v2, :cond_25

    .line 528
    .line 529
    instance-of v2, v0, Ljava/net/ConnectException;

    .line 530
    .line 531
    if-nez v2, :cond_25

    .line 532
    .line 533
    if-nez v18, :cond_25

    .line 534
    .line 535
    instance-of v2, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 536
    .line 537
    if-eqz v2, :cond_15

    .line 538
    .line 539
    move-object v2, v0

    .line 540
    check-cast v2, Lcom/xbet/onexcore/data/model/ServerException;

    .line 541
    .line 542
    invoke-virtual {v2}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 543
    .line 544
    .line 545
    move-result v2

    .line 546
    if-nez v2, :cond_14

    .line 547
    .line 548
    move-object v2, v0

    .line 549
    check-cast v2, Lcom/xbet/onexcore/data/model/ServerException;

    .line 550
    .line 551
    invoke-virtual {v2}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 552
    .line 553
    .line 554
    move-result v2

    .line 555
    if-eqz v2, :cond_13

    .line 556
    .line 557
    goto :goto_16

    .line 558
    :cond_13
    const/16 v17, 0x0

    .line 559
    .line 560
    :goto_15
    const/16 v18, 0x1

    .line 561
    .line 562
    goto :goto_17

    .line 563
    :cond_14
    :goto_16
    const/16 v17, 0x1

    .line 564
    .line 565
    goto :goto_15

    .line 566
    :cond_15
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 567
    .line 568
    .line 569
    move-result v2

    .line 570
    if-nez v2, :cond_13

    .line 571
    .line 572
    goto :goto_16

    .line 573
    :goto_17
    add-int/lit8 v2, p1, 0x1

    .line 574
    .line 575
    move-object/from16 v19, v6

    .line 576
    .line 577
    const/4 v6, 0x3

    .line 578
    if-gt v2, v6, :cond_18

    .line 579
    .line 580
    if-eqz v17, :cond_16

    .line 581
    .line 582
    goto/16 :goto_19

    .line 583
    .line 584
    :cond_16
    new-instance v6, Ljava/lang/StringBuilder;

    .line 585
    .line 586
    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_4

    .line 587
    .line 588
    .line 589
    move-object/from16 v17, v4

    .line 590
    .line 591
    :try_start_7
    const-string v4, "error ("

    .line 592
    .line 593
    invoke-virtual {v6, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 594
    .line 595
    .line 596
    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 597
    .line 598
    .line 599
    const-string v4, "): "

    .line 600
    .line 601
    invoke-virtual {v6, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 602
    .line 603
    .line 604
    invoke-virtual {v6, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 605
    .line 606
    .line 607
    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 608
    .line 609
    .line 610
    move-result-object v0

    .line 611
    sget-object v4, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 612
    .line 613
    invoke-virtual {v4, v0}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 614
    .line 615
    .line 616
    iput-object v10, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 617
    .line 618
    iput-object v15, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$1:Ljava/lang/Object;

    .line 619
    .line 620
    iput-object v11, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$2:Ljava/lang/Object;

    .line 621
    .line 622
    iput-object v1, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$3:Ljava/lang/Object;

    .line 623
    .line 624
    iput v12, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$0:I

    .line 625
    .line 626
    iput v9, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$1:I

    .line 627
    .line 628
    iput-wide v13, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->J$0:J

    .line 629
    .line 630
    iput-wide v7, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->J$1:J

    .line 631
    .line 632
    iput v5, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$2:I

    .line 633
    .line 634
    iput v2, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$3:I

    .line 635
    .line 636
    const/4 v4, 0x2

    .line 637
    iput v4, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->label:I

    .line 638
    .line 639
    move/from16 p1, v5

    .line 640
    .line 641
    const-wide/16 v4, 0xbb8

    .line 642
    .line 643
    invoke-static {v4, v5, v1}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 644
    .line 645
    .line 646
    move-result-object v0
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_5

    .line 647
    move-object/from16 v4, v17

    .line 648
    .line 649
    if-ne v0, v4, :cond_17

    .line 650
    .line 651
    goto/16 :goto_f

    .line 652
    .line 653
    :cond_17
    move-object v5, v3

    .line 654
    move/from16 v24, v12

    .line 655
    .line 656
    move v3, v2

    .line 657
    move-object v2, v10

    .line 658
    move-wide/from16 v30, v7

    .line 659
    .line 660
    move/from16 v8, p1

    .line 661
    .line 662
    move-object v7, v1

    .line 663
    move-wide/from16 v32, v13

    .line 664
    .line 665
    move v13, v9

    .line 666
    move-wide/from16 v9, v30

    .line 667
    .line 668
    move-object v14, v11

    .line 669
    move-wide/from16 v11, v32

    .line 670
    .line 671
    :goto_18
    move-object/from16 v1, p0

    .line 672
    .line 673
    move-object/from16 v6, v19

    .line 674
    .line 675
    goto/16 :goto_4

    .line 676
    .line 677
    :catchall_5
    move-exception v0

    .line 678
    move-object/from16 v4, v17

    .line 679
    .line 680
    goto/16 :goto_13

    .line 681
    .line 682
    :cond_18
    :goto_19
    :try_start_8
    sget-object v1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 683
    .line 684
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 685
    .line 686
    .line 687
    move-result-object v0

    .line 688
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 689
    .line 690
    .line 691
    move-result-object v0
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_4

    .line 692
    move-object v1, v3

    .line 693
    move-object v3, v10

    .line 694
    goto/16 :goto_11

    .line 695
    .line 696
    :goto_1a
    :try_start_9
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 697
    .line 698
    .line 699
    check-cast v0, Ljava/util/List;
    :try_end_9
    .catchall {:try_start_9 .. :try_end_9} :catchall_6

    .line 700
    .line 701
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 702
    .line 703
    .line 704
    move-result-wide v10

    .line 705
    sub-long/2addr v10, v7

    .line 706
    cmp-long v2, v10, v13

    .line 707
    .line 708
    if-gez v2, :cond_1a

    .line 709
    .line 710
    sub-long/2addr v13, v10

    .line 711
    iput-object v3, v1, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 712
    .line 713
    iput-object v0, v1, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$1:Ljava/lang/Object;

    .line 714
    .line 715
    const/4 v2, 0x0

    .line 716
    iput-object v2, v1, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$2:Ljava/lang/Object;

    .line 717
    .line 718
    iput-object v2, v1, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$3:Ljava/lang/Object;

    .line 719
    .line 720
    iput v5, v1, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$0:I

    .line 721
    .line 722
    iput v9, v1, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->I$1:I

    .line 723
    .line 724
    const/4 v6, 0x3

    .line 725
    iput v6, v1, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->label:I

    .line 726
    .line 727
    invoke-static {v13, v14, v1}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 728
    .line 729
    .line 730
    move-result-object v1

    .line 731
    if-ne v1, v4, :cond_19

    .line 732
    .line 733
    goto/16 :goto_f

    .line 734
    .line 735
    :cond_19
    move v4, v9

    .line 736
    move-object v9, v0

    .line 737
    :goto_1b
    move v12, v4

    .line 738
    move-object v0, v9

    .line 739
    :goto_1c
    move-object/from16 v2, p0

    .line 740
    .line 741
    goto :goto_1d

    .line 742
    :cond_1a
    move v12, v9

    .line 743
    goto :goto_1c

    .line 744
    :goto_1d
    iget-object v1, v2, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->i:Ljava/util/List;

    .line 745
    .line 746
    invoke-interface {v1, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 747
    .line 748
    .line 749
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 750
    .line 751
    .line 752
    move-result v1

    .line 753
    if-nez v1, :cond_23

    .line 754
    .line 755
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 756
    .line 757
    .line 758
    move-result v1

    .line 759
    const/16 v4, 0x3c

    .line 760
    .line 761
    if-lt v1, v4, :cond_23

    .line 762
    .line 763
    iget-boolean v1, v2, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->c:Z

    .line 764
    .line 765
    if-eqz v1, :cond_1b

    .line 766
    .line 767
    goto/16 :goto_28

    .line 768
    .line 769
    :cond_1b
    invoke-virtual {v3}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 770
    .line 771
    .line 772
    move-result-object v1

    .line 773
    check-cast v1, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 774
    .line 775
    if-eqz v1, :cond_1c

    .line 776
    .line 777
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->c()J

    .line 778
    .line 779
    .line 780
    move-result-wide v6

    .line 781
    :goto_1e
    move-wide v10, v6

    .line 782
    goto :goto_1f

    .line 783
    :cond_1c
    const-wide/16 v6, -0x1

    .line 784
    .line 785
    goto :goto_1e

    .line 786
    :goto_1f
    invoke-virtual {v3}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 787
    .line 788
    .line 789
    move-result-object v1

    .line 790
    check-cast v1, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 791
    .line 792
    if-eqz v1, :cond_1e

    .line 793
    .line 794
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->f()Ljava/lang/String;

    .line 795
    .line 796
    .line 797
    move-result-object v1

    .line 798
    if-nez v1, :cond_1d

    .line 799
    .line 800
    goto :goto_21

    .line 801
    :cond_1d
    :goto_20
    move-object v7, v1

    .line 802
    goto :goto_22

    .line 803
    :cond_1e
    :goto_21
    sget-object v1, Lorg/xplatform/aggregator/api/model/ProductSortType;->BY_POPULARITY:Lorg/xplatform/aggregator/api/model/ProductSortType;

    .line 804
    .line 805
    invoke-static {v1}, Lg81/g;->c(Lorg/xplatform/aggregator/api/model/ProductSortType;)Ljava/lang/String;

    .line 806
    .line 807
    .line 808
    move-result-object v1

    .line 809
    goto :goto_20

    .line 810
    :goto_22
    invoke-virtual {v3}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 811
    .line 812
    .line 813
    move-result-object v1

    .line 814
    check-cast v1, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 815
    .line 816
    if-eqz v1, :cond_1f

    .line 817
    .line 818
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->d()Ljava/lang/String;

    .line 819
    .line 820
    .line 821
    move-result-object v1

    .line 822
    goto :goto_23

    .line 823
    :cond_1f
    const/4 v1, 0x0

    .line 824
    :goto_23
    if-nez v1, :cond_20

    .line 825
    .line 826
    move-object/from16 v8, v19

    .line 827
    .line 828
    :goto_24
    const/16 v16, 0x3c

    .line 829
    .line 830
    goto :goto_25

    .line 831
    :cond_20
    move-object v8, v1

    .line 832
    goto :goto_24

    .line 833
    :goto_25
    add-int/lit8 v9, v5, 0x3c

    .line 834
    .line 835
    const-wide/16 v13, 0x3e8

    .line 836
    .line 837
    invoke-static {v13, v14}, LHc/a;->f(J)Ljava/lang/Long;

    .line 838
    .line 839
    .line 840
    move-result-object v1

    .line 841
    invoke-virtual {v1}, Ljava/lang/Number;->longValue()J

    .line 842
    .line 843
    .line 844
    if-nez v12, :cond_21

    .line 845
    .line 846
    goto :goto_26

    .line 847
    :cond_21
    const/4 v1, 0x0

    .line 848
    :goto_26
    if-eqz v1, :cond_22

    .line 849
    .line 850
    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    .line 851
    .line 852
    .line 853
    move-result-wide v13

    .line 854
    goto :goto_27

    .line 855
    :cond_22
    const-wide/16 v13, 0x0

    .line 856
    .line 857
    :goto_27
    new-instance v6, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 858
    .line 859
    invoke-direct/range {v6 .. v14}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;-><init>(Ljava/lang/String;Ljava/lang/String;IJIJ)V

    .line 860
    .line 861
    .line 862
    goto :goto_29

    .line 863
    :cond_23
    :goto_28
    const/4 v6, 0x0

    .line 864
    :goto_29
    new-instance v1, Landroidx/paging/PagingSource$b$c;

    .line 865
    .line 866
    iget-object v4, v2, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->g:LHX0/e;

    .line 867
    .line 868
    invoke-static {v0, v4}, La81/a;->a(Ljava/util/List;LHX0/e;)Ljava/util/List;

    .line 869
    .line 870
    .line 871
    move-result-object v0

    .line 872
    if-nez v5, :cond_24

    .line 873
    .line 874
    const/4 v14, 0x0

    .line 875
    goto :goto_2a

    .line 876
    :cond_24
    invoke-virtual {v3}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 877
    .line 878
    .line 879
    move-result-object v3

    .line 880
    move-object v14, v3

    .line 881
    check-cast v14, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 882
    .line 883
    :goto_2a
    invoke-direct {v1, v0, v14, v6}, Landroidx/paging/PagingSource$b$c;-><init>(Ljava/util/List;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 884
    .line 885
    .line 886
    invoke-static {v6, v1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 887
    .line 888
    .line 889
    move-result-object v0

    .line 890
    return-object v0

    .line 891
    :catchall_6
    move-exception v0

    .line 892
    move-object/from16 v2, p0

    .line 893
    .line 894
    move-object v3, v1

    .line 895
    goto :goto_2b

    .line 896
    :cond_25
    move-object/from16 v2, p0

    .line 897
    .line 898
    :try_start_a
    throw v0
    :try_end_a
    .catchall {:try_start_a .. :try_end_a} :catchall_7

    .line 899
    :catchall_7
    move-exception v0

    .line 900
    :goto_2b
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 901
    .line 902
    .line 903
    move-result-wide v5

    .line 904
    sub-long/2addr v5, v7

    .line 905
    cmp-long v1, v5, v13

    .line 906
    .line 907
    if-gez v1, :cond_26

    .line 908
    .line 909
    sub-long/2addr v13, v5

    .line 910
    iput-object v0, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 911
    .line 912
    const/4 v1, 0x0

    .line 913
    iput-object v1, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$1:Ljava/lang/Object;

    .line 914
    .line 915
    iput-object v1, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$2:Ljava/lang/Object;

    .line 916
    .line 917
    iput-object v1, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->L$3:Ljava/lang/Object;

    .line 918
    .line 919
    const/4 v1, 0x4

    .line 920
    iput v1, v3, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource$loadNextPage$1;->label:I

    .line 921
    .line 922
    invoke-static {v13, v14, v3}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 923
    .line 924
    .line 925
    move-result-object v1

    .line 926
    if-ne v1, v4, :cond_26

    .line 927
    .line 928
    :goto_2c
    return-object v4

    .line 929
    :cond_26
    :goto_2d
    throw v0
.end method

.method public final m()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lg81/j;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->i:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public n(Landroidx/paging/M;)Lorg/xplatform/aggregator/api/brands/presentation/paging/a;
    .locals 14
    .param p1    # Landroidx/paging/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/paging/M<",
            "Lorg/xplatform/aggregator/api/brands/presentation/paging/a;",
            "LP21/c;",
            ">;)",
            "Lorg/xplatform/aggregator/api/brands/presentation/paging/a;"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, Landroidx/paging/M;->d()Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_10

    .line 7
    .line 8
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    invoke-virtual {p1, v0}, Landroidx/paging/M;->c(I)Landroidx/paging/PagingSource$b$c;

    .line 13
    .line 14
    .line 15
    move-result-object v2

    .line 16
    if-eqz v2, :cond_0

    .line 17
    .line 18
    invoke-virtual {v2}, Landroidx/paging/PagingSource$b$c;->i()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    check-cast v2, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_0
    move-object v2, v1

    .line 26
    :goto_0
    invoke-virtual {p1, v0}, Landroidx/paging/M;->c(I)Landroidx/paging/PagingSource$b$c;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    if-eqz v0, :cond_1

    .line 31
    .line 32
    invoke-virtual {v0}, Landroidx/paging/PagingSource$b$c;->f()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    check-cast v0, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 37
    .line 38
    goto :goto_1

    .line 39
    :cond_1
    move-object v0, v1

    .line 40
    :goto_1
    const/4 v3, 0x0

    .line 41
    if-eqz v2, :cond_2

    .line 42
    .line 43
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->e()I

    .line 44
    .line 45
    .line 46
    move-result v4

    .line 47
    invoke-virtual {p1}, Landroidx/paging/M;->e()Landroidx/paging/C;

    .line 48
    .line 49
    .line 50
    move-result-object v5

    .line 51
    iget v5, v5, Landroidx/paging/C;->a:I

    .line 52
    .line 53
    add-int/2addr v4, v5

    .line 54
    :goto_2
    move v11, v4

    .line 55
    goto :goto_3

    .line 56
    :cond_2
    if-eqz v0, :cond_3

    .line 57
    .line 58
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->e()I

    .line 59
    .line 60
    .line 61
    move-result v4

    .line 62
    invoke-virtual {p1}, Landroidx/paging/M;->e()Landroidx/paging/C;

    .line 63
    .line 64
    .line 65
    move-result-object v5

    .line 66
    iget v5, v5, Landroidx/paging/C;->a:I

    .line 67
    .line 68
    sub-int/2addr v4, v5

    .line 69
    goto :goto_2

    .line 70
    :cond_3
    const/4 v11, 0x0

    .line 71
    :goto_3
    const-wide/16 v4, 0x0

    .line 72
    .line 73
    if-eqz v2, :cond_4

    .line 74
    .line 75
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->c()J

    .line 76
    .line 77
    .line 78
    move-result-wide v6

    .line 79
    :goto_4
    move-wide v9, v6

    .line 80
    goto :goto_5

    .line 81
    :cond_4
    if-eqz v0, :cond_5

    .line 82
    .line 83
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->c()J

    .line 84
    .line 85
    .line 86
    move-result-wide v6

    .line 87
    goto :goto_4

    .line 88
    :cond_5
    move-wide v9, v4

    .line 89
    :goto_5
    if-eqz v2, :cond_6

    .line 90
    .line 91
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->f()Ljava/lang/String;

    .line 92
    .line 93
    .line 94
    move-result-object v6

    .line 95
    if-nez v6, :cond_8

    .line 96
    .line 97
    :cond_6
    if-eqz v0, :cond_7

    .line 98
    .line 99
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->f()Ljava/lang/String;

    .line 100
    .line 101
    .line 102
    move-result-object v6

    .line 103
    goto :goto_6

    .line 104
    :cond_7
    sget-object v6, Lorg/xplatform/aggregator/api/model/ProductSortType;->BY_POPULARITY:Lorg/xplatform/aggregator/api/model/ProductSortType;

    .line 105
    .line 106
    invoke-static {v6}, Lg81/g;->c(Lorg/xplatform/aggregator/api/model/ProductSortType;)Ljava/lang/String;

    .line 107
    .line 108
    .line 109
    move-result-object v6

    .line 110
    :cond_8
    :goto_6
    if-eqz v2, :cond_9

    .line 111
    .line 112
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->d()Ljava/lang/String;

    .line 113
    .line 114
    .line 115
    move-result-object v7

    .line 116
    if-nez v7, :cond_b

    .line 117
    .line 118
    :cond_9
    if-eqz v0, :cond_a

    .line 119
    .line 120
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->d()Ljava/lang/String;

    .line 121
    .line 122
    .line 123
    move-result-object v7

    .line 124
    goto :goto_7

    .line 125
    :cond_a
    move-object v7, v1

    .line 126
    :goto_7
    if-nez v7, :cond_b

    .line 127
    .line 128
    const-string v7, ""

    .line 129
    .line 130
    :cond_b
    if-eqz v2, :cond_c

    .line 131
    .line 132
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->b()I

    .line 133
    .line 134
    .line 135
    move-result v0

    .line 136
    invoke-virtual {p1}, Landroidx/paging/M;->e()Landroidx/paging/C;

    .line 137
    .line 138
    .line 139
    move-result-object p1

    .line 140
    iget p1, p1, Landroidx/paging/C;->a:I

    .line 141
    .line 142
    add-int v3, v0, p1

    .line 143
    .line 144
    :goto_8
    move v8, v3

    .line 145
    goto :goto_9

    .line 146
    :cond_c
    if-eqz v0, :cond_d

    .line 147
    .line 148
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;->b()I

    .line 149
    .line 150
    .line 151
    move-result v0

    .line 152
    invoke-virtual {p1}, Landroidx/paging/M;->e()Landroidx/paging/C;

    .line 153
    .line 154
    .line 155
    move-result-object p1

    .line 156
    iget p1, p1, Landroidx/paging/C;->a:I

    .line 157
    .line 158
    sub-int v3, v0, p1

    .line 159
    .line 160
    goto :goto_8

    .line 161
    :cond_d
    const/4 v8, 0x0

    .line 162
    :goto_9
    const-wide/16 v2, 0x3e8

    .line 163
    .line 164
    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 165
    .line 166
    .line 167
    move-result-object p1

    .line 168
    if-nez v11, :cond_e

    .line 169
    .line 170
    iget-boolean v0, p0, Lorg/xplatform/aggregator/api/brands/presentation/paging/BrandsPagingSource;->c:Z

    .line 171
    .line 172
    if-nez v0, :cond_e

    .line 173
    .line 174
    move-object v1, p1

    .line 175
    :cond_e
    if-eqz v1, :cond_f

    .line 176
    .line 177
    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    .line 178
    .line 179
    .line 180
    move-result-wide v4

    .line 181
    :cond_f
    move-wide v12, v4

    .line 182
    new-instance v5, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;

    .line 183
    .line 184
    invoke-direct/range {v5 .. v13}, Lorg/xplatform/aggregator/api/brands/presentation/paging/a;-><init>(Ljava/lang/String;Ljava/lang/String;IJIJ)V

    .line 185
    .line 186
    .line 187
    return-object v5

    .line 188
    :cond_10
    return-object v1
.end method

.method public o(Lorg/xplatform/aggregator/api/brands/presentation/paging/a;Lorg/xplatform/aggregator/api/brands/presentation/paging/a;)Z
    .locals 0

    .line 1
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method
