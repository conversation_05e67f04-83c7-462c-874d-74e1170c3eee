.class public final LGJ0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LGJ0/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LGJ0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LGJ0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LGJ0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lf8/g;Lc8/h;LwX0/c;LCJ0/a;LSX0/a;LHX0/e;)LGJ0/c;
    .locals 8

    .line 1
    invoke-static {p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static {p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static {p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static {p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static {p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static {p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    new-instance v0, LGJ0/a$b;

    .line 20
    .line 21
    const/4 v7, 0x0

    .line 22
    move-object v1, p1

    .line 23
    move-object v2, p2

    .line 24
    move-object v3, p3

    .line 25
    move-object v4, p4

    .line 26
    move-object v5, p5

    .line 27
    move-object v6, p6

    .line 28
    invoke-direct/range {v0 .. v7}, LGJ0/a$b;-><init>(Lf8/g;Lc8/h;LwX0/c;LCJ0/a;LSX0/a;LHX0/e;LGJ0/b;)V

    .line 29
    .line 30
    .line 31
    return-object v0
.end method
