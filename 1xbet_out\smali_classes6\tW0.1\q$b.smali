.class public interface abstract LtW0/q$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/i;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LtW0/q;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "LQW0/i<",
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;",
        "LwX0/c;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0008g\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001\u00a8\u0006\u0004"
    }
    d2 = {
        "LtW0/q$b;",
        "LQW0/i;",
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;",
        "LwX0/c;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation
