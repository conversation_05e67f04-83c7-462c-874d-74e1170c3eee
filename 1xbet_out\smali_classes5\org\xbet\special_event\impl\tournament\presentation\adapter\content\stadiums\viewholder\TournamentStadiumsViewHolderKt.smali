.class public final Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/viewholder/TournamentStadiumsViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a3\u0010\t\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00080\u00070\u00062\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0004H\u0000\u00a2\u0006\u0004\u0008\t\u0010\n\u00a8\u0006\u000b"
    }
    d2 = {
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "",
        "nestedPrefetchItemCount",
        "Ltx0/a;",
        "tournamentStadiumClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "f",
        "(LUX0/k;ILtx0/a;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lrx0/a;LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/viewholder/TournamentStadiumsViewHolderKt;->i(Lrx0/a;LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/g1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/viewholder/TournamentStadiumsViewHolderKt;->g(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/g1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Ltx0/a;ILUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/viewholder/TournamentStadiumsViewHolderKt;->h(Ltx0/a;ILUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/viewholder/TournamentStadiumsViewHolderKt;->j(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/viewholder/TournamentStadiumsViewHolderKt;->k(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final f(LUX0/k;ILtx0/a;)LA4/c;
    .locals 2
    .param p0    # LUX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ltx0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LUX0/k;",
            "I",
            "Ltx0/a;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lwx0/a;

    .line 2
    .line 3
    invoke-direct {v0}, Lwx0/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lwx0/b;

    .line 7
    .line 8
    invoke-direct {v1, p2, p1, p0}, Lwx0/b;-><init>(Ltx0/a;ILUX0/k;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/viewholder/TournamentStadiumsViewHolderKt$tournamentStadiumsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/viewholder/TournamentStadiumsViewHolderKt$tournamentStadiumsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/viewholder/TournamentStadiumsViewHolderKt$tournamentStadiumsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/viewholder/TournamentStadiumsViewHolderKt$tournamentStadiumsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance p2, LB4/b;

    .line 19
    .line 20
    invoke-direct {p2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object p2
.end method

.method public static final g(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/g1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/g1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/g1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final h(Ltx0/a;ILUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 18

    .line 1
    move-object/from16 v0, p2

    .line 2
    .line 3
    move-object/from16 v1, p3

    .line 4
    .line 5
    new-instance v2, Lrx0/a;

    .line 6
    .line 7
    move-object/from16 v3, p0

    .line 8
    .line 9
    invoke-direct {v2, v3}, Lrx0/a;-><init>(Ltx0/a;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v1}, LB4/a;->e()LL2/a;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    check-cast v3, LGq0/g1;

    .line 17
    .line 18
    iget-object v3, v3, LGq0/g1;->b:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 19
    .line 20
    new-instance v4, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 21
    .line 22
    invoke-virtual {v3}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 23
    .line 24
    .line 25
    move-result-object v5

    .line 26
    const/4 v6, 0x0

    .line 27
    invoke-direct {v4, v5, v6, v6}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;IZ)V

    .line 28
    .line 29
    .line 30
    const/4 v5, 0x1

    .line 31
    invoke-virtual {v4, v5}, Landroidx/recyclerview/widget/RecyclerView$LayoutManager;->setItemPrefetchEnabled(Z)V

    .line 32
    .line 33
    .line 34
    move/from16 v6, p1

    .line 35
    .line 36
    invoke-virtual {v4, v6}, Landroidx/recyclerview/widget/LinearLayoutManager;->setInitialPrefetchItemCount(I)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {v3, v4}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {v3, v5}, Landroidx/recyclerview/widget/RecyclerView;->setHasFixedSize(Z)V

    .line 43
    .line 44
    .line 45
    new-instance v6, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;

    .line 46
    .line 47
    invoke-virtual {v3}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 48
    .line 49
    .line 50
    move-result-object v4

    .line 51
    sget v5, Lpb/f;->space_8:I

    .line 52
    .line 53
    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 54
    .line 55
    .line 56
    move-result v7

    .line 57
    const/16 v16, 0x1de

    .line 58
    .line 59
    const/16 v17, 0x0

    .line 60
    .line 61
    const/4 v8, 0x0

    .line 62
    const/4 v9, 0x0

    .line 63
    const/4 v10, 0x0

    .line 64
    const/4 v11, 0x0

    .line 65
    const/4 v12, 0x0

    .line 66
    const/4 v13, 0x0

    .line 67
    const/4 v14, 0x0

    .line 68
    const/4 v15, 0x0

    .line 69
    invoke-direct/range {v6 .. v17}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;-><init>(IIIIIILkotlin/jvm/functions/Function1;Lorg/xbet/ui_common/viewcomponents/recycler/decorators/SpacingItemDecorationBias;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 70
    .line 71
    .line 72
    invoke-virtual {v3, v6}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 73
    .line 74
    .line 75
    invoke-virtual {v3, v2}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 76
    .line 77
    .line 78
    new-instance v4, Lwx0/c;

    .line 79
    .line 80
    invoke-direct {v4, v2, v1}, Lwx0/c;-><init>(Lrx0/a;LB4/a;)V

    .line 81
    .line 82
    .line 83
    invoke-virtual {v1, v4}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 84
    .line 85
    .line 86
    new-instance v2, Lwx0/d;

    .line 87
    .line 88
    invoke-direct {v2, v0, v1, v3}, Lwx0/d;-><init>(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)V

    .line 89
    .line 90
    .line 91
    invoke-virtual {v1, v2}, LB4/a;->r(Lkotlin/jvm/functions/Function0;)V

    .line 92
    .line 93
    .line 94
    new-instance v2, Lwx0/e;

    .line 95
    .line 96
    invoke-direct {v2, v0, v1, v3}, Lwx0/e;-><init>(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)V

    .line 97
    .line 98
    .line 99
    invoke-virtual {v1, v2}, LB4/a;->s(Lkotlin/jvm/functions/Function0;)V

    .line 100
    .line 101
    .line 102
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 103
    .line 104
    return-object v0
.end method

.method public static final i(Lrx0/a;LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lsx0/a;

    .line 6
    .line 7
    invoke-virtual {p1}, Lsx0/a;->d()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-virtual {p0, p1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final j(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getBindingAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p0, p1, p2}, LUX0/k;->c(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final k(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getBindingAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p0, p1, p2}, LUX0/k;->e(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method
