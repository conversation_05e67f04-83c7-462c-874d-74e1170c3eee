.class public final LrP0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LrP0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LrP0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LrP0/a$b$a;
    }
.end annotation


# instance fields
.field public final a:LrP0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LoP0/b;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/team/impl/team_transfer/data/repository/TeamTransferRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LuP0/a;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/team/impl/team_transfer/presentation/viewmodel/TeamTransferViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lf8/g;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LHX0/e;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LrP0/a$b;->a:LrP0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p9}, LrP0/a$b;->b(LQW0/c;Lf8/g;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LHX0/e;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Lf8/g;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LHX0/e;LrP0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p9}, LrP0/a$b;-><init>(LQW0/c;Lf8/g;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LHX0/e;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/team/impl/team_transfer/presentation/fragment/TeamTransferFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LrP0/a$b;->c(Lorg/xbet/statistic/team/impl/team_transfer/presentation/fragment/TeamTransferFragment;)Lorg/xbet/statistic/team/impl/team_transfer/presentation/fragment/TeamTransferFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;Lf8/g;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LHX0/e;)V
    .locals 0

    .line 1
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    iput-object p3, p0, LrP0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    iput-object p3, p0, LrP0/a$b;->c:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 14
    .line 15
    .line 16
    move-result-object p3

    .line 17
    iput-object p3, p0, LrP0/a$b;->d:Ldagger/internal/h;

    .line 18
    .line 19
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 20
    .line 21
    .line 22
    move-result-object p3

    .line 23
    iput-object p3, p0, LrP0/a$b;->e:Ldagger/internal/h;

    .line 24
    .line 25
    invoke-static {p2}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 26
    .line 27
    .line 28
    move-result-object p2

    .line 29
    iput-object p2, p0, LrP0/a$b;->f:Ldagger/internal/h;

    .line 30
    .line 31
    invoke-static {p2}, LoP0/c;->a(LBc/a;)LoP0/c;

    .line 32
    .line 33
    .line 34
    move-result-object p2

    .line 35
    iput-object p2, p0, LrP0/a$b;->g:Ldagger/internal/h;

    .line 36
    .line 37
    new-instance p2, LrP0/a$b$a;

    .line 38
    .line 39
    invoke-direct {p2, p1}, LrP0/a$b$a;-><init>(LQW0/c;)V

    .line 40
    .line 41
    .line 42
    iput-object p2, p0, LrP0/a$b;->h:Ldagger/internal/h;

    .line 43
    .line 44
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput-object p1, p0, LrP0/a$b;->i:Ldagger/internal/h;

    .line 49
    .line 50
    iget-object p2, p0, LrP0/a$b;->g:Ldagger/internal/h;

    .line 51
    .line 52
    iget-object p3, p0, LrP0/a$b;->h:Ldagger/internal/h;

    .line 53
    .line 54
    invoke-static {p2, p3, p1}, Lorg/xbet/statistic/team/impl/team_transfer/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/team/impl/team_transfer/data/repository/a;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    iput-object p1, p0, LrP0/a$b;->j:Ldagger/internal/h;

    .line 59
    .line 60
    invoke-static {p1}, LuP0/b;->a(LBc/a;)LuP0/b;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    iput-object p1, p0, LrP0/a$b;->k:Ldagger/internal/h;

    .line 65
    .line 66
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    iput-object p1, p0, LrP0/a$b;->l:Ldagger/internal/h;

    .line 71
    .line 72
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 73
    .line 74
    .line 75
    move-result-object p8

    .line 76
    iput-object p8, p0, LrP0/a$b;->m:Ldagger/internal/h;

    .line 77
    .line 78
    iget-object p2, p0, LrP0/a$b;->b:Ldagger/internal/h;

    .line 79
    .line 80
    iget-object p3, p0, LrP0/a$b;->c:Ldagger/internal/h;

    .line 81
    .line 82
    iget-object p4, p0, LrP0/a$b;->d:Ldagger/internal/h;

    .line 83
    .line 84
    iget-object p5, p0, LrP0/a$b;->e:Ldagger/internal/h;

    .line 85
    .line 86
    iget-object p6, p0, LrP0/a$b;->k:Ldagger/internal/h;

    .line 87
    .line 88
    iget-object p7, p0, LrP0/a$b;->l:Ldagger/internal/h;

    .line 89
    .line 90
    invoke-static/range {p2 .. p8}, Lorg/xbet/statistic/team/impl/team_transfer/presentation/viewmodel/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/team/impl/team_transfer/presentation/viewmodel/a;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    iput-object p1, p0, LrP0/a$b;->n:Ldagger/internal/h;

    .line 95
    .line 96
    return-void
.end method

.method public final c(Lorg/xbet/statistic/team/impl/team_transfer/presentation/fragment/TeamTransferFragment;)Lorg/xbet/statistic/team/impl/team_transfer/presentation/fragment/TeamTransferFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LrP0/a$b;->e()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/team/impl/team_transfer/presentation/fragment/e;->a(Lorg/xbet/statistic/team/impl/team_transfer/presentation/fragment/TeamTransferFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final d()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/statistic/team/impl/team_transfer/presentation/viewmodel/TeamTransferViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LrP0/a$b;->n:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final e()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LrP0/a$b;->d()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
