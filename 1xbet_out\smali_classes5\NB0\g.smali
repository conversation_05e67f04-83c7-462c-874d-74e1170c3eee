.class public final LNB0/g;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001aW\u0010\u000c\u001a\u00020\t2\u0008\u0008\u0002\u0010\u0001\u001a\u00020\u00002\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00022\u0006\u0010\u0006\u001a\u00020\u00052\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u00072\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007H\u0001\u00a2\u0006\u0004\u0008\u000c\u0010\r\u001a\u000f\u0010\u000f\u001a\u00020\u000eH\u0003\u00a2\u0006\u0004\u0008\u000f\u0010\u0010\u00a8\u0006\u0011"
    }
    d2 = {
        "Landroidx/compose/ui/l;",
        "modifier",
        "Landroidx/compose/runtime/r1;",
        "LNB0/a;",
        "uiModel",
        "",
        "isPlayersDuel",
        "Lkotlin/Function1;",
        "Lorg/xbet/sportgame/markets/impl/presentation/base/i;",
        "",
        "betEventClickListener",
        "betEventLongClickListener",
        "b",
        "(Landroidx/compose/ui/l;Landroidx/compose/runtime/r1;ZLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V",
        "Landroidx/compose/ui/layout/J;",
        "d",
        "(Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroidx/compose/ui/l;Landroidx/compose/runtime/r1;ZLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p8}, LNB0/g;->c(Landroidx/compose/ui/l;Landroidx/compose/runtime/r1;ZLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final b(Landroidx/compose/ui/l;Landroidx/compose/runtime/r1;ZLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V
    .locals 13
    .param p1    # Landroidx/compose/runtime/r1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/l;",
            "Landroidx/compose/runtime/r1<",
            "LNB0/a;",
            ">;Z",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/sportgame/markets/impl/presentation/base/i;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/sportgame/markets/impl/presentation/base/i;",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    move-object/from16 v4, p3

    .line 2
    .line 3
    move-object/from16 v5, p4

    .line 4
    .line 5
    move/from16 v6, p6

    .line 6
    .line 7
    const v0, 0x264a33e5

    .line 8
    .line 9
    .line 10
    move-object/from16 v1, p5

    .line 11
    .line 12
    invoke-interface {v1, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 13
    .line 14
    .line 15
    move-result-object v10

    .line 16
    and-int/lit8 v1, p7, 0x1

    .line 17
    .line 18
    const/4 v2, 0x2

    .line 19
    if-eqz v1, :cond_0

    .line 20
    .line 21
    or-int/lit8 v3, v6, 0x6

    .line 22
    .line 23
    goto :goto_1

    .line 24
    :cond_0
    and-int/lit8 v3, v6, 0x6

    .line 25
    .line 26
    if-nez v3, :cond_2

    .line 27
    .line 28
    invoke-interface {v10, p0}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 29
    .line 30
    .line 31
    move-result v3

    .line 32
    if-eqz v3, :cond_1

    .line 33
    .line 34
    const/4 v3, 0x4

    .line 35
    goto :goto_0

    .line 36
    :cond_1
    const/4 v3, 0x2

    .line 37
    :goto_0
    or-int/2addr v3, v6

    .line 38
    goto :goto_1

    .line 39
    :cond_2
    move v3, v6

    .line 40
    :goto_1
    and-int/lit8 v7, p7, 0x2

    .line 41
    .line 42
    if-eqz v7, :cond_3

    .line 43
    .line 44
    or-int/lit8 v3, v3, 0x30

    .line 45
    .line 46
    goto :goto_3

    .line 47
    :cond_3
    and-int/lit8 v7, v6, 0x30

    .line 48
    .line 49
    if-nez v7, :cond_5

    .line 50
    .line 51
    invoke-interface {v10, p1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 52
    .line 53
    .line 54
    move-result v7

    .line 55
    if-eqz v7, :cond_4

    .line 56
    .line 57
    const/16 v7, 0x20

    .line 58
    .line 59
    goto :goto_2

    .line 60
    :cond_4
    const/16 v7, 0x10

    .line 61
    .line 62
    :goto_2
    or-int/2addr v3, v7

    .line 63
    :cond_5
    :goto_3
    and-int/lit8 v7, p7, 0x4

    .line 64
    .line 65
    if-eqz v7, :cond_6

    .line 66
    .line 67
    or-int/lit16 v3, v3, 0x180

    .line 68
    .line 69
    goto :goto_5

    .line 70
    :cond_6
    and-int/lit16 v7, v6, 0x180

    .line 71
    .line 72
    if-nez v7, :cond_8

    .line 73
    .line 74
    invoke-interface {v10, p2}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 75
    .line 76
    .line 77
    move-result v7

    .line 78
    if-eqz v7, :cond_7

    .line 79
    .line 80
    const/16 v7, 0x100

    .line 81
    .line 82
    goto :goto_4

    .line 83
    :cond_7
    const/16 v7, 0x80

    .line 84
    .line 85
    :goto_4
    or-int/2addr v3, v7

    .line 86
    :cond_8
    :goto_5
    and-int/lit8 v7, p7, 0x8

    .line 87
    .line 88
    if-eqz v7, :cond_9

    .line 89
    .line 90
    or-int/lit16 v3, v3, 0xc00

    .line 91
    .line 92
    goto :goto_7

    .line 93
    :cond_9
    and-int/lit16 v7, v6, 0xc00

    .line 94
    .line 95
    if-nez v7, :cond_b

    .line 96
    .line 97
    invoke-interface {v10, v4}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 98
    .line 99
    .line 100
    move-result v7

    .line 101
    if-eqz v7, :cond_a

    .line 102
    .line 103
    const/16 v7, 0x800

    .line 104
    .line 105
    goto :goto_6

    .line 106
    :cond_a
    const/16 v7, 0x400

    .line 107
    .line 108
    :goto_6
    or-int/2addr v3, v7

    .line 109
    :cond_b
    :goto_7
    and-int/lit8 v7, p7, 0x10

    .line 110
    .line 111
    if-eqz v7, :cond_c

    .line 112
    .line 113
    or-int/lit16 v3, v3, 0x6000

    .line 114
    .line 115
    goto :goto_9

    .line 116
    :cond_c
    and-int/lit16 v7, v6, 0x6000

    .line 117
    .line 118
    if-nez v7, :cond_e

    .line 119
    .line 120
    invoke-interface {v10, v5}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 121
    .line 122
    .line 123
    move-result v7

    .line 124
    if-eqz v7, :cond_d

    .line 125
    .line 126
    const/16 v7, 0x4000

    .line 127
    .line 128
    goto :goto_8

    .line 129
    :cond_d
    const/16 v7, 0x2000

    .line 130
    .line 131
    :goto_8
    or-int/2addr v3, v7

    .line 132
    :cond_e
    :goto_9
    and-int/lit16 v7, v3, 0x2493

    .line 133
    .line 134
    const/16 v8, 0x2492

    .line 135
    .line 136
    if-ne v7, v8, :cond_11

    .line 137
    .line 138
    invoke-interface {v10}, Landroidx/compose/runtime/j;->c()Z

    .line 139
    .line 140
    .line 141
    move-result v7

    .line 142
    if-nez v7, :cond_f

    .line 143
    .line 144
    goto :goto_b

    .line 145
    :cond_f
    invoke-interface {v10}, Landroidx/compose/runtime/j;->n()V

    .line 146
    .line 147
    .line 148
    :cond_10
    :goto_a
    move-object v1, p0

    .line 149
    goto :goto_c

    .line 150
    :cond_11
    :goto_b
    if-eqz v1, :cond_12

    .line 151
    .line 152
    sget-object p0, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 153
    .line 154
    :cond_12
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 155
    .line 156
    .line 157
    move-result v1

    .line 158
    if-eqz v1, :cond_13

    .line 159
    .line 160
    const/4 v1, -0x1

    .line 161
    const-string v7, "org.xbet.sportgame.markets.impl.presentation.base.compose_nodes.EventRows (EventRows.kt:31)"

    .line 162
    .line 163
    invoke-static {v0, v3, v1, v7}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 164
    .line 165
    .line 166
    :cond_13
    const/4 v0, 0x0

    .line 167
    const/4 v1, 0x1

    .line 168
    const/4 v3, 0x0

    .line 169
    invoke-static {p0, v0, v1, v3}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 170
    .line 171
    .line 172
    move-result-object v7

    .line 173
    sget-object v8, LA11/a;->a:LA11/a;

    .line 174
    .line 175
    invoke-virtual {v8}, LA11/a;->L1()F

    .line 176
    .line 177
    .line 178
    move-result v8

    .line 179
    invoke-static {v7, v8, v0, v2, v3}, Landroidx/compose/foundation/layout/PaddingKt;->k(Landroidx/compose/ui/l;FFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 180
    .line 181
    .line 182
    move-result-object v7

    .line 183
    new-instance v0, LNB0/g$a;

    .line 184
    .line 185
    invoke-direct {v0, p1, v4, p2, v5}, LNB0/g$a;-><init>(Landroidx/compose/runtime/r1;Lkotlin/jvm/functions/Function1;ZLkotlin/jvm/functions/Function1;)V

    .line 186
    .line 187
    .line 188
    const/16 v2, 0x36

    .line 189
    .line 190
    const v3, 0x11a5e662

    .line 191
    .line 192
    .line 193
    invoke-static {v3, v1, v0, v10, v2}, Landroidx/compose/runtime/internal/b;->d(IZLjava/lang/Object;Landroidx/compose/runtime/j;I)Landroidx/compose/runtime/internal/a;

    .line 194
    .line 195
    .line 196
    move-result-object v8

    .line 197
    const/4 v0, 0x0

    .line 198
    invoke-static {v10, v0}, LNB0/g;->d(Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 199
    .line 200
    .line 201
    move-result-object v9

    .line 202
    const/16 v11, 0x30

    .line 203
    .line 204
    const/4 v12, 0x0

    .line 205
    invoke-static/range {v7 .. v12}, Landroidx/compose/ui/layout/LayoutKt;->a(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function2;Landroidx/compose/ui/layout/J;Landroidx/compose/runtime/j;II)V

    .line 206
    .line 207
    .line 208
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 209
    .line 210
    .line 211
    move-result v0

    .line 212
    if-eqz v0, :cond_10

    .line 213
    .line 214
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 215
    .line 216
    .line 217
    goto :goto_a

    .line 218
    :goto_c
    invoke-interface {v10}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 219
    .line 220
    .line 221
    move-result-object p0

    .line 222
    if-eqz p0, :cond_14

    .line 223
    .line 224
    new-instance v0, LNB0/b;

    .line 225
    .line 226
    move-object v2, p1

    .line 227
    move v3, p2

    .line 228
    move/from16 v7, p7

    .line 229
    .line 230
    invoke-direct/range {v0 .. v7}, LNB0/b;-><init>(Landroidx/compose/ui/l;Landroidx/compose/runtime/r1;ZLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;II)V

    .line 231
    .line 232
    .line 233
    invoke-interface {p0, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 234
    .line 235
    .line 236
    :cond_14
    return-void
.end method

.method public static final c(Landroidx/compose/ui/l;Landroidx/compose/runtime/r1;ZLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 8

    .line 1
    or-int/lit8 p5, p5, 0x1

    .line 2
    .line 3
    invoke-static {p5}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result v6

    .line 7
    move-object v0, p0

    .line 8
    move-object v1, p1

    .line 9
    move v2, p2

    .line 10
    move-object v3, p3

    .line 11
    move-object v4, p4

    .line 12
    move v7, p6

    .line 13
    move-object v5, p7

    .line 14
    invoke-static/range {v0 .. v7}, LNB0/g;->b(Landroidx/compose/ui/l;Landroidx/compose/runtime/r1;ZLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 15
    .line 16
    .line 17
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 18
    .line 19
    return-object p0
.end method

.method public static final d(Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;
    .locals 3

    .line 1
    const v0, -0x7338b858

    .line 2
    .line 3
    .line 4
    invoke-interface {p0, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 5
    .line 6
    .line 7
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    const/4 v1, -0x1

    .line 14
    const-string v2, "org.xbet.sportgame.markets.impl.presentation.base.compose_nodes.flowMeasurePolicy (EventRows.kt:87)"

    .line 15
    .line 16
    invoke-static {v0, p1, v1, v2}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 17
    .line 18
    .line 19
    :cond_0
    const p1, 0x6e3c21fe

    .line 20
    .line 21
    .line 22
    invoke-interface {p0, p1}, Landroidx/compose/runtime/j;->t(I)V

    .line 23
    .line 24
    .line 25
    invoke-interface {p0}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    sget-object v0, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 30
    .line 31
    invoke-virtual {v0}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    if-ne p1, v0, :cond_1

    .line 36
    .line 37
    sget-object p1, LNB0/g$b;->a:LNB0/g$b;

    .line 38
    .line 39
    invoke-interface {p0, p1}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    :cond_1
    check-cast p1, Landroidx/compose/ui/layout/J;

    .line 43
    .line 44
    invoke-interface {p0}, Landroidx/compose/runtime/j;->q()V

    .line 45
    .line 46
    .line 47
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 48
    .line 49
    .line 50
    move-result v0

    .line 51
    if-eqz v0, :cond_2

    .line 52
    .line 53
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 54
    .line 55
    .line 56
    :cond_2
    invoke-interface {p0}, Landroidx/compose/runtime/j;->q()V

    .line 57
    .line 58
    .line 59
    return-object p1
.end method
