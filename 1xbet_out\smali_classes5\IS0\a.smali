.class public final LIS0/a;
.super Landroidx/recyclerview/widget/RecyclerView$o;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LIS0/a$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000v\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0007\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0012\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0000\u0018\u0000 O2\u00020\u0001:\u0001PB\u009d\u0001\u0012\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0008\u0008\u0001\u0010\u0004\u001a\u00020\u0002\u0012\u0008\u0008\u0002\u0010\u0006\u001a\u00020\u0005\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0002\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0002\u0012\u0008\u0008\u0002\u0010\t\u001a\u00020\u0002\u0012\u0008\u0008\u0002\u0010\n\u001a\u00020\u0002\u0012\u0008\u0008\u0002\u0010\u000b\u001a\u00020\u0002\u0012\u0008\u0008\u0002\u0010\u000c\u001a\u00020\u0002\u0012\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u000f0\r\u0012\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u000f0\r\u0012\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u000f0\r\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u001f\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u001f\u0010\u001c\u001a\u00020\u00192\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001bJ\u001f\u0010\u001d\u001a\u00020\u00192\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001bJ\u001f\u0010\u001e\u001a\u00020\u00192\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001bJ\u001f\u0010 \u001a\u00020\u00192\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u001f\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008 \u0010!J\'\u0010&\u001a\u00020\u00192\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010#\u001a\u00020\"2\u0006\u0010%\u001a\u00020$H\u0016\u00a2\u0006\u0004\u0008&\u0010\'J/\u0010*\u001a\u00020\u00192\u0006\u0010)\u001a\u00020(2\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010#\u001a\u00020\"2\u0006\u0010%\u001a\u00020$H\u0016\u00a2\u0006\u0004\u0008*\u0010+J\u001b\u0010/\u001a\u00020\u00192\u000c\u0010.\u001a\u0008\u0012\u0004\u0012\u00020-0,\u00a2\u0006\u0004\u0008/\u00100J\'\u00102\u001a\u00020\u00192\u0006\u00101\u001a\u00020\u00022\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0012\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u00082\u00103J/\u00106\u001a\u00020\u00192\u0006\u00101\u001a\u00020\u00022\u0006\u00105\u001a\u0002042\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0012\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u00086\u00107R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u00108R\u0014\u0010\u0004\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00108R\u0014\u0010\u0006\u001a\u00020\u00058\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u00109R\u0014\u0010\u0007\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u00108R\u0014\u0010\u0008\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u00108R\u0014\u0010\t\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00108R\u0014\u0010\n\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u00108R\u0014\u0010\u000b\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u00108R\u0014\u0010\u000c\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u00108R \u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u000f0\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008;\u0010<R \u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u000f0\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u0010<R \u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u000f0\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010<R\u001c\u0010A\u001a\u0008\u0012\u0004\u0012\u0002040,8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008?\u0010@R\u001c\u0010C\u001a\u0008\u0012\u0004\u0012\u0002040,8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008B\u0010@R\u0014\u0010F\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010ER\u0014\u0010J\u001a\u00020G8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0014\u0010N\u001a\u00020K8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010M\u00a8\u0006Q"
    }
    d2 = {
        "LIS0/a;",
        "Landroidx/recyclerview/widget/RecyclerView$o;",
        "",
        "background",
        "middleDividerColor",
        "",
        "cornerRadius",
        "horizontalPadding",
        "firstHeaderTopPadding",
        "headerTopPadding",
        "sectionTopPadding",
        "bottomPadding",
        "dividerMarginHorizontal",
        "Lkotlin/Function1;",
        "",
        "",
        "header",
        "section",
        "child",
        "<init>",
        "(IIFIIIIIILkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "Landroid/view/View;",
        "view",
        "",
        "l",
        "(Landroid/graphics/Canvas;Landroid/view/View;)V",
        "h",
        "j",
        "i",
        "addPadding",
        "m",
        "(Landroid/view/View;Z)V",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "parent",
        "Landroidx/recyclerview/widget/RecyclerView$z;",
        "state",
        "onDraw",
        "(Landroid/graphics/Canvas;Landroidx/recyclerview/widget/RecyclerView;Landroidx/recyclerview/widget/RecyclerView$z;)V",
        "Landroid/graphics/Rect;",
        "outRect",
        "getItemOffsets",
        "(Landroid/graphics/Rect;Landroid/view/View;Landroidx/recyclerview/widget/RecyclerView;Landroidx/recyclerview/widget/RecyclerView$z;)V",
        "",
        "LVX0/i;",
        "items",
        "f",
        "(Ljava/util/List;)V",
        "position",
        "k",
        "(ILandroid/graphics/Canvas;Landroid/view/View;)V",
        "Lkotlin/ranges/IntRange;",
        "range",
        "g",
        "(ILkotlin/ranges/IntRange;Landroid/graphics/Canvas;Landroid/view/View;)V",
        "I",
        "F",
        "n",
        "o",
        "Lkotlin/jvm/functions/Function1;",
        "p",
        "q",
        "r",
        "Ljava/util/List;",
        "previousGroupRangeList",
        "s",
        "groupRangeList",
        "t",
        "Landroid/graphics/Rect;",
        "viewRect",
        "Landroid/graphics/Path;",
        "u",
        "Landroid/graphics/Path;",
        "viewPath",
        "Landroid/graphics/Paint;",
        "v",
        "Landroid/graphics/Paint;",
        "paint",
        "w",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final w:LIS0/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final f:I

.field public final g:I

.field public final h:F

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:I

.field public final m:I

.field public final n:I

.field public final o:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Object;",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Object;",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Object;",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public r:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lkotlin/ranges/IntRange;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public s:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lkotlin/ranges/IntRange;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final t:Landroid/graphics/Rect;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final u:Landroid/graphics/Path;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LIS0/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LIS0/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LIS0/a;->w:LIS0/a$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>(IIFIIIIIILkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .param p10    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(IIFIIIIII",
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Object;",
            "Ljava/lang/Boolean;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Object;",
            "Ljava/lang/Boolean;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Object;",
            "Ljava/lang/Boolean;",
            ">;)V"
        }
    .end annotation

    .line 2
    invoke-direct {p0}, Landroidx/recyclerview/widget/RecyclerView$o;-><init>()V

    .line 3
    iput p1, p0, LIS0/a;->f:I

    .line 4
    iput p2, p0, LIS0/a;->g:I

    .line 5
    iput p3, p0, LIS0/a;->h:F

    .line 6
    iput p4, p0, LIS0/a;->i:I

    .line 7
    iput p5, p0, LIS0/a;->j:I

    .line 8
    iput p6, p0, LIS0/a;->k:I

    .line 9
    iput p7, p0, LIS0/a;->l:I

    .line 10
    iput p8, p0, LIS0/a;->m:I

    .line 11
    iput p9, p0, LIS0/a;->n:I

    .line 12
    iput-object p10, p0, LIS0/a;->o:Lkotlin/jvm/functions/Function1;

    .line 13
    iput-object p11, p0, LIS0/a;->p:Lkotlin/jvm/functions/Function1;

    .line 14
    iput-object p12, p0, LIS0/a;->q:Lkotlin/jvm/functions/Function1;

    .line 15
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, LIS0/a;->r:Ljava/util/List;

    .line 16
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, LIS0/a;->s:Ljava/util/List;

    .line 17
    new-instance p1, Landroid/graphics/Rect;

    invoke-direct {p1}, Landroid/graphics/Rect;-><init>()V

    iput-object p1, p0, LIS0/a;->t:Landroid/graphics/Rect;

    .line 18
    new-instance p1, Landroid/graphics/Path;

    invoke-direct {p1}, Landroid/graphics/Path;-><init>()V

    iput-object p1, p0, LIS0/a;->u:Landroid/graphics/Path;

    .line 19
    new-instance p1, Landroid/graphics/Paint;

    invoke-direct {p1}, Landroid/graphics/Paint;-><init>()V

    const/4 p2, 0x1

    .line 20
    invoke-virtual {p1, p2}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    const/4 p2, 0x0

    .line 21
    invoke-virtual {p1, p2}, Landroid/graphics/Paint;->setColor(I)V

    .line 22
    sget-object p2, Landroid/graphics/Paint$Style;->FILL:Landroid/graphics/Paint$Style;

    invoke-virtual {p1, p2}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 23
    iput-object p1, p0, LIS0/a;->v:Landroid/graphics/Paint;

    return-void
.end method

.method public synthetic constructor <init>(IIFIIIIIILkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 15

    move/from16 v0, p13

    and-int/lit8 v1, v0, 0x4

    if-eqz v1, :cond_0

    const/4 v1, 0x0

    const/4 v5, 0x0

    goto :goto_0

    :cond_0
    move/from16 v5, p3

    :goto_0
    and-int/lit8 v1, v0, 0x8

    const/4 v2, 0x0

    if-eqz v1, :cond_1

    const/4 v6, 0x0

    goto :goto_1

    :cond_1
    move/from16 v6, p4

    :goto_1
    and-int/lit8 v1, v0, 0x10

    if-eqz v1, :cond_2

    const/4 v7, 0x0

    goto :goto_2

    :cond_2
    move/from16 v7, p5

    :goto_2
    and-int/lit8 v1, v0, 0x20

    if-eqz v1, :cond_3

    const/4 v8, 0x0

    goto :goto_3

    :cond_3
    move/from16 v8, p6

    :goto_3
    and-int/lit8 v1, v0, 0x40

    if-eqz v1, :cond_4

    const/4 v9, 0x0

    goto :goto_4

    :cond_4
    move/from16 v9, p7

    :goto_4
    and-int/lit16 v1, v0, 0x80

    if-eqz v1, :cond_5

    const/4 v10, 0x0

    goto :goto_5

    :cond_5
    move/from16 v10, p8

    :goto_5
    and-int/lit16 v0, v0, 0x100

    if-eqz v0, :cond_6

    const/4 v11, 0x0

    :goto_6
    move-object v2, p0

    move/from16 v3, p1

    move/from16 v4, p2

    move-object/from16 v12, p10

    move-object/from16 v13, p11

    move-object/from16 v14, p12

    goto :goto_7

    :cond_6
    move/from16 v11, p9

    goto :goto_6

    .line 1
    :goto_7
    invoke-direct/range {v2 .. v14}, LIS0/a;-><init>(IIFIIIIIILkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    return-void
.end method

.method private final h(Landroid/graphics/Canvas;Landroid/view/View;)V
    .locals 10

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-direct {p0, p2, v0}, LIS0/a;->m(Landroid/view/View;Z)V

    .line 3
    .line 4
    .line 5
    iget-object v1, p0, LIS0/a;->u:Landroid/graphics/Path;

    .line 6
    .line 7
    iget p2, p0, LIS0/a;->h:F

    .line 8
    .line 9
    const/4 v0, 0x2

    .line 10
    int-to-float v0, v0

    .line 11
    mul-float p2, p2, v0

    .line 12
    .line 13
    iget-object v0, p0, LIS0/a;->t:Landroid/graphics/Rect;

    .line 14
    .line 15
    new-instance v9, Landroid/graphics/RectF;

    .line 16
    .line 17
    invoke-direct {v9, v0}, Landroid/graphics/RectF;-><init>(Landroid/graphics/Rect;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {v1}, Landroid/graphics/Path;->reset()V

    .line 21
    .line 22
    .line 23
    iget v0, v9, Landroid/graphics/RectF;->left:F

    .line 24
    .line 25
    iget v2, v9, Landroid/graphics/RectF;->bottom:F

    .line 26
    .line 27
    invoke-virtual {v1, v0, v2}, Landroid/graphics/Path;->moveTo(FF)V

    .line 28
    .line 29
    .line 30
    iget v0, v9, Landroid/graphics/RectF;->left:F

    .line 31
    .line 32
    iget v2, v9, Landroid/graphics/RectF;->top:F

    .line 33
    .line 34
    iget v3, p0, LIS0/a;->h:F

    .line 35
    .line 36
    sub-float/2addr v2, v3

    .line 37
    invoke-virtual {v1, v0, v2}, Landroid/graphics/Path;->lineTo(FF)V

    .line 38
    .line 39
    .line 40
    iget v2, v9, Landroid/graphics/RectF;->left:F

    .line 41
    .line 42
    iget v3, v9, Landroid/graphics/RectF;->top:F

    .line 43
    .line 44
    add-float v4, v2, p2

    .line 45
    .line 46
    add-float v5, v3, p2

    .line 47
    .line 48
    const/high16 v7, 0x42b40000    # 90.0f

    .line 49
    .line 50
    const/4 v8, 0x0

    .line 51
    const/high16 v6, 0x43340000    # 180.0f

    .line 52
    .line 53
    invoke-virtual/range {v1 .. v8}, Landroid/graphics/Path;->arcTo(FFFFFFZ)V

    .line 54
    .line 55
    .line 56
    iget v0, v9, Landroid/graphics/RectF;->right:F

    .line 57
    .line 58
    iget v2, p0, LIS0/a;->h:F

    .line 59
    .line 60
    sub-float/2addr v0, v2

    .line 61
    iget v2, v9, Landroid/graphics/RectF;->top:F

    .line 62
    .line 63
    invoke-virtual {v1, v0, v2}, Landroid/graphics/Path;->lineTo(FF)V

    .line 64
    .line 65
    .line 66
    iget v4, v9, Landroid/graphics/RectF;->right:F

    .line 67
    .line 68
    sub-float v2, v4, p2

    .line 69
    .line 70
    iget v3, v9, Landroid/graphics/RectF;->top:F

    .line 71
    .line 72
    add-float v5, v3, p2

    .line 73
    .line 74
    const/high16 v6, -0x3d4c0000    # -90.0f

    .line 75
    .line 76
    invoke-virtual/range {v1 .. v8}, Landroid/graphics/Path;->arcTo(FFFFFFZ)V

    .line 77
    .line 78
    .line 79
    iget p2, v9, Landroid/graphics/RectF;->right:F

    .line 80
    .line 81
    iget v0, v9, Landroid/graphics/RectF;->bottom:F

    .line 82
    .line 83
    invoke-virtual {v1, p2, v0}, Landroid/graphics/Path;->lineTo(FF)V

    .line 84
    .line 85
    .line 86
    iget p2, v9, Landroid/graphics/RectF;->left:F

    .line 87
    .line 88
    iget v0, v9, Landroid/graphics/RectF;->bottom:F

    .line 89
    .line 90
    invoke-virtual {v1, p2, v0}, Landroid/graphics/Path;->lineTo(FF)V

    .line 91
    .line 92
    .line 93
    invoke-virtual {v1}, Landroid/graphics/Path;->close()V

    .line 94
    .line 95
    .line 96
    iget-object p2, p0, LIS0/a;->v:Landroid/graphics/Paint;

    .line 97
    .line 98
    iget v0, p0, LIS0/a;->f:I

    .line 99
    .line 100
    invoke-virtual {p2, v0}, Landroid/graphics/Paint;->setColor(I)V

    .line 101
    .line 102
    .line 103
    iget-object p2, p0, LIS0/a;->u:Landroid/graphics/Path;

    .line 104
    .line 105
    iget-object v0, p0, LIS0/a;->v:Landroid/graphics/Paint;

    .line 106
    .line 107
    invoke-virtual {p1, p2, v0}, Landroid/graphics/Canvas;->drawPath(Landroid/graphics/Path;Landroid/graphics/Paint;)V

    .line 108
    .line 109
    .line 110
    return-void
.end method

.method private final i(Landroid/graphics/Canvas;Landroid/view/View;)V
    .locals 10

    .line 1
    const/4 v0, 0x1

    .line 2
    invoke-direct {p0, p2, v0}, LIS0/a;->m(Landroid/view/View;Z)V

    .line 3
    .line 4
    .line 5
    iget-object v1, p0, LIS0/a;->u:Landroid/graphics/Path;

    .line 6
    .line 7
    iget p2, p0, LIS0/a;->h:F

    .line 8
    .line 9
    const/4 v0, 0x2

    .line 10
    int-to-float v0, v0

    .line 11
    mul-float p2, p2, v0

    .line 12
    .line 13
    iget-object v0, p0, LIS0/a;->t:Landroid/graphics/Rect;

    .line 14
    .line 15
    new-instance v9, Landroid/graphics/RectF;

    .line 16
    .line 17
    invoke-direct {v9, v0}, Landroid/graphics/RectF;-><init>(Landroid/graphics/Rect;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {v1}, Landroid/graphics/Path;->reset()V

    .line 21
    .line 22
    .line 23
    iget v0, v9, Landroid/graphics/RectF;->left:F

    .line 24
    .line 25
    iget v2, v9, Landroid/graphics/RectF;->top:F

    .line 26
    .line 27
    invoke-virtual {v1, v0, v2}, Landroid/graphics/Path;->moveTo(FF)V

    .line 28
    .line 29
    .line 30
    iget v0, v9, Landroid/graphics/RectF;->right:F

    .line 31
    .line 32
    iget v2, v9, Landroid/graphics/RectF;->top:F

    .line 33
    .line 34
    invoke-virtual {v1, v0, v2}, Landroid/graphics/Path;->lineTo(FF)V

    .line 35
    .line 36
    .line 37
    iget v0, v9, Landroid/graphics/RectF;->right:F

    .line 38
    .line 39
    iget v2, v9, Landroid/graphics/RectF;->bottom:F

    .line 40
    .line 41
    iget v3, p0, LIS0/a;->h:F

    .line 42
    .line 43
    sub-float/2addr v2, v3

    .line 44
    invoke-virtual {v1, v0, v2}, Landroid/graphics/Path;->lineTo(FF)V

    .line 45
    .line 46
    .line 47
    iget v4, v9, Landroid/graphics/RectF;->right:F

    .line 48
    .line 49
    sub-float v2, v4, p2

    .line 50
    .line 51
    iget v5, v9, Landroid/graphics/RectF;->bottom:F

    .line 52
    .line 53
    sub-float v3, v5, p2

    .line 54
    .line 55
    const/high16 v7, 0x42b40000    # 90.0f

    .line 56
    .line 57
    const/4 v8, 0x0

    .line 58
    const/4 v6, 0x0

    .line 59
    invoke-virtual/range {v1 .. v8}, Landroid/graphics/Path;->arcTo(FFFFFFZ)V

    .line 60
    .line 61
    .line 62
    iget v0, v9, Landroid/graphics/RectF;->left:F

    .line 63
    .line 64
    iget v2, p0, LIS0/a;->h:F

    .line 65
    .line 66
    add-float/2addr v0, v2

    .line 67
    iget v2, v9, Landroid/graphics/RectF;->bottom:F

    .line 68
    .line 69
    invoke-virtual {v1, v0, v2}, Landroid/graphics/Path;->lineTo(FF)V

    .line 70
    .line 71
    .line 72
    iget v2, v9, Landroid/graphics/RectF;->left:F

    .line 73
    .line 74
    iget v5, v9, Landroid/graphics/RectF;->bottom:F

    .line 75
    .line 76
    sub-float v3, v5, p2

    .line 77
    .line 78
    add-float v4, v2, p2

    .line 79
    .line 80
    const/high16 v6, 0x42b40000    # 90.0f

    .line 81
    .line 82
    invoke-virtual/range {v1 .. v8}, Landroid/graphics/Path;->arcTo(FFFFFFZ)V

    .line 83
    .line 84
    .line 85
    iget p2, v9, Landroid/graphics/RectF;->left:F

    .line 86
    .line 87
    iget v0, v9, Landroid/graphics/RectF;->top:F

    .line 88
    .line 89
    invoke-virtual {v1, p2, v0}, Landroid/graphics/Path;->lineTo(FF)V

    .line 90
    .line 91
    .line 92
    invoke-virtual {v1}, Landroid/graphics/Path;->close()V

    .line 93
    .line 94
    .line 95
    iget-object p2, p0, LIS0/a;->v:Landroid/graphics/Paint;

    .line 96
    .line 97
    iget v0, p0, LIS0/a;->f:I

    .line 98
    .line 99
    invoke-virtual {p2, v0}, Landroid/graphics/Paint;->setColor(I)V

    .line 100
    .line 101
    .line 102
    iget-object p2, p0, LIS0/a;->u:Landroid/graphics/Path;

    .line 103
    .line 104
    iget-object v0, p0, LIS0/a;->v:Landroid/graphics/Paint;

    .line 105
    .line 106
    invoke-virtual {p1, p2, v0}, Landroid/graphics/Canvas;->drawPath(Landroid/graphics/Path;Landroid/graphics/Paint;)V

    .line 107
    .line 108
    .line 109
    return-void
.end method

.method private final j(Landroid/graphics/Canvas;Landroid/view/View;)V
    .locals 4

    .line 1
    const/4 v0, 0x1

    .line 2
    invoke-direct {p0, p2, v0}, LIS0/a;->m(Landroid/view/View;Z)V

    .line 3
    .line 4
    .line 5
    iget-object v1, p0, LIS0/a;->v:Landroid/graphics/Paint;

    .line 6
    .line 7
    iget v2, p0, LIS0/a;->f:I

    .line 8
    .line 9
    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setColor(I)V

    .line 10
    .line 11
    .line 12
    iget-object v1, p0, LIS0/a;->t:Landroid/graphics/Rect;

    .line 13
    .line 14
    iget v2, v1, Landroid/graphics/Rect;->left:I

    .line 15
    .line 16
    iget v3, p0, LIS0/a;->n:I

    .line 17
    .line 18
    add-int/2addr v2, v3

    .line 19
    iput v2, v1, Landroid/graphics/Rect;->right:I

    .line 20
    .line 21
    iget-object v2, p0, LIS0/a;->v:Landroid/graphics/Paint;

    .line 22
    .line 23
    invoke-virtual {p1, v1, v2}, Landroid/graphics/Canvas;->drawRect(Landroid/graphics/Rect;Landroid/graphics/Paint;)V

    .line 24
    .line 25
    .line 26
    invoke-direct {p0, p2, v0}, LIS0/a;->m(Landroid/view/View;Z)V

    .line 27
    .line 28
    .line 29
    iget-object v1, p0, LIS0/a;->t:Landroid/graphics/Rect;

    .line 30
    .line 31
    iget v2, v1, Landroid/graphics/Rect;->right:I

    .line 32
    .line 33
    iget v3, p0, LIS0/a;->n:I

    .line 34
    .line 35
    sub-int/2addr v2, v3

    .line 36
    iput v2, v1, Landroid/graphics/Rect;->left:I

    .line 37
    .line 38
    iget-object v2, p0, LIS0/a;->v:Landroid/graphics/Paint;

    .line 39
    .line 40
    invoke-virtual {p1, v1, v2}, Landroid/graphics/Canvas;->drawRect(Landroid/graphics/Rect;Landroid/graphics/Paint;)V

    .line 41
    .line 42
    .line 43
    invoke-direct {p0, p2, v0}, LIS0/a;->m(Landroid/view/View;Z)V

    .line 44
    .line 45
    .line 46
    iget-object p2, p0, LIS0/a;->t:Landroid/graphics/Rect;

    .line 47
    .line 48
    iget v0, p2, Landroid/graphics/Rect;->left:I

    .line 49
    .line 50
    iget v1, p0, LIS0/a;->n:I

    .line 51
    .line 52
    add-int/2addr v0, v1

    .line 53
    iput v0, p2, Landroid/graphics/Rect;->left:I

    .line 54
    .line 55
    iget v0, p2, Landroid/graphics/Rect;->right:I

    .line 56
    .line 57
    sub-int/2addr v0, v1

    .line 58
    iput v0, p2, Landroid/graphics/Rect;->right:I

    .line 59
    .line 60
    iget-object p2, p0, LIS0/a;->v:Landroid/graphics/Paint;

    .line 61
    .line 62
    iget v0, p0, LIS0/a;->g:I

    .line 63
    .line 64
    invoke-virtual {p2, v0}, Landroid/graphics/Paint;->setColor(I)V

    .line 65
    .line 66
    .line 67
    iget-object p2, p0, LIS0/a;->t:Landroid/graphics/Rect;

    .line 68
    .line 69
    iget-object v0, p0, LIS0/a;->v:Landroid/graphics/Paint;

    .line 70
    .line 71
    invoke-virtual {p1, p2, v0}, Landroid/graphics/Canvas;->drawRect(Landroid/graphics/Rect;Landroid/graphics/Paint;)V

    .line 72
    .line 73
    .line 74
    return-void
.end method

.method private final l(Landroid/graphics/Canvas;Landroid/view/View;)V
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-direct {p0, p2, v0}, LIS0/a;->m(Landroid/view/View;Z)V

    .line 3
    .line 4
    .line 5
    iget-object p2, p0, LIS0/a;->t:Landroid/graphics/Rect;

    .line 6
    .line 7
    new-instance v0, Landroid/graphics/RectF;

    .line 8
    .line 9
    invoke-direct {v0, p2}, Landroid/graphics/RectF;-><init>(Landroid/graphics/Rect;)V

    .line 10
    .line 11
    .line 12
    iget p2, p0, LIS0/a;->h:F

    .line 13
    .line 14
    iget-object v1, p0, LIS0/a;->v:Landroid/graphics/Paint;

    .line 15
    .line 16
    invoke-virtual {p1, v0, p2, p2, v1}, Landroid/graphics/Canvas;->drawRoundRect(Landroid/graphics/RectF;FFLandroid/graphics/Paint;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method private final m(Landroid/view/View;Z)V
    .locals 1

    .line 1
    iget-object v0, p0, LIS0/a;->t:Landroid/graphics/Rect;

    .line 2
    .line 3
    invoke-virtual {p1, v0}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    .line 4
    .line 5
    .line 6
    if-eqz p2, :cond_0

    .line 7
    .line 8
    iget-object p1, p0, LIS0/a;->t:Landroid/graphics/Rect;

    .line 9
    .line 10
    iget p2, p1, Landroid/graphics/Rect;->left:I

    .line 11
    .line 12
    iget v0, p0, LIS0/a;->i:I

    .line 13
    .line 14
    sub-int/2addr p2, v0

    .line 15
    iput p2, p1, Landroid/graphics/Rect;->left:I

    .line 16
    .line 17
    iget p2, p1, Landroid/graphics/Rect;->right:I

    .line 18
    .line 19
    add-int/2addr p2, v0

    .line 20
    iput p2, p1, Landroid/graphics/Rect;->right:I

    .line 21
    .line 22
    iget p2, p1, Landroid/graphics/Rect;->bottom:I

    .line 23
    .line 24
    iget v0, p0, LIS0/a;->m:I

    .line 25
    .line 26
    add-int/2addr p2, v0

    .line 27
    iput p2, p1, Landroid/graphics/Rect;->bottom:I

    .line 28
    .line 29
    :cond_0
    return-void
.end method


# virtual methods
.method public final f(Ljava/util/List;)V
    .locals 7
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LIS0/a;->s:Ljava/util/List;

    .line 2
    .line 3
    iput-object v0, p0, LIS0/a;->r:Ljava/util/List;

    .line 4
    .line 5
    new-instance v0, Ljava/util/ArrayList;

    .line 6
    .line 7
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 8
    .line 9
    .line 10
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    const/4 v1, 0x0

    .line 15
    const/4 v2, 0x0

    .line 16
    move-object v3, v1

    .line 17
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 18
    .line 19
    .line 20
    move-result v4

    .line 21
    if-eqz v4, :cond_6

    .line 22
    .line 23
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    add-int/lit8 v5, v2, 0x1

    .line 28
    .line 29
    if-gez v2, :cond_0

    .line 30
    .line 31
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 32
    .line 33
    .line 34
    :cond_0
    check-cast v4, LVX0/i;

    .line 35
    .line 36
    iget-object v6, p0, LIS0/a;->p:Lkotlin/jvm/functions/Function1;

    .line 37
    .line 38
    invoke-interface {v6, v4}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object v6

    .line 42
    check-cast v6, Ljava/lang/Boolean;

    .line 43
    .line 44
    invoke-virtual {v6}, Ljava/lang/Boolean;->booleanValue()Z

    .line 45
    .line 46
    .line 47
    move-result v6

    .line 48
    if-eqz v6, :cond_2

    .line 49
    .line 50
    if-eqz v3, :cond_1

    .line 51
    .line 52
    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 53
    .line 54
    .line 55
    :cond_1
    new-instance v3, Lkotlin/ranges/IntRange;

    .line 56
    .line 57
    invoke-direct {v3, v2, v2}, Lkotlin/ranges/IntRange;-><init>(II)V

    .line 58
    .line 59
    .line 60
    goto :goto_1

    .line 61
    :cond_2
    iget-object v6, p0, LIS0/a;->q:Lkotlin/jvm/functions/Function1;

    .line 62
    .line 63
    invoke-interface {v6, v4}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object v4

    .line 67
    check-cast v4, Ljava/lang/Boolean;

    .line 68
    .line 69
    invoke-virtual {v4}, Ljava/lang/Boolean;->booleanValue()Z

    .line 70
    .line 71
    .line 72
    move-result v4

    .line 73
    if-eqz v4, :cond_3

    .line 74
    .line 75
    if-eqz v3, :cond_5

    .line 76
    .line 77
    new-instance v4, Lkotlin/ranges/IntRange;

    .line 78
    .line 79
    invoke-virtual {v3}, Lkotlin/ranges/c;->f()I

    .line 80
    .line 81
    .line 82
    move-result v3

    .line 83
    invoke-direct {v4, v3, v2}, Lkotlin/ranges/IntRange;-><init>(II)V

    .line 84
    .line 85
    .line 86
    move-object v3, v4

    .line 87
    goto :goto_1

    .line 88
    :cond_3
    if-eqz v3, :cond_4

    .line 89
    .line 90
    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 91
    .line 92
    .line 93
    :cond_4
    move-object v3, v1

    .line 94
    :cond_5
    :goto_1
    move v2, v5

    .line 95
    goto :goto_0

    .line 96
    :cond_6
    if-eqz v3, :cond_7

    .line 97
    .line 98
    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 99
    .line 100
    .line 101
    :cond_7
    iput-object v0, p0, LIS0/a;->s:Ljava/util/List;

    .line 102
    .line 103
    return-void
.end method

.method public final g(ILkotlin/ranges/IntRange;Landroid/graphics/Canvas;Landroid/view/View;)V
    .locals 2

    .line 1
    invoke-virtual {p2}, Lkotlin/ranges/c;->f()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-ne p1, v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p2}, Lkotlin/ranges/c;->f()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    invoke-virtual {p2}, Lkotlin/ranges/c;->i()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    if-ne v0, v1, :cond_0

    .line 16
    .line 17
    invoke-direct {p0, p3, p4}, LIS0/a;->l(Landroid/graphics/Canvas;Landroid/view/View;)V

    .line 18
    .line 19
    .line 20
    return-void

    .line 21
    :cond_0
    invoke-virtual {p2}, Lkotlin/ranges/c;->f()I

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    if-ne p1, v0, :cond_1

    .line 26
    .line 27
    invoke-direct {p0, p3, p4}, LIS0/a;->h(Landroid/graphics/Canvas;Landroid/view/View;)V

    .line 28
    .line 29
    .line 30
    return-void

    .line 31
    :cond_1
    invoke-virtual {p2}, Lkotlin/ranges/c;->i()I

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    if-ne p1, v0, :cond_2

    .line 36
    .line 37
    invoke-direct {p0, p3, p4}, LIS0/a;->i(Landroid/graphics/Canvas;Landroid/view/View;)V

    .line 38
    .line 39
    .line 40
    return-void

    .line 41
    :cond_2
    invoke-virtual {p2}, Lkotlin/ranges/c;->f()I

    .line 42
    .line 43
    .line 44
    move-result v0

    .line 45
    invoke-virtual {p2}, Lkotlin/ranges/c;->i()I

    .line 46
    .line 47
    .line 48
    move-result p2

    .line 49
    if-gt p1, p2, :cond_3

    .line 50
    .line 51
    if-gt v0, p1, :cond_3

    .line 52
    .line 53
    invoke-direct {p0, p3, p4}, LIS0/a;->j(Landroid/graphics/Canvas;Landroid/view/View;)V

    .line 54
    .line 55
    .line 56
    :cond_3
    return-void
.end method

.method public getItemOffsets(Landroid/graphics/Rect;Landroid/view/View;Landroidx/recyclerview/widget/RecyclerView;Landroidx/recyclerview/widget/RecyclerView$z;)V
    .locals 1
    .param p1    # Landroid/graphics/Rect;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Landroidx/recyclerview/widget/RecyclerView;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Landroidx/recyclerview/widget/RecyclerView$z;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p3}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 2
    .line 3
    .line 4
    move-result-object p4

    .line 5
    instance-of v0, p4, LA4/e;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    check-cast p4, LA4/e;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 p4, 0x0

    .line 13
    :goto_0
    if-nez p4, :cond_1

    .line 14
    .line 15
    goto :goto_3

    .line 16
    :cond_1
    invoke-virtual {p3, p2}, Landroidx/recyclerview/widget/RecyclerView;->getChildAdapterPosition(Landroid/view/View;)I

    .line 17
    .line 18
    .line 19
    move-result p2

    .line 20
    invoke-virtual {p4}, LA4/e;->getItems()Ljava/util/List;

    .line 21
    .line 22
    .line 23
    move-result-object p3

    .line 24
    invoke-static {p3, p2}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object p3

    .line 28
    if-nez p3, :cond_2

    .line 29
    .line 30
    goto :goto_3

    .line 31
    :cond_2
    iget-object v0, p0, LIS0/a;->o:Lkotlin/jvm/functions/Function1;

    .line 32
    .line 33
    invoke-interface {v0, p3}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    check-cast v0, Ljava/lang/Boolean;

    .line 38
    .line 39
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 40
    .line 41
    .line 42
    move-result v0

    .line 43
    if-eqz v0, :cond_4

    .line 44
    .line 45
    if-nez p2, :cond_3

    .line 46
    .line 47
    iget p2, p0, LIS0/a;->j:I

    .line 48
    .line 49
    goto :goto_1

    .line 50
    :cond_3
    iget p2, p0, LIS0/a;->k:I

    .line 51
    .line 52
    :goto_1
    iput p2, p1, Landroid/graphics/Rect;->top:I

    .line 53
    .line 54
    return-void

    .line 55
    :cond_4
    iget-object v0, p0, LIS0/a;->p:Lkotlin/jvm/functions/Function1;

    .line 56
    .line 57
    invoke-interface {v0, p3}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    check-cast v0, Ljava/lang/Boolean;

    .line 62
    .line 63
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 64
    .line 65
    .line 66
    move-result v0

    .line 67
    if-eqz v0, :cond_6

    .line 68
    .line 69
    invoke-virtual {p4}, LA4/e;->getItems()Ljava/util/List;

    .line 70
    .line 71
    .line 72
    move-result-object p3

    .line 73
    add-int/lit8 p2, p2, -0x1

    .line 74
    .line 75
    invoke-static {p3, p2}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object p2

    .line 79
    if-eqz p2, :cond_5

    .line 80
    .line 81
    iget-object p3, p0, LIS0/a;->o:Lkotlin/jvm/functions/Function1;

    .line 82
    .line 83
    invoke-interface {p3, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 84
    .line 85
    .line 86
    move-result-object p2

    .line 87
    check-cast p2, Ljava/lang/Boolean;

    .line 88
    .line 89
    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    .line 90
    .line 91
    .line 92
    move-result p2

    .line 93
    goto :goto_2

    .line 94
    :cond_5
    const/4 p2, 0x0

    .line 95
    :goto_2
    if-nez p2, :cond_7

    .line 96
    .line 97
    iget p2, p0, LIS0/a;->l:I

    .line 98
    .line 99
    iput p2, p1, Landroid/graphics/Rect;->top:I

    .line 100
    .line 101
    return-void

    .line 102
    :cond_6
    iget-object p2, p0, LIS0/a;->q:Lkotlin/jvm/functions/Function1;

    .line 103
    .line 104
    invoke-interface {p2, p3}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 105
    .line 106
    .line 107
    move-result-object p2

    .line 108
    check-cast p2, Ljava/lang/Boolean;

    .line 109
    .line 110
    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    .line 111
    .line 112
    .line 113
    move-result p2

    .line 114
    if-eqz p2, :cond_7

    .line 115
    .line 116
    iget p2, p0, LIS0/a;->m:I

    .line 117
    .line 118
    iput p2, p1, Landroid/graphics/Rect;->bottom:I

    .line 119
    .line 120
    iget p2, p0, LIS0/a;->i:I

    .line 121
    .line 122
    iput p2, p1, Landroid/graphics/Rect;->left:I

    .line 123
    .line 124
    iput p2, p1, Landroid/graphics/Rect;->right:I

    .line 125
    .line 126
    :cond_7
    :goto_3
    return-void
.end method

.method public final k(ILandroid/graphics/Canvas;Landroid/view/View;)V
    .locals 5

    .line 1
    iget-object v0, p0, LIS0/a;->r:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_1

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    move-object v2, v1

    .line 18
    check-cast v2, Lkotlin/ranges/IntRange;

    .line 19
    .line 20
    invoke-virtual {v2}, Lkotlin/ranges/c;->f()I

    .line 21
    .line 22
    .line 23
    move-result v3

    .line 24
    invoke-virtual {v2}, Lkotlin/ranges/c;->i()I

    .line 25
    .line 26
    .line 27
    move-result v4

    .line 28
    if-gt p1, v4, :cond_0

    .line 29
    .line 30
    if-gt v3, p1, :cond_0

    .line 31
    .line 32
    invoke-virtual {v2}, Lkotlin/ranges/c;->i()I

    .line 33
    .line 34
    .line 35
    move-result v3

    .line 36
    invoke-virtual {v2}, Lkotlin/ranges/c;->f()I

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    sub-int/2addr v3, v2

    .line 41
    const/4 v2, 0x1

    .line 42
    if-le v3, v2, :cond_0

    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_1
    const/4 v1, 0x0

    .line 46
    :goto_0
    check-cast v1, Lkotlin/ranges/IntRange;

    .line 47
    .line 48
    if-eqz v1, :cond_2

    .line 49
    .line 50
    iget-object v0, p0, LIS0/a;->s:Ljava/util/List;

    .line 51
    .line 52
    iget-object v2, p0, LIS0/a;->r:Ljava/util/List;

    .line 53
    .line 54
    invoke-interface {v2, v1}, Ljava/util/List;->indexOf(Ljava/lang/Object;)I

    .line 55
    .line 56
    .line 57
    move-result v1

    .line 58
    invoke-static {v0, v1}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    check-cast v0, Lkotlin/ranges/IntRange;

    .line 63
    .line 64
    if-eqz v0, :cond_2

    .line 65
    .line 66
    invoke-virtual {p0, p1, v0, p2, p3}, LIS0/a;->g(ILkotlin/ranges/IntRange;Landroid/graphics/Canvas;Landroid/view/View;)V

    .line 67
    .line 68
    .line 69
    :cond_2
    return-void
.end method

.method public onDraw(Landroid/graphics/Canvas;Landroidx/recyclerview/widget/RecyclerView;Landroidx/recyclerview/widget/RecyclerView$z;)V
    .locals 4
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroidx/recyclerview/widget/RecyclerView;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Landroidx/recyclerview/widget/RecyclerView$z;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p2}, Landroidx/core/view/ViewGroupKt;->b(Landroid/view/ViewGroup;)Lkotlin/sequences/Sequence;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    invoke-interface {p3}, Lkotlin/sequences/Sequence;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object p3

    .line 9
    :cond_0
    :goto_0
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-eqz v0, :cond_2

    .line 14
    .line 15
    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    check-cast v0, Landroid/view/View;

    .line 20
    .line 21
    invoke-virtual {p2, v0}, Landroidx/recyclerview/widget/RecyclerView;->getChildAdapterPosition(Landroid/view/View;)I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    invoke-virtual {p2}, Landroidx/recyclerview/widget/RecyclerView;->isAnimating()Z

    .line 26
    .line 27
    .line 28
    move-result v2

    .line 29
    if-eqz v2, :cond_1

    .line 30
    .line 31
    invoke-virtual {p0, v1, p1, v0}, LIS0/a;->k(ILandroid/graphics/Canvas;Landroid/view/View;)V

    .line 32
    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_1
    iget-object v2, p0, LIS0/a;->s:Ljava/util/List;

    .line 36
    .line 37
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 42
    .line 43
    .line 44
    move-result v3

    .line 45
    if-eqz v3, :cond_0

    .line 46
    .line 47
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    move-result-object v3

    .line 51
    check-cast v3, Lkotlin/ranges/IntRange;

    .line 52
    .line 53
    invoke-virtual {p0, v1, v3, p1, v0}, LIS0/a;->g(ILkotlin/ranges/IntRange;Landroid/graphics/Canvas;Landroid/view/View;)V

    .line 54
    .line 55
    .line 56
    goto :goto_1

    .line 57
    :cond_2
    return-void
.end method
