.class public final LNC0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0014\u0008\u0007\u0018\u00002\u00020\u0001BQ\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\'\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001aH\u0000\u00a2\u0006\u0004\u0008\u001d\u0010\u001eR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\u001fR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010!R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010#R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010%R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010)R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/\u00a8\u00060"
    }
    d2 = {
        "LNC0/b;",
        "LQW0/a;",
        "LQW0/c;",
        "coroutinesLib",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lf8/g;",
        "serviceGenerator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "LkC0/a;",
        "gameScreenGeneralFactory",
        "LQD0/d;",
        "putStatisticHeaderDataUseCase",
        "LDH0/a;",
        "statisticScreenFactory",
        "LSX0/a;",
        "lottieConfigurator",
        "Lc8/h;",
        "requestParamsDataSource",
        "<init>",
        "(LQW0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;LkC0/a;LQD0/d;LDH0/a;LSX0/a;Lc8/h;)V",
        "",
        "gameId",
        "LwX0/c;",
        "router",
        "",
        "sportId",
        "LNC0/a;",
        "a",
        "(Ljava/lang/String;LwX0/c;J)LNC0/a;",
        "LQW0/c;",
        "b",
        "Lorg/xbet/ui_common/utils/M;",
        "c",
        "Lf8/g;",
        "d",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "e",
        "LkC0/a;",
        "f",
        "LQD0/d;",
        "g",
        "LDH0/a;",
        "h",
        "LSX0/a;",
        "i",
        "Lc8/h;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LQW0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LkC0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:LQD0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LDH0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(LQW0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;LkC0/a;LQD0/d;LDH0/a;LSX0/a;Lc8/h;)V
    .locals 0
    .param p1    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LkC0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LQD0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LDH0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LNC0/b;->a:LQW0/c;

    .line 5
    .line 6
    iput-object p2, p0, LNC0/b;->b:Lorg/xbet/ui_common/utils/M;

    .line 7
    .line 8
    iput-object p3, p0, LNC0/b;->c:Lf8/g;

    .line 9
    .line 10
    iput-object p4, p0, LNC0/b;->d:Lorg/xbet/ui_common/utils/internet/a;

    .line 11
    .line 12
    iput-object p5, p0, LNC0/b;->e:LkC0/a;

    .line 13
    .line 14
    iput-object p6, p0, LNC0/b;->f:LQD0/d;

    .line 15
    .line 16
    iput-object p7, p0, LNC0/b;->g:LDH0/a;

    .line 17
    .line 18
    iput-object p8, p0, LNC0/b;->h:LSX0/a;

    .line 19
    .line 20
    iput-object p9, p0, LNC0/b;->i:Lc8/h;

    .line 21
    .line 22
    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/String;LwX0/c;J)LNC0/a;
    .locals 14
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, LNC0/d;->a()LNC0/a$a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, LNC0/b;->a:LQW0/c;

    .line 6
    .line 7
    iget-object v2, p0, LNC0/b;->b:Lorg/xbet/ui_common/utils/M;

    .line 8
    .line 9
    iget-object v3, p0, LNC0/b;->c:Lf8/g;

    .line 10
    .line 11
    iget-object v5, p0, LNC0/b;->d:Lorg/xbet/ui_common/utils/internet/a;

    .line 12
    .line 13
    iget-object v6, p0, LNC0/b;->e:LkC0/a;

    .line 14
    .line 15
    iget-object v7, p0, LNC0/b;->g:LDH0/a;

    .line 16
    .line 17
    iget-object v11, p0, LNC0/b;->f:LQD0/d;

    .line 18
    .line 19
    iget-object v12, p0, LNC0/b;->h:LSX0/a;

    .line 20
    .line 21
    iget-object v13, p0, LNC0/b;->i:Lc8/h;

    .line 22
    .line 23
    move-object v4, p1

    .line 24
    move-object/from16 v8, p2

    .line 25
    .line 26
    move-wide/from16 v9, p3

    .line 27
    .line 28
    invoke-interface/range {v0 .. v13}, LNC0/a$a;->a(LQW0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;LkC0/a;LDH0/a;LwX0/c;JLQD0/d;LSX0/a;Lc8/h;)LNC0/a;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    return-object p1
.end method
