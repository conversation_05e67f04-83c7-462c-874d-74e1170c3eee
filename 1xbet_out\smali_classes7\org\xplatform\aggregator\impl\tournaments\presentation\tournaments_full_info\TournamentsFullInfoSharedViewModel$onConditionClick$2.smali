.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournaments_full_info.TournamentsFullInfoSharedViewModel$onConditionClick$2"
    f = "TournamentsFullInfoSharedViewModel.kt"
    l = {
        0x2f7
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->r4(Ljava/lang/String;JLjava/util/List;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $conditionId:J

.field final synthetic $items:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lkb1/l;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic $screenName:Ljava/lang/String;

.field I$0:I

.field I$1:I

.field J$0:J

.field J$1:J

.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field L$3:Ljava/lang/Object;

.field L$4:Ljava/lang/Object;

.field L$5:Ljava/lang/Object;

.field L$6:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;


# direct methods
.method public constructor <init>(Ljava/util/List;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLjava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lkb1/l;",
            ">;",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;",
            "J",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->$items:Ljava/util/List;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    iput-wide p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->$conditionId:J

    iput-object p5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->$screenName:Ljava/lang/String;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p6}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->$items:Ljava/util/List;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->$conditionId:J

    iget-object v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->$screenName:Ljava/lang/String;

    move-object v6, p2

    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;-><init>(Ljava/util/List;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLjava/lang/String;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 33

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->label:I

    .line 8
    .line 9
    const/16 v3, 0xa

    .line 10
    .line 11
    const/4 v5, 0x1

    .line 12
    if-eqz v2, :cond_1

    .line 13
    .line 14
    if-ne v2, v5, :cond_0

    .line 15
    .line 16
    iget-wide v6, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->J$1:J

    .line 17
    .line 18
    iget v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->I$1:I

    .line 19
    .line 20
    iget v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->I$0:I

    .line 21
    .line 22
    iget-wide v9, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->J$0:J

    .line 23
    .line 24
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->L$6:Ljava/lang/Object;

    .line 25
    .line 26
    check-cast v11, Ljava/util/Collection;

    .line 27
    .line 28
    iget-object v12, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->L$5:Ljava/lang/Object;

    .line 29
    .line 30
    check-cast v12, Lkotlinx/coroutines/flow/V;

    .line 31
    .line 32
    iget-object v13, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->L$4:Ljava/lang/Object;

    .line 33
    .line 34
    check-cast v13, Lkb1/j;

    .line 35
    .line 36
    iget-object v14, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->L$3:Ljava/lang/Object;

    .line 37
    .line 38
    check-cast v14, Ljava/util/Iterator;

    .line 39
    .line 40
    iget-object v15, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->L$2:Ljava/lang/Object;

    .line 41
    .line 42
    check-cast v15, Ljava/util/Collection;

    .line 43
    .line 44
    const/16 v16, 0x1

    .line 45
    .line 46
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->L$1:Ljava/lang/Object;

    .line 47
    .line 48
    check-cast v5, Ljava/lang/String;

    .line 49
    .line 50
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->L$0:Ljava/lang/Object;

    .line 51
    .line 52
    check-cast v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 53
    .line 54
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    move-wide/from16 v19, v6

    .line 58
    .line 59
    move-object/from16 v18, v13

    .line 60
    .line 61
    const/4 v3, 0x1

    .line 62
    move-object v6, v5

    .line 63
    move-object v5, v4

    .line 64
    move-object/from16 v4, p1

    .line 65
    .line 66
    goto/16 :goto_3

    .line 67
    .line 68
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 69
    .line 70
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 71
    .line 72
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 73
    .line 74
    .line 75
    throw v1

    .line 76
    :cond_1
    const/16 v16, 0x1

    .line 77
    .line 78
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 79
    .line 80
    .line 81
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->$items:Ljava/util/List;

    .line 82
    .line 83
    invoke-interface {v2}, Ljava/util/Collection;->isEmpty()Z

    .line 84
    .line 85
    .line 86
    move-result v2

    .line 87
    if-nez v2, :cond_a

    .line 88
    .line 89
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 90
    .line 91
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->x3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lkotlinx/coroutines/flow/V;

    .line 92
    .line 93
    .line 94
    move-result-object v2

    .line 95
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->$items:Ljava/util/List;

    .line 96
    .line 97
    iget-wide v5, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->$conditionId:J

    .line 98
    .line 99
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 100
    .line 101
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->$screenName:Ljava/lang/String;

    .line 102
    .line 103
    new-instance v9, Ljava/util/ArrayList;

    .line 104
    .line 105
    invoke-static {v4, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 106
    .line 107
    .line 108
    move-result v10

    .line 109
    invoke-direct {v9, v10}, Ljava/util/ArrayList;-><init>(I)V

    .line 110
    .line 111
    .line 112
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 113
    .line 114
    .line 115
    move-result-object v4

    .line 116
    move-object v14, v2

    .line 117
    move-object v15, v4

    .line 118
    move-wide v12, v5

    .line 119
    move-object v4, v7

    .line 120
    move-object v6, v8

    .line 121
    move-object v2, v9

    .line 122
    :goto_0
    invoke-interface {v15}, Ljava/util/Iterator;->hasNext()Z

    .line 123
    .line 124
    .line 125
    move-result v5

    .line 126
    if-eqz v5, :cond_9

    .line 127
    .line 128
    invoke-interface {v15}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 129
    .line 130
    .line 131
    move-result-object v5

    .line 132
    check-cast v5, Lkb1/l;

    .line 133
    .line 134
    invoke-interface {v5}, Lkb1/l;->getId()J

    .line 135
    .line 136
    .line 137
    move-result-wide v7

    .line 138
    cmp-long v9, v7, v12

    .line 139
    .line 140
    if-nez v9, :cond_8

    .line 141
    .line 142
    instance-of v7, v5, Lkb1/j;

    .line 143
    .line 144
    if-eqz v7, :cond_8

    .line 145
    .line 146
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->v3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/analytics/domain/scope/g;

    .line 147
    .line 148
    .line 149
    move-result-object v17

    .line 150
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->R3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)J

    .line 151
    .line 152
    .line 153
    move-result-wide v18

    .line 154
    check-cast v5, Lkb1/j;

    .line 155
    .line 156
    invoke-virtual {v5}, Lkb1/j;->c()Z

    .line 157
    .line 158
    .line 159
    move-result v7

    .line 160
    xor-int/lit8 v20, v7, 0x1

    .line 161
    .line 162
    invoke-virtual {v5}, Lkb1/j;->d()Z

    .line 163
    .line 164
    .line 165
    move-result v7

    .line 166
    const-wide/16 v8, 0x0

    .line 167
    .line 168
    if-eqz v7, :cond_2

    .line 169
    .line 170
    invoke-virtual {v5}, Lkb1/j;->getId()J

    .line 171
    .line 172
    .line 173
    move-result-wide v10

    .line 174
    move-wide/from16 v21, v10

    .line 175
    .line 176
    goto :goto_1

    .line 177
    :cond_2
    move-wide/from16 v21, v8

    .line 178
    .line 179
    :goto_1
    invoke-virtual/range {v17 .. v22}, Lorg/xbet/analytics/domain/scope/g;->n(JZJ)V

    .line 180
    .line 181
    .line 182
    move-object v7, v5

    .line 183
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->u3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LnR/d;

    .line 184
    .line 185
    .line 186
    move-result-object v5

    .line 187
    move-wide v10, v8

    .line 188
    move-object v9, v7

    .line 189
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->R3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)J

    .line 190
    .line 191
    .line 192
    move-result-wide v7

    .line 193
    invoke-virtual {v9}, Lkb1/j;->d()Z

    .line 194
    .line 195
    .line 196
    move-result v17

    .line 197
    if-eqz v17, :cond_3

    .line 198
    .line 199
    invoke-virtual {v9}, Lkb1/j;->getId()J

    .line 200
    .line 201
    .line 202
    move-result-wide v17

    .line 203
    goto :goto_2

    .line 204
    :cond_3
    move-wide/from16 v17, v10

    .line 205
    .line 206
    :goto_2
    invoke-virtual {v9}, Lkb1/j;->c()Z

    .line 207
    .line 208
    .line 209
    move-result v19

    .line 210
    xor-int/lit8 v19, v19, 0x1

    .line 211
    .line 212
    move-object/from16 p1, v9

    .line 213
    .line 214
    move-wide/from16 v9, v17

    .line 215
    .line 216
    move/from16 v11, v19

    .line 217
    .line 218
    invoke-interface/range {v5 .. v11}, LnR/d;->d(Ljava/lang/String;JJZ)V

    .line 219
    .line 220
    .line 221
    invoke-virtual/range {p1 .. p1}, Lkb1/j;->c()Z

    .line 222
    .line 223
    .line 224
    move-result v5

    .line 225
    xor-int/lit8 v8, v5, 0x1

    .line 226
    .line 227
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->H3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lw81/e;

    .line 228
    .line 229
    .line 230
    move-result-object v5

    .line 231
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->R3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)J

    .line 232
    .line 233
    .line 234
    move-result-wide v9

    .line 235
    invoke-static {v12, v13}, LHc/a;->f(J)Ljava/lang/Long;

    .line 236
    .line 237
    .line 238
    move-result-object v7

    .line 239
    iput-object v4, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->L$0:Ljava/lang/Object;

    .line 240
    .line 241
    iput-object v6, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->L$1:Ljava/lang/Object;

    .line 242
    .line 243
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->L$2:Ljava/lang/Object;

    .line 244
    .line 245
    iput-object v15, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->L$3:Ljava/lang/Object;

    .line 246
    .line 247
    move-object/from16 v11, p1

    .line 248
    .line 249
    iput-object v11, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->L$4:Ljava/lang/Object;

    .line 250
    .line 251
    iput-object v14, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->L$5:Ljava/lang/Object;

    .line 252
    .line 253
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->L$6:Ljava/lang/Object;

    .line 254
    .line 255
    iput-wide v12, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->J$0:J

    .line 256
    .line 257
    iput v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->I$0:I

    .line 258
    .line 259
    const/4 v3, 0x0

    .line 260
    iput v3, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->I$1:I

    .line 261
    .line 262
    move-object/from16 p1, v4

    .line 263
    .line 264
    const-wide/16 v3, 0x0

    .line 265
    .line 266
    iput-wide v3, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->J$1:J

    .line 267
    .line 268
    const/4 v3, 0x1

    .line 269
    iput v3, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onConditionClick$2;->label:I

    .line 270
    .line 271
    invoke-interface {v5, v9, v10, v7, v0}, Lw81/e;->a(JLjava/lang/Long;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 272
    .line 273
    .line 274
    move-result-object v4

    .line 275
    if-ne v4, v1, :cond_4

    .line 276
    .line 277
    return-object v1

    .line 278
    :cond_4
    move-object/from16 v5, p1

    .line 279
    .line 280
    move-object/from16 v18, v11

    .line 281
    .line 282
    move-wide v9, v12

    .line 283
    move-object v12, v14

    .line 284
    move-object v14, v15

    .line 285
    const-wide/16 v19, 0x0

    .line 286
    .line 287
    move-object v11, v2

    .line 288
    move-object v15, v11

    .line 289
    const/4 v2, 0x0

    .line 290
    :goto_3
    check-cast v4, Ljava/lang/Iterable;

    .line 291
    .line 292
    new-instance v7, Ljava/util/ArrayList;

    .line 293
    .line 294
    const/16 v13, 0xa

    .line 295
    .line 296
    invoke-static {v4, v13}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 297
    .line 298
    .line 299
    move-result v3

    .line 300
    invoke-direct {v7, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 301
    .line 302
    .line 303
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 304
    .line 305
    .line 306
    move-result-object v3

    .line 307
    :goto_4
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 308
    .line 309
    .line 310
    move-result v4

    .line 311
    if-eqz v4, :cond_5

    .line 312
    .line 313
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 314
    .line 315
    .line 316
    move-result-object v4

    .line 317
    check-cast v4, Lorg/xplatform/aggregator/api/model/Game;

    .line 318
    .line 319
    invoke-virtual {v4}, Lorg/xplatform/aggregator/api/model/Game;->getName()Ljava/lang/String;

    .line 320
    .line 321
    .line 322
    move-result-object v4

    .line 323
    invoke-interface {v7, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 324
    .line 325
    .line 326
    goto :goto_4

    .line 327
    :cond_5
    if-eqz v2, :cond_6

    .line 328
    .line 329
    const/16 v21, 0x1

    .line 330
    .line 331
    goto :goto_5

    .line 332
    :cond_6
    const/16 v21, 0x0

    .line 333
    .line 334
    :goto_5
    if-eqz v8, :cond_7

    .line 335
    .line 336
    const/16 v22, 0x1

    .line 337
    .line 338
    goto :goto_6

    .line 339
    :cond_7
    const/16 v22, 0x0

    .line 340
    .line 341
    :goto_6
    const/16 v31, 0x6fb

    .line 342
    .line 343
    const/16 v32, 0x0

    .line 344
    .line 345
    const/16 v23, 0x0

    .line 346
    .line 347
    const/16 v24, 0x0

    .line 348
    .line 349
    const/16 v25, 0x0

    .line 350
    .line 351
    const/16 v26, 0x0

    .line 352
    .line 353
    const/16 v27, 0x0

    .line 354
    .line 355
    const/16 v29, 0x0

    .line 356
    .line 357
    const/16 v30, 0x0

    .line 358
    .line 359
    move-object/from16 v28, v7

    .line 360
    .line 361
    invoke-static/range {v18 .. v32}, Lkb1/j;->b(Lkb1/j;JZZLjava/lang/String;Ljava/lang/String;Ljava/util/Date;Ljava/util/Date;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lkb1/j;

    .line 362
    .line 363
    .line 364
    move-result-object v2

    .line 365
    move-object v4, v5

    .line 366
    move-object v3, v14

    .line 367
    const/16 v17, 0xa

    .line 368
    .line 369
    move-object v5, v2

    .line 370
    move-object v2, v11

    .line 371
    move-object v14, v12

    .line 372
    move-wide v12, v9

    .line 373
    goto :goto_7

    .line 374
    :cond_8
    move-object/from16 p1, v4

    .line 375
    .line 376
    const/16 v17, 0xa

    .line 377
    .line 378
    move-object/from16 v4, p1

    .line 379
    .line 380
    move-object v3, v15

    .line 381
    move-object v15, v2

    .line 382
    :goto_7
    invoke-interface {v2, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 383
    .line 384
    .line 385
    move-object v2, v15

    .line 386
    const/16 v16, 0x1

    .line 387
    .line 388
    move-object v15, v3

    .line 389
    const/16 v3, 0xa

    .line 390
    .line 391
    goto/16 :goto_0

    .line 392
    .line 393
    :cond_9
    check-cast v2, Ljava/util/List;

    .line 394
    .line 395
    new-instance v1, Lkb1/k$a;

    .line 396
    .line 397
    invoke-direct {v1, v2}, Lkb1/k$a;-><init>(Ljava/util/List;)V

    .line 398
    .line 399
    .line 400
    invoke-interface {v14, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 401
    .line 402
    .line 403
    :cond_a
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 404
    .line 405
    return-object v1
.end method
