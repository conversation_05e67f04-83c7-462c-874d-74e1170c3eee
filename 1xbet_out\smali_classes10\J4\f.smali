.class public abstract LJ4/f;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<TResult:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(LJ4/c;)LJ4/f;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LJ4/c<",
            "TTResult;>;)",
            "LJ4/f<",
            "TTResult;>;"
        }
    .end annotation

    .line 1
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    .line 2
    .line 3
    const-string v0, "addOnCompleteListener is not implemented"

    .line 4
    .line 5
    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    throw p1
.end method

.method public abstract b(LJ4/d;)LJ4/f;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LJ4/d;",
            ")",
            "LJ4/f<",
            "TTResult;>;"
        }
    .end annotation
.end method

.method public abstract c(LJ4/e;)LJ4/f;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LJ4/e<",
            "TTResult;>;)",
            "LJ4/f<",
            "TTResult;>;"
        }
    .end annotation
.end method

.method public abstract d()Ljava/lang/Exception;
.end method

.method public abstract e()Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TTResult;"
        }
    .end annotation
.end method

.method public abstract f()Z
.end method

.method public abstract g()Z
.end method

.method public abstract h()Z
.end method
