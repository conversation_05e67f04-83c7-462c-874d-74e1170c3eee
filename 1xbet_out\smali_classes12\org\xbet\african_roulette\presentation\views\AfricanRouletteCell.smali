.class public final Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell$a;,
        Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0008\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u0000 \u001d2\u00020\u0001:\u0001#B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0017\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008H\u0000\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ#\u0010\u0011\u001a\u00020\n2\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\n0\rH\u0000\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0017\u0010\u0014\u001a\u00020\n2\u0006\u0010\u0013\u001a\u00020\u0012H\u0000\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0017\u0010\u0017\u001a\u00020\n2\u0006\u0010\u0016\u001a\u00020\u0012H\u0000\u00a2\u0006\u0004\u0008\u0017\u0010\u0015J\u000f\u0010\u001a\u001a\u00020\u0008H\u0000\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0017\u0010\u001d\u001a\u00020\u00082\u0006\u0010\u001c\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0017\u0010 \u001a\u00020\u001f2\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008 \u0010!R\u0014\u0010%\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010$R\"\u0010(\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\n0\r8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'\u00a8\u0006)"
    }
    d2 = {
        "Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
        "cellType",
        "",
        "e",
        "(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V",
        "Lkotlin/Function1;",
        "action",
        "setCellClickListener$african_roulette_release",
        "(Lkotlin/jvm/functions/Function1;)V",
        "setCellClickListener",
        "",
        "fullOpacity",
        "i",
        "(Z)V",
        "show",
        "h",
        "getBetType$african_roulette_release",
        "()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
        "getBetType",
        "",
        "text",
        "c",
        "(Ljava/lang/String;)Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
        "",
        "d",
        "(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)I",
        "Lgg/a;",
        "a",
        "Lgg/a;",
        "viewBinding",
        "b",
        "Lkotlin/jvm/functions/Function1;",
        "onCellClick",
        "african_roulette_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final c:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lgg/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->c:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell$a;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroid/util/AttributeSet;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 2
    .line 3
    .line 4
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    const/4 p2, 0x1

    .line 9
    invoke-static {p1, p0, p2}, Lgg/a;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)Lgg/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->a:Lgg/a;

    .line 14
    .line 15
    new-instance p1, Lkg/a;

    .line 16
    .line 17
    invoke-direct {p1}, Lkg/a;-><init>()V

    .line 18
    .line 19
    .line 20
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->b:Lkotlin/jvm/functions/Function1;

    .line 21
    .line 22
    new-instance p1, Landroid/view/ViewGroup$LayoutParams;

    .line 23
    .line 24
    const/4 p2, -0x1

    .line 25
    invoke-direct {p1, p2, p2}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public static synthetic a(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->g(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->f(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;Landroid/view/View;)V

    return-void
.end method

.method public static final f(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;Landroid/view/View;)V
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->b:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public static final g(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final c(Ljava/lang/String;)Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
    .locals 3

    .line 1
    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    packed-switch v0, :pswitch_data_0

    .line 6
    .line 7
    .line 8
    packed-switch v0, :pswitch_data_1

    .line 9
    .line 10
    .line 11
    sparse-switch v0, :sswitch_data_0

    .line 12
    .line 13
    .line 14
    goto/16 :goto_0

    .line 15
    .line 16
    :sswitch_0
    const-string v0, "BLACK"

    .line 17
    .line 18
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 19
    .line 20
    .line 21
    move-result v0

    .line 22
    if-eqz v0, :cond_0

    .line 23
    .line 24
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->BLACK:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 25
    .line 26
    return-object p1

    .line 27
    :sswitch_1
    const-string v0, "7-12"

    .line 28
    .line 29
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    if-eqz v0, :cond_0

    .line 34
    .line 35
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->LAST_HALF:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 36
    .line 37
    return-object p1

    .line 38
    :sswitch_2
    const-string v0, "RED"

    .line 39
    .line 40
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 41
    .line 42
    .line 43
    move-result v0

    .line 44
    if-eqz v0, :cond_0

    .line 45
    .line 46
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->RED:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 47
    .line 48
    return-object p1

    .line 49
    :sswitch_3
    const-string v0, "MID"

    .line 50
    .line 51
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 52
    .line 53
    .line 54
    move-result v0

    .line 55
    if-eqz v0, :cond_0

    .line 56
    .line 57
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->MIDDLE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 58
    .line 59
    return-object p1

    .line 60
    :sswitch_4
    const-string v0, "1-6"

    .line 61
    .line 62
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 63
    .line 64
    .line 65
    move-result v0

    .line 66
    if-eqz v0, :cond_0

    .line 67
    .line 68
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->FIRST_HALF:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 69
    .line 70
    return-object p1

    .line 71
    :sswitch_5
    const-string v0, "LO"

    .line 72
    .line 73
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 74
    .line 75
    .line 76
    move-result v0

    .line 77
    if-eqz v0, :cond_0

    .line 78
    .line 79
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->LOW:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 80
    .line 81
    return-object p1

    .line 82
    :sswitch_6
    const-string v0, "HI"

    .line 83
    .line 84
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 85
    .line 86
    .line 87
    move-result v0

    .line 88
    if-eqz v0, :cond_0

    .line 89
    .line 90
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->HIGH:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 91
    .line 92
    return-object p1

    .line 93
    :pswitch_0
    const-string v0, "12"

    .line 94
    .line 95
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 96
    .line 97
    .line 98
    move-result v0

    .line 99
    if-eqz v0, :cond_0

    .line 100
    .line 101
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->TWELVE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 102
    .line 103
    return-object p1

    .line 104
    :pswitch_1
    const-string v0, "11"

    .line 105
    .line 106
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 107
    .line 108
    .line 109
    move-result v0

    .line 110
    if-eqz v0, :cond_0

    .line 111
    .line 112
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->ELEVEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 113
    .line 114
    return-object p1

    .line 115
    :pswitch_2
    const-string v0, "10"

    .line 116
    .line 117
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 118
    .line 119
    .line 120
    move-result v0

    .line 121
    if-eqz v0, :cond_0

    .line 122
    .line 123
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->TEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 124
    .line 125
    return-object p1

    .line 126
    :pswitch_3
    const-string v0, "9"

    .line 127
    .line 128
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 129
    .line 130
    .line 131
    move-result v0

    .line 132
    if-eqz v0, :cond_0

    .line 133
    .line 134
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->NINE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 135
    .line 136
    return-object p1

    .line 137
    :pswitch_4
    const-string v0, "8"

    .line 138
    .line 139
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 140
    .line 141
    .line 142
    move-result v0

    .line 143
    if-eqz v0, :cond_0

    .line 144
    .line 145
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->EIGHT:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 146
    .line 147
    return-object p1

    .line 148
    :pswitch_5
    const-string v0, "7"

    .line 149
    .line 150
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 151
    .line 152
    .line 153
    move-result v0

    .line 154
    if-eqz v0, :cond_0

    .line 155
    .line 156
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->SEVEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 157
    .line 158
    return-object p1

    .line 159
    :pswitch_6
    const-string v0, "6"

    .line 160
    .line 161
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 162
    .line 163
    .line 164
    move-result v0

    .line 165
    if-eqz v0, :cond_0

    .line 166
    .line 167
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->SIX:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 168
    .line 169
    return-object p1

    .line 170
    :pswitch_7
    const-string v0, "5"

    .line 171
    .line 172
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 173
    .line 174
    .line 175
    move-result v0

    .line 176
    if-eqz v0, :cond_0

    .line 177
    .line 178
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->FIVE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 179
    .line 180
    return-object p1

    .line 181
    :pswitch_8
    const-string v0, "4"

    .line 182
    .line 183
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 184
    .line 185
    .line 186
    move-result v0

    .line 187
    if-eqz v0, :cond_0

    .line 188
    .line 189
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->FOUR:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 190
    .line 191
    return-object p1

    .line 192
    :pswitch_9
    const-string v0, "3"

    .line 193
    .line 194
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 195
    .line 196
    .line 197
    move-result v0

    .line 198
    if-eqz v0, :cond_0

    .line 199
    .line 200
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->THREE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 201
    .line 202
    return-object p1

    .line 203
    :pswitch_a
    const-string v0, "2"

    .line 204
    .line 205
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 206
    .line 207
    .line 208
    move-result v0

    .line 209
    if-eqz v0, :cond_0

    .line 210
    .line 211
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->TWO:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 212
    .line 213
    return-object p1

    .line 214
    :pswitch_b
    const-string v0, "1"

    .line 215
    .line 216
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 217
    .line 218
    .line 219
    move-result v0

    .line 220
    if-eqz v0, :cond_0

    .line 221
    .line 222
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->ONE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 223
    .line 224
    return-object p1

    .line 225
    :pswitch_c
    const-string v0, "0"

    .line 226
    .line 227
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 228
    .line 229
    .line 230
    move-result v0

    .line 231
    if-eqz v0, :cond_0

    .line 232
    .line 233
    sget-object p1, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->ZERO:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 234
    .line 235
    return-object p1

    .line 236
    :cond_0
    :goto_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 237
    .line 238
    new-instance v1, Ljava/lang/StringBuilder;

    .line 239
    .line 240
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 241
    .line 242
    .line 243
    const-string v2, "Unknown bet type: "

    .line 244
    .line 245
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 246
    .line 247
    .line 248
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 249
    .line 250
    .line 251
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 252
    .line 253
    .line 254
    move-result-object p1

    .line 255
    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 256
    .line 257
    .line 258
    throw v0

    .line 259
    :pswitch_data_0
    .packed-switch 0x30
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
    .end packed-switch

    .line 260
    .line 261
    .line 262
    .line 263
    .line 264
    .line 265
    .line 266
    .line 267
    .line 268
    .line 269
    .line 270
    .line 271
    .line 272
    .line 273
    .line 274
    .line 275
    .line 276
    .line 277
    .line 278
    .line 279
    .line 280
    .line 281
    .line 282
    .line 283
    :pswitch_data_1
    .packed-switch 0x61f
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch

    .line 284
    .line 285
    .line 286
    .line 287
    .line 288
    .line 289
    .line 290
    .line 291
    .line 292
    .line 293
    :sswitch_data_0
    .sparse-switch
        0x901 -> :sswitch_6
        0x983 -> :sswitch_5
        0xbd9a -> :sswitch_4
        0x12a28 -> :sswitch_3
        0x13c71 -> :sswitch_2
        0x19af77 -> :sswitch_1
        0x3c597df -> :sswitch_0
    .end sparse-switch
.end method

.method public final d(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)I
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell$b;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    packed-switch p1, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    sget p1, Lag/a;->cell_black:I

    .line 13
    .line 14
    return p1

    .line 15
    :pswitch_0
    sget p1, Lag/a;->cell_black_big:I

    .line 16
    .line 17
    return p1

    .line 18
    :pswitch_1
    sget p1, Lag/a;->cell_red_big:I

    .line 19
    .line 20
    return p1

    .line 21
    :pswitch_2
    sget p1, Lag/a;->cell_red:I

    .line 22
    .line 23
    return p1

    .line 24
    :pswitch_3
    sget p1, Lag/a;->cell_empty_big:I

    .line 25
    .line 26
    return p1

    .line 27
    :pswitch_4
    sget p1, Lag/a;->cell_empty:I

    .line 28
    .line 29
    return p1

    .line 30
    :pswitch_5
    sget p1, Lag/a;->cell_green:I

    .line 31
    .line 32
    return p1

    .line 33
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_5
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_3
        :pswitch_3
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V
    .locals 2
    .param p1    # Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->a:Lgg/a;

    .line 2
    .line 3
    iget-object v0, v0, Lgg/a;->c:Landroid/widget/TextView;

    .line 4
    .line 5
    invoke-virtual {p1}, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->getBetText()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->a:Lgg/a;

    .line 13
    .line 14
    invoke-virtual {v0}, Lgg/a;->b()Landroid/widget/FrameLayout;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {p0, p1}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->d(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)I

    .line 19
    .line 20
    .line 21
    move-result v1

    .line 22
    invoke-virtual {v0, v1}, Landroid/view/View;->setBackgroundResource(I)V

    .line 23
    .line 24
    .line 25
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->a:Lgg/a;

    .line 26
    .line 27
    invoke-virtual {v0}, Lgg/a;->b()Landroid/widget/FrameLayout;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    new-instance v1, Lkg/b;

    .line 32
    .line 33
    invoke-direct {v1, p0, p1}, Lkg/b;-><init>(Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method public final getBetType$african_roulette_release()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->a:Lgg/a;

    .line 2
    .line 3
    iget-object v0, v0, Lgg/a;->c:Landroid/widget/TextView;

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/widget/TextView;->getText()Ljava/lang/CharSequence;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {p0, v0}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->c(Ljava/lang/String;)Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    return-object v0
.end method

.method public final h(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->a:Lgg/a;

    .line 2
    .line 3
    iget-object v0, v0, Lgg/a;->b:Landroid/widget/ImageView;

    .line 4
    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    const/4 p1, 0x0

    .line 8
    goto :goto_0

    .line 9
    :cond_0
    const/16 p1, 0x8

    .line 10
    .line 11
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final i(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->a:Lgg/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lgg/a;->b()Landroid/widget/FrameLayout;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    const/high16 p1, 0x3f800000    # 1.0f

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const p1, 0x3e99999a

    .line 13
    .line 14
    .line 15
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setAlpha(F)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public final setCellClickListener$african_roulette_release(Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .param p1    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->b:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    return-void
.end method
