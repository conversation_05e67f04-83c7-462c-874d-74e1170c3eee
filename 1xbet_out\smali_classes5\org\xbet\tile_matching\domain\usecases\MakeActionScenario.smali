.class public final Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0006\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J(\u0010\u000e\u001a\u00020\r2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\n\u001a\u00020\u00082\u0006\u0010\u000c\u001a\u00020\u000bH\u0080B\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u0010R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0012\u00a8\u0006\u0013"
    }
    d2 = {
        "Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;",
        "",
        "Lorg/xbet/core/domain/usecases/balance/c;",
        "getActiveBalanceUseCase",
        "LAT0/a;",
        "tileMatchingRepository",
        "<init>",
        "(Lorg/xbet/core/domain/usecases/balance/c;LAT0/a;)V",
        "",
        "row",
        "column",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
        "gameType",
        "",
        "a",
        "(IILcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xbet/core/domain/usecases/balance/c;",
        "b",
        "LAT0/a;",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/core/domain/usecases/balance/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LAT0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/core/domain/usecases/balance/c;LAT0/a;)V
    .locals 0
    .param p1    # Lorg/xbet/core/domain/usecases/balance/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LAT0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;->a:Lorg/xbet/core/domain/usecases/balance/c;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;->b:LAT0/a;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a(IILcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .param p3    # Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p4, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario$invoke$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p4

    .line 6
    check-cast v0, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario$invoke$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario$invoke$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v8, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario$invoke$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p4}, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario$invoke$1;-><init>(Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p4, v8, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario$invoke$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v8, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario$invoke$1;->label:I

    .line 34
    .line 35
    const/4 v2, 0x1

    .line 36
    if-eqz v1, :cond_2

    .line 37
    .line 38
    if-ne v1, v2, :cond_1

    .line 39
    .line 40
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    goto :goto_2

    .line 44
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 45
    .line 46
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 47
    .line 48
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 49
    .line 50
    .line 51
    throw p1

    .line 52
    :cond_2
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    iget-object v1, p0, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;->b:LAT0/a;

    .line 56
    .line 57
    iget-object p4, p0, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;->a:Lorg/xbet/core/domain/usecases/balance/c;

    .line 58
    .line 59
    invoke-virtual {p4}, Lorg/xbet/core/domain/usecases/balance/c;->a()Lorg/xbet/balance/model/BalanceModel;

    .line 60
    .line 61
    .line 62
    move-result-object p4

    .line 63
    if-eqz p4, :cond_4

    .line 64
    .line 65
    invoke-virtual {p4}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 66
    .line 67
    .line 68
    move-result-wide v3

    .line 69
    iget-object p4, p0, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;->b:LAT0/a;

    .line 70
    .line 71
    invoke-interface {p4}, LAT0/a;->c()LzT0/e;

    .line 72
    .line 73
    .line 74
    move-result-object p4

    .line 75
    invoke-virtual {p4}, LzT0/e;->b()I

    .line 76
    .line 77
    .line 78
    move-result p4

    .line 79
    iput v2, v8, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario$invoke$1;->label:I

    .line 80
    .line 81
    move v5, p1

    .line 82
    move v6, p2

    .line 83
    move-object v7, p3

    .line 84
    move-wide v2, v3

    .line 85
    move v4, p4

    .line 86
    invoke-interface/range {v1 .. v8}, LAT0/a;->b(JIIILcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object p4

    .line 90
    if-ne p4, v0, :cond_3

    .line 91
    .line 92
    return-object v0

    .line 93
    :cond_3
    :goto_2
    check-cast p4, LzT0/e;

    .line 94
    .line 95
    iget-object p1, p0, Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;->b:LAT0/a;

    .line 96
    .line 97
    invoke-interface {p1, p4}, LAT0/a;->i(LzT0/e;)V

    .line 98
    .line 99
    .line 100
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 101
    .line 102
    return-object p1

    .line 103
    :cond_4
    new-instance p1, Lcom/xbet/onexuser/domain/exceptions/BalanceNotExistException;

    .line 104
    .line 105
    const-wide/16 p2, -0x1

    .line 106
    .line 107
    invoke-direct {p1, p2, p3}, Lcom/xbet/onexuser/domain/exceptions/BalanceNotExistException;-><init>(J)V

    .line 108
    .line 109
    .line 110
    throw p1
.end method
