.class public final LU61/a;
.super LwX0/B;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0005\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0017\u0010\t\u001a\u00020\u00082\u0006\u0010\u0007\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000c\u001a\u00020\u000bH\u0016\u00a2\u0006\u0004\u0008\u000c\u0010\rR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u000f\u00a8\u0006\u0010"
    }
    d2 = {
        "LU61/a;",
        "LwX0/B;",
        "LT61/a;",
        "contentModel",
        "<init>",
        "(LT61/a;)V",
        "Landroidx/fragment/app/u;",
        "factory",
        "Landroidx/fragment/app/Fragment;",
        "createFragment",
        "(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;",
        "",
        "needAuth",
        "()Z",
        "a",
        "LT61/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LT61/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LT61/a;)V
    .locals 0
    .param p1    # LT61/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, LwX0/B;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LU61/a;->a:LT61/a;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public createFragment(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;
    .locals 1
    .param p1    # Landroidx/fragment/app/u;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object p1, Lorg/xbet/web_rules/impl/presentation/WebRulesFragment;->n0:Lorg/xbet/web_rules/impl/presentation/WebRulesFragment$a;

    .line 2
    .line 3
    iget-object v0, p0, LU61/a;->a:LT61/a;

    .line 4
    .line 5
    invoke-virtual {p1, v0}, Lorg/xbet/web_rules/impl/presentation/WebRulesFragment$a;->a(LT61/a;)Lorg/xbet/web_rules/impl/presentation/WebRulesFragment;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    return-object p1
.end method

.method public needAuth()Z
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    return v0
.end method
