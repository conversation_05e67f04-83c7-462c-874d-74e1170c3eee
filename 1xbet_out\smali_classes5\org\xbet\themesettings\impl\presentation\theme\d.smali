.class public final synthetic Lorg/xbet/themesettings/impl/presentation/theme/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/themesettings/impl/presentation/theme/d;->a:Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/d;->a:Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, p1}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->D2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
