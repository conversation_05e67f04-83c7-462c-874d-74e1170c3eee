.class public interface abstract Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$a;,
        Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$b;,
        Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$c;,
        Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$d;,
        Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$e;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0008v\u0018\u00002\u00020\u0001:\u0005\u0002\u0003\u0004\u0005\u0006\u0082\u0001\u0005\u0007\u0008\t\n\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
        "",
        "b",
        "a",
        "d",
        "c",
        "e",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$a;",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$b;",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$c;",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$d;",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b$e;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation
