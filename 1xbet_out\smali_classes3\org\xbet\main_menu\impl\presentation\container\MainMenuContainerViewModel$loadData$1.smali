.class final Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.main_menu.impl.presentation.container.MainMenuContainerViewModel$loadData$1"
    f = "MainMenuContainerViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->u4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ln9/b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Ln9/b;",
        "loginState",
        "",
        "<anonymous>",
        "(Ln9/b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;

    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ln9/b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->invoke(Ln9/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ln9/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ln9/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 23

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    iget v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->label:I

    .line 7
    .line 8
    if-nez v1, :cond_7

    .line 9
    .line 10
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->L$0:Ljava/lang/Object;

    .line 14
    .line 15
    check-cast v1, Ln9/b;

    .line 16
    .line 17
    iget-object v2, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 18
    .line 19
    invoke-virtual {v1}, Ln9/b;->c()Z

    .line 20
    .line 21
    .line 22
    move-result v3

    .line 23
    invoke-static {v2, v3}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->f4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Z)V

    .line 24
    .line 25
    .line 26
    iget-object v2, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 27
    .line 28
    invoke-static {v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->I3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lek0/o;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    invoke-virtual {v2}, Lek0/o;->A1()Z

    .line 33
    .line 34
    .line 35
    move-result v2

    .line 36
    const/4 v3, 0x0

    .line 37
    const/4 v4, 0x1

    .line 38
    if-eqz v2, :cond_0

    .line 39
    .line 40
    invoke-virtual {v1}, Ln9/b;->c()Z

    .line 41
    .line 42
    .line 43
    move-result v2

    .line 44
    if-eqz v2, :cond_0

    .line 45
    .line 46
    const/4 v10, 0x1

    .line 47
    goto :goto_0

    .line 48
    :cond_0
    const/4 v10, 0x0

    .line 49
    :goto_0
    iget-object v2, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 50
    .line 51
    invoke-static {v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->Q3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/flow/V;

    .line 52
    .line 53
    .line 54
    move-result-object v2

    .line 55
    iget-object v5, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 56
    .line 57
    :goto_1
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v6

    .line 61
    move-object v7, v5

    .line 62
    move-object v5, v6

    .line 63
    check-cast v5, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;

    .line 64
    .line 65
    invoke-static {v7}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->t3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Z

    .line 66
    .line 67
    .line 68
    move-result v8

    .line 69
    if-eqz v8, :cond_1

    .line 70
    .line 71
    invoke-virtual {v1}, Ln9/b;->c()Z

    .line 72
    .line 73
    .line 74
    move-result v8

    .line 75
    if-eqz v8, :cond_1

    .line 76
    .line 77
    const/4 v12, 0x1

    .line 78
    goto :goto_2

    .line 79
    :cond_1
    const/4 v12, 0x0

    .line 80
    :goto_2
    invoke-virtual {v1}, Ln9/b;->c()Z

    .line 81
    .line 82
    .line 83
    move-result v8

    .line 84
    if-eqz v8, :cond_2

    .line 85
    .line 86
    invoke-static {v7}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->W3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lorg/xbet/remoteconfig/domain/usecases/k;

    .line 87
    .line 88
    .line 89
    move-result-object v8

    .line 90
    invoke-interface {v8}, Lorg/xbet/remoteconfig/domain/usecases/k;->invoke()Z

    .line 91
    .line 92
    .line 93
    move-result v8

    .line 94
    if-nez v8, :cond_2

    .line 95
    .line 96
    const/4 v11, 0x1

    .line 97
    goto :goto_3

    .line 98
    :cond_2
    const/4 v11, 0x0

    .line 99
    :goto_3
    invoke-virtual {v1}, Ln9/b;->c()Z

    .line 100
    .line 101
    .line 102
    move-result v8

    .line 103
    xor-int/2addr v8, v4

    .line 104
    invoke-virtual {v1}, Ln9/b;->c()Z

    .line 105
    .line 106
    .line 107
    move-result v9

    .line 108
    const/16 v19, 0x1f83

    .line 109
    .line 110
    const/16 v20, 0x0

    .line 111
    .line 112
    move-object v13, v6

    .line 113
    const/4 v6, 0x0

    .line 114
    move-object v14, v7

    .line 115
    const/4 v7, 0x0

    .line 116
    move-object v15, v13

    .line 117
    const/4 v13, 0x0

    .line 118
    move-object/from16 v16, v14

    .line 119
    .line 120
    const/4 v14, 0x0

    .line 121
    move-object/from16 v17, v15

    .line 122
    .line 123
    const/4 v15, 0x0

    .line 124
    move-object/from16 v18, v16

    .line 125
    .line 126
    const/16 v16, 0x0

    .line 127
    .line 128
    move-object/from16 v21, v17

    .line 129
    .line 130
    const/16 v17, 0x0

    .line 131
    .line 132
    move-object/from16 v22, v18

    .line 133
    .line 134
    const/16 v18, 0x0

    .line 135
    .line 136
    move-object/from16 v4, v21

    .line 137
    .line 138
    invoke-static/range {v5 .. v20}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->b(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;Ljava/lang/String;Ljava/lang/String;ZZZZZLjava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;

    .line 139
    .line 140
    .line 141
    move-result-object v5

    .line 142
    invoke-interface {v2, v4, v5}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 143
    .line 144
    .line 145
    move-result v4

    .line 146
    if-eqz v4, :cond_6

    .line 147
    .line 148
    invoke-virtual {v1}, Ln9/b;->c()Z

    .line 149
    .line 150
    .line 151
    move-result v1

    .line 152
    if-eqz v1, :cond_3

    .line 153
    .line 154
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 155
    .line 156
    invoke-static {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->X3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)V

    .line 157
    .line 158
    .line 159
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 160
    .line 161
    invoke-static {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->d4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)V

    .line 162
    .line 163
    .line 164
    if-eqz v10, :cond_5

    .line 165
    .line 166
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 167
    .line 168
    invoke-static {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->c4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)V

    .line 169
    .line 170
    .line 171
    goto :goto_4

    .line 172
    :cond_3
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 173
    .line 174
    invoke-static {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->Q3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/flow/V;

    .line 175
    .line 176
    .line 177
    move-result-object v4

    .line 178
    iget-object v5, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 179
    .line 180
    :cond_4
    invoke-interface {v4}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 181
    .line 182
    .line 183
    move-result-object v1

    .line 184
    move-object v6, v1

    .line 185
    check-cast v6, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;

    .line 186
    .line 187
    invoke-static {v5}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->J3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)LHX0/e;

    .line 188
    .line 189
    .line 190
    move-result-object v2

    .line 191
    sget v7, Lpb/k;->menu_title:I

    .line 192
    .line 193
    new-array v8, v3, [Ljava/lang/Object;

    .line 194
    .line 195
    invoke-interface {v2, v7, v8}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 196
    .line 197
    .line 198
    move-result-object v7

    .line 199
    const/16 v20, 0x1f7c

    .line 200
    .line 201
    const/16 v21, 0x0

    .line 202
    .line 203
    const-string v8, ""

    .line 204
    .line 205
    const/4 v9, 0x0

    .line 206
    const/4 v10, 0x0

    .line 207
    const/4 v11, 0x0

    .line 208
    const/4 v12, 0x0

    .line 209
    const/4 v13, 0x0

    .line 210
    const/4 v14, 0x0

    .line 211
    const/4 v15, 0x0

    .line 212
    const/16 v16, 0x0

    .line 213
    .line 214
    const/16 v17, 0x0

    .line 215
    .line 216
    const/16 v18, 0x0

    .line 217
    .line 218
    const/16 v19, 0x0

    .line 219
    .line 220
    invoke-static/range {v6 .. v21}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->b(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;Ljava/lang/String;Ljava/lang/String;ZZZZZLjava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;

    .line 221
    .line 222
    .line 223
    move-result-object v2

    .line 224
    invoke-interface {v4, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 225
    .line 226
    .line 227
    move-result v1

    .line 228
    if-eqz v1, :cond_4

    .line 229
    .line 230
    :cond_5
    :goto_4
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 231
    .line 232
    return-object v1

    .line 233
    :cond_6
    move-object/from16 v5, v22

    .line 234
    .line 235
    const/4 v4, 0x1

    .line 236
    goto/16 :goto_1

    .line 237
    .line 238
    :cond_7
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 239
    .line 240
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 241
    .line 242
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 243
    .line 244
    .line 245
    throw v1
.end method
