.class final Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.promo.domain.usecases.GetPromoGiftsUseCase$invoke$2"
    f = "GetPromoGiftsUseCase.kt"
    l = {
        0x16
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;->h(JZLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lg81/h;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "Lg81/h;",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)Lg81/h;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $currentAccountId:J

.field final synthetic $onlyActive:Z

.field private synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;JZLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;",
            "JZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    iput-wide p2, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->$currentAccountId:J

    iput-boolean p4, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->$onlyActive:Z

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->$currentAccountId:J

    iget-boolean v4, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->$onlyActive:Z

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;-><init>(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;JZLkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lg81/h;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    return-object p1

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->L$0:Ljava/lang/Object;

    .line 28
    .line 29
    move-object v6, p1

    .line 30
    check-cast v6, Lkotlinx/coroutines/N;

    .line 31
    .line 32
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    .line 33
    .line 34
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;->d(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;)Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    new-instance v3, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;

    .line 39
    .line 40
    iget-wide v4, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->$currentAccountId:J

    .line 41
    .line 42
    iget-object v7, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    .line 43
    .line 44
    iget-boolean v8, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->$onlyActive:Z

    .line 45
    .line 46
    const/4 v9, 0x0

    .line 47
    invoke-direct/range {v3 .. v9}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;-><init>(JLkotlinx/coroutines/N;Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;ZLkotlin/coroutines/e;)V

    .line 48
    .line 49
    .line 50
    iput v2, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->label:I

    .line 51
    .line 52
    invoke-virtual {p1, v3, p0}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->l(LOc/n;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    if-ne p1, v0, :cond_2

    .line 57
    .line 58
    return-object v0

    .line 59
    :cond_2
    return-object p1
.end method
