.class public final Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LSX0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;->f:LBc/a;

    .line 15
    .line 16
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LSX0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;)",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    move-object v6, p5

    .line 9
    invoke-direct/range {v0 .. v6}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 10
    .line 11
    .line 12
    return-object v0
.end method

.method public static c(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;LwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;)Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;
    .locals 8

    .line 1
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    move-object v6, p5

    .line 9
    move-object v7, p6

    .line 10
    invoke-direct/range {v0 .. v7}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;-><init>(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;LwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;)V

    .line 11
    .line 12
    .line 13
    return-object v0
.end method


# virtual methods
.method public b(LwX0/c;)Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;->b:LBc/a;

    .line 11
    .line 12
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    move-object v2, v0

    .line 17
    check-cast v2, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;->c:LBc/a;

    .line 20
    .line 21
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    move-object v4, v0

    .line 26
    check-cast v4, Lm8/a;

    .line 27
    .line 28
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;->d:LBc/a;

    .line 29
    .line 30
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    move-object v5, v0

    .line 35
    check-cast v5, Lorg/xbet/ui_common/utils/M;

    .line 36
    .line 37
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;->e:LBc/a;

    .line 38
    .line 39
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    move-object v6, v0

    .line 44
    check-cast v6, LSX0/a;

    .line 45
    .line 46
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;->f:LBc/a;

    .line 47
    .line 48
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    move-object v7, v0

    .line 53
    check-cast v7, Lorg/xbet/ui_common/utils/internet/a;

    .line 54
    .line 55
    move-object v3, p1

    .line 56
    invoke-static/range {v1 .. v7}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/k;->c(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;LwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;)Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    return-object p1
.end method
