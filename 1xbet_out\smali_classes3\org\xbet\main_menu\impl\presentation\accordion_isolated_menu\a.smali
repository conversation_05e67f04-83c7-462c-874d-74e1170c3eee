.class public final synthetic Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/a;->a:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/a;->a:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    invoke-static {v0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->C2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)LJ80/a;

    move-result-object v0

    return-object v0
.end method
