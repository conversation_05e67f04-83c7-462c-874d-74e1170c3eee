.class public final Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lv81/c;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u000e\u0018\u00002\u00020\u0001B1\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ \u0010\u0012\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u0010H\u0096B\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J \u0010\u0016\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0015\u001a\u00020\u0014H\u0096B\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0017\u0010\u0019\u001a\u00020\u000e2\u0006\u0010\u0018\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u001bR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u001cR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u001dR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001fR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010!\u00a8\u0006\""
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;",
        "Lv81/c;",
        "Lfk/l;",
        "getLastBalanceUseCase",
        "Lv81/d;",
        "checkBalanceForAggregatorWarningUseCase",
        "Lv81/u;",
        "updateBalanceForAggregatorWarningUseCase",
        "Lfk/j;",
        "getBalanceByIdUseCase",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "<init>",
        "(Lfk/l;Lv81/d;Lv81/u;Lfk/j;Lp9/c;)V",
        "",
        "needTransfer",
        "",
        "balanceId",
        "b",
        "(ZJLkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xbet/balance/model/BalanceModel;",
        "currentBalance",
        "a",
        "(ZLorg/xbet/balance/model/BalanceModel;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "balance",
        "c",
        "(Lorg/xbet/balance/model/BalanceModel;)Z",
        "Lfk/l;",
        "Lv81/d;",
        "Lv81/u;",
        "d",
        "Lfk/j;",
        "e",
        "Lp9/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lfk/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lv81/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lv81/u;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lfk/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lfk/l;Lv81/d;Lv81/u;Lfk/j;Lp9/c;)V
    .locals 0
    .param p1    # Lfk/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lv81/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lv81/u;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lfk/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;->a:Lfk/l;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;->b:Lv81/d;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;->c:Lv81/u;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;->d:Lfk/j;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;->e:Lp9/c;

    .line 13
    .line 14
    return-void
.end method


# virtual methods
.method public a(ZLorg/xbet/balance/model/BalanceModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .param p2    # Lorg/xbet/balance/model/BalanceModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lorg/xbet/balance/model/BalanceModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/lang/Boolean;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    if-eqz p1, :cond_1

    .line 2
    .line 3
    invoke-virtual {p2}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/configs/TypeAccount;->isBonus()Z

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    if-nez p1, :cond_0

    .line 12
    .line 13
    const/4 p1, 0x1

    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/4 p1, 0x0

    .line 16
    goto :goto_0

    .line 17
    :cond_1
    invoke-virtual {p0, p2}, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;->c(Lorg/xbet/balance/model/BalanceModel;)Z

    .line 18
    .line 19
    .line 20
    move-result p1

    .line 21
    :goto_0
    invoke-static {p1}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    return-object p1
.end method

.method public b(ZJLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 10
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZJ",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/lang/Boolean;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p4, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl$invoke$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p4

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl$invoke$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl$invoke$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v5, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl$invoke$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p4}, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl$invoke$1;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p4, v5, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl$invoke$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v5, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl$invoke$1;->label:I

    .line 34
    .line 35
    const/4 v2, 0x2

    .line 36
    const/4 v8, 0x0

    .line 37
    const/4 v9, 0x1

    .line 38
    if-eqz v1, :cond_3

    .line 39
    .line 40
    if-eq v1, v9, :cond_2

    .line 41
    .line 42
    if-ne v1, v2, :cond_1

    .line 43
    .line 44
    iget-boolean p1, v5, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl$invoke$1;->Z$0:Z

    .line 45
    .line 46
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 47
    .line 48
    .line 49
    goto :goto_4

    .line 50
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 51
    .line 52
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 53
    .line 54
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    throw p1

    .line 58
    :cond_2
    iget-boolean p1, v5, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl$invoke$1;->Z$0:Z

    .line 59
    .line 60
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 61
    .line 62
    .line 63
    goto :goto_2

    .line 64
    :cond_3
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 65
    .line 66
    .line 67
    iget-object p4, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;->e:Lp9/c;

    .line 68
    .line 69
    invoke-virtual {p4}, Lp9/c;->a()Z

    .line 70
    .line 71
    .line 72
    move-result p4

    .line 73
    if-eqz p4, :cond_a

    .line 74
    .line 75
    const-wide/16 v3, 0x0

    .line 76
    .line 77
    cmp-long p4, p2, v3

    .line 78
    .line 79
    if-eqz p4, :cond_5

    .line 80
    .line 81
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;->d:Lfk/j;

    .line 82
    .line 83
    iput-boolean p1, v5, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl$invoke$1;->Z$0:Z

    .line 84
    .line 85
    iput v9, v5, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl$invoke$1;->label:I

    .line 86
    .line 87
    const/4 v4, 0x0

    .line 88
    const/4 v6, 0x2

    .line 89
    const/4 v7, 0x0

    .line 90
    move-wide v2, p2

    .line 91
    invoke-static/range {v1 .. v7}, Lfk/j$a;->a(Lfk/j;JLorg/xbet/balance/model/BalanceRefreshType;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 92
    .line 93
    .line 94
    move-result-object p4

    .line 95
    if-ne p4, v0, :cond_4

    .line 96
    .line 97
    goto :goto_3

    .line 98
    :cond_4
    :goto_2
    check-cast p4, Lorg/xbet/balance/model/BalanceModel;

    .line 99
    .line 100
    goto :goto_5

    .line 101
    :cond_5
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;->a:Lfk/l;

    .line 102
    .line 103
    iput-boolean p1, v5, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl$invoke$1;->Z$0:Z

    .line 104
    .line 105
    iput v2, v5, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl$invoke$1;->label:I

    .line 106
    .line 107
    const/4 p3, 0x0

    .line 108
    invoke-static {p2, p3, v5, v9, p3}, Lfk/l$a;->a(Lfk/l;Lorg/xbet/balance/model/BalanceScreenType;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 109
    .line 110
    .line 111
    move-result-object p4

    .line 112
    if-ne p4, v0, :cond_6

    .line 113
    .line 114
    :goto_3
    return-object v0

    .line 115
    :cond_6
    :goto_4
    check-cast p4, Lorg/xbet/balance/model/BalanceModel;

    .line 116
    .line 117
    :goto_5
    if-eqz p1, :cond_7

    .line 118
    .line 119
    invoke-virtual {p4}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 120
    .line 121
    .line 122
    move-result-object p1

    .line 123
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/configs/TypeAccount;->isBonus()Z

    .line 124
    .line 125
    .line 126
    move-result p1

    .line 127
    if-nez p1, :cond_9

    .line 128
    .line 129
    :goto_6
    const/4 v8, 0x1

    .line 130
    goto :goto_7

    .line 131
    :cond_7
    invoke-virtual {p0, p4}, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;->c(Lorg/xbet/balance/model/BalanceModel;)Z

    .line 132
    .line 133
    .line 134
    move-result p1

    .line 135
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;->b:Lv81/d;

    .line 136
    .line 137
    invoke-interface {p2}, Lv81/d;->invoke()Z

    .line 138
    .line 139
    .line 140
    move-result p2

    .line 141
    if-eqz p2, :cond_8

    .line 142
    .line 143
    if-nez p1, :cond_8

    .line 144
    .line 145
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;->c:Lv81/u;

    .line 146
    .line 147
    invoke-interface {p2}, Lv81/u;->invoke()V

    .line 148
    .line 149
    .line 150
    :cond_8
    if-eqz p1, :cond_9

    .line 151
    .line 152
    goto :goto_6

    .line 153
    :cond_9
    :goto_7
    invoke-static {v8}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 154
    .line 155
    .line 156
    move-result-object p1

    .line 157
    return-object p1

    .line 158
    :cond_a
    invoke-static {v8}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 159
    .line 160
    .line 161
    move-result-object p1

    .line 162
    return-object p1
.end method

.method public final c(Lorg/xbet/balance/model/BalanceModel;)Z
    .locals 2

    .line 1
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lcom/xbet/onexcore/data/configs/TypeAccount;->SPORT_BONUS:Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 6
    .line 7
    if-eq v0, v1, :cond_0

    .line 8
    .line 9
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    sget-object v0, Lcom/xbet/onexcore/data/configs/TypeAccount;->GAME_BONUS:Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 14
    .line 15
    if-ne p1, v0, :cond_1

    .line 16
    .line 17
    :cond_0
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorGamesScenarioImpl;->b:Lv81/d;

    .line 18
    .line 19
    invoke-interface {p1}, Lv81/d;->invoke()Z

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    if-nez p1, :cond_2

    .line 24
    .line 25
    :cond_1
    const/4 p1, 0x1

    .line 26
    return p1

    .line 27
    :cond_2
    const/4 p1, 0x0

    .line 28
    return p1
.end method
