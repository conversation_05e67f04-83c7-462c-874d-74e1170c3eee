.class final Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.coupon.impl.make_bet.presentation.viewmodel.MakeBetAutoViewModel$logBetResult$2"
    f = "MakeBetAutoViewModel.kt"
    l = {
        0x314,
        0x315
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->A5(Lfo/d$b;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Lcom/xbet/onexcore/data/errors/IErrorCode;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $betResult:Lfo/d$b;

.field final synthetic $couponTypeModel:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

.field final synthetic $errorCode:Lcom/xbet/onexcore/data/errors/IErrorCode;

.field I$0:I

.field I$1:I

.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lfo/d$b;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Lcom/xbet/onexcore/data/errors/IErrorCode;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;",
            "Lfo/d$b;",
            "Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;",
            "Lcom/xbet/onexcore/data/errors/IErrorCode;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->$betResult:Lfo/d$b;

    .line 4
    .line 5
    iput-object p3, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->$couponTypeModel:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 6
    .line 7
    iput-object p4, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->$errorCode:Lcom/xbet/onexcore/data/errors/IErrorCode;

    .line 8
    .line 9
    const/4 p1, 0x2

    .line 10
    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;

    iget-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->$betResult:Lfo/d$b;

    iget-object v3, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->$couponTypeModel:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    iget-object v4, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->$errorCode:Lcom/xbet/onexcore/data/errors/IErrorCode;

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lfo/d$b;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Lcom/xbet/onexcore/data/errors/IErrorCode;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 13

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x0

    .line 9
    const/4 v4, 0x1

    .line 10
    if-eqz v1, :cond_2

    .line 11
    .line 12
    if-eq v1, v4, :cond_1

    .line 13
    .line 14
    if-ne v1, v2, :cond_0

    .line 15
    .line 16
    iget-boolean v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->Z$0:Z

    .line 17
    .line 18
    iget v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->I$1:I

    .line 19
    .line 20
    iget v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->I$0:I

    .line 21
    .line 22
    iget-object v5, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->L$1:Ljava/lang/Object;

    .line 23
    .line 24
    check-cast v5, Ljava/lang/String;

    .line 25
    .line 26
    iget-object v6, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->L$0:Ljava/lang/Object;

    .line 27
    .line 28
    check-cast v6, Lorg/xbet/betting/core/make_bet/domain/usecases/q;

    .line 29
    .line 30
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 31
    .line 32
    .line 33
    move v9, v0

    .line 34
    move-object v7, v5

    .line 35
    move-object v5, v6

    .line 36
    goto/16 :goto_2

    .line 37
    .line 38
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 39
    .line 40
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 41
    .line 42
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 43
    .line 44
    .line 45
    throw p1

    .line 46
    :cond_1
    iget v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->I$1:I

    .line 47
    .line 48
    iget v5, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->I$0:I

    .line 49
    .line 50
    iget-object v6, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->L$1:Ljava/lang/Object;

    .line 51
    .line 52
    check-cast v6, Ljava/lang/String;

    .line 53
    .line 54
    iget-object v7, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->L$0:Ljava/lang/Object;

    .line 55
    .line 56
    check-cast v7, Lorg/xbet/betting/core/make_bet/domain/usecases/q;

    .line 57
    .line 58
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 59
    .line 60
    .line 61
    goto :goto_0

    .line 62
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 63
    .line 64
    .line 65
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 66
    .line 67
    invoke-static {p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->g4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/coupon/impl/make_bet/domain/scenario/d;

    .line 68
    .line 69
    .line 70
    move-result-object v5

    .line 71
    const-class p1, Lorg/xbet/coupon/impl/make_bet/presentation/fragment/MakeBetAutoFragment;

    .line 72
    .line 73
    invoke-virtual {p1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 74
    .line 75
    .line 76
    move-result-object v12

    .line 77
    sget-object v8, Lorg/xbet/betting/core/make_bet/domain/model/MakeBetOptionType;->COEF:Lorg/xbet/betting/core/make_bet/domain/model/MakeBetOptionType;

    .line 78
    .line 79
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 80
    .line 81
    invoke-static {p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->I3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lkotlinx/coroutines/flow/V;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    check-cast p1, Ljava/lang/Number;

    .line 90
    .line 91
    invoke-virtual {p1}, Ljava/lang/Number;->longValue()J

    .line 92
    .line 93
    .line 94
    move-result-wide v10

    .line 95
    iget-object v6, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->$betResult:Lfo/d$b;

    .line 96
    .line 97
    iget-object v7, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->$couponTypeModel:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 98
    .line 99
    iget-object v9, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->$errorCode:Lcom/xbet/onexcore/data/errors/IErrorCode;

    .line 100
    .line 101
    invoke-virtual/range {v5 .. v12}, Lorg/xbet/coupon/impl/make_bet/domain/scenario/d;->a(Lfo/d$b;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Lorg/xbet/betting/core/make_bet/domain/model/MakeBetOptionType;Lcom/xbet/onexcore/data/errors/IErrorCode;JLjava/lang/String;)V

    .line 102
    .line 103
    .line 104
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->$betResult:Lfo/d$b;

    .line 105
    .line 106
    if-eqz p1, :cond_7

    .line 107
    .line 108
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 109
    .line 110
    invoke-static {p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->h4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/betting/core/make_bet/domain/usecases/q;

    .line 111
    .line 112
    .line 113
    move-result-object p1

    .line 114
    iget-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 115
    .line 116
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->Y3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Ltw/d;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    invoke-interface {v1}, Ltw/d;->invoke()Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 121
    .line 122
    .line 123
    move-result-object v1

    .line 124
    invoke-virtual {v1}, Ljava/lang/Enum;->name()Ljava/lang/String;

    .line 125
    .line 126
    .line 127
    move-result-object v1

    .line 128
    iget-object v5, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 129
    .line 130
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->L$0:Ljava/lang/Object;

    .line 131
    .line 132
    iput-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->L$1:Ljava/lang/Object;

    .line 133
    .line 134
    iput v3, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->I$0:I

    .line 135
    .line 136
    iput v4, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->I$1:I

    .line 137
    .line 138
    iput v4, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->label:I

    .line 139
    .line 140
    invoke-static {v5, p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->o4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 141
    .line 142
    .line 143
    move-result-object v5

    .line 144
    if-ne v5, v0, :cond_3

    .line 145
    .line 146
    goto :goto_1

    .line 147
    :cond_3
    move-object v7, p1

    .line 148
    move-object v6, v1

    .line 149
    move-object p1, v5

    .line 150
    const/4 v1, 0x1

    .line 151
    const/4 v5, 0x0

    .line 152
    :goto_0
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 153
    .line 154
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 155
    .line 156
    .line 157
    move-result-object p1

    .line 158
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/configs/TypeAccount;->isBonus()Z

    .line 159
    .line 160
    .line 161
    move-result p1

    .line 162
    iget-object v8, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 163
    .line 164
    invoke-static {v8}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->N3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lfk/c;

    .line 165
    .line 166
    .line 167
    move-result-object v8

    .line 168
    iput-object v7, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->L$0:Ljava/lang/Object;

    .line 169
    .line 170
    iput-object v6, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->L$1:Ljava/lang/Object;

    .line 171
    .line 172
    iput v5, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->I$0:I

    .line 173
    .line 174
    iput v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->I$1:I

    .line 175
    .line 176
    iput-boolean p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->Z$0:Z

    .line 177
    .line 178
    iput v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$logBetResult$2;->label:I

    .line 179
    .line 180
    invoke-interface {v8, p0}, Lfk/c;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 181
    .line 182
    .line 183
    move-result-object v2

    .line 184
    if-ne v2, v0, :cond_4

    .line 185
    .line 186
    :goto_1
    return-object v0

    .line 187
    :cond_4
    move v9, p1

    .line 188
    move-object p1, v2

    .line 189
    move v2, v5

    .line 190
    move-object v5, v7

    .line 191
    move-object v7, v6

    .line 192
    :goto_2
    if-eqz v2, :cond_5

    .line 193
    .line 194
    const/4 v6, 0x1

    .line 195
    goto :goto_3

    .line 196
    :cond_5
    const/4 v6, 0x0

    .line 197
    :goto_3
    if-eqz v1, :cond_6

    .line 198
    .line 199
    const/4 v8, 0x1

    .line 200
    goto :goto_4

    .line 201
    :cond_6
    const/4 v8, 0x0

    .line 202
    :goto_4
    check-cast p1, Ljava/lang/Boolean;

    .line 203
    .line 204
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 205
    .line 206
    .line 207
    move-result v10

    .line 208
    invoke-virtual/range {v5 .. v10}, Lorg/xbet/betting/core/make_bet/domain/usecases/q;->a(ZLjava/lang/String;ZZZ)V

    .line 209
    .line 210
    .line 211
    :cond_7
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 212
    .line 213
    return-object p1
.end method
