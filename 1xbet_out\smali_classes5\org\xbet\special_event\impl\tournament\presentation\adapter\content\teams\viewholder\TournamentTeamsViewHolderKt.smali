.class public final Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/teams/viewholder/TournamentTeamsViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a;\u0010\u000b\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\n0\t0\u00082\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "",
        "nestedPrefetchItemCount",
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "LCx0/a;",
        "tournamentTeamClickListener",
        "LAx0/a;",
        "tournamentTeamFilterClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "g",
        "(ILUX0/k;LCx0/a;LAx0/a;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LUX0/k;LB4/a;Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/teams/viewholder/TournamentTeamsViewHolderKt;->m(LUX0/k;LB4/a;Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LUX0/k;LB4/a;Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/teams/viewholder/TournamentTeamsViewHolderKt;->l(LUX0/k;LB4/a;Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lq31/c;LB4/a;Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/teams/viewholder/TournamentTeamsViewHolderKt;->k(Lq31/c;LB4/a;Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LAx0/a;LB4/a;LCx0/a;Lq31/c;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/teams/viewholder/TournamentTeamsViewHolderKt;->j(LAx0/a;LB4/a;LCx0/a;Lq31/c;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/i1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/teams/viewholder/TournamentTeamsViewHolderKt;->h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/i1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(ILAx0/a;LCx0/a;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/teams/viewholder/TournamentTeamsViewHolderKt;->i(ILAx0/a;LCx0/a;LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final g(ILUX0/k;LCx0/a;LAx0/a;)LA4/c;
    .locals 2
    .param p1    # LUX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LCx0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LAx0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "LUX0/k;",
            "LCx0/a;",
            "LAx0/a;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LDx0/a;

    .line 2
    .line 3
    invoke-direct {v0}, LDx0/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LDx0/b;

    .line 7
    .line 8
    invoke-direct {v1, p0, p3, p2, p1}, LDx0/b;-><init>(ILAx0/a;LCx0/a;LUX0/k;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/teams/viewholder/TournamentTeamsViewHolderKt$tournamentTeamsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/teams/viewholder/TournamentTeamsViewHolderKt$tournamentTeamsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/teams/viewholder/TournamentTeamsViewHolderKt$tournamentTeamsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/teams/viewholder/TournamentTeamsViewHolderKt$tournamentTeamsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance p2, LB4/b;

    .line 19
    .line 20
    invoke-direct {p2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object p2
.end method

.method public static final h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/i1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/i1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/i1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final i(ILAx0/a;LCx0/a;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 8

    .line 1
    new-instance v0, Lq31/c;

    .line 2
    .line 3
    sget-object v2, Lq31/e$a;->a:Lq31/e$a;

    .line 4
    .line 5
    sget v1, Lpb/k;->change:I

    .line 6
    .line 7
    invoke-virtual {p4, v1}, LB4/a;->j(I)Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    const/16 v6, 0x18

    .line 12
    .line 13
    const/4 v7, 0x0

    .line 14
    const/4 v1, 0x0

    .line 15
    const/4 v4, 0x0

    .line 16
    const/4 v5, 0x0

    .line 17
    invoke-direct/range {v0 .. v7}, Lq31/c;-><init>(ILq31/e;Ljava/lang/String;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p4}, LB4/a;->e()LL2/a;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    check-cast v1, LGq0/i1;

    .line 25
    .line 26
    iget-object v1, v1, LGq0/i1;->b:Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;

    .line 27
    .line 28
    invoke-virtual {v1}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    instance-of v3, v2, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 33
    .line 34
    if-eqz v3, :cond_0

    .line 35
    .line 36
    check-cast v2, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_0
    const/4 v2, 0x0

    .line 40
    :goto_0
    if-eqz v2, :cond_1

    .line 41
    .line 42
    invoke-virtual {v2, p0}, Landroidx/recyclerview/widget/LinearLayoutManager;->setInitialPrefetchItemCount(I)V

    .line 43
    .line 44
    .line 45
    const/4 p0, 0x0

    .line 46
    invoke-virtual {v1, p0}, Landroidx/recyclerview/widget/RecyclerView;->setNestedScrollingEnabled(Z)V

    .line 47
    .line 48
    .line 49
    :cond_1
    new-instance p0, LDx0/c;

    .line 50
    .line 51
    invoke-direct {p0, p1, p4, p2}, LDx0/c;-><init>(LAx0/a;LB4/a;LCx0/a;)V

    .line 52
    .line 53
    .line 54
    invoke-virtual {v1, p0}, Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function2;)V

    .line 55
    .line 56
    .line 57
    new-instance p0, LDx0/d;

    .line 58
    .line 59
    invoke-direct {p0, v0, p4, v1}, LDx0/d;-><init>(Lq31/c;LB4/a;Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;)V

    .line 60
    .line 61
    .line 62
    invoke-virtual {p4, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 63
    .line 64
    .line 65
    new-instance p0, LDx0/e;

    .line 66
    .line 67
    invoke-direct {p0, p3, p4, v1}, LDx0/e;-><init>(LUX0/k;LB4/a;Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;)V

    .line 68
    .line 69
    .line 70
    invoke-virtual {p4, p0}, LB4/a;->r(Lkotlin/jvm/functions/Function0;)V

    .line 71
    .line 72
    .line 73
    new-instance p0, LDx0/f;

    .line 74
    .line 75
    invoke-direct {p0, p3, p4, v1}, LDx0/f;-><init>(LUX0/k;LB4/a;Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;)V

    .line 76
    .line 77
    .line 78
    invoke-virtual {p4, p0}, LB4/a;->s(Lkotlin/jvm/functions/Function0;)V

    .line 79
    .line 80
    .line 81
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 82
    .line 83
    return-object p0
.end method

.method public static final j(LAx0/a;LB4/a;LCx0/a;Lq31/c;I)Lkotlin/Unit;
    .locals 0

    .line 1
    if-nez p4, :cond_0

    .line 2
    .line 3
    invoke-interface {p0}, LAx0/a;->c0()V

    .line 4
    .line 5
    .line 6
    goto :goto_0

    .line 7
    :cond_0
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    check-cast p0, LBx0/a;

    .line 12
    .line 13
    invoke-virtual {p0}, LBx0/a;->d()Ljava/util/List;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    add-int/lit8 p4, p4, -0x1

    .line 18
    .line 19
    invoke-interface {p0, p4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object p0

    .line 23
    check-cast p0, Lq31/c;

    .line 24
    .line 25
    invoke-virtual {p0}, Lq31/c;->e()I

    .line 26
    .line 27
    .line 28
    move-result p0

    .line 29
    invoke-interface {p2, p0}, LCx0/a;->c(I)V

    .line 30
    .line 31
    .line 32
    :goto_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 33
    .line 34
    return-object p0
.end method

.method public static final k(Lq31/c;LB4/a;Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    check-cast p1, LBx0/a;

    .line 10
    .line 11
    invoke-virtual {p1}, LBx0/a;->d()Ljava/util/List;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    invoke-static {p0, p1}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {p2, p0}, Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;->setItems(Ljava/util/List;)V

    .line 20
    .line 21
    .line 22
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 23
    .line 24
    return-object p0
.end method

.method public static final l(LUX0/k;LB4/a;Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getBindingAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p0, p1, p2}, LUX0/k;->c(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final m(LUX0/k;LB4/a;Lorg/xbet/uikit_sport/championshipcardcollection/SportChampionshipCardCollection;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getBindingAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p0, p1, p2}, LUX0/k;->e(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method
