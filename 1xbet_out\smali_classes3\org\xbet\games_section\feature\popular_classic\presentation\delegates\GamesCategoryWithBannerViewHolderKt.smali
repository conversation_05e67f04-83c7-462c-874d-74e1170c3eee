.class public final Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCategoryWithBannerViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a3\u0010\t\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00080\u00070\u00062\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0004H\u0000\u00a2\u0006\u0004\u0008\t\u0010\n\u00a8\u0006\u000b"
    }
    d2 = {
        "Li50/a;",
        "clickListener",
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "",
        "screenName",
        "LA4/c;",
        "",
        "LVX0/i;",
        "h",
        "(Li50/a;LUX0/k;Ljava/lang/String;)LA4/c;",
        "popular_classic_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Li50/a;Ljava/lang/String;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCategoryWithBannerViewHolderKt;->j(Li50/a;Ljava/lang/String;LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LUX0/k;LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCategoryWithBannerViewHolderKt;->n(LUX0/k;LB4/a;)V

    return-void
.end method

.method public static synthetic c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Ld50/d;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCategoryWithBannerViewHolderKt;->i(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Ld50/d;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Li50/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCategoryWithBannerViewHolderKt;->l(Li50/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(LB4/a;Li50/a;Ljava/lang/String;Ln41/m;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCategoryWithBannerViewHolderKt;->k(LB4/a;Li50/a;Ljava/lang/String;Ln41/m;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCategoryWithBannerViewHolderKt;->o(LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(LB4/a;LUX0/k;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCategoryWithBannerViewHolderKt;->m(LB4/a;LUX0/k;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final h(Li50/a;LUX0/k;Ljava/lang/String;)LA4/c;
    .locals 2
    .param p0    # Li50/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LUX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li50/a;",
            "LUX0/k;",
            "Ljava/lang/String;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lh50/a;

    .line 2
    .line 3
    invoke-direct {v0}, Lh50/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lh50/b;

    .line 7
    .line 8
    invoke-direct {v1, p0, p2, p1}, Lh50/b;-><init>(Li50/a;Ljava/lang/String;LUX0/k;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCategoryWithBannerViewHolderKt$getGamesCategoryWithBannerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCategoryWithBannerViewHolderKt$getGamesCategoryWithBannerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCategoryWithBannerViewHolderKt$getGamesCategoryWithBannerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/GamesCategoryWithBannerViewHolderKt$getGamesCategoryWithBannerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance p2, LB4/b;

    .line 19
    .line 20
    invoke-direct {p2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object p2
.end method

.method public static final i(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Ld50/d;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, Ld50/d;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)Ld50/d;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final j(Li50/a;Ljava/lang/String;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 7

    .line 1
    invoke-virtual {p3}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Ld50/d;

    .line 6
    .line 7
    iget-object v0, v0, Ld50/d;->b:Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;

    .line 8
    .line 9
    new-instance v1, Lh50/c;

    .line 10
    .line 11
    invoke-direct {v1, p3, p0, p1}, Lh50/c;-><init>(LB4/a;Li50/a;Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function2;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p3}, LB4/a;->e()LL2/a;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, Ld50/d;

    .line 22
    .line 23
    iget-object v0, v0, Ld50/d;->b:Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;

    .line 24
    .line 25
    new-instance v1, Lh50/d;

    .line 26
    .line 27
    invoke-direct {v1, p0, p1, p3}, Lh50/d;-><init>(Li50/a;Ljava/lang/String;LB4/a;)V

    .line 28
    .line 29
    .line 30
    const/4 p0, 0x1

    .line 31
    const/4 p1, 0x0

    .line 32
    invoke-static {v0, p1, v1, p0, p1}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 33
    .line 34
    .line 35
    invoke-virtual {p3}, LB4/a;->g()Landroid/content/Context;

    .line 36
    .line 37
    .line 38
    move-result-object p0

    .line 39
    invoke-virtual {p0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 40
    .line 41
    .line 42
    move-result-object p0

    .line 43
    sget p1, LlZ0/g;->medium_horizontal_margin_dynamic:I

    .line 44
    .line 45
    invoke-virtual {p0, p1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 46
    .line 47
    .line 48
    move-result v1

    .line 49
    iget-object v0, p3, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 50
    .line 51
    invoke-virtual {p3}, LB4/a;->g()Landroid/content/Context;

    .line 52
    .line 53
    .line 54
    move-result-object p0

    .line 55
    invoke-virtual {p0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 56
    .line 57
    .line 58
    move-result-object p0

    .line 59
    sget p1, Lpb/f;->space_8:I

    .line 60
    .line 61
    invoke-virtual {p0, p1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 62
    .line 63
    .line 64
    move-result v2

    .line 65
    const/16 v5, 0x8

    .line 66
    .line 67
    const/4 v6, 0x0

    .line 68
    const/4 v4, 0x0

    .line 69
    move v3, v1

    .line 70
    invoke-static/range {v0 .. v6}, Lorg/xbet/ui_common/utils/ExtensionsKt;->o0(Landroid/view/View;IIIIILjava/lang/Object;)V

    .line 71
    .line 72
    .line 73
    new-instance p0, Lh50/e;

    .line 74
    .line 75
    invoke-direct {p0, p3, p2}, Lh50/e;-><init>(LB4/a;LUX0/k;)V

    .line 76
    .line 77
    .line 78
    invoke-virtual {p3, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 79
    .line 80
    .line 81
    new-instance p0, Lh50/f;

    .line 82
    .line 83
    invoke-direct {p0, p2, p3}, Lh50/f;-><init>(LUX0/k;LB4/a;)V

    .line 84
    .line 85
    .line 86
    invoke-virtual {p3, p0}, LB4/a;->t(Lkotlin/jvm/functions/Function0;)V

    .line 87
    .line 88
    .line 89
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 90
    .line 91
    return-object p0
.end method

.method public static final k(LB4/a;Li50/a;Ljava/lang/String;Ln41/m;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    check-cast p3, Lk50/g$b;

    .line 6
    .line 7
    invoke-virtual {p3}, Lk50/g$b;->o()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    invoke-interface {p3, p4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p3

    .line 15
    check-cast p3, Lk50/f;

    .line 16
    .line 17
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    check-cast p0, Lk50/g$b;

    .line 22
    .line 23
    invoke-virtual {p0}, Lk50/g$b;->d()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    const/4 p4, 0x0

    .line 28
    invoke-interface {p1, p2, p3, p0, p4}, Li50/a;->Q1(Ljava/lang/String;Lk50/f;Ljava/lang/String;Z)V

    .line 29
    .line 30
    .line 31
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 32
    .line 33
    return-object p0
.end method

.method public static final l(Li50/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p2}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, Lk50/g$b;

    .line 6
    .line 7
    invoke-virtual {p2}, Lk50/g$b;->d()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    invoke-interface {p0, p1, p2}, Li50/a;->k(Ljava/lang/String;Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final m(LB4/a;LUX0/k;Ljava/util/List;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, Ld50/d;

    .line 6
    .line 7
    iget-object p2, p2, Ld50/d;->b:Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, Lk50/g$b;

    .line 14
    .line 15
    invoke-virtual {v0}, Lk50/g$b;->e()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    invoke-virtual {p2, v0}, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->setBackgroundResource(I)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    check-cast v0, Lk50/g$b;

    .line 27
    .line 28
    invoke-virtual {v0}, Lk50/g$b;->f()I

    .line 29
    .line 30
    .line 31
    move-result v0

    .line 32
    invoke-virtual {p2, v0}, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->setBackgroundPictureResource(I)V

    .line 33
    .line 34
    .line 35
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    check-cast v0, Lk50/g$b;

    .line 40
    .line 41
    invoke-virtual {v0}, Lk50/g$b;->s()Lorg/xbet/uikit/components/header/a$a;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    invoke-virtual {v0}, Lorg/xbet/uikit/components/header/a$a;->h()Ljava/lang/CharSequence;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    invoke-virtual {p2, v0}, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->setTitle(Ljava/lang/CharSequence;)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    check-cast v0, Lk50/g$b;

    .line 57
    .line 58
    invoke-virtual {v0}, Lk50/g$b;->j()Ljava/util/List;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    new-instance v1, Lh50/g;

    .line 63
    .line 64
    invoke-direct {v1, p1, p0}, Lh50/g;-><init>(LUX0/k;LB4/a;)V

    .line 65
    .line 66
    .line 67
    invoke-virtual {p2, v0, v1}, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->setItems(Ljava/util/List;Ljava/lang/Runnable;)V

    .line 68
    .line 69
    .line 70
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 71
    .line 72
    return-object p0
.end method

.method public static final n(LUX0/k;LB4/a;)V
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, Ld50/d;

    .line 14
    .line 15
    iget-object p1, p1, Ld50/d;->b:Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;

    .line 16
    .line 17
    invoke-virtual {p1}, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->getGameCollection()Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-virtual {p0, v0, p1}, LUX0/k;->c(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public static final o(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, Ld50/d;

    .line 14
    .line 15
    iget-object p1, p1, Ld50/d;->b:Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;

    .line 16
    .line 17
    invoke-virtual {p1}, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->getGameCollection()Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-virtual {p0, v0, p1}, LUX0/k;->e(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 22
    .line 23
    .line 24
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 25
    .line 26
    return-object p0
.end method
