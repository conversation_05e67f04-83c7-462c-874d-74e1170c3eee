.class public final Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0088\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\r\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0014\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 [2\u00020\u0001:\u0001.B\u001d\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u001f\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\n\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u000c\u0010\rJ7\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u00082\u0006\u0010\u0011\u001a\u00020\u00082\u0006\u0010\u0012\u001a\u00020\u00082\u0006\u0010\u0013\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\'\u0010\u001b\u001a\u00020\u000b2\u000c\u0010\u0018\u001a\u0008\u0012\u0004\u0012\u00020\u00170\u00162\n\u0008\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u0019\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\'\u0010\u001f\u001a\u00020\u000b2\u0018\u0010\u001e\u001a\u0014\u0012\u0004\u0012\u00020\u0017\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\u000b0\u001d\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0017\u0010#\u001a\u00020\u000b2\u0008\u0010\"\u001a\u0004\u0018\u00010!\u00a2\u0006\u0004\u0008#\u0010$J\u0017\u0010&\u001a\u00020\u000b2\u0008\u0008\u0001\u0010%\u001a\u00020\u0008\u00a2\u0006\u0004\u0008&\u0010\'J\u0017\u0010)\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008)\u0010\'J\u0017\u0010*\u001a\u00020\u000b2\u0008\u0008\u0001\u0010%\u001a\u00020\u0008\u00a2\u0006\u0004\u0008*\u0010\'J\r\u0010,\u001a\u00020+\u00a2\u0006\u0004\u0008,\u0010-J\u000f\u0010.\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008.\u0010/R\u0014\u00101\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u00100R\u0014\u00103\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00100R\u0014\u00105\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00100R\u0014\u00107\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00100R\u0014\u00109\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00100R\u0014\u0010;\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u00100R\u0014\u0010=\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u00100R\u0014\u0010?\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u00100R\u0014\u0010C\u001a\u00020@8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008A\u0010BR\u0014\u0010G\u001a\u00020D8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008E\u0010FR\u0014\u0010J\u001a\u00020+8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0014\u0010N\u001a\u00020K8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0014\u0010R\u001a\u00020O8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010QR\u0014\u0010V\u001a\u00020S8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008T\u0010UR\u0018\u0010Z\u001a\u0004\u0018\u00010W8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008X\u0010Y\u00a8\u0006\\"
    }
    d2 = {
        "Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "",
        "Ln41/m;",
        "items",
        "Ljava/lang/Runnable;",
        "commitCallback",
        "setItems",
        "(Ljava/util/List;Ljava/lang/Runnable;)V",
        "Lkotlin/Function2;",
        "listener",
        "setOnItemClickListener",
        "(Lkotlin/jvm/functions/Function2;)V",
        "",
        "newTitle",
        "setTitle",
        "(Ljava/lang/CharSequence;)V",
        "resId",
        "setBackgroundPictureResource",
        "(I)V",
        "resid",
        "setBackgroundResource",
        "setTitleColor",
        "Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;",
        "getGameCollection",
        "()Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;",
        "a",
        "()V",
        "I",
        "space8",
        "b",
        "space16",
        "c",
        "space96",
        "d",
        "size40",
        "e",
        "size128",
        "f",
        "radius24",
        "g",
        "backgroundPictureSizeMeasureSpec",
        "h",
        "skeletonHeight",
        "Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;",
        "i",
        "Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;",
        "gameType",
        "Landroid/widget/TextView;",
        "j",
        "Landroid/widget/TextView;",
        "title",
        "k",
        "Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;",
        "gameCollection",
        "Landroid/widget/ImageView;",
        "l",
        "Landroid/widget/ImageView;",
        "backgroundPicture",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "m",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "shimmerView",
        "Landroid/view/View;",
        "n",
        "Landroid/view/View;",
        "backgroundView",
        "Ln41/c;",
        "o",
        "Ln41/c;",
        "gameCollectionAdapter",
        "p",
        "uikit_web_games_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final p:Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final q:I


# instance fields
.field public final a:I

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Landroid/widget/TextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Landroid/widget/ImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:Landroid/view/View;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public o:Ln41/c;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->p:Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->q:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 14
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    move-object v1, p1

    .line 3
    invoke-direct/range {p0 .. p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_8:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->a:I

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_16:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->b:I

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_96:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->c:I

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_40:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->d:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_128:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->e:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v3, LlZ0/g;->radius_24:I

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v6

    iput v6, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->f:I

    const/high16 v2, 0x40000000    # 2.0f

    .line 10
    invoke-static {v0, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->g:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->one_x_games_top_games_collection_skeleton_height:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->h:I

    .line 12
    sget-object v8, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->Rectangle:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    iput-object v8, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->i:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 13
    new-instance v7, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v7, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 14
    sget v0, LlZ0/n;->TextStyle_Title_Bold_M_StaticWhite:I

    invoke-static {v7, v0}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    const/4 v0, 0x1

    .line 15
    invoke-virtual {v7, v0}, Landroid/widget/TextView;->setLines(I)V

    const/16 v2, 0xa

    const/16 v3, 0x14

    .line 16
    invoke-static {v7, v2, v3, v2, v0}, LX0/o;->h(Landroid/widget/TextView;IIII)V

    const/16 v0, 0x10

    .line 17
    invoke-virtual {v7, v0}, Landroid/widget/TextView;->setGravity(I)V

    .line 18
    sget-object v0, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {v7, v0}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    const/4 v0, 0x5

    .line 19
    invoke-virtual {v7, v0}, Landroid/view/View;->setTextDirection(I)V

    .line 20
    iput-object v7, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->j:Landroid/widget/TextView;

    .line 21
    new-instance v0, Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    move-object v13, v0

    iput-object v13, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->k:Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    .line 22
    new-instance v9, Landroid/widget/ImageView;

    invoke-direct {v9, p1}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    .line 23
    sget-object v0, Landroid/widget/ImageView$ScaleType;->CENTER_CROP:Landroid/widget/ImageView$ScaleType;

    invoke-virtual {v9, v0}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    const/16 v10, 0x8

    .line 24
    invoke-virtual {v9, v10}, Landroid/widget/ImageView;->setVisibility(I)V

    .line 25
    iput-object v9, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->l:Landroid/widget/ImageView;

    .line 26
    new-instance v0, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/shimmer/ShimmerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 27
    invoke-virtual {v0, v10}, Lorg/xbet/uikit/components/shimmer/ShimmerView;->setVisibility(I)V

    .line 28
    new-instance v2, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v2}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 29
    sget v3, LlZ0/d;->uikitSecondary20:I

    const/4 v4, 0x0

    const/4 v5, 0x2

    invoke-static {p1, v3, v4, v5, v4}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v3

    invoke-static {v3}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v3

    invoke-virtual {v2, v3}, Landroid/graphics/drawable/GradientDrawable;->setColor(Landroid/content/res/ColorStateList;)V

    .line 30
    invoke-virtual {v0, v2}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 31
    iput-object v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->m:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 32
    new-instance v2, Landroid/view/View;

    invoke-direct {v2, p1}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    iput-object v2, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->n:Landroid/view/View;

    .line 33
    invoke-static {p0, v6}, Lorg/xbet/uikit/utils/S;->m(Landroid/view/View;I)V

    .line 34
    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 35
    invoke-virtual {p0, v9}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 36
    invoke-virtual {p0, v7}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 37
    invoke-virtual {p0, v13}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 38
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 39
    sget-object v0, Lj41/f;->OneXGamesTopCollectionStyle:[I

    const/4 v2, 0x0

    move-object/from16 v3, p2

    .line 40
    invoke-virtual {p1, v3, v0, v2, v2}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object v0

    .line 41
    sget v3, Lj41/f;->OneXGamesTopCollectionStyle_title:I

    invoke-virtual {v0, v3}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    move-result-object v3

    .line 42
    invoke-virtual {p0, v3}, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->setTitle(Ljava/lang/CharSequence;)V

    .line 43
    sget v3, Lj41/f;->OneXGamesTopCollectionStyle_backgroundPicture:I

    invoke-virtual {v0, v3, v2}, Landroid/content/res/TypedArray;->getResourceId(II)I

    move-result v2

    .line 44
    invoke-virtual {p0, v2}, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->setBackgroundPictureResource(I)V

    .line 45
    invoke-virtual {v0}, Landroid/content/res/TypedArray;->recycle()V

    .line 46
    new-instance v7, Ln41/c;

    .line 47
    sget v0, Lj41/c;->ic_games_placeholder_icon:I

    .line 48
    sget v2, LlZ0/h;->banner_item_placeholder:I

    .line 49
    invoke-static {p1, v8, v0, v2}, Lj41/a;->b(Landroid/content/Context;Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;II)LL11/c;

    move-result-object v9

    const/4 v11, 0x4

    const/4 v12, 0x0

    const/4 v10, 0x0

    .line 50
    invoke-direct/range {v7 .. v12}, Ln41/c;-><init>(Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;LL11/c;Lkotlin/jvm/functions/Function2;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v7, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->o:Ln41/c;

    .line 51
    invoke-virtual {v13, v7}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 52
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->a()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic setItems$default(Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;Ljava/util/List;Ljava/lang/Runnable;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x2

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    const/4 p2, 0x0

    .line 6
    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->setItems(Ljava/util/List;Ljava/lang/Runnable;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method


# virtual methods
.method public final a()V
    .locals 11

    .line 1
    new-instance v0, LR11/c;

    .line 2
    .line 3
    iget v5, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->a:I

    .line 4
    .line 5
    const/16 v9, 0xc0

    .line 6
    .line 7
    const/4 v10, 0x0

    .line 8
    const/4 v1, 0x0

    .line 9
    const/4 v2, 0x0

    .line 10
    const/4 v3, 0x0

    .line 11
    const/4 v4, 0x0

    .line 12
    const/4 v6, 0x0

    .line 13
    const/4 v7, 0x0

    .line 14
    const/4 v8, 0x0

    .line 15
    invoke-direct/range {v0 .. v10}, LR11/c;-><init>(IIIIIILkotlin/jvm/functions/Function1;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 16
    .line 17
    .line 18
    iget-object v1, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->k:Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    .line 19
    .line 20
    invoke-virtual {v1, v0}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public final getGameCollection()Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->k:Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    .line 2
    .line 3
    return-object v0
.end method

.method public onLayout(ZIIII)V
    .locals 7

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->n:Landroid/view/View;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v2

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 8
    .line 9
    .line 10
    move-result v3

    .line 11
    const/4 v4, 0x0

    .line 12
    invoke-virtual {v1, v4, v4, v2, v3}, Landroid/view/View;->layout(IIII)V

    .line 13
    .line 14
    .line 15
    iget-object v1, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->m:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 16
    .line 17
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 22
    .line 23
    .line 24
    move-result v3

    .line 25
    invoke-virtual {v1, v4, v4, v2, v3}, Landroid/view/View;->layout(IIII)V

    .line 26
    .line 27
    .line 28
    iget-object v1, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->j:Landroid/widget/TextView;

    .line 29
    .line 30
    iget v2, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->b:I

    .line 31
    .line 32
    iget v3, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->a:I

    .line 33
    .line 34
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 35
    .line 36
    .line 37
    move-result v4

    .line 38
    iget v5, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->c:I

    .line 39
    .line 40
    sub-int/2addr v4, v5

    .line 41
    iget-object v5, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->j:Landroid/widget/TextView;

    .line 42
    .line 43
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredHeight()I

    .line 44
    .line 45
    .line 46
    move-result v5

    .line 47
    iget v6, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->a:I

    .line 48
    .line 49
    add-int/2addr v5, v6

    .line 50
    move-object v0, p0

    .line 51
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 52
    .line 53
    .line 54
    iget-object v1, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->l:Landroid/widget/ImageView;

    .line 55
    .line 56
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 57
    .line 58
    .line 59
    move-result v2

    .line 60
    iget v3, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->e:I

    .line 61
    .line 62
    sub-int/2addr v2, v3

    .line 63
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 64
    .line 65
    .line 66
    move-result v4

    .line 67
    iget v5, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->e:I

    .line 68
    .line 69
    const/4 v3, 0x0

    .line 70
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 71
    .line 72
    .line 73
    iget-object v1, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->k:Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    .line 74
    .line 75
    iget v3, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->d:I

    .line 76
    .line 77
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 78
    .line 79
    .line 80
    move-result v4

    .line 81
    iget v2, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->d:I

    .line 82
    .line 83
    iget-object v5, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->k:Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    .line 84
    .line 85
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredHeight()I

    .line 86
    .line 87
    .line 88
    move-result v5

    .line 89
    add-int/2addr v5, v2

    .line 90
    const/4 v2, 0x0

    .line 91
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 92
    .line 93
    .line 94
    return-void
.end method

.method public onMeasure(II)V
    .locals 4

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    iget v1, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->h:I

    .line 6
    .line 7
    const/high16 v2, 0x40000000    # 2.0f

    .line 8
    .line 9
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    iget-object v3, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->m:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 14
    .line 15
    invoke-virtual {v3, p1, v1}, Landroid/view/View;->measure(II)V

    .line 16
    .line 17
    .line 18
    iget-object v3, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->n:Landroid/view/View;

    .line 19
    .line 20
    invoke-virtual {v3, p1, v1}, Landroid/view/View;->measure(II)V

    .line 21
    .line 22
    .line 23
    iget v1, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->b:I

    .line 24
    .line 25
    sub-int v1, v0, v1

    .line 26
    .line 27
    iget v3, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->c:I

    .line 28
    .line 29
    sub-int/2addr v1, v3

    .line 30
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 31
    .line 32
    .line 33
    move-result v1

    .line 34
    const/4 v2, 0x0

    .line 35
    invoke-static {v2, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 36
    .line 37
    .line 38
    move-result v2

    .line 39
    iget-object v3, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->j:Landroid/widget/TextView;

    .line 40
    .line 41
    invoke-virtual {v3, v1, v2}, Landroid/view/View;->measure(II)V

    .line 42
    .line 43
    .line 44
    iget-object v1, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->k:Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    .line 45
    .line 46
    invoke-virtual {v1, p1, p2}, Landroid/view/View;->measure(II)V

    .line 47
    .line 48
    .line 49
    iget-object p1, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->l:Landroid/widget/ImageView;

    .line 50
    .line 51
    iget p2, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->g:I

    .line 52
    .line 53
    invoke-virtual {p1, p2, p2}, Landroid/view/View;->measure(II)V

    .line 54
    .line 55
    .line 56
    iget p1, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->h:I

    .line 57
    .line 58
    invoke-virtual {p0, v0, p1}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 59
    .line 60
    .line 61
    return-void
.end method

.method public final setBackgroundPictureResource(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->l:Landroid/widget/ImageView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->l:Landroid/widget/ImageView;

    .line 7
    .line 8
    const/4 v1, 0x0

    .line 9
    if-eqz p1, :cond_0

    .line 10
    .line 11
    const/4 p1, 0x1

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    const/4 p1, 0x0

    .line 14
    :goto_0
    if-eqz p1, :cond_1

    .line 15
    .line 16
    goto :goto_1

    .line 17
    :cond_1
    const/16 v1, 0x8

    .line 18
    .line 19
    :goto_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public setBackgroundResource(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->n:Landroid/view/View;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/view/View;->setBackgroundResource(I)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setItems(Ljava/util/List;Ljava/lang/Runnable;)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ln41/m;",
            ">;",
            "Ljava/lang/Runnable;",
            ")V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->k:Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;->setItems(Ljava/util/List;Ljava/lang/Runnable;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setOnItemClickListener(Lkotlin/jvm/functions/Function2;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ln41/m;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->k:Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_web_games/game_collection/WebGamesGameCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function2;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->j:Landroid/widget/TextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setTitleColor(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/top_games_collection/WebGamesTopGamesCollection;->j:Landroid/widget/TextView;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-static {v1, p1}, LF0/b;->getColor(Landroid/content/Context;I)I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setTextColor(I)V

    .line 12
    .line 13
    .line 14
    return-void
.end method
