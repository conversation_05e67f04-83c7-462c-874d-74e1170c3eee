.class public final synthetic LL1/p;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/video/c$d;

.field public final synthetic b:Landroidx/media3/exoplayer/video/VideoSink$a;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/video/c$d;Landroidx/media3/exoplayer/video/VideoSink$a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LL1/p;->a:Landroidx/media3/exoplayer/video/c$d;

    iput-object p2, p0, LL1/p;->b:Landroidx/media3/exoplayer/video/VideoSink$a;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, LL1/p;->a:Landroidx/media3/exoplayer/video/c$d;

    iget-object v1, p0, LL1/p;->b:Landroidx/media3/exoplayer/video/VideoSink$a;

    invoke-static {v0, v1}, Landroidx/media3/exoplayer/video/c$d;->C(Landroidx/media3/exoplayer/video/c$d;Landroidx/media3/exoplayer/video/VideoSink$a;)V

    return-void
.end method
