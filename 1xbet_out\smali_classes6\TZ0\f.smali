.class public final synthetic LTZ0/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LTZ0/h;


# direct methods
.method public synthetic constructor <init>(LTZ0/h;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LTZ0/f;->a:LTZ0/h;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LTZ0/f;->a:LTZ0/h;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, p1}, LTZ0/h;->P(LTZ0/h;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
