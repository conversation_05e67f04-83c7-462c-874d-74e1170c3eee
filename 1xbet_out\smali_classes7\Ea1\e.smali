.class public final LEa1/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a!\u0010\u0006\u001a\u00020\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;",
        "aggregatorTournamentCardsCollectionType",
        "LHX0/e;",
        "resourceManager",
        "Lk21/f;",
        "a",
        "(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;LHX0/e;)Lk21/f;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;LHX0/e;)Lk21/f;
    .locals 6
    .param p0    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;->ColorGradientL:Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;

    .line 2
    .line 3
    if-eq p1, v0, :cond_2

    .line 4
    .line 5
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;->ColorGradientS:Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;

    .line 6
    .line 7
    if-ne p1, v0, :cond_0

    .line 8
    .line 9
    goto :goto_1

    .line 10
    :cond_0
    invoke-interface {p2}, LHX0/e;->c()Z

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    if-eqz p1, :cond_1

    .line 15
    .line 16
    const-string p1, "dd.MM.yyyy, HH:mm"

    .line 17
    .line 18
    :goto_0
    move-object v2, p1

    .line 19
    goto :goto_2

    .line 20
    :cond_1
    const-string p1, "dd.MM.yyyy, hh:mm a"

    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_2
    :goto_1
    const-string p1, "dd.MM.yyyy"

    .line 24
    .line 25
    goto :goto_0

    .line 26
    :goto_2
    sget-object v0, Ll8/b;->a:Ll8/b;

    .line 27
    .line 28
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->e()Ljava/util/Date;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    const/4 v4, 0x4

    .line 33
    const/4 v5, 0x0

    .line 34
    const/4 v3, 0x0

    .line 35
    invoke-static/range {v0 .. v5}, Ll8/b;->h(Ll8/b;Ljava/util/Date;Ljava/lang/String;Ljava/util/Locale;ILjava/lang/Object;)Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->c()Ljava/util/Date;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    invoke-static/range {v0 .. v5}, Ll8/b;->h(Ll8/b;Ljava/util/Date;Ljava/lang/String;Ljava/util/Locale;ILjava/lang/Object;)Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object p0

    .line 47
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 48
    .line 49
    .line 50
    move-result v0

    .line 51
    const-string v1, ""

    .line 52
    .line 53
    const/4 v2, 0x0

    .line 54
    if-lez v0, :cond_3

    .line 55
    .line 56
    sget v0, Lpb/k;->tournament_title_date_start:I

    .line 57
    .line 58
    new-array v3, v2, [Ljava/lang/Object;

    .line 59
    .line 60
    invoke-interface {p2, v0, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    goto :goto_3

    .line 65
    :cond_3
    move-object v0, v1

    .line 66
    :goto_3
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 67
    .line 68
    .line 69
    move-result v3

    .line 70
    if-lez v3, :cond_4

    .line 71
    .line 72
    sget v1, Lpb/k;->tournament_title_date_end:I

    .line 73
    .line 74
    new-array v2, v2, [Ljava/lang/Object;

    .line 75
    .line 76
    invoke-interface {p2, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object v1

    .line 80
    :cond_4
    new-instance p2, Lk21/f;

    .line 81
    .line 82
    invoke-direct {p2, v0, p1, v1, p0}, Lk21/f;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 83
    .line 84
    .line 85
    return-object p2
.end method
