.class public final synthetic Lorg/xbet/uikit_web_games/game_card/itemview/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/c;->a:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/c;->a:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    invoke-static {v0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)Lorg/xbet/uikit/utils/z;

    move-result-object v0

    return-object v0
.end method
