.class public final Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;
.super Lorg/xbet/ui_common/paging/BasePagingSource;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/xbet/ui_common/paging/BasePagingSource<",
        "Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;",
        "Ly81/a;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000V\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0003\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u0000 (2\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001:\u0001)B!\u0008\u0007\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ8\u0010\u0010\u001a\u001c\u0012\u0006\u0012\u0004\u0018\u00010\u0002\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u000f0\u000e2\u000c\u0010\r\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u000cH\u0096@\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J#\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u000f2\u0006\u0010\u0013\u001a\u00020\u0012H\u0016\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u001f\u0010\u0018\u001a\u00020\u0017*\u0004\u0018\u00010\u00022\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0002H\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J%\u0010\u001c\u001a\u0004\u0018\u00010\u00022\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u001aH\u0016\u00a2\u0006\u0004\u0008\u001c\u0010\u001dR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001fR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010!R\u001b\u0010\'\u001a\u00020\"8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008#\u0010$\u001a\u0004\u0008%\u0010&\u00a8\u0006*"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;",
        "Lorg/xbet/ui_common/paging/BasePagingSource;",
        "Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;",
        "Ly81/a;",
        "Lc8/h;",
        "requestParamsDataSource",
        "LS8/a;",
        "profileLocalDataSource",
        "Lf8/g;",
        "serviceGenerator",
        "<init>",
        "(Lc8/h;LS8/a;Lf8/g;)V",
        "Landroidx/paging/PagingSource$a;",
        "params",
        "Lkotlin/Pair;",
        "Landroidx/paging/PagingSource$b;",
        "l",
        "(Landroidx/paging/PagingSource$a;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "throwable",
        "j",
        "(Ljava/lang/Throwable;)Landroidx/paging/PagingSource$b;",
        "nextKey",
        "",
        "p",
        "(Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;)Z",
        "Landroidx/paging/M;",
        "state",
        "n",
        "(Landroidx/paging/M;)Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;",
        "b",
        "Lc8/h;",
        "c",
        "LS8/a;",
        "Lva1/a;",
        "d",
        "Lkotlin/j;",
        "o",
        "()Lva1/a;",
        "service",
        "e",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final e:Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final b:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LS8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->e:Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$a;

    return-void
.end method

.method public constructor <init>(Lc8/h;LS8/a;Lf8/g;)V
    .locals 0
    .param p1    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LS8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/paging/BasePagingSource;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->b:Lc8/h;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->c:LS8/a;

    .line 7
    .line 8
    new-instance p1, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/a;

    .line 9
    .line 10
    invoke-direct {p1, p3}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/a;-><init>(Lf8/g;)V

    .line 11
    .line 12
    .line 13
    invoke-static {p1}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->d:Lkotlin/j;

    .line 18
    .line 19
    return-void
.end method

.method public static synthetic m(Lf8/g;)Lva1/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->q(Lf8/g;)Lva1/a;

    move-result-object p0

    return-object p0
.end method

.method public static final q(Lf8/g;)Lva1/a;
    .locals 1

    .line 1
    const-class v0, Lva1/a;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {p0, v0}, Lf8/g;->c(Lkotlin/reflect/d;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    check-cast p0, Lva1/a;

    .line 12
    .line 13
    return-object p0
.end method


# virtual methods
.method public bridge synthetic e(Landroidx/paging/M;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->n(Landroidx/paging/M;)Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public j(Ljava/lang/Throwable;)Landroidx/paging/PagingSource$b;
    .locals 1
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Throwable;",
            ")",
            "Landroidx/paging/PagingSource$b<",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;",
            "Ly81/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Landroidx/paging/PagingSource$b$a;

    .line 2
    .line 3
    invoke-direct {v0, p1}, Landroidx/paging/PagingSource$b$a;-><init>(Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public bridge synthetic k(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;

    .line 2
    .line 3
    check-cast p2, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->p(Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public l(Landroidx/paging/PagingSource$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 19
    .param p1    # Landroidx/paging/PagingSource$a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/paging/PagingSource$a<",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Pair<",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;",
            "+",
            "Landroidx/paging/PagingSource$b<",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;",
            "Ly81/a;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p2

    .line 4
    .line 5
    instance-of v2, v1, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    move-object v2, v1

    .line 10
    check-cast v2, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;

    .line 11
    .line 12
    iget v3, v2, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;->label:I

    .line 13
    .line 14
    const/high16 v4, -0x80000000

    .line 15
    .line 16
    and-int v5, v3, v4

    .line 17
    .line 18
    if-eqz v5, :cond_0

    .line 19
    .line 20
    sub-int/2addr v3, v4

    .line 21
    iput v3, v2, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;->label:I

    .line 22
    .line 23
    :goto_0
    move-object v15, v2

    .line 24
    goto :goto_1

    .line 25
    :cond_0
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;

    .line 26
    .line 27
    invoke-direct {v2, v0, v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;Lkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    goto :goto_0

    .line 31
    :goto_1
    iget-object v1, v15, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;->result:Ljava/lang/Object;

    .line 32
    .line 33
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    iget v3, v15, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;->label:I

    .line 38
    .line 39
    const/16 v4, 0x10

    .line 40
    .line 41
    const/4 v5, 0x1

    .line 42
    const/16 v16, 0x0

    .line 43
    .line 44
    if-eqz v3, :cond_2

    .line 45
    .line 46
    if-ne v3, v5, :cond_1

    .line 47
    .line 48
    iget v2, v15, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;->I$1:I

    .line 49
    .line 50
    iget v3, v15, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;->I$0:I

    .line 51
    .line 52
    iget-object v5, v15, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;->L$1:Ljava/lang/Object;

    .line 53
    .line 54
    check-cast v5, Ljava/lang/Boolean;

    .line 55
    .line 56
    iget-object v6, v15, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 57
    .line 58
    check-cast v6, Landroidx/paging/PagingSource$a;

    .line 59
    .line 60
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 61
    .line 62
    .line 63
    move-object v4, v5

    .line 64
    move v5, v3

    .line 65
    move-object v3, v1

    .line 66
    const/16 v1, 0x10

    .line 67
    .line 68
    goto/16 :goto_6

    .line 69
    .line 70
    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 71
    .line 72
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 73
    .line 74
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 75
    .line 76
    .line 77
    throw v1

    .line 78
    :cond_2
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 79
    .line 80
    .line 81
    invoke-virtual/range {p1 .. p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object v1

    .line 85
    check-cast v1, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;

    .line 86
    .line 87
    const/4 v3, 0x0

    .line 88
    if-eqz v1, :cond_3

    .line 89
    .line 90
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;->a()I

    .line 91
    .line 92
    .line 93
    move-result v1

    .line 94
    goto :goto_2

    .line 95
    :cond_3
    const/4 v1, 0x0

    .line 96
    :goto_2
    invoke-virtual/range {p1 .. p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 97
    .line 98
    .line 99
    move-result-object v6

    .line 100
    check-cast v6, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;

    .line 101
    .line 102
    if-eqz v6, :cond_4

    .line 103
    .line 104
    invoke-virtual {v6}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;->b()I

    .line 105
    .line 106
    .line 107
    move-result v3

    .line 108
    :cond_4
    invoke-virtual/range {p1 .. p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 109
    .line 110
    .line 111
    move-result-object v6

    .line 112
    check-cast v6, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;

    .line 113
    .line 114
    if-eqz v6, :cond_5

    .line 115
    .line 116
    invoke-virtual {v6}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;->c()Ljava/lang/Boolean;

    .line 117
    .line 118
    .line 119
    move-result-object v6

    .line 120
    goto :goto_3

    .line 121
    :cond_5
    move-object/from16 v6, v16

    .line 122
    .line 123
    :goto_3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->o()Lva1/a;

    .line 124
    .line 125
    .line 126
    move-result-object v7

    .line 127
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->b:Lc8/h;

    .line 128
    .line 129
    invoke-interface {v8}, Lc8/h;->d()I

    .line 130
    .line 131
    .line 132
    move-result v8

    .line 133
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->b:Lc8/h;

    .line 134
    .line 135
    invoke-interface {v9}, Lc8/h;->f()I

    .line 136
    .line 137
    .line 138
    move-result v9

    .line 139
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->c:LS8/a;

    .line 140
    .line 141
    invoke-virtual {v10}, LS8/a;->b()Le9/a;

    .line 142
    .line 143
    .line 144
    move-result-object v10

    .line 145
    if-eqz v10, :cond_6

    .line 146
    .line 147
    invoke-virtual {v10}, Le9/a;->w()Ljava/lang/String;

    .line 148
    .line 149
    .line 150
    move-result-object v10

    .line 151
    if-eqz v10, :cond_6

    .line 152
    .line 153
    invoke-static {v10}, Lkotlin/text/StringsKt;->toIntOrNull(Ljava/lang/String;)Ljava/lang/Integer;

    .line 154
    .line 155
    .line 156
    move-result-object v10

    .line 157
    goto :goto_4

    .line 158
    :cond_6
    move-object/from16 v10, v16

    .line 159
    .line 160
    :goto_4
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->b:Lc8/h;

    .line 161
    .line 162
    invoke-interface {v11}, Lc8/h;->b()I

    .line 163
    .line 164
    .line 165
    move-result v11

    .line 166
    iget-object v12, v0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->b:Lc8/h;

    .line 167
    .line 168
    invoke-interface {v12}, Lc8/h;->c()Ljava/lang/String;

    .line 169
    .line 170
    .line 171
    move-result-object v12

    .line 172
    invoke-static {v5}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 173
    .line 174
    .line 175
    move-result-object v13

    .line 176
    invoke-static {v6, v13}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 177
    .line 178
    .line 179
    move-result v13

    .line 180
    if-eqz v13, :cond_7

    .line 181
    .line 182
    invoke-static {v5}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 183
    .line 184
    .line 185
    move-result-object v13

    .line 186
    goto :goto_5

    .line 187
    :cond_7
    move-object/from16 v13, v16

    .line 188
    .line 189
    :goto_5
    iget-object v14, v0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->b:Lc8/h;

    .line 190
    .line 191
    invoke-interface {v14}, Lc8/h;->getGroupId()I

    .line 192
    .line 193
    .line 194
    move-result v14

    .line 195
    invoke-static {v14}, LHc/a;->e(I)Ljava/lang/Integer;

    .line 196
    .line 197
    .line 198
    move-result-object v14

    .line 199
    move/from16 v17, v9

    .line 200
    .line 201
    move-object v9, v12

    .line 202
    invoke-static {v4}, LHc/a;->e(I)Ljava/lang/Integer;

    .line 203
    .line 204
    .line 205
    move-result-object v12

    .line 206
    move-object/from16 v18, v7

    .line 207
    .line 208
    move-object v7, v10

    .line 209
    move-object v10, v13

    .line 210
    invoke-static {v3}, LHc/a;->e(I)Ljava/lang/Integer;

    .line 211
    .line 212
    .line 213
    move-result-object v13

    .line 214
    move-object/from16 p2, v14

    .line 215
    .line 216
    move-object/from16 v14, p1

    .line 217
    .line 218
    iput-object v14, v15, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 219
    .line 220
    iput-object v6, v15, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;->L$1:Ljava/lang/Object;

    .line 221
    .line 222
    iput v1, v15, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;->I$0:I

    .line 223
    .line 224
    iput v3, v15, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;->I$1:I

    .line 225
    .line 226
    iput v5, v15, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource$loadNextPage$1;->label:I

    .line 227
    .line 228
    const/4 v14, 0x0

    .line 229
    move/from16 v4, v17

    .line 230
    .line 231
    move/from16 v17, v3

    .line 232
    .line 233
    move-object/from16 v3, v18

    .line 234
    .line 235
    move-object/from16 v18, v6

    .line 236
    .line 237
    move v6, v4

    .line 238
    move v5, v1

    .line 239
    move v4, v8

    .line 240
    move v8, v11

    .line 241
    const/16 v1, 0x10

    .line 242
    .line 243
    move-object/from16 v11, p2

    .line 244
    .line 245
    invoke-interface/range {v3 .. v15}, Lva1/a;->c(IIILjava/lang/Integer;ILjava/lang/String;Ljava/lang/Boolean;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 246
    .line 247
    .line 248
    move-result-object v3

    .line 249
    if-ne v3, v2, :cond_8

    .line 250
    .line 251
    return-object v2

    .line 252
    :cond_8
    move-object/from16 v6, p1

    .line 253
    .line 254
    move/from16 v2, v17

    .line 255
    .line 256
    move-object/from16 v4, v18

    .line 257
    .line 258
    :goto_6
    check-cast v3, LV81/c;

    .line 259
    .line 260
    invoke-static {v3}, LV81/d;->a(LV81/c;)LV81/c;

    .line 261
    .line 262
    .line 263
    move-result-object v3

    .line 264
    check-cast v3, LV81/a;

    .line 265
    .line 266
    invoke-virtual {v3}, LV81/a;->d()LV81/a$a;

    .line 267
    .line 268
    .line 269
    move-result-object v3

    .line 270
    if-eqz v3, :cond_9

    .line 271
    .line 272
    invoke-virtual {v3}, LV81/a$a;->a()Ljava/util/List;

    .line 273
    .line 274
    .line 275
    move-result-object v3

    .line 276
    goto :goto_7

    .line 277
    :cond_9
    move-object/from16 v3, v16

    .line 278
    .line 279
    :goto_7
    if-eqz v3, :cond_c

    .line 280
    .line 281
    invoke-interface {v3}, Ljava/util/Collection;->isEmpty()Z

    .line 282
    .line 283
    .line 284
    move-result v7

    .line 285
    if-eqz v7, :cond_a

    .line 286
    .line 287
    goto :goto_8

    .line 288
    :cond_a
    invoke-interface {v3}, Ljava/util/List;->size()I

    .line 289
    .line 290
    .line 291
    move-result v7

    .line 292
    if-ge v7, v1, :cond_b

    .line 293
    .line 294
    goto :goto_8

    .line 295
    :cond_b
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;

    .line 296
    .line 297
    add-int/lit8 v7, v2, 0x10

    .line 298
    .line 299
    invoke-direct {v1, v5, v7, v4}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;-><init>(IILjava/lang/Boolean;)V

    .line 300
    .line 301
    .line 302
    goto :goto_9

    .line 303
    :cond_c
    :goto_8
    move-object/from16 v1, v16

    .line 304
    .line 305
    :goto_9
    new-instance v4, Landroidx/paging/PagingSource$b$c;

    .line 306
    .line 307
    if-nez v3, :cond_d

    .line 308
    .line 309
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 310
    .line 311
    .line 312
    move-result-object v3

    .line 313
    :cond_d
    if-nez v2, :cond_e

    .line 314
    .line 315
    :goto_a
    move-object/from16 v2, v16

    .line 316
    .line 317
    goto :goto_b

    .line 318
    :cond_e
    invoke-virtual {v6}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 319
    .line 320
    .line 321
    move-result-object v2

    .line 322
    move-object/from16 v16, v2

    .line 323
    .line 324
    check-cast v16, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;

    .line 325
    .line 326
    goto :goto_a

    .line 327
    :goto_b
    invoke-direct {v4, v3, v2, v1}, Landroidx/paging/PagingSource$b$c;-><init>(Ljava/util/List;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 328
    .line 329
    .line 330
    invoke-static {v1, v4}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 331
    .line 332
    .line 333
    move-result-object v1

    .line 334
    return-object v1
.end method

.method public n(Landroidx/paging/M;)Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;
    .locals 6
    .param p1    # Landroidx/paging/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/paging/M<",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;",
            "Ly81/a;",
            ">;)",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, Landroidx/paging/M;->d()Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_9

    .line 7
    .line 8
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    invoke-virtual {p1, v0}, Landroidx/paging/M;->c(I)Landroidx/paging/PagingSource$b$c;

    .line 13
    .line 14
    .line 15
    move-result-object v2

    .line 16
    if-eqz v2, :cond_0

    .line 17
    .line 18
    invoke-virtual {v2}, Landroidx/paging/PagingSource$b$c;->i()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    check-cast v2, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_0
    move-object v2, v1

    .line 26
    :goto_0
    invoke-virtual {p1, v0}, Landroidx/paging/M;->c(I)Landroidx/paging/PagingSource$b$c;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    if-eqz v0, :cond_1

    .line 31
    .line 32
    invoke-virtual {v0}, Landroidx/paging/PagingSource$b$c;->f()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;

    .line 37
    .line 38
    goto :goto_1

    .line 39
    :cond_1
    move-object v0, v1

    .line 40
    :goto_1
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;

    .line 41
    .line 42
    const/4 v4, 0x0

    .line 43
    if-eqz v2, :cond_2

    .line 44
    .line 45
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;->a()I

    .line 46
    .line 47
    .line 48
    move-result v5

    .line 49
    goto :goto_2

    .line 50
    :cond_2
    if-eqz v0, :cond_3

    .line 51
    .line 52
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;->a()I

    .line 53
    .line 54
    .line 55
    move-result v5

    .line 56
    goto :goto_2

    .line 57
    :cond_3
    const/4 v5, 0x0

    .line 58
    :goto_2
    if-eqz v2, :cond_4

    .line 59
    .line 60
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;->b()I

    .line 61
    .line 62
    .line 63
    move-result v4

    .line 64
    invoke-virtual {p1}, Landroidx/paging/M;->e()Landroidx/paging/C;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    iget p1, p1, Landroidx/paging/C;->a:I

    .line 69
    .line 70
    add-int/2addr v4, p1

    .line 71
    goto :goto_3

    .line 72
    :cond_4
    if-eqz v0, :cond_5

    .line 73
    .line 74
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;->b()I

    .line 75
    .line 76
    .line 77
    move-result v4

    .line 78
    invoke-virtual {p1}, Landroidx/paging/M;->e()Landroidx/paging/C;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    iget p1, p1, Landroidx/paging/C;->a:I

    .line 83
    .line 84
    sub-int/2addr v4, p1

    .line 85
    :cond_5
    :goto_3
    if-eqz v2, :cond_7

    .line 86
    .line 87
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;->c()Ljava/lang/Boolean;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    if-nez p1, :cond_6

    .line 92
    .line 93
    goto :goto_4

    .line 94
    :cond_6
    move-object v1, p1

    .line 95
    goto :goto_5

    .line 96
    :cond_7
    :goto_4
    if-eqz v0, :cond_8

    .line 97
    .line 98
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;->c()Ljava/lang/Boolean;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    :cond_8
    :goto_5
    invoke-direct {v3, v5, v4, v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;-><init>(IILjava/lang/Boolean;)V

    .line 103
    .line 104
    .line 105
    return-object v3

    .line 106
    :cond_9
    return-object v1
.end method

.method public final o()Lva1/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->d:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lva1/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public p(Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;)Z
    .locals 0

    .line 1
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method
