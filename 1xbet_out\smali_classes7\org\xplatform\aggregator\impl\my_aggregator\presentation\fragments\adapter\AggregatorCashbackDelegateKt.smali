.class public final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/AggregatorCashbackDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a)\u0010\u0006\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00050\u00040\u00032\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "Lkotlin/Function0;",
        "",
        "onCashbackClicked",
        "LA4/c;",
        "",
        "LVX0/i;",
        "f",
        "(Lkotlin/jvm/functions/Function0;)LA4/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LB4/a;Lkotlin/jvm/functions/Function0;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/AggregatorCashbackDelegateKt;->j(LB4/a;Lkotlin/jvm/functions/Function0;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/o1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/AggregatorCashbackDelegateKt;->g(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/o1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/AggregatorCashbackDelegateKt;->i(Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LVX0/i;Ljava/util/List;I)Z
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/AggregatorCashbackDelegateKt;->h(LVX0/i;Ljava/util/List;I)Z

    move-result p0

    return p0
.end method

.method public static synthetic e(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/AggregatorCashbackDelegateKt;->k(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V

    return-void
.end method

.method public static final f(Lkotlin/jvm/functions/Function0;)LA4/c;
    .locals 4
    .param p0    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lpa1/a;

    .line 2
    .line 3
    invoke-direct {v0}, Lpa1/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lpa1/b;

    .line 7
    .line 8
    invoke-direct {v1}, Lpa1/b;-><init>()V

    .line 9
    .line 10
    .line 11
    new-instance v2, Lpa1/c;

    .line 12
    .line 13
    invoke-direct {v2, p0}, Lpa1/c;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/AggregatorCashbackDelegateKt$getAggregatorCashbackDelegate$$inlined$adapterDelegateViewBinding$default$1;->INSTANCE:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/adapter/AggregatorCashbackDelegateKt$getAggregatorCashbackDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, v1, v2, p0}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final g(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/o1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/o1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/o1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final h(LVX0/i;Ljava/util/List;I)Z
    .locals 1

    .line 1
    :try_start_0
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 2
    .line 3
    invoke-static {p1, p2}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    check-cast p1, LVX0/i;

    .line 8
    .line 9
    if-nez p1, :cond_0

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    move-object p0, p1

    .line 13
    :goto_0
    nop

    .line 14
    instance-of p1, p0, Lra1/a;

    .line 15
    .line 16
    if-eqz p1, :cond_1

    .line 17
    .line 18
    check-cast p0, Lra1/a;

    .line 19
    .line 20
    invoke-interface {p0}, Lra1/a;->getType()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 21
    .line 22
    .line 23
    const/4 p0, 0x1

    .line 24
    goto :goto_1

    .line 25
    :cond_1
    const/4 p0, 0x0

    .line 26
    :goto_1
    invoke-static {p0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 27
    .line 28
    .line 29
    move-result-object p0

    .line 30
    invoke-static {p0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 34
    goto :goto_2

    .line 35
    :catchall_0
    move-exception p0

    .line 36
    sget-object p1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 37
    .line 38
    invoke-static {p0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object p0

    .line 42
    invoke-static {p0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    move-result-object p0

    .line 46
    :goto_2
    sget-object p1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 47
    .line 48
    invoke-static {p0}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 49
    .line 50
    .line 51
    move-result p2

    .line 52
    if-eqz p2, :cond_2

    .line 53
    .line 54
    move-object p0, p1

    .line 55
    :cond_2
    check-cast p0, Ljava/lang/Boolean;

    .line 56
    .line 57
    invoke-virtual {p0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 58
    .line 59
    .line 60
    move-result p0

    .line 61
    return p0
.end method

.method public static final i(Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, Lpa1/d;

    .line 2
    .line 3
    invoke-direct {v0, p1, p0}, Lpa1/d;-><init>(LB4/a;Lkotlin/jvm/functions/Function0;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p1, v0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 7
    .line 8
    .line 9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    return-object p0
.end method

.method public static final j(LB4/a;Lkotlin/jvm/functions/Function0;Ljava/util/List;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, LS91/o1;

    .line 6
    .line 7
    iget-object p2, p2, LS91/o1;->b:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, Lra1/a;

    .line 14
    .line 15
    invoke-interface {v0}, Lra1/a;->getType()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p2, v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->setType(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;)V

    .line 20
    .line 21
    .line 22
    new-instance v0, Lpa1/e;

    .line 23
    .line 24
    invoke-direct {v0, p1}, Lpa1/e;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 25
    .line 26
    .line 27
    invoke-virtual {p2, v0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p0

    .line 34
    check-cast p0, Lra1/a;

    .line 35
    .line 36
    instance-of p1, p0, Lra1/a$a;

    .line 37
    .line 38
    if-eqz p1, :cond_0

    .line 39
    .line 40
    check-cast p0, Lra1/a$a;

    .line 41
    .line 42
    invoke-virtual {p0}, Lra1/a$a;->d()Lg31/h;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    invoke-virtual {p0}, Lra1/a$a;->e()Ljava/util/List;

    .line 47
    .line 48
    .line 49
    move-result-object p0

    .line 50
    invoke-virtual {p2, p1, p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->setCashbackModel(Lg31/h;Ljava/util/List;)V

    .line 51
    .line 52
    .line 53
    goto :goto_0

    .line 54
    :cond_0
    instance-of p0, p0, Lra1/a$c;

    .line 55
    .line 56
    if-eqz p0, :cond_1

    .line 57
    .line 58
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->e()V

    .line 59
    .line 60
    .line 61
    :goto_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 62
    .line 63
    return-object p0

    .line 64
    :cond_1
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 65
    .line 66
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 67
    .line 68
    .line 69
    throw p0
.end method

.method public static final k(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    return-void
.end method
