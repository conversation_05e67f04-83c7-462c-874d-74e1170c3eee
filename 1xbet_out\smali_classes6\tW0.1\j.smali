.class public final LtW0/j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00a8\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u00084\u0018\u00002\u00020\u0001B\u00d1\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u0012\u0006\u0010/\u001a\u00020.\u0012\u0006\u00101\u001a\u000200\u0012\u0006\u00103\u001a\u000202\u00a2\u0006\u0004\u00084\u00105J\r\u00107\u001a\u000206\u00a2\u0006\u0004\u00087\u00108R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00087\u00109R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u0010;R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010?R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010AR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010CR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010ER\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010GR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008N\u0010OR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010QR\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008R\u0010SR\u0014\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008T\u0010UR\u0014\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008V\u0010WR\u0014\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008X\u0010YR\u0014\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Z\u0010[R\u0014\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\\\u0010]R\u0014\u0010)\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008^\u0010_R\u0014\u0010+\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008`\u0010aR\u0014\u0010-\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008b\u0010cR\u0014\u0010/\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008d\u0010eR\u0014\u00101\u001a\u0002008\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008f\u0010gR\u0014\u00103\u001a\u0002028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008h\u0010i\u00a8\u0006j"
    }
    d2 = {
        "LtW0/j;",
        "LQW0/a;",
        "LQW0/c;",
        "coroutinesLib",
        "Lak/a;",
        "balanceFeature",
        "LSX0/a;",
        "lottieConfigurator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "LwX0/a;",
        "appScreensProvider",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LwW0/a;",
        "getAvailableTotoTypesUseCase",
        "LwW0/c;",
        "getCacheJackpotTiragUseCase",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;",
        "clearOutcomesUseCase",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;",
        "hasTiragUseCase",
        "LwW0/e;",
        "getJackpotTypeUseCase",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;",
        "getOutcomesSubscriptionUseCase",
        "Lorg/xbet/toto_jackpot/impl/domain/scenario/a;",
        "checkCorrectBetSumScenario",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;",
        "getTiragSubscriptionUseCase",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;",
        "setOutcomesUseCase",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;",
        "getJackpotTiragUseCase",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;",
        "getChampionshipsGroupByChampIdUseCase",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;",
        "randomizeOutcomesUseCase",
        "LwW0/i;",
        "getOutcomesUseCase",
        "LwW0/o;",
        "setHasTiragUseCase",
        "LwW0/m;",
        "setHasCacheUseCase",
        "LTZ0/a;",
        "actionDialogManager",
        "LHX0/e;",
        "resourceManager",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "LfX/b;",
        "testRepository",
        "<init>",
        "(LQW0/c;Lak/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;LwW0/e;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/a;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;LwW0/i;LwW0/o;LwW0/m;LTZ0/a;LHX0/e;Lorg/xbet/remoteconfig/domain/usecases/i;LfX/b;)V",
        "LtW0/i;",
        "a",
        "()LtW0/i;",
        "LQW0/c;",
        "b",
        "Lak/a;",
        "c",
        "LSX0/a;",
        "d",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "e",
        "LwX0/a;",
        "f",
        "Lorg/xbet/ui_common/utils/M;",
        "g",
        "LwW0/a;",
        "h",
        "LwW0/c;",
        "i",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;",
        "j",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;",
        "k",
        "LwW0/e;",
        "l",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;",
        "m",
        "Lorg/xbet/toto_jackpot/impl/domain/scenario/a;",
        "n",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;",
        "o",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;",
        "p",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;",
        "q",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;",
        "r",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;",
        "s",
        "LwW0/i;",
        "t",
        "LwW0/o;",
        "u",
        "LwW0/m;",
        "v",
        "LTZ0/a;",
        "w",
        "LHX0/e;",
        "x",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "y",
        "LfX/b;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LQW0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lak/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LwW0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:LwW0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:LwW0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Lorg/xbet/toto_jackpot/impl/domain/scenario/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final r:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final s:LwW0/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final t:LwW0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final u:LwW0/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v:LTZ0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y:LfX/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lak/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;LwW0/e;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/a;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;LwW0/i;LwW0/o;LwW0/m;LTZ0/a;LHX0/e;Lorg/xbet/remoteconfig/domain/usecases/i;LfX/b;)V
    .locals 0
    .param p1    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lak/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LwW0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LwW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LwW0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lorg/xbet/toto_jackpot/impl/domain/scenario/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # LwW0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # LwW0/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # LwW0/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # LTZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # LfX/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LtW0/j;->a:LQW0/c;

    .line 5
    .line 6
    iput-object p2, p0, LtW0/j;->b:Lak/a;

    .line 7
    .line 8
    iput-object p3, p0, LtW0/j;->c:LSX0/a;

    .line 9
    .line 10
    iput-object p4, p0, LtW0/j;->d:Lorg/xbet/ui_common/utils/internet/a;

    .line 11
    .line 12
    iput-object p5, p0, LtW0/j;->e:LwX0/a;

    .line 13
    .line 14
    iput-object p6, p0, LtW0/j;->f:Lorg/xbet/ui_common/utils/M;

    .line 15
    .line 16
    iput-object p7, p0, LtW0/j;->g:LwW0/a;

    .line 17
    .line 18
    iput-object p8, p0, LtW0/j;->h:LwW0/c;

    .line 19
    .line 20
    iput-object p9, p0, LtW0/j;->i:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;

    .line 21
    .line 22
    iput-object p10, p0, LtW0/j;->j:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;

    .line 23
    .line 24
    iput-object p11, p0, LtW0/j;->k:LwW0/e;

    .line 25
    .line 26
    iput-object p12, p0, LtW0/j;->l:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;

    .line 27
    .line 28
    iput-object p13, p0, LtW0/j;->m:Lorg/xbet/toto_jackpot/impl/domain/scenario/a;

    .line 29
    .line 30
    iput-object p14, p0, LtW0/j;->n:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;

    .line 31
    .line 32
    iput-object p15, p0, LtW0/j;->o:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, LtW0/j;->p:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, LtW0/j;->q:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, LtW0/j;->r:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, LtW0/j;->s:LwW0/i;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, LtW0/j;->t:LwW0/o;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, LtW0/j;->u:LwW0/m;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, LtW0/j;->v:LTZ0/a;

    .line 61
    .line 62
    move-object/from16 p1, p23

    .line 63
    .line 64
    iput-object p1, p0, LtW0/j;->w:LHX0/e;

    .line 65
    .line 66
    move-object/from16 p1, p24

    .line 67
    .line 68
    iput-object p1, p0, LtW0/j;->x:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 69
    .line 70
    move-object/from16 p1, p25

    .line 71
    .line 72
    iput-object p1, p0, LtW0/j;->y:LfX/b;

    .line 73
    .line 74
    return-void
.end method


# virtual methods
.method public final a()LtW0/i;
    .locals 28
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, LtW0/a;->a()LtW0/i$a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, v0, LtW0/j;->a:LQW0/c;

    .line 8
    .line 9
    iget-object v3, v0, LtW0/j;->b:Lak/a;

    .line 10
    .line 11
    iget-object v5, v0, LtW0/j;->c:LSX0/a;

    .line 12
    .line 13
    iget-object v6, v0, LtW0/j;->d:Lorg/xbet/ui_common/utils/internet/a;

    .line 14
    .line 15
    iget-object v7, v0, LtW0/j;->e:LwX0/a;

    .line 16
    .line 17
    iget-object v8, v0, LtW0/j;->f:Lorg/xbet/ui_common/utils/M;

    .line 18
    .line 19
    iget-object v9, v0, LtW0/j;->g:LwW0/a;

    .line 20
    .line 21
    iget-object v10, v0, LtW0/j;->k:LwW0/e;

    .line 22
    .line 23
    iget-object v11, v0, LtW0/j;->j:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;

    .line 24
    .line 25
    iget-object v12, v0, LtW0/j;->i:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;

    .line 26
    .line 27
    iget-object v13, v0, LtW0/j;->h:LwW0/c;

    .line 28
    .line 29
    iget-object v14, v0, LtW0/j;->l:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;

    .line 30
    .line 31
    iget-object v15, v0, LtW0/j;->m:Lorg/xbet/toto_jackpot/impl/domain/scenario/a;

    .line 32
    .line 33
    iget-object v4, v0, LtW0/j;->n:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;

    .line 34
    .line 35
    move-object/from16 v16, v1

    .line 36
    .line 37
    iget-object v1, v0, LtW0/j;->o:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;

    .line 38
    .line 39
    move-object/from16 v17, v1

    .line 40
    .line 41
    iget-object v1, v0, LtW0/j;->p:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;

    .line 42
    .line 43
    move-object/from16 v18, v1

    .line 44
    .line 45
    move-object/from16 v1, v16

    .line 46
    .line 47
    move-object/from16 v16, v4

    .line 48
    .line 49
    iget-object v4, v0, LtW0/j;->v:LTZ0/a;

    .line 50
    .line 51
    move-object/from16 v19, v1

    .line 52
    .line 53
    iget-object v1, v0, LtW0/j;->q:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;

    .line 54
    .line 55
    move-object/from16 v20, v1

    .line 56
    .line 57
    iget-object v1, v0, LtW0/j;->r:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;

    .line 58
    .line 59
    move-object/from16 v21, v1

    .line 60
    .line 61
    iget-object v1, v0, LtW0/j;->s:LwW0/i;

    .line 62
    .line 63
    move-object/from16 v22, v1

    .line 64
    .line 65
    iget-object v1, v0, LtW0/j;->t:LwW0/o;

    .line 66
    .line 67
    move-object/from16 v23, v1

    .line 68
    .line 69
    iget-object v1, v0, LtW0/j;->u:LwW0/m;

    .line 70
    .line 71
    move-object/from16 v24, v1

    .line 72
    .line 73
    iget-object v1, v0, LtW0/j;->w:LHX0/e;

    .line 74
    .line 75
    move-object/from16 v25, v1

    .line 76
    .line 77
    iget-object v1, v0, LtW0/j;->x:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 78
    .line 79
    move-object/from16 v26, v1

    .line 80
    .line 81
    iget-object v1, v0, LtW0/j;->y:LfX/b;

    .line 82
    .line 83
    move-object/from16 v27, v26

    .line 84
    .line 85
    move-object/from16 v26, v1

    .line 86
    .line 87
    move-object/from16 v1, v19

    .line 88
    .line 89
    move-object/from16 v19, v20

    .line 90
    .line 91
    move-object/from16 v20, v21

    .line 92
    .line 93
    move-object/from16 v21, v22

    .line 94
    .line 95
    move-object/from16 v22, v23

    .line 96
    .line 97
    move-object/from16 v23, v24

    .line 98
    .line 99
    move-object/from16 v24, v25

    .line 100
    .line 101
    move-object/from16 v25, v27

    .line 102
    .line 103
    invoke-interface/range {v1 .. v26}, LtW0/i$a;->a(LQW0/c;Lak/a;LTZ0/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;LwW0/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/a;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;LwW0/i;LwW0/o;LwW0/m;LHX0/e;Lorg/xbet/remoteconfig/domain/usecases/i;LfX/b;)LtW0/i;

    .line 104
    .line 105
    .line 106
    move-result-object v1

    .line 107
    return-object v1
.end method
