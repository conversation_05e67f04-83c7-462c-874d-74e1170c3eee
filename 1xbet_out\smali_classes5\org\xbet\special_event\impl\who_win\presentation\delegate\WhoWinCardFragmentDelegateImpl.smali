.class public final Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u000e\u0008\u0001\u0018\u00002\u00020\u0001B1\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\'\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u0012H\u0016\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u001f\u0010\u0019\u001a\u00020\u00142\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0018\u001a\u00020\u0017H\u0016\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\'\u0010\u001f\u001a\u00020\u00142\u0006\u0010\u001c\u001a\u00020\u001b2\u0006\u0010\u001e\u001a\u00020\u001d2\u0006\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 J\u001f\u0010\"\u001a\u00020\u00142\u0006\u0010!\u001a\u00020\u001d2\u0006\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\"\u0010#R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010$R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008%\u0010&R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010(R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010)R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010*\u00a8\u0006+"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;",
        "Lqa0/a;",
        "makeBetDialogsManager",
        "Lf90/a;",
        "makeBetBottomSheetProvider",
        "Lww/a;",
        "addEventToCouponDelegate",
        "Lzg/a;",
        "betAnalytics",
        "LqR/a;",
        "betFatmanLogger",
        "<init>",
        "(Lqa0/a;Lf90/a;Lww/a;Lzg/a;LqR/a;)V",
        "Landroidx/fragment/app/Fragment;",
        "fragment",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;",
        "whoWinCardViewModel",
        "Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;",
        "entryPointType",
        "",
        "a",
        "(Landroidx/fragment/app/Fragment;Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V",
        "LNn/d;",
        "configureCouponResultModel",
        "d",
        "(Landroidx/fragment/app/Fragment;LNn/d;)V",
        "Landroidx/fragment/app/FragmentManager;",
        "fragmentManager",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;",
        "event",
        "g",
        "(Landroidx/fragment/app/FragmentManager;Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V",
        "betState",
        "e",
        "(Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V",
        "Lqa0/a;",
        "b",
        "Lf90/a;",
        "c",
        "Lww/a;",
        "Lzg/a;",
        "LqR/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lqa0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lf90/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lww/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lzg/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LqR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lqa0/a;Lf90/a;Lww/a;Lzg/a;LqR/a;)V
    .locals 0
    .param p1    # Lqa0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lf90/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lww/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lzg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LqR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->a:Lqa0/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->b:Lf90/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->c:Lww/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->d:Lzg/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->e:LqR/a;

    .line 13
    .line 14
    return-void
.end method

.method public static synthetic b(Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;Lorg/xbet/betting/core/coupon/models/SingleBetGame;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->f(Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;Lorg/xbet/betting/core/coupon/models/SingleBetGame;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic c(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;Landroidx/fragment/app/FragmentManager;Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->g(Landroidx/fragment/app/FragmentManager;Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final f(Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;Lorg/xbet/betting/core/coupon/models/SingleBetGame;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0, p2, p1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;->f2(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method


# virtual methods
.method public a(Landroidx/fragment/app/Fragment;Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V
    .locals 12
    .param p1    # Landroidx/fragment/app/Fragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->c:Lww/a;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/c;

    .line 4
    .line 5
    invoke-direct {v1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/c;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1, v1}, Lww/a;->b(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    invoke-interface {p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;->A1()Lkotlinx/coroutines/flow/e;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    new-instance v4, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;

    .line 16
    .line 17
    const/4 v9, 0x0

    .line 18
    move-object v5, p0

    .line 19
    move-object v6, p1

    .line 20
    move-object v8, p2

    .line 21
    move-object v7, p3

    .line 22
    invoke-direct/range {v4 .. v9}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;Landroidx/fragment/app/Fragment;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    sget-object v5, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 26
    .line 27
    invoke-static {v6}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-static {p1}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 32
    .line 33
    .line 34
    move-result-object p2

    .line 35
    new-instance v2, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$$inlined$observeWithLifecycle$default$1;

    .line 36
    .line 37
    const/4 v7, 0x0

    .line 38
    move-object v6, v4

    .line 39
    move-object v4, p1

    .line 40
    invoke-direct/range {v2 .. v7}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 41
    .line 42
    .line 43
    const/4 v10, 0x3

    .line 44
    const/4 v11, 0x0

    .line 45
    const/4 v8, 0x0

    .line 46
    move-object v6, p2

    .line 47
    move-object v9, v2

    .line 48
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 49
    .line 50
    .line 51
    return-void
.end method

.method public d(Landroidx/fragment/app/Fragment;LNn/d;)V
    .locals 6
    .param p1    # Landroidx/fragment/app/Fragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LNn/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->c:Lww/a;

    .line 2
    .line 3
    const/4 v4, 0x0

    .line 4
    const/4 v5, 0x1

    .line 5
    const/4 v3, 0x0

    .line 6
    move-object v1, p1

    .line 7
    move-object v2, p2

    .line 8
    invoke-interface/range {v0 .. v5}, Lww/a;->a(Landroidx/fragment/app/Fragment;LNn/d;Landroid/view/ViewGroup;Lkotlin/jvm/functions/Function0;Z)Lww/b;

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final e(Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p2

    .line 4
    .line 5
    instance-of v2, v1, Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$SpecialEventScreenSection$SportGamesEntryPoint;

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    move-object v2, v1

    .line 10
    check-cast v2, Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$SpecialEventScreenSection$SportGamesEntryPoint;

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    const/4 v2, 0x0

    .line 14
    :goto_0
    if-eqz v2, :cond_1

    .line 15
    .line 16
    sget-object v3, LHg/d;->b:LHg/d$a;

    .line 17
    .line 18
    check-cast v1, Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$SpecialEventScreenSection$SportGamesEntryPoint;

    .line 19
    .line 20
    invoke-virtual {v3, v1}, LHg/d$a;->a(Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$SpecialEventScreenSection$SportGamesEntryPoint;)Ljava/lang/String;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    :goto_1
    move-object v8, v1

    .line 25
    goto :goto_2

    .line 26
    :cond_1
    invoke-static {v1}, Lzg/i;->a(Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    goto :goto_1

    .line 31
    :goto_2
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->b()Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    iget-object v3, v0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->d:Lzg/a;

    .line 36
    .line 37
    invoke-virtual {v1}, Lorg/xbet/betting/core/coupon/models/SingleBetGame;->getSportId()J

    .line 38
    .line 39
    .line 40
    move-result-wide v4

    .line 41
    invoke-virtual {v1}, Lorg/xbet/betting/core/coupon/models/SingleBetGame;->getSubSportId()J

    .line 42
    .line 43
    .line 44
    move-result-wide v6

    .line 45
    const-wide/16 v13, 0x0

    .line 46
    .line 47
    const-wide/16 v15, -0x1

    .line 48
    .line 49
    cmp-long v9, v6, v13

    .line 50
    .line 51
    if-lez v9, :cond_2

    .line 52
    .line 53
    invoke-virtual {v1}, Lorg/xbet/betting/core/coupon/models/SingleBetGame;->getSubSportId()J

    .line 54
    .line 55
    .line 56
    move-result-wide v6

    .line 57
    goto :goto_3

    .line 58
    :cond_2
    move-wide v6, v15

    .line 59
    :goto_3
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->a()Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    .line 60
    .line 61
    .line 62
    move-result-object v9

    .line 63
    invoke-virtual {v9}, Lorg/xbet/betting/core/zip/model/bet/BetInfo;->getGroupId()J

    .line 64
    .line 65
    .line 66
    move-result-wide v9

    .line 67
    if-eqz v2, :cond_3

    .line 68
    .line 69
    invoke-virtual {v1}, Lorg/xbet/betting/core/coupon/models/SingleBetGame;->getChampId()J

    .line 70
    .line 71
    .line 72
    move-result-wide v11

    .line 73
    goto :goto_4

    .line 74
    :cond_3
    move-wide v11, v15

    .line 75
    :goto_4
    invoke-virtual/range {v3 .. v12}, Lzg/a;->h(JJLjava/lang/String;JJ)V

    .line 76
    .line 77
    .line 78
    iget-object v3, v0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->e:LqR/a;

    .line 79
    .line 80
    invoke-virtual {v1}, Lorg/xbet/betting/core/coupon/models/SingleBetGame;->getSportId()J

    .line 81
    .line 82
    .line 83
    move-result-wide v4

    .line 84
    invoke-virtual {v1}, Lorg/xbet/betting/core/coupon/models/SingleBetGame;->getSubSportId()J

    .line 85
    .line 86
    .line 87
    move-result-wide v6

    .line 88
    cmp-long v9, v6, v13

    .line 89
    .line 90
    if-lez v9, :cond_4

    .line 91
    .line 92
    invoke-virtual {v1}, Lorg/xbet/betting/core/coupon/models/SingleBetGame;->getSubSportId()J

    .line 93
    .line 94
    .line 95
    move-result-wide v6

    .line 96
    goto :goto_5

    .line 97
    :cond_4
    move-wide v6, v15

    .line 98
    :goto_5
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->a()Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    .line 99
    .line 100
    .line 101
    move-result-object v9

    .line 102
    invoke-virtual {v9}, Lorg/xbet/betting/core/zip/model/bet/BetInfo;->getGroupId()J

    .line 103
    .line 104
    .line 105
    move-result-wide v9

    .line 106
    if-eqz v2, :cond_5

    .line 107
    .line 108
    invoke-virtual {v1}, Lorg/xbet/betting/core/coupon/models/SingleBetGame;->getChampId()J

    .line 109
    .line 110
    .line 111
    move-result-wide v15

    .line 112
    :cond_5
    move-wide v11, v15

    .line 113
    invoke-interface/range {v3 .. v12}, LqR/a;->k(JJLjava/lang/String;JJ)V

    .line 114
    .line 115
    .line 116
    return-void
.end method

.method public final g(Landroidx/fragment/app/FragmentManager;Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V
    .locals 2

    .line 1
    invoke-virtual {p0, p2, p3}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->e(Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->c()Z

    .line 5
    .line 6
    .line 7
    move-result v0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->b:Lf90/a;

    .line 11
    .line 12
    invoke-virtual {p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->a()Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    invoke-virtual {p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->b()Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    .line 17
    .line 18
    .line 19
    move-result-object p2

    .line 20
    invoke-interface {v0, p1, v1, p2, p3}, Lf90/a;->b(Landroidx/fragment/app/FragmentManager;Lorg/xbet/betting/core/zip/model/bet/BetInfo;Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V

    .line 21
    .line 22
    .line 23
    return-void

    .line 24
    :cond_0
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->a:Lqa0/a;

    .line 25
    .line 26
    invoke-virtual {p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->b()Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-virtual {p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;->a()Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    .line 31
    .line 32
    .line 33
    move-result-object p2

    .line 34
    invoke-interface {v0, p1, v1, p2, p3}, Lqa0/a;->e(Landroidx/fragment/app/FragmentManager;Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/zip/model/bet/BetInfo;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V

    .line 35
    .line 36
    .line 37
    return-void
.end method
