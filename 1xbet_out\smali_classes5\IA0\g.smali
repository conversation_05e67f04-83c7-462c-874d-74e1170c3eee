.class public final LIA0/g;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LIA0/g$a;,
        LIA0/g$b;,
        LIA0/g$c;,
        LIA0/g$d;,
        LIA0/g$e;,
        LIA0/g$f;,
        LIA0/g$g;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000h\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0008\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010$\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0000\u0018\u00002\u00020\u0001:\u0007\u0008\u000c*\u0012\u0005C#R\u001c\u0010\u0003\u001a\u0004\u0018\u00010\u00028\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0003\u0010\u0004\u001a\u0004\u0008\u0005\u0010\u0006R\u001c\u0010\u0007\u001a\u0004\u0018\u00010\u00028\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0007\u0010\u0004\u001a\u0004\u0008\u0008\u0010\u0006R\u001c\u0010\n\u001a\u0004\u0018\u00010\t8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\n\u0010\u000b\u001a\u0004\u0008\u000c\u0010\rR\"\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u000f\u0018\u00010\u000e8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0010\u0010\u0011\u001a\u0004\u0008\u0012\u0010\u0013R\u001c\u0010\u0015\u001a\u0004\u0018\u00010\u00148\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0015\u0010\u0016\u001a\u0004\u0008\u0017\u0010\u0018R\u001c\u0010\u0019\u001a\u0004\u0018\u00010\u00148\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0019\u0010\u0016\u001a\u0004\u0008\u001a\u0010\u0018R\u001c\u0010\u001b\u001a\u0004\u0018\u00010\u00148\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u001b\u0010\u0016\u001a\u0004\u0008\u001c\u0010\u0018R\u001c\u0010\u001e\u001a\u0004\u0018\u00010\u001d8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u001e\u0010\u001f\u001a\u0004\u0008 \u0010!R\u001c\u0010\"\u001a\u0004\u0018\u00010\u00028\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\"\u0010\u0004\u001a\u0004\u0008#\u0010\u0006R\u001c\u0010%\u001a\u0004\u0018\u00010$8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008%\u0010&\u001a\u0004\u0008\'\u0010(R\u001c\u0010)\u001a\u0004\u0018\u00010\u00028\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008)\u0010\u0004\u001a\u0004\u0008*\u0010\u0006R\u001c\u0010,\u001a\u0004\u0018\u00010+8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008,\u0010-\u001a\u0004\u0008.\u0010/R0\u00102\u001a\u0018\u0012\u0004\u0012\u00020\u0002\u0012\u000c\u0012\n\u0012\u0004\u0012\u000201\u0018\u00010\u000e\u0018\u0001008\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u00082\u00103\u001a\u0004\u00084\u00105R\u001c\u00107\u001a\u0004\u0018\u0001068\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u00087\u00108\u001a\u0004\u00089\u0010:R\u001c\u0010<\u001a\u0004\u0018\u00010;8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008<\u0010=\u001a\u0004\u0008>\u0010?R\u001c\u0010A\u001a\u0004\u0018\u00010@8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008A\u0010B\u001a\u0004\u0008C\u0010D\u00a8\u0006E"
    }
    d2 = {
        "LIA0/g;",
        "",
        "",
        "fullScore",
        "Ljava/lang/String;",
        "c",
        "()Ljava/lang/String;",
        "currentPeriodName",
        "b",
        "LIA0/g$a;",
        "cricketScores",
        "LIA0/g$a;",
        "a",
        "()LIA0/g$a;",
        "",
        "LIA0/g$b;",
        "periodScores",
        "Ljava/util/List;",
        "g",
        "()Ljava/util/List;",
        "",
        "scoreOpp1",
        "Ljava/lang/Integer;",
        "h",
        "()Ljava/lang/Integer;",
        "scoreOpp2",
        "i",
        "serve",
        "j",
        "LIA0/g$d;",
        "subScore",
        "LIA0/g$d;",
        "l",
        "()LIA0/g$d;",
        "periodFullScore",
        "f",
        "LIA0/g$g;",
        "timer",
        "LIA0/g$g;",
        "o",
        "()LIA0/g$g;",
        "info",
        "d",
        "LIA0/g$c;",
        "statistic",
        "LIA0/g$c;",
        "k",
        "()LIA0/g$c;",
        "",
        "LIA0/g$e;",
        "tabloStatistic",
        "Ljava/util/Map;",
        "m",
        "()Ljava/util/Map;",
        "",
        "isBreak",
        "Ljava/lang/Boolean;",
        "p",
        "()Ljava/lang/Boolean;",
        "LIA0/g$f;",
        "tennisScore",
        "LIA0/g$f;",
        "n",
        "()LIA0/g$f;",
        "LJA0/b;",
        "penalty",
        "LJA0/b;",
        "e",
        "()LJA0/b;",
        "core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field private final cricketScores:LIA0/g$a;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "extendedScore"
    .end annotation
.end field

.field private final currentPeriodName:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "currentPeriodName"
    .end annotation
.end field

.field private final fullScore:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "fullScore"
    .end annotation
.end field

.field private final info:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "info"
    .end annotation
.end field

.field private final isBreak:Ljava/lang/Boolean;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "isBreak"
    .end annotation
.end field

.field private final penalty:LJA0/b;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "penalty"
    .end annotation
.end field

.field private final periodFullScore:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "periodScoresStr"
    .end annotation
.end field

.field private final periodScores:Ljava/util/List;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "periodScores"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LIA0/g$b;",
            ">;"
        }
    .end annotation
.end field

.field private final scoreOpp1:Ljava/lang/Integer;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "scoreOpp1"
    .end annotation
.end field

.field private final scoreOpp2:Ljava/lang/Integer;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "scoreOpp2"
    .end annotation
.end field

.field private final serve:Ljava/lang/Integer;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "serve"
    .end annotation
.end field

.field private final statistic:LIA0/g$c;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "statistic"
    .end annotation
.end field

.field private final subScore:LIA0/g$d;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "subScore"
    .end annotation
.end field

.field private final tabloStatistic:Ljava/util/Map;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "tabloStats"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "LIA0/g$e;",
            ">;>;"
        }
    .end annotation
.end field

.field private final tennisScore:LIA0/g$f;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "points"
    .end annotation
.end field

.field private final timer:LIA0/g$g;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "timer"
    .end annotation
.end field


# virtual methods
.method public final a()LIA0/g$a;
    .locals 1

    .line 1
    iget-object v0, p0, LIA0/g;->cricketScores:LIA0/g$a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, LIA0/g;->currentPeriodName:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, LIA0/g;->fullScore:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, LIA0/g;->info:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e()LJA0/b;
    .locals 1

    .line 1
    iget-object v0, p0, LIA0/g;->penalty:LJA0/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final f()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, LIA0/g;->periodFullScore:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "LIA0/g$b;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LIA0/g;->periodScores:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h()Ljava/lang/Integer;
    .locals 1

    .line 1
    iget-object v0, p0, LIA0/g;->scoreOpp1:Ljava/lang/Integer;

    .line 2
    .line 3
    return-object v0
.end method

.method public final i()Ljava/lang/Integer;
    .locals 1

    .line 1
    iget-object v0, p0, LIA0/g;->scoreOpp2:Ljava/lang/Integer;

    .line 2
    .line 3
    return-object v0
.end method

.method public final j()Ljava/lang/Integer;
    .locals 1

    .line 1
    iget-object v0, p0, LIA0/g;->serve:Ljava/lang/Integer;

    .line 2
    .line 3
    return-object v0
.end method

.method public final k()LIA0/g$c;
    .locals 1

    .line 1
    iget-object v0, p0, LIA0/g;->statistic:LIA0/g$c;

    .line 2
    .line 3
    return-object v0
.end method

.method public final l()LIA0/g$d;
    .locals 1

    .line 1
    iget-object v0, p0, LIA0/g;->subScore:LIA0/g$d;

    .line 2
    .line 3
    return-object v0
.end method

.method public final m()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "LIA0/g$e;",
            ">;>;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LIA0/g;->tabloStatistic:Ljava/util/Map;

    .line 2
    .line 3
    return-object v0
.end method

.method public final n()LIA0/g$f;
    .locals 1

    .line 1
    iget-object v0, p0, LIA0/g;->tennisScore:LIA0/g$f;

    .line 2
    .line 3
    return-object v0
.end method

.method public final o()LIA0/g$g;
    .locals 1

    .line 1
    iget-object v0, p0, LIA0/g;->timer:LIA0/g$g;

    .line 2
    .line 3
    return-object v0
.end method

.method public final p()Ljava/lang/Boolean;
    .locals 1

    .line 1
    iget-object v0, p0, LIA0/g;->isBreak:Ljava/lang/Boolean;

    .line 2
    .line 3
    return-object v0
.end method
