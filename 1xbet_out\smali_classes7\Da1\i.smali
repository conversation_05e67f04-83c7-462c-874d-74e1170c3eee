.class public final synthetic LDa1/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:LUX0/k;


# direct methods
.method public synthetic constructor <init>(LB4/a;LUX0/k;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LDa1/i;->a:LB4/a;

    iput-object p2, p0, LDa1/i;->b:LUX0/k;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LDa1/i;->a:LB4/a;

    iget-object v1, p0, LDa1/i;->b:LUX0/k;

    invoke-static {v0, v1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt;->f(LB4/a;LUX0/k;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
