.class public final Ljb1/k;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a3\u0010\n\u001a\u00020\t*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0008\u001a\u00020\u0007H\u0000\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;",
        "styleType",
        "LHX0/e;",
        "resourceManager",
        "",
        "currencySymbol",
        "Ljava/util/Locale;",
        "locale",
        "Lv21/c;",
        "a",
        "(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;LHX0/e;Ljava/lang/String;Ljava/util/Locale;)Lv21/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;LHX0/e;Ljava/lang/String;Ljava/util/Locale;)Lv21/c;
    .locals 18
    .param p0    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/util/Locale;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p2

    .line 2
    .line 3
    sget-object v1, Ll8/j;->a:Ll8/j;

    .line 4
    .line 5
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->f()J

    .line 10
    .line 11
    .line 12
    move-result-wide v2

    .line 13
    long-to-double v2, v2

    .line 14
    sget-object v4, Lcom/xbet/onexcore/utils/ValueType;->PRIZE:Lcom/xbet/onexcore/utils/ValueType;

    .line 15
    .line 16
    invoke-virtual {v1, v2, v3, v4}, Ll8/j;->n(DLcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->g()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;

    .line 21
    .line 22
    .line 23
    move-result-object v2

    .line 24
    invoke-static {v2, v0}, Ljb1/f;->a(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;LHX0/e;)Lk21/m;

    .line 25
    .line 26
    .line 27
    move-result-object v14

    .line 28
    instance-of v2, v14, Lk21/m$b;

    .line 29
    .line 30
    new-instance v3, Lv21/c;

    .line 31
    .line 32
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->h()J

    .line 33
    .line 34
    .line 35
    move-result-wide v5

    .line 36
    sget-object v4, LCX0/l;->a:LCX0/l;

    .line 37
    .line 38
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->d()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;

    .line 39
    .line 40
    .line 41
    move-result-object v7

    .line 42
    invoke-virtual {v7}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;->a()Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v7

    .line 46
    invoke-virtual {v4, v7}, LCX0/l;->K(Ljava/lang/String;)Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object v4

    .line 50
    invoke-static {v4}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v4

    .line 54
    invoke-static {v4}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 55
    .line 56
    .line 57
    move-result-object v7

    .line 58
    sget v4, Lpb/g;->ic_tournament_banner:I

    .line 59
    .line 60
    invoke-static {v4}, LL11/c$c;->d(I)I

    .line 61
    .line 62
    .line 63
    move-result v4

    .line 64
    invoke-static {v4}, LL11/c$c;->c(I)LL11/c$c;

    .line 65
    .line 66
    .line 67
    move-result-object v8

    .line 68
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 69
    .line 70
    .line 71
    move-result-object v4

    .line 72
    invoke-virtual {v4}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->d()Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object v9

    .line 76
    new-instance v4, Ljava/lang/StringBuilder;

    .line 77
    .line 78
    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    .line 79
    .line 80
    .line 81
    move-object/from16 v10, p3

    .line 82
    .line 83
    invoke-virtual {v4, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 84
    .line 85
    .line 86
    const-string v10, " "

    .line 87
    .line 88
    invoke-virtual {v4, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 89
    .line 90
    .line 91
    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 92
    .line 93
    .line 94
    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 95
    .line 96
    .line 97
    move-result-object v10

    .line 98
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->g()Ljava/lang/String;

    .line 103
    .line 104
    .line 105
    move-result-object v11

    .line 106
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 107
    .line 108
    .line 109
    move-result-object v1

    .line 110
    move-object/from16 v4, p4

    .line 111
    .line 112
    invoke-static {v1, v0, v4}, Ljb1/c;->a(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;LHX0/e;Ljava/util/Locale;)Lv21/g;

    .line 113
    .line 114
    .line 115
    move-result-object v12

    .line 116
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->a()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;

    .line 121
    .line 122
    .line 123
    move-result-object v1

    .line 124
    if-eqz v1, :cond_0

    .line 125
    .line 126
    invoke-static {v1, v0}, Ljb1/e;->a(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;LHX0/e;)Lv21/t;

    .line 127
    .line 128
    .line 129
    move-result-object v1

    .line 130
    :goto_0
    move-object v13, v1

    .line 131
    goto :goto_1

    .line 132
    :cond_0
    const/4 v1, 0x0

    .line 133
    goto :goto_0

    .line 134
    :goto_1
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->l()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;

    .line 135
    .line 136
    .line 137
    move-result-object v1

    .line 138
    invoke-static {v1, v0}, Ljb1/a;->a(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;LHX0/e;)Lk21/a;

    .line 139
    .line 140
    .line 141
    move-result-object v15

    .line 142
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->m()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 143
    .line 144
    .line 145
    move-result-object v1

    .line 146
    invoke-static {v1, v2, v0}, Ljb1/b;->a(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;ZLHX0/e;)Lv21/a;

    .line 147
    .line 148
    .line 149
    move-result-object v16

    .line 150
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->j()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 151
    .line 152
    .line 153
    move-result-object v0

    .line 154
    invoke-static {v0}, Ljb1/d;->a(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;)Lv21/h;

    .line 155
    .line 156
    .line 157
    move-result-object v17

    .line 158
    move-object/from16 v4, p1

    .line 159
    .line 160
    invoke-direct/range {v3 .. v17}, Lv21/c;-><init>(Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;JLL11/c;LL11/c;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lv21/g;Lv21/t;Lk21/m;Lk21/a;Lv21/a;Lv21/h;)V

    .line 161
    .line 162
    .line 163
    return-object v3
.end method
