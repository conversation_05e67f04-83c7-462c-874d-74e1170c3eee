.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView$a;,
        Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u001b\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0018\u0000 I2\u00020\u0001:\u0001(B\u001b\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u000f\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000b\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\nJ\u001f\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u000c\u001a\u00020\u00082\u0006\u0010\r\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J7\u0010\u0017\u001a\u00020\u000e2\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0013\u001a\u00020\u00082\u0006\u0010\u0014\u001a\u00020\u00082\u0006\u0010\u0015\u001a\u00020\u00082\u0006\u0010\u0016\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0015\u0010\u001b\u001a\u00020\u000e2\u0006\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u000f\u0010\u001d\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u000f\u0010\u001f\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010\u001eJ\u000f\u0010 \u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008 \u0010\u001eJ\u000f\u0010!\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008!\u0010\u001eJ\u000f\u0010\"\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\"\u0010\u001eJ\u000f\u0010#\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008#\u0010\u001eJ\u000f\u0010$\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008$\u0010\u001eJ\u000f\u0010%\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008%\u0010\u001eJ\u000f\u0010&\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008&\u0010\u001eJ\u000f\u0010\'\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\'\u0010\u001eR\u0014\u0010*\u001a\u00020\u00118\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010)R\u0014\u0010-\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008+\u0010,R\u0014\u0010/\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010,R\u0014\u00101\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u0010,R\u0014\u00102\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010,R\u0014\u00103\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010,R\u0014\u00104\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010,R\u0014\u00107\u001a\u0002058\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u00106R\u0014\u0010:\u001a\u0002088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008%\u00109R\u0014\u0010=\u001a\u00020;8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010<R\u0014\u0010>\u001a\u0002088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u00109R\u0014\u0010A\u001a\u00020?8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001f\u0010@R\u001b\u0010F\u001a\u00020B8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\"\u0010C\u001a\u0004\u0008D\u0010ER\u001b\u0010H\u001a\u00020B8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008 \u0010C\u001a\u0004\u0008G\u0010E\u00a8\u0006J"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "getBackgroundWidth",
        "()I",
        "getContainerWidth",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "Lmb1/a;",
        "model",
        "setModel",
        "(Lmb1/a;)V",
        "j",
        "()V",
        "l",
        "n",
        "k",
        "m",
        "e",
        "g",
        "i",
        "f",
        "h",
        "a",
        "Z",
        "isRtl",
        "b",
        "I",
        "cellSize",
        "c",
        "halfCellSize",
        "d",
        "iconSize",
        "textWidth",
        "minTextWidth",
        "space16",
        "Landroid/view/View;",
        "Landroid/view/View;",
        "backgroundView",
        "Lcom/google/android/material/imageview/ShapeableImageView;",
        "Lcom/google/android/material/imageview/ShapeableImageView;",
        "imageView",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "textView",
        "iconView",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;",
        "circularProgressBar",
        "Lorg/xbet/uikit/utils/z;",
        "Lkotlin/j;",
        "getImageLoadHelper",
        "()Lorg/xbet/uikit/utils/z;",
        "imageLoadHelper",
        "getIconLoadHelper",
        "iconLoadHelper",
        "o",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final o:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Z

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:I

.field public final h:Landroid/view/View;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lcom/google/android/material/imageview/ShapeableImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lcom/google/android/material/imageview/ShapeableImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->o:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView$a;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 3
    invoke-static {}, LQ0/a;->c()LQ0/a;

    move-result-object p2

    invoke-virtual {p2}, LQ0/a;->h()Z

    move-result p2

    iput-boolean p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->a:Z

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_36:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->b:I

    const/4 v1, 0x2

    .line 5
    div-int/2addr v0, v1

    iput v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->c:I

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_24:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->d:I

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_92:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->e:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_20:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->f:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v3, LlZ0/g;->space_16:I

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    iput v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->g:I

    .line 10
    new-instance v2, Landroid/view/View;

    invoke-direct {v2, p1}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    .line 11
    const-string v3, "TournamentCardCellView.tag_background_view"

    invoke-virtual {v2, v3}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 12
    sget v3, LlZ0/h;->rounded_background_full:I

    invoke-static {p1, v3}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v3

    invoke-virtual {v2, v3}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 13
    sget v3, LlZ0/d;->uikitStaticTransparent:I

    const/4 v4, 0x0

    invoke-static {p1, v3, v4, v1, v4}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v3

    invoke-static {v3}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v3

    invoke-static {v2, v3}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 14
    iput-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 15
    new-instance v2, Lcom/google/android/material/imageview/ShapeableImageView;

    invoke-direct {v2, p1}, Lcom/google/android/material/imageview/ShapeableImageView;-><init>(Landroid/content/Context;)V

    .line 16
    const-string v3, "TournamentCardCellView.tag_image_view"

    invoke-virtual {v2, v3}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 17
    sget-object v3, Landroid/widget/ImageView$ScaleType;->CENTER_CROP:Landroid/widget/ImageView$ScaleType;

    invoke-virtual {v2, v3}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 18
    iput-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 19
    new-instance v2, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v2, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 20
    const-string v5, "TournamentCardCellView.tag_text_view"

    invoke-virtual {v2, v5}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 21
    sget v5, LlZ0/n;->TextStyle_Headline_Bold:I

    invoke-static {v2, v5}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    const/4 v5, 0x1

    .line 22
    invoke-virtual {v2, v5}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 23
    sget-object v5, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {v2, v5}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    const/16 v5, 0x11

    .line 24
    invoke-virtual {v2, v5}, Landroid/widget/TextView;->setGravity(I)V

    .line 25
    invoke-virtual {v2, v0}, Landroid/view/View;->setMinimumWidth(I)V

    const/4 v0, 0x3

    .line 26
    invoke-virtual {v2, v0}, Landroid/view/View;->setLayoutDirection(I)V

    .line 27
    iput-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 28
    new-instance v0, Lcom/google/android/material/imageview/ShapeableImageView;

    invoke-direct {v0, p1}, Lcom/google/android/material/imageview/ShapeableImageView;-><init>(Landroid/content/Context;)V

    .line 29
    const-string v2, "TournamentCardCellView.tag_icon_view"

    invoke-virtual {v0, v2}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 30
    invoke-virtual {v0, v3}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 31
    sget v2, LlZ0/d;->uikitSecondary:I

    invoke-static {p1, v2, v4, v1, v4}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v2

    invoke-static {v2}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v2

    invoke-virtual {v0, v2}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 32
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 33
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;

    invoke-direct {v0, p1, v4, v1, v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    if-eqz p2, :cond_0

    .line 34
    sget-object p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView$Direction;->COUNTER_CLOCKWISE:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView$Direction;

    goto :goto_0

    :cond_0
    sget-object p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView$Direction;->CLOCKWISE:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView$Direction;

    :goto_0
    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;->setProgressDirection(Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView$Direction;)V

    .line 35
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->l:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;

    .line 36
    new-instance p1, Lnb1/a;

    invoke-direct {p1, p0}, Lnb1/a;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;)V

    .line 37
    sget-object p2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    invoke-static {p2, p1}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    .line 38
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->m:Lkotlin/j;

    .line 39
    new-instance p1, Lnb1/b;

    invoke-direct {p1, p0}, Lnb1/b;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;)V

    .line 40
    invoke-static {p2, p1}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    .line 41
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->n:Lkotlin/j;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 1
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic a(Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;)Lorg/xbet/uikit/utils/z;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->c(Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;)Lorg/xbet/uikit/utils/z;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;)Lorg/xbet/uikit/utils/z;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->d(Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;)Lorg/xbet/uikit/utils/z;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;)Lorg/xbet/uikit/utils/z;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/uikit/utils/z;

    .line 2
    .line 3
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 4
    .line 5
    invoke-direct {v0, p0}, Lorg/xbet/uikit/utils/z;-><init>(Landroid/widget/ImageView;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public static final d(Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;)Lorg/xbet/uikit/utils/z;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/uikit/utils/z;

    .line 2
    .line 3
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 4
    .line 5
    invoke-direct {v0, p0}, Lorg/xbet/uikit/utils/z;-><init>(Landroid/widget/ImageView;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method private final getBackgroundWidth()I
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->g:I

    .line 8
    .line 9
    add-int/2addr v0, v1

    .line 10
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    iget v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->g:I

    .line 19
    .line 20
    if-le v1, v2, :cond_0

    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    const/4 v0, 0x0

    .line 24
    :goto_0
    if-eqz v0, :cond_1

    .line 25
    .line 26
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    return v0

    .line 31
    :cond_1
    iget v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->b:I

    .line 32
    .line 33
    return v0
.end method

.method private final getContainerWidth()I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 10
    .line 11
    :goto_0
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    return v0

    .line 16
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 17
    .line 18
    goto :goto_0
.end method

.method private final getIconLoadHelper()Lorg/xbet/uikit/utils/z;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->n:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/utils/z;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getImageLoadHelper()Lorg/xbet/uikit/utils/z;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->m:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/utils/z;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final e()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 13
    .line 14
    .line 15
    move-result v2

    .line 16
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 17
    .line 18
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredHeight()I

    .line 19
    .line 20
    .line 21
    move-result v3

    .line 22
    invoke-virtual {v0, v1, v1, v2, v3}, Landroid/view/View;->layout(IIII)V

    .line 23
    .line 24
    .line 25
    return-void

    .line 26
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 27
    .line 28
    invoke-virtual {v0, v1, v1, v1, v1}, Landroid/view/View;->layout(IIII)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public final f()V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->c:I

    .line 10
    .line 11
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 12
    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    div-int/lit8 v1, v1, 0x2

    .line 18
    .line 19
    sub-int/2addr v0, v1

    .line 20
    iget v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->c:I

    .line 21
    .line 22
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 23
    .line 24
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    div-int/lit8 v2, v2, 0x2

    .line 29
    .line 30
    sub-int/2addr v1, v2

    .line 31
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 32
    .line 33
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 34
    .line 35
    .line 36
    move-result v3

    .line 37
    add-int/2addr v3, v1

    .line 38
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 39
    .line 40
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredHeight()I

    .line 41
    .line 42
    .line 43
    move-result v4

    .line 44
    add-int/2addr v4, v0

    .line 45
    invoke-virtual {v2, v1, v0, v3, v4}, Landroid/view/View;->layout(IIII)V

    .line 46
    .line 47
    .line 48
    return-void

    .line 49
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 50
    .line 51
    const/4 v1, 0x0

    .line 52
    invoke-virtual {v0, v1, v1, v1, v1}, Landroid/view/View;->layout(IIII)V

    .line 53
    .line 54
    .line 55
    return-void
.end method

.method public final g()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 13
    .line 14
    .line 15
    move-result v2

    .line 16
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 17
    .line 18
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredHeight()I

    .line 19
    .line 20
    .line 21
    move-result v3

    .line 22
    invoke-virtual {v0, v1, v1, v2, v3}, Landroid/view/View;->layout(IIII)V

    .line 23
    .line 24
    .line 25
    return-void

    .line 26
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 27
    .line 28
    invoke-virtual {v0, v1, v1, v1, v1}, Landroid/view/View;->layout(IIII)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public final h()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->l:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->l:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;

    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 13
    .line 14
    .line 15
    move-result v2

    .line 16
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->l:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;

    .line 17
    .line 18
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredHeight()I

    .line 19
    .line 20
    .line 21
    move-result v3

    .line 22
    invoke-virtual {v0, v1, v1, v2, v3}, Landroid/view/View;->layout(IIII)V

    .line 23
    .line 24
    .line 25
    return-void

    .line 26
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->l:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;

    .line 27
    .line 28
    invoke-virtual {v0, v1, v1, v1, v1}, Landroid/view/View;->layout(IIII)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public final i()V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->c:I

    .line 10
    .line 11
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 12
    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    div-int/lit8 v1, v1, 0x2

    .line 18
    .line 19
    sub-int/2addr v0, v1

    .line 20
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->getBackgroundWidth()I

    .line 21
    .line 22
    .line 23
    move-result v1

    .line 24
    div-int/lit8 v1, v1, 0x2

    .line 25
    .line 26
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 27
    .line 28
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 29
    .line 30
    .line 31
    move-result v2

    .line 32
    div-int/lit8 v2, v2, 0x2

    .line 33
    .line 34
    sub-int/2addr v1, v2

    .line 35
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 36
    .line 37
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 38
    .line 39
    .line 40
    move-result v3

    .line 41
    add-int/2addr v3, v1

    .line 42
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 43
    .line 44
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredHeight()I

    .line 45
    .line 46
    .line 47
    move-result v4

    .line 48
    add-int/2addr v4, v0

    .line 49
    invoke-virtual {v2, v1, v0, v3, v4}, Landroid/view/View;->layout(IIII)V

    .line 50
    .line 51
    .line 52
    return-void

    .line 53
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 54
    .line 55
    const/4 v1, 0x0

    .line 56
    invoke-virtual {v0, v1, v1, v1, v1}, Landroid/view/View;->layout(IIII)V

    .line 57
    .line 58
    .line 59
    return-void
.end method

.method public final j()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/high16 v1, 0x40000000    # 2.0f

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->getBackgroundWidth()I

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    iget v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->b:I

    .line 22
    .line 23
    invoke-static {v3, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    invoke-virtual {v0, v2, v1}, Landroid/view/View;->measure(II)V

    .line 28
    .line 29
    .line 30
    return-void

    .line 31
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 32
    .line 33
    const/4 v2, 0x0

    .line 34
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 35
    .line 36
    .line 37
    move-result v3

    .line 38
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 39
    .line 40
    .line 41
    move-result v1

    .line 42
    invoke-virtual {v0, v3, v1}, Landroid/view/View;->measure(II)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public final k()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/high16 v1, 0x40000000    # 2.0f

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 12
    .line 13
    iget v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->d:I

    .line 14
    .line 15
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 16
    .line 17
    .line 18
    move-result v2

    .line 19
    iget v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->d:I

    .line 20
    .line 21
    invoke-static {v3, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    invoke-virtual {v0, v2, v1}, Landroid/view/View;->measure(II)V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 30
    .line 31
    const/4 v2, 0x0

    .line 32
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 33
    .line 34
    .line 35
    move-result v3

    .line 36
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 37
    .line 38
    .line 39
    move-result v1

    .line 40
    invoke-virtual {v0, v3, v1}, Landroid/view/View;->measure(II)V

    .line 41
    .line 42
    .line 43
    return-void
.end method

.method public final l()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/high16 v1, 0x40000000    # 2.0f

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 12
    .line 13
    iget v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->b:I

    .line 14
    .line 15
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 16
    .line 17
    .line 18
    move-result v2

    .line 19
    iget v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->b:I

    .line 20
    .line 21
    invoke-static {v3, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    invoke-virtual {v0, v2, v1}, Landroid/view/View;->measure(II)V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 30
    .line 31
    const/4 v2, 0x0

    .line 32
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 33
    .line 34
    .line 35
    move-result v3

    .line 36
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 37
    .line 38
    .line 39
    move-result v1

    .line 40
    invoke-virtual {v0, v3, v1}, Landroid/view/View;->measure(II)V

    .line 41
    .line 42
    .line 43
    return-void
.end method

.method public final m()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->l:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/high16 v1, 0x40000000    # 2.0f

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->l:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;

    .line 12
    .line 13
    iget v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->b:I

    .line 14
    .line 15
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 16
    .line 17
    .line 18
    move-result v2

    .line 19
    iget v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->b:I

    .line 20
    .line 21
    invoke-static {v3, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    invoke-virtual {v0, v2, v1}, Landroid/view/View;->measure(II)V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->l:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;

    .line 30
    .line 31
    const/4 v2, 0x0

    .line 32
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 33
    .line 34
    .line 35
    move-result v3

    .line 36
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 37
    .line 38
    .line 39
    move-result v1

    .line 40
    invoke-virtual {v0, v3, v1}, Landroid/view/View;->measure(II)V

    .line 41
    .line 42
    .line 43
    return-void
.end method

.method public final n()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 11
    .line 12
    iget v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->e:I

    .line 13
    .line 14
    const/high16 v3, -0x80000000

    .line 15
    .line 16
    invoke-static {v2, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 17
    .line 18
    .line 19
    move-result v2

    .line 20
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 21
    .line 22
    .line 23
    move-result v1

    .line 24
    invoke-virtual {v0, v2, v1}, Landroid/view/View;->measure(II)V

    .line 25
    .line 26
    .line 27
    return-void

    .line 28
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 29
    .line 30
    const/high16 v2, 0x40000000    # 2.0f

    .line 31
    .line 32
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 33
    .line 34
    .line 35
    move-result v3

    .line 36
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 37
    .line 38
    .line 39
    move-result v1

    .line 40
    invoke-virtual {v0, v3, v1}, Landroid/view/View;->measure(II)V

    .line 41
    .line 42
    .line 43
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->e()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->g()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->f()V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h()V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public onMeasure(II)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->l()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->n()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->m()V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j()V

    .line 14
    .line 15
    .line 16
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->getContainerWidth()I

    .line 17
    .line 18
    .line 19
    move-result p1

    .line 20
    iget p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->b:I

    .line 21
    .line 22
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public final setModel(Lmb1/a;)V
    .locals 10
    .param p1    # Lmb1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, Lmb1/a$b;

    .line 2
    .line 3
    const/4 v1, 0x2

    .line 4
    const/4 v2, 0x0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 8
    .line 9
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->l(Landroid/view/View;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 13
    .line 14
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->l(Landroid/view/View;)V

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->l:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;

    .line 18
    .line 19
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->l(Landroid/view/View;)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 23
    .line 24
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->l(Landroid/view/View;)V

    .line 25
    .line 26
    .line 27
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 28
    .line 29
    invoke-static {p0, v0, v2, v1, v2}, Lorg/xbet/uikit/utils/S;->b(Landroid/view/ViewGroup;Landroid/view/View;Ljava/lang/Integer;ILjava/lang/Object;)V

    .line 30
    .line 31
    .line 32
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->getImageLoadHelper()Lorg/xbet/uikit/utils/z;

    .line 33
    .line 34
    .line 35
    move-result-object v3

    .line 36
    check-cast p1, Lmb1/a$b;

    .line 37
    .line 38
    invoke-virtual {p1}, Lmb1/a$b;->a()LL11/c;

    .line 39
    .line 40
    .line 41
    move-result-object v4

    .line 42
    const/16 v8, 0xc

    .line 43
    .line 44
    const/4 v9, 0x0

    .line 45
    const/4 v5, 0x0

    .line 46
    const/4 v6, 0x0

    .line 47
    const/4 v7, 0x0

    .line 48
    invoke-static/range {v3 .. v9}, Lorg/xbet/uikit/utils/z;->y(Lorg/xbet/uikit/utils/z;LL11/c;LL11/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 49
    .line 50
    .line 51
    goto/16 :goto_1

    .line 52
    .line 53
    :cond_0
    instance-of v0, p1, Lmb1/a$a;

    .line 54
    .line 55
    const/4 v3, 0x0

    .line 56
    if-eqz v0, :cond_1

    .line 57
    .line 58
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 59
    .line 60
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->l(Landroid/view/View;)V

    .line 61
    .line 62
    .line 63
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 64
    .line 65
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->l(Landroid/view/View;)V

    .line 66
    .line 67
    .line 68
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 69
    .line 70
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 71
    .line 72
    .line 73
    move-result-object v4

    .line 74
    sget v5, LlZ0/d;->uikitBackground:I

    .line 75
    .line 76
    invoke-static {v4, v5, v2, v1, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 77
    .line 78
    .line 79
    move-result v4

    .line 80
    invoke-static {v4}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 81
    .line 82
    .line 83
    move-result-object v4

    .line 84
    invoke-static {v0, v4}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 85
    .line 86
    .line 87
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 88
    .line 89
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 90
    .line 91
    .line 92
    move-result-object v3

    .line 93
    invoke-static {p0, v0, v3}, Lorg/xbet/uikit/utils/S;->a(Landroid/view/ViewGroup;Landroid/view/View;Ljava/lang/Integer;)V

    .line 94
    .line 95
    .line 96
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 97
    .line 98
    invoke-static {p0, v0, v2, v1, v2}, Lorg/xbet/uikit/utils/S;->b(Landroid/view/ViewGroup;Landroid/view/View;Ljava/lang/Integer;ILjava/lang/Object;)V

    .line 99
    .line 100
    .line 101
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->getIconLoadHelper()Lorg/xbet/uikit/utils/z;

    .line 102
    .line 103
    .line 104
    move-result-object v3

    .line 105
    check-cast p1, Lmb1/a$a;

    .line 106
    .line 107
    invoke-virtual {p1}, Lmb1/a$a;->a()LL11/c;

    .line 108
    .line 109
    .line 110
    move-result-object v4

    .line 111
    const/16 v8, 0xc

    .line 112
    .line 113
    const/4 v9, 0x0

    .line 114
    const/4 v5, 0x0

    .line 115
    const/4 v6, 0x0

    .line 116
    const/4 v7, 0x0

    .line 117
    invoke-static/range {v3 .. v9}, Lorg/xbet/uikit/utils/z;->y(Lorg/xbet/uikit/utils/z;LL11/c;LL11/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 118
    .line 119
    .line 120
    goto/16 :goto_1

    .line 121
    .line 122
    :cond_1
    instance-of v0, p1, Lmb1/a$d;

    .line 123
    .line 124
    const/4 v4, 0x1

    .line 125
    if-eqz v0, :cond_5

    .line 126
    .line 127
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 128
    .line 129
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->l(Landroid/view/View;)V

    .line 130
    .line 131
    .line 132
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 133
    .line 134
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->l(Landroid/view/View;)V

    .line 135
    .line 136
    .line 137
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->l:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;

    .line 138
    .line 139
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->l(Landroid/view/View;)V

    .line 140
    .line 141
    .line 142
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 143
    .line 144
    invoke-static {p0, v0, v2, v1, v2}, Lorg/xbet/uikit/utils/S;->b(Landroid/view/ViewGroup;Landroid/view/View;Ljava/lang/Integer;ILjava/lang/Object;)V

    .line 145
    .line 146
    .line 147
    check-cast p1, Lmb1/a$d;

    .line 148
    .line 149
    invoke-virtual {p1}, Lmb1/a$d;->b()Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;

    .line 150
    .line 151
    .line 152
    move-result-object v0

    .line 153
    sget-object v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView$b;->a:[I

    .line 154
    .line 155
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 156
    .line 157
    .line 158
    move-result v0

    .line 159
    aget v0, v5, v0

    .line 160
    .line 161
    if-eq v0, v4, :cond_4

    .line 162
    .line 163
    if-eq v0, v1, :cond_3

    .line 164
    .line 165
    const/4 v4, 0x3

    .line 166
    if-ne v0, v4, :cond_2

    .line 167
    .line 168
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 169
    .line 170
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 171
    .line 172
    .line 173
    move-result-object v4

    .line 174
    sget v5, LlZ0/d;->uikitStaticGreen:I

    .line 175
    .line 176
    invoke-static {v4, v5, v2, v1, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 177
    .line 178
    .line 179
    move-result v4

    .line 180
    invoke-static {v4}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 181
    .line 182
    .line 183
    move-result-object v4

    .line 184
    invoke-static {v0, v4}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 185
    .line 186
    .line 187
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 188
    .line 189
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 190
    .line 191
    .line 192
    move-result-object v4

    .line 193
    sget v5, LlZ0/d;->uikitStaticWhite:I

    .line 194
    .line 195
    invoke-static {v4, v5, v2, v1, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 196
    .line 197
    .line 198
    move-result v1

    .line 199
    invoke-static {v1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 200
    .line 201
    .line 202
    move-result-object v1

    .line 203
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setTextColor(Landroid/content/res/ColorStateList;)V

    .line 204
    .line 205
    .line 206
    goto :goto_0

    .line 207
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 208
    .line 209
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 210
    .line 211
    .line 212
    throw p1

    .line 213
    :cond_3
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 214
    .line 215
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 216
    .line 217
    .line 218
    move-result-object v4

    .line 219
    sget v5, LlZ0/d;->uikitPrimary:I

    .line 220
    .line 221
    invoke-static {v4, v5, v2, v1, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 222
    .line 223
    .line 224
    move-result v4

    .line 225
    invoke-static {v4}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 226
    .line 227
    .line 228
    move-result-object v4

    .line 229
    invoke-static {v0, v4}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 230
    .line 231
    .line 232
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 233
    .line 234
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 235
    .line 236
    .line 237
    move-result-object v4

    .line 238
    sget v5, LlZ0/d;->uikitStaticWhite:I

    .line 239
    .line 240
    invoke-static {v4, v5, v2, v1, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 241
    .line 242
    .line 243
    move-result v1

    .line 244
    invoke-static {v1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 245
    .line 246
    .line 247
    move-result-object v1

    .line 248
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setTextColor(Landroid/content/res/ColorStateList;)V

    .line 249
    .line 250
    .line 251
    goto :goto_0

    .line 252
    :cond_4
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 253
    .line 254
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 255
    .line 256
    .line 257
    move-result-object v4

    .line 258
    sget v5, LlZ0/d;->uikitBackground:I

    .line 259
    .line 260
    invoke-static {v4, v5, v2, v1, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 261
    .line 262
    .line 263
    move-result v4

    .line 264
    invoke-static {v4}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 265
    .line 266
    .line 267
    move-result-object v4

    .line 268
    invoke-static {v0, v4}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 269
    .line 270
    .line 271
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 272
    .line 273
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 274
    .line 275
    .line 276
    move-result-object v4

    .line 277
    sget v5, LlZ0/d;->uikitSecondary:I

    .line 278
    .line 279
    invoke-static {v4, v5, v2, v1, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 280
    .line 281
    .line 282
    move-result v1

    .line 283
    invoke-static {v1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 284
    .line 285
    .line 286
    move-result-object v1

    .line 287
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setTextColor(Landroid/content/res/ColorStateList;)V

    .line 288
    .line 289
    .line 290
    :goto_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 291
    .line 292
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 293
    .line 294
    .line 295
    move-result-object v1

    .line 296
    invoke-static {p0, v0, v1}, Lorg/xbet/uikit/utils/S;->a(Landroid/view/ViewGroup;Landroid/view/View;Ljava/lang/Integer;)V

    .line 297
    .line 298
    .line 299
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 300
    .line 301
    invoke-virtual {p1}, Lmb1/a$d;->a()Ljava/lang/String;

    .line 302
    .line 303
    .line 304
    move-result-object p1

    .line 305
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 306
    .line 307
    .line 308
    goto :goto_1

    .line 309
    :cond_5
    instance-of v0, p1, Lmb1/a$c;

    .line 310
    .line 311
    if-eqz v0, :cond_6

    .line 312
    .line 313
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->i:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 314
    .line 315
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->l(Landroid/view/View;)V

    .line 316
    .line 317
    .line 318
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->k:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 319
    .line 320
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->l(Landroid/view/View;)V

    .line 321
    .line 322
    .line 323
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 324
    .line 325
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 326
    .line 327
    .line 328
    move-result-object v5

    .line 329
    sget v6, LlZ0/d;->uikitPrimary:I

    .line 330
    .line 331
    invoke-static {v5, v6, v2, v1, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 332
    .line 333
    .line 334
    move-result v5

    .line 335
    invoke-static {v5}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 336
    .line 337
    .line 338
    move-result-object v5

    .line 339
    invoke-virtual {v0, v5}, Landroid/widget/TextView;->setTextColor(Landroid/content/res/ColorStateList;)V

    .line 340
    .line 341
    .line 342
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 343
    .line 344
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 345
    .line 346
    .line 347
    move-result-object v5

    .line 348
    sget v6, LlZ0/d;->uikitBackgroundContent:I

    .line 349
    .line 350
    invoke-static {v5, v6, v2, v1, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 351
    .line 352
    .line 353
    move-result v5

    .line 354
    invoke-static {v5}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 355
    .line 356
    .line 357
    move-result-object v5

    .line 358
    invoke-static {v0, v5}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 359
    .line 360
    .line 361
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->h:Landroid/view/View;

    .line 362
    .line 363
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 364
    .line 365
    .line 366
    move-result-object v3

    .line 367
    invoke-static {p0, v0, v3}, Lorg/xbet/uikit/utils/S;->a(Landroid/view/ViewGroup;Landroid/view/View;Ljava/lang/Integer;)V

    .line 368
    .line 369
    .line 370
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->l:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;

    .line 371
    .line 372
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 373
    .line 374
    .line 375
    move-result-object v3

    .line 376
    invoke-static {p0, v0, v3}, Lorg/xbet/uikit/utils/S;->a(Landroid/view/ViewGroup;Landroid/view/View;Ljava/lang/Integer;)V

    .line 377
    .line 378
    .line 379
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 380
    .line 381
    invoke-static {p0, v0, v2, v1, v2}, Lorg/xbet/uikit/utils/S;->b(Landroid/view/ViewGroup;Landroid/view/View;Ljava/lang/Integer;ILjava/lang/Object;)V

    .line 382
    .line 383
    .line 384
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 385
    .line 386
    check-cast p1, Lmb1/a$c;

    .line 387
    .line 388
    invoke-virtual {p1}, Lmb1/a$c;->c()Ljava/lang/String;

    .line 389
    .line 390
    .line 391
    move-result-object v1

    .line 392
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 393
    .line 394
    .line 395
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->l:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;

    .line 396
    .line 397
    invoke-virtual {p1}, Lmb1/a$c;->a()I

    .line 398
    .line 399
    .line 400
    move-result v1

    .line 401
    invoke-virtual {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;->setProgress(I)V

    .line 402
    .line 403
    .line 404
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCellView;->l:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;

    .line 405
    .line 406
    invoke-virtual {p1}, Lmb1/a$c;->b()I

    .line 407
    .line 408
    .line 409
    move-result p1

    .line 410
    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/views/TournamentCardCircularProgressView;->setMaxProgress(I)V

    .line 411
    .line 412
    .line 413
    :goto_1
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 414
    .line 415
    .line 416
    return-void

    .line 417
    :cond_6
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 418
    .line 419
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 420
    .line 421
    .line 422
    throw p1
.end method
