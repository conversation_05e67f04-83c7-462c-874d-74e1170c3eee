.class public final enum Lorg/xbet/tile_matching/domain/models/TileMatchingType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/tile_matching/domain/models/TileMatchingType$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/tile_matching/domain/models/TileMatchingType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\t\n\u0002\u0010\u0008\n\u0000\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0006\u0010\n\u001a\u00020\u000bj\u0002\u0008\u0004j\u0002\u0008\u0005j\u0002\u0008\u0006j\u0002\u0008\u0007j\u0002\u0008\u0008j\u0002\u0008\t\u00a8\u0006\u000c"
    }
    d2 = {
        "Lorg/xbet/tile_matching/domain/models/TileMatchingType;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "CELL_ONE",
        "CELL_TWO",
        "CELL_THREE",
        "CELL_FOUR",
        "CELL_FIVE",
        "CELL_SIX",
        "getNumber",
        "",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/tile_matching/domain/models/TileMatchingType;

.field public static final enum CELL_FIVE:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

.field public static final enum CELL_FOUR:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

.field public static final enum CELL_ONE:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

.field public static final enum CELL_SIX:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

.field public static final enum CELL_THREE:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

.field public static final enum CELL_TWO:Lorg/xbet/tile_matching/domain/models/TileMatchingType;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 2
    .line 3
    const-string v1, "CELL_ONE"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xbet/tile_matching/domain/models/TileMatchingType;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->CELL_ONE:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 12
    .line 13
    const-string v1, "CELL_TWO"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xbet/tile_matching/domain/models/TileMatchingType;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->CELL_TWO:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 20
    .line 21
    new-instance v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 22
    .line 23
    const-string v1, "CELL_THREE"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2}, Lorg/xbet/tile_matching/domain/models/TileMatchingType;-><init>(Ljava/lang/String;I)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->CELL_THREE:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 30
    .line 31
    new-instance v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 32
    .line 33
    const-string v1, "CELL_FOUR"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2}, Lorg/xbet/tile_matching/domain/models/TileMatchingType;-><init>(Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->CELL_FOUR:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 42
    .line 43
    const-string v1, "CELL_FIVE"

    .line 44
    .line 45
    const/4 v2, 0x4

    .line 46
    invoke-direct {v0, v1, v2}, Lorg/xbet/tile_matching/domain/models/TileMatchingType;-><init>(Ljava/lang/String;I)V

    .line 47
    .line 48
    .line 49
    sput-object v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->CELL_FIVE:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 50
    .line 51
    new-instance v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 52
    .line 53
    const-string v1, "CELL_SIX"

    .line 54
    .line 55
    const/4 v2, 0x5

    .line 56
    invoke-direct {v0, v1, v2}, Lorg/xbet/tile_matching/domain/models/TileMatchingType;-><init>(Ljava/lang/String;I)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->CELL_SIX:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 60
    .line 61
    invoke-static {}, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->a()[Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    sput-object v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->$VALUES:[Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 66
    .line 67
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    sput-object v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->$ENTRIES:Lkotlin/enums/a;

    .line 72
    .line 73
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/tile_matching/domain/models/TileMatchingType;
    .locals 3

    .line 1
    const/4 v0, 0x6

    new-array v0, v0, [Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    sget-object v1, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->CELL_ONE:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->CELL_TWO:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->CELL_THREE:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->CELL_FOUR:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->CELL_FIVE:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->CELL_SIX:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/tile_matching/domain/models/TileMatchingType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/tile_matching/domain/models/TileMatchingType;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/tile_matching/domain/models/TileMatchingType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->$VALUES:[Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getNumber()I
    .locals 2

    .line 1
    sget-object v0, Lorg/xbet/tile_matching/domain/models/TileMatchingType$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    packed-switch v0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw v0

    .line 18
    :pswitch_0
    const/4 v0, 0x5

    .line 19
    return v0

    .line 20
    :pswitch_1
    const/4 v0, 0x4

    .line 21
    return v0

    .line 22
    :pswitch_2
    const/4 v0, 0x3

    .line 23
    return v0

    .line 24
    :pswitch_3
    const/4 v0, 0x2

    .line 25
    return v0

    .line 26
    :pswitch_4
    const/4 v0, 0x1

    .line 27
    return v0

    .line 28
    :pswitch_5
    const/4 v0, 0x0

    .line 29
    return v0

    .line 30
    nop

    .line 31
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
