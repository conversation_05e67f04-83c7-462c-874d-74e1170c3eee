.class public final LqC0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LqC0/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LqC0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LqC0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LqC0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LQW0/c;LKA0/c;LWb0/a;LiR/a;Lorg/xbet/sportgame/subgames/api/SubGamesParams;LDg/a;LlB0/a;LuB0/a;Ljava/lang/String;LHX0/e;)LqC0/c;
    .locals 12

    .line 1
    invoke-static {p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static {p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static {p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    invoke-static/range {p10 .. p10}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    new-instance v0, LqC0/a$b;

    .line 32
    .line 33
    const/4 v11, 0x0

    .line 34
    move-object v1, p1

    .line 35
    move-object v2, p2

    .line 36
    move-object v3, p3

    .line 37
    move-object/from16 v4, p4

    .line 38
    .line 39
    move-object/from16 v5, p5

    .line 40
    .line 41
    move-object/from16 v6, p6

    .line 42
    .line 43
    move-object/from16 v7, p7

    .line 44
    .line 45
    move-object/from16 v8, p8

    .line 46
    .line 47
    move-object/from16 v9, p9

    .line 48
    .line 49
    move-object/from16 v10, p10

    .line 50
    .line 51
    invoke-direct/range {v0 .. v11}, LqC0/a$b;-><init>(LQW0/c;LKA0/c;LWb0/a;LiR/a;Lorg/xbet/sportgame/subgames/api/SubGamesParams;LDg/a;LlB0/a;LuB0/a;Ljava/lang/String;LHX0/e;LqC0/b;)V

    .line 52
    .line 53
    .line 54
    return-object v0
.end method
