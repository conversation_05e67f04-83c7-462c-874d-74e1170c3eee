.class public final Ljb1/h;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ljb1/h$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u000c\u001a1\u0010\n\u001a\u00020\t*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\n\u0010\u000b\u001aA\u0010\u0011\u001a\u0004\u0018\u00010\u00032\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u000e2\u0006\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012\u001aA\u0010\u0017\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u00132\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0016\u001a\u00020\u000e2\u0006\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018\u001a\u0017\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u0006\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u001b\u001a\u0017\u0010\u001c\u001a\u00020\u00192\u0006\u0010\u0006\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001b\u001a\u0017\u0010\u001d\u001a\u00020\u00192\u0006\u0010\u0006\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001b\u001a\'\u0010 \u001a\u00020\u00032\u0006\u0010\u001e\u001a\u00020\u00132\u0006\u0010\u001f\u001a\u00020\u00132\u0006\u0010\u0006\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008 \u0010!\u001a\'\u0010#\u001a\u00020\u00032\u0006\u0010\u0016\u001a\u00020\u000e2\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\"\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008#\u0010$\u00a8\u0006%"
    }
    d2 = {
        "Li81/a;",
        "LHX0/e;",
        "resourceManager",
        "",
        "currencySymbol",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;",
        "styleType",
        "Ljava/util/Locale;",
        "locale",
        "Ln21/a;",
        "h",
        "(Li81/a;LHX0/e;Ljava/lang/String;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Locale;)Ln21/a;",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/TournamentPrizePoolEnum;",
        "status",
        "Ljava/util/Date;",
        "startDate",
        "endDate",
        "f",
        "(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/TournamentPrizePoolEnum;LHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Date;Ljava/util/Date;Ljava/util/Locale;)Ljava/lang/String;",
        "",
        "start",
        "textCount",
        "date",
        "g",
        "(ZZLHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Date;Ljava/util/Locale;)Ljava/lang/String;",
        "LL11/c;",
        "d",
        "(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)LL11/c;",
        "c",
        "e",
        "timeOnly",
        "is24HourFormat",
        "b",
        "(ZZLorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)Ljava/lang/String;",
        "format",
        "a",
        "(Ljava/util/Date;Ljava/util/Locale;Ljava/lang/String;)Ljava/lang/String;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ljava/util/Date;Ljava/util/Locale;Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Ll8/b;->a:Ll8/b;

    .line 2
    .line 3
    invoke-virtual {v0, p0, p2, p1}, Ll8/b;->g(Ljava/util/Date;Ljava/lang/String;Ljava/util/Locale;)Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    return-object p0
.end method

.method public static final b(ZZLorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)Ljava/lang/String;
    .locals 2

    .line 1
    const-string v0, "dd.MM.yy"

    .line 2
    .line 3
    if-eqz p1, :cond_4

    .line 4
    .line 5
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;->BACKGROUND_PICTURE:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 6
    .line 7
    const-string v1, "dd MMMM yyyy HH:mm"

    .line 8
    .line 9
    if-ne p2, p1, :cond_0

    .line 10
    .line 11
    return-object v1

    .line 12
    :cond_0
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;->PICTURE_S:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 13
    .line 14
    if-ne p2, p1, :cond_1

    .line 15
    .line 16
    return-object v1

    .line 17
    :cond_1
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;->DATES:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 18
    .line 19
    if-ne p2, p1, :cond_2

    .line 20
    .line 21
    const-string p0, "dd.MM.yy HH:mm"

    .line 22
    .line 23
    return-object p0

    .line 24
    :cond_2
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;->BACKGROUND_ILLUSTRATION:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 25
    .line 26
    if-ne p2, p1, :cond_3

    .line 27
    .line 28
    if-eqz p0, :cond_3

    .line 29
    .line 30
    const-string p0, "HH:mm"

    .line 31
    .line 32
    return-object p0

    .line 33
    :cond_3
    return-object v0

    .line 34
    :cond_4
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;->BACKGROUND_PICTURE:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 35
    .line 36
    const-string v1, "dd MMMM yyyy hh:mm a"

    .line 37
    .line 38
    if-ne p2, p1, :cond_5

    .line 39
    .line 40
    return-object v1

    .line 41
    :cond_5
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;->PICTURE_S:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 42
    .line 43
    if-ne p2, p1, :cond_6

    .line 44
    .line 45
    return-object v1

    .line 46
    :cond_6
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;->DATES:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 47
    .line 48
    if-ne p2, p1, :cond_7

    .line 49
    .line 50
    const-string p0, "dd.MM.yy hh:mm a"

    .line 51
    .line 52
    return-object p0

    .line 53
    :cond_7
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;->BACKGROUND_ILLUSTRATION:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 54
    .line 55
    if-ne p2, p1, :cond_8

    .line 56
    .line 57
    if-eqz p0, :cond_8

    .line 58
    .line 59
    const-string p0, "hh:mm a"

    .line 60
    .line 61
    return-object p0

    .line 62
    :cond_8
    return-object v0
.end method

.method public static final c(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)LL11/c;
    .locals 3

    .line 1
    sget-object v0, Ljb1/h$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x4

    .line 10
    if-ne p0, v0, :cond_0

    .line 11
    .line 12
    const-string p0, "Background Illustration_Left.webp"

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const-string p0, ""

    .line 16
    .line 17
    :goto_0
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 18
    .line 19
    new-instance v1, Ljava/lang/StringBuilder;

    .line 20
    .line 21
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 22
    .line 23
    .line 24
    const-string v2, "/static/img/android/casino/alt_design/aggregator_tournament_prize_pool/"

    .line 25
    .line 26
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 27
    .line 28
    .line 29
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 30
    .line 31
    .line 32
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-virtual {v0, p0}, LCX0/l;->K(Ljava/lang/String;)Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-static {p0}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    invoke-static {p0}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    return-object p0
.end method

.method public static final d(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)LL11/c;
    .locals 3

    .line 1
    sget-object v0, Ljb1/h$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p0, v0, :cond_3

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p0, v0, :cond_2

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-eq p0, v0, :cond_1

    .line 17
    .line 18
    const/4 v0, 0x4

    .line 19
    if-ne p0, v0, :cond_0

    .line 20
    .line 21
    const-string p0, ""

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 25
    .line 26
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 27
    .line 28
    .line 29
    throw p0

    .line 30
    :cond_1
    const-string p0, "Dates.webp"

    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_2
    const-string p0, "Background Picture.webp"

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_3
    const-string p0, "Picture S.webp"

    .line 37
    .line 38
    :goto_0
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 39
    .line 40
    new-instance v1, Ljava/lang/StringBuilder;

    .line 41
    .line 42
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 43
    .line 44
    .line 45
    const-string v2, "/static/img/android/casino/alt_design/aggregator_tournament_prize_pool/"

    .line 46
    .line 47
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 48
    .line 49
    .line 50
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 51
    .line 52
    .line 53
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object p0

    .line 57
    invoke-virtual {v0, p0}, LCX0/l;->K(Ljava/lang/String;)Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object p0

    .line 61
    invoke-static {p0}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object p0

    .line 65
    invoke-static {p0}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 66
    .line 67
    .line 68
    move-result-object p0

    .line 69
    return-object p0
.end method

.method public static final e(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)LL11/c;
    .locals 3

    .line 1
    sget-object v0, Ljb1/h$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x4

    .line 10
    if-ne p0, v0, :cond_0

    .line 11
    .line 12
    const-string p0, "Background Illustration_Right.webp"

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const-string p0, ""

    .line 16
    .line 17
    :goto_0
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 18
    .line 19
    new-instance v1, Ljava/lang/StringBuilder;

    .line 20
    .line 21
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 22
    .line 23
    .line 24
    const-string v2, "/static/img/android/casino/alt_design/aggregator_tournament_prize_pool/"

    .line 25
    .line 26
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 27
    .line 28
    .line 29
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 30
    .line 31
    .line 32
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-virtual {v0, p0}, LCX0/l;->K(Ljava/lang/String;)Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-static {p0}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    invoke-static {p0}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    return-object p0
.end method

.method public static final f(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/TournamentPrizePoolEnum;LHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Date;Ljava/util/Date;Ljava/util/Locale;)Ljava/lang/String;
    .locals 2

    .line 1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/TournamentPrizePoolEnum;->COMPLETED:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/TournamentPrizePoolEnum;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-ne p0, v0, :cond_0

    .line 5
    .line 6
    sget p0, Lpb/k;->end_of_tournament:I

    .line 7
    .line 8
    new-array p2, v1, [Ljava/lang/Object;

    .line 9
    .line 10
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 11
    .line 12
    .line 13
    move-result-object p0

    .line 14
    return-object p0

    .line 15
    :cond_0
    sget-object p0, Ljb1/h$a;->a:[I

    .line 16
    .line 17
    invoke-virtual {p2}, Ljava/lang/Enum;->ordinal()I

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    aget p0, p0, v0

    .line 22
    .line 23
    const/4 v0, 0x1

    .line 24
    if-eq p0, v0, :cond_1

    .line 25
    .line 26
    const/4 v0, 0x2

    .line 27
    if-eq p0, v0, :cond_1

    .line 28
    .line 29
    const/4 p0, 0x0

    .line 30
    return-object p0

    .line 31
    :cond_1
    invoke-interface {p1}, LHX0/e;->c()Z

    .line 32
    .line 33
    .line 34
    move-result p0

    .line 35
    invoke-static {v1, p0, p2}, Ljb1/h;->b(ZZLorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object p0

    .line 39
    invoke-static {p3, p5, p0}, Ljb1/h;->a(Ljava/util/Date;Ljava/util/Locale;Ljava/lang/String;)Ljava/lang/String;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    invoke-static {p4, p5, p0}, Ljb1/h;->a(Ljava/util/Date;Ljava/util/Locale;Ljava/lang/String;)Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object p0

    .line 47
    new-instance p2, Ljava/lang/StringBuilder;

    .line 48
    .line 49
    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    .line 50
    .line 51
    .line 52
    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 53
    .line 54
    .line 55
    const-string p1, " - "

    .line 56
    .line 57
    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 58
    .line 59
    .line 60
    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 61
    .line 62
    .line 63
    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 64
    .line 65
    .line 66
    move-result-object p0

    .line 67
    return-object p0
.end method

.method public static final g(ZZLHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Date;Ljava/util/Locale;)Ljava/lang/String;
    .locals 2

    .line 1
    sget-object v0, Ljb1/h$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p3}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    const/4 v1, 0x3

    .line 10
    if-eq v0, v1, :cond_1

    .line 11
    .line 12
    const/4 p0, 0x4

    .line 13
    if-eq v0, p0, :cond_0

    .line 14
    .line 15
    const/4 p0, 0x0

    .line 16
    return-object p0

    .line 17
    :cond_0
    xor-int/lit8 p0, p1, 0x1

    .line 18
    .line 19
    invoke-interface {p2}, LHX0/e;->c()Z

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    invoke-static {p0, p1, p3}, Ljb1/h;->b(ZZLorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    invoke-static {p4, p5, p0}, Ljb1/h;->a(Ljava/util/Date;Ljava/util/Locale;Ljava/lang/String;)Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object p0

    .line 31
    return-object p0

    .line 32
    :cond_1
    if-eqz p1, :cond_2

    .line 33
    .line 34
    xor-int/lit8 p0, p1, 0x1

    .line 35
    .line 36
    invoke-interface {p2}, LHX0/e;->c()Z

    .line 37
    .line 38
    .line 39
    move-result p1

    .line 40
    invoke-static {p0, p1, p3}, Ljb1/h;->b(ZZLorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    invoke-static {p4, p5, p0}, Ljb1/h;->a(Ljava/util/Date;Ljava/util/Locale;Ljava/lang/String;)Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    return-object p0

    .line 49
    :cond_2
    const/4 p1, 0x0

    .line 50
    if-eqz p0, :cond_3

    .line 51
    .line 52
    sget p0, Lpb/k;->tournament_prize_pool_title_date_start:I

    .line 53
    .line 54
    new-array p1, p1, [Ljava/lang/Object;

    .line 55
    .line 56
    invoke-interface {p2, p0, p1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object p0

    .line 60
    return-object p0

    .line 61
    :cond_3
    sget p0, Lpb/k;->tournament_title_date_end:I

    .line 62
    .line 63
    new-array p1, p1, [Ljava/lang/Object;

    .line 64
    .line 65
    invoke-interface {p2, p0, p1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 66
    .line 67
    .line 68
    move-result-object p0

    .line 69
    return-object p0
.end method

.method public static final h(Li81/a;LHX0/e;Ljava/lang/String;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Locale;)Ln21/a;
    .locals 30
    .param p0    # Li81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/util/Locale;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Ll8/b;->a:Ll8/b;

    .line 2
    .line 3
    invoke-virtual/range {p0 .. p0}, Li81/a;->d()Lj81/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lj81/a;->h()J

    .line 8
    .line 9
    .line 10
    move-result-wide v1

    .line 11
    const/4 v4, 0x2

    .line 12
    const/4 v5, 0x0

    .line 13
    const/4 v3, 0x0

    .line 14
    invoke-static/range {v0 .. v5}, Ll8/b;->p0(Ll8/b;JZILjava/lang/Object;)Ljava/util/Date;

    .line 15
    .line 16
    .line 17
    move-result-object v10

    .line 18
    invoke-virtual/range {p0 .. p0}, Li81/a;->d()Lj81/a;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    invoke-virtual {v1}, Lj81/a;->c()J

    .line 23
    .line 24
    .line 25
    move-result-wide v1

    .line 26
    invoke-static/range {v0 .. v5}, Ll8/b;->p0(Ll8/b;JZILjava/lang/Object;)Ljava/util/Date;

    .line 27
    .line 28
    .line 29
    move-result-object v15

    .line 30
    new-instance v1, Ljava/util/Date;

    .line 31
    .line 32
    invoke-direct {v1}, Ljava/util/Date;-><init>()V

    .line 33
    .line 34
    .line 35
    invoke-virtual {v0, v1, v10, v15}, Ll8/b;->f(Ljava/util/Date;Ljava/util/Date;Ljava/util/Date;)Z

    .line 36
    .line 37
    .line 38
    move-result v0

    .line 39
    if-nez v0, :cond_1

    .line 40
    .line 41
    new-instance v0, Ljava/util/Date;

    .line 42
    .line 43
    invoke-direct {v0}, Ljava/util/Date;-><init>()V

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0, v10}, Ljava/util/Date;->before(Ljava/util/Date;)Z

    .line 47
    .line 48
    .line 49
    move-result v0

    .line 50
    if-eqz v0, :cond_0

    .line 51
    .line 52
    goto :goto_1

    .line 53
    :cond_0
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/TournamentPrizePoolEnum;->COMPLETED:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/TournamentPrizePoolEnum;

    .line 54
    .line 55
    :goto_0
    move-object/from16 v18, v0

    .line 56
    .line 57
    goto :goto_2

    .line 58
    :cond_1
    :goto_1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/TournamentPrizePoolEnum;->CURRENT:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/TournamentPrizePoolEnum;

    .line 59
    .line 60
    goto :goto_0

    .line 61
    :goto_2
    invoke-virtual/range {p0 .. p0}, Li81/a;->d()Lj81/a;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    invoke-virtual {v0}, Lj81/a;->c()J

    .line 66
    .line 67
    .line 68
    move-result-wide v0

    .line 69
    invoke-virtual/range {p0 .. p0}, Li81/a;->d()Lj81/a;

    .line 70
    .line 71
    .line 72
    move-result-object v2

    .line 73
    invoke-virtual {v2}, Lj81/a;->h()J

    .line 74
    .line 75
    .line 76
    move-result-wide v2

    .line 77
    sub-long/2addr v0, v2

    .line 78
    const/16 v2, 0x3e8

    .line 79
    .line 80
    int-to-long v2, v2

    .line 81
    mul-long v0, v0, v2

    .line 82
    .line 83
    sget-object v2, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 84
    .line 85
    invoke-virtual {v2, v0, v1}, Ljava/util/concurrent/TimeUnit;->toDays(J)J

    .line 86
    .line 87
    .line 88
    move-result-wide v0

    .line 89
    sget-object v2, Ll8/j;->a:Ll8/j;

    .line 90
    .line 91
    invoke-virtual/range {p0 .. p0}, Li81/a;->d()Lj81/a;

    .line 92
    .line 93
    .line 94
    move-result-object v3

    .line 95
    invoke-virtual {v3}, Lj81/a;->j()J

    .line 96
    .line 97
    .line 98
    move-result-wide v3

    .line 99
    long-to-double v3, v3

    .line 100
    sget-object v5, Lcom/xbet/onexcore/utils/ValueType;->PRIZE:Lcom/xbet/onexcore/utils/ValueType;

    .line 101
    .line 102
    invoke-virtual {v2, v3, v4, v5}, Ll8/j;->n(DLcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 103
    .line 104
    .line 105
    move-result-object v2

    .line 106
    new-instance v3, Ljava/lang/StringBuilder;

    .line 107
    .line 108
    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 109
    .line 110
    .line 111
    move-object/from16 v4, p2

    .line 112
    .line 113
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 114
    .line 115
    .line 116
    const-string v4, " "

    .line 117
    .line 118
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 119
    .line 120
    .line 121
    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 122
    .line 123
    .line 124
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 125
    .line 126
    .line 127
    move-result-object v20

    .line 128
    invoke-static/range {p3 .. p3}, Ljb1/h;->d(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)LL11/c;

    .line 129
    .line 130
    .line 131
    move-result-object v27

    .line 132
    new-instance v2, Ln21/a;

    .line 133
    .line 134
    invoke-virtual/range {p0 .. p0}, Li81/a;->d()Lj81/a;

    .line 135
    .line 136
    .line 137
    move-result-object v3

    .line 138
    invoke-virtual {v3}, Lj81/a;->g()Ljava/lang/String;

    .line 139
    .line 140
    .line 141
    move-result-object v19

    .line 142
    const/4 v6, 0x1

    .line 143
    const/4 v7, 0x0

    .line 144
    move-object/from16 v8, p1

    .line 145
    .line 146
    move-object/from16 v9, p3

    .line 147
    .line 148
    move-object/from16 v11, p4

    .line 149
    .line 150
    invoke-static/range {v6 .. v11}, Ljb1/h;->g(ZZLHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Date;Ljava/util/Locale;)Ljava/lang/String;

    .line 151
    .line 152
    .line 153
    move-result-object v21

    .line 154
    const/4 v7, 0x1

    .line 155
    invoke-static/range {v6 .. v11}, Ljb1/h;->g(ZZLHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Date;Ljava/util/Locale;)Ljava/lang/String;

    .line 156
    .line 157
    .line 158
    move-result-object v22

    .line 159
    const/4 v11, 0x0

    .line 160
    const/4 v12, 0x0

    .line 161
    move-object/from16 v13, p1

    .line 162
    .line 163
    move-object/from16 v14, p3

    .line 164
    .line 165
    move-object/from16 v16, p4

    .line 166
    .line 167
    invoke-static/range {v11 .. v16}, Ljb1/h;->g(ZZLHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Date;Ljava/util/Locale;)Ljava/lang/String;

    .line 168
    .line 169
    .line 170
    move-result-object v23

    .line 171
    const/4 v12, 0x1

    .line 172
    invoke-static/range {v11 .. v16}, Ljb1/h;->g(ZZLHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Date;Ljava/util/Locale;)Ljava/lang/String;

    .line 173
    .line 174
    .line 175
    move-result-object v24

    .line 176
    sget v3, Lpb/k;->days:I

    .line 177
    .line 178
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 179
    .line 180
    .line 181
    move-result-object v0

    .line 182
    const/4 v1, 0x1

    .line 183
    new-array v1, v1, [Ljava/lang/Object;

    .line 184
    .line 185
    const/4 v4, 0x0

    .line 186
    aput-object v0, v1, v4

    .line 187
    .line 188
    invoke-interface {v13, v3, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 189
    .line 190
    .line 191
    move-result-object v25

    .line 192
    move-object/from16 v8, p3

    .line 193
    .line 194
    move-object/from16 v11, p4

    .line 195
    .line 196
    move-object v9, v10

    .line 197
    move-object v7, v13

    .line 198
    move-object v10, v15

    .line 199
    move-object/from16 v6, v18

    .line 200
    .line 201
    invoke-static/range {v6 .. v11}, Ljb1/h;->f(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/TournamentPrizePoolEnum;LHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Date;Ljava/util/Date;Ljava/util/Locale;)Ljava/lang/String;

    .line 202
    .line 203
    .line 204
    move-result-object v26

    .line 205
    invoke-static/range {p3 .. p3}, Ljb1/h;->c(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)LL11/c;

    .line 206
    .line 207
    .line 208
    move-result-object v28

    .line 209
    invoke-static/range {p3 .. p3}, Ljb1/h;->e(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)LL11/c;

    .line 210
    .line 211
    .line 212
    move-result-object v29

    .line 213
    move-object/from16 v17, p3

    .line 214
    .line 215
    move-object/from16 v16, v2

    .line 216
    .line 217
    invoke-direct/range {v16 .. v29}, Ln21/a;-><init>(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/TournamentPrizePoolEnum;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;LL11/c;LL11/c;LL11/c;)V

    .line 218
    .line 219
    .line 220
    return-object v16
.end method
