.class public final Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;
.super Landroid/widget/LinearLayout;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_sport/eventcard/info/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\n\n\u0002\u0010\r\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0007\u0018\u0000 \u000c2\u00020\u00012\u00020\u0002:\u0001.B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ7\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u00072\u0006\u0010\u0011\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u00072\u0006\u0010\u0013\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0017\u0010\u0017\u001a\u00020\u000b2\u0008\u0008\u0001\u0010\u0016\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0017\u0010\u0017\u001a\u00020\u000b2\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0019\u00a2\u0006\u0004\u0008\u0017\u0010\u001aJ\u0015\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\u001c\u001a\u00020\u001b\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0015\u0010 \u001a\u00020\u000b2\u0006\u0010\u001f\u001a\u00020\u000e\u00a2\u0006\u0004\u0008 \u0010!J\u0015\u0010#\u001a\u00020\u000b2\u0006\u0010\"\u001a\u00020\u001b\u00a2\u0006\u0004\u0008#\u0010\u001eJ\u0015\u0010&\u001a\u00020\u000b2\u0006\u0010%\u001a\u00020$\u00a2\u0006\u0004\u0008&\u0010\'J\u0015\u0010)\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020\u000e\u00a2\u0006\u0004\u0008)\u0010!J\r\u0010*\u001a\u00020\u000b\u00a2\u0006\u0004\u0008*\u0010\rJ\r\u0010+\u001a\u00020\u000b\u00a2\u0006\u0004\u0008+\u0010\rJ\u000f\u0010,\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008,\u0010\rR\u0014\u00100\u001a\u00020-8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/\u00a8\u00061"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;",
        "Landroid/widget/LinearLayout;",
        "Lorg/xbet/uikit_sport/eventcard/info/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attr",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "b",
        "()V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "text",
        "setInfoText",
        "(I)V",
        "",
        "(Ljava/lang/CharSequence;)V",
        "",
        "milliSeconds",
        "setTime",
        "(J)V",
        "isVisible",
        "setTimerVisibility",
        "(Z)V",
        "intervalMs",
        "setUpdateInterval",
        "Lorg/xbet/uikit/components/timer/Timer$TimeDirection;",
        "timeDirection",
        "setTimeDirection",
        "(Lorg/xbet/uikit/components/timer/Timer$TimeDirection;)V",
        "hideAfterFinished",
        "setHideAfterFinished",
        "c",
        "d",
        "e",
        "LC31/q;",
        "a",
        "LC31/q;",
        "binding",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final b:Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final c:I


# instance fields
.field public final a:LC31/q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->b:Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->c:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 3
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object v0

    invoke-static {v0, p0}, LC31/q;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/q;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    const/4 v1, 0x1

    .line 7
    invoke-virtual {p0, v1}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 8
    sget-object v1, Lm31/g;->EventCardInfo:[I

    const/4 v2, 0x0

    .line 9
    invoke-virtual {p1, p2, v1, p3, v2}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p2

    .line 10
    sget p3, Lm31/g;->EventCardInfo_infoText:I

    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p3

    invoke-static {p2, p1, p3}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->setInfoText(Ljava/lang/CharSequence;)V

    .line 11
    invoke-virtual {p2}, Landroid/content/res/TypedArray;->recycle()V

    .line 12
    iget-object p1, v0, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    new-instance p2, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive$2;

    invoke-direct {p2, p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive$2;-><init>(Ljava/lang/Object;)V

    invoke-virtual {p1, p2}, Lorg/xbet/uikit/components/timer/Timer;->setOnTimerFinished(Lkotlin/jvm/functions/Function0;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->eventCardInfoLiveStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static final synthetic a(Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->b()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final b()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 2
    .line 3
    invoke-virtual {v0}, LC31/q;->getRoot()Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, v0, LC31/q;->b:Landroid/widget/TextView;

    .line 8
    .line 9
    invoke-virtual {v2}, Landroid/view/View;->getVisibility()I

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    const/4 v3, 0x0

    .line 14
    if-nez v2, :cond_0

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    iget-object v0, v0, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 18
    .line 19
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    if-nez v0, :cond_1

    .line 24
    .line 25
    :goto_0
    const/4 v0, 0x1

    .line 26
    goto :goto_1

    .line 27
    :cond_1
    const/4 v0, 0x0

    .line 28
    :goto_1
    if-eqz v0, :cond_2

    .line 29
    .line 30
    goto :goto_2

    .line 31
    :cond_2
    const/16 v3, 0x8

    .line 32
    .line 33
    :goto_2
    invoke-virtual {v1, v3}, Landroid/view/View;->setVisibility(I)V

    .line 34
    .line 35
    .line 36
    return-void
.end method


# virtual methods
.method public final c()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 2
    .line 3
    iget-object v0, v0, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/components/timer/Timer;->t()V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->e()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final d()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 2
    .line 3
    iget-object v0, v0, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/components/timer/Timer;->u()V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->e()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final e()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 2
    .line 3
    iget-object v1, v0, LC31/q;->b:Landroid/widget/TextView;

    .line 4
    .line 5
    iget-object v0, v0, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-nez v0, :cond_0

    .line 12
    .line 13
    const/4 v0, 0x1

    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/4 v0, 0x2

    .line 16
    :goto_0
    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 2

    .line 1
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 2
    .line 3
    iget-object p1, p1, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    const/4 p2, 0x0

    .line 10
    if-nez p1, :cond_5

    .line 11
    .line 12
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 13
    .line 14
    iget-object p1, p1, LC31/q;->b:Landroid/widget/TextView;

    .line 15
    .line 16
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 17
    .line 18
    .line 19
    move-result p1

    .line 20
    if-nez p1, :cond_5

    .line 21
    .line 22
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    div-int/lit8 p1, p1, 0x2

    .line 27
    .line 28
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 29
    .line 30
    iget-object p3, p3, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 31
    .line 32
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredWidth()I

    .line 33
    .line 34
    .line 35
    move-result p3

    .line 36
    div-int/lit8 p3, p3, 0x2

    .line 37
    .line 38
    sub-int/2addr p1, p3

    .line 39
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 40
    .line 41
    iget-object p3, p3, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 42
    .line 43
    invoke-virtual {p3}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 44
    .line 45
    .line 46
    move-result-object p3

    .line 47
    instance-of p4, p3, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 48
    .line 49
    const/4 p5, 0x0

    .line 50
    if-eqz p4, :cond_0

    .line 51
    .line 52
    check-cast p3, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 53
    .line 54
    goto :goto_0

    .line 55
    :cond_0
    move-object p3, p5

    .line 56
    :goto_0
    if-eqz p3, :cond_1

    .line 57
    .line 58
    iget p3, p3, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 59
    .line 60
    goto :goto_1

    .line 61
    :cond_1
    const/4 p3, 0x0

    .line 62
    :goto_1
    iget-object p4, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 63
    .line 64
    iget-object p4, p4, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 65
    .line 66
    invoke-virtual {p4}, Landroid/view/View;->getMeasuredHeight()I

    .line 67
    .line 68
    .line 69
    move-result p4

    .line 70
    add-int/2addr p4, p3

    .line 71
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 72
    .line 73
    iget-object v0, v0, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 74
    .line 75
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 76
    .line 77
    .line 78
    move-result v1

    .line 79
    add-int/2addr v1, p1

    .line 80
    invoke-virtual {v0, p1, p3, v1, p4}, Landroid/view/View;->layout(IIII)V

    .line 81
    .line 82
    .line 83
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 84
    .line 85
    iget-object p1, p1, LC31/q;->b:Landroid/widget/TextView;

    .line 86
    .line 87
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    instance-of p3, p1, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 92
    .line 93
    if-eqz p3, :cond_2

    .line 94
    .line 95
    move-object p5, p1

    .line 96
    check-cast p5, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 97
    .line 98
    :cond_2
    if-eqz p5, :cond_3

    .line 99
    .line 100
    iget p1, p5, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 101
    .line 102
    goto :goto_2

    .line 103
    :cond_3
    const/4 p1, 0x0

    .line 104
    :goto_2
    add-int/2addr p4, p1

    .line 105
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 106
    .line 107
    iget-object p1, p1, LC31/q;->b:Landroid/widget/TextView;

    .line 108
    .line 109
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 110
    .line 111
    .line 112
    move-result-object p1

    .line 113
    instance-of p3, p1, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 114
    .line 115
    if-eqz p3, :cond_4

    .line 116
    .line 117
    check-cast p1, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 118
    .line 119
    invoke-virtual {p1}, Landroid/view/ViewGroup$MarginLayoutParams;->getMarginStart()I

    .line 120
    .line 121
    .line 122
    move-result p2

    .line 123
    :cond_4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 124
    .line 125
    iget-object p1, p1, LC31/q;->b:Landroid/widget/TextView;

    .line 126
    .line 127
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 128
    .line 129
    .line 130
    move-result p1

    .line 131
    add-int/2addr p1, p2

    .line 132
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 133
    .line 134
    iget-object p3, p3, LC31/q;->b:Landroid/widget/TextView;

    .line 135
    .line 136
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 137
    .line 138
    .line 139
    move-result p5

    .line 140
    add-int/2addr p5, p4

    .line 141
    invoke-virtual {p3, p2, p4, p1, p5}, Landroid/view/View;->layout(IIII)V

    .line 142
    .line 143
    .line 144
    return-void

    .line 145
    :cond_5
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 146
    .line 147
    iget-object p1, p1, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 148
    .line 149
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 150
    .line 151
    .line 152
    move-result p1

    .line 153
    if-nez p1, :cond_7

    .line 154
    .line 155
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 156
    .line 157
    iget-object p1, p1, LC31/q;->b:Landroid/widget/TextView;

    .line 158
    .line 159
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 160
    .line 161
    .line 162
    move-result p1

    .line 163
    if-nez p1, :cond_6

    .line 164
    .line 165
    goto :goto_3

    .line 166
    :cond_6
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 167
    .line 168
    .line 169
    move-result p1

    .line 170
    div-int/lit8 p1, p1, 0x2

    .line 171
    .line 172
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 173
    .line 174
    iget-object p2, p2, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 175
    .line 176
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredWidth()I

    .line 177
    .line 178
    .line 179
    move-result p2

    .line 180
    div-int/lit8 p2, p2, 0x2

    .line 181
    .line 182
    sub-int/2addr p1, p2

    .line 183
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 184
    .line 185
    .line 186
    move-result p2

    .line 187
    div-int/lit8 p2, p2, 0x2

    .line 188
    .line 189
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 190
    .line 191
    iget-object p3, p3, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 192
    .line 193
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 194
    .line 195
    .line 196
    move-result p3

    .line 197
    div-int/lit8 p3, p3, 0x2

    .line 198
    .line 199
    sub-int/2addr p2, p3

    .line 200
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 201
    .line 202
    iget-object p3, p3, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 203
    .line 204
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 205
    .line 206
    .line 207
    move-result p3

    .line 208
    add-int/2addr p3, p2

    .line 209
    iget-object p4, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 210
    .line 211
    iget-object p4, p4, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 212
    .line 213
    invoke-virtual {p4}, Landroid/view/View;->getMeasuredWidth()I

    .line 214
    .line 215
    .line 216
    move-result p5

    .line 217
    add-int/2addr p5, p1

    .line 218
    invoke-virtual {p4, p1, p2, p5, p3}, Landroid/view/View;->layout(IIII)V

    .line 219
    .line 220
    .line 221
    return-void

    .line 222
    :cond_7
    :goto_3
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 223
    .line 224
    iget-object p1, p1, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 225
    .line 226
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 227
    .line 228
    .line 229
    move-result p1

    .line 230
    if-nez p1, :cond_8

    .line 231
    .line 232
    return-void

    .line 233
    :cond_8
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 234
    .line 235
    iget-object p1, p1, LC31/q;->b:Landroid/widget/TextView;

    .line 236
    .line 237
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 238
    .line 239
    .line 240
    move-result p1

    .line 241
    if-nez p1, :cond_a

    .line 242
    .line 243
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 244
    .line 245
    .line 246
    move-result p1

    .line 247
    div-int/lit8 p1, p1, 0x2

    .line 248
    .line 249
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 250
    .line 251
    iget-object p3, p3, LC31/q;->b:Landroid/widget/TextView;

    .line 252
    .line 253
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 254
    .line 255
    .line 256
    move-result p3

    .line 257
    div-int/lit8 p3, p3, 0x2

    .line 258
    .line 259
    sub-int/2addr p1, p3

    .line 260
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 261
    .line 262
    iget-object p3, p3, LC31/q;->b:Landroid/widget/TextView;

    .line 263
    .line 264
    invoke-virtual {p3}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 265
    .line 266
    .line 267
    move-result-object p3

    .line 268
    instance-of p4, p3, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 269
    .line 270
    if-eqz p4, :cond_9

    .line 271
    .line 272
    check-cast p3, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 273
    .line 274
    invoke-virtual {p3}, Landroid/view/ViewGroup$MarginLayoutParams;->getMarginStart()I

    .line 275
    .line 276
    .line 277
    move-result p2

    .line 278
    :cond_9
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 279
    .line 280
    iget-object p3, p3, LC31/q;->b:Landroid/widget/TextView;

    .line 281
    .line 282
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredWidth()I

    .line 283
    .line 284
    .line 285
    move-result p3

    .line 286
    add-int/2addr p3, p2

    .line 287
    iget-object p4, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 288
    .line 289
    iget-object p4, p4, LC31/q;->b:Landroid/widget/TextView;

    .line 290
    .line 291
    invoke-virtual {p4}, Landroid/view/View;->getMeasuredHeight()I

    .line 292
    .line 293
    .line 294
    move-result p5

    .line 295
    add-int/2addr p5, p1

    .line 296
    invoke-virtual {p4, p2, p1, p3, p5}, Landroid/view/View;->layout(IIII)V

    .line 297
    .line 298
    .line 299
    :cond_a
    return-void
.end method

.method public final setHideAfterFinished(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 2
    .line 3
    iget-object v0, v0, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/timer/Timer;->setHideAfterFinished(Z)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setInfoText(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->setInfoText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setInfoText(Ljava/lang/CharSequence;)V
    .locals 2

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    iget-object v0, v0, LC31/q;->b:Landroid/widget/TextView;

    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    .line 4
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result p1

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    if-eqz p1, :cond_2

    const/16 v1, 0x8

    .line 5
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 6
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->e()V

    .line 7
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->b()V

    return-void
.end method

.method public final setTime(J)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 2
    .line 3
    iget-object v0, v0, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit/components/timer/Timer;->setTime(J)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setTimeDirection(Lorg/xbet/uikit/components/timer/Timer$TimeDirection;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit/components/timer/Timer$TimeDirection;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 2
    .line 3
    iget-object v0, v0, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/timer/Timer;->setTimeDirection(Lorg/xbet/uikit/components/timer/Timer$TimeDirection;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setTimerVisibility(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 2
    .line 3
    iget-object v0, v0, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    const/4 p1, 0x0

    .line 8
    goto :goto_0

    .line 9
    :cond_0
    const/16 p1, 0x8

    .line 10
    .line 11
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->e()V

    .line 15
    .line 16
    .line 17
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->b()V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final setUpdateInterval(J)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLive;->a:LC31/q;

    .line 2
    .line 3
    iget-object v0, v0, LC31/q;->c:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit/components/timer/Timer;->setUpdateTimeIntervalMs(J)V

    .line 6
    .line 7
    .line 8
    return-void
.end method
