.class public final synthetic LHP0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LHP0/c;


# direct methods
.method public synthetic constructor <init>(LHP0/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LHP0/b;->a:LHP0/c;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LHP0/b;->a:LHP0/c;

    check-cast p1, Ljava/lang/Throwable;

    invoke-static {v0, p1}, LHP0/c;->f(LHP0/c;Ljava/lang/Throwable;)L<PERSON>lin/Unit;

    move-result-object p1

    return-object p1
.end method
