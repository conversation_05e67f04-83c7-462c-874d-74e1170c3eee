.class public final LQK0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000~\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008 \u0008\u0007\u0018\u00002\u00020\u0001B\u0081\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u00a2\u0006\u0004\u0008 \u0010!J\'\u0010)\u001a\u00020(2\u0006\u0010#\u001a\u00020\"2\u0006\u0010%\u001a\u00020$2\u0006\u0010\'\u001a\u00020&H\u0000\u00a2\u0006\u0004\u0008)\u0010*R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008)\u0010+R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00105R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00109R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u0010;R\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=R\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010?R\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010AR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010CR\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010ER\u0014\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010G\u00a8\u0006H"
    }
    d2 = {
        "LQK0/d;",
        "LQW0/a;",
        "LQW0/c;",
        "coroutinesLib",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lc8/b;",
        "deviceDataSource",
        "Lf8/g;",
        "serviceGenerator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Li8/m;",
        "getThemeUseCase",
        "LHX0/e;",
        "resourceManager",
        "LSX0/a;",
        "lottieConfigurator",
        "Lorg/xbet/onexdatabase/OnexDatabase;",
        "onexDatabase",
        "Lc8/h;",
        "requestParamsDataSource",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "LTn/a;",
        "sportRepository",
        "LEN0/f;",
        "statisticCoreFeature",
        "LGL0/a;",
        "stadiumFeature",
        "LaN0/a;",
        "statisticResultsScreenFactory",
        "<init>",
        "(LQW0/c;Lorg/xbet/ui_common/utils/M;Lc8/b;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LHX0/e;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;LSX0/c;LTn/a;LEN0/f;LGL0/a;LaN0/a;)V",
        "LwX0/c;",
        "router",
        "",
        "gameId",
        "",
        "sportId",
        "LQK0/c;",
        "a",
        "(LwX0/c;Ljava/lang/String;J)LQK0/c;",
        "LQW0/c;",
        "b",
        "Lorg/xbet/ui_common/utils/M;",
        "c",
        "Lc8/b;",
        "d",
        "Lf8/g;",
        "e",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "f",
        "Li8/m;",
        "g",
        "LHX0/e;",
        "h",
        "LSX0/a;",
        "i",
        "Lorg/xbet/onexdatabase/OnexDatabase;",
        "j",
        "Lc8/h;",
        "k",
        "LSX0/c;",
        "l",
        "LTn/a;",
        "m",
        "LEN0/f;",
        "n",
        "LGL0/a;",
        "o",
        "LaN0/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LQW0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lc8/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Li8/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lorg/xbet/onexdatabase/OnexDatabase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:LTn/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:LEN0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:LGL0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:LaN0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(LQW0/c;Lorg/xbet/ui_common/utils/M;Lc8/b;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LHX0/e;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;LSX0/c;LTn/a;LEN0/f;LGL0/a;LaN0/a;)V
    .locals 0
    .param p1    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lc8/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Li8/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/onexdatabase/OnexDatabase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LTn/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # LEN0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LGL0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # LaN0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LQK0/d;->a:LQW0/c;

    .line 5
    .line 6
    iput-object p2, p0, LQK0/d;->b:Lorg/xbet/ui_common/utils/M;

    .line 7
    .line 8
    iput-object p3, p0, LQK0/d;->c:Lc8/b;

    .line 9
    .line 10
    iput-object p4, p0, LQK0/d;->d:Lf8/g;

    .line 11
    .line 12
    iput-object p5, p0, LQK0/d;->e:Lorg/xbet/ui_common/utils/internet/a;

    .line 13
    .line 14
    iput-object p6, p0, LQK0/d;->f:Li8/m;

    .line 15
    .line 16
    iput-object p7, p0, LQK0/d;->g:LHX0/e;

    .line 17
    .line 18
    iput-object p8, p0, LQK0/d;->h:LSX0/a;

    .line 19
    .line 20
    iput-object p9, p0, LQK0/d;->i:Lorg/xbet/onexdatabase/OnexDatabase;

    .line 21
    .line 22
    iput-object p10, p0, LQK0/d;->j:Lc8/h;

    .line 23
    .line 24
    iput-object p11, p0, LQK0/d;->k:LSX0/c;

    .line 25
    .line 26
    iput-object p12, p0, LQK0/d;->l:LTn/a;

    .line 27
    .line 28
    iput-object p13, p0, LQK0/d;->m:LEN0/f;

    .line 29
    .line 30
    iput-object p14, p0, LQK0/d;->n:LGL0/a;

    .line 31
    .line 32
    iput-object p15, p0, LQK0/d;->o:LaN0/a;

    .line 33
    .line 34
    return-void
.end method


# virtual methods
.method public final a(LwX0/c;Ljava/lang/String;J)LQK0/c;
    .locals 21
    .param p1    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, LQK0/a;->a()LQK0/c$a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, v0, LQK0/d;->a:LQW0/c;

    .line 8
    .line 9
    iget-object v7, v0, LQK0/d;->b:Lorg/xbet/ui_common/utils/M;

    .line 10
    .line 11
    iget-object v8, v0, LQK0/d;->c:Lc8/b;

    .line 12
    .line 13
    iget-object v9, v0, LQK0/d;->d:Lf8/g;

    .line 14
    .line 15
    iget-object v11, v0, LQK0/d;->e:Lorg/xbet/ui_common/utils/internet/a;

    .line 16
    .line 17
    iget-object v12, v0, LQK0/d;->f:Li8/m;

    .line 18
    .line 19
    iget-object v13, v0, LQK0/d;->g:LHX0/e;

    .line 20
    .line 21
    iget-object v6, v0, LQK0/d;->k:LSX0/c;

    .line 22
    .line 23
    iget-object v3, v0, LQK0/d;->h:LSX0/a;

    .line 24
    .line 25
    iget-object v4, v0, LQK0/d;->i:Lorg/xbet/onexdatabase/OnexDatabase;

    .line 26
    .line 27
    iget-object v5, v0, LQK0/d;->j:Lc8/h;

    .line 28
    .line 29
    iget-object v10, v0, LQK0/d;->l:LTn/a;

    .line 30
    .line 31
    move-object/from16 v16, v3

    .line 32
    .line 33
    iget-object v3, v0, LQK0/d;->m:LEN0/f;

    .line 34
    .line 35
    move-object/from16 v17, v4

    .line 36
    .line 37
    iget-object v4, v0, LQK0/d;->n:LGL0/a;

    .line 38
    .line 39
    iget-object v14, v0, LQK0/d;->o:LaN0/a;

    .line 40
    .line 41
    move-object/from16 v18, v5

    .line 42
    .line 43
    move-object/from16 v19, v10

    .line 44
    .line 45
    move-object/from16 v20, v14

    .line 46
    .line 47
    move-object/from16 v5, p1

    .line 48
    .line 49
    move-object/from16 v10, p2

    .line 50
    .line 51
    move-wide/from16 v14, p3

    .line 52
    .line 53
    invoke-interface/range {v1 .. v20}, LQK0/c$a;->a(LQW0/c;LEN0/f;LGL0/a;LwX0/c;LSX0/c;Lorg/xbet/ui_common/utils/M;Lc8/b;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LHX0/e;JLSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;LTn/a;LaN0/a;)LQK0/c;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    return-object v1
.end method
