.class public final LIB0/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LIB0/g;


# instance fields
.field public final a:Lorg/xbet/sportgame/markets/impl/presentation/markets/j;


# direct methods
.method public constructor <init>(Lorg/xbet/sportgame/markets/impl/presentation/markets/j;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LIB0/h;->a:Lorg/xbet/sportgame/markets/impl/presentation/markets/j;

    .line 5
    .line 6
    return-void
.end method

.method public static c(Lorg/xbet/sportgame/markets/impl/presentation/markets/j;)Ldagger/internal/h;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/sportgame/markets/impl/presentation/markets/j;",
            ")",
            "Ldagger/internal/h<",
            "LIB0/g;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, LIB0/h;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LIB0/h;-><init>(Lorg/xbet/sportgame/markets/impl/presentation/markets/j;)V

    .line 4
    .line 5
    .line 6
    invoke-static {v0}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    return-object p0
.end method


# virtual methods
.method public bridge synthetic a(Landroidx/lifecycle/Q;)Landroidx/lifecycle/b0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LIB0/h;->b(Landroidx/lifecycle/Q;)Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public b(Landroidx/lifecycle/Q;)Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, LIB0/h;->a:Lorg/xbet/sportgame/markets/impl/presentation/markets/j;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/sportgame/markets/impl/presentation/markets/j;->b(Landroidx/lifecycle/Q;)Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method
