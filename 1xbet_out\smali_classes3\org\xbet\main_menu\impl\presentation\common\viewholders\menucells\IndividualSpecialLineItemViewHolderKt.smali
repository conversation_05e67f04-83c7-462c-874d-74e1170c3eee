.class public final Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/IndividualSpecialLineItemViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a/\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lkotlin/Function1;",
        "LN80/c$u;",
        "",
        "onItemClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "h",
        "(Lkotlin/jvm/functions/Function1;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/X;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/IndividualSpecialLineItemViewHolderKt;->i(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/X;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/IndividualSpecialLineItemViewHolderKt;->k(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lorg/xbet/uikit/components/cells/right/CellRightBanner;Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/IndividualSpecialLineItemViewHolderKt;->m(Lorg/xbet/uikit/components/cells/right/CellRightBanner;Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/IndividualSpecialLineItemViewHolderKt;->l(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/IndividualSpecialLineItemViewHolderKt;->o(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Lorg/xbet/uikit/components/cells/right/CellRightBanner;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/IndividualSpecialLineItemViewHolderKt;->n(Lorg/xbet/uikit/components/cells/right/CellRightBanner;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/IndividualSpecialLineItemViewHolderKt;->j(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final h(Lkotlin/jvm/functions/Function1;)LA4/c;
    .locals 4
    .param p0    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LN80/c$u;",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LT80/B0;

    .line 2
    .line 3
    invoke-direct {v0}, LT80/B0;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LT80/C0;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LT80/C0;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/IndividualSpecialLineItemViewHolderKt$getIndividualSpecialLineItemDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/IndividualSpecialLineItemViewHolderKt$getIndividualSpecialLineItemDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/IndividualSpecialLineItemViewHolderKt$getIndividualSpecialLineItemDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/IndividualSpecialLineItemViewHolderKt$getIndividualSpecialLineItemDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final i(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/X;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, Lv80/X;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)Lv80/X;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final j(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lv80/X;

    .line 6
    .line 7
    invoke-virtual {v0}, Lv80/X;->b()Lorg/xbet/uikit/components/cells/MenuCell;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    new-instance v1, LT80/D0;

    .line 12
    .line 13
    invoke-direct {v1, p0, p1}, LT80/D0;-><init>(Lkotlin/jvm/functions/Function1;LB4/a;)V

    .line 14
    .line 15
    .line 16
    const/4 p0, 0x1

    .line 17
    const/4 v2, 0x0

    .line 18
    invoke-static {v0, v2, v1, p0, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 19
    .line 20
    .line 21
    new-instance p0, LT80/E0;

    .line 22
    .line 23
    invoke-direct {p0, p1}, LT80/E0;-><init>(LB4/a;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 27
    .line 28
    .line 29
    new-instance p0, LT80/F0;

    .line 30
    .line 31
    invoke-direct {p0, p1}, LT80/F0;-><init>(LB4/a;)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p1, p0}, LB4/a;->t(Lkotlin/jvm/functions/Function0;)V

    .line 35
    .line 36
    .line 37
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 38
    .line 39
    return-object p0
.end method

.method public static final k(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final l(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 12

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LN80/c$t;

    .line 6
    .line 7
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, Lv80/X;

    .line 12
    .line 13
    iget-object v0, v0, Lv80/X;->d:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 14
    .line 15
    sget-object v1, Lub/b;->a:Lub/b;

    .line 16
    .line 17
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    invoke-virtual {p1}, LN80/c$t;->s()I

    .line 22
    .line 23
    .line 24
    move-result v3

    .line 25
    const/4 v5, 0x4

    .line 26
    const/4 v6, 0x0

    .line 27
    const/4 v4, 0x0

    .line 28
    invoke-static/range {v1 .. v6}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 29
    .line 30
    .line 31
    move-result v2

    .line 32
    invoke-virtual {v0, v2}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->setIconTint(I)V

    .line 33
    .line 34
    .line 35
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    check-cast v0, Lv80/X;

    .line 40
    .line 41
    iget-object v0, v0, Lv80/X;->d:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 42
    .line 43
    invoke-virtual {p1}, LN80/c$t;->o()I

    .line 44
    .line 45
    .line 46
    move-result v2

    .line 47
    invoke-virtual {v0, v2}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->setIconResource(I)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    check-cast v0, Lv80/X;

    .line 55
    .line 56
    iget-object v0, v0, Lv80/X;->d:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 57
    .line 58
    invoke-virtual {p1}, LN80/c$t;->j()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v7

    .line 62
    invoke-virtual {p1}, LN80/c$t;->B()Ljava/lang/String;

    .line 63
    .line 64
    .line 65
    move-result-object v8

    .line 66
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    invoke-virtual {p1}, LN80/c$t;->A()I

    .line 71
    .line 72
    .line 73
    move-result v3

    .line 74
    invoke-static/range {v1 .. v6}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 75
    .line 76
    .line 77
    move-result v2

    .line 78
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 79
    .line 80
    .line 81
    move-result-object v9

    .line 82
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 83
    .line 84
    .line 85
    move-result-object v2

    .line 86
    sget v3, LlZ0/d;->uikitBackground:I

    .line 87
    .line 88
    invoke-static/range {v1 .. v6}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 89
    .line 90
    .line 91
    move-result v2

    .line 92
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 93
    .line 94
    .line 95
    move-result-object v10

    .line 96
    invoke-virtual {p1}, LN80/c$t;->o()I

    .line 97
    .line 98
    .line 99
    move-result v2

    .line 100
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 101
    .line 102
    .line 103
    move-result-object v11

    .line 104
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 105
    .line 106
    .line 107
    move-result-object v2

    .line 108
    invoke-virtual {p1}, LN80/c$t;->s()I

    .line 109
    .line 110
    .line 111
    move-result v3

    .line 112
    invoke-static/range {v1 .. v6}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 113
    .line 114
    .line 115
    move-result v1

    .line 116
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    move-object v2, v0

    .line 121
    move-object v3, v7

    .line 122
    move-object v4, v8

    .line 123
    move-object v5, v9

    .line 124
    move-object v6, v10

    .line 125
    move-object v7, v11

    .line 126
    move-object v8, v1

    .line 127
    invoke-virtual/range {v2 .. v8}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->S0(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)V

    .line 128
    .line 129
    .line 130
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 131
    .line 132
    .line 133
    move-result-object v0

    .line 134
    check-cast v0, Lv80/X;

    .line 135
    .line 136
    iget-object v0, v0, Lv80/X;->d:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 137
    .line 138
    invoke-virtual {p1}, LN80/c$t;->u()Z

    .line 139
    .line 140
    .line 141
    move-result v1

    .line 142
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->setBadgeVisible(Z)V

    .line 143
    .line 144
    .line 145
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 146
    .line 147
    .line 148
    move-result-object v0

    .line 149
    check-cast v0, Lv80/X;

    .line 150
    .line 151
    iget-object v0, v0, Lv80/X;->e:Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;

    .line 152
    .line 153
    invoke-virtual {p1}, LN80/c$t;->getTitle()Ljava/lang/String;

    .line 154
    .line 155
    .line 156
    move-result-object v1

    .line 157
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;->setTitle(Ljava/lang/CharSequence;)V

    .line 158
    .line 159
    .line 160
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 161
    .line 162
    .line 163
    move-result-object v0

    .line 164
    check-cast v0, Lv80/X;

    .line 165
    .line 166
    iget-object v0, v0, Lv80/X;->e:Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;

    .line 167
    .line 168
    invoke-virtual {p1}, LN80/c$t;->C()Ljava/lang/String;

    .line 169
    .line 170
    .line 171
    move-result-object v1

    .line 172
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;->setSubtitle(Ljava/lang/CharSequence;)V

    .line 173
    .line 174
    .line 175
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 176
    .line 177
    .line 178
    move-result-object v0

    .line 179
    check-cast v0, Lv80/X;

    .line 180
    .line 181
    iget-object v0, v0, Lv80/X;->c:Lorg/xbet/uikit/components/cells/right/CellRightBanner;

    .line 182
    .line 183
    const/4 v1, 0x0

    .line 184
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/right/CellRightBanner;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 185
    .line 186
    .line 187
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 188
    .line 189
    .line 190
    move-result-object p0

    .line 191
    check-cast p0, Lv80/X;

    .line 192
    .line 193
    iget-object p0, p0, Lv80/X;->c:Lorg/xbet/uikit/components/cells/right/CellRightBanner;

    .line 194
    .line 195
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 196
    .line 197
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 198
    .line 199
    .line 200
    move-result-object v1

    .line 201
    invoke-virtual {p1}, LN80/c$t;->f()Ljava/lang/String;

    .line 202
    .line 203
    .line 204
    move-result-object v2

    .line 205
    new-instance v6, LT80/G0;

    .line 206
    .line 207
    invoke-direct {v6, p0}, LT80/G0;-><init>(Lorg/xbet/uikit/components/cells/right/CellRightBanner;)V

    .line 208
    .line 209
    .line 210
    new-instance v7, LT80/H0;

    .line 211
    .line 212
    invoke-direct {v7, p0}, LT80/H0;-><init>(Lorg/xbet/uikit/components/cells/right/CellRightBanner;)V

    .line 213
    .line 214
    .line 215
    const/16 v8, 0xe

    .line 216
    .line 217
    const/4 v9, 0x0

    .line 218
    const/4 v3, 0x0

    .line 219
    const/4 v4, 0x0

    .line 220
    const/4 v5, 0x0

    .line 221
    invoke-static/range {v0 .. v9}, LCX0/l;->M(LCX0/l;Landroid/content/Context;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;[LYW0/d;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 222
    .line 223
    .line 224
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 225
    .line 226
    return-object p0
.end method

.method public static final m(Lorg/xbet/uikit/components/cells/right/CellRightBanner;Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit/components/cells/right/CellRightBanner;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    invoke-virtual {p0, p1}, Lorg/xbet/uikit/components/cells/right/CellRightBanner;->onRtlPropertiesChanged(I)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final n(Lorg/xbet/uikit/components/cells/right/CellRightBanner;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    const/4 p1, 0x0

    .line 2
    invoke-virtual {p0, p1}, Lorg/xbet/uikit/components/cells/right/CellRightBanner;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 3
    .line 4
    .line 5
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 6
    .line 7
    return-object p0
.end method

.method public static final o(LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lv80/X;

    .line 6
    .line 7
    iget-object v0, v0, Lv80/X;->d:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 8
    .line 9
    invoke-virtual {v0}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->M0()V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    check-cast p0, Lv80/X;

    .line 17
    .line 18
    iget-object p0, p0, Lv80/X;->d:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 19
    .line 20
    invoke-virtual {p0}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->L0()V

    .line 21
    .line 22
    .line 23
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 24
    .line 25
    return-object p0
.end method
