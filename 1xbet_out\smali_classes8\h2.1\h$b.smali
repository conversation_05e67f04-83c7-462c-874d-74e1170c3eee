.class public final Lh2/h$b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lh2/h;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:LN1/T;

.field public final b:Lh2/v;

.field public final c:Lt1/G;

.field public d:Lh2/w;

.field public e:Lh2/c;

.field public f:I

.field public g:I

.field public h:I

.field public i:I

.field public final j:Ljava/lang/String;

.field public final k:Lt1/G;

.field public final l:Lt1/G;

.field public m:Z


# direct methods
.method public constructor <init>(LN1/T;Lh2/w;Lh2/c;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lh2/h$b;->a:LN1/T;

    .line 5
    .line 6
    iput-object p2, p0, Lh2/h$b;->d:Lh2/w;

    .line 7
    .line 8
    iput-object p3, p0, Lh2/h$b;->e:Lh2/c;

    .line 9
    .line 10
    iput-object p4, p0, Lh2/h$b;->j:Ljava/lang/String;

    .line 11
    .line 12
    new-instance p1, Lh2/v;

    .line 13
    .line 14
    invoke-direct {p1}, Lh2/v;-><init>()V

    .line 15
    .line 16
    .line 17
    iput-object p1, p0, Lh2/h$b;->b:Lh2/v;

    .line 18
    .line 19
    new-instance p1, Lt1/G;

    .line 20
    .line 21
    invoke-direct {p1}, Lt1/G;-><init>()V

    .line 22
    .line 23
    .line 24
    iput-object p1, p0, Lh2/h$b;->c:Lt1/G;

    .line 25
    .line 26
    new-instance p1, Lt1/G;

    .line 27
    .line 28
    const/4 p4, 0x1

    .line 29
    invoke-direct {p1, p4}, Lt1/G;-><init>(I)V

    .line 30
    .line 31
    .line 32
    iput-object p1, p0, Lh2/h$b;->k:Lt1/G;

    .line 33
    .line 34
    new-instance p1, Lt1/G;

    .line 35
    .line 36
    invoke-direct {p1}, Lt1/G;-><init>()V

    .line 37
    .line 38
    .line 39
    iput-object p1, p0, Lh2/h$b;->l:Lt1/G;

    .line 40
    .line 41
    invoke-virtual {p0, p2, p3}, Lh2/h$b;->j(Lh2/w;Lh2/c;)V

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method public static synthetic a(Lh2/h$b;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lh2/h$b;->m:Z

    .line 2
    .line 3
    return p0
.end method

.method public static synthetic b(Lh2/h$b;Z)Z
    .locals 0

    .line 1
    iput-boolean p1, p0, Lh2/h$b;->m:Z

    .line 2
    .line 3
    return p1
.end method


# virtual methods
.method public c()I
    .locals 2

    .line 1
    iget-boolean v0, p0, Lh2/h$b;->m:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lh2/h$b;->d:Lh2/w;

    .line 6
    .line 7
    iget-object v0, v0, Lh2/w;->g:[I

    .line 8
    .line 9
    iget v1, p0, Lh2/h$b;->f:I

    .line 10
    .line 11
    aget v0, v0, v1

    .line 12
    .line 13
    goto :goto_0

    .line 14
    :cond_0
    iget-object v0, p0, Lh2/h$b;->b:Lh2/v;

    .line 15
    .line 16
    iget-object v0, v0, Lh2/v;->k:[Z

    .line 17
    .line 18
    iget v1, p0, Lh2/h$b;->f:I

    .line 19
    .line 20
    aget-boolean v0, v0, v1

    .line 21
    .line 22
    if-eqz v0, :cond_1

    .line 23
    .line 24
    const/4 v0, 0x1

    .line 25
    goto :goto_0

    .line 26
    :cond_1
    const/4 v0, 0x0

    .line 27
    :goto_0
    invoke-virtual {p0}, Lh2/h$b;->g()Lh2/u;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    if-eqz v1, :cond_2

    .line 32
    .line 33
    const/high16 v1, 0x40000000    # 2.0f

    .line 34
    .line 35
    or-int/2addr v0, v1

    .line 36
    :cond_2
    return v0
.end method

.method public d()J
    .locals 3

    .line 1
    iget-boolean v0, p0, Lh2/h$b;->m:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lh2/h$b;->d:Lh2/w;

    .line 6
    .line 7
    iget-object v0, v0, Lh2/w;->c:[J

    .line 8
    .line 9
    iget v1, p0, Lh2/h$b;->f:I

    .line 10
    .line 11
    aget-wide v1, v0, v1

    .line 12
    .line 13
    return-wide v1

    .line 14
    :cond_0
    iget-object v0, p0, Lh2/h$b;->b:Lh2/v;

    .line 15
    .line 16
    iget-object v0, v0, Lh2/v;->g:[J

    .line 17
    .line 18
    iget v1, p0, Lh2/h$b;->h:I

    .line 19
    .line 20
    aget-wide v1, v0, v1

    .line 21
    .line 22
    return-wide v1
.end method

.method public e()J
    .locals 3

    .line 1
    iget-boolean v0, p0, Lh2/h$b;->m:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lh2/h$b;->d:Lh2/w;

    .line 6
    .line 7
    iget-object v0, v0, Lh2/w;->f:[J

    .line 8
    .line 9
    iget v1, p0, Lh2/h$b;->f:I

    .line 10
    .line 11
    aget-wide v1, v0, v1

    .line 12
    .line 13
    return-wide v1

    .line 14
    :cond_0
    iget-object v0, p0, Lh2/h$b;->b:Lh2/v;

    .line 15
    .line 16
    iget v1, p0, Lh2/h$b;->f:I

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Lh2/v;->c(I)J

    .line 19
    .line 20
    .line 21
    move-result-wide v0

    .line 22
    return-wide v0
.end method

.method public f()I
    .locals 2

    .line 1
    iget-boolean v0, p0, Lh2/h$b;->m:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lh2/h$b;->d:Lh2/w;

    .line 6
    .line 7
    iget-object v0, v0, Lh2/w;->d:[I

    .line 8
    .line 9
    iget v1, p0, Lh2/h$b;->f:I

    .line 10
    .line 11
    aget v0, v0, v1

    .line 12
    .line 13
    return v0

    .line 14
    :cond_0
    iget-object v0, p0, Lh2/h$b;->b:Lh2/v;

    .line 15
    .line 16
    iget-object v0, v0, Lh2/v;->i:[I

    .line 17
    .line 18
    iget v1, p0, Lh2/h$b;->f:I

    .line 19
    .line 20
    aget v0, v0, v1

    .line 21
    .line 22
    return v0
.end method

.method public g()Lh2/u;
    .locals 3

    .line 1
    iget-boolean v0, p0, Lh2/h$b;->m:Z

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-nez v0, :cond_0

    .line 5
    .line 6
    return-object v1

    .line 7
    :cond_0
    iget-object v0, p0, Lh2/h$b;->b:Lh2/v;

    .line 8
    .line 9
    iget-object v0, v0, Lh2/v;->a:Lh2/c;

    .line 10
    .line 11
    invoke-static {v0}, Lt1/a0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    check-cast v0, Lh2/c;

    .line 16
    .line 17
    iget v0, v0, Lh2/c;->a:I

    .line 18
    .line 19
    iget-object v2, p0, Lh2/h$b;->b:Lh2/v;

    .line 20
    .line 21
    iget-object v2, v2, Lh2/v;->n:Lh2/u;

    .line 22
    .line 23
    if-eqz v2, :cond_1

    .line 24
    .line 25
    goto :goto_0

    .line 26
    :cond_1
    iget-object v2, p0, Lh2/h$b;->d:Lh2/w;

    .line 27
    .line 28
    iget-object v2, v2, Lh2/w;->a:Lh2/t;

    .line 29
    .line 30
    invoke-virtual {v2, v0}, Lh2/t;->b(I)Lh2/u;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    :goto_0
    if-eqz v2, :cond_2

    .line 35
    .line 36
    iget-boolean v0, v2, Lh2/u;->a:Z

    .line 37
    .line 38
    if-eqz v0, :cond_2

    .line 39
    .line 40
    return-object v2

    .line 41
    :cond_2
    return-object v1
.end method

.method public h()Z
    .locals 5

    .line 1
    iget v0, p0, Lh2/h$b;->f:I

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    add-int/2addr v0, v1

    .line 5
    iput v0, p0, Lh2/h$b;->f:I

    .line 6
    .line 7
    iget-boolean v0, p0, Lh2/h$b;->m:Z

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    if-nez v0, :cond_0

    .line 11
    .line 12
    return v2

    .line 13
    :cond_0
    iget v0, p0, Lh2/h$b;->g:I

    .line 14
    .line 15
    add-int/2addr v0, v1

    .line 16
    iput v0, p0, Lh2/h$b;->g:I

    .line 17
    .line 18
    iget-object v3, p0, Lh2/h$b;->b:Lh2/v;

    .line 19
    .line 20
    iget-object v3, v3, Lh2/v;->h:[I

    .line 21
    .line 22
    iget v4, p0, Lh2/h$b;->h:I

    .line 23
    .line 24
    aget v3, v3, v4

    .line 25
    .line 26
    if-ne v0, v3, :cond_1

    .line 27
    .line 28
    add-int/2addr v4, v1

    .line 29
    iput v4, p0, Lh2/h$b;->h:I

    .line 30
    .line 31
    iput v2, p0, Lh2/h$b;->g:I

    .line 32
    .line 33
    return v2

    .line 34
    :cond_1
    return v1
.end method

.method public i(II)I
    .locals 10

    .line 1
    invoke-virtual {p0}, Lh2/h$b;->g()Lh2/u;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    return v1

    .line 9
    :cond_0
    iget v2, v0, Lh2/u;->d:I

    .line 10
    .line 11
    if-eqz v2, :cond_1

    .line 12
    .line 13
    iget-object v0, p0, Lh2/h$b;->b:Lh2/v;

    .line 14
    .line 15
    iget-object v0, v0, Lh2/v;->o:Lt1/G;

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_1
    iget-object v0, v0, Lh2/u;->e:[B

    .line 19
    .line 20
    invoke-static {v0}, Lt1/a0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    check-cast v0, [B

    .line 25
    .line 26
    iget-object v2, p0, Lh2/h$b;->l:Lt1/G;

    .line 27
    .line 28
    array-length v3, v0

    .line 29
    invoke-virtual {v2, v0, v3}, Lt1/G;->U([BI)V

    .line 30
    .line 31
    .line 32
    iget-object v2, p0, Lh2/h$b;->l:Lt1/G;

    .line 33
    .line 34
    array-length v0, v0

    .line 35
    move-object v9, v2

    .line 36
    move v2, v0

    .line 37
    move-object v0, v9

    .line 38
    :goto_0
    iget-object v3, p0, Lh2/h$b;->b:Lh2/v;

    .line 39
    .line 40
    iget v4, p0, Lh2/h$b;->f:I

    .line 41
    .line 42
    invoke-virtual {v3, v4}, Lh2/v;->g(I)Z

    .line 43
    .line 44
    .line 45
    move-result v3

    .line 46
    const/4 v4, 0x1

    .line 47
    if-nez v3, :cond_3

    .line 48
    .line 49
    if-eqz p2, :cond_2

    .line 50
    .line 51
    goto :goto_1

    .line 52
    :cond_2
    const/4 v5, 0x0

    .line 53
    goto :goto_2

    .line 54
    :cond_3
    :goto_1
    const/4 v5, 0x1

    .line 55
    :goto_2
    iget-object v6, p0, Lh2/h$b;->k:Lt1/G;

    .line 56
    .line 57
    invoke-virtual {v6}, Lt1/G;->e()[B

    .line 58
    .line 59
    .line 60
    move-result-object v6

    .line 61
    if-eqz v5, :cond_4

    .line 62
    .line 63
    const/16 v7, 0x80

    .line 64
    .line 65
    goto :goto_3

    .line 66
    :cond_4
    const/4 v7, 0x0

    .line 67
    :goto_3
    or-int/2addr v7, v2

    .line 68
    int-to-byte v7, v7

    .line 69
    aput-byte v7, v6, v1

    .line 70
    .line 71
    iget-object v6, p0, Lh2/h$b;->k:Lt1/G;

    .line 72
    .line 73
    invoke-virtual {v6, v1}, Lt1/G;->W(I)V

    .line 74
    .line 75
    .line 76
    iget-object v6, p0, Lh2/h$b;->a:LN1/T;

    .line 77
    .line 78
    iget-object v7, p0, Lh2/h$b;->k:Lt1/G;

    .line 79
    .line 80
    invoke-interface {v6, v7, v4, v4}, LN1/T;->f(Lt1/G;II)V

    .line 81
    .line 82
    .line 83
    iget-object v6, p0, Lh2/h$b;->a:LN1/T;

    .line 84
    .line 85
    invoke-interface {v6, v0, v2, v4}, LN1/T;->f(Lt1/G;II)V

    .line 86
    .line 87
    .line 88
    if-nez v5, :cond_5

    .line 89
    .line 90
    add-int/2addr v2, v4

    .line 91
    return v2

    .line 92
    :cond_5
    const/4 v0, 0x6

    .line 93
    const/4 v5, 0x3

    .line 94
    const/4 v6, 0x2

    .line 95
    const/16 v7, 0x8

    .line 96
    .line 97
    if-nez v3, :cond_6

    .line 98
    .line 99
    iget-object v3, p0, Lh2/h$b;->c:Lt1/G;

    .line 100
    .line 101
    invoke-virtual {v3, v7}, Lt1/G;->S(I)V

    .line 102
    .line 103
    .line 104
    iget-object v3, p0, Lh2/h$b;->c:Lt1/G;

    .line 105
    .line 106
    invoke-virtual {v3}, Lt1/G;->e()[B

    .line 107
    .line 108
    .line 109
    move-result-object v3

    .line 110
    aput-byte v1, v3, v1

    .line 111
    .line 112
    aput-byte v4, v3, v4

    .line 113
    .line 114
    shr-int/lit8 v1, p2, 0x8

    .line 115
    .line 116
    and-int/lit16 v1, v1, 0xff

    .line 117
    .line 118
    int-to-byte v1, v1

    .line 119
    aput-byte v1, v3, v6

    .line 120
    .line 121
    and-int/lit16 p2, p2, 0xff

    .line 122
    .line 123
    int-to-byte p2, p2

    .line 124
    aput-byte p2, v3, v5

    .line 125
    .line 126
    shr-int/lit8 p2, p1, 0x18

    .line 127
    .line 128
    and-int/lit16 p2, p2, 0xff

    .line 129
    .line 130
    int-to-byte p2, p2

    .line 131
    const/4 v1, 0x4

    .line 132
    aput-byte p2, v3, v1

    .line 133
    .line 134
    shr-int/lit8 p2, p1, 0x10

    .line 135
    .line 136
    and-int/lit16 p2, p2, 0xff

    .line 137
    .line 138
    int-to-byte p2, p2

    .line 139
    const/4 v1, 0x5

    .line 140
    aput-byte p2, v3, v1

    .line 141
    .line 142
    shr-int/lit8 p2, p1, 0x8

    .line 143
    .line 144
    and-int/lit16 p2, p2, 0xff

    .line 145
    .line 146
    int-to-byte p2, p2

    .line 147
    aput-byte p2, v3, v0

    .line 148
    .line 149
    and-int/lit16 p1, p1, 0xff

    .line 150
    .line 151
    int-to-byte p1, p1

    .line 152
    const/4 p2, 0x7

    .line 153
    aput-byte p1, v3, p2

    .line 154
    .line 155
    iget-object p1, p0, Lh2/h$b;->a:LN1/T;

    .line 156
    .line 157
    iget-object p2, p0, Lh2/h$b;->c:Lt1/G;

    .line 158
    .line 159
    invoke-interface {p1, p2, v7, v4}, LN1/T;->f(Lt1/G;II)V

    .line 160
    .line 161
    .line 162
    add-int/lit8 v2, v2, 0x9

    .line 163
    .line 164
    return v2

    .line 165
    :cond_6
    iget-object p1, p0, Lh2/h$b;->b:Lh2/v;

    .line 166
    .line 167
    iget-object p1, p1, Lh2/v;->o:Lt1/G;

    .line 168
    .line 169
    invoke-virtual {p1}, Lt1/G;->P()I

    .line 170
    .line 171
    .line 172
    move-result v3

    .line 173
    const/4 v8, -0x2

    .line 174
    invoke-virtual {p1, v8}, Lt1/G;->X(I)V

    .line 175
    .line 176
    .line 177
    mul-int/lit8 v3, v3, 0x6

    .line 178
    .line 179
    add-int/2addr v3, v6

    .line 180
    if-eqz p2, :cond_7

    .line 181
    .line 182
    iget-object v0, p0, Lh2/h$b;->c:Lt1/G;

    .line 183
    .line 184
    invoke-virtual {v0, v3}, Lt1/G;->S(I)V

    .line 185
    .line 186
    .line 187
    iget-object v0, p0, Lh2/h$b;->c:Lt1/G;

    .line 188
    .line 189
    invoke-virtual {v0}, Lt1/G;->e()[B

    .line 190
    .line 191
    .line 192
    move-result-object v0

    .line 193
    invoke-virtual {p1, v0, v1, v3}, Lt1/G;->l([BII)V

    .line 194
    .line 195
    .line 196
    aget-byte p1, v0, v6

    .line 197
    .line 198
    and-int/lit16 p1, p1, 0xff

    .line 199
    .line 200
    shl-int/2addr p1, v7

    .line 201
    aget-byte v1, v0, v5

    .line 202
    .line 203
    and-int/lit16 v1, v1, 0xff

    .line 204
    .line 205
    or-int/2addr p1, v1

    .line 206
    add-int/2addr p1, p2

    .line 207
    shr-int/lit8 p2, p1, 0x8

    .line 208
    .line 209
    and-int/lit16 p2, p2, 0xff

    .line 210
    .line 211
    int-to-byte p2, p2

    .line 212
    aput-byte p2, v0, v6

    .line 213
    .line 214
    and-int/lit16 p1, p1, 0xff

    .line 215
    .line 216
    int-to-byte p1, p1

    .line 217
    aput-byte p1, v0, v5

    .line 218
    .line 219
    iget-object p1, p0, Lh2/h$b;->c:Lt1/G;

    .line 220
    .line 221
    :cond_7
    iget-object p2, p0, Lh2/h$b;->a:LN1/T;

    .line 222
    .line 223
    invoke-interface {p2, p1, v3, v4}, LN1/T;->f(Lt1/G;II)V

    .line 224
    .line 225
    .line 226
    add-int/2addr v2, v4

    .line 227
    add-int/2addr v2, v3

    .line 228
    return v2
.end method

.method public j(Lh2/w;Lh2/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lh2/h$b;->d:Lh2/w;

    .line 2
    .line 3
    iput-object p2, p0, Lh2/h$b;->e:Lh2/c;

    .line 4
    .line 5
    iget-object p1, p1, Lh2/w;->a:Lh2/t;

    .line 6
    .line 7
    iget-object p1, p1, Lh2/t;->g:Landroidx/media3/common/r;

    .line 8
    .line 9
    invoke-virtual {p1}, Landroidx/media3/common/r;->b()Landroidx/media3/common/r$b;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    iget-object p2, p0, Lh2/h$b;->j:Ljava/lang/String;

    .line 14
    .line 15
    invoke-virtual {p1, p2}, Landroidx/media3/common/r$b;->U(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {p1}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    iget-object p2, p0, Lh2/h$b;->a:LN1/T;

    .line 24
    .line 25
    invoke-interface {p2, p1}, LN1/T;->e(Landroidx/media3/common/r;)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0}, Lh2/h$b;->k()V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public k()V
    .locals 1

    .line 1
    iget-object v0, p0, Lh2/h$b;->b:Lh2/v;

    .line 2
    .line 3
    invoke-virtual {v0}, Lh2/v;->f()V

    .line 4
    .line 5
    .line 6
    const/4 v0, 0x0

    .line 7
    iput v0, p0, Lh2/h$b;->f:I

    .line 8
    .line 9
    iput v0, p0, Lh2/h$b;->h:I

    .line 10
    .line 11
    iput v0, p0, Lh2/h$b;->g:I

    .line 12
    .line 13
    iput v0, p0, Lh2/h$b;->i:I

    .line 14
    .line 15
    iput-boolean v0, p0, Lh2/h$b;->m:Z

    .line 16
    .line 17
    return-void
.end method

.method public l(J)V
    .locals 4

    .line 1
    iget v0, p0, Lh2/h$b;->f:I

    .line 2
    .line 3
    :goto_0
    iget-object v1, p0, Lh2/h$b;->b:Lh2/v;

    .line 4
    .line 5
    iget v2, v1, Lh2/v;->f:I

    .line 6
    .line 7
    if-ge v0, v2, :cond_1

    .line 8
    .line 9
    invoke-virtual {v1, v0}, Lh2/v;->c(I)J

    .line 10
    .line 11
    .line 12
    move-result-wide v1

    .line 13
    cmp-long v3, v1, p1

    .line 14
    .line 15
    if-gtz v3, :cond_1

    .line 16
    .line 17
    iget-object v1, p0, Lh2/h$b;->b:Lh2/v;

    .line 18
    .line 19
    iget-object v1, v1, Lh2/v;->k:[Z

    .line 20
    .line 21
    aget-boolean v1, v1, v0

    .line 22
    .line 23
    if-eqz v1, :cond_0

    .line 24
    .line 25
    iput v0, p0, Lh2/h$b;->i:I

    .line 26
    .line 27
    :cond_0
    add-int/lit8 v0, v0, 0x1

    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_1
    return-void
.end method

.method public m()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lh2/h$b;->g()Lh2/u;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    iget-object v1, p0, Lh2/h$b;->b:Lh2/v;

    .line 9
    .line 10
    iget-object v1, v1, Lh2/v;->o:Lt1/G;

    .line 11
    .line 12
    iget v0, v0, Lh2/u;->d:I

    .line 13
    .line 14
    if-eqz v0, :cond_1

    .line 15
    .line 16
    invoke-virtual {v1, v0}, Lt1/G;->X(I)V

    .line 17
    .line 18
    .line 19
    :cond_1
    iget-object v0, p0, Lh2/h$b;->b:Lh2/v;

    .line 20
    .line 21
    iget v2, p0, Lh2/h$b;->f:I

    .line 22
    .line 23
    invoke-virtual {v0, v2}, Lh2/v;->g(I)Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-eqz v0, :cond_2

    .line 28
    .line 29
    invoke-virtual {v1}, Lt1/G;->P()I

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    mul-int/lit8 v0, v0, 0x6

    .line 34
    .line 35
    invoke-virtual {v1, v0}, Lt1/G;->X(I)V

    .line 36
    .line 37
    .line 38
    :cond_2
    :goto_0
    return-void
.end method

.method public n(Landroidx/media3/common/DrmInitData;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lh2/h$b;->d:Lh2/w;

    .line 2
    .line 3
    iget-object v0, v0, Lh2/w;->a:Lh2/t;

    .line 4
    .line 5
    iget-object v1, p0, Lh2/h$b;->b:Lh2/v;

    .line 6
    .line 7
    iget-object v1, v1, Lh2/v;->a:Lh2/c;

    .line 8
    .line 9
    invoke-static {v1}, Lt1/a0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    check-cast v1, Lh2/c;

    .line 14
    .line 15
    iget v1, v1, Lh2/c;->a:I

    .line 16
    .line 17
    invoke-virtual {v0, v1}, Lh2/t;->b(I)Lh2/u;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    if-eqz v0, :cond_0

    .line 22
    .line 23
    iget-object v0, v0, Lh2/u;->b:Ljava/lang/String;

    .line 24
    .line 25
    goto :goto_0

    .line 26
    :cond_0
    const/4 v0, 0x0

    .line 27
    :goto_0
    invoke-virtual {p1, v0}, Landroidx/media3/common/DrmInitData;->c(Ljava/lang/String;)Landroidx/media3/common/DrmInitData;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    iget-object v0, p0, Lh2/h$b;->d:Lh2/w;

    .line 32
    .line 33
    iget-object v0, v0, Lh2/w;->a:Lh2/t;

    .line 34
    .line 35
    iget-object v0, v0, Lh2/t;->g:Landroidx/media3/common/r;

    .line 36
    .line 37
    invoke-virtual {v0}, Landroidx/media3/common/r;->b()Landroidx/media3/common/r$b;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    iget-object v1, p0, Lh2/h$b;->j:Ljava/lang/String;

    .line 42
    .line 43
    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->U(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    invoke-virtual {v0, p1}, Landroidx/media3/common/r$b;->Y(Landroidx/media3/common/DrmInitData;)Landroidx/media3/common/r$b;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    invoke-virtual {p1}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    iget-object v0, p0, Lh2/h$b;->a:LN1/T;

    .line 56
    .line 57
    invoke-interface {v0, p1}, LN1/T;->e(Landroidx/media3/common/r;)V

    .line 58
    .line 59
    .line 60
    return-void
.end method
