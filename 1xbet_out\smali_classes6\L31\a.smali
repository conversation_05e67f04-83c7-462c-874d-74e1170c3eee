.class public final synthetic LL31/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Landroid/content/Context;

.field public final synthetic b:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;


# direct methods
.method public synthetic constructor <init>(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LL31/a;->a:Landroid/content/Context;

    iput-object p2, p0, LL31/a;->b:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LL31/a;->a:Landroid/content/Context;

    iget-object v1, p0, LL31/a;->b:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    invoke-static {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->c(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;)Landroidx/appcompat/widget/AppCompatTextView;

    move-result-object v0

    return-object v0
.end method
