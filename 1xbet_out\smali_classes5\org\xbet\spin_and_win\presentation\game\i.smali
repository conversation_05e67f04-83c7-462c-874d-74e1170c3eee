.class public final synthetic Lorg/xbet/spin_and_win/presentation/game/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/i;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/i;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    invoke-static {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
