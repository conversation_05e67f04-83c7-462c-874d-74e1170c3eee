.class final Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.jackpot.presentation.fragments.JackpotFragment$onObserveData$3"
    f = "JackpotFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "LP40/a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "LP40/a;",
        "state",
        "",
        "<anonymous>",
        "(LP40/a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;->this$0:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;

    iget-object v1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;->this$0:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public final invoke(LP40/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LP40/a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, LP40/a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;->invoke(LP40/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, LP40/a;

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;->this$0:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;

    .line 16
    .line 17
    invoke-virtual {p1}, LP40/a;->a()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    invoke-static {v0, v1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->E2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;->this$0:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;

    .line 25
    .line 26
    invoke-virtual {p1}, LP40/a;->b()Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-static {v0, v1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->F2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Ljava/lang/String;)V

    .line 31
    .line 32
    .line 33
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;->this$0:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;

    .line 34
    .line 35
    invoke-virtual {p1}, LP40/a;->c()Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    invoke-static {v0, p1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->G2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Ljava/lang/String;)V

    .line 40
    .line 41
    .line 42
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 43
    .line 44
    return-object p1

    .line 45
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 46
    .line 47
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 48
    .line 49
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 50
    .line 51
    .line 52
    throw p1
.end method
