.class public final synthetic LnI0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LnI0/b;


# direct methods
.method public synthetic constructor <init>(LnI0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LnI0/a;->a:LnI0/b;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LnI0/a;->a:LnI0/b;

    invoke-static {v0}, LnI0/b;->a(LnI0/b;)LmI0/a;

    move-result-object v0

    return-object v0
.end method
