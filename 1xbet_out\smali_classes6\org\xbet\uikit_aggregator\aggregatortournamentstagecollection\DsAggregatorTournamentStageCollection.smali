.class public final Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;
.super Landroid/widget/LinearLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000h\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\r\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 42\u00020\u0001:\u0001$B\u001b\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0015\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0015\u0010\u000f\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u001b\u0010\u0014\u001a\u00020\n2\u000c\u0010\u0013\u001a\u0008\u0012\u0004\u0012\u00020\u00120\u0011\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0017\u0010\u0018\u001a\u00020\n2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0017\u0010\u001a\u001a\u00020\n2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u0008\u001a\u0010\u0019J\u0017\u0010\u001d\u001a\u00020\n2\u0008\u0010\u001c\u001a\u0004\u0018\u00010\u001b\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0015\u0010!\u001a\u00020\n2\u0006\u0010 \u001a\u00020\u001f\u00a2\u0006\u0004\u0008!\u0010\"R\u0014\u0010&\u001a\u00020#8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010%R\u0014\u0010(\u001a\u00020#8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010%R\u0016\u0010+\u001a\u00020\r8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008)\u0010*R\u0014\u0010/\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u0010.R\u001a\u00103\u001a\u0008\u0012\u0004\u0012\u0002000\u00118\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00081\u00102\u00a8\u00065"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;",
        "Landroid/widget/LinearLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "Lb31/b;",
        "model",
        "",
        "setModel",
        "(Lb31/b;)V",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;",
        "type",
        "setStageCellType",
        "(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;)V",
        "",
        "Lc31/a;",
        "tournamentStageCellsStates",
        "setState",
        "(Ljava/util/List;)V",
        "",
        "text",
        "setHeaderText",
        "(Ljava/lang/CharSequence;)V",
        "setHeaderButtonText",
        "Landroid/view/View$OnClickListener;",
        "listener",
        "setOnHeaderButtonClickListener",
        "(Landroid/view/View$OnClickListener;)V",
        "",
        "showShimmer",
        "setShowShimmer",
        "(Z)V",
        "",
        "a",
        "I",
        "space6",
        "b",
        "space8",
        "c",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;",
        "styleType",
        "Lorg/xbet/uikit/components/header/DSHeader;",
        "d",
        "Lorg/xbet/uikit/components/header/DSHeader;",
        "headerView",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;",
        "e",
        "Ljava/util/List;",
        "stageCellViews",
        "f",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final f:Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final g:I


# instance fields
.field public final a:I

.field public final b:I

.field public c:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/uikit/components/header/DSHeader;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->f:Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->g:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 8
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0, p1, p2}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    sget v1, LlZ0/g;->space_6:I

    .line 9
    .line 10
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->a:I

    .line 15
    .line 16
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    sget v1, LlZ0/g;->space_8:I

    .line 21
    .line 22
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->b:I

    .line 27
    .line 28
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;->NUMBER:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 29
    .line 30
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->c:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 31
    .line 32
    new-instance v2, Lorg/xbet/uikit/components/header/DSHeader;

    .line 33
    .line 34
    const/4 v6, 0x6

    .line 35
    const/4 v7, 0x0

    .line 36
    const/4 v4, 0x0

    .line 37
    const/4 v5, 0x0

    .line 38
    move-object v3, p1

    .line 39
    invoke-direct/range {v2 .. v7}, Lorg/xbet/uikit/components/header/DSHeader;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 40
    .line 41
    .line 42
    const-string p1, "DsAggregatorTournamentStageCollection.TAG_HEADER"

    .line 43
    .line 44
    invoke-virtual {v2, p1}, Lorg/xbet/uikit/components/header/DSHeader;->setTag(Ljava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {v2, v0}, Lorg/xbet/uikit/components/header/DSHeader;->d(I)V

    .line 48
    .line 49
    .line 50
    iput-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->d:Lorg/xbet/uikit/components/header/DSHeader;

    .line 51
    .line 52
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    const/4 v0, 0x0

    .line 57
    const/4 v1, 0x0

    .line 58
    :goto_0
    const/4 v2, 0x3

    .line 59
    if-ge v1, v2, :cond_1

    .line 60
    .line 61
    new-instance v2, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;

    .line 62
    .line 63
    const/4 v4, 0x2

    .line 64
    const/4 v5, 0x0

    .line 65
    invoke-direct {v2, v3, v5, v4, v5}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 66
    .line 67
    .line 68
    const-string v4, "DsAggregatorTournamentStageCollection.TAG_STAGE_CELL"

    .line 69
    .line 70
    invoke-virtual {v2, v4}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 71
    .line 72
    .line 73
    if-lez v1, :cond_0

    .line 74
    .line 75
    new-instance v4, Landroid/widget/LinearLayout$LayoutParams;

    .line 76
    .line 77
    const/4 v5, -0x1

    .line 78
    const/4 v6, -0x2

    .line 79
    invoke-direct {v4, v5, v6}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 80
    .line 81
    .line 82
    iget v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->a:I

    .line 83
    .line 84
    iput v5, v4, Landroid/widget/LinearLayout$LayoutParams;->topMargin:I

    .line 85
    .line 86
    invoke-virtual {v2, v4}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 87
    .line 88
    .line 89
    :cond_0
    invoke-interface {p1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 90
    .line 91
    .line 92
    add-int/lit8 v1, v1, 0x1

    .line 93
    .line 94
    goto :goto_0

    .line 95
    :cond_1
    invoke-static {p1}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->e:Ljava/util/List;

    .line 100
    .line 101
    const/4 v1, 0x1

    .line 102
    invoke-virtual {p0, v1}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 103
    .line 104
    .line 105
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->d:Lorg/xbet/uikit/components/header/DSHeader;

    .line 106
    .line 107
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 108
    .line 109
    .line 110
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 111
    .line 112
    .line 113
    move-result-object p1

    .line 114
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 115
    .line 116
    .line 117
    move-result v1

    .line 118
    if-eqz v1, :cond_2

    .line 119
    .line 120
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 121
    .line 122
    .line 123
    move-result-object v1

    .line 124
    check-cast v1, Landroid/view/View;

    .line 125
    .line 126
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 127
    .line 128
    .line 129
    goto :goto_1

    .line 130
    :cond_2
    sget-object p1, LS11/h;->DSAggregatorTournamentStageCollection:[I

    .line 131
    .line 132
    invoke-virtual {v3, p2, p1, v0, v0}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    .line 133
    .line 134
    .line 135
    move-result-object p1

    .line 136
    sget-object p2, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;->Companion:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType$a;

    .line 137
    .line 138
    sget v1, LS11/h;->DSAggregatorTournamentStageCollection_tournamentStagesCellType:I

    .line 139
    .line 140
    invoke-virtual {p1, v1, v0}, Landroid/content/res/TypedArray;->getInt(II)I

    .line 141
    .line 142
    .line 143
    move-result v0

    .line 144
    invoke-virtual {p2, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType$a;->a(I)Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 145
    .line 146
    .line 147
    move-result-object p2

    .line 148
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->setStageCellType(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;)V

    .line 149
    .line 150
    .line 151
    sget p2, LS11/h;->DSAggregatorTournamentStageCollection_tournamentStagesCollectionHeaderText:I

    .line 152
    .line 153
    invoke-virtual {p1, p2}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    .line 154
    .line 155
    .line 156
    move-result-object p2

    .line 157
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->setHeaderText(Ljava/lang/CharSequence;)V

    .line 158
    .line 159
    .line 160
    sget p2, LS11/h;->DSAggregatorTournamentStageCollection_tournamentStagesCollectionHeaderButtonText:I

    .line 161
    .line 162
    invoke-virtual {p1, p2}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    .line 163
    .line 164
    .line 165
    move-result-object p2

    .line 166
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->setHeaderButtonText(Ljava/lang/CharSequence;)V

    .line 167
    .line 168
    .line 169
    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    .line 170
    .line 171
    .line 172
    return-void
.end method


# virtual methods
.method public final setHeaderButtonText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->d:Lorg/xbet/uikit/components/header/DSHeader;

    .line 2
    .line 3
    if-nez p1, :cond_0

    .line 4
    .line 5
    const-string p1, ""

    .line 6
    .line 7
    :cond_0
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/header/DSHeader;->setButtonLabel(Ljava/lang/CharSequence;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final setHeaderText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->d:Lorg/xbet/uikit/components/header/DSHeader;

    .line 2
    .line 3
    if-nez p1, :cond_0

    .line 4
    .line 5
    const-string p1, ""

    .line 6
    .line 7
    :cond_0
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/header/DSHeader;->setLabel(Ljava/lang/CharSequence;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final setModel(Lb31/b;)V
    .locals 13
    .param p1    # Lb31/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, Lb31/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->d:Lorg/xbet/uikit/components/header/DSHeader;

    .line 6
    .line 7
    new-instance v1, Lorg/xbet/uikit/components/header/a$a;

    .line 8
    .line 9
    check-cast p1, Lb31/a;

    .line 10
    .line 11
    invoke-virtual {p1}, Lb31/a;->c()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-virtual {p1}, Lb31/a;->a()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v5

    .line 19
    const/16 v11, 0x1f6

    .line 20
    .line 21
    const/4 v12, 0x0

    .line 22
    const/4 v3, 0x0

    .line 23
    const/4 v4, 0x0

    .line 24
    const/4 v6, 0x0

    .line 25
    const/4 v7, 0x0

    .line 26
    const/4 v8, 0x0

    .line 27
    const/4 v9, 0x0

    .line 28
    const/4 v10, 0x0

    .line 29
    invoke-direct/range {v1 .. v12}, Lorg/xbet/uikit/components/header/a$a;-><init>(Ljava/lang/CharSequence;ZLjava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/Integer;Landroid/graphics/drawable/Drawable;LL11/c;LL11/c;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 30
    .line 31
    .line 32
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/header/DSHeader;->setModel(Lorg/xbet/uikit/components/header/a;)V

    .line 33
    .line 34
    .line 35
    invoke-virtual {p1}, Lb31/a;->d()Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->setStageCellType(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {p1}, Lb31/a;->b()Ljava/util/List;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->setState(Ljava/util/List;)V

    .line 47
    .line 48
    .line 49
    return-void

    .line 50
    :cond_0
    instance-of v0, p1, Lb31/c;

    .line 51
    .line 52
    if-eqz v0, :cond_1

    .line 53
    .line 54
    check-cast p1, Lb31/c;

    .line 55
    .line 56
    invoke-virtual {p1}, Lb31/c;->a()Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->setStageCellType(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;)V

    .line 61
    .line 62
    .line 63
    const/4 p1, 0x1

    .line 64
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->setShowShimmer(Z)V

    .line 65
    .line 66
    .line 67
    :cond_1
    return-void
.end method

.method public final setOnHeaderButtonClickListener(Landroid/view/View$OnClickListener;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->d:Lorg/xbet/uikit/components/header/DSHeader;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/header/DSHeader;->setButtonClickListener(Landroid/view/View$OnClickListener;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setShowShimmer(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->d:Lorg/xbet/uikit/components/header/DSHeader;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/header/DSHeader;->c(Z)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->e:Ljava/util/List;

    .line 7
    .line 8
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    if-eqz v1, :cond_0

    .line 17
    .line 18
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    check-cast v1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;

    .line 23
    .line 24
    invoke-virtual {v1, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->setShowShimmer(Z)V

    .line 25
    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_0
    return-void
.end method

.method public final setStageCellType(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;)V
    .locals 2
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->c:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 2
    .line 3
    if-ne v0, p1, :cond_0

    .line 4
    .line 5
    goto :goto_1

    .line 6
    :cond_0
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->c:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->e:Ljava/util/List;

    .line 9
    .line 10
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    if-eqz v1, :cond_1

    .line 19
    .line 20
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    check-cast v1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;

    .line 25
    .line 26
    invoke-virtual {v1, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->setType(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;)V

    .line 27
    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_1
    :goto_1
    return-void
.end method

.method public final setState(Ljava/util/List;)V
    .locals 6
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lc31/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->d:Lorg/xbet/uikit/components/header/DSHeader;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/header/DSHeader;->c(Z)V

    .line 5
    .line 6
    .line 7
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    const/4 v2, 0x3

    .line 12
    invoke-static {v0, v2}, Ljava/lang/Math;->min(II)I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    invoke-static {p1, v0}, Lkotlin/collections/CollectionsKt;->p1(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    const/4 v2, 0x0

    .line 25
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 26
    .line 27
    .line 28
    move-result v3

    .line 29
    if-eqz v3, :cond_2

    .line 30
    .line 31
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    add-int/lit8 v4, v2, 0x1

    .line 36
    .line 37
    if-gez v2, :cond_0

    .line 38
    .line 39
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 40
    .line 41
    .line 42
    :cond_0
    check-cast v3, Lc31/a;

    .line 43
    .line 44
    iget-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->e:Ljava/util/List;

    .line 45
    .line 46
    invoke-static {v5, v2}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    check-cast v2, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;

    .line 51
    .line 52
    if-eqz v2, :cond_1

    .line 53
    .line 54
    invoke-virtual {v2, v3}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->setState(Lc31/a;)V

    .line 55
    .line 56
    .line 57
    :cond_1
    move v2, v4

    .line 58
    goto :goto_0

    .line 59
    :cond_2
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagecollection/DsAggregatorTournamentStageCollection;->e:Ljava/util/List;

    .line 60
    .line 61
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    const/4 v2, 0x0

    .line 66
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 67
    .line 68
    .line 69
    move-result v3

    .line 70
    if-eqz v3, :cond_6

    .line 71
    .line 72
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object v3

    .line 76
    add-int/lit8 v4, v2, 0x1

    .line 77
    .line 78
    if-gez v2, :cond_3

    .line 79
    .line 80
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 81
    .line 82
    .line 83
    :cond_3
    check-cast v3, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;

    .line 84
    .line 85
    if-ge v2, v0, :cond_4

    .line 86
    .line 87
    const/4 v2, 0x1

    .line 88
    goto :goto_2

    .line 89
    :cond_4
    const/4 v2, 0x0

    .line 90
    :goto_2
    if-eqz v2, :cond_5

    .line 91
    .line 92
    const/4 v2, 0x0

    .line 93
    goto :goto_3

    .line 94
    :cond_5
    const/16 v2, 0x8

    .line 95
    .line 96
    :goto_3
    invoke-virtual {v3, v2}, Landroid/view/View;->setVisibility(I)V

    .line 97
    .line 98
    .line 99
    move v2, v4

    .line 100
    goto :goto_1

    .line 101
    :cond_6
    return-void
.end method
