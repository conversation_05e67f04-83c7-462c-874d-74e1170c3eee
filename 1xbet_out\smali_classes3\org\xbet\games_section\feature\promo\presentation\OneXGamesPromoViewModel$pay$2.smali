.class final Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.promo.presentation.OneXGamesPromoViewModel$pay$2"
    f = "OneXGamesPromoViewModel.kt"
    l = {
        0xa7
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->o4(Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;

    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->w3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Lak/a;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-interface {p1}, Lak/a;->a()Lfk/l;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->GAMES:Lorg/xbet/balance/model/BalanceScreenType;

    .line 38
    .line 39
    iput v2, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;->label:I

    .line 40
    .line 41
    invoke-interface {p1, v1, p0}, Lfk/l;->a(Lorg/xbet/balance/model/BalanceScreenType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    if-ne p1, v0, :cond_2

    .line 46
    .line 47
    return-object v0

    .line 48
    :cond_2
    :goto_0
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 49
    .line 50
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 51
    .line 52
    invoke-static {v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->w3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Lak/a;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    invoke-interface {v0}, Lak/a;->m()Lek/f;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->GAMES:Lorg/xbet/balance/model/BalanceScreenType;

    .line 61
    .line 62
    invoke-interface {v0, v1, p1}, Lek/f;->a(Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceModel;)V

    .line 63
    .line 64
    .line 65
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$pay$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 66
    .line 67
    invoke-static {v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->x3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)LxX0/a;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    invoke-static {v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->I3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)LwX0/c;

    .line 72
    .line 73
    .line 74
    move-result-object v0

    .line 75
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 76
    .line 77
    .line 78
    move-result-wide v3

    .line 79
    invoke-interface {v1, v0, v2, v3, v4}, LxX0/a;->b(LwX0/c;ZJ)V

    .line 80
    .line 81
    .line 82
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 83
    .line 84
    return-object p1
.end method
