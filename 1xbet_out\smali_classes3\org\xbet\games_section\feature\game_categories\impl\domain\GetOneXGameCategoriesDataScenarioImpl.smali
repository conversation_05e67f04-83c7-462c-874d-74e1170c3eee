.class public final Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lv40/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\t\u0018\u0000 \u00132\u00020\u0001:\u0001\u000cB!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0016\u0010\u000c\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\nH\u0096B\u00a2\u0006\u0004\u0008\u000c\u0010\rR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\u000eR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0010R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0012\u00a8\u0006\u0014"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl;",
        "Lv40/a;",
        "Lw30/n;",
        "getGamesCategoriesUseCase",
        "Li8/j;",
        "getServiceUseCase",
        "Lv30/a;",
        "getCenterOfAttentionGameScenario",
        "<init>",
        "(Lw30/n;Li8/j;Lv30/a;)V",
        "",
        "Lu40/a;",
        "a",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lw30/n;",
        "b",
        "Li8/j;",
        "c",
        "Lv30/a;",
        "d",
        "impl_games_section_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d:Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lw30/n;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Li8/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lv30/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl;->d:Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$a;

    return-void
.end method

.method public constructor <init>(Lw30/n;Li8/j;Lv30/a;)V
    .locals 0
    .param p1    # Lw30/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lv30/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl;->a:Lw30/n;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl;->b:Li8/j;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl;->c:Lv30/a;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public a(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 10
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "+",
            "Lu40/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x1

    .line 2
    const/4 v1, 0x0

    .line 3
    instance-of v2, p1, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$invoke$1;

    .line 4
    .line 5
    if-eqz v2, :cond_0

    .line 6
    .line 7
    move-object v2, p1

    .line 8
    check-cast v2, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$invoke$1;

    .line 9
    .line 10
    iget v3, v2, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$invoke$1;->label:I

    .line 11
    .line 12
    const/high16 v4, -0x80000000

    .line 13
    .line 14
    and-int v5, v3, v4

    .line 15
    .line 16
    if-eqz v5, :cond_0

    .line 17
    .line 18
    sub-int/2addr v3, v4

    .line 19
    iput v3, v2, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$invoke$1;->label:I

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    new-instance v2, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$invoke$1;

    .line 23
    .line 24
    invoke-direct {v2, p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$invoke$1;-><init>(Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    :goto_0
    iget-object p1, v2, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$invoke$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v3

    .line 33
    iget v4, v2, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$invoke$1;->label:I

    .line 34
    .line 35
    const/4 v5, 0x2

    .line 36
    if-eqz v4, :cond_3

    .line 37
    .line 38
    if-eq v4, v0, :cond_2

    .line 39
    .line 40
    if-ne v4, v5, :cond_1

    .line 41
    .line 42
    iget-object v2, v2, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$invoke$1;->L$0:Ljava/lang/Object;

    .line 43
    .line 44
    check-cast v2, Ljava/util/List;

    .line 45
    .line 46
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 47
    .line 48
    .line 49
    goto :goto_3

    .line 50
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 51
    .line 52
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 53
    .line 54
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    throw p1

    .line 58
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 59
    .line 60
    .line 61
    goto :goto_1

    .line 62
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 63
    .line 64
    .line 65
    iget-object p1, p0, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl;->b:Li8/j;

    .line 66
    .line 67
    invoke-interface {p1}, Li8/j;->invoke()Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    iget-object v4, p0, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl;->a:Lw30/n;

    .line 72
    .line 73
    iput v0, v2, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$invoke$1;->label:I

    .line 74
    .line 75
    invoke-interface {v4, v1, p1, v2}, Lw30/n;->a(ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    if-ne p1, v3, :cond_4

    .line 80
    .line 81
    goto :goto_2

    .line 82
    :cond_4
    :goto_1
    check-cast p1, Ljava/util/List;

    .line 83
    .line 84
    iget-object v4, p0, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl;->c:Lv30/a;

    .line 85
    .line 86
    iput-object p1, v2, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$invoke$1;->L$0:Ljava/lang/Object;

    .line 87
    .line 88
    iput v5, v2, Lorg/xbet/games_section/feature/game_categories/impl/domain/GetOneXGameCategoriesDataScenarioImpl$invoke$1;->label:I

    .line 89
    .line 90
    invoke-interface {v4, v2}, Lv30/a;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 91
    .line 92
    .line 93
    move-result-object v2

    .line 94
    if-ne v2, v3, :cond_5

    .line 95
    .line 96
    :goto_2
    return-object v3

    .line 97
    :cond_5
    move-object v9, v2

    .line 98
    move-object v2, p1

    .line 99
    move-object p1, v9

    .line 100
    :goto_3
    check-cast p1, Ls30/a;

    .line 101
    .line 102
    if-eqz p1, :cond_6

    .line 103
    .line 104
    invoke-virtual {p1}, Ls30/a;->a()Ljava/lang/String;

    .line 105
    .line 106
    .line 107
    move-result-object v3

    .line 108
    if-eqz v3, :cond_6

    .line 109
    .line 110
    invoke-static {v3}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    .line 111
    .line 112
    .line 113
    move-result v3

    .line 114
    goto :goto_4

    .line 115
    :cond_6
    const/4 v3, 0x0

    .line 116
    :goto_4
    if-eqz p1, :cond_7

    .line 117
    .line 118
    new-instance v4, Lu40/a$a;

    .line 119
    .line 120
    sget-object v5, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->CENTER_OF_ATTENTION:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 121
    .line 122
    invoke-virtual {v5}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 123
    .line 124
    .line 125
    move-result-object v5

    .line 126
    invoke-static {v5}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    .line 127
    .line 128
    .line 129
    move-result v5

    .line 130
    new-instance v6, Lu40/b;

    .line 131
    .line 132
    invoke-virtual {p1}, Ls30/a;->b()Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 133
    .line 134
    .line 135
    move-result-object v7

    .line 136
    invoke-virtual {v7}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->c()Ljava/lang/String;

    .line 137
    .line 138
    .line 139
    move-result-object v7

    .line 140
    invoke-virtual {p1}, Ls30/a;->b()Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 141
    .line 142
    .line 143
    move-result-object v8

    .line 144
    invoke-virtual {v8}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->f()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 145
    .line 146
    .line 147
    move-result-object v8

    .line 148
    invoke-virtual {p1}, Ls30/a;->b()Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 149
    .line 150
    .line 151
    move-result-object p1

    .line 152
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->d()Ljava/lang/String;

    .line 153
    .line 154
    .line 155
    move-result-object p1

    .line 156
    invoke-direct {v6, v7, v8, p1}, Lu40/b;-><init>(Ljava/lang/String;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;Ljava/lang/String;)V

    .line 157
    .line 158
    .line 159
    const-string p1, ""

    .line 160
    .line 161
    invoke-direct {v4, v5, p1, v6}, Lu40/a$a;-><init>(ILjava/lang/String;Lu40/b;)V

    .line 162
    .line 163
    .line 164
    goto :goto_5

    .line 165
    :cond_7
    const/4 v4, 0x0

    .line 166
    :goto_5
    sget-object p1, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->CENTER_OF_ATTENTION:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 167
    .line 168
    invoke-virtual {p1}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 169
    .line 170
    .line 171
    move-result-object p1

    .line 172
    invoke-static {p1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    .line 173
    .line 174
    .line 175
    move-result p1

    .line 176
    if-ne v3, p1, :cond_a

    .line 177
    .line 178
    new-instance p1, Ljava/util/ArrayList;

    .line 179
    .line 180
    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    .line 181
    .line 182
    .line 183
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 184
    .line 185
    .line 186
    move-result-object v2

    .line 187
    :cond_8
    :goto_6
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 188
    .line 189
    .line 190
    move-result v3

    .line 191
    if-eqz v3, :cond_9

    .line 192
    .line 193
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 194
    .line 195
    .line 196
    move-result-object v3

    .line 197
    move-object v5, v3

    .line 198
    check-cast v5, Lg9/a;

    .line 199
    .line 200
    invoke-virtual {v5}, Lg9/a;->a()I

    .line 201
    .line 202
    .line 203
    move-result v5

    .line 204
    sget-object v6, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->CENTER_OF_ATTENTION:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 205
    .line 206
    invoke-virtual {v6}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 207
    .line 208
    .line 209
    move-result-object v6

    .line 210
    invoke-static {v6}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    .line 211
    .line 212
    .line 213
    move-result v6

    .line 214
    if-eq v5, v6, :cond_8

    .line 215
    .line 216
    invoke-interface {p1, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 217
    .line 218
    .line 219
    goto :goto_6

    .line 220
    :cond_9
    move-object v2, p1

    .line 221
    :cond_a
    new-instance p1, Ljava/util/ArrayList;

    .line 222
    .line 223
    const/16 v3, 0xa

    .line 224
    .line 225
    invoke-static {v2, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 226
    .line 227
    .line 228
    move-result v3

    .line 229
    invoke-direct {p1, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 230
    .line 231
    .line 232
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 233
    .line 234
    .line 235
    move-result-object v2

    .line 236
    :goto_7
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 237
    .line 238
    .line 239
    move-result v3

    .line 240
    if-eqz v3, :cond_b

    .line 241
    .line 242
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 243
    .line 244
    .line 245
    move-result-object v3

    .line 246
    check-cast v3, Lg9/a;

    .line 247
    .line 248
    new-instance v5, Lu40/a$c;

    .line 249
    .line 250
    invoke-virtual {v3}, Lg9/a;->a()I

    .line 251
    .line 252
    .line 253
    move-result v6

    .line 254
    invoke-virtual {v3}, Lg9/a;->b()Ljava/lang/String;

    .line 255
    .line 256
    .line 257
    move-result-object v3

    .line 258
    invoke-direct {v5, v6, v3}, Lu40/a$c;-><init>(ILjava/lang/String;)V

    .line 259
    .line 260
    .line 261
    invoke-interface {p1, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 262
    .line 263
    .line 264
    goto :goto_7

    .line 265
    :cond_b
    new-instance v2, Lu40/a$b;

    .line 266
    .line 267
    invoke-direct {v2, p1}, Lu40/a$b;-><init>(Ljava/util/List;)V

    .line 268
    .line 269
    .line 270
    new-array p1, v0, [Lu40/a;

    .line 271
    .line 272
    aput-object v2, p1, v1

    .line 273
    .line 274
    invoke-static {p1}, Lkotlin/collections/v;->t([Ljava/lang/Object;)Ljava/util/List;

    .line 275
    .line 276
    .line 277
    move-result-object p1

    .line 278
    if-eqz v4, :cond_c

    .line 279
    .line 280
    invoke-interface {p1, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 281
    .line 282
    .line 283
    move-result v0

    .line 284
    invoke-static {v0}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 285
    .line 286
    .line 287
    :cond_c
    return-object p1
.end method
