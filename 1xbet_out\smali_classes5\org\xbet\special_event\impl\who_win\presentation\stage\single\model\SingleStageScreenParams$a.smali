.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/os/Parcelable$Creator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroid/os/Parcelable$Creator<",
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroid/os/Parcel;)Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;

    invoke-virtual {p1}, Landroid/os/Parcel;->readInt()I

    move-result v1

    sget-object v2, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageType;->CREATOR:Landroid/os/Parcelable$Creator;

    invoke-interface {v2, p1}, Landroid/os/Parcelable$Creator;->createFromParcel(Landroid/os/Parcel;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageType;

    invoke-virtual {p1}, Landroid/os/Parcel;->readLong()J

    move-result-wide v3

    invoke-virtual {p1}, Landroid/os/Parcel;->readInt()I

    move-result v5

    invoke-direct/range {v0 .. v5}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;-><init>(ILorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageType;JI)V

    return-object v0
.end method

.method public final b(I)[Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;
    .locals 0

    .line 1
    new-array p1, p1, [Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;

    return-object p1
.end method

.method public bridge synthetic createFromParcel(Landroid/os/Parcel;)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams$a;->a(Landroid/os/Parcel;)Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic newArray(I)[Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams$a;->b(I)[Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;

    move-result-object p1

    return-object p1
.end method
