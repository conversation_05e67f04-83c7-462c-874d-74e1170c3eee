.class public final Lorg/xbet/crystal/presentation/views/CrystalFieldView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/crystal/presentation/views/CrystalFieldView$a;,
        Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0098\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010$\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\"\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u0014\n\u0002\u0010!\n\u0002\u0008\u0003\n\u0002\u0010%\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0018\u0000 ;2\u00020\u0001:\u0002PNB\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u000f\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000b\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\nJ%\u0010\u0010\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000f0\u000e0\u000e2\u0008\u0008\u0002\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\'\u0010\u0015\u001a\u00028\u0000\"\u000c\u0008\u0000\u0010\u0013*\u0006\u0012\u0002\u0008\u00030\u0012*\u0008\u0012\u0004\u0012\u00028\u00000\u0014H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J#\u0010\u0018\u001a\u00020\u00082\u0012\u0010\u0017\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000f0\u000e0\u000eH\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0017\u0010\u001c\u001a\u00020\u00082\u0006\u0010\u001b\u001a\u00020\u001aH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u000f\u0010\u001e\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\nJ#\u0010\u001f\u001a\u00020\u00082\u0012\u0010\u0017\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000f0\u000e0\u000eH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010\u0019J)\u0010\"\u001a\u00020\u00082\u0018\u0010!\u001a\u0014\u0012\u0004\u0012\u00020\u000c\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000f0\u000e0 H\u0002\u00a2\u0006\u0004\u0008\"\u0010#J#\u0010(\u001a\u0008\u0012\u0004\u0012\u00020\'0&2\u000c\u0010%\u001a\u0008\u0012\u0004\u0012\u00020$0\u000eH\u0002\u00a2\u0006\u0004\u0008(\u0010)J)\u0010+\u001a\u000e\u0012\u0004\u0012\u00020\'\u0012\u0004\u0012\u00020\u000c0 2\u000c\u0010*\u001a\u0008\u0012\u0004\u0012\u00020\'0&H\u0002\u00a2\u0006\u0004\u0008+\u0010,J9\u0010.\u001a\u00020\u00082\u0006\u0010\u001b\u001a\u00020\u001a2\u000c\u0010*\u001a\u0008\u0012\u0004\u0012\u00020\'0&2\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\'\u0012\u0004\u0012\u00020\u000c0 H\u0002\u00a2\u0006\u0004\u0008.\u0010/J9\u00101\u001a\u0002002\u0006\u0010\u001b\u001a\u00020\u001a2\u000c\u0010*\u001a\u0008\u0012\u0004\u0012\u00020\'0&2\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\'\u0012\u0004\u0012\u00020\u000c0 H\u0002\u00a2\u0006\u0004\u00081\u00102J1\u00103\u001a\u00020\u00082\u000c\u0010*\u001a\u0008\u0012\u0004\u0012\u00020\'0&2\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\'\u0012\u0004\u0012\u00020\u000c0 H\u0002\u00a2\u0006\u0004\u00083\u00104J#\u00105\u001a\u00020\u00082\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\'\u0012\u0004\u0012\u00020\u000c0 H\u0002\u00a2\u0006\u0004\u00085\u0010#J\u000f\u00106\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u00086\u0010\nJ\u0017\u00109\u001a\u00020\u00082\u0008\u00108\u001a\u0004\u0018\u000107\u00a2\u0006\u0004\u00089\u0010:J\r\u0010;\u001a\u00020\u0008\u00a2\u0006\u0004\u0008;\u0010\nJ\u001b\u0010=\u001a\u00020\u00082\u000c\u0010<\u001a\u0008\u0012\u0004\u0012\u00020\u001a0\u000e\u00a2\u0006\u0004\u0008=\u0010\u0019J#\u0010@\u001a\u00020\u00082\u000c\u0010<\u001a\u0008\u0012\u0004\u0012\u00020\u001a0\u000e2\u0006\u0010?\u001a\u00020>\u00a2\u0006\u0004\u0008@\u0010AJ\u000f\u0010B\u001a\u00020\u0008H\u0000\u00a2\u0006\u0004\u0008B\u0010\nJ\u001f\u0010E\u001a\u00020\u00082\u0006\u0010C\u001a\u00020\u000c2\u0006\u0010D\u001a\u00020\u000cH\u0014\u00a2\u0006\u0004\u0008E\u0010FJ7\u0010L\u001a\u00020\u00082\u0006\u0010G\u001a\u00020>2\u0006\u0010H\u001a\u00020\u000c2\u0006\u0010I\u001a\u00020\u000c2\u0006\u0010J\u001a\u00020\u000c2\u0006\u0010K\u001a\u00020\u000cH\u0014\u00a2\u0006\u0004\u0008L\u0010MR\u0018\u00108\u001a\u0004\u0018\u0001078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008N\u0010OR\u0014\u0010R\u001a\u00020\u000c8\u0002X\u0082D\u00a2\u0006\u0006\n\u0004\u0008P\u0010QR\u001a\u0010V\u001a\u0008\u0012\u0004\u0012\u00020\'0S8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008T\u0010UR&\u0010Z\u001a\u0014\u0012\u0004\u0012\u00020\u000c\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\'0\u000e0W8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008X\u0010YR\u0016\u0010\\\u001a\u00020\u000c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008[\u0010QR\u0016\u0010^\u001a\u00020\u000c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008]\u0010QR\u0018\u0010b\u001a\u0004\u0018\u00010_8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008`\u0010aR\u0018\u0010e\u001a\u0004\u0018\u0001008\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008c\u0010dR\u0018\u0010h\u001a\u0004\u0018\u00010f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008B\u0010g\u00a8\u0006i"
    }
    d2 = {
        "Lorg/xbet/crystal/presentation/views/CrystalFieldView;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "E",
        "()V",
        "D",
        "",
        "size",
        "",
        "Lorg/xbet/crystal/domain/models/CrystalTypeEnum;",
        "o",
        "(I)Ljava/util/List;",
        "",
        "T",
        "Lkotlin/reflect/d;",
        "u",
        "(Lkotlin/reflect/d;)Ljava/lang/Enum;",
        "field",
        "setupPreviewField",
        "(Ljava/util/List;)V",
        "LZx/c;",
        "round",
        "setupRound",
        "(LZx/c;)V",
        "w",
        "setupCrystals",
        "",
        "crystals",
        "setupNewCrystals",
        "(Ljava/util/Map;)V",
        "LZx/e;",
        "winCombos",
        "",
        "Lorg/xbet/crystal/presentation/views/Crystal;",
        "q",
        "(Ljava/util/List;)Ljava/util/Set;",
        "winCrystalViews",
        "k",
        "(Ljava/util/Set;)Ljava/util/Map;",
        "shifts",
        "C",
        "(LZx/c;Ljava/util/Set;Ljava/util/Map;)V",
        "Landroid/animation/AnimatorSet;",
        "l",
        "(LZx/c;Ljava/util/Set;Ljava/util/Map;)Landroid/animation/AnimatorSet;",
        "x",
        "(Ljava/util/Set;Ljava/util/Map;)V",
        "setupShiftAnimator",
        "F",
        "Lorg/xbet/crystal/presentation/game/f;",
        "updateInterface",
        "setUpdateInterface",
        "(Lorg/xbet/crystal/presentation/game/f;)V",
        "j",
        "rounds",
        "r",
        "",
        "gameInProcess",
        "v",
        "(Ljava/util/List;Z)V",
        "i",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "a",
        "Lorg/xbet/crystal/presentation/game/f;",
        "b",
        "I",
        "fieldSize",
        "",
        "c",
        "Ljava/util/List;",
        "crystalViews",
        "",
        "d",
        "Ljava/util/Map;",
        "newCrystalViews",
        "e",
        "itemSize",
        "f",
        "viewSize",
        "Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;",
        "g",
        "Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;",
        "gameState",
        "h",
        "Landroid/animation/AnimatorSet;",
        "animatorSet",
        "Landroid/animation/ValueAnimator;",
        "Landroid/animation/ValueAnimator;",
        "blinkAnimator",
        "crystal_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final j:Lorg/xbet/crystal/presentation/views/CrystalFieldView$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public a:Lorg/xbet/crystal/presentation/game/f;

.field public final b:I

.field public final c:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xbet/crystal/presentation/views/Crystal;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Ljava/util/List<",
            "Lorg/xbet/crystal/presentation/views/Crystal;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:I

.field public f:I

.field public g:Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;

.field public h:Landroid/animation/AnimatorSet;

.field public i:Landroid/animation/ValueAnimator;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/crystal/presentation/views/CrystalFieldView$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->j:Lorg/xbet/crystal/presentation/views/CrystalFieldView$a;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroid/util/AttributeSet;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 2
    .line 3
    .line 4
    const/4 p1, 0x7

    .line 5
    iput p1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->b:I

    .line 6
    .line 7
    new-instance p1, Ljava/util/ArrayList;

    .line 8
    .line 9
    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    .line 10
    .line 11
    .line 12
    iput-object p1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->c:Ljava/util/List;

    .line 13
    .line 14
    new-instance p1, Ljava/util/LinkedHashMap;

    .line 15
    .line 16
    invoke-direct {p1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 17
    .line 18
    .line 19
    iput-object p1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->d:Ljava/util/Map;

    .line 20
    .line 21
    const/4 p1, 0x1

    .line 22
    const/4 p2, 0x0

    .line 23
    const/4 v0, 0x0

    .line 24
    invoke-static {p0, v0, p1, p2}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->p(Lorg/xbet/crystal/presentation/views/CrystalFieldView;IILjava/lang/Object;)Ljava/util/List;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    invoke-direct {p0, p1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->setupPreviewField(Ljava/util/List;)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public static final A(Lorg/xbet/crystal/presentation/views/Crystal;Landroid/animation/ValueAnimator;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->getAnimatedValue()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Ljava/lang/Float;

    .line 6
    .line 7
    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    invoke-virtual {p0, p1}, Landroid/view/View;->setTranslationY(F)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public static final B(Lorg/xbet/crystal/presentation/views/CrystalFieldView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->F()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic a(ILorg/xbet/crystal/presentation/views/CrystalFieldView;Lorg/xbet/crystal/presentation/views/Crystal;)I
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->s(ILorg/xbet/crystal/presentation/views/CrystalFieldView;Lorg/xbet/crystal/presentation/views/Crystal;)I

    move-result p0

    return p0
.end method

.method public static synthetic b(Lorg/xbet/crystal/presentation/views/CrystalFieldView;Ljava/util/Map;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->z(Lorg/xbet/crystal/presentation/views/CrystalFieldView;Ljava/util/Map;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lorg/xbet/crystal/presentation/views/CrystalFieldView;LZx/c;Ljava/util/Set;Ljava/util/Map;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->n(Lorg/xbet/crystal/presentation/views/CrystalFieldView;LZx/c;Ljava/util/Set;Ljava/util/Map;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Ljava/util/Set;Landroid/animation/ValueAnimator;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->y(Ljava/util/Set;Landroid/animation/ValueAnimator;)V

    return-void
.end method

.method public static synthetic e(ILorg/xbet/crystal/presentation/views/CrystalFieldView;Lorg/xbet/crystal/presentation/views/Crystal;)I
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->t(ILorg/xbet/crystal/presentation/views/CrystalFieldView;Lorg/xbet/crystal/presentation/views/Crystal;)I

    move-result p0

    return p0
.end method

.method public static synthetic f(Lorg/xbet/crystal/presentation/views/Crystal;Landroid/animation/ValueAnimator;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->A(Lorg/xbet/crystal/presentation/views/Crystal;Landroid/animation/ValueAnimator;)V

    return-void
.end method

.method public static synthetic g(Lorg/xbet/crystal/presentation/views/Crystal;Landroid/animation/ValueAnimator;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->m(Lorg/xbet/crystal/presentation/views/Crystal;Landroid/animation/ValueAnimator;)V

    return-void
.end method

.method public static synthetic h(Lorg/xbet/crystal/presentation/views/CrystalFieldView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->B(Lorg/xbet/crystal/presentation/views/CrystalFieldView;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final m(Lorg/xbet/crystal/presentation/views/Crystal;Landroid/animation/ValueAnimator;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->getAnimatedValue()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Ljava/lang/Float;

    .line 6
    .line 7
    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    invoke-virtual {p0, p1}, Landroid/view/View;->setTranslationY(F)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public static final n(Lorg/xbet/crystal/presentation/views/CrystalFieldView;LZx/c;Ljava/util/Set;Ljava/util/Map;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->C(LZx/c;Ljava/util/Set;Ljava/util/Map;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic p(Lorg/xbet/crystal/presentation/views/CrystalFieldView;IILjava/lang/Object;)Ljava/util/List;
    .locals 0

    .line 1
    and-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    if-eqz p2, :cond_0

    .line 4
    .line 5
    const/4 p1, 0x7

    .line 6
    :cond_0
    invoke-virtual {p0, p1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->o(I)Ljava/util/List;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    return-object p0
.end method

.method public static final s(ILorg/xbet/crystal/presentation/views/CrystalFieldView;Lorg/xbet/crystal/presentation/views/Crystal;)I
    .locals 0

    .line 1
    iget p1, p1, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->e:I

    .line 2
    .line 3
    invoke-virtual {p2}, Lorg/xbet/crystal/presentation/views/Crystal;->getX()I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    mul-int p1, p1, p2

    .line 8
    .line 9
    add-int/2addr p0, p1

    .line 10
    return p0
.end method

.method private final setupCrystals(Ljava/util/List;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "+",
            "Lorg/xbet/crystal/domain/models/CrystalTypeEnum;",
            ">;>;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    const/4 v0, 0x0

    .line 6
    const/4 v1, 0x0

    .line 7
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    if-eqz v2, :cond_3

    .line 12
    .line 13
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    add-int/lit8 v3, v1, 0x1

    .line 18
    .line 19
    if-gez v1, :cond_0

    .line 20
    .line 21
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 22
    .line 23
    .line 24
    :cond_0
    check-cast v2, Ljava/util/List;

    .line 25
    .line 26
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    const/4 v4, 0x0

    .line 31
    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 32
    .line 33
    .line 34
    move-result v5

    .line 35
    if-eqz v5, :cond_2

    .line 36
    .line 37
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v5

    .line 41
    add-int/lit8 v6, v4, 0x1

    .line 42
    .line 43
    if-gez v4, :cond_1

    .line 44
    .line 45
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 46
    .line 47
    .line 48
    :cond_1
    check-cast v5, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 49
    .line 50
    new-instance v7, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 51
    .line 52
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 53
    .line 54
    .line 55
    move-result-object v8

    .line 56
    invoke-direct {v7, v8, v5}, Lorg/xbet/crystal/presentation/views/Crystal;-><init>(Landroid/content/Context;Lorg/xbet/crystal/domain/models/CrystalTypeEnum;)V

    .line 57
    .line 58
    .line 59
    invoke-virtual {v7, v4}, Lorg/xbet/crystal/presentation/views/Crystal;->setX(I)V

    .line 60
    .line 61
    .line 62
    invoke-virtual {v7, v1}, Lorg/xbet/crystal/presentation/views/Crystal;->setY(I)V

    .line 63
    .line 64
    .line 65
    const/4 v4, 0x0

    .line 66
    invoke-virtual {v7, v4}, Landroid/view/View;->setAlpha(F)V

    .line 67
    .line 68
    .line 69
    invoke-virtual {p0, v7}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 70
    .line 71
    .line 72
    iget-object v4, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->c:Ljava/util/List;

    .line 73
    .line 74
    invoke-interface {v4, v7}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 75
    .line 76
    .line 77
    move v4, v6

    .line 78
    goto :goto_1

    .line 79
    :cond_2
    move v1, v3

    .line 80
    goto :goto_0

    .line 81
    :cond_3
    return-void
.end method

.method private final setupNewCrystals(Ljava/util/Map;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "+",
            "Ljava/util/List<",
            "+",
            "Lorg/xbet/crystal/domain/models/CrystalTypeEnum;",
            ">;>;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-eqz v0, :cond_2

    .line 14
    .line 15
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    check-cast v0, Ljava/util/Map$Entry;

    .line 20
    .line 21
    invoke-interface {v0}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    check-cast v1, Ljava/lang/Number;

    .line 26
    .line 27
    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    invoke-interface {v0}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    check-cast v0, Ljava/util/List;

    .line 36
    .line 37
    iget-object v2, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->d:Ljava/util/Map;

    .line 38
    .line 39
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    new-instance v4, Ljava/util/ArrayList;

    .line 44
    .line 45
    const/16 v5, 0xa

    .line 46
    .line 47
    invoke-static {v0, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 48
    .line 49
    .line 50
    move-result v5

    .line 51
    invoke-direct {v4, v5}, Ljava/util/ArrayList;-><init>(I)V

    .line 52
    .line 53
    .line 54
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    const/4 v5, 0x0

    .line 59
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 60
    .line 61
    .line 62
    move-result v6

    .line 63
    if-eqz v6, :cond_1

    .line 64
    .line 65
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object v6

    .line 69
    add-int/lit8 v7, v5, 0x1

    .line 70
    .line 71
    if-gez v5, :cond_0

    .line 72
    .line 73
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 74
    .line 75
    .line 76
    :cond_0
    check-cast v6, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 77
    .line 78
    new-instance v5, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 79
    .line 80
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 81
    .line 82
    .line 83
    move-result-object v8

    .line 84
    invoke-direct {v5, v8, v6}, Lorg/xbet/crystal/presentation/views/Crystal;-><init>(Landroid/content/Context;Lorg/xbet/crystal/domain/models/CrystalTypeEnum;)V

    .line 85
    .line 86
    .line 87
    invoke-virtual {v5, v1}, Lorg/xbet/crystal/presentation/views/Crystal;->setX(I)V

    .line 88
    .line 89
    .line 90
    neg-int v6, v7

    .line 91
    invoke-virtual {v5, v6}, Lorg/xbet/crystal/presentation/views/Crystal;->setY(I)V

    .line 92
    .line 93
    .line 94
    invoke-virtual {p0, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 95
    .line 96
    .line 97
    invoke-interface {v4, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 98
    .line 99
    .line 100
    move v5, v7

    .line 101
    goto :goto_1

    .line 102
    :cond_1
    invoke-interface {v2, v3, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 103
    .line 104
    .line 105
    goto :goto_0

    .line 106
    :cond_2
    return-void
.end method

.method private final setupPreviewField(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "+",
            "Lorg/xbet/crystal/domain/models/CrystalTypeEnum;",
            ">;>;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->setupCrystals(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->w()V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method private final setupRound(LZx/c;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->c:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->clear()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->d:Ljava/util/Map;

    .line 7
    .line 8
    invoke-interface {v0}, Ljava/util/Map;->clear()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Landroid/view/ViewGroup;->removeAllViews()V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p1}, LZx/c;->a()Ljava/util/List;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-direct {p0, v0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->setupCrystals(Ljava/util/List;)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p1}, LZx/c;->b()Ljava/util/Map;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-direct {p0, v0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->setupNewCrystals(Ljava/util/Map;)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p1}, LZx/c;->c()Ljava/util/List;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    invoke-virtual {p0, v0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->q(Ljava/util/List;)Ljava/util/Set;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    invoke-virtual {p0, v0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->k(Ljava/util/Set;)Ljava/util/Map;

    .line 37
    .line 38
    .line 39
    move-result-object v1

    .line 40
    iget-object v2, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->g:Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;

    .line 41
    .line 42
    if-eqz v2, :cond_1

    .line 43
    .line 44
    invoke-virtual {v2}, Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;->d()Z

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    const/4 v3, 0x1

    .line 49
    if-ne v2, v3, :cond_1

    .line 50
    .line 51
    invoke-virtual {p0, p1, v0, v1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->l(LZx/c;Ljava/util/Set;Ljava/util/Map;)Landroid/animation/AnimatorSet;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    iput-object p1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->h:Landroid/animation/AnimatorSet;

    .line 56
    .line 57
    if-eqz p1, :cond_0

    .line 58
    .line 59
    invoke-virtual {p1}, Landroid/animation/AnimatorSet;->start()V

    .line 60
    .line 61
    .line 62
    :cond_0
    return-void

    .line 63
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->w()V

    .line 64
    .line 65
    .line 66
    invoke-virtual {p0, p1, v0, v1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->C(LZx/c;Ljava/util/Set;Ljava/util/Map;)V

    .line 67
    .line 68
    .line 69
    return-void
.end method

.method private final setupShiftAnimator(Ljava/util/Map;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lorg/xbet/crystal/presentation/views/Crystal;",
            "Ljava/lang/Integer;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-interface {p1}, Ljava/util/Map;->size()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 8
    .line 9
    .line 10
    invoke-interface {p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 19
    .line 20
    .line 21
    move-result v1

    .line 22
    if-eqz v1, :cond_0

    .line 23
    .line 24
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    check-cast v1, Ljava/util/Map$Entry;

    .line 29
    .line 30
    invoke-interface {v1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    check-cast v2, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 35
    .line 36
    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    .line 37
    .line 38
    .line 39
    move-result-object v1

    .line 40
    check-cast v1, Ljava/lang/Number;

    .line 41
    .line 42
    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    invoke-virtual {v2}, Lorg/xbet/crystal/presentation/views/Crystal;->getY()I

    .line 47
    .line 48
    .line 49
    move-result v3

    .line 50
    sub-int/2addr v1, v3

    .line 51
    iget v3, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->e:I

    .line 52
    .line 53
    mul-int v1, v1, v3

    .line 54
    .line 55
    int-to-float v1, v1

    .line 56
    const/4 v3, 0x2

    .line 57
    new-array v3, v3, [F

    .line 58
    .line 59
    const/4 v4, 0x0

    .line 60
    const/4 v5, 0x0

    .line 61
    aput v4, v3, v5

    .line 62
    .line 63
    const/4 v4, 0x1

    .line 64
    aput v1, v3, v4

    .line 65
    .line 66
    invoke-static {v3}, Landroid/animation/ValueAnimator;->ofFloat([F)Landroid/animation/ValueAnimator;

    .line 67
    .line 68
    .line 69
    move-result-object v1

    .line 70
    new-instance v3, Ldy/a;

    .line 71
    .line 72
    invoke-direct {v3, v2}, Ldy/a;-><init>(Lorg/xbet/crystal/presentation/views/Crystal;)V

    .line 73
    .line 74
    .line 75
    invoke-virtual {v1, v3}, Landroid/animation/ValueAnimator;->addUpdateListener(Landroid/animation/ValueAnimator$AnimatorUpdateListener;)V

    .line 76
    .line 77
    .line 78
    const-wide/16 v2, 0x190

    .line 79
    .line 80
    invoke-virtual {v1, v2, v3}, Landroid/animation/ValueAnimator;->setDuration(J)Landroid/animation/ValueAnimator;

    .line 81
    .line 82
    .line 83
    invoke-interface {v0, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 84
    .line 85
    .line 86
    goto :goto_0

    .line 87
    :cond_0
    new-instance p1, Landroid/animation/AnimatorSet;

    .line 88
    .line 89
    invoke-direct {p1}, Landroid/animation/AnimatorSet;-><init>()V

    .line 90
    .line 91
    .line 92
    invoke-virtual {p1, v0}, Landroid/animation/AnimatorSet;->playTogether(Ljava/util/Collection;)V

    .line 93
    .line 94
    .line 95
    invoke-static {p0}, Landroidx/lifecycle/ViewTreeLifecycleOwner;->a(Landroid/view/View;)Landroidx/lifecycle/w;

    .line 96
    .line 97
    .line 98
    move-result-object v1

    .line 99
    new-instance v4, Ldy/b;

    .line 100
    .line 101
    invoke-direct {v4, p0}, Ldy/b;-><init>(Lorg/xbet/crystal/presentation/views/CrystalFieldView;)V

    .line 102
    .line 103
    .line 104
    const/16 v6, 0xb

    .line 105
    .line 106
    const/4 v7, 0x0

    .line 107
    const/4 v2, 0x0

    .line 108
    const/4 v3, 0x0

    .line 109
    const/4 v5, 0x0

    .line 110
    invoke-static/range {v1 .. v7}, Lqb/s;->f(Landroidx/lifecycle/w;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lqb/n;

    .line 111
    .line 112
    .line 113
    move-result-object v0

    .line 114
    invoke-virtual {p1, v0}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 115
    .line 116
    .line 117
    iput-object p1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->h:Landroid/animation/AnimatorSet;

    .line 118
    .line 119
    return-void
.end method

.method public static final t(ILorg/xbet/crystal/presentation/views/CrystalFieldView;Lorg/xbet/crystal/presentation/views/Crystal;)I
    .locals 0

    .line 1
    iget p1, p1, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->e:I

    .line 2
    .line 3
    invoke-virtual {p2}, Lorg/xbet/crystal/presentation/views/Crystal;->getY()I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    mul-int p1, p1, p2

    .line 8
    .line 9
    add-int/2addr p0, p1

    .line 10
    return p0
.end method

.method public static final y(Ljava/util/Set;Landroid/animation/ValueAnimator;)V
    .locals 2

    .line 1
    check-cast p0, Ljava/lang/Iterable;

    .line 2
    .line 3
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-eqz v0, :cond_0

    .line 12
    .line 13
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    check-cast v0, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 18
    .line 19
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->getAnimatedValue()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    check-cast v1, Ljava/lang/Float;

    .line 24
    .line 25
    invoke-virtual {v1}, Ljava/lang/Float;->floatValue()F

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    invoke-virtual {v0, v1}, Landroid/view/View;->setAlpha(F)V

    .line 30
    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_0
    return-void
.end method

.method public static final z(Lorg/xbet/crystal/presentation/views/CrystalFieldView;Ljava/util/Map;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->setupShiftAnimator(Ljava/util/Map;)V

    .line 2
    .line 3
    .line 4
    iget-object p0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->h:Landroid/animation/AnimatorSet;

    .line 5
    .line 6
    if-eqz p0, :cond_0

    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/animation/AnimatorSet;->start()V

    .line 9
    .line 10
    .line 11
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method


# virtual methods
.method public final C(LZx/c;Ljava/util/Set;Ljava/util/Map;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LZx/c;",
            "Ljava/util/Set<",
            "Lorg/xbet/crystal/presentation/views/Crystal;",
            ">;",
            "Ljava/util/Map<",
            "Lorg/xbet/crystal/presentation/views/Crystal;",
            "Ljava/lang/Integer;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->a:Lorg/xbet/crystal/presentation/game/f;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p1}, Lorg/xbet/crystal/presentation/game/f;->P1(LZx/c;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    iget-object p1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->g:Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;

    .line 9
    .line 10
    if-eqz p1, :cond_1

    .line 11
    .line 12
    invoke-virtual {p1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;->c()Z

    .line 13
    .line 14
    .line 15
    move-result p1

    .line 16
    const/4 v0, 0x1

    .line 17
    if-ne p1, v0, :cond_1

    .line 18
    .line 19
    invoke-virtual {p0, p2, p3}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->x(Ljava/util/Set;Ljava/util/Map;)V

    .line 20
    .line 21
    .line 22
    iget-object p1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->i:Landroid/animation/ValueAnimator;

    .line 23
    .line 24
    if-eqz p1, :cond_2

    .line 25
    .line 26
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->start()V

    .line 27
    .line 28
    .line 29
    return-void

    .line 30
    :cond_1
    iget-object p1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->a:Lorg/xbet/crystal/presentation/game/f;

    .line 31
    .line 32
    if-eqz p1, :cond_2

    .line 33
    .line 34
    invoke-interface {p1}, Lorg/xbet/crystal/presentation/game/f;->O0()V

    .line 35
    .line 36
    .line 37
    :cond_2
    return-void
.end method

.method public final D()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->i:Landroid/animation/ValueAnimator;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/animation/Animator;->removeAllListeners()V

    .line 6
    .line 7
    .line 8
    :cond_0
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->i:Landroid/animation/ValueAnimator;

    .line 9
    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->cancel()V

    .line 13
    .line 14
    .line 15
    :cond_1
    return-void
.end method

.method public final E()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->h:Landroid/animation/AnimatorSet;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/animation/Animator;->removeAllListeners()V

    .line 6
    .line 7
    .line 8
    :cond_0
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->h:Landroid/animation/AnimatorSet;

    .line 9
    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/animation/AnimatorSet;->cancel()V

    .line 13
    .line 14
    .line 15
    :cond_1
    return-void
.end method

.method public final F()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->g:Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-virtual {v0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;->b()Z

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    invoke-virtual {v0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;->e()LZx/c;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-direct {p0, v0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->setupRound(LZx/c;)V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :cond_0
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->a:Lorg/xbet/crystal/presentation/game/f;

    .line 20
    .line 21
    if-eqz v0, :cond_1

    .line 22
    .line 23
    invoke-interface {v0}, Lorg/xbet/crystal/presentation/game/f;->O0()V

    .line 24
    .line 25
    .line 26
    :cond_1
    return-void
.end method

.method public final i()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->h:Landroid/animation/AnimatorSet;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-virtual {v0}, Landroid/animation/AnimatorSet;->isStarted()Z

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    if-ne v0, v1, :cond_0

    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->E()V

    .line 15
    .line 16
    .line 17
    :cond_0
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->i:Landroid/animation/ValueAnimator;

    .line 18
    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    if-eqz v0, :cond_1

    .line 22
    .line 23
    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->isStarted()Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-ne v0, v1, :cond_1

    .line 28
    .line 29
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->D()V

    .line 30
    .line 31
    .line 32
    :cond_1
    return-void
.end method

.method public final j()V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    iput-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->a:Lorg/xbet/crystal/presentation/game/f;

    .line 3
    .line 4
    return-void
.end method

.method public final k(Ljava/util/Set;)Ljava/util/Map;
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Set<",
            "Lorg/xbet/crystal/presentation/views/Crystal;",
            ">;)",
            "Ljava/util/Map<",
            "Lorg/xbet/crystal/presentation/views/Crystal;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 4
    .line 5
    .line 6
    check-cast p1, Ljava/util/Collection;

    .line 7
    .line 8
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->c:Ljava/util/List;

    .line 12
    .line 13
    iget-object v1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->d:Ljava/util/Map;

    .line 14
    .line 15
    new-instance v2, Ljava/util/ArrayList;

    .line 16
    .line 17
    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 18
    .line 19
    .line 20
    invoke-interface {v1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 29
    .line 30
    .line 31
    move-result v3

    .line 32
    if-eqz v3, :cond_0

    .line 33
    .line 34
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v3

    .line 38
    check-cast v3, Ljava/util/Map$Entry;

    .line 39
    .line 40
    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    .line 41
    .line 42
    .line 43
    move-result-object v3

    .line 44
    check-cast v3, Ljava/util/List;

    .line 45
    .line 46
    invoke-static {v2, v3}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 47
    .line 48
    .line 49
    goto :goto_0

    .line 50
    :cond_0
    invoke-static {p1, v2}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    new-instance v1, Ljava/util/LinkedHashMap;

    .line 55
    .line 56
    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 57
    .line 58
    .line 59
    iget v2, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->b:I

    .line 60
    .line 61
    add-int/lit8 v2, v2, -0x1

    .line 62
    .line 63
    const/4 v3, 0x0

    .line 64
    invoke-static {v2, v3}, Lkotlin/ranges/f;->u(II)Lkotlin/ranges/c;

    .line 65
    .line 66
    .line 67
    move-result-object v2

    .line 68
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 69
    .line 70
    .line 71
    move-result-object v2

    .line 72
    :cond_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 73
    .line 74
    .line 75
    move-result v3

    .line 76
    if-eqz v3, :cond_d

    .line 77
    .line 78
    move-object v3, v2

    .line 79
    check-cast v3, Lkotlin/collections/L;

    .line 80
    .line 81
    invoke-virtual {v3}, Lkotlin/collections/L;->b()I

    .line 82
    .line 83
    .line 84
    move-result v3

    .line 85
    new-instance v4, Ljava/util/ArrayList;

    .line 86
    .line 87
    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 88
    .line 89
    .line 90
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 91
    .line 92
    .line 93
    move-result-object v5

    .line 94
    :cond_2
    :goto_1
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    .line 95
    .line 96
    .line 97
    move-result v6

    .line 98
    if-eqz v6, :cond_3

    .line 99
    .line 100
    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 101
    .line 102
    .line 103
    move-result-object v6

    .line 104
    move-object v7, v6

    .line 105
    check-cast v7, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 106
    .line 107
    invoke-virtual {v7}, Lorg/xbet/crystal/presentation/views/Crystal;->getY()I

    .line 108
    .line 109
    .line 110
    move-result v7

    .line 111
    if-ne v7, v3, :cond_2

    .line 112
    .line 113
    invoke-interface {v4, v6}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 114
    .line 115
    .line 116
    goto :goto_1

    .line 117
    :cond_3
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 118
    .line 119
    .line 120
    move-result-object v3

    .line 121
    :cond_4
    :goto_2
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 122
    .line 123
    .line 124
    move-result v4

    .line 125
    if-eqz v4, :cond_1

    .line 126
    .line 127
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 128
    .line 129
    .line 130
    move-result-object v4

    .line 131
    check-cast v4, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 132
    .line 133
    new-instance v5, Ljava/util/ArrayList;

    .line 134
    .line 135
    invoke-direct {v5}, Ljava/util/ArrayList;-><init>()V

    .line 136
    .line 137
    .line 138
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 139
    .line 140
    .line 141
    move-result-object v6

    .line 142
    :cond_5
    :goto_3
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    .line 143
    .line 144
    .line 145
    move-result v7

    .line 146
    if-eqz v7, :cond_6

    .line 147
    .line 148
    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 149
    .line 150
    .line 151
    move-result-object v7

    .line 152
    move-object v8, v7

    .line 153
    check-cast v8, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 154
    .line 155
    invoke-virtual {v8}, Lorg/xbet/crystal/presentation/views/Crystal;->getX()I

    .line 156
    .line 157
    .line 158
    move-result v8

    .line 159
    invoke-virtual {v4}, Lorg/xbet/crystal/presentation/views/Crystal;->getX()I

    .line 160
    .line 161
    .line 162
    move-result v9

    .line 163
    if-ne v8, v9, :cond_5

    .line 164
    .line 165
    invoke-interface {v5, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 166
    .line 167
    .line 168
    goto :goto_3

    .line 169
    :cond_6
    invoke-virtual {v4}, Lorg/xbet/crystal/presentation/views/Crystal;->getY()I

    .line 170
    .line 171
    .line 172
    move-result v6

    .line 173
    iget v7, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->b:I

    .line 174
    .line 175
    neg-int v7, v7

    .line 176
    if-gt v7, v6, :cond_4

    .line 177
    .line 178
    :goto_4
    invoke-interface {v5}, Ljava/util/Collection;->isEmpty()Z

    .line 179
    .line 180
    .line 181
    move-result v8

    .line 182
    if-eqz v8, :cond_7

    .line 183
    .line 184
    goto :goto_6

    .line 185
    :cond_7
    invoke-interface {v5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 186
    .line 187
    .line 188
    move-result-object v8

    .line 189
    :goto_5
    invoke-interface {v8}, Ljava/util/Iterator;->hasNext()Z

    .line 190
    .line 191
    .line 192
    move-result v9

    .line 193
    if-eqz v9, :cond_8

    .line 194
    .line 195
    invoke-interface {v8}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 196
    .line 197
    .line 198
    move-result-object v9

    .line 199
    check-cast v9, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 200
    .line 201
    invoke-virtual {v9}, Lorg/xbet/crystal/presentation/views/Crystal;->getY()I

    .line 202
    .line 203
    .line 204
    move-result v9

    .line 205
    if-eq v9, v6, :cond_b

    .line 206
    .line 207
    goto :goto_5

    .line 208
    :cond_8
    :goto_6
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 209
    .line 210
    .line 211
    move-result-object v8

    .line 212
    :cond_9
    invoke-interface {v8}, Ljava/util/Iterator;->hasNext()Z

    .line 213
    .line 214
    .line 215
    move-result v9

    .line 216
    if-eqz v9, :cond_a

    .line 217
    .line 218
    invoke-interface {v8}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 219
    .line 220
    .line 221
    move-result-object v9

    .line 222
    move-object v10, v9

    .line 223
    check-cast v10, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 224
    .line 225
    invoke-virtual {v10}, Lorg/xbet/crystal/presentation/views/Crystal;->getY()I

    .line 226
    .line 227
    .line 228
    move-result v11

    .line 229
    if-ne v11, v6, :cond_9

    .line 230
    .line 231
    invoke-virtual {v10}, Lorg/xbet/crystal/presentation/views/Crystal;->getX()I

    .line 232
    .line 233
    .line 234
    move-result v10

    .line 235
    invoke-virtual {v4}, Lorg/xbet/crystal/presentation/views/Crystal;->getX()I

    .line 236
    .line 237
    .line 238
    move-result v11

    .line 239
    if-ne v10, v11, :cond_9

    .line 240
    .line 241
    goto :goto_7

    .line 242
    :cond_a
    const/4 v9, 0x0

    .line 243
    :goto_7
    check-cast v9, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 244
    .line 245
    if-nez v9, :cond_c

    .line 246
    .line 247
    :cond_b
    if-eq v6, v7, :cond_4

    .line 248
    .line 249
    add-int/lit8 v6, v6, -0x1

    .line 250
    .line 251
    goto :goto_4

    .line 252
    :cond_c
    invoke-virtual {v4}, Lorg/xbet/crystal/presentation/views/Crystal;->getY()I

    .line 253
    .line 254
    .line 255
    move-result v5

    .line 256
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 257
    .line 258
    .line 259
    move-result-object v5

    .line 260
    invoke-interface {v1, v9, v5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 261
    .line 262
    .line 263
    invoke-interface {v0, v9}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 264
    .line 265
    .line 266
    invoke-interface {v0, v4}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 267
    .line 268
    .line 269
    goto/16 :goto_2

    .line 270
    .line 271
    :cond_d
    return-object v1
.end method

.method public final l(LZx/c;Ljava/util/Set;Ljava/util/Map;)Landroid/animation/AnimatorSet;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LZx/c;",
            "Ljava/util/Set<",
            "Lorg/xbet/crystal/presentation/views/Crystal;",
            ">;",
            "Ljava/util/Map<",
            "Lorg/xbet/crystal/presentation/views/Crystal;",
            "Ljava/lang/Integer;",
            ">;)",
            "Landroid/animation/AnimatorSet;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->c:Ljava/util/List;

    .line 2
    .line 3
    new-instance v1, Ljava/util/ArrayList;

    .line 4
    .line 5
    const/16 v2, 0xa

    .line 6
    .line 7
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 12
    .line 13
    .line 14
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 19
    .line 20
    .line 21
    move-result v2

    .line 22
    if-eqz v2, :cond_0

    .line 23
    .line 24
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v2

    .line 28
    check-cast v2, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 29
    .line 30
    const/high16 v3, 0x3f800000    # 1.0f

    .line 31
    .line 32
    invoke-virtual {v2, v3}, Landroid/view/View;->setAlpha(F)V

    .line 33
    .line 34
    .line 35
    iget v3, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->f:I

    .line 36
    .line 37
    int-to-float v3, v3

    .line 38
    neg-float v3, v3

    .line 39
    const/4 v4, 0x2

    .line 40
    new-array v4, v4, [F

    .line 41
    .line 42
    const/4 v5, 0x0

    .line 43
    aput v3, v4, v5

    .line 44
    .line 45
    const/4 v3, 0x0

    .line 46
    const/4 v5, 0x1

    .line 47
    aput v3, v4, v5

    .line 48
    .line 49
    invoke-static {v4}, Landroid/animation/ValueAnimator;->ofFloat([F)Landroid/animation/ValueAnimator;

    .line 50
    .line 51
    .line 52
    move-result-object v3

    .line 53
    new-instance v4, Ldy/g;

    .line 54
    .line 55
    invoke-direct {v4, v2}, Ldy/g;-><init>(Lorg/xbet/crystal/presentation/views/Crystal;)V

    .line 56
    .line 57
    .line 58
    invoke-virtual {v3, v4}, Landroid/animation/ValueAnimator;->addUpdateListener(Landroid/animation/ValueAnimator$AnimatorUpdateListener;)V

    .line 59
    .line 60
    .line 61
    iget v4, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->b:I

    .line 62
    .line 63
    invoke-virtual {v2}, Lorg/xbet/crystal/presentation/views/Crystal;->getX()I

    .line 64
    .line 65
    .line 66
    move-result v2

    .line 67
    sub-int/2addr v4, v2

    .line 68
    int-to-long v4, v4

    .line 69
    const-wide/16 v6, 0x64

    .line 70
    .line 71
    mul-long v4, v4, v6

    .line 72
    .line 73
    const-wide/16 v6, 0x12c

    .line 74
    .line 75
    add-long/2addr v4, v6

    .line 76
    invoke-virtual {v3, v4, v5}, Landroid/animation/ValueAnimator;->setDuration(J)Landroid/animation/ValueAnimator;

    .line 77
    .line 78
    .line 79
    invoke-interface {v1, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 80
    .line 81
    .line 82
    goto :goto_0

    .line 83
    :cond_0
    new-instance v0, Landroid/animation/AnimatorSet;

    .line 84
    .line 85
    invoke-direct {v0}, Landroid/animation/AnimatorSet;-><init>()V

    .line 86
    .line 87
    .line 88
    invoke-virtual {v0, v1}, Landroid/animation/AnimatorSet;->playTogether(Ljava/util/Collection;)V

    .line 89
    .line 90
    .line 91
    invoke-static {p0}, Landroidx/lifecycle/ViewTreeLifecycleOwner;->a(Landroid/view/View;)Landroidx/lifecycle/w;

    .line 92
    .line 93
    .line 94
    move-result-object v2

    .line 95
    new-instance v5, Ldy/h;

    .line 96
    .line 97
    invoke-direct {v5, p0, p1, p2, p3}, Ldy/h;-><init>(Lorg/xbet/crystal/presentation/views/CrystalFieldView;LZx/c;Ljava/util/Set;Ljava/util/Map;)V

    .line 98
    .line 99
    .line 100
    const/16 v7, 0xb

    .line 101
    .line 102
    const/4 v8, 0x0

    .line 103
    const/4 v3, 0x0

    .line 104
    const/4 v4, 0x0

    .line 105
    const/4 v6, 0x0

    .line 106
    invoke-static/range {v2 .. v8}, Lqb/s;->f(Landroidx/lifecycle/w;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lqb/n;

    .line 107
    .line 108
    .line 109
    move-result-object p1

    .line 110
    invoke-virtual {v0, p1}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 111
    .line 112
    .line 113
    return-object v0
.end method

.method public final o(I)Ljava/util/List;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Ljava/util/List<",
            "Ljava/util/List<",
            "Lorg/xbet/crystal/domain/models/CrystalTypeEnum;",
            ">;>;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v0, p1}, Ljava/util/ArrayList;-><init>(I)V

    .line 4
    .line 5
    .line 6
    const/4 v1, 0x0

    .line 7
    const/4 v2, 0x0

    .line 8
    :goto_0
    if-ge v2, p1, :cond_1

    .line 9
    .line 10
    new-instance v3, Ljava/util/ArrayList;

    .line 11
    .line 12
    invoke-direct {v3, p1}, Ljava/util/ArrayList;-><init>(I)V

    .line 13
    .line 14
    .line 15
    const/4 v4, 0x0

    .line 16
    :goto_1
    if-ge v4, p1, :cond_0

    .line 17
    .line 18
    const-class v5, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 19
    .line 20
    invoke-static {v5}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 21
    .line 22
    .line 23
    move-result-object v5

    .line 24
    invoke-virtual {p0, v5}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->u(Lkotlin/reflect/d;)Ljava/lang/Enum;

    .line 25
    .line 26
    .line 27
    move-result-object v5

    .line 28
    check-cast v5, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 29
    .line 30
    invoke-virtual {v3, v5}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 31
    .line 32
    .line 33
    add-int/lit8 v4, v4, 0x1

    .line 34
    .line 35
    goto :goto_1

    .line 36
    :cond_0
    invoke-virtual {v0, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 37
    .line 38
    .line 39
    add-int/lit8 v2, v2, 0x1

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_1
    return-object v0
.end method

.method public onLayout(ZIIII)V
    .locals 7

    .line 1
    invoke-super/range {p0 .. p5}, Landroid/widget/FrameLayout;->onLayout(ZIIII)V

    .line 2
    .line 3
    .line 4
    move-object p1, p0

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    iget p3, p1, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->f:I

    .line 10
    .line 11
    sub-int/2addr p2, p3

    .line 12
    div-int/lit8 p2, p2, 0x2

    .line 13
    .line 14
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 15
    .line 16
    .line 17
    move-result p3

    .line 18
    iget p4, p1, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->f:I

    .line 19
    .line 20
    sub-int/2addr p3, p4

    .line 21
    div-int/lit8 p3, p3, 0x2

    .line 22
    .line 23
    iget p4, p1, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->b:I

    .line 24
    .line 25
    const/4 p5, 0x0

    .line 26
    invoke-static {p5, p4}, Lkotlin/ranges/f;->z(II)Lkotlin/ranges/IntRange;

    .line 27
    .line 28
    .line 29
    move-result-object p4

    .line 30
    invoke-interface {p4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 31
    .line 32
    .line 33
    move-result-object p4

    .line 34
    move v0, p3

    .line 35
    const/4 v1, 0x0

    .line 36
    :goto_0
    invoke-interface {p4}, Ljava/util/Iterator;->hasNext()Z

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    if-eqz v2, :cond_1

    .line 41
    .line 42
    move-object v2, p4

    .line 43
    check-cast v2, Lkotlin/collections/L;

    .line 44
    .line 45
    invoke-virtual {v2}, Lkotlin/collections/L;->b()I

    .line 46
    .line 47
    .line 48
    iget v2, p1, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->b:I

    .line 49
    .line 50
    invoke-static {p5, v2}, Lkotlin/ranges/f;->z(II)Lkotlin/ranges/IntRange;

    .line 51
    .line 52
    .line 53
    move-result-object v2

    .line 54
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 55
    .line 56
    .line 57
    move-result-object v2

    .line 58
    move v3, p2

    .line 59
    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 60
    .line 61
    .line 62
    move-result v4

    .line 63
    if-eqz v4, :cond_0

    .line 64
    .line 65
    move-object v4, v2

    .line 66
    check-cast v4, Lkotlin/collections/L;

    .line 67
    .line 68
    invoke-virtual {v4}, Lkotlin/collections/L;->b()I

    .line 69
    .line 70
    .line 71
    iget-object v4, p1, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->c:Ljava/util/List;

    .line 72
    .line 73
    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    move-result-object v4

    .line 77
    check-cast v4, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 78
    .line 79
    iget v5, p1, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->e:I

    .line 80
    .line 81
    add-int v6, v3, v5

    .line 82
    .line 83
    add-int/2addr v5, v0

    .line 84
    invoke-virtual {v4, v3, v0, v6, v5}, Landroid/view/View;->layout(IIII)V

    .line 85
    .line 86
    .line 87
    iget v4, p1, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->e:I

    .line 88
    .line 89
    add-int/2addr v3, v4

    .line 90
    add-int/lit8 v1, v1, 0x1

    .line 91
    .line 92
    goto :goto_1

    .line 93
    :cond_0
    iget v2, p1, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->e:I

    .line 94
    .line 95
    add-int/2addr v0, v2

    .line 96
    goto :goto_0

    .line 97
    :cond_1
    new-instance p4, Ldy/c;

    .line 98
    .line 99
    invoke-direct {p4, p2, p0}, Ldy/c;-><init>(ILorg/xbet/crystal/presentation/views/CrystalFieldView;)V

    .line 100
    .line 101
    .line 102
    new-instance p2, Ldy/d;

    .line 103
    .line 104
    invoke-direct {p2, p3, p0}, Ldy/d;-><init>(ILorg/xbet/crystal/presentation/views/CrystalFieldView;)V

    .line 105
    .line 106
    .line 107
    iget-object p3, p1, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->d:Ljava/util/Map;

    .line 108
    .line 109
    new-instance p5, Ljava/util/ArrayList;

    .line 110
    .line 111
    invoke-direct {p5}, Ljava/util/ArrayList;-><init>()V

    .line 112
    .line 113
    .line 114
    invoke-interface {p3}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    .line 115
    .line 116
    .line 117
    move-result-object p3

    .line 118
    invoke-interface {p3}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    .line 119
    .line 120
    .line 121
    move-result-object p3

    .line 122
    :goto_2
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    .line 123
    .line 124
    .line 125
    move-result v0

    .line 126
    if-eqz v0, :cond_2

    .line 127
    .line 128
    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 129
    .line 130
    .line 131
    move-result-object v0

    .line 132
    check-cast v0, Ljava/util/Map$Entry;

    .line 133
    .line 134
    invoke-interface {v0}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    .line 135
    .line 136
    .line 137
    move-result-object v0

    .line 138
    check-cast v0, Ljava/util/List;

    .line 139
    .line 140
    invoke-static {p5, v0}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 141
    .line 142
    .line 143
    goto :goto_2

    .line 144
    :cond_2
    invoke-interface {p5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 145
    .line 146
    .line 147
    move-result-object p3

    .line 148
    :goto_3
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    .line 149
    .line 150
    .line 151
    move-result p5

    .line 152
    if-eqz p5, :cond_3

    .line 153
    .line 154
    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 155
    .line 156
    .line 157
    move-result-object p5

    .line 158
    check-cast p5, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 159
    .line 160
    invoke-interface {p4, p5}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 161
    .line 162
    .line 163
    move-result-object v0

    .line 164
    check-cast v0, Ljava/lang/Number;

    .line 165
    .line 166
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 167
    .line 168
    .line 169
    move-result v0

    .line 170
    invoke-interface {p2, p5}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 171
    .line 172
    .line 173
    move-result-object v1

    .line 174
    check-cast v1, Ljava/lang/Number;

    .line 175
    .line 176
    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    .line 177
    .line 178
    .line 179
    move-result v1

    .line 180
    invoke-interface {p4, p5}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 181
    .line 182
    .line 183
    move-result-object v2

    .line 184
    check-cast v2, Ljava/lang/Number;

    .line 185
    .line 186
    invoke-virtual {v2}, Ljava/lang/Number;->intValue()I

    .line 187
    .line 188
    .line 189
    move-result v2

    .line 190
    iget v3, p1, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->e:I

    .line 191
    .line 192
    add-int/2addr v2, v3

    .line 193
    invoke-interface {p2, p5}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 194
    .line 195
    .line 196
    move-result-object v3

    .line 197
    check-cast v3, Ljava/lang/Number;

    .line 198
    .line 199
    invoke-virtual {v3}, Ljava/lang/Number;->intValue()I

    .line 200
    .line 201
    .line 202
    move-result v3

    .line 203
    iget v4, p1, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->e:I

    .line 204
    .line 205
    add-int/2addr v3, v4

    .line 206
    invoke-virtual {p5, v0, v1, v2, v3}, Landroid/view/View;->layout(IIII)V

    .line 207
    .line 208
    .line 209
    goto :goto_3

    .line 210
    :cond_3
    return-void
.end method

.method public onMeasure(II)V
    .locals 2

    .line 1
    invoke-super {p0, p1, p2}, Landroid/widget/FrameLayout;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 9
    .line 10
    .line 11
    move-result p2

    .line 12
    if-le p1, p2, :cond_0

    .line 13
    .line 14
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 15
    .line 16
    .line 17
    move-result p1

    .line 18
    goto :goto_0

    .line 19
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    :goto_0
    iput p1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->f:I

    .line 24
    .line 25
    iget p2, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->b:I

    .line 26
    .line 27
    div-int/2addr p1, p2

    .line 28
    iput p1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->e:I

    .line 29
    .line 30
    const/high16 p2, 0x40000000    # 2.0f

    .line 31
    .line 32
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 33
    .line 34
    .line 35
    move-result p1

    .line 36
    iget-object p2, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->c:Ljava/util/List;

    .line 37
    .line 38
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 39
    .line 40
    .line 41
    move-result-object p2

    .line 42
    :goto_1
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 43
    .line 44
    .line 45
    move-result v0

    .line 46
    if-eqz v0, :cond_1

    .line 47
    .line 48
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    check-cast v0, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 53
    .line 54
    invoke-virtual {v0, p1, p1}, Landroid/view/View;->measure(II)V

    .line 55
    .line 56
    .line 57
    goto :goto_1

    .line 58
    :cond_1
    iget-object p2, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->d:Ljava/util/Map;

    .line 59
    .line 60
    invoke-interface {p2}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    .line 61
    .line 62
    .line 63
    move-result-object p2

    .line 64
    invoke-interface {p2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    .line 65
    .line 66
    .line 67
    move-result-object p2

    .line 68
    :cond_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 69
    .line 70
    .line 71
    move-result v0

    .line 72
    if-eqz v0, :cond_3

    .line 73
    .line 74
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    check-cast v0, Ljava/util/Map$Entry;

    .line 79
    .line 80
    invoke-interface {v0}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    check-cast v0, Ljava/util/List;

    .line 85
    .line 86
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 87
    .line 88
    .line 89
    move-result-object v0

    .line 90
    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 91
    .line 92
    .line 93
    move-result v1

    .line 94
    if-eqz v1, :cond_2

    .line 95
    .line 96
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 97
    .line 98
    .line 99
    move-result-object v1

    .line 100
    check-cast v1, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 101
    .line 102
    invoke-virtual {v1, p1, p1}, Landroid/view/View;->measure(II)V

    .line 103
    .line 104
    .line 105
    goto :goto_2

    .line 106
    :cond_3
    return-void
.end method

.method public final q(Ljava/util/List;)Ljava/util/Set;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LZx/e;",
            ">;)",
            "Ljava/util/Set<",
            "Lorg/xbet/crystal/presentation/views/Crystal;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    if-eqz v1, :cond_3

    .line 15
    .line 16
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    check-cast v1, LZx/e;

    .line 21
    .line 22
    invoke-virtual {v1}, LZx/e;->c()Ljava/util/List;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    new-instance v2, Ljava/util/ArrayList;

    .line 27
    .line 28
    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 29
    .line 30
    .line 31
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 36
    .line 37
    .line 38
    move-result v3

    .line 39
    if-eqz v3, :cond_2

    .line 40
    .line 41
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object v3

    .line 45
    check-cast v3, Lkotlin/Pair;

    .line 46
    .line 47
    iget-object v4, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->c:Ljava/util/List;

    .line 48
    .line 49
    new-instance v5, Ljava/util/ArrayList;

    .line 50
    .line 51
    invoke-direct {v5}, Ljava/util/ArrayList;-><init>()V

    .line 52
    .line 53
    .line 54
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 55
    .line 56
    .line 57
    move-result-object v4

    .line 58
    :cond_0
    :goto_2
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 59
    .line 60
    .line 61
    move-result v6

    .line 62
    if-eqz v6, :cond_1

    .line 63
    .line 64
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object v6

    .line 68
    move-object v7, v6

    .line 69
    check-cast v7, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 70
    .line 71
    new-instance v8, Lkotlin/Pair;

    .line 72
    .line 73
    invoke-virtual {v7}, Lorg/xbet/crystal/presentation/views/Crystal;->getX()I

    .line 74
    .line 75
    .line 76
    move-result v9

    .line 77
    invoke-static {v9}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 78
    .line 79
    .line 80
    move-result-object v9

    .line 81
    invoke-virtual {v7}, Lorg/xbet/crystal/presentation/views/Crystal;->getY()I

    .line 82
    .line 83
    .line 84
    move-result v7

    .line 85
    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 86
    .line 87
    .line 88
    move-result-object v7

    .line 89
    invoke-direct {v8, v9, v7}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 90
    .line 91
    .line 92
    invoke-static {v3, v8}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 93
    .line 94
    .line 95
    move-result v7

    .line 96
    if-eqz v7, :cond_0

    .line 97
    .line 98
    invoke-interface {v5, v6}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 99
    .line 100
    .line 101
    goto :goto_2

    .line 102
    :cond_1
    invoke-static {v2, v5}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 103
    .line 104
    .line 105
    goto :goto_1

    .line 106
    :cond_2
    invoke-static {v0, v2}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 107
    .line 108
    .line 109
    goto :goto_0

    .line 110
    :cond_3
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->E1(Ljava/lang/Iterable;)Ljava/util/Set;

    .line 111
    .line 112
    .line 113
    move-result-object p1

    .line 114
    return-object p1
.end method

.method public final r(Ljava/util/List;)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LZx/c;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;

    .line 2
    .line 3
    invoke-direct {v0, p1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;-><init>(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    iput-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->g:Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;

    .line 7
    .line 8
    invoke-virtual {v0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView$b;->a()LZx/c;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    invoke-direct {p0, p1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->setupRound(LZx/c;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final setUpdateInterface(Lorg/xbet/crystal/presentation/game/f;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->a:Lorg/xbet/crystal/presentation/game/f;

    .line 2
    .line 3
    return-void
.end method

.method public final u(Lkotlin/reflect/d;)Ljava/lang/Enum;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Enum<",
            "*>;>(",
            "Lkotlin/reflect/d<",
            "TT;>;)TT;"
        }
    .end annotation

    .line 1
    invoke-static {p1}, LNc/a;->b(Lkotlin/reflect/d;)Ljava/lang/Class;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p1}, Ljava/lang/Class;->getEnumConstants()[Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    check-cast p1, [Ljava/lang/Enum;

    .line 10
    .line 11
    if-eqz p1, :cond_0

    .line 12
    .line 13
    sget-object v0, Lkotlin/random/Random;->Default:Lkotlin/random/Random$Default;

    .line 14
    .line 15
    array-length v1, p1

    .line 16
    invoke-virtual {v0, v1}, Lkotlin/random/Random$Default;->nextInt(I)I

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    aget-object p1, p1, v0

    .line 21
    .line 22
    return-object p1

    .line 23
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    .line 24
    .line 25
    const-string v0, "Enum random values == null"

    .line 26
    .line 27
    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    throw p1
.end method

.method public final v(Ljava/util/List;Z)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LZx/c;",
            ">;Z)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->c:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->clear()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->d:Ljava/util/Map;

    .line 7
    .line 8
    invoke-interface {v0}, Ljava/util/Map;->clear()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Landroid/view/ViewGroup;->removeAllViews()V

    .line 12
    .line 13
    .line 14
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->I0(Ljava/util/List;)Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    check-cast p1, LZx/c;

    .line 19
    .line 20
    invoke-virtual {p1}, LZx/c;->a()Ljava/util/List;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    invoke-direct {p0, p1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->setupCrystals(Ljava/util/List;)V

    .line 25
    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->w()V

    .line 28
    .line 29
    .line 30
    if-eqz p2, :cond_0

    .line 31
    .line 32
    iget-object p1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->a:Lorg/xbet/crystal/presentation/game/f;

    .line 33
    .line 34
    if-eqz p1, :cond_0

    .line 35
    .line 36
    invoke-interface {p1}, Lorg/xbet/crystal/presentation/game/f;->O0()V

    .line 37
    .line 38
    .line 39
    :cond_0
    return-void
.end method

.method public final w()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->c:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, Lorg/xbet/crystal/presentation/views/Crystal;

    .line 18
    .line 19
    const/high16 v2, 0x3f800000    # 1.0f

    .line 20
    .line 21
    invoke-virtual {v1, v2}, Landroid/view/View;->setAlpha(F)V

    .line 22
    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_0
    return-void
.end method

.method public final x(Ljava/util/Set;Ljava/util/Map;)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Set<",
            "Lorg/xbet/crystal/presentation/views/Crystal;",
            ">;",
            "Ljava/util/Map<",
            "Lorg/xbet/crystal/presentation/views/Crystal;",
            "Ljava/lang/Integer;",
            ">;)V"
        }
    .end annotation

    .line 1
    const/4 v0, 0x2

    .line 2
    new-array v1, v0, [F

    .line 3
    .line 4
    fill-array-data v1, :array_0

    .line 5
    .line 6
    .line 7
    invoke-static {v1}, Landroid/animation/ValueAnimator;->ofFloat([F)Landroid/animation/ValueAnimator;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    new-instance v2, Ldy/e;

    .line 12
    .line 13
    invoke-direct {v2, p1}, Ldy/e;-><init>(Ljava/util/Set;)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {v1, v2}, Landroid/animation/ValueAnimator;->addUpdateListener(Landroid/animation/ValueAnimator$AnimatorUpdateListener;)V

    .line 17
    .line 18
    .line 19
    invoke-static {p0}, Landroidx/lifecycle/ViewTreeLifecycleOwner;->a(Landroid/view/View;)Landroidx/lifecycle/w;

    .line 20
    .line 21
    .line 22
    move-result-object v3

    .line 23
    new-instance v6, Ldy/f;

    .line 24
    .line 25
    invoke-direct {v6, p0, p2}, Ldy/f;-><init>(Lorg/xbet/crystal/presentation/views/CrystalFieldView;Ljava/util/Map;)V

    .line 26
    .line 27
    .line 28
    const/16 v8, 0xb

    .line 29
    .line 30
    const/4 v9, 0x0

    .line 31
    const/4 v4, 0x0

    .line 32
    const/4 v5, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    invoke-static/range {v3 .. v9}, Lqb/s;->f(Landroidx/lifecycle/w;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lqb/n;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    invoke-virtual {v1, p1}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 39
    .line 40
    .line 41
    invoke-virtual {v1, v0}, Landroid/animation/ValueAnimator;->setRepeatCount(I)V

    .line 42
    .line 43
    .line 44
    const-wide/16 p1, 0x3e8

    .line 45
    .line 46
    invoke-virtual {v1, p1, p2}, Landroid/animation/ValueAnimator;->setDuration(J)Landroid/animation/ValueAnimator;

    .line 47
    .line 48
    .line 49
    iput-object v1, p0, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->i:Landroid/animation/ValueAnimator;

    .line 50
    .line 51
    return-void

    .line 52
    nop

    :array_0
    .array-data 4
        0x3f800000    # 1.0f
        0x0
    .end array-data
.end method
