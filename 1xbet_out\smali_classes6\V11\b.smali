.class public final synthetic LV11/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LV11/d;

.field public final synthetic b:LV11/d$b;


# direct methods
.method public synthetic constructor <init>(LV11/d;LV11/d$b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LV11/b;->a:LV11/d;

    iput-object p2, p0, LV11/b;->b:LV11/d$b;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LV11/b;->a:LV11/d;

    iget-object v1, p0, LV11/b;->b:LV11/d$b;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, p1}, LV11/d;->t(LV11/d;LV11/d$b;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
