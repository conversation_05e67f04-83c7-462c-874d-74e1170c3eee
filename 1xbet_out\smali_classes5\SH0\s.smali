.class public final LSH0/s;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LL2/a;


# instance fields
.field public final a:Landroidx/constraintlayout/widget/ConstraintLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/uikit/components/lottie/LottieView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final c:Landroid/widget/ImageView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final d:Landroidx/recyclerview/widget/RecyclerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final e:Landroidx/core/widget/NestedScrollView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final f:LSH0/B;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final g:Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final h:Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamCardView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroidx/constraintlayout/widget/ConstraintLayout;Lorg/xbet/uikit/components/lottie/LottieView;Landroid/widget/ImageView;Landroidx/recyclerview/widget/RecyclerView;Landroidx/core/widget/NestedScrollView;LSH0/B;Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamCardView;)V
    .locals 0
    .param p1    # Landroidx/constraintlayout/widget/ConstraintLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit/components/lottie/LottieView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Landroid/widget/ImageView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Landroidx/recyclerview/widget/RecyclerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p5    # Landroidx/core/widget/NestedScrollView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p6    # LSH0/B;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamCardView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LSH0/s;->a:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 5
    .line 6
    iput-object p2, p0, LSH0/s;->b:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 7
    .line 8
    iput-object p3, p0, LSH0/s;->c:Landroid/widget/ImageView;

    .line 9
    .line 10
    iput-object p4, p0, LSH0/s;->d:Landroidx/recyclerview/widget/RecyclerView;

    .line 11
    .line 12
    iput-object p5, p0, LSH0/s;->e:Landroidx/core/widget/NestedScrollView;

    .line 13
    .line 14
    iput-object p6, p0, LSH0/s;->f:LSH0/B;

    .line 15
    .line 16
    iput-object p7, p0, LSH0/s;->g:Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;

    .line 17
    .line 18
    iput-object p8, p0, LSH0/s;->h:Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamCardView;

    .line 19
    .line 20
    return-void
.end method

.method public static a(Landroid/view/View;)LSH0/s;
    .locals 11
    .param p0    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    sget v0, LQH0/a;->emptyView:I

    .line 2
    .line 3
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    move-object v4, v1

    .line 8
    check-cast v4, Lorg/xbet/uikit/components/lottie/LottieView;

    .line 9
    .line 10
    if-eqz v4, :cond_0

    .line 11
    .line 12
    sget v0, LQH0/a;->ivBackground:I

    .line 13
    .line 14
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    move-object v5, v1

    .line 19
    check-cast v5, Landroid/widget/ImageView;

    .line 20
    .line 21
    if-eqz v5, :cond_0

    .line 22
    .line 23
    sget v0, LQH0/a;->rvStatisticBlock:I

    .line 24
    .line 25
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    move-object v6, v1

    .line 30
    check-cast v6, Landroidx/recyclerview/widget/RecyclerView;

    .line 31
    .line 32
    if-eqz v6, :cond_0

    .line 33
    .line 34
    sget v0, LQH0/a;->scrollView:I

    .line 35
    .line 36
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 37
    .line 38
    .line 39
    move-result-object v1

    .line 40
    move-object v7, v1

    .line 41
    check-cast v7, Landroidx/core/widget/NestedScrollView;

    .line 42
    .line 43
    if-eqz v7, :cond_0

    .line 44
    .line 45
    sget v0, LQH0/a;->shimmers:I

    .line 46
    .line 47
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    if-eqz v1, :cond_0

    .line 52
    .line 53
    invoke-static {v1}, LSH0/B;->a(Landroid/view/View;)LSH0/B;

    .line 54
    .line 55
    .line 56
    move-result-object v8

    .line 57
    sget v0, LQH0/a;->staticNavigationBar:I

    .line 58
    .line 59
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    move-object v9, v1

    .line 64
    check-cast v9, Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;

    .line 65
    .line 66
    if-eqz v9, :cond_0

    .line 67
    .line 68
    sget v0, LQH0/a;->teamCardView:I

    .line 69
    .line 70
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    move-object v10, v1

    .line 75
    check-cast v10, Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamCardView;

    .line 76
    .line 77
    if-eqz v10, :cond_0

    .line 78
    .line 79
    new-instance v2, LSH0/s;

    .line 80
    .line 81
    move-object v3, p0

    .line 82
    check-cast v3, Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 83
    .line 84
    invoke-direct/range {v2 .. v10}, LSH0/s;-><init>(Landroidx/constraintlayout/widget/ConstraintLayout;Lorg/xbet/uikit/components/lottie/LottieView;Landroid/widget/ImageView;Landroidx/recyclerview/widget/RecyclerView;Landroidx/core/widget/NestedScrollView;LSH0/B;Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;Lorg/xbet/statistic/statistic_core/presentation/view/TwoTeamCardView;)V

    .line 85
    .line 86
    .line 87
    return-object v2

    .line 88
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 89
    .line 90
    .line 91
    move-result-object p0

    .line 92
    invoke-virtual {p0, v0}, Landroid/content/res/Resources;->getResourceName(I)Ljava/lang/String;

    .line 93
    .line 94
    .line 95
    move-result-object p0

    .line 96
    new-instance v0, Ljava/lang/NullPointerException;

    .line 97
    .line 98
    const-string v1, "Missing required view with ID: "

    .line 99
    .line 100
    invoke-virtual {v1, p0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 101
    .line 102
    .line 103
    move-result-object p0

    .line 104
    invoke-direct {v0, p0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 105
    .line 106
    .line 107
    throw v0
.end method


# virtual methods
.method public b()Landroidx/constraintlayout/widget/ConstraintLayout;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    iget-object v0, p0, LSH0/s;->a:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 2
    .line 3
    return-object v0
.end method

.method public bridge synthetic getRoot()Landroid/view/View;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LSH0/s;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
