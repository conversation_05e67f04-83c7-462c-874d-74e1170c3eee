.class public final Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$a;,
        Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00aa\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0000\u0018\u0000 \u0087\u00012\u00020\u0001:\u0002\u0088\u0001B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u000f\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u000f\u0010\t\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\t\u0010\u0003J\u000f\u0010\n\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u0003J\u000f\u0010\u000b\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u0003J\u001d\u0010\u000e\u001a\u00020\u00042\u000c\u0010\r\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u000cH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u0012\u001a\u00020\u00042\u0006\u0010\u0011\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0017\u0010\u0016\u001a\u00020\u00042\u0006\u0010\u0015\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u001f\u0010\u001b\u001a\u00020\u00042\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001a\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\'\u0010\u001e\u001a\u00020\u00042\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001d\u001a\u00020\u00062\u0006\u0010\u001a\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u000f\u0010 \u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008 \u0010\u0003J\u000f\u0010!\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008!\u0010\u0003J\u0017\u0010$\u001a\u00020\u00042\u0006\u0010#\u001a\u00020\"H\u0002\u00a2\u0006\u0004\u0008$\u0010%J\u000f\u0010&\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008&\u0010\u0003J\u0019\u0010)\u001a\u00020\u00042\u0008\u0010(\u001a\u0004\u0018\u00010\'H\u0014\u00a2\u0006\u0004\u0008)\u0010*J\u0019\u0010+\u001a\u00020\u00042\u0008\u0010(\u001a\u0004\u0018\u00010\'H\u0016\u00a2\u0006\u0004\u0008+\u0010*J\u000f\u0010,\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008,\u0010\u0003J\u000f\u0010-\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008-\u0010\u0003J\u000f\u0010.\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008.\u0010\u0003J!\u00101\u001a\u00020\u00042\u0006\u00100\u001a\u00020/2\u0008\u0010(\u001a\u0004\u0018\u00010\'H\u0016\u00a2\u0006\u0004\u00081\u00102R+\u00109\u001a\u00020\u00062\u0006\u00103\u001a\u00020\u00068B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u00084\u00105\u001a\u0004\u00086\u0010\u0008\"\u0004\u00087\u00108R\u001b\u0010?\u001a\u00020:8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008;\u0010<\u001a\u0004\u0008=\u0010>R\"\u0010G\u001a\u00020@8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008A\u0010B\u001a\u0004\u0008C\u0010D\"\u0004\u0008E\u0010FR\"\u0010O\u001a\u00020H8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008I\u0010J\u001a\u0004\u0008K\u0010L\"\u0004\u0008M\u0010NR\"\u0010W\u001a\u00020P8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008Q\u0010R\u001a\u0004\u0008S\u0010T\"\u0004\u0008U\u0010VR\"\u0010_\u001a\u00020X8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008Y\u0010Z\u001a\u0004\u0008[\u0010\\\"\u0004\u0008]\u0010^R\"\u0010g\u001a\u00020`8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008a\u0010b\u001a\u0004\u0008c\u0010d\"\u0004\u0008e\u0010fR+\u0010o\u001a\u00020h2\u0006\u00103\u001a\u00020h8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008i\u0010j\u001a\u0004\u0008k\u0010l\"\u0004\u0008m\u0010nR\u001b\u0010u\u001a\u00020p8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008q\u0010r\u001a\u0004\u0008s\u0010tR\u0018\u0010y\u001a\u0004\u0018\u00010v8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008w\u0010xR\u0016\u0010|\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008z\u0010{R\u001d\u0010\u0081\u0001\u001a\u00020}8BX\u0082\u0084\u0002\u00a2\u0006\r\n\u0004\u0008~\u0010r\u001a\u0005\u0008\u007f\u0010\u0080\u0001R \u0010\u0086\u0001\u001a\u00030\u0082\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000f\n\u0005\u0008\u0083\u0001\u0010r\u001a\u0006\u0008\u0084\u0001\u0010\u0085\u0001\u00a8\u0006\u0089\u0001"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "B3",
        "",
        "r3",
        "()Z",
        "A3",
        "E3",
        "J3",
        "Lkotlin/Function0;",
        "runFunction",
        "H3",
        "(Lkotlin/jvm/functions/Function0;)V",
        "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;",
        "state",
        "o3",
        "(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;)V",
        "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b;",
        "effect",
        "n3",
        "(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b;)V",
        "",
        "gameId",
        "recommended",
        "v3",
        "(JZ)V",
        "isFavorite",
        "u3",
        "(JZZ)V",
        "w3",
        "z3",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "F3",
        "(Lorg/xplatform/aggregator/api/model/Game;)V",
        "G3",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "onCreate",
        "onResume",
        "onPause",
        "u2",
        "Landroid/view/View;",
        "view",
        "onViewCreated",
        "(Landroid/view/View;Landroid/os/Bundle;)V",
        "<set-?>",
        "i0",
        "LeX0/a;",
        "getBundleVirtual",
        "x3",
        "(Z)V",
        "bundleVirtual",
        "LS91/D;",
        "j0",
        "LRc/c;",
        "l3",
        "()LS91/D;",
        "viewBinding",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "k0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "getViewModelFactory",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "Lorg/xplatform/aggregator/impl/core/presentation/c;",
        "l0",
        "Lorg/xplatform/aggregator/impl/core/presentation/c;",
        "f3",
        "()Lorg/xplatform/aggregator/impl/core/presentation/c;",
        "setAggregatorCategoriesDelegate",
        "(Lorg/xplatform/aggregator/impl/core/presentation/c;)V",
        "aggregatorCategoriesDelegate",
        "Lck/a;",
        "m0",
        "Lck/a;",
        "h3",
        "()Lck/a;",
        "setChangeBalanceDialogProvider",
        "(Lck/a;)V",
        "changeBalanceDialogProvider",
        "LTZ0/a;",
        "n0",
        "LTZ0/a;",
        "e3",
        "()LTZ0/a;",
        "setActionDialogManager",
        "(LTZ0/a;)V",
        "actionDialogManager",
        "LzX0/k;",
        "o0",
        "LzX0/k;",
        "k3",
        "()LzX0/k;",
        "setSnackbarManager",
        "(LzX0/k;)V",
        "snackbarManager",
        "Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;",
        "b1",
        "LeX0/j;",
        "j3",
        "()Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;",
        "y3",
        "(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V",
        "screenType",
        "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;",
        "k1",
        "Lkotlin/j;",
        "m3",
        "()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;",
        "viewModel",
        "LZ91/a;",
        "v1",
        "LZ91/a;",
        "lockBalanceSelectorListener",
        "x1",
        "Z",
        "isNeedScrollFavoriteGamesToTop",
        "LY91/i;",
        "y1",
        "i3",
        "()LY91/i;",
        "favoriteAdapter",
        "Lorg/xplatform/aggregator/impl/gifts/f;",
        "F1",
        "g3",
        "()Lorg/xplatform/aggregator/impl/gifts/f;",
        "appBarObserver",
        "H1",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final H1:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic I1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final F1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b1:LeX0/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i0:LeX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public k0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public final k1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public l0:Lorg/xplatform/aggregator/impl/core/presentation/c;

.field public m0:Lck/a;

.field public n0:LTZ0/a;

.field public o0:LzX0/k;

.field public v1:LZ91/a;

.field public x1:Z

.field public final y1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 1
    new-instance v0, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;

    .line 4
    .line 5
    const-string v2, "bundleVirtual"

    .line 6
    .line 7
    const-string v3, "getBundleVirtual()Z"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "viewBinding"

    .line 20
    .line 21
    const-string v5, "getViewBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentAggregatorFavoriteItemBinding;"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "screenType"

    .line 33
    .line 34
    const-string v6, "getScreenType()Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    const/4 v3, 0x3

    .line 44
    new-array v3, v3, [Lkotlin/reflect/m;

    .line 45
    .line 46
    aput-object v0, v3, v4

    .line 47
    .line 48
    const/4 v0, 0x1

    .line 49
    aput-object v2, v3, v0

    .line 50
    .line 51
    const/4 v0, 0x2

    .line 52
    aput-object v1, v3, v0

    .line 53
    .line 54
    sput-object v3, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->I1:[Lkotlin/reflect/m;

    .line 55
    .line 56
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$a;

    .line 57
    .line 58
    const/4 v1, 0x0

    .line 59
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 60
    .line 61
    .line 62
    sput-object v0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->H1:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$a;

    .line 63
    .line 64
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, Lu91/c;->fragment_aggregator_favorite_item:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, LeX0/a;

    .line 7
    .line 8
    const-string v1, "BUNDLE_VIRTUAL"

    .line 9
    .line 10
    const/4 v2, 0x0

    .line 11
    const/4 v3, 0x2

    .line 12
    const/4 v4, 0x0

    .line 13
    invoke-direct {v0, v1, v2, v3, v4}, LeX0/a;-><init>(Ljava/lang/String;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 14
    .line 15
    .line 16
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->i0:LeX0/a;

    .line 17
    .line 18
    sget-object v0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$viewBinding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$viewBinding$2;

    .line 19
    .line 20
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->j0:LRc/c;

    .line 25
    .line 26
    new-instance v0, LeX0/j;

    .line 27
    .line 28
    const-string v1, "favorite_type"

    .line 29
    .line 30
    invoke-direct {v0, v1}, LeX0/j;-><init>(Ljava/lang/String;)V

    .line 31
    .line 32
    .line 33
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->b1:LeX0/j;

    .line 34
    .line 35
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/presentation/n;

    .line 36
    .line 37
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/n;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V

    .line 38
    .line 39
    .line 40
    sget-object v1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 41
    .line 42
    new-instance v2, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$special$$inlined$viewModels$default$1;

    .line 43
    .line 44
    invoke-direct {v2, v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$special$$inlined$viewModels$default$1;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 45
    .line 46
    .line 47
    invoke-static {v1, v2}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    const-class v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 52
    .line 53
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 54
    .line 55
    .line 56
    move-result-object v2

    .line 57
    new-instance v3, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$special$$inlined$viewModels$default$2;

    .line 58
    .line 59
    invoke-direct {v3, v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/j;)V

    .line 60
    .line 61
    .line 62
    new-instance v5, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$special$$inlined$viewModels$default$3;

    .line 63
    .line 64
    invoke-direct {v5, v4, v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 65
    .line 66
    .line 67
    new-instance v4, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$special$$inlined$viewModels$default$4;

    .line 68
    .line 69
    invoke-direct {v4, p0, v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$special$$inlined$viewModels$default$4;-><init>(Landroidx/fragment/app/Fragment;Lkotlin/j;)V

    .line 70
    .line 71
    .line 72
    invoke-static {p0, v2, v3, v5, v4}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->k1:Lkotlin/j;

    .line 77
    .line 78
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/presentation/o;

    .line 79
    .line 80
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/o;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V

    .line 81
    .line 82
    .line 83
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 84
    .line 85
    .line 86
    move-result-object v0

    .line 87
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->y1:Lkotlin/j;

    .line 88
    .line 89
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/presentation/p;

    .line 90
    .line 91
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/p;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V

    .line 92
    .line 93
    .line 94
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->F1:Lkotlin/j;

    .line 99
    .line 100
    return-void
.end method

.method public static synthetic A2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->t3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->a3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;II)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final B3()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/D;->b:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->m3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->P4()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setStyle(I)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iget-object v0, v0, LS91/D;->b:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 23
    .line 24
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    sget v2, LlZ0/g;->space_8:I

    .line 29
    .line 30
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 31
    .line 32
    .line 33
    move-result v1

    .line 34
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    sget v3, LlZ0/g;->space_16:I

    .line 39
    .line 40
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 41
    .line 42
    .line 43
    move-result v2

    .line 44
    invoke-virtual {v0, v1, v2}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->q(II)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    iget-object v0, v0, LS91/D;->b:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 52
    .line 53
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/presentation/s;

    .line 54
    .line 55
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/s;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V

    .line 56
    .line 57
    .line 58
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 59
    .line 60
    .line 61
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    iget-object v0, v0, LS91/D;->b:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 66
    .line 67
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/presentation/t;

    .line 68
    .line 69
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/t;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V

    .line 70
    .line 71
    .line 72
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnActionIconClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 73
    .line 74
    .line 75
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 76
    .line 77
    .line 78
    move-result-object v0

    .line 79
    iget-object v0, v0, LS91/D;->b:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 80
    .line 81
    const/4 v1, 0x0

    .line 82
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setItemAnimator(Landroidx/recyclerview/widget/RecyclerView$m;)V

    .line 83
    .line 84
    .line 85
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 86
    .line 87
    .line 88
    move-result-object v0

    .line 89
    iget-object v0, v0, LS91/D;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 90
    .line 91
    const/4 v2, 0x0

    .line 92
    invoke-virtual {v0, v2}, Landroid/view/ViewGroup;->setMotionEventSplittingEnabled(Z)V

    .line 93
    .line 94
    .line 95
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setItemAnimator(Landroidx/recyclerview/widget/RecyclerView$m;)V

    .line 96
    .line 97
    .line 98
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->i3()LY91/i;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 103
    .line 104
    .line 105
    return-void
.end method

.method public static synthetic C2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)Lorg/xplatform/aggregator/impl/gifts/f;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->Z2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)Lorg/xplatform/aggregator/impl/gifts/f;

    move-result-object p0

    return-object p0
.end method

.method public static final C3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;LN21/k;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, LN21/k;->e()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    const/4 p1, 0x0

    .line 6
    invoke-virtual {p0, v0, v1, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->v3(JZ)V

    .line 7
    .line 8
    .line 9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    return-object p0
.end method

.method public static synthetic D2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->b3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;II)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final D3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;LN21/k;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p1}, LN21/k;->e()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    invoke-virtual {p1}, LN21/k;->c()LN21/m;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p1}, LN21/m;->b()Z

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    const/4 v2, 0x0

    .line 14
    invoke-virtual {p0, v0, v1, p1, v2}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->u3(JZZ)V

    .line 15
    .line 16
    .line 17
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 18
    .line 19
    return-object p0
.end method

.method public static synthetic E2(Landroidx/recyclerview/widget/RecyclerView;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->p3(Landroidx/recyclerview/widget/RecyclerView;)V

    return-void
.end method

.method private final E3()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->k3()LzX0/k;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Ly01/g;

    .line 6
    .line 7
    sget-object v2, Ly01/i$c;->a:Ly01/i$c;

    .line 8
    .line 9
    sget v3, Lpb/k;->get_balance_list_error:I

    .line 10
    .line 11
    invoke-virtual {p0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    const/16 v8, 0x3c

    .line 16
    .line 17
    const/4 v9, 0x0

    .line 18
    const/4 v4, 0x0

    .line 19
    const/4 v5, 0x0

    .line 20
    const/4 v6, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 23
    .line 24
    .line 25
    const/16 v10, 0x1fc

    .line 26
    .line 27
    const/4 v11, 0x0

    .line 28
    const/4 v3, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v8, 0x0

    .line 32
    move-object v2, p0

    .line 33
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public static synthetic F2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)Landroidx/lifecycle/h0;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->K3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)Landroidx/lifecycle/h0;

    move-result-object p0

    return-object p0
.end method

.method private final F3(Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getArguments()Landroid/os/Bundle;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const-string v1, "OPEN_GAME_ITEM"

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {v0, v1, p1}, Landroid/os/Bundle;->putSerializable(Ljava/lang/String;Ljava/io/Serializable;)V

    .line 10
    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    invoke-static {v1, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    const/4 v0, 0x1

    .line 18
    new-array v0, v0, [Lkotlin/Pair;

    .line 19
    .line 20
    const/4 v1, 0x0

    .line 21
    aput-object p1, v0, v1

    .line 22
    .line 23
    invoke-static {v0}, Landroidx/core/os/d;->b([Lkotlin/Pair;)Landroid/os/Bundle;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-virtual {p0, p1}, Landroidx/fragment/app/Fragment;->setArguments(Landroid/os/Bundle;)V

    .line 28
    .line 29
    .line 30
    :goto_0
    sget-object p1, LKW0/b;->a:LKW0/b;

    .line 31
    .line 32
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->e3()LTZ0/a;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    invoke-virtual {p1, p0, v0}, LKW0/b;->c(Landroidx/fragment/app/Fragment;LTZ0/a;)V

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method public static synthetic G2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;LN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->D3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;LN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final G3()V
    .locals 13

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->h3()Lck/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 6
    .line 7
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    sget v3, Lpb/k;->gift_balance_dialog_description:I

    .line 12
    .line 13
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object v4

    .line 17
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    const/16 v11, 0x2e6

    .line 22
    .line 23
    const/4 v12, 0x0

    .line 24
    const/4 v2, 0x0

    .line 25
    const/4 v3, 0x0

    .line 26
    const/4 v6, 0x0

    .line 27
    const/4 v7, 0x0

    .line 28
    const/4 v8, 0x0

    .line 29
    const-string v9, "REQUEST_CHANGE_BALANCE_KEY"

    .line 30
    .line 31
    const/4 v10, 0x0

    .line 32
    invoke-static/range {v0 .. v12}, Lck/a$a;->a(Lck/a;Lorg/xbet/balance/model/BalanceScreenType;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Landroidx/fragment/app/FragmentManager;ZZZLjava/lang/String;ZILjava/lang/Object;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public static synthetic H2(Landroidx/recyclerview/widget/RecyclerView;Landroid/widget/ProgressBar;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->q3(Landroidx/recyclerview/widget/RecyclerView;Landroid/widget/ProgressBar;)V

    return-void
.end method

.method private final H3(Lkotlin/jvm/functions/Function0;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    sget-object v0, LKW0/b;->a:LKW0/b;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->e3()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lorg/xplatform/aggregator/impl/favorite/presentation/m;

    .line 8
    .line 9
    invoke-direct {v2, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/m;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0, p0, v2, v1}, LKW0/b;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function0;LTZ0/a;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public static synthetic I2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;LN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->C3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;LN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final I3(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic J2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;III)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->c3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;III)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final J3()V
    .locals 2

    .line 1
    sget-object v0, LKW0/b;->a:LKW0/b;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->e3()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v0, p0, v1}, LKW0/b;->f(Landroidx/fragment/app/Fragment;LTZ0/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static synthetic K2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)LY91/i;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->d3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)LY91/i;

    move-result-object p0

    return-object p0
.end method

.method public static final K3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)Landroidx/lifecycle/h0;
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic L2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->m3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic M2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->n3(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic N2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->o3(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic O2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;JZZ)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->u3(JZZ)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic P2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;JZ)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->v3(JZ)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic Q2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->w3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic R2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->x3(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic S2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;LZ91/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->v1:LZ91/a;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic T2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->y3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic U2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->z3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic V2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->E3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic W2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->F3(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic X2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->H3(Lkotlin/jvm/functions/Function0;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic Y2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->J3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final Z2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)Lorg/xplatform/aggregator/impl/gifts/f;
    .locals 9

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/f;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->i3()LY91/i;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$appBarObserver$2$1;

    .line 8
    .line 9
    invoke-direct {v2, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$appBarObserver$2$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    new-instance v4, Lorg/xplatform/aggregator/impl/favorite/presentation/i;

    .line 13
    .line 14
    invoke-direct {v4, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/i;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V

    .line 15
    .line 16
    .line 17
    new-instance v5, Lorg/xplatform/aggregator/impl/favorite/presentation/j;

    .line 18
    .line 19
    invoke-direct {v5, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/j;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V

    .line 20
    .line 21
    .line 22
    new-instance v6, Lorg/xplatform/aggregator/impl/favorite/presentation/k;

    .line 23
    .line 24
    invoke-direct {v6, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/k;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V

    .line 25
    .line 26
    .line 27
    const/4 v7, 0x4

    .line 28
    const/4 v8, 0x0

    .line 29
    const/4 v3, 0x0

    .line 30
    invoke-direct/range {v0 .. v8}, Lorg/xplatform/aggregator/impl/gifts/f;-><init>(Landroidx/recyclerview/widget/RecyclerView$Adapter;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;LOc/n;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 31
    .line 32
    .line 33
    return-object v0
.end method

.method public static final a3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->w3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final b3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->w3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final c3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;III)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->w3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final d3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)LY91/i;
    .locals 3

    .line 1
    new-instance v0, LY91/i;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$favoriteAdapter$2$1;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$favoriteAdapter$2$1;-><init>(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    new-instance v2, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$favoriteAdapter$2$2;

    .line 9
    .line 10
    invoke-direct {v2, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$favoriteAdapter$2$2;-><init>(Ljava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    invoke-direct {v0, v1, v2}, LY91/i;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;)V

    .line 14
    .line 15
    .line 16
    return-object v0
.end method

.method private final g3()Lorg/xplatform/aggregator/impl/gifts/f;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->F1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/f;

    .line 8
    .line 9
    return-object v0
.end method

.method private final m3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->k1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final p3(Landroidx/recyclerview/widget/RecyclerView;)V
    .locals 1

    .line 1
    const/16 v0, 0x8

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Landroid/view/View;->setVisibility(I)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public static final q3(Landroidx/recyclerview/widget/RecyclerView;Landroid/widget/ProgressBar;)V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-virtual {p0, v0}, Landroid/view/View;->setVisibility(I)V

    .line 3
    .line 4
    .line 5
    const/16 p0, 0x8

    .line 6
    .line 7
    invoke-virtual {p1, p0}, Landroid/view/View;->setVisibility(I)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final s3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->m3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const-class v1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;

    .line 6
    .line 7
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    const/4 v2, 0x0

    .line 12
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->j3()Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    invoke-virtual {v0, v1, p1, v2, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->b5(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;ZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V

    .line 17
    .line 18
    .line 19
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 20
    .line 21
    return-object p0
.end method

.method public static final t3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->G3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final w3()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/D;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$c;

    .line 8
    .line 9
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$c;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0, v1}, Landroid/view/View;->addOnLayoutChangeListener(Landroid/view/View$OnLayoutChangeListener;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method private final x3(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->i0:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/a;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Z)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static synthetic y2(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->s3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->I3(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final A3()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->j3()Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget-object v2, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$b;->a:[I

    .line 8
    .line 9
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    aget v1, v2, v1

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    const/4 v3, 0x0

    .line 17
    if-eq v1, v2, :cond_1

    .line 18
    .line 19
    const/4 v2, 0x2

    .line 20
    if-ne v1, v2, :cond_0

    .line 21
    .line 22
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->m3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->R4()Lkotlinx/coroutines/flow/V;

    .line 27
    .line 28
    .line 29
    move-result-object v5

    .line 30
    new-instance v8, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$2;

    .line 31
    .line 32
    invoke-direct {v8, v0, v3}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$2;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lkotlin/coroutines/e;)V

    .line 33
    .line 34
    .line 35
    sget-object v7, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 36
    .line 37
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 38
    .line 39
    .line 40
    move-result-object v6

    .line 41
    invoke-static {v6}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    new-instance v4, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$$inlined$observeWithLifecycle$default$2;

    .line 46
    .line 47
    const/4 v9, 0x0

    .line 48
    invoke-direct/range {v4 .. v9}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 49
    .line 50
    .line 51
    const/4 v13, 0x3

    .line 52
    const/4 v14, 0x0

    .line 53
    const/4 v10, 0x0

    .line 54
    const/4 v11, 0x0

    .line 55
    move-object v9, v1

    .line 56
    move-object v12, v4

    .line 57
    invoke-static/range {v9 .. v14}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 58
    .line 59
    .line 60
    goto :goto_0

    .line 61
    :cond_0
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    .line 62
    .line 63
    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 64
    .line 65
    .line 66
    throw v1

    .line 67
    :cond_1
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->m3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->M4()Lkotlinx/coroutines/flow/V;

    .line 72
    .line 73
    .line 74
    move-result-object v5

    .line 75
    new-instance v8, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$1;

    .line 76
    .line 77
    invoke-direct {v8, v0, v3}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lkotlin/coroutines/e;)V

    .line 78
    .line 79
    .line 80
    sget-object v7, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 81
    .line 82
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 83
    .line 84
    .line 85
    move-result-object v6

    .line 86
    invoke-static {v6}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    new-instance v4, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$$inlined$observeWithLifecycle$default$1;

    .line 91
    .line 92
    const/4 v9, 0x0

    .line 93
    invoke-direct/range {v4 .. v9}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 94
    .line 95
    .line 96
    const/4 v13, 0x3

    .line 97
    const/4 v14, 0x0

    .line 98
    const/4 v10, 0x0

    .line 99
    const/4 v11, 0x0

    .line 100
    move-object v9, v1

    .line 101
    move-object v12, v4

    .line 102
    invoke-static/range {v9 .. v14}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 103
    .line 104
    .line 105
    :goto_0
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->m3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 106
    .line 107
    .line 108
    move-result-object v1

    .line 109
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->L4()Lkotlinx/coroutines/flow/U;

    .line 110
    .line 111
    .line 112
    move-result-object v5

    .line 113
    sget-object v7, Landroidx/lifecycle/Lifecycle$State;->RESUMED:Landroidx/lifecycle/Lifecycle$State;

    .line 114
    .line 115
    new-instance v8, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$3;

    .line 116
    .line 117
    invoke-direct {v8, v0, v3}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$3;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lkotlin/coroutines/e;)V

    .line 118
    .line 119
    .line 120
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 121
    .line 122
    .line 123
    move-result-object v6

    .line 124
    invoke-static {v6}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 125
    .line 126
    .line 127
    move-result-object v1

    .line 128
    new-instance v4, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$$inlined$observeWithLifecycle$1;

    .line 129
    .line 130
    const/4 v9, 0x0

    .line 131
    invoke-direct/range {v4 .. v9}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$$inlined$observeWithLifecycle$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 132
    .line 133
    .line 134
    const/4 v13, 0x3

    .line 135
    const/4 v14, 0x0

    .line 136
    const/4 v10, 0x0

    .line 137
    const/4 v11, 0x0

    .line 138
    move-object v9, v1

    .line 139
    move-object v12, v4

    .line 140
    invoke-static/range {v9 .. v14}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 141
    .line 142
    .line 143
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->m3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 144
    .line 145
    .line 146
    move-result-object v1

    .line 147
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->N4()Lkotlinx/coroutines/flow/Z;

    .line 148
    .line 149
    .line 150
    move-result-object v1

    .line 151
    new-instance v10, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$4;

    .line 152
    .line 153
    invoke-direct {v10, v0, v3}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$4;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lkotlin/coroutines/e;)V

    .line 154
    .line 155
    .line 156
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 157
    .line 158
    .line 159
    move-result-object v8

    .line 160
    invoke-static {v8}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 161
    .line 162
    .line 163
    move-result-object v2

    .line 164
    new-instance v6, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$$inlined$observeWithLifecycle$2;

    .line 165
    .line 166
    move-object v9, v7

    .line 167
    move-object v7, v1

    .line 168
    invoke-direct/range {v6 .. v11}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$setupBindings$$inlined$observeWithLifecycle$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 169
    .line 170
    .line 171
    const/4 v15, 0x3

    .line 172
    const/16 v16, 0x0

    .line 173
    .line 174
    const/4 v12, 0x0

    .line 175
    const/4 v13, 0x0

    .line 176
    move-object v11, v2

    .line 177
    move-object v14, v6

    .line 178
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 179
    .line 180
    .line 181
    return-void
.end method

.method public final e3()LTZ0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->n0:LTZ0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final f3()Lorg/xplatform/aggregator/impl/core/presentation/c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l0:Lorg/xplatform/aggregator/impl/core/presentation/c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final h3()Lck/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->m0:Lck/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final i3()LY91/i;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->y1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LY91/i;

    .line 8
    .line 9
    return-object v0
.end method

.method public final j3()Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->b1:LeX0/j;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/j;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/io/Serializable;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 13
    .line 14
    return-object v0
.end method

.method public final k3()LzX0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->o0:LzX0/k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final l3()LS91/D;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->j0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/D;

    .line 13
    .line 14
    return-object v0
.end method

.method public final n3(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b;)V
    .locals 17

    .line 1
    move-object/from16 v2, p0

    .line 2
    .line 3
    move-object/from16 v0, p1

    .line 4
    .line 5
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b$c;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->k3()LzX0/k;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    new-instance v1, Ly01/g;

    .line 14
    .line 15
    sget-object v4, Ly01/i$c;->a:Ly01/i$c;

    .line 16
    .line 17
    sget v3, Lpb/k;->get_balance_list_error:I

    .line 18
    .line 19
    invoke-virtual {v2, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v5

    .line 23
    const/16 v10, 0x3c

    .line 24
    .line 25
    const/4 v11, 0x0

    .line 26
    const/4 v6, 0x0

    .line 27
    const/4 v7, 0x0

    .line 28
    const/4 v8, 0x0

    .line 29
    const/4 v9, 0x0

    .line 30
    move-object v3, v1

    .line 31
    invoke-direct/range {v3 .. v11}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 32
    .line 33
    .line 34
    const/16 v10, 0x1fc

    .line 35
    .line 36
    const/4 v3, 0x0

    .line 37
    const/4 v4, 0x0

    .line 38
    const/4 v5, 0x0

    .line 39
    const/4 v6, 0x0

    .line 40
    const/4 v8, 0x0

    .line 41
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 42
    .line 43
    .line 44
    return-void

    .line 45
    :cond_0
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b$a;

    .line 46
    .line 47
    if-eqz v1, :cond_1

    .line 48
    .line 49
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->k3()LzX0/k;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    new-instance v1, Ly01/g;

    .line 54
    .line 55
    sget-object v4, Ly01/i$c;->a:Ly01/i$c;

    .line 56
    .line 57
    sget v3, Lpb/k;->access_denied_with_bonus_currency_message:I

    .line 58
    .line 59
    invoke-virtual {v2, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 60
    .line 61
    .line 62
    move-result-object v5

    .line 63
    const/16 v10, 0x3c

    .line 64
    .line 65
    const/4 v11, 0x0

    .line 66
    const/4 v6, 0x0

    .line 67
    const/4 v7, 0x0

    .line 68
    const/4 v8, 0x0

    .line 69
    const/4 v9, 0x0

    .line 70
    move-object v3, v1

    .line 71
    invoke-direct/range {v3 .. v11}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 72
    .line 73
    .line 74
    const/16 v10, 0x1fc

    .line 75
    .line 76
    const/4 v3, 0x0

    .line 77
    const/4 v4, 0x0

    .line 78
    const/4 v5, 0x0

    .line 79
    const/4 v6, 0x0

    .line 80
    const/4 v8, 0x0

    .line 81
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 82
    .line 83
    .line 84
    return-void

    .line 85
    :cond_1
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b$b;

    .line 86
    .line 87
    if-eqz v1, :cond_6

    .line 88
    .line 89
    check-cast v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b$b;

    .line 90
    .line 91
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$b$b;->a()Ld81/b;

    .line 92
    .line 93
    .line 94
    move-result-object v0

    .line 95
    invoke-virtual {v2}, Landroidx/fragment/app/Fragment;->getContext()Landroid/content/Context;

    .line 96
    .line 97
    .line 98
    move-result-object v1

    .line 99
    if-eqz v1, :cond_5

    .line 100
    .line 101
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->f3()Lorg/xplatform/aggregator/impl/core/presentation/c;

    .line 102
    .line 103
    .line 104
    move-result-object v3

    .line 105
    invoke-virtual {v0}, Ld81/b;->e()J

    .line 106
    .line 107
    .line 108
    move-result-wide v4

    .line 109
    sget-object v6, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->RECOMMENDED:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 110
    .line 111
    invoke-virtual {v6}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 112
    .line 113
    .line 114
    move-result-wide v6

    .line 115
    cmp-long v8, v4, v6

    .line 116
    .line 117
    if-nez v8, :cond_2

    .line 118
    .line 119
    invoke-virtual {v0}, Ld81/b;->e()J

    .line 120
    .line 121
    .line 122
    move-result-wide v4

    .line 123
    goto :goto_0

    .line 124
    :cond_2
    invoke-virtual {v0}, Ld81/b;->g()J

    .line 125
    .line 126
    .line 127
    move-result-wide v4

    .line 128
    :goto_0
    invoke-virtual {v0}, Ld81/b;->f()J

    .line 129
    .line 130
    .line 131
    move-result-wide v6

    .line 132
    invoke-virtual {v0}, Ld81/b;->e()J

    .line 133
    .line 134
    .line 135
    move-result-wide v8

    .line 136
    invoke-virtual {v0}, Ld81/b;->f()J

    .line 137
    .line 138
    .line 139
    move-result-wide v10

    .line 140
    invoke-virtual {v0}, Ld81/b;->h()Ljava/lang/String;

    .line 141
    .line 142
    .line 143
    move-result-object v12

    .line 144
    invoke-static {v10, v11, v1, v12}, Lorg/xplatform/aggregator/impl/core/presentation/i;->b(JLandroid/content/Context;Ljava/lang/String;)Ljava/lang/String;

    .line 145
    .line 146
    .line 147
    move-result-object v10

    .line 148
    sget v1, Lpb/k;->casino_category_folder_and_section_description:I

    .line 149
    .line 150
    invoke-virtual {v2, v1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 151
    .line 152
    .line 153
    move-result-object v11

    .line 154
    invoke-virtual {v0}, Ld81/b;->e()J

    .line 155
    .line 156
    .line 157
    move-result-wide v12

    .line 158
    sget-object v1, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 159
    .line 160
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 161
    .line 162
    .line 163
    move-result-wide v14

    .line 164
    cmp-long v1, v12, v14

    .line 165
    .line 166
    if-nez v1, :cond_3

    .line 167
    .line 168
    const-wide/16 v0, 0x11

    .line 169
    .line 170
    goto :goto_1

    .line 171
    :cond_3
    sget-object v1, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->UNKNOWN:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 172
    .line 173
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 174
    .line 175
    .line 176
    move-result-wide v14

    .line 177
    cmp-long v1, v12, v14

    .line 178
    .line 179
    if-nez v1, :cond_4

    .line 180
    .line 181
    const-wide v0, 0x7fffffffffffffffL

    .line 182
    .line 183
    .line 184
    .line 185
    .line 186
    goto :goto_1

    .line 187
    :cond_4
    invoke-virtual {v0}, Ld81/b;->e()J

    .line 188
    .line 189
    .line 190
    move-result-wide v0

    .line 191
    :goto_1
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 192
    .line 193
    .line 194
    move-result-object v0

    .line 195
    invoke-static {v0}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 196
    .line 197
    .line 198
    move-result-object v13

    .line 199
    const/16 v15, 0x80

    .line 200
    .line 201
    const/16 v16, 0x0

    .line 202
    .line 203
    const/4 v12, 0x0

    .line 204
    const/4 v14, 0x0

    .line 205
    invoke-static/range {v3 .. v16}, Lorg/xplatform/aggregator/impl/core/presentation/c;->d(Lorg/xplatform/aggregator/impl/core/presentation/c;JJJLjava/lang/String;Ljava/lang/String;ZLjava/util/List;Ljava/lang/String;ILjava/lang/Object;)V

    .line 206
    .line 207
    .line 208
    :cond_5
    return-void

    .line 209
    :cond_6
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 210
    .line 211
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 212
    .line 213
    .line 214
    throw v0
.end method

.method public final o3(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c;)V
    .locals 6

    .line 1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$b;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/16 v2, 0x8

    .line 5
    .line 6
    if-eqz v0, :cond_1

    .line 7
    .line 8
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->v1:LZ91/a;

    .line 9
    .line 10
    if-eqz p1, :cond_0

    .line 11
    .line 12
    invoke-interface {p1, v1}, LZ91/a;->G(Z)V

    .line 13
    .line 14
    .line 15
    :cond_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    iget-object p1, p1, LS91/D;->d:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 20
    .line 21
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    iget-object p1, p1, LS91/D;->e:LPW0/Y;

    .line 29
    .line 30
    iget-object p1, p1, LPW0/Y;->b:Landroid/widget/ProgressBar;

    .line 31
    .line 32
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 33
    .line 34
    .line 35
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    iget-object p1, p1, LS91/D;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 40
    .line 41
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 42
    .line 43
    .line 44
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iget-object p1, p1, LS91/D;->b:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 49
    .line 50
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 51
    .line 52
    .line 53
    return-void

    .line 54
    :cond_1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$d;

    .line 55
    .line 56
    const/4 v3, 0x0

    .line 57
    if-eqz v0, :cond_3

    .line 58
    .line 59
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->v1:LZ91/a;

    .line 60
    .line 61
    if-eqz v0, :cond_2

    .line 62
    .line 63
    invoke-interface {v0, v1}, LZ91/a;->G(Z)V

    .line 64
    .line 65
    .line 66
    :cond_2
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    iget-object v0, v0, LS91/D;->d:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 71
    .line 72
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 73
    .line 74
    .line 75
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 76
    .line 77
    .line 78
    move-result-object v0

    .line 79
    iget-object v0, v0, LS91/D;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 80
    .line 81
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->i3()LY91/i;

    .line 82
    .line 83
    .line 84
    move-result-object v4

    .line 85
    new-instance v5, Lorg/xplatform/aggregator/impl/favorite/presentation/q;

    .line 86
    .line 87
    invoke-direct {v5, v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/q;-><init>(Landroidx/recyclerview/widget/RecyclerView;)V

    .line 88
    .line 89
    .line 90
    invoke-virtual {v4, v3, v5}, LA4/e;->n(Ljava/util/List;Ljava/lang/Runnable;)V

    .line 91
    .line 92
    .line 93
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    iget-object v0, v0, LS91/D;->b:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 98
    .line 99
    check-cast p1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$d;

    .line 100
    .line 101
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$d;->a()Ljava/util/List;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setItems(Ljava/util/List;)V

    .line 106
    .line 107
    .line 108
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 109
    .line 110
    .line 111
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 112
    .line 113
    .line 114
    move-result-object p1

    .line 115
    iget-object p1, p1, LS91/D;->e:LPW0/Y;

    .line 116
    .line 117
    iget-object p1, p1, LPW0/Y;->b:Landroid/widget/ProgressBar;

    .line 118
    .line 119
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 120
    .line 121
    .line 122
    return-void

    .line 123
    :cond_3
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$e;

    .line 124
    .line 125
    if-eqz v0, :cond_5

    .line 126
    .line 127
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->v1:LZ91/a;

    .line 128
    .line 129
    if-eqz v0, :cond_4

    .line 130
    .line 131
    invoke-interface {v0, v1}, LZ91/a;->G(Z)V

    .line 132
    .line 133
    .line 134
    :cond_4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 135
    .line 136
    .line 137
    move-result-object v0

    .line 138
    iget-object v0, v0, LS91/D;->d:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 139
    .line 140
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 141
    .line 142
    .line 143
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 144
    .line 145
    .line 146
    move-result-object v0

    .line 147
    iget-object v0, v0, LS91/D;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 148
    .line 149
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 150
    .line 151
    .line 152
    move-result-object v1

    .line 153
    iget-object v1, v1, LS91/D;->e:LPW0/Y;

    .line 154
    .line 155
    iget-object v1, v1, LPW0/Y;->b:Landroid/widget/ProgressBar;

    .line 156
    .line 157
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 158
    .line 159
    .line 160
    move-result-object v4

    .line 161
    iget-object v4, v4, LS91/D;->b:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 162
    .line 163
    invoke-virtual {v4, v3}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setItems(Ljava/util/List;)V

    .line 164
    .line 165
    .line 166
    invoke-virtual {v4, v2}, Landroid/view/View;->setVisibility(I)V

    .line 167
    .line 168
    .line 169
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->i3()LY91/i;

    .line 170
    .line 171
    .line 172
    move-result-object v2

    .line 173
    check-cast p1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$e;

    .line 174
    .line 175
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$e;->a()Ljava/util/List;

    .line 176
    .line 177
    .line 178
    move-result-object p1

    .line 179
    new-instance v3, Lorg/xplatform/aggregator/impl/favorite/presentation/r;

    .line 180
    .line 181
    invoke-direct {v3, v0, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/r;-><init>(Landroidx/recyclerview/widget/RecyclerView;Landroid/widget/ProgressBar;)V

    .line 182
    .line 183
    .line 184
    invoke-virtual {v2, p1, v3}, LA4/e;->n(Ljava/util/List;Ljava/lang/Runnable;)V

    .line 185
    .line 186
    .line 187
    return-void

    .line 188
    :cond_5
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$a;

    .line 189
    .line 190
    if-eqz v0, :cond_7

    .line 191
    .line 192
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->v1:LZ91/a;

    .line 193
    .line 194
    if-eqz p1, :cond_6

    .line 195
    .line 196
    invoke-interface {p1, v1}, LZ91/a;->G(Z)V

    .line 197
    .line 198
    .line 199
    :cond_6
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 200
    .line 201
    .line 202
    move-result-object p1

    .line 203
    iget-object p1, p1, LS91/D;->e:LPW0/Y;

    .line 204
    .line 205
    iget-object p1, p1, LPW0/Y;->b:Landroid/widget/ProgressBar;

    .line 206
    .line 207
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 208
    .line 209
    .line 210
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 211
    .line 212
    .line 213
    move-result-object p1

    .line 214
    iget-object p1, p1, LS91/D;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 215
    .line 216
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 217
    .line 218
    .line 219
    return-void

    .line 220
    :cond_7
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$c;

    .line 221
    .line 222
    if-eqz v0, :cond_9

    .line 223
    .line 224
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 225
    .line 226
    .line 227
    move-result-object v0

    .line 228
    iget-object v0, v0, LS91/D;->d:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 229
    .line 230
    check-cast p1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$c;

    .line 231
    .line 232
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel$c$c;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 233
    .line 234
    .line 235
    move-result-object p1

    .line 236
    sget v3, Lpb/k;->update_again_after:I

    .line 237
    .line 238
    const-wide/16 v4, 0x2710

    .line 239
    .line 240
    invoke-virtual {v0, p1, v3, v4, v5}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->g(Lorg/xbet/uikit/components/lottie_empty/n;IJ)V

    .line 241
    .line 242
    .line 243
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 244
    .line 245
    .line 246
    move-result-object p1

    .line 247
    iget-object p1, p1, LS91/D;->d:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 248
    .line 249
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 250
    .line 251
    .line 252
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->v1:LZ91/a;

    .line 253
    .line 254
    if-eqz p1, :cond_8

    .line 255
    .line 256
    const/4 v0, 0x1

    .line 257
    invoke-interface {p1, v0}, LZ91/a;->G(Z)V

    .line 258
    .line 259
    .line 260
    :cond_8
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 261
    .line 262
    .line 263
    move-result-object p1

    .line 264
    iget-object p1, p1, LS91/D;->e:LPW0/Y;

    .line 265
    .line 266
    iget-object p1, p1, LPW0/Y;->b:Landroid/widget/ProgressBar;

    .line 267
    .line 268
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 269
    .line 270
    .line 271
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 272
    .line 273
    .line 274
    move-result-object p1

    .line 275
    iget-object p1, p1, LS91/D;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 276
    .line 277
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 278
    .line 279
    .line 280
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 281
    .line 282
    .line 283
    move-result-object p1

    .line 284
    iget-object p1, p1, LS91/D;->b:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 285
    .line 286
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 287
    .line 288
    .line 289
    return-void

    .line 290
    :cond_9
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 291
    .line 292
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 293
    .line 294
    .line 295
    throw p1
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .locals 1

    .line 1
    invoke-super {p0, p1}, LXW0/a;->onCreate(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    new-instance p1, Lorg/xplatform/aggregator/impl/favorite/presentation/h;

    .line 5
    .line 6
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/h;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V

    .line 7
    .line 8
    .line 9
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/i;->e(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)V

    .line 10
    .line 11
    .line 12
    new-instance p1, Lorg/xplatform/aggregator/impl/favorite/presentation/l;

    .line 13
    .line 14
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/l;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V

    .line 15
    .line 16
    .line 17
    const-string v0, "REQUEST_BONUS_BALANCE_WARNING_DIALOG_KEY"

    .line 18
    .line 19
    invoke-static {p0, v0, p1}, LVZ0/c;->f(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public onPause()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->r3()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    const/4 v0, 0x1

    .line 8
    iput-boolean v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->x1:Z

    .line 9
    .line 10
    :cond_0
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->g3()Lorg/xplatform/aggregator/impl/gifts/f;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/f;->l()V

    .line 15
    .line 16
    .line 17
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onPause()V

    .line 18
    .line 19
    .line 20
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->m3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->d5()V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public onResume()V
    .locals 2

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->x1:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iget-object v0, v0, LS91/D;->b:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 10
    .line 11
    const/4 v1, 0x0

    .line 12
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->smoothScrollToPosition(I)V

    .line 13
    .line 14
    .line 15
    iput-boolean v1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->x1:Z

    .line 16
    .line 17
    :cond_0
    invoke-super {p0}, LXW0/a;->onResume()V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->z3()V

    .line 21
    .line 22
    .line 23
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->g3()Lorg/xplatform/aggregator/impl/gifts/f;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/f;->k()V

    .line 28
    .line 29
    .line 30
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->m3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->e5()V

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method public onViewCreated(Landroid/view/View;Landroid/os/Bundle;)V
    .locals 0
    .param p1    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1, p2}, LXW0/a;->onViewCreated(Landroid/view/View;Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->B3()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->A3()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final r3()Z
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/D;->b:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-nez v1, :cond_0

    .line 12
    .line 13
    const/4 v1, 0x1

    .line 14
    invoke-virtual {v0, v1}, Landroid/view/View;->canScrollVertically(I)Z

    .line 15
    .line 16
    .line 17
    move-result v2

    .line 18
    if-eqz v2, :cond_0

    .line 19
    .line 20
    const/4 v2, -0x1

    .line 21
    invoke-virtual {v0, v2}, Landroid/view/View;->canScrollVertically(I)Z

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    if-nez v0, :cond_0

    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->j3()Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    sget-object v2, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;->FAVORITES:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 32
    .line 33
    if-ne v0, v2, :cond_0

    .line 34
    .line 35
    return v1

    .line 36
    :cond_0
    const/4 v0, 0x0

    .line 37
    return v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 12

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object p1, p1, LS91/D;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 9
    .line 10
    new-instance v0, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;

    .line 11
    .line 12
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    sget v2, Lpb/f;->space_8:I

    .line 17
    .line 18
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 19
    .line 20
    .line 21
    move-result v1

    .line 22
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 23
    .line 24
    .line 25
    move-result-object v2

    .line 26
    sget v3, Lpb/f;->space_8:I

    .line 27
    .line 28
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 29
    .line 30
    .line 31
    move-result v3

    .line 32
    const/16 v10, 0x1da

    .line 33
    .line 34
    const/4 v11, 0x0

    .line 35
    const/4 v2, 0x0

    .line 36
    const/4 v4, 0x0

    .line 37
    const/4 v5, 0x0

    .line 38
    const/4 v6, 0x1

    .line 39
    const/4 v7, 0x0

    .line 40
    const/4 v8, 0x0

    .line 41
    const/4 v9, 0x0

    .line 42
    invoke-direct/range {v0 .. v11}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;-><init>(IIIIIILkotlin/jvm/functions/Function1;Lorg/xbet/ui_common/viewcomponents/recycler/decorators/SpacingItemDecorationBias;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 43
    .line 44
    .line 45
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 46
    .line 47
    .line 48
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    instance-of v1, v0, LQW0/b;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    check-cast v0, LQW0/b;

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    move-object v0, v2

    .line 21
    :goto_0
    const-class v1, LV91/b;

    .line 22
    .line 23
    if-eqz v0, :cond_3

    .line 24
    .line 25
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, LBc/a;

    .line 34
    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, LQW0/a;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object v0, v2

    .line 45
    :goto_1
    instance-of v3, v0, LV91/b;

    .line 46
    .line 47
    if-nez v3, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    move-object v2, v0

    .line 51
    :goto_2
    check-cast v2, LV91/b;

    .line 52
    .line 53
    if-eqz v2, :cond_3

    .line 54
    .line 55
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->j3()Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    invoke-virtual {v2, v0}, LV91/b;->a(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)LV91/a;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-interface {v0, p0}, LV91/a;->a(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;)V

    .line 64
    .line 65
    .line 66
    return-void

    .line 67
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 68
    .line 69
    new-instance v2, Ljava/lang/StringBuilder;

    .line 70
    .line 71
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 72
    .line 73
    .line 74
    const-string v3, "Cannot create dependency "

    .line 75
    .line 76
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 77
    .line 78
    .line 79
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 80
    .line 81
    .line 82
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 83
    .line 84
    .line 85
    move-result-object v1

    .line 86
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 91
    .line 92
    .line 93
    throw v0
.end method

.method public final u3(JZZ)V
    .locals 6

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->m3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->j3()Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 6
    .line 7
    .line 8
    move-result-object v5

    .line 9
    move-wide v1, p1

    .line 10
    move v3, p3

    .line 11
    move v4, p4

    .line 12
    invoke-virtual/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->Z4(JZZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final v3(JZ)V
    .locals 6

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->m3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const-class v1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;

    .line 6
    .line 7
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->j3()Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 12
    .line 13
    .line 14
    move-result-object v5

    .line 15
    move-wide v2, p1

    .line 16
    move v4, p3

    .line 17
    invoke-virtual/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->a5(Ljava/lang/String;JZLorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final y3(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->b1:LeX0/j;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/j;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/io/Serializable;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final z3()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/D;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    invoke-static {v0}, Lorg/xbet/ui_common/utils/ViewExtensionsKt;->m(Landroid/view/View;)Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    const/4 v1, 0x1

    .line 12
    if-nez v0, :cond_1

    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l3()LS91/D;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    iget-object v0, v0, LS91/D;->b:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 19
    .line 20
    invoke-static {v0}, Lorg/xbet/ui_common/utils/ViewExtensionsKt;->m(Landroid/view/View;)Z

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    if-eqz v0, :cond_0

    .line 25
    .line 26
    goto :goto_0

    .line 27
    :cond_0
    const/4 v0, 0x0

    .line 28
    goto :goto_1

    .line 29
    :cond_1
    :goto_0
    const/4 v0, 0x1

    .line 30
    :goto_1
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->v1:LZ91/a;

    .line 31
    .line 32
    if-eqz v2, :cond_2

    .line 33
    .line 34
    xor-int/2addr v0, v1

    .line 35
    invoke-interface {v2, v0}, LZ91/a;->G(Z)V

    .line 36
    .line 37
    .line 38
    :cond_2
    return-void
.end method
