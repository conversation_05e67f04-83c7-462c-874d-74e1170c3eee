.class final Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.new_games.presentation.NewGamesFolderViewModel$onFavoriteClick$2"
    f = "NewGamesFolderViewModel.kt"
    l = {
        0x104,
        0x10b
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->m5(LN21/k;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $game:LN21/k;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;LN21/k;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;",
            "LN21/k;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->$game:LN21/k;

    .line 4
    .line 5
    const/4 p1, 0x2

    .line 6
    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->$game:LN21/k;

    invoke-direct {p1, v0, v1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;LN21/k;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    :goto_0
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    move-object v9, p0

    .line 28
    goto :goto_2

    .line 29
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 30
    .line 31
    .line 32
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 33
    .line 34
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->E4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Z

    .line 35
    .line 36
    .line 37
    move-result p1

    .line 38
    if-nez p1, :cond_3

    .line 39
    .line 40
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 41
    .line 42
    return-object p1

    .line 43
    :cond_3
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 44
    .line 45
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->D4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    invoke-interface {p1}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    invoke-virtual {p1}, Lek0/o;->o()Lek0/a;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    invoke-virtual {p1}, Lek0/a;->c()Z

    .line 58
    .line 59
    .line 60
    move-result v7

    .line 61
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->$game:LN21/k;

    .line 62
    .line 63
    invoke-virtual {p1}, LN21/k;->c()LN21/m;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    invoke-virtual {p1}, LN21/m;->b()Z

    .line 68
    .line 69
    .line 70
    move-result p1

    .line 71
    if-eqz p1, :cond_4

    .line 72
    .line 73
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 74
    .line 75
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->H4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lf81/d;

    .line 76
    .line 77
    .line 78
    move-result-object v4

    .line 79
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->$game:LN21/k;

    .line 80
    .line 81
    invoke-virtual {p1}, LN21/k;->e()J

    .line 82
    .line 83
    .line 84
    move-result-wide v5

    .line 85
    iput v3, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->label:I

    .line 86
    .line 87
    const/4 v8, 0x0

    .line 88
    move-object v9, p0

    .line 89
    invoke-interface/range {v4 .. v9}, Lf81/d;->a(JZILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 90
    .line 91
    .line 92
    move-result-object p1

    .line 93
    if-ne p1, v0, :cond_5

    .line 94
    .line 95
    goto :goto_1

    .line 96
    :cond_4
    move-object v9, p0

    .line 97
    iget-object p1, v9, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 98
    .line 99
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->y4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Ljava/util/Map;

    .line 100
    .line 101
    .line 102
    move-result-object p1

    .line 103
    iget-object v1, v9, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->$game:LN21/k;

    .line 104
    .line 105
    invoke-virtual {v1}, LN21/k;->e()J

    .line 106
    .line 107
    .line 108
    move-result-wide v3

    .line 109
    invoke-static {v3, v4}, LHc/a;->f(J)Ljava/lang/Long;

    .line 110
    .line 111
    .line 112
    move-result-object v1

    .line 113
    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object p1

    .line 117
    check-cast p1, Lorg/xplatform/aggregator/api/model/Game;

    .line 118
    .line 119
    if-eqz p1, :cond_5

    .line 120
    .line 121
    iget-object v1, v9, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 122
    .line 123
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->q4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lf81/a;

    .line 124
    .line 125
    .line 126
    move-result-object v1

    .line 127
    iput v2, v9, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;->label:I

    .line 128
    .line 129
    const/4 v2, 0x0

    .line 130
    invoke-interface {v1, p1, v7, v2, p0}, Lf81/a;->a(Lorg/xplatform/aggregator/api/model/Game;ZILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    move-result-object p1

    .line 134
    if-ne p1, v0, :cond_5

    .line 135
    .line 136
    :goto_1
    return-object v0

    .line 137
    :cond_5
    :goto_2
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 138
    .line 139
    return-object p1
.end method
