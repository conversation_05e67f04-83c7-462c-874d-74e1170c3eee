.class public final synthetic LNB0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Landroidx/compose/ui/l;

.field public final synthetic b:Landroidx/compose/runtime/r1;

.field public final synthetic c:Z

.field public final synthetic d:Lkotlin/jvm/functions/Function1;

.field public final synthetic e:Lkotlin/jvm/functions/Function1;

.field public final synthetic f:I

.field public final synthetic g:I


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/ui/l;Landroidx/compose/runtime/r1;ZLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LNB0/b;->a:Landroidx/compose/ui/l;

    iput-object p2, p0, LNB0/b;->b:Landroidx/compose/runtime/r1;

    iput-boolean p3, p0, LNB0/b;->c:Z

    iput-object p4, p0, LNB0/b;->d:L<PERSON><PERSON>/jvm/functions/Function1;

    iput-object p5, p0, LNB0/b;->e:Lkotlin/jvm/functions/Function1;

    iput p6, p0, LNB0/b;->f:I

    iput p7, p0, LNB0/b;->g:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    iget-object v0, p0, LNB0/b;->a:Landroidx/compose/ui/l;

    iget-object v1, p0, LNB0/b;->b:Landroidx/compose/runtime/r1;

    iget-boolean v2, p0, LNB0/b;->c:Z

    iget-object v3, p0, LNB0/b;->d:Lkotlin/jvm/functions/Function1;

    iget-object v4, p0, LNB0/b;->e:Lkotlin/jvm/functions/Function1;

    iget v5, p0, LNB0/b;->f:I

    iget v6, p0, LNB0/b;->g:I

    move-object v7, p1

    check-cast v7, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v8

    invoke-static/range {v0 .. v8}, LNB0/g;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/r1;ZLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
