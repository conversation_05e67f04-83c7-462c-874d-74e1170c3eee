.class public final synthetic Ll11/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Landroidx/compose/ui/l;

.field public final synthetic b:Ll11/m;

.field public final synthetic c:Z

.field public final synthetic d:I

.field public final synthetic e:I


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/ui/l;Ll11/m;ZII)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ll11/d;->a:Landroidx/compose/ui/l;

    iput-object p2, p0, Ll11/d;->b:Ll11/m;

    iput-boolean p3, p0, Ll11/d;->c:Z

    iput p4, p0, Ll11/d;->d:I

    iput p5, p0, Ll11/d;->e:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    iget-object v0, p0, Ll11/d;->a:Landroidx/compose/ui/l;

    iget-object v1, p0, Ll11/d;->b:Ll11/m;

    iget-boolean v2, p0, Ll11/d;->c:Z

    iget v3, p0, Ll11/d;->d:I

    iget v4, p0, Ll11/d;->e:I

    move-object v5, p1

    check-cast v5, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v6

    invoke-static/range {v0 .. v6}, Ll11/g;->e(Landroidx/compose/ui/l;Ll11/m;ZIILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
