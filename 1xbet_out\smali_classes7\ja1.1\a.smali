.class public final Lja1/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "Lua1/e;",
        "Lxa1/b;",
        "a",
        "(Lua1/e;)Lxa1/b;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lua1/e;)Lxa1/b;
    .locals 28
    .param p0    # Lua1/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p0}, Lua1/e;->a()Lua1/e$a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_1f

    .line 6
    .line 7
    invoke-virtual {v0}, Lua1/e$a;->a()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    if-eqz v0, :cond_1f

    .line 12
    .line 13
    new-instance v2, Ljava/util/ArrayList;

    .line 14
    .line 15
    const/16 v3, 0xa

    .line 16
    .line 17
    invoke-static {v0, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 18
    .line 19
    .line 20
    move-result v4

    .line 21
    invoke-direct {v2, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 22
    .line 23
    .line 24
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 29
    .line 30
    .line 31
    move-result v4

    .line 32
    if-eqz v4, :cond_20

    .line 33
    .line 34
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v4

    .line 38
    check-cast v4, Lua1/d;

    .line 39
    .line 40
    invoke-virtual {v4}, Lua1/d;->g()I

    .line 41
    .line 42
    .line 43
    move-result v6

    .line 44
    invoke-virtual {v4}, Lua1/d;->a()D

    .line 45
    .line 46
    .line 47
    move-result-wide v7

    .line 48
    invoke-virtual {v4}, Lua1/d;->e()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object v5

    .line 52
    const-string v9, ""

    .line 53
    .line 54
    if-nez v5, :cond_0

    .line 55
    .line 56
    move-object v5, v9

    .line 57
    :cond_0
    invoke-virtual {v4}, Lua1/d;->f()D

    .line 58
    .line 59
    .line 60
    move-result-wide v10

    .line 61
    invoke-virtual {v4}, Lua1/d;->o()I

    .line 62
    .line 63
    .line 64
    move-result v12

    .line 65
    invoke-virtual {v4}, Lua1/d;->i()J

    .line 66
    .line 67
    .line 68
    move-result-wide v13

    .line 69
    new-instance v15, Lxa1/i;

    .line 70
    .line 71
    sget-object v1, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    .line 72
    .line 73
    move-object/from16 v16, v4

    .line 74
    .line 75
    invoke-virtual/range {v16 .. v16}, Lua1/d;->j()J

    .line 76
    .line 77
    .line 78
    move-result-wide v3

    .line 79
    invoke-virtual {v1, v3, v4}, Ljava/util/concurrent/TimeUnit;->toMillis(J)J

    .line 80
    .line 81
    .line 82
    move-result-wide v3

    .line 83
    invoke-direct {v15, v3, v4}, Lxa1/i;-><init>(J)V

    .line 84
    .line 85
    .line 86
    move-object/from16 v4, v16

    .line 87
    .line 88
    invoke-virtual {v4}, Lua1/d;->k()J

    .line 89
    .line 90
    .line 91
    move-result-wide v16

    .line 92
    new-instance v1, Lxa1/h;

    .line 93
    .line 94
    invoke-virtual {v4}, Lua1/d;->h()Lua1/m;

    .line 95
    .line 96
    .line 97
    move-result-object v3

    .line 98
    if-eqz v3, :cond_1

    .line 99
    .line 100
    invoke-virtual {v3}, Lua1/m;->b()Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 101
    .line 102
    .line 103
    move-result-object v3

    .line 104
    if-nez v3, :cond_2

    .line 105
    .line 106
    :cond_1
    sget-object v3, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->UNKNOWN:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 107
    .line 108
    :cond_2
    invoke-virtual {v4}, Lua1/d;->h()Lua1/m;

    .line 109
    .line 110
    .line 111
    move-result-object v18

    .line 112
    if-eqz v18, :cond_3

    .line 113
    .line 114
    invoke-virtual/range {v18 .. v18}, Lua1/m;->a()Ljava/lang/String;

    .line 115
    .line 116
    .line 117
    move-result-object v18

    .line 118
    goto :goto_1

    .line 119
    :cond_3
    const/16 v18, 0x0

    .line 120
    .line 121
    :goto_1
    move-object/from16 v25, v0

    .line 122
    .line 123
    if-nez v18, :cond_4

    .line 124
    .line 125
    move-object v0, v9

    .line 126
    goto :goto_2

    .line 127
    :cond_4
    move-object/from16 v0, v18

    .line 128
    .line 129
    :goto_2
    invoke-direct {v1, v3, v0}, Lxa1/h;-><init>(Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;Ljava/lang/String;)V

    .line 130
    .line 131
    .line 132
    invoke-virtual {v4}, Lua1/d;->b()Ljava/util/List;

    .line 133
    .line 134
    .line 135
    move-result-object v0

    .line 136
    if-eqz v0, :cond_6

    .line 137
    .line 138
    new-instance v3, Ljava/util/ArrayList;

    .line 139
    .line 140
    move-object/from16 v18, v1

    .line 141
    .line 142
    move-object/from16 v19, v4

    .line 143
    .line 144
    const/16 v1, 0xa

    .line 145
    .line 146
    invoke-static {v0, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 147
    .line 148
    .line 149
    move-result v4

    .line 150
    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 151
    .line 152
    .line 153
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 154
    .line 155
    .line 156
    move-result-object v0

    .line 157
    :goto_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 158
    .line 159
    .line 160
    move-result v1

    .line 161
    if-eqz v1, :cond_7

    .line 162
    .line 163
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 164
    .line 165
    .line 166
    move-result-object v1

    .line 167
    check-cast v1, Lua1/h;

    .line 168
    .line 169
    new-instance v4, Lxa1/e;

    .line 170
    .line 171
    move-object/from16 v20, v0

    .line 172
    .line 173
    invoke-virtual {v1}, Lua1/h;->a()I

    .line 174
    .line 175
    .line 176
    move-result v0

    .line 177
    invoke-virtual {v1}, Lua1/h;->b()Ljava/lang/String;

    .line 178
    .line 179
    .line 180
    move-result-object v1

    .line 181
    if-nez v1, :cond_5

    .line 182
    .line 183
    move-object v1, v9

    .line 184
    :cond_5
    invoke-direct {v4, v0, v1}, Lxa1/e;-><init>(ILjava/lang/String;)V

    .line 185
    .line 186
    .line 187
    invoke-interface {v3, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 188
    .line 189
    .line 190
    move-object/from16 v0, v20

    .line 191
    .line 192
    goto :goto_3

    .line 193
    :cond_6
    move-object/from16 v18, v1

    .line 194
    .line 195
    move-object/from16 v19, v4

    .line 196
    .line 197
    const/4 v3, 0x0

    .line 198
    :cond_7
    if-nez v3, :cond_8

    .line 199
    .line 200
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 201
    .line 202
    .line 203
    move-result-object v3

    .line 204
    :cond_8
    invoke-virtual/range {v19 .. v19}, Lua1/d;->c()Ljava/util/List;

    .line 205
    .line 206
    .line 207
    move-result-object v0

    .line 208
    if-eqz v0, :cond_b

    .line 209
    .line 210
    new-instance v4, Ljava/util/ArrayList;

    .line 211
    .line 212
    move-object/from16 v21, v3

    .line 213
    .line 214
    const/16 v1, 0xa

    .line 215
    .line 216
    invoke-static {v0, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 217
    .line 218
    .line 219
    move-result v3

    .line 220
    invoke-direct {v4, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 221
    .line 222
    .line 223
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 224
    .line 225
    .line 226
    move-result-object v0

    .line 227
    :goto_4
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 228
    .line 229
    .line 230
    move-result v1

    .line 231
    if-eqz v1, :cond_c

    .line 232
    .line 233
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 234
    .line 235
    .line 236
    move-result-object v1

    .line 237
    check-cast v1, Lua1/k;

    .line 238
    .line 239
    new-instance v3, Lxa1/f;

    .line 240
    .line 241
    invoke-virtual {v1}, Lua1/k;->a()Ljava/lang/Integer;

    .line 242
    .line 243
    .line 244
    move-result-object v22

    .line 245
    if-eqz v22, :cond_9

    .line 246
    .line 247
    invoke-virtual/range {v22 .. v22}, Ljava/lang/Integer;->intValue()I

    .line 248
    .line 249
    .line 250
    move-result v22

    .line 251
    move/from16 v27, v22

    .line 252
    .line 253
    move-object/from16 v22, v0

    .line 254
    .line 255
    move/from16 v0, v27

    .line 256
    .line 257
    goto :goto_5

    .line 258
    :cond_9
    move-object/from16 v22, v0

    .line 259
    .line 260
    const/4 v0, 0x0

    .line 261
    :goto_5
    invoke-virtual {v1}, Lua1/k;->b()Ljava/lang/String;

    .line 262
    .line 263
    .line 264
    move-result-object v1

    .line 265
    if-nez v1, :cond_a

    .line 266
    .line 267
    move-object v1, v9

    .line 268
    :cond_a
    invoke-direct {v3, v0, v1}, Lxa1/f;-><init>(ILjava/lang/String;)V

    .line 269
    .line 270
    .line 271
    invoke-interface {v4, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 272
    .line 273
    .line 274
    move-object/from16 v0, v22

    .line 275
    .line 276
    goto :goto_4

    .line 277
    :cond_b
    move-object/from16 v21, v3

    .line 278
    .line 279
    const/4 v4, 0x0

    .line 280
    :cond_c
    if-nez v4, :cond_d

    .line 281
    .line 282
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 283
    .line 284
    .line 285
    move-result-object v4

    .line 286
    :cond_d
    invoke-virtual/range {v19 .. v19}, Lua1/d;->d()Ljava/util/List;

    .line 287
    .line 288
    .line 289
    move-result-object v0

    .line 290
    if-eqz v0, :cond_f

    .line 291
    .line 292
    new-instance v1, Ljava/util/ArrayList;

    .line 293
    .line 294
    move-object/from16 v22, v4

    .line 295
    .line 296
    const/16 v3, 0xa

    .line 297
    .line 298
    invoke-static {v0, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 299
    .line 300
    .line 301
    move-result v4

    .line 302
    invoke-direct {v1, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 303
    .line 304
    .line 305
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 306
    .line 307
    .line 308
    move-result-object v0

    .line 309
    :goto_6
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 310
    .line 311
    .line 312
    move-result v3

    .line 313
    if-eqz v3, :cond_10

    .line 314
    .line 315
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 316
    .line 317
    .line 318
    move-result-object v3

    .line 319
    check-cast v3, Lua1/l;

    .line 320
    .line 321
    new-instance v4, Lxa1/g;

    .line 322
    .line 323
    move-object/from16 v23, v0

    .line 324
    .line 325
    invoke-virtual {v3}, Lua1/l;->b()I

    .line 326
    .line 327
    .line 328
    move-result v0

    .line 329
    invoke-virtual {v3}, Lua1/l;->a()Ljava/lang/String;

    .line 330
    .line 331
    .line 332
    move-result-object v3

    .line 333
    if-nez v3, :cond_e

    .line 334
    .line 335
    move-object v3, v9

    .line 336
    :cond_e
    invoke-direct {v4, v0, v3}, Lxa1/g;-><init>(ILjava/lang/String;)V

    .line 337
    .line 338
    .line 339
    invoke-interface {v1, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 340
    .line 341
    .line 342
    move-object/from16 v0, v23

    .line 343
    .line 344
    goto :goto_6

    .line 345
    :cond_f
    move-object/from16 v22, v4

    .line 346
    .line 347
    const/4 v1, 0x0

    .line 348
    :cond_10
    if-nez v1, :cond_11

    .line 349
    .line 350
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 351
    .line 352
    .line 353
    move-result-object v1

    .line 354
    :cond_11
    invoke-virtual/range {v19 .. v19}, Lua1/d;->l()Ljava/util/List;

    .line 355
    .line 356
    .line 357
    move-result-object v0

    .line 358
    if-eqz v0, :cond_13

    .line 359
    .line 360
    new-instance v3, Ljava/util/ArrayList;

    .line 361
    .line 362
    move-object/from16 v23, v1

    .line 363
    .line 364
    const/16 v4, 0xa

    .line 365
    .line 366
    invoke-static {v0, v4}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 367
    .line 368
    .line 369
    move-result v1

    .line 370
    invoke-direct {v3, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 371
    .line 372
    .line 373
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 374
    .line 375
    .line 376
    move-result-object v0

    .line 377
    :goto_7
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 378
    .line 379
    .line 380
    move-result v1

    .line 381
    if-eqz v1, :cond_14

    .line 382
    .line 383
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 384
    .line 385
    .line 386
    move-result-object v1

    .line 387
    check-cast v1, Lua1/h;

    .line 388
    .line 389
    new-instance v4, Lxa1/e;

    .line 390
    .line 391
    move-object/from16 v24, v0

    .line 392
    .line 393
    invoke-virtual {v1}, Lua1/h;->a()I

    .line 394
    .line 395
    .line 396
    move-result v0

    .line 397
    invoke-virtual {v1}, Lua1/h;->b()Ljava/lang/String;

    .line 398
    .line 399
    .line 400
    move-result-object v1

    .line 401
    if-nez v1, :cond_12

    .line 402
    .line 403
    move-object v1, v9

    .line 404
    :cond_12
    invoke-direct {v4, v0, v1}, Lxa1/e;-><init>(ILjava/lang/String;)V

    .line 405
    .line 406
    .line 407
    invoke-interface {v3, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 408
    .line 409
    .line 410
    move-object/from16 v0, v24

    .line 411
    .line 412
    goto :goto_7

    .line 413
    :cond_13
    move-object/from16 v23, v1

    .line 414
    .line 415
    const/4 v3, 0x0

    .line 416
    :cond_14
    if-nez v3, :cond_15

    .line 417
    .line 418
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 419
    .line 420
    .line 421
    move-result-object v3

    .line 422
    :cond_15
    invoke-virtual/range {v19 .. v19}, Lua1/d;->m()Ljava/util/List;

    .line 423
    .line 424
    .line 425
    move-result-object v0

    .line 426
    if-eqz v0, :cond_18

    .line 427
    .line 428
    new-instance v1, Ljava/util/ArrayList;

    .line 429
    .line 430
    move-object/from16 v24, v3

    .line 431
    .line 432
    const/16 v4, 0xa

    .line 433
    .line 434
    invoke-static {v0, v4}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 435
    .line 436
    .line 437
    move-result v3

    .line 438
    invoke-direct {v1, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 439
    .line 440
    .line 441
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 442
    .line 443
    .line 444
    move-result-object v0

    .line 445
    :goto_8
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 446
    .line 447
    .line 448
    move-result v3

    .line 449
    if-eqz v3, :cond_19

    .line 450
    .line 451
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 452
    .line 453
    .line 454
    move-result-object v3

    .line 455
    check-cast v3, Lua1/k;

    .line 456
    .line 457
    new-instance v4, Lxa1/f;

    .line 458
    .line 459
    invoke-virtual {v3}, Lua1/k;->a()Ljava/lang/Integer;

    .line 460
    .line 461
    .line 462
    move-result-object v26

    .line 463
    if-eqz v26, :cond_16

    .line 464
    .line 465
    invoke-virtual/range {v26 .. v26}, Ljava/lang/Integer;->intValue()I

    .line 466
    .line 467
    .line 468
    move-result v26

    .line 469
    move/from16 v27, v26

    .line 470
    .line 471
    move-object/from16 v26, v0

    .line 472
    .line 473
    move/from16 v0, v27

    .line 474
    .line 475
    goto :goto_9

    .line 476
    :cond_16
    move-object/from16 v26, v0

    .line 477
    .line 478
    const/4 v0, 0x0

    .line 479
    :goto_9
    invoke-virtual {v3}, Lua1/k;->b()Ljava/lang/String;

    .line 480
    .line 481
    .line 482
    move-result-object v3

    .line 483
    if-nez v3, :cond_17

    .line 484
    .line 485
    move-object v3, v9

    .line 486
    :cond_17
    invoke-direct {v4, v0, v3}, Lxa1/f;-><init>(ILjava/lang/String;)V

    .line 487
    .line 488
    .line 489
    invoke-interface {v1, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 490
    .line 491
    .line 492
    move-object/from16 v0, v26

    .line 493
    .line 494
    goto :goto_8

    .line 495
    :cond_18
    move-object/from16 v24, v3

    .line 496
    .line 497
    const/4 v1, 0x0

    .line 498
    :cond_19
    if-nez v1, :cond_1a

    .line 499
    .line 500
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 501
    .line 502
    .line 503
    move-result-object v1

    .line 504
    :cond_1a
    invoke-virtual/range {v19 .. v19}, Lua1/d;->n()Ljava/util/List;

    .line 505
    .line 506
    .line 507
    move-result-object v0

    .line 508
    if-eqz v0, :cond_1c

    .line 509
    .line 510
    new-instance v3, Ljava/util/ArrayList;

    .line 511
    .line 512
    move-object/from16 v19, v1

    .line 513
    .line 514
    const/16 v4, 0xa

    .line 515
    .line 516
    invoke-static {v0, v4}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 517
    .line 518
    .line 519
    move-result v1

    .line 520
    invoke-direct {v3, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 521
    .line 522
    .line 523
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 524
    .line 525
    .line 526
    move-result-object v0

    .line 527
    :goto_a
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 528
    .line 529
    .line 530
    move-result v1

    .line 531
    if-eqz v1, :cond_1d

    .line 532
    .line 533
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 534
    .line 535
    .line 536
    move-result-object v1

    .line 537
    check-cast v1, Lua1/l;

    .line 538
    .line 539
    new-instance v4, Lxa1/g;

    .line 540
    .line 541
    move-object/from16 v20, v0

    .line 542
    .line 543
    invoke-virtual {v1}, Lua1/l;->b()I

    .line 544
    .line 545
    .line 546
    move-result v0

    .line 547
    invoke-virtual {v1}, Lua1/l;->a()Ljava/lang/String;

    .line 548
    .line 549
    .line 550
    move-result-object v1

    .line 551
    if-nez v1, :cond_1b

    .line 552
    .line 553
    move-object v1, v9

    .line 554
    :cond_1b
    invoke-direct {v4, v0, v1}, Lxa1/g;-><init>(ILjava/lang/String;)V

    .line 555
    .line 556
    .line 557
    invoke-interface {v3, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 558
    .line 559
    .line 560
    move-object/from16 v0, v20

    .line 561
    .line 562
    const/16 v4, 0xa

    .line 563
    .line 564
    goto :goto_a

    .line 565
    :cond_1c
    move-object/from16 v19, v1

    .line 566
    .line 567
    const/4 v3, 0x0

    .line 568
    :cond_1d
    if-nez v3, :cond_1e

    .line 569
    .line 570
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 571
    .line 572
    .line 573
    move-result-object v3

    .line 574
    :cond_1e
    move-object v9, v5

    .line 575
    new-instance v5, Lxa1/a;

    .line 576
    .line 577
    move-object/from16 v20, v23

    .line 578
    .line 579
    move-object/from16 v23, v19

    .line 580
    .line 581
    move-object/from16 v19, v21

    .line 582
    .line 583
    move-object/from16 v21, v20

    .line 584
    .line 585
    move-object/from16 v20, v22

    .line 586
    .line 587
    move-object/from16 v22, v24

    .line 588
    .line 589
    move-object/from16 v24, v3

    .line 590
    .line 591
    invoke-direct/range {v5 .. v24}, Lxa1/a;-><init>(IDLjava/lang/String;DIJLxa1/i;JLxa1/h;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V

    .line 592
    .line 593
    .line 594
    invoke-interface {v2, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 595
    .line 596
    .line 597
    move-object/from16 v0, v25

    .line 598
    .line 599
    const/16 v3, 0xa

    .line 600
    .line 601
    goto/16 :goto_0

    .line 602
    .line 603
    :cond_1f
    const/4 v2, 0x0

    .line 604
    :cond_20
    if-nez v2, :cond_21

    .line 605
    .line 606
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 607
    .line 608
    .line 609
    move-result-object v2

    .line 610
    :cond_21
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 611
    .line 612
    .line 613
    move-result-object v0

    .line 614
    :cond_22
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 615
    .line 616
    .line 617
    move-result v1

    .line 618
    if-eqz v1, :cond_23

    .line 619
    .line 620
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 621
    .line 622
    .line 623
    move-result-object v1

    .line 624
    move-object v3, v1

    .line 625
    check-cast v3, Lxa1/a;

    .line 626
    .line 627
    invoke-virtual {v3}, Lxa1/a;->i()Lxa1/h;

    .line 628
    .line 629
    .line 630
    move-result-object v3

    .line 631
    invoke-virtual {v3}, Lxa1/h;->a()Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 632
    .line 633
    .line 634
    move-result-object v3

    .line 635
    sget-object v4, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->ACTIVE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 636
    .line 637
    if-ne v3, v4, :cond_22

    .line 638
    .line 639
    goto :goto_b

    .line 640
    :cond_23
    const/4 v1, 0x0

    .line 641
    :goto_b
    check-cast v1, Lxa1/a;

    .line 642
    .line 643
    if-eqz v1, :cond_26

    .line 644
    .line 645
    invoke-static {v1}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 646
    .line 647
    .line 648
    move-result-object v0

    .line 649
    new-instance v1, Ljava/util/ArrayList;

    .line 650
    .line 651
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 652
    .line 653
    .line 654
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 655
    .line 656
    .line 657
    move-result-object v2

    .line 658
    :cond_24
    :goto_c
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 659
    .line 660
    .line 661
    move-result v3

    .line 662
    if-eqz v3, :cond_25

    .line 663
    .line 664
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 665
    .line 666
    .line 667
    move-result-object v3

    .line 668
    move-object v4, v3

    .line 669
    check-cast v4, Lxa1/a;

    .line 670
    .line 671
    invoke-virtual {v4}, Lxa1/a;->i()Lxa1/h;

    .line 672
    .line 673
    .line 674
    move-result-object v4

    .line 675
    invoke-virtual {v4}, Lxa1/h;->a()Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 676
    .line 677
    .line 678
    move-result-object v4

    .line 679
    sget-object v5, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->ACTIVE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 680
    .line 681
    if-eq v4, v5, :cond_24

    .line 682
    .line 683
    invoke-interface {v1, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 684
    .line 685
    .line 686
    goto :goto_c

    .line 687
    :cond_25
    invoke-static {v0, v1}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 688
    .line 689
    .line 690
    move-result-object v0

    .line 691
    new-instance v1, Lxa1/b;

    .line 692
    .line 693
    invoke-direct {v1, v0}, Lxa1/b;-><init>(Ljava/util/List;)V

    .line 694
    .line 695
    .line 696
    return-object v1

    .line 697
    :cond_26
    new-instance v0, Lxa1/b;

    .line 698
    .line 699
    invoke-direct {v0, v2}, Lxa1/b;-><init>(Ljava/util/List;)V

    .line 700
    .line 701
    .line 702
    return-object v0
.end method
