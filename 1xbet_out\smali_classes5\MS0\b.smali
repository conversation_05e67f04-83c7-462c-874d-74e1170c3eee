.class public final LMS0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a+\u0010\u0007\u001a\u00020\u0006*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;",
        "",
        "expanded",
        "",
        "totalChamps",
        "selectedChamps",
        "LLS0/c;",
        "a",
        "(Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;ZII)LLS0/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;ZII)LLS0/c;
    .locals 13
    .param p0    # Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;->getSportId()J

    .line 2
    .line 3
    .line 4
    move-result-wide v1

    .line 5
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;->getSubSportName()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 10
    .line 11
    .line 12
    move-result v3

    .line 13
    if-nez v3, :cond_0

    .line 14
    .line 15
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;->getSportName()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    :cond_0
    move-object v3, v0

    .line 20
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;->isCyber()Z

    .line 21
    .line 22
    .line 23
    move-result v4

    .line 24
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;->getSubSportId()J

    .line 25
    .line 26
    .line 27
    move-result-wide v5

    .line 28
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;->getImageUrl()Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 33
    .line 34
    .line 35
    move-result v7

    .line 36
    if-nez v7, :cond_1

    .line 37
    .line 38
    sget-object v0, LDX0/d;->a:LDX0/d;

    .line 39
    .line 40
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;->getSportId()J

    .line 41
    .line 42
    .line 43
    move-result-wide v7

    .line 44
    invoke-virtual {v0, v7, v8}, LDX0/d;->c(J)Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    :cond_1
    move-object v7, v0

    .line 49
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;->getSelected()Z

    .line 50
    .line 51
    .line 52
    move-result p0

    .line 53
    invoke-static {p0}, LLS0/c$a$b;->b(Z)Z

    .line 54
    .line 55
    .line 56
    move-result v11

    .line 57
    invoke-static {p1}, LLS0/c$a$a;->b(Z)Z

    .line 58
    .line 59
    .line 60
    move-result v8

    .line 61
    if-eqz p1, :cond_2

    .line 62
    .line 63
    sget p0, LlZ0/h;->ic_glyph_chevron_up_small:I

    .line 64
    .line 65
    :goto_0
    move v9, p0

    .line 66
    goto :goto_1

    .line 67
    :cond_2
    sget p0, LlZ0/h;->ic_glyph_chevron_down_small:I

    .line 68
    .line 69
    goto :goto_0

    .line 70
    :goto_1
    new-instance p0, Ljava/lang/StringBuilder;

    .line 71
    .line 72
    invoke-direct {p0}, Ljava/lang/StringBuilder;-><init>()V

    .line 73
    .line 74
    .line 75
    move/from16 p1, p3

    .line 76
    .line 77
    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 78
    .line 79
    .line 80
    const-string p1, "/"

    .line 81
    .line 82
    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 83
    .line 84
    .line 85
    invoke-virtual {p0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 86
    .line 87
    .line 88
    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 89
    .line 90
    .line 91
    move-result-object p0

    .line 92
    invoke-static {p0}, LLS0/c$a$c;->b(Ljava/lang/String;)Ljava/lang/String;

    .line 93
    .line 94
    .line 95
    move-result-object v10

    .line 96
    new-instance v0, LLS0/c;

    .line 97
    .line 98
    const/4 v12, 0x0

    .line 99
    invoke-direct/range {v0 .. v12}, LLS0/c;-><init>(JLjava/lang/String;ZJLjava/lang/String;ZILjava/lang/String;ZLkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 100
    .line 101
    .line 102
    return-object v0
.end method
