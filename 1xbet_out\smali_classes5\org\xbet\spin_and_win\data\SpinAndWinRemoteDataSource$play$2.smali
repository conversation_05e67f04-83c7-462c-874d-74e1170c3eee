.class final Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.spin_and_win.data.SpinAndWinRemoteDataSource$play$2"
    f = "SpinAndWinRemoteDataSource.kt"
    l = {
        0x1c
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;->c(DJLorg/xbet/games_section/api/models/GameBonus;Ljava/util/List;ILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/String;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lg9/d<",
        "+",
        "Laz0/a;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "",
        "token",
        "Lg9/d;",
        "Laz0/a;",
        "<anonymous>",
        "(Ljava/lang/String;)Lg9/d;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $activeId:J

.field final synthetic $betSum:D

.field final synthetic $betUser:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LZy0/a;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic $gameBonus:Lorg/xbet/games_section/api/models/GameBonus;

.field final synthetic $language:Ljava/lang/String;

.field final synthetic $whence:I

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;


# direct methods
.method public constructor <init>(Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;Lorg/xbet/games_section/api/models/GameBonus;Ljava/util/List;DJLjava/lang/String;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;",
            "Lorg/xbet/games_section/api/models/GameBonus;",
            "Ljava/util/List<",
            "LZy0/a;",
            ">;DJ",
            "Ljava/lang/String;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->this$0:Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;

    iput-object p2, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$gameBonus:Lorg/xbet/games_section/api/models/GameBonus;

    iput-object p3, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$betUser:Ljava/util/List;

    iput-wide p4, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$betSum:D

    iput-wide p6, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$activeId:J

    iput-object p8, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$language:Ljava/lang/String;

    iput p9, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$whence:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p10}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;

    iget-object v1, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->this$0:Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;

    iget-object v2, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$gameBonus:Lorg/xbet/games_section/api/models/GameBonus;

    iget-object v3, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$betUser:Ljava/util/List;

    iget-wide v4, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$betSum:D

    iget-wide v6, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$activeId:J

    iget-object v8, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$language:Ljava/lang/String;

    iget v9, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$whence:I

    move-object v10, p2

    invoke-direct/range {v0 .. v10}, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;-><init>(Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;Lorg/xbet/games_section/api/models/GameBonus;Ljava/util/List;DJLjava/lang/String;ILkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lg9/d<",
            "Laz0/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    if-eqz v2, :cond_1

    .line 11
    .line 12
    if-ne v2, v3, :cond_0

    .line 13
    .line 14
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    return-object p1

    .line 18
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 19
    .line 20
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 21
    .line 22
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 23
    .line 24
    .line 25
    throw v1

    .line 26
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    iget-object v2, v0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->L$0:Ljava/lang/Object;

    .line 30
    .line 31
    check-cast v2, Ljava/lang/String;

    .line 32
    .line 33
    iget-object v4, v0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->this$0:Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;

    .line 34
    .line 35
    invoke-static {v4}, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;->b(Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;)Lkotlin/jvm/functions/Function0;

    .line 36
    .line 37
    .line 38
    move-result-object v4

    .line 39
    invoke-interface {v4}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v4

    .line 43
    check-cast v4, LXy0/a;

    .line 44
    .line 45
    iget-object v5, v0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$gameBonus:Lorg/xbet/games_section/api/models/GameBonus;

    .line 46
    .line 47
    if-eqz v5, :cond_2

    .line 48
    .line 49
    invoke-virtual {v5}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusId()J

    .line 50
    .line 51
    .line 52
    move-result-wide v5

    .line 53
    :goto_0
    move-wide v11, v5

    .line 54
    goto :goto_1

    .line 55
    :cond_2
    const-wide/16 v5, 0x0

    .line 56
    .line 57
    goto :goto_0

    .line 58
    :goto_1
    sget-object v5, Lorg/xbet/core/data/LuckyWheelBonusType;->Companion:Lorg/xbet/core/data/LuckyWheelBonusType$a;

    .line 59
    .line 60
    iget-object v6, v0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$gameBonus:Lorg/xbet/games_section/api/models/GameBonus;

    .line 61
    .line 62
    if-eqz v6, :cond_3

    .line 63
    .line 64
    invoke-virtual {v6}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 65
    .line 66
    .line 67
    move-result-object v6

    .line 68
    goto :goto_2

    .line 69
    :cond_3
    const/4 v6, 0x0

    .line 70
    :goto_2
    invoke-virtual {v5, v6}, Lorg/xbet/core/data/LuckyWheelBonusType$a;->b(Lorg/xbet/games_section/api/models/GameBonusType;)Lorg/xbet/core/data/LuckyWheelBonusType;

    .line 71
    .line 72
    .line 73
    move-result-object v13

    .line 74
    new-instance v7, LZy0/b;

    .line 75
    .line 76
    iget-object v8, v0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$betUser:Ljava/util/List;

    .line 77
    .line 78
    iget-wide v9, v0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$betSum:D

    .line 79
    .line 80
    iget-wide v14, v0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$activeId:J

    .line 81
    .line 82
    iget-object v5, v0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$language:Ljava/lang/String;

    .line 83
    .line 84
    iget v6, v0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->$whence:I

    .line 85
    .line 86
    move-object/from16 v16, v5

    .line 87
    .line 88
    move/from16 v17, v6

    .line 89
    .line 90
    invoke-direct/range {v7 .. v17}, LZy0/b;-><init>(Ljava/util/List;DJLorg/xbet/core/data/LuckyWheelBonusType;JLjava/lang/String;I)V

    .line 91
    .line 92
    .line 93
    iput v3, v0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;->label:I

    .line 94
    .line 95
    invoke-interface {v4, v2, v7, v0}, LXy0/a;->a(Ljava/lang/String;LZy0/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object v2

    .line 99
    if-ne v2, v1, :cond_4

    .line 100
    .line 101
    return-object v1

    .line 102
    :cond_4
    return-object v2
.end method
