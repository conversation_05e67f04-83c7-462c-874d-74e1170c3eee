.class public final LgS0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQv/a$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LgS0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:LgS0/a$d;


# direct methods
.method public constructor <init>(LgS0/a$d;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, LgS0/a$b;->a:LgS0/a$d;

    return-void
.end method

.method public synthetic constructor <init>(LgS0/a$d;LgS0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, LgS0/a$b;-><init>(LgS0/a$d;)V

    return-void
.end method


# virtual methods
.method public a()LQv/a;
    .locals 3

    .line 1
    new-instance v0, LgS0/a$c;

    .line 2
    .line 3
    iget-object v1, p0, LgS0/a$b;->a:LgS0/a$d;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, LgS0/a$c;-><init>(LgS0/a$d;LgS0/b;)V

    .line 7
    .line 8
    .line 9
    return-object v0
.end method
