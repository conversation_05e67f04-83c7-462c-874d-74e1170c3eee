.class final Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.popular_classic.domain.scenarios.GetOpenActionBannerInfoScenario$invoke$1"
    f = "GetOpenActionBannerInfoScenario.kt"
    l = {
        0x17,
        0x18,
        0x19
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/flow/f<",
        "-",
        "Lf50/b;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u0002*\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lkotlinx/coroutines/flow/f;",
        "Lf50/b;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/flow/f;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field private synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->this$0:Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;

    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->this$0:Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;

    invoke-direct {v0, v1, p2}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;-><init>(Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/flow/f;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->invoke(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/f<",
            "-",
            "Lf50/b;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x3

    .line 8
    const/4 v3, 0x2

    .line 9
    const/4 v4, 0x1

    .line 10
    if-eqz v1, :cond_3

    .line 11
    .line 12
    if-eq v1, v4, :cond_2

    .line 13
    .line 14
    if-eq v1, v3, :cond_1

    .line 15
    .line 16
    if-ne v1, v2, :cond_0

    .line 17
    .line 18
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    goto/16 :goto_3

    .line 22
    .line 23
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 24
    .line 25
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 26
    .line 27
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    throw p1

    .line 31
    :cond_1
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 32
    .line 33
    check-cast v1, Ljava/util/List;

    .line 34
    .line 35
    iget-object v3, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 36
    .line 37
    check-cast v3, Lkotlinx/coroutines/flow/f;

    .line 38
    .line 39
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    goto :goto_1

    .line 43
    :cond_2
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 44
    .line 45
    check-cast v1, Lkotlinx/coroutines/flow/f;

    .line 46
    .line 47
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 48
    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 52
    .line 53
    .line 54
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 55
    .line 56
    check-cast p1, Lkotlinx/coroutines/flow/f;

    .line 57
    .line 58
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->this$0:Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;

    .line 59
    .line 60
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 61
    .line 62
    iput v4, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->label:I

    .line 63
    .line 64
    invoke-static {v1, p0}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;->b(Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object v1

    .line 68
    if-ne v1, v0, :cond_4

    .line 69
    .line 70
    goto :goto_2

    .line 71
    :cond_4
    move-object v5, v1

    .line 72
    move-object v1, p1

    .line 73
    move-object p1, v5

    .line 74
    :goto_0
    check-cast p1, Ljava/util/List;

    .line 75
    .line 76
    iget-object v4, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->this$0:Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;

    .line 77
    .line 78
    invoke-static {v4}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;->a(Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;)Lak/a;

    .line 79
    .line 80
    .line 81
    move-result-object v4

    .line 82
    invoke-interface {v4}, Lak/a;->v()Lek/a;

    .line 83
    .line 84
    .line 85
    move-result-object v4

    .line 86
    iput-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 87
    .line 88
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 89
    .line 90
    iput v3, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->label:I

    .line 91
    .line 92
    invoke-interface {v4, p0}, Lek/a;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 93
    .line 94
    .line 95
    move-result-object v3

    .line 96
    if-ne v3, v0, :cond_5

    .line 97
    .line 98
    goto :goto_2

    .line 99
    :cond_5
    move-object v5, v1

    .line 100
    move-object v1, p1

    .line 101
    move-object p1, v3

    .line 102
    move-object v3, v5

    .line 103
    :goto_1
    check-cast p1, Ljava/lang/Boolean;

    .line 104
    .line 105
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 106
    .line 107
    .line 108
    move-result p1

    .line 109
    new-instance v4, Lf50/b;

    .line 110
    .line 111
    invoke-direct {v4, v1, p1}, Lf50/b;-><init>(Ljava/util/List;Z)V

    .line 112
    .line 113
    .line 114
    const/4 p1, 0x0

    .line 115
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 116
    .line 117
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 118
    .line 119
    iput v2, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetOpenActionBannerInfoScenario$invoke$1;->label:I

    .line 120
    .line 121
    invoke-interface {v3, v4, p0}, Lkotlinx/coroutines/flow/f;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 122
    .line 123
    .line 124
    move-result-object p1

    .line 125
    if-ne p1, v0, :cond_6

    .line 126
    .line 127
    :goto_2
    return-object v0

    .line 128
    :cond_6
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 129
    .line 130
    return-object p1
.end method
