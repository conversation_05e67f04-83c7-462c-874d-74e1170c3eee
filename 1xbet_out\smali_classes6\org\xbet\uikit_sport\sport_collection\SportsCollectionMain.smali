.class public final Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain$a;,
        Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00a6\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0010\u000e\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\r\n\u0002\u0008\u000e\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0007\u0018\u0000 d2\u00020\u0001:\u00015B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0019\u0010\u000c\u001a\u00020\u000b2\u0008\u0008\u0001\u0010\n\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0017\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u0015\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\'\u0010\u001b\u001a\u00020\u000b2\u000c\u0010\u0018\u001a\u0008\u0012\u0004\u0012\u00020\u00170\u00162\n\u0008\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u0019\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ1\u0010\"\u001a\u00020\u000b2\"\u0010!\u001a\u001e\u0012\u0004\u0012\u00020\u001e\u0012\u0006\u0012\u0004\u0018\u00010\u001f\u0012\u0006\u0012\u0004\u0018\u00010 \u0012\u0004\u0012\u00020\u000b0\u001d\u00a2\u0006\u0004\u0008\"\u0010#J\r\u0010$\u001a\u00020\u000b\u00a2\u0006\u0004\u0008$\u0010%J\r\u0010&\u001a\u00020\u000b\u00a2\u0006\u0004\u0008&\u0010%J\u000f\u0010(\u001a\u0004\u0018\u00010\'\u00a2\u0006\u0004\u0008(\u0010)J\u000f\u0010*\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008*\u0010%J\u0017\u0010,\u001a\u00020\u000b2\u0006\u0010+\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008,\u0010\rJ\u000f\u0010.\u001a\u00020-H\u0002\u00a2\u0006\u0004\u0008.\u0010/J\u0017\u00100\u001a\u00020\u000b2\u0006\u0010+\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u00080\u0010\rJ\u001f\u00103\u001a\u00020\u000b2\u0006\u00102\u001a\u0002012\u0006\u0010\n\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u00083\u00104R\u0018\u00102\u001a\u0004\u0018\u00010\u000e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00085\u00106R\u0014\u0010:\u001a\u0002078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00109R\u0016\u0010=\u001a\u00020;8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008,\u0010<R\u0016\u0010?\u001a\u00020;8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008>\u0010<R\u0016\u0010A\u001a\u00020;8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008@\u0010<R\u0016\u0010C\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00083\u0010BR\u0016\u0010D\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00080\u0010BR\u0016\u0010E\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008.\u0010BR\u0016\u0010G\u001a\u00020\u00128\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008*\u0010FR4\u0010I\u001a \u0012\u0004\u0012\u00020\u001e\u0012\u0006\u0012\u0004\u0018\u00010\u001f\u0012\u0006\u0012\u0004\u0018\u00010 \u0012\u0004\u0012\u00020\u000b\u0018\u00010\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008&\u0010HR\u001a\u0010M\u001a\u0008\u0012\u0004\u0012\u00020K0J8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010LR\u0018\u0010P\u001a\u0004\u0018\u00010-8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008N\u0010OR\u0018\u0010T\u001a\u0004\u0018\u00010Q8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008R\u0010SR\u0016\u0010V\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008U\u0010BR\u0016\u0010X\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008W\u0010BR\u001b\u0010^\u001a\u00020Y8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008Z\u0010[\u001a\u0004\u0008\\\u0010]R\u001b\u0010c\u001a\u00020_8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008`\u0010[\u001a\u0004\u0008a\u0010b\u00a8\u0006e"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "style",
        "",
        "setStyle",
        "(I)V",
        "LN31/g;",
        "sportCollection",
        "setSportCollection",
        "(LN31/g;)V",
        "Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;",
        "sportsCollectionType",
        "setType",
        "(Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;)V",
        "",
        "Lorg/xbet/uikit_sport/sport_collection/b$c;",
        "items",
        "Ljava/lang/Runnable;",
        "commitCallback",
        "setItems",
        "(Ljava/util/List;Ljava/lang/Runnable;)V",
        "Lkotlin/Function3;",
        "Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;",
        "",
        "",
        "listener",
        "setOnButtonClickTypeListener",
        "(LOc/n;)V",
        "k",
        "()V",
        "j",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "getRecyclerView",
        "()Landroidx/recyclerview/widget/RecyclerView;",
        "i",
        "countShimmers",
        "c",
        "Landroid/widget/HorizontalScrollView;",
        "h",
        "()Landroid/widget/HorizontalScrollView;",
        "g",
        "Landroid/view/View;",
        "sportsCollectionView",
        "f",
        "(Landroid/view/View;I)V",
        "a",
        "LN31/g;",
        "Landroid/widget/FrameLayout$LayoutParams;",
        "b",
        "Landroid/widget/FrameLayout$LayoutParams;",
        "layoutParams",
        "",
        "Ljava/lang/CharSequence;",
        "headerTitle",
        "d",
        "buttonAllTitle",
        "e",
        "buttonFilterTitle",
        "I",
        "iconFilterResId",
        "iconAllResId",
        "sportCollectionStyle",
        "Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;",
        "type",
        "LOc/n;",
        "buttonClickTypeListener",
        "",
        "Lorg/xbet/uikit_sport/sport_collection/b;",
        "Ljava/util/List;",
        "itemsList",
        "l",
        "Landroid/widget/HorizontalScrollView;",
        "shimmersScrollView",
        "Landroid/widget/LinearLayout;",
        "m",
        "Landroid/widget/LinearLayout;",
        "shimmersContainer",
        "n",
        "marginHorizontalBetweenItems",
        "o",
        "marginForShimmerWithHeader",
        "LP31/b;",
        "p",
        "Lkotlin/j;",
        "getButtonFilterUiModel",
        "()LP31/b;",
        "buttonFilterUiModel",
        "LP31/a;",
        "q",
        "getButtonAllUiModel",
        "()LP31/a;",
        "buttonAllUiModel",
        "r",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final r:Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final s:I


# instance fields
.field public a:LN31/g;

.field public final b:Landroid/widget/FrameLayout$LayoutParams;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:Ljava/lang/CharSequence;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:Ljava/lang/CharSequence;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:Ljava/lang/CharSequence;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:I

.field public g:I

.field public h:I

.field public i:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public j:LOc/n;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LOc/n<",
            "-",
            "Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public final k:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/sport_collection/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public l:Landroid/widget/HorizontalScrollView;

.field public m:Landroid/widget/LinearLayout;

.field public n:I

.field public o:I

.field public final p:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->r:Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->s:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance p3, Landroid/widget/FrameLayout$LayoutParams;

    const/4 v0, -0x1

    const/4 v1, -0x2

    invoke-direct {p3, v0, v1}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    iput-object p3, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->b:Landroid/widget/FrameLayout$LayoutParams;

    .line 6
    const-string p3, ""

    iput-object p3, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->c:Ljava/lang/CharSequence;

    .line 7
    iput-object p3, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->d:Ljava/lang/CharSequence;

    .line 8
    iput-object p3, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->e:Ljava/lang/CharSequence;

    .line 9
    sget-object v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->RECTANGLE_L:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->i:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 10
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->k:Ljava/util/List;

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_4:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->n:I

    .line 12
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_40:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->o:I

    .line 13
    new-instance v0, LN31/e;

    invoke-direct {v0, p0}, LN31/e;-><init>(Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->p:Lkotlin/j;

    .line 14
    new-instance v0, LN31/f;

    invoke-direct {v0, p0}, LN31/f;-><init>(Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->q:Lkotlin/j;

    .line 15
    sget-object v0, Lm31/g;->SportCollectionWithHeader:[I

    const/4 v1, 0x0

    .line 16
    invoke-virtual {p1, p2, v0, v1, v1}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p2

    .line 17
    sget v0, Lm31/g;->SportCollectionWithHeader_headerTitle:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-static {p2, p1, v0}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v0

    if-nez v0, :cond_0

    move-object v0, p3

    :cond_0
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->c:Ljava/lang/CharSequence;

    .line 18
    sget v0, Lm31/g;->SportCollectionWithHeader_buttonAllTitle:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-static {p2, p1, v0}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v0

    if-nez v0, :cond_1

    move-object v0, p3

    :cond_1
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->d:Ljava/lang/CharSequence;

    .line 19
    sget v0, Lm31/g;->SportCollectionWithHeader_buttonFilterTitle:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-static {p2, p1, v0}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object p1

    if-nez p1, :cond_2

    goto :goto_0

    :cond_2
    move-object p3, p1

    :goto_0
    iput-object p3, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->e:Ljava/lang/CharSequence;

    .line 20
    sget p1, Lm31/g;->SportCollectionWithHeader_iconFilterResId:I

    invoke-virtual {p2, p1, v1}, Landroid/content/res/TypedArray;->getResourceId(II)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->f:I

    .line 21
    sget p1, Lm31/g;->SportCollectionWithHeader_iconAllResId:I

    invoke-virtual {p2, p1, v1}, Landroid/content/res/TypedArray;->getResourceId(II)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->g:I

    .line 22
    invoke-virtual {p2}, Landroid/content/res/TypedArray;->recycle()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic a(Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;)LP31/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->d(Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;)LP31/a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;)LP31/b;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->e(Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;)LP31/b;

    move-result-object p0

    return-object p0
.end method

.method public static final d(Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;)LP31/a;
    .locals 2

    .line 1
    new-instance v0, LP31/a;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->d:Ljava/lang/CharSequence;

    .line 4
    .line 5
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iget p0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->g:I

    .line 10
    .line 11
    invoke-static {p0}, LL11/c$c;->d(I)I

    .line 12
    .line 13
    .line 14
    move-result p0

    .line 15
    invoke-static {p0}, LL11/c$c;->c(I)LL11/c$c;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-direct {v0, v1, p0}, LP31/a;-><init>(Ljava/lang/String;LL11/c;)V

    .line 20
    .line 21
    .line 22
    return-object v0
.end method

.method public static final e(Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;)LP31/b;
    .locals 2

    .line 1
    new-instance v0, LP31/b;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->e:Ljava/lang/CharSequence;

    .line 4
    .line 5
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iget p0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->f:I

    .line 10
    .line 11
    invoke-static {p0}, LL11/c$c;->d(I)I

    .line 12
    .line 13
    .line 14
    move-result p0

    .line 15
    invoke-static {p0}, LL11/c$c;->c(I)LL11/c$c;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-direct {v0, v1, p0}, LP31/b;-><init>(Ljava/lang/String;LL11/c;)V

    .line 20
    .line 21
    .line 22
    return-object v0
.end method

.method private final getButtonAllUiModel()LP31/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->q:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LP31/a;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getButtonFilterUiModel()LP31/b;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->p:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LP31/b;

    .line 8
    .line 9
    return-object v0
.end method

.method public static synthetic setItems$default(Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;Ljava/util/List;Ljava/lang/Runnable;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x2

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    const/4 p2, 0x0

    .line 6
    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->setItems(Ljava/util/List;Ljava/lang/Runnable;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method private final setSportCollection(LN31/g;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->a:LN31/g;

    .line 2
    .line 3
    return-void
.end method

.method private final setStyle(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->a:LN31/g;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p1}, LN31/g;->setStyle(I)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method


# virtual methods
.method public final c(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->l:Landroid/widget/HorizontalScrollView;

    .line 2
    .line 3
    if-nez v0, :cond_1

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->h()Landroid/widget/HorizontalScrollView;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->l:Landroid/widget/HorizontalScrollView;

    .line 10
    .line 11
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->g(I)V

    .line 12
    .line 13
    .line 14
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->l:Landroid/widget/HorizontalScrollView;

    .line 15
    .line 16
    if-eqz p1, :cond_0

    .line 17
    .line 18
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->m:Landroid/widget/LinearLayout;

    .line 19
    .line 20
    invoke-virtual {p1, v0}, Landroid/widget/HorizontalScrollView;->addView(Landroid/view/View;)V

    .line 21
    .line 22
    .line 23
    :cond_0
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->l:Landroid/widget/HorizontalScrollView;

    .line 24
    .line 25
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 26
    .line 27
    .line 28
    :cond_1
    return-void
.end method

.method public final f(Landroid/view/View;I)V
    .locals 2

    .line 1
    instance-of v0, p1, LN31/g;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, LN31/g;

    .line 7
    .line 8
    invoke-direct {p0, v0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->setSportCollection(LN31/g;)V

    .line 9
    .line 10
    .line 11
    iget v1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->n:I

    .line 12
    .line 13
    invoke-interface {v0, v1}, LN31/g;->c(I)V

    .line 14
    .line 15
    .line 16
    invoke-direct {p0, p2}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->setStyle(I)V

    .line 17
    .line 18
    .line 19
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->b:Landroid/widget/FrameLayout$LayoutParams;

    .line 20
    .line 21
    invoke-virtual {p0, p1, p2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 22
    .line 23
    .line 24
    :cond_0
    return-void
.end method

.method public final g(I)V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->m:Landroid/widget/LinearLayout;

    .line 2
    .line 3
    if-nez v0, :cond_2

    .line 4
    .line 5
    new-instance v0, Landroid/widget/LinearLayout;

    .line 6
    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-direct {v0, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 12
    .line 13
    .line 14
    new-instance v1, Landroid/widget/FrameLayout$LayoutParams;

    .line 15
    .line 16
    const/4 v2, -0x1

    .line 17
    const/4 v3, -0x2

    .line 18
    invoke-direct {v1, v2, v3}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 22
    .line 23
    .line 24
    const/4 v1, 0x0

    .line 25
    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    sget v3, LlZ0/g;->small_horizontal_margin_dynamic:I

    .line 33
    .line 34
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 35
    .line 36
    .line 37
    move-result v2

    .line 38
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 39
    .line 40
    .line 41
    move-result-object v3

    .line 42
    sget v4, LlZ0/g;->small_horizontal_margin_dynamic:I

    .line 43
    .line 44
    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 45
    .line 46
    .line 47
    move-result v3

    .line 48
    invoke-virtual {v0}, Landroid/view/View;->getPaddingTop()I

    .line 49
    .line 50
    .line 51
    move-result v4

    .line 52
    invoke-virtual {v0}, Landroid/view/View;->getPaddingBottom()I

    .line 53
    .line 54
    .line 55
    move-result v5

    .line 56
    invoke-virtual {v0, v2, v4, v3, v5}, Landroid/view/View;->setPaddingRelative(IIII)V

    .line 57
    .line 58
    .line 59
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->m:Landroid/widget/LinearLayout;

    .line 60
    .line 61
    :goto_0
    if-ge v1, p1, :cond_2

    .line 62
    .line 63
    new-instance v2, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;

    .line 64
    .line 65
    new-instance v3, Landroid/view/ContextThemeWrapper;

    .line 66
    .line 67
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    iget v4, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->h:I

    .line 72
    .line 73
    invoke-direct {v3, v0, v4}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    .line 74
    .line 75
    .line 76
    const/4 v6, 0x6

    .line 77
    const/4 v7, 0x0

    .line 78
    const/4 v4, 0x0

    .line 79
    const/4 v5, 0x0

    .line 80
    invoke-direct/range {v2 .. v7}, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 81
    .line 82
    .line 83
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 84
    .line 85
    .line 86
    move-result v0

    .line 87
    invoke-virtual {v2, v0}, Landroid/view/View;->setId(I)V

    .line 88
    .line 89
    .line 90
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    if-eqz v0, :cond_1

    .line 95
    .line 96
    check-cast v0, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 97
    .line 98
    iget v3, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->n:I

    .line 99
    .line 100
    invoke-virtual {v0, v3}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginStart(I)V

    .line 101
    .line 102
    .line 103
    invoke-virtual {v2, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 104
    .line 105
    .line 106
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->m:Landroid/widget/LinearLayout;

    .line 107
    .line 108
    if-eqz v0, :cond_0

    .line 109
    .line 110
    invoke-virtual {v0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 111
    .line 112
    .line 113
    :cond_0
    invoke-virtual {v2}, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;->f()V

    .line 114
    .line 115
    .line 116
    add-int/lit8 v1, v1, 0x1

    .line 117
    .line 118
    goto :goto_0

    .line 119
    :cond_1
    new-instance p1, Ljava/lang/NullPointerException;

    .line 120
    .line 121
    const-string v0, "null cannot be cast to non-null type android.view.ViewGroup.MarginLayoutParams"

    .line 122
    .line 123
    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 124
    .line 125
    .line 126
    throw p1

    .line 127
    :cond_2
    return-void
.end method

.method public final getRecyclerView()Landroidx/recyclerview/widget/RecyclerView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->a:LN31/g;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, LN31/g;->getRecyclerView()Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    return-object v0
.end method

.method public final h()Landroid/widget/HorizontalScrollView;
    .locals 4

    .line 1
    new-instance v0, Landroid/widget/HorizontalScrollView;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Landroid/widget/HorizontalScrollView;-><init>(Landroid/content/Context;)V

    .line 8
    .line 9
    .line 10
    new-instance v1, Landroid/widget/FrameLayout$LayoutParams;

    .line 11
    .line 12
    const/4 v2, -0x2

    .line 13
    invoke-direct {v1, v2, v2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 14
    .line 15
    .line 16
    const/16 v2, 0x50

    .line 17
    .line 18
    iput v2, v1, Landroid/widget/FrameLayout$LayoutParams;->gravity:I

    .line 19
    .line 20
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->i:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 21
    .line 22
    sget-object v3, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->RECTANGLE_S_WITH_HEADER:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 23
    .line 24
    if-ne v2, v3, :cond_0

    .line 25
    .line 26
    iget v2, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->o:I

    .line 27
    .line 28
    iput v2, v1, Landroid/widget/FrameLayout$LayoutParams;->topMargin:I

    .line 29
    .line 30
    :cond_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 31
    .line 32
    .line 33
    const/4 v1, 0x0

    .line 34
    invoke-virtual {v0, v1}, Landroid/view/View;->setHorizontalScrollBarEnabled(Z)V

    .line 35
    .line 36
    .line 37
    return-object v0
.end method

.method public final i()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->m:Landroid/widget/LinearLayout;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/view/ViewGroup;->removeAllViews()V

    .line 6
    .line 7
    .line 8
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->l:Landroid/widget/HorizontalScrollView;

    .line 9
    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/view/ViewGroup;->removeAllViews()V

    .line 13
    .line 14
    .line 15
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->l:Landroid/widget/HorizontalScrollView;

    .line 16
    .line 17
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 18
    .line 19
    .line 20
    const/4 v0, 0x0

    .line 21
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->l:Landroid/widget/HorizontalScrollView;

    .line 22
    .line 23
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->m:Landroid/widget/LinearLayout;

    .line 24
    .line 25
    return-void
.end method

.method public final j()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->i()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->a:LN31/g;

    .line 5
    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-interface {v0}, LN31/g;->a()V

    .line 9
    .line 10
    .line 11
    :cond_0
    return-void
.end method

.method public final k()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->i:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain$b;->a:[I

    .line 4
    .line 5
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    aget v0, v1, v0

    .line 10
    .line 11
    const/4 v1, 0x1

    .line 12
    if-eq v0, v1, :cond_2

    .line 13
    .line 14
    const/4 v1, 0x2

    .line 15
    const/16 v2, 0xd

    .line 16
    .line 17
    if-eq v0, v1, :cond_3

    .line 18
    .line 19
    const/4 v1, 0x3

    .line 20
    if-eq v0, v1, :cond_3

    .line 21
    .line 22
    const/4 v1, 0x4

    .line 23
    if-eq v0, v1, :cond_1

    .line 24
    .line 25
    const/4 v1, 0x5

    .line 26
    if-ne v0, v1, :cond_0

    .line 27
    .line 28
    const/16 v2, 0xb

    .line 29
    .line 30
    goto :goto_0

    .line 31
    :cond_0
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 32
    .line 33
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 34
    .line 35
    .line 36
    throw v0

    .line 37
    :cond_1
    const/16 v2, 0xa

    .line 38
    .line 39
    goto :goto_0

    .line 40
    :cond_2
    const/16 v2, 0xc

    .line 41
    .line 42
    :cond_3
    :goto_0
    invoke-virtual {p0, v2}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->c(I)V

    .line 43
    .line 44
    .line 45
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->a:LN31/g;

    .line 46
    .line 47
    if-eqz v0, :cond_4

    .line 48
    .line 49
    invoke-interface {v0}, LN31/g;->d()V

    .line 50
    .line 51
    .line 52
    :cond_4
    return-void
.end method

.method public final setItems(Ljava/util/List;Ljava/lang/Runnable;)V
    .locals 2
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/sport_collection/b$c;",
            ">;",
            "Ljava/lang/Runnable;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->j()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->k:Ljava/util/List;

    .line 5
    .line 6
    invoke-interface {v0}, Ljava/util/List;->clear()V

    .line 7
    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->k:Ljava/util/List;

    .line 10
    .line 11
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 12
    .line 13
    .line 14
    iget v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->h:I

    .line 15
    .line 16
    sget v1, Lm31/f;->SportsCollectionSimple_RectangleS_WithHeader:I

    .line 17
    .line 18
    if-ne v0, v1, :cond_0

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    if-nez v0, :cond_1

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_1
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->k:Ljava/util/List;

    .line 25
    .line 26
    new-instance v0, Lorg/xbet/uikit_sport/sport_collection/b$a;

    .line 27
    .line 28
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->getButtonAllUiModel()LP31/a;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/sport_collection/b$a;-><init>(LP31/a;)V

    .line 33
    .line 34
    .line 35
    const/4 v1, 0x0

    .line 36
    invoke-interface {p1, v1, v0}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 37
    .line 38
    .line 39
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->k:Ljava/util/List;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/uikit_sport/sport_collection/b$b;

    .line 42
    .line 43
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->getButtonFilterUiModel()LP31/b;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/sport_collection/b$b;-><init>(LP31/b;)V

    .line 48
    .line 49
    .line 50
    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 51
    .line 52
    .line 53
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->k:Ljava/util/List;

    .line 54
    .line 55
    :goto_0
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->z1(Ljava/lang/Iterable;)Ljava/util/List;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->a:LN31/g;

    .line 60
    .line 61
    if-eqz v0, :cond_2

    .line 62
    .line 63
    invoke-interface {v0, p1, p2}, LN31/g;->b(Ljava/util/List;Ljava/lang/Runnable;)V

    .line 64
    .line 65
    .line 66
    :cond_2
    return-void
.end method

.method public final setOnButtonClickTypeListener(LOc/n;)V
    .locals 1
    .param p1    # LOc/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LOc/n<",
            "-",
            "Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->j:LOc/n;

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->a:LN31/g;

    .line 4
    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-interface {v0, p1}, LN31/g;->setSportCollectionClickListener(LOc/n;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public final setType(Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;)V
    .locals 12
    .param p1    # Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->i:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 2
    .line 3
    if-ne v0, p1, :cond_0

    .line 4
    .line 5
    goto/16 :goto_2

    .line 6
    .line 7
    :cond_0
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->i:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->a:LN31/g;

    .line 10
    .line 11
    instance-of v1, v0, Landroid/view/View;

    .line 12
    .line 13
    const/4 v2, 0x0

    .line 14
    if-eqz v1, :cond_1

    .line 15
    .line 16
    check-cast v0, Landroid/view/View;

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_1
    move-object v0, v2

    .line 20
    :goto_0
    if-eqz v0, :cond_2

    .line 21
    .line 22
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 23
    .line 24
    .line 25
    :cond_2
    sget-object v0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain$b;->a:[I

    .line 26
    .line 27
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 28
    .line 29
    .line 30
    move-result p1

    .line 31
    aget p1, v0, p1

    .line 32
    .line 33
    const/4 v0, 0x1

    .line 34
    if-eq p1, v0, :cond_7

    .line 35
    .line 36
    const/4 v0, 0x2

    .line 37
    if-eq p1, v0, :cond_6

    .line 38
    .line 39
    const/4 v0, 0x3

    .line 40
    if-eq p1, v0, :cond_5

    .line 41
    .line 42
    const/4 v0, 0x4

    .line 43
    if-eq p1, v0, :cond_4

    .line 44
    .line 45
    const/4 v0, 0x5

    .line 46
    if-ne p1, v0, :cond_3

    .line 47
    .line 48
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    sget v0, LlZ0/g;->space_4:I

    .line 53
    .line 54
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 55
    .line 56
    .line 57
    move-result p1

    .line 58
    iput p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->n:I

    .line 59
    .line 60
    sget p1, Lm31/f;->SportsCollectionSimple_RectangleL:I

    .line 61
    .line 62
    iput p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->h:I

    .line 63
    .line 64
    new-instance v3, Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;

    .line 65
    .line 66
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 67
    .line 68
    .line 69
    move-result-object v4

    .line 70
    const/4 v7, 0x6

    .line 71
    const/4 v8, 0x0

    .line 72
    const/4 v5, 0x0

    .line 73
    const/4 v6, 0x0

    .line 74
    invoke-direct/range {v3 .. v8}, Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 75
    .line 76
    .line 77
    iget p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->h:I

    .line 78
    .line 79
    invoke-virtual {p0, v3, p1}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->f(Landroid/view/View;I)V

    .line 80
    .line 81
    .line 82
    goto/16 :goto_1

    .line 83
    .line 84
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 85
    .line 86
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 87
    .line 88
    .line 89
    throw p1

    .line 90
    :cond_4
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    sget v0, LlZ0/g;->space_4:I

    .line 95
    .line 96
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 97
    .line 98
    .line 99
    move-result p1

    .line 100
    iput p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->n:I

    .line 101
    .line 102
    sget p1, Lm31/f;->SportsCollectionSimple_RectangleM:I

    .line 103
    .line 104
    iput p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->h:I

    .line 105
    .line 106
    new-instance v3, Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;

    .line 107
    .line 108
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 109
    .line 110
    .line 111
    move-result-object v4

    .line 112
    const/4 v7, 0x6

    .line 113
    const/4 v8, 0x0

    .line 114
    const/4 v5, 0x0

    .line 115
    const/4 v6, 0x0

    .line 116
    invoke-direct/range {v3 .. v8}, Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 117
    .line 118
    .line 119
    iget p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->h:I

    .line 120
    .line 121
    invoke-virtual {p0, v3, p1}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->f(Landroid/view/View;I)V

    .line 122
    .line 123
    .line 124
    goto :goto_1

    .line 125
    :cond_5
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 126
    .line 127
    .line 128
    move-result-object p1

    .line 129
    sget v0, LlZ0/g;->space_4:I

    .line 130
    .line 131
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 132
    .line 133
    .line 134
    move-result p1

    .line 135
    iput p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->n:I

    .line 136
    .line 137
    sget p1, Lm31/f;->SportsCollectionSimple_RectangleS_WithHeader:I

    .line 138
    .line 139
    iput p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->h:I

    .line 140
    .line 141
    new-instance v4, Landroid/view/ContextThemeWrapper;

    .line 142
    .line 143
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 144
    .line 145
    .line 146
    move-result-object p1

    .line 147
    sget v0, Lm31/f;->SportCollectionWithHeader:I

    .line 148
    .line 149
    invoke-direct {v4, p1, v0}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    .line 150
    .line 151
    .line 152
    new-instance v3, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;

    .line 153
    .line 154
    iget-object v5, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->c:Ljava/lang/CharSequence;

    .line 155
    .line 156
    iget-object v6, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->d:Ljava/lang/CharSequence;

    .line 157
    .line 158
    iget v7, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->f:I

    .line 159
    .line 160
    const/16 v10, 0x30

    .line 161
    .line 162
    const/4 v11, 0x0

    .line 163
    const/4 v8, 0x0

    .line 164
    const/4 v9, 0x0

    .line 165
    invoke-direct/range {v3 .. v11}, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;-><init>(Landroid/content/Context;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILandroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 166
    .line 167
    .line 168
    iget p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->h:I

    .line 169
    .line 170
    invoke-virtual {p0, v3, p1}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->f(Landroid/view/View;I)V

    .line 171
    .line 172
    .line 173
    goto :goto_1

    .line 174
    :cond_6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 175
    .line 176
    .line 177
    move-result-object p1

    .line 178
    sget v0, LlZ0/g;->space_4:I

    .line 179
    .line 180
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 181
    .line 182
    .line 183
    move-result p1

    .line 184
    iput p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->n:I

    .line 185
    .line 186
    sget p1, Lm31/f;->SportsCollectionSimple_RectangleS:I

    .line 187
    .line 188
    iput p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->h:I

    .line 189
    .line 190
    new-instance v3, Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;

    .line 191
    .line 192
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 193
    .line 194
    .line 195
    move-result-object v4

    .line 196
    const/4 v7, 0x6

    .line 197
    const/4 v8, 0x0

    .line 198
    const/4 v5, 0x0

    .line 199
    const/4 v6, 0x0

    .line 200
    invoke-direct/range {v3 .. v8}, Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 201
    .line 202
    .line 203
    iget p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->h:I

    .line 204
    .line 205
    invoke-virtual {p0, v3, p1}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->f(Landroid/view/View;I)V

    .line 206
    .line 207
    .line 208
    goto :goto_1

    .line 209
    :cond_7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 210
    .line 211
    .line 212
    move-result-object p1

    .line 213
    sget v0, LlZ0/g;->space_8:I

    .line 214
    .line 215
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 216
    .line 217
    .line 218
    move-result p1

    .line 219
    iput p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->n:I

    .line 220
    .line 221
    sget p1, Lm31/f;->SportsCollectionSimple_CircleWithLabel:I

    .line 222
    .line 223
    iput p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->h:I

    .line 224
    .line 225
    new-instance v3, Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;

    .line 226
    .line 227
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 228
    .line 229
    .line 230
    move-result-object v4

    .line 231
    const/4 v7, 0x6

    .line 232
    const/4 v8, 0x0

    .line 233
    const/4 v5, 0x0

    .line 234
    const/4 v6, 0x0

    .line 235
    invoke-direct/range {v3 .. v8}, Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 236
    .line 237
    .line 238
    iget p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->h:I

    .line 239
    .line 240
    invoke-virtual {p0, v3, p1}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->f(Landroid/view/View;I)V

    .line 241
    .line 242
    .line 243
    :goto_1
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->j:LOc/n;

    .line 244
    .line 245
    if-eqz p1, :cond_8

    .line 246
    .line 247
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->a:LN31/g;

    .line 248
    .line 249
    if-eqz v0, :cond_8

    .line 250
    .line 251
    invoke-interface {v0, p1}, LN31/g;->setSportCollectionClickListener(LOc/n;)V

    .line 252
    .line 253
    .line 254
    :cond_8
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->k:Ljava/util/List;

    .line 255
    .line 256
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 257
    .line 258
    .line 259
    move-result p1

    .line 260
    if-nez p1, :cond_9

    .line 261
    .line 262
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->a:LN31/g;

    .line 263
    .line 264
    if-eqz p1, :cond_9

    .line 265
    .line 266
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->k:Ljava/util/List;

    .line 267
    .line 268
    invoke-interface {p1, v0, v2}, LN31/g;->b(Ljava/util/List;Ljava/lang/Runnable;)V

    .line 269
    .line 270
    .line 271
    :cond_9
    iget p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->h:I

    .line 272
    .line 273
    if-eqz p1, :cond_a

    .line 274
    .line 275
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->a:LN31/g;

    .line 276
    .line 277
    if-eqz v0, :cond_a

    .line 278
    .line 279
    invoke-interface {v0, p1}, LN31/g;->setStyle(I)V

    .line 280
    .line 281
    .line 282
    :cond_a
    :goto_2
    return-void
.end method
