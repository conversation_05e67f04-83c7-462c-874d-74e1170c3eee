.class public final Lorg/xbet/games_section/feature/popular/presentation/delegates/GamesCollectionShimmerViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0003\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "()LA4/c;",
        "popular_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/popular/presentation/delegates/GamesCollectionShimmerViewHolderKt;->i(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LU40/f;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/delegates/GamesCollectionShimmerViewHolderKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LU40/f;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/popular/presentation/delegates/GamesCollectionShimmerViewHolderKt;->g(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/popular/presentation/delegates/GamesCollectionShimmerViewHolderKt;->h(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e()LA4/c;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LY40/m;

    .line 2
    .line 3
    invoke-direct {v0}, LY40/m;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LY40/n;

    .line 7
    .line 8
    invoke-direct {v1}, LY40/n;-><init>()V

    .line 9
    .line 10
    .line 11
    new-instance v2, Lorg/xbet/games_section/feature/popular/presentation/delegates/GamesCollectionShimmerViewHolderKt$getGamesCollectionShimmerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {v2}, Lorg/xbet/games_section/feature/popular/presentation/delegates/GamesCollectionShimmerViewHolderKt$getGamesCollectionShimmerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v3, Lorg/xbet/games_section/feature/popular/presentation/delegates/GamesCollectionShimmerViewHolderKt$getGamesCollectionShimmerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/games_section/feature/popular/presentation/delegates/GamesCollectionShimmerViewHolderKt$getGamesCollectionShimmerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v4, LB4/b;

    .line 19
    .line 20
    invoke-direct {v4, v0, v2, v1, v3}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v4
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LU40/f;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LU40/f;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LU40/f;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    sget v1, Lpb/d;->isTablet:I

    .line 10
    .line 11
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getBoolean(I)Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, LU40/f;

    .line 22
    .line 23
    iget-object v0, v0, LU40/f;->d:LU40/g;

    .line 24
    .line 25
    iget-object v0, v0, LU40/g;->b:Landroidx/constraintlayout/widget/Guideline;

    .line 26
    .line 27
    invoke-virtual {v0}, Landroid/view/View;->getId()I

    .line 28
    .line 29
    .line 30
    move-result v0

    .line 31
    goto :goto_0

    .line 32
    :cond_0
    const/4 v0, 0x0

    .line 33
    :goto_0
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    check-cast v1, LU40/f;

    .line 38
    .line 39
    iget-object v1, v1, LU40/f;->d:LU40/g;

    .line 40
    .line 41
    iget-object v1, v1, LU40/g;->c:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 42
    .line 43
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    if-eqz v2, :cond_1

    .line 48
    .line 49
    check-cast v2, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;

    .line 50
    .line 51
    iput v0, v2, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;->v:I

    .line 52
    .line 53
    invoke-virtual {v1, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 54
    .line 55
    .line 56
    new-instance v0, LY40/o;

    .line 57
    .line 58
    invoke-direct {v0, p0}, LY40/o;-><init>(LB4/a;)V

    .line 59
    .line 60
    .line 61
    invoke-virtual {p0, v0}, LB4/a;->r(Lkotlin/jvm/functions/Function0;)V

    .line 62
    .line 63
    .line 64
    new-instance v0, LY40/p;

    .line 65
    .line 66
    invoke-direct {v0, p0}, LY40/p;-><init>(LB4/a;)V

    .line 67
    .line 68
    .line 69
    invoke-virtual {p0, v0}, LB4/a;->s(Lkotlin/jvm/functions/Function0;)V

    .line 70
    .line 71
    .line 72
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 73
    .line 74
    return-object p0

    .line 75
    :cond_1
    new-instance p0, Ljava/lang/NullPointerException;

    .line 76
    .line 77
    const-string v0, "null cannot be cast to non-null type androidx.constraintlayout.widget.ConstraintLayout.LayoutParams"

    .line 78
    .line 79
    invoke-direct {p0, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 80
    .line 81
    .line 82
    throw p0
.end method

.method public static final h(LB4/a;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LU40/f;

    .line 6
    .line 7
    invoke-virtual {v0}, LU40/f;->b()Landroid/widget/LinearLayout;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-static {v0}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    check-cast v0, LU40/f;

    .line 19
    .line 20
    iget-object v0, v0, LU40/f;->b:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;

    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    check-cast v1, Lb50/b;

    .line 27
    .line 28
    invoke-virtual {v1}, Lb50/b;->d()Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->s(Ljava/lang/String;)V

    .line 33
    .line 34
    .line 35
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    check-cast v0, LU40/f;

    .line 40
    .line 41
    iget-object v0, v0, LU40/f;->c:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;

    .line 42
    .line 43
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object p0

    .line 47
    check-cast p0, Lb50/b;

    .line 48
    .line 49
    invoke-virtual {p0}, Lb50/b;->d()Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object p0

    .line 53
    invoke-virtual {v0, p0}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->s(Ljava/lang/String;)V

    .line 54
    .line 55
    .line 56
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 57
    .line 58
    return-object p0
.end method

.method public static final i(LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LU40/f;

    .line 6
    .line 7
    invoke-virtual {v0}, LU40/f;->b()Landroid/widget/LinearLayout;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-static {v0}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    check-cast v0, LU40/f;

    .line 19
    .line 20
    iget-object v0, v0, LU40/f;->b:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;

    .line 21
    .line 22
    invoke-virtual {v0}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->t()V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 26
    .line 27
    .line 28
    move-result-object p0

    .line 29
    check-cast p0, LU40/f;

    .line 30
    .line 31
    iget-object p0, p0, LU40/f;->c:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;

    .line 32
    .line 33
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->t()V

    .line 34
    .line 35
    .line 36
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 37
    .line 38
    return-object p0
.end method
