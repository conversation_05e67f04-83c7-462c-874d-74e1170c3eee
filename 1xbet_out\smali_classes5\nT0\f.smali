.class public final LnT0/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LnT0/c;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006H\u0096\u0001\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0010\u0010\n\u001a\u00020\tH\u0096\u0001\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0010\u0010\r\u001a\u00020\u000cH\u0096\u0001\u00a2\u0006\u0004\u0008\r\u0010\u000eR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\n\u0010\u000f\u00a8\u0006\u0010"
    }
    d2 = {
        "LnT0/f;",
        "LnT0/c;",
        "LnT0/d;",
        "themeSwitchFeatureComponentFactory",
        "<init>",
        "(LnT0/d;)V",
        "LmT0/c;",
        "a",
        "()LmT0/c;",
        "LmT0/a;",
        "b",
        "()LmT0/a;",
        "LmT0/b;",
        "c",
        "()LmT0/b;",
        "LnT0/d;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LnT0/c;

.field public final b:LnT0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LnT0/d;)V
    .locals 1
    .param p1    # LnT0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p1}, LnT0/d;->a()LnT0/c;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iput-object v0, p0, LnT0/f;->a:LnT0/c;

    .line 9
    .line 10
    iput-object p1, p0, LnT0/f;->b:LnT0/d;

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public a()LmT0/c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LnT0/f;->a:LnT0/c;

    .line 2
    .line 3
    invoke-interface {v0}, LlT0/a;->a()LmT0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public b()LmT0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LnT0/f;->a:LnT0/c;

    .line 2
    .line 3
    invoke-interface {v0}, LlT0/a;->b()LmT0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public c()LmT0/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LnT0/f;->a:LnT0/c;

    .line 2
    .line 3
    invoke-interface {v0}, LlT0/a;->c()LmT0/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method
