.class public final synthetic LtL0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LtL0/c;


# direct methods
.method public synthetic constructor <init>(LtL0/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LtL0/b;->a:LtL0/c;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LtL0/b;->a:LtL0/c;

    invoke-static {v0}, LtL0/c;->a(LtL0/c;)LsL0/a;

    move-result-object v0

    return-object v0
.end method
