.class public final Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardFragmentDelegateImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lx40/b;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0006\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u001f\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\r\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\u000f\u00a8\u0006\u0010"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardFragmentDelegateImpl;",
        "Lx40/b;",
        "LzX0/k;",
        "snackbarManager",
        "<init>",
        "(LzX0/k;)V",
        "Landroidx/fragment/app/Fragment;",
        "fragment",
        "Lw40/b;",
        "gameCategoryViewModel",
        "",
        "a",
        "(Landroidx/fragment/app/Fragment;Lw40/b;)V",
        "c",
        "(Landroidx/fragment/app/Fragment;)V",
        "LzX0/k;",
        "impl_games_section_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LzX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LzX0/k;)V
    .locals 0
    .param p1    # LzX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardFragmentDelegateImpl;->a:LzX0/k;

    .line 5
    .line 6
    return-void
.end method

.method public static final synthetic b(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardFragmentDelegateImpl;Landroidx/fragment/app/Fragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardFragmentDelegateImpl;->c(Landroidx/fragment/app/Fragment;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(Landroidx/fragment/app/Fragment;Lw40/b;)V
    .locals 11
    .param p1    # Landroidx/fragment/app/Fragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lw40/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-interface {p2}, Lw40/b;->E2()Lkotlinx/coroutines/flow/e;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    new-instance v4, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardFragmentDelegateImpl$setup$1;

    .line 6
    .line 7
    const/4 p2, 0x0

    .line 8
    invoke-direct {v4, p0, p1, p2}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardFragmentDelegateImpl$setup$1;-><init>(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardFragmentDelegateImpl;Landroidx/fragment/app/Fragment;Lkotlin/coroutines/e;)V

    .line 9
    .line 10
    .line 11
    sget-object v3, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 12
    .line 13
    invoke-static {p1}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    invoke-static {v2}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    new-instance v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardFragmentDelegateImpl$setup$$inlined$observeWithLifecycle$default$1;

    .line 22
    .line 23
    const/4 v5, 0x0

    .line 24
    invoke-direct/range {v0 .. v5}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardFragmentDelegateImpl$setup$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    const/4 v9, 0x3

    .line 28
    const/4 v10, 0x0

    .line 29
    const/4 v6, 0x0

    .line 30
    const/4 v7, 0x0

    .line 31
    move-object v5, p1

    .line 32
    move-object v8, v0

    .line 33
    invoke-static/range {v5 .. v10}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public final c(Landroidx/fragment/app/Fragment;)V
    .locals 12

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardFragmentDelegateImpl;->a:LzX0/k;

    .line 2
    .line 3
    new-instance v1, Ly01/g;

    .line 4
    .line 5
    sget-object v2, Ly01/i$c;->a:Ly01/i$c;

    .line 6
    .line 7
    sget v3, Lpb/k;->get_balance_list_error:I

    .line 8
    .line 9
    invoke-virtual {p1, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    const/16 v8, 0x3c

    .line 14
    .line 15
    const/4 v9, 0x0

    .line 16
    const/4 v4, 0x0

    .line 17
    const/4 v5, 0x0

    .line 18
    const/4 v6, 0x0

    .line 19
    const/4 v7, 0x0

    .line 20
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 21
    .line 22
    .line 23
    const/16 v10, 0x1fc

    .line 24
    .line 25
    const/4 v11, 0x0

    .line 26
    const/4 v3, 0x0

    .line 27
    const/4 v5, 0x0

    .line 28
    const/4 v6, 0x0

    .line 29
    const/4 v8, 0x0

    .line 30
    move-object v2, p1

    .line 31
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 32
    .line 33
    .line 34
    return-void
.end method
