.class public abstract LgP0/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LNN0/h;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LgP0/c$a;,
        LgP0/c$b;,
        LgP0/c$c;,
        LgP0/c$d;,
        LgP0/c$e;,
        LgP0/c$f;,
        LgP0/c$g;,
        LgP0/c$h;,
        LgP0/c$i;,
        LgP0/c$j;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0011\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u00080\u0018\u0000 \u00102\u00020\u0001:\n\u0011\t\u0012\u0013\u0010\u0014\u0015\u000b\u0016\rB#\u0008\u0004\u0012\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0004\u0008\u0007\u0010\u0008R\u001a\u0010\u0003\u001a\u00020\u00028\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\t\u0010\n\u001a\u0004\u0008\u000b\u0010\u000cR\u001a\u0010\u0004\u001a\u00020\u00028\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u000b\u0010\n\u001a\u0004\u0008\r\u0010\u000cR\u001a\u0010\u0006\u001a\u00020\u00058\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\r\u0010\u000e\u001a\u0004\u0008\t\u0010\u000f\u0082\u0001\t\u0017\u0018\u0019\u001a\u001b\u001c\u001d\u001e\u001f\u00a8\u0006 "
    }
    d2 = {
        "LgP0/c;",
        "LNN0/h;",
        "",
        "iconRes",
        "type",
        "",
        "implemented",
        "<init>",
        "(IIZ)V",
        "a",
        "I",
        "b",
        "()I",
        "c",
        "Z",
        "()Z",
        "d",
        "i",
        "f",
        "e",
        "g",
        "h",
        "j",
        "LgP0/c$a;",
        "LgP0/c$b;",
        "LgP0/c$d;",
        "LgP0/c$e;",
        "LgP0/c$f;",
        "LgP0/c$g;",
        "LgP0/c$h;",
        "LgP0/c$i;",
        "LgP0/c$j;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d:LgP0/c$c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:I

.field public final b:I

.field public final c:Z


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LgP0/c$c;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LgP0/c$c;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LgP0/c;->d:LgP0/c$c;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>(IIZ)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput p1, p0, LgP0/c;->a:I

    .line 4
    iput p2, p0, LgP0/c;->b:I

    .line 5
    iput-boolean p3, p0, LgP0/c;->c:Z

    return-void
.end method

.method public synthetic constructor <init>(IIZLkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3}, LgP0/c;-><init>(IIZ)V

    return-void
.end method


# virtual methods
.method public a()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, LgP0/c;->c:Z

    .line 2
    .line 3
    return v0
.end method

.method public b()I
    .locals 1

    .line 1
    iget v0, p0, LgP0/c;->a:I

    .line 2
    .line 3
    return v0
.end method

.method public c()I
    .locals 1

    .line 1
    iget v0, p0, LgP0/c;->b:I

    .line 2
    .line 3
    return v0
.end method
