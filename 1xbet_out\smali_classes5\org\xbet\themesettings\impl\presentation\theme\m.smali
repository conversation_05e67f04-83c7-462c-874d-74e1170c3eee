.class public final Lorg/xbet/themesettings/impl/presentation/theme/m;
.super Landroidx/lifecycle/b0;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/themesettings/impl/presentation/theme/m$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00ae\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0010 \n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008#\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0000\u0018\u0000 p2\u00020\u0001:\u0001qB\u008d\u0001\u0008\u0007\u0012\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0008\u0008\u0001\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u00a2\u0006\u0004\u0008\"\u0010#J\u0013\u0010&\u001a\u0008\u0012\u0004\u0012\u00020%0$\u00a2\u0006\u0004\u0008&\u0010\'J\u000f\u0010)\u001a\u00020(H\u0014\u00a2\u0006\u0004\u0008)\u0010*J\u0015\u0010-\u001a\u00020(2\u0006\u0010,\u001a\u00020+\u00a2\u0006\u0004\u0008-\u0010.J\r\u0010/\u001a\u00020(\u00a2\u0006\u0004\u0008/\u0010*J\r\u00100\u001a\u00020(\u00a2\u0006\u0004\u00080\u0010*J\r\u00101\u001a\u00020(\u00a2\u0006\u0004\u00081\u0010*J\u0015\u00103\u001a\u00020(2\u0006\u00102\u001a\u00020+\u00a2\u0006\u0004\u00083\u0010.J\u0015\u00106\u001a\u00020(2\u0006\u00105\u001a\u000204\u00a2\u0006\u0004\u00086\u00107J\u001d\u00109\u001a\u00020(2\u0006\u0010,\u001a\u00020+2\u0006\u00108\u001a\u00020+\u00a2\u0006\u0004\u00089\u0010:J\r\u0010;\u001a\u00020(\u00a2\u0006\u0004\u0008;\u0010*J\u0017\u0010=\u001a\u00020(2\u0006\u0010<\u001a\u000204H\u0002\u00a2\u0006\u0004\u0008=\u00107J\u0015\u0010?\u001a\u0008\u0012\u0004\u0012\u0002040>H\u0002\u00a2\u0006\u0004\u0008?\u0010@J\'\u0010D\u001a\u00020\u00042\u0006\u0010,\u001a\u00020+2\u0006\u0010B\u001a\u00020A2\u0006\u0010C\u001a\u00020AH\u0002\u00a2\u0006\u0004\u0008D\u0010EJ\u0013\u0010F\u001a\u00020\u0004*\u00020AH\u0002\u00a2\u0006\u0004\u0008F\u0010GJ\u001d\u0010J\u001a\u00020(2\u000c\u0010I\u001a\u0008\u0012\u0004\u0012\u00020(0HH\u0002\u00a2\u0006\u0004\u0008J\u0010KR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008N\u0010OR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010QR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008R\u0010SR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008T\u0010UR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008V\u0010WR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008X\u0010YR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Z\u0010[R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\\\u0010]R\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008^\u0010_R\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008`\u0010aR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008b\u0010cR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008d\u0010eR\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008f\u0010gR\u0014\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008h\u0010iR\u0014\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008j\u0010kR\u001a\u0010o\u001a\u0008\u0012\u0004\u0012\u00020%0l8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008m\u0010n\u00a8\u0006r"
    }
    d2 = {
        "Lorg/xbet/themesettings/impl/presentation/theme/m;",
        "Landroidx/lifecycle/b0;",
        "LwX0/c;",
        "router",
        "",
        "screenName",
        "Lorg/xbet/analytics/domain/scope/h1;",
        "themesAnalytics",
        "Li8/m;",
        "getThemeUseCase",
        "LhT0/c;",
        "convertTo12FormatUseCase",
        "LA7/a;",
        "getCommonConfigUseCase",
        "LhT0/e;",
        "getSelectedThemeUseCase",
        "LhT0/n;",
        "setSelectedThemeUseCase",
        "LhT0/k;",
        "isTimeTableEnabledUseCase",
        "LhT0/p;",
        "setTimeTableEnabledUseCase",
        "LhT0/i;",
        "getTimeTableUseCase",
        "LhT0/g;",
        "getTimeTableTimeModelUseCase",
        "LmT0/b;",
        "updateThemeSwitchStreamUseCase",
        "LjP/g;",
        "isDemoModeUseCase",
        "LjP/c;",
        "getAvailableDemoThemesUseCase",
        "LcS/d;",
        "themeSettingsFatmanLogger",
        "<init>",
        "(LwX0/c;Ljava/lang/String;Lorg/xbet/analytics/domain/scope/h1;Li8/m;LhT0/c;LA7/a;LhT0/e;LhT0/n;LhT0/k;LhT0/p;LhT0/i;LhT0/g;LmT0/b;LjP/g;LjP/c;LcS/d;)V",
        "Lkotlinx/coroutines/flow/f0;",
        "LjT0/a;",
        "r3",
        "()Lkotlinx/coroutines/flow/f0;",
        "",
        "onCleared",
        "()V",
        "",
        "is24HourFormat",
        "u3",
        "(Z)V",
        "v3",
        "t3",
        "w3",
        "selected",
        "B3",
        "Lcom/xbet/onexcore/themes/Theme;",
        "oldTheme",
        "z3",
        "(Lcom/xbet/onexcore/themes/Theme;)V",
        "turnOn",
        "A3",
        "(ZZ)V",
        "onBackPressed",
        "theme",
        "x3",
        "",
        "q3",
        "()Ljava/util/List;",
        "",
        "hour",
        "minute",
        "s3",
        "(ZII)Ljava/lang/String;",
        "D3",
        "(I)Ljava/lang/String;",
        "Lkotlin/Function0;",
        "block",
        "E3",
        "(Lkotlin/jvm/functions/Function0;)V",
        "b1",
        "LwX0/c;",
        "k1",
        "Ljava/lang/String;",
        "v1",
        "Lorg/xbet/analytics/domain/scope/h1;",
        "x1",
        "Li8/m;",
        "y1",
        "LhT0/c;",
        "F1",
        "LA7/a;",
        "H1",
        "LhT0/e;",
        "I1",
        "LhT0/n;",
        "P1",
        "LhT0/k;",
        "S1",
        "LhT0/p;",
        "V1",
        "LhT0/i;",
        "b2",
        "LhT0/g;",
        "v2",
        "LmT0/b;",
        "x2",
        "LjP/g;",
        "y2",
        "LjP/c;",
        "F2",
        "LcS/d;",
        "Lkotlinx/coroutines/flow/V;",
        "H2",
        "Lkotlinx/coroutines/flow/V;",
        "state",
        "I2",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final I2:Lorg/xbet/themesettings/impl/presentation/theme/m$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final F1:LA7/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:LcS/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:LhT0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LjT0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:LhT0/n;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:LhT0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:LhT0/p;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:LhT0/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:LhT0/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k1:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lorg/xbet/analytics/domain/scope/h1;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:LmT0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Li8/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:LjP/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:LhT0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:LjP/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/themesettings/impl/presentation/theme/m$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/themesettings/impl/presentation/theme/m$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/themesettings/impl/presentation/theme/m;->I2:Lorg/xbet/themesettings/impl/presentation/theme/m$a;

    return-void
.end method

.method public constructor <init>(LwX0/c;Ljava/lang/String;Lorg/xbet/analytics/domain/scope/h1;Li8/m;LhT0/c;LA7/a;LhT0/e;LhT0/n;LhT0/k;LhT0/p;LhT0/i;LhT0/g;LmT0/b;LjP/g;LjP/c;LcS/d;)V
    .locals 1
    .param p1    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/analytics/domain/scope/h1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Li8/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LhT0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LA7/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LhT0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LhT0/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LhT0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LhT0/p;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LhT0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LhT0/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # LmT0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LjP/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # LjP/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # LcS/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p16

    .line 2
    .line 3
    invoke-direct {p0}, Landroidx/lifecycle/b0;-><init>()V

    .line 4
    .line 5
    .line 6
    iput-object p1, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->b1:LwX0/c;

    .line 7
    .line 8
    iput-object p2, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->k1:Ljava/lang/String;

    .line 9
    .line 10
    iput-object p3, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->v1:Lorg/xbet/analytics/domain/scope/h1;

    .line 11
    .line 12
    iput-object p4, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->x1:Li8/m;

    .line 13
    .line 14
    iput-object p5, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->y1:LhT0/c;

    .line 15
    .line 16
    iput-object p6, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->F1:LA7/a;

    .line 17
    .line 18
    iput-object p7, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->H1:LhT0/e;

    .line 19
    .line 20
    iput-object p8, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->I1:LhT0/n;

    .line 21
    .line 22
    iput-object p9, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->P1:LhT0/k;

    .line 23
    .line 24
    iput-object p10, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->S1:LhT0/p;

    .line 25
    .line 26
    iput-object p11, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->V1:LhT0/i;

    .line 27
    .line 28
    iput-object p12, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->b2:LhT0/g;

    .line 29
    .line 30
    iput-object p13, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->v2:LmT0/b;

    .line 31
    .line 32
    iput-object p14, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->x2:LjP/g;

    .line 33
    .line 34
    move-object/from16 p1, p15

    .line 35
    .line 36
    iput-object p1, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->y2:LjP/c;

    .line 37
    .line 38
    iput-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->F2:LcS/d;

    .line 39
    .line 40
    sget-object p1, LjT0/a;->g:LjT0/a$a;

    .line 41
    .line 42
    invoke-virtual {p1}, LjT0/a$a;->a()LjT0/a;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    iput-object p1, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->H2:Lkotlinx/coroutines/flow/V;

    .line 51
    .line 52
    invoke-virtual {p7}, LhT0/e;->a()Lcom/xbet/onexcore/themes/Theme;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    invoke-virtual {p1}, Ljava/lang/Enum;->name()Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    sget-object p2, Ljava/util/Locale;->ROOT:Ljava/util/Locale;

    .line 61
    .line 62
    invoke-virtual {p1, p2}, Ljava/lang/String;->toLowerCase(Ljava/util/Locale;)Ljava/lang/String;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    invoke-virtual {p9}, LhT0/k;->a()Z

    .line 67
    .line 68
    .line 69
    move-result p2

    .line 70
    invoke-virtual {p3, p1}, Lorg/xbet/analytics/domain/scope/h1;->d(Ljava/lang/String;)V

    .line 71
    .line 72
    .line 73
    invoke-virtual {p3, p2}, Lorg/xbet/analytics/domain/scope/h1;->e(Z)V

    .line 74
    .line 75
    .line 76
    invoke-interface {v0, p1, p2}, LcS/d;->b(Ljava/lang/String;Z)V

    .line 77
    .line 78
    .line 79
    return-void
.end method

.method public static final C3(Lorg/xbet/themesettings/impl/presentation/theme/m;Z)Lkotlin/Unit;
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->S1:LhT0/p;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LhT0/p;->a(Z)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->v1:Lorg/xbet/analytics/domain/scope/h1;

    .line 7
    .line 8
    invoke-virtual {v0, p1}, Lorg/xbet/analytics/domain/scope/h1;->b(Z)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->F2:LcS/d;

    .line 12
    .line 13
    invoke-interface {v0, p1}, LcS/d;->c(Z)V

    .line 14
    .line 15
    .line 16
    iget-object p0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->H2:Lkotlinx/coroutines/flow/V;

    .line 17
    .line 18
    :goto_0
    invoke-interface {p0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    move-object v1, v0

    .line 23
    check-cast v1, LjT0/a;

    .line 24
    .line 25
    const/16 v8, 0x3b

    .line 26
    .line 27
    const/4 v9, 0x0

    .line 28
    const/4 v2, 0x0

    .line 29
    const/4 v3, 0x0

    .line 30
    const/4 v5, 0x0

    .line 31
    const/4 v6, 0x0

    .line 32
    const/4 v7, 0x0

    .line 33
    move v4, p1

    .line 34
    invoke-static/range {v1 .. v9}, LjT0/a;->b(LjT0/a;Ljava/util/List;Lcom/xbet/onexcore/themes/Theme;ZZLjava/lang/String;Ljava/lang/String;ILjava/lang/Object;)LjT0/a;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    invoke-interface {p0, v0, p1}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 39
    .line 40
    .line 41
    move-result p1

    .line 42
    if-eqz p1, :cond_0

    .line 43
    .line 44
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 45
    .line 46
    return-object p0

    .line 47
    :cond_0
    move p1, v4

    .line 48
    goto :goto_0
.end method

.method public static synthetic o3(Lorg/xbet/themesettings/impl/presentation/theme/m;Z)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/themesettings/impl/presentation/theme/m;->C3(Lorg/xbet/themesettings/impl/presentation/theme/m;Z)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic p3(Lorg/xbet/themesettings/impl/presentation/theme/m;Lcom/xbet/onexcore/themes/Theme;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/themesettings/impl/presentation/theme/m;->y3(Lorg/xbet/themesettings/impl/presentation/theme/m;Lcom/xbet/onexcore/themes/Theme;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final y3(Lorg/xbet/themesettings/impl/presentation/theme/m;Lcom/xbet/onexcore/themes/Theme;)Lkotlin/Unit;
    .locals 11

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->I1:LhT0/n;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LhT0/n;->a(Lcom/xbet/onexcore/themes/Theme;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->v1:Lorg/xbet/analytics/domain/scope/h1;

    .line 7
    .line 8
    invoke-virtual {p1}, Ljava/lang/Enum;->name()Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v0, v1}, Lorg/xbet/analytics/domain/scope/h1;->a(Ljava/lang/String;)V

    .line 13
    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->F2:LcS/d;

    .line 16
    .line 17
    invoke-virtual {p1}, Ljava/lang/Enum;->name()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    sget-object v2, Ljava/util/Locale;->ROOT:Ljava/util/Locale;

    .line 22
    .line 23
    invoke-virtual {v1, v2}, Ljava/lang/String;->toLowerCase(Ljava/util/Locale;)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-interface {v0, v1}, LcS/d;->d(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->H2:Lkotlinx/coroutines/flow/V;

    .line 31
    .line 32
    :goto_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    move-object v2, v1

    .line 37
    check-cast v2, LjT0/a;

    .line 38
    .line 39
    iget-object v3, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->P1:LhT0/k;

    .line 40
    .line 41
    invoke-virtual {v3}, LhT0/k;->a()Z

    .line 42
    .line 43
    .line 44
    move-result v3

    .line 45
    if-eqz v3, :cond_0

    .line 46
    .line 47
    sget-object v3, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 48
    .line 49
    invoke-virtual {v3, p1}, Lcom/xbet/onexcore/themes/Theme$a;->e(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 50
    .line 51
    .line 52
    move-result v3

    .line 53
    if-eqz v3, :cond_0

    .line 54
    .line 55
    const/4 v3, 0x1

    .line 56
    const/4 v5, 0x1

    .line 57
    goto :goto_1

    .line 58
    :cond_0
    const/4 v3, 0x0

    .line 59
    const/4 v5, 0x0

    .line 60
    :goto_1
    sget-object v3, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 61
    .line 62
    invoke-virtual {v3, p1}, Lcom/xbet/onexcore/themes/Theme$a;->e(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 63
    .line 64
    .line 65
    move-result v6

    .line 66
    const/16 v9, 0x31

    .line 67
    .line 68
    const/4 v10, 0x0

    .line 69
    const/4 v3, 0x0

    .line 70
    const/4 v7, 0x0

    .line 71
    const/4 v8, 0x0

    .line 72
    move-object v4, p1

    .line 73
    invoke-static/range {v2 .. v10}, LjT0/a;->b(LjT0/a;Ljava/util/List;Lcom/xbet/onexcore/themes/Theme;ZZLjava/lang/String;Ljava/lang/String;ILjava/lang/Object;)LjT0/a;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    invoke-interface {v0, v1, p1}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 78
    .line 79
    .line 80
    move-result p1

    .line 81
    if-eqz p1, :cond_1

    .line 82
    .line 83
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 84
    .line 85
    return-object p0

    .line 86
    :cond_1
    move-object p1, v4

    .line 87
    goto :goto_0
.end method


# virtual methods
.method public final A3(ZZ)V
    .locals 11

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->b2:LhT0/g;

    .line 2
    .line 3
    invoke-virtual {v0, p2}, LhT0/g;->a(Z)LfT0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, LfT0/a;->a()I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    invoke-virtual {v0}, LfT0/a;->b()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    invoke-virtual {p0, p1, v1, v0}, Lorg/xbet/themesettings/impl/presentation/theme/m;->s3(ZII)Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v7

    .line 19
    iget-object p1, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->H2:Lkotlinx/coroutines/flow/V;

    .line 20
    .line 21
    :cond_0
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    move-object v2, v0

    .line 26
    check-cast v2, LjT0/a;

    .line 27
    .line 28
    if-eqz p2, :cond_1

    .line 29
    .line 30
    const/16 v9, 0x2f

    .line 31
    .line 32
    const/4 v10, 0x0

    .line 33
    const/4 v3, 0x0

    .line 34
    const/4 v4, 0x0

    .line 35
    const/4 v5, 0x0

    .line 36
    const/4 v6, 0x0

    .line 37
    const/4 v8, 0x0

    .line 38
    invoke-static/range {v2 .. v10}, LjT0/a;->b(LjT0/a;Ljava/util/List;Lcom/xbet/onexcore/themes/Theme;ZZLjava/lang/String;Ljava/lang/String;ILjava/lang/Object;)LjT0/a;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    goto :goto_0

    .line 43
    :cond_1
    const/16 v9, 0x1f

    .line 44
    .line 45
    const/4 v10, 0x0

    .line 46
    const/4 v3, 0x0

    .line 47
    const/4 v4, 0x0

    .line 48
    const/4 v5, 0x0

    .line 49
    const/4 v6, 0x0

    .line 50
    move-object v8, v7

    .line 51
    const/4 v7, 0x0

    .line 52
    invoke-static/range {v2 .. v10}, LjT0/a;->b(LjT0/a;Ljava/util/List;Lcom/xbet/onexcore/themes/Theme;ZZLjava/lang/String;Ljava/lang/String;ILjava/lang/Object;)LjT0/a;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    move-object v7, v8

    .line 57
    :goto_0
    invoke-interface {p1, v0, v1}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 58
    .line 59
    .line 60
    move-result v0

    .line 61
    if-eqz v0, :cond_0

    .line 62
    .line 63
    return-void
.end method

.method public final B3(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->P1:LhT0/k;

    .line 2
    .line 3
    invoke-virtual {v0}, LhT0/k;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eq v0, p1, :cond_0

    .line 8
    .line 9
    new-instance v0, Lorg/xbet/themesettings/impl/presentation/theme/l;

    .line 10
    .line 11
    invoke-direct {v0, p0, p1}, Lorg/xbet/themesettings/impl/presentation/theme/l;-><init>(Lorg/xbet/themesettings/impl/presentation/theme/m;Z)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0, v0}, Lorg/xbet/themesettings/impl/presentation/theme/m;->E3(Lkotlin/jvm/functions/Function0;)V

    .line 15
    .line 16
    .line 17
    :cond_0
    return-void
.end method

.method public final D3(I)Ljava/lang/String;
    .locals 2

    .line 1
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    const/4 v0, 0x2

    .line 6
    const/16 v1, 0x30

    .line 7
    .line 8
    invoke-static {p1, v0, v1}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    return-object p1
.end method

.method public final E3(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->x1:Li8/m;

    .line 2
    .line 3
    invoke-interface {v0}, Li8/m;->invoke()Lcom/xbet/onexcore/themes/Theme;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-interface {p1}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    iget-object p1, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->x1:Li8/m;

    .line 11
    .line 12
    invoke-interface {p1}, Li8/m;->invoke()Lcom/xbet/onexcore/themes/Theme;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    if-eq p1, v0, :cond_0

    .line 17
    .line 18
    iget-object p1, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->v2:LmT0/b;

    .line 19
    .line 20
    invoke-interface {p1, v0}, LmT0/b;->a(Lcom/xbet/onexcore/themes/Theme;)V

    .line 21
    .line 22
    .line 23
    :cond_0
    return-void
.end method

.method public final onBackPressed()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->F2:LcS/d;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->k1:Ljava/lang/String;

    .line 4
    .line 5
    invoke-interface {v0, v1}, LcS/d;->a(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->b1:LwX0/c;

    .line 9
    .line 10
    invoke-virtual {v0}, LwX0/c;->h()V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public onCleared()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->v1:Lorg/xbet/analytics/domain/scope/h1;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/analytics/domain/scope/h1;->c()V

    .line 4
    .line 5
    .line 6
    invoke-super {p0}, Landroidx/lifecycle/b0;->onCleared()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final q3()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/xbet/onexcore/themes/Theme;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->x2:LjP/g;

    .line 2
    .line 3
    invoke-interface {v0}, LjP/g;->invoke()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->F1:LA7/a;

    .line 10
    .line 11
    invoke-virtual {v0}, LA7/a;->a()Lv7/b;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-virtual {v0}, Lv7/b;->b()Ljava/util/List;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    return-object v0

    .line 20
    :cond_0
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->y2:LjP/c;

    .line 21
    .line 22
    invoke-interface {v0}, LjP/c;->invoke()Ljava/util/List;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    return-object v0
.end method

.method public final r3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "LjT0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->H2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final s3(ZII)Ljava/lang/String;
    .locals 2

    .line 1
    const-string v0, ":"

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0, p2}, Lorg/xbet/themesettings/impl/presentation/theme/m;->D3(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p0, p3}, Lorg/xbet/themesettings/impl/presentation/theme/m;->D3(I)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p2

    .line 13
    new-instance p3, Ljava/lang/StringBuilder;

    .line 14
    .line 15
    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 19
    .line 20
    .line 21
    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 22
    .line 23
    .line 24
    invoke-virtual {p3, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 25
    .line 26
    .line 27
    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    return-object p1

    .line 32
    :cond_0
    iget-object p1, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->y1:LhT0/c;

    .line 33
    .line 34
    invoke-virtual {p1, p2, p3}, LhT0/c;->a(II)LfT0/a;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    invoke-virtual {p1}, LfT0/a;->a()I

    .line 39
    .line 40
    .line 41
    move-result p2

    .line 42
    invoke-virtual {p0, p2}, Lorg/xbet/themesettings/impl/presentation/theme/m;->D3(I)Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object p2

    .line 46
    invoke-virtual {p1}, LfT0/a;->b()I

    .line 47
    .line 48
    .line 49
    move-result p3

    .line 50
    invoke-virtual {p0, p3}, Lorg/xbet/themesettings/impl/presentation/theme/m;->D3(I)Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object p3

    .line 54
    invoke-virtual {p1}, LfT0/a;->c()Lorg/xbet/themesettings/impl/domain/model/TimeFrame;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    new-instance v1, Ljava/lang/StringBuilder;

    .line 59
    .line 60
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 61
    .line 62
    .line 63
    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 64
    .line 65
    .line 66
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 67
    .line 68
    .line 69
    invoke-virtual {v1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 70
    .line 71
    .line 72
    const-string p2, " "

    .line 73
    .line 74
    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 75
    .line 76
    .line 77
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 78
    .line 79
    .line 80
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    return-object p1
.end method

.method public final t3()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->H2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LjT0/a;

    .line 8
    .line 9
    invoke-virtual {v0}, LjT0/a;->c()Ljava/util/List;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    if-eqz v1, :cond_1

    .line 22
    .line 23
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    move-object v2, v1

    .line 28
    check-cast v2, Lcom/xbet/onexcore/themes/Theme;

    .line 29
    .line 30
    sget-object v3, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 31
    .line 32
    invoke-virtual {v3, v2}, Lcom/xbet/onexcore/themes/Theme$a;->b(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 33
    .line 34
    .line 35
    move-result v2

    .line 36
    if-eqz v2, :cond_0

    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_1
    const/4 v1, 0x0

    .line 40
    :goto_0
    check-cast v1, Lcom/xbet/onexcore/themes/Theme;

    .line 41
    .line 42
    if-nez v1, :cond_2

    .line 43
    .line 44
    return-void

    .line 45
    :cond_2
    invoke-virtual {p0, v1}, Lorg/xbet/themesettings/impl/presentation/theme/m;->x3(Lcom/xbet/onexcore/themes/Theme;)V

    .line 46
    .line 47
    .line 48
    return-void
.end method

.method public final u3(Z)V
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->V1:LhT0/i;

    .line 2
    .line 3
    invoke-virtual {v0}, LhT0/i;->a()LfT0/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->H2:Lkotlinx/coroutines/flow/V;

    .line 8
    .line 9
    new-instance v2, LjT0/a;

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/theme/m;->q3()Ljava/util/List;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    iget-object v4, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->H1:LhT0/e;

    .line 16
    .line 17
    invoke-virtual {v4}, LhT0/e;->a()Lcom/xbet/onexcore/themes/Theme;

    .line 18
    .line 19
    .line 20
    move-result-object v4

    .line 21
    iget-object v5, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->P1:LhT0/k;

    .line 22
    .line 23
    invoke-virtual {v5}, LhT0/k;->a()Z

    .line 24
    .line 25
    .line 26
    move-result v5

    .line 27
    sget-object v6, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 28
    .line 29
    iget-object v7, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->H1:LhT0/e;

    .line 30
    .line 31
    invoke-virtual {v7}, LhT0/e;->a()Lcom/xbet/onexcore/themes/Theme;

    .line 32
    .line 33
    .line 34
    move-result-object v7

    .line 35
    invoke-virtual {v6, v7}, Lcom/xbet/onexcore/themes/Theme$a;->e(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 36
    .line 37
    .line 38
    move-result v6

    .line 39
    invoke-virtual {v0}, LfT0/b;->c()I

    .line 40
    .line 41
    .line 42
    move-result v7

    .line 43
    invoke-virtual {v0}, LfT0/b;->d()I

    .line 44
    .line 45
    .line 46
    move-result v8

    .line 47
    invoke-virtual {p0, p1, v7, v8}, Lorg/xbet/themesettings/impl/presentation/theme/m;->s3(ZII)Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object v7

    .line 51
    invoke-virtual {v0}, LfT0/b;->a()I

    .line 52
    .line 53
    .line 54
    move-result v8

    .line 55
    invoke-virtual {v0}, LfT0/b;->b()I

    .line 56
    .line 57
    .line 58
    move-result v0

    .line 59
    invoke-virtual {p0, p1, v8, v0}, Lorg/xbet/themesettings/impl/presentation/theme/m;->s3(ZII)Ljava/lang/String;

    .line 60
    .line 61
    .line 62
    move-result-object v8

    .line 63
    invoke-direct/range {v2 .. v8}, LjT0/a;-><init>(Ljava/util/List;Lcom/xbet/onexcore/themes/Theme;ZZLjava/lang/String;Ljava/lang/String;)V

    .line 64
    .line 65
    .line 66
    invoke-interface {v1, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 67
    .line 68
    .line 69
    return-void
.end method

.method public final v3()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->H2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LjT0/a;

    .line 8
    .line 9
    invoke-virtual {v0}, LjT0/a;->c()Ljava/util/List;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    if-eqz v1, :cond_1

    .line 22
    .line 23
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    move-object v2, v1

    .line 28
    check-cast v2, Lcom/xbet/onexcore/themes/Theme;

    .line 29
    .line 30
    sget-object v3, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 31
    .line 32
    invoke-virtual {v3, v2}, Lcom/xbet/onexcore/themes/Theme$a;->c(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 33
    .line 34
    .line 35
    move-result v2

    .line 36
    if-eqz v2, :cond_0

    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_1
    const/4 v1, 0x0

    .line 40
    :goto_0
    check-cast v1, Lcom/xbet/onexcore/themes/Theme;

    .line 41
    .line 42
    if-nez v1, :cond_2

    .line 43
    .line 44
    return-void

    .line 45
    :cond_2
    invoke-virtual {p0, v1}, Lorg/xbet/themesettings/impl/presentation/theme/m;->x3(Lcom/xbet/onexcore/themes/Theme;)V

    .line 46
    .line 47
    .line 48
    return-void
.end method

.method public final w3()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->H2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LjT0/a;

    .line 8
    .line 9
    invoke-virtual {v0}, LjT0/a;->c()Ljava/util/List;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    if-eqz v1, :cond_1

    .line 22
    .line 23
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    move-object v2, v1

    .line 28
    check-cast v2, Lcom/xbet/onexcore/themes/Theme;

    .line 29
    .line 30
    sget-object v3, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 31
    .line 32
    invoke-virtual {v3, v2}, Lcom/xbet/onexcore/themes/Theme$a;->d(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 33
    .line 34
    .line 35
    move-result v2

    .line 36
    if-eqz v2, :cond_0

    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_1
    const/4 v1, 0x0

    .line 40
    :goto_0
    check-cast v1, Lcom/xbet/onexcore/themes/Theme;

    .line 41
    .line 42
    if-nez v1, :cond_2

    .line 43
    .line 44
    return-void

    .line 45
    :cond_2
    invoke-virtual {p0, v1}, Lorg/xbet/themesettings/impl/presentation/theme/m;->x3(Lcom/xbet/onexcore/themes/Theme;)V

    .line 46
    .line 47
    .line 48
    return-void
.end method

.method public final x3(Lcom/xbet/onexcore/themes/Theme;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->H1:LhT0/e;

    .line 2
    .line 3
    invoke-virtual {v0}, LhT0/e;->a()Lcom/xbet/onexcore/themes/Theme;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eq v0, p1, :cond_0

    .line 8
    .line 9
    new-instance v0, Lorg/xbet/themesettings/impl/presentation/theme/k;

    .line 10
    .line 11
    invoke-direct {v0, p0, p1}, Lorg/xbet/themesettings/impl/presentation/theme/k;-><init>(Lorg/xbet/themesettings/impl/presentation/theme/m;Lcom/xbet/onexcore/themes/Theme;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0, v0}, Lorg/xbet/themesettings/impl/presentation/theme/m;->E3(Lkotlin/jvm/functions/Function0;)V

    .line 15
    .line 16
    .line 17
    :cond_0
    return-void
.end method

.method public final z3(Lcom/xbet/onexcore/themes/Theme;)V
    .locals 1
    .param p1    # Lcom/xbet/onexcore/themes/Theme;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/m;->v2:LmT0/b;

    .line 2
    .line 3
    invoke-interface {v0, p1}, LmT0/b;->a(Lcom/xbet/onexcore/themes/Theme;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
