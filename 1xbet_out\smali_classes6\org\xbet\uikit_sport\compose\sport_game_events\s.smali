.class public final synthetic Lorg/xbet/uikit_sport/compose/sport_game_events/s;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Ljava/lang/String;

.field public final synthetic b:Landroidx/compose/ui/l;

.field public final synthetic c:I

.field public final synthetic d:I


# direct methods
.method public synthetic constructor <init>(Ljava/lang/String;Landroidx/compose/ui/l;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/s;->a:Ljava/lang/String;

    iput-object p2, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/s;->b:Landroidx/compose/ui/l;

    iput p3, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/s;->c:I

    iput p4, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/s;->d:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/s;->a:Ljava/lang/String;

    iget-object v1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/s;->b:Landroidx/compose/ui/l;

    iget v2, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/s;->c:I

    iget v3, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/s;->d:I

    move-object v4, p1

    check-cast v4, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v5

    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit_sport/compose/sport_game_events/t;->a(Ljava/lang/String;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
