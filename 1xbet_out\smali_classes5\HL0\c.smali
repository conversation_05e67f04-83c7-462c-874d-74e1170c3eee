.class public final LHL0/c;
.super Ljava/lang/Object;


# static fields
.field public static fragment_stadium:I = 0x7f0d0499

.field public static fragment_stadium_shimmer_list:I = 0x7f0d049b

.field public static vh_stadium_image:I = 0x7f0d0acd

.field public static vh_stadium_info:I = 0x7f0d0ace

.field public static vh_stadium_main:I = 0x7f0d0acf

.field public static vh_track_circles:I = 0x7f0d0ade

.field public static vh_track_info:I = 0x7f0d0adf

.field public static vh_track_main:I = 0x7f0d0ae0


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
