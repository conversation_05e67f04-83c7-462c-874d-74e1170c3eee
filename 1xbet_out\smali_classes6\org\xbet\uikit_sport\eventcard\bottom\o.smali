.class public final synthetic Lorg/xbet/uikit_sport/eventcard/bottom/o;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

.field public final synthetic b:I

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_sport/eventcard/bottom/p;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/o;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    iput p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/o;->b:I

    iput p3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/o;->c:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/o;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/o;->b:I

    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/o;->c:I

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, v2, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->a(Lorg/xbet/uikit_sport/eventcard/bottom/p;IILandroid/view/View;)Z

    move-result p1

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method
