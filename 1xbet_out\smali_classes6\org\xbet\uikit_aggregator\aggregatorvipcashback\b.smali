.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0012\u0008\u0001\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0015\u0010\t\u001a\u00020\u00082\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\t\u0010\nR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\u000cR\u001b\u0010\u0011\u001a\u00020\u00088BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\r\u0010\u000e\u001a\u0004\u0008\u000f\u0010\u0010R\u001b\u0010\u0014\u001a\u00020\u00088BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0012\u0010\u000e\u001a\u0004\u0008\u0013\u0010\u0010R\u001b\u0010\u0017\u001a\u00020\u00088BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0015\u0010\u000e\u001a\u0004\u0008\u0016\u0010\u0010R\u001b\u0010\u0019\u001a\u00020\u00088BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\t\u0010\u000e\u001a\u0004\u0008\u0018\u0010\u0010\u00a8\u0006\u001a"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;",
        "",
        "Landroid/content/Context;",
        "context",
        "<init>",
        "(Landroid/content/Context;)V",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;",
        "type",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "e",
        "(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;)Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "a",
        "Landroid/content/Context;",
        "b",
        "Lkotlin/j;",
        "i",
        "()Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "shimmerTypeStatus",
        "c",
        "h",
        "shimmerTypeRectangleVertical",
        "d",
        "g",
        "shimmerTypeRectangleHorizontal",
        "f",
        "shimmerTypeRectangleArrow",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Landroid/content/Context;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->a:Landroid/content/Context;

    .line 5
    .line 6
    new-instance p1, Lg31/d;

    .line 7
    .line 8
    invoke-direct {p1, p0}, Lg31/d;-><init>(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)V

    .line 9
    .line 10
    .line 11
    invoke-static {p1}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->b:Lkotlin/j;

    .line 16
    .line 17
    new-instance p1, Lg31/e;

    .line 18
    .line 19
    invoke-direct {p1, p0}, Lg31/e;-><init>(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)V

    .line 20
    .line 21
    .line 22
    invoke-static {p1}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->c:Lkotlin/j;

    .line 27
    .line 28
    new-instance p1, Lg31/f;

    .line 29
    .line 30
    invoke-direct {p1, p0}, Lg31/f;-><init>(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)V

    .line 31
    .line 32
    .line 33
    invoke-static {p1}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->d:Lkotlin/j;

    .line 38
    .line 39
    new-instance p1, Lg31/g;

    .line 40
    .line 41
    invoke-direct {p1, p0}, Lg31/g;-><init>(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p1}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->e:Lkotlin/j;

    .line 49
    .line 50
    return-void
.end method

.method public static synthetic a(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->k(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)Lorg/xbet/uikit/components/shimmer/ShimmerView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->j(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)Lorg/xbet/uikit/components/shimmer/ShimmerView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->l(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)Lorg/xbet/uikit/components/shimmer/ShimmerView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->m(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)Lorg/xbet/uikit/components/shimmer/ShimmerView;

    move-result-object p0

    return-object p0
.end method

.method public static final j(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->a:Landroid/content/Context;

    .line 4
    .line 5
    const/4 v4, 0x6

    .line 6
    const/4 v5, 0x0

    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/shimmer/ShimmerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    invoke-virtual {p0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 17
    .line 18
    .line 19
    move-result-object p0

    .line 20
    sget v1, LlZ0/g;->radius_full:I

    .line 21
    .line 22
    invoke-virtual {p0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 23
    .line 24
    .line 25
    move-result p0

    .line 26
    invoke-static {v0, p0}, Lorg/xbet/uikit/utils/S;->m(Landroid/view/View;I)V

    .line 27
    .line 28
    .line 29
    new-instance p0, Landroid/widget/LinearLayout$LayoutParams;

    .line 30
    .line 31
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    sget v2, LlZ0/g;->size_80:I

    .line 40
    .line 41
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 42
    .line 43
    .line 44
    move-result v1

    .line 45
    const/4 v2, -0x1

    .line 46
    invoke-direct {p0, v2, v1}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 50
    .line 51
    .line 52
    return-object v0
.end method

.method public static final k(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->a:Landroid/content/Context;

    .line 4
    .line 5
    const/4 v4, 0x6

    .line 6
    const/4 v5, 0x0

    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/shimmer/ShimmerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    invoke-virtual {p0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 17
    .line 18
    .line 19
    move-result-object p0

    .line 20
    sget v1, LlZ0/g;->radius_16:I

    .line 21
    .line 22
    invoke-virtual {p0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 23
    .line 24
    .line 25
    move-result p0

    .line 26
    invoke-static {v0, p0}, Lorg/xbet/uikit/utils/S;->m(Landroid/view/View;I)V

    .line 27
    .line 28
    .line 29
    new-instance p0, Landroid/widget/LinearLayout$LayoutParams;

    .line 30
    .line 31
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    sget v2, LlZ0/g;->size_152:I

    .line 40
    .line 41
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 42
    .line 43
    .line 44
    move-result v1

    .line 45
    const/4 v2, -0x1

    .line 46
    invoke-direct {p0, v2, v1}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 50
    .line 51
    .line 52
    return-object v0
.end method

.method public static final l(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->a:Landroid/content/Context;

    .line 4
    .line 5
    const/4 v4, 0x6

    .line 6
    const/4 v5, 0x0

    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/shimmer/ShimmerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    invoke-virtual {p0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 17
    .line 18
    .line 19
    move-result-object p0

    .line 20
    sget v1, LlZ0/g;->radius_16:I

    .line 21
    .line 22
    invoke-virtual {p0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 23
    .line 24
    .line 25
    move-result p0

    .line 26
    invoke-static {v0, p0}, Lorg/xbet/uikit/utils/S;->m(Landroid/view/View;I)V

    .line 27
    .line 28
    .line 29
    new-instance p0, Landroid/widget/LinearLayout$LayoutParams;

    .line 30
    .line 31
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    sget v2, LlZ0/g;->size_210:I

    .line 40
    .line 41
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 42
    .line 43
    .line 44
    move-result v1

    .line 45
    const/4 v2, -0x1

    .line 46
    invoke-direct {p0, v2, v1}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 50
    .line 51
    .line 52
    return-object v0
.end method

.method public static final m(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;)Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->a:Landroid/content/Context;

    .line 4
    .line 5
    const/4 v4, 0x6

    .line 6
    const/4 v5, 0x0

    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x0

    .line 9
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/shimmer/ShimmerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    invoke-virtual {p0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 17
    .line 18
    .line 19
    move-result-object p0

    .line 20
    sget v1, LlZ0/g;->radius_16:I

    .line 21
    .line 22
    invoke-virtual {p0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 23
    .line 24
    .line 25
    move-result p0

    .line 26
    invoke-static {v0, p0}, Lorg/xbet/uikit/utils/S;->m(Landroid/view/View;I)V

    .line 27
    .line 28
    .line 29
    new-instance p0, Landroid/widget/LinearLayout$LayoutParams;

    .line 30
    .line 31
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    sget v2, LlZ0/g;->size_118:I

    .line 40
    .line 41
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 42
    .line 43
    .line 44
    move-result v1

    .line 45
    const/4 v2, -0x1

    .line 46
    invoke-direct {p0, v2, v1}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 50
    .line 51
    .line 52
    return-object v0
.end method


# virtual methods
.method public final e(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;)Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 1
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p1, v0, :cond_3

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p1, v0, :cond_2

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-eq p1, v0, :cond_1

    .line 17
    .line 18
    const/4 v0, 0x4

    .line 19
    if-ne p1, v0, :cond_0

    .line 20
    .line 21
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->f()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    return-object p1

    .line 26
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 27
    .line 28
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 29
    .line 30
    .line 31
    throw p1

    .line 32
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->g()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    return-object p1

    .line 37
    :cond_2
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->h()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    return-object p1

    .line 42
    :cond_3
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->i()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    return-object p1
.end method

.method public final f()Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->e:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 8
    .line 9
    return-object v0
.end method

.method public final g()Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->d:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 8
    .line 9
    return-object v0
.end method

.method public final h()Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->c:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 8
    .line 9
    return-object v0
.end method

.method public final i()Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->b:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 8
    .line 9
    return-object v0
.end method
