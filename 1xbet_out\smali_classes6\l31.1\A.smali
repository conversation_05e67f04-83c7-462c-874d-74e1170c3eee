.class public final Ll31/A;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LL2/a;


# instance fields
.field public final A:Landroidx/constraintlayout/helper/widget/Flow;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final B:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final C:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final a:Landroid/view/View;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final b:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final c:Landroid/view/View;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/uikit/components/buttons/DSButton;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final e:Landroidx/constraintlayout/helper/widget/Flow;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final f:Landroidx/appcompat/widget/AppCompatImageView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final g:Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TickerDividerHorizontalView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final h:Landroidx/constraintlayout/helper/widget/Flow;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final i:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final j:Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final k:Landroidx/constraintlayout/helper/widget/Flow;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final l:Lorg/xbet/uikit/components/views/LoadableImageView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final m:Landroid/widget/ProgressBar;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final n:Landroidx/constraintlayout/helper/widget/Flow;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final o:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final p:Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final q:Lorg/xbet/uikit/components/separator/Separator;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final r:Lorg/xbet/uikit/components/separator/Separator;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final s:Lorg/xbet/uikit/components/tag/Tag;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final t:Landroid/view/View;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final u:Landroidx/constraintlayout/helper/widget/Flow;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final v:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final w:Landroidx/compose/ui/platform/ComposeView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final x:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final y:Lorg/xbet/uikit/components/buttons/DSButton;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final z:Landroidx/constraintlayout/helper/widget/Flow;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/view/View;Landroidx/appcompat/widget/AppCompatTextView;Landroid/view/View;Lorg/xbet/uikit/components/buttons/DSButton;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatImageView;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TickerDividerHorizontalView;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatTextView;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;Landroidx/constraintlayout/helper/widget/Flow;Lorg/xbet/uikit/components/views/LoadableImageView;Landroid/widget/ProgressBar;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatTextView;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;Lorg/xbet/uikit/components/separator/Separator;Lorg/xbet/uikit/components/separator/Separator;Lorg/xbet/uikit/components/tag/Tag;Landroid/view/View;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatTextView;Landroidx/compose/ui/platform/ComposeView;Landroidx/appcompat/widget/AppCompatTextView;Lorg/xbet/uikit/components/buttons/DSButton;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatTextView;Landroidx/appcompat/widget/AppCompatTextView;)V
    .locals 0
    .param p1    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Landroidx/appcompat/widget/AppCompatTextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/uikit/components/buttons/DSButton;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p5    # Landroidx/constraintlayout/helper/widget/Flow;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p6    # Landroidx/appcompat/widget/AppCompatImageView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TickerDividerHorizontalView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p8    # Landroidx/constraintlayout/helper/widget/Flow;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p9    # Landroidx/appcompat/widget/AppCompatTextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p11    # Landroidx/constraintlayout/helper/widget/Flow;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p12    # Lorg/xbet/uikit/components/views/LoadableImageView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p13    # Landroid/widget/ProgressBar;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p14    # Landroidx/constraintlayout/helper/widget/Flow;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p15    # Landroidx/appcompat/widget/AppCompatTextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p16    # Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p17    # Lorg/xbet/uikit/components/separator/Separator;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p18    # Lorg/xbet/uikit/components/separator/Separator;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p19    # Lorg/xbet/uikit/components/tag/Tag;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p20    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p21    # Landroidx/constraintlayout/helper/widget/Flow;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p22    # Landroidx/appcompat/widget/AppCompatTextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p23    # Landroidx/compose/ui/platform/ComposeView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p24    # Landroidx/appcompat/widget/AppCompatTextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p25    # Lorg/xbet/uikit/components/buttons/DSButton;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p26    # Landroidx/constraintlayout/helper/widget/Flow;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p27    # Landroidx/constraintlayout/helper/widget/Flow;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p28    # Landroidx/appcompat/widget/AppCompatTextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p29    # Landroidx/appcompat/widget/AppCompatTextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Ll31/A;->a:Landroid/view/View;

    .line 5
    .line 6
    iput-object p2, p0, Ll31/A;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 7
    .line 8
    iput-object p3, p0, Ll31/A;->c:Landroid/view/View;

    .line 9
    .line 10
    iput-object p4, p0, Ll31/A;->d:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 11
    .line 12
    iput-object p5, p0, Ll31/A;->e:Landroidx/constraintlayout/helper/widget/Flow;

    .line 13
    .line 14
    iput-object p6, p0, Ll31/A;->f:Landroidx/appcompat/widget/AppCompatImageView;

    .line 15
    .line 16
    iput-object p7, p0, Ll31/A;->g:Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TickerDividerHorizontalView;

    .line 17
    .line 18
    iput-object p8, p0, Ll31/A;->h:Landroidx/constraintlayout/helper/widget/Flow;

    .line 19
    .line 20
    iput-object p9, p0, Ll31/A;->i:Landroidx/appcompat/widget/AppCompatTextView;

    .line 21
    .line 22
    iput-object p10, p0, Ll31/A;->j:Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;

    .line 23
    .line 24
    iput-object p11, p0, Ll31/A;->k:Landroidx/constraintlayout/helper/widget/Flow;

    .line 25
    .line 26
    iput-object p12, p0, Ll31/A;->l:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 27
    .line 28
    iput-object p13, p0, Ll31/A;->m:Landroid/widget/ProgressBar;

    .line 29
    .line 30
    iput-object p14, p0, Ll31/A;->n:Landroidx/constraintlayout/helper/widget/Flow;

    .line 31
    .line 32
    iput-object p15, p0, Ll31/A;->o:Landroidx/appcompat/widget/AppCompatTextView;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, Ll31/A;->p:Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, Ll31/A;->q:Lorg/xbet/uikit/components/separator/Separator;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, Ll31/A;->r:Lorg/xbet/uikit/components/separator/Separator;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, Ll31/A;->s:Lorg/xbet/uikit/components/tag/Tag;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, Ll31/A;->t:Landroid/view/View;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, Ll31/A;->u:Landroidx/constraintlayout/helper/widget/Flow;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, Ll31/A;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 61
    .line 62
    move-object/from16 p1, p23

    .line 63
    .line 64
    iput-object p1, p0, Ll31/A;->w:Landroidx/compose/ui/platform/ComposeView;

    .line 65
    .line 66
    move-object/from16 p1, p24

    .line 67
    .line 68
    iput-object p1, p0, Ll31/A;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 69
    .line 70
    move-object/from16 p1, p25

    .line 71
    .line 72
    iput-object p1, p0, Ll31/A;->y:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 73
    .line 74
    move-object/from16 p1, p26

    .line 75
    .line 76
    iput-object p1, p0, Ll31/A;->z:Landroidx/constraintlayout/helper/widget/Flow;

    .line 77
    .line 78
    move-object/from16 p1, p27

    .line 79
    .line 80
    iput-object p1, p0, Ll31/A;->A:Landroidx/constraintlayout/helper/widget/Flow;

    .line 81
    .line 82
    move-object/from16 p1, p28

    .line 83
    .line 84
    iput-object p1, p0, Ll31/A;->B:Landroidx/appcompat/widget/AppCompatTextView;

    .line 85
    .line 86
    move-object/from16 p1, p29

    .line 87
    .line 88
    iput-object p1, p0, Ll31/A;->C:Landroidx/appcompat/widget/AppCompatTextView;

    .line 89
    .line 90
    return-void
.end method

.method public static a(Landroid/view/View;)Ll31/A;
    .locals 30
    .param p0    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    sget v0, LS11/d;->amountTv:I

    .line 4
    .line 5
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    check-cast v2, Landroidx/appcompat/widget/AppCompatTextView;

    .line 10
    .line 11
    if-eqz v2, :cond_0

    .line 12
    .line 13
    sget v0, LS11/d;->background:I

    .line 14
    .line 15
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 16
    .line 17
    .line 18
    move-result-object v3

    .line 19
    if-eqz v3, :cond_0

    .line 20
    .line 21
    sget v0, LS11/d;->bottomButton:I

    .line 22
    .line 23
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    check-cast v4, Lorg/xbet/uikit/components/buttons/DSButton;

    .line 28
    .line 29
    if-eqz v4, :cond_0

    .line 30
    .line 31
    sget v0, LS11/d;->buttonContainer:I

    .line 32
    .line 33
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 34
    .line 35
    .line 36
    move-result-object v5

    .line 37
    check-cast v5, Landroidx/constraintlayout/helper/widget/Flow;

    .line 38
    .line 39
    if-eqz v5, :cond_0

    .line 40
    .line 41
    sget v0, LS11/d;->closeIv:I

    .line 42
    .line 43
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 44
    .line 45
    .line 46
    move-result-object v6

    .line 47
    check-cast v6, Landroidx/appcompat/widget/AppCompatImageView;

    .line 48
    .line 49
    if-eqz v6, :cond_0

    .line 50
    .line 51
    sget v0, LS11/d;->divider:I

    .line 52
    .line 53
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 54
    .line 55
    .line 56
    move-result-object v7

    .line 57
    check-cast v7, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TickerDividerHorizontalView;

    .line 58
    .line 59
    if-eqz v7, :cond_0

    .line 60
    .line 61
    sget v0, LS11/d;->gamesContainer:I

    .line 62
    .line 63
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 64
    .line 65
    .line 66
    move-result-object v8

    .line 67
    check-cast v8, Landroidx/constraintlayout/helper/widget/Flow;

    .line 68
    .line 69
    if-eqz v8, :cond_0

    .line 70
    .line 71
    sget v0, LS11/d;->gamesTv:I

    .line 72
    .line 73
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 74
    .line 75
    .line 76
    move-result-object v9

    .line 77
    check-cast v9, Landroidx/appcompat/widget/AppCompatTextView;

    .line 78
    .line 79
    if-eqz v9, :cond_0

    .line 80
    .line 81
    sget v0, LS11/d;->gamesValue:I

    .line 82
    .line 83
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 84
    .line 85
    .line 86
    move-result-object v10

    .line 87
    check-cast v10, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;

    .line 88
    .line 89
    if-eqz v10, :cond_0

    .line 90
    .line 91
    sget v0, LS11/d;->infoContainer:I

    .line 92
    .line 93
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 94
    .line 95
    .line 96
    move-result-object v11

    .line 97
    check-cast v11, Landroidx/constraintlayout/helper/widget/Flow;

    .line 98
    .line 99
    if-eqz v11, :cond_0

    .line 100
    .line 101
    sget v0, LS11/d;->loadableImage:I

    .line 102
    .line 103
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 104
    .line 105
    .line 106
    move-result-object v12

    .line 107
    check-cast v12, Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 108
    .line 109
    if-eqz v12, :cond_0

    .line 110
    .line 111
    sget v0, LS11/d;->loaderWager:I

    .line 112
    .line 113
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 114
    .line 115
    .line 116
    move-result-object v13

    .line 117
    check-cast v13, Landroid/widget/ProgressBar;

    .line 118
    .line 119
    if-eqz v13, :cond_0

    .line 120
    .line 121
    sget v0, LS11/d;->providersContainer:I

    .line 122
    .line 123
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 124
    .line 125
    .line 126
    move-result-object v14

    .line 127
    check-cast v14, Landroidx/constraintlayout/helper/widget/Flow;

    .line 128
    .line 129
    if-eqz v14, :cond_0

    .line 130
    .line 131
    sget v0, LS11/d;->providersTv:I

    .line 132
    .line 133
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 134
    .line 135
    .line 136
    move-result-object v15

    .line 137
    check-cast v15, Landroidx/appcompat/widget/AppCompatTextView;

    .line 138
    .line 139
    if-eqz v15, :cond_0

    .line 140
    .line 141
    sget v0, LS11/d;->providersValue:I

    .line 142
    .line 143
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 144
    .line 145
    .line 146
    move-result-object v16

    .line 147
    check-cast v16, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;

    .line 148
    .line 149
    if-eqz v16, :cond_0

    .line 150
    .line 151
    sget v0, LS11/d;->sellSeparatorProviders:I

    .line 152
    .line 153
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 154
    .line 155
    .line 156
    move-result-object v17

    .line 157
    check-cast v17, Lorg/xbet/uikit/components/separator/Separator;

    .line 158
    .line 159
    if-eqz v17, :cond_0

    .line 160
    .line 161
    sget v0, LS11/d;->sellSeparatorTimer:I

    .line 162
    .line 163
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 164
    .line 165
    .line 166
    move-result-object v18

    .line 167
    check-cast v18, Lorg/xbet/uikit/components/separator/Separator;

    .line 168
    .line 169
    if-eqz v18, :cond_0

    .line 170
    .line 171
    sget v0, LS11/d;->tagTv:I

    .line 172
    .line 173
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 174
    .line 175
    .line 176
    move-result-object v19

    .line 177
    check-cast v19, Lorg/xbet/uikit/components/tag/Tag;

    .line 178
    .line 179
    if-eqz v19, :cond_0

    .line 180
    .line 181
    sget v0, LS11/d;->ticketBackground:I

    .line 182
    .line 183
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 184
    .line 185
    .line 186
    move-result-object v20

    .line 187
    if-eqz v20, :cond_0

    .line 188
    .line 189
    sget v0, LS11/d;->timerContainer:I

    .line 190
    .line 191
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 192
    .line 193
    .line 194
    move-result-object v21

    .line 195
    check-cast v21, Landroidx/constraintlayout/helper/widget/Flow;

    .line 196
    .line 197
    if-eqz v21, :cond_0

    .line 198
    .line 199
    sget v0, LS11/d;->timerTv:I

    .line 200
    .line 201
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 202
    .line 203
    .line 204
    move-result-object v22

    .line 205
    check-cast v22, Landroidx/appcompat/widget/AppCompatTextView;

    .line 206
    .line 207
    if-eqz v22, :cond_0

    .line 208
    .line 209
    sget v0, LS11/d;->timerValue:I

    .line 210
    .line 211
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 212
    .line 213
    .line 214
    move-result-object v23

    .line 215
    check-cast v23, Landroidx/compose/ui/platform/ComposeView;

    .line 216
    .line 217
    if-eqz v23, :cond_0

    .line 218
    .line 219
    sget v0, LS11/d;->titleTv:I

    .line 220
    .line 221
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 222
    .line 223
    .line 224
    move-result-object v24

    .line 225
    check-cast v24, Landroidx/appcompat/widget/AppCompatTextView;

    .line 226
    .line 227
    if-eqz v24, :cond_0

    .line 228
    .line 229
    sget v0, LS11/d;->topButton:I

    .line 230
    .line 231
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 232
    .line 233
    .line 234
    move-result-object v25

    .line 235
    check-cast v25, Lorg/xbet/uikit/components/buttons/DSButton;

    .line 236
    .line 237
    if-eqz v25, :cond_0

    .line 238
    .line 239
    sget v0, LS11/d;->topTitleContainer:I

    .line 240
    .line 241
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 242
    .line 243
    .line 244
    move-result-object v26

    .line 245
    check-cast v26, Landroidx/constraintlayout/helper/widget/Flow;

    .line 246
    .line 247
    if-eqz v26, :cond_0

    .line 248
    .line 249
    sget v0, LS11/d;->wagerHorizontal:I

    .line 250
    .line 251
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 252
    .line 253
    .line 254
    move-result-object v27

    .line 255
    check-cast v27, Landroidx/constraintlayout/helper/widget/Flow;

    .line 256
    .line 257
    if-eqz v27, :cond_0

    .line 258
    .line 259
    sget v0, LS11/d;->wagerTv:I

    .line 260
    .line 261
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 262
    .line 263
    .line 264
    move-result-object v28

    .line 265
    check-cast v28, Landroidx/appcompat/widget/AppCompatTextView;

    .line 266
    .line 267
    if-eqz v28, :cond_0

    .line 268
    .line 269
    sget v0, LS11/d;->wagerValue:I

    .line 270
    .line 271
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 272
    .line 273
    .line 274
    move-result-object v29

    .line 275
    check-cast v29, Landroidx/appcompat/widget/AppCompatTextView;

    .line 276
    .line 277
    if-eqz v29, :cond_0

    .line 278
    .line 279
    new-instance v0, Ll31/A;

    .line 280
    .line 281
    invoke-direct/range {v0 .. v29}, Ll31/A;-><init>(Landroid/view/View;Landroidx/appcompat/widget/AppCompatTextView;Landroid/view/View;Lorg/xbet/uikit/components/buttons/DSButton;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatImageView;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TickerDividerHorizontalView;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatTextView;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;Landroidx/constraintlayout/helper/widget/Flow;Lorg/xbet/uikit/components/views/LoadableImageView;Landroid/widget/ProgressBar;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatTextView;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/view/TagContainerView;Lorg/xbet/uikit/components/separator/Separator;Lorg/xbet/uikit/components/separator/Separator;Lorg/xbet/uikit/components/tag/Tag;Landroid/view/View;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatTextView;Landroidx/compose/ui/platform/ComposeView;Landroidx/appcompat/widget/AppCompatTextView;Lorg/xbet/uikit/components/buttons/DSButton;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/constraintlayout/helper/widget/Flow;Landroidx/appcompat/widget/AppCompatTextView;Landroidx/appcompat/widget/AppCompatTextView;)V

    .line 282
    .line 283
    .line 284
    return-object v0

    .line 285
    :cond_0
    invoke-virtual/range {p0 .. p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 286
    .line 287
    .line 288
    move-result-object v1

    .line 289
    invoke-virtual {v1, v0}, Landroid/content/res/Resources;->getResourceName(I)Ljava/lang/String;

    .line 290
    .line 291
    .line 292
    move-result-object v0

    .line 293
    new-instance v1, Ljava/lang/NullPointerException;

    .line 294
    .line 295
    const-string v2, "Missing required view with ID: "

    .line 296
    .line 297
    invoke-virtual {v2, v0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 298
    .line 299
    .line 300
    move-result-object v0

    .line 301
    invoke-direct {v1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 302
    .line 303
    .line 304
    throw v1
.end method

.method public static b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Ll31/A;
    .locals 1
    .param p0    # Landroid/view/LayoutInflater;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p1    # Landroid/view/ViewGroup;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    sget v0, LS11/f;->aggregator_gift_card_ticket_primary:I

    .line 4
    .line 5
    invoke-virtual {p0, v0, p1}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;)Landroid/view/View;

    .line 6
    .line 7
    .line 8
    invoke-static {p1}, Ll31/A;->a(Landroid/view/View;)Ll31/A;

    .line 9
    .line 10
    .line 11
    move-result-object p0

    .line 12
    return-object p0

    .line 13
    :cond_0
    new-instance p0, Ljava/lang/NullPointerException;

    .line 14
    .line 15
    const-string p1, "parent"

    .line 16
    .line 17
    invoke-direct {p0, p1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    throw p0
.end method


# virtual methods
.method public getRoot()Landroid/view/View;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    iget-object v0, p0, Ll31/A;->a:Landroid/view/View;

    .line 2
    .line 3
    return-object v0
.end method
