.class public final synthetic LQI0/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:J

.field public final synthetic b:L<PERSON>lin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(JLkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-wide p1, p0, LQI0/c;->a:J

    iput-object p3, p0, LQI0/c;->b:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-wide v0, p0, LQI0/c;->a:J

    iget-object v2, p0, LQI0/c;->b:Lkotlin/jvm/functions/Function1;

    check-cast p1, LB4/a;

    invoke-static {v0, v1, v2, p1}, Lorg/xbet/statistic/player/impl/player/player_lastgame/presentation/adapter/PlayerLastGameAdapterDelegateKt;->a(JLkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
