.class public final LF91/a$a;
.super Landroidx/recyclerview/widget/i$f;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LF91/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroidx/recyclerview/widget/i$f<",
        "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u0000\n\u0002\u0008\u0003\u0008\u0082\u0003\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u001f\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\n\u001a\u00020\u00072\u0006\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\n\u0010\tJ!\u0010\u000c\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "LF91/a$a;",
        "Landroidx/recyclerview/widget/i$f;",
        "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;",
        "<init>",
        "()V",
        "oldItem",
        "newItem",
        "",
        "e",
        "(Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;)Z",
        "d",
        "",
        "f",
        "(Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;)Ljava/lang/Object;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Landroidx/recyclerview/widget/i$f;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LF91/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;

    .line 2
    .line 3
    check-cast p2, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LF91/a$a;->d(Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public bridge synthetic b(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;

    .line 2
    .line 3
    check-cast p2, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LF91/a$a;->e(Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public bridge synthetic c(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;

    .line 2
    .line 3
    check-cast p2, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LF91/a$a;->f(Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    return-object p1
.end method

.method public d(Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;)Z
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method

.method public e(Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;)Z
    .locals 2
    .param p1    # Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;->getId()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p2}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;->getId()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-eqz v0, :cond_0

    .line 14
    .line 15
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;->e()Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    invoke-virtual {p2}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;->e()Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;

    .line 24
    .line 25
    .line 26
    move-result-object p2

    .line 27
    invoke-virtual {p2}, Ljava/lang/Enum;->ordinal()I

    .line 28
    .line 29
    .line 30
    move-result p2

    .line 31
    if-ne p1, p2, :cond_0

    .line 32
    .line 33
    const/4 p1, 0x1

    .line 34
    return p1

    .line 35
    :cond_0
    const/4 p1, 0x0

    .line 36
    return p1
.end method

.method public f(Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;)Ljava/lang/Object;
    .locals 8
    .param p1    # Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;->c()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p2}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;->c()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-super {p0, p1, p2}, Landroidx/recyclerview/widget/i$f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    return-object p1

    .line 20
    :cond_0
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;->d()Ljava/util/List;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    new-instance v1, Ljava/util/ArrayList;

    .line 25
    .line 26
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 27
    .line 28
    .line 29
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    const/4 v2, 0x0

    .line 34
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 35
    .line 36
    .line 37
    move-result v3

    .line 38
    if-eqz v3, :cond_4

    .line 39
    .line 40
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 41
    .line 42
    .line 43
    move-result-object v3

    .line 44
    add-int/lit8 v4, v2, 0x1

    .line 45
    .line 46
    if-gez v2, :cond_1

    .line 47
    .line 48
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 49
    .line 50
    .line 51
    :cond_1
    move-object v5, v3

    .line 52
    check-cast v5, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    .line 53
    .line 54
    invoke-virtual {p2}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;->d()Ljava/util/List;

    .line 55
    .line 56
    .line 57
    move-result-object v6

    .line 58
    invoke-static {v6, v2}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object v2

    .line 62
    check-cast v2, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    .line 63
    .line 64
    if-eqz v2, :cond_2

    .line 65
    .line 66
    invoke-interface {v2}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getId()Ljava/lang/String;

    .line 67
    .line 68
    .line 69
    move-result-object v6

    .line 70
    invoke-interface {v5}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getId()Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object v7

    .line 74
    invoke-static {v6, v7}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 75
    .line 76
    .line 77
    move-result v6

    .line 78
    if-eqz v6, :cond_3

    .line 79
    .line 80
    invoke-interface {v2}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getName()Ljava/lang/String;

    .line 81
    .line 82
    .line 83
    move-result-object v6

    .line 84
    invoke-interface {v5}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getName()Ljava/lang/String;

    .line 85
    .line 86
    .line 87
    move-result-object v7

    .line 88
    invoke-static {v6, v7}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 89
    .line 90
    .line 91
    move-result v6

    .line 92
    if-eqz v6, :cond_3

    .line 93
    .line 94
    invoke-interface {v2}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->P()Z

    .line 95
    .line 96
    .line 97
    move-result v2

    .line 98
    invoke-interface {v5}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->P()Z

    .line 99
    .line 100
    .line 101
    move-result v5

    .line 102
    if-eq v2, v5, :cond_3

    .line 103
    .line 104
    :cond_2
    invoke-interface {v1, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 105
    .line 106
    .line 107
    :cond_3
    move v2, v4

    .line 108
    goto :goto_0

    .line 109
    :cond_4
    new-instance v0, Ljava/util/ArrayList;

    .line 110
    .line 111
    const/16 v2, 0xa

    .line 112
    .line 113
    invoke-static {v1, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 114
    .line 115
    .line 116
    move-result v2

    .line 117
    invoke-direct {v0, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 118
    .line 119
    .line 120
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 121
    .line 122
    .line 123
    move-result-object v1

    .line 124
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 125
    .line 126
    .line 127
    move-result v2

    .line 128
    if-eqz v2, :cond_5

    .line 129
    .line 130
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    move-result-object v2

    .line 134
    check-cast v2, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    .line 135
    .line 136
    invoke-interface {v2}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getId()Ljava/lang/String;

    .line 137
    .line 138
    .line 139
    move-result-object v2

    .line 140
    invoke-interface {v0, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 141
    .line 142
    .line 143
    goto :goto_1

    .line 144
    :cond_5
    new-instance v1, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi$a$a;

    .line 145
    .line 146
    invoke-direct {v1, v0}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi$a$a;-><init>(Ljava/util/List;)V

    .line 147
    .line 148
    .line 149
    invoke-static {v1}, Lkotlin/collections/Z;->j(Ljava/lang/Object;)Ljava/util/Set;

    .line 150
    .line 151
    .line 152
    move-result-object v0

    .line 153
    check-cast v0, Ljava/util/Collection;

    .line 154
    .line 155
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 156
    .line 157
    .line 158
    move-result v1

    .line 159
    if-eqz v1, :cond_6

    .line 160
    .line 161
    invoke-static {}, LF91/a;->o()LF91/a$a;

    .line 162
    .line 163
    .line 164
    move-result-object v0

    .line 165
    invoke-super {v0, p1, p2}, Landroidx/recyclerview/widget/i$f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 166
    .line 167
    .line 168
    move-result-object p1

    .line 169
    return-object p1

    .line 170
    :cond_6
    return-object v0
.end method
