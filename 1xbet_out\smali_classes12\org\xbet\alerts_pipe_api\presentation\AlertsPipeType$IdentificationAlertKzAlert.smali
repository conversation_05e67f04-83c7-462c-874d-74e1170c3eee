.class public final Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "IdentificationAlertKzAlert"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0008\u00c7\n\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0006\u0010\u0004\u001a\u00020\u0005J\u0013\u0010\u0006\u001a\u00020\u00072\u0008\u0010\u0008\u001a\u0004\u0018\u00010\tH\u00d6\u0003J\t\u0010\n\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u000b\u001a\u00020\u000cH\u00d6\u0001J\u0016\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0005\u00a8\u0006\u0012"
    }
    d2 = {
        "Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;",
        "Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;",
        "<init>",
        "()V",
        "describeContents",
        "",
        "equals",
        "",
        "other",
        "",
        "hashCode",
        "toString",
        "",
        "writeToParcel",
        "",
        "dest",
        "Landroid/os/Parcel;",
        "flags",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final CREATOR:Landroid/os/Parcelable$Creator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/os/Parcelable$Creator<",
            "Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final INSTANCE:Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;

    invoke-direct {v0}, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;-><init>()V

    sput-object v0, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;->INSTANCE:Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;

    new-instance v0, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert$a;

    invoke-direct {v0}, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert$a;-><init>()V

    sput-object v0, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;->CREATOR:Landroid/os/Parcelable$Creator;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final describeContents()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 1

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of p1, p1, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;

    if-nez p1, :cond_1

    const/4 p1, 0x0

    return p1

    :cond_1
    return v0
.end method

.method public hashCode()I
    .locals 1

    const v0, 0x61c95dc2

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    const-string v0, "IdentificationAlertKzAlert"

    return-object v0
.end method

.method public final writeToParcel(Landroid/os/Parcel;I)V
    .locals 0
    .param p1    # Landroid/os/Parcel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    const/4 p2, 0x1

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeInt(I)V

    return-void
.end method
