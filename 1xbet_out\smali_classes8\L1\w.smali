.class public final synthetic LL1/w;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/video/f$a;

.field public final synthetic b:Landroidx/media3/common/N;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/video/f$a;Landroidx/media3/common/N;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LL1/w;->a:Landroidx/media3/exoplayer/video/f$a;

    iput-object p2, p0, LL1/w;->b:Landroidx/media3/common/N;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, LL1/w;->a:Landroidx/media3/exoplayer/video/f$a;

    iget-object v1, p0, LL1/w;->b:Landroidx/media3/common/N;

    invoke-static {v0, v1}, Landroidx/media3/exoplayer/video/f$a;->f(Landroidx/media3/exoplayer/video/f$a;Landroidx/media3/common/N;)V

    return-void
.end method
