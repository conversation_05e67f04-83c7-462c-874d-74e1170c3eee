.class public final LSN0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LSN0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LSN0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:Lf8/g;

.field public final b:LQW0/c;

.field public final c:Lc8/h;

.field public final d:LSN0/a$b;


# direct methods
.method public constructor <init>(LQW0/c;Lf8/g;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LSN0/a$b;->d:LSN0/a$b;

    .line 4
    iput-object p2, p0, LSN0/a$b;->a:Lf8/g;

    .line 5
    iput-object p1, p0, LSN0/a$b;->b:LQW0/c;

    .line 6
    iput-object p3, p0, LSN0/a$b;->c:Lc8/h;

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Lf8/g;Lc8/h;LSN0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3}, LSN0/a$b;-><init>(LQW0/c;Lf8/g;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a()LQN0/c;
    .locals 1

    .line 1
    new-instance v0, LSN0/f;

    .line 2
    .line 3
    invoke-direct {v0}, LSN0/f;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public b()LQN0/a;
    .locals 1

    .line 1
    invoke-virtual {p0}, LSN0/a$b;->c()LiP0/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final c()LiP0/c;
    .locals 2

    .line 1
    new-instance v0, LiP0/c;

    .line 2
    .line 3
    invoke-virtual {p0}, LSN0/a$b;->e()Lorg/xbet/statistic/team/impl/team_statistic/data/repository/TeamStatisticsRepositoryImpl;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LiP0/c;-><init>(LhP0/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final d()LcP0/b;
    .locals 2

    .line 1
    new-instance v0, LcP0/b;

    .line 2
    .line 3
    iget-object v1, p0, LSN0/a$b;->a:Lf8/g;

    .line 4
    .line 5
    invoke-direct {v0, v1}, LcP0/b;-><init>(Lf8/g;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final e()Lorg/xbet/statistic/team/impl/team_statistic/data/repository/TeamStatisticsRepositoryImpl;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/statistic/team/impl/team_statistic/data/repository/TeamStatisticsRepositoryImpl;

    .line 2
    .line 3
    invoke-virtual {p0}, LSN0/a$b;->d()LcP0/b;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, p0, LSN0/a$b;->b:LQW0/c;

    .line 8
    .line 9
    invoke-interface {v2}, LQW0/c;->a()Lm8/a;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-static {v2}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    check-cast v2, Lm8/a;

    .line 18
    .line 19
    iget-object v3, p0, LSN0/a$b;->c:Lc8/h;

    .line 20
    .line 21
    invoke-direct {v0, v1, v2, v3}, Lorg/xbet/statistic/team/impl/team_statistic/data/repository/TeamStatisticsRepositoryImpl;-><init>(LcP0/b;Lm8/a;Lc8/h;)V

    .line 22
    .line 23
    .line 24
    return-object v0
.end method
