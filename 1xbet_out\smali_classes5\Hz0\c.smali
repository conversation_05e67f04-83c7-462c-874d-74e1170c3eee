.class public final LHz0/c;
.super Ljava/lang/Object;


# static fields
.field public static fragment_sport_game_classic:I = 0x7f0d0490

.field public static item_card_common:I = 0x7f0d058a

.field public static item_card_common_multi_teams:I = 0x7f0d058b

.field public static item_card_common_single_game:I = 0x7f0d058c

.field public static item_card_cricket:I = 0x7f0d058d

.field public static item_card_error:I = 0x7f0d058e

.field public static item_card_football_period:I = 0x7f0d058f

.field public static item_card_host_vs_guests:I = 0x7f0d0590

.field public static item_card_line_statistic:I = 0x7f0d0591

.field public static item_card_penalty:I = 0x7f0d0592

.field public static item_card_period:I = 0x7f0d0593

.field public static item_card_review:I = 0x7f0d0594

.field public static item_card_shot_statistic:I = 0x7f0d0595

.field public static item_card_stadium:I = 0x7f0d0596

.field public static item_card_timer:I = 0x7f0d0597

.field public static item_card_weather:I = 0x7f0d0598

.field public static item_card_with_timer:I = 0x7f0d0599

.field public static item_compressed_card_error:I = 0x7f0d05b3

.field public static item_compressed_card_football_period:I = 0x7f0d05b4

.field public static item_compressed_card_period:I = 0x7f0d05b5

.field public static item_compressed_common:I = 0x7f0d05b6

.field public static item_compressed_main_cricket_binding:I = 0x7f0d05b7

.field public static item_compressed_multi_teams:I = 0x7f0d05b8

.field public static item_compressed_period:I = 0x7f0d05b9

.field public static item_compressed_period_event:I = 0x7f0d05ba

.field public static item_compressed_period_inning:I = 0x7f0d05bb

.field public static item_compressed_single_game:I = 0x7f0d05bc

.field public static item_football_active_zones:I = 0x7f0d05ed

.field public static item_game_header_event:I = 0x7f0d0604

.field public static item_host_vs_guests:I = 0x7f0d060f

.field public static item_line_statistic_header:I = 0x7f0d0620

.field public static item_line_statistic_meeting_info:I = 0x7f0d0621

.field public static item_multi_team_one:I = 0x7f0d0637

.field public static item_multi_team_two:I = 0x7f0d0638

.field public static item_period:I = 0x7f0d0644

.field public static item_period_inning:I = 0x7f0d0645

.field public static item_review_event_:I = 0x7f0d0695

.field public static item_short_statistic_info:I = 0x7f0d06b0

.field public static item_short_statistic_team_logo:I = 0x7f0d06b1

.field public static item_stadium_info:I = 0x7f0d06ea

.field public static view_expand_btn:I = 0x7f0d0b17

.field public static view_football_events:I = 0x7f0d0b1b

.field public static view_game_classic_toolbar:I = 0x7f0d0b20

.field public static view_match_info_content:I = 0x7f0d0b5c

.field public static view_match_info_tabs_container:I = 0x7f0d0b5d

.field public static view_match_timer:I = 0x7f0d0b5e

.field public static view_sub_games_content:I = 0x7f0d0b99


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
