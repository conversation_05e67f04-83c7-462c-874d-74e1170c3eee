.class public final synthetic Lorg/xbet/main_menu/impl/presentation/container/q;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/q;->a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/q;->a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;

    invoke-static {v0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->S2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Landroid/view/View;)V

    return-void
.end method
