.class public final Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;,
        Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$a;,
        Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00a8\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0016\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0008\n\u0008\u0000\u0018\u0000 .2\u00020\u0001:\u0003\u008c\u0001gB1\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u000f\u0010\u000e\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\rJ\u000f\u0010\u000f\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\rJ3\u0010\u0015\u001a\u00020\u000b*\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u001f\u0010\u0019\u001a\u00020\u000b2\u0006\u0010\u0017\u001a\u00020\u00062\u0006\u0010\u0018\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u000f\u0010\u001b\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\rJ\u000f\u0010\u001c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\rJ\u001f\u0010 \u001a\u00020\u000b2\u0006\u0010\u001e\u001a\u00020\u001d2\u0006\u0010\u001f\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008 \u0010!J\u0017\u0010$\u001a\u00020\u000b2\u0006\u0010#\u001a\u00020\"H\u0002\u00a2\u0006\u0004\u0008$\u0010%J\u001b\u0010(\u001a\u00020\u000b*\u00020\u001d2\u0006\u0010\'\u001a\u00020&H\u0002\u00a2\u0006\u0004\u0008(\u0010)J\u000f\u0010*\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008*\u0010\rJ\u000f\u0010+\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008+\u0010\rJ\u0019\u0010.\u001a\u00020&2\u0008\u0010-\u001a\u0004\u0018\u00010,H\u0002\u00a2\u0006\u0004\u0008.\u0010/J\u0019\u00102\u001a\u00020&2\u0008\u00101\u001a\u0004\u0018\u000100H\u0002\u00a2\u0006\u0004\u00082\u00103J\u0017\u00104\u001a\u00020\u000b2\u0006\u0010\'\u001a\u00020&H\u0002\u00a2\u0006\u0004\u00084\u00105J\u0017\u00109\u001a\u0002082\u0006\u00107\u001a\u000206H\u0002\u00a2\u0006\u0004\u00089\u0010:J\u0017\u0010;\u001a\u00020\u00062\u0006\u00107\u001a\u000206H\u0002\u00a2\u0006\u0004\u0008;\u0010<J\u0017\u0010=\u001a\u00020\u00062\u0006\u00107\u001a\u000206H\u0002\u00a2\u0006\u0004\u0008=\u0010<J\u0019\u0010?\u001a\u0004\u0018\u00010>2\u0006\u00107\u001a\u000206H\u0002\u00a2\u0006\u0004\u0008?\u0010@J\u0019\u0010A\u001a\u0004\u0018\u00010>2\u0006\u00107\u001a\u000206H\u0002\u00a2\u0006\u0004\u0008A\u0010@J\u0017\u0010B\u001a\u00020\u00062\u0006\u00107\u001a\u000206H\u0002\u00a2\u0006\u0004\u0008B\u0010<J\u0017\u0010C\u001a\u00020\u00062\u0006\u00107\u001a\u000206H\u0002\u00a2\u0006\u0004\u0008C\u0010<J\u0017\u0010D\u001a\u00020\u00062\u0006\u00107\u001a\u000206H\u0002\u00a2\u0006\u0004\u0008D\u0010<J\u0017\u0010E\u001a\u00020\u00062\u0006\u00107\u001a\u000206H\u0002\u00a2\u0006\u0004\u0008E\u0010<J\u000f\u0010F\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008F\u0010GJ\u0017\u0010I\u001a\u00020\u000b2\u0006\u0010H\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008I\u0010JJ\u001f\u0010M\u001a\u00020\u000b2\u0006\u0010K\u001a\u00020\u00062\u0006\u0010L\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008M\u0010\u001aJ7\u0010S\u001a\u00020\u000b2\u0006\u0010N\u001a\u00020&2\u0006\u0010O\u001a\u00020\u00062\u0006\u0010P\u001a\u00020\u00062\u0006\u0010Q\u001a\u00020\u00062\u0006\u0010R\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008S\u0010TJ)\u0010Y\u001a\u00020&2\u0006\u0010#\u001a\u00020\"2\u0008\u0010V\u001a\u0004\u0018\u00010U2\u0006\u0010X\u001a\u00020WH\u0014\u00a2\u0006\u0004\u0008Y\u0010ZJ\u0017\u0010[\u001a\u00020\u000b2\u0006\u0010#\u001a\u00020\"H\u0014\u00a2\u0006\u0004\u0008[\u0010%J\u0015\u0010^\u001a\u00020\u000b2\u0006\u0010]\u001a\u00020\\\u00a2\u0006\u0004\u0008^\u0010_J\u0015\u0010a\u001a\u00020\u000b2\u0006\u0010`\u001a\u00020\\\u00a2\u0006\u0004\u0008a\u0010_J\u0015\u0010c\u001a\u00020\u000b2\u0006\u0010b\u001a\u00020&\u00a2\u0006\u0004\u0008c\u00105J\u0015\u0010e\u001a\u00020\u000b2\u0006\u0010d\u001a\u00020\\\u00a2\u0006\u0004\u0008e\u0010_R\u0014\u0010i\u001a\u00020f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008g\u0010hR\u0014\u0010l\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008j\u0010kR\u0014\u0010n\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008m\u0010kR\u0014\u0010q\u001a\u00020o8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010pR\u0014\u0010t\u001a\u00020r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010sR\u0014\u0010w\u001a\u00020u8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010vR\u0014\u0010y\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010xR\u0014\u0010z\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u0010xR\u0014\u0010{\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008E\u0010xR\u0014\u0010|\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008;\u0010xR\u0014\u0010}\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u0010xR\u0014\u0010\u007f\u001a\u0002088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008?\u0010~R\u0018\u0010\u0081\u0001\u001a\u0004\u0018\u00010>8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008B\u0010\u0080\u0001R\u0018\u0010\u0082\u0001\u001a\u0004\u0018\u00010>8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008A\u0010\u0080\u0001R\u0017\u0010\u0085\u0001\u001a\u00030\u0083\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008C\u0010\u0084\u0001R\u0015\u0010\u0086\u0001\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010xR\u0015\u0010\u0087\u0001\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010xR\u0015\u0010\u0088\u0001\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010xR\u0017\u0010\u0089\u0001\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008 \u0010xR\u0018\u0010\u008b\u0001\u001a\u00020&8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u0019\u0010\u008a\u0001\u00a8\u0006\u008d\u0001"
    }
    d2 = {
        "Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "defStyleRes",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;II)V",
        "",
        "x",
        "()V",
        "y",
        "z",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "textColor",
        "lineHeight",
        "textMinSize",
        "textMaxSize",
        "e",
        "(Landroidx/appcompat/widget/AppCompatTextView;IIII)V",
        "width",
        "height",
        "t",
        "(II)V",
        "q",
        "r",
        "Landroid/widget/TextView;",
        "view",
        "viewY",
        "s",
        "(Landroid/widget/TextView;I)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "g",
        "(Landroid/graphics/Canvas;)V",
        "",
        "isVisible",
        "f",
        "(Landroid/widget/TextView;Z)V",
        "d",
        "w",
        "Lcom/bumptech/glide/load/engine/GlideException;",
        "exception",
        "u",
        "(Lcom/bumptech/glide/load/engine/GlideException;)Z",
        "Landroid/graphics/Bitmap;",
        "bitmap",
        "v",
        "(Landroid/graphics/Bitmap;)Z",
        "setPlaceHolderIconDrawableVisibility",
        "(Z)V",
        "Landroid/content/res/TypedArray;",
        "array",
        "",
        "h",
        "(Landroid/content/res/TypedArray;)F",
        "j",
        "(Landroid/content/res/TypedArray;)I",
        "k",
        "Landroid/graphics/drawable/Drawable;",
        "l",
        "(Landroid/content/res/TypedArray;)Landroid/graphics/drawable/Drawable;",
        "n",
        "m",
        "o",
        "p",
        "i",
        "getPlaceholderColor",
        "()I",
        "layoutDirection",
        "onRtlPropertiesChanged",
        "(I)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "Landroid/view/View;",
        "child",
        "",
        "drawingTime",
        "drawChild",
        "(Landroid/graphics/Canvas;Landroid/view/View;J)Z",
        "onDraw",
        "",
        "title",
        "setTitle",
        "(Ljava/lang/String;)V",
        "subtitle",
        "setSubtitle",
        "value",
        "setLooksLikeShimmer",
        "imageUrl",
        "setImageUrl",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "a",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "shimmerView",
        "b",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "titleTextView",
        "c",
        "subtitleTextView",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "imageView",
        "Lorg/xbet/uikit/utils/z;",
        "Lorg/xbet/uikit/utils/z;",
        "imageLoadHelper",
        "Landroid/graphics/drawable/ColorDrawable;",
        "Landroid/graphics/drawable/ColorDrawable;",
        "backgroundDrawable",
        "I",
        "noImagePlaceholderColor",
        "noTextNoImagePlaceholderColor",
        "titleGradientDrawableHeight",
        "textStartPadding",
        "titleSubtitleSpacing",
        "F",
        "backgroundCornerRadius",
        "Landroid/graphics/drawable/Drawable;",
        "placeholderIconDrawable",
        "titleGradientDrawable",
        "Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;",
        "Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;",
        "bannerStyle",
        "textEndPadding",
        "titleLineHeight",
        "titleTextSize",
        "bannerLayoutDirection",
        "Z",
        "looksLikeShimmer",
        "BannerStyle",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final u:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Landroidx/appcompat/widget/AppCompatImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xbet/uikit/utils/z;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Landroid/graphics/drawable/ColorDrawable;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:F

.field public final m:Landroid/graphics/drawable/Drawable;

.field public final n:Landroid/graphics/drawable/Drawable;

.field public final o:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:I

.field public final q:I

.field public final r:I

.field public s:I

.field public t:Z


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->u:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$a;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 7
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/16 v5, 0xe

    const/4 v6, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v6}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 7
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/16 v5, 0xc

    const/4 v6, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v6}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 7
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    const/16 v5, 0x8

    const/4 v6, 0x0

    const/4 v4, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move v3, p3

    invoke-direct/range {v0 .. v6}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;II)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 6
    invoke-direct {p0, p1, p2, p3, p4}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;II)V

    .line 7
    new-instance v0, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/shimmer/ShimmerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->a:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 8
    new-instance p1, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {p1, v1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 9
    new-instance v0, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v0, v1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 10
    new-instance v2, Landroidx/appcompat/widget/AppCompatImageView;

    invoke-direct {v2, v1}, Landroidx/appcompat/widget/AppCompatImageView;-><init>(Landroid/content/Context;)V

    iput-object v2, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 11
    new-instance v3, Lorg/xbet/uikit/utils/z;

    invoke-direct {v3, v2}, Lorg/xbet/uikit/utils/z;-><init>(Landroid/widget/ImageView;)V

    iput-object v3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->e:Lorg/xbet/uikit/utils/z;

    .line 12
    new-instance v3, Landroid/graphics/drawable/ColorDrawable;

    sget v4, LlZ0/f;->static_gray:I

    invoke-static {v1, v4}, LF0/b;->getColor(Landroid/content/Context;I)I

    move-result v4

    invoke-direct {v3, v4}, Landroid/graphics/drawable/ColorDrawable;-><init>(I)V

    iput-object v3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->f:Landroid/graphics/drawable/ColorDrawable;

    .line 13
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->size_40:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->i:I

    .line 14
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->space_12:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->j:I

    .line 15
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->space_2:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->k:I

    .line 16
    sget-object v3, Ls80/d;->AggregatorBanner:[I

    invoke-virtual {v1, p2, v3, p3, p4}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p2

    .line 17
    invoke-virtual {p0, p2}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->i(Landroid/content/res/TypedArray;)I

    move-result p3

    .line 18
    invoke-static {}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;->getEntries()Lkotlin/enums/a;

    move-result-object p4

    if-ltz p3, :cond_0

    invoke-interface {p4}, Ljava/util/List;->size()I

    move-result v1

    if-ge p3, v1, :cond_0

    invoke-interface {p4, p3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p3

    goto :goto_0

    :cond_0
    sget-object p3, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;->LINE:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;

    :goto_0
    check-cast p3, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;

    iput-object p3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->o:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;

    .line 19
    invoke-virtual {p0, p2}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->h(Landroid/content/res/TypedArray;)F

    move-result p3

    iput p3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->l:F

    .line 20
    invoke-virtual {p0, p2}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->j(Landroid/content/res/TypedArray;)I

    move-result p3

    iput p3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->g:I

    .line 21
    invoke-virtual {p0, p2}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->k(Landroid/content/res/TypedArray;)I

    move-result p3

    iput p3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->h:I

    .line 22
    invoke-virtual {p0, p2}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->l(Landroid/content/res/TypedArray;)Landroid/graphics/drawable/Drawable;

    move-result-object p3

    iput-object p3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->m:Landroid/graphics/drawable/Drawable;

    .line 23
    invoke-virtual {p0, p2}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->n(Landroid/content/res/TypedArray;)Landroid/graphics/drawable/Drawable;

    move-result-object p3

    iput-object p3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->n:Landroid/graphics/drawable/Drawable;

    .line 24
    invoke-virtual {p0, p2}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->m(Landroid/content/res/TypedArray;)I

    move-result p3

    iput p3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->p:I

    .line 25
    invoke-virtual {p0, p2}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->o(Landroid/content/res/TypedArray;)I

    move-result p3

    iput p3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->q:I

    .line 26
    invoke-virtual {p0, p2}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->p(Landroid/content/res/TypedArray;)I

    move-result p3

    iput p3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->r:I

    .line 27
    invoke-virtual {p2}, Landroid/content/res/TypedArray;->recycle()V

    const/4 p2, 0x0

    .line 28
    invoke-direct {p0, p2}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->setPlaceHolderIconDrawableVisibility(Z)V

    .line 29
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->y()V

    .line 30
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->z()V

    .line 31
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->x()V

    .line 32
    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 33
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 34
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 35
    invoke-virtual {p0, p2}, Landroid/view/View;->setWillNotDraw(Z)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IIILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p6, p5, 0x2

    if-eqz p6, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p6, p5, 0x4

    if-eqz p6, :cond_1

    const/4 p3, 0x0

    :cond_1
    and-int/lit8 p5, p5, 0x8

    if-eqz p5, :cond_2

    .line 4
    sget p4, Ls80/c;->AggregatorBanner_Line:I

    .line 5
    :cond_2
    invoke-direct {p0, p1, p2, p3, p4}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;II)V

    return-void
.end method

.method public static final synthetic a(Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->t:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic b(Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;Lcom/bumptech/glide/load/engine/GlideException;)Z
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->u(Lcom/bumptech/glide/load/engine/GlideException;)Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic c(Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;Landroid/graphics/Bitmap;)Z
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->v(Landroid/graphics/Bitmap;)Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method private final getPlaceholderColor()I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-nez v0, :cond_0

    .line 12
    .line 13
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 14
    .line 15
    invoke-virtual {v0}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    if-nez v0, :cond_0

    .line 24
    .line 25
    iget v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->h:I

    .line 26
    .line 27
    return v0

    .line 28
    :cond_0
    iget v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->g:I

    .line 29
    .line 30
    return v0
.end method

.method private final setPlaceHolderIconDrawableVisibility(Z)V
    .locals 4

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    sget p1, LlZ0/d;->uikitSecondary40:I

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    sget p1, LlZ0/d;->uikitStaticTransparent:I

    .line 7
    .line 8
    :goto_0
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->m:Landroid/graphics/drawable/Drawable;

    .line 9
    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    const/4 v2, 0x2

    .line 17
    const/4 v3, 0x0

    .line 18
    invoke-static {v1, p1, v3, v2, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    invoke-virtual {v0, p1}, Landroid/graphics/drawable/Drawable;->setTint(I)V

    .line 23
    .line 24
    .line 25
    :cond_1
    return-void
.end method


# virtual methods
.method public final d()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->a:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->isAttachedToWindow()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 13
    .line 14
    .line 15
    :cond_0
    return-void
.end method

.method public drawChild(Landroid/graphics/Canvas;Landroid/view/View;J)Z
    .locals 1
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->n:Landroid/graphics/drawable/Drawable;

    .line 10
    .line 11
    if-eqz v0, :cond_0

    .line 12
    .line 13
    iget-boolean v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->t:Z

    .line 14
    .line 15
    if-nez v0, :cond_0

    .line 16
    .line 17
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 18
    .line 19
    invoke-virtual {v0}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-lez v0, :cond_0

    .line 28
    .line 29
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->n:Landroid/graphics/drawable/Drawable;

    .line 30
    .line 31
    invoke-virtual {v0, p1}, Landroid/graphics/drawable/Drawable;->draw(Landroid/graphics/Canvas;)V

    .line 32
    .line 33
    .line 34
    :cond_0
    invoke-super {p0, p1, p2, p3, p4}, Landroid/widget/FrameLayout;->drawChild(Landroid/graphics/Canvas;Landroid/view/View;J)Z

    .line 35
    .line 36
    .line 37
    move-result p1

    .line 38
    return p1
.end method

.method public final e(Landroidx/appcompat/widget/AppCompatTextView;IIII)V
    .locals 3

    .line 1
    new-instance v0, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 2
    .line 3
    const/4 v1, -0x1

    .line 4
    const/4 v2, -0x2

    .line 5
    invoke-direct {v0, v1, v2}, Landroid/view/ViewGroup$MarginLayoutParams;-><init>(II)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p1, p2}, Landroid/widget/TextView;->setTextColor(I)V

    .line 12
    .line 13
    .line 14
    sget-object p2, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    .line 15
    .line 16
    invoke-virtual {p1, p2}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p1, p3}, Landroidx/appcompat/widget/AppCompatTextView;->setLineHeight(I)V

    .line 20
    .line 21
    .line 22
    const/4 p2, 0x2

    .line 23
    invoke-virtual {p1, p2}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 24
    .line 25
    .line 26
    const/4 p2, 0x0

    .line 27
    if-le p5, p4, :cond_0

    .line 28
    .line 29
    const/4 p3, 0x1

    .line 30
    invoke-static {p1, p4, p5, p3, p2}, LX0/o;->h(Landroid/widget/TextView;IIII)V

    .line 31
    .line 32
    .line 33
    return-void

    .line 34
    :cond_0
    int-to-float p3, p5

    .line 35
    invoke-virtual {p1, p2, p3}, Landroidx/appcompat/widget/AppCompatTextView;->setTextSize(IF)V

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method public final f(Landroid/widget/TextView;Z)V
    .locals 2

    .line 1
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    const/4 v0, 0x1

    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    :goto_0
    if-eq p2, v0, :cond_2

    .line 12
    .line 13
    iget-boolean v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->t:Z

    .line 14
    .line 15
    if-nez v0, :cond_2

    .line 16
    .line 17
    if-eqz p2, :cond_1

    .line 18
    .line 19
    goto :goto_1

    .line 20
    :cond_1
    const/16 v1, 0x8

    .line 21
    .line 22
    :goto_1
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 23
    .line 24
    .line 25
    :cond_2
    return-void
.end method

.method public final g(Landroid/graphics/Canvas;)V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->f:Landroid/graphics/drawable/ColorDrawable;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/graphics/drawable/Drawable;->getBounds()Landroid/graphics/Rect;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Landroid/graphics/Path;

    .line 8
    .line 9
    invoke-direct {v1}, Landroid/graphics/Path;-><init>()V

    .line 10
    .line 11
    .line 12
    new-instance v2, Landroid/graphics/RectF;

    .line 13
    .line 14
    iget v3, v0, Landroid/graphics/Rect;->left:I

    .line 15
    .line 16
    int-to-float v3, v3

    .line 17
    iget v4, v0, Landroid/graphics/Rect;->top:I

    .line 18
    .line 19
    int-to-float v4, v4

    .line 20
    iget v5, v0, Landroid/graphics/Rect;->right:I

    .line 21
    .line 22
    int-to-float v5, v5

    .line 23
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 24
    .line 25
    int-to-float v0, v0

    .line 26
    invoke-direct {v2, v3, v4, v5, v0}, Landroid/graphics/RectF;-><init>(FFFF)V

    .line 27
    .line 28
    .line 29
    iget v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->l:F

    .line 30
    .line 31
    sget-object v3, Landroid/graphics/Path$Direction;->CW:Landroid/graphics/Path$Direction;

    .line 32
    .line 33
    invoke-virtual {v1, v2, v0, v0, v3}, Landroid/graphics/Path;->addRoundRect(Landroid/graphics/RectF;FFLandroid/graphics/Path$Direction;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p1, v1}, Landroid/graphics/Canvas;->clipPath(Landroid/graphics/Path;)Z

    .line 37
    .line 38
    .line 39
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->f:Landroid/graphics/drawable/ColorDrawable;

    .line 40
    .line 41
    invoke-virtual {v0, p1}, Landroid/graphics/drawable/ColorDrawable;->draw(Landroid/graphics/Canvas;)V

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method public final h(Landroid/content/res/TypedArray;)F
    .locals 3

    .line 1
    sget v0, Ls80/d;->AggregatorBanner_AggregatorBanner_cornerRadius:I

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget v2, LlZ0/g;->radius_16:I

    .line 8
    .line 9
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getInt(II)I

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    invoke-static {p1}, LlZ0/b;->a(I)I

    .line 18
    .line 19
    .line 20
    move-result p1

    .line 21
    int-to-float p1, p1

    .line 22
    return p1
.end method

.method public final i(Landroid/content/res/TypedArray;)I
    .locals 2

    .line 1
    sget v0, Ls80/d;->AggregatorBanner_AggregatorBanner_type:I

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;->LINE:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;

    .line 4
    .line 5
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getInt(II)I

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    return p1
.end method

.method public final j(Landroid/content/res/TypedArray;)I
    .locals 3

    .line 1
    sget v0, Ls80/d;->AggregatorBanner_AggregatorBanner_noImagePlaceholderColor:I

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget v2, LlZ0/f;->static_gray:I

    .line 8
    .line 9
    invoke-static {v1, v2}, LF0/b;->getColor(Landroid/content/Context;I)I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getColor(II)I

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    return p1
.end method

.method public final k(Landroid/content/res/TypedArray;)I
    .locals 3

    .line 1
    sget v0, Ls80/d;->AggregatorBanner_AggregatorBanner_noTextNoImagePlaceholderColor:I

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget v2, LlZ0/f;->static_gray:I

    .line 8
    .line 9
    invoke-static {v1, v2}, LF0/b;->getColor(Landroid/content/Context;I)I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getColor(II)I

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    return p1
.end method

.method public final l(Landroid/content/res/TypedArray;)Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 1
    sget v0, Ls80/d;->AggregatorBanner_AggregatorBanner_placeholderIcon:I

    .line 2
    .line 3
    invoke-virtual {p1, v0}, Landroid/content/res/TypedArray;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public final m(Landroid/content/res/TypedArray;)I
    .locals 2

    .line 1
    sget v0, Ls80/d;->AggregatorBanner_AggregatorBanner_textEndMargin:I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    return p1
.end method

.method public final n(Landroid/content/res/TypedArray;)Landroid/graphics/drawable/Drawable;
    .locals 3

    .line 1
    sget v0, Ls80/d;->AggregatorBanner_AggregatorBanner_useTitleGradientBackground:I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    if-eqz p1, :cond_0

    .line 9
    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    sget v0, LlZ0/f;->static_black_80:I

    .line 15
    .line 16
    invoke-static {p1, v0}, LF0/b;->getColor(Landroid/content/Context;I)I

    .line 17
    .line 18
    .line 19
    move-result p1

    .line 20
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    sget v1, LlZ0/f;->static_transparent:I

    .line 25
    .line 26
    invoke-static {v0, v1}, LF0/b;->getColor(Landroid/content/Context;I)I

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    new-instance v1, Landroid/graphics/drawable/GradientDrawable;

    .line 31
    .line 32
    sget-object v2, Landroid/graphics/drawable/GradientDrawable$Orientation;->BOTTOM_TOP:Landroid/graphics/drawable/GradientDrawable$Orientation;

    .line 33
    .line 34
    filled-new-array {p1, v0}, [I

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    invoke-direct {v1, v2, p1}, Landroid/graphics/drawable/GradientDrawable;-><init>(Landroid/graphics/drawable/GradientDrawable$Orientation;[I)V

    .line 39
    .line 40
    .line 41
    return-object v1

    .line 42
    :cond_0
    const/4 p1, 0x0

    .line 43
    return-object p1
.end method

.method public final o(Landroid/content/res/TypedArray;)I
    .locals 2

    .line 1
    sget v0, Ls80/d;->AggregatorBanner_AggregatorBanner_titleLineHeight:I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    return p1
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 1
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, Landroid/widget/FrameLayout;->onDraw(Landroid/graphics/Canvas;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->g(Landroid/graphics/Canvas;)V

    .line 5
    .line 6
    .line 7
    iget-boolean v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->t:Z

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->m:Landroid/graphics/drawable/Drawable;

    .line 13
    .line 14
    if-eqz v0, :cond_1

    .line 15
    .line 16
    invoke-virtual {v0, p1}, Landroid/graphics/drawable/Drawable;->draw(Landroid/graphics/Canvas;)V

    .line 17
    .line 18
    .line 19
    :cond_1
    :goto_0
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    iget-boolean p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->t:Z

    .line 2
    .line 3
    const/4 p2, 0x0

    .line 4
    if-eqz p1, :cond_0

    .line 5
    .line 6
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->a:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 9
    .line 10
    .line 11
    move-result p3

    .line 12
    iget-object p4, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 13
    .line 14
    invoke-virtual {p4}, Landroid/view/View;->getMeasuredHeight()I

    .line 15
    .line 16
    .line 17
    move-result p4

    .line 18
    invoke-virtual {p1, p2, p2, p3, p4}, Landroid/view/View;->layout(IIII)V

    .line 19
    .line 20
    .line 21
    return-void

    .line 22
    :cond_0
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->o:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;

    .line 23
    .line 24
    sget-object p3, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$b;->a:[I

    .line 25
    .line 26
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 27
    .line 28
    .line 29
    move-result p1

    .line 30
    aget p1, p3, p1

    .line 31
    .line 32
    const/4 p3, 0x1

    .line 33
    if-eq p1, p3, :cond_2

    .line 34
    .line 35
    const/4 p3, 0x2

    .line 36
    if-ne p1, p3, :cond_1

    .line 37
    .line 38
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->q()V

    .line 39
    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_1
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 43
    .line 44
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 45
    .line 46
    .line 47
    throw p1

    .line 48
    :cond_2
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->r()V

    .line 49
    .line 50
    .line 51
    :goto_0
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 52
    .line 53
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 54
    .line 55
    .line 56
    move-result p3

    .line 57
    iget-object p4, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 58
    .line 59
    invoke-virtual {p4}, Landroid/view/View;->getMeasuredHeight()I

    .line 60
    .line 61
    .line 62
    move-result p4

    .line 63
    invoke-virtual {p1, p2, p2, p3, p4}, Landroid/view/View;->layout(IIII)V

    .line 64
    .line 65
    .line 66
    return-void
.end method

.method public onMeasure(II)V
    .locals 5

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-static {p2}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->f:Landroid/graphics/drawable/ColorDrawable;

    .line 13
    .line 14
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 19
    .line 20
    .line 21
    move-result v2

    .line 22
    const/4 v3, 0x0

    .line 23
    invoke-virtual {v0, v3, v3, v1, v2}, Landroid/graphics/drawable/Drawable;->setBounds(IIII)V

    .line 24
    .line 25
    .line 26
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->n:Landroid/graphics/drawable/Drawable;

    .line 27
    .line 28
    if-eqz v0, :cond_0

    .line 29
    .line 30
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 31
    .line 32
    .line 33
    move-result v1

    .line 34
    iget v2, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->i:I

    .line 35
    .line 36
    sub-int/2addr v1, v2

    .line 37
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 38
    .line 39
    .line 40
    move-result v2

    .line 41
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 42
    .line 43
    .line 44
    move-result v4

    .line 45
    invoke-virtual {v0, v3, v1, v2, v4}, Landroid/graphics/drawable/Drawable;->setBounds(IIII)V

    .line 46
    .line 47
    .line 48
    :cond_0
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->m:Landroid/graphics/drawable/Drawable;

    .line 49
    .line 50
    if-eqz v0, :cond_1

    .line 51
    .line 52
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    sget v2, LlZ0/g;->size_24:I

    .line 57
    .line 58
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 59
    .line 60
    .line 61
    move-result v1

    .line 62
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 63
    .line 64
    .line 65
    move-result v2

    .line 66
    sub-int/2addr v2, v1

    .line 67
    div-int/lit8 v2, v2, 0x2

    .line 68
    .line 69
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 70
    .line 71
    .line 72
    move-result v3

    .line 73
    sub-int/2addr v3, v1

    .line 74
    div-int/lit8 v3, v3, 0x2

    .line 75
    .line 76
    add-int v4, v2, v1

    .line 77
    .line 78
    add-int/2addr v1, v3

    .line 79
    invoke-virtual {v0, v2, v3, v4, v1}, Landroid/graphics/drawable/Drawable;->setBounds(IIII)V

    .line 80
    .line 81
    .line 82
    :cond_1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->t(II)V

    .line 83
    .line 84
    .line 85
    return-void
.end method

.method public onRtlPropertiesChanged(I)V
    .locals 3

    .line 1
    invoke-super {p0, p1}, Landroid/widget/FrameLayout;->onRtlPropertiesChanged(I)V

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->s:I

    .line 5
    .line 6
    if-eq v0, p1, :cond_2

    .line 7
    .line 8
    iput p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->s:I

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/widget/ImageView;->getScaleType()Landroid/widget/ImageView$ScaleType;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    sget-object v1, Landroid/widget/ImageView$ScaleType;->FIT_START:Landroid/widget/ImageView$ScaleType;

    .line 17
    .line 18
    if-ne v0, v1, :cond_0

    .line 19
    .line 20
    if-nez p1, :cond_0

    .line 21
    .line 22
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 23
    .line 24
    sget-object v0, Landroid/widget/ImageView$ScaleType;->FIT_END:Landroid/widget/ImageView$ScaleType;

    .line 25
    .line 26
    invoke-virtual {p1, v0}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 27
    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_0
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 31
    .line 32
    invoke-virtual {v0}, Landroid/widget/ImageView;->getScaleType()Landroid/widget/ImageView$ScaleType;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    sget-object v2, Landroid/widget/ImageView$ScaleType;->FIT_END:Landroid/widget/ImageView$ScaleType;

    .line 37
    .line 38
    if-ne v0, v2, :cond_1

    .line 39
    .line 40
    const/4 v0, 0x1

    .line 41
    if-ne p1, v0, :cond_1

    .line 42
    .line 43
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 44
    .line 45
    invoke-virtual {p1, v1}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 46
    .line 47
    .line 48
    :cond_1
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 49
    .line 50
    .line 51
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 52
    .line 53
    .line 54
    :cond_2
    return-void
.end method

.method public final p(Landroid/content/res/TypedArray;)I
    .locals 2

    .line 1
    sget v0, Ls80/d;->AggregatorBanner_AggregatorBanner_titleTextSize:I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    return p1
.end method

.method public final q()V
    .locals 6

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    iget v1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->p:I

    .line 6
    .line 7
    sub-int/2addr v0, v1

    .line 8
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 9
    .line 10
    iget v2, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->j:I

    .line 11
    .line 12
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 13
    .line 14
    .line 15
    move-result v3

    .line 16
    sub-int v3, v0, v3

    .line 17
    .line 18
    iget-object v4, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 19
    .line 20
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredWidth()I

    .line 21
    .line 22
    .line 23
    move-result v4

    .line 24
    iget v5, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->j:I

    .line 25
    .line 26
    add-int/2addr v4, v5

    .line 27
    invoke-virtual {v1, v2, v3, v4, v0}, Landroid/view/View;->layout(IIII)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public final r()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    const/4 v0, 0x0

    .line 12
    :goto_0
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 13
    .line 14
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    if-nez v1, :cond_2

    .line 19
    .line 20
    if-nez v0, :cond_1

    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 23
    .line 24
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    iget-object v2, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 29
    .line 30
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 31
    .line 32
    .line 33
    move-result v2

    .line 34
    sub-int/2addr v1, v2

    .line 35
    div-int/lit8 v1, v1, 0x2

    .line 36
    .line 37
    invoke-virtual {p0, v0, v1}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->s(Landroid/widget/TextView;I)V

    .line 38
    .line 39
    .line 40
    return-void

    .line 41
    :cond_1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 42
    .line 43
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 44
    .line 45
    .line 46
    move-result v0

    .line 47
    iget v1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->k:I

    .line 48
    .line 49
    add-int/2addr v0, v1

    .line 50
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 51
    .line 52
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 53
    .line 54
    .line 55
    move-result v1

    .line 56
    add-int/2addr v1, v0

    .line 57
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 58
    .line 59
    .line 60
    move-result v2

    .line 61
    sub-int/2addr v2, v1

    .line 62
    div-int/lit8 v2, v2, 0x2

    .line 63
    .line 64
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 65
    .line 66
    invoke-virtual {p0, v1, v2}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->s(Landroid/widget/TextView;I)V

    .line 67
    .line 68
    .line 69
    add-int/2addr v2, v0

    .line 70
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 71
    .line 72
    invoke-virtual {p0, v0, v2}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->s(Landroid/widget/TextView;I)V

    .line 73
    .line 74
    .line 75
    return-void

    .line 76
    :cond_2
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 77
    .line 78
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 79
    .line 80
    .line 81
    move-result v1

    .line 82
    iget-object v2, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 83
    .line 84
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 85
    .line 86
    .line 87
    move-result v2

    .line 88
    sub-int/2addr v1, v2

    .line 89
    div-int/lit8 v1, v1, 0x2

    .line 90
    .line 91
    invoke-virtual {p0, v0, v1}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->s(Landroid/widget/TextView;I)V

    .line 92
    .line 93
    .line 94
    return-void
.end method

.method public final s(Landroid/widget/TextView;I)V
    .locals 3

    .line 1
    iget v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->s:I

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-ne v0, v1, :cond_0

    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    iget v1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->j:I

    .line 11
    .line 12
    sub-int/2addr v0, v1

    .line 13
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    sub-int v1, v0, v1

    .line 18
    .line 19
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    add-int/2addr v2, p2

    .line 24
    invoke-virtual {p1, v1, p2, v0, v2}, Landroid/view/View;->layout(IIII)V

    .line 25
    .line 26
    .line 27
    return-void

    .line 28
    :cond_0
    iget v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->j:I

    .line 29
    .line 30
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 31
    .line 32
    .line 33
    move-result v1

    .line 34
    iget v2, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->j:I

    .line 35
    .line 36
    add-int/2addr v1, v2

    .line 37
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 38
    .line 39
    .line 40
    move-result v2

    .line 41
    add-int/2addr v2, p2

    .line 42
    invoke-virtual {p1, v0, p2, v1, v2}, Landroid/view/View;->layout(IIII)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public final setImageUrl(Ljava/lang/String;)V
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x1

    .line 2
    invoke-direct {p0, v0}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->setPlaceHolderIconDrawableVisibility(Z)V

    .line 3
    .line 4
    .line 5
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->f:Landroid/graphics/drawable/ColorDrawable;

    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->getPlaceholderColor()I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    invoke-virtual {v0, v1}, Landroid/graphics/drawable/Drawable;->setTint(I)V

    .line 12
    .line 13
    .line 14
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->e:Lorg/xbet/uikit/utils/z;

    .line 15
    .line 16
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/utils/z;->o(Landroid/widget/ImageView;)V

    .line 19
    .line 20
    .line 21
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->e:Lorg/xbet/uikit/utils/z;

    .line 22
    .line 23
    invoke-static {p1}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-static {p1}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$setImageUrl$1;

    .line 32
    .line 33
    invoke-direct {v1, p0}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$setImageUrl$1;-><init>(Ljava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$setImageUrl$2;

    .line 37
    .line 38
    invoke-direct {v2, p0}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$setImageUrl$2;-><init>(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    const/4 v3, 0x0

    .line 42
    invoke-virtual {v0, p1, v3, v1, v2}, Lorg/xbet/uikit/utils/z;->v(LL11/c;LL11/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public final setLooksLikeShimmer(Z)V
    .locals 4

    .line 1
    const/4 v0, 0x1

    .line 2
    const/16 v1, 0x8

    .line 3
    .line 4
    if-eqz p1, :cond_0

    .line 5
    .line 6
    iput-boolean v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->t:Z

    .line 7
    .line 8
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 9
    .line 10
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 11
    .line 12
    .line 13
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 14
    .line 15
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 16
    .line 17
    .line 18
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 19
    .line 20
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d()V

    .line 24
    .line 25
    .line 26
    return-void

    .line 27
    :cond_0
    const/4 p1, 0x0

    .line 28
    iput-boolean p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->t:Z

    .line 29
    .line 30
    iget-object v2, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 31
    .line 32
    invoke-virtual {v2, p1}, Landroid/view/View;->setVisibility(I)V

    .line 33
    .line 34
    .line 35
    iget-object v2, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 36
    .line 37
    invoke-virtual {v2}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 38
    .line 39
    .line 40
    move-result-object v3

    .line 41
    invoke-interface {v3}, Ljava/lang/CharSequence;->length()I

    .line 42
    .line 43
    .line 44
    move-result v3

    .line 45
    if-lez v3, :cond_1

    .line 46
    .line 47
    const/4 v3, 0x1

    .line 48
    goto :goto_0

    .line 49
    :cond_1
    const/4 v3, 0x0

    .line 50
    :goto_0
    if-eqz v3, :cond_2

    .line 51
    .line 52
    const/4 v3, 0x0

    .line 53
    goto :goto_1

    .line 54
    :cond_2
    const/16 v3, 0x8

    .line 55
    .line 56
    :goto_1
    invoke-virtual {v2, v3}, Landroid/view/View;->setVisibility(I)V

    .line 57
    .line 58
    .line 59
    iget-object v2, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 60
    .line 61
    invoke-virtual {v2}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 62
    .line 63
    .line 64
    move-result-object v3

    .line 65
    invoke-interface {v3}, Ljava/lang/CharSequence;->length()I

    .line 66
    .line 67
    .line 68
    move-result v3

    .line 69
    if-lez v3, :cond_3

    .line 70
    .line 71
    goto :goto_2

    .line 72
    :cond_3
    const/4 v0, 0x0

    .line 73
    :goto_2
    if-eqz v0, :cond_4

    .line 74
    .line 75
    const/4 v1, 0x0

    .line 76
    :cond_4
    invoke-virtual {v2, v1}, Landroid/view/View;->setVisibility(I)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->w()V

    .line 80
    .line 81
    .line 82
    return-void
.end method

.method public final setSubtitle(Ljava/lang/String;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->o:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;->COMPACT:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;

    .line 4
    .line 5
    if-ne v0, v1, :cond_0

    .line 6
    .line 7
    return-void

    .line 8
    :cond_0
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 9
    .line 10
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 11
    .line 12
    .line 13
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    const/4 v0, 0x1

    .line 18
    if-lez p1, :cond_1

    .line 19
    .line 20
    const/4 p1, 0x1

    .line 21
    goto :goto_0

    .line 22
    :cond_1
    const/4 p1, 0x0

    .line 23
    :goto_0
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 24
    .line 25
    invoke-virtual {p0, v1, p1}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->f(Landroid/widget/TextView;Z)V

    .line 26
    .line 27
    .line 28
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 29
    .line 30
    if-eqz p1, :cond_2

    .line 31
    .line 32
    goto :goto_1

    .line 33
    :cond_2
    const/4 v0, 0x2

    .line 34
    :goto_1
    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method public final setTitle(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 7
    .line 8
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 9
    .line 10
    .line 11
    move-result p1

    .line 12
    if-lez p1, :cond_0

    .line 13
    .line 14
    const/4 p1, 0x1

    .line 15
    goto :goto_0

    .line 16
    :cond_0
    const/4 p1, 0x0

    .line 17
    :goto_0
    invoke-virtual {p0, v0, p1}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->f(Landroid/widget/TextView;Z)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final t(II)V
    .locals 5

    .line 1
    const/high16 v0, 0x40000000    # 2.0f

    .line 2
    .line 3
    invoke-static {p1, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    invoke-static {p2, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    iget-object v3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->a:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 12
    .line 13
    invoke-virtual {v3, v1, v2}, Landroid/view/View;->measure(II)V

    .line 14
    .line 15
    .line 16
    iget-object v3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 17
    .line 18
    invoke-virtual {v3, v1, v2}, Landroid/view/View;->measure(II)V

    .line 19
    .line 20
    .line 21
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 22
    .line 23
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    const/4 v2, 0x0

    .line 28
    const/4 v3, 0x1

    .line 29
    if-nez v1, :cond_0

    .line 30
    .line 31
    const/4 v1, 0x1

    .line 32
    goto :goto_0

    .line 33
    :cond_0
    const/4 v1, 0x0

    .line 34
    :goto_0
    iget-object v4, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 35
    .line 36
    invoke-virtual {v4}, Landroid/view/View;->getVisibility()I

    .line 37
    .line 38
    .line 39
    move-result v4

    .line 40
    if-nez v4, :cond_1

    .line 41
    .line 42
    const/4 v2, 0x1

    .line 43
    :cond_1
    if-nez v1, :cond_2

    .line 44
    .line 45
    if-nez v2, :cond_2

    .line 46
    .line 47
    goto :goto_1

    .line 48
    :cond_2
    iget v3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->j:I

    .line 49
    .line 50
    sub-int/2addr p1, v3

    .line 51
    iget v3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->p:I

    .line 52
    .line 53
    sub-int/2addr p1, v3

    .line 54
    invoke-static {p1, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 55
    .line 56
    .line 57
    move-result p1

    .line 58
    const/high16 v0, -0x80000000

    .line 59
    .line 60
    invoke-static {p2, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 61
    .line 62
    .line 63
    move-result p2

    .line 64
    if-eqz v1, :cond_3

    .line 65
    .line 66
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 67
    .line 68
    invoke-virtual {v0, p1, p2}, Landroid/view/View;->measure(II)V

    .line 69
    .line 70
    .line 71
    :cond_3
    if-eqz v2, :cond_4

    .line 72
    .line 73
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 74
    .line 75
    invoke-virtual {v0, p1, p2}, Landroid/view/View;->measure(II)V

    .line 76
    .line 77
    .line 78
    :cond_4
    :goto_1
    return-void
.end method

.method public final u(Lcom/bumptech/glide/load/engine/GlideException;)Z
    .locals 1

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, Lcom/bumptech/glide/load/engine/GlideException;->getMessage()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 10
    .line 11
    invoke-virtual {v0, p1}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 15
    .line 16
    invoke-virtual {p1}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 21
    .line 22
    .line 23
    move-result p1

    .line 24
    if-nez p1, :cond_1

    .line 25
    .line 26
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 27
    .line 28
    invoke-virtual {p1}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 33
    .line 34
    .line 35
    move-result p1

    .line 36
    if-nez p1, :cond_1

    .line 37
    .line 38
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->f:Landroid/graphics/drawable/ColorDrawable;

    .line 39
    .line 40
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->getPlaceholderColor()I

    .line 41
    .line 42
    .line 43
    move-result v0

    .line 44
    invoke-virtual {p1, v0}, Landroid/graphics/drawable/Drawable;->setTint(I)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 48
    .line 49
    .line 50
    :cond_1
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 51
    .line 52
    const/4 v0, 0x0

    .line 53
    invoke-virtual {p1, v0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 54
    .line 55
    .line 56
    const/4 p1, 0x1

    .line 57
    return p1
.end method

.method public final v(Landroid/graphics/Bitmap;)Z
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-direct {p0, v0}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->setPlaceHolderIconDrawableVisibility(Z)V

    .line 3
    .line 4
    .line 5
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    const/4 v1, 0x2

    .line 12
    div-int/2addr v0, v1

    .line 13
    if-eqz p1, :cond_0

    .line 14
    .line 15
    invoke-virtual {p1, v1, v0}, Landroid/graphics/Bitmap;->getPixel(II)I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->f:Landroid/graphics/drawable/ColorDrawable;

    .line 20
    .line 21
    invoke-virtual {v1, v0}, Landroid/graphics/drawable/Drawable;->setTint(I)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 25
    .line 26
    .line 27
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 28
    .line 29
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageBitmap(Landroid/graphics/Bitmap;)V

    .line 30
    .line 31
    .line 32
    const/4 p1, 0x1

    .line 33
    return p1

    .line 34
    :cond_0
    const/4 p1, 0x0

    .line 35
    invoke-virtual {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->u(Lcom/bumptech/glide/load/engine/GlideException;)Z

    .line 36
    .line 37
    .line 38
    move-result p1

    .line 39
    return p1
.end method

.method public final w()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->a:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->isAttachedToWindow()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 13
    .line 14
    .line 15
    :cond_0
    return-void
.end method

.method public final x()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setAdjustViewBounds(Z)V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->d:Landroidx/appcompat/widget/AppCompatImageView;

    .line 8
    .line 9
    iget-object v2, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->o:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;

    .line 10
    .line 11
    sget-object v3, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$b;->a:[I

    .line 12
    .line 13
    invoke-virtual {v2}, Ljava/lang/Enum;->ordinal()I

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    aget v2, v3, v2

    .line 18
    .line 19
    if-eq v2, v1, :cond_1

    .line 20
    .line 21
    const/4 v1, 0x2

    .line 22
    if-ne v2, v1, :cond_0

    .line 23
    .line 24
    sget-object v1, Landroid/widget/ImageView$ScaleType;->CENTER_CROP:Landroid/widget/ImageView$ScaleType;

    .line 25
    .line 26
    goto :goto_0

    .line 27
    :cond_0
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 28
    .line 29
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 30
    .line 31
    .line 32
    throw v0

    .line 33
    :cond_1
    sget-object v1, Lvb/a;->a:Lvb/a;

    .line 34
    .line 35
    invoke-virtual {v1}, Lvb/a;->c()Z

    .line 36
    .line 37
    .line 38
    move-result v1

    .line 39
    if-eqz v1, :cond_2

    .line 40
    .line 41
    sget-object v1, Landroid/widget/ImageView$ScaleType;->FIT_START:Landroid/widget/ImageView$ScaleType;

    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_2
    sget-object v1, Landroid/widget/ImageView$ScaleType;->FIT_END:Landroid/widget/ImageView$ScaleType;

    .line 45
    .line 46
    :goto_0
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 47
    .line 48
    .line 49
    return-void
.end method

.method public final y()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->a:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    new-instance v1, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 4
    .line 5
    const/4 v2, -0x1

    .line 6
    invoke-direct {v1, v2, v2}, Landroid/view/ViewGroup$MarginLayoutParams;-><init>(II)V

    .line 7
    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 10
    .line 11
    .line 12
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$c;

    .line 13
    .line 14
    invoke-direct {v0, p0}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$c;-><init>(Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0, v0}, Landroid/view/View;->addOnAttachStateChangeListener(Landroid/view/View$OnAttachStateChangeListener;)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final z()V
    .locals 10

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget v1, LlZ0/g;->text_12:I

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 8
    .line 9
    .line 10
    move-result v6

    .line 11
    iget-object v3, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 12
    .line 13
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    sget v1, LlZ0/f;->static_white:I

    .line 18
    .line 19
    invoke-static {v0, v1}, LF0/b;->getColor(Landroid/content/Context;I)I

    .line 20
    .line 21
    .line 22
    move-result v4

    .line 23
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    sget v1, LlZ0/g;->line_height_18:I

    .line 28
    .line 29
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 30
    .line 31
    .line 32
    move-result v5

    .line 33
    iget v7, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->r:I

    .line 34
    .line 35
    move-object v2, p0

    .line 36
    invoke-virtual/range {v2 .. v7}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->e(Landroidx/appcompat/widget/AppCompatTextView;IIII)V

    .line 37
    .line 38
    .line 39
    iget-object v0, v2, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 40
    .line 41
    const/16 v1, 0x8

    .line 42
    .line 43
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 44
    .line 45
    .line 46
    iget-object v0, v2, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 47
    .line 48
    const/4 v8, 0x5

    .line 49
    invoke-virtual {v0, v8}, Landroid/view/View;->setTextDirection(I)V

    .line 50
    .line 51
    .line 52
    iget-object v0, v2, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 53
    .line 54
    iget-object v3, v2, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->o:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$BannerStyle;

    .line 55
    .line 56
    sget-object v4, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$b;->a:[I

    .line 57
    .line 58
    invoke-virtual {v3}, Ljava/lang/Enum;->ordinal()I

    .line 59
    .line 60
    .line 61
    move-result v3

    .line 62
    aget v3, v4, v3

    .line 63
    .line 64
    const/4 v4, 0x1

    .line 65
    const/16 v9, 0x10

    .line 66
    .line 67
    if-eq v3, v4, :cond_1

    .line 68
    .line 69
    const/4 v4, 0x2

    .line 70
    if-ne v3, v4, :cond_0

    .line 71
    .line 72
    const/16 v3, 0x50

    .line 73
    .line 74
    goto :goto_0

    .line 75
    :cond_0
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 76
    .line 77
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 78
    .line 79
    .line 80
    throw v0

    .line 81
    :cond_1
    const/16 v3, 0x10

    .line 82
    .line 83
    :goto_0
    invoke-virtual {v0, v3}, Landroid/widget/TextView;->setGravity(I)V

    .line 84
    .line 85
    .line 86
    iget-object v3, v2, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 87
    .line 88
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 89
    .line 90
    .line 91
    move-result-object v0

    .line 92
    sget v4, LlZ0/f;->static_white_80:I

    .line 93
    .line 94
    invoke-static {v0, v4}, LF0/b;->getColor(Landroid/content/Context;I)I

    .line 95
    .line 96
    .line 97
    move-result v4

    .line 98
    iget v5, v2, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->q:I

    .line 99
    .line 100
    move v7, v6

    .line 101
    invoke-virtual/range {v2 .. v7}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->e(Landroidx/appcompat/widget/AppCompatTextView;IIII)V

    .line 102
    .line 103
    .line 104
    iget-object v0, v2, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 105
    .line 106
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 107
    .line 108
    .line 109
    iget-object v0, v2, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 110
    .line 111
    invoke-virtual {v0, v8}, Landroid/view/View;->setTextDirection(I)V

    .line 112
    .line 113
    .line 114
    iget-object v0, v2, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 115
    .line 116
    invoke-virtual {v0, v9}, Landroid/widget/TextView;->setGravity(I)V

    .line 117
    .line 118
    .line 119
    return-void
.end method
