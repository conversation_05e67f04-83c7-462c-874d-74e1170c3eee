.class public final LIN0/v$a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LIN0/v$a;->a(Landroidx/compose/foundation/layout/m;Landroidx/compose/runtime/j;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "LOc/n<",
        "Landroidx/compose/animation/e;",
        "Landroidx/compose/runtime/j;",
        "Ljava/lang/Integer;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LOc/n;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LOc/n<",
            "Landroidx/compose/foundation/layout/m;",
            "Landroidx/compose/runtime/j;",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public final synthetic b:Landroidx/compose/foundation/layout/m;


# direct methods
.method public constructor <init>(LOc/n;Landroidx/compose/foundation/layout/m;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LOc/n<",
            "-",
            "Landroidx/compose/foundation/layout/m;",
            "-",
            "Landroidx/compose/runtime/j;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/foundation/layout/m;",
            ")V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LIN0/v$a$a;->a:LOc/n;

    .line 2
    .line 3
    iput-object p2, p0, LIN0/v$a$a;->b:Landroidx/compose/foundation/layout/m;

    .line 4
    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a(Landroidx/compose/animation/e;Landroidx/compose/runtime/j;I)V
    .locals 2

    .line 1
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    const/4 p1, -0x1

    .line 8
    const-string v0, "org.xbet.statistic.statistic_core.presentation.composable.StatisticBoxCardComponent.<anonymous>.<anonymous>.<anonymous> (StatisticBoxCardComponent.kt:112)"

    .line 9
    .line 10
    const v1, 0x7eb37701

    .line 11
    .line 12
    .line 13
    invoke-static {v1, p3, p1, v0}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 14
    .line 15
    .line 16
    :cond_0
    iget-object p1, p0, LIN0/v$a$a;->a:LOc/n;

    .line 17
    .line 18
    iget-object p3, p0, LIN0/v$a$a;->b:Landroidx/compose/foundation/layout/m;

    .line 19
    .line 20
    const/4 v0, 0x0

    .line 21
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-interface {p1, p3, p2, v0}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 29
    .line 30
    .line 31
    move-result p1

    .line 32
    if-eqz p1, :cond_1

    .line 33
    .line 34
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 35
    .line 36
    .line 37
    :cond_1
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/animation/e;

    .line 2
    .line 3
    check-cast p2, Landroidx/compose/runtime/j;

    .line 4
    .line 5
    check-cast p3, Ljava/lang/Number;

    .line 6
    .line 7
    invoke-virtual {p3}, Ljava/lang/Number;->intValue()I

    .line 8
    .line 9
    .line 10
    move-result p3

    .line 11
    invoke-virtual {p0, p1, p2, p3}, LIN0/v$a$a;->a(Landroidx/compose/animation/e;Landroidx/compose/runtime/j;I)V

    .line 12
    .line 13
    .line 14
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p1
.end method
