.class public final synthetic Lorg/xbet/main_menu/impl/presentation/container/y;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/y;->a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/y;->a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    invoke-static {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->s3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
