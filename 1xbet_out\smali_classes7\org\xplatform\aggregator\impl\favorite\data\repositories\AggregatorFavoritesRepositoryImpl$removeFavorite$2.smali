.class final Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.favorite.data.repositories.AggregatorFavoritesRepositoryImpl$removeFavorite$2"
    f = "AggregatorFavoritesRepositoryImpl.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->k(JZILkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlinx/coroutines/x0;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "Lkotlinx/coroutines/x0;",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $brandsApi:Z

.field final synthetic $gameId:J

.field final synthetic $subcategoryId:I

.field private synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ZJILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;",
            "ZJI",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    iput-boolean p2, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->$brandsApi:Z

    iput-wide p3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->$gameId:J

    iput p5, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->$subcategoryId:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p6}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method

.method public static synthetic a(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->c(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private static final c(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->s(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)LT91/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const/4 p1, 0x0

    .line 6
    invoke-virtual {p0, p1}, LT91/a;->p(Z)V

    .line 7
    .line 8
    .line 9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    return-object p0
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->$brandsApi:Z

    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->$gameId:J

    iget v5, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->$subcategoryId:I

    move-object v6, p2

    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ZJILkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlinx/coroutines/x0;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    move-object v0, p1

    .line 14
    check-cast v0, Lkotlinx/coroutines/N;

    .line 15
    .line 16
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;

    .line 17
    .line 18
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 19
    .line 20
    iget-boolean v3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->$brandsApi:Z

    .line 21
    .line 22
    iget-wide v4, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->$gameId:J

    .line 23
    .line 24
    iget v6, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->$subcategoryId:I

    .line 25
    .line 26
    const/4 v7, 0x0

    .line 27
    invoke-direct/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ZJILkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    const/4 v4, 0x3

    .line 31
    const/4 v5, 0x0

    .line 32
    move-object v3, v1

    .line 33
    const/4 v1, 0x0

    .line 34
    const/4 v2, 0x0

    .line 35
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 40
    .line 41
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/b;

    .line 42
    .line 43
    invoke-direct {v1, v0}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/b;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)V

    .line 44
    .line 45
    .line 46
    invoke-interface {p1, v1}, Lkotlinx/coroutines/x0;->l(Lkotlin/jvm/functions/Function1;)Lkotlinx/coroutines/d0;

    .line 47
    .line 48
    .line 49
    return-object p1

    .line 50
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 51
    .line 52
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 53
    .line 54
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    throw p1
.end method
