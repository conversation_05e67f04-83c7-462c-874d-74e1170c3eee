.class public final enum Lorg/xbet/crystal/domain/models/CrystalTypeEnum;
.super Ljava/lang/Enum;
.source "SourceFile"

# interfaces
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/crystal/domain/models/CrystalTypeEnum$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/crystal/domain/models/CrystalTypeEnum;",
        ">;",
        "Ljava/io/Serializable;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0010\u0008\n\u0002\u0008\u0010\u0008\u0086\u0081\u0002\u0018\u0000 \n2\u00020\u00012\u0008\u0012\u0004\u0012\u00020\u00000\u0002:\u0001\u000bB\u0011\u0008\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0005\u0010\u0006R\u0017\u0010\u0004\u001a\u00020\u00038\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0004\u0010\u0007\u001a\u0004\u0008\u0008\u0010\tj\u0002\u0008\u000cj\u0002\u0008\rj\u0002\u0008\u000ej\u0002\u0008\u000fj\u0002\u0008\u0010j\u0002\u0008\u0011j\u0002\u0008\u0012\u00a8\u0006\u0013"
    }
    d2 = {
        "Lorg/xbet/crystal/domain/models/CrystalTypeEnum;",
        "Ljava/io/Serializable;",
        "",
        "",
        "value",
        "<init>",
        "(Ljava/lang/String;II)V",
        "I",
        "getValue",
        "()I",
        "Companion",
        "a",
        "WILD_COIN",
        "RED",
        "PURPLE",
        "GREEN",
        "ORANGE",
        "DIAMOND",
        "BLUE",
        "crystal_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

.field public static final enum BLUE:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

.field public static final Companion:Lorg/xbet/crystal/domain/models/CrystalTypeEnum$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final enum DIAMOND:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

.field public static final enum GREEN:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

.field public static final enum ORANGE:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

.field public static final enum PURPLE:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

.field public static final enum RED:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

.field public static final enum WILD_COIN:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

.field private static final map:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lorg/xbet/crystal/domain/models/CrystalTypeEnum;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field private final value:I


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 2
    .line 3
    const-string v1, "WILD_COIN"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2, v2}, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;-><init>(Ljava/lang/String;II)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->WILD_COIN:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 12
    .line 13
    const-string v1, "RED"

    .line 14
    .line 15
    const/4 v3, 0x1

    .line 16
    invoke-direct {v0, v1, v3, v3}, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;-><init>(Ljava/lang/String;II)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->RED:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 20
    .line 21
    new-instance v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 22
    .line 23
    const-string v1, "PURPLE"

    .line 24
    .line 25
    const/4 v3, 0x2

    .line 26
    invoke-direct {v0, v1, v3, v3}, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;-><init>(Ljava/lang/String;II)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->PURPLE:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 30
    .line 31
    new-instance v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 32
    .line 33
    const-string v1, "GREEN"

    .line 34
    .line 35
    const/4 v3, 0x3

    .line 36
    invoke-direct {v0, v1, v3, v3}, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;-><init>(Ljava/lang/String;II)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->GREEN:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 42
    .line 43
    const-string v1, "ORANGE"

    .line 44
    .line 45
    const/4 v3, 0x4

    .line 46
    invoke-direct {v0, v1, v3, v3}, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;-><init>(Ljava/lang/String;II)V

    .line 47
    .line 48
    .line 49
    sput-object v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->ORANGE:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 50
    .line 51
    new-instance v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 52
    .line 53
    const-string v1, "DIAMOND"

    .line 54
    .line 55
    const/4 v3, 0x5

    .line 56
    invoke-direct {v0, v1, v3, v3}, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;-><init>(Ljava/lang/String;II)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->DIAMOND:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 60
    .line 61
    new-instance v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 62
    .line 63
    const-string v1, "BLUE"

    .line 64
    .line 65
    const/4 v3, 0x6

    .line 66
    invoke-direct {v0, v1, v3, v3}, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;-><init>(Ljava/lang/String;II)V

    .line 67
    .line 68
    .line 69
    sput-object v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->BLUE:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 70
    .line 71
    invoke-static {}, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->a()[Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 72
    .line 73
    .line 74
    move-result-object v0

    .line 75
    sput-object v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->$VALUES:[Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 76
    .line 77
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    sput-object v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->$ENTRIES:Lkotlin/enums/a;

    .line 82
    .line 83
    new-instance v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum$a;

    .line 84
    .line 85
    const/4 v1, 0x0

    .line 86
    invoke-direct {v0, v1}, Lorg/xbet/crystal/domain/models/CrystalTypeEnum$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 87
    .line 88
    .line 89
    sput-object v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->Companion:Lorg/xbet/crystal/domain/models/CrystalTypeEnum$a;

    .line 90
    .line 91
    invoke-static {}, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->values()[Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 92
    .line 93
    .line 94
    move-result-object v0

    .line 95
    array-length v1, v0

    .line 96
    invoke-static {v1}, Lkotlin/collections/P;->e(I)I

    .line 97
    .line 98
    .line 99
    move-result v1

    .line 100
    const/16 v3, 0x10

    .line 101
    .line 102
    invoke-static {v1, v3}, Lkotlin/ranges/f;->g(II)I

    .line 103
    .line 104
    .line 105
    move-result v1

    .line 106
    new-instance v3, Ljava/util/LinkedHashMap;

    .line 107
    .line 108
    invoke-direct {v3, v1}, Ljava/util/LinkedHashMap;-><init>(I)V

    .line 109
    .line 110
    .line 111
    array-length v1, v0

    .line 112
    :goto_0
    if-ge v2, v1, :cond_0

    .line 113
    .line 114
    aget-object v4, v0, v2

    .line 115
    .line 116
    iget v5, v4, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->value:I

    .line 117
    .line 118
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 119
    .line 120
    .line 121
    move-result-object v5

    .line 122
    invoke-interface {v3, v5, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    add-int/lit8 v2, v2, 0x1

    .line 126
    .line 127
    goto :goto_0

    .line 128
    :cond_0
    sput-object v3, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->map:Ljava/util/Map;

    .line 129
    .line 130
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;II)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    iput p3, p0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->value:I

    .line 5
    .line 6
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/crystal/domain/models/CrystalTypeEnum;
    .locals 3

    .line 1
    const/4 v0, 0x7

    new-array v0, v0, [Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    sget-object v1, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->WILD_COIN:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->RED:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->PURPLE:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->GREEN:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->ORANGE:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->DIAMOND:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->BLUE:Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static final synthetic access$getMap$cp()Ljava/util/Map;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->map:Ljava/util/Map;

    .line 2
    .line 3
    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/crystal/domain/models/CrystalTypeEnum;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/crystal/domain/models/CrystalTypeEnum;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/crystal/domain/models/CrystalTypeEnum;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->$VALUES:[Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/crystal/domain/models/CrystalTypeEnum;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getValue()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/crystal/domain/models/CrystalTypeEnum;->value:I

    .line 2
    .line 3
    return v0
.end method
