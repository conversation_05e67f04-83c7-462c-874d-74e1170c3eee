.class public final LRN0/b;
.super Ljava/lang/Object;


# static fields
.field public static characteristic_card_view:I = 0x7f0d00d9

.field public static football_team_squad_item:I = 0x7f0d031b

.field public static football_team_squad_table_header:I = 0x7f0d031c

.field public static fragment_feature_match:I = 0x7f0d039c

.field public static fragment_team_champ_statistic:I = 0x7f0d04b7

.field public static fragment_team_characteristic_statistic:I = 0x7f0d04b8

.field public static fragment_team_completed_matches:I = 0x7f0d04b9

.field public static fragment_team_one_statistic:I = 0x7f0d04bc

.field public static fragment_team_rating_chart:I = 0x7f0d04bd

.field public static fragment_team_squad:I = 0x7f0d04be

.field public static fragment_team_statistic:I = 0x7f0d04bf

.field public static fragment_team_statistic_menu_items:I = 0x7f0d04c0

.field public static fragment_team_transfer:I = 0x7f0d04c1

.field public static item_team_transfer:I = 0x7f0d06f7

.field public static line_statistic:I = 0x7f0d0764

.field public static shimmer_team_characteristic:I = 0x7f0d08eb

.field public static table_shimmer_list:I = 0x7f0d09f7

.field public static team_champ_shimmer:I = 0x7f0d0a01

.field public static team_squad_filter_chip:I = 0x7f0d0a04

.field public static team_squad_item:I = 0x7f0d0a05

.field public static team_squad_table_header:I = 0x7f0d0a06

.field public static view_holder_forecast:I = 0x7f0d0b30

.field public static view_holder_teams_characteristic:I = 0x7f0d0b41


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
