.class public final Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/tournamentgrid/viewholder/TournamentTournamentGridViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "LMx0/a;",
        "tournamentTournamentGridClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "d",
        "(LMx0/a;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LMx0/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/tournamentgrid/viewholder/TournamentTournamentGridViewHolderKt;->g(LMx0/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/l1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/tournamentgrid/viewholder/TournamentTournamentGridViewHolderKt;->e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/l1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LMx0/a;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/tournamentgrid/viewholder/TournamentTournamentGridViewHolderKt;->f(LMx0/a;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final d(LMx0/a;)LA4/c;
    .locals 4
    .param p0    # LMx0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LMx0/a;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LOx0/a;

    .line 2
    .line 3
    invoke-direct {v0}, LOx0/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LOx0/b;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LOx0/b;-><init>(LMx0/a;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/tournamentgrid/viewholder/TournamentTournamentGridViewHolderKt$tournamentTournamentGridAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/tournamentgrid/viewholder/TournamentTournamentGridViewHolderKt$tournamentTournamentGridAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/tournamentgrid/viewholder/TournamentTournamentGridViewHolderKt$tournamentTournamentGridAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/tournamentgrid/viewholder/TournamentTournamentGridViewHolderKt$tournamentTournamentGridAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/l1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/l1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/l1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final f(LMx0/a;LB4/a;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LGq0/l1;

    .line 6
    .line 7
    iget-object p1, p1, LGq0/l1;->d:Lorg/xbet/uikit/components/cells/SettingsCell;

    .line 8
    .line 9
    new-instance v0, LOx0/c;

    .line 10
    .line 11
    invoke-direct {v0, p0}, LOx0/c;-><init>(LMx0/a;)V

    .line 12
    .line 13
    .line 14
    const/4 p0, 0x1

    .line 15
    const/4 v1, 0x0

    .line 16
    invoke-static {p1, v1, v0, p0, v1}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 17
    .line 18
    .line 19
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 20
    .line 21
    return-object p0
.end method

.method public static final g(LMx0/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0}, LMx0/a;->S()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method
