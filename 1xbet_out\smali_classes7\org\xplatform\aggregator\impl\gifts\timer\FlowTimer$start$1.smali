.class final Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.timer.FlowTimer$start$1"
    f = "FlowTimer.kt"
    l = {
        0x2c
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->l(J)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field private synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->L$0:Ljava/lang/Object;

    .line 13
    .line 14
    check-cast v1, Lkotlinx/coroutines/N;

    .line 15
    .line 16
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    goto :goto_1

    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->L$0:Ljava/lang/Object;

    .line 32
    .line 33
    check-cast p1, Lkotlinx/coroutines/N;

    .line 34
    .line 35
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 36
    .line 37
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->g(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)Lkotlinx/coroutines/flow/V;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    sget-object v3, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$PlayerMode;->PLAYING:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$PlayerMode;

    .line 42
    .line 43
    invoke-interface {v1, v3}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 44
    .line 45
    .line 46
    move-object v1, p1

    .line 47
    :cond_2
    :goto_0
    invoke-static {v1}, Lkotlinx/coroutines/O;->h(Lkotlinx/coroutines/N;)Z

    .line 48
    .line 49
    .line 50
    move-result p1

    .line 51
    if-eqz p1, :cond_5

    .line 52
    .line 53
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 54
    .line 55
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->h(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)Lkotlinx/coroutines/flow/V;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    check-cast p1, Ljava/lang/Number;

    .line 64
    .line 65
    invoke-virtual {p1}, Ljava/lang/Number;->longValue()J

    .line 66
    .line 67
    .line 68
    move-result-wide v3

    .line 69
    const-wide/16 v5, 0x0

    .line 70
    .line 71
    cmp-long p1, v3, v5

    .line 72
    .line 73
    if-gtz p1, :cond_3

    .line 74
    .line 75
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 76
    .line 77
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->i(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)Lkotlinx/coroutines/x0;

    .line 78
    .line 79
    .line 80
    move-result-object p1

    .line 81
    const/4 v0, 0x0

    .line 82
    invoke-static {p1, v0, v2, v0}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 83
    .line 84
    .line 85
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 86
    .line 87
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->e(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)Lkotlin/jvm/functions/Function0;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    invoke-interface {p1}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 92
    .line 93
    .line 94
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 95
    .line 96
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->g(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)Lkotlinx/coroutines/flow/V;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    sget-object v0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$PlayerMode;->STOPPED:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$PlayerMode;

    .line 101
    .line 102
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 103
    .line 104
    .line 105
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 106
    .line 107
    return-object p1

    .line 108
    :cond_3
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 109
    .line 110
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->d(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)J

    .line 111
    .line 112
    .line 113
    move-result-wide v3

    .line 114
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->L$0:Ljava/lang/Object;

    .line 115
    .line 116
    iput v2, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->label:I

    .line 117
    .line 118
    invoke-static {v3, v4, p0}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    if-ne p1, v0, :cond_4

    .line 123
    .line 124
    return-object v0

    .line 125
    :cond_4
    :goto_1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 126
    .line 127
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->h(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)Lkotlinx/coroutines/flow/V;

    .line 128
    .line 129
    .line 130
    move-result-object p1

    .line 131
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 132
    .line 133
    .line 134
    move-result-object v3

    .line 135
    check-cast v3, Ljava/lang/Number;

    .line 136
    .line 137
    invoke-virtual {v3}, Ljava/lang/Number;->longValue()J

    .line 138
    .line 139
    .line 140
    move-result-wide v3

    .line 141
    iget-object v5, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 142
    .line 143
    invoke-static {v5}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->d(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)J

    .line 144
    .line 145
    .line 146
    move-result-wide v5

    .line 147
    sub-long/2addr v3, v5

    .line 148
    invoke-static {v3, v4}, LHc/a;->f(J)Ljava/lang/Long;

    .line 149
    .line 150
    .line 151
    move-result-object v3

    .line 152
    invoke-interface {p1, v3}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 153
    .line 154
    .line 155
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 156
    .line 157
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->f(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)Lkotlin/jvm/functions/Function1;

    .line 158
    .line 159
    .line 160
    move-result-object p1

    .line 161
    if-eqz p1, :cond_2

    .line 162
    .line 163
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer$start$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 164
    .line 165
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->h(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;)Lkotlinx/coroutines/flow/V;

    .line 166
    .line 167
    .line 168
    move-result-object v3

    .line 169
    invoke-interface {v3}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 170
    .line 171
    .line 172
    move-result-object v3

    .line 173
    invoke-interface {p1, v3}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 174
    .line 175
    .line 176
    goto/16 :goto_0

    .line 177
    .line 178
    :cond_5
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 179
    .line 180
    return-object p1
.end method
