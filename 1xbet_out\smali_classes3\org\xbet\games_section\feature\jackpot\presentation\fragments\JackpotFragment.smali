.class public final Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0006\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0018\u0000 G2\u00020\u0001:\u0001HB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0017\u0010\t\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\t\u0010\u0008J\u0017\u0010\n\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u0008J\u001f\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u000f\u0010\u0010\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0003J\u000f\u0010\u0011\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0003J\u0017\u0010\u0014\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u000f\u0010\u0016\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0003J\u0017\u0010\u0019\u001a\u00020\u00062\u0006\u0010\u0018\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u000f\u0010\u001b\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u0003J\u000f\u0010\u001c\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u0003J\u001f\u0010\u001f\u001a\u00020\u00042\u0006\u0010\u001e\u001a\u00020\u001d2\u0006\u0010\r\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 J\u000f\u0010!\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008!\u0010\u0003J\u000f\u0010\"\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\"\u0010\u0003J\u000f\u0010#\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008#\u0010\u0003J\u000f\u0010$\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008$\u0010\u0003J\u0019\u0010\'\u001a\u00020\u00062\u0008\u0010&\u001a\u0004\u0018\u00010%H\u0014\u00a2\u0006\u0004\u0008\'\u0010(J\u000f\u0010)\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008)\u0010\u0003J\u000f\u0010*\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008*\u0010\u0003R\"\u00102\u001a\u00020+8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008,\u0010-\u001a\u0004\u0008.\u0010/\"\u0004\u00080\u00101R\"\u0010:\u001a\u0002038\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u00084\u00105\u001a\u0004\u00086\u00107\"\u0004\u00088\u00109R\u001b\u0010@\u001a\u00020;8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008<\u0010=\u001a\u0004\u0008>\u0010?R\u001b\u0010F\u001a\u00020A8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008B\u0010C\u001a\u0004\u0008D\u0010E\u00a8\u0006I"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "url",
        "",
        "W2",
        "(Ljava/lang/String;)V",
        "X2",
        "Y2",
        "LP40/c;",
        "jackpotModel",
        "currencySymbol",
        "c3",
        "(LP40/c;Ljava/lang/String;)V",
        "S2",
        "T2",
        "",
        "visibility",
        "Z2",
        "(Z)V",
        "P2",
        "Lorg/xbet/uikit/components/lottie/a;",
        "lottieConfig",
        "b3",
        "(Lorg/xbet/uikit/components/lottie/a;)V",
        "a3",
        "Q2",
        "",
        "amount",
        "L2",
        "(DLjava/lang/String;)Ljava/lang/String;",
        "s2",
        "x2",
        "onStart",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "onResume",
        "LO40/c$b;",
        "i0",
        "LO40/c$b;",
        "M2",
        "()LO40/c$b;",
        "setJackpotViewModelFactory",
        "(LO40/c$b;)V",
        "jackpotViewModelFactory",
        "LTZ0/a;",
        "j0",
        "LTZ0/a;",
        "K2",
        "()LTZ0/a;",
        "setActionDialogManager",
        "(LTZ0/a;)V",
        "actionDialogManager",
        "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;",
        "k0",
        "Lkotlin/j;",
        "O2",
        "()Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;",
        "viewModel",
        "LN40/a;",
        "l0",
        "LRc/c;",
        "N2",
        "()LN40/a;",
        "viewBinding",
        "m0",
        "a",
        "jackpot_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final m0:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic n0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public i0:LO40/c$b;

.field public j0:LTZ0/a;

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getViewBinding()Lorg/xbet/games_section/feature/jackpot/databinding/FragmentOneXGamesJackpotFgBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;

    .line 7
    .line 8
    const-string v4, "viewBinding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->n0:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->m0:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$a;

    .line 31
    .line 32
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LI40/b;->fragment_one_x_games_jackpot_fg:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/c;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/c;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)V

    .line 9
    .line 10
    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$special$$inlined$viewModels$default$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 17
    .line 18
    new-instance v3, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$special$$inlined$viewModels$default$2;

    .line 19
    .line 20
    invoke-direct {v3, v1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 21
    .line 22
    .line 23
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const-class v2, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 28
    .line 29
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    new-instance v3, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$special$$inlined$viewModels$default$3;

    .line 34
    .line 35
    invoke-direct {v3, v1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 36
    .line 37
    .line 38
    new-instance v4, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$special$$inlined$viewModels$default$4;

    .line 39
    .line 40
    const/4 v5, 0x0

    .line 41
    invoke-direct {v4, v5, v1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->k0:Lkotlin/j;

    .line 49
    .line 50
    sget-object v0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$viewBinding$2;->INSTANCE:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$viewBinding$2;

    .line 51
    .line 52
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->l0:LRc/c;

    .line 57
    .line 58
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->V2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic B2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->U2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Landroid/view/View;)V

    return-void
.end method

.method public static final synthetic C2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->O2()Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic D2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->P2()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->W2(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic F2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->X2(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic G2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->Y2(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic H2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->a3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic I2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Lorg/xbet/uikit/components/lottie/a;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->b3(Lorg/xbet/uikit/components/lottie/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic J2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;LP40/c;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->c3(LP40/c;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final P2()V
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    invoke-virtual {p0, v0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->Z2(Z)V

    .line 3
    .line 4
    .line 5
    return-void
.end method

.method private final Q2()V
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/d;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/d;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)V

    .line 4
    .line 5
    .line 6
    const-string v1, "CHANGE_BONUS_BALANCE_TO_PRIMARY_KEY"

    .line 7
    .line 8
    invoke-static {p0, v1, v0}, LVZ0/c;->e(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static final R2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->O2()Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->K3()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method private final T2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LN40/a;->m:Lcom/google/android/material/appbar/MaterialToolbar;

    .line 6
    .line 7
    new-instance v1, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/a;

    .line 8
    .line 9
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/a;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0, v1}, Landroidx/appcompat/widget/Toolbar;->setNavigationOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    iget-object v0, v0, LN40/a;->i:Landroidx/appcompat/widget/AppCompatImageView;

    .line 20
    .line 21
    new-instance v1, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/b;

    .line 22
    .line 23
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/b;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public static final U2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->O2()Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->onBackPressed()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public static final V2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->O2()Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->N3()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method private final a3()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->K2()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 8
    .line 9
    sget v3, Lpb/k;->attention:I

    .line 10
    .line 11
    invoke-virtual {v0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    sget v4, Lpb/k;->unacceptable_account_for_section:I

    .line 16
    .line 17
    invoke-virtual {v0, v4}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v4

    .line 21
    sget v5, Lpb/k;->ok:I

    .line 22
    .line 23
    invoke-virtual {v0, v5}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v5

    .line 27
    sget-object v13, Lorg/xbet/uikit/components/dialog/AlertType;->WARNING:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 28
    .line 29
    const/16 v15, 0xbd8

    .line 30
    .line 31
    const/16 v16, 0x0

    .line 32
    .line 33
    const/4 v6, 0x0

    .line 34
    const/4 v7, 0x0

    .line 35
    const-string v8, "CHANGE_BONUS_BALANCE_TO_PRIMARY_KEY"

    .line 36
    .line 37
    const/4 v9, 0x0

    .line 38
    const/4 v10, 0x0

    .line 39
    const/4 v11, 0x0

    .line 40
    const/4 v12, 0x0

    .line 41
    const/4 v14, 0x0

    .line 42
    invoke-direct/range {v2 .. v16}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 43
    .line 44
    .line 45
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 46
    .line 47
    .line 48
    move-result-object v3

    .line 49
    invoke-virtual {v1, v2, v3}, LTZ0/a;->d(Lorg/xbet/uikit/components/dialog/DialogFields;Landroidx/fragment/app/FragmentManager;)V

    .line 50
    .line 51
    .line 52
    return-void
.end method

.method private final b3(Lorg/xbet/uikit/components/lottie/a;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LN40/a;->d:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/lottie/LottieView;->L(Lorg/xbet/uikit/components/lottie/a;)V

    .line 8
    .line 9
    .line 10
    const/4 p1, 0x0

    .line 11
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->Z2(Z)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public static final d3(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)Landroidx/lifecycle/e0$c;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/a;

    .line 2
    .line 3
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->M2()LO40/c$b;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, v1, p0}, Lorg/xbet/ui_common/viewmodel/core/a;-><init>(LwX0/c;LQW0/i;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->R2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->d3(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final K2()LTZ0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->j0:LTZ0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final L2(DLjava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 1
    sget-object v0, Ll8/j;->a:Ll8/j;

    .line 2
    .line 3
    sget-object v1, Lcom/xbet/onexcore/utils/ValueType;->AMOUNT:Lcom/xbet/onexcore/utils/ValueType;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2, p3, v1}, Ll8/j;->e(DLjava/lang/String;Lcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-static {p1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    return-object p1
.end method

.method public final M2()LO40/c$b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->i0:LO40/c$b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final N2()LN40/a;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->l0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->n0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LN40/a;

    .line 13
    .line 14
    return-object v0
.end method

.method public final O2()Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final S2()V
    .locals 3

    .line 1
    sget-object v0, LS40/a;->a:LS40/a;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    iget-object v2, v2, LN40/a;->m:Lcom/google/android/material/appbar/MaterialToolbar;

    .line 12
    .line 13
    invoke-virtual {v0, v1, v2}, LS40/a;->b(Landroid/app/Activity;Landroid/view/View;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final W2(Ljava/lang/String;)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LN40/a;->e:Landroid/widget/ImageView;

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-static {v0}, Lcom/bumptech/glide/b;->t(Landroid/content/Context;)Lcom/bumptech/glide/i;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    new-instance v1, Lorg/xbet/ui_common/utils/Y;

    .line 16
    .line 17
    invoke-direct {v1, p1}, Lorg/xbet/ui_common/utils/Y;-><init>(Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {v0, v1}, Lcom/bumptech/glide/i;->w(Ljava/lang/Object;)Lcom/bumptech/glide/h;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    new-instance v0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$b;

    .line 25
    .line 26
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$b;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p1, v0}, Lcom/bumptech/glide/h;->K0(Lcom/bumptech/glide/request/g;)Lcom/bumptech/glide/h;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    sget-object v0, Lorg/xbet/ui_common/utils/g;->a:Lorg/xbet/ui_common/utils/g;

    .line 34
    .line 35
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    iget-object v1, v1, LN40/a;->e:Landroid/widget/ImageView;

    .line 40
    .line 41
    invoke-virtual {v1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/utils/g;->P(Landroid/content/Context;)I

    .line 46
    .line 47
    .line 48
    move-result v1

    .line 49
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    iget-object v2, v2, LN40/a;->e:Landroid/widget/ImageView;

    .line 54
    .line 55
    invoke-virtual {v2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 56
    .line 57
    .line 58
    move-result-object v2

    .line 59
    invoke-virtual {v0, v2}, Lorg/xbet/ui_common/utils/g;->M(Landroid/content/Context;)I

    .line 60
    .line 61
    .line 62
    move-result v0

    .line 63
    invoke-virtual {p1, v1, v0}, Lcom/bumptech/glide/request/a;->c0(II)Lcom/bumptech/glide/request/a;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    check-cast p1, Lcom/bumptech/glide/h;

    .line 68
    .line 69
    sget-object v0, Lcom/bumptech/glide/load/engine/h;->c:Lcom/bumptech/glide/load/engine/h;

    .line 70
    .line 71
    invoke-virtual {p1, v0}, Lcom/bumptech/glide/request/a;->g(Lcom/bumptech/glide/load/engine/h;)Lcom/bumptech/glide/request/a;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    check-cast p1, Lcom/bumptech/glide/h;

    .line 76
    .line 77
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    iget-object v0, v0, LN40/a;->b:Landroid/widget/ImageView;

    .line 82
    .line 83
    invoke-virtual {p1, v0}, Lcom/bumptech/glide/h;->I0(Landroid/widget/ImageView;)LO3/j;

    .line 84
    .line 85
    .line 86
    return-void
.end method

.method public final X2(Ljava/lang/String;)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LN40/a;->e:Landroid/widget/ImageView;

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-static {v0}, Lcom/bumptech/glide/b;->t(Landroid/content/Context;)Lcom/bumptech/glide/i;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    new-instance v1, Lorg/xbet/ui_common/utils/Y;

    .line 16
    .line 17
    invoke-direct {v1, p1}, Lorg/xbet/ui_common/utils/Y;-><init>(Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {v0, v1}, Lcom/bumptech/glide/i;->w(Ljava/lang/Object;)Lcom/bumptech/glide/h;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    sget-object v0, Lorg/xbet/ui_common/utils/g;->a:Lorg/xbet/ui_common/utils/g;

    .line 25
    .line 26
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    iget-object v1, v1, LN40/a;->e:Landroid/widget/ImageView;

    .line 31
    .line 32
    invoke-virtual {v1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/utils/g;->P(Landroid/content/Context;)I

    .line 37
    .line 38
    .line 39
    move-result v1

    .line 40
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 41
    .line 42
    .line 43
    move-result-object v2

    .line 44
    iget-object v2, v2, LN40/a;->e:Landroid/widget/ImageView;

    .line 45
    .line 46
    invoke-virtual {v2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    invoke-virtual {v0, v2}, Lorg/xbet/ui_common/utils/g;->M(Landroid/content/Context;)I

    .line 51
    .line 52
    .line 53
    move-result v0

    .line 54
    invoke-virtual {p1, v1, v0}, Lcom/bumptech/glide/request/a;->c0(II)Lcom/bumptech/glide/request/a;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    check-cast p1, Lcom/bumptech/glide/h;

    .line 59
    .line 60
    sget-object v0, Lcom/bumptech/glide/load/engine/h;->c:Lcom/bumptech/glide/load/engine/h;

    .line 61
    .line 62
    invoke-virtual {p1, v0}, Lcom/bumptech/glide/request/a;->g(Lcom/bumptech/glide/load/engine/h;)Lcom/bumptech/glide/request/a;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    check-cast p1, Lcom/bumptech/glide/h;

    .line 67
    .line 68
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    iget-object v0, v0, LN40/a;->e:Landroid/widget/ImageView;

    .line 73
    .line 74
    invoke-virtual {p1, v0}, Lcom/bumptech/glide/h;->I0(Landroid/widget/ImageView;)LO3/j;

    .line 75
    .line 76
    .line 77
    return-void
.end method

.method public final Y2(Ljava/lang/String;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LN40/a;->e:Landroid/widget/ImageView;

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-static {v0}, Lcom/bumptech/glide/b;->t(Landroid/content/Context;)Lcom/bumptech/glide/i;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    new-instance v1, Lorg/xbet/ui_common/utils/Y;

    .line 16
    .line 17
    invoke-direct {v1, p1}, Lorg/xbet/ui_common/utils/Y;-><init>(Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {v0, v1}, Lcom/bumptech/glide/i;->w(Ljava/lang/Object;)Lcom/bumptech/glide/h;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    sget-object v0, Lcom/bumptech/glide/load/engine/h;->c:Lcom/bumptech/glide/load/engine/h;

    .line 25
    .line 26
    invoke-virtual {p1, v0}, Lcom/bumptech/glide/request/a;->g(Lcom/bumptech/glide/load/engine/h;)Lcom/bumptech/glide/request/a;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    check-cast p1, Lcom/bumptech/glide/h;

    .line 31
    .line 32
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    iget-object v0, v0, LN40/a;->l:Landroid/widget/ImageView;

    .line 37
    .line 38
    invoke-virtual {p1, v0}, Lcom/bumptech/glide/h;->I0(Landroid/widget/ImageView;)LO3/j;

    .line 39
    .line 40
    .line 41
    return-void
.end method

.method public final Z2(Z)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LN40/a;->d:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    if-nez p1, :cond_0

    .line 11
    .line 12
    const/4 v3, 0x0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/16 v3, 0x8

    .line 15
    .line 16
    :goto_0
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iget-object v0, v0, LN40/a;->j:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 24
    .line 25
    if-eqz p1, :cond_1

    .line 26
    .line 27
    const/4 v3, 0x0

    .line 28
    goto :goto_1

    .line 29
    :cond_1
    const/16 v3, 0x8

    .line 30
    .line 31
    :goto_1
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    iget-object v0, v0, LN40/a;->e:Landroid/widget/ImageView;

    .line 39
    .line 40
    if-eqz p1, :cond_2

    .line 41
    .line 42
    const/4 v3, 0x0

    .line 43
    goto :goto_2

    .line 44
    :cond_2
    const/16 v3, 0x8

    .line 45
    .line 46
    :goto_2
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    iget-object v0, v0, LN40/a;->b:Landroid/widget/ImageView;

    .line 54
    .line 55
    if-eqz p1, :cond_3

    .line 56
    .line 57
    const/4 v3, 0x0

    .line 58
    goto :goto_3

    .line 59
    :cond_3
    const/16 v3, 0x8

    .line 60
    .line 61
    :goto_3
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 62
    .line 63
    .line 64
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    iget-object v0, v0, LN40/a;->l:Landroid/widget/ImageView;

    .line 69
    .line 70
    if-eqz p1, :cond_4

    .line 71
    .line 72
    const/4 v1, 0x0

    .line 73
    :cond_4
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 74
    .line 75
    .line 76
    return-void
.end method

.method public final c3(LP40/c;Ljava/lang/String;)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LN40/a;->h:Landroid/widget/TextView;

    .line 6
    .line 7
    invoke-virtual {p1}, LP40/c;->b()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-static {v1}, Ljava/lang/Double;->parseDouble(Ljava/lang/String;)D

    .line 12
    .line 13
    .line 14
    move-result-wide v1

    .line 15
    invoke-virtual {p0, v1, v2, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->L2(DLjava/lang/String;)Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    iget-object v0, v0, LN40/a;->c:Landroid/widget/TextView;

    .line 27
    .line 28
    invoke-virtual {p1}, LP40/c;->a()Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    invoke-static {v1}, Ljava/lang/Double;->parseDouble(Ljava/lang/String;)D

    .line 33
    .line 34
    .line 35
    move-result-wide v1

    .line 36
    invoke-virtual {p0, v1, v2, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->L2(DLjava/lang/String;)Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v1

    .line 40
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    iget-object v0, v0, LN40/a;->o:Landroid/widget/TextView;

    .line 48
    .line 49
    invoke-virtual {p1}, LP40/c;->d()Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object v1

    .line 53
    invoke-static {v1}, Ljava/lang/Double;->parseDouble(Ljava/lang/String;)D

    .line 54
    .line 55
    .line 56
    move-result-wide v1

    .line 57
    invoke-virtual {p0, v1, v2, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->L2(DLjava/lang/String;)Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 62
    .line 63
    .line 64
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->N2()LN40/a;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    iget-object v0, v0, LN40/a;->k:Landroid/widget/TextView;

    .line 69
    .line 70
    invoke-virtual {p1}, LP40/c;->c()Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    invoke-static {p1}, Ljava/lang/Double;->parseDouble(Ljava/lang/String;)D

    .line 75
    .line 76
    .line 77
    move-result-wide v1

    .line 78
    invoke-virtual {p0, v1, v2, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->L2(DLjava/lang/String;)Ljava/lang/String;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 83
    .line 84
    .line 85
    return-void
.end method

.method public onResume()V
    .locals 0

    .line 1
    invoke-super {p0}, LXW0/a;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->S2()V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public onStart()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onStart()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->O2()Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->z3()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public s2()V
    .locals 0

    .line 1
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->T2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->O2()Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->L3()V

    .line 9
    .line 10
    .line 11
    invoke-direct {p0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->Q2()V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-static {}, LO40/a;->a()LO40/c$a;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v1}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    instance-of v2, v1, LQW0/f;

    .line 17
    .line 18
    const-string v3, "Can not find dependencies provider for "

    .line 19
    .line 20
    if-eqz v2, :cond_2

    .line 21
    .line 22
    check-cast v1, LQW0/f;

    .line 23
    .line 24
    invoke-interface {v1}, LQW0/f;->a()Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v2

    .line 28
    instance-of v2, v2, LSv/d;

    .line 29
    .line 30
    if-eqz v2, :cond_1

    .line 31
    .line 32
    invoke-interface {v1}, LQW0/f;->a()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    if-eqz v1, :cond_0

    .line 37
    .line 38
    check-cast v1, LSv/d;

    .line 39
    .line 40
    new-instance v2, LO40/e;

    .line 41
    .line 42
    invoke-direct {v2}, LO40/e;-><init>()V

    .line 43
    .line 44
    .line 45
    invoke-interface {v0, v1, v2}, LO40/c$a;->a(LSv/d;LO40/e;)LO40/c;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    invoke-interface {v0, p0}, LO40/c;->a(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)V

    .line 50
    .line 51
    .line 52
    return-void

    .line 53
    :cond_0
    new-instance v0, Ljava/lang/NullPointerException;

    .line 54
    .line 55
    const-string v1, "null cannot be cast to non-null type org.xbet.core.di.dependencies.JackPotDependencies"

    .line 56
    .line 57
    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 58
    .line 59
    .line 60
    throw v0

    .line 61
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 62
    .line 63
    new-instance v1, Ljava/lang/StringBuilder;

    .line 64
    .line 65
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 66
    .line 67
    .line 68
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 69
    .line 70
    .line 71
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 72
    .line 73
    .line 74
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 75
    .line 76
    .line 77
    move-result-object v1

    .line 78
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 79
    .line 80
    .line 81
    throw v0

    .line 82
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 83
    .line 84
    new-instance v1, Ljava/lang/StringBuilder;

    .line 85
    .line 86
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 87
    .line 88
    .line 89
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 90
    .line 91
    .line 92
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 93
    .line 94
    .line 95
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 96
    .line 97
    .line 98
    move-result-object v1

    .line 99
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 100
    .line 101
    .line 102
    throw v0
.end method

.method public v2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->O2()Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->E3()Lkotlinx/coroutines/flow/e;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v6, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$1;

    .line 12
    .line 13
    const/4 v1, 0x0

    .line 14
    invoke-direct {v6, v0, v1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$1;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Lkotlin/coroutines/e;)V

    .line 15
    .line 16
    .line 17
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 18
    .line 19
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 20
    .line 21
    .line 22
    move-result-object v4

    .line 23
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 24
    .line 25
    .line 26
    move-result-object v11

    .line 27
    new-instance v2, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 28
    .line 29
    const/4 v7, 0x0

    .line 30
    move-object v5, v10

    .line 31
    invoke-direct/range {v2 .. v7}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/4 v15, 0x3

    .line 35
    const/16 v16, 0x0

    .line 36
    .line 37
    const/4 v12, 0x0

    .line 38
    const/4 v13, 0x0

    .line 39
    move-object v14, v2

    .line 40
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 41
    .line 42
    .line 43
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->O2()Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    invoke-virtual {v2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->B3()Lkotlinx/coroutines/flow/e;

    .line 48
    .line 49
    .line 50
    move-result-object v8

    .line 51
    new-instance v11, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$2;

    .line 52
    .line 53
    invoke-direct {v11, v0, v1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$2;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Lkotlin/coroutines/e;)V

    .line 54
    .line 55
    .line 56
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 57
    .line 58
    .line 59
    move-result-object v9

    .line 60
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 61
    .line 62
    .line 63
    move-result-object v2

    .line 64
    new-instance v5, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 65
    .line 66
    move-object v7, v5

    .line 67
    invoke-direct/range {v7 .. v12}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 68
    .line 69
    .line 70
    const/4 v6, 0x3

    .line 71
    const/4 v7, 0x0

    .line 72
    const/4 v3, 0x0

    .line 73
    const/4 v4, 0x0

    .line 74
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 75
    .line 76
    .line 77
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->O2()Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 78
    .line 79
    .line 80
    move-result-object v2

    .line 81
    invoke-virtual {v2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->C3()Lkotlinx/coroutines/flow/f0;

    .line 82
    .line 83
    .line 84
    move-result-object v8

    .line 85
    new-instance v11, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;

    .line 86
    .line 87
    invoke-direct {v11, v0, v1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$3;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Lkotlin/coroutines/e;)V

    .line 88
    .line 89
    .line 90
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 91
    .line 92
    .line 93
    move-result-object v9

    .line 94
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 95
    .line 96
    .line 97
    move-result-object v2

    .line 98
    new-instance v5, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 99
    .line 100
    move-object v7, v5

    .line 101
    invoke-direct/range {v7 .. v12}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 102
    .line 103
    .line 104
    const/4 v7, 0x0

    .line 105
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 106
    .line 107
    .line 108
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->O2()Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 109
    .line 110
    .line 111
    move-result-object v2

    .line 112
    invoke-virtual {v2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->A3()Lkotlinx/coroutines/flow/e;

    .line 113
    .line 114
    .line 115
    move-result-object v8

    .line 116
    new-instance v11, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$4;

    .line 117
    .line 118
    invoke-direct {v11, v0, v1}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$4;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;Lkotlin/coroutines/e;)V

    .line 119
    .line 120
    .line 121
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 122
    .line 123
    .line 124
    move-result-object v9

    .line 125
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 126
    .line 127
    .line 128
    move-result-object v1

    .line 129
    new-instance v4, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$$inlined$observeWithLifecycle$default$4;

    .line 130
    .line 131
    move-object v7, v4

    .line 132
    invoke-direct/range {v7 .. v12}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment$onObserveData$$inlined$observeWithLifecycle$default$4;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 133
    .line 134
    .line 135
    const/4 v5, 0x3

    .line 136
    const/4 v6, 0x0

    .line 137
    const/4 v2, 0x0

    .line 138
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 139
    .line 140
    .line 141
    return-void
.end method

.method public x2()V
    .locals 7

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/app/Activity;->getWindow()Landroid/view/Window;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    sget v3, Lpb/e;->transparent:I

    .line 18
    .line 19
    sget-object v0, Lub/b;->a:Lub/b;

    .line 20
    .line 21
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 22
    .line 23
    .line 24
    move-result-object v4

    .line 25
    sget v5, Lpb/c;->black:I

    .line 26
    .line 27
    const/4 v6, 0x1

    .line 28
    invoke-virtual {v0, v4, v5, v6}, Lub/b;->e(Landroid/content/Context;IZ)I

    .line 29
    .line 30
    .line 31
    move-result v4

    .line 32
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getActivity()Landroidx/fragment/app/FragmentActivity;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    invoke-static {v0}, LBX0/b;->b(Landroid/content/Context;)Z

    .line 37
    .line 38
    .line 39
    move-result v0

    .line 40
    xor-int/2addr v6, v0

    .line 41
    const/4 v5, 0x0

    .line 42
    invoke-static/range {v1 .. v6}, Lorg/xbet/ui_common/utils/I0;->g(Landroid/view/Window;Landroid/content/Context;IIZZ)V

    .line 43
    .line 44
    .line 45
    :cond_0
    return-void
.end method
