.class public final Ljb1/l;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0008\u0003\u001a\u001b\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u001a\u001d\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00062\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lorg/xplatform/banners/api/domain/models/BannerModel;",
        "",
        "aggregatorTournamentCardsOldStyle",
        "Lkb1/b;",
        "b",
        "(Lorg/xplatform/banners/api/domain/models/BannerModel;Ljava/lang/String;)Lkb1/b;",
        "",
        "a",
        "(Ljava/lang/String;)Ljava/util/List;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ljava/lang/String;)Ljava/util/List;
    .locals 4
    .param p0    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Lkb1/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lkb1/b;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/banners/api/domain/models/BannerModel;->Companion:Lorg/xplatform/banners/api/domain/models/BannerModel$a;

    .line 4
    .line 5
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel$a;->a()Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v2, LU21/e;

    .line 10
    .line 11
    invoke-direct {v2}, LU21/e;-><init>()V

    .line 12
    .line 13
    .line 14
    const/4 v3, 0x0

    .line 15
    invoke-direct {v0, v3, v1, p0, v2}, Lkb1/b;-><init>(ILorg/xplatform/banners/api/domain/models/BannerModel;Ljava/lang/String;LU21/c;)V

    .line 16
    .line 17
    .line 18
    const/4 p0, 0x4

    .line 19
    new-array p0, p0, [Lkb1/b;

    .line 20
    .line 21
    aput-object v0, p0, v3

    .line 22
    .line 23
    const/4 v1, 0x1

    .line 24
    aput-object v0, p0, v1

    .line 25
    .line 26
    const/4 v1, 0x2

    .line 27
    aput-object v0, p0, v1

    .line 28
    .line 29
    const/4 v1, 0x3

    .line 30
    aput-object v0, p0, v1

    .line 31
    .line 32
    invoke-static {p0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    return-object p0
.end method

.method public static final b(Lorg/xplatform/banners/api/domain/models/BannerModel;Ljava/lang/String;)Lkb1/b;
    .locals 6
    .param p0    # Lorg/xplatform/banners/api/domain/models/BannerModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lkb1/b;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    new-instance v2, LU21/b;

    .line 8
    .line 9
    invoke-virtual {p0}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getTitle()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    invoke-virtual {p0}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getUrl()Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object v4

    .line 17
    invoke-static {v4}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v4

    .line 21
    invoke-static {v4}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 22
    .line 23
    .line 24
    move-result-object v4

    .line 25
    const/4 v5, 0x0

    .line 26
    invoke-direct {v2, v3, v4, v5}, LU21/b;-><init>(Ljava/lang/String;LL11/c;LL11/c;)V

    .line 27
    .line 28
    .line 29
    invoke-direct {v0, v1, p0, p1, v2}, Lkb1/b;-><init>(ILorg/xplatform/banners/api/domain/models/BannerModel;Ljava/lang/String;LU21/c;)V

    .line 30
    .line 31
    .line 32
    return-object v0
.end method
