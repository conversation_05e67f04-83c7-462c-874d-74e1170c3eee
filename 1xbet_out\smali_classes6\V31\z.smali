.class public final synthetic LV31/z;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LV31/z;->a:Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LV31/z;->a:Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;

    invoke-static {v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->i(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/h;

    move-result-object v0

    return-object v0
.end method
