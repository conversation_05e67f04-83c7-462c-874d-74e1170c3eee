.class public interface abstract LhF0/g;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LhF0/g$a;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008a\u0018\u00002\u00020\u0001:\u0001\u0008J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0017\u0010\u0008\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0007H&\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "LhF0/g;",
        "",
        "Lorg/xbet/statistic/heat_map/impl/presentation/fragment/HeatMapStatisticFragment;",
        "fragment",
        "",
        "b",
        "(Lorg/xbet/statistic/heat_map/impl/presentation/fragment/HeatMapStatisticFragment;)V",
        "Lorg/xbet/statistic/heat_map/impl/presentation/fragment/TeamHeatMapFragment;",
        "a",
        "(Lorg/xbet/statistic/heat_map/impl/presentation/fragment/TeamHeatMapFragment;)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Lorg/xbet/statistic/heat_map/impl/presentation/fragment/TeamHeatMapFragment;)V
    .param p1    # Lorg/xbet/statistic/heat_map/impl/presentation/fragment/TeamHeatMapFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract b(Lorg/xbet/statistic/heat_map/impl/presentation/fragment/HeatMapStatisticFragment;)V
    .param p1    # Lorg/xbet/statistic/heat_map/impl/presentation/fragment/HeatMapStatisticFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method
