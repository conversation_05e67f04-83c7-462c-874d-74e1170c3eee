.class public final LGA0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a)\u0010\u0007\u001a\u00020\u0006*\u00020\u00002\u000c\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u0004H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "LJA0/c;",
        "",
        "LJc0/o;",
        "sportEntityList",
        "",
        "live",
        "LYA0/a;",
        "a",
        "(LJA0/c;Ljava/util/List;Z)LYA0/a;",
        "core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LJA0/c;Ljava/util/List;Z)LYA0/a;
    .locals 63
    .param p0    # LJA0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LJA0/c;",
            "Ljava/util/List<",
            "LJc0/o;",
            ">;Z)",
            "LYA0/a;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p0}, LJA0/c;->u()LIA0/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0}, LIA0/e;->c()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-nez v0, :cond_1

    .line 14
    .line 15
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    :cond_1
    invoke-virtual/range {p0 .. p0}, LJA0/c;->v()LIA0/e;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    if-eqz v2, :cond_2

    .line 24
    .line 25
    invoke-virtual {v2}, LIA0/e;->c()Ljava/util/List;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    goto :goto_1

    .line 30
    :cond_2
    const/4 v2, 0x0

    .line 31
    :goto_1
    if-nez v2, :cond_3

    .line 32
    .line 33
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    :cond_3
    invoke-virtual/range {p0 .. p0}, LJA0/c;->o()Ljava/lang/Long;

    .line 38
    .line 39
    .line 40
    move-result-object v3

    .line 41
    if-eqz v3, :cond_4

    .line 42
    .line 43
    invoke-virtual {v3}, Ljava/lang/Long;->longValue()J

    .line 44
    .line 45
    .line 46
    move-result-wide v6

    .line 47
    move-wide v9, v6

    .line 48
    goto :goto_2

    .line 49
    :cond_4
    const-wide/16 v9, 0x0

    .line 50
    .line 51
    :goto_2
    invoke-virtual/range {p0 .. p0}, LJA0/c;->m()Ljava/lang/Long;

    .line 52
    .line 53
    .line 54
    move-result-object v3

    .line 55
    if-eqz v3, :cond_5

    .line 56
    .line 57
    invoke-virtual {v3}, Ljava/lang/Long;->longValue()J

    .line 58
    .line 59
    .line 60
    move-result-wide v6

    .line 61
    move-wide v11, v6

    .line 62
    goto :goto_3

    .line 63
    :cond_5
    const-wide/16 v11, 0x0

    .line 64
    .line 65
    :goto_3
    invoke-virtual/range {p0 .. p0}, LJA0/c;->u()LIA0/e;

    .line 66
    .line 67
    .line 68
    move-result-object v3

    .line 69
    if-eqz v3, :cond_7

    .line 70
    .line 71
    invoke-virtual {v3}, LIA0/e;->c()Ljava/util/List;

    .line 72
    .line 73
    .line 74
    move-result-object v3

    .line 75
    if-nez v3, :cond_6

    .line 76
    .line 77
    goto :goto_5

    .line 78
    :cond_6
    :goto_4
    move-object v13, v3

    .line 79
    goto :goto_6

    .line 80
    :cond_7
    :goto_5
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 81
    .line 82
    .line 83
    move-result-object v3

    .line 84
    goto :goto_4

    .line 85
    :goto_6
    invoke-virtual/range {p0 .. p0}, LJA0/c;->v()LIA0/e;

    .line 86
    .line 87
    .line 88
    move-result-object v3

    .line 89
    if-eqz v3, :cond_9

    .line 90
    .line 91
    invoke-virtual {v3}, LIA0/e;->c()Ljava/util/List;

    .line 92
    .line 93
    .line 94
    move-result-object v3

    .line 95
    if-nez v3, :cond_8

    .line 96
    .line 97
    goto :goto_8

    .line 98
    :cond_8
    :goto_7
    move-object v14, v3

    .line 99
    goto :goto_9

    .line 100
    :cond_9
    :goto_8
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 101
    .line 102
    .line 103
    move-result-object v3

    .line 104
    goto :goto_7

    .line 105
    :goto_9
    invoke-virtual/range {p0 .. p0}, LJA0/c;->n()Ljava/lang/String;

    .line 106
    .line 107
    .line 108
    move-result-object v3

    .line 109
    const-string v6, ""

    .line 110
    .line 111
    if-nez v3, :cond_a

    .line 112
    .line 113
    move-object v15, v6

    .line 114
    goto :goto_a

    .line 115
    :cond_a
    move-object v15, v3

    .line 116
    :goto_a
    invoke-virtual/range {p0 .. p0}, LJA0/c;->y()LIA0/i;

    .line 117
    .line 118
    .line 119
    move-result-object v3

    .line 120
    if-eqz v3, :cond_b

    .line 121
    .line 122
    invoke-virtual {v3}, LIA0/i;->d()Ljava/lang/String;

    .line 123
    .line 124
    .line 125
    move-result-object v3

    .line 126
    goto :goto_b

    .line 127
    :cond_b
    const/4 v3, 0x0

    .line 128
    :goto_b
    if-nez v3, :cond_c

    .line 129
    .line 130
    move-object/from16 v16, v6

    .line 131
    .line 132
    goto :goto_c

    .line 133
    :cond_c
    move-object/from16 v16, v3

    .line 134
    .line 135
    :goto_c
    invoke-virtual/range {p0 .. p0}, LJA0/c;->y()LIA0/i;

    .line 136
    .line 137
    .line 138
    move-result-object v3

    .line 139
    if-eqz v3, :cond_d

    .line 140
    .line 141
    invoke-virtual {v3}, LIA0/i;->a()Ljava/lang/Boolean;

    .line 142
    .line 143
    .line 144
    move-result-object v3

    .line 145
    if-eqz v3, :cond_d

    .line 146
    .line 147
    invoke-virtual {v3}, Ljava/lang/Boolean;->booleanValue()Z

    .line 148
    .line 149
    .line 150
    move-result v3

    .line 151
    move/from16 v17, v3

    .line 152
    .line 153
    goto :goto_d

    .line 154
    :cond_d
    const/16 v17, 0x0

    .line 155
    .line 156
    :goto_d
    invoke-virtual/range {p0 .. p0}, LJA0/c;->y()LIA0/i;

    .line 157
    .line 158
    .line 159
    move-result-object v3

    .line 160
    if-eqz v3, :cond_e

    .line 161
    .line 162
    invoke-virtual {v3}, LIA0/i;->b()Ljava/lang/Boolean;

    .line 163
    .line 164
    .line 165
    move-result-object v3

    .line 166
    if-eqz v3, :cond_e

    .line 167
    .line 168
    invoke-virtual {v3}, Ljava/lang/Boolean;->booleanValue()Z

    .line 169
    .line 170
    .line 171
    move-result v3

    .line 172
    move/from16 v19, v3

    .line 173
    .line 174
    goto :goto_e

    .line 175
    :cond_e
    const/16 v19, 0x0

    .line 176
    .line 177
    :goto_e
    invoke-virtual/range {p0 .. p0}, LJA0/c;->y()LIA0/i;

    .line 178
    .line 179
    .line 180
    move-result-object v3

    .line 181
    if-eqz v3, :cond_f

    .line 182
    .line 183
    invoke-virtual {v3}, LIA0/i;->c()Ljava/lang/Boolean;

    .line 184
    .line 185
    .line 186
    move-result-object v3

    .line 187
    if-eqz v3, :cond_f

    .line 188
    .line 189
    invoke-virtual {v3}, Ljava/lang/Boolean;->booleanValue()Z

    .line 190
    .line 191
    .line 192
    move-result v3

    .line 193
    move/from16 v18, v3

    .line 194
    .line 195
    goto :goto_f

    .line 196
    :cond_f
    const/16 v18, 0x0

    .line 197
    .line 198
    :goto_f
    invoke-virtual/range {p0 .. p0}, LJA0/c;->i()Ljava/lang/Boolean;

    .line 199
    .line 200
    .line 201
    move-result-object v3

    .line 202
    if-eqz v3, :cond_10

    .line 203
    .line 204
    invoke-virtual {v3}, Ljava/lang/Boolean;->booleanValue()Z

    .line 205
    .line 206
    .line 207
    move-result v3

    .line 208
    move/from16 v20, v3

    .line 209
    .line 210
    goto :goto_10

    .line 211
    :cond_10
    const/16 v20, 0x0

    .line 212
    .line 213
    :goto_10
    invoke-virtual/range {p0 .. p0}, LJA0/c;->l()LIA0/d;

    .line 214
    .line 215
    .line 216
    move-result-object v3

    .line 217
    if-eqz v3, :cond_11

    .line 218
    .line 219
    invoke-virtual {v3}, LIA0/d;->b()Ljava/lang/String;

    .line 220
    .line 221
    .line 222
    move-result-object v3

    .line 223
    goto :goto_11

    .line 224
    :cond_11
    const/4 v3, 0x0

    .line 225
    :goto_11
    if-nez v3, :cond_12

    .line 226
    .line 227
    move-object/from16 v21, v6

    .line 228
    .line 229
    goto :goto_12

    .line 230
    :cond_12
    move-object/from16 v21, v3

    .line 231
    .line 232
    :goto_12
    invoke-virtual/range {p0 .. p0}, LJA0/c;->s()LIA0/b;

    .line 233
    .line 234
    .line 235
    move-result-object v3

    .line 236
    invoke-static {v3}, LFA0/f;->a(LIA0/b;)LYA0/e;

    .line 237
    .line 238
    .line 239
    move-result-object v22

    .line 240
    invoke-virtual/range {p0 .. p0}, LJA0/c;->D()LIA0/k;

    .line 241
    .line 242
    .line 243
    move-result-object v3

    .line 244
    if-eqz v3, :cond_14

    .line 245
    .line 246
    invoke-virtual {v3}, LIA0/k;->a()Ljava/lang/String;

    .line 247
    .line 248
    .line 249
    move-result-object v3

    .line 250
    if-nez v3, :cond_13

    .line 251
    .line 252
    goto :goto_14

    .line 253
    :cond_13
    :goto_13
    move-object/from16 v24, v3

    .line 254
    .line 255
    goto :goto_16

    .line 256
    :cond_14
    :goto_14
    invoke-virtual/range {p0 .. p0}, LJA0/c;->u()LIA0/e;

    .line 257
    .line 258
    .line 259
    move-result-object v3

    .line 260
    if-eqz v3, :cond_15

    .line 261
    .line 262
    invoke-virtual {v3}, LIA0/e;->a()Ljava/lang/String;

    .line 263
    .line 264
    .line 265
    move-result-object v3

    .line 266
    goto :goto_15

    .line 267
    :cond_15
    const/4 v3, 0x0

    .line 268
    :goto_15
    if-nez v3, :cond_16

    .line 269
    .line 270
    move-object v3, v6

    .line 271
    :cond_16
    invoke-static {v3}, Lkotlin/text/StringsKt;->H1(Ljava/lang/CharSequence;)Ljava/lang/CharSequence;

    .line 272
    .line 273
    .line 274
    move-result-object v3

    .line 275
    invoke-virtual {v3}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 276
    .line 277
    .line 278
    move-result-object v3

    .line 279
    goto :goto_13

    .line 280
    :goto_16
    invoke-virtual/range {p0 .. p0}, LJA0/c;->v()LIA0/e;

    .line 281
    .line 282
    .line 283
    move-result-object v3

    .line 284
    if-eqz v3, :cond_17

    .line 285
    .line 286
    invoke-virtual {v3}, LIA0/e;->a()Ljava/lang/String;

    .line 287
    .line 288
    .line 289
    move-result-object v3

    .line 290
    goto :goto_17

    .line 291
    :cond_17
    const/4 v3, 0x0

    .line 292
    :goto_17
    if-nez v3, :cond_18

    .line 293
    .line 294
    move-object v3, v6

    .line 295
    :cond_18
    invoke-static {v3}, Lkotlin/text/StringsKt;->H1(Ljava/lang/CharSequence;)Ljava/lang/CharSequence;

    .line 296
    .line 297
    .line 298
    move-result-object v3

    .line 299
    invoke-virtual {v3}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 300
    .line 301
    .line 302
    move-result-object v25

    .line 303
    invoke-virtual/range {p0 .. p0}, LJA0/c;->u()LIA0/e;

    .line 304
    .line 305
    .line 306
    move-result-object v3

    .line 307
    if-eqz v3, :cond_19

    .line 308
    .line 309
    invoke-virtual {v3}, LIA0/e;->b()Ljava/util/List;

    .line 310
    .line 311
    .line 312
    move-result-object v3

    .line 313
    goto :goto_18

    .line 314
    :cond_19
    const/4 v3, 0x0

    .line 315
    :goto_18
    if-nez v3, :cond_1a

    .line 316
    .line 317
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 318
    .line 319
    .line 320
    move-result-object v3

    .line 321
    :cond_1a
    move-object/from16 v26, v3

    .line 322
    .line 323
    invoke-virtual/range {p0 .. p0}, LJA0/c;->v()LIA0/e;

    .line 324
    .line 325
    .line 326
    move-result-object v3

    .line 327
    if-eqz v3, :cond_1b

    .line 328
    .line 329
    invoke-virtual {v3}, LIA0/e;->b()Ljava/util/List;

    .line 330
    .line 331
    .line 332
    move-result-object v3

    .line 333
    goto :goto_19

    .line 334
    :cond_1b
    const/4 v3, 0x0

    .line 335
    :goto_19
    if-nez v3, :cond_1c

    .line 336
    .line 337
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 338
    .line 339
    .line 340
    move-result-object v3

    .line 341
    :cond_1c
    move-object/from16 v27, v3

    .line 342
    .line 343
    invoke-virtual/range {p0 .. p0}, LJA0/c;->w()LIA0/h;

    .line 344
    .line 345
    .line 346
    move-result-object v3

    .line 347
    if-eqz v3, :cond_1d

    .line 348
    .line 349
    invoke-virtual {v3}, LIA0/h;->a()Ljava/lang/Long;

    .line 350
    .line 351
    .line 352
    move-result-object v3

    .line 353
    if-eqz v3, :cond_1d

    .line 354
    .line 355
    invoke-virtual {v3}, Ljava/lang/Long;->longValue()J

    .line 356
    .line 357
    .line 358
    move-result-wide v28

    .line 359
    goto :goto_1a

    .line 360
    :cond_1d
    const-wide/16 v28, 0x0

    .line 361
    .line 362
    :goto_1a
    invoke-virtual/range {p0 .. p0}, LJA0/c;->w()LIA0/h;

    .line 363
    .line 364
    .line 365
    move-result-object v3

    .line 366
    if-eqz v3, :cond_1e

    .line 367
    .line 368
    invoke-virtual {v3}, LIA0/h;->b()Ljava/lang/Long;

    .line 369
    .line 370
    .line 371
    move-result-object v3

    .line 372
    if-eqz v3, :cond_1e

    .line 373
    .line 374
    invoke-virtual {v3}, Ljava/lang/Long;->longValue()J

    .line 375
    .line 376
    .line 377
    move-result-wide v30

    .line 378
    goto :goto_1b

    .line 379
    :cond_1e
    const-wide/16 v30, 0x0

    .line 380
    .line 381
    :goto_1b
    invoke-virtual/range {p0 .. p0}, LJA0/c;->F()Ljava/lang/Boolean;

    .line 382
    .line 383
    .line 384
    move-result-object v3

    .line 385
    if-eqz v3, :cond_1f

    .line 386
    .line 387
    invoke-virtual {v3}, Ljava/lang/Boolean;->booleanValue()Z

    .line 388
    .line 389
    .line 390
    move-result v3

    .line 391
    move/from16 v32, v3

    .line 392
    .line 393
    goto :goto_1c

    .line 394
    :cond_1f
    const/16 v32, 0x0

    .line 395
    .line 396
    :goto_1c
    invoke-virtual/range {p0 .. p0}, LJA0/c;->q()Ljava/lang/String;

    .line 397
    .line 398
    .line 399
    move-result-object v3

    .line 400
    if-nez v3, :cond_20

    .line 401
    .line 402
    move-object/from16 v34, v6

    .line 403
    .line 404
    goto :goto_1d

    .line 405
    :cond_20
    move-object/from16 v34, v3

    .line 406
    .line 407
    :goto_1d
    invoke-virtual/range {p0 .. p0}, LJA0/c;->C()LIA0/c;

    .line 408
    .line 409
    .line 410
    move-result-object v3

    .line 411
    if-eqz v3, :cond_21

    .line 412
    .line 413
    invoke-virtual {v3}, LIA0/c;->a()Ljava/lang/String;

    .line 414
    .line 415
    .line 416
    move-result-object v3

    .line 417
    goto :goto_1e

    .line 418
    :cond_21
    const/4 v3, 0x0

    .line 419
    :goto_1e
    if-nez v3, :cond_22

    .line 420
    .line 421
    move-object/from16 v35, v6

    .line 422
    .line 423
    goto :goto_1f

    .line 424
    :cond_22
    move-object/from16 v35, v3

    .line 425
    .line 426
    :goto_1f
    invoke-virtual/range {p0 .. p0}, LJA0/c;->x()Ljava/lang/Long;

    .line 427
    .line 428
    .line 429
    move-result-object v3

    .line 430
    if-eqz v3, :cond_23

    .line 431
    .line 432
    invoke-virtual {v3}, Ljava/lang/Long;->longValue()J

    .line 433
    .line 434
    .line 435
    move-result-wide v36

    .line 436
    goto :goto_20

    .line 437
    :cond_23
    const-wide/16 v36, 0x0

    .line 438
    .line 439
    :goto_20
    invoke-static/range {v36 .. v37}, Ll8/b$a$c;->f(J)J

    .line 440
    .line 441
    .line 442
    move-result-wide v36

    .line 443
    invoke-interface/range {p1 .. p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 444
    .line 445
    .line 446
    move-result-object v3

    .line 447
    :cond_24
    :goto_21
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 448
    .line 449
    .line 450
    move-result v8

    .line 451
    if-eqz v8, :cond_26

    .line 452
    .line 453
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 454
    .line 455
    .line 456
    move-result-object v8

    .line 457
    move-object/from16 v23, v8

    .line 458
    .line 459
    check-cast v23, LJc0/o;

    .line 460
    .line 461
    invoke-virtual/range {p0 .. p0}, LJA0/c;->w()LIA0/h;

    .line 462
    .line 463
    .line 464
    move-result-object v33

    .line 465
    if-eqz v33, :cond_24

    .line 466
    .line 467
    invoke-virtual/range {v23 .. v23}, LJc0/o;->i()J

    .line 468
    .line 469
    .line 470
    move-result-wide v38

    .line 471
    invoke-virtual/range {v33 .. v33}, LIA0/h;->a()Ljava/lang/Long;

    .line 472
    .line 473
    .line 474
    move-result-object v23

    .line 475
    if-nez v23, :cond_25

    .line 476
    .line 477
    goto :goto_21

    .line 478
    :cond_25
    invoke-virtual/range {v23 .. v23}, Ljava/lang/Long;->longValue()J

    .line 479
    .line 480
    .line 481
    move-result-wide v40

    .line 482
    cmp-long v23, v38, v40

    .line 483
    .line 484
    if-nez v23, :cond_24

    .line 485
    .line 486
    goto :goto_22

    .line 487
    :cond_26
    const/4 v8, 0x0

    .line 488
    :goto_22
    check-cast v8, LJc0/o;

    .line 489
    .line 490
    if-eqz v8, :cond_27

    .line 491
    .line 492
    invoke-virtual {v8}, LJc0/o;->m()Ljava/lang/String;

    .line 493
    .line 494
    .line 495
    move-result-object v3

    .line 496
    goto :goto_23

    .line 497
    :cond_27
    const/4 v3, 0x0

    .line 498
    :goto_23
    if-nez v3, :cond_28

    .line 499
    .line 500
    move-object/from16 v39, v6

    .line 501
    .line 502
    goto :goto_24

    .line 503
    :cond_28
    move-object/from16 v39, v3

    .line 504
    .line 505
    :goto_24
    invoke-virtual/range {p0 .. p0}, LJA0/c;->l()LIA0/d;

    .line 506
    .line 507
    .line 508
    move-result-object v3

    .line 509
    if-eqz v3, :cond_29

    .line 510
    .line 511
    invoke-virtual {v3}, LIA0/d;->a()Ljava/lang/Long;

    .line 512
    .line 513
    .line 514
    move-result-object v3

    .line 515
    if-eqz v3, :cond_29

    .line 516
    .line 517
    invoke-virtual {v3}, Ljava/lang/Long;->longValue()J

    .line 518
    .line 519
    .line 520
    move-result-wide v40

    .line 521
    goto :goto_25

    .line 522
    :cond_29
    const-wide/16 v40, 0x0

    .line 523
    .line 524
    :goto_25
    invoke-virtual/range {p0 .. p0}, LJA0/c;->a()Ljava/util/List;

    .line 525
    .line 526
    .line 527
    move-result-object v3

    .line 528
    const/16 v8, 0xa

    .line 529
    .line 530
    if-eqz v3, :cond_2b

    .line 531
    .line 532
    new-instance v1, Ljava/util/ArrayList;

    .line 533
    .line 534
    invoke-static {v3, v8}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 535
    .line 536
    .line 537
    move-result v4

    .line 538
    invoke-direct {v1, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 539
    .line 540
    .line 541
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 542
    .line 543
    .line 544
    move-result-object v3

    .line 545
    :goto_26
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 546
    .line 547
    .line 548
    move-result v4

    .line 549
    if-eqz v4, :cond_2a

    .line 550
    .line 551
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 552
    .line 553
    .line 554
    move-result-object v4

    .line 555
    check-cast v4, LIA0/a;

    .line 556
    .line 557
    move/from16 v5, p2

    .line 558
    .line 559
    invoke-static {v4, v5}, LFA0/d;->a(LIA0/a;Z)LYA0/d;

    .line 560
    .line 561
    .line 562
    move-result-object v4

    .line 563
    invoke-interface {v1, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 564
    .line 565
    .line 566
    goto :goto_26

    .line 567
    :cond_2a
    move/from16 v5, p2

    .line 568
    .line 569
    goto :goto_27

    .line 570
    :cond_2b
    move/from16 v5, p2

    .line 571
    .line 572
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 573
    .line 574
    .line 575
    move-result-object v1

    .line 576
    :goto_27
    invoke-virtual/range {p0 .. p0}, LJA0/c;->E()Ljava/lang/Integer;

    .line 577
    .line 578
    .line 579
    move-result-object v3

    .line 580
    if-eqz v3, :cond_2c

    .line 581
    .line 582
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 583
    .line 584
    .line 585
    move-result v3

    .line 586
    goto :goto_28

    .line 587
    :cond_2c
    const/4 v3, 0x0

    .line 588
    :goto_28
    invoke-virtual/range {p0 .. p0}, LJA0/c;->g()Ljava/lang/Boolean;

    .line 589
    .line 590
    .line 591
    move-result-object v4

    .line 592
    sget-object v7, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 593
    .line 594
    invoke-static {v4, v7}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 595
    .line 596
    .line 597
    move-result v44

    .line 598
    invoke-virtual/range {p0 .. p0}, LJA0/c;->f()Ljava/lang/Boolean;

    .line 599
    .line 600
    .line 601
    move-result-object v4

    .line 602
    if-eqz v4, :cond_2d

    .line 603
    .line 604
    invoke-virtual {v4}, Ljava/lang/Boolean;->booleanValue()Z

    .line 605
    .line 606
    .line 607
    move-result v4

    .line 608
    move/from16 v45, v4

    .line 609
    .line 610
    goto :goto_29

    .line 611
    :cond_2d
    const/16 v45, 0x0

    .line 612
    .line 613
    :goto_29
    invoke-virtual/range {p0 .. p0}, LJA0/c;->y()LIA0/i;

    .line 614
    .line 615
    .line 616
    move-result-object v4

    .line 617
    if-eqz v4, :cond_2e

    .line 618
    .line 619
    invoke-virtual {v4}, LIA0/i;->e()Ljava/lang/Integer;

    .line 620
    .line 621
    .line 622
    move-result-object v4

    .line 623
    if-eqz v4, :cond_2e

    .line 624
    .line 625
    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    .line 626
    .line 627
    .line 628
    move-result v4

    .line 629
    move/from16 v46, v4

    .line 630
    .line 631
    goto :goto_2a

    .line 632
    :cond_2e
    const/16 v46, 0x0

    .line 633
    .line 634
    :goto_2a
    invoke-virtual/range {p0 .. p0}, LJA0/c;->c()Ljava/lang/String;

    .line 635
    .line 636
    .line 637
    move-result-object v4

    .line 638
    if-nez v4, :cond_2f

    .line 639
    .line 640
    move-object/from16 v47, v6

    .line 641
    .line 642
    goto :goto_2b

    .line 643
    :cond_2f
    move-object/from16 v47, v4

    .line 644
    .line 645
    :goto_2b
    invoke-virtual/range {p0 .. p0}, LJA0/c;->j()Ljava/lang/Boolean;

    .line 646
    .line 647
    .line 648
    move-result-object v4

    .line 649
    invoke-static {v4, v7}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 650
    .line 651
    .line 652
    move-result v4

    .line 653
    if-eqz v4, :cond_30

    .line 654
    .line 655
    sget-object v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;->HOSTS_VS_GUESTS:Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 656
    .line 657
    :goto_2c
    move-object/from16 v48, v0

    .line 658
    .line 659
    goto :goto_2f

    .line 660
    :cond_30
    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    .line 661
    .line 662
    .line 663
    move-result v4

    .line 664
    if-eqz v4, :cond_31

    .line 665
    .line 666
    sget-object v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;->SINGLE_TEAM:Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 667
    .line 668
    goto :goto_2c

    .line 669
    :cond_31
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 670
    .line 671
    .line 672
    move-result v4

    .line 673
    const/4 v8, 0x2

    .line 674
    if-eq v4, v8, :cond_35

    .line 675
    .line 676
    invoke-interface {v2}, Ljava/util/List;->size()I

    .line 677
    .line 678
    .line 679
    move-result v4

    .line 680
    if-ne v4, v8, :cond_32

    .line 681
    .line 682
    goto :goto_2e

    .line 683
    :cond_32
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 684
    .line 685
    .line 686
    move-result v0

    .line 687
    if-gt v0, v8, :cond_34

    .line 688
    .line 689
    invoke-interface {v2}, Ljava/util/List;->size()I

    .line 690
    .line 691
    .line 692
    move-result v0

    .line 693
    if-le v0, v8, :cond_33

    .line 694
    .line 695
    goto :goto_2d

    .line 696
    :cond_33
    sget-object v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;->ONE_PLAYER_VS_ONE_PLAYER:Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 697
    .line 698
    goto :goto_2c

    .line 699
    :cond_34
    :goto_2d
    sget-object v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;->MULTITUDE_VS_MULTITUDE:Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 700
    .line 701
    goto :goto_2c

    .line 702
    :cond_35
    :goto_2e
    sget-object v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;->TWO_PLAYERS_VS_TWO_PLAYERS:Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 703
    .line 704
    goto :goto_2c

    .line 705
    :goto_2f
    invoke-virtual/range {p0 .. p0}, LJA0/c;->C()LIA0/c;

    .line 706
    .line 707
    .line 708
    move-result-object v0

    .line 709
    if-eqz v0, :cond_36

    .line 710
    .line 711
    invoke-virtual {v0}, LIA0/c;->b()Ljava/lang/Boolean;

    .line 712
    .line 713
    .line 714
    move-result-object v0

    .line 715
    if-eqz v0, :cond_36

    .line 716
    .line 717
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 718
    .line 719
    .line 720
    move-result v0

    .line 721
    move/from16 v49, v0

    .line 722
    .line 723
    goto :goto_30

    .line 724
    :cond_36
    const/16 v49, 0x0

    .line 725
    .line 726
    :goto_30
    invoke-virtual/range {p0 .. p0}, LJA0/c;->t()Ljava/lang/Long;

    .line 727
    .line 728
    .line 729
    move-result-object v50

    .line 730
    invoke-virtual/range {p0 .. p0}, LJA0/c;->e()Ljava/lang/Long;

    .line 731
    .line 732
    .line 733
    move-result-object v0

    .line 734
    if-eqz v0, :cond_37

    .line 735
    .line 736
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 737
    .line 738
    .line 739
    move-result-wide v51

    .line 740
    goto :goto_31

    .line 741
    :cond_37
    const-wide/16 v51, -0x1

    .line 742
    .line 743
    :goto_31
    invoke-virtual/range {p0 .. p0}, LJA0/c;->s()LIA0/b;

    .line 744
    .line 745
    .line 746
    move-result-object v0

    .line 747
    if-eqz v0, :cond_38

    .line 748
    .line 749
    invoke-virtual {v0}, LIA0/b;->h()Ljava/lang/String;

    .line 750
    .line 751
    .line 752
    move-result-object v0

    .line 753
    if-eqz v0, :cond_38

    .line 754
    .line 755
    invoke-static {v0}, Lkotlin/text/StringsKt;->y(Ljava/lang/String;)Ljava/lang/Long;

    .line 756
    .line 757
    .line 758
    move-result-object v0

    .line 759
    move-object/from16 v53, v0

    .line 760
    .line 761
    goto :goto_32

    .line 762
    :cond_38
    const/16 v53, 0x0

    .line 763
    .line 764
    :goto_32
    invoke-virtual/range {p0 .. p0}, LJA0/c;->B()Ljava/lang/Boolean;

    .line 765
    .line 766
    .line 767
    move-result-object v0

    .line 768
    invoke-static {v0, v7}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 769
    .line 770
    .line 771
    move-result v0

    .line 772
    invoke-virtual/range {p0 .. p0}, LJA0/c;->p()LIA0/g;

    .line 773
    .line 774
    .line 775
    move-result-object v2

    .line 776
    if-eqz v2, :cond_39

    .line 777
    .line 778
    invoke-virtual {v2}, LIA0/g;->o()LIA0/g$g;

    .line 779
    .line 780
    .line 781
    move-result-object v2

    .line 782
    if-eqz v2, :cond_39

    .line 783
    .line 784
    invoke-virtual {v2}, LIA0/g$g;->c()Ljava/lang/Long;

    .line 785
    .line 786
    .line 787
    move-result-object v2

    .line 788
    if-eqz v2, :cond_39

    .line 789
    .line 790
    invoke-virtual {v2}, Ljava/lang/Long;->longValue()J

    .line 791
    .line 792
    .line 793
    move-result-wide v7

    .line 794
    goto :goto_33

    .line 795
    :cond_39
    const-wide/16 v7, 0x0

    .line 796
    .line 797
    :goto_33
    invoke-static {v7, v8}, Ll8/b$a$c;->f(J)J

    .line 798
    .line 799
    .line 800
    move-result-wide v54

    .line 801
    invoke-virtual/range {p0 .. p0}, LJA0/c;->p()LIA0/g;

    .line 802
    .line 803
    .line 804
    move-result-object v2

    .line 805
    const/4 v4, 0x1

    .line 806
    if-eqz v2, :cond_3b

    .line 807
    .line 808
    invoke-virtual {v2}, LIA0/g;->o()LIA0/g$g;

    .line 809
    .line 810
    .line 811
    move-result-object v2

    .line 812
    if-eqz v2, :cond_3b

    .line 813
    .line 814
    invoke-virtual {v2}, LIA0/g$g;->a()Ljava/lang/Integer;

    .line 815
    .line 816
    .line 817
    move-result-object v2

    .line 818
    if-nez v2, :cond_3a

    .line 819
    .line 820
    goto :goto_34

    .line 821
    :cond_3a
    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    .line 822
    .line 823
    .line 824
    move-result v2

    .line 825
    const/4 v7, -0x1

    .line 826
    if-ne v2, v7, :cond_3b

    .line 827
    .line 828
    const/16 v56, 0x1

    .line 829
    .line 830
    goto :goto_35

    .line 831
    :cond_3b
    :goto_34
    const/16 v56, 0x0

    .line 832
    .line 833
    :goto_35
    invoke-virtual/range {p0 .. p0}, LJA0/c;->p()LIA0/g;

    .line 834
    .line 835
    .line 836
    move-result-object v2

    .line 837
    if-eqz v2, :cond_3c

    .line 838
    .line 839
    invoke-virtual {v2}, LIA0/g;->o()LIA0/g$g;

    .line 840
    .line 841
    .line 842
    move-result-object v2

    .line 843
    if-eqz v2, :cond_3c

    .line 844
    .line 845
    invoke-virtual {v2}, LIA0/g$g;->b()Ljava/lang/Boolean;

    .line 846
    .line 847
    .line 848
    move-result-object v2

    .line 849
    if-eqz v2, :cond_3c

    .line 850
    .line 851
    invoke-virtual {v2}, Ljava/lang/Boolean;->booleanValue()Z

    .line 852
    .line 853
    .line 854
    move-result v4

    .line 855
    move/from16 v57, v4

    .line 856
    .line 857
    goto :goto_36

    .line 858
    :cond_3c
    const/16 v57, 0x1

    .line 859
    .line 860
    :goto_36
    invoke-virtual/range {p0 .. p0}, LJA0/c;->p()LIA0/g;

    .line 861
    .line 862
    .line 863
    move-result-object v2

    .line 864
    if-eqz v2, :cond_3d

    .line 865
    .line 866
    invoke-virtual {v2}, LIA0/g;->d()Ljava/lang/String;

    .line 867
    .line 868
    .line 869
    move-result-object v2

    .line 870
    goto :goto_37

    .line 871
    :cond_3d
    const/4 v2, 0x0

    .line 872
    :goto_37
    if-nez v2, :cond_3e

    .line 873
    .line 874
    move-object/from16 v58, v6

    .line 875
    .line 876
    goto :goto_38

    .line 877
    :cond_3e
    move-object/from16 v58, v2

    .line 878
    .line 879
    :goto_38
    invoke-virtual/range {p0 .. p0}, LJA0/c;->p()LIA0/g;

    .line 880
    .line 881
    .line 882
    move-result-object v2

    .line 883
    if-eqz v2, :cond_40

    .line 884
    .line 885
    invoke-virtual {v2}, LIA0/g;->k()LIA0/g$c;

    .line 886
    .line 887
    .line 888
    move-result-object v2

    .line 889
    if-eqz v2, :cond_40

    .line 890
    .line 891
    invoke-virtual {v2}, LIA0/g$c;->a()Ljava/util/Map;

    .line 892
    .line 893
    .line 894
    move-result-object v2

    .line 895
    if-eqz v2, :cond_40

    .line 896
    .line 897
    new-instance v4, Ljava/util/ArrayList;

    .line 898
    .line 899
    invoke-interface {v2}, Ljava/util/Map;->size()I

    .line 900
    .line 901
    .line 902
    move-result v7

    .line 903
    invoke-direct {v4, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 904
    .line 905
    .line 906
    invoke-interface {v2}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    .line 907
    .line 908
    .line 909
    move-result-object v2

    .line 910
    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    .line 911
    .line 912
    .line 913
    move-result-object v2

    .line 914
    :goto_39
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 915
    .line 916
    .line 917
    move-result v7

    .line 918
    if-eqz v7, :cond_3f

    .line 919
    .line 920
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 921
    .line 922
    .line 923
    move-result-object v7

    .line 924
    check-cast v7, Ljava/util/Map$Entry;

    .line 925
    .line 926
    new-instance v8, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;

    .line 927
    .line 928
    move/from16 v23, v0

    .line 929
    .line 930
    sget-object v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;->Companion:Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key$a;

    .line 931
    .line 932
    invoke-interface {v7}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    .line 933
    .line 934
    .line 935
    move-result-object v38

    .line 936
    move-object/from16 v42, v1

    .line 937
    .line 938
    move-object/from16 v1, v38

    .line 939
    .line 940
    check-cast v1, Ljava/lang/String;

    .line 941
    .line 942
    invoke-virtual {v0, v1}, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key$a;->a(Ljava/lang/String;)Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;

    .line 943
    .line 944
    .line 945
    move-result-object v0

    .line 946
    invoke-interface {v7}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    .line 947
    .line 948
    .line 949
    move-result-object v1

    .line 950
    check-cast v1, Ljava/lang/String;

    .line 951
    .line 952
    invoke-direct {v8, v0, v1}, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;-><init>(Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;Ljava/lang/String;)V

    .line 953
    .line 954
    .line 955
    invoke-interface {v4, v8}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 956
    .line 957
    .line 958
    move/from16 v0, v23

    .line 959
    .line 960
    move-object/from16 v1, v42

    .line 961
    .line 962
    goto :goto_39

    .line 963
    :cond_3f
    move/from16 v23, v0

    .line 964
    .line 965
    move-object/from16 v42, v1

    .line 966
    .line 967
    :goto_3a
    move-object/from16 v59, v4

    .line 968
    .line 969
    goto :goto_3b

    .line 970
    :cond_40
    move/from16 v23, v0

    .line 971
    .line 972
    move-object/from16 v42, v1

    .line 973
    .line 974
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 975
    .line 976
    .line 977
    move-result-object v4

    .line 978
    goto :goto_3a

    .line 979
    :goto_3b
    invoke-virtual/range {p0 .. p0}, LJA0/c;->p()LIA0/g;

    .line 980
    .line 981
    .line 982
    move-result-object v0

    .line 983
    if-eqz v0, :cond_47

    .line 984
    .line 985
    invoke-virtual {v0}, LIA0/g;->m()Ljava/util/Map;

    .line 986
    .line 987
    .line 988
    move-result-object v0

    .line 989
    if-eqz v0, :cond_47

    .line 990
    .line 991
    new-instance v1, Ljava/util/ArrayList;

    .line 992
    .line 993
    invoke-interface {v0}, Ljava/util/Map;->size()I

    .line 994
    .line 995
    .line 996
    move-result v2

    .line 997
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 998
    .line 999
    .line 1000
    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    .line 1001
    .line 1002
    .line 1003
    move-result-object v0

    .line 1004
    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    .line 1005
    .line 1006
    .line 1007
    move-result-object v0

    .line 1008
    :goto_3c
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 1009
    .line 1010
    .line 1011
    move-result v2

    .line 1012
    if-eqz v2, :cond_46

    .line 1013
    .line 1014
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 1015
    .line 1016
    .line 1017
    move-result-object v2

    .line 1018
    check-cast v2, Ljava/util/Map$Entry;

    .line 1019
    .line 1020
    invoke-interface {v2}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    .line 1021
    .line 1022
    .line 1023
    move-result-object v2

    .line 1024
    check-cast v2, Ljava/util/List;

    .line 1025
    .line 1026
    if-eqz v2, :cond_45

    .line 1027
    .line 1028
    new-instance v4, Ljava/util/ArrayList;

    .line 1029
    .line 1030
    const/16 v7, 0xa

    .line 1031
    .line 1032
    invoke-static {v2, v7}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 1033
    .line 1034
    .line 1035
    move-result v8

    .line 1036
    invoke-direct {v4, v8}, Ljava/util/ArrayList;-><init>(I)V

    .line 1037
    .line 1038
    .line 1039
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 1040
    .line 1041
    .line 1042
    move-result-object v2

    .line 1043
    :goto_3d
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 1044
    .line 1045
    .line 1046
    move-result v8

    .line 1047
    if-eqz v8, :cond_44

    .line 1048
    .line 1049
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 1050
    .line 1051
    .line 1052
    move-result-object v8

    .line 1053
    check-cast v8, LIA0/g$e;

    .line 1054
    .line 1055
    new-instance v7, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic;

    .line 1056
    .line 1057
    move-object/from16 v38, v0

    .line 1058
    .line 1059
    sget-object v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;->Companion:Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type$a;

    .line 1060
    .line 1061
    move-object/from16 v43, v2

    .line 1062
    .line 1063
    invoke-virtual {v8}, LIA0/g$e;->a()Ljava/lang/Integer;

    .line 1064
    .line 1065
    .line 1066
    move-result-object v2

    .line 1067
    invoke-virtual {v0, v2}, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type$a;->a(Ljava/lang/Integer;)Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;

    .line 1068
    .line 1069
    .line 1070
    move-result-object v0

    .line 1071
    invoke-virtual {v8}, LIA0/g$e;->d()Ljava/lang/String;

    .line 1072
    .line 1073
    .line 1074
    move-result-object v2

    .line 1075
    if-nez v2, :cond_41

    .line 1076
    .line 1077
    move-object v2, v6

    .line 1078
    :cond_41
    invoke-virtual {v8}, LIA0/g$e;->b()Ljava/lang/String;

    .line 1079
    .line 1080
    .line 1081
    move-result-object v60

    .line 1082
    move/from16 v61, v3

    .line 1083
    .line 1084
    if-nez v60, :cond_42

    .line 1085
    .line 1086
    move-object v3, v6

    .line 1087
    goto :goto_3e

    .line 1088
    :cond_42
    move-object/from16 v3, v60

    .line 1089
    .line 1090
    :goto_3e
    invoke-virtual {v8}, LIA0/g$e;->c()Ljava/lang/String;

    .line 1091
    .line 1092
    .line 1093
    move-result-object v8

    .line 1094
    if-nez v8, :cond_43

    .line 1095
    .line 1096
    move-object v8, v6

    .line 1097
    :cond_43
    invoke-direct {v7, v0, v2, v3, v8}, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic;-><init>(Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 1098
    .line 1099
    .line 1100
    invoke-interface {v4, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 1101
    .line 1102
    .line 1103
    move-object/from16 v0, v38

    .line 1104
    .line 1105
    move-object/from16 v2, v43

    .line 1106
    .line 1107
    move/from16 v3, v61

    .line 1108
    .line 1109
    const/16 v7, 0xa

    .line 1110
    .line 1111
    goto :goto_3d

    .line 1112
    :cond_44
    move-object/from16 v38, v0

    .line 1113
    .line 1114
    move/from16 v61, v3

    .line 1115
    .line 1116
    goto :goto_3f

    .line 1117
    :cond_45
    move-object/from16 v38, v0

    .line 1118
    .line 1119
    move/from16 v61, v3

    .line 1120
    .line 1121
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 1122
    .line 1123
    .line 1124
    move-result-object v4

    .line 1125
    :goto_3f
    new-instance v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel;

    .line 1126
    .line 1127
    invoke-direct {v0, v4}, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel;-><init>(Ljava/util/List;)V

    .line 1128
    .line 1129
    .line 1130
    invoke-interface {v1, v0}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 1131
    .line 1132
    .line 1133
    move-object/from16 v0, v38

    .line 1134
    .line 1135
    move/from16 v3, v61

    .line 1136
    .line 1137
    goto/16 :goto_3c

    .line 1138
    .line 1139
    :cond_46
    move/from16 v61, v3

    .line 1140
    .line 1141
    :goto_40
    move-object/from16 v60, v1

    .line 1142
    .line 1143
    goto :goto_41

    .line 1144
    :cond_47
    move/from16 v61, v3

    .line 1145
    .line 1146
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 1147
    .line 1148
    .line 1149
    move-result-object v1

    .line 1150
    goto :goto_40

    .line 1151
    :goto_41
    invoke-virtual/range {p0 .. p0}, LJA0/c;->p()LIA0/g;

    .line 1152
    .line 1153
    .line 1154
    move-result-object v0

    .line 1155
    if-eqz v0, :cond_48

    .line 1156
    .line 1157
    invoke-virtual {v0}, LIA0/g;->p()Ljava/lang/Boolean;

    .line 1158
    .line 1159
    .line 1160
    move-result-object v0

    .line 1161
    if-eqz v0, :cond_48

    .line 1162
    .line 1163
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 1164
    .line 1165
    .line 1166
    move-result v7

    .line 1167
    goto :goto_42

    .line 1168
    :cond_48
    const/4 v7, 0x0

    .line 1169
    :goto_42
    invoke-virtual/range {p0 .. p0}, LJA0/c;->p()LIA0/g;

    .line 1170
    .line 1171
    .line 1172
    move-result-object v0

    .line 1173
    if-eqz v0, :cond_4a

    .line 1174
    .line 1175
    invoke-virtual {v0}, LIA0/g;->c()Ljava/lang/String;

    .line 1176
    .line 1177
    .line 1178
    move-result-object v0

    .line 1179
    if-nez v0, :cond_49

    .line 1180
    .line 1181
    goto :goto_43

    .line 1182
    :cond_49
    move-object v6, v0

    .line 1183
    :cond_4a
    :goto_43
    new-instance v8, LYA0/a;

    .line 1184
    .line 1185
    const/16 v62, 0x0

    .line 1186
    .line 1187
    move/from16 v38, v5

    .line 1188
    .line 1189
    move/from16 v33, v23

    .line 1190
    .line 1191
    move/from16 v43, v61

    .line 1192
    .line 1193
    move-object/from16 v23, v6

    .line 1194
    .line 1195
    move/from16 v61, v7

    .line 1196
    .line 1197
    invoke-direct/range {v8 .. v62}, LYA0/a;-><init>(JJLjava/util/List;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;ZZZZLjava/lang/String;LYA0/e;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;JJZZLjava/lang/String;Ljava/lang/String;JZLjava/lang/String;JLjava/util/List;IZZILjava/lang/String;Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;ZLjava/lang/Long;JLjava/lang/Long;JZZLjava/lang/String;Ljava/util/List;Ljava/util/List;ZLkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 1198
    .line 1199
    .line 1200
    return-object v8
.end method
