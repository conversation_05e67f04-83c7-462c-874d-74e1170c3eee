.class public interface abstract LL3/j;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract a(LL3/l;)V
    .param p1    # LL3/l;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method

.method public abstract c(LL3/l;)V
    .param p1    # LL3/l;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
.end method
