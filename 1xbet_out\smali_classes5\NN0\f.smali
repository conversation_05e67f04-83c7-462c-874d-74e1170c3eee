.class public abstract LNN0/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LGN0/f;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LNN0/f$a;,
        LNN0/f$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u00087\u0018\u00002\u00020\u0001:\u0002\t\u0006B\u0011\u0008\u0004\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u001a\u0010\u0003\u001a\u00020\u00028\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0006\u0010\u0007\u001a\u0004\u0008\u0006\u0010\u0008\u0082\u0001\u0002\n\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "LNN0/f;",
        "LGN0/f;",
        "Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;",
        "color",
        "<init>",
        "(Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;)V",
        "a",
        "Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;",
        "()Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;",
        "b",
        "LNN0/f$a;",
        "LNN0/f$b;",
        "statistic_core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, LNN0/f;->a:Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;

    return-void
.end method

.method public synthetic constructor <init>(Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, LNN0/f;-><init>(Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;)V

    return-void
.end method


# virtual methods
.method public a()Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LNN0/f;->a:Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;

    .line 2
    .line 3
    return-object v0
.end method
