.class final Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.new_games.presentation.NewGamesFolderViewModel$loadBanners$2"
    f = "NewGamesFolderViewModel.kt"
    l = {
        0x122
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->f5()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->r4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->g()Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 38
    .line 39
    .line 40
    move-result p1

    .line 41
    if-lez p1, :cond_2

    .line 42
    .line 43
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    goto :goto_1

    .line 48
    :cond_2
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 49
    .line 50
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->B4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lkc1/b;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 55
    .line 56
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->r4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->d()J

    .line 61
    .line 62
    .line 63
    move-result-wide v3

    .line 64
    invoke-interface {p1, v3, v4}, Lkc1/b;->a(J)Lkotlinx/coroutines/flow/e;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    iput v2, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;->label:I

    .line 69
    .line 70
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/g;->L(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    if-ne p1, v0, :cond_3

    .line 75
    .line 76
    return-object v0

    .line 77
    :cond_3
    :goto_0
    check-cast p1, Ljava/util/List;

    .line 78
    .line 79
    :goto_1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 80
    .line 81
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->u4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Ljava/util/List;

    .line 82
    .line 83
    .line 84
    move-result-object v0

    .line 85
    invoke-interface {v0}, Ljava/util/List;->clear()V

    .line 86
    .line 87
    .line 88
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 89
    .line 90
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->u4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Ljava/util/List;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 95
    .line 96
    .line 97
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 98
    .line 99
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->v4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lkotlinx/coroutines/flow/V;

    .line 100
    .line 101
    .line 102
    move-result-object v0

    .line 103
    new-instance v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$a;

    .line 104
    .line 105
    new-instance v2, Lorg/xbet/uikit/components/bannercollection/a$a;

    .line 106
    .line 107
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 108
    .line 109
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->t4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;

    .line 110
    .line 111
    .line 112
    move-result-object v3

    .line 113
    invoke-static {p1, v3}, Lorg/xplatform/aggregator/impl/new_games/presentation/a;->a(Ljava/util/List;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;)Ljava/util/List;

    .line 114
    .line 115
    .line 116
    move-result-object p1

    .line 117
    invoke-direct {v2, p1}, Lorg/xbet/uikit/components/bannercollection/a$a;-><init>(Ljava/util/List;)V

    .line 118
    .line 119
    .line 120
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$a;-><init>(Lorg/xbet/uikit/components/bannercollection/a$a;)V

    .line 121
    .line 122
    .line 123
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 124
    .line 125
    .line 126
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 127
    .line 128
    return-object p1
.end method
