.class public final synthetic Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;

.field public final synthetic b:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/b;->a:Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;

    iput-object p2, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/b;->b:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/b;->a:Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;

    iget-object v1, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/b;->b:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    invoke-static {v0, v1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->k(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
