.class public final synthetic LV01/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:F

.field public final synthetic b:F

.field public final synthetic c:F

.field public final synthetic d:F

.field public final synthetic e:F

.field public final synthetic f:J


# direct methods
.method public synthetic constructor <init>(FFFFFJ)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, LV01/f;->a:F

    iput p2, p0, LV01/f;->b:F

    iput p3, p0, LV01/f;->c:F

    iput p4, p0, LV01/f;->d:F

    iput p5, p0, LV01/f;->e:F

    iput-wide p6, p0, LV01/f;->f:J

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    iget v0, p0, LV01/f;->a:F

    iget v1, p0, LV01/f;->b:F

    iget v2, p0, LV01/f;->c:F

    iget v3, p0, LV01/f;->d:F

    iget v4, p0, LV01/f;->e:F

    iget-wide v5, p0, LV01/f;->f:J

    move-object v7, p1

    check-cast v7, Landroidx/compose/ui/graphics/drawscope/f;

    invoke-static/range {v0 .. v7}, LV01/g;->b(FFFFFJLandroidx/compose/ui/graphics/drawscope/f;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
