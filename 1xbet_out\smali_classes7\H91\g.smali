.class public final LH91/g;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005\u001a\u001f\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u00002\u0008\u0010\u0003\u001a\u0004\u0018\u00010\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u001a\u001f\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u00002\u0008\u0010\u0003\u001a\u0004\u0018\u00010\u0002\u00a2\u0006\u0004\u0008\u0006\u0010\u0005\u00a8\u0006\u0007"
    }
    d2 = {
        "",
        "categoryId",
        "",
        "promoCategoryId",
        "b",
        "(JLjava/lang/Integer;)I",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(JLjava/lang/Integer;)I
    .locals 3

    .line 1
    const-wide v0, 0x7fffffffffffffffL

    .line 2
    .line 3
    .line 4
    .line 5
    .line 6
    cmp-long v2, p0, v0

    .line 7
    .line 8
    if-nez v2, :cond_0

    .line 9
    .line 10
    const/16 p0, 0x4c5

    .line 11
    .line 12
    return p0

    .line 13
    :cond_0
    const-wide/16 v0, 0x4b

    .line 14
    .line 15
    cmp-long v2, p0, v0

    .line 16
    .line 17
    if-nez v2, :cond_1

    .line 18
    .line 19
    const/16 p0, 0x4c6

    .line 20
    .line 21
    return p0

    .line 22
    :cond_1
    const-wide/16 v0, 0x11

    .line 23
    .line 24
    cmp-long v2, p0, v0

    .line 25
    .line 26
    if-nez v2, :cond_2

    .line 27
    .line 28
    const/16 p0, 0x4c7

    .line 29
    .line 30
    return p0

    .line 31
    :cond_2
    const-wide/16 v0, 0x5e

    .line 32
    .line 33
    cmp-long v2, p0, v0

    .line 34
    .line 35
    if-nez v2, :cond_3

    .line 36
    .line 37
    const/16 p0, 0x4c8

    .line 38
    .line 39
    return p0

    .line 40
    :cond_3
    const-wide/16 v0, 0x5

    .line 41
    .line 42
    cmp-long v2, p0, v0

    .line 43
    .line 44
    if-nez v2, :cond_4

    .line 45
    .line 46
    const/16 p0, 0x4c9

    .line 47
    .line 48
    return p0

    .line 49
    :cond_4
    const-wide/16 v0, 0x1f

    .line 50
    .line 51
    cmp-long v2, p0, v0

    .line 52
    .line 53
    if-nez v2, :cond_5

    .line 54
    .line 55
    const/16 p0, 0x4ca

    .line 56
    .line 57
    return p0

    .line 58
    :cond_5
    const-wide/16 v0, 0x9

    .line 59
    .line 60
    cmp-long v2, p0, v0

    .line 61
    .line 62
    if-nez v2, :cond_6

    .line 63
    .line 64
    const/16 p0, 0x4cb

    .line 65
    .line 66
    return p0

    .line 67
    :cond_6
    const-wide/16 v0, 0xf

    .line 68
    .line 69
    cmp-long v2, p0, v0

    .line 70
    .line 71
    if-nez v2, :cond_7

    .line 72
    .line 73
    const/16 p0, 0x4cc

    .line 74
    .line 75
    return p0

    .line 76
    :cond_7
    const-wide/16 v0, 0x3

    .line 77
    .line 78
    cmp-long v2, p0, v0

    .line 79
    .line 80
    if-nez v2, :cond_8

    .line 81
    .line 82
    const/16 p0, 0x4cd

    .line 83
    .line 84
    return p0

    .line 85
    :cond_8
    const-wide/16 v0, 0x5d

    .line 86
    .line 87
    cmp-long v2, p0, v0

    .line 88
    .line 89
    if-nez v2, :cond_9

    .line 90
    .line 91
    const/16 p0, 0x4ce

    .line 92
    .line 93
    return p0

    .line 94
    :cond_9
    const-wide/16 v0, 0x12c

    .line 95
    .line 96
    cmp-long v2, p0, v0

    .line 97
    .line 98
    if-nez v2, :cond_a

    .line 99
    .line 100
    const/16 p0, 0x477

    .line 101
    .line 102
    return p0

    .line 103
    :cond_a
    if-eqz p2, :cond_b

    .line 104
    .line 105
    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    .line 106
    .line 107
    .line 108
    move-result p0

    .line 109
    return p0

    .line 110
    :cond_b
    const/4 p0, 0x0

    .line 111
    return p0
.end method

.method public static final b(JLjava/lang/Integer;)I
    .locals 3

    .line 1
    const-wide v0, 0x7fffffffffffffffL

    .line 2
    .line 3
    .line 4
    .line 5
    .line 6
    cmp-long v2, p0, v0

    .line 7
    .line 8
    if-nez v2, :cond_0

    .line 9
    .line 10
    const/16 p0, 0x461

    .line 11
    .line 12
    return p0

    .line 13
    :cond_0
    const-wide/16 v0, 0x11

    .line 14
    .line 15
    cmp-long v2, p0, v0

    .line 16
    .line 17
    if-nez v2, :cond_1

    .line 18
    .line 19
    const/16 p0, 0x462

    .line 20
    .line 21
    return p0

    .line 22
    :cond_1
    const-wide/16 v0, 0x15

    .line 23
    .line 24
    cmp-long v2, p0, v0

    .line 25
    .line 26
    if-nez v2, :cond_2

    .line 27
    .line 28
    const/16 p0, 0x463

    .line 29
    .line 30
    return p0

    .line 31
    :cond_2
    const-wide/16 v0, 0x59

    .line 32
    .line 33
    cmp-long v2, p0, v0

    .line 34
    .line 35
    if-nez v2, :cond_3

    .line 36
    .line 37
    const/16 p0, 0x464

    .line 38
    .line 39
    return p0

    .line 40
    :cond_3
    const-wide/16 v0, 0x99

    .line 41
    .line 42
    cmp-long v2, p0, v0

    .line 43
    .line 44
    if-nez v2, :cond_4

    .line 45
    .line 46
    const/16 p0, 0x465

    .line 47
    .line 48
    return p0

    .line 49
    :cond_4
    const-wide/16 v0, 0x70

    .line 50
    .line 51
    cmp-long v2, p0, v0

    .line 52
    .line 53
    if-nez v2, :cond_5

    .line 54
    .line 55
    const/16 p0, 0x466

    .line 56
    .line 57
    return p0

    .line 58
    :cond_5
    const-wide/16 v0, 0x71

    .line 59
    .line 60
    cmp-long v2, p0, v0

    .line 61
    .line 62
    if-nez v2, :cond_6

    .line 63
    .line 64
    const/16 p0, 0x467

    .line 65
    .line 66
    return p0

    .line 67
    :cond_6
    const-wide/16 v0, 0x6a

    .line 68
    .line 69
    cmp-long v2, p0, v0

    .line 70
    .line 71
    if-nez v2, :cond_7

    .line 72
    .line 73
    const/16 p0, 0x468

    .line 74
    .line 75
    return p0

    .line 76
    :cond_7
    const-wide/16 v0, 0xcc

    .line 77
    .line 78
    cmp-long v2, p0, v0

    .line 79
    .line 80
    if-nez v2, :cond_8

    .line 81
    .line 82
    const/16 p0, 0x469

    .line 83
    .line 84
    return p0

    .line 85
    :cond_8
    const-wide/16 v0, 0xcd

    .line 86
    .line 87
    cmp-long v2, p0, v0

    .line 88
    .line 89
    if-nez v2, :cond_9

    .line 90
    .line 91
    const/16 p0, 0x46b

    .line 92
    .line 93
    return p0

    .line 94
    :cond_9
    const-wide/16 v0, 0x1a

    .line 95
    .line 96
    cmp-long v2, p0, v0

    .line 97
    .line 98
    if-nez v2, :cond_a

    .line 99
    .line 100
    const/16 p0, 0x46c

    .line 101
    .line 102
    return p0

    .line 103
    :cond_a
    const-wide/16 v0, 0x10e

    .line 104
    .line 105
    cmp-long v2, p0, v0

    .line 106
    .line 107
    if-nez v2, :cond_b

    .line 108
    .line 109
    const/16 p0, 0x474

    .line 110
    .line 111
    return p0

    .line 112
    :cond_b
    const-wide/16 v0, 0x10c

    .line 113
    .line 114
    cmp-long v2, p0, v0

    .line 115
    .line 116
    if-nez v2, :cond_c

    .line 117
    .line 118
    const/16 p0, 0x475

    .line 119
    .line 120
    return p0

    .line 121
    :cond_c
    const-wide/16 v0, 0x12c

    .line 122
    .line 123
    cmp-long v2, p0, v0

    .line 124
    .line 125
    if-nez v2, :cond_d

    .line 126
    .line 127
    const/16 p0, 0x476

    .line 128
    .line 129
    return p0

    .line 130
    :cond_d
    if-eqz p2, :cond_e

    .line 131
    .line 132
    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    .line 133
    .line 134
    .line 135
    move-result p0

    .line 136
    return p0

    .line 137
    :cond_e
    const/4 p0, 0x0

    .line 138
    return p0
.end method
