.class public final LQA0/b$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQA0/b;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LQA0/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0082\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0000\n\u0002\u00084\u0008\u0086\u0008\u0018\u00002\u00020\u0001B\u009f\u0001\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0008\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u0012\u0008\u0010\t\u001a\u0004\u0018\u00010\u0008\u0012\u0008\u0010\u000b\u001a\u0004\u0018\u00010\n\u0012\u0008\u0010\r\u001a\u0004\u0018\u00010\u000c\u0012\u0008\u0010\u000f\u001a\u0004\u0018\u00010\u000e\u0012\u0008\u0010\u0011\u001a\u0004\u0018\u00010\u0010\u0012\u0008\u0010\u0013\u001a\u0004\u0018\u00010\u0012\u0012\u0008\u0010\u0015\u001a\u0004\u0018\u00010\u0014\u0012\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0008\u0010\u001b\u001a\u0004\u0018\u00010\u001a\u0012\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u001c\u0012\u0008\u0010\u001f\u001a\u0004\u0018\u00010\u001e\u0012\u0006\u0010!\u001a\u00020 \u00a2\u0006\u0004\u0008\"\u0010#J\u0010\u0010%\u001a\u00020$H\u00d6\u0001\u00a2\u0006\u0004\u0008%\u0010&J\u0010\u0010(\u001a\u00020\'H\u00d6\u0001\u00a2\u0006\u0004\u0008(\u0010)J\u001a\u0010,\u001a\u00020 2\u0008\u0010+\u001a\u0004\u0018\u00010*H\u00d6\u0003\u00a2\u0006\u0004\u0008,\u0010-R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008.\u0010/\u001a\u0004\u0008.\u00100R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u00081\u00102\u001a\u0004\u00083\u00104R\u0019\u0010\u0007\u001a\u0004\u0018\u00010\u00068\u0006\u00a2\u0006\u000c\n\u0004\u00085\u00106\u001a\u0004\u00085\u00107R\u0019\u0010\t\u001a\u0004\u0018\u00010\u00088\u0006\u00a2\u0006\u000c\n\u0004\u00088\u00109\u001a\u0004\u0008:\u0010;R\u0019\u0010\u000b\u001a\u0004\u0018\u00010\n8\u0006\u00a2\u0006\u000c\n\u0004\u0008<\u0010=\u001a\u0004\u0008>\u0010?R\u0019\u0010\r\u001a\u0004\u0018\u00010\u000c8\u0006\u00a2\u0006\u000c\n\u0004\u0008@\u0010A\u001a\u0004\u0008B\u0010CR\u0019\u0010\u000f\u001a\u0004\u0018\u00010\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008>\u0010D\u001a\u0004\u0008@\u0010ER\u0019\u0010\u0011\u001a\u0004\u0018\u00010\u00108\u0006\u00a2\u0006\u000c\n\u0004\u0008F\u0010G\u001a\u0004\u0008<\u0010HR\u0019\u0010\u0013\u001a\u0004\u0018\u00010\u00128\u0006\u00a2\u0006\u000c\n\u0004\u0008I\u0010J\u001a\u0004\u00088\u0010KR\u0019\u0010\u0015\u001a\u0004\u0018\u00010\u00148\u0006\u00a2\u0006\u000c\n\u0004\u0008L\u0010M\u001a\u0004\u0008F\u0010NR\u0019\u0010\u0017\u001a\u0004\u0018\u00010\u00168\u0006\u00a2\u0006\u000c\n\u0004\u00083\u0010O\u001a\u0004\u0008P\u0010QR\u0017\u0010\u0019\u001a\u00020\u00188\u0006\u00a2\u0006\u000c\n\u0004\u0008:\u0010R\u001a\u0004\u0008I\u0010SR\u0019\u0010\u001b\u001a\u0004\u0018\u00010\u001a8\u0006\u00a2\u0006\u000c\n\u0004\u0008B\u0010T\u001a\u0004\u0008U\u0010VR\u0019\u0010\u001d\u001a\u0004\u0018\u00010\u001c8\u0006\u00a2\u0006\u000c\n\u0004\u0008U\u0010W\u001a\u0004\u0008L\u0010XR\u0019\u0010\u001f\u001a\u0004\u0018\u00010\u001e8\u0006\u00a2\u0006\u000c\n\u0004\u0008Y\u0010Z\u001a\u0004\u00081\u0010[R\u0017\u0010!\u001a\u00020 8\u0006\u00a2\u0006\u000c\n\u0004\u0008P\u0010\\\u001a\u0004\u0008Y\u0010]\u00a8\u0006^"
    }
    d2 = {
        "LQA0/b$a;",
        "LQA0/b;",
        "LSA0/a;",
        "cardCommonModel",
        "LTA0/a;",
        "compressedCardCommonModel",
        "LUA0/a;",
        "cardFootballPeriodModel",
        "LUA0/b;",
        "compressedCardFootballPeriodModel",
        "LVA0/a;",
        "cardPeriodModel",
        "LVA0/b;",
        "compressedCardPeriodModel",
        "LRA0/d;",
        "cardPenaltyModel",
        "LRA0/c;",
        "cardMatchReviewModel",
        "LRA0/b;",
        "cardHostVsGuestsModel",
        "LRA0/e;",
        "cardShortStatisticModel",
        "LeB0/a;",
        "stadiumInfoModel",
        "LRA0/f;",
        "cardTimerSectionModel",
        "LfB0/a;",
        "lineStatisticModel",
        "Lorg/xbet/sportgame/core/domain/models/cards/CardWeatherModel;",
        "cardWeatherModel",
        "LRA0/a;",
        "cardFootballActivitiesModel",
        "",
        "show24",
        "<init>",
        "(LSA0/a;LTA0/a;LUA0/a;LUA0/b;LVA0/a;LVA0/b;LRA0/d;LRA0/c;LRA0/b;LRA0/e;LeB0/a;LRA0/f;LfB0/a;Lorg/xbet/sportgame/core/domain/models/cards/CardWeatherModel;LRA0/a;Z)V",
        "",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "",
        "other",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "LSA0/a;",
        "()LSA0/a;",
        "b",
        "LTA0/a;",
        "k",
        "()LTA0/a;",
        "c",
        "LUA0/a;",
        "()LUA0/a;",
        "d",
        "LUA0/b;",
        "l",
        "()LUA0/b;",
        "e",
        "LVA0/a;",
        "g",
        "()LVA0/a;",
        "f",
        "LVA0/b;",
        "m",
        "()LVA0/b;",
        "LRA0/d;",
        "()LRA0/d;",
        "h",
        "LRA0/c;",
        "()LRA0/c;",
        "i",
        "LRA0/b;",
        "()LRA0/b;",
        "j",
        "LRA0/e;",
        "()LRA0/e;",
        "LeB0/a;",
        "p",
        "()LeB0/a;",
        "LRA0/f;",
        "()LRA0/f;",
        "LfB0/a;",
        "n",
        "()LfB0/a;",
        "Lorg/xbet/sportgame/core/domain/models/cards/CardWeatherModel;",
        "()Lorg/xbet/sportgame/core/domain/models/cards/CardWeatherModel;",
        "o",
        "LRA0/a;",
        "()LRA0/a;",
        "Z",
        "()Z",
        "core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LSA0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LTA0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LUA0/a;

.field public final d:LUA0/b;

.field public final e:LVA0/a;

.field public final f:LVA0/b;

.field public final g:LRA0/d;

.field public final h:LRA0/c;

.field public final i:LRA0/b;

.field public final j:LRA0/e;

.field public final k:LeB0/a;

.field public final l:LRA0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:LfB0/a;

.field public final n:Lorg/xbet/sportgame/core/domain/models/cards/CardWeatherModel;

.field public final o:LRA0/a;

.field public final p:Z


# direct methods
.method public constructor <init>(LSA0/a;LTA0/a;LUA0/a;LUA0/b;LVA0/a;LVA0/b;LRA0/d;LRA0/c;LRA0/b;LRA0/e;LeB0/a;LRA0/f;LfB0/a;Lorg/xbet/sportgame/core/domain/models/cards/CardWeatherModel;LRA0/a;Z)V
    .locals 0
    .param p1    # LSA0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LTA0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LRA0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LQA0/b$a;->a:LSA0/a;

    .line 5
    .line 6
    iput-object p2, p0, LQA0/b$a;->b:LTA0/a;

    .line 7
    .line 8
    iput-object p3, p0, LQA0/b$a;->c:LUA0/a;

    .line 9
    .line 10
    iput-object p4, p0, LQA0/b$a;->d:LUA0/b;

    .line 11
    .line 12
    iput-object p5, p0, LQA0/b$a;->e:LVA0/a;

    .line 13
    .line 14
    iput-object p6, p0, LQA0/b$a;->f:LVA0/b;

    .line 15
    .line 16
    iput-object p7, p0, LQA0/b$a;->g:LRA0/d;

    .line 17
    .line 18
    iput-object p8, p0, LQA0/b$a;->h:LRA0/c;

    .line 19
    .line 20
    iput-object p9, p0, LQA0/b$a;->i:LRA0/b;

    .line 21
    .line 22
    iput-object p10, p0, LQA0/b$a;->j:LRA0/e;

    .line 23
    .line 24
    iput-object p11, p0, LQA0/b$a;->k:LeB0/a;

    .line 25
    .line 26
    iput-object p12, p0, LQA0/b$a;->l:LRA0/f;

    .line 27
    .line 28
    iput-object p13, p0, LQA0/b$a;->m:LfB0/a;

    .line 29
    .line 30
    iput-object p14, p0, LQA0/b$a;->n:Lorg/xbet/sportgame/core/domain/models/cards/CardWeatherModel;

    .line 31
    .line 32
    iput-object p15, p0, LQA0/b$a;->o:LRA0/a;

    .line 33
    .line 34
    move/from16 p1, p16

    .line 35
    .line 36
    iput-boolean p1, p0, LQA0/b$a;->p:Z

    .line 37
    .line 38
    return-void
.end method


# virtual methods
.method public final a()LSA0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LQA0/b$a;->a:LSA0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()LRA0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LQA0/b$a;->o:LRA0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()LUA0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LQA0/b$a;->c:LUA0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()LRA0/b;
    .locals 1

    .line 1
    iget-object v0, p0, LQA0/b$a;->i:LRA0/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e()LRA0/c;
    .locals 1

    .line 1
    iget-object v0, p0, LQA0/b$a;->h:LRA0/c;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    .line 1
    const/4 v0, 0x1

    .line 2
    if-ne p0, p1, :cond_0

    .line 3
    .line 4
    return v0

    .line 5
    :cond_0
    instance-of v1, p1, LQA0/b$a;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-nez v1, :cond_1

    .line 9
    .line 10
    return v2

    .line 11
    :cond_1
    check-cast p1, LQA0/b$a;

    .line 12
    .line 13
    iget-object v1, p0, LQA0/b$a;->a:LSA0/a;

    .line 14
    .line 15
    iget-object v3, p1, LQA0/b$a;->a:LSA0/a;

    .line 16
    .line 17
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    if-nez v1, :cond_2

    .line 22
    .line 23
    return v2

    .line 24
    :cond_2
    iget-object v1, p0, LQA0/b$a;->b:LTA0/a;

    .line 25
    .line 26
    iget-object v3, p1, LQA0/b$a;->b:LTA0/a;

    .line 27
    .line 28
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 29
    .line 30
    .line 31
    move-result v1

    .line 32
    if-nez v1, :cond_3

    .line 33
    .line 34
    return v2

    .line 35
    :cond_3
    iget-object v1, p0, LQA0/b$a;->c:LUA0/a;

    .line 36
    .line 37
    iget-object v3, p1, LQA0/b$a;->c:LUA0/a;

    .line 38
    .line 39
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 40
    .line 41
    .line 42
    move-result v1

    .line 43
    if-nez v1, :cond_4

    .line 44
    .line 45
    return v2

    .line 46
    :cond_4
    iget-object v1, p0, LQA0/b$a;->d:LUA0/b;

    .line 47
    .line 48
    iget-object v3, p1, LQA0/b$a;->d:LUA0/b;

    .line 49
    .line 50
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 51
    .line 52
    .line 53
    move-result v1

    .line 54
    if-nez v1, :cond_5

    .line 55
    .line 56
    return v2

    .line 57
    :cond_5
    iget-object v1, p0, LQA0/b$a;->e:LVA0/a;

    .line 58
    .line 59
    iget-object v3, p1, LQA0/b$a;->e:LVA0/a;

    .line 60
    .line 61
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 62
    .line 63
    .line 64
    move-result v1

    .line 65
    if-nez v1, :cond_6

    .line 66
    .line 67
    return v2

    .line 68
    :cond_6
    iget-object v1, p0, LQA0/b$a;->f:LVA0/b;

    .line 69
    .line 70
    iget-object v3, p1, LQA0/b$a;->f:LVA0/b;

    .line 71
    .line 72
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 73
    .line 74
    .line 75
    move-result v1

    .line 76
    if-nez v1, :cond_7

    .line 77
    .line 78
    return v2

    .line 79
    :cond_7
    iget-object v1, p0, LQA0/b$a;->g:LRA0/d;

    .line 80
    .line 81
    iget-object v3, p1, LQA0/b$a;->g:LRA0/d;

    .line 82
    .line 83
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 84
    .line 85
    .line 86
    move-result v1

    .line 87
    if-nez v1, :cond_8

    .line 88
    .line 89
    return v2

    .line 90
    :cond_8
    iget-object v1, p0, LQA0/b$a;->h:LRA0/c;

    .line 91
    .line 92
    iget-object v3, p1, LQA0/b$a;->h:LRA0/c;

    .line 93
    .line 94
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 95
    .line 96
    .line 97
    move-result v1

    .line 98
    if-nez v1, :cond_9

    .line 99
    .line 100
    return v2

    .line 101
    :cond_9
    iget-object v1, p0, LQA0/b$a;->i:LRA0/b;

    .line 102
    .line 103
    iget-object v3, p1, LQA0/b$a;->i:LRA0/b;

    .line 104
    .line 105
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 106
    .line 107
    .line 108
    move-result v1

    .line 109
    if-nez v1, :cond_a

    .line 110
    .line 111
    return v2

    .line 112
    :cond_a
    iget-object v1, p0, LQA0/b$a;->j:LRA0/e;

    .line 113
    .line 114
    iget-object v3, p1, LQA0/b$a;->j:LRA0/e;

    .line 115
    .line 116
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 117
    .line 118
    .line 119
    move-result v1

    .line 120
    if-nez v1, :cond_b

    .line 121
    .line 122
    return v2

    .line 123
    :cond_b
    iget-object v1, p0, LQA0/b$a;->k:LeB0/a;

    .line 124
    .line 125
    iget-object v3, p1, LQA0/b$a;->k:LeB0/a;

    .line 126
    .line 127
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 128
    .line 129
    .line 130
    move-result v1

    .line 131
    if-nez v1, :cond_c

    .line 132
    .line 133
    return v2

    .line 134
    :cond_c
    iget-object v1, p0, LQA0/b$a;->l:LRA0/f;

    .line 135
    .line 136
    iget-object v3, p1, LQA0/b$a;->l:LRA0/f;

    .line 137
    .line 138
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 139
    .line 140
    .line 141
    move-result v1

    .line 142
    if-nez v1, :cond_d

    .line 143
    .line 144
    return v2

    .line 145
    :cond_d
    iget-object v1, p0, LQA0/b$a;->m:LfB0/a;

    .line 146
    .line 147
    iget-object v3, p1, LQA0/b$a;->m:LfB0/a;

    .line 148
    .line 149
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 150
    .line 151
    .line 152
    move-result v1

    .line 153
    if-nez v1, :cond_e

    .line 154
    .line 155
    return v2

    .line 156
    :cond_e
    iget-object v1, p0, LQA0/b$a;->n:Lorg/xbet/sportgame/core/domain/models/cards/CardWeatherModel;

    .line 157
    .line 158
    iget-object v3, p1, LQA0/b$a;->n:Lorg/xbet/sportgame/core/domain/models/cards/CardWeatherModel;

    .line 159
    .line 160
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 161
    .line 162
    .line 163
    move-result v1

    .line 164
    if-nez v1, :cond_f

    .line 165
    .line 166
    return v2

    .line 167
    :cond_f
    iget-object v1, p0, LQA0/b$a;->o:LRA0/a;

    .line 168
    .line 169
    iget-object v3, p1, LQA0/b$a;->o:LRA0/a;

    .line 170
    .line 171
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 172
    .line 173
    .line 174
    move-result v1

    .line 175
    if-nez v1, :cond_10

    .line 176
    .line 177
    return v2

    .line 178
    :cond_10
    iget-boolean v1, p0, LQA0/b$a;->p:Z

    .line 179
    .line 180
    iget-boolean p1, p1, LQA0/b$a;->p:Z

    .line 181
    .line 182
    if-eq v1, p1, :cond_11

    .line 183
    .line 184
    return v2

    .line 185
    :cond_11
    return v0
.end method

.method public final f()LRA0/d;
    .locals 1

    .line 1
    iget-object v0, p0, LQA0/b$a;->g:LRA0/d;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g()LVA0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LQA0/b$a;->e:LVA0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h()LRA0/e;
    .locals 1

    .line 1
    iget-object v0, p0, LQA0/b$a;->j:LRA0/e;

    .line 2
    .line 3
    return-object v0
.end method

.method public hashCode()I
    .locals 3

    .line 1
    iget-object v0, p0, LQA0/b$a;->a:LSA0/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    mul-int/lit8 v0, v0, 0x1f

    .line 8
    .line 9
    iget-object v1, p0, LQA0/b$a;->b:LTA0/a;

    .line 10
    .line 11
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    add-int/2addr v0, v1

    .line 16
    mul-int/lit8 v0, v0, 0x1f

    .line 17
    .line 18
    iget-object v1, p0, LQA0/b$a;->c:LUA0/a;

    .line 19
    .line 20
    const/4 v2, 0x0

    .line 21
    if-nez v1, :cond_0

    .line 22
    .line 23
    const/4 v1, 0x0

    .line 24
    goto :goto_0

    .line 25
    :cond_0
    invoke-virtual {v1}, LUA0/a;->hashCode()I

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    :goto_0
    add-int/2addr v0, v1

    .line 30
    mul-int/lit8 v0, v0, 0x1f

    .line 31
    .line 32
    iget-object v1, p0, LQA0/b$a;->d:LUA0/b;

    .line 33
    .line 34
    if-nez v1, :cond_1

    .line 35
    .line 36
    const/4 v1, 0x0

    .line 37
    goto :goto_1

    .line 38
    :cond_1
    invoke-virtual {v1}, LUA0/b;->hashCode()I

    .line 39
    .line 40
    .line 41
    move-result v1

    .line 42
    :goto_1
    add-int/2addr v0, v1

    .line 43
    mul-int/lit8 v0, v0, 0x1f

    .line 44
    .line 45
    iget-object v1, p0, LQA0/b$a;->e:LVA0/a;

    .line 46
    .line 47
    if-nez v1, :cond_2

    .line 48
    .line 49
    const/4 v1, 0x0

    .line 50
    goto :goto_2

    .line 51
    :cond_2
    invoke-virtual {v1}, LVA0/a;->hashCode()I

    .line 52
    .line 53
    .line 54
    move-result v1

    .line 55
    :goto_2
    add-int/2addr v0, v1

    .line 56
    mul-int/lit8 v0, v0, 0x1f

    .line 57
    .line 58
    iget-object v1, p0, LQA0/b$a;->f:LVA0/b;

    .line 59
    .line 60
    if-nez v1, :cond_3

    .line 61
    .line 62
    const/4 v1, 0x0

    .line 63
    goto :goto_3

    .line 64
    :cond_3
    invoke-virtual {v1}, LVA0/b;->hashCode()I

    .line 65
    .line 66
    .line 67
    move-result v1

    .line 68
    :goto_3
    add-int/2addr v0, v1

    .line 69
    mul-int/lit8 v0, v0, 0x1f

    .line 70
    .line 71
    iget-object v1, p0, LQA0/b$a;->g:LRA0/d;

    .line 72
    .line 73
    if-nez v1, :cond_4

    .line 74
    .line 75
    const/4 v1, 0x0

    .line 76
    goto :goto_4

    .line 77
    :cond_4
    invoke-virtual {v1}, LRA0/d;->hashCode()I

    .line 78
    .line 79
    .line 80
    move-result v1

    .line 81
    :goto_4
    add-int/2addr v0, v1

    .line 82
    mul-int/lit8 v0, v0, 0x1f

    .line 83
    .line 84
    iget-object v1, p0, LQA0/b$a;->h:LRA0/c;

    .line 85
    .line 86
    if-nez v1, :cond_5

    .line 87
    .line 88
    const/4 v1, 0x0

    .line 89
    goto :goto_5

    .line 90
    :cond_5
    invoke-virtual {v1}, LRA0/c;->hashCode()I

    .line 91
    .line 92
    .line 93
    move-result v1

    .line 94
    :goto_5
    add-int/2addr v0, v1

    .line 95
    mul-int/lit8 v0, v0, 0x1f

    .line 96
    .line 97
    iget-object v1, p0, LQA0/b$a;->i:LRA0/b;

    .line 98
    .line 99
    if-nez v1, :cond_6

    .line 100
    .line 101
    const/4 v1, 0x0

    .line 102
    goto :goto_6

    .line 103
    :cond_6
    invoke-virtual {v1}, LRA0/b;->hashCode()I

    .line 104
    .line 105
    .line 106
    move-result v1

    .line 107
    :goto_6
    add-int/2addr v0, v1

    .line 108
    mul-int/lit8 v0, v0, 0x1f

    .line 109
    .line 110
    iget-object v1, p0, LQA0/b$a;->j:LRA0/e;

    .line 111
    .line 112
    if-nez v1, :cond_7

    .line 113
    .line 114
    const/4 v1, 0x0

    .line 115
    goto :goto_7

    .line 116
    :cond_7
    invoke-virtual {v1}, LRA0/e;->hashCode()I

    .line 117
    .line 118
    .line 119
    move-result v1

    .line 120
    :goto_7
    add-int/2addr v0, v1

    .line 121
    mul-int/lit8 v0, v0, 0x1f

    .line 122
    .line 123
    iget-object v1, p0, LQA0/b$a;->k:LeB0/a;

    .line 124
    .line 125
    if-nez v1, :cond_8

    .line 126
    .line 127
    const/4 v1, 0x0

    .line 128
    goto :goto_8

    .line 129
    :cond_8
    invoke-virtual {v1}, LeB0/a;->hashCode()I

    .line 130
    .line 131
    .line 132
    move-result v1

    .line 133
    :goto_8
    add-int/2addr v0, v1

    .line 134
    mul-int/lit8 v0, v0, 0x1f

    .line 135
    .line 136
    iget-object v1, p0, LQA0/b$a;->l:LRA0/f;

    .line 137
    .line 138
    invoke-virtual {v1}, LRA0/f;->hashCode()I

    .line 139
    .line 140
    .line 141
    move-result v1

    .line 142
    add-int/2addr v0, v1

    .line 143
    mul-int/lit8 v0, v0, 0x1f

    .line 144
    .line 145
    iget-object v1, p0, LQA0/b$a;->m:LfB0/a;

    .line 146
    .line 147
    if-nez v1, :cond_9

    .line 148
    .line 149
    const/4 v1, 0x0

    .line 150
    goto :goto_9

    .line 151
    :cond_9
    invoke-virtual {v1}, LfB0/a;->hashCode()I

    .line 152
    .line 153
    .line 154
    move-result v1

    .line 155
    :goto_9
    add-int/2addr v0, v1

    .line 156
    mul-int/lit8 v0, v0, 0x1f

    .line 157
    .line 158
    iget-object v1, p0, LQA0/b$a;->n:Lorg/xbet/sportgame/core/domain/models/cards/CardWeatherModel;

    .line 159
    .line 160
    if-nez v1, :cond_a

    .line 161
    .line 162
    const/4 v1, 0x0

    .line 163
    goto :goto_a

    .line 164
    :cond_a
    invoke-virtual {v1}, Lorg/xbet/sportgame/core/domain/models/cards/CardWeatherModel;->hashCode()I

    .line 165
    .line 166
    .line 167
    move-result v1

    .line 168
    :goto_a
    add-int/2addr v0, v1

    .line 169
    mul-int/lit8 v0, v0, 0x1f

    .line 170
    .line 171
    iget-object v1, p0, LQA0/b$a;->o:LRA0/a;

    .line 172
    .line 173
    if-nez v1, :cond_b

    .line 174
    .line 175
    goto :goto_b

    .line 176
    :cond_b
    invoke-virtual {v1}, LRA0/a;->hashCode()I

    .line 177
    .line 178
    .line 179
    move-result v2

    .line 180
    :goto_b
    add-int/2addr v0, v2

    .line 181
    mul-int/lit8 v0, v0, 0x1f

    .line 182
    .line 183
    iget-boolean v1, p0, LQA0/b$a;->p:Z

    .line 184
    .line 185
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 186
    .line 187
    .line 188
    move-result v1

    .line 189
    add-int/2addr v0, v1

    .line 190
    return v0
.end method

.method public final i()LRA0/f;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LQA0/b$a;->l:LRA0/f;

    .line 2
    .line 3
    return-object v0
.end method

.method public final j()Lorg/xbet/sportgame/core/domain/models/cards/CardWeatherModel;
    .locals 1

    .line 1
    iget-object v0, p0, LQA0/b$a;->n:Lorg/xbet/sportgame/core/domain/models/cards/CardWeatherModel;

    .line 2
    .line 3
    return-object v0
.end method

.method public final k()LTA0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LQA0/b$a;->b:LTA0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final l()LUA0/b;
    .locals 1

    .line 1
    iget-object v0, p0, LQA0/b$a;->d:LUA0/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final m()LVA0/b;
    .locals 1

    .line 1
    iget-object v0, p0, LQA0/b$a;->f:LVA0/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final n()LfB0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LQA0/b$a;->m:LfB0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final o()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, LQA0/b$a;->p:Z

    .line 2
    .line 3
    return v0
.end method

.method public final p()LeB0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LQA0/b$a;->k:LeB0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 18
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, LQA0/b$a;->a:LSA0/a;

    .line 4
    .line 5
    iget-object v2, v0, LQA0/b$a;->b:LTA0/a;

    .line 6
    .line 7
    iget-object v3, v0, LQA0/b$a;->c:LUA0/a;

    .line 8
    .line 9
    iget-object v4, v0, LQA0/b$a;->d:LUA0/b;

    .line 10
    .line 11
    iget-object v5, v0, LQA0/b$a;->e:LVA0/a;

    .line 12
    .line 13
    iget-object v6, v0, LQA0/b$a;->f:LVA0/b;

    .line 14
    .line 15
    iget-object v7, v0, LQA0/b$a;->g:LRA0/d;

    .line 16
    .line 17
    iget-object v8, v0, LQA0/b$a;->h:LRA0/c;

    .line 18
    .line 19
    iget-object v9, v0, LQA0/b$a;->i:LRA0/b;

    .line 20
    .line 21
    iget-object v10, v0, LQA0/b$a;->j:LRA0/e;

    .line 22
    .line 23
    iget-object v11, v0, LQA0/b$a;->k:LeB0/a;

    .line 24
    .line 25
    iget-object v12, v0, LQA0/b$a;->l:LRA0/f;

    .line 26
    .line 27
    iget-object v13, v0, LQA0/b$a;->m:LfB0/a;

    .line 28
    .line 29
    iget-object v14, v0, LQA0/b$a;->n:Lorg/xbet/sportgame/core/domain/models/cards/CardWeatherModel;

    .line 30
    .line 31
    iget-object v15, v0, LQA0/b$a;->o:LRA0/a;

    .line 32
    .line 33
    move-object/from16 v16, v15

    .line 34
    .line 35
    iget-boolean v15, v0, LQA0/b$a;->p:Z

    .line 36
    .line 37
    new-instance v0, Ljava/lang/StringBuilder;

    .line 38
    .line 39
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 40
    .line 41
    .line 42
    move/from16 v17, v15

    .line 43
    .line 44
    const-string v15, "Content(cardCommonModel="

    .line 45
    .line 46
    invoke-virtual {v0, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 47
    .line 48
    .line 49
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 50
    .line 51
    .line 52
    const-string v1, ", compressedCardCommonModel="

    .line 53
    .line 54
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 55
    .line 56
    .line 57
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 58
    .line 59
    .line 60
    const-string v1, ", cardFootballPeriodModel="

    .line 61
    .line 62
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 63
    .line 64
    .line 65
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 66
    .line 67
    .line 68
    const-string v1, ", compressedCardFootballPeriodModel="

    .line 69
    .line 70
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 71
    .line 72
    .line 73
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 74
    .line 75
    .line 76
    const-string v1, ", cardPeriodModel="

    .line 77
    .line 78
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 79
    .line 80
    .line 81
    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 82
    .line 83
    .line 84
    const-string v1, ", compressedCardPeriodModel="

    .line 85
    .line 86
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 87
    .line 88
    .line 89
    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 90
    .line 91
    .line 92
    const-string v1, ", cardPenaltyModel="

    .line 93
    .line 94
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 95
    .line 96
    .line 97
    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 98
    .line 99
    .line 100
    const-string v1, ", cardMatchReviewModel="

    .line 101
    .line 102
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 103
    .line 104
    .line 105
    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 106
    .line 107
    .line 108
    const-string v1, ", cardHostVsGuestsModel="

    .line 109
    .line 110
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 111
    .line 112
    .line 113
    invoke-virtual {v0, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 114
    .line 115
    .line 116
    const-string v1, ", cardShortStatisticModel="

    .line 117
    .line 118
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 119
    .line 120
    .line 121
    invoke-virtual {v0, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 122
    .line 123
    .line 124
    const-string v1, ", stadiumInfoModel="

    .line 125
    .line 126
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 127
    .line 128
    .line 129
    invoke-virtual {v0, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 130
    .line 131
    .line 132
    const-string v1, ", cardTimerSectionModel="

    .line 133
    .line 134
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 135
    .line 136
    .line 137
    invoke-virtual {v0, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 138
    .line 139
    .line 140
    const-string v1, ", lineStatisticModel="

    .line 141
    .line 142
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 143
    .line 144
    .line 145
    invoke-virtual {v0, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 146
    .line 147
    .line 148
    const-string v1, ", cardWeatherModel="

    .line 149
    .line 150
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 151
    .line 152
    .line 153
    invoke-virtual {v0, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 154
    .line 155
    .line 156
    const-string v1, ", cardFootballActivitiesModel="

    .line 157
    .line 158
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 159
    .line 160
    .line 161
    move-object/from16 v1, v16

    .line 162
    .line 163
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 164
    .line 165
    .line 166
    const-string v1, ", show24="

    .line 167
    .line 168
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 169
    .line 170
    .line 171
    move/from16 v1, v17

    .line 172
    .line 173
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 174
    .line 175
    .line 176
    const-string v1, ")"

    .line 177
    .line 178
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 179
    .line 180
    .line 181
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 182
    .line 183
    .line 184
    move-result-object v0

    .line 185
    return-object v0
.end method
