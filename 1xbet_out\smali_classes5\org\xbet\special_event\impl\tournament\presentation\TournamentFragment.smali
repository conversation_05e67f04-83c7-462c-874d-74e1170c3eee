.class public final Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00b6\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u000e\n\u0002\u0008\u0008\n\u0002\u0010\u0008\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u000c\u0008\u0001\u0018\u0000 \u0090\u00012\u00020\u0001:\u0002\u0091\u0001B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0017\u0010\u000b\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u001d\u0010\u0010\u001a\u00020\u00062\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\rH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u000f\u0010\u0012\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0003J\u0017\u0010\u0015\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u000f\u0010\u0017\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0003J\u000f\u0010\u0018\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0003J\u000f\u0010\u0019\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u0003J\u0017\u0010\u001c\u001a\u00020\u00062\u0006\u0010\u001b\u001a\u00020\u001aH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u0017\u0010 \u001a\u00020\u00062\u0006\u0010\u001f\u001a\u00020\u001eH\u0002\u00a2\u0006\u0004\u0008 \u0010!J\u000f\u0010\"\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\"\u0010\u0003J\u000f\u0010#\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008#\u0010\u0003J\u000f\u0010$\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008$\u0010\u0003J\u0019\u0010\'\u001a\u00020\u00062\u0008\u0010&\u001a\u0004\u0018\u00010%H\u0014\u00a2\u0006\u0004\u0008\'\u0010(J\u000f\u0010)\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008)\u0010\u0003J\u000f\u0010*\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008*\u0010\u0003J\u000f\u0010+\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008+\u0010\u0003J\u000f\u0010,\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008,\u0010\u0003R\"\u00104\u001a\u00020-8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008.\u0010/\u001a\u0004\u00080\u00101\"\u0004\u00082\u00103R\"\u0010<\u001a\u0002058\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u00086\u00107\u001a\u0004\u00088\u00109\"\u0004\u0008:\u0010;R\"\u0010D\u001a\u00020=8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008>\u0010?\u001a\u0004\u0008@\u0010A\"\u0004\u0008B\u0010CR\"\u0010L\u001a\u00020E8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008F\u0010G\u001a\u0004\u0008H\u0010I\"\u0004\u0008J\u0010KR\"\u0010T\u001a\u00020M8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008N\u0010O\u001a\u0004\u0008P\u0010Q\"\u0004\u0008R\u0010SR\"\u0010\\\u001a\u00020U8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008V\u0010W\u001a\u0004\u0008X\u0010Y\"\u0004\u0008Z\u0010[R\"\u0010d\u001a\u00020]8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008^\u0010_\u001a\u0004\u0008`\u0010a\"\u0004\u0008b\u0010cR+\u0010m\u001a\u00020e2\u0006\u0010f\u001a\u00020e8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008g\u0010h\u001a\u0004\u0008i\u0010j\"\u0004\u0008k\u0010lR+\u0010u\u001a\u00020n2\u0006\u0010f\u001a\u00020n8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008o\u0010p\u001a\u0004\u0008q\u0010r\"\u0004\u0008s\u0010tR\u001b\u0010{\u001a\u00020v8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008w\u0010x\u001a\u0004\u0008y\u0010zR\u001d\u0010\u0081\u0001\u001a\u00020|8BX\u0082\u0084\u0002\u00a2\u0006\r\n\u0004\u0008}\u0010~\u001a\u0005\u0008\u007f\u0010\u0080\u0001R\u0018\u0010\u0085\u0001\u001a\u00030\u0082\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0083\u0001\u0010\u0084\u0001R \u0010\u008a\u0001\u001a\u00030\u0086\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000f\n\u0005\u0008\u0087\u0001\u0010x\u001a\u0006\u0008\u0088\u0001\u0010\u0089\u0001R\u001f\u0010\u008f\u0001\u001a\u00020\u001a8\u0016X\u0096D\u00a2\u0006\u0010\n\u0006\u0008\u008b\u0001\u0010\u008c\u0001\u001a\u0006\u0008\u008d\u0001\u0010\u008e\u0001\u00a8\u0006\u0092\u0001"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "Lorg/xbet/special_event/impl/tournament/presentation/a;",
        "event",
        "",
        "X2",
        "(Lorg/xbet/special_event/impl/tournament/presentation/a;)V",
        "Lorg/xbet/special_event/impl/tournament/presentation/e;",
        "state",
        "Y2",
        "(Lorg/xbet/special_event/impl/tournament/presentation/e;)V",
        "",
        "LVX0/i;",
        "list",
        "U2",
        "(Ljava/util/List;)V",
        "W2",
        "Lorg/xbet/uikit/components/lottie/a;",
        "lottieConfig",
        "V2",
        "(Lorg/xbet/uikit/components/lottie/a;)V",
        "f3",
        "e3",
        "T2",
        "",
        "isScrollAvailable",
        "G2",
        "(Z)V",
        "Lyp0/a;",
        "socialNetUiModel",
        "b3",
        "(Lyp0/a;)V",
        "s2",
        "x2",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "onStart",
        "onStop",
        "onDestroyView",
        "LRw0/f;",
        "i0",
        "LRw0/f;",
        "R2",
        "()LRw0/f;",
        "setViewModelFactory",
        "(LRw0/f;)V",
        "viewModelFactory",
        "LVo/a;",
        "j0",
        "LVo/a;",
        "I2",
        "()LVo/a;",
        "setGameCardCommonAdapterDelegate",
        "(LVo/a;)V",
        "gameCardCommonAdapterDelegate",
        "LVo/b;",
        "k0",
        "LVo/b;",
        "J2",
        "()LVo/b;",
        "setGameCardFragmentDelegate",
        "(LVo/b;)V",
        "gameCardFragmentDelegate",
        "Lbl0/d;",
        "l0",
        "Lbl0/d;",
        "L2",
        "()Lbl0/d;",
        "setResultGameCardFragmentDelegate",
        "(Lbl0/d;)V",
        "resultGameCardFragmentDelegate",
        "Lbl0/b;",
        "m0",
        "Lbl0/b;",
        "K2",
        "()Lbl0/b;",
        "setResultGameCardAdapterDelegates",
        "(Lbl0/b;)V",
        "resultGameCardAdapterDelegates",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;",
        "n0",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;",
        "S2",
        "()Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;",
        "setWhoWinFragmentDelegate",
        "(Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;)V",
        "whoWinFragmentDelegate",
        "LzX0/k;",
        "o0",
        "LzX0/k;",
        "M2",
        "()LzX0/k;",
        "setSnackbarManager",
        "(LzX0/k;)V",
        "snackbarManager",
        "",
        "<set-?>",
        "b1",
        "LeX0/k;",
        "N2",
        "()Ljava/lang/String;",
        "d3",
        "(Ljava/lang/String;)V",
        "specialEventTitle",
        "",
        "k1",
        "LeX0/d;",
        "H2",
        "()I",
        "c3",
        "(I)V",
        "eventId",
        "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;",
        "v1",
        "Lkotlin/j;",
        "Q2",
        "()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;",
        "viewModel",
        "LGq0/F;",
        "x1",
        "LRc/c;",
        "P2",
        "()LGq0/F;",
        "viewBinding",
        "LUX0/k;",
        "y1",
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "LTw0/a;",
        "F1",
        "O2",
        "()LTw0/a;",
        "tournamentAdapter",
        "H1",
        "Z",
        "r2",
        "()Z",
        "showNavBar",
        "I1",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final I1:Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic P1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final S1:I

.field public static final V1:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final F1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Z

.field public final b1:LeX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public i0:LRw0/f;

.field public j0:LVo/a;

.field public k0:LVo/b;

.field public final k1:LeX0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public l0:Lbl0/d;

.field public m0:Lbl0/b;

.field public n0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;

.field public o0:LzX0/k;

.field public final v1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:LUX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 1
    new-instance v0, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;

    .line 4
    .line 5
    const-string v2, "specialEventTitle"

    .line 6
    .line 7
    const-string v3, "getSpecialEventTitle()Ljava/lang/String;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "eventId"

    .line 20
    .line 21
    const-string v5, "getEventId()I"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "viewBinding"

    .line 33
    .line 34
    const-string v6, "getViewBinding()Lorg/xbet/special_event/impl/databinding/FragmentTournamentBinding;"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    const/4 v5, 0x3

    .line 44
    new-array v5, v5, [Lkotlin/reflect/m;

    .line 45
    .line 46
    aput-object v0, v5, v4

    .line 47
    .line 48
    const/4 v0, 0x1

    .line 49
    aput-object v2, v5, v0

    .line 50
    .line 51
    const/4 v0, 0x2

    .line 52
    aput-object v3, v5, v0

    .line 53
    .line 54
    sput-object v5, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->P1:[Lkotlin/reflect/m;

    .line 55
    .line 56
    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$a;

    .line 57
    .line 58
    const/4 v2, 0x0

    .line 59
    invoke-direct {v0, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 60
    .line 61
    .line 62
    sput-object v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->I1:Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$a;

    .line 63
    .line 64
    const/16 v0, 0x8

    .line 65
    .line 66
    sput v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->S1:I

    .line 67
    .line 68
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    sput-object v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->V1:Ljava/lang/String;

    .line 73
    .line 74
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LUo0/c;->fragment_tournament:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, LeX0/k;

    .line 7
    .line 8
    const-string v1, "SPECIAL_EVENT_TITLE_BUNDLE_KEY"

    .line 9
    .line 10
    const/4 v2, 0x0

    .line 11
    const/4 v3, 0x2

    .line 12
    invoke-direct {v0, v1, v2, v3, v2}, LeX0/k;-><init>(Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 13
    .line 14
    .line 15
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->b1:LeX0/k;

    .line 16
    .line 17
    new-instance v0, LeX0/d;

    .line 18
    .line 19
    const-string v1, "SPECIAL_EVENT_ID_BUNDLE_KEY"

    .line 20
    .line 21
    const/4 v4, 0x0

    .line 22
    invoke-direct {v0, v1, v4, v3, v2}, LeX0/d;-><init>(Ljava/lang/String;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 23
    .line 24
    .line 25
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->k1:LeX0/d;

    .line 26
    .line 27
    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/b;

    .line 28
    .line 29
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/tournament/presentation/b;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;)V

    .line 30
    .line 31
    .line 32
    new-instance v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$special$$inlined$viewModels$default$1;

    .line 33
    .line 34
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 35
    .line 36
    .line 37
    sget-object v3, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 38
    .line 39
    new-instance v4, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$special$$inlined$viewModels$default$2;

    .line 40
    .line 41
    invoke-direct {v4, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 42
    .line 43
    .line 44
    invoke-static {v3, v4}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    const-class v3, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 49
    .line 50
    invoke-static {v3}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    new-instance v4, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$special$$inlined$viewModels$default$3;

    .line 55
    .line 56
    invoke-direct {v4, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 57
    .line 58
    .line 59
    new-instance v5, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$special$$inlined$viewModels$default$4;

    .line 60
    .line 61
    invoke-direct {v5, v2, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 62
    .line 63
    .line 64
    invoke-static {p0, v3, v4, v5, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->v1:Lkotlin/j;

    .line 69
    .line 70
    sget-object v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$viewBinding$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$viewBinding$2;

    .line 71
    .line 72
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->x1:LRc/c;

    .line 77
    .line 78
    new-instance v0, LUX0/k;

    .line 79
    .line 80
    invoke-direct {v0}, LUX0/k;-><init>()V

    .line 81
    .line 82
    .line 83
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->y1:LUX0/k;

    .line 84
    .line 85
    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/c;

    .line 86
    .line 87
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/tournament/presentation/c;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;)V

    .line 88
    .line 89
    .line 90
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    iput-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->F1:Lkotlin/j;

    .line 95
    .line 96
    const/4 v0, 0x1

    .line 97
    iput-boolean v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->H1:Z

    .line 98
    .line 99
    return-void
.end method

.method public static final synthetic A2(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->G2(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic B2()Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->V1:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final synthetic C2(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Lorg/xbet/special_event/impl/tournament/presentation/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Z2(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Lorg/xbet/special_event/impl/tournament/presentation/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic D2(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Lorg/xbet/special_event/impl/tournament/presentation/e;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->a3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Lorg/xbet/special_event/impl/tournament/presentation/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic E2(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;I)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->c3(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic F2(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->d3(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final G2(Z)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->P2()LGq0/F;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LGq0/F;->e:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Landroidx/recyclerview/widget/RecyclerView;->setNestedScrollingEnabled(Z)V

    .line 8
    .line 9
    .line 10
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    const-string v0, "BUNDLE_KEY_HEADER_SCROLL_STATE_CHANGE"

    .line 15
    .line 16
    invoke-static {v0, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    const/4 v0, 0x1

    .line 21
    new-array v0, v0, [Lkotlin/Pair;

    .line 22
    .line 23
    const/4 v1, 0x0

    .line 24
    aput-object p1, v0, v1

    .line 25
    .line 26
    invoke-static {v0}, Landroidx/core/os/d;->b([Lkotlin/Pair;)Landroid/os/Bundle;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    const-string v0, "REQUEST_KEY_HEADER_SCROLL_STATE_CHANGE"

    .line 31
    .line 32
    invoke-static {p0, v0, p1}, Landroidx/fragment/app/x;->d(Landroidx/fragment/app/Fragment;Ljava/lang/String;Landroid/os/Bundle;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method private final H2()I
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->k1:LeX0/d;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->P1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/d;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Integer;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method private final N2()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->b1:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->P1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/k;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    return-object v0
.end method

.method private final U2(Ljava/util/List;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->P2()LGq0/F;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, LGq0/F;->b:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 6
    .line 7
    const/16 v2, 0x8

    .line 8
    .line 9
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    iget-object v1, v0, LGq0/F;->d:LGq0/f0;

    .line 13
    .line 14
    invoke-virtual {v1}, LGq0/f0;->b()Landroid/widget/LinearLayout;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 19
    .line 20
    .line 21
    iget-object v1, v0, LGq0/F;->d:LGq0/f0;

    .line 22
    .line 23
    invoke-virtual {v1}, LGq0/f0;->b()Landroid/widget/LinearLayout;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-static {v1}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->O2()LTw0/a;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    invoke-virtual {v1, p1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 35
    .line 36
    .line 37
    iget-object p1, v0, LGq0/F;->e:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 38
    .line 39
    const/4 v0, 0x0

    .line 40
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 41
    .line 42
    .line 43
    return-void
.end method

.method private final V2(Lorg/xbet/uikit/components/lottie/a;)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->P2()LGq0/F;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, LGq0/F;->d:LGq0/f0;

    .line 6
    .line 7
    invoke-virtual {v1}, LGq0/f0;->b()Landroid/widget/LinearLayout;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    const/16 v2, 0x8

    .line 12
    .line 13
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 14
    .line 15
    .line 16
    iget-object v1, v0, LGq0/F;->d:LGq0/f0;

    .line 17
    .line 18
    invoke-virtual {v1}, LGq0/f0;->b()Landroid/widget/LinearLayout;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    invoke-static {v1}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 23
    .line 24
    .line 25
    iget-object v1, v0, LGq0/F;->b:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 26
    .line 27
    sget v3, Lpb/k;->update_again_after:I

    .line 28
    .line 29
    invoke-virtual {v1, p1, v3}, Lorg/xbet/uikit/components/lottie/LottieView;->P(Lorg/xbet/uikit/components/lottie/a;I)V

    .line 30
    .line 31
    .line 32
    iget-object p1, v0, LGq0/F;->b:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 33
    .line 34
    const/4 v1, 0x0

    .line 35
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 36
    .line 37
    .line 38
    iget-object p1, v0, LGq0/F;->e:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 39
    .line 40
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 41
    .line 42
    .line 43
    return-void
.end method

.method private final W2()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->P2()LGq0/F;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, LGq0/F;->d:LGq0/f0;

    .line 6
    .line 7
    invoke-virtual {v1}, LGq0/f0;->b()Landroid/widget/LinearLayout;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    const/4 v2, 0x0

    .line 12
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 13
    .line 14
    .line 15
    iget-object v1, v0, LGq0/F;->d:LGq0/f0;

    .line 16
    .line 17
    invoke-virtual {v1}, LGq0/f0;->b()Landroid/widget/LinearLayout;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    invoke-static {v1}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 22
    .line 23
    .line 24
    iget-object v1, v0, LGq0/F;->e:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 25
    .line 26
    const/16 v2, 0x8

    .line 27
    .line 28
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 29
    .line 30
    .line 31
    iget-object v0, v0, LGq0/F;->b:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 32
    .line 33
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public static final synthetic Z2(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Lorg/xbet/special_event/impl/tournament/presentation/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->X2(Lorg/xbet/special_event/impl/tournament/presentation/a;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic a3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Lorg/xbet/special_event/impl/tournament/presentation/e;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Y2(Lorg/xbet/special_event/impl/tournament/presentation/e;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final b3(Lyp0/a;)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->M2()LzX0/k;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {p1}, Lyp0/a;->f()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-virtual {p1}, Lyp0/a;->d()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v3

    .line 19
    invoke-virtual {p1}, Lyp0/a;->e()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-static {v0, v1, v2, v3, p1}, Lorg/xbet/ui_common/utils/e0;->b(Landroidx/fragment/app/FragmentActivity;LzX0/k;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    :cond_0
    return-void
.end method

.method private final c3(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->k1:LeX0/d;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->P1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/d;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final d3(Ljava/lang/String;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->b1:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->P1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/k;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final e3()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->J2()LVo/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v2, Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$SpecialEventScreenSection$SportGamesEntryPoint$TournamentScreen;

    .line 10
    .line 11
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->H2()I

    .line 12
    .line 13
    .line 14
    move-result v3

    .line 15
    invoke-direct {v2, v3}, Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$SpecialEventScreenSection$SportGamesEntryPoint$TournamentScreen;-><init>(I)V

    .line 16
    .line 17
    .line 18
    invoke-interface {v0, p0, v1, v2}, LVo/b;->a(Landroidx/fragment/app/Fragment;LVo/d;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->L2()Lbl0/d;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    invoke-interface {v0, p0, v1}, Lbl0/d;->a(Landroidx/fragment/app/Fragment;Lbl0/f;)V

    .line 30
    .line 31
    .line 32
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->S2()Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 37
    .line 38
    .line 39
    move-result-object v1

    .line 40
    new-instance v2, Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$SpecialEventScreenSection$SportGamesEntryPoint$TournamentScreen;

    .line 41
    .line 42
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->H2()I

    .line 43
    .line 44
    .line 45
    move-result v3

    .line 46
    invoke-direct {v2, v3}, Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$SpecialEventScreenSection$SportGamesEntryPoint$TournamentScreen;-><init>(I)V

    .line 47
    .line 48
    .line 49
    invoke-interface {v0, p0, v1, v2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;->a(Landroidx/fragment/app/Fragment;Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V

    .line 50
    .line 51
    .line 52
    return-void
.end method

.method private final f3()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->M2()LzX0/k;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Ly01/g;

    .line 6
    .line 7
    sget-object v2, Ly01/i$c;->a:Ly01/i$c;

    .line 8
    .line 9
    sget v3, Lpb/k;->access_denied_with_bonus_currency_message:I

    .line 10
    .line 11
    invoke-virtual {p0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    const/16 v8, 0x3c

    .line 16
    .line 17
    const/4 v9, 0x0

    .line 18
    const/4 v4, 0x0

    .line 19
    const/4 v5, 0x0

    .line 20
    const/4 v6, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 23
    .line 24
    .line 25
    const/16 v10, 0x1fc

    .line 26
    .line 27
    const/4 v11, 0x0

    .line 28
    const/4 v3, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v8, 0x0

    .line 32
    move-object v2, p0

    .line 33
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public static final g3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;)LTw0/a;
    .locals 26

    .line 1
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->I2()LVo/a;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 14
    .line 15
    .line 16
    move-result-object v4

    .line 17
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 22
    .line 23
    .line 24
    move-result-object v6

    .line 25
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 26
    .line 27
    .line 28
    move-result-object v7

    .line 29
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->K2()Lbl0/b;

    .line 30
    .line 31
    .line 32
    move-result-object v9

    .line 33
    move-object/from16 v0, p0

    .line 34
    .line 35
    iget-object v8, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->y1:LUX0/k;

    .line 36
    .line 37
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 38
    .line 39
    .line 40
    move-result-object v10

    .line 41
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 42
    .line 43
    .line 44
    move-result-object v11

    .line 45
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 46
    .line 47
    .line 48
    move-result-object v12

    .line 49
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 50
    .line 51
    .line 52
    move-result-object v13

    .line 53
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 54
    .line 55
    .line 56
    move-result-object v14

    .line 57
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 58
    .line 59
    .line 60
    move-result-object v15

    .line 61
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 62
    .line 63
    .line 64
    move-result-object v16

    .line 65
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 66
    .line 67
    .line 68
    move-result-object v17

    .line 69
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 70
    .line 71
    .line 72
    move-result-object v18

    .line 73
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 74
    .line 75
    .line 76
    move-result-object v19

    .line 77
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 78
    .line 79
    .line 80
    move-result-object v20

    .line 81
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 82
    .line 83
    .line 84
    move-result-object v21

    .line 85
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 86
    .line 87
    .line 88
    move-result-object v22

    .line 89
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 90
    .line 91
    .line 92
    move-result-object v23

    .line 93
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 94
    .line 95
    .line 96
    move-result-object v24

    .line 97
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 98
    .line 99
    .line 100
    move-result-object v25

    .line 101
    new-instance v0, LTw0/a;

    .line 102
    .line 103
    invoke-direct/range {v0 .. v25}, LTw0/a;-><init>(LVo/a;Lorg/xbet/betting/event_card/presentation/delegates/a;Lcx0/a;LCx0/a;LAx0/a;LVw0/a;Lox0/a;LUX0/k;Lbl0/b;Lbl0/c;Lxx0/a;LMx0/a;LFx0/a;Ltx0/a;LEx0/b;Lgx0/a;Lfx0/a;Llx0/a;LHy0/a;LHy0/b;Lmp0/a;Lmp0/d;Lmp0/c;Lmp0/e;Lmp0/b;)V

    .line 104
    .line 105
    .line 106
    return-object v0
.end method

.method public static final h3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;)Landroidx/lifecycle/e0$c;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/f;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->R2()LRw0/f;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    const/4 v4, 0x4

    .line 8
    const/4 v5, 0x0

    .line 9
    const/4 v3, 0x0

    .line 10
    move-object v2, p0

    .line 11
    invoke-direct/range {v0 .. v5}, Lorg/xbet/ui_common/viewmodel/core/f;-><init>(Lorg/xbet/ui_common/viewmodel/core/e;Landroidx/savedstate/f;Landroid/os/Bundle;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->h3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;)LTw0/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->g3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;)LTw0/a;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final I2()LVo/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->j0:LVo/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final J2()LVo/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->k0:LVo/b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final K2()Lbl0/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->m0:Lbl0/b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final L2()Lbl0/d;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->l0:Lbl0/d;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final M2()LzX0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->o0:LzX0/k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final O2()LTw0/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->F1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LTw0/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public final P2()LGq0/F;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->x1:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->P1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LGq0/F;

    .line 13
    .line 14
    return-object v0
.end method

.method public final Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->v1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final R2()LRw0/f;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->i0:LRw0/f;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final S2()Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->n0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final T2()V
    .locals 5

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->P2()LGq0/F;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LGq0/F;->e:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 6
    .line 7
    new-instance v1, Lorg/xbet/special_event/impl/utils/recyclerview/ScrollDefinesLinearLayoutManager;

    .line 8
    .line 9
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    const/4 v3, 0x1

    .line 14
    const/4 v4, 0x0

    .line 15
    invoke-direct {v1, v2, v3, v4}, Lorg/xbet/special_event/impl/utils/recyclerview/ScrollDefinesLinearLayoutManager;-><init>(Landroid/content/Context;IZ)V

    .line 16
    .line 17
    .line 18
    new-instance v2, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$initTournamentRecycler$1$1$1;

    .line 19
    .line 20
    invoke-direct {v2, p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$initTournamentRecycler$1$1$1;-><init>(Ljava/lang/Object;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v1, v2}, Lorg/xbet/special_event/impl/utils/recyclerview/ScrollDefinesLinearLayoutManager;->k(Lkotlin/jvm/functions/Function1;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    sget v2, LlZ0/g;->medium_horizontal_margin_dynamic:I

    .line 34
    .line 35
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 36
    .line 37
    .line 38
    move-result v1

    .line 39
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    sget v3, LlZ0/g;->space_8:I

    .line 44
    .line 45
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 46
    .line 47
    .line 48
    move-result v2

    .line 49
    new-instance v3, LTw0/b;

    .line 50
    .line 51
    invoke-direct {v3, v1, v2}, LTw0/b;-><init>(II)V

    .line 52
    .line 53
    .line 54
    invoke-virtual {v0, v3}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 55
    .line 56
    .line 57
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getItemAnimator()Landroidx/recyclerview/widget/RecyclerView$m;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    instance-of v2, v1, Landroidx/recyclerview/widget/A;

    .line 62
    .line 63
    if-eqz v2, :cond_0

    .line 64
    .line 65
    check-cast v1, Landroidx/recyclerview/widget/A;

    .line 66
    .line 67
    goto :goto_0

    .line 68
    :cond_0
    const/4 v1, 0x0

    .line 69
    :goto_0
    if-eqz v1, :cond_1

    .line 70
    .line 71
    invoke-virtual {v1, v4}, Landroidx/recyclerview/widget/A;->V(Z)V

    .line 72
    .line 73
    .line 74
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->O2()LTw0/a;

    .line 75
    .line 76
    .line 77
    move-result-object v1

    .line 78
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 79
    .line 80
    .line 81
    return-void
.end method

.method public final X2(Lorg/xbet/special_event/impl/tournament/presentation/a;)V
    .locals 3

    .line 1
    sget-object v0, Lorg/xbet/special_event/impl/tournament/presentation/a$a;->a:Lorg/xbet/special_event/impl/tournament/presentation/a$a;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    return-void

    .line 10
    :cond_0
    instance-of v0, p1, Lorg/xbet/special_event/impl/tournament/presentation/a$b;

    .line 11
    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    check-cast p1, Lorg/xbet/special_event/impl/tournament/presentation/a$b;

    .line 19
    .line 20
    invoke-virtual {p1}, Lorg/xbet/special_event/impl/tournament/presentation/a$b;->f()Ljava/lang/String;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    invoke-static {v0, p1}, Lorg/xbet/ui_common/utils/h;->l(Landroid/content/Context;Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_1
    instance-of v0, p1, Lorg/xbet/special_event/impl/tournament/presentation/a$c;

    .line 29
    .line 30
    if-eqz v0, :cond_2

    .line 31
    .line 32
    check-cast p1, Lorg/xbet/special_event/impl/tournament/presentation/a$c;

    .line 33
    .line 34
    invoke-virtual {p1}, Lorg/xbet/special_event/impl/tournament/presentation/a$c;->f()Landroid/content/Intent;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    invoke-virtual {p0, p1}, Landroidx/fragment/app/Fragment;->startActivity(Landroid/content/Intent;)V

    .line 39
    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_2
    sget-object v0, Lorg/xbet/special_event/impl/tournament/presentation/a$g;->a:Lorg/xbet/special_event/impl/tournament/presentation/a$g;

    .line 43
    .line 44
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 45
    .line 46
    .line 47
    move-result v0

    .line 48
    if-eqz v0, :cond_3

    .line 49
    .line 50
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->f3()V

    .line 51
    .line 52
    .line 53
    goto :goto_0

    .line 54
    :cond_3
    sget-object v0, Lorg/xbet/special_event/impl/tournament/presentation/a$f;->a:Lorg/xbet/special_event/impl/tournament/presentation/a$f;

    .line 55
    .line 56
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 57
    .line 58
    .line 59
    move-result v0

    .line 60
    if-eqz v0, :cond_4

    .line 61
    .line 62
    sget-object p1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 63
    .line 64
    const-string v0, "REQUEST_KEY_RETRY_SPECIAL_EVENT_REQUESTS"

    .line 65
    .line 66
    invoke-static {v0, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    const/4 v1, 0x1

    .line 71
    new-array v1, v1, [Lkotlin/Pair;

    .line 72
    .line 73
    const/4 v2, 0x0

    .line 74
    aput-object p1, v1, v2

    .line 75
    .line 76
    invoke-static {v1}, Landroidx/core/os/d;->b([Lkotlin/Pair;)Landroid/os/Bundle;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    invoke-static {p0, v0, p1}, Landroidx/fragment/app/x;->d(Landroidx/fragment/app/Fragment;Ljava/lang/String;Landroid/os/Bundle;)V

    .line 81
    .line 82
    .line 83
    goto :goto_0

    .line 84
    :cond_4
    sget-object v0, Lorg/xbet/special_event/impl/tournament/presentation/a$d;->a:Lorg/xbet/special_event/impl/tournament/presentation/a$d;

    .line 85
    .line 86
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 87
    .line 88
    .line 89
    move-result v0

    .line 90
    if-eqz v0, :cond_5

    .line 91
    .line 92
    const-string p1, "REQUEST_KEY_OPEN_SCHEDULE_TAB"

    .line 93
    .line 94
    sget-object v0, Landroid/os/Bundle;->EMPTY:Landroid/os/Bundle;

    .line 95
    .line 96
    invoke-static {p0, p1, v0}, Landroidx/fragment/app/x;->d(Landroidx/fragment/app/Fragment;Ljava/lang/String;Landroid/os/Bundle;)V

    .line 97
    .line 98
    .line 99
    goto :goto_0

    .line 100
    :cond_5
    instance-of v0, p1, Lorg/xbet/special_event/impl/tournament/presentation/a$e;

    .line 101
    .line 102
    if-eqz v0, :cond_6

    .line 103
    .line 104
    check-cast p1, Lorg/xbet/special_event/impl/tournament/presentation/a$e;

    .line 105
    .line 106
    invoke-virtual {p1}, Lorg/xbet/special_event/impl/tournament/presentation/a$e;->a()Lyp0/a;

    .line 107
    .line 108
    .line 109
    move-result-object p1

    .line 110
    invoke-direct {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->b3(Lyp0/a;)V

    .line 111
    .line 112
    .line 113
    :goto_0
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 114
    .line 115
    .line 116
    move-result-object p1

    .line 117
    invoke-virtual {p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->l5()V

    .line 118
    .line 119
    .line 120
    return-void

    .line 121
    :cond_6
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 122
    .line 123
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 124
    .line 125
    .line 126
    throw p1
.end method

.method public final Y2(Lorg/xbet/special_event/impl/tournament/presentation/e;)V
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/special_event/impl/tournament/presentation/e$c;->a:Lorg/xbet/special_event/impl/tournament/presentation/e$c;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->W2()V

    .line 10
    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    instance-of v0, p1, Lorg/xbet/special_event/impl/tournament/presentation/e$a;

    .line 14
    .line 15
    if-eqz v0, :cond_1

    .line 16
    .line 17
    check-cast p1, Lorg/xbet/special_event/impl/tournament/presentation/e$a;

    .line 18
    .line 19
    invoke-virtual {p1}, Lorg/xbet/special_event/impl/tournament/presentation/e$a;->a()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-direct {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->U2(Ljava/util/List;)V

    .line 24
    .line 25
    .line 26
    return-void

    .line 27
    :cond_1
    instance-of v0, p1, Lorg/xbet/special_event/impl/tournament/presentation/e$b;

    .line 28
    .line 29
    if-eqz v0, :cond_2

    .line 30
    .line 31
    check-cast p1, Lorg/xbet/special_event/impl/tournament/presentation/e$b;

    .line 32
    .line 33
    invoke-virtual {p1}, Lorg/xbet/special_event/impl/tournament/presentation/e$b;->a()Lorg/xbet/uikit/components/lottie/a;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    invoke-direct {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->V2(Lorg/xbet/uikit/components/lottie/a;)V

    .line 38
    .line 39
    .line 40
    return-void

    .line 41
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 42
    .line 43
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 44
    .line 45
    .line 46
    throw p1
.end method

.method public onDestroyView()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->P2()LGq0/F;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LGq0/F;->e:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 9
    .line 10
    .line 11
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroyView()V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public onStart()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onStart()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->a5()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onStop()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onStop()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->o4()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public r2()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->H1:Z

    .line 2
    .line 3
    return v0
.end method

.method public s2()V
    .locals 0

    .line 1
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->T2()V

    .line 5
    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->e3()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public u2()V
    .locals 13

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    instance-of v1, v0, LQW0/b;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    check-cast v0, LQW0/b;

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    move-object v0, v2

    .line 21
    :goto_0
    const-class v1, LRw0/c;

    .line 22
    .line 23
    if-eqz v0, :cond_3

    .line 24
    .line 25
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, LBc/a;

    .line 34
    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, LQW0/a;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object v0, v2

    .line 45
    :goto_1
    instance-of v3, v0, LRw0/c;

    .line 46
    .line 47
    if-nez v3, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    move-object v2, v0

    .line 51
    :goto_2
    move-object v3, v2

    .line 52
    check-cast v3, LRw0/c;

    .line 53
    .line 54
    if-eqz v3, :cond_3

    .line 55
    .line 56
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 57
    .line 58
    .line 59
    move-result-object v4

    .line 60
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->H2()I

    .line 61
    .line 62
    .line 63
    move-result v5

    .line 64
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->N2()Ljava/lang/String;

    .line 65
    .line 66
    .line 67
    move-result-object v6

    .line 68
    sget-object v7, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->V1:Ljava/lang/String;

    .line 69
    .line 70
    sget-object v0, Lms0/c;->a:Lms0/c;

    .line 71
    .line 72
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->H2()I

    .line 73
    .line 74
    .line 75
    move-result v1

    .line 76
    invoke-static {p0}, LQW0/h;->a(Landroidx/fragment/app/Fragment;)Lorg/xbet/ui_common/router/NavBarScreenTypes;

    .line 77
    .line 78
    .line 79
    move-result-object v2

    .line 80
    invoke-virtual {v2}, Lorg/xbet/ui_common/router/NavBarScreenTypes;->getTag()Ljava/lang/String;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    invoke-virtual {v0, v1, v2}, Lms0/c;->b(ILjava/lang/String;)Ljava/lang/String;

    .line 85
    .line 86
    .line 87
    move-result-object v1

    .line 88
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 89
    .line 90
    .line 91
    move-result-object v2

    .line 92
    invoke-virtual {v2}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 93
    .line 94
    .line 95
    move-result-object v2

    .line 96
    invoke-virtual {v0, v1, v2}, Lms0/c;->d(Ljava/lang/String;Landroid/app/Application;)Lks0/f;

    .line 97
    .line 98
    .line 99
    move-result-object v8

    .line 100
    sget-object v0, Lyy0/c;->a:Lyy0/c;

    .line 101
    .line 102
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->H2()I

    .line 103
    .line 104
    .line 105
    move-result v1

    .line 106
    invoke-static {p0}, LQW0/h;->a(Landroidx/fragment/app/Fragment;)Lorg/xbet/ui_common/router/NavBarScreenTypes;

    .line 107
    .line 108
    .line 109
    move-result-object v2

    .line 110
    invoke-virtual {v2}, Lorg/xbet/ui_common/router/NavBarScreenTypes;->getTag()Ljava/lang/String;

    .line 111
    .line 112
    .line 113
    move-result-object v2

    .line 114
    invoke-virtual {v0, v1, v2}, Lyy0/c;->c(ILjava/lang/String;)Ljava/lang/String;

    .line 115
    .line 116
    .line 117
    move-result-object v1

    .line 118
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 119
    .line 120
    .line 121
    move-result-object v2

    .line 122
    invoke-virtual {v2}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 123
    .line 124
    .line 125
    move-result-object v2

    .line 126
    invoke-virtual {v0, v1, v2}, Lyy0/c;->d(Ljava/lang/String;Landroid/app/Application;)Lyy0/d;

    .line 127
    .line 128
    .line 129
    move-result-object v9

    .line 130
    sget-object v0, LYp0/g;->a:LYp0/g;

    .line 131
    .line 132
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->H2()I

    .line 133
    .line 134
    .line 135
    move-result v1

    .line 136
    invoke-static {p0}, LQW0/h;->a(Landroidx/fragment/app/Fragment;)Lorg/xbet/ui_common/router/NavBarScreenTypes;

    .line 137
    .line 138
    .line 139
    move-result-object v2

    .line 140
    invoke-virtual {v2}, Lorg/xbet/ui_common/router/NavBarScreenTypes;->getTag()Ljava/lang/String;

    .line 141
    .line 142
    .line 143
    move-result-object v2

    .line 144
    invoke-virtual {v0, v1, v2}, LYp0/g;->c(ILjava/lang/String;)LYp0/c;

    .line 145
    .line 146
    .line 147
    move-result-object v10

    .line 148
    sget-object v0, Loq0/g;->a:Loq0/g;

    .line 149
    .line 150
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->H2()I

    .line 151
    .line 152
    .line 153
    move-result v1

    .line 154
    invoke-static {p0}, LQW0/h;->a(Landroidx/fragment/app/Fragment;)Lorg/xbet/ui_common/router/NavBarScreenTypes;

    .line 155
    .line 156
    .line 157
    move-result-object v2

    .line 158
    invoke-virtual {v2}, Lorg/xbet/ui_common/router/NavBarScreenTypes;->getTag()Ljava/lang/String;

    .line 159
    .line 160
    .line 161
    move-result-object v2

    .line 162
    invoke-virtual {v0, v1, v2}, Loq0/g;->c(ILjava/lang/String;)Loq0/c;

    .line 163
    .line 164
    .line 165
    move-result-object v11

    .line 166
    sget-object v0, LMp0/e;->a:LMp0/e;

    .line 167
    .line 168
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->H2()I

    .line 169
    .line 170
    .line 171
    move-result v1

    .line 172
    invoke-static {p0}, LQW0/h;->a(Landroidx/fragment/app/Fragment;)Lorg/xbet/ui_common/router/NavBarScreenTypes;

    .line 173
    .line 174
    .line 175
    move-result-object v2

    .line 176
    invoke-virtual {v2}, Lorg/xbet/ui_common/router/NavBarScreenTypes;->getTag()Ljava/lang/String;

    .line 177
    .line 178
    .line 179
    move-result-object v2

    .line 180
    invoke-virtual {v0, v1, v2}, LMp0/e;->c(ILjava/lang/String;)LMp0/a;

    .line 181
    .line 182
    .line 183
    move-result-object v12

    .line 184
    invoke-virtual/range {v3 .. v12}, LRw0/c;->a(LwX0/c;ILjava/lang/String;Ljava/lang/String;Lks0/f;Lyy0/d;LYp0/c;Loq0/c;LMp0/a;)LRw0/e;

    .line 185
    .line 186
    .line 187
    move-result-object v0

    .line 188
    invoke-interface {v0, p0}, LRw0/e;->a(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;)V

    .line 189
    .line 190
    .line 191
    return-void

    .line 192
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 193
    .line 194
    new-instance v2, Ljava/lang/StringBuilder;

    .line 195
    .line 196
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 197
    .line 198
    .line 199
    const-string v3, "Cannot create dependency "

    .line 200
    .line 201
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 202
    .line 203
    .line 204
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 205
    .line 206
    .line 207
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 208
    .line 209
    .line 210
    move-result-object v1

    .line 211
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 212
    .line 213
    .line 214
    move-result-object v1

    .line 215
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 216
    .line 217
    .line 218
    throw v0
.end method

.method public v2()V
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-super {v0}, LXW0/a;->v2()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->t4()Lkotlinx/coroutines/flow/e;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    new-instance v6, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$onObserveData$1;

    .line 15
    .line 16
    invoke-direct {v6, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$onObserveData$1;-><init>(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    sget-object v5, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 20
    .line 21
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 22
    .line 23
    .line 24
    move-result-object v4

    .line 25
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    new-instance v2, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 30
    .line 31
    const/4 v7, 0x0

    .line 32
    invoke-direct/range {v2 .. v7}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 33
    .line 34
    .line 35
    const/4 v11, 0x3

    .line 36
    const/4 v12, 0x0

    .line 37
    const/4 v8, 0x0

    .line 38
    const/4 v9, 0x0

    .line 39
    move-object v7, v1

    .line 40
    move-object v10, v2

    .line 41
    invoke-static/range {v7 .. v12}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 42
    .line 43
    .line 44
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->Q2()Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    invoke-virtual {v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->u4()Lkotlinx/coroutines/flow/e;

    .line 49
    .line 50
    .line 51
    move-result-object v8

    .line 52
    new-instance v11, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$onObserveData$2;

    .line 53
    .line 54
    invoke-direct {v11, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$onObserveData$2;-><init>(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 58
    .line 59
    .line 60
    move-result-object v9

    .line 61
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    new-instance v15, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 66
    .line 67
    move-object v10, v5

    .line 68
    move-object v7, v15

    .line 69
    invoke-direct/range {v7 .. v12}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 70
    .line 71
    .line 72
    const/16 v16, 0x3

    .line 73
    .line 74
    const/16 v17, 0x0

    .line 75
    .line 76
    const/4 v13, 0x0

    .line 77
    const/4 v14, 0x0

    .line 78
    move-object v12, v1

    .line 79
    invoke-static/range {v12 .. v17}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 80
    .line 81
    .line 82
    return-void
.end method

.method public x2()V
    .locals 0

    .line 1
    return-void
.end method
