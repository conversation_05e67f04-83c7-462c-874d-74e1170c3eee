.class public final synthetic Lk2/m;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lt1/l;


# instance fields
.field public final synthetic a:Lk2/n;


# direct methods
.method public synthetic constructor <init>(Lk2/n;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lk2/m;->a:Lk2/n;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lk2/m;->a:Lk2/n;

    check-cast p1, Lk2/e;

    invoke-static {v0, p1}, Lk2/n;->c(Lk2/n;Lk2/e;)V

    return-void
.end method
