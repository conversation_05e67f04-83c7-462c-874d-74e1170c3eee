.class public final Lorg/xbet/crystal/data/datasources/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0006\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\r\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0015\u0010\t\u001a\u00020\u00082\u0006\u0010\u0007\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\t\u0010\nJ\r\u0010\u000b\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u0003R\u0016\u0010\r\u001a\u00020\u00048\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\u000e"
    }
    d2 = {
        "Lorg/xbet/crystal/data/datasources/a;",
        "",
        "<init>",
        "()V",
        "LZx/b;",
        "b",
        "()LZx/b;",
        "gameState",
        "",
        "c",
        "(LZx/b;)V",
        "a",
        "LZx/b;",
        "gameModel",
        "crystal_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public a:LZx/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 14

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, LZx/b;

    .line 5
    .line 6
    const/16 v12, 0x7f

    .line 7
    .line 8
    const/4 v13, 0x0

    .line 9
    const-wide/16 v1, 0x0

    .line 10
    .line 11
    const-wide/16 v3, 0x0

    .line 12
    .line 13
    const/4 v5, 0x0

    .line 14
    const-wide/16 v6, 0x0

    .line 15
    .line 16
    const/4 v8, 0x0

    .line 17
    const-wide/16 v9, 0x0

    .line 18
    .line 19
    const/4 v11, 0x0

    .line 20
    invoke-direct/range {v0 .. v13}, LZx/b;-><init>(JDLorg/xbet/games_section/api/models/GameBonus;DLZx/d;DLorg/xbet/core/domain/StatusBetEnum;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 21
    .line 22
    .line 23
    iput-object v0, p0, Lorg/xbet/crystal/data/datasources/a;->a:LZx/b;

    .line 24
    .line 25
    return-void
.end method


# virtual methods
.method public final a()V
    .locals 14

    .line 1
    new-instance v0, LZx/b;

    .line 2
    .line 3
    const/16 v12, 0x7f

    .line 4
    .line 5
    const/4 v13, 0x0

    .line 6
    const-wide/16 v1, 0x0

    .line 7
    .line 8
    const-wide/16 v3, 0x0

    .line 9
    .line 10
    const/4 v5, 0x0

    .line 11
    const-wide/16 v6, 0x0

    .line 12
    .line 13
    const/4 v8, 0x0

    .line 14
    const-wide/16 v9, 0x0

    .line 15
    .line 16
    const/4 v11, 0x0

    .line 17
    invoke-direct/range {v0 .. v13}, LZx/b;-><init>(JDLorg/xbet/games_section/api/models/GameBonus;DLZx/d;DLorg/xbet/core/domain/StatusBetEnum;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 18
    .line 19
    .line 20
    iput-object v0, p0, Lorg/xbet/crystal/data/datasources/a;->a:LZx/b;

    .line 21
    .line 22
    return-void
.end method

.method public final b()LZx/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/data/datasources/a;->a:LZx/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c(LZx/b;)V
    .locals 0
    .param p1    # LZx/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/crystal/data/datasources/a;->a:LZx/b;

    .line 2
    .line 3
    return-void
.end method
