.class public final synthetic LFa1/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:Lkotlin/jvm/functions/Function2;

.field public final synthetic c:Landroidx/lifecycle/LifecycleCoroutineScope;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;Landroidx/lifecycle/LifecycleCoroutineScope;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LFa1/c;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, LFa1/c;->b:Lkotlin/jvm/functions/Function2;

    iput-object p3, p0, LFa1/c;->c:Landroidx/lifecycle/LifecycleCoroutineScope;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, LFa1/c;->a:Lkotlin/jvm/functions/Function1;

    iget-object v1, p0, LFa1/c;->b:Lkotlin/jvm/functions/Function2;

    iget-object v2, p0, LFa1/c;->c:Landroidx/lifecycle/LifecycleCoroutineScope;

    check-cast p1, LB4/a;

    invoke-static {v0, v1, v2, p1}, Lorg/xplatform/aggregator/impl/providers/presentation/adapter/CategoryWithProvidersViewHolderKt;->a(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;Landroidx/lifecycle/LifecycleCoroutineScope;LB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
