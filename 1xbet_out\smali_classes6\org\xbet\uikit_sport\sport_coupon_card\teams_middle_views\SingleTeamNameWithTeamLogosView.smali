.class public final Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\n\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0011\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u001c\u0008\u0007\u0018\u00002\u00020\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u001f\u0010\u0011\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J7\u0010\u0019\u001a\u00020\u000c2\u0006\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u00062\u0006\u0010\u0016\u001a\u00020\u00062\u0006\u0010\u0017\u001a\u00020\u00062\u0006\u0010\u0018\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u0015\u0010\u001c\u001a\u00020\u000c2\u0006\u0010\u001b\u001a\u00020\u0013\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u001d\u0010 \u001a\u00020\u000c2\u0006\u0010\u001f\u001a\u00020\u001e2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008 \u0010!J\u000f\u0010#\u001a\u00020\"H\u0002\u00a2\u0006\u0004\u0008#\u0010$J\u0017\u0010&\u001a\u00020\u000c2\u0006\u0010%\u001a\u00020\u001eH\u0002\u00a2\u0006\u0004\u0008&\u0010\'J\u0017\u0010(\u001a\u00020\u000c2\u0006\u0010%\u001a\u00020\u001eH\u0002\u00a2\u0006\u0004\u0008(\u0010\'J\u001b\u0010)\u001a\u00020\u000c*\u00020\"2\u0006\u0010%\u001a\u00020\u001eH\u0002\u00a2\u0006\u0004\u0008)\u0010*J\u000f\u0010+\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008+\u0010,J\u000f\u0010-\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008-\u0010,J\u001f\u00100\u001a\u00020\u000c2\u0006\u0010.\u001a\u00020\u00062\u0006\u0010/\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u00080\u0010\u0012J\u001f\u00102\u001a\u00020\u000c2\u0006\u00101\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u00082\u0010\u0012J\u000f\u00103\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u00083\u0010,R\u0014\u00106\u001a\u0002048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00105R\u0018\u00108\u001a\u0004\u0018\u00010\"8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008-\u00107R\u0018\u00109\u001a\u0004\u0018\u00010\"8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008+\u00107R\u0014\u0010;\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010:R\u0014\u0010<\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010:R\u0014\u0010=\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010:R\u0014\u0010@\u001a\u00020>8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008)\u0010?R\u0014\u0010A\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u0010:R\u0014\u0010B\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u0010:R\u0014\u0010D\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008C\u0010:R\u0014\u0010F\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008E\u0010:R\u0014\u0010H\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010:R\u0016\u0010K\u001a\u00020\u00138\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008I\u0010JR\u0016\u0010M\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008L\u0010:R\u0016\u0010O\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008N\u0010:R\u0016\u0010Q\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008P\u0010:R\u0016\u0010S\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008R\u0010:R\u0016\u0010U\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008T\u0010:R\u0016\u0010W\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008V\u0010:R\u0016\u0010Y\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008X\u0010:\u00a8\u0006Z"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "LX31/e;",
        "teamLogosUiModel",
        "",
        "setTeamLogos",
        "(LX31/e;)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "reversedOrder",
        "setReversedOrder",
        "(Z)V",
        "",
        "teamName",
        "setTeam",
        "(Ljava/lang/String;LX31/e;)V",
        "Lorg/xbet/uikit/components/teamlogo/TeamLogo;",
        "e",
        "()Lorg/xbet/uikit/components/teamlogo/TeamLogo;",
        "teamLogoUrl",
        "f",
        "(Ljava/lang/String;)V",
        "d",
        "g",
        "(Lorg/xbet/uikit/components/teamlogo/TeamLogo;Ljava/lang/String;)V",
        "c",
        "()V",
        "b",
        "viewWidth",
        "parentHeight",
        "a",
        "width",
        "i",
        "h",
        "Landroid/view/ContextThemeWrapper;",
        "Landroid/view/ContextThemeWrapper;",
        "teamLogoContextThemeWrapper",
        "Lorg/xbet/uikit/components/teamlogo/TeamLogo;",
        "topTeamLogo",
        "bottomTeamLogo",
        "I",
        "minTextSize",
        "maxTextSize",
        "autoSizeStep",
        "Landroid/widget/TextView;",
        "Landroid/widget/TextView;",
        "teamNameTextView",
        "pairTeamLogoSize",
        "singleTeamLogoSize",
        "j",
        "minHeight",
        "k",
        "teamLogosAndNameBetweenMargin",
        "l",
        "teamLogosPairBetweenMargin",
        "m",
        "Z",
        "isReversedOrder",
        "n",
        "teamLogoSize",
        "o",
        "teamLogosOccupiedWidth",
        "p",
        "teamNameTopPosition",
        "q",
        "topTeamLogoTopPosition",
        "r",
        "bottomTeamLogoTopPosition",
        "s",
        "teamLogosLeftPosition",
        "t",
        "teamNamePositionX",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Landroid/view/ContextThemeWrapper;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

.field public c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:Landroid/widget/TextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:I

.field public m:Z

.field public n:I

.field public o:I

.field public p:I

.field public q:I

.field public r:I

.field public s:I

.field public t:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance p2, Landroid/view/ContextThemeWrapper;

    .line 6
    sget p3, LlZ0/n;->Widget_TeamLogo:I

    .line 7
    invoke-direct {p2, p1, p3}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->a:Landroid/view/ContextThemeWrapper;

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->text_12:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->d:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->text_14:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->e:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->text_1:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->f:I

    .line 11
    new-instance p2, Landroid/widget/TextView;

    invoke-direct {p2, p1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 12
    sget p1, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    invoke-static {p2, p1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    const/4 p1, 0x3

    .line 13
    invoke-virtual {p2, p1}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 14
    sget-object p1, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {p2, p1}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    const/16 p1, 0x10

    .line 15
    invoke-virtual {p2, p1}, Landroid/widget/TextView;->setGravity(I)V

    .line 16
    invoke-virtual {p0, p2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 17
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->g:Landroid/widget/TextView;

    .line 18
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->size_24:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->h:I

    .line 19
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->size_32:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->i:I

    .line 20
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->size_54:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->j:I

    .line 21
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_8:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->k:I

    .line 22
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_2:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->l:I

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method private final setTeamLogos(LX31/e;)V
    .locals 1

    .line 1
    sget-object v0, LX31/e$a;->a:LX31/e$a;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->c()V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->b()V

    .line 13
    .line 14
    .line 15
    return-void

    .line 16
    :cond_0
    instance-of v0, p1, LX31/e$b;

    .line 17
    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    check-cast p1, LX31/e$b;

    .line 21
    .line 22
    invoke-virtual {p1}, LX31/e$b;->a()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->f(Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p1}, LX31/e$b;->b()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->d(Ljava/lang/String;)V

    .line 34
    .line 35
    .line 36
    return-void

    .line 37
    :cond_1
    instance-of v0, p1, LX31/e$c;

    .line 38
    .line 39
    if-eqz v0, :cond_2

    .line 40
    .line 41
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->b()V

    .line 42
    .line 43
    .line 44
    check-cast p1, LX31/e$c;

    .line 45
    .line 46
    invoke-virtual {p1}, LX31/e$c;->a()Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->f(Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    return-void

    .line 54
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 55
    .line 56
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 57
    .line 58
    .line 59
    throw p1
.end method


# virtual methods
.method public final a(II)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    div-int/lit8 p2, p2, 0x2

    .line 6
    .line 7
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->l:I

    .line 8
    .line 9
    div-int/lit8 v0, v0, 0x2

    .line 10
    .line 11
    sub-int/2addr p2, v0

    .line 12
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->n:I

    .line 13
    .line 14
    :goto_0
    sub-int/2addr p2, v0

    .line 15
    goto :goto_1

    .line 16
    :cond_0
    div-int/lit8 p2, p2, 0x2

    .line 17
    .line 18
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->n:I

    .line 19
    .line 20
    div-int/lit8 v0, v0, 0x2

    .line 21
    .line 22
    goto :goto_0

    .line 23
    :goto_1
    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->q:I

    .line 24
    .line 25
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->n:I

    .line 26
    .line 27
    add-int/2addr p2, v0

    .line 28
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->l:I

    .line 29
    .line 30
    add-int/2addr p2, v1

    .line 31
    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->r:I

    .line 32
    .line 33
    sub-int/2addr p1, v0

    .line 34
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    iget-boolean p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->m:Z

    .line 39
    .line 40
    if-nez p2, :cond_1

    .line 41
    .line 42
    goto :goto_2

    .line 43
    :cond_1
    const/4 p1, 0x0

    .line 44
    :goto_2
    if-eqz p1, :cond_2

    .line 45
    .line 46
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 47
    .line 48
    .line 49
    move-result p1

    .line 50
    goto :goto_3

    .line 51
    :cond_2
    const/4 p1, 0x0

    .line 52
    :goto_3
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->s:I

    .line 53
    .line 54
    return-void
.end method

.method public final b()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    const/4 v0, 0x0

    .line 15
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 16
    .line 17
    :cond_1
    return-void
.end method

.method public final c()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    const/4 v0, 0x0

    .line 15
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 16
    .line 17
    :cond_1
    return-void
.end method

.method public final d(Ljava/lang/String;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->e()Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 10
    .line 11
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 12
    .line 13
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {p0, v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->g(Lorg/xbet/uikit/components/teamlogo/TeamLogo;Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    :cond_1
    return-void
.end method

.method public final e()Lorg/xbet/uikit/components/teamlogo/TeamLogo;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->a:Landroid/view/ContextThemeWrapper;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    const/4 v3, 0x0

    .line 7
    invoke-direct {v0, v1, v3, v2, v3}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 8
    .line 9
    .line 10
    const/4 v1, 0x1

    .line 11
    invoke-virtual {v0, v1, v3}, Landroid/view/View;->setLayerType(ILandroid/graphics/Paint;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 15
    .line 16
    .line 17
    return-object v0
.end method

.method public final f(Ljava/lang/String;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->e()Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 10
    .line 11
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 12
    .line 13
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {p0, v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->g(Lorg/xbet/uikit/components/teamlogo/TeamLogo;Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    :cond_1
    return-void
.end method

.method public final g(Lorg/xbet/uikit/components/teamlogo/TeamLogo;Ljava/lang/String;)V
    .locals 8

    .line 1
    invoke-interface {p2}, Ljava/lang/CharSequence;->length()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-lez v0, :cond_0

    .line 6
    .line 7
    const/16 v6, 0xe

    .line 8
    .line 9
    const/4 v7, 0x0

    .line 10
    const/4 v3, 0x0

    .line 11
    const/4 v4, 0x0

    .line 12
    const/4 v5, 0x0

    .line 13
    move-object v1, p1

    .line 14
    move-object v2, p2

    .line 15
    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :cond_0
    move-object v1, p1

    .line 20
    invoke-virtual {v1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    sget p2, LlZ0/h;->ic_glyph_placeholder_player_secondary60:I

    .line 25
    .line 26
    invoke-static {p1, p2}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    invoke-virtual {v1, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final h()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-nez v0, :cond_1

    .line 5
    .line 6
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 7
    .line 8
    if-nez v2, :cond_1

    .line 9
    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    goto :goto_0

    .line 12
    :cond_1
    if-eqz v0, :cond_2

    .line 13
    .line 14
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 15
    .line 16
    if-nez v2, :cond_2

    .line 17
    .line 18
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->i:I

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_2
    if-eqz v0, :cond_0

    .line 22
    .line 23
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 24
    .line 25
    if-eqz v0, :cond_0

    .line 26
    .line 27
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->h:I

    .line 28
    .line 29
    :goto_0
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->n:I

    .line 30
    .line 31
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->k:I

    .line 32
    .line 33
    add-int/2addr v0, v2

    .line 34
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 39
    .line 40
    if-eqz v2, :cond_3

    .line 41
    .line 42
    goto :goto_1

    .line 43
    :cond_3
    const/4 v0, 0x0

    .line 44
    :goto_1
    if-eqz v0, :cond_4

    .line 45
    .line 46
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 47
    .line 48
    .line 49
    move-result v1

    .line 50
    :cond_4
    iput v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->o:I

    .line 51
    .line 52
    return-void
.end method

.method public final i(II)V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->g:Landroid/widget/TextView;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->d:I

    .line 4
    .line 5
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->e:I

    .line 6
    .line 7
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->f:I

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-static {v0, v1, v2, v3, v4}, LX0/o;->h(Landroid/widget/TextView;IIII)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->g:Landroid/widget/TextView;

    .line 14
    .line 15
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->m:Z

    .line 16
    .line 17
    if-eqz v1, :cond_0

    .line 18
    .line 19
    const/4 v1, 0x3

    .line 20
    goto :goto_0

    .line 21
    :cond_0
    const/4 v1, 0x4

    .line 22
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setTextDirection(I)V

    .line 23
    .line 24
    .line 25
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->g:Landroid/widget/TextView;

    .line 26
    .line 27
    const/high16 v1, 0x40000000    # 2.0f

    .line 28
    .line 29
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    invoke-virtual {v0, p1, p2}, Landroid/view/View;->measure(II)V

    .line 34
    .line 35
    .line 36
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->j:I

    .line 37
    .line 38
    div-int/lit8 p1, p1, 0x2

    .line 39
    .line 40
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->g:Landroid/widget/TextView;

    .line 41
    .line 42
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 43
    .line 44
    .line 45
    move-result p2

    .line 46
    div-int/lit8 p2, p2, 0x2

    .line 47
    .line 48
    sub-int/2addr p1, p2

    .line 49
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->j:I

    .line 54
    .line 55
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->g:Landroid/widget/TextView;

    .line 56
    .line 57
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 58
    .line 59
    .line 60
    move-result v0

    .line 61
    const/4 v1, 0x0

    .line 62
    if-le p2, v0, :cond_1

    .line 63
    .line 64
    goto :goto_1

    .line 65
    :cond_1
    move-object p1, v1

    .line 66
    :goto_1
    if-eqz p1, :cond_2

    .line 67
    .line 68
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 69
    .line 70
    .line 71
    move-result p1

    .line 72
    goto :goto_2

    .line 73
    :cond_2
    const/4 p1, 0x0

    .line 74
    :goto_2
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->p:I

    .line 75
    .line 76
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    iget-boolean p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->m:Z

    .line 81
    .line 82
    if-nez p2, :cond_3

    .line 83
    .line 84
    move-object v1, p1

    .line 85
    :cond_3
    if-eqz v1, :cond_4

    .line 86
    .line 87
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 88
    .line 89
    .line 90
    move-result p1

    .line 91
    goto :goto_3

    .line 92
    :cond_4
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->o:I

    .line 93
    .line 94
    :goto_3
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->t:I

    .line 95
    .line 96
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 1

    .line 1
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->g:Landroid/widget/TextView;

    .line 2
    .line 3
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->t:I

    .line 4
    .line 5
    iget p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->p:I

    .line 6
    .line 7
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 8
    .line 9
    .line 10
    move-result p4

    .line 11
    add-int/2addr p4, p2

    .line 12
    iget p5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->p:I

    .line 13
    .line 14
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->g:Landroid/widget/TextView;

    .line 15
    .line 16
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    add-int/2addr p5, v0

    .line 21
    invoke-virtual {p1, p2, p3, p4, p5}, Landroid/view/View;->layout(IIII)V

    .line 22
    .line 23
    .line 24
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 25
    .line 26
    if-eqz p1, :cond_0

    .line 27
    .line 28
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->s:I

    .line 29
    .line 30
    iget p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->q:I

    .line 31
    .line 32
    iget p4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->n:I

    .line 33
    .line 34
    add-int p5, p2, p4

    .line 35
    .line 36
    add-int/2addr p4, p3

    .line 37
    invoke-virtual {p1, p2, p3, p5, p4}, Landroid/view/View;->layout(IIII)V

    .line 38
    .line 39
    .line 40
    :cond_0
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 41
    .line 42
    if-eqz p1, :cond_1

    .line 43
    .line 44
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->s:I

    .line 45
    .line 46
    iget p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->r:I

    .line 47
    .line 48
    iget p4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->n:I

    .line 49
    .line 50
    add-int p5, p2, p4

    .line 51
    .line 52
    add-int/2addr p4, p3

    .line 53
    invoke-virtual {p1, p2, p3, p5, p4}, Landroid/view/View;->layout(IIII)V

    .line 54
    .line 55
    .line 56
    :cond_1
    return-void
.end method

.method public onMeasure(II)V
    .locals 3

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->h()V

    .line 6
    .line 7
    .line 8
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 9
    .line 10
    .line 11
    move-result v1

    .line 12
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->o:I

    .line 13
    .line 14
    sub-int/2addr v1, v2

    .line 15
    invoke-virtual {p0, v1, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->i(II)V

    .line 16
    .line 17
    .line 18
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->j:I

    .line 19
    .line 20
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->g:Landroid/widget/TextView;

    .line 21
    .line 22
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 23
    .line 24
    .line 25
    move-result v1

    .line 26
    invoke-static {p2, v1}, Ljava/lang/Math;->max(II)I

    .line 27
    .line 28
    .line 29
    move-result p2

    .line 30
    invoke-virtual {p0, v0, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->a(II)V

    .line 31
    .line 32
    .line 33
    const/high16 v0, 0x40000000    # 2.0f

    .line 34
    .line 35
    invoke-static {p2, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 36
    .line 37
    .line 38
    move-result p2

    .line 39
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 40
    .line 41
    .line 42
    return-void
.end method

.method public final setReversedOrder(Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->m:Z

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setTeam(Ljava/lang/String;LX31/e;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LX31/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->g:Landroid/widget/TextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    invoke-direct {p0, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->setTeamLogos(LX31/e;)V

    .line 7
    .line 8
    .line 9
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 10
    .line 11
    .line 12
    return-void
.end method
