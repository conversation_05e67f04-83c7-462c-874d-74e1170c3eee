.class public final Lg2/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LN1/r;


# static fields
.field public static final v:LN1/x;

.field public static final w:Lb2/h$a;


# instance fields
.field public final a:I

.field public final b:J

.field public final c:Lt1/G;

.field public final d:LN1/I$a;

.field public final e:LN1/E;

.field public final f:LN1/G;

.field public final g:LN1/T;

.field public h:LN1/t;

.field public i:LN1/T;

.field public j:LN1/T;

.field public k:I

.field public l:Landroidx/media3/common/x;

.field public m:J

.field public n:J

.field public o:J

.field public p:J

.field public q:I

.field public r:Lg2/g;

.field public s:Z

.field public t:Z

.field public u:J


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lg2/d;

    .line 2
    .line 3
    invoke-direct {v0}, Lg2/d;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Lg2/f;->v:LN1/x;

    .line 7
    .line 8
    new-instance v0, Lg2/e;

    .line 9
    .line 10
    invoke-direct {v0}, Lg2/e;-><init>()V

    .line 11
    .line 12
    .line 13
    sput-object v0, Lg2/f;->w:Lb2/h$a;

    .line 14
    .line 15
    return-void
.end method

.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    .line 1
    invoke-direct {p0, v0}, Lg2/f;-><init>(I)V

    return-void
.end method

.method public constructor <init>(I)V
    .locals 2

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    .line 2
    invoke-direct {p0, p1, v0, v1}, Lg2/f;-><init>(IJ)V

    return-void
.end method

.method public constructor <init>(IJ)V
    .locals 1

    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    and-int/lit8 v0, p1, 0x2

    if-eqz v0, :cond_0

    or-int/lit8 p1, p1, 0x1

    .line 4
    :cond_0
    iput p1, p0, Lg2/f;->a:I

    .line 5
    iput-wide p2, p0, Lg2/f;->b:J

    .line 6
    new-instance p1, Lt1/G;

    const/16 p2, 0xa

    invoke-direct {p1, p2}, Lt1/G;-><init>(I)V

    iput-object p1, p0, Lg2/f;->c:Lt1/G;

    .line 7
    new-instance p1, LN1/I$a;

    invoke-direct {p1}, LN1/I$a;-><init>()V

    iput-object p1, p0, Lg2/f;->d:LN1/I$a;

    .line 8
    new-instance p1, LN1/E;

    invoke-direct {p1}, LN1/E;-><init>()V

    iput-object p1, p0, Lg2/f;->e:LN1/E;

    const-wide p1, -0x7fffffffffffffffL    # -4.9E-324

    .line 9
    iput-wide p1, p0, Lg2/f;->m:J

    .line 10
    new-instance p1, LN1/G;

    invoke-direct {p1}, LN1/G;-><init>()V

    iput-object p1, p0, Lg2/f;->f:LN1/G;

    .line 11
    new-instance p1, LN1/n;

    invoke-direct {p1}, LN1/n;-><init>()V

    iput-object p1, p0, Lg2/f;->g:LN1/T;

    .line 12
    iput-object p1, p0, Lg2/f;->j:LN1/T;

    const-wide/16 p1, -0x1

    .line 13
    iput-wide p1, p0, Lg2/f;->p:J

    return-void
.end method

.method public static synthetic c()[LN1/r;
    .locals 3

    .line 1
    new-instance v0, Lg2/f;

    .line 2
    .line 3
    invoke-direct {v0}, Lg2/f;-><init>()V

    .line 4
    .line 5
    .line 6
    const/4 v1, 0x1

    .line 7
    new-array v1, v1, [LN1/r;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    aput-object v0, v1, v2

    .line 11
    .line 12
    return-object v1
.end method

.method public static synthetic e(IIIII)Z
    .locals 3

    .line 1
    const/16 v0, 0x43

    const/4 v1, 0x2

    const/16 v2, 0x4d

    if-ne p1, v0, :cond_0

    const/16 v0, 0x4f

    if-ne p2, v0, :cond_0

    if-ne p3, v2, :cond_0

    if-eq p4, v2, :cond_1

    if-eq p0, v1, :cond_1

    :cond_0
    if-ne p1, v2, :cond_2

    const/16 p1, 0x4c

    if-ne p2, p1, :cond_2

    if-ne p3, p1, :cond_2

    const/16 p1, 0x54

    if-eq p4, p1, :cond_1

    if-ne p0, v1, :cond_2

    :cond_1
    const/4 p0, 0x1

    return p0

    :cond_2
    const/4 p0, 0x0

    return p0
.end method

.method private g()V
    .locals 1

    .line 1
    iget-object v0, p0, Lg2/f;->i:LN1/T;

    .line 2
    .line 3
    invoke-static {v0}, Lt1/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lg2/f;->h:LN1/t;

    .line 7
    .line 8
    invoke-static {v0}, Lt1/a0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static o(Landroidx/media3/common/x;)J
    .locals 6

    .line 1
    if-eqz p0, :cond_1

    .line 2
    .line 3
    invoke-virtual {p0}, Landroidx/media3/common/x;->e()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x0

    .line 8
    const/4 v2, 0x0

    .line 9
    :goto_0
    if-ge v2, v0, :cond_1

    .line 10
    .line 11
    invoke-virtual {p0, v2}, Landroidx/media3/common/x;->d(I)Landroidx/media3/common/x$a;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    instance-of v4, v3, Lb2/n;

    .line 16
    .line 17
    if-eqz v4, :cond_0

    .line 18
    .line 19
    check-cast v3, Lb2/n;

    .line 20
    .line 21
    iget-object v4, v3, Lb2/i;->a:Ljava/lang/String;

    .line 22
    .line 23
    const-string v5, "TLEN"

    .line 24
    .line 25
    invoke-virtual {v4, v5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 26
    .line 27
    .line 28
    move-result v4

    .line 29
    if-eqz v4, :cond_0

    .line 30
    .line 31
    iget-object p0, v3, Lb2/n;->d:Lcom/google/common/collect/ImmutableList;

    .line 32
    .line 33
    invoke-interface {p0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object p0

    .line 37
    check-cast p0, Ljava/lang/String;

    .line 38
    .line 39
    invoke-static {p0}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    .line 40
    .line 41
    .line 42
    move-result-wide v0

    .line 43
    invoke-static {v0, v1}, Lt1/a0;->Q0(J)J

    .line 44
    .line 45
    .line 46
    move-result-wide v0

    .line 47
    return-wide v0

    .line 48
    :cond_0
    add-int/lit8 v2, v2, 0x1

    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_1
    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    .line 52
    .line 53
    .line 54
    .line 55
    .line 56
    return-wide v0
.end method

.method public static p(Lt1/G;I)I
    .locals 2

    .line 1
    invoke-virtual {p0}, Lt1/G;->g()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    add-int/lit8 v1, p1, 0x4

    .line 6
    .line 7
    if-lt v0, v1, :cond_1

    .line 8
    .line 9
    invoke-virtual {p0, p1}, Lt1/G;->W(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 13
    .line 14
    .line 15
    move-result p1

    .line 16
    const v0, 0x58696e67

    .line 17
    .line 18
    .line 19
    if-eq p1, v0, :cond_0

    .line 20
    .line 21
    const v0, 0x496e666f

    .line 22
    .line 23
    .line 24
    if-ne p1, v0, :cond_1

    .line 25
    .line 26
    :cond_0
    return p1

    .line 27
    :cond_1
    invoke-virtual {p0}, Lt1/G;->g()I

    .line 28
    .line 29
    .line 30
    move-result p1

    .line 31
    const/16 v0, 0x28

    .line 32
    .line 33
    if-lt p1, v0, :cond_2

    .line 34
    .line 35
    const/16 p1, 0x24

    .line 36
    .line 37
    invoke-virtual {p0, p1}, Lt1/G;->W(I)V

    .line 38
    .line 39
    .line 40
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 41
    .line 42
    .line 43
    move-result p0

    .line 44
    const p1, 0x56425249

    .line 45
    .line 46
    .line 47
    if-ne p0, p1, :cond_2

    .line 48
    .line 49
    return p1

    .line 50
    :cond_2
    const/4 p0, 0x0

    .line 51
    return p0
.end method

.method public static q(IJ)Z
    .locals 4

    .line 1
    const v0, -0x1f400

    .line 2
    .line 3
    .line 4
    and-int/2addr p0, v0

    .line 5
    int-to-long v0, p0

    .line 6
    const-wide/32 v2, -0x1f400

    .line 7
    .line 8
    .line 9
    and-long/2addr p1, v2

    .line 10
    cmp-long p0, v0, p1

    .line 11
    .line 12
    if-nez p0, :cond_0

    .line 13
    .line 14
    const/4 p0, 0x1

    .line 15
    return p0

    .line 16
    :cond_0
    const/4 p0, 0x0

    .line 17
    return p0
.end method

.method public static r(Landroidx/media3/common/x;J)Lg2/c;
    .locals 4

    .line 1
    if-eqz p0, :cond_1

    .line 2
    .line 3
    invoke-virtual {p0}, Landroidx/media3/common/x;->e()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x0

    .line 8
    :goto_0
    if-ge v1, v0, :cond_1

    .line 9
    .line 10
    invoke-virtual {p0, v1}, Landroidx/media3/common/x;->d(I)Landroidx/media3/common/x$a;

    .line 11
    .line 12
    .line 13
    move-result-object v2

    .line 14
    instance-of v3, v2, Lb2/l;

    .line 15
    .line 16
    if-eqz v3, :cond_0

    .line 17
    .line 18
    check-cast v2, Lb2/l;

    .line 19
    .line 20
    invoke-static {p0}, Lg2/f;->o(Landroidx/media3/common/x;)J

    .line 21
    .line 22
    .line 23
    move-result-wide v0

    .line 24
    invoke-static {p1, p2, v2, v0, v1}, Lg2/c;->a(JLb2/l;J)Lg2/c;

    .line 25
    .line 26
    .line 27
    move-result-object p0

    .line 28
    return-object p0

    .line 29
    :cond_0
    add-int/lit8 v1, v1, 0x1

    .line 30
    .line 31
    goto :goto_0

    .line 32
    :cond_1
    const/4 p0, 0x0

    .line 33
    return-object p0
.end method

.method private w(LN1/s;)I
    .locals 11
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget v0, p0, Lg2/f;->q:I

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    const/4 v2, -0x1

    .line 5
    const/4 v3, 0x0

    .line 6
    if-nez v0, :cond_4

    .line 7
    .line 8
    invoke-interface {p1}, LN1/s;->k()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0, p1}, Lg2/f;->u(LN1/s;)Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    return v2

    .line 18
    :cond_0
    iget-object v0, p0, Lg2/f;->c:Lt1/G;

    .line 19
    .line 20
    invoke-virtual {v0, v3}, Lt1/G;->W(I)V

    .line 21
    .line 22
    .line 23
    iget-object v0, p0, Lg2/f;->c:Lt1/G;

    .line 24
    .line 25
    invoke-virtual {v0}, Lt1/G;->q()I

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    iget v4, p0, Lg2/f;->k:I

    .line 30
    .line 31
    int-to-long v4, v4

    .line 32
    invoke-static {v0, v4, v5}, Lg2/f;->q(IJ)Z

    .line 33
    .line 34
    .line 35
    move-result v4

    .line 36
    if-eqz v4, :cond_3

    .line 37
    .line 38
    invoke-static {v0}, LN1/I;->j(I)I

    .line 39
    .line 40
    .line 41
    move-result v4

    .line 42
    if-ne v4, v2, :cond_1

    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_1
    iget-object v4, p0, Lg2/f;->d:LN1/I$a;

    .line 46
    .line 47
    invoke-virtual {v4, v0}, LN1/I$a;->a(I)Z

    .line 48
    .line 49
    .line 50
    iget-wide v4, p0, Lg2/f;->m:J

    .line 51
    .line 52
    const-wide v6, -0x7fffffffffffffffL    # -4.9E-324

    .line 53
    .line 54
    .line 55
    .line 56
    .line 57
    cmp-long v0, v4, v6

    .line 58
    .line 59
    if-nez v0, :cond_2

    .line 60
    .line 61
    iget-object v0, p0, Lg2/f;->r:Lg2/g;

    .line 62
    .line 63
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 64
    .line 65
    .line 66
    move-result-wide v4

    .line 67
    invoke-interface {v0, v4, v5}, Lg2/g;->i(J)J

    .line 68
    .line 69
    .line 70
    move-result-wide v4

    .line 71
    iput-wide v4, p0, Lg2/f;->m:J

    .line 72
    .line 73
    iget-wide v4, p0, Lg2/f;->b:J

    .line 74
    .line 75
    cmp-long v0, v4, v6

    .line 76
    .line 77
    if-eqz v0, :cond_2

    .line 78
    .line 79
    iget-object v0, p0, Lg2/f;->r:Lg2/g;

    .line 80
    .line 81
    const-wide/16 v4, 0x0

    .line 82
    .line 83
    invoke-interface {v0, v4, v5}, Lg2/g;->i(J)J

    .line 84
    .line 85
    .line 86
    move-result-wide v4

    .line 87
    iget-wide v6, p0, Lg2/f;->m:J

    .line 88
    .line 89
    iget-wide v8, p0, Lg2/f;->b:J

    .line 90
    .line 91
    sub-long/2addr v8, v4

    .line 92
    add-long/2addr v6, v8

    .line 93
    iput-wide v6, p0, Lg2/f;->m:J

    .line 94
    .line 95
    :cond_2
    iget-object v0, p0, Lg2/f;->d:LN1/I$a;

    .line 96
    .line 97
    iget v0, v0, LN1/I$a;->c:I

    .line 98
    .line 99
    iput v0, p0, Lg2/f;->q:I

    .line 100
    .line 101
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 102
    .line 103
    .line 104
    move-result-wide v4

    .line 105
    iget-object v0, p0, Lg2/f;->d:LN1/I$a;

    .line 106
    .line 107
    iget v6, v0, LN1/I$a;->c:I

    .line 108
    .line 109
    int-to-long v6, v6

    .line 110
    add-long/2addr v4, v6

    .line 111
    iput-wide v4, p0, Lg2/f;->p:J

    .line 112
    .line 113
    iget-object v4, p0, Lg2/f;->r:Lg2/g;

    .line 114
    .line 115
    instance-of v5, v4, Lg2/b;

    .line 116
    .line 117
    if-eqz v5, :cond_4

    .line 118
    .line 119
    check-cast v4, Lg2/b;

    .line 120
    .line 121
    iget-wide v5, p0, Lg2/f;->n:J

    .line 122
    .line 123
    iget v0, v0, LN1/I$a;->g:I

    .line 124
    .line 125
    int-to-long v7, v0

    .line 126
    add-long/2addr v5, v7

    .line 127
    invoke-virtual {p0, v5, v6}, Lg2/f;->k(J)J

    .line 128
    .line 129
    .line 130
    move-result-wide v5

    .line 131
    iget-wide v7, p0, Lg2/f;->p:J

    .line 132
    .line 133
    invoke-virtual {v4, v5, v6, v7, v8}, Lg2/b;->b(JJ)V

    .line 134
    .line 135
    .line 136
    iget-boolean v0, p0, Lg2/f;->t:Z

    .line 137
    .line 138
    if-eqz v0, :cond_4

    .line 139
    .line 140
    iget-wide v5, p0, Lg2/f;->u:J

    .line 141
    .line 142
    invoke-virtual {v4, v5, v6}, Lg2/b;->a(J)Z

    .line 143
    .line 144
    .line 145
    move-result v0

    .line 146
    if-eqz v0, :cond_4

    .line 147
    .line 148
    iput-boolean v3, p0, Lg2/f;->t:Z

    .line 149
    .line 150
    iget-object v0, p0, Lg2/f;->i:LN1/T;

    .line 151
    .line 152
    iput-object v0, p0, Lg2/f;->j:LN1/T;

    .line 153
    .line 154
    goto :goto_1

    .line 155
    :cond_3
    :goto_0
    invoke-interface {p1, v1}, LN1/s;->n(I)V

    .line 156
    .line 157
    .line 158
    iput v3, p0, Lg2/f;->k:I

    .line 159
    .line 160
    return v3

    .line 161
    :cond_4
    :goto_1
    iget-object v0, p0, Lg2/f;->j:LN1/T;

    .line 162
    .line 163
    iget v4, p0, Lg2/f;->q:I

    .line 164
    .line 165
    invoke-interface {v0, p1, v4, v1}, LN1/T;->g(Landroidx/media3/common/j;IZ)I

    .line 166
    .line 167
    .line 168
    move-result p1

    .line 169
    if-ne p1, v2, :cond_5

    .line 170
    .line 171
    return v2

    .line 172
    :cond_5
    iget v0, p0, Lg2/f;->q:I

    .line 173
    .line 174
    sub-int/2addr v0, p1

    .line 175
    iput v0, p0, Lg2/f;->q:I

    .line 176
    .line 177
    if-lez v0, :cond_6

    .line 178
    .line 179
    return v3

    .line 180
    :cond_6
    iget-object v4, p0, Lg2/f;->j:LN1/T;

    .line 181
    .line 182
    iget-wide v0, p0, Lg2/f;->n:J

    .line 183
    .line 184
    invoke-virtual {p0, v0, v1}, Lg2/f;->k(J)J

    .line 185
    .line 186
    .line 187
    move-result-wide v5

    .line 188
    iget-object p1, p0, Lg2/f;->d:LN1/I$a;

    .line 189
    .line 190
    iget v8, p1, LN1/I$a;->c:I

    .line 191
    .line 192
    const/4 v9, 0x0

    .line 193
    const/4 v10, 0x0

    .line 194
    const/4 v7, 0x1

    .line 195
    invoke-interface/range {v4 .. v10}, LN1/T;->d(JIIILN1/T$a;)V

    .line 196
    .line 197
    .line 198
    iget-wide v0, p0, Lg2/f;->n:J

    .line 199
    .line 200
    iget-object p1, p0, Lg2/f;->d:LN1/I$a;

    .line 201
    .line 202
    iget p1, p1, LN1/I$a;->g:I

    .line 203
    .line 204
    int-to-long v4, p1

    .line 205
    add-long/2addr v0, v4

    .line 206
    iput-wide v0, p0, Lg2/f;->n:J

    .line 207
    .line 208
    iput v3, p0, Lg2/f;->q:I

    .line 209
    .line 210
    return v3
.end method


# virtual methods
.method public a(JJ)V
    .locals 2

    .line 1
    const/4 p1, 0x0

    .line 2
    iput p1, p0, Lg2/f;->k:I

    .line 3
    .line 4
    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    .line 5
    .line 6
    .line 7
    .line 8
    .line 9
    iput-wide v0, p0, Lg2/f;->m:J

    .line 10
    .line 11
    const-wide/16 v0, 0x0

    .line 12
    .line 13
    iput-wide v0, p0, Lg2/f;->n:J

    .line 14
    .line 15
    iput p1, p0, Lg2/f;->q:I

    .line 16
    .line 17
    iput-wide p3, p0, Lg2/f;->u:J

    .line 18
    .line 19
    iget-object p1, p0, Lg2/f;->r:Lg2/g;

    .line 20
    .line 21
    instance-of p2, p1, Lg2/b;

    .line 22
    .line 23
    if-eqz p2, :cond_0

    .line 24
    .line 25
    check-cast p1, Lg2/b;

    .line 26
    .line 27
    invoke-virtual {p1, p3, p4}, Lg2/b;->a(J)Z

    .line 28
    .line 29
    .line 30
    move-result p1

    .line 31
    if-nez p1, :cond_0

    .line 32
    .line 33
    const/4 p1, 0x1

    .line 34
    iput-boolean p1, p0, Lg2/f;->t:Z

    .line 35
    .line 36
    iget-object p1, p0, Lg2/f;->g:LN1/T;

    .line 37
    .line 38
    iput-object p1, p0, Lg2/f;->j:LN1/T;

    .line 39
    .line 40
    :cond_0
    return-void
.end method

.method public b(LN1/t;)V
    .locals 2

    .line 1
    iput-object p1, p0, Lg2/f;->h:LN1/t;

    .line 2
    .line 3
    const/4 v0, 0x0

    .line 4
    const/4 v1, 0x1

    .line 5
    invoke-interface {p1, v0, v1}, LN1/t;->n(II)LN1/T;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    iput-object p1, p0, Lg2/f;->i:LN1/T;

    .line 10
    .line 11
    iput-object p1, p0, Lg2/f;->j:LN1/T;

    .line 12
    .line 13
    iget-object p1, p0, Lg2/f;->h:LN1/t;

    .line 14
    .line 15
    invoke-interface {p1}, LN1/t;->l()V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public synthetic d()LN1/r;
    .locals 1

    .line 1
    invoke-static {p0}, LN1/q;->b(LN1/r;)LN1/r;

    move-result-object v0

    return-object v0
.end method

.method public synthetic f()Ljava/util/List;
    .locals 1

    .line 1
    invoke-static {p0}, LN1/q;->a(LN1/r;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public h(LN1/s;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    const/4 v0, 0x1

    .line 2
    invoke-virtual {p0, p1, v0}, Lg2/f;->x(LN1/s;Z)Z

    .line 3
    .line 4
    .line 5
    move-result p1

    .line 6
    return p1
.end method

.method public final i(LN1/s;)Lg2/g;
    .locals 11
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1}, Lg2/f;->s(LN1/s;)Lg2/g;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lg2/f;->l:Landroidx/media3/common/x;

    .line 6
    .line 7
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 8
    .line 9
    .line 10
    move-result-wide v2

    .line 11
    invoke-static {v1, v2, v3}, Lg2/f;->r(Landroidx/media3/common/x;J)Lg2/c;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    iget-boolean v2, p0, Lg2/f;->s:Z

    .line 16
    .line 17
    if-eqz v2, :cond_0

    .line 18
    .line 19
    new-instance p1, Lg2/g$a;

    .line 20
    .line 21
    invoke-direct {p1}, Lg2/g$a;-><init>()V

    .line 22
    .line 23
    .line 24
    return-object p1

    .line 25
    :cond_0
    iget v2, p0, Lg2/f;->a:I

    .line 26
    .line 27
    and-int/lit8 v2, v2, 0x4

    .line 28
    .line 29
    if-eqz v2, :cond_3

    .line 30
    .line 31
    if-eqz v1, :cond_1

    .line 32
    .line 33
    invoke-interface {v1}, LN1/M;->l()J

    .line 34
    .line 35
    .line 36
    move-result-wide v2

    .line 37
    invoke-interface {v1}, Lg2/g;->g()J

    .line 38
    .line 39
    .line 40
    move-result-wide v0

    .line 41
    :goto_0
    move-wide v9, v0

    .line 42
    move-wide v5, v2

    .line 43
    goto :goto_1

    .line 44
    :cond_1
    if-eqz v0, :cond_2

    .line 45
    .line 46
    invoke-interface {v0}, LN1/M;->l()J

    .line 47
    .line 48
    .line 49
    move-result-wide v2

    .line 50
    invoke-interface {v0}, Lg2/g;->g()J

    .line 51
    .line 52
    .line 53
    move-result-wide v0

    .line 54
    goto :goto_0

    .line 55
    :cond_2
    iget-object v0, p0, Lg2/f;->l:Landroidx/media3/common/x;

    .line 56
    .line 57
    invoke-static {v0}, Lg2/f;->o(Landroidx/media3/common/x;)J

    .line 58
    .line 59
    .line 60
    move-result-wide v2

    .line 61
    const-wide/16 v0, -0x1

    .line 62
    .line 63
    goto :goto_0

    .line 64
    :goto_1
    new-instance v4, Lg2/b;

    .line 65
    .line 66
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 67
    .line 68
    .line 69
    move-result-wide v7

    .line 70
    invoke-direct/range {v4 .. v10}, Lg2/b;-><init>(JJJ)V

    .line 71
    .line 72
    .line 73
    move-object v0, v4

    .line 74
    goto :goto_2

    .line 75
    :cond_3
    if-eqz v1, :cond_4

    .line 76
    .line 77
    move-object v0, v1

    .line 78
    goto :goto_2

    .line 79
    :cond_4
    if-eqz v0, :cond_5

    .line 80
    .line 81
    goto :goto_2

    .line 82
    :cond_5
    const/4 v0, 0x0

    .line 83
    :goto_2
    const/4 v1, 0x1

    .line 84
    if-eqz v0, :cond_6

    .line 85
    .line 86
    invoke-interface {v0}, LN1/M;->e()Z

    .line 87
    .line 88
    .line 89
    move-result v2

    .line 90
    if-nez v2, :cond_8

    .line 91
    .line 92
    iget v2, p0, Lg2/f;->a:I

    .line 93
    .line 94
    and-int/2addr v2, v1

    .line 95
    if-eqz v2, :cond_8

    .line 96
    .line 97
    :cond_6
    iget v0, p0, Lg2/f;->a:I

    .line 98
    .line 99
    and-int/lit8 v0, v0, 0x2

    .line 100
    .line 101
    if-eqz v0, :cond_7

    .line 102
    .line 103
    goto :goto_3

    .line 104
    :cond_7
    const/4 v1, 0x0

    .line 105
    :goto_3
    invoke-virtual {p0, p1, v1}, Lg2/f;->n(LN1/s;Z)Lg2/g;

    .line 106
    .line 107
    .line 108
    move-result-object v0

    .line 109
    :cond_8
    if-eqz v0, :cond_9

    .line 110
    .line 111
    iget-object p1, p0, Lg2/f;->i:LN1/T;

    .line 112
    .line 113
    invoke-interface {v0}, LN1/M;->l()J

    .line 114
    .line 115
    .line 116
    move-result-wide v1

    .line 117
    invoke-interface {p1, v1, v2}, LN1/T;->b(J)V

    .line 118
    .line 119
    .line 120
    :cond_9
    return-object v0
.end method

.method public j(LN1/s;LN1/L;)I
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lg2/f;->g()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0, p1}, Lg2/f;->v(LN1/s;)I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    const/4 p2, -0x1

    .line 9
    if-ne p1, p2, :cond_0

    .line 10
    .line 11
    iget-object p2, p0, Lg2/f;->r:Lg2/g;

    .line 12
    .line 13
    instance-of p2, p2, Lg2/b;

    .line 14
    .line 15
    if-eqz p2, :cond_0

    .line 16
    .line 17
    iget-wide v0, p0, Lg2/f;->n:J

    .line 18
    .line 19
    invoke-virtual {p0, v0, v1}, Lg2/f;->k(J)J

    .line 20
    .line 21
    .line 22
    move-result-wide v0

    .line 23
    iget-object p2, p0, Lg2/f;->r:Lg2/g;

    .line 24
    .line 25
    invoke-interface {p2}, LN1/M;->l()J

    .line 26
    .line 27
    .line 28
    move-result-wide v2

    .line 29
    cmp-long p2, v2, v0

    .line 30
    .line 31
    if-eqz p2, :cond_0

    .line 32
    .line 33
    iget-object p2, p0, Lg2/f;->r:Lg2/g;

    .line 34
    .line 35
    check-cast p2, Lg2/b;

    .line 36
    .line 37
    invoke-virtual {p2, v0, v1}, Lg2/b;->d(J)V

    .line 38
    .line 39
    .line 40
    iget-object p2, p0, Lg2/f;->h:LN1/t;

    .line 41
    .line 42
    iget-object v0, p0, Lg2/f;->r:Lg2/g;

    .line 43
    .line 44
    invoke-interface {p2, v0}, LN1/t;->q(LN1/M;)V

    .line 45
    .line 46
    .line 47
    iget-object p2, p0, Lg2/f;->i:LN1/T;

    .line 48
    .line 49
    iget-object v0, p0, Lg2/f;->r:Lg2/g;

    .line 50
    .line 51
    invoke-interface {v0}, LN1/M;->l()J

    .line 52
    .line 53
    .line 54
    move-result-wide v0

    .line 55
    invoke-interface {p2, v0, v1}, LN1/T;->b(J)V

    .line 56
    .line 57
    .line 58
    :cond_0
    return p1
.end method

.method public final k(J)J
    .locals 4

    .line 1
    iget-wide v0, p0, Lg2/f;->m:J

    .line 2
    .line 3
    const-wide/32 v2, 0xf4240

    .line 4
    .line 5
    .line 6
    mul-long p1, p1, v2

    .line 7
    .line 8
    iget-object v2, p0, Lg2/f;->d:LN1/I$a;

    .line 9
    .line 10
    iget v2, v2, LN1/I$a;->d:I

    .line 11
    .line 12
    int-to-long v2, v2

    .line 13
    div-long/2addr p1, v2

    .line 14
    add-long/2addr v0, p1

    .line 15
    return-wide v0
.end method

.method public l()V
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    iput-boolean v0, p0, Lg2/f;->s:Z

    .line 3
    .line 4
    return-void
.end method

.method public final m(JLg2/i;J)Lg2/g;
    .locals 15

    .line 1
    move-object/from16 v0, p3

    .line 2
    .line 3
    invoke-virtual {v0}, Lg2/i;->a()J

    .line 4
    .line 5
    .line 6
    move-result-wide v5

    .line 7
    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    .line 8
    .line 9
    .line 10
    .line 11
    .line 12
    const/4 v3, 0x0

    .line 13
    cmp-long v4, v5, v1

    .line 14
    .line 15
    if-nez v4, :cond_0

    .line 16
    .line 17
    return-object v3

    .line 18
    :cond_0
    iget-wide v1, v0, Lg2/i;->c:J

    .line 19
    .line 20
    const-wide/16 v7, -0x1

    .line 21
    .line 22
    cmp-long v4, v1, v7

    .line 23
    .line 24
    if-eqz v4, :cond_1

    .line 25
    .line 26
    add-long v3, p1, v1

    .line 27
    .line 28
    iget-object v7, v0, Lg2/i;->a:LN1/I$a;

    .line 29
    .line 30
    iget v7, v7, LN1/I$a;->c:I

    .line 31
    .line 32
    int-to-long v7, v7

    .line 33
    sub-long/2addr v1, v7

    .line 34
    move-wide v8, v3

    .line 35
    goto :goto_0

    .line 36
    :cond_1
    cmp-long v1, p4, v7

    .line 37
    .line 38
    if-eqz v1, :cond_2

    .line 39
    .line 40
    sub-long v1, p4, p1

    .line 41
    .line 42
    iget-object v3, v0, Lg2/i;->a:LN1/I$a;

    .line 43
    .line 44
    iget v3, v3, LN1/I$a;->c:I

    .line 45
    .line 46
    int-to-long v3, v3

    .line 47
    sub-long/2addr v1, v3

    .line 48
    move-wide/from16 v8, p4

    .line 49
    .line 50
    :goto_0
    sget-object v7, Ljava/math/RoundingMode;->HALF_UP:Ljava/math/RoundingMode;

    .line 51
    .line 52
    const-wide/32 v3, 0x7a1200

    .line 53
    .line 54
    .line 55
    invoke-static/range {v1 .. v7}, Lt1/a0;->e1(JJJLjava/math/RoundingMode;)J

    .line 56
    .line 57
    .line 58
    move-result-wide v3

    .line 59
    invoke-static {v3, v4}, Lcom/google/common/primitives/Ints;->d(J)I

    .line 60
    .line 61
    .line 62
    move-result v12

    .line 63
    iget-wide v3, v0, Lg2/i;->b:J

    .line 64
    .line 65
    invoke-static {v1, v2, v3, v4, v7}, Lcom/google/common/math/LongMath;->c(JJLjava/math/RoundingMode;)J

    .line 66
    .line 67
    .line 68
    move-result-wide v1

    .line 69
    invoke-static {v1, v2}, Lcom/google/common/primitives/Ints;->d(J)I

    .line 70
    .line 71
    .line 72
    move-result v13

    .line 73
    new-instance v7, Lg2/a;

    .line 74
    .line 75
    iget-object v0, v0, Lg2/i;->a:LN1/I$a;

    .line 76
    .line 77
    iget v0, v0, LN1/I$a;->c:I

    .line 78
    .line 79
    int-to-long v0, v0

    .line 80
    add-long v10, p1, v0

    .line 81
    .line 82
    const/4 v14, 0x0

    .line 83
    invoke-direct/range {v7 .. v14}, Lg2/a;-><init>(JJIIZ)V

    .line 84
    .line 85
    .line 86
    return-object v7

    .line 87
    :cond_2
    return-object v3
.end method

.method public final n(LN1/s;Z)Lg2/g;
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lg2/f;->c:Lt1/G;

    .line 2
    .line 3
    invoke-virtual {v0}, Lt1/G;->e()[B

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    const/4 v1, 0x4

    .line 8
    const/4 v2, 0x0

    .line 9
    invoke-interface {p1, v0, v2, v1}, LN1/s;->i([BII)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lg2/f;->c:Lt1/G;

    .line 13
    .line 14
    invoke-virtual {v0, v2}, Lt1/G;->W(I)V

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, Lg2/f;->d:LN1/I$a;

    .line 18
    .line 19
    iget-object v1, p0, Lg2/f;->c:Lt1/G;

    .line 20
    .line 21
    invoke-virtual {v1}, Lt1/G;->q()I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    invoke-virtual {v0, v1}, LN1/I$a;->a(I)Z

    .line 26
    .line 27
    .line 28
    new-instance v2, Lg2/a;

    .line 29
    .line 30
    invoke-interface {p1}, LN1/s;->getLength()J

    .line 31
    .line 32
    .line 33
    move-result-wide v3

    .line 34
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 35
    .line 36
    .line 37
    move-result-wide v5

    .line 38
    iget-object v7, p0, Lg2/f;->d:LN1/I$a;

    .line 39
    .line 40
    move v8, p2

    .line 41
    invoke-direct/range {v2 .. v8}, Lg2/a;-><init>(JJLN1/I$a;Z)V

    .line 42
    .line 43
    .line 44
    return-object v2
.end method

.method public release()V
    .locals 0

    .line 1
    return-void
.end method

.method public final s(LN1/s;)Lg2/g;
    .locals 12
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    new-instance v5, Lt1/G;

    .line 2
    .line 3
    iget-object v0, p0, Lg2/f;->d:LN1/I$a;

    .line 4
    .line 5
    iget v0, v0, LN1/I$a;->c:I

    .line 6
    .line 7
    invoke-direct {v5, v0}, Lt1/G;-><init>(I)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {v5}, Lt1/G;->e()[B

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    iget-object v1, p0, Lg2/f;->d:LN1/I$a;

    .line 15
    .line 16
    iget v1, v1, LN1/I$a;->c:I

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-interface {p1, v0, v2, v1}, LN1/s;->i([BII)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lg2/f;->d:LN1/I$a;

    .line 23
    .line 24
    iget v1, v0, LN1/I$a;->a:I

    .line 25
    .line 26
    const/4 v2, 0x1

    .line 27
    and-int/2addr v1, v2

    .line 28
    const/16 v3, 0x15

    .line 29
    .line 30
    if-eqz v1, :cond_0

    .line 31
    .line 32
    iget v0, v0, LN1/I$a;->e:I

    .line 33
    .line 34
    if-eq v0, v2, :cond_2

    .line 35
    .line 36
    const/16 v3, 0x24

    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_0
    iget v0, v0, LN1/I$a;->e:I

    .line 40
    .line 41
    if-eq v0, v2, :cond_1

    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_1
    const/16 v3, 0xd

    .line 45
    .line 46
    :cond_2
    :goto_0
    invoke-static {v5, v3}, Lg2/f;->p(Lt1/G;I)I

    .line 47
    .line 48
    .line 49
    move-result v0

    .line 50
    const v1, 0x496e666f

    .line 51
    .line 52
    .line 53
    const v2, 0x58696e67

    .line 54
    .line 55
    .line 56
    if-eq v0, v1, :cond_4

    .line 57
    .line 58
    const v1, 0x56425249

    .line 59
    .line 60
    .line 61
    if-eq v0, v1, :cond_3

    .line 62
    .line 63
    if-eq v0, v2, :cond_4

    .line 64
    .line 65
    invoke-interface {p1}, LN1/s;->k()V

    .line 66
    .line 67
    .line 68
    const/4 p1, 0x0

    .line 69
    return-object p1

    .line 70
    :cond_3
    invoke-interface {p1}, LN1/s;->getLength()J

    .line 71
    .line 72
    .line 73
    move-result-wide v0

    .line 74
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 75
    .line 76
    .line 77
    move-result-wide v2

    .line 78
    iget-object v4, p0, Lg2/f;->d:LN1/I$a;

    .line 79
    .line 80
    invoke-static/range {v0 .. v5}, Lg2/h;->a(JJLN1/I$a;Lt1/G;)Lg2/h;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    iget-object v1, p0, Lg2/f;->d:LN1/I$a;

    .line 85
    .line 86
    iget v1, v1, LN1/I$a;->c:I

    .line 87
    .line 88
    invoke-interface {p1, v1}, LN1/s;->n(I)V

    .line 89
    .line 90
    .line 91
    return-object v0

    .line 92
    :cond_4
    iget-object v1, p0, Lg2/f;->d:LN1/I$a;

    .line 93
    .line 94
    invoke-static {v1, v5}, Lg2/i;->b(LN1/I$a;Lt1/G;)Lg2/i;

    .line 95
    .line 96
    .line 97
    move-result-object v9

    .line 98
    iget-object v1, p0, Lg2/f;->e:LN1/E;

    .line 99
    .line 100
    invoke-virtual {v1}, LN1/E;->a()Z

    .line 101
    .line 102
    .line 103
    move-result v1

    .line 104
    if-nez v1, :cond_5

    .line 105
    .line 106
    iget v1, v9, Lg2/i;->d:I

    .line 107
    .line 108
    const/4 v3, -0x1

    .line 109
    if-eq v1, v3, :cond_5

    .line 110
    .line 111
    iget v4, v9, Lg2/i;->e:I

    .line 112
    .line 113
    if-eq v4, v3, :cond_5

    .line 114
    .line 115
    iget-object v3, p0, Lg2/f;->e:LN1/E;

    .line 116
    .line 117
    iput v1, v3, LN1/E;->a:I

    .line 118
    .line 119
    iput v4, v3, LN1/E;->b:I

    .line 120
    .line 121
    :cond_5
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 122
    .line 123
    .line 124
    move-result-wide v7

    .line 125
    invoke-interface {p1}, LN1/s;->getLength()J

    .line 126
    .line 127
    .line 128
    move-result-wide v3

    .line 129
    const-wide/16 v5, -0x1

    .line 130
    .line 131
    cmp-long v1, v3, v5

    .line 132
    .line 133
    if-eqz v1, :cond_6

    .line 134
    .line 135
    iget-wide v3, v9, Lg2/i;->c:J

    .line 136
    .line 137
    cmp-long v1, v3, v5

    .line 138
    .line 139
    if-eqz v1, :cond_6

    .line 140
    .line 141
    invoke-interface {p1}, LN1/s;->getLength()J

    .line 142
    .line 143
    .line 144
    move-result-wide v3

    .line 145
    iget-wide v5, v9, Lg2/i;->c:J

    .line 146
    .line 147
    add-long/2addr v5, v7

    .line 148
    cmp-long v1, v3, v5

    .line 149
    .line 150
    if-eqz v1, :cond_6

    .line 151
    .line 152
    new-instance v1, Ljava/lang/StringBuilder;

    .line 153
    .line 154
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 155
    .line 156
    .line 157
    const-string v3, "Data size mismatch between stream ("

    .line 158
    .line 159
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 160
    .line 161
    .line 162
    invoke-interface {p1}, LN1/s;->getLength()J

    .line 163
    .line 164
    .line 165
    move-result-wide v3

    .line 166
    invoke-virtual {v1, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 167
    .line 168
    .line 169
    const-string v3, ") and Xing frame ("

    .line 170
    .line 171
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 172
    .line 173
    .line 174
    iget-wide v3, v9, Lg2/i;->c:J

    .line 175
    .line 176
    add-long/2addr v3, v7

    .line 177
    invoke-virtual {v1, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 178
    .line 179
    .line 180
    const-string v3, "), using Xing value."

    .line 181
    .line 182
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 183
    .line 184
    .line 185
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 186
    .line 187
    .line 188
    move-result-object v1

    .line 189
    const-string v3, "Mp3Extractor"

    .line 190
    .line 191
    invoke-static {v3, v1}, Lt1/r;->f(Ljava/lang/String;Ljava/lang/String;)V

    .line 192
    .line 193
    .line 194
    :cond_6
    iget-object v1, p0, Lg2/f;->d:LN1/I$a;

    .line 195
    .line 196
    iget v1, v1, LN1/I$a;->c:I

    .line 197
    .line 198
    invoke-interface {p1, v1}, LN1/s;->n(I)V

    .line 199
    .line 200
    .line 201
    if-ne v0, v2, :cond_7

    .line 202
    .line 203
    invoke-static {v9, v7, v8}, Lg2/j;->a(Lg2/i;J)Lg2/j;

    .line 204
    .line 205
    .line 206
    move-result-object p1

    .line 207
    return-object p1

    .line 208
    :cond_7
    invoke-interface {p1}, LN1/s;->getLength()J

    .line 209
    .line 210
    .line 211
    move-result-wide v10

    .line 212
    move-object v6, p0

    .line 213
    invoke-virtual/range {v6 .. v11}, Lg2/f;->m(JLg2/i;J)Lg2/g;

    .line 214
    .line 215
    .line 216
    move-result-object p1

    .line 217
    return-object p1
.end method

.method public final t()V
    .locals 5

    .line 1
    iget-object v0, p0, Lg2/f;->r:Lg2/g;

    .line 2
    .line 3
    instance-of v1, v0, Lg2/a;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    invoke-interface {v0}, LN1/M;->e()Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-eqz v0, :cond_0

    .line 12
    .line 13
    iget-wide v0, p0, Lg2/f;->p:J

    .line 14
    .line 15
    const-wide/16 v2, -0x1

    .line 16
    .line 17
    cmp-long v4, v0, v2

    .line 18
    .line 19
    if-eqz v4, :cond_0

    .line 20
    .line 21
    iget-object v2, p0, Lg2/f;->r:Lg2/g;

    .line 22
    .line 23
    invoke-interface {v2}, Lg2/g;->g()J

    .line 24
    .line 25
    .line 26
    move-result-wide v2

    .line 27
    cmp-long v4, v0, v2

    .line 28
    .line 29
    if-eqz v4, :cond_0

    .line 30
    .line 31
    iget-object v0, p0, Lg2/f;->r:Lg2/g;

    .line 32
    .line 33
    check-cast v0, Lg2/a;

    .line 34
    .line 35
    iget-wide v1, p0, Lg2/f;->p:J

    .line 36
    .line 37
    invoke-virtual {v0, v1, v2}, Lg2/a;->f(J)Lg2/a;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    iput-object v0, p0, Lg2/f;->r:Lg2/g;

    .line 42
    .line 43
    iget-object v0, p0, Lg2/f;->h:LN1/t;

    .line 44
    .line 45
    invoke-static {v0}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    check-cast v0, LN1/t;

    .line 50
    .line 51
    iget-object v1, p0, Lg2/f;->r:Lg2/g;

    .line 52
    .line 53
    invoke-interface {v0, v1}, LN1/t;->q(LN1/M;)V

    .line 54
    .line 55
    .line 56
    iget-object v0, p0, Lg2/f;->i:LN1/T;

    .line 57
    .line 58
    invoke-static {v0}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    check-cast v0, LN1/T;

    .line 63
    .line 64
    iget-object v1, p0, Lg2/f;->r:Lg2/g;

    .line 65
    .line 66
    invoke-interface {v1}, LN1/M;->l()J

    .line 67
    .line 68
    .line 69
    move-result-wide v1

    .line 70
    invoke-interface {v0, v1, v2}, LN1/T;->b(J)V

    .line 71
    .line 72
    .line 73
    :cond_0
    return-void
.end method

.method public final u(LN1/s;)Z
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lg2/f;->r:Lg2/g;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    invoke-interface {v0}, Lg2/g;->g()J

    .line 7
    .line 8
    .line 9
    move-result-wide v2

    .line 10
    const-wide/16 v4, -0x1

    .line 11
    .line 12
    cmp-long v0, v2, v4

    .line 13
    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    invoke-interface {p1}, LN1/s;->l()J

    .line 17
    .line 18
    .line 19
    move-result-wide v4

    .line 20
    const-wide/16 v6, 0x4

    .line 21
    .line 22
    sub-long/2addr v2, v6

    .line 23
    cmp-long v0, v4, v2

    .line 24
    .line 25
    if-lez v0, :cond_0

    .line 26
    .line 27
    return v1

    .line 28
    :cond_0
    :try_start_0
    iget-object v0, p0, Lg2/f;->c:Lt1/G;

    .line 29
    .line 30
    invoke-virtual {v0}, Lt1/G;->e()[B

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    const/4 v2, 0x0

    .line 35
    const/4 v3, 0x4

    .line 36
    invoke-interface {p1, v0, v2, v3, v1}, LN1/s;->j([BIIZ)Z

    .line 37
    .line 38
    .line 39
    move-result p1
    :try_end_0
    .catch Ljava/io/EOFException; {:try_start_0 .. :try_end_0} :catch_0

    .line 40
    xor-int/2addr p1, v1

    .line 41
    return p1

    .line 42
    :catch_0
    return v1
.end method

.method public final v(LN1/s;)I
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget v0, p0, Lg2/f;->k:I

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    :try_start_0
    invoke-virtual {p0, p1, v0}, Lg2/f;->x(LN1/s;Z)Z
    :try_end_0
    .catch Ljava/io/EOFException; {:try_start_0 .. :try_end_0} :catch_0

    .line 7
    .line 8
    .line 9
    goto :goto_0

    .line 10
    :catch_0
    const/4 p1, -0x1

    .line 11
    return p1

    .line 12
    :cond_0
    :goto_0
    iget-object v0, p0, Lg2/f;->r:Lg2/g;

    .line 13
    .line 14
    if-nez v0, :cond_3

    .line 15
    .line 16
    invoke-virtual {p0, p1}, Lg2/f;->i(LN1/s;)Lg2/g;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    iput-object v0, p0, Lg2/f;->r:Lg2/g;

    .line 21
    .line 22
    iget-object v1, p0, Lg2/f;->h:LN1/t;

    .line 23
    .line 24
    invoke-interface {v1, v0}, LN1/t;->q(LN1/M;)V

    .line 25
    .line 26
    .line 27
    new-instance v0, Landroidx/media3/common/r$b;

    .line 28
    .line 29
    invoke-direct {v0}, Landroidx/media3/common/r$b;-><init>()V

    .line 30
    .line 31
    .line 32
    const-string v1, "audio/mpeg"

    .line 33
    .line 34
    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->U(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    iget-object v1, p0, Lg2/f;->d:LN1/I$a;

    .line 39
    .line 40
    iget-object v1, v1, LN1/I$a;->b:Ljava/lang/String;

    .line 41
    .line 42
    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->u0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    const/16 v1, 0x1000

    .line 47
    .line 48
    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->k0(I)Landroidx/media3/common/r$b;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    iget-object v1, p0, Lg2/f;->d:LN1/I$a;

    .line 53
    .line 54
    iget v1, v1, LN1/I$a;->e:I

    .line 55
    .line 56
    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->R(I)Landroidx/media3/common/r$b;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    iget-object v1, p0, Lg2/f;->d:LN1/I$a;

    .line 61
    .line 62
    iget v1, v1, LN1/I$a;->d:I

    .line 63
    .line 64
    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->v0(I)Landroidx/media3/common/r$b;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    iget-object v1, p0, Lg2/f;->e:LN1/E;

    .line 69
    .line 70
    iget v1, v1, LN1/E;->a:I

    .line 71
    .line 72
    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->Z(I)Landroidx/media3/common/r$b;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    iget-object v1, p0, Lg2/f;->e:LN1/E;

    .line 77
    .line 78
    iget v1, v1, LN1/E;->b:I

    .line 79
    .line 80
    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->a0(I)Landroidx/media3/common/r$b;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    iget v1, p0, Lg2/f;->a:I

    .line 85
    .line 86
    and-int/lit8 v1, v1, 0x8

    .line 87
    .line 88
    if-eqz v1, :cond_1

    .line 89
    .line 90
    const/4 v1, 0x0

    .line 91
    goto :goto_1

    .line 92
    :cond_1
    iget-object v1, p0, Lg2/f;->l:Landroidx/media3/common/x;

    .line 93
    .line 94
    :goto_1
    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->n0(Landroidx/media3/common/x;)Landroidx/media3/common/r$b;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    iget-object v1, p0, Lg2/f;->r:Lg2/g;

    .line 99
    .line 100
    invoke-interface {v1}, Lg2/g;->k()I

    .line 101
    .line 102
    .line 103
    move-result v1

    .line 104
    const v2, -0x7fffffff

    .line 105
    .line 106
    .line 107
    if-eq v1, v2, :cond_2

    .line 108
    .line 109
    iget-object v1, p0, Lg2/f;->r:Lg2/g;

    .line 110
    .line 111
    invoke-interface {v1}, Lg2/g;->k()I

    .line 112
    .line 113
    .line 114
    move-result v1

    .line 115
    invoke-virtual {v0, v1}, Landroidx/media3/common/r$b;->Q(I)Landroidx/media3/common/r$b;

    .line 116
    .line 117
    .line 118
    :cond_2
    iget-object v1, p0, Lg2/f;->j:LN1/T;

    .line 119
    .line 120
    invoke-virtual {v0}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    .line 121
    .line 122
    .line 123
    move-result-object v0

    .line 124
    invoke-interface {v1, v0}, LN1/T;->e(Landroidx/media3/common/r;)V

    .line 125
    .line 126
    .line 127
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 128
    .line 129
    .line 130
    move-result-wide v0

    .line 131
    iput-wide v0, p0, Lg2/f;->o:J

    .line 132
    .line 133
    goto :goto_2

    .line 134
    :cond_3
    iget-wide v0, p0, Lg2/f;->o:J

    .line 135
    .line 136
    const-wide/16 v2, 0x0

    .line 137
    .line 138
    cmp-long v4, v0, v2

    .line 139
    .line 140
    if-eqz v4, :cond_4

    .line 141
    .line 142
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 143
    .line 144
    .line 145
    move-result-wide v0

    .line 146
    iget-wide v2, p0, Lg2/f;->o:J

    .line 147
    .line 148
    cmp-long v4, v0, v2

    .line 149
    .line 150
    if-gez v4, :cond_4

    .line 151
    .line 152
    sub-long/2addr v2, v0

    .line 153
    long-to-int v0, v2

    .line 154
    invoke-interface {p1, v0}, LN1/s;->n(I)V

    .line 155
    .line 156
    .line 157
    :cond_4
    :goto_2
    invoke-direct {p0, p1}, Lg2/f;->w(LN1/s;)I

    .line 158
    .line 159
    .line 160
    move-result p1

    .line 161
    return p1
.end method

.method public final x(LN1/s;Z)Z
    .locals 10
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    if-eqz p2, :cond_0

    .line 2
    .line 3
    const v0, 0x8000

    .line 4
    .line 5
    .line 6
    goto :goto_0

    .line 7
    :cond_0
    const/high16 v0, 0x20000

    .line 8
    .line 9
    :goto_0
    invoke-interface {p1}, LN1/s;->k()V

    .line 10
    .line 11
    .line 12
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 13
    .line 14
    .line 15
    move-result-wide v1

    .line 16
    const-wide/16 v3, 0x0

    .line 17
    .line 18
    const/4 v5, 0x0

    .line 19
    cmp-long v6, v1, v3

    .line 20
    .line 21
    if-nez v6, :cond_4

    .line 22
    .line 23
    iget v1, p0, Lg2/f;->a:I

    .line 24
    .line 25
    and-int/lit8 v1, v1, 0x8

    .line 26
    .line 27
    if-nez v1, :cond_1

    .line 28
    .line 29
    const/4 v1, 0x0

    .line 30
    goto :goto_1

    .line 31
    :cond_1
    sget-object v1, Lg2/f;->w:Lb2/h$a;

    .line 32
    .line 33
    :goto_1
    iget-object v2, p0, Lg2/f;->f:LN1/G;

    .line 34
    .line 35
    invoke-virtual {v2, p1, v1}, LN1/G;->a(LN1/s;Lb2/h$a;)Landroidx/media3/common/x;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    iput-object v1, p0, Lg2/f;->l:Landroidx/media3/common/x;

    .line 40
    .line 41
    if-eqz v1, :cond_2

    .line 42
    .line 43
    iget-object v2, p0, Lg2/f;->e:LN1/E;

    .line 44
    .line 45
    invoke-virtual {v2, v1}, LN1/E;->c(Landroidx/media3/common/x;)Z

    .line 46
    .line 47
    .line 48
    :cond_2
    invoke-interface {p1}, LN1/s;->l()J

    .line 49
    .line 50
    .line 51
    move-result-wide v1

    .line 52
    long-to-int v2, v1

    .line 53
    if-nez p2, :cond_3

    .line 54
    .line 55
    invoke-interface {p1, v2}, LN1/s;->n(I)V

    .line 56
    .line 57
    .line 58
    :cond_3
    const/4 v1, 0x0

    .line 59
    :goto_2
    const/4 v3, 0x0

    .line 60
    const/4 v4, 0x0

    .line 61
    goto :goto_3

    .line 62
    :cond_4
    const/4 v1, 0x0

    .line 63
    const/4 v2, 0x0

    .line 64
    goto :goto_2

    .line 65
    :goto_3
    invoke-virtual {p0, p1}, Lg2/f;->u(LN1/s;)Z

    .line 66
    .line 67
    .line 68
    move-result v6

    .line 69
    const/4 v7, 0x1

    .line 70
    if-eqz v6, :cond_6

    .line 71
    .line 72
    if-lez v3, :cond_5

    .line 73
    .line 74
    goto :goto_5

    .line 75
    :cond_5
    invoke-virtual {p0}, Lg2/f;->t()V

    .line 76
    .line 77
    .line 78
    new-instance p1, Ljava/io/EOFException;

    .line 79
    .line 80
    invoke-direct {p1}, Ljava/io/EOFException;-><init>()V

    .line 81
    .line 82
    .line 83
    throw p1

    .line 84
    :cond_6
    iget-object v6, p0, Lg2/f;->c:Lt1/G;

    .line 85
    .line 86
    invoke-virtual {v6, v5}, Lt1/G;->W(I)V

    .line 87
    .line 88
    .line 89
    iget-object v6, p0, Lg2/f;->c:Lt1/G;

    .line 90
    .line 91
    invoke-virtual {v6}, Lt1/G;->q()I

    .line 92
    .line 93
    .line 94
    move-result v6

    .line 95
    if-eqz v1, :cond_7

    .line 96
    .line 97
    int-to-long v8, v1

    .line 98
    invoke-static {v6, v8, v9}, Lg2/f;->q(IJ)Z

    .line 99
    .line 100
    .line 101
    move-result v8

    .line 102
    if-eqz v8, :cond_8

    .line 103
    .line 104
    :cond_7
    invoke-static {v6}, LN1/I;->j(I)I

    .line 105
    .line 106
    .line 107
    move-result v8

    .line 108
    const/4 v9, -0x1

    .line 109
    if-ne v8, v9, :cond_c

    .line 110
    .line 111
    :cond_8
    add-int/lit8 v1, v4, 0x1

    .line 112
    .line 113
    if-ne v4, v0, :cond_a

    .line 114
    .line 115
    if-eqz p2, :cond_9

    .line 116
    .line 117
    return v5

    .line 118
    :cond_9
    invoke-virtual {p0}, Lg2/f;->t()V

    .line 119
    .line 120
    .line 121
    new-instance p1, Ljava/io/EOFException;

    .line 122
    .line 123
    invoke-direct {p1}, Ljava/io/EOFException;-><init>()V

    .line 124
    .line 125
    .line 126
    throw p1

    .line 127
    :cond_a
    if-eqz p2, :cond_b

    .line 128
    .line 129
    invoke-interface {p1}, LN1/s;->k()V

    .line 130
    .line 131
    .line 132
    add-int v3, v2, v1

    .line 133
    .line 134
    invoke-interface {p1, v3}, LN1/s;->m(I)V

    .line 135
    .line 136
    .line 137
    goto :goto_4

    .line 138
    :cond_b
    invoke-interface {p1, v7}, LN1/s;->n(I)V

    .line 139
    .line 140
    .line 141
    :goto_4
    move v4, v1

    .line 142
    const/4 v1, 0x0

    .line 143
    const/4 v3, 0x0

    .line 144
    goto :goto_3

    .line 145
    :cond_c
    add-int/lit8 v3, v3, 0x1

    .line 146
    .line 147
    if-ne v3, v7, :cond_d

    .line 148
    .line 149
    iget-object v1, p0, Lg2/f;->d:LN1/I$a;

    .line 150
    .line 151
    invoke-virtual {v1, v6}, LN1/I$a;->a(I)Z

    .line 152
    .line 153
    .line 154
    move v1, v6

    .line 155
    goto :goto_7

    .line 156
    :cond_d
    const/4 v6, 0x4

    .line 157
    if-ne v3, v6, :cond_f

    .line 158
    .line 159
    :goto_5
    if-eqz p2, :cond_e

    .line 160
    .line 161
    add-int/2addr v2, v4

    .line 162
    invoke-interface {p1, v2}, LN1/s;->n(I)V

    .line 163
    .line 164
    .line 165
    goto :goto_6

    .line 166
    :cond_e
    invoke-interface {p1}, LN1/s;->k()V

    .line 167
    .line 168
    .line 169
    :goto_6
    iput v1, p0, Lg2/f;->k:I

    .line 170
    .line 171
    return v7

    .line 172
    :cond_f
    :goto_7
    add-int/lit8 v8, v8, -0x4

    .line 173
    .line 174
    invoke-interface {p1, v8}, LN1/s;->m(I)V

    .line 175
    .line 176
    .line 177
    goto :goto_3
.end method
