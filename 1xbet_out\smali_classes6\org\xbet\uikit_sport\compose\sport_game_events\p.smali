.class public final Lorg/xbet/uikit_sport/compose/sport_game_events/p;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u000f\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u001a5\u0010\u0008\u001a\u00020\u00062\u0006\u0010\u0001\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004H\u0001\u00a2\u0006\u0004\u0008\u0008\u0010\t\u001a=\u0010\u000c\u001a\u00020\u00062\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u000b\u001a\u00020\n2\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004H\u0001\u00a2\u0006\u0004\u0008\u000c\u0010\r\u001a=\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u000e\u001a\u00020\n2\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004H\u0001\u00a2\u0006\u0004\u0008\u000f\u0010\r\u001a/\u0010\u0014\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\n2\u0006\u0010\u0013\u001a\u00020\u0005H\u0001\u00a2\u0006\u0004\u0008\u0014\u0010\u0015\u001a/\u0010\u0017\u001a\u00020\u00062\u0006\u0010\u0016\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\n2\u0006\u0010\u0013\u001a\u00020\u0005H\u0001\u00a2\u0006\u0004\u0008\u0017\u0010\u0015\u001a7\u0010\u001b\u001a\u00020\u00062\u0006\u0010\u0018\u001a\u00020\u00052\u0006\u0010\u0019\u001a\u00020\u00052\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u000c\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u001aH\u0003\u00a2\u0006\u0004\u0008\u001b\u0010\u001c\u001a?\u0010\u001e\u001a\u00020\u00062\u0006\u0010\u0018\u001a\u00020\u00052\u0006\u0010\u0019\u001a\u00020\u00052\u0006\u0010\u001d\u001a\u00020\n2\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u000c\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u001aH\u0003\u00a2\u0006\u0004\u0008\u001e\u0010\u001f\u001a7\u0010 \u001a\u00020\u00062\u0006\u0010\u0018\u001a\u00020\u00052\u0006\u0010\u0019\u001a\u00020\u00052\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u000c\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u001aH\u0003\u00a2\u0006\u0004\u0008 \u0010\u001c\u001a?\u0010!\u001a\u00020\u00062\u0006\u0010\u0018\u001a\u00020\u00052\u0006\u0010\u0019\u001a\u00020\u00052\u0006\u0010\u001d\u001a\u00020\n2\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u000c\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u001aH\u0003\u00a2\u0006\u0004\u0008!\u0010\u001f\u00a8\u0006\""
    }
    d2 = {
        "Ls31/a$b;",
        "gameEventUiModel",
        "Landroidx/compose/ui/l;",
        "modifier",
        "Lkotlin/Function1;",
        "",
        "",
        "onPlayerClick",
        "n",
        "(Ls31/a$b;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V",
        "",
        "isLeftPlayer",
        "t",
        "(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V",
        "isRightPlayer",
        "F",
        "isEventLeft",
        "isChange",
        "isAssistant",
        "image",
        "p",
        "(ZZZLjava/lang/String;Landroidx/compose/runtime/j;I)V",
        "isEventRight",
        "r",
        "playerName",
        "playerImage",
        "Lkotlin/Function0;",
        "x",
        "(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V",
        "isChangePlayer",
        "y",
        "(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V",
        "B",
        "C",
        "uikit_sport_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final A(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 8

    .line 1
    or-int/lit8 p5, p5, 0x1

    invoke-static {p5}, Landroidx/compose/runtime/A0;->a(I)I

    move-result v6

    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p4

    move v7, p6

    move-object v5, p7

    invoke-static/range {v0 .. v7}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->y(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V

    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p0
.end method

.method public static final B(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V
    .locals 31
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Landroidx/compose/ui/l;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    move/from16 v5, p5

    .line 2
    .line 3
    const v0, 0x2160458

    .line 4
    .line 5
    .line 6
    move-object/from16 v1, p4

    .line 7
    .line 8
    invoke-interface {v1, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    and-int/lit8 v2, p6, 0x1

    .line 13
    .line 14
    if-eqz v2, :cond_0

    .line 15
    .line 16
    or-int/lit8 v2, v5, 0x6

    .line 17
    .line 18
    move v3, v2

    .line 19
    move-object/from16 v2, p0

    .line 20
    .line 21
    goto :goto_1

    .line 22
    :cond_0
    and-int/lit8 v2, v5, 0x6

    .line 23
    .line 24
    if-nez v2, :cond_2

    .line 25
    .line 26
    move-object/from16 v2, p0

    .line 27
    .line 28
    invoke-interface {v1, v2}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 29
    .line 30
    .line 31
    move-result v3

    .line 32
    if-eqz v3, :cond_1

    .line 33
    .line 34
    const/4 v3, 0x4

    .line 35
    goto :goto_0

    .line 36
    :cond_1
    const/4 v3, 0x2

    .line 37
    :goto_0
    or-int/2addr v3, v5

    .line 38
    goto :goto_1

    .line 39
    :cond_2
    move-object/from16 v2, p0

    .line 40
    .line 41
    move v3, v5

    .line 42
    :goto_1
    and-int/lit8 v4, p6, 0x2

    .line 43
    .line 44
    if-eqz v4, :cond_3

    .line 45
    .line 46
    or-int/lit8 v3, v3, 0x30

    .line 47
    .line 48
    move-object/from16 v6, p1

    .line 49
    .line 50
    goto :goto_3

    .line 51
    :cond_3
    and-int/lit8 v4, v5, 0x30

    .line 52
    .line 53
    move-object/from16 v6, p1

    .line 54
    .line 55
    if-nez v4, :cond_5

    .line 56
    .line 57
    invoke-interface {v1, v6}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 58
    .line 59
    .line 60
    move-result v4

    .line 61
    if-eqz v4, :cond_4

    .line 62
    .line 63
    const/16 v4, 0x20

    .line 64
    .line 65
    goto :goto_2

    .line 66
    :cond_4
    const/16 v4, 0x10

    .line 67
    .line 68
    :goto_2
    or-int/2addr v3, v4

    .line 69
    :cond_5
    :goto_3
    and-int/lit8 v4, p6, 0x4

    .line 70
    .line 71
    if-eqz v4, :cond_7

    .line 72
    .line 73
    or-int/lit16 v3, v3, 0x180

    .line 74
    .line 75
    :cond_6
    move-object/from16 v7, p2

    .line 76
    .line 77
    goto :goto_5

    .line 78
    :cond_7
    and-int/lit16 v7, v5, 0x180

    .line 79
    .line 80
    if-nez v7, :cond_6

    .line 81
    .line 82
    move-object/from16 v7, p2

    .line 83
    .line 84
    invoke-interface {v1, v7}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 85
    .line 86
    .line 87
    move-result v8

    .line 88
    if-eqz v8, :cond_8

    .line 89
    .line 90
    const/16 v8, 0x100

    .line 91
    .line 92
    goto :goto_4

    .line 93
    :cond_8
    const/16 v8, 0x80

    .line 94
    .line 95
    :goto_4
    or-int/2addr v3, v8

    .line 96
    :goto_5
    and-int/lit8 v8, p6, 0x8

    .line 97
    .line 98
    if-eqz v8, :cond_9

    .line 99
    .line 100
    or-int/lit16 v3, v3, 0xc00

    .line 101
    .line 102
    move-object/from16 v15, p3

    .line 103
    .line 104
    goto :goto_7

    .line 105
    :cond_9
    and-int/lit16 v8, v5, 0xc00

    .line 106
    .line 107
    move-object/from16 v15, p3

    .line 108
    .line 109
    if-nez v8, :cond_b

    .line 110
    .line 111
    invoke-interface {v1, v15}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 112
    .line 113
    .line 114
    move-result v8

    .line 115
    if-eqz v8, :cond_a

    .line 116
    .line 117
    const/16 v8, 0x800

    .line 118
    .line 119
    goto :goto_6

    .line 120
    :cond_a
    const/16 v8, 0x400

    .line 121
    .line 122
    :goto_6
    or-int/2addr v3, v8

    .line 123
    :cond_b
    :goto_7
    and-int/lit16 v8, v3, 0x493

    .line 124
    .line 125
    const/16 v9, 0x492

    .line 126
    .line 127
    if-ne v8, v9, :cond_d

    .line 128
    .line 129
    invoke-interface {v1}, Landroidx/compose/runtime/j;->c()Z

    .line 130
    .line 131
    .line 132
    move-result v8

    .line 133
    if-nez v8, :cond_c

    .line 134
    .line 135
    goto :goto_8

    .line 136
    :cond_c
    invoke-interface {v1}, Landroidx/compose/runtime/j;->n()V

    .line 137
    .line 138
    .line 139
    move-object/from16 v27, v1

    .line 140
    .line 141
    move-object v3, v7

    .line 142
    goto/16 :goto_b

    .line 143
    .line 144
    :cond_d
    :goto_8
    if-eqz v4, :cond_e

    .line 145
    .line 146
    sget-object v4, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 147
    .line 148
    move-object v9, v4

    .line 149
    goto :goto_9

    .line 150
    :cond_e
    move-object v9, v7

    .line 151
    :goto_9
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 152
    .line 153
    .line 154
    move-result v4

    .line 155
    if-eqz v4, :cond_f

    .line 156
    .line 157
    const/4 v4, -0x1

    .line 158
    const-string v7, "org.xbet.uikit_sport.compose.sport_game_events.PlayerEventRight (EventComponent.kt:288)"

    .line 159
    .line 160
    invoke-static {v0, v3, v4, v7}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 161
    .line 162
    .line 163
    :cond_f
    const/16 v16, 0x1c

    .line 164
    .line 165
    const/16 v17, 0x0

    .line 166
    .line 167
    const/4 v10, 0x0

    .line 168
    const/4 v11, 0x0

    .line 169
    const/4 v12, 0x0

    .line 170
    const/4 v13, 0x0

    .line 171
    const/4 v14, 0x0

    .line 172
    invoke-static/range {v9 .. v17}, Landroidx/compose/foundation/ClickableKt;->d(Landroidx/compose/ui/l;Landroidx/compose/foundation/interaction/i;Landroidx/compose/foundation/E;ZLjava/lang/String;Landroidx/compose/ui/semantics/i;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 173
    .line 174
    .line 175
    move-result-object v0

    .line 176
    move-object v4, v9

    .line 177
    sget-object v7, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 178
    .line 179
    invoke-virtual {v7}, Landroidx/compose/ui/e$a;->i()Landroidx/compose/ui/e$c;

    .line 180
    .line 181
    .line 182
    move-result-object v7

    .line 183
    sget-object v8, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 184
    .line 185
    invoke-virtual {v8}, Landroidx/compose/foundation/layout/Arrangement;->g()Landroidx/compose/foundation/layout/Arrangement$e;

    .line 186
    .line 187
    .line 188
    move-result-object v8

    .line 189
    const/16 v9, 0x30

    .line 190
    .line 191
    invoke-static {v8, v7, v1, v9}, Landroidx/compose/foundation/layout/h0;->b(Landroidx/compose/foundation/layout/Arrangement$e;Landroidx/compose/ui/e$c;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 192
    .line 193
    .line 194
    move-result-object v7

    .line 195
    const/4 v8, 0x0

    .line 196
    invoke-static {v1, v8}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 197
    .line 198
    .line 199
    move-result v10

    .line 200
    invoke-interface {v1}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 201
    .line 202
    .line 203
    move-result-object v11

    .line 204
    invoke-static {v1, v0}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 205
    .line 206
    .line 207
    move-result-object v0

    .line 208
    sget-object v12, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 209
    .line 210
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 211
    .line 212
    .line 213
    move-result-object v13

    .line 214
    invoke-interface {v1}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 215
    .line 216
    .line 217
    move-result-object v14

    .line 218
    invoke-static {v14}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 219
    .line 220
    .line 221
    move-result v14

    .line 222
    if-nez v14, :cond_10

    .line 223
    .line 224
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 225
    .line 226
    .line 227
    :cond_10
    invoke-interface {v1}, Landroidx/compose/runtime/j;->l()V

    .line 228
    .line 229
    .line 230
    invoke-interface {v1}, Landroidx/compose/runtime/j;->B()Z

    .line 231
    .line 232
    .line 233
    move-result v14

    .line 234
    if-eqz v14, :cond_11

    .line 235
    .line 236
    invoke-interface {v1, v13}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 237
    .line 238
    .line 239
    goto :goto_a

    .line 240
    :cond_11
    invoke-interface {v1}, Landroidx/compose/runtime/j;->h()V

    .line 241
    .line 242
    .line 243
    :goto_a
    invoke-static {v1}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 244
    .line 245
    .line 246
    move-result-object v13

    .line 247
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 248
    .line 249
    .line 250
    move-result-object v14

    .line 251
    invoke-static {v13, v7, v14}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 252
    .line 253
    .line 254
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 255
    .line 256
    .line 257
    move-result-object v7

    .line 258
    invoke-static {v13, v11, v7}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 259
    .line 260
    .line 261
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 262
    .line 263
    .line 264
    move-result-object v7

    .line 265
    invoke-interface {v13}, Landroidx/compose/runtime/j;->B()Z

    .line 266
    .line 267
    .line 268
    move-result v11

    .line 269
    if-nez v11, :cond_12

    .line 270
    .line 271
    invoke-interface {v13}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 272
    .line 273
    .line 274
    move-result-object v11

    .line 275
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 276
    .line 277
    .line 278
    move-result-object v14

    .line 279
    invoke-static {v11, v14}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 280
    .line 281
    .line 282
    move-result v11

    .line 283
    if-nez v11, :cond_13

    .line 284
    .line 285
    :cond_12
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 286
    .line 287
    .line 288
    move-result-object v11

    .line 289
    invoke-interface {v13, v11}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 290
    .line 291
    .line 292
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 293
    .line 294
    .line 295
    move-result-object v10

    .line 296
    invoke-interface {v13, v10, v7}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 297
    .line 298
    .line 299
    :cond_13
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 300
    .line 301
    .line 302
    move-result-object v7

    .line 303
    invoke-static {v13, v0, v7}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 304
    .line 305
    .line 306
    sget-object v0, Landroidx/compose/foundation/layout/k0;->a:Landroidx/compose/foundation/layout/k0;

    .line 307
    .line 308
    sget v7, LlZ0/h;->ic_glyph_placeholder_player_secondary60:I

    .line 309
    .line 310
    invoke-static {v7, v1, v8}, Lm0/d;->c(ILandroidx/compose/runtime/j;I)Landroidx/compose/ui/graphics/painter/Painter;

    .line 311
    .line 312
    .line 313
    move-result-object v7

    .line 314
    sget-object v10, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 315
    .line 316
    sget-object v25, LA11/a;->a:LA11/a;

    .line 317
    .line 318
    invoke-virtual/range {v25 .. v25}, LA11/a;->o0()F

    .line 319
    .line 320
    .line 321
    move-result v11

    .line 322
    invoke-static {v10, v11}, Landroidx/compose/foundation/layout/SizeKt;->v(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 323
    .line 324
    .line 325
    move-result-object v11

    .line 326
    invoke-virtual/range {v25 .. v25}, LA11/a;->Q0()F

    .line 327
    .line 328
    .line 329
    move-result v12

    .line 330
    invoke-static {v12}, LR/i;->f(F)LR/h;

    .line 331
    .line 332
    .line 333
    move-result-object v12

    .line 334
    invoke-static {v11, v12}, Landroidx/compose/ui/draw/d;->a(Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    .line 335
    .line 336
    .line 337
    move-result-object v11

    .line 338
    shr-int/lit8 v12, v3, 0x3

    .line 339
    .line 340
    and-int/lit8 v12, v12, 0xe

    .line 341
    .line 342
    or-int/lit8 v22, v12, 0x30

    .line 343
    .line 344
    const/16 v23, 0x0

    .line 345
    .line 346
    const/16 v24, 0x7fe0

    .line 347
    .line 348
    move-object v9, v7

    .line 349
    const/4 v7, 0x0

    .line 350
    move-object v8, v11

    .line 351
    const/4 v12, 0x0

    .line 352
    const/4 v11, 0x0

    .line 353
    const/4 v13, 0x0

    .line 354
    const/4 v12, 0x0

    .line 355
    const/4 v14, 0x0

    .line 356
    const/4 v13, 0x0

    .line 357
    const/4 v15, 0x0

    .line 358
    const/4 v14, 0x0

    .line 359
    const/16 v16, 0x0

    .line 360
    .line 361
    const/4 v15, 0x0

    .line 362
    const/16 v17, 0x0

    .line 363
    .line 364
    const/16 v16, 0x0

    .line 365
    .line 366
    const/16 v18, 0x0

    .line 367
    .line 368
    const/16 v17, 0x0

    .line 369
    .line 370
    const/16 v19, 0x0

    .line 371
    .line 372
    const/16 v18, 0x0

    .line 373
    .line 374
    const/16 v20, 0x0

    .line 375
    .line 376
    const/16 v19, 0x0

    .line 377
    .line 378
    const/16 v21, 0x0

    .line 379
    .line 380
    const/16 v20, 0x0

    .line 381
    .line 382
    move-object/from16 v26, v10

    .line 383
    .line 384
    move-object v10, v9

    .line 385
    move-object/from16 v21, v1

    .line 386
    .line 387
    const/4 v1, 0x0

    .line 388
    invoke-static/range {v6 .. v24}, Lcoil3/compose/r;->b(Ljava/lang/Object;Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/e;Landroidx/compose/ui/layout/h;FLandroidx/compose/ui/graphics/w0;IZLandroidx/compose/runtime/j;III)V

    .line 389
    .line 390
    .line 391
    move-object/from16 v6, v21

    .line 392
    .line 393
    const/16 v18, 0x2

    .line 394
    .line 395
    const/16 v19, 0x0

    .line 396
    .line 397
    const/high16 v16, 0x3f800000    # 1.0f

    .line 398
    .line 399
    const/16 v17, 0x0

    .line 400
    .line 401
    move-object v14, v0

    .line 402
    move-object/from16 v15, v26

    .line 403
    .line 404
    invoke-static/range {v14 .. v19}, Landroidx/compose/foundation/layout/i0;->a(Landroidx/compose/foundation/layout/j0;Landroidx/compose/ui/l;FZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 405
    .line 406
    .line 407
    move-result-object v7

    .line 408
    invoke-virtual/range {v25 .. v25}, LA11/a;->L1()F

    .line 409
    .line 410
    .line 411
    move-result v8

    .line 412
    const/16 v12, 0xe

    .line 413
    .line 414
    const/4 v9, 0x0

    .line 415
    const/4 v10, 0x0

    .line 416
    const/4 v11, 0x0

    .line 417
    invoke-static/range {v7 .. v13}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 418
    .line 419
    .line 420
    move-result-object v7

    .line 421
    sget-object v0, LC11/a;->a:LC11/a;

    .line 422
    .line 423
    invoke-virtual {v0}, LC11/a;->e()Landroidx/compose/ui/text/a0;

    .line 424
    .line 425
    .line 426
    move-result-object v0

    .line 427
    invoke-static {v0, v6, v1}, LB11/a;->d(Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/text/a0;

    .line 428
    .line 429
    .line 430
    move-result-object v26

    .line 431
    sget-object v0, Landroidx/compose/ui/text/style/i;->b:Landroidx/compose/ui/text/style/i$a;

    .line 432
    .line 433
    invoke-virtual {v0}, Landroidx/compose/ui/text/style/i$a;->f()I

    .line 434
    .line 435
    .line 436
    move-result v0

    .line 437
    sget-object v1, Landroidx/compose/ui/text/style/s;->b:Landroidx/compose/ui/text/style/s$a;

    .line 438
    .line 439
    invoke-virtual {v1}, Landroidx/compose/ui/text/style/s$a;->b()I

    .line 440
    .line 441
    .line 442
    move-result v21

    .line 443
    invoke-static {v0}, Landroidx/compose/ui/text/style/i;->h(I)Landroidx/compose/ui/text/style/i;

    .line 444
    .line 445
    .line 446
    move-result-object v18

    .line 447
    and-int/lit8 v28, v3, 0xe

    .line 448
    .line 449
    const/16 v29, 0xc30

    .line 450
    .line 451
    const v30, 0xd5fc

    .line 452
    .line 453
    .line 454
    const-wide/16 v8, 0x0

    .line 455
    .line 456
    const-wide/16 v10, 0x0

    .line 457
    .line 458
    const/4 v12, 0x0

    .line 459
    const/4 v14, 0x0

    .line 460
    const-wide/16 v15, 0x0

    .line 461
    .line 462
    const/16 v17, 0x0

    .line 463
    .line 464
    const-wide/16 v19, 0x0

    .line 465
    .line 466
    const/16 v22, 0x0

    .line 467
    .line 468
    const/16 v23, 0x2

    .line 469
    .line 470
    const/16 v24, 0x0

    .line 471
    .line 472
    const/16 v25, 0x0

    .line 473
    .line 474
    move-object/from16 v27, v6

    .line 475
    .line 476
    move-object v6, v2

    .line 477
    invoke-static/range {v6 .. v30}, Landroidx/compose/material3/TextKt;->c(Ljava/lang/String;Landroidx/compose/ui/l;JJLandroidx/compose/ui/text/font/t;Landroidx/compose/ui/text/font/y;Landroidx/compose/ui/text/font/j;JLandroidx/compose/ui/text/style/j;Landroidx/compose/ui/text/style/i;JIZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;III)V

    .line 478
    .line 479
    .line 480
    invoke-interface/range {v27 .. v27}, Landroidx/compose/runtime/j;->j()V

    .line 481
    .line 482
    .line 483
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 484
    .line 485
    .line 486
    move-result v0

    .line 487
    if-eqz v0, :cond_14

    .line 488
    .line 489
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 490
    .line 491
    .line 492
    :cond_14
    move-object v3, v4

    .line 493
    :goto_b
    invoke-interface/range {v27 .. v27}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 494
    .line 495
    .line 496
    move-result-object v7

    .line 497
    if-eqz v7, :cond_15

    .line 498
    .line 499
    new-instance v0, Lorg/xbet/uikit_sport/compose/sport_game_events/f;

    .line 500
    .line 501
    move-object/from16 v1, p0

    .line 502
    .line 503
    move-object/from16 v2, p1

    .line 504
    .line 505
    move-object/from16 v4, p3

    .line 506
    .line 507
    move/from16 v6, p6

    .line 508
    .line 509
    invoke-direct/range {v0 .. v6}, Lorg/xbet/uikit_sport/compose/sport_game_events/f;-><init>(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;II)V

    .line 510
    .line 511
    .line 512
    invoke-interface {v7, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 513
    .line 514
    .line 515
    :cond_15
    return-void
.end method

.method public static final C(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V
    .locals 32
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Z",
            "Landroidx/compose/ui/l;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    move/from16 v3, p2

    .line 2
    .line 3
    move/from16 v6, p6

    .line 4
    .line 5
    const v0, 0x7b2e82b4

    .line 6
    .line 7
    .line 8
    move-object/from16 v1, p5

    .line 9
    .line 10
    invoke-interface {v1, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    and-int/lit8 v2, p7, 0x1

    .line 15
    .line 16
    if-eqz v2, :cond_0

    .line 17
    .line 18
    or-int/lit8 v2, v6, 0x6

    .line 19
    .line 20
    move v4, v2

    .line 21
    move-object/from16 v2, p0

    .line 22
    .line 23
    goto :goto_1

    .line 24
    :cond_0
    and-int/lit8 v2, v6, 0x6

    .line 25
    .line 26
    if-nez v2, :cond_2

    .line 27
    .line 28
    move-object/from16 v2, p0

    .line 29
    .line 30
    invoke-interface {v1, v2}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 31
    .line 32
    .line 33
    move-result v4

    .line 34
    if-eqz v4, :cond_1

    .line 35
    .line 36
    const/4 v4, 0x4

    .line 37
    goto :goto_0

    .line 38
    :cond_1
    const/4 v4, 0x2

    .line 39
    :goto_0
    or-int/2addr v4, v6

    .line 40
    goto :goto_1

    .line 41
    :cond_2
    move-object/from16 v2, p0

    .line 42
    .line 43
    move v4, v6

    .line 44
    :goto_1
    and-int/lit8 v5, p7, 0x2

    .line 45
    .line 46
    if-eqz v5, :cond_3

    .line 47
    .line 48
    or-int/lit8 v4, v4, 0x30

    .line 49
    .line 50
    move-object/from16 v7, p1

    .line 51
    .line 52
    goto :goto_3

    .line 53
    :cond_3
    and-int/lit8 v5, v6, 0x30

    .line 54
    .line 55
    move-object/from16 v7, p1

    .line 56
    .line 57
    if-nez v5, :cond_5

    .line 58
    .line 59
    invoke-interface {v1, v7}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 60
    .line 61
    .line 62
    move-result v5

    .line 63
    if-eqz v5, :cond_4

    .line 64
    .line 65
    const/16 v5, 0x20

    .line 66
    .line 67
    goto :goto_2

    .line 68
    :cond_4
    const/16 v5, 0x10

    .line 69
    .line 70
    :goto_2
    or-int/2addr v4, v5

    .line 71
    :cond_5
    :goto_3
    and-int/lit8 v5, p7, 0x4

    .line 72
    .line 73
    if-eqz v5, :cond_6

    .line 74
    .line 75
    or-int/lit16 v4, v4, 0x180

    .line 76
    .line 77
    goto :goto_5

    .line 78
    :cond_6
    and-int/lit16 v5, v6, 0x180

    .line 79
    .line 80
    if-nez v5, :cond_8

    .line 81
    .line 82
    invoke-interface {v1, v3}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 83
    .line 84
    .line 85
    move-result v5

    .line 86
    if-eqz v5, :cond_7

    .line 87
    .line 88
    const/16 v5, 0x100

    .line 89
    .line 90
    goto :goto_4

    .line 91
    :cond_7
    const/16 v5, 0x80

    .line 92
    .line 93
    :goto_4
    or-int/2addr v4, v5

    .line 94
    :cond_8
    :goto_5
    and-int/lit8 v5, p7, 0x8

    .line 95
    .line 96
    if-eqz v5, :cond_a

    .line 97
    .line 98
    or-int/lit16 v4, v4, 0xc00

    .line 99
    .line 100
    :cond_9
    move-object/from16 v8, p3

    .line 101
    .line 102
    goto :goto_7

    .line 103
    :cond_a
    and-int/lit16 v8, v6, 0xc00

    .line 104
    .line 105
    if-nez v8, :cond_9

    .line 106
    .line 107
    move-object/from16 v8, p3

    .line 108
    .line 109
    invoke-interface {v1, v8}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 110
    .line 111
    .line 112
    move-result v9

    .line 113
    if-eqz v9, :cond_b

    .line 114
    .line 115
    const/16 v9, 0x800

    .line 116
    .line 117
    goto :goto_6

    .line 118
    :cond_b
    const/16 v9, 0x400

    .line 119
    .line 120
    :goto_6
    or-int/2addr v4, v9

    .line 121
    :goto_7
    and-int/lit8 v9, p7, 0x10

    .line 122
    .line 123
    if-eqz v9, :cond_d

    .line 124
    .line 125
    or-int/lit16 v4, v4, 0x6000

    .line 126
    .line 127
    :cond_c
    move-object/from16 v9, p4

    .line 128
    .line 129
    goto :goto_9

    .line 130
    :cond_d
    and-int/lit16 v9, v6, 0x6000

    .line 131
    .line 132
    if-nez v9, :cond_c

    .line 133
    .line 134
    move-object/from16 v9, p4

    .line 135
    .line 136
    invoke-interface {v1, v9}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 137
    .line 138
    .line 139
    move-result v10

    .line 140
    if-eqz v10, :cond_e

    .line 141
    .line 142
    const/16 v10, 0x4000

    .line 143
    .line 144
    goto :goto_8

    .line 145
    :cond_e
    const/16 v10, 0x2000

    .line 146
    .line 147
    :goto_8
    or-int/2addr v4, v10

    .line 148
    :goto_9
    and-int/lit16 v10, v4, 0x2493

    .line 149
    .line 150
    const/16 v11, 0x2492

    .line 151
    .line 152
    if-ne v10, v11, :cond_10

    .line 153
    .line 154
    invoke-interface {v1}, Landroidx/compose/runtime/j;->c()Z

    .line 155
    .line 156
    .line 157
    move-result v10

    .line 158
    if-nez v10, :cond_f

    .line 159
    .line 160
    goto :goto_a

    .line 161
    :cond_f
    invoke-interface {v1}, Landroidx/compose/runtime/j;->n()V

    .line 162
    .line 163
    .line 164
    move-object/from16 v28, v1

    .line 165
    .line 166
    move-object v4, v8

    .line 167
    goto/16 :goto_f

    .line 168
    .line 169
    :cond_10
    :goto_a
    if-eqz v5, :cond_11

    .line 170
    .line 171
    sget-object v5, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 172
    .line 173
    move-object v10, v5

    .line 174
    goto :goto_b

    .line 175
    :cond_11
    move-object v10, v8

    .line 176
    :goto_b
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 177
    .line 178
    .line 179
    move-result v5

    .line 180
    if-eqz v5, :cond_12

    .line 181
    .line 182
    const/4 v5, -0x1

    .line 183
    const-string v8, "org.xbet.uikit_sport.compose.sport_game_events.PlayerEventRight (EventComponent.kt:323)"

    .line 184
    .line 185
    invoke-static {v0, v4, v5, v8}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 186
    .line 187
    .line 188
    :cond_12
    const/16 v17, 0x1c

    .line 189
    .line 190
    const/16 v18, 0x0

    .line 191
    .line 192
    const/4 v11, 0x0

    .line 193
    const/4 v12, 0x0

    .line 194
    const/4 v13, 0x0

    .line 195
    const/4 v14, 0x0

    .line 196
    const/4 v15, 0x0

    .line 197
    move-object/from16 v16, v9

    .line 198
    .line 199
    invoke-static/range {v10 .. v18}, Landroidx/compose/foundation/ClickableKt;->d(Landroidx/compose/ui/l;Landroidx/compose/foundation/interaction/i;Landroidx/compose/foundation/E;ZLjava/lang/String;Landroidx/compose/ui/semantics/i;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 200
    .line 201
    .line 202
    move-result-object v0

    .line 203
    move-object v5, v10

    .line 204
    sget-object v8, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 205
    .line 206
    invoke-virtual {v8}, Landroidx/compose/ui/e$a;->i()Landroidx/compose/ui/e$c;

    .line 207
    .line 208
    .line 209
    move-result-object v8

    .line 210
    sget-object v9, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 211
    .line 212
    invoke-virtual {v9}, Landroidx/compose/foundation/layout/Arrangement;->g()Landroidx/compose/foundation/layout/Arrangement$e;

    .line 213
    .line 214
    .line 215
    move-result-object v9

    .line 216
    const/16 v10, 0x30

    .line 217
    .line 218
    invoke-static {v9, v8, v1, v10}, Landroidx/compose/foundation/layout/h0;->b(Landroidx/compose/foundation/layout/Arrangement$e;Landroidx/compose/ui/e$c;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 219
    .line 220
    .line 221
    move-result-object v8

    .line 222
    const/4 v9, 0x0

    .line 223
    invoke-static {v1, v9}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 224
    .line 225
    .line 226
    move-result v11

    .line 227
    invoke-interface {v1}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 228
    .line 229
    .line 230
    move-result-object v12

    .line 231
    invoke-static {v1, v0}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 232
    .line 233
    .line 234
    move-result-object v0

    .line 235
    sget-object v13, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 236
    .line 237
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 238
    .line 239
    .line 240
    move-result-object v14

    .line 241
    invoke-interface {v1}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 242
    .line 243
    .line 244
    move-result-object v15

    .line 245
    invoke-static {v15}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 246
    .line 247
    .line 248
    move-result v15

    .line 249
    if-nez v15, :cond_13

    .line 250
    .line 251
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 252
    .line 253
    .line 254
    :cond_13
    invoke-interface {v1}, Landroidx/compose/runtime/j;->l()V

    .line 255
    .line 256
    .line 257
    invoke-interface {v1}, Landroidx/compose/runtime/j;->B()Z

    .line 258
    .line 259
    .line 260
    move-result v15

    .line 261
    if-eqz v15, :cond_14

    .line 262
    .line 263
    invoke-interface {v1, v14}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 264
    .line 265
    .line 266
    goto :goto_c

    .line 267
    :cond_14
    invoke-interface {v1}, Landroidx/compose/runtime/j;->h()V

    .line 268
    .line 269
    .line 270
    :goto_c
    invoke-static {v1}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 271
    .line 272
    .line 273
    move-result-object v14

    .line 274
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 275
    .line 276
    .line 277
    move-result-object v15

    .line 278
    invoke-static {v14, v8, v15}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 279
    .line 280
    .line 281
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 282
    .line 283
    .line 284
    move-result-object v8

    .line 285
    invoke-static {v14, v12, v8}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 286
    .line 287
    .line 288
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 289
    .line 290
    .line 291
    move-result-object v8

    .line 292
    invoke-interface {v14}, Landroidx/compose/runtime/j;->B()Z

    .line 293
    .line 294
    .line 295
    move-result v12

    .line 296
    if-nez v12, :cond_15

    .line 297
    .line 298
    invoke-interface {v14}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 299
    .line 300
    .line 301
    move-result-object v12

    .line 302
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 303
    .line 304
    .line 305
    move-result-object v15

    .line 306
    invoke-static {v12, v15}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 307
    .line 308
    .line 309
    move-result v12

    .line 310
    if-nez v12, :cond_16

    .line 311
    .line 312
    :cond_15
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 313
    .line 314
    .line 315
    move-result-object v12

    .line 316
    invoke-interface {v14, v12}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 317
    .line 318
    .line 319
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 320
    .line 321
    .line 322
    move-result-object v11

    .line 323
    invoke-interface {v14, v11, v8}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 324
    .line 325
    .line 326
    :cond_16
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 327
    .line 328
    .line 329
    move-result-object v8

    .line 330
    invoke-static {v14, v0, v8}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 331
    .line 332
    .line 333
    sget-object v0, Landroidx/compose/foundation/layout/k0;->a:Landroidx/compose/foundation/layout/k0;

    .line 334
    .line 335
    sget v8, LlZ0/h;->ic_glyph_placeholder_player_secondary60:I

    .line 336
    .line 337
    invoke-static {v8, v1, v9}, Lm0/d;->c(ILandroidx/compose/runtime/j;I)Landroidx/compose/ui/graphics/painter/Painter;

    .line 338
    .line 339
    .line 340
    move-result-object v8

    .line 341
    sget-object v11, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 342
    .line 343
    sget-object v26, LA11/a;->a:LA11/a;

    .line 344
    .line 345
    invoke-virtual/range {v26 .. v26}, LA11/a;->o0()F

    .line 346
    .line 347
    .line 348
    move-result v12

    .line 349
    invoke-static {v11, v12}, Landroidx/compose/foundation/layout/SizeKt;->v(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 350
    .line 351
    .line 352
    move-result-object v12

    .line 353
    invoke-virtual/range {v26 .. v26}, LA11/a;->Q0()F

    .line 354
    .line 355
    .line 356
    move-result v13

    .line 357
    invoke-static {v13}, LR/i;->f(F)LR/h;

    .line 358
    .line 359
    .line 360
    move-result-object v13

    .line 361
    invoke-static {v12, v13}, Landroidx/compose/ui/draw/d;->a(Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    .line 362
    .line 363
    .line 364
    move-result-object v12

    .line 365
    const/high16 v27, 0x3f800000    # 1.0f

    .line 366
    .line 367
    const/high16 v28, 0x3f000000    # 0.5f

    .line 368
    .line 369
    if-eqz v3, :cond_17

    .line 370
    .line 371
    const/high16 v13, 0x3f000000    # 0.5f

    .line 372
    .line 373
    goto :goto_d

    .line 374
    :cond_17
    const/high16 v13, 0x3f800000    # 1.0f

    .line 375
    .line 376
    :goto_d
    invoke-static {v12, v13}, Landroidx/compose/ui/draw/a;->a(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 377
    .line 378
    .line 379
    move-result-object v12

    .line 380
    shr-int/lit8 v13, v4, 0x3

    .line 381
    .line 382
    and-int/lit8 v13, v13, 0xe

    .line 383
    .line 384
    or-int/lit8 v23, v13, 0x30

    .line 385
    .line 386
    const/16 v24, 0x0

    .line 387
    .line 388
    const/16 v25, 0x7fe0

    .line 389
    .line 390
    move-object v10, v8

    .line 391
    const/4 v8, 0x0

    .line 392
    move-object v9, v12

    .line 393
    const/4 v13, 0x0

    .line 394
    const/4 v12, 0x0

    .line 395
    const/4 v14, 0x0

    .line 396
    const/4 v13, 0x0

    .line 397
    const/4 v15, 0x0

    .line 398
    const/4 v14, 0x0

    .line 399
    const/16 v16, 0x0

    .line 400
    .line 401
    const/4 v15, 0x0

    .line 402
    const/16 v17, 0x0

    .line 403
    .line 404
    const/16 v16, 0x0

    .line 405
    .line 406
    const/16 v18, 0x0

    .line 407
    .line 408
    const/16 v17, 0x0

    .line 409
    .line 410
    const/16 v19, 0x0

    .line 411
    .line 412
    const/16 v18, 0x0

    .line 413
    .line 414
    const/16 v20, 0x0

    .line 415
    .line 416
    const/16 v19, 0x0

    .line 417
    .line 418
    const/16 v21, 0x0

    .line 419
    .line 420
    const/16 v20, 0x0

    .line 421
    .line 422
    const/16 v22, 0x0

    .line 423
    .line 424
    const/16 v21, 0x0

    .line 425
    .line 426
    move-object/from16 v29, v11

    .line 427
    .line 428
    move-object v11, v10

    .line 429
    move-object/from16 v22, v1

    .line 430
    .line 431
    const/4 v1, 0x0

    .line 432
    invoke-static/range {v7 .. v25}, Lcoil3/compose/r;->b(Ljava/lang/Object;Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/e;Landroidx/compose/ui/layout/h;FLandroidx/compose/ui/graphics/w0;IZLandroidx/compose/runtime/j;III)V

    .line 433
    .line 434
    .line 435
    move-object/from16 v7, v22

    .line 436
    .line 437
    const/16 v19, 0x2

    .line 438
    .line 439
    const/16 v20, 0x0

    .line 440
    .line 441
    const/high16 v17, 0x3f800000    # 1.0f

    .line 442
    .line 443
    const/16 v18, 0x0

    .line 444
    .line 445
    move-object v15, v0

    .line 446
    move-object/from16 v16, v29

    .line 447
    .line 448
    invoke-static/range {v15 .. v20}, Landroidx/compose/foundation/layout/i0;->a(Landroidx/compose/foundation/layout/j0;Landroidx/compose/ui/l;FZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 449
    .line 450
    .line 451
    move-result-object v8

    .line 452
    invoke-virtual/range {v26 .. v26}, LA11/a;->L1()F

    .line 453
    .line 454
    .line 455
    move-result v9

    .line 456
    const/16 v13, 0xe

    .line 457
    .line 458
    const/4 v10, 0x0

    .line 459
    const/4 v11, 0x0

    .line 460
    const/4 v12, 0x0

    .line 461
    invoke-static/range {v8 .. v14}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 462
    .line 463
    .line 464
    move-result-object v0

    .line 465
    if-eqz v3, :cond_18

    .line 466
    .line 467
    const/high16 v8, 0x3f000000    # 0.5f

    .line 468
    .line 469
    goto :goto_e

    .line 470
    :cond_18
    const/high16 v8, 0x3f800000    # 1.0f

    .line 471
    .line 472
    :goto_e
    invoke-static {v0, v8}, Landroidx/compose/ui/draw/a;->a(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 473
    .line 474
    .line 475
    move-result-object v8

    .line 476
    sget-object v0, LC11/a;->a:LC11/a;

    .line 477
    .line 478
    invoke-virtual {v0}, LC11/a;->e()Landroidx/compose/ui/text/a0;

    .line 479
    .line 480
    .line 481
    move-result-object v0

    .line 482
    invoke-static {v0, v7, v1}, LB11/a;->d(Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/text/a0;

    .line 483
    .line 484
    .line 485
    move-result-object v27

    .line 486
    sget-object v0, Landroidx/compose/ui/text/style/i;->b:Landroidx/compose/ui/text/style/i$a;

    .line 487
    .line 488
    invoke-virtual {v0}, Landroidx/compose/ui/text/style/i$a;->f()I

    .line 489
    .line 490
    .line 491
    move-result v0

    .line 492
    sget-object v1, Landroidx/compose/ui/text/style/s;->b:Landroidx/compose/ui/text/style/s$a;

    .line 493
    .line 494
    invoke-virtual {v1}, Landroidx/compose/ui/text/style/s$a;->b()I

    .line 495
    .line 496
    .line 497
    move-result v22

    .line 498
    invoke-static {v0}, Landroidx/compose/ui/text/style/i;->h(I)Landroidx/compose/ui/text/style/i;

    .line 499
    .line 500
    .line 501
    move-result-object v19

    .line 502
    and-int/lit8 v29, v4, 0xe

    .line 503
    .line 504
    const/16 v30, 0xc30

    .line 505
    .line 506
    const v31, 0xd5fc

    .line 507
    .line 508
    .line 509
    const-wide/16 v9, 0x0

    .line 510
    .line 511
    const-wide/16 v11, 0x0

    .line 512
    .line 513
    const/4 v13, 0x0

    .line 514
    const/4 v14, 0x0

    .line 515
    const/4 v15, 0x0

    .line 516
    const-wide/16 v16, 0x0

    .line 517
    .line 518
    const/16 v18, 0x0

    .line 519
    .line 520
    const-wide/16 v20, 0x0

    .line 521
    .line 522
    const/16 v23, 0x0

    .line 523
    .line 524
    const/16 v24, 0x2

    .line 525
    .line 526
    const/16 v25, 0x0

    .line 527
    .line 528
    const/16 v26, 0x0

    .line 529
    .line 530
    move-object/from16 v28, v7

    .line 531
    .line 532
    move-object v7, v2

    .line 533
    invoke-static/range {v7 .. v31}, Landroidx/compose/material3/TextKt;->c(Ljava/lang/String;Landroidx/compose/ui/l;JJLandroidx/compose/ui/text/font/t;Landroidx/compose/ui/text/font/y;Landroidx/compose/ui/text/font/j;JLandroidx/compose/ui/text/style/j;Landroidx/compose/ui/text/style/i;JIZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;III)V

    .line 534
    .line 535
    .line 536
    invoke-interface/range {v28 .. v28}, Landroidx/compose/runtime/j;->j()V

    .line 537
    .line 538
    .line 539
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 540
    .line 541
    .line 542
    move-result v0

    .line 543
    if-eqz v0, :cond_19

    .line 544
    .line 545
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 546
    .line 547
    .line 548
    :cond_19
    move-object v4, v5

    .line 549
    :goto_f
    invoke-interface/range {v28 .. v28}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 550
    .line 551
    .line 552
    move-result-object v8

    .line 553
    if-eqz v8, :cond_1a

    .line 554
    .line 555
    new-instance v0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;

    .line 556
    .line 557
    move-object/from16 v1, p0

    .line 558
    .line 559
    move-object/from16 v2, p1

    .line 560
    .line 561
    move-object/from16 v5, p4

    .line 562
    .line 563
    move/from16 v7, p7

    .line 564
    .line 565
    invoke-direct/range {v0 .. v7}, Lorg/xbet/uikit_sport/compose/sport_game_events/d;-><init>(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;II)V

    .line 566
    .line 567
    .line 568
    invoke-interface {v8, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 569
    .line 570
    .line 571
    :cond_1a
    return-void
.end method

.method public static final D(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 7

    .line 1
    or-int/lit8 p4, p4, 0x1

    invoke-static {p4}, Landroidx/compose/runtime/A0;->a(I)I

    move-result v5

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move v6, p5

    move-object v4, p6

    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->B(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V

    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p0
.end method

.method public static final E(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 8

    .line 1
    or-int/lit8 p5, p5, 0x1

    invoke-static {p5}, Landroidx/compose/runtime/A0;->a(I)I

    move-result v6

    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p4

    move v7, p6

    move-object v5, p7

    invoke-static/range {v0 .. v7}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->C(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V

    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p0
.end method

.method public static final F(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V
    .locals 20
    .param p0    # Ls31/a$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ls31/a$b;",
            "Z",
            "Landroidx/compose/ui/l;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    move/from16 v2, p1

    .line 4
    .line 5
    move-object/from16 v4, p3

    .line 6
    .line 7
    move/from16 v5, p5

    .line 8
    .line 9
    const/4 v0, 0x2

    .line 10
    const v3, -0x3aad905c

    .line 11
    .line 12
    .line 13
    move-object/from16 v6, p4

    .line 14
    .line 15
    invoke-interface {v6, v3}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 16
    .line 17
    .line 18
    move-result-object v10

    .line 19
    const/4 v13, 0x1

    .line 20
    and-int/lit8 v6, p6, 0x1

    .line 21
    .line 22
    const/4 v14, 0x4

    .line 23
    if-eqz v6, :cond_0

    .line 24
    .line 25
    or-int/lit8 v6, v5, 0x6

    .line 26
    .line 27
    goto :goto_2

    .line 28
    :cond_0
    and-int/lit8 v6, v5, 0x6

    .line 29
    .line 30
    if-nez v6, :cond_3

    .line 31
    .line 32
    and-int/lit8 v6, v5, 0x8

    .line 33
    .line 34
    if-nez v6, :cond_1

    .line 35
    .line 36
    invoke-interface {v10, v1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 37
    .line 38
    .line 39
    move-result v6

    .line 40
    goto :goto_0

    .line 41
    :cond_1
    invoke-interface {v10, v1}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 42
    .line 43
    .line 44
    move-result v6

    .line 45
    :goto_0
    if-eqz v6, :cond_2

    .line 46
    .line 47
    const/4 v6, 0x4

    .line 48
    goto :goto_1

    .line 49
    :cond_2
    const/4 v6, 0x2

    .line 50
    :goto_1
    or-int/2addr v6, v5

    .line 51
    goto :goto_2

    .line 52
    :cond_3
    move v6, v5

    .line 53
    :goto_2
    and-int/lit8 v0, p6, 0x2

    .line 54
    .line 55
    if-eqz v0, :cond_4

    .line 56
    .line 57
    or-int/lit8 v6, v6, 0x30

    .line 58
    .line 59
    goto :goto_4

    .line 60
    :cond_4
    and-int/lit8 v0, v5, 0x30

    .line 61
    .line 62
    if-nez v0, :cond_6

    .line 63
    .line 64
    invoke-interface {v10, v2}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 65
    .line 66
    .line 67
    move-result v0

    .line 68
    if-eqz v0, :cond_5

    .line 69
    .line 70
    const/16 v0, 0x20

    .line 71
    .line 72
    goto :goto_3

    .line 73
    :cond_5
    const/16 v0, 0x10

    .line 74
    .line 75
    :goto_3
    or-int/2addr v6, v0

    .line 76
    :cond_6
    :goto_4
    and-int/lit8 v0, p6, 0x4

    .line 77
    .line 78
    if-eqz v0, :cond_8

    .line 79
    .line 80
    or-int/lit16 v6, v6, 0x180

    .line 81
    .line 82
    :cond_7
    move-object/from16 v7, p2

    .line 83
    .line 84
    goto :goto_6

    .line 85
    :cond_8
    and-int/lit16 v7, v5, 0x180

    .line 86
    .line 87
    if-nez v7, :cond_7

    .line 88
    .line 89
    move-object/from16 v7, p2

    .line 90
    .line 91
    invoke-interface {v10, v7}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 92
    .line 93
    .line 94
    move-result v8

    .line 95
    if-eqz v8, :cond_9

    .line 96
    .line 97
    const/16 v8, 0x100

    .line 98
    .line 99
    goto :goto_5

    .line 100
    :cond_9
    const/16 v8, 0x80

    .line 101
    .line 102
    :goto_5
    or-int/2addr v6, v8

    .line 103
    :goto_6
    and-int/lit8 v8, p6, 0x8

    .line 104
    .line 105
    const/16 v15, 0x800

    .line 106
    .line 107
    if-eqz v8, :cond_a

    .line 108
    .line 109
    or-int/lit16 v6, v6, 0xc00

    .line 110
    .line 111
    goto :goto_8

    .line 112
    :cond_a
    and-int/lit16 v8, v5, 0xc00

    .line 113
    .line 114
    if-nez v8, :cond_c

    .line 115
    .line 116
    invoke-interface {v10, v4}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 117
    .line 118
    .line 119
    move-result v8

    .line 120
    if-eqz v8, :cond_b

    .line 121
    .line 122
    const/16 v8, 0x800

    .line 123
    .line 124
    goto :goto_7

    .line 125
    :cond_b
    const/16 v8, 0x400

    .line 126
    .line 127
    :goto_7
    or-int/2addr v6, v8

    .line 128
    :cond_c
    :goto_8
    and-int/lit16 v8, v6, 0x493

    .line 129
    .line 130
    const/16 v9, 0x492

    .line 131
    .line 132
    if-ne v8, v9, :cond_e

    .line 133
    .line 134
    invoke-interface {v10}, Landroidx/compose/runtime/j;->c()Z

    .line 135
    .line 136
    .line 137
    move-result v8

    .line 138
    if-nez v8, :cond_d

    .line 139
    .line 140
    goto :goto_9

    .line 141
    :cond_d
    invoke-interface {v10}, Landroidx/compose/runtime/j;->n()V

    .line 142
    .line 143
    .line 144
    move-object v3, v7

    .line 145
    goto/16 :goto_16

    .line 146
    .line 147
    :cond_e
    :goto_9
    if-eqz v0, :cond_f

    .line 148
    .line 149
    sget-object v0, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 150
    .line 151
    goto :goto_a

    .line 152
    :cond_f
    move-object v0, v7

    .line 153
    :goto_a
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 154
    .line 155
    .line 156
    move-result v7

    .line 157
    if-eqz v7, :cond_10

    .line 158
    .line 159
    const/4 v7, -0x1

    .line 160
    const-string v8, "org.xbet.uikit_sport.compose.sport_game_events.RightPlayer (EventComponent.kt:128)"

    .line 161
    .line 162
    invoke-static {v3, v6, v7, v8}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 163
    .line 164
    .line 165
    :cond_10
    if-eqz v2, :cond_23

    .line 166
    .line 167
    const v3, -0x6bce1df5

    .line 168
    .line 169
    .line 170
    invoke-interface {v10, v3}, Landroidx/compose/runtime/j;->t(I)V

    .line 171
    .line 172
    .line 173
    const/4 v3, 0x0

    .line 174
    const/4 v7, 0x0

    .line 175
    invoke-static {v0, v3, v13, v7}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 176
    .line 177
    .line 178
    move-result-object v8

    .line 179
    sget-object v16, LA11/a;->a:LA11/a;

    .line 180
    .line 181
    invoke-virtual/range {v16 .. v16}, LA11/a;->L1()F

    .line 182
    .line 183
    .line 184
    move-result v9

    .line 185
    invoke-static {v8, v3, v9, v13, v7}, Landroidx/compose/foundation/layout/PaddingKt;->k(Landroidx/compose/ui/l;FFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 186
    .line 187
    .line 188
    move-result-object v3

    .line 189
    sget-object v7, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 190
    .line 191
    invoke-virtual {v7}, Landroidx/compose/foundation/layout/Arrangement;->h()Landroidx/compose/foundation/layout/Arrangement$m;

    .line 192
    .line 193
    .line 194
    move-result-object v7

    .line 195
    sget-object v8, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 196
    .line 197
    invoke-virtual {v8}, Landroidx/compose/ui/e$a;->k()Landroidx/compose/ui/e$b;

    .line 198
    .line 199
    .line 200
    move-result-object v8

    .line 201
    const/4 v9, 0x0

    .line 202
    invoke-static {v7, v8, v10, v9}, Landroidx/compose/foundation/layout/k;->a(Landroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 203
    .line 204
    .line 205
    move-result-object v7

    .line 206
    invoke-static {v10, v9}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 207
    .line 208
    .line 209
    move-result v8

    .line 210
    invoke-interface {v10}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 211
    .line 212
    .line 213
    move-result-object v11

    .line 214
    invoke-static {v10, v3}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 215
    .line 216
    .line 217
    move-result-object v3

    .line 218
    sget-object v12, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 219
    .line 220
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 221
    .line 222
    .line 223
    move-result-object v9

    .line 224
    invoke-interface {v10}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 225
    .line 226
    .line 227
    move-result-object v17

    .line 228
    invoke-static/range {v17 .. v17}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 229
    .line 230
    .line 231
    move-result v17

    .line 232
    if-nez v17, :cond_11

    .line 233
    .line 234
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 235
    .line 236
    .line 237
    :cond_11
    invoke-interface {v10}, Landroidx/compose/runtime/j;->l()V

    .line 238
    .line 239
    .line 240
    invoke-interface {v10}, Landroidx/compose/runtime/j;->B()Z

    .line 241
    .line 242
    .line 243
    move-result v17

    .line 244
    if-eqz v17, :cond_12

    .line 245
    .line 246
    invoke-interface {v10, v9}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 247
    .line 248
    .line 249
    goto :goto_b

    .line 250
    :cond_12
    invoke-interface {v10}, Landroidx/compose/runtime/j;->h()V

    .line 251
    .line 252
    .line 253
    :goto_b
    invoke-static {v10}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 254
    .line 255
    .line 256
    move-result-object v9

    .line 257
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 258
    .line 259
    .line 260
    move-result-object v13

    .line 261
    invoke-static {v9, v7, v13}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 262
    .line 263
    .line 264
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 265
    .line 266
    .line 267
    move-result-object v7

    .line 268
    invoke-static {v9, v11, v7}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 269
    .line 270
    .line 271
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 272
    .line 273
    .line 274
    move-result-object v7

    .line 275
    invoke-interface {v9}, Landroidx/compose/runtime/j;->B()Z

    .line 276
    .line 277
    .line 278
    move-result v11

    .line 279
    if-nez v11, :cond_13

    .line 280
    .line 281
    invoke-interface {v9}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 282
    .line 283
    .line 284
    move-result-object v11

    .line 285
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 286
    .line 287
    .line 288
    move-result-object v13

    .line 289
    invoke-static {v11, v13}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 290
    .line 291
    .line 292
    move-result v11

    .line 293
    if-nez v11, :cond_14

    .line 294
    .line 295
    :cond_13
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 296
    .line 297
    .line 298
    move-result-object v11

    .line 299
    invoke-interface {v9, v11}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 300
    .line 301
    .line 302
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 303
    .line 304
    .line 305
    move-result-object v8

    .line 306
    invoke-interface {v9, v8, v7}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 307
    .line 308
    .line 309
    :cond_14
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 310
    .line 311
    .line 312
    move-result-object v7

    .line 313
    invoke-static {v9, v3, v7}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 314
    .line 315
    .line 316
    sget-object v3, Landroidx/compose/foundation/layout/n;->a:Landroidx/compose/foundation/layout/n;

    .line 317
    .line 318
    invoke-virtual {v1}, Ls31/a$b;->e()LM11/b;

    .line 319
    .line 320
    .line 321
    move-result-object v3

    .line 322
    invoke-virtual {v3}, LM11/b;->c()Ljava/lang/String;

    .line 323
    .line 324
    .line 325
    move-result-object v3

    .line 326
    invoke-interface {v3}, Ljava/lang/CharSequence;->length()I

    .line 327
    .line 328
    .line 329
    move-result v7

    .line 330
    if-nez v7, :cond_15

    .line 331
    .line 332
    const/4 v7, 0x1

    .line 333
    goto :goto_c

    .line 334
    :cond_15
    const/4 v7, 0x0

    .line 335
    :goto_c
    if-eqz v7, :cond_16

    .line 336
    .line 337
    invoke-virtual {v1}, Ls31/a$b;->f()LM11/d;

    .line 338
    .line 339
    .line 340
    move-result-object v3

    .line 341
    invoke-virtual {v3}, LM11/d;->a()Ljava/lang/String;

    .line 342
    .line 343
    .line 344
    move-result-object v3

    .line 345
    :cond_16
    invoke-virtual {v1}, Ls31/a$b;->e()LM11/b;

    .line 346
    .line 347
    .line 348
    move-result-object v7

    .line 349
    invoke-virtual {v7}, LM11/b;->b()Ljava/lang/String;

    .line 350
    .line 351
    .line 352
    move-result-object v7

    .line 353
    const v13, -0x615d173a

    .line 354
    .line 355
    .line 356
    invoke-interface {v10, v13}, Landroidx/compose/runtime/j;->t(I)V

    .line 357
    .line 358
    .line 359
    and-int/lit16 v8, v6, 0x1c00

    .line 360
    .line 361
    if-ne v8, v15, :cond_17

    .line 362
    .line 363
    const/4 v9, 0x1

    .line 364
    goto :goto_d

    .line 365
    :cond_17
    const/4 v9, 0x0

    .line 366
    :goto_d
    and-int/lit8 v11, v6, 0xe

    .line 367
    .line 368
    if-eq v11, v14, :cond_19

    .line 369
    .line 370
    and-int/lit8 v12, v6, 0x8

    .line 371
    .line 372
    if-eqz v12, :cond_18

    .line 373
    .line 374
    invoke-interface {v10, v1}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 375
    .line 376
    .line 377
    move-result v12

    .line 378
    if-eqz v12, :cond_18

    .line 379
    .line 380
    goto :goto_e

    .line 381
    :cond_18
    const/4 v12, 0x0

    .line 382
    goto :goto_f

    .line 383
    :cond_19
    :goto_e
    const/4 v12, 0x1

    .line 384
    :goto_f
    or-int/2addr v9, v12

    .line 385
    invoke-interface {v10}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 386
    .line 387
    .line 388
    move-result-object v12

    .line 389
    if-nez v9, :cond_1a

    .line 390
    .line 391
    sget-object v9, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 392
    .line 393
    invoke-virtual {v9}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 394
    .line 395
    .line 396
    move-result-object v9

    .line 397
    if-ne v12, v9, :cond_1b

    .line 398
    .line 399
    :cond_1a
    new-instance v12, Lorg/xbet/uikit_sport/compose/sport_game_events/g;

    .line 400
    .line 401
    invoke-direct {v12, v4, v1}, Lorg/xbet/uikit_sport/compose/sport_game_events/g;-><init>(Lkotlin/jvm/functions/Function1;Ls31/a$b;)V

    .line 402
    .line 403
    .line 404
    invoke-interface {v10, v12}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 405
    .line 406
    .line 407
    :cond_1b
    move-object v9, v12

    .line 408
    check-cast v9, Lkotlin/jvm/functions/Function0;

    .line 409
    .line 410
    invoke-interface {v10}, Landroidx/compose/runtime/j;->q()V

    .line 411
    .line 412
    .line 413
    move v12, v11

    .line 414
    const/4 v11, 0x0

    .line 415
    move/from16 v17, v12

    .line 416
    .line 417
    const/4 v12, 0x4

    .line 418
    move/from16 v18, v8

    .line 419
    .line 420
    const/4 v8, 0x0

    .line 421
    move/from16 v19, v17

    .line 422
    .line 423
    move/from16 v14, v18

    .line 424
    .line 425
    move/from16 v17, v6

    .line 426
    .line 427
    move-object v6, v3

    .line 428
    const/4 v3, 0x0

    .line 429
    invoke-static/range {v6 .. v12}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->B(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V

    .line 430
    .line 431
    .line 432
    const v6, -0x5f770edb

    .line 433
    .line 434
    .line 435
    invoke-interface {v10, v6}, Landroidx/compose/runtime/j;->t(I)V

    .line 436
    .line 437
    .line 438
    invoke-virtual {v1}, Ls31/a$b;->b()LM11/b;

    .line 439
    .line 440
    .line 441
    move-result-object v6

    .line 442
    invoke-virtual {v6}, LM11/b;->a()Ljava/lang/String;

    .line 443
    .line 444
    .line 445
    move-result-object v6

    .line 446
    invoke-interface {v6}, Ljava/lang/CharSequence;->length()I

    .line 447
    .line 448
    .line 449
    move-result v6

    .line 450
    if-lez v6, :cond_1c

    .line 451
    .line 452
    const/4 v9, 0x1

    .line 453
    goto :goto_10

    .line 454
    :cond_1c
    const/4 v9, 0x0

    .line 455
    :goto_10
    if-eqz v9, :cond_22

    .line 456
    .line 457
    sget-object v6, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 458
    .line 459
    invoke-virtual/range {v16 .. v16}, LA11/a;->L1()F

    .line 460
    .line 461
    .line 462
    move-result v7

    .line 463
    invoke-static {v6, v7}, Landroidx/compose/foundation/layout/SizeKt;->i(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 464
    .line 465
    .line 466
    move-result-object v6

    .line 467
    invoke-static {v6, v10, v3}, Landroidx/compose/foundation/layout/o0;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;I)V

    .line 468
    .line 469
    .line 470
    invoke-virtual {v1}, Ls31/a$b;->b()LM11/b;

    .line 471
    .line 472
    .line 473
    move-result-object v6

    .line 474
    invoke-virtual {v6}, LM11/b;->c()Ljava/lang/String;

    .line 475
    .line 476
    .line 477
    move-result-object v6

    .line 478
    invoke-virtual {v1}, Ls31/a$b;->b()LM11/b;

    .line 479
    .line 480
    .line 481
    move-result-object v7

    .line 482
    invoke-virtual {v7}, LM11/b;->b()Ljava/lang/String;

    .line 483
    .line 484
    .line 485
    move-result-object v7

    .line 486
    invoke-virtual {v1}, Ls31/a$b;->h()Z

    .line 487
    .line 488
    .line 489
    move-result v8

    .line 490
    invoke-interface {v10, v13}, Landroidx/compose/runtime/j;->t(I)V

    .line 491
    .line 492
    .line 493
    if-ne v14, v15, :cond_1d

    .line 494
    .line 495
    const/4 v9, 0x1

    .line 496
    :goto_11
    move/from16 v12, v19

    .line 497
    .line 498
    const/4 v11, 0x4

    .line 499
    goto :goto_12

    .line 500
    :cond_1d
    const/4 v9, 0x0

    .line 501
    goto :goto_11

    .line 502
    :goto_12
    if-eq v12, v11, :cond_1f

    .line 503
    .line 504
    and-int/lit8 v11, v17, 0x8

    .line 505
    .line 506
    if-eqz v11, :cond_1e

    .line 507
    .line 508
    invoke-interface {v10, v1}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 509
    .line 510
    .line 511
    move-result v11

    .line 512
    if-eqz v11, :cond_1e

    .line 513
    .line 514
    goto :goto_13

    .line 515
    :cond_1e
    const/4 v13, 0x0

    .line 516
    goto :goto_14

    .line 517
    :cond_1f
    :goto_13
    const/4 v13, 0x1

    .line 518
    :goto_14
    or-int v3, v9, v13

    .line 519
    .line 520
    invoke-interface {v10}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 521
    .line 522
    .line 523
    move-result-object v9

    .line 524
    if-nez v3, :cond_20

    .line 525
    .line 526
    sget-object v3, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 527
    .line 528
    invoke-virtual {v3}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 529
    .line 530
    .line 531
    move-result-object v3

    .line 532
    if-ne v9, v3, :cond_21

    .line 533
    .line 534
    :cond_20
    new-instance v9, Lorg/xbet/uikit_sport/compose/sport_game_events/h;

    .line 535
    .line 536
    invoke-direct {v9, v4, v1}, Lorg/xbet/uikit_sport/compose/sport_game_events/h;-><init>(Lkotlin/jvm/functions/Function1;Ls31/a$b;)V

    .line 537
    .line 538
    .line 539
    invoke-interface {v10, v9}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 540
    .line 541
    .line 542
    :cond_21
    check-cast v9, Lkotlin/jvm/functions/Function0;

    .line 543
    .line 544
    invoke-interface {v10}, Landroidx/compose/runtime/j;->q()V

    .line 545
    .line 546
    .line 547
    const/4 v12, 0x0

    .line 548
    const/16 v13, 0x8

    .line 549
    .line 550
    move-object v11, v10

    .line 551
    move-object v10, v9

    .line 552
    const/4 v9, 0x0

    .line 553
    invoke-static/range {v6 .. v13}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->C(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V

    .line 554
    .line 555
    .line 556
    move-object v10, v11

    .line 557
    :cond_22
    invoke-interface {v10}, Landroidx/compose/runtime/j;->q()V

    .line 558
    .line 559
    .line 560
    invoke-interface {v10}, Landroidx/compose/runtime/j;->j()V

    .line 561
    .line 562
    .line 563
    invoke-interface {v10}, Landroidx/compose/runtime/j;->q()V

    .line 564
    .line 565
    .line 566
    goto :goto_15

    .line 567
    :cond_23
    move/from16 v17, v6

    .line 568
    .line 569
    const v3, -0x6bc096ad

    .line 570
    .line 571
    .line 572
    invoke-interface {v10, v3}, Landroidx/compose/runtime/j;->t(I)V

    .line 573
    .line 574
    .line 575
    shr-int/lit8 v3, v17, 0x6

    .line 576
    .line 577
    and-int/lit8 v3, v3, 0xe

    .line 578
    .line 579
    invoke-static {v0, v10, v3}, Landroidx/compose/foundation/layout/o0;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;I)V

    .line 580
    .line 581
    .line 582
    invoke-interface {v10}, Landroidx/compose/runtime/j;->q()V

    .line 583
    .line 584
    .line 585
    :goto_15
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 586
    .line 587
    .line 588
    move-result v3

    .line 589
    if-eqz v3, :cond_24

    .line 590
    .line 591
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 592
    .line 593
    .line 594
    :cond_24
    move-object v3, v0

    .line 595
    :goto_16
    invoke-interface {v10}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 596
    .line 597
    .line 598
    move-result-object v7

    .line 599
    if-eqz v7, :cond_25

    .line 600
    .line 601
    new-instance v0, Lorg/xbet/uikit_sport/compose/sport_game_events/i;

    .line 602
    .line 603
    move/from16 v6, p6

    .line 604
    .line 605
    invoke-direct/range {v0 .. v6}, Lorg/xbet/uikit_sport/compose/sport_game_events/i;-><init>(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;II)V

    .line 606
    .line 607
    .line 608
    invoke-interface {v7, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 609
    .line 610
    .line 611
    :cond_25
    return-void
.end method

.method public static final G(Lkotlin/jvm/functions/Function1;Ls31/a$b;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, Ls31/a$b;->e()LM11/b;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p1}, LM11/b;->a()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final H(Lkotlin/jvm/functions/Function1;Ls31/a$b;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, Ls31/a$b;->b()LM11/b;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p1}, LM11/b;->a()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final I(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 7

    .line 1
    or-int/lit8 p4, p4, 0x1

    .line 2
    .line 3
    invoke-static {p4}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result v5

    .line 7
    move-object v0, p0

    .line 8
    move v1, p1

    .line 9
    move-object v2, p2

    .line 10
    move-object v3, p3

    .line 11
    move v6, p5

    .line 12
    move-object v4, p6

    .line 13
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->F(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method

.method public static synthetic a(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p7}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->w(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(ZZZLjava/lang/String;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p6}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->q(ZZZLjava/lang/String;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p7}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->D(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p7}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->I(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Lkotlin/jvm/functions/Function1;Ls31/a$b;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->G(Lkotlin/jvm/functions/Function1;Ls31/a$b;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Lkotlin/jvm/functions/Function1;Ls31/a$b;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->H(Lkotlin/jvm/functions/Function1;Ls31/a$b;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Lkotlin/jvm/functions/Function1;Ls31/a$b;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->v(Lkotlin/jvm/functions/Function1;Ls31/a$b;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic h(Ls31/a$b;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p6}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->o(Ls31/a$b;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic i(Lkotlin/jvm/functions/Function1;Ls31/a$b;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->u(Lkotlin/jvm/functions/Function1;Ls31/a$b;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic j(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p7}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->z(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic k(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p8}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->A(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic l(ZZZLjava/lang/String;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p6}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->s(ZZZLjava/lang/String;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic m(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p8}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->E(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final n(Ls31/a$b;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V
    .locals 25
    .param p0    # Ls31/a$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ls31/a$b;",
            "Landroidx/compose/ui/l;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move/from16 v7, p4

    .line 4
    .line 5
    const v1, -0x696ff9fa

    .line 6
    .line 7
    .line 8
    move-object/from16 v2, p3

    .line 9
    .line 10
    invoke-interface {v2, v1}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 11
    .line 12
    .line 13
    move-result-object v12

    .line 14
    and-int/lit8 v2, p5, 0x1

    .line 15
    .line 16
    if-eqz v2, :cond_0

    .line 17
    .line 18
    or-int/lit8 v2, v7, 0x6

    .line 19
    .line 20
    goto :goto_2

    .line 21
    :cond_0
    and-int/lit8 v2, v7, 0x6

    .line 22
    .line 23
    if-nez v2, :cond_3

    .line 24
    .line 25
    and-int/lit8 v2, v7, 0x8

    .line 26
    .line 27
    if-nez v2, :cond_1

    .line 28
    .line 29
    invoke-interface {v12, v0}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result v2

    .line 33
    goto :goto_0

    .line 34
    :cond_1
    invoke-interface {v12, v0}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 35
    .line 36
    .line 37
    move-result v2

    .line 38
    :goto_0
    if-eqz v2, :cond_2

    .line 39
    .line 40
    const/4 v2, 0x4

    .line 41
    goto :goto_1

    .line 42
    :cond_2
    const/4 v2, 0x2

    .line 43
    :goto_1
    or-int/2addr v2, v7

    .line 44
    goto :goto_2

    .line 45
    :cond_3
    move v2, v7

    .line 46
    :goto_2
    and-int/lit8 v3, p5, 0x2

    .line 47
    .line 48
    if-eqz v3, :cond_5

    .line 49
    .line 50
    or-int/lit8 v2, v2, 0x30

    .line 51
    .line 52
    :cond_4
    move-object/from16 v4, p1

    .line 53
    .line 54
    goto :goto_4

    .line 55
    :cond_5
    and-int/lit8 v4, v7, 0x30

    .line 56
    .line 57
    if-nez v4, :cond_4

    .line 58
    .line 59
    move-object/from16 v4, p1

    .line 60
    .line 61
    invoke-interface {v12, v4}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 62
    .line 63
    .line 64
    move-result v5

    .line 65
    if-eqz v5, :cond_6

    .line 66
    .line 67
    const/16 v5, 0x20

    .line 68
    .line 69
    goto :goto_3

    .line 70
    :cond_6
    const/16 v5, 0x10

    .line 71
    .line 72
    :goto_3
    or-int/2addr v2, v5

    .line 73
    :goto_4
    and-int/lit8 v5, p5, 0x4

    .line 74
    .line 75
    if-eqz v5, :cond_8

    .line 76
    .line 77
    or-int/lit16 v2, v2, 0x180

    .line 78
    .line 79
    :cond_7
    move-object/from16 v5, p2

    .line 80
    .line 81
    goto :goto_6

    .line 82
    :cond_8
    and-int/lit16 v5, v7, 0x180

    .line 83
    .line 84
    if-nez v5, :cond_7

    .line 85
    .line 86
    move-object/from16 v5, p2

    .line 87
    .line 88
    invoke-interface {v12, v5}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 89
    .line 90
    .line 91
    move-result v6

    .line 92
    if-eqz v6, :cond_9

    .line 93
    .line 94
    const/16 v6, 0x100

    .line 95
    .line 96
    goto :goto_5

    .line 97
    :cond_9
    const/16 v6, 0x80

    .line 98
    .line 99
    :goto_5
    or-int/2addr v2, v6

    .line 100
    :goto_6
    and-int/lit16 v6, v2, 0x93

    .line 101
    .line 102
    const/16 v8, 0x92

    .line 103
    .line 104
    if-ne v6, v8, :cond_b

    .line 105
    .line 106
    invoke-interface {v12}, Landroidx/compose/runtime/j;->c()Z

    .line 107
    .line 108
    .line 109
    move-result v6

    .line 110
    if-nez v6, :cond_a

    .line 111
    .line 112
    goto :goto_7

    .line 113
    :cond_a
    invoke-interface {v12}, Landroidx/compose/runtime/j;->n()V

    .line 114
    .line 115
    .line 116
    move-object v2, v4

    .line 117
    goto/16 :goto_d

    .line 118
    .line 119
    :cond_b
    :goto_7
    if-eqz v3, :cond_c

    .line 120
    .line 121
    sget-object v3, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 122
    .line 123
    move-object v14, v3

    .line 124
    goto :goto_8

    .line 125
    :cond_c
    move-object v14, v4

    .line 126
    :goto_8
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 127
    .line 128
    .line 129
    move-result v3

    .line 130
    if-eqz v3, :cond_d

    .line 131
    .line 132
    const/4 v3, -0x1

    .line 133
    const-string v4, "org.xbet.uikit_sport.compose.sport_game_events.EventComponent (EventComponent.kt:50)"

    .line 134
    .line 135
    invoke-static {v1, v2, v3, v4}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 136
    .line 137
    .line 138
    :cond_d
    invoke-virtual {v0}, Ls31/a$b;->g()Lorg/xbet/uikit_sport/compose/sport_game_events/model/DsTeamSideUiPosition;

    .line 139
    .line 140
    .line 141
    move-result-object v1

    .line 142
    sget-object v3, Lorg/xbet/uikit_sport/compose/sport_game_events/model/DsTeamSideUiPosition;->LEFT:Lorg/xbet/uikit_sport/compose/sport_game_events/model/DsTeamSideUiPosition;

    .line 143
    .line 144
    const/4 v15, 0x0

    .line 145
    const/4 v8, 0x1

    .line 146
    if-ne v1, v3, :cond_e

    .line 147
    .line 148
    const/4 v1, 0x1

    .line 149
    goto :goto_9

    .line 150
    :cond_e
    const/4 v1, 0x0

    .line 151
    :goto_9
    const/4 v3, 0x0

    .line 152
    const/4 v4, 0x0

    .line 153
    invoke-static {v14, v3, v8, v4}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 154
    .line 155
    .line 156
    move-result-object v3

    .line 157
    sget-object v4, Landroidx/compose/foundation/layout/IntrinsicSize;->Max:Landroidx/compose/foundation/layout/IntrinsicSize;

    .line 158
    .line 159
    invoke-static {v3, v4}, Landroidx/compose/foundation/layout/IntrinsicKt;->a(Landroidx/compose/ui/l;Landroidx/compose/foundation/layout/IntrinsicSize;)Landroidx/compose/ui/l;

    .line 160
    .line 161
    .line 162
    move-result-object v3

    .line 163
    sget-object v4, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 164
    .line 165
    invoke-virtual {v4}, Landroidx/compose/ui/e$a;->i()Landroidx/compose/ui/e$c;

    .line 166
    .line 167
    .line 168
    move-result-object v4

    .line 169
    sget-object v6, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 170
    .line 171
    invoke-virtual {v6}, Landroidx/compose/foundation/layout/Arrangement;->g()Landroidx/compose/foundation/layout/Arrangement$e;

    .line 172
    .line 173
    .line 174
    move-result-object v6

    .line 175
    const/16 v9, 0x30

    .line 176
    .line 177
    invoke-static {v6, v4, v12, v9}, Landroidx/compose/foundation/layout/h0;->b(Landroidx/compose/foundation/layout/Arrangement$e;Landroidx/compose/ui/e$c;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 178
    .line 179
    .line 180
    move-result-object v4

    .line 181
    invoke-static {v12, v15}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 182
    .line 183
    .line 184
    move-result v6

    .line 185
    invoke-interface {v12}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 186
    .line 187
    .line 188
    move-result-object v9

    .line 189
    invoke-static {v12, v3}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 190
    .line 191
    .line 192
    move-result-object v3

    .line 193
    sget-object v10, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 194
    .line 195
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 196
    .line 197
    .line 198
    move-result-object v11

    .line 199
    invoke-interface {v12}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 200
    .line 201
    .line 202
    move-result-object v13

    .line 203
    invoke-static {v13}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 204
    .line 205
    .line 206
    move-result v13

    .line 207
    if-nez v13, :cond_f

    .line 208
    .line 209
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 210
    .line 211
    .line 212
    :cond_f
    invoke-interface {v12}, Landroidx/compose/runtime/j;->l()V

    .line 213
    .line 214
    .line 215
    invoke-interface {v12}, Landroidx/compose/runtime/j;->B()Z

    .line 216
    .line 217
    .line 218
    move-result v13

    .line 219
    if-eqz v13, :cond_10

    .line 220
    .line 221
    invoke-interface {v12, v11}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 222
    .line 223
    .line 224
    goto :goto_a

    .line 225
    :cond_10
    invoke-interface {v12}, Landroidx/compose/runtime/j;->h()V

    .line 226
    .line 227
    .line 228
    :goto_a
    invoke-static {v12}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 229
    .line 230
    .line 231
    move-result-object v11

    .line 232
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 233
    .line 234
    .line 235
    move-result-object v13

    .line 236
    invoke-static {v11, v4, v13}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 237
    .line 238
    .line 239
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 240
    .line 241
    .line 242
    move-result-object v4

    .line 243
    invoke-static {v11, v9, v4}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 244
    .line 245
    .line 246
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 247
    .line 248
    .line 249
    move-result-object v4

    .line 250
    invoke-interface {v11}, Landroidx/compose/runtime/j;->B()Z

    .line 251
    .line 252
    .line 253
    move-result v9

    .line 254
    if-nez v9, :cond_11

    .line 255
    .line 256
    invoke-interface {v11}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 257
    .line 258
    .line 259
    move-result-object v9

    .line 260
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 261
    .line 262
    .line 263
    move-result-object v13

    .line 264
    invoke-static {v9, v13}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 265
    .line 266
    .line 267
    move-result v9

    .line 268
    if-nez v9, :cond_12

    .line 269
    .line 270
    :cond_11
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 271
    .line 272
    .line 273
    move-result-object v9

    .line 274
    invoke-interface {v11, v9}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 275
    .line 276
    .line 277
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 278
    .line 279
    .line 280
    move-result-object v6

    .line 281
    invoke-interface {v11, v6, v4}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 282
    .line 283
    .line 284
    :cond_12
    invoke-virtual {v10}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 285
    .line 286
    .line 287
    move-result-object v4

    .line 288
    invoke-static {v11, v3, v4}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 289
    .line 290
    .line 291
    sget-object v16, Landroidx/compose/foundation/layout/k0;->a:Landroidx/compose/foundation/layout/k0;

    .line 292
    .line 293
    sget-object v17, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 294
    .line 295
    const/16 v20, 0x2

    .line 296
    .line 297
    const/16 v21, 0x0

    .line 298
    .line 299
    const/high16 v18, 0x3f800000    # 1.0f

    .line 300
    .line 301
    const/16 v19, 0x0

    .line 302
    .line 303
    invoke-static/range {v16 .. v21}, Landroidx/compose/foundation/layout/i0;->a(Landroidx/compose/foundation/layout/j0;Landroidx/compose/ui/l;FZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 304
    .line 305
    .line 306
    move-result-object v3

    .line 307
    sget v22, LM11/b;->g:I

    .line 308
    .line 309
    sget v23, LM11/d;->f:I

    .line 310
    .line 311
    or-int v4, v22, v23

    .line 312
    .line 313
    and-int/lit8 v24, v2, 0xe

    .line 314
    .line 315
    or-int v4, v4, v24

    .line 316
    .line 317
    shl-int/lit8 v2, v2, 0x3

    .line 318
    .line 319
    and-int/lit16 v9, v2, 0x1c00

    .line 320
    .line 321
    or-int v2, v4, v9

    .line 322
    .line 323
    const/4 v6, 0x0

    .line 324
    move-object v4, v5

    .line 325
    move v5, v2

    .line 326
    move-object v2, v3

    .line 327
    move-object v3, v4

    .line 328
    move-object v4, v12

    .line 329
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->t(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 330
    .line 331
    .line 332
    move v0, v9

    .line 333
    invoke-virtual/range {p0 .. p0}, Ls31/a$b;->h()Z

    .line 334
    .line 335
    .line 336
    move-result v9

    .line 337
    invoke-virtual/range {p0 .. p0}, Ls31/a$b;->b()LM11/b;

    .line 338
    .line 339
    .line 340
    move-result-object v2

    .line 341
    invoke-virtual {v2}, LM11/b;->a()Ljava/lang/String;

    .line 342
    .line 343
    .line 344
    move-result-object v2

    .line 345
    invoke-interface {v2}, Ljava/lang/CharSequence;->length()I

    .line 346
    .line 347
    .line 348
    move-result v2

    .line 349
    if-lez v2, :cond_13

    .line 350
    .line 351
    const/4 v10, 0x1

    .line 352
    goto :goto_b

    .line 353
    :cond_13
    const/4 v10, 0x0

    .line 354
    :goto_b
    invoke-virtual/range {p0 .. p0}, Ls31/a$b;->a()Ljava/lang/String;

    .line 355
    .line 356
    .line 357
    move-result-object v11

    .line 358
    const/4 v13, 0x0

    .line 359
    move v8, v1

    .line 360
    move v1, v0

    .line 361
    const/4 v0, 0x1

    .line 362
    invoke-static/range {v8 .. v13}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->p(ZZZLjava/lang/String;Landroidx/compose/runtime/j;I)V

    .line 363
    .line 364
    .line 365
    move v2, v8

    .line 366
    invoke-virtual/range {p0 .. p0}, Ls31/a$b;->d()Ljava/lang/String;

    .line 367
    .line 368
    .line 369
    move-result-object v8

    .line 370
    invoke-virtual/range {p0 .. p0}, Ls31/a$b;->c()Lorg/xbet/uikit_sport/compose/sport_game_events/model/DsEventPositionInSection;

    .line 371
    .line 372
    .line 373
    move-result-object v9

    .line 374
    move-object v4, v12

    .line 375
    const/4 v12, 0x0

    .line 376
    const/4 v13, 0x4

    .line 377
    const/4 v10, 0x0

    .line 378
    move-object v11, v4

    .line 379
    invoke-static/range {v8 .. v13}, Lorg/xbet/uikit_sport/compose/sport_game_events/r;->b(Ljava/lang/String;Lorg/xbet/uikit_sport/compose/sport_game_events/model/DsEventPositionInSection;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 380
    .line 381
    .line 382
    move-object v12, v11

    .line 383
    xor-int/lit8 v8, v2, 0x1

    .line 384
    .line 385
    invoke-virtual/range {p0 .. p0}, Ls31/a$b;->h()Z

    .line 386
    .line 387
    .line 388
    move-result v9

    .line 389
    invoke-virtual/range {p0 .. p0}, Ls31/a$b;->b()LM11/b;

    .line 390
    .line 391
    .line 392
    move-result-object v3

    .line 393
    invoke-virtual {v3}, LM11/b;->a()Ljava/lang/String;

    .line 394
    .line 395
    .line 396
    move-result-object v3

    .line 397
    invoke-interface {v3}, Ljava/lang/CharSequence;->length()I

    .line 398
    .line 399
    .line 400
    move-result v3

    .line 401
    if-lez v3, :cond_14

    .line 402
    .line 403
    const/4 v10, 0x1

    .line 404
    goto :goto_c

    .line 405
    :cond_14
    const/4 v10, 0x0

    .line 406
    :goto_c
    invoke-virtual/range {p0 .. p0}, Ls31/a$b;->a()Ljava/lang/String;

    .line 407
    .line 408
    .line 409
    move-result-object v11

    .line 410
    const/4 v13, 0x0

    .line 411
    invoke-static/range {v8 .. v13}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->r(ZZZLjava/lang/String;Landroidx/compose/runtime/j;I)V

    .line 412
    .line 413
    .line 414
    xor-int/2addr v0, v2

    .line 415
    const/16 v20, 0x2

    .line 416
    .line 417
    const/16 v21, 0x0

    .line 418
    .line 419
    const/high16 v18, 0x3f800000    # 1.0f

    .line 420
    .line 421
    const/16 v19, 0x0

    .line 422
    .line 423
    invoke-static/range {v16 .. v21}, Landroidx/compose/foundation/layout/i0;->a(Landroidx/compose/foundation/layout/j0;Landroidx/compose/ui/l;FZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 424
    .line 425
    .line 426
    move-result-object v2

    .line 427
    or-int v3, v22, v23

    .line 428
    .line 429
    or-int v3, v3, v24

    .line 430
    .line 431
    or-int v5, v3, v1

    .line 432
    .line 433
    const/4 v6, 0x0

    .line 434
    move-object/from16 v3, p2

    .line 435
    .line 436
    move v1, v0

    .line 437
    move-object v4, v12

    .line 438
    move-object/from16 v0, p0

    .line 439
    .line 440
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->F(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 441
    .line 442
    .line 443
    invoke-interface {v12}, Landroidx/compose/runtime/j;->j()V

    .line 444
    .line 445
    .line 446
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 447
    .line 448
    .line 449
    move-result v0

    .line 450
    if-eqz v0, :cond_15

    .line 451
    .line 452
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 453
    .line 454
    .line 455
    :cond_15
    move-object v2, v14

    .line 456
    :goto_d
    invoke-interface {v12}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 457
    .line 458
    .line 459
    move-result-object v6

    .line 460
    if-eqz v6, :cond_16

    .line 461
    .line 462
    new-instance v0, Lorg/xbet/uikit_sport/compose/sport_game_events/c;

    .line 463
    .line 464
    move-object/from16 v1, p0

    .line 465
    .line 466
    move-object/from16 v3, p2

    .line 467
    .line 468
    move/from16 v5, p5

    .line 469
    .line 470
    move v4, v7

    .line 471
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/compose/sport_game_events/c;-><init>(Ls31/a$b;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;II)V

    .line 472
    .line 473
    .line 474
    invoke-interface {v6, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 475
    .line 476
    .line 477
    :cond_16
    return-void
.end method

.method public static final o(Ls31/a$b;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 6

    .line 1
    or-int/lit8 p3, p3, 0x1

    .line 2
    .line 3
    invoke-static {p3}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result v4

    .line 7
    move-object v0, p0

    .line 8
    move-object v1, p1

    .line 9
    move-object v2, p2

    .line 10
    move v5, p4

    .line 11
    move-object v3, p5

    .line 12
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->n(Ls31/a$b;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final p(ZZZLjava/lang/String;Landroidx/compose/runtime/j;I)V
    .locals 22
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move/from16 v1, p0

    .line 2
    .line 3
    move/from16 v2, p1

    .line 4
    .line 5
    move/from16 v3, p2

    .line 6
    .line 7
    move/from16 v5, p5

    .line 8
    .line 9
    const v0, 0x7559ba60

    .line 10
    .line 11
    .line 12
    move-object/from16 v4, p4

    .line 13
    .line 14
    invoke-interface {v4, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 15
    .line 16
    .line 17
    move-result-object v13

    .line 18
    and-int/lit8 v4, v5, 0x6

    .line 19
    .line 20
    const/4 v6, 0x2

    .line 21
    if-nez v4, :cond_1

    .line 22
    .line 23
    invoke-interface {v13, v1}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 24
    .line 25
    .line 26
    move-result v4

    .line 27
    if-eqz v4, :cond_0

    .line 28
    .line 29
    const/4 v4, 0x4

    .line 30
    goto :goto_0

    .line 31
    :cond_0
    const/4 v4, 0x2

    .line 32
    :goto_0
    or-int/2addr v4, v5

    .line 33
    goto :goto_1

    .line 34
    :cond_1
    move v4, v5

    .line 35
    :goto_1
    and-int/lit8 v7, v5, 0x30

    .line 36
    .line 37
    if-nez v7, :cond_3

    .line 38
    .line 39
    invoke-interface {v13, v2}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 40
    .line 41
    .line 42
    move-result v7

    .line 43
    if-eqz v7, :cond_2

    .line 44
    .line 45
    const/16 v7, 0x20

    .line 46
    .line 47
    goto :goto_2

    .line 48
    :cond_2
    const/16 v7, 0x10

    .line 49
    .line 50
    :goto_2
    or-int/2addr v4, v7

    .line 51
    :cond_3
    and-int/lit16 v7, v5, 0x180

    .line 52
    .line 53
    if-nez v7, :cond_5

    .line 54
    .line 55
    invoke-interface {v13, v3}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 56
    .line 57
    .line 58
    move-result v7

    .line 59
    if-eqz v7, :cond_4

    .line 60
    .line 61
    const/16 v7, 0x100

    .line 62
    .line 63
    goto :goto_3

    .line 64
    :cond_4
    const/16 v7, 0x80

    .line 65
    .line 66
    :goto_3
    or-int/2addr v4, v7

    .line 67
    :cond_5
    and-int/lit16 v7, v5, 0xc00

    .line 68
    .line 69
    if-nez v7, :cond_7

    .line 70
    .line 71
    move-object/from16 v7, p3

    .line 72
    .line 73
    invoke-interface {v13, v7}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 74
    .line 75
    .line 76
    move-result v8

    .line 77
    if-eqz v8, :cond_6

    .line 78
    .line 79
    const/16 v8, 0x800

    .line 80
    .line 81
    goto :goto_4

    .line 82
    :cond_6
    const/16 v8, 0x400

    .line 83
    .line 84
    :goto_4
    or-int/2addr v4, v8

    .line 85
    goto :goto_5

    .line 86
    :cond_7
    move-object/from16 v7, p3

    .line 87
    .line 88
    :goto_5
    and-int/lit16 v8, v4, 0x493

    .line 89
    .line 90
    const/16 v9, 0x492

    .line 91
    .line 92
    if-ne v8, v9, :cond_9

    .line 93
    .line 94
    invoke-interface {v13}, Landroidx/compose/runtime/j;->c()Z

    .line 95
    .line 96
    .line 97
    move-result v8

    .line 98
    if-nez v8, :cond_8

    .line 99
    .line 100
    goto :goto_6

    .line 101
    :cond_8
    invoke-interface {v13}, Landroidx/compose/runtime/j;->n()V

    .line 102
    .line 103
    .line 104
    goto/16 :goto_9

    .line 105
    .line 106
    :cond_9
    :goto_6
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 107
    .line 108
    .line 109
    move-result v8

    .line 110
    if-eqz v8, :cond_a

    .line 111
    .line 112
    const/4 v8, -0x1

    .line 113
    const-string v9, "org.xbet.uikit_sport.compose.sport_game_events.EventLeft (EventComponent.kt:161)"

    .line 114
    .line 115
    invoke-static {v0, v4, v8, v9}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 116
    .line 117
    .line 118
    :cond_a
    const/4 v0, 0x0

    .line 119
    if-eqz v1, :cond_10

    .line 120
    .line 121
    const v8, 0x34c24577

    .line 122
    .line 123
    .line 124
    invoke-interface {v13, v8}, Landroidx/compose/runtime/j;->t(I)V

    .line 125
    .line 126
    .line 127
    sget-object v8, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 128
    .line 129
    sget-object v21, LA11/a;->a:LA11/a;

    .line 130
    .line 131
    invoke-virtual/range {v21 .. v21}, LA11/a;->L1()F

    .line 132
    .line 133
    .line 134
    move-result v9

    .line 135
    const/4 v10, 0x0

    .line 136
    const/4 v11, 0x0

    .line 137
    invoke-static {v8, v9, v10, v6, v11}, Landroidx/compose/foundation/layout/PaddingKt;->k(Landroidx/compose/ui/l;FFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 138
    .line 139
    .line 140
    move-result-object v6

    .line 141
    sget-object v9, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 142
    .line 143
    invoke-virtual {v9}, Landroidx/compose/foundation/layout/Arrangement;->h()Landroidx/compose/foundation/layout/Arrangement$m;

    .line 144
    .line 145
    .line 146
    move-result-object v9

    .line 147
    sget-object v10, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 148
    .line 149
    invoke-virtual {v10}, Landroidx/compose/ui/e$a;->k()Landroidx/compose/ui/e$b;

    .line 150
    .line 151
    .line 152
    move-result-object v10

    .line 153
    invoke-static {v9, v10, v13, v0}, Landroidx/compose/foundation/layout/k;->a(Landroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 154
    .line 155
    .line 156
    move-result-object v9

    .line 157
    invoke-static {v13, v0}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 158
    .line 159
    .line 160
    move-result v10

    .line 161
    invoke-interface {v13}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 162
    .line 163
    .line 164
    move-result-object v11

    .line 165
    invoke-static {v13, v6}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 166
    .line 167
    .line 168
    move-result-object v6

    .line 169
    sget-object v12, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 170
    .line 171
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 172
    .line 173
    .line 174
    move-result-object v14

    .line 175
    invoke-interface {v13}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 176
    .line 177
    .line 178
    move-result-object v15

    .line 179
    invoke-static {v15}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 180
    .line 181
    .line 182
    move-result v15

    .line 183
    if-nez v15, :cond_b

    .line 184
    .line 185
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 186
    .line 187
    .line 188
    :cond_b
    invoke-interface {v13}, Landroidx/compose/runtime/j;->l()V

    .line 189
    .line 190
    .line 191
    invoke-interface {v13}, Landroidx/compose/runtime/j;->B()Z

    .line 192
    .line 193
    .line 194
    move-result v15

    .line 195
    if-eqz v15, :cond_c

    .line 196
    .line 197
    invoke-interface {v13, v14}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 198
    .line 199
    .line 200
    goto :goto_7

    .line 201
    :cond_c
    invoke-interface {v13}, Landroidx/compose/runtime/j;->h()V

    .line 202
    .line 203
    .line 204
    :goto_7
    invoke-static {v13}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 205
    .line 206
    .line 207
    move-result-object v14

    .line 208
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 209
    .line 210
    .line 211
    move-result-object v15

    .line 212
    invoke-static {v14, v9, v15}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 213
    .line 214
    .line 215
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 216
    .line 217
    .line 218
    move-result-object v9

    .line 219
    invoke-static {v14, v11, v9}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 220
    .line 221
    .line 222
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 223
    .line 224
    .line 225
    move-result-object v9

    .line 226
    invoke-interface {v14}, Landroidx/compose/runtime/j;->B()Z

    .line 227
    .line 228
    .line 229
    move-result v11

    .line 230
    if-nez v11, :cond_d

    .line 231
    .line 232
    invoke-interface {v14}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 233
    .line 234
    .line 235
    move-result-object v11

    .line 236
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 237
    .line 238
    .line 239
    move-result-object v15

    .line 240
    invoke-static {v11, v15}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 241
    .line 242
    .line 243
    move-result v11

    .line 244
    if-nez v11, :cond_e

    .line 245
    .line 246
    :cond_d
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 247
    .line 248
    .line 249
    move-result-object v11

    .line 250
    invoke-interface {v14, v11}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 251
    .line 252
    .line 253
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 254
    .line 255
    .line 256
    move-result-object v10

    .line 257
    invoke-interface {v14, v10, v9}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 258
    .line 259
    .line 260
    :cond_e
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 261
    .line 262
    .line 263
    move-result-object v9

    .line 264
    invoke-static {v14, v6, v9}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 265
    .line 266
    .line 267
    sget-object v6, Landroidx/compose/foundation/layout/n;->a:Landroidx/compose/foundation/layout/n;

    .line 268
    .line 269
    invoke-virtual/range {v21 .. v21}, LA11/a;->U()F

    .line 270
    .line 271
    .line 272
    move-result v6

    .line 273
    invoke-static {v8, v6}, Landroidx/compose/foundation/layout/SizeKt;->v(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 274
    .line 275
    .line 276
    move-result-object v6

    .line 277
    shr-int/lit8 v4, v4, 0x9

    .line 278
    .line 279
    and-int/lit8 v4, v4, 0xe

    .line 280
    .line 281
    or-int/lit8 v18, v4, 0x30

    .line 282
    .line 283
    const/16 v19, 0x0

    .line 284
    .line 285
    const/16 v20, 0x7f8

    .line 286
    .line 287
    const/4 v7, 0x0

    .line 288
    const/4 v9, 0x0

    .line 289
    const/4 v10, 0x0

    .line 290
    const/4 v11, 0x0

    .line 291
    const/4 v12, 0x0

    .line 292
    move-object/from16 v17, v13

    .line 293
    .line 294
    const/4 v13, 0x0

    .line 295
    const/4 v14, 0x0

    .line 296
    const/4 v15, 0x0

    .line 297
    const/16 v16, 0x0

    .line 298
    .line 299
    move-object v4, v8

    .line 300
    move-object v8, v6

    .line 301
    move-object/from16 v6, p3

    .line 302
    .line 303
    invoke-static/range {v6 .. v20}, Lcoil3/compose/r;->a(Ljava/lang/Object;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/e;Landroidx/compose/ui/layout/h;FLandroidx/compose/ui/graphics/w0;IZLandroidx/compose/runtime/j;III)V

    .line 304
    .line 305
    .line 306
    move-object/from16 v13, v17

    .line 307
    .line 308
    const v6, -0x6c87c607

    .line 309
    .line 310
    .line 311
    invoke-interface {v13, v6}, Landroidx/compose/runtime/j;->t(I)V

    .line 312
    .line 313
    .line 314
    if-eqz v3, :cond_f

    .line 315
    .line 316
    if-nez v2, :cond_f

    .line 317
    .line 318
    invoke-virtual/range {v21 .. v21}, LA11/a;->q1()F

    .line 319
    .line 320
    .line 321
    move-result v6

    .line 322
    invoke-static {v4, v6}, Landroidx/compose/foundation/layout/SizeKt;->i(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 323
    .line 324
    .line 325
    move-result-object v6

    .line 326
    invoke-static {v6, v13, v0}, Landroidx/compose/foundation/layout/o0;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;I)V

    .line 327
    .line 328
    .line 329
    invoke-virtual/range {v21 .. v21}, LA11/a;->U()F

    .line 330
    .line 331
    .line 332
    move-result v6

    .line 333
    invoke-static {v4, v6}, Landroidx/compose/foundation/layout/SizeKt;->v(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 334
    .line 335
    .line 336
    move-result-object v8

    .line 337
    sget v4, LlZ0/h;->ic_game_assistant:I

    .line 338
    .line 339
    invoke-static {v4, v13, v0}, Lm0/d;->c(ILandroidx/compose/runtime/j;I)Landroidx/compose/ui/graphics/painter/Painter;

    .line 340
    .line 341
    .line 342
    move-result-object v6

    .line 343
    const/16 v14, 0x30

    .line 344
    .line 345
    const/16 v15, 0x78

    .line 346
    .line 347
    const/4 v7, 0x0

    .line 348
    const/4 v9, 0x0

    .line 349
    const/4 v10, 0x0

    .line 350
    const/4 v11, 0x0

    .line 351
    const/4 v12, 0x0

    .line 352
    invoke-static/range {v6 .. v15}, Landroidx/compose/foundation/ImageKt;->a(Landroidx/compose/ui/graphics/painter/Painter;Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/ui/e;Landroidx/compose/ui/layout/h;FLandroidx/compose/ui/graphics/w0;Landroidx/compose/runtime/j;II)V

    .line 353
    .line 354
    .line 355
    :cond_f
    invoke-interface {v13}, Landroidx/compose/runtime/j;->q()V

    .line 356
    .line 357
    .line 358
    invoke-interface {v13}, Landroidx/compose/runtime/j;->j()V

    .line 359
    .line 360
    .line 361
    invoke-interface {v13}, Landroidx/compose/runtime/j;->q()V

    .line 362
    .line 363
    .line 364
    goto :goto_8

    .line 365
    :cond_10
    const v4, 0x34caff29

    .line 366
    .line 367
    .line 368
    invoke-interface {v13, v4}, Landroidx/compose/runtime/j;->t(I)V

    .line 369
    .line 370
    .line 371
    sget-object v4, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 372
    .line 373
    sget-object v6, LA11/a;->a:LA11/a;

    .line 374
    .line 375
    invoke-virtual {v6}, LA11/a;->v0()F

    .line 376
    .line 377
    .line 378
    move-result v6

    .line 379
    invoke-static {v4, v6}, Landroidx/compose/foundation/layout/SizeKt;->A(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 380
    .line 381
    .line 382
    move-result-object v4

    .line 383
    invoke-static {v4, v13, v0}, Landroidx/compose/foundation/layout/o0;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;I)V

    .line 384
    .line 385
    .line 386
    invoke-interface {v13}, Landroidx/compose/runtime/j;->q()V

    .line 387
    .line 388
    .line 389
    :goto_8
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 390
    .line 391
    .line 392
    move-result v0

    .line 393
    if-eqz v0, :cond_11

    .line 394
    .line 395
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 396
    .line 397
    .line 398
    :cond_11
    :goto_9
    invoke-interface {v13}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 399
    .line 400
    .line 401
    move-result-object v6

    .line 402
    if-eqz v6, :cond_12

    .line 403
    .line 404
    new-instance v0, Lorg/xbet/uikit_sport/compose/sport_game_events/j;

    .line 405
    .line 406
    move-object/from16 v4, p3

    .line 407
    .line 408
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/compose/sport_game_events/j;-><init>(ZZZLjava/lang/String;I)V

    .line 409
    .line 410
    .line 411
    invoke-interface {v6, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 412
    .line 413
    .line 414
    :cond_12
    return-void
.end method

.method public static final q(ZZZLjava/lang/String;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 6

    .line 1
    or-int/lit8 p4, p4, 0x1

    invoke-static {p4}, Landroidx/compose/runtime/A0;->a(I)I

    move-result v5

    move v0, p0

    move v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p5

    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->p(ZZZLjava/lang/String;Landroidx/compose/runtime/j;I)V

    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p0
.end method

.method public static final r(ZZZLjava/lang/String;Landroidx/compose/runtime/j;I)V
    .locals 22
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move/from16 v1, p0

    .line 2
    .line 3
    move/from16 v2, p1

    .line 4
    .line 5
    move/from16 v3, p2

    .line 6
    .line 7
    move/from16 v5, p5

    .line 8
    .line 9
    const v0, -0x6f53b263

    .line 10
    .line 11
    .line 12
    move-object/from16 v4, p4

    .line 13
    .line 14
    invoke-interface {v4, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 15
    .line 16
    .line 17
    move-result-object v13

    .line 18
    and-int/lit8 v4, v5, 0x6

    .line 19
    .line 20
    const/4 v6, 0x2

    .line 21
    if-nez v4, :cond_1

    .line 22
    .line 23
    invoke-interface {v13, v1}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 24
    .line 25
    .line 26
    move-result v4

    .line 27
    if-eqz v4, :cond_0

    .line 28
    .line 29
    const/4 v4, 0x4

    .line 30
    goto :goto_0

    .line 31
    :cond_0
    const/4 v4, 0x2

    .line 32
    :goto_0
    or-int/2addr v4, v5

    .line 33
    goto :goto_1

    .line 34
    :cond_1
    move v4, v5

    .line 35
    :goto_1
    and-int/lit8 v7, v5, 0x30

    .line 36
    .line 37
    if-nez v7, :cond_3

    .line 38
    .line 39
    invoke-interface {v13, v2}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 40
    .line 41
    .line 42
    move-result v7

    .line 43
    if-eqz v7, :cond_2

    .line 44
    .line 45
    const/16 v7, 0x20

    .line 46
    .line 47
    goto :goto_2

    .line 48
    :cond_2
    const/16 v7, 0x10

    .line 49
    .line 50
    :goto_2
    or-int/2addr v4, v7

    .line 51
    :cond_3
    and-int/lit16 v7, v5, 0x180

    .line 52
    .line 53
    if-nez v7, :cond_5

    .line 54
    .line 55
    invoke-interface {v13, v3}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 56
    .line 57
    .line 58
    move-result v7

    .line 59
    if-eqz v7, :cond_4

    .line 60
    .line 61
    const/16 v7, 0x100

    .line 62
    .line 63
    goto :goto_3

    .line 64
    :cond_4
    const/16 v7, 0x80

    .line 65
    .line 66
    :goto_3
    or-int/2addr v4, v7

    .line 67
    :cond_5
    and-int/lit16 v7, v5, 0xc00

    .line 68
    .line 69
    if-nez v7, :cond_7

    .line 70
    .line 71
    move-object/from16 v7, p3

    .line 72
    .line 73
    invoke-interface {v13, v7}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 74
    .line 75
    .line 76
    move-result v8

    .line 77
    if-eqz v8, :cond_6

    .line 78
    .line 79
    const/16 v8, 0x800

    .line 80
    .line 81
    goto :goto_4

    .line 82
    :cond_6
    const/16 v8, 0x400

    .line 83
    .line 84
    :goto_4
    or-int/2addr v4, v8

    .line 85
    goto :goto_5

    .line 86
    :cond_7
    move-object/from16 v7, p3

    .line 87
    .line 88
    :goto_5
    and-int/lit16 v8, v4, 0x493

    .line 89
    .line 90
    const/16 v9, 0x492

    .line 91
    .line 92
    if-ne v8, v9, :cond_9

    .line 93
    .line 94
    invoke-interface {v13}, Landroidx/compose/runtime/j;->c()Z

    .line 95
    .line 96
    .line 97
    move-result v8

    .line 98
    if-nez v8, :cond_8

    .line 99
    .line 100
    goto :goto_6

    .line 101
    :cond_8
    invoke-interface {v13}, Landroidx/compose/runtime/j;->n()V

    .line 102
    .line 103
    .line 104
    goto/16 :goto_9

    .line 105
    .line 106
    :cond_9
    :goto_6
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 107
    .line 108
    .line 109
    move-result v8

    .line 110
    if-eqz v8, :cond_a

    .line 111
    .line 112
    const/4 v8, -0x1

    .line 113
    const-string v9, "org.xbet.uikit_sport.compose.sport_game_events.EventRight (EventComponent.kt:189)"

    .line 114
    .line 115
    invoke-static {v0, v4, v8, v9}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 116
    .line 117
    .line 118
    :cond_a
    const/4 v0, 0x0

    .line 119
    if-eqz v1, :cond_10

    .line 120
    .line 121
    const v8, -0x3ce16765

    .line 122
    .line 123
    .line 124
    invoke-interface {v13, v8}, Landroidx/compose/runtime/j;->t(I)V

    .line 125
    .line 126
    .line 127
    sget-object v8, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 128
    .line 129
    sget-object v21, LA11/a;->a:LA11/a;

    .line 130
    .line 131
    invoke-virtual/range {v21 .. v21}, LA11/a;->L1()F

    .line 132
    .line 133
    .line 134
    move-result v9

    .line 135
    const/4 v10, 0x0

    .line 136
    const/4 v11, 0x0

    .line 137
    invoke-static {v8, v9, v10, v6, v11}, Landroidx/compose/foundation/layout/PaddingKt;->k(Landroidx/compose/ui/l;FFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 138
    .line 139
    .line 140
    move-result-object v6

    .line 141
    sget-object v9, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 142
    .line 143
    invoke-virtual {v9}, Landroidx/compose/foundation/layout/Arrangement;->h()Landroidx/compose/foundation/layout/Arrangement$m;

    .line 144
    .line 145
    .line 146
    move-result-object v9

    .line 147
    sget-object v10, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 148
    .line 149
    invoke-virtual {v10}, Landroidx/compose/ui/e$a;->k()Landroidx/compose/ui/e$b;

    .line 150
    .line 151
    .line 152
    move-result-object v10

    .line 153
    invoke-static {v9, v10, v13, v0}, Landroidx/compose/foundation/layout/k;->a(Landroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 154
    .line 155
    .line 156
    move-result-object v9

    .line 157
    invoke-static {v13, v0}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 158
    .line 159
    .line 160
    move-result v10

    .line 161
    invoke-interface {v13}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 162
    .line 163
    .line 164
    move-result-object v11

    .line 165
    invoke-static {v13, v6}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 166
    .line 167
    .line 168
    move-result-object v6

    .line 169
    sget-object v12, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 170
    .line 171
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 172
    .line 173
    .line 174
    move-result-object v14

    .line 175
    invoke-interface {v13}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 176
    .line 177
    .line 178
    move-result-object v15

    .line 179
    invoke-static {v15}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 180
    .line 181
    .line 182
    move-result v15

    .line 183
    if-nez v15, :cond_b

    .line 184
    .line 185
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 186
    .line 187
    .line 188
    :cond_b
    invoke-interface {v13}, Landroidx/compose/runtime/j;->l()V

    .line 189
    .line 190
    .line 191
    invoke-interface {v13}, Landroidx/compose/runtime/j;->B()Z

    .line 192
    .line 193
    .line 194
    move-result v15

    .line 195
    if-eqz v15, :cond_c

    .line 196
    .line 197
    invoke-interface {v13, v14}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 198
    .line 199
    .line 200
    goto :goto_7

    .line 201
    :cond_c
    invoke-interface {v13}, Landroidx/compose/runtime/j;->h()V

    .line 202
    .line 203
    .line 204
    :goto_7
    invoke-static {v13}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 205
    .line 206
    .line 207
    move-result-object v14

    .line 208
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 209
    .line 210
    .line 211
    move-result-object v15

    .line 212
    invoke-static {v14, v9, v15}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 213
    .line 214
    .line 215
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 216
    .line 217
    .line 218
    move-result-object v9

    .line 219
    invoke-static {v14, v11, v9}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 220
    .line 221
    .line 222
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 223
    .line 224
    .line 225
    move-result-object v9

    .line 226
    invoke-interface {v14}, Landroidx/compose/runtime/j;->B()Z

    .line 227
    .line 228
    .line 229
    move-result v11

    .line 230
    if-nez v11, :cond_d

    .line 231
    .line 232
    invoke-interface {v14}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 233
    .line 234
    .line 235
    move-result-object v11

    .line 236
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 237
    .line 238
    .line 239
    move-result-object v15

    .line 240
    invoke-static {v11, v15}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 241
    .line 242
    .line 243
    move-result v11

    .line 244
    if-nez v11, :cond_e

    .line 245
    .line 246
    :cond_d
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 247
    .line 248
    .line 249
    move-result-object v11

    .line 250
    invoke-interface {v14, v11}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 251
    .line 252
    .line 253
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 254
    .line 255
    .line 256
    move-result-object v10

    .line 257
    invoke-interface {v14, v10, v9}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 258
    .line 259
    .line 260
    :cond_e
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 261
    .line 262
    .line 263
    move-result-object v9

    .line 264
    invoke-static {v14, v6, v9}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 265
    .line 266
    .line 267
    sget-object v6, Landroidx/compose/foundation/layout/n;->a:Landroidx/compose/foundation/layout/n;

    .line 268
    .line 269
    invoke-virtual/range {v21 .. v21}, LA11/a;->U()F

    .line 270
    .line 271
    .line 272
    move-result v6

    .line 273
    invoke-static {v8, v6}, Landroidx/compose/foundation/layout/SizeKt;->v(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 274
    .line 275
    .line 276
    move-result-object v6

    .line 277
    shr-int/lit8 v4, v4, 0x9

    .line 278
    .line 279
    and-int/lit8 v4, v4, 0xe

    .line 280
    .line 281
    or-int/lit8 v18, v4, 0x30

    .line 282
    .line 283
    const/16 v19, 0x0

    .line 284
    .line 285
    const/16 v20, 0x7f8

    .line 286
    .line 287
    const/4 v7, 0x0

    .line 288
    const/4 v9, 0x0

    .line 289
    const/4 v10, 0x0

    .line 290
    const/4 v11, 0x0

    .line 291
    const/4 v12, 0x0

    .line 292
    move-object/from16 v17, v13

    .line 293
    .line 294
    const/4 v13, 0x0

    .line 295
    const/4 v14, 0x0

    .line 296
    const/4 v15, 0x0

    .line 297
    const/16 v16, 0x0

    .line 298
    .line 299
    move-object v4, v8

    .line 300
    move-object v8, v6

    .line 301
    move-object/from16 v6, p3

    .line 302
    .line 303
    invoke-static/range {v6 .. v20}, Lcoil3/compose/r;->a(Ljava/lang/Object;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/e;Landroidx/compose/ui/layout/h;FLandroidx/compose/ui/graphics/w0;IZLandroidx/compose/runtime/j;III)V

    .line 304
    .line 305
    .line 306
    move-object/from16 v13, v17

    .line 307
    .line 308
    const v6, -0x4264048d

    .line 309
    .line 310
    .line 311
    invoke-interface {v13, v6}, Landroidx/compose/runtime/j;->t(I)V

    .line 312
    .line 313
    .line 314
    if-eqz v3, :cond_f

    .line 315
    .line 316
    if-nez v2, :cond_f

    .line 317
    .line 318
    invoke-virtual/range {v21 .. v21}, LA11/a;->q1()F

    .line 319
    .line 320
    .line 321
    move-result v6

    .line 322
    invoke-static {v4, v6}, Landroidx/compose/foundation/layout/SizeKt;->i(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 323
    .line 324
    .line 325
    move-result-object v6

    .line 326
    invoke-static {v6, v13, v0}, Landroidx/compose/foundation/layout/o0;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;I)V

    .line 327
    .line 328
    .line 329
    invoke-virtual/range {v21 .. v21}, LA11/a;->U()F

    .line 330
    .line 331
    .line 332
    move-result v6

    .line 333
    invoke-static {v4, v6}, Landroidx/compose/foundation/layout/SizeKt;->v(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 334
    .line 335
    .line 336
    move-result-object v8

    .line 337
    sget v4, LlZ0/h;->ic_game_assistant:I

    .line 338
    .line 339
    invoke-static {v4, v13, v0}, Lm0/d;->c(ILandroidx/compose/runtime/j;I)Landroidx/compose/ui/graphics/painter/Painter;

    .line 340
    .line 341
    .line 342
    move-result-object v6

    .line 343
    const/16 v14, 0x30

    .line 344
    .line 345
    const/16 v15, 0x78

    .line 346
    .line 347
    const/4 v7, 0x0

    .line 348
    const/4 v9, 0x0

    .line 349
    const/4 v10, 0x0

    .line 350
    const/4 v11, 0x0

    .line 351
    const/4 v12, 0x0

    .line 352
    invoke-static/range {v6 .. v15}, Landroidx/compose/foundation/ImageKt;->a(Landroidx/compose/ui/graphics/painter/Painter;Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/ui/e;Landroidx/compose/ui/layout/h;FLandroidx/compose/ui/graphics/w0;Landroidx/compose/runtime/j;II)V

    .line 353
    .line 354
    .line 355
    :cond_f
    invoke-interface {v13}, Landroidx/compose/runtime/j;->q()V

    .line 356
    .line 357
    .line 358
    invoke-interface {v13}, Landroidx/compose/runtime/j;->j()V

    .line 359
    .line 360
    .line 361
    invoke-interface {v13}, Landroidx/compose/runtime/j;->q()V

    .line 362
    .line 363
    .line 364
    goto :goto_8

    .line 365
    :cond_10
    const v4, -0x3cd8b174

    .line 366
    .line 367
    .line 368
    invoke-interface {v13, v4}, Landroidx/compose/runtime/j;->t(I)V

    .line 369
    .line 370
    .line 371
    sget-object v4, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 372
    .line 373
    sget-object v6, LA11/a;->a:LA11/a;

    .line 374
    .line 375
    invoke-virtual {v6}, LA11/a;->v0()F

    .line 376
    .line 377
    .line 378
    move-result v6

    .line 379
    invoke-static {v4, v6}, Landroidx/compose/foundation/layout/SizeKt;->A(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 380
    .line 381
    .line 382
    move-result-object v4

    .line 383
    invoke-static {v4, v13, v0}, Landroidx/compose/foundation/layout/o0;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;I)V

    .line 384
    .line 385
    .line 386
    invoke-interface {v13}, Landroidx/compose/runtime/j;->q()V

    .line 387
    .line 388
    .line 389
    :goto_8
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 390
    .line 391
    .line 392
    move-result v0

    .line 393
    if-eqz v0, :cond_11

    .line 394
    .line 395
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 396
    .line 397
    .line 398
    :cond_11
    :goto_9
    invoke-interface {v13}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 399
    .line 400
    .line 401
    move-result-object v6

    .line 402
    if-eqz v6, :cond_12

    .line 403
    .line 404
    new-instance v0, Lorg/xbet/uikit_sport/compose/sport_game_events/n;

    .line 405
    .line 406
    move-object/from16 v4, p3

    .line 407
    .line 408
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/compose/sport_game_events/n;-><init>(ZZZLjava/lang/String;I)V

    .line 409
    .line 410
    .line 411
    invoke-interface {v6, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 412
    .line 413
    .line 414
    :cond_12
    return-void
.end method

.method public static final s(ZZZLjava/lang/String;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 6

    .line 1
    or-int/lit8 p4, p4, 0x1

    invoke-static {p4}, Landroidx/compose/runtime/A0;->a(I)I

    move-result v5

    move v0, p0

    move v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p5

    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->r(ZZZLjava/lang/String;Landroidx/compose/runtime/j;I)V

    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p0
.end method

.method public static final t(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V
    .locals 20
    .param p0    # Ls31/a$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ls31/a$b;",
            "Z",
            "Landroidx/compose/ui/l;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    move/from16 v2, p1

    .line 4
    .line 5
    move-object/from16 v4, p3

    .line 6
    .line 7
    move/from16 v5, p5

    .line 8
    .line 9
    const/4 v0, 0x2

    .line 10
    const v3, 0x3cbea8fd

    .line 11
    .line 12
    .line 13
    move-object/from16 v6, p4

    .line 14
    .line 15
    invoke-interface {v6, v3}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 16
    .line 17
    .line 18
    move-result-object v10

    .line 19
    const/4 v13, 0x1

    .line 20
    and-int/lit8 v6, p6, 0x1

    .line 21
    .line 22
    const/4 v14, 0x4

    .line 23
    if-eqz v6, :cond_0

    .line 24
    .line 25
    or-int/lit8 v6, v5, 0x6

    .line 26
    .line 27
    goto :goto_2

    .line 28
    :cond_0
    and-int/lit8 v6, v5, 0x6

    .line 29
    .line 30
    if-nez v6, :cond_3

    .line 31
    .line 32
    and-int/lit8 v6, v5, 0x8

    .line 33
    .line 34
    if-nez v6, :cond_1

    .line 35
    .line 36
    invoke-interface {v10, v1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 37
    .line 38
    .line 39
    move-result v6

    .line 40
    goto :goto_0

    .line 41
    :cond_1
    invoke-interface {v10, v1}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 42
    .line 43
    .line 44
    move-result v6

    .line 45
    :goto_0
    if-eqz v6, :cond_2

    .line 46
    .line 47
    const/4 v6, 0x4

    .line 48
    goto :goto_1

    .line 49
    :cond_2
    const/4 v6, 0x2

    .line 50
    :goto_1
    or-int/2addr v6, v5

    .line 51
    goto :goto_2

    .line 52
    :cond_3
    move v6, v5

    .line 53
    :goto_2
    and-int/lit8 v0, p6, 0x2

    .line 54
    .line 55
    if-eqz v0, :cond_4

    .line 56
    .line 57
    or-int/lit8 v6, v6, 0x30

    .line 58
    .line 59
    goto :goto_4

    .line 60
    :cond_4
    and-int/lit8 v0, v5, 0x30

    .line 61
    .line 62
    if-nez v0, :cond_6

    .line 63
    .line 64
    invoke-interface {v10, v2}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 65
    .line 66
    .line 67
    move-result v0

    .line 68
    if-eqz v0, :cond_5

    .line 69
    .line 70
    const/16 v0, 0x20

    .line 71
    .line 72
    goto :goto_3

    .line 73
    :cond_5
    const/16 v0, 0x10

    .line 74
    .line 75
    :goto_3
    or-int/2addr v6, v0

    .line 76
    :cond_6
    :goto_4
    and-int/lit8 v0, p6, 0x4

    .line 77
    .line 78
    if-eqz v0, :cond_8

    .line 79
    .line 80
    or-int/lit16 v6, v6, 0x180

    .line 81
    .line 82
    :cond_7
    move-object/from16 v7, p2

    .line 83
    .line 84
    goto :goto_6

    .line 85
    :cond_8
    and-int/lit16 v7, v5, 0x180

    .line 86
    .line 87
    if-nez v7, :cond_7

    .line 88
    .line 89
    move-object/from16 v7, p2

    .line 90
    .line 91
    invoke-interface {v10, v7}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 92
    .line 93
    .line 94
    move-result v8

    .line 95
    if-eqz v8, :cond_9

    .line 96
    .line 97
    const/16 v8, 0x100

    .line 98
    .line 99
    goto :goto_5

    .line 100
    :cond_9
    const/16 v8, 0x80

    .line 101
    .line 102
    :goto_5
    or-int/2addr v6, v8

    .line 103
    :goto_6
    and-int/lit8 v8, p6, 0x8

    .line 104
    .line 105
    const/16 v15, 0x800

    .line 106
    .line 107
    if-eqz v8, :cond_a

    .line 108
    .line 109
    or-int/lit16 v6, v6, 0xc00

    .line 110
    .line 111
    goto :goto_8

    .line 112
    :cond_a
    and-int/lit16 v8, v5, 0xc00

    .line 113
    .line 114
    if-nez v8, :cond_c

    .line 115
    .line 116
    invoke-interface {v10, v4}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 117
    .line 118
    .line 119
    move-result v8

    .line 120
    if-eqz v8, :cond_b

    .line 121
    .line 122
    const/16 v8, 0x800

    .line 123
    .line 124
    goto :goto_7

    .line 125
    :cond_b
    const/16 v8, 0x400

    .line 126
    .line 127
    :goto_7
    or-int/2addr v6, v8

    .line 128
    :cond_c
    :goto_8
    and-int/lit16 v8, v6, 0x493

    .line 129
    .line 130
    const/16 v9, 0x492

    .line 131
    .line 132
    if-ne v8, v9, :cond_e

    .line 133
    .line 134
    invoke-interface {v10}, Landroidx/compose/runtime/j;->c()Z

    .line 135
    .line 136
    .line 137
    move-result v8

    .line 138
    if-nez v8, :cond_d

    .line 139
    .line 140
    goto :goto_9

    .line 141
    :cond_d
    invoke-interface {v10}, Landroidx/compose/runtime/j;->n()V

    .line 142
    .line 143
    .line 144
    move-object v3, v7

    .line 145
    goto/16 :goto_16

    .line 146
    .line 147
    :cond_e
    :goto_9
    if-eqz v0, :cond_f

    .line 148
    .line 149
    sget-object v0, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 150
    .line 151
    goto :goto_a

    .line 152
    :cond_f
    move-object v0, v7

    .line 153
    :goto_a
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 154
    .line 155
    .line 156
    move-result v7

    .line 157
    if-eqz v7, :cond_10

    .line 158
    .line 159
    const/4 v7, -0x1

    .line 160
    const-string v8, "org.xbet.uikit_sport.compose.sport_game_events.LeftPlayer (EventComponent.kt:95)"

    .line 161
    .line 162
    invoke-static {v3, v6, v7, v8}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 163
    .line 164
    .line 165
    :cond_10
    if-eqz v2, :cond_23

    .line 166
    .line 167
    const v3, -0x10a88d50

    .line 168
    .line 169
    .line 170
    invoke-interface {v10, v3}, Landroidx/compose/runtime/j;->t(I)V

    .line 171
    .line 172
    .line 173
    const/4 v3, 0x0

    .line 174
    const/4 v7, 0x0

    .line 175
    invoke-static {v0, v3, v13, v7}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 176
    .line 177
    .line 178
    move-result-object v8

    .line 179
    sget-object v16, LA11/a;->a:LA11/a;

    .line 180
    .line 181
    invoke-virtual/range {v16 .. v16}, LA11/a;->L1()F

    .line 182
    .line 183
    .line 184
    move-result v9

    .line 185
    invoke-static {v8, v3, v9, v13, v7}, Landroidx/compose/foundation/layout/PaddingKt;->k(Landroidx/compose/ui/l;FFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 186
    .line 187
    .line 188
    move-result-object v3

    .line 189
    sget-object v7, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 190
    .line 191
    invoke-virtual {v7}, Landroidx/compose/foundation/layout/Arrangement;->h()Landroidx/compose/foundation/layout/Arrangement$m;

    .line 192
    .line 193
    .line 194
    move-result-object v7

    .line 195
    sget-object v8, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 196
    .line 197
    invoke-virtual {v8}, Landroidx/compose/ui/e$a;->k()Landroidx/compose/ui/e$b;

    .line 198
    .line 199
    .line 200
    move-result-object v8

    .line 201
    const/4 v9, 0x0

    .line 202
    invoke-static {v7, v8, v10, v9}, Landroidx/compose/foundation/layout/k;->a(Landroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 203
    .line 204
    .line 205
    move-result-object v7

    .line 206
    invoke-static {v10, v9}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 207
    .line 208
    .line 209
    move-result v8

    .line 210
    invoke-interface {v10}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 211
    .line 212
    .line 213
    move-result-object v11

    .line 214
    invoke-static {v10, v3}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 215
    .line 216
    .line 217
    move-result-object v3

    .line 218
    sget-object v12, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 219
    .line 220
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 221
    .line 222
    .line 223
    move-result-object v9

    .line 224
    invoke-interface {v10}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 225
    .line 226
    .line 227
    move-result-object v17

    .line 228
    invoke-static/range {v17 .. v17}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 229
    .line 230
    .line 231
    move-result v17

    .line 232
    if-nez v17, :cond_11

    .line 233
    .line 234
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 235
    .line 236
    .line 237
    :cond_11
    invoke-interface {v10}, Landroidx/compose/runtime/j;->l()V

    .line 238
    .line 239
    .line 240
    invoke-interface {v10}, Landroidx/compose/runtime/j;->B()Z

    .line 241
    .line 242
    .line 243
    move-result v17

    .line 244
    if-eqz v17, :cond_12

    .line 245
    .line 246
    invoke-interface {v10, v9}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 247
    .line 248
    .line 249
    goto :goto_b

    .line 250
    :cond_12
    invoke-interface {v10}, Landroidx/compose/runtime/j;->h()V

    .line 251
    .line 252
    .line 253
    :goto_b
    invoke-static {v10}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 254
    .line 255
    .line 256
    move-result-object v9

    .line 257
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 258
    .line 259
    .line 260
    move-result-object v13

    .line 261
    invoke-static {v9, v7, v13}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 262
    .line 263
    .line 264
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 265
    .line 266
    .line 267
    move-result-object v7

    .line 268
    invoke-static {v9, v11, v7}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 269
    .line 270
    .line 271
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 272
    .line 273
    .line 274
    move-result-object v7

    .line 275
    invoke-interface {v9}, Landroidx/compose/runtime/j;->B()Z

    .line 276
    .line 277
    .line 278
    move-result v11

    .line 279
    if-nez v11, :cond_13

    .line 280
    .line 281
    invoke-interface {v9}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 282
    .line 283
    .line 284
    move-result-object v11

    .line 285
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 286
    .line 287
    .line 288
    move-result-object v13

    .line 289
    invoke-static {v11, v13}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 290
    .line 291
    .line 292
    move-result v11

    .line 293
    if-nez v11, :cond_14

    .line 294
    .line 295
    :cond_13
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 296
    .line 297
    .line 298
    move-result-object v11

    .line 299
    invoke-interface {v9, v11}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 300
    .line 301
    .line 302
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 303
    .line 304
    .line 305
    move-result-object v8

    .line 306
    invoke-interface {v9, v8, v7}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 307
    .line 308
    .line 309
    :cond_14
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 310
    .line 311
    .line 312
    move-result-object v7

    .line 313
    invoke-static {v9, v3, v7}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 314
    .line 315
    .line 316
    sget-object v3, Landroidx/compose/foundation/layout/n;->a:Landroidx/compose/foundation/layout/n;

    .line 317
    .line 318
    invoke-virtual {v1}, Ls31/a$b;->e()LM11/b;

    .line 319
    .line 320
    .line 321
    move-result-object v3

    .line 322
    invoke-virtual {v3}, LM11/b;->c()Ljava/lang/String;

    .line 323
    .line 324
    .line 325
    move-result-object v3

    .line 326
    invoke-interface {v3}, Ljava/lang/CharSequence;->length()I

    .line 327
    .line 328
    .line 329
    move-result v7

    .line 330
    if-nez v7, :cond_15

    .line 331
    .line 332
    const/4 v7, 0x1

    .line 333
    goto :goto_c

    .line 334
    :cond_15
    const/4 v7, 0x0

    .line 335
    :goto_c
    if-eqz v7, :cond_16

    .line 336
    .line 337
    invoke-virtual {v1}, Ls31/a$b;->f()LM11/d;

    .line 338
    .line 339
    .line 340
    move-result-object v3

    .line 341
    invoke-virtual {v3}, LM11/d;->a()Ljava/lang/String;

    .line 342
    .line 343
    .line 344
    move-result-object v3

    .line 345
    :cond_16
    invoke-virtual {v1}, Ls31/a$b;->e()LM11/b;

    .line 346
    .line 347
    .line 348
    move-result-object v7

    .line 349
    invoke-virtual {v7}, LM11/b;->b()Ljava/lang/String;

    .line 350
    .line 351
    .line 352
    move-result-object v7

    .line 353
    const v13, -0x615d173a

    .line 354
    .line 355
    .line 356
    invoke-interface {v10, v13}, Landroidx/compose/runtime/j;->t(I)V

    .line 357
    .line 358
    .line 359
    and-int/lit16 v8, v6, 0x1c00

    .line 360
    .line 361
    if-ne v8, v15, :cond_17

    .line 362
    .line 363
    const/4 v9, 0x1

    .line 364
    goto :goto_d

    .line 365
    :cond_17
    const/4 v9, 0x0

    .line 366
    :goto_d
    and-int/lit8 v11, v6, 0xe

    .line 367
    .line 368
    if-eq v11, v14, :cond_19

    .line 369
    .line 370
    and-int/lit8 v12, v6, 0x8

    .line 371
    .line 372
    if-eqz v12, :cond_18

    .line 373
    .line 374
    invoke-interface {v10, v1}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 375
    .line 376
    .line 377
    move-result v12

    .line 378
    if-eqz v12, :cond_18

    .line 379
    .line 380
    goto :goto_e

    .line 381
    :cond_18
    const/4 v12, 0x0

    .line 382
    goto :goto_f

    .line 383
    :cond_19
    :goto_e
    const/4 v12, 0x1

    .line 384
    :goto_f
    or-int/2addr v9, v12

    .line 385
    invoke-interface {v10}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 386
    .line 387
    .line 388
    move-result-object v12

    .line 389
    if-nez v9, :cond_1a

    .line 390
    .line 391
    sget-object v9, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 392
    .line 393
    invoke-virtual {v9}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 394
    .line 395
    .line 396
    move-result-object v9

    .line 397
    if-ne v12, v9, :cond_1b

    .line 398
    .line 399
    :cond_1a
    new-instance v12, Lorg/xbet/uikit_sport/compose/sport_game_events/k;

    .line 400
    .line 401
    invoke-direct {v12, v4, v1}, Lorg/xbet/uikit_sport/compose/sport_game_events/k;-><init>(Lkotlin/jvm/functions/Function1;Ls31/a$b;)V

    .line 402
    .line 403
    .line 404
    invoke-interface {v10, v12}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 405
    .line 406
    .line 407
    :cond_1b
    move-object v9, v12

    .line 408
    check-cast v9, Lkotlin/jvm/functions/Function0;

    .line 409
    .line 410
    invoke-interface {v10}, Landroidx/compose/runtime/j;->q()V

    .line 411
    .line 412
    .line 413
    move v12, v11

    .line 414
    const/4 v11, 0x0

    .line 415
    move/from16 v17, v12

    .line 416
    .line 417
    const/4 v12, 0x4

    .line 418
    move/from16 v18, v8

    .line 419
    .line 420
    const/4 v8, 0x0

    .line 421
    move/from16 v19, v17

    .line 422
    .line 423
    move/from16 v14, v18

    .line 424
    .line 425
    move/from16 v17, v6

    .line 426
    .line 427
    move-object v6, v3

    .line 428
    const/4 v3, 0x0

    .line 429
    invoke-static/range {v6 .. v12}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->x(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V

    .line 430
    .line 431
    .line 432
    const v6, 0x1fa8a39f

    .line 433
    .line 434
    .line 435
    invoke-interface {v10, v6}, Landroidx/compose/runtime/j;->t(I)V

    .line 436
    .line 437
    .line 438
    invoke-virtual {v1}, Ls31/a$b;->b()LM11/b;

    .line 439
    .line 440
    .line 441
    move-result-object v6

    .line 442
    invoke-virtual {v6}, LM11/b;->a()Ljava/lang/String;

    .line 443
    .line 444
    .line 445
    move-result-object v6

    .line 446
    invoke-interface {v6}, Ljava/lang/CharSequence;->length()I

    .line 447
    .line 448
    .line 449
    move-result v6

    .line 450
    if-lez v6, :cond_1c

    .line 451
    .line 452
    const/4 v9, 0x1

    .line 453
    goto :goto_10

    .line 454
    :cond_1c
    const/4 v9, 0x0

    .line 455
    :goto_10
    if-eqz v9, :cond_22

    .line 456
    .line 457
    sget-object v6, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 458
    .line 459
    invoke-virtual/range {v16 .. v16}, LA11/a;->L1()F

    .line 460
    .line 461
    .line 462
    move-result v7

    .line 463
    invoke-static {v6, v7}, Landroidx/compose/foundation/layout/SizeKt;->i(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 464
    .line 465
    .line 466
    move-result-object v6

    .line 467
    invoke-static {v6, v10, v3}, Landroidx/compose/foundation/layout/o0;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;I)V

    .line 468
    .line 469
    .line 470
    invoke-virtual {v1}, Ls31/a$b;->b()LM11/b;

    .line 471
    .line 472
    .line 473
    move-result-object v6

    .line 474
    invoke-virtual {v6}, LM11/b;->c()Ljava/lang/String;

    .line 475
    .line 476
    .line 477
    move-result-object v6

    .line 478
    invoke-virtual {v1}, Ls31/a$b;->b()LM11/b;

    .line 479
    .line 480
    .line 481
    move-result-object v7

    .line 482
    invoke-virtual {v7}, LM11/b;->b()Ljava/lang/String;

    .line 483
    .line 484
    .line 485
    move-result-object v7

    .line 486
    invoke-virtual {v1}, Ls31/a$b;->h()Z

    .line 487
    .line 488
    .line 489
    move-result v8

    .line 490
    invoke-interface {v10, v13}, Landroidx/compose/runtime/j;->t(I)V

    .line 491
    .line 492
    .line 493
    if-ne v14, v15, :cond_1d

    .line 494
    .line 495
    const/4 v9, 0x1

    .line 496
    :goto_11
    move/from16 v12, v19

    .line 497
    .line 498
    const/4 v11, 0x4

    .line 499
    goto :goto_12

    .line 500
    :cond_1d
    const/4 v9, 0x0

    .line 501
    goto :goto_11

    .line 502
    :goto_12
    if-eq v12, v11, :cond_1f

    .line 503
    .line 504
    and-int/lit8 v11, v17, 0x8

    .line 505
    .line 506
    if-eqz v11, :cond_1e

    .line 507
    .line 508
    invoke-interface {v10, v1}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 509
    .line 510
    .line 511
    move-result v11

    .line 512
    if-eqz v11, :cond_1e

    .line 513
    .line 514
    goto :goto_13

    .line 515
    :cond_1e
    const/4 v13, 0x0

    .line 516
    goto :goto_14

    .line 517
    :cond_1f
    :goto_13
    const/4 v13, 0x1

    .line 518
    :goto_14
    or-int v3, v9, v13

    .line 519
    .line 520
    invoke-interface {v10}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 521
    .line 522
    .line 523
    move-result-object v9

    .line 524
    if-nez v3, :cond_20

    .line 525
    .line 526
    sget-object v3, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 527
    .line 528
    invoke-virtual {v3}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 529
    .line 530
    .line 531
    move-result-object v3

    .line 532
    if-ne v9, v3, :cond_21

    .line 533
    .line 534
    :cond_20
    new-instance v9, Lorg/xbet/uikit_sport/compose/sport_game_events/l;

    .line 535
    .line 536
    invoke-direct {v9, v4, v1}, Lorg/xbet/uikit_sport/compose/sport_game_events/l;-><init>(Lkotlin/jvm/functions/Function1;Ls31/a$b;)V

    .line 537
    .line 538
    .line 539
    invoke-interface {v10, v9}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 540
    .line 541
    .line 542
    :cond_21
    check-cast v9, Lkotlin/jvm/functions/Function0;

    .line 543
    .line 544
    invoke-interface {v10}, Landroidx/compose/runtime/j;->q()V

    .line 545
    .line 546
    .line 547
    const/4 v12, 0x0

    .line 548
    const/16 v13, 0x8

    .line 549
    .line 550
    move-object v11, v10

    .line 551
    move-object v10, v9

    .line 552
    const/4 v9, 0x0

    .line 553
    invoke-static/range {v6 .. v13}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->y(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V

    .line 554
    .line 555
    .line 556
    move-object v10, v11

    .line 557
    :cond_22
    invoke-interface {v10}, Landroidx/compose/runtime/j;->q()V

    .line 558
    .line 559
    .line 560
    invoke-interface {v10}, Landroidx/compose/runtime/j;->j()V

    .line 561
    .line 562
    .line 563
    invoke-interface {v10}, Landroidx/compose/runtime/j;->q()V

    .line 564
    .line 565
    .line 566
    goto :goto_15

    .line 567
    :cond_23
    move/from16 v17, v6

    .line 568
    .line 569
    const v3, -0x109afe86

    .line 570
    .line 571
    .line 572
    invoke-interface {v10, v3}, Landroidx/compose/runtime/j;->t(I)V

    .line 573
    .line 574
    .line 575
    shr-int/lit8 v3, v17, 0x6

    .line 576
    .line 577
    and-int/lit8 v3, v3, 0xe

    .line 578
    .line 579
    invoke-static {v0, v10, v3}, Landroidx/compose/foundation/layout/o0;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;I)V

    .line 580
    .line 581
    .line 582
    invoke-interface {v10}, Landroidx/compose/runtime/j;->q()V

    .line 583
    .line 584
    .line 585
    :goto_15
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 586
    .line 587
    .line 588
    move-result v3

    .line 589
    if-eqz v3, :cond_24

    .line 590
    .line 591
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 592
    .line 593
    .line 594
    :cond_24
    move-object v3, v0

    .line 595
    :goto_16
    invoke-interface {v10}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 596
    .line 597
    .line 598
    move-result-object v7

    .line 599
    if-eqz v7, :cond_25

    .line 600
    .line 601
    new-instance v0, Lorg/xbet/uikit_sport/compose/sport_game_events/m;

    .line 602
    .line 603
    move/from16 v6, p6

    .line 604
    .line 605
    invoke-direct/range {v0 .. v6}, Lorg/xbet/uikit_sport/compose/sport_game_events/m;-><init>(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;II)V

    .line 606
    .line 607
    .line 608
    invoke-interface {v7, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 609
    .line 610
    .line 611
    :cond_25
    return-void
.end method

.method public static final u(Lkotlin/jvm/functions/Function1;Ls31/a$b;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, Ls31/a$b;->e()LM11/b;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p1}, LM11/b;->a()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final v(Lkotlin/jvm/functions/Function1;Ls31/a$b;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, Ls31/a$b;->b()LM11/b;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p1}, LM11/b;->a()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final w(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 7

    .line 1
    or-int/lit8 p4, p4, 0x1

    .line 2
    .line 3
    invoke-static {p4}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result v5

    .line 7
    move-object v0, p0

    .line 8
    move v1, p1

    .line 9
    move-object v2, p2

    .line 10
    move-object v3, p3

    .line 11
    move v6, p5

    .line 12
    move-object v4, p6

    .line 13
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->t(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method

.method public static final x(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V
    .locals 34
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Landroidx/compose/ui/l;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    move/from16 v5, p5

    .line 2
    .line 3
    const v0, -0x22b27247

    .line 4
    .line 5
    .line 6
    move-object/from16 v1, p4

    .line 7
    .line 8
    invoke-interface {v1, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    and-int/lit8 v2, p6, 0x1

    .line 13
    .line 14
    if-eqz v2, :cond_0

    .line 15
    .line 16
    or-int/lit8 v2, v5, 0x6

    .line 17
    .line 18
    move-object/from16 v6, p0

    .line 19
    .line 20
    goto :goto_1

    .line 21
    :cond_0
    and-int/lit8 v2, v5, 0x6

    .line 22
    .line 23
    move-object/from16 v6, p0

    .line 24
    .line 25
    if-nez v2, :cond_2

    .line 26
    .line 27
    invoke-interface {v1, v6}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 28
    .line 29
    .line 30
    move-result v2

    .line 31
    if-eqz v2, :cond_1

    .line 32
    .line 33
    const/4 v2, 0x4

    .line 34
    goto :goto_0

    .line 35
    :cond_1
    const/4 v2, 0x2

    .line 36
    :goto_0
    or-int/2addr v2, v5

    .line 37
    goto :goto_1

    .line 38
    :cond_2
    move v2, v5

    .line 39
    :goto_1
    and-int/lit8 v3, p6, 0x2

    .line 40
    .line 41
    if-eqz v3, :cond_4

    .line 42
    .line 43
    or-int/lit8 v2, v2, 0x30

    .line 44
    .line 45
    :cond_3
    move-object/from16 v3, p1

    .line 46
    .line 47
    goto :goto_3

    .line 48
    :cond_4
    and-int/lit8 v3, v5, 0x30

    .line 49
    .line 50
    if-nez v3, :cond_3

    .line 51
    .line 52
    move-object/from16 v3, p1

    .line 53
    .line 54
    invoke-interface {v1, v3}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 55
    .line 56
    .line 57
    move-result v4

    .line 58
    if-eqz v4, :cond_5

    .line 59
    .line 60
    const/16 v4, 0x20

    .line 61
    .line 62
    goto :goto_2

    .line 63
    :cond_5
    const/16 v4, 0x10

    .line 64
    .line 65
    :goto_2
    or-int/2addr v2, v4

    .line 66
    :goto_3
    and-int/lit8 v4, p6, 0x4

    .line 67
    .line 68
    if-eqz v4, :cond_7

    .line 69
    .line 70
    or-int/lit16 v2, v2, 0x180

    .line 71
    .line 72
    :cond_6
    move-object/from16 v7, p2

    .line 73
    .line 74
    goto :goto_5

    .line 75
    :cond_7
    and-int/lit16 v7, v5, 0x180

    .line 76
    .line 77
    if-nez v7, :cond_6

    .line 78
    .line 79
    move-object/from16 v7, p2

    .line 80
    .line 81
    invoke-interface {v1, v7}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 82
    .line 83
    .line 84
    move-result v8

    .line 85
    if-eqz v8, :cond_8

    .line 86
    .line 87
    const/16 v8, 0x100

    .line 88
    .line 89
    goto :goto_4

    .line 90
    :cond_8
    const/16 v8, 0x80

    .line 91
    .line 92
    :goto_4
    or-int/2addr v2, v8

    .line 93
    :goto_5
    and-int/lit8 v8, p6, 0x8

    .line 94
    .line 95
    if-eqz v8, :cond_9

    .line 96
    .line 97
    or-int/lit16 v2, v2, 0xc00

    .line 98
    .line 99
    move-object/from16 v15, p3

    .line 100
    .line 101
    goto :goto_7

    .line 102
    :cond_9
    and-int/lit16 v8, v5, 0xc00

    .line 103
    .line 104
    move-object/from16 v15, p3

    .line 105
    .line 106
    if-nez v8, :cond_b

    .line 107
    .line 108
    invoke-interface {v1, v15}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 109
    .line 110
    .line 111
    move-result v8

    .line 112
    if-eqz v8, :cond_a

    .line 113
    .line 114
    const/16 v8, 0x800

    .line 115
    .line 116
    goto :goto_6

    .line 117
    :cond_a
    const/16 v8, 0x400

    .line 118
    .line 119
    :goto_6
    or-int/2addr v2, v8

    .line 120
    :cond_b
    :goto_7
    and-int/lit16 v8, v2, 0x493

    .line 121
    .line 122
    const/16 v9, 0x492

    .line 123
    .line 124
    if-ne v8, v9, :cond_d

    .line 125
    .line 126
    invoke-interface {v1}, Landroidx/compose/runtime/j;->c()Z

    .line 127
    .line 128
    .line 129
    move-result v8

    .line 130
    if-nez v8, :cond_c

    .line 131
    .line 132
    goto :goto_8

    .line 133
    :cond_c
    invoke-interface {v1}, Landroidx/compose/runtime/j;->n()V

    .line 134
    .line 135
    .line 136
    move-object/from16 v27, v1

    .line 137
    .line 138
    move-object v3, v7

    .line 139
    goto/16 :goto_b

    .line 140
    .line 141
    :cond_d
    :goto_8
    if-eqz v4, :cond_e

    .line 142
    .line 143
    sget-object v4, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 144
    .line 145
    move-object v9, v4

    .line 146
    goto :goto_9

    .line 147
    :cond_e
    move-object v9, v7

    .line 148
    :goto_9
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 149
    .line 150
    .line 151
    move-result v4

    .line 152
    if-eqz v4, :cond_f

    .line 153
    .line 154
    const/4 v4, -0x1

    .line 155
    const-string v7, "org.xbet.uikit_sport.compose.sport_game_events.PlayerEventLeft (EventComponent.kt:217)"

    .line 156
    .line 157
    invoke-static {v0, v2, v4, v7}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 158
    .line 159
    .line 160
    :cond_f
    const/16 v16, 0x1c

    .line 161
    .line 162
    const/16 v17, 0x0

    .line 163
    .line 164
    const/4 v10, 0x0

    .line 165
    const/4 v11, 0x0

    .line 166
    const/4 v12, 0x0

    .line 167
    const/4 v13, 0x0

    .line 168
    const/4 v14, 0x0

    .line 169
    invoke-static/range {v9 .. v17}, Landroidx/compose/foundation/ClickableKt;->d(Landroidx/compose/ui/l;Landroidx/compose/foundation/interaction/i;Landroidx/compose/foundation/E;ZLjava/lang/String;Landroidx/compose/ui/semantics/i;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 170
    .line 171
    .line 172
    move-result-object v0

    .line 173
    move-object v4, v9

    .line 174
    sget-object v7, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 175
    .line 176
    invoke-virtual {v7}, Landroidx/compose/ui/e$a;->i()Landroidx/compose/ui/e$c;

    .line 177
    .line 178
    .line 179
    move-result-object v7

    .line 180
    sget-object v8, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 181
    .line 182
    invoke-virtual {v8}, Landroidx/compose/foundation/layout/Arrangement;->g()Landroidx/compose/foundation/layout/Arrangement$e;

    .line 183
    .line 184
    .line 185
    move-result-object v8

    .line 186
    const/16 v9, 0x30

    .line 187
    .line 188
    invoke-static {v8, v7, v1, v9}, Landroidx/compose/foundation/layout/h0;->b(Landroidx/compose/foundation/layout/Arrangement$e;Landroidx/compose/ui/e$c;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 189
    .line 190
    .line 191
    move-result-object v7

    .line 192
    const/4 v8, 0x0

    .line 193
    invoke-static {v1, v8}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 194
    .line 195
    .line 196
    move-result v10

    .line 197
    invoke-interface {v1}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 198
    .line 199
    .line 200
    move-result-object v11

    .line 201
    invoke-static {v1, v0}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 202
    .line 203
    .line 204
    move-result-object v0

    .line 205
    sget-object v12, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 206
    .line 207
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 208
    .line 209
    .line 210
    move-result-object v13

    .line 211
    invoke-interface {v1}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 212
    .line 213
    .line 214
    move-result-object v14

    .line 215
    invoke-static {v14}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 216
    .line 217
    .line 218
    move-result v14

    .line 219
    if-nez v14, :cond_10

    .line 220
    .line 221
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 222
    .line 223
    .line 224
    :cond_10
    invoke-interface {v1}, Landroidx/compose/runtime/j;->l()V

    .line 225
    .line 226
    .line 227
    invoke-interface {v1}, Landroidx/compose/runtime/j;->B()Z

    .line 228
    .line 229
    .line 230
    move-result v14

    .line 231
    if-eqz v14, :cond_11

    .line 232
    .line 233
    invoke-interface {v1, v13}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 234
    .line 235
    .line 236
    goto :goto_a

    .line 237
    :cond_11
    invoke-interface {v1}, Landroidx/compose/runtime/j;->h()V

    .line 238
    .line 239
    .line 240
    :goto_a
    invoke-static {v1}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 241
    .line 242
    .line 243
    move-result-object v13

    .line 244
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 245
    .line 246
    .line 247
    move-result-object v14

    .line 248
    invoke-static {v13, v7, v14}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 249
    .line 250
    .line 251
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 252
    .line 253
    .line 254
    move-result-object v7

    .line 255
    invoke-static {v13, v11, v7}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 256
    .line 257
    .line 258
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 259
    .line 260
    .line 261
    move-result-object v7

    .line 262
    invoke-interface {v13}, Landroidx/compose/runtime/j;->B()Z

    .line 263
    .line 264
    .line 265
    move-result v11

    .line 266
    if-nez v11, :cond_12

    .line 267
    .line 268
    invoke-interface {v13}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 269
    .line 270
    .line 271
    move-result-object v11

    .line 272
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 273
    .line 274
    .line 275
    move-result-object v14

    .line 276
    invoke-static {v11, v14}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 277
    .line 278
    .line 279
    move-result v11

    .line 280
    if-nez v11, :cond_13

    .line 281
    .line 282
    :cond_12
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 283
    .line 284
    .line 285
    move-result-object v11

    .line 286
    invoke-interface {v13, v11}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 287
    .line 288
    .line 289
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 290
    .line 291
    .line 292
    move-result-object v10

    .line 293
    invoke-interface {v13, v10, v7}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 294
    .line 295
    .line 296
    :cond_13
    invoke-virtual {v12}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 297
    .line 298
    .line 299
    move-result-object v7

    .line 300
    invoke-static {v13, v0, v7}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 301
    .line 302
    .line 303
    sget-object v14, Landroidx/compose/foundation/layout/k0;->a:Landroidx/compose/foundation/layout/k0;

    .line 304
    .line 305
    sget-object v15, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 306
    .line 307
    sget-object v0, LA11/a;->a:LA11/a;

    .line 308
    .line 309
    invoke-virtual {v0}, LA11/a;->L1()F

    .line 310
    .line 311
    .line 312
    move-result v18

    .line 313
    const/16 v20, 0xb

    .line 314
    .line 315
    const/16 v21, 0x0

    .line 316
    .line 317
    const/16 v16, 0x0

    .line 318
    .line 319
    const/16 v17, 0x0

    .line 320
    .line 321
    const/16 v19, 0x0

    .line 322
    .line 323
    invoke-static/range {v15 .. v21}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 324
    .line 325
    .line 326
    move-result-object v7

    .line 327
    move-object v10, v15

    .line 328
    const/16 v18, 0x2

    .line 329
    .line 330
    const/16 v19, 0x0

    .line 331
    .line 332
    const/high16 v16, 0x3f800000    # 1.0f

    .line 333
    .line 334
    const/16 v17, 0x0

    .line 335
    .line 336
    move-object v15, v7

    .line 337
    invoke-static/range {v14 .. v19}, Landroidx/compose/foundation/layout/i0;->a(Landroidx/compose/foundation/layout/j0;Landroidx/compose/ui/l;FZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 338
    .line 339
    .line 340
    move-result-object v7

    .line 341
    sget-object v11, LC11/a;->a:LC11/a;

    .line 342
    .line 343
    invoke-virtual {v11}, LC11/a;->e()Landroidx/compose/ui/text/a0;

    .line 344
    .line 345
    .line 346
    move-result-object v11

    .line 347
    invoke-static {v11, v1, v8}, LB11/a;->d(Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/text/a0;

    .line 348
    .line 349
    .line 350
    move-result-object v26

    .line 351
    sget-object v11, Landroidx/compose/ui/text/style/i;->b:Landroidx/compose/ui/text/style/i$a;

    .line 352
    .line 353
    invoke-virtual {v11}, Landroidx/compose/ui/text/style/i$a;->b()I

    .line 354
    .line 355
    .line 356
    move-result v11

    .line 357
    sget-object v12, Landroidx/compose/ui/text/style/s;->b:Landroidx/compose/ui/text/style/s$a;

    .line 358
    .line 359
    invoke-virtual {v12}, Landroidx/compose/ui/text/style/s$a;->b()I

    .line 360
    .line 361
    .line 362
    move-result v21

    .line 363
    invoke-static {v11}, Landroidx/compose/ui/text/style/i;->h(I)Landroidx/compose/ui/text/style/i;

    .line 364
    .line 365
    .line 366
    move-result-object v18

    .line 367
    and-int/lit8 v28, v2, 0xe

    .line 368
    .line 369
    const/16 v29, 0xc30

    .line 370
    .line 371
    const v30, 0xd5fc

    .line 372
    .line 373
    .line 374
    const/16 v11, 0x30

    .line 375
    .line 376
    const/4 v12, 0x0

    .line 377
    const-wide/16 v8, 0x0

    .line 378
    .line 379
    move-object v15, v10

    .line 380
    const/16 v13, 0x30

    .line 381
    .line 382
    const-wide/16 v10, 0x0

    .line 383
    .line 384
    const/4 v14, 0x0

    .line 385
    const/4 v12, 0x0

    .line 386
    const/16 v16, 0x30

    .line 387
    .line 388
    const/4 v13, 0x0

    .line 389
    const/16 v17, 0x0

    .line 390
    .line 391
    const/4 v14, 0x0

    .line 392
    move-object/from16 v20, v15

    .line 393
    .line 394
    const/16 v19, 0x30

    .line 395
    .line 396
    const-wide/16 v15, 0x0

    .line 397
    .line 398
    const/16 v22, 0x0

    .line 399
    .line 400
    const/16 v17, 0x0

    .line 401
    .line 402
    move-object/from16 v24, v20

    .line 403
    .line 404
    const/16 v23, 0x30

    .line 405
    .line 406
    const-wide/16 v19, 0x0

    .line 407
    .line 408
    const/16 v25, 0x0

    .line 409
    .line 410
    const/16 v22, 0x0

    .line 411
    .line 412
    const/16 v27, 0x30

    .line 413
    .line 414
    const/16 v23, 0x2

    .line 415
    .line 416
    move-object/from16 v31, v24

    .line 417
    .line 418
    const/16 v24, 0x0

    .line 419
    .line 420
    const/16 v32, 0x0

    .line 421
    .line 422
    const/16 v25, 0x0

    .line 423
    .line 424
    move-object/from16 v27, v1

    .line 425
    .line 426
    move-object/from16 v33, v31

    .line 427
    .line 428
    const/16 p2, 0x30

    .line 429
    .line 430
    const/4 v1, 0x0

    .line 431
    invoke-static/range {v6 .. v30}, Landroidx/compose/material3/TextKt;->c(Ljava/lang/String;Landroidx/compose/ui/l;JJLandroidx/compose/ui/text/font/t;Landroidx/compose/ui/text/font/y;Landroidx/compose/ui/text/font/j;JLandroidx/compose/ui/text/style/j;Landroidx/compose/ui/text/style/i;JIZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;III)V

    .line 432
    .line 433
    .line 434
    move-object/from16 v6, v27

    .line 435
    .line 436
    sget v7, LlZ0/h;->ic_glyph_placeholder_player_secondary60:I

    .line 437
    .line 438
    invoke-static {v7, v6, v1}, Lm0/d;->c(ILandroidx/compose/runtime/j;I)Landroidx/compose/ui/graphics/painter/Painter;

    .line 439
    .line 440
    .line 441
    move-result-object v9

    .line 442
    invoke-virtual {v0}, LA11/a;->o0()F

    .line 443
    .line 444
    .line 445
    move-result v1

    .line 446
    move-object/from16 v15, v33

    .line 447
    .line 448
    invoke-static {v15, v1}, Landroidx/compose/foundation/layout/SizeKt;->v(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 449
    .line 450
    .line 451
    move-result-object v1

    .line 452
    invoke-virtual {v0}, LA11/a;->Q0()F

    .line 453
    .line 454
    .line 455
    move-result v0

    .line 456
    invoke-static {v0}, LR/i;->f(F)LR/h;

    .line 457
    .line 458
    .line 459
    move-result-object v0

    .line 460
    invoke-static {v1, v0}, Landroidx/compose/ui/draw/d;->a(Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    .line 461
    .line 462
    .line 463
    move-result-object v8

    .line 464
    shr-int/lit8 v0, v2, 0x3

    .line 465
    .line 466
    and-int/lit8 v0, v0, 0xe

    .line 467
    .line 468
    or-int/lit8 v22, v0, 0x30

    .line 469
    .line 470
    const/16 v23, 0x0

    .line 471
    .line 472
    const/16 v24, 0x7fe0

    .line 473
    .line 474
    const/4 v7, 0x0

    .line 475
    const/4 v11, 0x0

    .line 476
    const/4 v15, 0x0

    .line 477
    const/16 v16, 0x0

    .line 478
    .line 479
    const/16 v17, 0x0

    .line 480
    .line 481
    const/16 v18, 0x0

    .line 482
    .line 483
    const/16 v19, 0x0

    .line 484
    .line 485
    const/16 v20, 0x0

    .line 486
    .line 487
    move-object v10, v9

    .line 488
    move-object/from16 v21, v6

    .line 489
    .line 490
    move-object v6, v3

    .line 491
    invoke-static/range {v6 .. v24}, Lcoil3/compose/r;->b(Ljava/lang/Object;Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/e;Landroidx/compose/ui/layout/h;FLandroidx/compose/ui/graphics/w0;IZLandroidx/compose/runtime/j;III)V

    .line 492
    .line 493
    .line 494
    move-object/from16 v27, v21

    .line 495
    .line 496
    invoke-interface/range {v27 .. v27}, Landroidx/compose/runtime/j;->j()V

    .line 497
    .line 498
    .line 499
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 500
    .line 501
    .line 502
    move-result v0

    .line 503
    if-eqz v0, :cond_14

    .line 504
    .line 505
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 506
    .line 507
    .line 508
    :cond_14
    move-object v3, v4

    .line 509
    :goto_b
    invoke-interface/range {v27 .. v27}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 510
    .line 511
    .line 512
    move-result-object v7

    .line 513
    if-eqz v7, :cond_15

    .line 514
    .line 515
    new-instance v0, Lorg/xbet/uikit_sport/compose/sport_game_events/e;

    .line 516
    .line 517
    move-object/from16 v1, p0

    .line 518
    .line 519
    move-object/from16 v2, p1

    .line 520
    .line 521
    move-object/from16 v4, p3

    .line 522
    .line 523
    move/from16 v6, p6

    .line 524
    .line 525
    invoke-direct/range {v0 .. v6}, Lorg/xbet/uikit_sport/compose/sport_game_events/e;-><init>(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;II)V

    .line 526
    .line 527
    .line 528
    invoke-interface {v7, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 529
    .line 530
    .line 531
    :cond_15
    return-void
.end method

.method public static final y(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V
    .locals 37
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Z",
            "Landroidx/compose/ui/l;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    move/from16 v3, p2

    .line 2
    .line 3
    move/from16 v6, p6

    .line 4
    .line 5
    const v0, -0x344e4eb

    .line 6
    .line 7
    .line 8
    move-object/from16 v1, p5

    .line 9
    .line 10
    invoke-interface {v1, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    and-int/lit8 v2, p7, 0x1

    .line 15
    .line 16
    if-eqz v2, :cond_0

    .line 17
    .line 18
    or-int/lit8 v2, v6, 0x6

    .line 19
    .line 20
    move-object/from16 v7, p0

    .line 21
    .line 22
    goto :goto_1

    .line 23
    :cond_0
    and-int/lit8 v2, v6, 0x6

    .line 24
    .line 25
    move-object/from16 v7, p0

    .line 26
    .line 27
    if-nez v2, :cond_2

    .line 28
    .line 29
    invoke-interface {v1, v7}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result v2

    .line 33
    if-eqz v2, :cond_1

    .line 34
    .line 35
    const/4 v2, 0x4

    .line 36
    goto :goto_0

    .line 37
    :cond_1
    const/4 v2, 0x2

    .line 38
    :goto_0
    or-int/2addr v2, v6

    .line 39
    goto :goto_1

    .line 40
    :cond_2
    move v2, v6

    .line 41
    :goto_1
    and-int/lit8 v4, p7, 0x2

    .line 42
    .line 43
    if-eqz v4, :cond_4

    .line 44
    .line 45
    or-int/lit8 v2, v2, 0x30

    .line 46
    .line 47
    :cond_3
    move-object/from16 v4, p1

    .line 48
    .line 49
    goto :goto_3

    .line 50
    :cond_4
    and-int/lit8 v4, v6, 0x30

    .line 51
    .line 52
    if-nez v4, :cond_3

    .line 53
    .line 54
    move-object/from16 v4, p1

    .line 55
    .line 56
    invoke-interface {v1, v4}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 57
    .line 58
    .line 59
    move-result v5

    .line 60
    if-eqz v5, :cond_5

    .line 61
    .line 62
    const/16 v5, 0x20

    .line 63
    .line 64
    goto :goto_2

    .line 65
    :cond_5
    const/16 v5, 0x10

    .line 66
    .line 67
    :goto_2
    or-int/2addr v2, v5

    .line 68
    :goto_3
    and-int/lit8 v5, p7, 0x4

    .line 69
    .line 70
    if-eqz v5, :cond_6

    .line 71
    .line 72
    or-int/lit16 v2, v2, 0x180

    .line 73
    .line 74
    goto :goto_5

    .line 75
    :cond_6
    and-int/lit16 v5, v6, 0x180

    .line 76
    .line 77
    if-nez v5, :cond_8

    .line 78
    .line 79
    invoke-interface {v1, v3}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 80
    .line 81
    .line 82
    move-result v5

    .line 83
    if-eqz v5, :cond_7

    .line 84
    .line 85
    const/16 v5, 0x100

    .line 86
    .line 87
    goto :goto_4

    .line 88
    :cond_7
    const/16 v5, 0x80

    .line 89
    .line 90
    :goto_4
    or-int/2addr v2, v5

    .line 91
    :cond_8
    :goto_5
    and-int/lit8 v5, p7, 0x8

    .line 92
    .line 93
    if-eqz v5, :cond_a

    .line 94
    .line 95
    or-int/lit16 v2, v2, 0xc00

    .line 96
    .line 97
    :cond_9
    move-object/from16 v8, p3

    .line 98
    .line 99
    goto :goto_7

    .line 100
    :cond_a
    and-int/lit16 v8, v6, 0xc00

    .line 101
    .line 102
    if-nez v8, :cond_9

    .line 103
    .line 104
    move-object/from16 v8, p3

    .line 105
    .line 106
    invoke-interface {v1, v8}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 107
    .line 108
    .line 109
    move-result v9

    .line 110
    if-eqz v9, :cond_b

    .line 111
    .line 112
    const/16 v9, 0x800

    .line 113
    .line 114
    goto :goto_6

    .line 115
    :cond_b
    const/16 v9, 0x400

    .line 116
    .line 117
    :goto_6
    or-int/2addr v2, v9

    .line 118
    :goto_7
    and-int/lit8 v9, p7, 0x10

    .line 119
    .line 120
    if-eqz v9, :cond_d

    .line 121
    .line 122
    or-int/lit16 v2, v2, 0x6000

    .line 123
    .line 124
    :cond_c
    move-object/from16 v9, p4

    .line 125
    .line 126
    goto :goto_9

    .line 127
    :cond_d
    and-int/lit16 v9, v6, 0x6000

    .line 128
    .line 129
    if-nez v9, :cond_c

    .line 130
    .line 131
    move-object/from16 v9, p4

    .line 132
    .line 133
    invoke-interface {v1, v9}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 134
    .line 135
    .line 136
    move-result v10

    .line 137
    if-eqz v10, :cond_e

    .line 138
    .line 139
    const/16 v10, 0x4000

    .line 140
    .line 141
    goto :goto_8

    .line 142
    :cond_e
    const/16 v10, 0x2000

    .line 143
    .line 144
    :goto_8
    or-int/2addr v2, v10

    .line 145
    :goto_9
    and-int/lit16 v10, v2, 0x2493

    .line 146
    .line 147
    const/16 v11, 0x2492

    .line 148
    .line 149
    if-ne v10, v11, :cond_10

    .line 150
    .line 151
    invoke-interface {v1}, Landroidx/compose/runtime/j;->c()Z

    .line 152
    .line 153
    .line 154
    move-result v10

    .line 155
    if-nez v10, :cond_f

    .line 156
    .line 157
    goto :goto_a

    .line 158
    :cond_f
    invoke-interface {v1}, Landroidx/compose/runtime/j;->n()V

    .line 159
    .line 160
    .line 161
    move-object/from16 v28, v1

    .line 162
    .line 163
    move-object v4, v8

    .line 164
    goto/16 :goto_f

    .line 165
    .line 166
    :cond_10
    :goto_a
    if-eqz v5, :cond_11

    .line 167
    .line 168
    sget-object v5, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 169
    .line 170
    move-object v10, v5

    .line 171
    goto :goto_b

    .line 172
    :cond_11
    move-object v10, v8

    .line 173
    :goto_b
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 174
    .line 175
    .line 176
    move-result v5

    .line 177
    if-eqz v5, :cond_12

    .line 178
    .line 179
    const/4 v5, -0x1

    .line 180
    const-string v8, "org.xbet.uikit_sport.compose.sport_game_events.PlayerEventLeft (EventComponent.kt:252)"

    .line 181
    .line 182
    invoke-static {v0, v2, v5, v8}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 183
    .line 184
    .line 185
    :cond_12
    const/16 v17, 0x1c

    .line 186
    .line 187
    const/16 v18, 0x0

    .line 188
    .line 189
    const/4 v11, 0x0

    .line 190
    const/4 v12, 0x0

    .line 191
    const/4 v13, 0x0

    .line 192
    const/4 v14, 0x0

    .line 193
    const/4 v15, 0x0

    .line 194
    move-object/from16 v16, v9

    .line 195
    .line 196
    invoke-static/range {v10 .. v18}, Landroidx/compose/foundation/ClickableKt;->d(Landroidx/compose/ui/l;Landroidx/compose/foundation/interaction/i;Landroidx/compose/foundation/E;ZLjava/lang/String;Landroidx/compose/ui/semantics/i;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 197
    .line 198
    .line 199
    move-result-object v0

    .line 200
    move-object v5, v10

    .line 201
    sget-object v8, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 202
    .line 203
    invoke-virtual {v8}, Landroidx/compose/ui/e$a;->i()Landroidx/compose/ui/e$c;

    .line 204
    .line 205
    .line 206
    move-result-object v8

    .line 207
    sget-object v9, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 208
    .line 209
    invoke-virtual {v9}, Landroidx/compose/foundation/layout/Arrangement;->g()Landroidx/compose/foundation/layout/Arrangement$e;

    .line 210
    .line 211
    .line 212
    move-result-object v9

    .line 213
    const/16 v10, 0x30

    .line 214
    .line 215
    invoke-static {v9, v8, v1, v10}, Landroidx/compose/foundation/layout/h0;->b(Landroidx/compose/foundation/layout/Arrangement$e;Landroidx/compose/ui/e$c;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 216
    .line 217
    .line 218
    move-result-object v8

    .line 219
    const/4 v9, 0x0

    .line 220
    invoke-static {v1, v9}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 221
    .line 222
    .line 223
    move-result v11

    .line 224
    invoke-interface {v1}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 225
    .line 226
    .line 227
    move-result-object v12

    .line 228
    invoke-static {v1, v0}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 229
    .line 230
    .line 231
    move-result-object v0

    .line 232
    sget-object v13, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 233
    .line 234
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 235
    .line 236
    .line 237
    move-result-object v14

    .line 238
    invoke-interface {v1}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 239
    .line 240
    .line 241
    move-result-object v15

    .line 242
    invoke-static {v15}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 243
    .line 244
    .line 245
    move-result v15

    .line 246
    if-nez v15, :cond_13

    .line 247
    .line 248
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 249
    .line 250
    .line 251
    :cond_13
    invoke-interface {v1}, Landroidx/compose/runtime/j;->l()V

    .line 252
    .line 253
    .line 254
    invoke-interface {v1}, Landroidx/compose/runtime/j;->B()Z

    .line 255
    .line 256
    .line 257
    move-result v15

    .line 258
    if-eqz v15, :cond_14

    .line 259
    .line 260
    invoke-interface {v1, v14}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 261
    .line 262
    .line 263
    goto :goto_c

    .line 264
    :cond_14
    invoke-interface {v1}, Landroidx/compose/runtime/j;->h()V

    .line 265
    .line 266
    .line 267
    :goto_c
    invoke-static {v1}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 268
    .line 269
    .line 270
    move-result-object v14

    .line 271
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 272
    .line 273
    .line 274
    move-result-object v15

    .line 275
    invoke-static {v14, v8, v15}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 276
    .line 277
    .line 278
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 279
    .line 280
    .line 281
    move-result-object v8

    .line 282
    invoke-static {v14, v12, v8}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 283
    .line 284
    .line 285
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 286
    .line 287
    .line 288
    move-result-object v8

    .line 289
    invoke-interface {v14}, Landroidx/compose/runtime/j;->B()Z

    .line 290
    .line 291
    .line 292
    move-result v12

    .line 293
    if-nez v12, :cond_15

    .line 294
    .line 295
    invoke-interface {v14}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 296
    .line 297
    .line 298
    move-result-object v12

    .line 299
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 300
    .line 301
    .line 302
    move-result-object v15

    .line 303
    invoke-static {v12, v15}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 304
    .line 305
    .line 306
    move-result v12

    .line 307
    if-nez v12, :cond_16

    .line 308
    .line 309
    :cond_15
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 310
    .line 311
    .line 312
    move-result-object v12

    .line 313
    invoke-interface {v14, v12}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 314
    .line 315
    .line 316
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 317
    .line 318
    .line 319
    move-result-object v11

    .line 320
    invoke-interface {v14, v11, v8}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 321
    .line 322
    .line 323
    :cond_16
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 324
    .line 325
    .line 326
    move-result-object v8

    .line 327
    invoke-static {v14, v0, v8}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 328
    .line 329
    .line 330
    sget-object v15, Landroidx/compose/foundation/layout/k0;->a:Landroidx/compose/foundation/layout/k0;

    .line 331
    .line 332
    sget-object v16, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 333
    .line 334
    sget-object v0, LA11/a;->a:LA11/a;

    .line 335
    .line 336
    invoke-virtual {v0}, LA11/a;->L1()F

    .line 337
    .line 338
    .line 339
    move-result v19

    .line 340
    const/16 v21, 0xb

    .line 341
    .line 342
    const/16 v22, 0x0

    .line 343
    .line 344
    const/16 v17, 0x0

    .line 345
    .line 346
    const/16 v18, 0x0

    .line 347
    .line 348
    const/16 v20, 0x0

    .line 349
    .line 350
    invoke-static/range {v16 .. v22}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 351
    .line 352
    .line 353
    move-result-object v8

    .line 354
    move-object/from16 v11, v16

    .line 355
    .line 356
    const/16 v19, 0x2

    .line 357
    .line 358
    const/16 v20, 0x0

    .line 359
    .line 360
    const/high16 v17, 0x3f800000    # 1.0f

    .line 361
    .line 362
    const/16 v18, 0x0

    .line 363
    .line 364
    move-object/from16 v16, v8

    .line 365
    .line 366
    invoke-static/range {v15 .. v20}, Landroidx/compose/foundation/layout/i0;->a(Landroidx/compose/foundation/layout/j0;Landroidx/compose/ui/l;FZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 367
    .line 368
    .line 369
    move-result-object v8

    .line 370
    const/high16 v32, 0x3f800000    # 1.0f

    .line 371
    .line 372
    const/high16 v33, 0x3f000000    # 0.5f

    .line 373
    .line 374
    if-eqz v3, :cond_17

    .line 375
    .line 376
    const/high16 v12, 0x3f000000    # 0.5f

    .line 377
    .line 378
    goto :goto_d

    .line 379
    :cond_17
    const/high16 v12, 0x3f800000    # 1.0f

    .line 380
    .line 381
    :goto_d
    invoke-static {v8, v12}, Landroidx/compose/ui/draw/a;->a(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 382
    .line 383
    .line 384
    move-result-object v8

    .line 385
    sget-object v12, LC11/a;->a:LC11/a;

    .line 386
    .line 387
    invoke-virtual {v12}, LC11/a;->e()Landroidx/compose/ui/text/a0;

    .line 388
    .line 389
    .line 390
    move-result-object v12

    .line 391
    invoke-static {v12, v1, v9}, LB11/a;->d(Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/text/a0;

    .line 392
    .line 393
    .line 394
    move-result-object v27

    .line 395
    sget-object v12, Landroidx/compose/ui/text/style/i;->b:Landroidx/compose/ui/text/style/i$a;

    .line 396
    .line 397
    invoke-virtual {v12}, Landroidx/compose/ui/text/style/i$a;->b()I

    .line 398
    .line 399
    .line 400
    move-result v12

    .line 401
    sget-object v13, Landroidx/compose/ui/text/style/s;->b:Landroidx/compose/ui/text/style/s$a;

    .line 402
    .line 403
    invoke-virtual {v13}, Landroidx/compose/ui/text/style/s$a;->b()I

    .line 404
    .line 405
    .line 406
    move-result v22

    .line 407
    invoke-static {v12}, Landroidx/compose/ui/text/style/i;->h(I)Landroidx/compose/ui/text/style/i;

    .line 408
    .line 409
    .line 410
    move-result-object v19

    .line 411
    and-int/lit8 v29, v2, 0xe

    .line 412
    .line 413
    const/16 v30, 0xc30

    .line 414
    .line 415
    const v31, 0xd5fc

    .line 416
    .line 417
    .line 418
    const/16 v12, 0x30

    .line 419
    .line 420
    const/4 v13, 0x0

    .line 421
    const-wide/16 v9, 0x0

    .line 422
    .line 423
    move-object/from16 v16, v11

    .line 424
    .line 425
    const/16 v14, 0x30

    .line 426
    .line 427
    const-wide/16 v11, 0x0

    .line 428
    .line 429
    const/4 v15, 0x0

    .line 430
    const/4 v13, 0x0

    .line 431
    const/16 v17, 0x30

    .line 432
    .line 433
    const/4 v14, 0x0

    .line 434
    const/16 v18, 0x0

    .line 435
    .line 436
    const/4 v15, 0x0

    .line 437
    move-object/from16 v21, v16

    .line 438
    .line 439
    const/16 v20, 0x30

    .line 440
    .line 441
    const-wide/16 v16, 0x0

    .line 442
    .line 443
    const/16 v23, 0x0

    .line 444
    .line 445
    const/16 v18, 0x0

    .line 446
    .line 447
    move-object/from16 v25, v21

    .line 448
    .line 449
    const/16 v24, 0x30

    .line 450
    .line 451
    const-wide/16 v20, 0x0

    .line 452
    .line 453
    const/16 v26, 0x0

    .line 454
    .line 455
    const/16 v23, 0x0

    .line 456
    .line 457
    const/16 v28, 0x30

    .line 458
    .line 459
    const/16 v24, 0x2

    .line 460
    .line 461
    move-object/from16 v34, v25

    .line 462
    .line 463
    const/16 v25, 0x0

    .line 464
    .line 465
    const/16 v35, 0x0

    .line 466
    .line 467
    const/16 v26, 0x0

    .line 468
    .line 469
    move-object/from16 v28, v1

    .line 470
    .line 471
    move-object/from16 v36, v34

    .line 472
    .line 473
    const/16 p3, 0x30

    .line 474
    .line 475
    const/4 v1, 0x0

    .line 476
    invoke-static/range {v7 .. v31}, Landroidx/compose/material3/TextKt;->c(Ljava/lang/String;Landroidx/compose/ui/l;JJLandroidx/compose/ui/text/font/t;Landroidx/compose/ui/text/font/y;Landroidx/compose/ui/text/font/j;JLandroidx/compose/ui/text/style/j;Landroidx/compose/ui/text/style/i;JIZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;III)V

    .line 477
    .line 478
    .line 479
    move-object/from16 v7, v28

    .line 480
    .line 481
    sget v8, LlZ0/h;->ic_glyph_placeholder_player_secondary60:I

    .line 482
    .line 483
    invoke-static {v8, v7, v1}, Lm0/d;->c(ILandroidx/compose/runtime/j;I)Landroidx/compose/ui/graphics/painter/Painter;

    .line 484
    .line 485
    .line 486
    move-result-object v10

    .line 487
    invoke-virtual {v0}, LA11/a;->o0()F

    .line 488
    .line 489
    .line 490
    move-result v1

    .line 491
    move-object/from16 v11, v36

    .line 492
    .line 493
    invoke-static {v11, v1}, Landroidx/compose/foundation/layout/SizeKt;->v(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 494
    .line 495
    .line 496
    move-result-object v1

    .line 497
    invoke-virtual {v0}, LA11/a;->Q0()F

    .line 498
    .line 499
    .line 500
    move-result v0

    .line 501
    invoke-static {v0}, LR/i;->f(F)LR/h;

    .line 502
    .line 503
    .line 504
    move-result-object v0

    .line 505
    invoke-static {v1, v0}, Landroidx/compose/ui/draw/d;->a(Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    .line 506
    .line 507
    .line 508
    move-result-object v0

    .line 509
    if-eqz v3, :cond_18

    .line 510
    .line 511
    const/high16 v1, 0x3f000000    # 0.5f

    .line 512
    .line 513
    goto :goto_e

    .line 514
    :cond_18
    const/high16 v1, 0x3f800000    # 1.0f

    .line 515
    .line 516
    :goto_e
    invoke-static {v0, v1}, Landroidx/compose/ui/draw/a;->a(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 517
    .line 518
    .line 519
    move-result-object v9

    .line 520
    shr-int/lit8 v0, v2, 0x3

    .line 521
    .line 522
    and-int/lit8 v0, v0, 0xe

    .line 523
    .line 524
    or-int/lit8 v23, v0, 0x30

    .line 525
    .line 526
    const/16 v24, 0x0

    .line 527
    .line 528
    const/16 v25, 0x7fe0

    .line 529
    .line 530
    const/4 v8, 0x0

    .line 531
    const/4 v12, 0x0

    .line 532
    const/4 v13, 0x0

    .line 533
    const/4 v14, 0x0

    .line 534
    const/4 v15, 0x0

    .line 535
    const/16 v16, 0x0

    .line 536
    .line 537
    const/16 v17, 0x0

    .line 538
    .line 539
    const/16 v18, 0x0

    .line 540
    .line 541
    const/16 v19, 0x0

    .line 542
    .line 543
    const/16 v20, 0x0

    .line 544
    .line 545
    const/16 v21, 0x0

    .line 546
    .line 547
    move-object v11, v10

    .line 548
    move-object/from16 v22, v7

    .line 549
    .line 550
    move-object v7, v4

    .line 551
    invoke-static/range {v7 .. v25}, Lcoil3/compose/r;->b(Ljava/lang/Object;Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/e;Landroidx/compose/ui/layout/h;FLandroidx/compose/ui/graphics/w0;IZLandroidx/compose/runtime/j;III)V

    .line 552
    .line 553
    .line 554
    move-object/from16 v28, v22

    .line 555
    .line 556
    invoke-interface/range {v28 .. v28}, Landroidx/compose/runtime/j;->j()V

    .line 557
    .line 558
    .line 559
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 560
    .line 561
    .line 562
    move-result v0

    .line 563
    if-eqz v0, :cond_19

    .line 564
    .line 565
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 566
    .line 567
    .line 568
    :cond_19
    move-object v4, v5

    .line 569
    :goto_f
    invoke-interface/range {v28 .. v28}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 570
    .line 571
    .line 572
    move-result-object v8

    .line 573
    if-eqz v8, :cond_1a

    .line 574
    .line 575
    new-instance v0, Lorg/xbet/uikit_sport/compose/sport_game_events/o;

    .line 576
    .line 577
    move-object/from16 v1, p0

    .line 578
    .line 579
    move-object/from16 v2, p1

    .line 580
    .line 581
    move-object/from16 v5, p4

    .line 582
    .line 583
    move/from16 v7, p7

    .line 584
    .line 585
    invoke-direct/range {v0 .. v7}, Lorg/xbet/uikit_sport/compose/sport_game_events/o;-><init>(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;II)V

    .line 586
    .line 587
    .line 588
    invoke-interface {v8, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 589
    .line 590
    .line 591
    :cond_1a
    return-void
.end method

.method public static final z(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 7

    .line 1
    or-int/lit8 p4, p4, 0x1

    invoke-static {p4}, Landroidx/compose/runtime/A0;->a(I)I

    move-result v5

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move v6, p5

    move-object v4, p6

    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->x(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V

    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p0
.end method
