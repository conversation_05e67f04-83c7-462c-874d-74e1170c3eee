.class public final Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$a;,
        Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b;,
        Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$c;,
        Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;,
        Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e;,
        Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$f;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00e8\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\r\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0003\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008)\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u000f\u0018\u0000 \u0095\u00012\u00020\u0001:\n\u0096\u0001\u0097\u0001\u0098\u0001\u0099\u0001\u009a\u0001B\u00a3\u0001\u0008\u0007\u0012\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u00a2\u0006\u0004\u0008(\u0010)J\u000f\u0010+\u001a\u00020*H\u0002\u00a2\u0006\u0004\u0008+\u0010,J\u000f\u0010-\u001a\u00020*H\u0002\u00a2\u0006\u0004\u0008-\u0010,J\u000f\u0010.\u001a\u00020*H\u0002\u00a2\u0006\u0004\u0008.\u0010,J\u000f\u0010/\u001a\u00020*H\u0002\u00a2\u0006\u0004\u0008/\u0010,J\u000f\u00100\u001a\u00020*H\u0002\u00a2\u0006\u0004\u00080\u0010,J\u000f\u00101\u001a\u00020*H\u0002\u00a2\u0006\u0004\u00081\u0010,J\u000f\u00102\u001a\u00020*H\u0002\u00a2\u0006\u0004\u00082\u0010,J\u000f\u00103\u001a\u00020*H\u0002\u00a2\u0006\u0004\u00083\u0010,J\u000f\u00104\u001a\u00020*H\u0002\u00a2\u0006\u0004\u00084\u0010,J\u000f\u00105\u001a\u00020*H\u0002\u00a2\u0006\u0004\u00085\u0010,J\u000f\u00106\u001a\u00020*H\u0002\u00a2\u0006\u0004\u00086\u0010,J\u000f\u00107\u001a\u00020*H\u0002\u00a2\u0006\u0004\u00087\u0010,J\u001d\u0010;\u001a\u00020*2\u000c\u0010:\u001a\u0008\u0012\u0004\u0012\u00020908H\u0002\u00a2\u0006\u0004\u0008;\u0010<J\u0017\u0010?\u001a\u00020*2\u0006\u0010>\u001a\u00020=H\u0002\u00a2\u0006\u0004\u0008?\u0010@J\u0017\u0010C\u001a\u00020*2\u0006\u0010B\u001a\u00020AH\u0002\u00a2\u0006\u0004\u0008C\u0010DJ\u0017\u0010E\u001a\u00020*2\u0006\u0010>\u001a\u00020=H\u0002\u00a2\u0006\u0004\u0008E\u0010@J\u0015\u0010H\u001a\u0008\u0012\u0004\u0012\u00020G0FH\u0000\u00a2\u0006\u0004\u0008H\u0010IJ\u0015\u0010K\u001a\u0008\u0012\u0004\u0012\u00020J0FH\u0000\u00a2\u0006\u0004\u0008K\u0010IJ\u0015\u0010M\u001a\u0008\u0012\u0004\u0012\u00020L0FH\u0000\u00a2\u0006\u0004\u0008M\u0010IJ\u0015\u0010O\u001a\u0008\u0012\u0004\u0012\u00020N0FH\u0000\u00a2\u0006\u0004\u0008O\u0010IJ\u000f\u0010P\u001a\u00020*H\u0000\u00a2\u0006\u0004\u0008P\u0010,J\u000f\u0010Q\u001a\u00020*H\u0000\u00a2\u0006\u0004\u0008Q\u0010,J\u001f\u0010U\u001a\u00020*2\u0006\u0010S\u001a\u00020R2\u0006\u0010T\u001a\u00020RH\u0000\u00a2\u0006\u0004\u0008U\u0010VJ\u000f\u0010W\u001a\u00020*H\u0000\u00a2\u0006\u0004\u0008W\u0010,R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008X\u0010YR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Z\u0010[R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\\\u0010]R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008^\u0010_R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008`\u0010aR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008b\u0010cR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008d\u0010eR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008f\u0010gR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008h\u0010iR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008j\u0010kR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008l\u0010mR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008n\u0010oR\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008p\u0010qR\u0014\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008r\u0010sR\u0014\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008t\u0010uR\u0014\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008v\u0010wR\u0014\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008x\u0010yR\u0014\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008z\u0010{R\u0016\u0010\u007f\u001a\u00020|8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008}\u0010~R\u0018\u0010\u0083\u0001\u001a\u00030\u0080\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0081\u0001\u0010\u0082\u0001R\u001c\u0010\u0087\u0001\u001a\u0005\u0018\u00010\u0084\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0085\u0001\u0010\u0086\u0001R\u001c\u0010\u0089\u0001\u001a\u0005\u0018\u00010\u0084\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0088\u0001\u0010\u0086\u0001R\u001c\u0010\u008b\u0001\u001a\u0005\u0018\u00010\u0084\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u008a\u0001\u0010\u0086\u0001R\u001d\u0010\u008e\u0001\u001a\t\u0012\u0004\u0012\u00020G0\u008c\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008K\u0010\u008d\u0001R\u001e\u0010\u0090\u0001\u001a\t\u0012\u0004\u0012\u00020J0\u008c\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008f\u0001\u0010\u008d\u0001R\u001e\u0010\u0092\u0001\u001a\t\u0012\u0004\u0012\u00020L0\u008c\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0091\u0001\u0010\u008d\u0001R\u001e\u0010\u0094\u0001\u001a\t\u0012\u0004\u0012\u00020N0\u008c\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0093\u0001\u0010\u008d\u0001\u00a8\u0006\u009b\u0001"
    }
    d2 = {
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "LwX0/c;",
        "router",
        "Lorg/xbet/core/domain/usecases/u;",
        "observeCommandUseCase",
        "Lorg/xbet/core/domain/usecases/d;",
        "choiceErrorActionScenario",
        "Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;",
        "unfinishedGameLoadedScenario",
        "Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;",
        "getActiveGameScenario",
        "Lorg/xbet/core/domain/usecases/game_state/l;",
        "setGameInProgressUseCase",
        "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
        "startGameIfPossibleScenario",
        "Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;",
        "playNewGameScenario",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "addCommandScenario",
        "Lorg/xbet/tile_matching/domain/usecases/b;",
        "getTileMatchingGameModelUseCase",
        "Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;",
        "makeActionScenario",
        "Lorg/xbet/tile_matching/domain/usecases/d;",
        "isTileMatchingGameActiveUseCase",
        "Lorg/xbet/tile_matching/domain/usecases/a;",
        "gameFinishedScenario",
        "Lorg/xbet/core/domain/usecases/game_state/h;",
        "isGameInProgressUseCase",
        "Lorg/xbet/core/domain/usecases/game_info/q;",
        "getGameStateUseCase",
        "Lorg/xbet/tile_matching/domain/usecases/e;",
        "resetGameUseCase",
        "Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;",
        "loadTileMatchingCoeflUseCase",
        "Lm8/a;",
        "coroutineDispatchers",
        "LTv/e;",
        "gameConfig",
        "<init>",
        "(LwX0/c;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/tile_matching/domain/usecases/b;Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;Lorg/xbet/tile_matching/domain/usecases/d;Lorg/xbet/tile_matching/domain/usecases/a;Lorg/xbet/core/domain/usecases/game_state/h;Lorg/xbet/core/domain/usecases/game_info/q;Lorg/xbet/tile_matching/domain/usecases/e;Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;Lm8/a;LTv/e;)V",
        "",
        "M3",
        "()V",
        "N3",
        "P3",
        "g4",
        "Q3",
        "k4",
        "Z3",
        "i4",
        "O3",
        "j4",
        "e4",
        "d4",
        "",
        "LzT0/c;",
        "coeffs",
        "L3",
        "(Ljava/util/List;)V",
        "LTv/d;",
        "command",
        "W3",
        "(LTv/d;)V",
        "",
        "throwable",
        "X3",
        "(Ljava/lang/Throwable;)V",
        "K3",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e;",
        "V3",
        "()Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$a;",
        "S3",
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;",
        "U3",
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b;",
        "T3",
        "Y3",
        "f4",
        "",
        "row",
        "column",
        "c4",
        "(II)V",
        "h4",
        "v1",
        "LwX0/c;",
        "x1",
        "Lorg/xbet/core/domain/usecases/d;",
        "y1",
        "Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;",
        "F1",
        "Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;",
        "H1",
        "Lorg/xbet/core/domain/usecases/game_state/l;",
        "I1",
        "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
        "P1",
        "Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;",
        "S1",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "V1",
        "Lorg/xbet/tile_matching/domain/usecases/b;",
        "b2",
        "Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;",
        "v2",
        "Lorg/xbet/tile_matching/domain/usecases/d;",
        "x2",
        "Lorg/xbet/tile_matching/domain/usecases/a;",
        "y2",
        "Lorg/xbet/core/domain/usecases/game_state/h;",
        "F2",
        "Lorg/xbet/core/domain/usecases/game_info/q;",
        "H2",
        "Lorg/xbet/tile_matching/domain/usecases/e;",
        "I2",
        "Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;",
        "P2",
        "Lm8/a;",
        "S2",
        "LTv/e;",
        "",
        "V2",
        "Z",
        "unfinishedGameResumed",
        "Lkotlinx/coroutines/CoroutineExceptionHandler;",
        "X2",
        "Lkotlinx/coroutines/CoroutineExceptionHandler;",
        "coroutineErrorHandler",
        "Lkotlinx/coroutines/x0;",
        "F3",
        "Lkotlinx/coroutines/x0;",
        "getGameJob",
        "H3",
        "makeBetJob",
        "I3",
        "makeActionJob",
        "Lkotlinx/coroutines/flow/V;",
        "Lkotlinx/coroutines/flow/V;",
        "viewStateFlow",
        "H4",
        "coeffStateFlow",
        "X4",
        "gameStateFlow",
        "v5",
        "combinationStateFlow",
        "w5",
        "e",
        "b",
        "a",
        "d",
        "c",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final w5:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final F1:Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:Lorg/xbet/core/domain/usecases/game_info/q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public F3:Lkotlinx/coroutines/x0;

.field public final H1:Lorg/xbet/core/domain/usecases/game_state/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lorg/xbet/tile_matching/domain/usecases/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public H3:Lkotlinx/coroutines/x0;

.field public final H4:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public I3:Lkotlinx/coroutines/x0;

.field public final P1:Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/core/domain/usecases/AddCommandScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:LTv/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S3:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lorg/xbet/tile_matching/domain/usecases/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public V2:Z

.field public final X2:Lkotlinx/coroutines/CoroutineExceptionHandler;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X4:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lorg/xbet/tile_matching/domain/usecases/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Lorg/xbet/core/domain/usecases/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lorg/xbet/tile_matching/domain/usecases/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:Lorg/xbet/core/domain/usecases/game_state/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$c;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$c;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->w5:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$c;

    return-void
.end method

.method public constructor <init>(LwX0/c;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/tile_matching/domain/usecases/b;Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;Lorg/xbet/tile_matching/domain/usecases/d;Lorg/xbet/tile_matching/domain/usecases/a;Lorg/xbet/core/domain/usecases/game_state/h;Lorg/xbet/core/domain/usecases/game_info/q;Lorg/xbet/tile_matching/domain/usecases/e;Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;Lm8/a;LTv/e;)V
    .locals 0
    .param p1    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/core/domain/usecases/u;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/core/domain/usecases/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/core/domain/usecases/game_state/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/core/domain/usecases/AddCommandScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/tile_matching/domain/usecases/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lorg/xbet/tile_matching/domain/usecases/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lorg/xbet/tile_matching/domain/usecases/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Lorg/xbet/core/domain/usecases/game_state/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lorg/xbet/core/domain/usecases/game_info/q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lorg/xbet/tile_matching/domain/usecases/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # LTv/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->v1:LwX0/c;

    .line 5
    .line 6
    iput-object p3, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->x1:Lorg/xbet/core/domain/usecases/d;

    .line 7
    .line 8
    iput-object p4, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->y1:Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;

    .line 9
    .line 10
    iput-object p5, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->F1:Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;

    .line 11
    .line 12
    iput-object p6, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->H1:Lorg/xbet/core/domain/usecases/game_state/l;

    .line 13
    .line 14
    iput-object p7, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->I1:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;

    .line 15
    .line 16
    iput-object p8, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->P1:Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;

    .line 17
    .line 18
    iput-object p9, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S1:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 19
    .line 20
    iput-object p10, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->V1:Lorg/xbet/tile_matching/domain/usecases/b;

    .line 21
    .line 22
    iput-object p11, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->b2:Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;

    .line 23
    .line 24
    iput-object p12, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->v2:Lorg/xbet/tile_matching/domain/usecases/d;

    .line 25
    .line 26
    iput-object p13, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->x2:Lorg/xbet/tile_matching/domain/usecases/a;

    .line 27
    .line 28
    iput-object p14, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->y2:Lorg/xbet/core/domain/usecases/game_state/h;

    .line 29
    .line 30
    iput-object p15, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->F2:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 31
    .line 32
    move-object/from16 p1, p16

    .line 33
    .line 34
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->H2:Lorg/xbet/tile_matching/domain/usecases/e;

    .line 35
    .line 36
    move-object/from16 p1, p17

    .line 37
    .line 38
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->I2:Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;

    .line 39
    .line 40
    move-object/from16 p1, p18

    .line 41
    .line 42
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->P2:Lm8/a;

    .line 43
    .line 44
    move-object/from16 p1, p19

    .line 45
    .line 46
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S2:LTv/e;

    .line 47
    .line 48
    sget-object p1, Lkotlinx/coroutines/CoroutineExceptionHandler;->Y3:Lkotlinx/coroutines/CoroutineExceptionHandler$a;

    .line 49
    .line 50
    new-instance p3, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$g;

    .line 51
    .line 52
    invoke-direct {p3, p1, p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$g;-><init>(Lkotlinx/coroutines/CoroutineExceptionHandler$a;Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)V

    .line 53
    .line 54
    .line 55
    iput-object p3, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X2:Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 56
    .line 57
    sget-object p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e$a;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e$a;

    .line 58
    .line 59
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 64
    .line 65
    sget-object p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$a$c;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$a$c;

    .line 66
    .line 67
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 72
    .line 73
    sget-object p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$b;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$b;

    .line 74
    .line 75
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 80
    .line 81
    sget-object p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$b;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$b;

    .line 82
    .line 83
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 84
    .line 85
    .line 86
    move-result-object p1

    .line 87
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 88
    .line 89
    invoke-virtual {p2}, Lorg/xbet/core/domain/usecases/u;->a()Lkotlinx/coroutines/flow/e;

    .line 90
    .line 91
    .line 92
    move-result-object p1

    .line 93
    new-instance p2, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$1;

    .line 94
    .line 95
    invoke-direct {p2, p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$1;-><init>(Ljava/lang/Object;)V

    .line 96
    .line 97
    .line 98
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 99
    .line 100
    .line 101
    move-result-object p1

    .line 102
    new-instance p2, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$2;

    .line 103
    .line 104
    const/4 p3, 0x0

    .line 105
    invoke-direct {p2, p0, p3}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Lkotlin/coroutines/e;)V

    .line 106
    .line 107
    .line 108
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 113
    .line 114
    .line 115
    move-result-object p2

    .line 116
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 117
    .line 118
    .line 119
    return-void
.end method

.method public static final synthetic A3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lorg/xbet/tile_matching/domain/usecases/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->x2:Lorg/xbet/tile_matching/domain/usecases/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->F1:Lorg/xbet/tile_matching/domain/usecases/GetActiveGameScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lorg/xbet/tile_matching/domain/usecases/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->V1:Lorg/xbet/tile_matching/domain/usecases/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->I2:Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic F3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->b2:Lorg/xbet/tile_matching/domain/usecases/MakeActionScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic G3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->P1:Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic H3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->I1:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic I3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->y1:Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic J3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X3(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final K3(LTv/d;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->P2:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    sget-object v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$addCommand$1;->INSTANCE:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$addCommand$1;

    .line 12
    .line 13
    new-instance v5, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$addCommand$2;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$addCommand$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;LTv/d;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/16 v6, 0xa

    .line 20
    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method private final P3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$gameFinished$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$gameFinished$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->P2:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$gameFinished$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$gameFinished$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method private final Q3()V
    .locals 15

    .line 1
    const/4 v0, 0x1

    .line 2
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->F3:Lkotlinx/coroutines/x0;

    .line 3
    .line 4
    if-eqz v1, :cond_0

    .line 5
    .line 6
    invoke-interface {v1}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 7
    .line 8
    .line 9
    move-result v1

    .line 10
    if-ne v1, v0, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    const-class v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 18
    .line 19
    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    new-instance v3, Ljava/lang/StringBuilder;

    .line 24
    .line 25
    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 26
    .line 27
    .line 28
    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 29
    .line 30
    .line 31
    const-string v1, ".getActiveGame"

    .line 32
    .line 33
    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 34
    .line 35
    .line 36
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v3

    .line 40
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->P2:Lm8/a;

    .line 41
    .line 42
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 43
    .line 44
    .line 45
    move-result-object v10

    .line 46
    const/4 v1, 0x3

    .line 47
    new-array v1, v1, [Ljava/lang/Class;

    .line 48
    .line 49
    const-class v4, Lcom/xbet/onexcore/data/errors/UserAuthException;

    .line 50
    .line 51
    const/4 v5, 0x0

    .line 52
    aput-object v4, v1, v5

    .line 53
    .line 54
    const-class v4, Lcom/xbet/onexcore/BadDataResponseException;

    .line 55
    .line 56
    aput-object v4, v1, v0

    .line 57
    .line 58
    const-class v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 59
    .line 60
    const/4 v4, 0x2

    .line 61
    aput-object v0, v1, v4

    .line 62
    .line 63
    invoke-static {v1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 64
    .line 65
    .line 66
    move-result-object v7

    .line 67
    new-instance v8, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$getActiveGame$1;

    .line 68
    .line 69
    const/4 v0, 0x0

    .line 70
    invoke-direct {v8, p0, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$getActiveGame$1;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Lkotlin/coroutines/e;)V

    .line 71
    .line 72
    .line 73
    new-instance v11, Lorg/xbet/tile_matching/presentation/game/l;

    .line 74
    .line 75
    invoke-direct {v11, p0}, Lorg/xbet/tile_matching/presentation/game/l;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)V

    .line 76
    .line 77
    .line 78
    const/16 v13, 0x120

    .line 79
    .line 80
    const/4 v14, 0x0

    .line 81
    const/4 v4, 0x5

    .line 82
    const-wide/16 v5, 0x5

    .line 83
    .line 84
    const/4 v9, 0x0

    .line 85
    const/4 v12, 0x0

    .line 86
    invoke-static/range {v2 .. v14}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->Y(Lkotlinx/coroutines/N;Ljava/lang/String;IJLjava/util/List;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 87
    .line 88
    .line 89
    move-result-object v0

    .line 90
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->F3:Lkotlinx/coroutines/x0;

    .line 91
    .line 92
    return-void
.end method

.method public static final R3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    instance-of v0, p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 7
    .line 8
    invoke-virtual {v0}, Lcom/xbet/onexcore/data/model/ServerException;->getErrorCode()Lcom/xbet/onexcore/data/errors/IErrorCode;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    sget-object v1, Lcom/xbet/onexuser/domain/entity/onexgame/errors/GamesErrorsCode;->GameNotAvailable:Lcom/xbet/onexuser/domain/entity/onexgame/errors/GamesErrorsCode;

    .line 13
    .line 14
    if-ne v0, v1, :cond_0

    .line 15
    .line 16
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->Z3()V

    .line 17
    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    instance-of v0, p1, Ljava/net/UnknownHostException;

    .line 21
    .line 22
    if-nez v0, :cond_1

    .line 23
    .line 24
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->Z3()V

    .line 25
    .line 26
    .line 27
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X2:Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 28
    .line 29
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    invoke-interface {p0}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 34
    .line 35
    .line 36
    move-result-object p0

    .line 37
    invoke-interface {v0, p0, p1}, Lkotlinx/coroutines/CoroutineExceptionHandler;->handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V

    .line 38
    .line 39
    .line 40
    goto :goto_0

    .line 41
    :cond_1
    const/4 v0, 0x1

    .line 42
    iput-boolean v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->V2:Z

    .line 43
    .line 44
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->j4()V

    .line 45
    .line 46
    .line 47
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X2:Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 48
    .line 49
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 50
    .line 51
    .line 52
    move-result-object p0

    .line 53
    invoke-interface {p0}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 54
    .line 55
    .line 56
    move-result-object p0

    .line 57
    invoke-interface {v0, p0, p1}, Lkotlinx/coroutines/CoroutineExceptionHandler;->handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V

    .line 58
    .line 59
    .line 60
    :goto_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 61
    .line 62
    return-object p0
.end method

.method private final W3(LTv/d;)V
    .locals 1

    .line 1
    instance-of v0, p1, LTv/a$d;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->i4()V

    .line 6
    .line 7
    .line 8
    return-void

    .line 9
    :cond_0
    instance-of v0, p1, LTv/a$w;

    .line 10
    .line 11
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->k4()V

    .line 14
    .line 15
    .line 16
    return-void

    .line 17
    :cond_1
    instance-of v0, p1, LTv/a$l;

    .line 18
    .line 19
    if-eqz v0, :cond_2

    .line 20
    .line 21
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->Q3()V

    .line 22
    .line 23
    .line 24
    return-void

    .line 25
    :cond_2
    instance-of v0, p1, LTv/a$s;

    .line 26
    .line 27
    if-eqz v0, :cond_3

    .line 28
    .line 29
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->O3()V

    .line 30
    .line 31
    .line 32
    return-void

    .line 33
    :cond_3
    instance-of v0, p1, LTv/a$p;

    .line 34
    .line 35
    if-nez v0, :cond_8

    .line 36
    .line 37
    instance-of v0, p1, LTv/a$r;

    .line 38
    .line 39
    if-eqz v0, :cond_4

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_4
    instance-of v0, p1, LTv/a$i;

    .line 43
    .line 44
    if-eqz v0, :cond_5

    .line 45
    .line 46
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->e4()V

    .line 47
    .line 48
    .line 49
    return-void

    .line 50
    :cond_5
    instance-of v0, p1, LTv/a$h;

    .line 51
    .line 52
    if-eqz v0, :cond_6

    .line 53
    .line 54
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->d4()V

    .line 55
    .line 56
    .line 57
    return-void

    .line 58
    :cond_6
    instance-of p1, p1, LTv/a$j;

    .line 59
    .line 60
    if-eqz p1, :cond_7

    .line 61
    .line 62
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->g4()V

    .line 63
    .line 64
    .line 65
    :cond_7
    return-void

    .line 66
    :cond_8
    :goto_0
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->j4()V

    .line 67
    .line 68
    .line 69
    return-void
.end method

.method private final X3(Ljava/lang/Throwable;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->P2:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    sget-object v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$handleGameError$1;->INSTANCE:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$handleGameError$1;

    .line 12
    .line 13
    new-instance v5, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$handleGameError$2;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$handleGameError$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/16 v6, 0xa

    .line 20
    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public static final a4(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lkotlin/Unit;
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->P2:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    sget-object v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$2$1;->INSTANCE:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$2$1;

    .line 12
    .line 13
    new-instance v5, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$2$2;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-direct {v5, p0, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$2$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/16 v6, 0xa

    .line 20
    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    const/4 v0, 0x1

    .line 27
    iput-boolean v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->V2:Z

    .line 28
    .line 29
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->j4()V

    .line 30
    .line 31
    .line 32
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 33
    .line 34
    return-object p0
.end method

.method public static final b4(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X2:Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 2
    .line 3
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    invoke-interface {p0}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-interface {v0, p0, p1}, Lkotlinx/coroutines/CoroutineExceptionHandler;->handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method private final g4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$c;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$c;-><init>(Z)V

    .line 7
    .line 8
    .line 9
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method private final i4()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->H1:Lorg/xbet/core/domain/usecases/game_state/l;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-virtual {v0, v1}, Lorg/xbet/core/domain/usecases/game_state/l;->a(Z)V

    .line 5
    .line 6
    .line 7
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    new-instance v3, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$playIfPossible$1;

    .line 12
    .line 13
    invoke-direct {v3, p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$playIfPossible$1;-><init>(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->P2:Lm8/a;

    .line 17
    .line 18
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 19
    .line 20
    .line 21
    move-result-object v5

    .line 22
    new-instance v7, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$playIfPossible$2;

    .line 23
    .line 24
    const/4 v0, 0x0

    .line 25
    invoke-direct {v7, p0, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$playIfPossible$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Lkotlin/coroutines/e;)V

    .line 26
    .line 27
    .line 28
    const/16 v8, 0xa

    .line 29
    .line 30
    const/4 v9, 0x0

    .line 31
    const/4 v4, 0x0

    .line 32
    const/4 v6, 0x0

    .line 33
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method private final j4()V
    .locals 6

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->M3()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->H2:Lorg/xbet/tile_matching/domain/usecases/e;

    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xbet/tile_matching/domain/usecases/e;->b()V

    .line 7
    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->V1:Lorg/xbet/tile_matching/domain/usecases/b;

    .line 10
    .line 11
    invoke-virtual {v0}, Lorg/xbet/tile_matching/domain/usecases/b;->a()LzT0/d;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 16
    .line 17
    new-instance v2, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$g;

    .line 18
    .line 19
    invoke-virtual {v0}, LzT0/d;->c()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object v3

    .line 23
    const/4 v4, 0x0

    .line 24
    const/4 v5, 0x2

    .line 25
    invoke-direct {v2, v3, v4, v5, v4}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$g;-><init>(Ljava/util/List;Ljava/util/List;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 26
    .line 27
    .line 28
    invoke-interface {v1, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    invoke-virtual {v0}, LzT0/d;->a()Ljava/util/List;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    invoke-virtual {p0, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->L3(Ljava/util/List;)V

    .line 36
    .line 37
    .line 38
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 39
    .line 40
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e$b;

    .line 41
    .line 42
    const/4 v2, 0x1

    .line 43
    invoke-direct {v1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e$b;-><init>(Z)V

    .line 44
    .line 45
    .line 46
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 47
    .line 48
    .line 49
    return-void
.end method

.method private final k4()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->H3:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->P2:Lm8/a;

    .line 18
    .line 19
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 20
    .line 21
    .line 22
    move-result-object v5

    .line 23
    new-instance v3, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$startGame$1;

    .line 24
    .line 25
    invoke-direct {v3, p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$startGame$1;-><init>(Ljava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    new-instance v7, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$startGame$2;

    .line 29
    .line 30
    const/4 v0, 0x0

    .line 31
    invoke-direct {v7, p0, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$startGame$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/16 v8, 0xa

    .line 35
    .line 36
    const/4 v9, 0x0

    .line 37
    const/4 v4, 0x0

    .line 38
    const/4 v6, 0x0

    .line 39
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->H3:Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    return-void
.end method

.method public static synthetic p3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->b4(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->R3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->a4(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic s3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->W3(LTv/d;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->s3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->L3(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic v3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->M3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic w3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->O3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic x3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lorg/xbet/core/domain/usecases/AddCommandScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S1:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lorg/xbet/core/domain/usecases/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->x1:Lorg/xbet/core/domain/usecases/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)LTv/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S2:LTv/e;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final L3(Ljava/util/List;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LzT0/c;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/16 v1, 0xa

    .line 6
    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    new-instance v0, Ljava/util/ArrayList;

    .line 10
    .line 11
    invoke-static {p1, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    invoke-direct {v0, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 16
    .line 17
    .line 18
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v3

    .line 26
    if-eqz v3, :cond_1

    .line 27
    .line 28
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v3

    .line 32
    check-cast v3, LzT0/c;

    .line 33
    .line 34
    invoke-virtual {v3}, LzT0/c;->c()I

    .line 35
    .line 36
    .line 37
    move-result v3

    .line 38
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 39
    .line 40
    .line 41
    move-result-object v3

    .line 42
    invoke-interface {v0, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 43
    .line 44
    .line 45
    goto :goto_0

    .line 46
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    :cond_1
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 51
    .line 52
    .line 53
    move-result v2

    .line 54
    if-nez v2, :cond_2

    .line 55
    .line 56
    new-instance v2, Ljava/util/ArrayList;

    .line 57
    .line 58
    invoke-static {p1, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 59
    .line 60
    .line 61
    move-result v3

    .line 62
    invoke-direct {v2, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 63
    .line 64
    .line 65
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 66
    .line 67
    .line 68
    move-result-object v3

    .line 69
    :goto_1
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 70
    .line 71
    .line 72
    move-result v4

    .line 73
    if-eqz v4, :cond_3

    .line 74
    .line 75
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object v4

    .line 79
    check-cast v4, LzT0/c;

    .line 80
    .line 81
    invoke-virtual {v4}, LzT0/c;->a()D

    .line 82
    .line 83
    .line 84
    move-result-wide v4

    .line 85
    invoke-static {v4, v5}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    .line 86
    .line 87
    .line 88
    move-result-object v4

    .line 89
    invoke-interface {v2, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 90
    .line 91
    .line 92
    goto :goto_1

    .line 93
    :cond_2
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 94
    .line 95
    .line 96
    move-result-object v2

    .line 97
    :cond_3
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 98
    .line 99
    .line 100
    move-result v3

    .line 101
    if-nez v3, :cond_4

    .line 102
    .line 103
    new-instance v3, Ljava/util/ArrayList;

    .line 104
    .line 105
    invoke-static {p1, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 106
    .line 107
    .line 108
    move-result v1

    .line 109
    invoke-direct {v3, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 110
    .line 111
    .line 112
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 113
    .line 114
    .line 115
    move-result-object p1

    .line 116
    :goto_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 117
    .line 118
    .line 119
    move-result v1

    .line 120
    if-eqz v1, :cond_5

    .line 121
    .line 122
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v1

    .line 126
    check-cast v1, LzT0/c;

    .line 127
    .line 128
    invoke-virtual {v1}, LzT0/c;->b()I

    .line 129
    .line 130
    .line 131
    move-result v1

    .line 132
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 133
    .line 134
    .line 135
    move-result-object v1

    .line 136
    invoke-interface {v3, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 137
    .line 138
    .line 139
    goto :goto_2

    .line 140
    :cond_4
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 141
    .line 142
    .line 143
    move-result-object v3

    .line 144
    :cond_5
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 145
    .line 146
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$a$a;

    .line 147
    .line 148
    invoke-direct {v1, v2, v0, v3}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$a$a;-><init>(Ljava/util/List;Ljava/util/List;Ljava/util/List;)V

    .line 149
    .line 150
    .line 151
    invoke-interface {p1, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 152
    .line 153
    .line 154
    return-void
.end method

.method public final M3()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$a$c;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$a$c;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 9
    .line 10
    sget-object v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e$a;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e$a;

    .line 11
    .line 12
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 16
    .line 17
    sget-object v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$b;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$b;

    .line 18
    .line 19
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 23
    .line 24
    sget-object v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$b;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$b;

    .line 25
    .line 26
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final N3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$a;

    .line 4
    .line 5
    iget-object v2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S2:LTv/e;

    .line 6
    .line 7
    invoke-virtual {v2}, LTv/e;->j()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-direct {v1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$a;-><init>(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V

    .line 12
    .line 13
    .line 14
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 18
    .line 19
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$a$b;

    .line 20
    .line 21
    iget-object v2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S2:LTv/e;

    .line 22
    .line 23
    invoke-virtual {v2}, LTv/e;->j()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    invoke-direct {v1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$a$b;-><init>(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V

    .line 28
    .line 29
    .line 30
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 31
    .line 32
    .line 33
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 34
    .line 35
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$a;

    .line 36
    .line 37
    iget-object v2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S2:LTv/e;

    .line 38
    .line 39
    invoke-virtual {v2}, LTv/e;->j()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    invoke-direct {v1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$a;-><init>(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V

    .line 44
    .line 45
    .line 46
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 47
    .line 48
    .line 49
    return-void
.end method

.method public final O3()V
    .locals 5

    .line 1
    const/4 v0, 0x1

    .line 2
    iput-boolean v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->V2:Z

    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->M3()V

    .line 5
    .line 6
    .line 7
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->V1:Lorg/xbet/tile_matching/domain/usecases/b;

    .line 8
    .line 9
    invoke-virtual {v1}, Lorg/xbet/tile_matching/domain/usecases/b;->a()LzT0/d;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v1}, LzT0/d;->a()Ljava/util/List;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    invoke-virtual {p0, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->L3(Ljava/util/List;)V

    .line 18
    .line 19
    .line 20
    sget-object v2, LTv/a$a;->a:LTv/a$a;

    .line 21
    .line 22
    invoke-direct {p0, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->K3(LTv/d;)V

    .line 23
    .line 24
    .line 25
    iget-object v2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 26
    .line 27
    new-instance v3, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$f;

    .line 28
    .line 29
    invoke-virtual {v1}, LzT0/d;->c()Ljava/util/List;

    .line 30
    .line 31
    .line 32
    move-result-object v4

    .line 33
    invoke-virtual {v1}, LzT0/d;->e()Ljava/util/List;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    invoke-direct {v3, v4, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$f;-><init>(Ljava/util/List;Ljava/util/List;)V

    .line 38
    .line 39
    .line 40
    invoke-interface {v2, v3}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 44
    .line 45
    new-instance v2, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e$b;

    .line 46
    .line 47
    const/4 v3, 0x0

    .line 48
    invoke-direct {v2, v3}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e$b;-><init>(Z)V

    .line 49
    .line 50
    .line 51
    invoke-interface {v1, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 52
    .line 53
    .line 54
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 55
    .line 56
    new-instance v2, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$c;

    .line 57
    .line 58
    invoke-direct {v2, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$c;-><init>(Z)V

    .line 59
    .line 60
    .line 61
    invoke-interface {v1, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    return-void
.end method

.method public final S3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final T3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final U3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final V3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final Y3()V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->M3()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->N3()V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public final Z3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->P2:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$1;-><init>(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    new-instance v2, Lorg/xbet/tile_matching/presentation/game/n;

    .line 17
    .line 18
    invoke-direct {v2, p0}, Lorg/xbet/tile_matching/presentation/game/n;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)V

    .line 19
    .line 20
    .line 21
    new-instance v5, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;

    .line 22
    .line 23
    const/4 v4, 0x0

    .line 24
    invoke-direct {v5, p0, v4}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    const/16 v6, 0x8

    .line 28
    .line 29
    const/4 v7, 0x0

    .line 30
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final c4(II)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    iget-object v2, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->I3:Lkotlinx/coroutines/x0;

    .line 5
    .line 6
    if-eqz v2, :cond_0

    .line 7
    .line 8
    invoke-interface {v2}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 9
    .line 10
    .line 11
    move-result v2

    .line 12
    if-ne v2, v1, :cond_0

    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 16
    .line 17
    .line 18
    move-result-object v3

    .line 19
    const-class v2, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 20
    .line 21
    invoke-virtual {v2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    new-instance v4, Ljava/lang/StringBuilder;

    .line 26
    .line 27
    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    .line 28
    .line 29
    .line 30
    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 31
    .line 32
    .line 33
    const-string v2, ".makeAction"

    .line 34
    .line 35
    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 36
    .line 37
    .line 38
    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object v4

    .line 42
    iget-object v2, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->P2:Lm8/a;

    .line 43
    .line 44
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 45
    .line 46
    .line 47
    move-result-object v11

    .line 48
    const/4 v2, 0x4

    .line 49
    new-array v2, v2, [Ljava/lang/Class;

    .line 50
    .line 51
    const-class v5, Lcom/xbet/onexcore/data/errors/UserAuthException;

    .line 52
    .line 53
    const/4 v6, 0x0

    .line 54
    aput-object v5, v2, v6

    .line 55
    .line 56
    const-class v5, Lcom/xbet/onexcore/BadDataResponseException;

    .line 57
    .line 58
    aput-object v5, v2, v1

    .line 59
    .line 60
    const-class v1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 61
    .line 62
    const/4 v5, 0x2

    .line 63
    aput-object v1, v2, v5

    .line 64
    .line 65
    const-class v1, Lcom/xbet/onexuser/domain/entity/onexgame/exception/GamesServerException;

    .line 66
    .line 67
    const/4 v5, 0x3

    .line 68
    aput-object v1, v2, v5

    .line 69
    .line 70
    invoke-static {v2}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 71
    .line 72
    .line 73
    move-result-object v8

    .line 74
    new-instance v9, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;

    .line 75
    .line 76
    const/4 v1, 0x0

    .line 77
    move/from16 v2, p1

    .line 78
    .line 79
    move/from16 v5, p2

    .line 80
    .line 81
    invoke-direct {v9, v0, v2, v5, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$makeAction$1;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;IILkotlin/coroutines/e;)V

    .line 82
    .line 83
    .line 84
    new-instance v12, Lorg/xbet/tile_matching/presentation/game/m;

    .line 85
    .line 86
    invoke-direct {v12, v0}, Lorg/xbet/tile_matching/presentation/game/m;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)V

    .line 87
    .line 88
    .line 89
    const/16 v14, 0x120

    .line 90
    .line 91
    const/4 v15, 0x0

    .line 92
    const/4 v5, 0x5

    .line 93
    const-wide/16 v6, 0x5

    .line 94
    .line 95
    const/4 v10, 0x0

    .line 96
    const/4 v13, 0x0

    .line 97
    invoke-static/range {v3 .. v15}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->Y(Lkotlinx/coroutines/N;Ljava/lang/String;IJLjava/util/List;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 98
    .line 99
    .line 100
    move-result-object v1

    .line 101
    iput-object v1, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->I3:Lkotlinx/coroutines/x0;

    .line 102
    .line 103
    return-void
.end method

.method public final d4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->y2:Lorg/xbet/core/domain/usecases/game_state/h;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/game_state/h;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 10
    .line 11
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$c;

    .line 12
    .line 13
    const/4 v2, 0x1

    .line 14
    invoke-direct {v1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$c;-><init>(Z)V

    .line 15
    .line 16
    .line 17
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 18
    .line 19
    .line 20
    :cond_0
    return-void
.end method

.method public final e4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->y2:Lorg/xbet/core/domain/usecases/game_state/h;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/game_state/h;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 10
    .line 11
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$c;

    .line 12
    .line 13
    const/4 v2, 0x0

    .line 14
    invoke-direct {v1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$c;-><init>(Z)V

    .line 15
    .line 16
    .line 17
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 18
    .line 19
    .line 20
    :cond_0
    return-void
.end method

.method public final f4()V
    .locals 1

    .line 1
    sget-object v0, LTv/a$b;->a:LTv/a$b;

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->K3(LTv/d;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->M3()V

    .line 7
    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->v2:Lorg/xbet/tile_matching/domain/usecases/d;

    .line 10
    .line 11
    invoke-virtual {v0}, Lorg/xbet/tile_matching/domain/usecases/d;->a()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-nez v0, :cond_0

    .line 16
    .line 17
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->P3()V

    .line 18
    .line 19
    .line 20
    :cond_0
    return-void
.end method

.method public final h4()V
    .locals 7

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->M3()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 5
    .line 6
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$a$b;

    .line 7
    .line 8
    iget-object v2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S2:LTv/e;

    .line 9
    .line 10
    invoke-virtual {v2}, LTv/e;->j()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 11
    .line 12
    .line 13
    move-result-object v2

    .line 14
    invoke-direct {v1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$a$b;-><init>(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V

    .line 15
    .line 16
    .line 17
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 18
    .line 19
    .line 20
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->V1:Lorg/xbet/tile_matching/domain/usecases/b;

    .line 21
    .line 22
    invoke-virtual {v0}, Lorg/xbet/tile_matching/domain/usecases/b;->a()LzT0/d;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->F2:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 27
    .line 28
    invoke-virtual {v1}, Lorg/xbet/core/domain/usecases/game_info/q;->a()Lorg/xbet/core/domain/GameState;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    sget-object v2, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$f;->a:[I

    .line 33
    .line 34
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    aget v1, v2, v1

    .line 39
    .line 40
    const/4 v2, 0x2

    .line 41
    const/4 v3, 0x1

    .line 42
    const/4 v4, 0x0

    .line 43
    const/4 v5, 0x0

    .line 44
    if-eq v1, v3, :cond_2

    .line 45
    .line 46
    if-eq v1, v2, :cond_1

    .line 47
    .line 48
    const/4 v3, 0x3

    .line 49
    if-ne v1, v3, :cond_0

    .line 50
    .line 51
    invoke-virtual {v0}, LzT0/d;->a()Ljava/util/List;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    invoke-virtual {p0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->L3(Ljava/util/List;)V

    .line 56
    .line 57
    .line 58
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 59
    .line 60
    new-instance v3, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$g;

    .line 61
    .line 62
    invoke-virtual {v0}, LzT0/d;->c()Ljava/util/List;

    .line 63
    .line 64
    .line 65
    move-result-object v0

    .line 66
    invoke-direct {v3, v0, v5, v2, v5}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$g;-><init>(Ljava/util/List;Ljava/util/List;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 67
    .line 68
    .line 69
    invoke-interface {v1, v3}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 70
    .line 71
    .line 72
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 73
    .line 74
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e$b;

    .line 75
    .line 76
    invoke-direct {v1, v4}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e$b;-><init>(Z)V

    .line 77
    .line 78
    .line 79
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 80
    .line 81
    .line 82
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 83
    .line 84
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$c;

    .line 85
    .line 86
    invoke-direct {v1, v4}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$c;-><init>(Z)V

    .line 87
    .line 88
    .line 89
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 90
    .line 91
    .line 92
    return-void

    .line 93
    :cond_0
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 94
    .line 95
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 96
    .line 97
    .line 98
    throw v0

    .line 99
    :cond_1
    invoke-virtual {v0}, LzT0/d;->a()Ljava/util/List;

    .line 100
    .line 101
    .line 102
    move-result-object v1

    .line 103
    invoke-virtual {p0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->L3(Ljava/util/List;)V

    .line 104
    .line 105
    .line 106
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 107
    .line 108
    new-instance v2, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$g;

    .line 109
    .line 110
    invoke-virtual {v0}, LzT0/d;->c()Ljava/util/List;

    .line 111
    .line 112
    .line 113
    move-result-object v5

    .line 114
    invoke-virtual {v0}, LzT0/d;->e()Ljava/util/List;

    .line 115
    .line 116
    .line 117
    move-result-object v0

    .line 118
    invoke-direct {v2, v5, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$g;-><init>(Ljava/util/List;Ljava/util/List;)V

    .line 119
    .line 120
    .line 121
    invoke-interface {v1, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 122
    .line 123
    .line 124
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 125
    .line 126
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e$b;

    .line 127
    .line 128
    invoke-direct {v1, v4}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e$b;-><init>(Z)V

    .line 129
    .line 130
    .line 131
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 132
    .line 133
    .line 134
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 135
    .line 136
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$c;

    .line 137
    .line 138
    invoke-direct {v1, v3}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$c;-><init>(Z)V

    .line 139
    .line 140
    .line 141
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 142
    .line 143
    .line 144
    return-void

    .line 145
    :cond_2
    invoke-virtual {v0}, LzT0/d;->a()Ljava/util/List;

    .line 146
    .line 147
    .line 148
    move-result-object v1

    .line 149
    invoke-virtual {p0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->L3(Ljava/util/List;)V

    .line 150
    .line 151
    .line 152
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 153
    .line 154
    new-instance v6, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$g;

    .line 155
    .line 156
    invoke-virtual {v0}, LzT0/d;->c()Ljava/util/List;

    .line 157
    .line 158
    .line 159
    move-result-object v0

    .line 160
    invoke-direct {v6, v0, v5, v2, v5}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$g;-><init>(Ljava/util/List;Ljava/util/List;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 161
    .line 162
    .line 163
    invoke-interface {v1, v6}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 164
    .line 165
    .line 166
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 167
    .line 168
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e$b;

    .line 169
    .line 170
    invoke-direct {v1, v3}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$e$b;-><init>(Z)V

    .line 171
    .line 172
    .line 173
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 174
    .line 175
    .line 176
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 177
    .line 178
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$c;

    .line 179
    .line 180
    invoke-direct {v1, v4}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$c;-><init>(Z)V

    .line 181
    .line 182
    .line 183
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 184
    .line 185
    .line 186
    return-void
.end method
