.class public final LnT0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\n\u0008\u0000\u0018\u00002\u00020\u0001B)\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\r\u0010\r\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\r\u0010\u000eR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\u000fR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u0011R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u0013R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0015\u00a8\u0006\u0016"
    }
    d2 = {
        "LnT0/d;",
        "LQW0/a;",
        "LRf0/o;",
        "settingsPrefsRepository",
        "Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;",
        "themeSwitchDataSource",
        "Li8/m;",
        "getThemeUseCase",
        "Lm8/a;",
        "coroutineDispatchers",
        "<init>",
        "(LRf0/o;Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;Li8/m;Lm8/a;)V",
        "LnT0/c;",
        "a",
        "()LnT0/c;",
        "LRf0/o;",
        "b",
        "Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;",
        "c",
        "Li8/m;",
        "d",
        "Lm8/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LRf0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Li8/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LRf0/o;Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;Li8/m;Lm8/a;)V
    .locals 0
    .param p1    # LRf0/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Li8/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LnT0/d;->a:LRf0/o;

    .line 5
    .line 6
    iput-object p2, p0, LnT0/d;->b:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;

    .line 7
    .line 8
    iput-object p3, p0, LnT0/d;->c:Li8/m;

    .line 9
    .line 10
    iput-object p4, p0, LnT0/d;->d:Lm8/a;

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public final a()LnT0/c;
    .locals 5
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, LnT0/a;->a()LnT0/c$a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, LnT0/d;->a:LRf0/o;

    .line 6
    .line 7
    iget-object v2, p0, LnT0/d;->b:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;

    .line 8
    .line 9
    iget-object v3, p0, LnT0/d;->c:Li8/m;

    .line 10
    .line 11
    iget-object v4, p0, LnT0/d;->d:Lm8/a;

    .line 12
    .line 13
    invoke-interface {v0, v1, v2, v3, v4}, LnT0/c$a;->a(LRf0/o;Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;Li8/m;Lm8/a;)LnT0/c;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    return-object v0
.end method
