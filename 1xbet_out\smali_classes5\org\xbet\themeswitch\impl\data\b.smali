.class public final Lorg/xbet/themeswitch/impl/data/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LpT0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0006\u0008\u0000\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0015\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u0006H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\n\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u0008\u000c\u0010\rJ/\u0010\u0015\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0014\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u0008\u0015\u0010\u0016R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0008\u0010\u0017\u00a8\u0006\u0018"
    }
    d2 = {
        "Lorg/xbet/themeswitch/impl/data/b;",
        "LpT0/a;",
        "Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;",
        "themeSwitchDataSource",
        "<init>",
        "(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;)V",
        "Lkotlinx/coroutines/flow/e;",
        "Lcom/xbet/onexcore/themes/Theme;",
        "a",
        "()Lkotlinx/coroutines/flow/e;",
        "oldTheme",
        "",
        "c",
        "(Lcom/xbet/onexcore/themes/Theme;)V",
        "",
        "name",
        "",
        "initialDelay",
        "",
        "timeTableEnabled",
        "theme",
        "b",
        "(Ljava/lang/String;JZLcom/xbet/onexcore/themes/Theme;)V",
        "Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;)V
    .locals 0
    .param p1    # Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/themeswitch/impl/data/b;->a:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lcom/xbet/onexcore/themes/Theme;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/themeswitch/impl/data/b;->a:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->e()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public b(Ljava/lang/String;JZLcom/xbet/onexcore/themes/Theme;)V
    .locals 6
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lcom/xbet/onexcore/themes/Theme;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/themeswitch/impl/data/b;->a:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;

    .line 2
    .line 3
    move-object v1, p1

    .line 4
    move-wide v2, p2

    .line 5
    move v4, p4

    .line 6
    move-object v5, p5

    .line 7
    invoke-virtual/range {v0 .. v5}, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->i(Ljava/lang/String;JZLcom/xbet/onexcore/themes/Theme;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public c(Lcom/xbet/onexcore/themes/Theme;)V
    .locals 1
    .param p1    # Lcom/xbet/onexcore/themes/Theme;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/themeswitch/impl/data/b;->a:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->h(Lcom/xbet/onexcore/themes/Theme;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
