.class public final Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/topplayerstatistic/viewholder/TournamentTopPlayerStatisticViewHolderKt$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/topplayerstatistic/viewholder/TournamentTopPlayerStatisticViewHolderKt;->g()LA4/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function1<",
        "Ljava/util/List<",
        "+",
        "Ljava/lang/Object;",
        ">;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:LB4/a;


# direct methods
.method public constructor <init>(LB4/a;LB4/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/topplayerstatistic/viewholder/TournamentTopPlayerStatisticViewHolderKt$a;->a:LB4/a;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/topplayerstatistic/viewholder/TournamentTopPlayerStatisticViewHolderKt$a;->b:LB4/a;

    .line 4
    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/topplayerstatistic/viewholder/TournamentTopPlayerStatisticViewHolderKt$a;->a:LB4/a;

    .line 8
    .line 9
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/topplayerstatistic/viewholder/TournamentTopPlayerStatisticViewHolderKt;->d(LB4/a;)V

    .line 10
    .line 11
    .line 12
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/topplayerstatistic/viewholder/TournamentTopPlayerStatisticViewHolderKt$a;->a:LB4/a;

    .line 13
    .line 14
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/topplayerstatistic/viewholder/TournamentTopPlayerStatisticViewHolderKt;->c(LB4/a;)V

    .line 15
    .line 16
    .line 17
    return-void

    .line 18
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    .line 19
    .line 20
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 21
    .line 22
    .line 23
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    if-eqz v1, :cond_1

    .line 32
    .line 33
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    check-cast v1, Ljava/util/Collection;

    .line 38
    .line 39
    check-cast v1, Ljava/lang/Iterable;

    .line 40
    .line 41
    invoke-static {v0, v1}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 42
    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_1
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 50
    .line 51
    .line 52
    move-result v0

    .line 53
    if-eqz v0, :cond_4

    .line 54
    .line 55
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    check-cast v0, LJx0/a$a;

    .line 60
    .line 61
    instance-of v1, v0, LJx0/a$a$b;

    .line 62
    .line 63
    if-eqz v1, :cond_2

    .line 64
    .line 65
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/topplayerstatistic/viewholder/TournamentTopPlayerStatisticViewHolderKt$a;->b:LB4/a;

    .line 66
    .line 67
    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/topplayerstatistic/viewholder/TournamentTopPlayerStatisticViewHolderKt;->d(LB4/a;)V

    .line 68
    .line 69
    .line 70
    goto :goto_1

    .line 71
    :cond_2
    instance-of v0, v0, LJx0/a$a$a;

    .line 72
    .line 73
    if-eqz v0, :cond_3

    .line 74
    .line 75
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/topplayerstatistic/viewholder/TournamentTopPlayerStatisticViewHolderKt$a;->b:LB4/a;

    .line 76
    .line 77
    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/topplayerstatistic/viewholder/TournamentTopPlayerStatisticViewHolderKt;->c(LB4/a;)V

    .line 78
    .line 79
    .line 80
    goto :goto_1

    .line 81
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 82
    .line 83
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 84
    .line 85
    .line 86
    throw p1

    .line 87
    :cond_4
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/topplayerstatistic/viewholder/TournamentTopPlayerStatisticViewHolderKt$a;->a(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p1
.end method
