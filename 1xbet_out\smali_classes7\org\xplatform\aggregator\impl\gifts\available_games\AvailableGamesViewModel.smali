.class public final Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$a;,
        Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00fa\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008#\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010%\n\u0002\u0008\u0005\n\u0002\u0010\"\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\n\u0008\u0000\u0018\u0000 \u0092\u00012\u00020\u0001:\u0004\u0093\u0001\u0094\u0001By\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u000f\u0010!\u001a\u00020 H\u0007\u00a2\u0006\u0004\u0008!\u0010\"J\u0019\u0010&\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020%0$0#\u00a2\u0006\u0004\u0008&\u0010\'J\r\u0010)\u001a\u00020(\u00a2\u0006\u0004\u0008)\u0010*J\u0013\u0010,\u001a\u0008\u0012\u0004\u0012\u00020+0#\u00a2\u0006\u0004\u0008,\u0010\'J\u0013\u0010-\u001a\u0008\u0012\u0004\u0012\u00020(0#\u00a2\u0006\u0004\u0008-\u0010\'J\u0013\u0010/\u001a\u0008\u0012\u0004\u0012\u00020.0#\u00a2\u0006\u0004\u0008/\u0010\'J\u0013\u00101\u001a\u0008\u0012\u0004\u0012\u0002000#\u00a2\u0006\u0004\u00081\u0010\'J\u0015\u00104\u001a\u00020(2\u0006\u00103\u001a\u000202\u00a2\u0006\u0004\u00084\u00105J\u0015\u00108\u001a\u00020(2\u0006\u00107\u001a\u000206\u00a2\u0006\u0004\u00088\u00109J\u001d\u0010;\u001a\u00020(2\u0006\u00103\u001a\u0002022\u0006\u0010:\u001a\u000200\u00a2\u0006\u0004\u0008;\u0010<J\r\u0010=\u001a\u00020(\u00a2\u0006\u0004\u0008=\u0010*J\r\u0010>\u001a\u00020(\u00a2\u0006\u0004\u0008>\u0010*J\u0015\u0010A\u001a\u00020(2\u0006\u0010@\u001a\u00020?\u00a2\u0006\u0004\u0008A\u0010BJ\r\u0010D\u001a\u00020C\u00a2\u0006\u0004\u0008D\u0010EJ\u000f\u0010F\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008F\u0010*J\u000f\u0010G\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008G\u0010*J\u000f\u0010H\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008H\u0010*J\u000f\u0010I\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008I\u0010*J\u0017\u0010K\u001a\u00020(2\u0006\u0010J\u001a\u00020?H\u0002\u00a2\u0006\u0004\u0008K\u0010BJ\u000f\u0010L\u001a\u00020CH\u0002\u00a2\u0006\u0004\u0008L\u0010ER\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008M\u0010NR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008O\u0010PR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Q\u0010RR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008S\u0010TR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008U\u0010VR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008W\u0010XR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Y\u0010ZR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008[\u0010\\R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008]\u0010^R\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008_\u0010`R\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008a\u0010bR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008c\u0010dR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008e\u0010fR\u0014\u0010j\u001a\u00020g8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008h\u0010iR\u0014\u0010n\u001a\u00020k8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008l\u0010mR\u001a\u0010r\u001a\u0008\u0012\u0004\u0012\u00020+0o8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008p\u0010qR\u001a\u0010v\u001a\u0008\u0012\u0004\u0012\u00020(0s8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008t\u0010uR\u0018\u0010z\u001a\u0004\u0018\u00010w8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008x\u0010yR\u0014\u0010}\u001a\u0002008\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008{\u0010|R\"\u0010\u0081\u0001\u001a\u000e\u0012\u0004\u0012\u000202\u0012\u0004\u0012\u0002060~8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u007f\u0010\u0080\u0001R\u001c\u0010\u0083\u0001\u001a\u0008\u0012\u0004\u0012\u0002000o8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0082\u0001\u0010qR#\u0010\u0086\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0004\u0012\u0002020\u0084\u00010s8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0085\u0001\u0010uR\u0018\u0010\u008a\u0001\u001a\u00030\u0087\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0088\u0001\u0010\u0089\u0001R\u001d\u0010\u008d\u0001\u001a\t\u0012\u0004\u0012\u00020(0\u008b\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008K\u0010\u008c\u0001R*\u0010\u0091\u0001\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u0002060$0#8\u0002X\u0082\u0004\u00a2\u0006\u000f\n\u0006\u0008\u008e\u0001\u0010\u008f\u0001\u0012\u0005\u0008\u0090\u0001\u0010*\u00a8\u0006\u0095\u0001"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lfa1/a;",
        "getGamesByBonusPagesScenario",
        "LHX0/e;",
        "resourceManager",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lea1/a;",
        "gamesInfo",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "openGameDelegate",
        "Lf81/a;",
        "addFavoriteUseCase",
        "Lf81/d;",
        "removeFavoriteUseCase",
        "Le81/c;",
        "getFavoriteGamesFlowScenario",
        "LwX0/C;",
        "routerHolder",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lm8/a;",
        "dispatchers",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "<init>",
        "(Lfa1/a;LHX0/e;Lp9/c;Lorg/xbet/ui_common/utils/internet/a;Lea1/a;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lf81/a;Lf81/d;Le81/c;LwX0/C;Lorg/xbet/ui_common/utils/M;Lm8/a;LSX0/c;Lorg/xbet/remoteconfig/domain/usecases/i;)V",
        "",
        "M3",
        "()I",
        "Lkotlinx/coroutines/flow/e;",
        "Landroidx/paging/PagingData;",
        "LN21/d;",
        "L3",
        "()Lkotlinx/coroutines/flow/e;",
        "",
        "c4",
        "()V",
        "Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b;",
        "K3",
        "R3",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
        "Q3",
        "",
        "N3",
        "",
        "gameId",
        "a4",
        "(J)V",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "b4",
        "(Lorg/xplatform/aggregator/api/model/Game;)V",
        "isFavorite",
        "Z3",
        "(JZ)V",
        "W3",
        "f4",
        "",
        "error",
        "U3",
        "(Ljava/lang/Throwable;)V",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "O3",
        "()Lorg/xbet/uikit/components/lottie_empty/n;",
        "V3",
        "g4",
        "X3",
        "d4",
        "throwable",
        "S3",
        "P3",
        "v1",
        "Lfa1/a;",
        "x1",
        "LHX0/e;",
        "y1",
        "Lp9/c;",
        "F1",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "H1",
        "Lea1/a;",
        "I1",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "P1",
        "Lf81/a;",
        "S1",
        "Lf81/d;",
        "V1",
        "Le81/c;",
        "b2",
        "LwX0/C;",
        "v2",
        "Lorg/xbet/ui_common/utils/M;",
        "x2",
        "Lm8/a;",
        "y2",
        "LSX0/c;",
        "Lek0/o;",
        "F2",
        "Lek0/o;",
        "remoteConfigModel",
        "Lek0/a;",
        "H2",
        "Lek0/a;",
        "aggregatorModel",
        "Lkotlinx/coroutines/flow/V;",
        "I2",
        "Lkotlinx/coroutines/flow/V;",
        "errorState",
        "Lkotlinx/coroutines/flow/U;",
        "P2",
        "Lkotlinx/coroutines/flow/U;",
        "refreshState",
        "Lkotlinx/coroutines/x0;",
        "S2",
        "Lkotlinx/coroutines/x0;",
        "networkConnectionJob",
        "V2",
        "Z",
        "virtual",
        "",
        "X2",
        "Ljava/util/Map;",
        "gamesMap",
        "F3",
        "loadingStateFlow",
        "",
        "H3",
        "favoriteGamesFlow",
        "Lkotlinx/coroutines/CoroutineExceptionHandler;",
        "I3",
        "Lkotlinx/coroutines/CoroutineExceptionHandler;",
        "coroutineErrorHandler",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "showFavoritesErrorState",
        "H4",
        "Lkotlinx/coroutines/flow/e;",
        "getGames$annotations",
        "games",
        "X4",
        "b",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final X4:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final F1:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:Lek0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F3:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lea1/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lek0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H3:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Ljava/util/Set<",
            "Ljava/lang/Long;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H4:Lkotlinx/coroutines/flow/e;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/e<",
            "Landroidx/paging/PagingData<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I3:Lkotlinx/coroutines/CoroutineExceptionHandler;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lf81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lf81/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public S2:Lkotlinx/coroutines/x0;

.field public final S3:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Le81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:Z

.field public final X2:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Long;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lfa1/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->X4:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$a;

    return-void
.end method

.method public constructor <init>(Lfa1/a;LHX0/e;Lp9/c;Lorg/xbet/ui_common/utils/internet/a;Lea1/a;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lf81/a;Lf81/d;Le81/c;LwX0/C;Lorg/xbet/ui_common/utils/M;Lm8/a;LSX0/c;Lorg/xbet/remoteconfig/domain/usecases/i;)V
    .locals 12
    .param p1    # Lfa1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lea1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lf81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lf81/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Le81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->v1:Lfa1/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->x1:LHX0/e;

    .line 7
    .line 8
    move-object v0, p3

    .line 9
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->y1:Lp9/c;

    .line 10
    .line 11
    move-object/from16 v0, p4

    .line 12
    .line 13
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->F1:Lorg/xbet/ui_common/utils/internet/a;

    .line 14
    .line 15
    move-object/from16 v0, p5

    .line 16
    .line 17
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->H1:Lea1/a;

    .line 18
    .line 19
    move-object/from16 v0, p6

    .line 20
    .line 21
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->I1:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 22
    .line 23
    move-object/from16 v0, p7

    .line 24
    .line 25
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->P1:Lf81/a;

    .line 26
    .line 27
    move-object/from16 v0, p8

    .line 28
    .line 29
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->S1:Lf81/d;

    .line 30
    .line 31
    move-object/from16 v0, p9

    .line 32
    .line 33
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->V1:Le81/c;

    .line 34
    .line 35
    move-object/from16 v0, p10

    .line 36
    .line 37
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->b2:LwX0/C;

    .line 38
    .line 39
    move-object/from16 v0, p11

    .line 40
    .line 41
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->v2:Lorg/xbet/ui_common/utils/M;

    .line 42
    .line 43
    move-object/from16 v0, p12

    .line 44
    .line 45
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->x2:Lm8/a;

    .line 46
    .line 47
    move-object/from16 v1, p13

    .line 48
    .line 49
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->y2:LSX0/c;

    .line 50
    .line 51
    invoke-interface/range {p14 .. p14}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->F2:Lek0/o;

    .line 56
    .line 57
    invoke-virtual {v1}, Lek0/o;->o()Lek0/a;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->H2:Lek0/a;

    .line 62
    .line 63
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b$b;->a:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b$b;

    .line 64
    .line 65
    invoke-static {v1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->I2:Lkotlinx/coroutines/flow/V;

    .line 70
    .line 71
    const/4 v1, 0x6

    .line 72
    const/4 v2, 0x1

    .line 73
    const/4 v3, 0x0

    .line 74
    const/4 v4, 0x0

    .line 75
    invoke-static {v2, v3, v4, v1, v4}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    .line 76
    .line 77
    .line 78
    move-result-object v1

    .line 79
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->P2:Lkotlinx/coroutines/flow/U;

    .line 80
    .line 81
    invoke-interface/range {p14 .. p14}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 82
    .line 83
    .line 84
    move-result-object v5

    .line 85
    invoke-virtual {v5}, Lek0/o;->y1()Z

    .line 86
    .line 87
    .line 88
    move-result v5

    .line 89
    iput-boolean v5, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->V2:Z

    .line 90
    .line 91
    new-instance v5, Ljava/util/LinkedHashMap;

    .line 92
    .line 93
    invoke-direct {v5}, Ljava/util/LinkedHashMap;-><init>()V

    .line 94
    .line 95
    .line 96
    iput-object v5, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->X2:Ljava/util/Map;

    .line 97
    .line 98
    sget-object v5, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 99
    .line 100
    invoke-static {v5}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 101
    .line 102
    .line 103
    move-result-object v5

    .line 104
    iput-object v5, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->F3:Lkotlinx/coroutines/flow/V;

    .line 105
    .line 106
    const/4 v5, 0x7

    .line 107
    invoke-static {v3, v3, v4, v5, v4}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    .line 108
    .line 109
    .line 110
    move-result-object v5

    .line 111
    iput-object v5, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->H3:Lkotlinx/coroutines/flow/U;

    .line 112
    .line 113
    sget-object v5, Lkotlinx/coroutines/CoroutineExceptionHandler;->Y3:Lkotlinx/coroutines/CoroutineExceptionHandler$a;

    .line 114
    .line 115
    new-instance v6, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$c;

    .line 116
    .line 117
    invoke-direct {v6, v5, p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$c;-><init>(Lkotlinx/coroutines/CoroutineExceptionHandler$a;Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)V

    .line 118
    .line 119
    .line 120
    iput-object v6, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->I3:Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 121
    .line 122
    new-instance v5, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 123
    .line 124
    sget-object v7, Lkotlinx/coroutines/channels/BufferOverflow;->DROP_OLDEST:Lkotlinx/coroutines/channels/BufferOverflow;

    .line 125
    .line 126
    invoke-direct {v5, v2, v7}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;)V

    .line 127
    .line 128
    .line 129
    iput-object v5, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->S3:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 130
    .line 131
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$special$$inlined$flatMapLatest$1;

    .line 132
    .line 133
    invoke-direct {v5, v4, p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$special$$inlined$flatMapLatest$1;-><init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)V

    .line 134
    .line 135
    .line 136
    invoke-static {v1, v5}, Lkotlinx/coroutines/flow/g;->C0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 137
    .line 138
    .line 139
    move-result-object v1

    .line 140
    const/4 v4, 0x2

    .line 141
    new-array v4, v4, [Ljava/lang/Class;

    .line 142
    .line 143
    const-class v5, Ljava/net/UnknownHostException;

    .line 144
    .line 145
    aput-object v5, v4, v3

    .line 146
    .line 147
    const-class v3, Ljava/net/SocketTimeoutException;

    .line 148
    .line 149
    aput-object v3, v4, v2

    .line 150
    .line 151
    invoke-static {v4}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 152
    .line 153
    .line 154
    move-result-object v2

    .line 155
    const/16 v3, 0x34

    .line 156
    .line 157
    const/4 v4, 0x0

    .line 158
    const-string v5, "AvailableGamesViewModel.updateData"

    .line 159
    .line 160
    const/4 v7, 0x5

    .line 161
    const-wide/16 v8, 0x0

    .line 162
    .line 163
    const/4 v10, 0x0

    .line 164
    const/4 v11, 0x0

    .line 165
    move-object p1, v1

    .line 166
    move-object/from16 p6, v2

    .line 167
    .line 168
    move-object/from16 p10, v4

    .line 169
    .line 170
    move-object p2, v5

    .line 171
    move-wide/from16 p4, v8

    .line 172
    .line 173
    move-object/from16 p7, v10

    .line 174
    .line 175
    move-object/from16 p8, v11

    .line 176
    .line 177
    const/4 p3, 0x5

    .line 178
    const/16 p9, 0x34

    .line 179
    .line 180
    invoke-static/range {p1 .. p10}, Lcom/xbet/onexcore/utils/flows/FlowBuilderKt;->e(Lkotlinx/coroutines/flow/e;Ljava/lang/String;IJLjava/util/List;Ljava/util/List;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 181
    .line 182
    .line 183
    move-result-object v1

    .line 184
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 185
    .line 186
    .line 187
    move-result-object v2

    .line 188
    invoke-static {v2, v6}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 189
    .line 190
    .line 191
    move-result-object v2

    .line 192
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 193
    .line 194
    .line 195
    move-result-object v0

    .line 196
    invoke-static {v2, v0}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 197
    .line 198
    .line 199
    move-result-object v0

    .line 200
    invoke-static {v1, v0}, Landroidx/paging/CachedPagingDataKt;->a(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/flow/e;

    .line 201
    .line 202
    .line 203
    move-result-object v0

    .line 204
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->H4:Lkotlinx/coroutines/flow/e;

    .line 205
    .line 206
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->d4()V

    .line 207
    .line 208
    .line 209
    return-void
.end method

.method public static final synthetic A3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->P2:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lek0/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->F2:Lek0/o;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lf81/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->S1:Lf81/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->x1:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->S3:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic F3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->V2:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic G3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->S3(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic H3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->V3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic I3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->Y3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic J3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->d4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final P3()Lorg/xbet/uikit/components/lottie_empty/n;
    .locals 12

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->y2:LSX0/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 4
    .line 5
    sget v6, Lpb/k;->data_retrieval_error:I

    .line 6
    .line 7
    const/16 v10, 0x1de

    .line 8
    .line 9
    const/4 v11, 0x0

    .line 10
    const/4 v2, 0x0

    .line 11
    const/4 v3, 0x0

    .line 12
    const/4 v4, 0x0

    .line 13
    const/4 v5, 0x0

    .line 14
    const/4 v7, 0x0

    .line 15
    const/4 v8, 0x0

    .line 16
    const/4 v9, 0x0

    .line 17
    invoke-static/range {v0 .. v11}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    return-object v0
.end method

.method private final S3(Ljava/lang/Throwable;)V
    .locals 9

    .line 1
    instance-of v0, p1, Ljava/net/UnknownHostException;

    .line 2
    .line 3
    if-nez v0, :cond_3

    .line 4
    .line 5
    instance-of v0, p1, Ljava/net/SocketTimeoutException;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    instance-of v0, p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 11
    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->g4()V

    .line 15
    .line 16
    .line 17
    return-void

    .line 18
    :cond_1
    instance-of v0, p1, Lorg/xplatform/aggregator/api/domain/exceptions/FavoritesLimitException;

    .line 19
    .line 20
    if-eqz v0, :cond_2

    .line 21
    .line 22
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->x2:Lm8/a;

    .line 27
    .line 28
    invoke-interface {v0}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 29
    .line 30
    .line 31
    move-result-object v4

    .line 32
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/available_games/j;

    .line 33
    .line 34
    invoke-direct {v2, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/j;-><init>(Ljava/lang/Throwable;)V

    .line 35
    .line 36
    .line 37
    new-instance v6, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$handleError$2;

    .line 38
    .line 39
    const/4 p1, 0x0

    .line 40
    invoke-direct {v6, p0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$handleError$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Lkotlin/coroutines/e;)V

    .line 41
    .line 42
    .line 43
    const/16 v7, 0xa

    .line 44
    .line 45
    const/4 v8, 0x0

    .line 46
    const/4 v3, 0x0

    .line 47
    const/4 v5, 0x0

    .line 48
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 49
    .line 50
    .line 51
    return-void

    .line 52
    :cond_2
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->v2:Lorg/xbet/ui_common/utils/M;

    .line 53
    .line 54
    invoke-interface {v0, p1}, Lorg/xbet/ui_common/utils/M;->i(Ljava/lang/Throwable;)V

    .line 55
    .line 56
    .line 57
    return-void

    .line 58
    :cond_3
    :goto_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->f4()V

    .line 59
    .line 60
    .line 61
    return-void
.end method

.method public static final T3(Ljava/lang/Throwable;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final V3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->y1:Lp9/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Lp9/c;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->V1:Le81/c;

    .line 10
    .line 11
    invoke-interface {v0}, Le81/c;->invoke()Lkotlinx/coroutines/flow/e;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    goto :goto_0

    .line 16
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->X(Ljava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    :goto_0
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$loadFavoriteGames$1;

    .line 25
    .line 26
    const/4 v2, 0x0

    .line 27
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$loadFavoriteGames$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Lkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->I3:Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 39
    .line 40
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 45
    .line 46
    .line 47
    return-void
.end method

.method private final X3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->S2:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->F1:Lorg/xbet/ui_common/utils/internet/a;

    .line 14
    .line 15
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$observeConnection$1;

    .line 20
    .line 21
    const/4 v2, 0x0

    .line 22
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$observeConnection$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->x2:Lm8/a;

    .line 34
    .line 35
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 36
    .line 37
    .line 38
    move-result-object v2

    .line 39
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$observeConnection$2;

    .line 44
    .line 45
    invoke-direct {v2, p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$observeConnection$2;-><init>(Ljava/lang/Object;)V

    .line 46
    .line 47
    .line 48
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->S2:Lkotlinx/coroutines/x0;

    .line 53
    .line 54
    return-void
.end method

.method public static final synthetic Y3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->S3(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final d4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->x2:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/available_games/k;

    .line 12
    .line 13
    invoke-direct {v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/k;-><init>()V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$refresh$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$refresh$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public static final e4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final g4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->I2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b$a;

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->O3()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 10
    .line 11
    .line 12
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public static synthetic p3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->e4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q3(Ljava/lang/Throwable;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->T3(Ljava/lang/Throwable;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic r3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lf81/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->P1:Lf81/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lek0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->H2:Lek0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->I2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->H3:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lea1/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->H1:Lea1/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Ljava/util/Map;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->X2:Ljava/util/Map;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lp9/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->y1:Lp9/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lfa1/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->v1:Lfa1/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->F3:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final K3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->I2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final L3()Lkotlinx/coroutines/flow/e;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Landroidx/paging/PagingData<",
            "LN21/d;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->H4:Lkotlinx/coroutines/flow/e;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->H3:Lkotlinx/coroutines/flow/U;

    .line 4
    .line 5
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;

    .line 6
    .line 7
    const/4 v3, 0x0

    .line 8
    invoke-direct {v2, p0, v3}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Lkotlin/coroutines/e;)V

    .line 9
    .line 10
    .line 11
    invoke-static {v0, v1, v2}, Lkotlinx/coroutines/flow/g;->W(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$2;

    .line 16
    .line 17
    invoke-direct {v1, p0, v3}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->I3:Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 29
    .line 30
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->x2:Lm8/a;

    .line 35
    .line 36
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 37
    .line 38
    .line 39
    move-result-object v2

    .line 40
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    invoke-static {v0, v1}, Landroidx/paging/CachedPagingDataKt;->a(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/flow/e;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    return-object v0
.end method

.method public final M3()I
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->F2:Lek0/o;

    .line 2
    .line 3
    invoke-virtual {v0}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-static {v0, v1}, Ls81/b;->b(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Z)I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    return v0
.end method

.method public final N3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->F3:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final O3()Lorg/xbet/uikit/components/lottie_empty/n;
    .locals 12
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->y2:LSX0/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->SEARCH:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 4
    .line 5
    sget v6, Lpb/k;->nothing_found:I

    .line 6
    .line 7
    const/16 v10, 0x1de

    .line 8
    .line 9
    const/4 v11, 0x0

    .line 10
    const/4 v2, 0x0

    .line 11
    const/4 v3, 0x0

    .line 12
    const/4 v4, 0x0

    .line 13
    const/4 v5, 0x0

    .line 14
    const/4 v7, 0x0

    .line 15
    const/4 v8, 0x0

    .line 16
    const/4 v9, 0x0

    .line 17
    invoke-static/range {v0 .. v11}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    return-object v0
.end method

.method public final Q3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->I1:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->q()Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final R3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->S3:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final U3(Ljava/lang/Throwable;)V
    .locals 2
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->I3:Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 2
    .line 3
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-interface {v1}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-interface {v0, v1, p1}, Lkotlinx/coroutines/CoroutineExceptionHandler;->handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final W3()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->b2:LwX0/C;

    .line 2
    .line 3
    invoke-virtual {v0}, LwX0/D;->a()LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {v0}, LwX0/c;->h()V

    .line 10
    .line 11
    .line 12
    :cond_0
    return-void
.end method

.method public final Z3(JZ)V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->X2:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    check-cast p1, Lorg/xplatform/aggregator/api/model/Game;

    .line 12
    .line 13
    if-eqz p1, :cond_0

    .line 14
    .line 15
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->x2:Lm8/a;

    .line 20
    .line 21
    invoke-interface {p2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 22
    .line 23
    .line 24
    move-result-object v3

    .line 25
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$1;

    .line 26
    .line 27
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$1;-><init>(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;

    .line 31
    .line 32
    const/4 p2, 0x0

    .line 33
    invoke-direct {v5, p3, p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onFavoriteClick$1$2;-><init>(ZLorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Lorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)V

    .line 34
    .line 35
    .line 36
    const/16 v6, 0xa

    .line 37
    .line 38
    const/4 v7, 0x0

    .line 39
    const/4 v2, 0x0

    .line 40
    const/4 v4, 0x0

    .line 41
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 42
    .line 43
    .line 44
    :cond_0
    return-void
.end method

.method public final a4(J)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->X2:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    check-cast p1, Lorg/xplatform/aggregator/api/model/Game;

    .line 12
    .line 13
    if-eqz p1, :cond_0

    .line 14
    .line 15
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->b4(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 16
    .line 17
    .line 18
    :cond_0
    return-void
.end method

.method public final b4(Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 3
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->I1:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onGameClicked$1;

    .line 4
    .line 5
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->v2:Lorg/xbet/ui_common/utils/M;

    .line 6
    .line 7
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onGameClicked$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    const/4 v2, 0x0

    .line 11
    invoke-virtual {v0, p1, v2, v1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->u(Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final c4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->P2:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onInitView$1;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$onInitView$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Lkotlin/coroutines/e;)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->I3:Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 18
    .line 19
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final f4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->I2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b$a;

    .line 4
    .line 5
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->P3()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 10
    .line 11
    .line 12
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->X3()V

    .line 16
    .line 17
    .line 18
    return-void
.end method
