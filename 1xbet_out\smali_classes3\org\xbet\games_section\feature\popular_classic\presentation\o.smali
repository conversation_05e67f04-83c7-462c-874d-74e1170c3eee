.class public final synthetic Lorg/xbet/games_section/feature/popular_classic/presentation/o;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

.field public final synthetic b:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/o;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    iput-object p2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/o;->b:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/o;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/o;->b:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    invoke-static {v0, v1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$openGame$2;->a(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
