.class public final Lorg/spongycastle/pqc/crypto/xmss/q;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:Lorg/spongycastle/pqc/crypto/xmss/p;

.field public final b:Lorg/spongycastle/pqc/crypto/xmss/g;

.field public final c:I

.field public final d:I


# direct methods
.method public constructor <init>(ILorg/spongycastle/crypto/e;)V
    .locals 3

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x2

    .line 5
    if-lt p1, v0, :cond_1

    .line 6
    .line 7
    if-eqz p2, :cond_0

    .line 8
    .line 9
    new-instance v0, Lorg/spongycastle/pqc/crypto/xmss/g;

    .line 10
    .line 11
    new-instance v1, Lorg/spongycastle/pqc/crypto/xmss/i;

    .line 12
    .line 13
    invoke-direct {v1, p2}, Lorg/spongycastle/pqc/crypto/xmss/i;-><init>(Lorg/spongycastle/crypto/e;)V

    .line 14
    .line 15
    .line 16
    invoke-direct {v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/g;-><init>(Lorg/spongycastle/pqc/crypto/xmss/i;)V

    .line 17
    .line 18
    .line 19
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/q;->b:Lorg/spongycastle/pqc/crypto/xmss/g;

    .line 20
    .line 21
    iput p1, p0, Lorg/spongycastle/pqc/crypto/xmss/q;->c:I

    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/spongycastle/pqc/crypto/xmss/q;->a()I

    .line 24
    .line 25
    .line 26
    move-result p2

    .line 27
    iput p2, p0, Lorg/spongycastle/pqc/crypto/xmss/q;->d:I

    .line 28
    .line 29
    invoke-virtual {p0}, Lorg/spongycastle/pqc/crypto/xmss/q;->b()Lorg/spongycastle/crypto/e;

    .line 30
    .line 31
    .line 32
    move-result-object p2

    .line 33
    invoke-interface {p2}, Lorg/spongycastle/crypto/e;->c()Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object p2

    .line 37
    invoke-virtual {p0}, Lorg/spongycastle/pqc/crypto/xmss/q;->c()I

    .line 38
    .line 39
    .line 40
    move-result v1

    .line 41
    invoke-virtual {p0}, Lorg/spongycastle/pqc/crypto/xmss/q;->g()I

    .line 42
    .line 43
    .line 44
    move-result v2

    .line 45
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/g;->d()Lorg/spongycastle/pqc/crypto/xmss/i;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/i;->c()I

    .line 50
    .line 51
    .line 52
    move-result v0

    .line 53
    invoke-static {p2, v1, v2, v0, p1}, Lorg/spongycastle/pqc/crypto/xmss/b;->b(Ljava/lang/String;IIII)Lorg/spongycastle/pqc/crypto/xmss/b;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/q;->a:Lorg/spongycastle/pqc/crypto/xmss/p;

    .line 58
    .line 59
    return-void

    .line 60
    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    .line 61
    .line 62
    const-string p2, "digest == null"

    .line 63
    .line 64
    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 65
    .line 66
    .line 67
    throw p1

    .line 68
    :cond_1
    new-instance p1, Ljava/lang/IllegalArgumentException;

    .line 69
    .line 70
    const-string p2, "height must be >= 2"

    .line 71
    .line 72
    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 73
    .line 74
    .line 75
    throw p1
.end method


# virtual methods
.method public final a()I
    .locals 3

    .line 1
    const/4 v0, 0x2

    .line 2
    const/4 v1, 0x2

    .line 3
    :goto_0
    iget v2, p0, Lorg/spongycastle/pqc/crypto/xmss/q;->c:I

    .line 4
    .line 5
    if-gt v1, v2, :cond_1

    .line 6
    .line 7
    sub-int/2addr v2, v1

    .line 8
    rem-int/2addr v2, v0

    .line 9
    if-nez v2, :cond_0

    .line 10
    .line 11
    return v1

    .line 12
    :cond_0
    add-int/lit8 v1, v1, 0x1

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 16
    .line 17
    const-string v1, "should never happen..."

    .line 18
    .line 19
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 20
    .line 21
    .line 22
    throw v0
.end method

.method public b()Lorg/spongycastle/crypto/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/q;->b:Lorg/spongycastle/pqc/crypto/xmss/g;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/g;->d()Lorg/spongycastle/pqc/crypto/xmss/i;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/i;->a()Lorg/spongycastle/crypto/e;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0
.end method

.method public c()I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/q;->b:Lorg/spongycastle/pqc/crypto/xmss/g;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/g;->d()Lorg/spongycastle/pqc/crypto/xmss/i;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/i;->b()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    return v0
.end method

.method public d()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/spongycastle/pqc/crypto/xmss/q;->c:I

    .line 2
    .line 3
    return v0
.end method

.method public e()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/spongycastle/pqc/crypto/xmss/q;->d:I

    .line 2
    .line 3
    return v0
.end method

.method public f()Lorg/spongycastle/pqc/crypto/xmss/g;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/q;->b:Lorg/spongycastle/pqc/crypto/xmss/g;

    .line 2
    .line 3
    return-object v0
.end method

.method public g()I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/q;->b:Lorg/spongycastle/pqc/crypto/xmss/g;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/g;->d()Lorg/spongycastle/pqc/crypto/xmss/i;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/i;->d()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    return v0
.end method
