.class public final Lorg/xplatform/aggregator/impl/core/presentation/t;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
        ">",
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment<",
        "TT;>;>;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;LTZ0/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
            ">(",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment<",
            "TT;>;",
            "LTZ0/a;",
            ")V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->i0:LTZ0/a;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lck/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
            ">(",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment<",
            "TT;>;",
            "Lck/a;",
            ")V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->k0:Lck/a;

    .line 2
    .line 3
    return-void
.end method

.method public static c(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;LzX0/k;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
            ">(",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment<",
            "TT;>;",
            "LzX0/k;",
            ")V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->j0:LzX0/k;

    .line 2
    .line 3
    return-void
.end method
