.class public final LNC0/d$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LNC0/a$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LNC0/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LNC0/e;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LNC0/d$b;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LQW0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;LkC0/a;LDH0/a;LwX0/c;JLQD0/d;LSX0/a;Lc8/h;)LNC0/a;
    .locals 15

    .line 1
    invoke-static/range {p1 .. p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static/range {p2 .. p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    invoke-static/range {p9 .. p10}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    invoke-static/range {p12 .. p12}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    invoke-static/range {p13 .. p13}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    new-instance v1, LNC0/d$a;

    .line 42
    .line 43
    invoke-static/range {p9 .. p10}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 44
    .line 45
    .line 46
    move-result-object v10

    .line 47
    const/4 v14, 0x0

    .line 48
    move-object/from16 v2, p1

    .line 49
    .line 50
    move-object/from16 v3, p2

    .line 51
    .line 52
    move-object/from16 v4, p3

    .line 53
    .line 54
    move-object/from16 v5, p4

    .line 55
    .line 56
    move-object/from16 v6, p5

    .line 57
    .line 58
    move-object/from16 v7, p6

    .line 59
    .line 60
    move-object/from16 v8, p7

    .line 61
    .line 62
    move-object/from16 v9, p8

    .line 63
    .line 64
    move-object/from16 v11, p11

    .line 65
    .line 66
    move-object/from16 v12, p12

    .line 67
    .line 68
    move-object/from16 v13, p13

    .line 69
    .line 70
    invoke-direct/range {v1 .. v14}, LNC0/d$a;-><init>(LQW0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;LkC0/a;LDH0/a;LwX0/c;Ljava/lang/Long;LQD0/d;LSX0/a;Lc8/h;LNC0/e;)V

    .line 71
    .line 72
    .line 73
    return-object v1
.end method
