.class public final Lorg/xbet/ui_common/dialogs/PeriodDatePicker;
.super Lorg/xbet/ui_common/moxy/dialogs/IntellijDialog;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/ui_common/dialogs/PeriodDatePicker$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0005\n\u0002\u0010\u0008\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\u001d\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0007\u0018\u0000 H2\u00020\u0001:\u0001IB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u0017\u0010\t\u001a\u00020\u00082\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\u000b\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\r\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000cJ\u000f\u0010\u000f\u001a\u00020\u000eH\u0014\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u000f\u0010\u0011\u001a\u00020\u000eH\u0014\u00a2\u0006\u0004\u0008\u0011\u0010\u0010J\u000f\u0010\u0012\u001a\u00020\u000eH\u0014\u00a2\u0006\u0004\u0008\u0012\u0010\u0010J\u000f\u0010\u0013\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0013\u0010\u0003J\u000f\u0010\u0014\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0014\u0010\u0003J\u000f\u0010\u0015\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0015\u0010\u0003J\u000f\u0010\u0016\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0003J\u0017\u0010\u0019\u001a\u00020\u00042\u0006\u0010\u0018\u001a\u00020\u0017H\u0014\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u000f\u0010\u001b\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u001b\u0010\u0003R+\u0010$\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001c8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008\u001e\u0010\u001f\u001a\u0004\u0008 \u0010!\"\u0004\u0008\"\u0010#R+\u0010*\u001a\u00020\u000e2\u0006\u0010\u001d\u001a\u00020\u000e8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008%\u0010&\u001a\u0004\u0008\'\u0010\u0010\"\u0004\u0008(\u0010)R+\u00101\u001a\u00020\u00082\u0006\u0010\u001d\u001a\u00020\u00088B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.\"\u0004\u0008/\u00100R+\u00105\u001a\u00020\u00082\u0006\u0010\u001d\u001a\u00020\u00088B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u00082\u0010,\u001a\u0004\u00083\u0010.\"\u0004\u00084\u00100R+\u00109\u001a\u00020\u00082\u0006\u0010\u001d\u001a\u00020\u00088B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u00086\u0010,\u001a\u0004\u00087\u0010.\"\u0004\u00088\u00100R+\u0010A\u001a\u00020:2\u0006\u0010\u001d\u001a\u00020:8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008;\u0010<\u001a\u0004\u0008=\u0010>\"\u0004\u0008?\u0010@R\u001b\u0010G\u001a\u00020B8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008C\u0010D\u001a\u0004\u0008E\u0010F\u00a8\u0006J"
    }
    d2 = {
        "Lorg/xbet/ui_common/dialogs/PeriodDatePicker;",
        "Lorg/xbet/ui_common/moxy/dialogs/IntellijDialog;",
        "<init>",
        "()V",
        "",
        "y3",
        "Ljava/util/Calendar;",
        "calendar",
        "",
        "k3",
        "(Ljava/util/Calendar;)J",
        "u3",
        "(Ljava/util/Calendar;)V",
        "A3",
        "",
        "V2",
        "()I",
        "L2",
        "B2",
        "Z2",
        "v2",
        "N2",
        "X2",
        "Landroidx/appcompat/app/a$a;",
        "builder",
        "Y2",
        "(Landroidx/appcompat/app/a$a;)V",
        "E2",
        "",
        "<set-?>",
        "o0",
        "LeX0/k;",
        "p3",
        "()Ljava/lang/String;",
        "x3",
        "(Ljava/lang/String;)V",
        "requestKey",
        "b1",
        "LeX0/d;",
        "o3",
        "w3",
        "(I)V",
        "maxPeriod",
        "k1",
        "LeX0/f;",
        "q3",
        "()J",
        "z3",
        "(J)V",
        "startTimeSec",
        "v1",
        "l3",
        "s3",
        "defaultFromTimeSec",
        "x1",
        "m3",
        "t3",
        "endTimeSec",
        "",
        "y1",
        "LeX0/a;",
        "n3",
        "()Z",
        "v3",
        "(Z)V",
        "fromStartDate",
        "LPW0/f;",
        "F1",
        "LRc/c;",
        "j3",
        "()LPW0/f;",
        "binding",
        "H1",
        "a",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final H1:Lorg/xbet/ui_common/dialogs/PeriodDatePicker$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic I1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final P1:I


# instance fields
.field public final F1:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b1:LeX0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k1:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o0:LeX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:LeX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 11

    .line 1
    new-instance v0, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;

    .line 4
    .line 5
    const-string v2, "requestKey"

    .line 6
    .line 7
    const-string v3, "getRequestKey()Ljava/lang/String;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "maxPeriod"

    .line 20
    .line 21
    const-string v5, "getMaxPeriod()I"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "startTimeSec"

    .line 33
    .line 34
    const-string v6, "getStartTimeSec()J"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    new-instance v5, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 44
    .line 45
    const-string v6, "defaultFromTimeSec"

    .line 46
    .line 47
    const-string v7, "getDefaultFromTimeSec()J"

    .line 48
    .line 49
    invoke-direct {v5, v1, v6, v7, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 50
    .line 51
    .line 52
    invoke-static {v5}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 53
    .line 54
    .line 55
    move-result-object v5

    .line 56
    new-instance v6, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 57
    .line 58
    const-string v7, "endTimeSec"

    .line 59
    .line 60
    const-string v8, "getEndTimeSec()J"

    .line 61
    .line 62
    invoke-direct {v6, v1, v7, v8, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 63
    .line 64
    .line 65
    invoke-static {v6}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 66
    .line 67
    .line 68
    move-result-object v6

    .line 69
    new-instance v7, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 70
    .line 71
    const-string v8, "fromStartDate"

    .line 72
    .line 73
    const-string v9, "getFromStartDate()Z"

    .line 74
    .line 75
    invoke-direct {v7, v1, v8, v9, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 76
    .line 77
    .line 78
    invoke-static {v7}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 79
    .line 80
    .line 81
    move-result-object v7

    .line 82
    new-instance v8, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 83
    .line 84
    const-string v9, "binding"

    .line 85
    .line 86
    const-string v10, "getBinding()Lorg/xbet/ui_common/databinding/DatePickerViewBinding;"

    .line 87
    .line 88
    invoke-direct {v8, v1, v9, v10, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 89
    .line 90
    .line 91
    invoke-static {v8}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 92
    .line 93
    .line 94
    move-result-object v1

    .line 95
    const/4 v8, 0x7

    .line 96
    new-array v8, v8, [Lkotlin/reflect/m;

    .line 97
    .line 98
    aput-object v0, v8, v4

    .line 99
    .line 100
    const/4 v0, 0x1

    .line 101
    aput-object v2, v8, v0

    .line 102
    .line 103
    const/4 v0, 0x2

    .line 104
    aput-object v3, v8, v0

    .line 105
    .line 106
    const/4 v0, 0x3

    .line 107
    aput-object v5, v8, v0

    .line 108
    .line 109
    const/4 v0, 0x4

    .line 110
    aput-object v6, v8, v0

    .line 111
    .line 112
    const/4 v0, 0x5

    .line 113
    aput-object v7, v8, v0

    .line 114
    .line 115
    const/4 v0, 0x6

    .line 116
    aput-object v1, v8, v0

    .line 117
    .line 118
    sput-object v8, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->I1:[Lkotlin/reflect/m;

    .line 119
    .line 120
    new-instance v0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker$a;

    .line 121
    .line 122
    const/4 v1, 0x0

    .line 123
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 124
    .line 125
    .line 126
    sput-object v0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->H1:Lorg/xbet/ui_common/dialogs/PeriodDatePicker$a;

    .line 127
    .line 128
    const/16 v0, 0x8

    .line 129
    .line 130
    sput v0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->P1:I

    .line 131
    .line 132
    return-void
.end method

.method public constructor <init>()V
    .locals 12

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/moxy/dialogs/IntellijDialog;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, LeX0/k;

    .line 5
    .line 6
    const-string v1, "BUNDLE_REQUEST_KEY"

    .line 7
    .line 8
    const/4 v2, 0x0

    .line 9
    const/4 v3, 0x2

    .line 10
    invoke-direct {v0, v1, v2, v3, v2}, LeX0/k;-><init>(Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 11
    .line 12
    .line 13
    iput-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->o0:LeX0/k;

    .line 14
    .line 15
    new-instance v0, LeX0/d;

    .line 16
    .line 17
    const-string v1, "BUNDLE_MAX_PERIOD"

    .line 18
    .line 19
    const/4 v4, 0x0

    .line 20
    invoke-direct {v0, v1, v4, v3, v2}, LeX0/d;-><init>(Ljava/lang/String;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 21
    .line 22
    .line 23
    iput-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->b1:LeX0/d;

    .line 24
    .line 25
    new-instance v5, LeX0/f;

    .line 26
    .line 27
    const/4 v9, 0x2

    .line 28
    const/4 v10, 0x0

    .line 29
    const-string v6, "BUNDLE_START_DATE"

    .line 30
    .line 31
    const-wide/16 v7, 0x0

    .line 32
    .line 33
    invoke-direct/range {v5 .. v10}, LeX0/f;-><init>(Ljava/lang/String;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 34
    .line 35
    .line 36
    iput-object v5, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->k1:LeX0/f;

    .line 37
    .line 38
    new-instance v6, LeX0/f;

    .line 39
    .line 40
    const/4 v10, 0x2

    .line 41
    const/4 v11, 0x0

    .line 42
    const-string v7, "BUNDLE_DEFAULT_FROM_DATE"

    .line 43
    .line 44
    const-wide/16 v8, 0x0

    .line 45
    .line 46
    invoke-direct/range {v6 .. v11}, LeX0/f;-><init>(Ljava/lang/String;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 47
    .line 48
    .line 49
    iput-object v6, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->v1:LeX0/f;

    .line 50
    .line 51
    new-instance v0, LeX0/f;

    .line 52
    .line 53
    const-string v1, "BUNDLE_END_DATE"

    .line 54
    .line 55
    const-wide/16 v5, 0x0

    .line 56
    .line 57
    invoke-direct {v0, v1, v5, v6}, LeX0/f;-><init>(Ljava/lang/String;J)V

    .line 58
    .line 59
    .line 60
    iput-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->x1:LeX0/f;

    .line 61
    .line 62
    new-instance v0, LeX0/a;

    .line 63
    .line 64
    const-string v1, "BUNDLE_FROM_START_DATE"

    .line 65
    .line 66
    invoke-direct {v0, v1, v4, v3, v2}, LeX0/a;-><init>(Ljava/lang/String;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 67
    .line 68
    .line 69
    iput-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->y1:LeX0/a;

    .line 70
    .line 71
    sget-object v0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker$binding$2;->INSTANCE:Lorg/xbet/ui_common/dialogs/PeriodDatePicker$binding$2;

    .line 72
    .line 73
    invoke-static {p0, v0}, LLX0/j;->e(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    iput-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->F1:LRc/c;

    .line 78
    .line 79
    return-void
.end method

.method public static synthetic d3(Ljava/util/Calendar;Lorg/xbet/ui_common/dialogs/PeriodDatePicker;Landroid/widget/CalendarView;III)V
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->r3(Ljava/util/Calendar;Lorg/xbet/ui_common/dialogs/PeriodDatePicker;Landroid/widget/CalendarView;III)V

    return-void
.end method

.method public static final synthetic e3(Lorg/xbet/ui_common/dialogs/PeriodDatePicker;J)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->s3(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic f3(Lorg/xbet/ui_common/dialogs/PeriodDatePicker;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->v3(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic g3(Lorg/xbet/ui_common/dialogs/PeriodDatePicker;I)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->w3(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic h3(Lorg/xbet/ui_common/dialogs/PeriodDatePicker;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->x3(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic i3(Lorg/xbet/ui_common/dialogs/PeriodDatePicker;J)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->z3(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final o3()I
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->b1:LeX0/d;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/d;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Integer;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method private final p3()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->o0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/k;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    return-object v0
.end method

.method public static final r3(Ljava/util/Calendar;Lorg/xbet/ui_common/dialogs/PeriodDatePicker;Landroid/widget/CalendarView;III)V
    .locals 1

    .line 1
    invoke-virtual {p0, p3, p4, p5}, Ljava/util/Calendar;->set(III)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p1}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->j3()LPW0/f;

    .line 5
    .line 6
    .line 7
    move-result-object p2

    .line 8
    iget-object p2, p2, LPW0/f;->b:Landroid/widget/CalendarView;

    .line 9
    .line 10
    invoke-virtual {p1, p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->k3(Ljava/util/Calendar;)J

    .line 11
    .line 12
    .line 13
    move-result-wide p3

    .line 14
    const/4 p5, 0x0

    .line 15
    const/4 v0, 0x1

    .line 16
    invoke-virtual {p2, p3, p4, p5, v0}, Landroid/widget/CalendarView;->setDate(JZZ)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p1}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->n3()Z

    .line 20
    .line 21
    .line 22
    move-result p2

    .line 23
    if-eqz p2, :cond_0

    .line 24
    .line 25
    invoke-virtual {p1, p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->u3(Ljava/util/Calendar;)V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :cond_0
    invoke-virtual {p1, p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->A3(Ljava/util/Calendar;)V

    .line 30
    .line 31
    .line 32
    return-void
.end method

.method private final w3(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->b1:LeX0/d;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/d;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final x3(Ljava/lang/String;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->o0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/k;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method


# virtual methods
.method public final A3(Ljava/util/Calendar;)V
    .locals 4

    .line 1
    invoke-virtual {p1}, Ljava/util/Calendar;->getTimeInMillis()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    invoke-static {v0, v1}, Landroid/text/format/DateUtils;->isToday(J)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-nez v0, :cond_0

    .line 10
    .line 11
    const/16 v0, 0xb

    .line 12
    .line 13
    const/16 v1, 0x17

    .line 14
    .line 15
    invoke-virtual {p1, v0, v1}, Ljava/util/Calendar;->set(II)V

    .line 16
    .line 17
    .line 18
    const/16 v0, 0xc

    .line 19
    .line 20
    const/16 v1, 0x3b

    .line 21
    .line 22
    invoke-virtual {p1, v0, v1}, Ljava/util/Calendar;->set(II)V

    .line 23
    .line 24
    .line 25
    const/16 v0, 0xd

    .line 26
    .line 27
    invoke-virtual {p1, v0, v1}, Ljava/util/Calendar;->set(II)V

    .line 28
    .line 29
    .line 30
    const/16 v0, 0xe

    .line 31
    .line 32
    const/4 v1, 0x0

    .line 33
    invoke-virtual {p1, v0, v1}, Ljava/util/Calendar;->set(II)V

    .line 34
    .line 35
    .line 36
    :cond_0
    invoke-virtual {p1}, Ljava/util/Calendar;->getTimeInMillis()J

    .line 37
    .line 38
    .line 39
    move-result-wide v0

    .line 40
    const/16 p1, 0x3e8

    .line 41
    .line 42
    int-to-long v2, p1

    .line 43
    div-long/2addr v0, v2

    .line 44
    invoke-virtual {p0, v0, v1}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->t3(J)V

    .line 45
    .line 46
    .line 47
    return-void
.end method

.method public B2()I
    .locals 1

    .line 1
    sget v0, Lpb/l;->ThemeOverlay_AppTheme_MaterialAlertDialog_New_DatePicker:I

    .line 2
    .line 3
    return v0
.end method

.method public E2()V
    .locals 11

    .line 1
    const/4 v0, 0x1

    .line 2
    const/4 v1, 0x0

    .line 3
    sget v2, Landroid/os/Build$VERSION;->SDK_INT:I

    .line 4
    .line 5
    const/16 v3, 0x16

    .line 6
    .line 7
    if-gt v2, v3, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->j3()LPW0/f;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    iget-object v2, v2, LPW0/f;->b:Landroid/widget/CalendarView;

    .line 14
    .line 15
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    const/16 v3, 0x1f4

    .line 20
    .line 21
    iput v3, v2, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 22
    .line 23
    :cond_0
    invoke-direct {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->o3()I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    if-lez v2, :cond_2

    .line 28
    .line 29
    sget v2, Lpb/k;->max_period_description:I

    .line 30
    .line 31
    invoke-virtual {p0, v2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v2

    .line 35
    invoke-direct {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->o3()I

    .line 36
    .line 37
    .line 38
    move-result v3

    .line 39
    if-lez v3, :cond_1

    .line 40
    .line 41
    invoke-direct {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->o3()I

    .line 42
    .line 43
    .line 44
    move-result v3

    .line 45
    goto :goto_0

    .line 46
    :cond_1
    const/16 v3, 0x1e

    .line 47
    .line 48
    :goto_0
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 49
    .line 50
    .line 51
    move-result-object v4

    .line 52
    sget v5, Lpb/k;->days_count:I

    .line 53
    .line 54
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 55
    .line 56
    .line 57
    move-result-object v3

    .line 58
    new-array v6, v0, [Ljava/lang/Object;

    .line 59
    .line 60
    aput-object v3, v6, v1

    .line 61
    .line 62
    invoke-virtual {v4, v5, v6}, Landroid/content/res/Resources;->getString(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 63
    .line 64
    .line 65
    move-result-object v3

    .line 66
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->j3()LPW0/f;

    .line 67
    .line 68
    .line 69
    move-result-object v4

    .line 70
    iget-object v4, v4, LPW0/f;->e:Landroid/widget/TextView;

    .line 71
    .line 72
    invoke-virtual {v4, v1}, Landroid/view/View;->setVisibility(I)V

    .line 73
    .line 74
    .line 75
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->j3()LPW0/f;

    .line 76
    .line 77
    .line 78
    move-result-object v4

    .line 79
    iget-object v4, v4, LPW0/f;->e:Landroid/widget/TextView;

    .line 80
    .line 81
    new-instance v5, Ljava/lang/StringBuilder;

    .line 82
    .line 83
    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    .line 84
    .line 85
    .line 86
    invoke-virtual {v5, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 87
    .line 88
    .line 89
    const-string v2, " "

    .line 90
    .line 91
    invoke-virtual {v5, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 92
    .line 93
    .line 94
    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 95
    .line 96
    .line 97
    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 98
    .line 99
    .line 100
    move-result-object v2

    .line 101
    invoke-virtual {v4, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 102
    .line 103
    .line 104
    :cond_2
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->j3()LPW0/f;

    .line 105
    .line 106
    .line 107
    move-result-object v2

    .line 108
    iget-object v2, v2, LPW0/f;->f:Landroid/widget/TextView;

    .line 109
    .line 110
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->n3()Z

    .line 111
    .line 112
    .line 113
    move-result v3

    .line 114
    if-eqz v3, :cond_3

    .line 115
    .line 116
    sget v3, Lpb/k;->start_date_period:I

    .line 117
    .line 118
    invoke-virtual {p0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 119
    .line 120
    .line 121
    move-result-object v3

    .line 122
    goto :goto_1

    .line 123
    :cond_3
    sget v3, Lpb/k;->end_date_period:I

    .line 124
    .line 125
    invoke-virtual {p0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 126
    .line 127
    .line 128
    move-result-object v3

    .line 129
    :goto_1
    invoke-virtual {v2, v3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 130
    .line 131
    .line 132
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/dialogs/IntellijDialog;->A2()Landroid/widget/Button;

    .line 133
    .line 134
    .line 135
    move-result-object v2

    .line 136
    if-eqz v2, :cond_5

    .line 137
    .line 138
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->n3()Z

    .line 139
    .line 140
    .line 141
    move-result v3

    .line 142
    if-eqz v3, :cond_4

    .line 143
    .line 144
    sget v3, Lpb/k;->next:I

    .line 145
    .line 146
    invoke-virtual {p0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 147
    .line 148
    .line 149
    move-result-object v3

    .line 150
    goto :goto_2

    .line 151
    :cond_4
    sget v3, Lpb/k;->apply:I

    .line 152
    .line 153
    invoke-virtual {p0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 154
    .line 155
    .line 156
    move-result-object v3

    .line 157
    :goto_2
    invoke-virtual {v2, v3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 158
    .line 159
    .line 160
    :cond_5
    invoke-static {}, Ljava/util/Calendar;->getInstance()Ljava/util/Calendar;

    .line 161
    .line 162
    .line 163
    move-result-object v2

    .line 164
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->j3()LPW0/f;

    .line 165
    .line 166
    .line 167
    move-result-object v3

    .line 168
    iget-object v3, v3, LPW0/f;->b:Landroid/widget/CalendarView;

    .line 169
    .line 170
    invoke-virtual {v2}, Ljava/util/Calendar;->getTimeInMillis()J

    .line 171
    .line 172
    .line 173
    move-result-wide v4

    .line 174
    invoke-virtual {v3, v4, v5}, Landroid/widget/CalendarView;->setMaxDate(J)V

    .line 175
    .line 176
    .line 177
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->n3()Z

    .line 178
    .line 179
    .line 180
    move-result v3

    .line 181
    const/16 v4, 0x3e8

    .line 182
    .line 183
    const-wide/16 v5, 0x0

    .line 184
    .line 185
    if-eqz v3, :cond_8

    .line 186
    .line 187
    invoke-static {}, Ljava/util/Calendar;->getInstance()Ljava/util/Calendar;

    .line 188
    .line 189
    .line 190
    move-result-object v3

    .line 191
    const/16 v7, 0x7d0

    .line 192
    .line 193
    invoke-virtual {v3, v7, v1, v0}, Ljava/util/Calendar;->set(III)V

    .line 194
    .line 195
    .line 196
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->j3()LPW0/f;

    .line 197
    .line 198
    .line 199
    move-result-object v7

    .line 200
    iget-object v7, v7, LPW0/f;->b:Landroid/widget/CalendarView;

    .line 201
    .line 202
    invoke-virtual {v3}, Ljava/util/Calendar;->getTimeInMillis()J

    .line 203
    .line 204
    .line 205
    move-result-wide v8

    .line 206
    invoke-virtual {v7, v8, v9}, Landroid/widget/CalendarView;->setMinDate(J)V

    .line 207
    .line 208
    .line 209
    invoke-direct {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->o3()I

    .line 210
    .line 211
    .line 212
    move-result v3

    .line 213
    if-eqz v3, :cond_7

    .line 214
    .line 215
    invoke-direct {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->o3()I

    .line 216
    .line 217
    .line 218
    move-result v3

    .line 219
    sub-int/2addr v3, v0

    .line 220
    neg-int v3, v3

    .line 221
    const/4 v7, 0x5

    .line 222
    invoke-virtual {v2, v7, v3}, Ljava/util/Calendar;->add(II)V

    .line 223
    .line 224
    .line 225
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->j3()LPW0/f;

    .line 226
    .line 227
    .line 228
    move-result-object v3

    .line 229
    iget-object v3, v3, LPW0/f;->b:Landroid/widget/CalendarView;

    .line 230
    .line 231
    invoke-virtual {v2}, Ljava/util/Calendar;->getTimeInMillis()J

    .line 232
    .line 233
    .line 234
    move-result-wide v7

    .line 235
    invoke-virtual {v3, v7, v8}, Landroid/widget/CalendarView;->setMinDate(J)V

    .line 236
    .line 237
    .line 238
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->l3()J

    .line 239
    .line 240
    .line 241
    move-result-wide v7

    .line 242
    cmp-long v3, v7, v5

    .line 243
    .line 244
    if-eqz v3, :cond_6

    .line 245
    .line 246
    invoke-static {}, Ljava/util/Calendar;->getInstance()Ljava/util/Calendar;

    .line 247
    .line 248
    .line 249
    move-result-object v3

    .line 250
    sget-object v7, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    .line 251
    .line 252
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->l3()J

    .line 253
    .line 254
    .line 255
    move-result-wide v8

    .line 256
    invoke-virtual {v7, v8, v9}, Ljava/util/concurrent/TimeUnit;->toMillis(J)J

    .line 257
    .line 258
    .line 259
    move-result-wide v7

    .line 260
    invoke-virtual {v3, v7, v8}, Ljava/util/Calendar;->setTimeInMillis(J)V

    .line 261
    .line 262
    .line 263
    invoke-virtual {p0, v3}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->u3(Ljava/util/Calendar;)V

    .line 264
    .line 265
    .line 266
    goto :goto_3

    .line 267
    :cond_6
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->q3()J

    .line 268
    .line 269
    .line 270
    move-result-wide v7

    .line 271
    cmp-long v3, v7, v5

    .line 272
    .line 273
    if-nez v3, :cond_7

    .line 274
    .line 275
    invoke-virtual {p0, v2}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->u3(Ljava/util/Calendar;)V

    .line 276
    .line 277
    .line 278
    :cond_7
    :goto_3
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->q3()J

    .line 279
    .line 280
    .line 281
    move-result-wide v7

    .line 282
    cmp-long v3, v7, v5

    .line 283
    .line 284
    if-eqz v3, :cond_a

    .line 285
    .line 286
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->q3()J

    .line 287
    .line 288
    .line 289
    move-result-wide v7

    .line 290
    int-to-long v9, v4

    .line 291
    mul-long v7, v7, v9

    .line 292
    .line 293
    invoke-virtual {v2, v7, v8}, Ljava/util/Calendar;->setTimeInMillis(J)V

    .line 294
    .line 295
    .line 296
    goto :goto_5

    .line 297
    :cond_8
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->m3()J

    .line 298
    .line 299
    .line 300
    move-result-wide v7

    .line 301
    cmp-long v3, v7, v5

    .line 302
    .line 303
    if-nez v3, :cond_9

    .line 304
    .line 305
    invoke-virtual {v2}, Ljava/util/Calendar;->getTimeInMillis()J

    .line 306
    .line 307
    .line 308
    move-result-wide v7

    .line 309
    int-to-long v9, v4

    .line 310
    div-long/2addr v7, v9

    .line 311
    invoke-virtual {p0, v7, v8}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->t3(J)V

    .line 312
    .line 313
    .line 314
    goto :goto_4

    .line 315
    :cond_9
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->m3()J

    .line 316
    .line 317
    .line 318
    move-result-wide v7

    .line 319
    int-to-long v9, v4

    .line 320
    mul-long v7, v7, v9

    .line 321
    .line 322
    invoke-virtual {v2, v7, v8}, Ljava/util/Calendar;->setTimeInMillis(J)V

    .line 323
    .line 324
    .line 325
    :goto_4
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->j3()LPW0/f;

    .line 326
    .line 327
    .line 328
    move-result-object v3

    .line 329
    iget-object v3, v3, LPW0/f;->b:Landroid/widget/CalendarView;

    .line 330
    .line 331
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->q3()J

    .line 332
    .line 333
    .line 334
    move-result-wide v7

    .line 335
    int-to-long v9, v4

    .line 336
    mul-long v7, v7, v9

    .line 337
    .line 338
    invoke-virtual {v3, v7, v8}, Landroid/widget/CalendarView;->setMinDate(J)V

    .line 339
    .line 340
    .line 341
    invoke-virtual {p0, v2}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->A3(Ljava/util/Calendar;)V

    .line 342
    .line 343
    .line 344
    :cond_a
    :goto_5
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->j3()LPW0/f;

    .line 345
    .line 346
    .line 347
    move-result-object v3

    .line 348
    iget-object v3, v3, LPW0/f;->b:Landroid/widget/CalendarView;

    .line 349
    .line 350
    invoke-virtual {p0, v2}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->k3(Ljava/util/Calendar;)J

    .line 351
    .line 352
    .line 353
    move-result-wide v7

    .line 354
    invoke-virtual {v3, v7, v8, v1, v0}, Landroid/widget/CalendarView;->setDate(JZZ)V

    .line 355
    .line 356
    .line 357
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->j3()LPW0/f;

    .line 358
    .line 359
    .line 360
    move-result-object v0

    .line 361
    iget-object v0, v0, LPW0/f;->b:Landroid/widget/CalendarView;

    .line 362
    .line 363
    new-instance v1, Lorg/xbet/ui_common/dialogs/e;

    .line 364
    .line 365
    invoke-direct {v1, v2, p0}, Lorg/xbet/ui_common/dialogs/e;-><init>(Ljava/util/Calendar;Lorg/xbet/ui_common/dialogs/PeriodDatePicker;)V

    .line 366
    .line 367
    .line 368
    invoke-virtual {v0, v1}, Landroid/widget/CalendarView;->setOnDateChangeListener(Landroid/widget/CalendarView$OnDateChangeListener;)V

    .line 369
    .line 370
    .line 371
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->q3()J

    .line 372
    .line 373
    .line 374
    move-result-wide v0

    .line 375
    cmp-long v3, v0, v5

    .line 376
    .line 377
    if-nez v3, :cond_b

    .line 378
    .line 379
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->n3()Z

    .line 380
    .line 381
    .line 382
    move-result v0

    .line 383
    if-eqz v0, :cond_b

    .line 384
    .line 385
    invoke-virtual {v2}, Ljava/util/Calendar;->getTimeInMillis()J

    .line 386
    .line 387
    .line 388
    move-result-wide v0

    .line 389
    int-to-long v2, v4

    .line 390
    div-long/2addr v0, v2

    .line 391
    invoke-virtual {p0, v0, v1}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->z3(J)V

    .line 392
    .line 393
    .line 394
    :cond_b
    return-void
.end method

.method public L2()I
    .locals 1

    .line 1
    sget v0, Lpb/k;->cancel:I

    .line 2
    .line 3
    return v0
.end method

.method public N2()V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->y3()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/l;->dismiss()V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public V2()I
    .locals 1

    .line 1
    sget v0, Lpb/k;->next:I

    .line 2
    .line 3
    return v0
.end method

.method public X2()V
    .locals 5

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->q3()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    const-wide/16 v2, 0x0

    .line 6
    .line 7
    cmp-long v4, v0, v2

    .line 8
    .line 9
    if-nez v4, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->m3()J

    .line 12
    .line 13
    .line 14
    move-result-wide v0

    .line 15
    cmp-long v4, v0, v2

    .line 16
    .line 17
    if-nez v4, :cond_0

    .line 18
    .line 19
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->y3()V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, Landroidx/fragment/app/l;->dismiss()V

    .line 23
    .line 24
    .line 25
    return-void

    .line 26
    :cond_0
    new-instance v0, Landroid/os/Bundle;

    .line 27
    .line 28
    invoke-direct {v0}, Landroid/os/Bundle;-><init>()V

    .line 29
    .line 30
    .line 31
    const-string v1, "BUNDLE_RESULT_START_TIME_SEC"

    .line 32
    .line 33
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->q3()J

    .line 34
    .line 35
    .line 36
    move-result-wide v2

    .line 37
    invoke-virtual {v0, v1, v2, v3}, Landroid/os/BaseBundle;->putLong(Ljava/lang/String;J)V

    .line 38
    .line 39
    .line 40
    const-string v1, "BUNDLE_RESULT_END_TIME_SEC"

    .line 41
    .line 42
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->m3()J

    .line 43
    .line 44
    .line 45
    move-result-wide v2

    .line 46
    invoke-virtual {v0, v1, v2, v3}, Landroid/os/BaseBundle;->putLong(Ljava/lang/String;J)V

    .line 47
    .line 48
    .line 49
    invoke-direct {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->p3()Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object v1

    .line 53
    invoke-static {p0, v1, v0}, Landroidx/fragment/app/x;->d(Landroidx/fragment/app/Fragment;Ljava/lang/String;Landroid/os/Bundle;)V

    .line 54
    .line 55
    .line 56
    invoke-virtual {p0}, Landroidx/fragment/app/l;->dismiss()V

    .line 57
    .line 58
    .line 59
    return-void
.end method

.method public Y2(Landroidx/appcompat/app/a$a;)V
    .locals 1
    .param p1    # Landroidx/appcompat/app/a$a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->j3()LPW0/f;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, LPW0/f;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1, v0}, Landroidx/appcompat/app/a$a;->setView(Landroid/view/View;)Landroidx/appcompat/app/a$a;

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public Z2()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    sget v2, Lpb/f;->popup_width:I

    .line 10
    .line 11
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    sget-object v2, Lorg/xbet/ui_common/utils/g;->a:Lorg/xbet/ui_common/utils/g;

    .line 16
    .line 17
    invoke-virtual {v2, v0}, Lorg/xbet/ui_common/utils/g;->M(Landroid/content/Context;)I

    .line 18
    .line 19
    .line 20
    move-result v3

    .line 21
    invoke-virtual {v2, v0}, Lorg/xbet/ui_common/utils/g;->P(Landroid/content/Context;)I

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    invoke-static {v3, v2}, Ljava/lang/Math;->min(II)I

    .line 26
    .line 27
    .line 28
    move-result v2

    .line 29
    invoke-static {v2, v1}, Ljava/lang/Math;->min(II)I

    .line 30
    .line 31
    .line 32
    move-result v1

    .line 33
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    sget v2, Lpb/f;->space_32:I

    .line 38
    .line 39
    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 40
    .line 41
    .line 42
    move-result v0

    .line 43
    mul-int/lit8 v0, v0, 0x2

    .line 44
    .line 45
    sub-int/2addr v1, v0

    .line 46
    invoke-virtual {p0}, Landroidx/fragment/app/l;->getDialog()Landroid/app/Dialog;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    if-eqz v0, :cond_1

    .line 51
    .line 52
    invoke-virtual {v0}, Landroid/app/Dialog;->getWindow()Landroid/view/Window;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    if-nez v0, :cond_0

    .line 57
    .line 58
    goto :goto_0

    .line 59
    :cond_0
    const/4 v2, -0x2

    .line 60
    invoke-virtual {v0, v1, v2}, Landroid/view/Window;->setLayout(II)V

    .line 61
    .line 62
    .line 63
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 64
    .line 65
    .line 66
    move-result-object v1

    .line 67
    sget v2, Lpb/g;->background_round_content_background_new:I

    .line 68
    .line 69
    invoke-static {v1, v2}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    invoke-virtual {v0, v1}, Landroid/view/Window;->setBackgroundDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 74
    .line 75
    .line 76
    :cond_1
    :goto_0
    return-void
.end method

.method public final j3()LPW0/f;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->F1:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x6

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LPW0/f;

    .line 13
    .line 14
    return-object v0
.end method

.method public final k3(Ljava/util/Calendar;)J
    .locals 4

    .line 1
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    .line 2
    .line 3
    const/16 v1, 0x16

    .line 4
    .line 5
    if-gt v0, v1, :cond_0

    .line 6
    .line 7
    invoke-virtual {p1}, Ljava/util/Calendar;->getTimeInMillis()J

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    const-wide/32 v2, 0x5265c00

    .line 12
    .line 13
    .line 14
    add-long/2addr v0, v2

    .line 15
    return-wide v0

    .line 16
    :cond_0
    invoke-virtual {p1}, Ljava/util/Calendar;->getTimeInMillis()J

    .line 17
    .line 18
    .line 19
    move-result-wide v0

    .line 20
    return-wide v0
.end method

.method public final l3()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->v1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/f;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Long;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v0

    .line 16
    return-wide v0
.end method

.method public final m3()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->x1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x4

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/f;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Long;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v0

    .line 16
    return-wide v0
.end method

.method public final n3()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->y1:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x5

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/a;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Boolean;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method public final q3()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->k1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/f;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Long;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v0

    .line 16
    return-wide v0
.end method

.method public final s3(J)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->v1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1, p2}, LeX0/f;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;J)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final t3(J)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->x1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x4

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1, p2}, LeX0/f;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;J)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final u3(Ljava/util/Calendar;)V
    .locals 4

    .line 1
    const/16 v0, 0xb

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {p1, v0, v1}, Ljava/util/Calendar;->set(II)V

    .line 5
    .line 6
    .line 7
    const/16 v0, 0xc

    .line 8
    .line 9
    invoke-virtual {p1, v0, v1}, Ljava/util/Calendar;->set(II)V

    .line 10
    .line 11
    .line 12
    const/16 v0, 0xd

    .line 13
    .line 14
    invoke-virtual {p1, v0, v1}, Ljava/util/Calendar;->set(II)V

    .line 15
    .line 16
    .line 17
    const/16 v0, 0xe

    .line 18
    .line 19
    invoke-virtual {p1, v0, v1}, Ljava/util/Calendar;->set(II)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p1}, Ljava/util/Calendar;->getTimeInMillis()J

    .line 23
    .line 24
    .line 25
    move-result-wide v0

    .line 26
    const/16 p1, 0x3e8

    .line 27
    .line 28
    int-to-long v2, p1

    .line 29
    div-long/2addr v0, v2

    .line 30
    invoke-virtual {p0, v0, v1}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->z3(J)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public v2()V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->y3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final v3(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->y1:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x5

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/a;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Z)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final y3()V
    .locals 3

    .line 1
    new-instance v0, Landroid/os/Bundle;

    .line 2
    .line 3
    invoke-direct {v0}, Landroid/os/Bundle;-><init>()V

    .line 4
    .line 5
    .line 6
    const-string v1, "BUNDLE_RESULT_CANCELED"

    .line 7
    .line 8
    const/4 v2, 0x1

    .line 9
    invoke-virtual {v0, v1, v2}, Landroid/os/Bundle;->putBoolean(Ljava/lang/String;Z)V

    .line 10
    .line 11
    .line 12
    invoke-direct {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->p3()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    invoke-static {p0, v1, v0}, Landroidx/fragment/app/x;->d(Landroidx/fragment/app/Fragment;Ljava/lang/String;Landroid/os/Bundle;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public final z3(J)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->k1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->I1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1, p2}, LeX0/f;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;J)V

    .line 9
    .line 10
    .line 11
    return-void
.end method
