.class public final Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0002\u0008\u0019\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0001\u0018\u00002\u00020\u0001Be\u0008\u0007\u0012\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0015\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u001b\u001a\u00020\u001a\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\r\u0010\u001f\u001a\u00020\u001c\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0013\u0010#\u001a\u0008\u0012\u0004\u0012\u00020\"0!\u00a2\u0006\u0004\u0008#\u0010$J\u000f\u0010%\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008%\u0010 J\u001f\u0010(\u001a\u00020\u001c2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\'\u001a\u00020&H\u0002\u00a2\u0006\u0004\u0008(\u0010)R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00105R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00109R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u0010;R\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=R\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010?R\u001a\u0010D\u001a\u0008\u0012\u0004\u0012\u00020A0@8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010C\u00a8\u0006E"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Landroidx/lifecycle/Q;",
        "savedStateHandle",
        "",
        "eventId",
        "LHX0/e;",
        "resourceManager",
        "LIu0/b;",
        "getStadiumsUseCase",
        "Lm8/a;",
        "coroutineDispatchers",
        "LSX0/a;",
        "lottieConfigurator",
        "LWo0/a;",
        "allEventGamesScreenFactory",
        "LwX0/c;",
        "router",
        "LHg/d;",
        "specialEventAnalytics",
        "LfS/a;",
        "specialEventFatmanLogger",
        "",
        "screenName",
        "<init>",
        "(Landroidx/lifecycle/Q;ILHX0/e;LIu0/b;Lm8/a;LSX0/a;LWo0/a;LwX0/c;LHg/d;LfS/a;Ljava/lang/String;)V",
        "Lsy0/a;",
        "venueUiModel",
        "",
        "z3",
        "(Lsy0/a;)V",
        "onBackPressed",
        "()V",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/special_event/impl/venues/presentation/g;",
        "v3",
        "()Lkotlinx/coroutines/flow/e;",
        "w3",
        "",
        "stadiumId",
        "y3",
        "(IJ)V",
        "v1",
        "Landroidx/lifecycle/Q;",
        "x1",
        "I",
        "y1",
        "LHX0/e;",
        "F1",
        "LIu0/b;",
        "H1",
        "Lm8/a;",
        "I1",
        "LSX0/a;",
        "P1",
        "LWo0/a;",
        "S1",
        "LwX0/c;",
        "V1",
        "LHg/d;",
        "b2",
        "LfS/a;",
        "v2",
        "Ljava/lang/String;",
        "Lkotlinx/coroutines/flow/V;",
        "Lorg/xbet/special_event/impl/venues/presentation/b;",
        "x2",
        "Lkotlinx/coroutines/flow/V;",
        "venuesContentStateModel",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final F1:LIu0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:LWo0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:LHg/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:LfS/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Landroidx/lifecycle/Q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:I

.field public final x2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/special_event/impl/venues/presentation/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroidx/lifecycle/Q;ILHX0/e;LIu0/b;Lm8/a;LSX0/a;LWo0/a;LwX0/c;LHg/d;LfS/a;Ljava/lang/String;)V
    .locals 0
    .param p1    # Landroidx/lifecycle/Q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LIu0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LWo0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LHg/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LfS/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->v1:Landroidx/lifecycle/Q;

    .line 5
    .line 6
    iput p2, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->x1:I

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->y1:LHX0/e;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->F1:LIu0/b;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->H1:Lm8/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->I1:LSX0/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->P1:LWo0/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->S1:LwX0/c;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->V1:LHg/d;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->b2:LfS/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->v2:Ljava/lang/String;

    .line 25
    .line 26
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    sget-object p3, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 31
    .line 32
    sget p4, Lpb/k;->data_retrieval_error:I

    .line 33
    .line 34
    sget p5, Lpb/k;->try_again_text:I

    .line 35
    .line 36
    move-object p2, p6

    .line 37
    new-instance p6, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel$venuesContentStateModel$1;

    .line 38
    .line 39
    invoke-direct {p6, p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel$venuesContentStateModel$1;-><init>(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    const/16 p9, 0x10

    .line 43
    .line 44
    const/4 p10, 0x0

    .line 45
    const-wide/16 p7, 0x0

    .line 46
    .line 47
    invoke-static/range {p2 .. p10}, LSX0/a$a;->a(LSX0/a;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;JILjava/lang/Object;)Lorg/xbet/uikit/components/lottie/a;

    .line 48
    .line 49
    .line 50
    move-result-object p2

    .line 51
    new-instance p3, Lorg/xbet/special_event/impl/venues/presentation/b;

    .line 52
    .line 53
    const/4 p4, 0x0

    .line 54
    const/4 p5, 0x1

    .line 55
    invoke-direct {p3, p1, p2, p4, p5}, Lorg/xbet/special_event/impl/venues/presentation/b;-><init>(Ljava/util/List;Lorg/xbet/uikit/components/lottie/a;ZZ)V

    .line 56
    .line 57
    .line 58
    invoke-static {p3}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    iput-object p1, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 63
    .line 64
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->w3()V

    .line 65
    .line 66
    .line 67
    return-void
.end method

.method public static synthetic p3(Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->x3(Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic q3(Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;)I
    .locals 0

    .line 1
    iget p0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->x1:I

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic r3(Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;)LIu0/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->F1:LIu0/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s3(Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->y1:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->w3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final x3(Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 7

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    :cond_0
    invoke-interface {p0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    move-object v0, p1

    .line 8
    check-cast v0, Lorg/xbet/special_event/impl/venues/presentation/b;

    .line 9
    .line 10
    const/4 v5, 0x3

    .line 11
    const/4 v6, 0x0

    .line 12
    const/4 v1, 0x0

    .line 13
    const/4 v2, 0x0

    .line 14
    const/4 v3, 0x1

    .line 15
    const/4 v4, 0x0

    .line 16
    invoke-static/range {v0 .. v6}, Lorg/xbet/special_event/impl/venues/presentation/b;->b(Lorg/xbet/special_event/impl/venues/presentation/b;Ljava/util/List;Lorg/xbet/uikit/components/lottie/a;ZZILjava/lang/Object;)Lorg/xbet/special_event/impl/venues/presentation/b;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    invoke-interface {p0, p1, v0}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 21
    .line 22
    .line 23
    move-result p1

    .line 24
    if-eqz p1, :cond_0

    .line 25
    .line 26
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 27
    .line 28
    return-object p0
.end method


# virtual methods
.method public final onBackPressed()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->S1:LwX0/c;

    .line 2
    .line 3
    invoke-virtual {v0}, LwX0/c;->h()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final v3()Lkotlinx/coroutines/flow/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/special_event/impl/venues/presentation/g;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel$getVenuesUiState$$inlined$map$1;

    .line 4
    .line 5
    invoke-direct {v1, v0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel$getVenuesUiState$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;)V

    .line 6
    .line 7
    .line 8
    return-object v1
.end method

.method public final w3()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    move-object v2, v1

    .line 8
    check-cast v2, Lorg/xbet/special_event/impl/venues/presentation/b;

    .line 9
    .line 10
    const/4 v7, 0x3

    .line 11
    const/4 v8, 0x0

    .line 12
    const/4 v3, 0x0

    .line 13
    const/4 v4, 0x0

    .line 14
    const/4 v5, 0x0

    .line 15
    const/4 v6, 0x1

    .line 16
    invoke-static/range {v2 .. v8}, Lorg/xbet/special_event/impl/venues/presentation/b;->b(Lorg/xbet/special_event/impl/venues/presentation/b;Ljava/util/List;Lorg/xbet/uikit/components/lottie/a;ZZILjava/lang/Object;)Lorg/xbet/special_event/impl/venues/presentation/b;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 21
    .line 22
    .line 23
    move-result v1

    .line 24
    if-eqz v1, :cond_0

    .line 25
    .line 26
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->H1:Lm8/a;

    .line 31
    .line 32
    invoke-interface {v0}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 33
    .line 34
    .line 35
    move-result-object v5

    .line 36
    new-instance v3, Lorg/xbet/special_event/impl/venues/presentation/i;

    .line 37
    .line 38
    invoke-direct {v3, p0}, Lorg/xbet/special_event/impl/venues/presentation/i;-><init>(Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;)V

    .line 39
    .line 40
    .line 41
    new-instance v7, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel$loadVenues$3;

    .line 42
    .line 43
    const/4 v0, 0x0

    .line 44
    invoke-direct {v7, p0, v0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel$loadVenues$3;-><init>(Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;Lkotlin/coroutines/e;)V

    .line 45
    .line 46
    .line 47
    const/16 v8, 0xa

    .line 48
    .line 49
    const/4 v9, 0x0

    .line 50
    const/4 v4, 0x0

    .line 51
    const/4 v6, 0x0

    .line 52
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 53
    .line 54
    .line 55
    return-void
.end method

.method public final y3(IJ)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->V1:LHg/d;

    .line 2
    .line 3
    const-string v1, "all_stadiums"

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2, p3, v1}, LHg/d;->E(IJLjava/lang/String;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->b2:LfS/a;

    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->v2:Ljava/lang/String;

    .line 11
    .line 12
    invoke-static {p2, p3}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object p2

    .line 16
    invoke-interface {v0, v2, p1, p2, v1}, LfS/a;->x(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public final z3(Lsy0/a;)V
    .locals 6
    .param p1    # Lsy0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->P1:LWo0/a;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams$StadiumGames;

    .line 4
    .line 5
    iget v2, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->x1:I

    .line 6
    .line 7
    invoke-virtual {p1}, Lsy0/a;->getId()J

    .line 8
    .line 9
    .line 10
    move-result-wide v3

    .line 11
    invoke-virtual {p1}, Lsy0/a;->getTitle()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v5

    .line 15
    invoke-direct {v1, v2, v3, v4, v5}, Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams$StadiumGames;-><init>(IJLjava/lang/String;)V

    .line 16
    .line 17
    .line 18
    invoke-interface {v0, v1}, LWo0/a;->a(Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams;)Lq4/q;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iget v1, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->x1:I

    .line 23
    .line 24
    invoke-virtual {p1}, Lsy0/a;->getId()J

    .line 25
    .line 26
    .line 27
    move-result-wide v2

    .line 28
    invoke-virtual {p0, v1, v2, v3}, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->y3(IJ)V

    .line 29
    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->S1:LwX0/c;

    .line 32
    .line 33
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 34
    .line 35
    .line 36
    return-void
.end method
