.class final Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.new_games.presentation.NewGamesFolderViewModel$handleContentErrors$1"
    f = "NewGamesFolderViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->c5()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;",
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\n\u00a2\u0006\u0004\u0008\u0005\u0010\u0006"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;",
        "games",
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;",
        "banners",
        "",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    const/4 p1, 0x3

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;

    check-cast p2, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;->invoke(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance p2, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    invoke-direct {p2, v0, p3}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, p2, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;->L$0:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p2, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_1

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$b;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 20
    .line 21
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->w4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 26
    .line 27
    invoke-static {v1}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    invoke-interface {v1}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    check-cast p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$b;

    .line 36
    .line 37
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$b;->a()Ljava/lang/Throwable;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    invoke-interface {v0, v1, p1}, Lkotlinx/coroutines/CoroutineExceptionHandler;->handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V

    .line 42
    .line 43
    .line 44
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 45
    .line 46
    return-object p1

    .line 47
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 48
    .line 49
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 50
    .line 51
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    throw p1
.end method
