.class public final LIa1/i$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LIa1/d;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LIa1/i;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LIa1/i$a$a;,
        LIa1/i$a$b;,
        LIa1/i$a$d;,
        LIa1/i$a$f;,
        LIa1/i$a$g;,
        LIa1/i$a$h;,
        LIa1/i$a$c;,
        LIa1/i$a$e;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lek/f;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/l;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LC81/f;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/publishers/AggregatorPublishersViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LIa1/i$a;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lya1/a;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/gifts/usecases/j;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Leu/l;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/j;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/m;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/publishers/usecases/GetPublishersPagesScenario;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lea1/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/C;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lo9/a;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lp9/c;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LP91/b;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lek/d;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LGg/a;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/s;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/scope/I;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/o;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LAR/a;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LZR/a;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lgk0/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LN91/e;Lak/a;Lz81/a;LwX0/C;Lea1/a;Lorg/xbet/ui_common/utils/M;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lo9/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;LxX0/a;LHX0/e;LAR/a;LZR/a;Leu/l;Li8/j;Li8/m;Lgk0/a;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LIa1/i$a;->a:LIa1/i$a;

    .line 4
    invoke-virtual/range {p0 .. p21}, LIa1/i$a;->b(LQW0/c;LN91/e;Lak/a;Lz81/a;LwX0/C;Lea1/a;Lorg/xbet/ui_common/utils/M;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lo9/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;LxX0/a;LHX0/e;LAR/a;LZR/a;Leu/l;Li8/j;Li8/m;Lgk0/a;)V

    .line 5
    invoke-virtual/range {p0 .. p21}, LIa1/i$a;->c(LQW0/c;LN91/e;Lak/a;Lz81/a;LwX0/C;Lea1/a;Lorg/xbet/ui_common/utils/M;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lo9/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;LxX0/a;LHX0/e;LAR/a;LZR/a;Leu/l;Li8/j;Li8/m;Lgk0/a;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LN91/e;Lak/a;Lz81/a;LwX0/C;Lea1/a;Lorg/xbet/ui_common/utils/M;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lo9/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;LxX0/a;LHX0/e;LAR/a;LZR/a;Leu/l;Li8/j;Li8/m;Lgk0/a;LIa1/j;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p21}, LIa1/i$a;-><init>(LQW0/c;LN91/e;Lak/a;Lz81/a;LwX0/C;Lea1/a;Lorg/xbet/ui_common/utils/M;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lo9/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;LxX0/a;LHX0/e;LAR/a;LZR/a;Leu/l;Li8/j;Li8/m;Lgk0/a;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xplatform/aggregator/impl/publishers/AggregatorPublishersFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LIa1/i$a;->d(Lorg/xplatform/aggregator/impl/publishers/AggregatorPublishersFragment;)Lorg/xplatform/aggregator/impl/publishers/AggregatorPublishersFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;LN91/e;Lak/a;Lz81/a;LwX0/C;Lea1/a;Lorg/xbet/ui_common/utils/M;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lo9/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;LxX0/a;LHX0/e;LAR/a;LZR/a;Leu/l;Li8/j;Li8/m;Lgk0/a;)V
    .locals 2

    .line 1
    new-instance p4, LIa1/i$a$a;

    .line 2
    .line 3
    invoke-direct {p4, p2}, LIa1/i$a$a;-><init>(LN91/e;)V

    .line 4
    .line 5
    .line 6
    iput-object p4, p0, LIa1/i$a;->b:Ldagger/internal/h;

    .line 7
    .line 8
    invoke-static {p4}, Lorg/xplatform/aggregator/impl/gifts/usecases/k;->a(LBc/a;)Lorg/xplatform/aggregator/impl/gifts/usecases/k;

    .line 9
    .line 10
    .line 11
    move-result-object p2

    .line 12
    iput-object p2, p0, LIa1/i$a;->c:Ldagger/internal/h;

    .line 13
    .line 14
    invoke-static/range {p18 .. p18}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 15
    .line 16
    .line 17
    move-result-object p2

    .line 18
    iput-object p2, p0, LIa1/i$a;->d:Ldagger/internal/h;

    .line 19
    .line 20
    invoke-static/range {p19 .. p19}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 21
    .line 22
    .line 23
    move-result-object p2

    .line 24
    iput-object p2, p0, LIa1/i$a;->e:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static/range {p20 .. p20}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 27
    .line 28
    .line 29
    move-result-object p2

    .line 30
    iput-object p2, p0, LIa1/i$a;->f:Ldagger/internal/h;

    .line 31
    .line 32
    iget-object p4, p0, LIa1/i$a;->c:Ldagger/internal/h;

    .line 33
    .line 34
    iget-object v0, p0, LIa1/i$a;->d:Ldagger/internal/h;

    .line 35
    .line 36
    iget-object v1, p0, LIa1/i$a;->e:Ldagger/internal/h;

    .line 37
    .line 38
    invoke-static {p4, v0, v1, p2}, Lorg/xplatform/aggregator/impl/publishers/usecases/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/publishers/usecases/a;

    .line 39
    .line 40
    .line 41
    move-result-object p2

    .line 42
    iput-object p2, p0, LIa1/i$a;->g:Ldagger/internal/h;

    .line 43
    .line 44
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 45
    .line 46
    .line 47
    move-result-object p2

    .line 48
    iput-object p2, p0, LIa1/i$a;->h:Ldagger/internal/h;

    .line 49
    .line 50
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 51
    .line 52
    .line 53
    move-result-object p2

    .line 54
    iput-object p2, p0, LIa1/i$a;->i:Ldagger/internal/h;

    .line 55
    .line 56
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 57
    .line 58
    .line 59
    move-result-object p2

    .line 60
    iput-object p2, p0, LIa1/i$a;->j:Ldagger/internal/h;

    .line 61
    .line 62
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 63
    .line 64
    .line 65
    move-result-object p2

    .line 66
    iput-object p2, p0, LIa1/i$a;->k:Ldagger/internal/h;

    .line 67
    .line 68
    new-instance p2, LIa1/i$a$b;

    .line 69
    .line 70
    invoke-direct {p2, p1}, LIa1/i$a$b;-><init>(LQW0/c;)V

    .line 71
    .line 72
    .line 73
    iput-object p2, p0, LIa1/i$a;->l:Ldagger/internal/h;

    .line 74
    .line 75
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    iput-object p1, p0, LIa1/i$a;->m:Ldagger/internal/h;

    .line 80
    .line 81
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    iput-object p1, p0, LIa1/i$a;->n:Ldagger/internal/h;

    .line 86
    .line 87
    invoke-static {p1}, Lp9/d;->a(LBc/a;)Lp9/d;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    iput-object p1, p0, LIa1/i$a;->o:Ldagger/internal/h;

    .line 92
    .line 93
    invoke-static/range {p15 .. p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    iput-object p1, p0, LIa1/i$a;->p:Ldagger/internal/h;

    .line 98
    .line 99
    invoke-static {p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 100
    .line 101
    .line 102
    move-result-object p1

    .line 103
    iput-object p1, p0, LIa1/i$a;->q:Ldagger/internal/h;

    .line 104
    .line 105
    new-instance p1, LIa1/i$a$d;

    .line 106
    .line 107
    invoke-direct {p1, p3}, LIa1/i$a$d;-><init>(Lak/a;)V

    .line 108
    .line 109
    .line 110
    iput-object p1, p0, LIa1/i$a;->r:Ldagger/internal/h;

    .line 111
    .line 112
    invoke-static {p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 113
    .line 114
    .line 115
    move-result-object p1

    .line 116
    iput-object p1, p0, LIa1/i$a;->s:Ldagger/internal/h;

    .line 117
    .line 118
    new-instance p1, LIa1/i$a$f;

    .line 119
    .line 120
    invoke-direct {p1, p3}, LIa1/i$a$f;-><init>(Lak/a;)V

    .line 121
    .line 122
    .line 123
    iput-object p1, p0, LIa1/i$a;->t:Ldagger/internal/h;

    .line 124
    .line 125
    invoke-static {p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 126
    .line 127
    .line 128
    move-result-object p1

    .line 129
    iput-object p1, p0, LIa1/i$a;->u:Ldagger/internal/h;

    .line 130
    .line 131
    invoke-static/range {p14 .. p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 132
    .line 133
    .line 134
    move-result-object p1

    .line 135
    iput-object p1, p0, LIa1/i$a;->v:Ldagger/internal/h;

    .line 136
    .line 137
    new-instance p1, LIa1/i$a$g;

    .line 138
    .line 139
    invoke-direct {p1, p3}, LIa1/i$a$g;-><init>(Lak/a;)V

    .line 140
    .line 141
    .line 142
    iput-object p1, p0, LIa1/i$a;->w:Ldagger/internal/h;

    .line 143
    .line 144
    invoke-static/range {p16 .. p16}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 145
    .line 146
    .line 147
    move-result-object p1

    .line 148
    iput-object p1, p0, LIa1/i$a;->x:Ldagger/internal/h;

    .line 149
    .line 150
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 151
    .line 152
    .line 153
    move-result-object p1

    .line 154
    iput-object p1, p0, LIa1/i$a;->y:Ldagger/internal/h;

    .line 155
    .line 156
    invoke-static/range {p21 .. p21}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 157
    .line 158
    .line 159
    move-result-object p1

    .line 160
    iput-object p1, p0, LIa1/i$a;->z:Ldagger/internal/h;

    .line 161
    .line 162
    return-void
.end method

.method public final c(LQW0/c;LN91/e;Lak/a;Lz81/a;LwX0/C;Lea1/a;Lorg/xbet/ui_common/utils/M;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lo9/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;LxX0/a;LHX0/e;LAR/a;LZR/a;Leu/l;Li8/j;Li8/m;Lgk0/a;)V
    .locals 25

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p3

    .line 4
    .line 5
    new-instance v2, LIa1/i$a$h;

    .line 6
    .line 7
    invoke-direct {v2, v1}, LIa1/i$a$h;-><init>(Lak/a;)V

    .line 8
    .line 9
    .line 10
    iput-object v2, v0, LIa1/i$a;->A:Ldagger/internal/h;

    .line 11
    .line 12
    new-instance v2, LIa1/i$a$c;

    .line 13
    .line 14
    invoke-direct {v2, v1}, LIa1/i$a$c;-><init>(Lak/a;)V

    .line 15
    .line 16
    .line 17
    iput-object v2, v0, LIa1/i$a;->B:Ldagger/internal/h;

    .line 18
    .line 19
    new-instance v1, LIa1/i$a$e;

    .line 20
    .line 21
    move-object/from16 v2, p4

    .line 22
    .line 23
    invoke-direct {v1, v2}, LIa1/i$a$e;-><init>(Lz81/a;)V

    .line 24
    .line 25
    .line 26
    iput-object v1, v0, LIa1/i$a;->C:Ldagger/internal/h;

    .line 27
    .line 28
    iget-object v3, v0, LIa1/i$a;->g:Ldagger/internal/h;

    .line 29
    .line 30
    iget-object v4, v0, LIa1/i$a;->h:Ldagger/internal/h;

    .line 31
    .line 32
    iget-object v5, v0, LIa1/i$a;->i:Ldagger/internal/h;

    .line 33
    .line 34
    iget-object v6, v0, LIa1/i$a;->j:Ldagger/internal/h;

    .line 35
    .line 36
    iget-object v7, v0, LIa1/i$a;->k:Ldagger/internal/h;

    .line 37
    .line 38
    iget-object v8, v0, LIa1/i$a;->l:Ldagger/internal/h;

    .line 39
    .line 40
    iget-object v9, v0, LIa1/i$a;->m:Ldagger/internal/h;

    .line 41
    .line 42
    iget-object v10, v0, LIa1/i$a;->o:Ldagger/internal/h;

    .line 43
    .line 44
    iget-object v11, v0, LIa1/i$a;->p:Ldagger/internal/h;

    .line 45
    .line 46
    iget-object v12, v0, LIa1/i$a;->q:Ldagger/internal/h;

    .line 47
    .line 48
    iget-object v13, v0, LIa1/i$a;->r:Ldagger/internal/h;

    .line 49
    .line 50
    iget-object v14, v0, LIa1/i$a;->s:Ldagger/internal/h;

    .line 51
    .line 52
    iget-object v15, v0, LIa1/i$a;->t:Ldagger/internal/h;

    .line 53
    .line 54
    iget-object v2, v0, LIa1/i$a;->u:Ldagger/internal/h;

    .line 55
    .line 56
    move-object/from16 v24, v1

    .line 57
    .line 58
    iget-object v1, v0, LIa1/i$a;->v:Ldagger/internal/h;

    .line 59
    .line 60
    move-object/from16 v17, v1

    .line 61
    .line 62
    iget-object v1, v0, LIa1/i$a;->w:Ldagger/internal/h;

    .line 63
    .line 64
    move-object/from16 v18, v1

    .line 65
    .line 66
    iget-object v1, v0, LIa1/i$a;->x:Ldagger/internal/h;

    .line 67
    .line 68
    move-object/from16 v19, v1

    .line 69
    .line 70
    iget-object v1, v0, LIa1/i$a;->y:Ldagger/internal/h;

    .line 71
    .line 72
    move-object/from16 v20, v1

    .line 73
    .line 74
    iget-object v1, v0, LIa1/i$a;->z:Ldagger/internal/h;

    .line 75
    .line 76
    move-object/from16 v21, v1

    .line 77
    .line 78
    iget-object v1, v0, LIa1/i$a;->A:Ldagger/internal/h;

    .line 79
    .line 80
    move-object/from16 v22, v1

    .line 81
    .line 82
    iget-object v1, v0, LIa1/i$a;->B:Ldagger/internal/h;

    .line 83
    .line 84
    move-object/from16 v23, v1

    .line 85
    .line 86
    move-object/from16 v16, v2

    .line 87
    .line 88
    invoke-static/range {v3 .. v24}, Lorg/xplatform/aggregator/impl/publishers/g;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/publishers/g;

    .line 89
    .line 90
    .line 91
    move-result-object v1

    .line 92
    iput-object v1, v0, LIa1/i$a;->D:Ldagger/internal/h;

    .line 93
    .line 94
    return-void
.end method

.method public final d(Lorg/xplatform/aggregator/impl/publishers/AggregatorPublishersFragment;)Lorg/xplatform/aggregator/impl/publishers/AggregatorPublishersFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LIa1/i$a;->f()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/publishers/e;->a(Lorg/xplatform/aggregator/impl/publishers/AggregatorPublishersFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final e()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xplatform/aggregator/impl/publishers/AggregatorPublishersViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LIa1/i$a;->D:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final f()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LIa1/i$a;->e()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
