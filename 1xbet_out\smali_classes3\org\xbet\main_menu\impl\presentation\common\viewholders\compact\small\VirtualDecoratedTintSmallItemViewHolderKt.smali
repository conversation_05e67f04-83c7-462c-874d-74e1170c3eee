.class public final Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/small/VirtualDecoratedTintSmallItemViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a/\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lkotlin/Function1;",
        "LN80/c;",
        "",
        "onItemClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "(Lkotlin/jvm/functions/Function1;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/small/VirtualDecoratedTintSmallItemViewHolderKt;->i(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/Q;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/small/VirtualDecoratedTintSmallItemViewHolderKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/Q;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/small/VirtualDecoratedTintSmallItemViewHolderKt;->h(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/small/VirtualDecoratedTintSmallItemViewHolderKt;->g(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lkotlin/jvm/functions/Function1;)LA4/c;
    .locals 4
    .param p0    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LN80/c;",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LR80/C;

    .line 2
    .line 3
    invoke-direct {v0}, LR80/C;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LR80/D;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LR80/D;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/small/VirtualDecoratedTintSmallItemViewHolderKt$getVirtualDecoratedTintSmallItemDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/small/VirtualDecoratedTintSmallItemViewHolderKt$getVirtualDecoratedTintSmallItemDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/small/VirtualDecoratedTintSmallItemViewHolderKt$getVirtualDecoratedTintSmallItemDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/small/VirtualDecoratedTintSmallItemViewHolderKt$getVirtualDecoratedTintSmallItemDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/Q;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, Lv80/Q;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)Lv80/Q;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lv80/Q;

    .line 6
    .line 7
    invoke-virtual {v0}, Lv80/Q;->b()Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    new-instance v1, LR80/E;

    .line 12
    .line 13
    invoke-direct {v1, p0, p1}, LR80/E;-><init>(Lkotlin/jvm/functions/Function1;LB4/a;)V

    .line 14
    .line 15
    .line 16
    const/4 p0, 0x1

    .line 17
    const/4 v2, 0x0

    .line 18
    invoke-static {v0, v2, v1, p0, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 19
    .line 20
    .line 21
    new-instance p0, LR80/F;

    .line 22
    .line 23
    invoke-direct {p0, p1}, LR80/F;-><init>(LB4/a;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 27
    .line 28
    .line 29
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 30
    .line 31
    return-object p0
.end method

.method public static final h(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final i(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 4

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lv80/Q;

    .line 6
    .line 7
    iget-object p1, p1, Lv80/Q;->b:Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, LN80/c$g;

    .line 14
    .line 15
    invoke-virtual {v0}, LN80/c$g;->getIcon()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;->setIconResource(I)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    check-cast p1, Lv80/Q;

    .line 27
    .line 28
    iget-object p1, p1, Lv80/Q;->b:Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    .line 29
    .line 30
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    check-cast v1, LN80/c$g;

    .line 39
    .line 40
    invoke-virtual {v1}, LN80/c$g;->o()I

    .line 41
    .line 42
    .line 43
    move-result v1

    .line 44
    const/4 v2, 0x0

    .line 45
    const/4 v3, 0x2

    .line 46
    invoke-static {v0, v1, v2, v3, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 47
    .line 48
    .line 49
    move-result v0

    .line 50
    invoke-static {v0}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;->setIconTint(Landroid/content/res/ColorStateList;)V

    .line 55
    .line 56
    .line 57
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    check-cast p1, Lv80/Q;

    .line 62
    .line 63
    iget-object p1, p1, Lv80/Q;->b:Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    .line 64
    .line 65
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    check-cast v1, LN80/c$g;

    .line 74
    .line 75
    invoke-virtual {v1}, LN80/c$g;->f()I

    .line 76
    .line 77
    .line 78
    move-result v1

    .line 79
    invoke-static {v0, v1, v2, v3, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 80
    .line 81
    .line 82
    move-result v0

    .line 83
    invoke-static {v0}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 84
    .line 85
    .line 86
    move-result-object v0

    .line 87
    invoke-static {p1, v0}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 88
    .line 89
    .line 90
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    check-cast p1, Lv80/Q;

    .line 95
    .line 96
    iget-object p1, p1, Lv80/Q;->b:Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    .line 97
    .line 98
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 99
    .line 100
    .line 101
    move-result-object v0

    .line 102
    check-cast v0, LN80/c$g;

    .line 103
    .line 104
    invoke-virtual {v0}, LN80/c$g;->getTitle()Ljava/lang/String;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;->setTitle(Ljava/lang/String;)V

    .line 109
    .line 110
    .line 111
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 112
    .line 113
    .line 114
    move-result-object p1

    .line 115
    check-cast p1, Lv80/Q;

    .line 116
    .line 117
    iget-object p1, p1, Lv80/Q;->b:Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    .line 118
    .line 119
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 120
    .line 121
    .line 122
    move-result-object v0

    .line 123
    check-cast v0, LN80/c$g;

    .line 124
    .line 125
    invoke-virtual {v0}, LN80/c$g;->A()Ljava/lang/String;

    .line 126
    .line 127
    .line 128
    move-result-object v0

    .line 129
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 130
    .line 131
    .line 132
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 133
    .line 134
    .line 135
    move-result-object p1

    .line 136
    check-cast p1, Lv80/Q;

    .line 137
    .line 138
    iget-object p1, p1, Lv80/Q;->b:Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    .line 139
    .line 140
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 141
    .line 142
    .line 143
    move-result-object p0

    .line 144
    check-cast p0, LN80/c$g;

    .line 145
    .line 146
    invoke-virtual {p0}, LN80/c$g;->u()Z

    .line 147
    .line 148
    .line 149
    move-result p0

    .line 150
    invoke-virtual {p1, p0}, Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;->setBadgeVisible(Z)V

    .line 151
    .line 152
    .line 153
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 154
    .line 155
    return-object p0
.end method
