.class final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.my_aggregator.presentation.viewmodels.MyAggregatorViewModel$cashbackClicked$1"
    f = "MyAggregatorViewModel.kt"
    l = {
        0x269
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->O5(Ljava/lang/String;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $fromDeepLink:Z

.field final synthetic $screenName:Ljava/lang/String;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;ZLjava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;",
            "Z",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    iput-boolean p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->$fromDeepLink:Z

    iput-object p3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->$screenName:Ljava/lang/String;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    iget-boolean v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->$fromDeepLink:Z

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->$screenName:Ljava/lang/String;

    invoke-direct {p1, v0, v1, v2, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;ZLjava/lang/String;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 14

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->R4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    iput v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->label:I

    .line 34
    .line 35
    const/4 v1, 0x0

    .line 36
    invoke-virtual {p1, v1, p0}, Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;->c(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    if-ne p1, v0, :cond_2

    .line 41
    .line 42
    return-object v0

    .line 43
    :cond_2
    :goto_0
    check-cast p1, Le9/a;

    .line 44
    .line 45
    invoke-virtual {p1}, Le9/a;->e()Z

    .line 46
    .line 47
    .line 48
    move-result p1

    .line 49
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 50
    .line 51
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->h5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lek0/o;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    invoke-virtual {v0}, Lek0/o;->e2()Lek0/m;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    invoke-virtual {v0}, Lek0/m;->A()Z

    .line 60
    .line 61
    .line 62
    move-result v0

    .line 63
    if-eqz p1, :cond_4

    .line 64
    .line 65
    if-eqz v0, :cond_4

    .line 66
    .line 67
    iget-boolean p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->$fromDeepLink:Z

    .line 68
    .line 69
    if-nez p1, :cond_3

    .line 70
    .line 71
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 72
    .line 73
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->d5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)LOR/a;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->$screenName:Ljava/lang/String;

    .line 78
    .line 79
    invoke-interface {p1, v0}, LOR/a;->b(Ljava/lang/String;)V

    .line 80
    .line 81
    .line 82
    :cond_3
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 83
    .line 84
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->c5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lorg/xbet/analytics/domain/scope/g0;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    invoke-virtual {p1}, Lorg/xbet/analytics/domain/scope/g0;->L()V

    .line 89
    .line 90
    .line 91
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 92
    .line 93
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->s4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)LP91/b;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    new-instance v0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 98
    .line 99
    sget-object v5, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$VipCashBackScreen;->INSTANCE:Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$VipCashBackScreen;

    .line 100
    .line 101
    const/16 v12, 0xf7

    .line 102
    .line 103
    const/4 v13, 0x0

    .line 104
    const/4 v1, 0x0

    .line 105
    const/4 v2, 0x0

    .line 106
    const-wide/16 v3, 0x0

    .line 107
    .line 108
    const/4 v6, 0x0

    .line 109
    const-wide/16 v7, 0x0

    .line 110
    .line 111
    const-wide/16 v9, 0x0

    .line 112
    .line 113
    const/4 v11, 0x0

    .line 114
    invoke-direct/range {v0 .. v13}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 115
    .line 116
    .line 117
    invoke-virtual {p1, v0}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 118
    .line 119
    .line 120
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 121
    .line 122
    return-object p1
.end method
