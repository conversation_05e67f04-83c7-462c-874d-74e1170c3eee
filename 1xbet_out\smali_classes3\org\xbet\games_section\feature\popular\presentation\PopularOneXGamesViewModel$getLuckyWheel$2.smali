.class final Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.popular.presentation.PopularOneXGamesViewModel$getLuckyWheel$2"
    f = "PopularOneXGamesViewModel.kt"
    l = {
        0x246,
        0x247
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;

    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    goto :goto_2

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    goto :goto_0

    .line 31
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 35
    .line 36
    iput v3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;->label:I

    .line 37
    .line 38
    invoke-static {p1, p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->f4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    if-ne p1, v0, :cond_3

    .line 43
    .line 44
    goto :goto_1

    .line 45
    :cond_3
    :goto_0
    check-cast p1, Ljava/lang/Boolean;

    .line 46
    .line 47
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 48
    .line 49
    .line 50
    move-result p1

    .line 51
    if-eqz p1, :cond_5

    .line 52
    .line 53
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 54
    .line 55
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->Z3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lorg/xbet/games_section/feature/popular/domain/usecases/PopularOneXGamesLuckyWheelUseCase;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    iput v2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;->label:I

    .line 60
    .line 61
    invoke-virtual {p1, p0}, Lorg/xbet/games_section/feature/popular/domain/usecases/PopularOneXGamesLuckyWheelUseCase;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    if-ne p1, v0, :cond_4

    .line 66
    .line 67
    :goto_1
    return-object v0

    .line 68
    :cond_4
    :goto_2
    check-cast p1, LW40/a;

    .line 69
    .line 70
    new-instance v0, Lb50/f;

    .line 71
    .line 72
    invoke-virtual {p1}, LW40/a;->b()Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object v2

    .line 76
    invoke-virtual {p1}, LW40/a;->a()Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object v3

    .line 80
    sget v4, LT40/a;->lucky_wheel_popular_banner:I

    .line 81
    .line 82
    invoke-virtual {p1}, LW40/a;->c()Z

    .line 83
    .line 84
    .line 85
    move-result v5

    .line 86
    const/4 v1, 0x1

    .line 87
    invoke-direct/range {v0 .. v5}, Lb50/f;-><init>(ILjava/lang/String;Ljava/lang/String;IZ)V

    .line 88
    .line 89
    .line 90
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 91
    .line 92
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->X3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 97
    .line 98
    new-instance v2, Lb50/d$a;

    .line 99
    .line 100
    invoke-direct {v2, v0}, Lb50/d$a;-><init>(Lb50/f;)V

    .line 101
    .line 102
    .line 103
    invoke-direct {v1, v2}, Lorg/xbet/games_section/feature/popular/presentation/a$d;-><init>(Ljava/lang/Object;)V

    .line 104
    .line 105
    .line 106
    invoke-interface {p1, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 107
    .line 108
    .line 109
    goto :goto_3

    .line 110
    :cond_5
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 111
    .line 112
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->X3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 113
    .line 114
    .line 115
    move-result-object p1

    .line 116
    sget-object v0, Lorg/xbet/games_section/feature/popular/presentation/a$b;->a:Lorg/xbet/games_section/feature/popular/presentation/a$b;

    .line 117
    .line 118
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 119
    .line 120
    .line 121
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 122
    .line 123
    return-object p1
.end method
