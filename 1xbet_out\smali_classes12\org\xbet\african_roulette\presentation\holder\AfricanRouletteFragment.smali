.class public final Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;
.super Lorg/xbet/core/presentation/holder/OnexGamesHolderFragment;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u0000  2\u00020\u0001:\u0001!B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u000f\u0010\u0007\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0017\u0010\u000b\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\u000cR\"\u0010\u0014\u001a\u00020\r8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u000e\u0010\u000f\u001a\u0004\u0008\u0010\u0010\u0011\"\u0004\u0008\u0012\u0010\u0013R\u001b\u0010\u001a\u001a\u00020\u00158VX\u0096\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0016\u0010\u0017\u001a\u0004\u0008\u0018\u0010\u0019R\u001b\u0010\u001f\u001a\u00020\u001b8@X\u0080\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010\u0017\u001a\u0004\u0008\u001d\u0010\u001e\u00a8\u0006\""
    }
    d2 = {
        "Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;",
        "Lorg/xbet/core/presentation/holder/OnexGamesHolderFragment;",
        "<init>",
        "()V",
        "",
        "u2",
        "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;",
        "j4",
        "()Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "image",
        "l3",
        "(Landroidx/appcompat/widget/AppCompatImageView;)V",
        "LQv/a$s;",
        "o0",
        "LQv/a$s;",
        "k4",
        "()LQv/a$s;",
        "setViewModelFactory",
        "(LQv/a$s;)V",
        "viewModelFactory",
        "Lorg/xbet/core/presentation/holder/OnexGamesHolderViewModel;",
        "b1",
        "Lkotlin/j;",
        "m3",
        "()Lorg/xbet/core/presentation/holder/OnexGamesHolderViewModel;",
        "viewModel",
        "Lhg/a;",
        "k1",
        "i4",
        "()Lhg/a;",
        "africanRouletteComponent",
        "v1",
        "a",
        "african_roulette_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final v1:Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final b1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public o0:LQv/a$s;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;->v1:Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment$a;

    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    invoke-direct {p0}, Lorg/xbet/core/presentation/holder/OnexGamesHolderFragment;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lorg/xbet/african_roulette/presentation/holder/a;

    .line 5
    .line 6
    invoke-direct {v0, p0}, Lorg/xbet/african_roulette/presentation/holder/a;-><init>(Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;)V

    .line 7
    .line 8
    .line 9
    new-instance v1, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment$special$$inlined$viewModels$default$1;

    .line 10
    .line 11
    invoke-direct {v1, p0}, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 12
    .line 13
    .line 14
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 15
    .line 16
    new-instance v3, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment$special$$inlined$viewModels$default$2;

    .line 17
    .line 18
    invoke-direct {v3, v1}, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 19
    .line 20
    .line 21
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    const-class v2, Lorg/xbet/core/presentation/holder/OnexGamesHolderViewModel;

    .line 26
    .line 27
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 28
    .line 29
    .line 30
    move-result-object v2

    .line 31
    new-instance v3, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment$special$$inlined$viewModels$default$3;

    .line 32
    .line 33
    invoke-direct {v3, v1}, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 34
    .line 35
    .line 36
    new-instance v4, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment$special$$inlined$viewModels$default$4;

    .line 37
    .line 38
    const/4 v5, 0x0

    .line 39
    invoke-direct {v4, v5, v1}, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 40
    .line 41
    .line 42
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    iput-object v0, p0, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;->b1:Lkotlin/j;

    .line 47
    .line 48
    new-instance v0, Lorg/xbet/african_roulette/presentation/holder/b;

    .line 49
    .line 50
    invoke-direct {v0, p0}, Lorg/xbet/african_roulette/presentation/holder/b;-><init>(Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;)V

    .line 51
    .line 52
    .line 53
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    iput-object v0, p0, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;->k1:Lkotlin/j;

    .line 58
    .line 59
    return-void
.end method

.method public static synthetic f4(Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;->l4(Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g4(Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;)Lhg/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;->h4(Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;)Lhg/a;

    move-result-object p0

    return-object p0
.end method

.method public static final h4(Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;)Lhg/a;
    .locals 4

    .line 1
    invoke-static {}, Lhg/h;->a()Lhg/a$b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v1}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    instance-of v2, v1, LQW0/f;

    .line 14
    .line 15
    const-string v3, "Can not find dependencies provider for "

    .line 16
    .line 17
    if-eqz v2, :cond_2

    .line 18
    .line 19
    check-cast v1, LQW0/f;

    .line 20
    .line 21
    invoke-interface {v1}, LQW0/f;->a()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    instance-of v2, v2, LQv/v;

    .line 26
    .line 27
    if-eqz v2, :cond_1

    .line 28
    .line 29
    invoke-interface {v1}, LQW0/f;->a()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    if-eqz p0, :cond_0

    .line 34
    .line 35
    check-cast p0, LQv/v;

    .line 36
    .line 37
    new-instance v1, Lhg/c;

    .line 38
    .line 39
    invoke-direct {v1}, Lhg/c;-><init>()V

    .line 40
    .line 41
    .line 42
    invoke-interface {v0, p0, v1}, Lhg/a$b;->a(LQv/v;Lhg/c;)Lhg/a;

    .line 43
    .line 44
    .line 45
    move-result-object p0

    .line 46
    return-object p0

    .line 47
    :cond_0
    new-instance p0, Ljava/lang/NullPointerException;

    .line 48
    .line 49
    const-string v0, "null cannot be cast to non-null type org.xbet.core.di.GamesCoreDependencies"

    .line 50
    .line 51
    invoke-direct {p0, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    throw p0

    .line 55
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 56
    .line 57
    new-instance v1, Ljava/lang/StringBuilder;

    .line 58
    .line 59
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 60
    .line 61
    .line 62
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 63
    .line 64
    .line 65
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 66
    .line 67
    .line 68
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 69
    .line 70
    .line 71
    move-result-object p0

    .line 72
    invoke-direct {v0, p0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 73
    .line 74
    .line 75
    throw v0

    .line 76
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 77
    .line 78
    new-instance v1, Ljava/lang/StringBuilder;

    .line 79
    .line 80
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 81
    .line 82
    .line 83
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 84
    .line 85
    .line 86
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 87
    .line 88
    .line 89
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 90
    .line 91
    .line 92
    move-result-object p0

    .line 93
    invoke-direct {v0, p0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 94
    .line 95
    .line 96
    throw v0
.end method

.method public static final l4(Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;)Landroidx/lifecycle/e0$c;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/a;

    .line 2
    .line 3
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;->k4()LQv/a$s;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, v1, p0}, Lorg/xbet/ui_common/viewmodel/core/a;-><init>(LwX0/c;LQW0/i;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method


# virtual methods
.method public final i4()Lhg/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;->k1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lhg/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public bridge synthetic j3()Landroidx/fragment/app/Fragment;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;->j4()Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public j4()Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->k1:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$a;->a()Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final k4()LQv/a$s;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;->o0:LQv/a$s;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public l3(Landroidx/appcompat/widget/AppCompatImageView;)V
    .locals 12
    .param p1    # Landroidx/appcompat/widget/AppCompatImageView;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/core/presentation/holder/OnexGamesHolderFragment;->g3()Landroid/widget/ImageView;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    const/4 p1, 0x0

    .line 8
    new-array v6, p1, [LYW0/d;

    .line 9
    .line 10
    const/16 v10, 0xee

    .line 11
    .line 12
    const/4 v11, 0x0

    .line 13
    const-string v2, "/static/img/android/games/background/africanroulete/new_back.webp"

    .line 14
    .line 15
    const/4 v3, 0x0

    .line 16
    const/4 v4, 0x0

    .line 17
    const/4 v5, 0x0

    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v8, 0x0

    .line 20
    const/4 v9, 0x0

    .line 21
    invoke-static/range {v0 .. v11}, LCX0/l;->w(LCX0/l;Landroid/widget/ImageView;Ljava/lang/String;IIZ[LYW0/d;LYW0/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public m3()Lorg/xbet/core/presentation/holder/OnexGamesHolderViewModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;->b1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/core/presentation/holder/OnexGamesHolderViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public u2()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;->i4()Lhg/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0, p0}, Lhg/a;->c(Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/holder/AfricanRouletteFragment;->i4()Lhg/a;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-interface {v0}, Lhg/a;->a()LQv/a$a;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-interface {v0}, LQv/a$a;->a()LQv/a;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    invoke-virtual {p0, v0}, Lorg/xbet/core/presentation/holder/OnexGamesHolderFragment;->M3(LQv/a;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method
