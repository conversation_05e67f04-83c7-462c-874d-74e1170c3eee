.class public Lk2/i;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static a(Lk2/k;J)I
    .locals 4

    .line 1
    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    .line 2
    .line 3
    .line 4
    .line 5
    .line 6
    cmp-long v2, p1, v0

    .line 7
    .line 8
    if-nez v2, :cond_0

    .line 9
    .line 10
    const/4 p0, 0x0

    .line 11
    return p0

    .line 12
    :cond_0
    invoke-interface {p0, p1, p2}, Lk2/k;->c(J)I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    const/4 v1, -0x1

    .line 17
    if-ne v0, v1, :cond_1

    .line 18
    .line 19
    invoke-interface {p0}, Lk2/k;->b()I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    :cond_1
    if-lez v0, :cond_2

    .line 24
    .line 25
    add-int/lit8 v2, v0, -0x1

    .line 26
    .line 27
    invoke-interface {p0, v2}, Lk2/k;->a(I)J

    .line 28
    .line 29
    .line 30
    move-result-wide v2

    .line 31
    cmp-long p0, v2, p1

    .line 32
    .line 33
    if-nez p0, :cond_2

    .line 34
    .line 35
    add-int/2addr v0, v1

    .line 36
    :cond_2
    return v0
.end method

.method public static b(Lk2/k;ILt1/l;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lk2/k;",
            "I",
            "Lt1/l<",
            "Lk2/e;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p0, p1}, Lk2/k;->a(I)J

    .line 2
    .line 3
    .line 4
    move-result-wide v2

    .line 5
    invoke-interface {p0, v2, v3}, Lk2/k;->g(J)Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-eqz v0, :cond_0

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    invoke-interface {p0}, Lk2/k;->b()I

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    add-int/lit8 v0, v0, -0x1

    .line 21
    .line 22
    if-eq p1, v0, :cond_2

    .line 23
    .line 24
    add-int/lit8 v0, p1, 0x1

    .line 25
    .line 26
    invoke-interface {p0, v0}, Lk2/k;->a(I)J

    .line 27
    .line 28
    .line 29
    move-result-wide v4

    .line 30
    invoke-interface {p0, p1}, Lk2/k;->a(I)J

    .line 31
    .line 32
    .line 33
    move-result-wide p0

    .line 34
    sub-long/2addr v4, p0

    .line 35
    const-wide/16 p0, 0x0

    .line 36
    .line 37
    cmp-long v0, v4, p0

    .line 38
    .line 39
    if-lez v0, :cond_1

    .line 40
    .line 41
    new-instance v0, Lk2/e;

    .line 42
    .line 43
    invoke-direct/range {v0 .. v5}, Lk2/e;-><init>(Ljava/util/List;JJ)V

    .line 44
    .line 45
    .line 46
    invoke-interface {p2, v0}, Lt1/l;->accept(Ljava/lang/Object;)V

    .line 47
    .line 48
    .line 49
    :cond_1
    :goto_0
    return-void

    .line 50
    :cond_2
    new-instance p0, Ljava/lang/IllegalStateException;

    .line 51
    .line 52
    invoke-direct {p0}, Ljava/lang/IllegalStateException;-><init>()V

    .line 53
    .line 54
    .line 55
    throw p0
.end method

.method public static c(Lk2/k;Lk2/s$b;Lt1/l;)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lk2/k;",
            "Lk2/s$b;",
            "Lt1/l<",
            "Lk2/e;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-wide v0, p1, Lk2/s$b;->a:J

    .line 2
    .line 3
    invoke-static {p0, v0, v1}, Lk2/i;->a(Lk2/k;J)I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget-wide v1, p1, Lk2/s$b;->a:J

    .line 8
    .line 9
    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    .line 10
    .line 11
    .line 12
    .line 13
    .line 14
    const/4 v5, 0x0

    .line 15
    cmp-long v6, v1, v3

    .line 16
    .line 17
    if-eqz v6, :cond_0

    .line 18
    .line 19
    invoke-interface {p0}, Lk2/k;->b()I

    .line 20
    .line 21
    .line 22
    move-result v1

    .line 23
    if-ge v0, v1, :cond_0

    .line 24
    .line 25
    iget-wide v1, p1, Lk2/s$b;->a:J

    .line 26
    .line 27
    invoke-interface {p0, v1, v2}, Lk2/k;->g(J)Ljava/util/List;

    .line 28
    .line 29
    .line 30
    move-result-object v7

    .line 31
    invoke-interface {p0, v0}, Lk2/k;->a(I)J

    .line 32
    .line 33
    .line 34
    move-result-wide v1

    .line 35
    invoke-interface {v7}, Ljava/util/List;->isEmpty()Z

    .line 36
    .line 37
    .line 38
    move-result v3

    .line 39
    if-nez v3, :cond_0

    .line 40
    .line 41
    iget-wide v8, p1, Lk2/s$b;->a:J

    .line 42
    .line 43
    cmp-long v3, v8, v1

    .line 44
    .line 45
    if-gez v3, :cond_0

    .line 46
    .line 47
    new-instance v6, Lk2/e;

    .line 48
    .line 49
    sub-long v10, v1, v8

    .line 50
    .line 51
    invoke-direct/range {v6 .. v11}, Lk2/e;-><init>(Ljava/util/List;JJ)V

    .line 52
    .line 53
    .line 54
    invoke-interface {p2, v6}, Lt1/l;->accept(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    const/4 v1, 0x1

    .line 58
    goto :goto_0

    .line 59
    :cond_0
    const/4 v1, 0x0

    .line 60
    :goto_0
    move v2, v0

    .line 61
    :goto_1
    invoke-interface {p0}, Lk2/k;->b()I

    .line 62
    .line 63
    .line 64
    move-result v3

    .line 65
    if-ge v2, v3, :cond_1

    .line 66
    .line 67
    invoke-static {p0, v2, p2}, Lk2/i;->b(Lk2/k;ILt1/l;)V

    .line 68
    .line 69
    .line 70
    add-int/lit8 v2, v2, 0x1

    .line 71
    .line 72
    goto :goto_1

    .line 73
    :cond_1
    iget-boolean v2, p1, Lk2/s$b;->b:Z

    .line 74
    .line 75
    if-eqz v2, :cond_4

    .line 76
    .line 77
    if-eqz v1, :cond_2

    .line 78
    .line 79
    add-int/lit8 v0, v0, -0x1

    .line 80
    .line 81
    :cond_2
    :goto_2
    if-ge v5, v0, :cond_3

    .line 82
    .line 83
    invoke-static {p0, v5, p2}, Lk2/i;->b(Lk2/k;ILt1/l;)V

    .line 84
    .line 85
    .line 86
    add-int/lit8 v5, v5, 0x1

    .line 87
    .line 88
    goto :goto_2

    .line 89
    :cond_3
    if-eqz v1, :cond_4

    .line 90
    .line 91
    new-instance v6, Lk2/e;

    .line 92
    .line 93
    iget-wide v1, p1, Lk2/s$b;->a:J

    .line 94
    .line 95
    invoke-interface {p0, v1, v2}, Lk2/k;->g(J)Ljava/util/List;

    .line 96
    .line 97
    .line 98
    move-result-object v7

    .line 99
    invoke-interface {p0, v0}, Lk2/k;->a(I)J

    .line 100
    .line 101
    .line 102
    move-result-wide v8

    .line 103
    iget-wide v1, p1, Lk2/s$b;->a:J

    .line 104
    .line 105
    invoke-interface {p0, v0}, Lk2/k;->a(I)J

    .line 106
    .line 107
    .line 108
    move-result-wide p0

    .line 109
    sub-long v10, v1, p0

    .line 110
    .line 111
    invoke-direct/range {v6 .. v11}, Lk2/e;-><init>(Ljava/util/List;JJ)V

    .line 112
    .line 113
    .line 114
    invoke-interface {p2, v6}, Lt1/l;->accept(Ljava/lang/Object;)V

    .line 115
    .line 116
    .line 117
    :cond_4
    return-void
.end method
