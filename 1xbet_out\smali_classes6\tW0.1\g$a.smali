.class public final LtW0/g$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LtW0/u$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LtW0/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LtW0/h;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LtW0/g$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lak/b;Lak/a;LTZ0/a;LwW0/g;LxW0/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/c;Lorg/xbet/toto_jackpot/impl/domain/scenario/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;LxX0/a;Lorg/xbet/ui_common/utils/M;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;LzX0/k;)LtW0/u;
    .locals 14

    .line 1
    invoke-static {p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static/range {p2 .. p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    invoke-static/range {p10 .. p10}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    invoke-static/range {p12 .. p12}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    new-instance v0, LtW0/g$b;

    .line 38
    .line 39
    const/4 v13, 0x0

    .line 40
    move-object v1, p1

    .line 41
    move-object/from16 v2, p2

    .line 42
    .line 43
    move-object/from16 v3, p3

    .line 44
    .line 45
    move-object/from16 v4, p4

    .line 46
    .line 47
    move-object/from16 v5, p5

    .line 48
    .line 49
    move-object/from16 v6, p6

    .line 50
    .line 51
    move-object/from16 v7, p7

    .line 52
    .line 53
    move-object/from16 v8, p8

    .line 54
    .line 55
    move-object/from16 v9, p9

    .line 56
    .line 57
    move-object/from16 v10, p10

    .line 58
    .line 59
    move-object/from16 v11, p11

    .line 60
    .line 61
    move-object/from16 v12, p12

    .line 62
    .line 63
    invoke-direct/range {v0 .. v13}, LtW0/g$b;-><init>(Lak/b;Lak/a;LTZ0/a;LwW0/g;LxW0/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/c;Lorg/xbet/toto_jackpot/impl/domain/scenario/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/MakeBetScenario;LxX0/a;Lorg/xbet/ui_common/utils/M;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;LzX0/k;LtW0/h;)V

    .line 64
    .line 65
    .line 66
    return-object v0
.end method
