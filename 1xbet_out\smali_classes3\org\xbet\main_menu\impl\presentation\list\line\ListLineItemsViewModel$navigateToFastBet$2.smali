.class final Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.main_menu.impl.presentation.list.line.ListLineItemsViewModel$navigateToFastBet$2"
    f = "ListLineItemsViewModel.kt"
    l = {
        0x252,
        0x255
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;->u4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;

    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;-><init>(Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 14

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->L$2:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, LwX0/c;

    .line 18
    .line 19
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->L$1:Ljava/lang/Object;

    .line 20
    .line 21
    check-cast v1, LW81/a;

    .line 22
    .line 23
    iget-object v2, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->L$0:Ljava/lang/Object;

    .line 24
    .line 25
    check-cast v2, LD80/a$b;

    .line 26
    .line 27
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    goto :goto_2

    .line 31
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 32
    .line 33
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 34
    .line 35
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 36
    .line 37
    .line 38
    throw p1

    .line 39
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 44
    .line 45
    .line 46
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    .line 47
    .line 48
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;->I3(Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;)Lorg/xbet/main_menu/impl/domain/usecases/GetFastBetGameUseCase;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iput v3, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->label:I

    .line 53
    .line 54
    invoke-virtual {p1, p0}, Lorg/xbet/main_menu/impl/domain/usecases/GetFastBetGameUseCase;->b(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    if-ne p1, v0, :cond_3

    .line 59
    .line 60
    goto :goto_1

    .line 61
    :cond_3
    :goto_0
    check-cast p1, LD80/a$b;

    .line 62
    .line 63
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    .line 64
    .line 65
    invoke-static {v1}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;->Q3(Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;)LwX0/c;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    iget-object v4, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    .line 70
    .line 71
    invoke-static {v4}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;->z3(Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;)LW81/a;

    .line 72
    .line 73
    .line 74
    move-result-object v4

    .line 75
    iget-object v5, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    .line 76
    .line 77
    invoke-static {v5}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;->J3(Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;)Lfk/l;

    .line 78
    .line 79
    .line 80
    move-result-object v5

    .line 81
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->L$0:Ljava/lang/Object;

    .line 82
    .line 83
    iput-object v4, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->L$1:Ljava/lang/Object;

    .line 84
    .line 85
    iput-object v1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->L$2:Ljava/lang/Object;

    .line 86
    .line 87
    iput v2, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$navigateToFastBet$2;->label:I

    .line 88
    .line 89
    const/4 v2, 0x0

    .line 90
    invoke-static {v5, v2, p0, v3, v2}, Lfk/l$a;->a(Lfk/l;Lorg/xbet/balance/model/BalanceScreenType;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 91
    .line 92
    .line 93
    move-result-object v2

    .line 94
    if-ne v2, v0, :cond_4

    .line 95
    .line 96
    :goto_1
    return-object v0

    .line 97
    :cond_4
    move-object v0, v2

    .line 98
    move-object v2, p1

    .line 99
    move-object p1, v0

    .line 100
    move-object v0, v1

    .line 101
    move-object v1, v4

    .line 102
    :goto_2
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 103
    .line 104
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 105
    .line 106
    .line 107
    move-result-wide v10

    .line 108
    invoke-virtual {v2}, LD80/a$b;->a()Lg81/b;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    invoke-virtual {p1}, Lg81/b;->e()J

    .line 113
    .line 114
    .line 115
    move-result-wide v3

    .line 116
    invoke-virtual {v2}, LD80/a$b;->a()Lg81/b;

    .line 117
    .line 118
    .line 119
    move-result-object p1

    .line 120
    invoke-virtual {p1}, Lg81/b;->j()Z

    .line 121
    .line 122
    .line 123
    move-result v6

    .line 124
    invoke-virtual {v2}, LD80/a$b;->a()Lg81/b;

    .line 125
    .line 126
    .line 127
    move-result-object p1

    .line 128
    invoke-virtual {p1}, Lg81/b;->m()J

    .line 129
    .line 130
    .line 131
    move-result-wide v7

    .line 132
    invoke-virtual {v2}, LD80/a$b;->a()Lg81/b;

    .line 133
    .line 134
    .line 135
    move-result-object p1

    .line 136
    invoke-virtual {p1}, Lg81/b;->k()Z

    .line 137
    .line 138
    .line 139
    move-result v9

    .line 140
    const/4 v12, 0x1

    .line 141
    const/4 v13, 0x0

    .line 142
    move-wide v2, v3

    .line 143
    const-wide/16 v4, 0x0

    .line 144
    .line 145
    invoke-interface/range {v1 .. v13}, LW81/a;->a(JJZJZJZI)Lr4/d;

    .line 146
    .line 147
    .line 148
    move-result-object p1

    .line 149
    invoke-virtual {v0, p1}, LwX0/c;->m(Lq4/q;)V

    .line 150
    .line 151
    .line 152
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 153
    .line 154
    return-object p1
.end method
