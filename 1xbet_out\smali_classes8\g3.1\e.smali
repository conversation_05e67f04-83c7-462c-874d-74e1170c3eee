.class public final Lg3/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0005\u001a+\u0010\u0006\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "Lee/f;",
        "Lokio/ByteString;",
        "bytes",
        "",
        "fromIndex",
        "toIndex",
        "a",
        "(<PERSON>/f;Lokio/ByteString;JJ)J",
        "coil-svg_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lee/f;Lokio/ByteString;JJ)J
    .locals 7
    .param p0    # Lee/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lokio/ByteString;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Lokio/ByteString;->size()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-lez v0, :cond_3

    .line 6
    .line 7
    const/4 v0, 0x0

    .line 8
    invoke-virtual {p1, v0}, Lokio/ByteString;->getByte(I)B

    .line 9
    .line 10
    .line 11
    move-result v2

    .line 12
    invoke-virtual {p1}, Lokio/ByteString;->size()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    int-to-long v0, v0

    .line 17
    sub-long v5, p4, v0

    .line 18
    .line 19
    move-wide v3, p2

    .line 20
    :goto_0
    const-wide/16 p2, -0x1

    .line 21
    .line 22
    cmp-long p4, v3, v5

    .line 23
    .line 24
    if-gez p4, :cond_2

    .line 25
    .line 26
    move-object v1, p0

    .line 27
    invoke-interface/range {v1 .. v6}, Lee/f;->m1(BJJ)J

    .line 28
    .line 29
    .line 30
    move-result-wide p4

    .line 31
    cmp-long p0, p4, p2

    .line 32
    .line 33
    if-eqz p0, :cond_1

    .line 34
    .line 35
    invoke-interface {v1, p4, p5, p1}, Lee/f;->a0(JLokio/ByteString;)Z

    .line 36
    .line 37
    .line 38
    move-result p0

    .line 39
    if-eqz p0, :cond_0

    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_0
    const-wide/16 p2, 0x1

    .line 43
    .line 44
    add-long v3, p4, p2

    .line 45
    .line 46
    move-object p0, v1

    .line 47
    goto :goto_0

    .line 48
    :cond_1
    :goto_1
    return-wide p4

    .line 49
    :cond_2
    return-wide p2

    .line 50
    :cond_3
    new-instance p0, Ljava/lang/IllegalArgumentException;

    .line 51
    .line 52
    const-string p1, "bytes is empty"

    .line 53
    .line 54
    invoke-direct {p0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    throw p0
.end method
