.class public final Lib1/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lib1/a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u001a\u001b\u0010\u0007\u001a\u00020\u0003*\u00020\u00062\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u001a\u0013\u0010\u000b\u001a\u00020\n*\u00020\tH\u0001\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u001a\u0013\u0010\r\u001a\u00020\n*\u00020\tH\u0001\u00a2\u0006\u0004\u0008\r\u0010\u000c\u001a\u0013\u0010\u000f\u001a\u00020\n*\u00020\u000eH\u0000\u00a2\u0006\u0004\u0008\u000f\u0010\u0010\u00a8\u0006\u0011"
    }
    d2 = {
        "Lkb1/v;",
        "Landroid/content/Context;",
        "context",
        "Landroid/graphics/drawable/ShapeDrawable;",
        "a",
        "(Lkb1/v;Landroid/content/Context;)Landroid/graphics/drawable/ShapeDrawable;",
        "Lm81/b;",
        "b",
        "(Lm81/b;Landroid/content/Context;)Landroid/graphics/drawable/ShapeDrawable;",
        "Lorg/xplatform/aggregator/api/navigation/TournamentsPage;",
        "",
        "d",
        "(Lorg/xplatform/aggregator/api/navigation/TournamentsPage;)I",
        "c",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "e",
        "(Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;)I",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lkb1/v;Landroid/content/Context;)Landroid/graphics/drawable/ShapeDrawable;
    .locals 5
    .param p0    # Lkb1/v;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lkb1/v$a;->a:Lkb1/v$a;

    .line 2
    .line 3
    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    sget p0, Lpb/e;->green:I

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    sget-object v0, Lkb1/v$b;->a:Lkb1/v$b;

    .line 13
    .line 14
    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    sget p0, Lpb/e;->dark_background_light:I

    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_1
    sget-object v0, Lkb1/v$c;->a:Lkb1/v$c;

    .line 24
    .line 25
    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    if-eqz v0, :cond_2

    .line 30
    .line 31
    sget p0, Lpb/e;->market_yellow:I

    .line 32
    .line 33
    goto :goto_0

    .line 34
    :cond_2
    sget-object v0, Lkb1/v$d;->a:Lkb1/v$d;

    .line 35
    .line 36
    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 37
    .line 38
    .line 39
    move-result p0

    .line 40
    if-eqz p0, :cond_3

    .line 41
    .line 42
    sget p0, Lpb/e;->text_color_secondary_light:I

    .line 43
    .line 44
    :goto_0
    new-instance v0, Landroid/graphics/drawable/ShapeDrawable;

    .line 45
    .line 46
    invoke-direct {v0}, Landroid/graphics/drawable/ShapeDrawable;-><init>()V

    .line 47
    .line 48
    .line 49
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 50
    .line 51
    .line 52
    move-result-object v1

    .line 53
    sget v2, Lpb/f;->corner_radius_12:I

    .line 54
    .line 55
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimension(I)F

    .line 56
    .line 57
    .line 58
    move-result v1

    .line 59
    invoke-static {p1, v1}, Lorg/xbet/ui_common/utils/c0;->a(Landroid/content/Context;F)F

    .line 60
    .line 61
    .line 62
    move-result v1

    .line 63
    new-instance v2, Landroid/graphics/drawable/shapes/RoundRectShape;

    .line 64
    .line 65
    const/16 v3, 0x8

    .line 66
    .line 67
    new-array v3, v3, [F

    .line 68
    .line 69
    const/4 v4, 0x0

    .line 70
    aput v1, v3, v4

    .line 71
    .line 72
    const/4 v4, 0x1

    .line 73
    aput v1, v3, v4

    .line 74
    .line 75
    const/4 v4, 0x2

    .line 76
    aput v1, v3, v4

    .line 77
    .line 78
    const/4 v4, 0x3

    .line 79
    aput v1, v3, v4

    .line 80
    .line 81
    const/4 v4, 0x4

    .line 82
    aput v1, v3, v4

    .line 83
    .line 84
    const/4 v4, 0x5

    .line 85
    aput v1, v3, v4

    .line 86
    .line 87
    const/4 v4, 0x6

    .line 88
    aput v1, v3, v4

    .line 89
    .line 90
    const/4 v4, 0x7

    .line 91
    aput v1, v3, v4

    .line 92
    .line 93
    const/4 v1, 0x0

    .line 94
    invoke-direct {v2, v3, v1, v1}, Landroid/graphics/drawable/shapes/RoundRectShape;-><init>([FLandroid/graphics/RectF;[F)V

    .line 95
    .line 96
    .line 97
    invoke-virtual {v0, v2}, Landroid/graphics/drawable/ShapeDrawable;->setShape(Landroid/graphics/drawable/shapes/Shape;)V

    .line 98
    .line 99
    .line 100
    invoke-virtual {v0}, Landroid/graphics/drawable/ShapeDrawable;->getPaint()Landroid/graphics/Paint;

    .line 101
    .line 102
    .line 103
    move-result-object v1

    .line 104
    invoke-static {p1, p0}, LF0/b;->getColor(Landroid/content/Context;I)I

    .line 105
    .line 106
    .line 107
    move-result p0

    .line 108
    invoke-virtual {v1, p0}, Landroid/graphics/Paint;->setColor(I)V

    .line 109
    .line 110
    .line 111
    return-object v0

    .line 112
    :cond_3
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 113
    .line 114
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 115
    .line 116
    .line 117
    throw p0
.end method

.method public static final b(Lm81/b;Landroid/content/Context;)Landroid/graphics/drawable/ShapeDrawable;
    .locals 8
    .param p0    # Lm81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lm81/b;->c()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x1

    .line 6
    if-gt v1, v0, :cond_0

    .line 7
    .line 8
    const/16 v2, 0xb

    .line 9
    .line 10
    if-ge v0, v2, :cond_0

    .line 11
    .line 12
    sget p0, Lpb/c;->transparent:I

    .line 13
    .line 14
    :goto_0
    move v4, p0

    .line 15
    goto :goto_1

    .line 16
    :cond_0
    invoke-virtual {p0}, Lm81/b;->b()Z

    .line 17
    .line 18
    .line 19
    move-result p0

    .line 20
    if-eqz p0, :cond_1

    .line 21
    .line 22
    sget p0, Lf/a;->colorPrimary:I

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_1
    sget p0, Lpb/c;->background:I

    .line 26
    .line 27
    goto :goto_0

    .line 28
    :goto_1
    new-instance p0, Landroid/graphics/drawable/ShapeDrawable;

    .line 29
    .line 30
    invoke-direct {p0}, Landroid/graphics/drawable/ShapeDrawable;-><init>()V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    sget v2, Lpb/f;->corner_radius_12:I

    .line 38
    .line 39
    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimension(I)F

    .line 40
    .line 41
    .line 42
    move-result v0

    .line 43
    invoke-static {p1, v0}, Lorg/xbet/ui_common/utils/c0;->a(Landroid/content/Context;F)F

    .line 44
    .line 45
    .line 46
    move-result v0

    .line 47
    new-instance v2, Landroid/graphics/drawable/shapes/RoundRectShape;

    .line 48
    .line 49
    const/16 v3, 0x8

    .line 50
    .line 51
    new-array v3, v3, [F

    .line 52
    .line 53
    const/4 v5, 0x0

    .line 54
    aput v0, v3, v5

    .line 55
    .line 56
    aput v0, v3, v1

    .line 57
    .line 58
    const/4 v1, 0x2

    .line 59
    aput v0, v3, v1

    .line 60
    .line 61
    const/4 v1, 0x3

    .line 62
    aput v0, v3, v1

    .line 63
    .line 64
    const/4 v1, 0x4

    .line 65
    aput v0, v3, v1

    .line 66
    .line 67
    const/4 v1, 0x5

    .line 68
    aput v0, v3, v1

    .line 69
    .line 70
    const/4 v1, 0x6

    .line 71
    aput v0, v3, v1

    .line 72
    .line 73
    const/4 v1, 0x7

    .line 74
    aput v0, v3, v1

    .line 75
    .line 76
    const/4 v0, 0x0

    .line 77
    invoke-direct {v2, v3, v0, v0}, Landroid/graphics/drawable/shapes/RoundRectShape;-><init>([FLandroid/graphics/RectF;[F)V

    .line 78
    .line 79
    .line 80
    invoke-virtual {p0, v2}, Landroid/graphics/drawable/ShapeDrawable;->setShape(Landroid/graphics/drawable/shapes/Shape;)V

    .line 81
    .line 82
    .line 83
    invoke-virtual {p0}, Landroid/graphics/drawable/ShapeDrawable;->getPaint()Landroid/graphics/Paint;

    .line 84
    .line 85
    .line 86
    move-result-object v0

    .line 87
    sget-object v2, Lub/b;->a:Lub/b;

    .line 88
    .line 89
    const/4 v6, 0x4

    .line 90
    const/4 v7, 0x0

    .line 91
    move-object v3, p1

    .line 92
    invoke-static/range {v2 .. v7}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 93
    .line 94
    .line 95
    move-result p1

    .line 96
    invoke-virtual {v0, p1}, Landroid/graphics/Paint;->setColor(I)V

    .line 97
    .line 98
    .line 99
    return-object p0
.end method

.method public static final c(Lorg/xplatform/aggregator/api/navigation/TournamentsPage;)I
    .locals 1
    .param p0    # Lorg/xplatform/aggregator/api/navigation/TournamentsPage;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lib1/a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p0, v0, :cond_3

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p0, v0, :cond_2

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-eq p0, v0, :cond_1

    .line 17
    .line 18
    const/4 v0, 0x4

    .line 19
    if-ne p0, v0, :cond_0

    .line 20
    .line 21
    sget p0, LlZ0/h;->ic_glyph_info_circle_filled:I

    .line 22
    .line 23
    return p0

    .line 24
    :cond_0
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 25
    .line 26
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 27
    .line 28
    .line 29
    throw p0

    .line 30
    :cond_1
    sget p0, LlZ0/h;->ic_glyph_results:I

    .line 31
    .line 32
    return p0

    .line 33
    :cond_2
    sget p0, LlZ0/h;->ic_glyph_cards:I

    .line 34
    .line 35
    return p0

    .line 36
    :cond_3
    sget p0, LlZ0/h;->ic_glyph_tournaments:I

    .line 37
    .line 38
    return p0
.end method

.method public static final d(Lorg/xplatform/aggregator/api/navigation/TournamentsPage;)I
    .locals 1
    .param p0    # Lorg/xplatform/aggregator/api/navigation/TournamentsPage;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lib1/a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p0, v0, :cond_3

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p0, v0, :cond_2

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-eq p0, v0, :cond_1

    .line 17
    .line 18
    const/4 v0, 0x4

    .line 19
    if-ne p0, v0, :cond_0

    .line 20
    .line 21
    sget p0, Lpb/k;->casino_tournaments_conditions:I

    .line 22
    .line 23
    return p0

    .line 24
    :cond_0
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 25
    .line 26
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 27
    .line 28
    .line 29
    throw p0

    .line 30
    :cond_1
    sget p0, Lpb/k;->results:I

    .line 31
    .line 32
    return p0

    .line 33
    :cond_2
    sget p0, Lpb/k;->games:I

    .line 34
    .line 35
    return p0

    .line 36
    :cond_3
    sget p0, Lpb/k;->casino_tournaments_descriptions:I

    .line 37
    .line 38
    return p0
.end method

.method public static final e(Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;)I
    .locals 1
    .param p0    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lib1/a$a;->b:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p0, v0, :cond_1

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-ne p0, v0, :cond_0

    .line 14
    .line 15
    return v0

    .line 16
    :cond_0
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 17
    .line 18
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 19
    .line 20
    .line 21
    throw p0

    .line 22
    :cond_1
    return v0
.end method
