.class public final Lorg/xbet/main_menu/impl/presentation/common/mappers/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/main_menu/impl/presentation/common/mappers/a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00004\n\u0002\u0010$\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u001aE\u0010\u000c\u001a\u00020\u000b*\u0014\u0012\u0004\u0012\u00020\u0001\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00030\u00020\u00002\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\n\u001a\u00020\u0008H\u0000\u00a2\u0006\u0004\u0008\u000c\u0010\r\u001a+\u0010\u000f\u001a\u00020\u000e*\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010\u001a#\u0010\u0011\u001a\u00020\u000e*\u00020\u00012\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012\u001a#\u0010\u0013\u001a\u00020\u000e*\u00020\u00012\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0012\u00a8\u0006\u0014"
    }
    d2 = {
        "",
        "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
        "",
        "LD80/a;",
        "Lorg/xbet/feature/calendar_event/api/domain/models/CalendarEventType;",
        "currentCalendarEventType",
        "LHX0/e;",
        "resourceManager",
        "",
        "gamesName",
        "tabStyle",
        "LN80/b;",
        "c",
        "(Ljava/util/Map;Lorg/xbet/feature/calendar_event/api/domain/models/CalendarEventType;LHX0/e;Ljava/lang/String;Ljava/lang/String;)LN80/b;",
        "LH01/h;",
        "b",
        "(Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Lorg/xbet/feature/calendar_event/api/domain/models/CalendarEventType;LHX0/e;Ljava/lang/String;)LH01/h;",
        "a",
        "(Lorg/xbet/main_menu/api/domain/models/MenuSectionType;LHX0/e;Ljava/lang/String;)LH01/h;",
        "d",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xbet/main_menu/api/domain/models/MenuSectionType;LHX0/e;Ljava/lang/String;)LH01/h;
    .locals 11

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/common/mappers/a$a;->b:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x0

    .line 10
    packed-switch p0, :pswitch_data_0

    .line 11
    .line 12
    .line 13
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 14
    .line 15
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 16
    .line 17
    .line 18
    throw p0

    .line 19
    :pswitch_0
    sget v2, LlZ0/h;->ic_glyph_other:I

    .line 20
    .line 21
    sget p0, Lpb/k;->other_menu:I

    .line 22
    .line 23
    new-array p2, v0, [Ljava/lang/Object;

    .line 24
    .line 25
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    new-instance v0, LH01/h;

    .line 30
    .line 31
    const/4 v4, 0x4

    .line 32
    const/4 v5, 0x0

    .line 33
    const/4 v3, 0x0

    .line 34
    invoke-direct/range {v0 .. v5}, LH01/h;-><init>(Ljava/lang/String;ILjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 35
    .line 36
    .line 37
    return-object v0

    .line 38
    :pswitch_1
    sget v3, LlZ0/h;->ic_glyph_games:I

    .line 39
    .line 40
    new-instance v1, LH01/h;

    .line 41
    .line 42
    const/4 v5, 0x4

    .line 43
    const/4 v6, 0x0

    .line 44
    const/4 v4, 0x0

    .line 45
    move-object v2, p2

    .line 46
    invoke-direct/range {v1 .. v6}, LH01/h;-><init>(Ljava/lang/String;ILjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 47
    .line 48
    .line 49
    return-object v1

    .line 50
    :pswitch_2
    sget v4, LlZ0/h;->ic_glyph_virtual:I

    .line 51
    .line 52
    sget p0, Lpb/k;->virtual:I

    .line 53
    .line 54
    new-array p2, v0, [Ljava/lang/Object;

    .line 55
    .line 56
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object v3

    .line 60
    new-instance v2, LH01/h;

    .line 61
    .line 62
    const/4 v6, 0x4

    .line 63
    const/4 v7, 0x0

    .line 64
    const/4 v5, 0x0

    .line 65
    invoke-direct/range {v2 .. v7}, LH01/h;-><init>(Ljava/lang/String;ILjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 66
    .line 67
    .line 68
    return-object v2

    .line 69
    :pswitch_3
    sget v5, LlZ0/h;->ic_glyph_cards:I

    .line 70
    .line 71
    sget p0, Lpb/k;->casino_chip:I

    .line 72
    .line 73
    new-array p2, v0, [Ljava/lang/Object;

    .line 74
    .line 75
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 76
    .line 77
    .line 78
    move-result-object v4

    .line 79
    new-instance v3, LH01/h;

    .line 80
    .line 81
    const/4 v7, 0x4

    .line 82
    const/4 v8, 0x0

    .line 83
    const/4 v6, 0x0

    .line 84
    invoke-direct/range {v3 .. v8}, LH01/h;-><init>(Ljava/lang/String;ILjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 85
    .line 86
    .line 87
    return-object v3

    .line 88
    :pswitch_4
    sget v6, LlZ0/h;->ic_glyph_sport:I

    .line 89
    .line 90
    sget p0, Lpb/k;->sport:I

    .line 91
    .line 92
    new-array p2, v0, [Ljava/lang/Object;

    .line 93
    .line 94
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 95
    .line 96
    .line 97
    move-result-object v5

    .line 98
    new-instance v4, LH01/h;

    .line 99
    .line 100
    const/4 v8, 0x4

    .line 101
    const/4 v9, 0x0

    .line 102
    const/4 v7, 0x0

    .line 103
    invoke-direct/range {v4 .. v9}, LH01/h;-><init>(Ljava/lang/String;ILjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 104
    .line 105
    .line 106
    return-object v4

    .line 107
    :pswitch_5
    sget v7, LlZ0/h;->ic_glyph_top:I

    .line 108
    .line 109
    sget p0, Lpb/k;->top:I

    .line 110
    .line 111
    new-array p2, v0, [Ljava/lang/Object;

    .line 112
    .line 113
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 114
    .line 115
    .line 116
    move-result-object v6

    .line 117
    new-instance v5, LH01/h;

    .line 118
    .line 119
    const/4 v9, 0x4

    .line 120
    const/4 v10, 0x0

    .line 121
    const/4 v8, 0x0

    .line 122
    invoke-direct/range {v5 .. v10}, LH01/h;-><init>(Ljava/lang/String;ILjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 123
    .line 124
    .line 125
    return-object v5

    .line 126
    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_5
        :pswitch_5
        :pswitch_4
        :pswitch_4
        :pswitch_3
        :pswitch_3
        :pswitch_2
        :pswitch_2
        :pswitch_1
        :pswitch_1
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method

.method public static final b(Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Lorg/xbet/feature/calendar_event/api/domain/models/CalendarEventType;LHX0/e;Ljava/lang/String;)LH01/h;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/common/mappers/a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-ne p1, v0, :cond_0

    .line 11
    .line 12
    invoke-static {p0, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/a;->d(Lorg/xbet/main_menu/api/domain/models/MenuSectionType;LHX0/e;Ljava/lang/String;)LH01/h;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    return-object p0

    .line 17
    :cond_0
    invoke-static {p0, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/a;->a(Lorg/xbet/main_menu/api/domain/models/MenuSectionType;LHX0/e;Ljava/lang/String;)LH01/h;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    return-object p0
.end method

.method public static final c(Ljava/util/Map;Lorg/xbet/feature/calendar_event/api/domain/models/CalendarEventType;LHX0/e;Ljava/lang/String;Ljava/lang/String;)LN80/b;
    .locals 3
    .param p0    # Ljava/util/Map;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/feature/calendar_event/api/domain/models/CalendarEventType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "+",
            "Ljava/util/List<",
            "+",
            "LD80/a;",
            ">;>;",
            "Lorg/xbet/feature/calendar_event/api/domain/models/CalendarEventType;",
            "LHX0/e;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ")",
            "LN80/b;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-interface {p0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Ljava/lang/Iterable;

    .line 6
    .line 7
    new-instance v1, Ljava/util/ArrayList;

    .line 8
    .line 9
    const/16 v2, 0xa

    .line 10
    .line 11
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 16
    .line 17
    .line 18
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    if-eqz v2, :cond_0

    .line 27
    .line 28
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    check-cast v2, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 33
    .line 34
    invoke-static {v2, p1, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/a;->b(Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Lorg/xbet/feature/calendar_event/api/domain/models/CalendarEventType;LHX0/e;Ljava/lang/String;)LH01/h;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 39
    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_0
    invoke-interface {p0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    .line 43
    .line 44
    .line 45
    move-result-object p0

    .line 46
    check-cast p0, Ljava/lang/Iterable;

    .line 47
    .line 48
    invoke-static {p0}, Lkotlin/collections/CollectionsKt;->z1(Ljava/lang/Iterable;)Ljava/util/List;

    .line 49
    .line 50
    .line 51
    move-result-object p0

    .line 52
    new-instance p1, LN80/b;

    .line 53
    .line 54
    invoke-direct {p1, p4, v1, p0}, LN80/b;-><init>(Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V

    .line 55
    .line 56
    .line 57
    return-object p1
.end method

.method public static final d(Lorg/xbet/main_menu/api/domain/models/MenuSectionType;LHX0/e;Ljava/lang/String;)LH01/h;
    .locals 11

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/common/mappers/a$a;->b:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x0

    .line 10
    packed-switch p0, :pswitch_data_0

    .line 11
    .line 12
    .line 13
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 14
    .line 15
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 16
    .line 17
    .line 18
    throw p0

    .line 19
    :pswitch_0
    sget v2, LlZ0/h;->ic_glyph_other_ny:I

    .line 20
    .line 21
    sget p0, Lpb/k;->other_menu:I

    .line 22
    .line 23
    new-array p2, v0, [Ljava/lang/Object;

    .line 24
    .line 25
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    new-instance v0, LH01/h;

    .line 30
    .line 31
    const/4 v4, 0x4

    .line 32
    const/4 v5, 0x0

    .line 33
    const/4 v3, 0x0

    .line 34
    invoke-direct/range {v0 .. v5}, LH01/h;-><init>(Ljava/lang/String;ILjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 35
    .line 36
    .line 37
    return-object v0

    .line 38
    :pswitch_1
    sget v3, LlZ0/h;->ic_glyph_games_ny:I

    .line 39
    .line 40
    new-instance v1, LH01/h;

    .line 41
    .line 42
    const/4 v5, 0x4

    .line 43
    const/4 v6, 0x0

    .line 44
    const/4 v4, 0x0

    .line 45
    move-object v2, p2

    .line 46
    invoke-direct/range {v1 .. v6}, LH01/h;-><init>(Ljava/lang/String;ILjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 47
    .line 48
    .line 49
    return-object v1

    .line 50
    :pswitch_2
    sget v4, LlZ0/h;->ic_glyph_virtual:I

    .line 51
    .line 52
    sget p0, Lpb/k;->virtual:I

    .line 53
    .line 54
    new-array p2, v0, [Ljava/lang/Object;

    .line 55
    .line 56
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object v3

    .line 60
    new-instance v2, LH01/h;

    .line 61
    .line 62
    const/4 v6, 0x4

    .line 63
    const/4 v7, 0x0

    .line 64
    const/4 v5, 0x0

    .line 65
    invoke-direct/range {v2 .. v7}, LH01/h;-><init>(Ljava/lang/String;ILjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 66
    .line 67
    .line 68
    return-object v2

    .line 69
    :pswitch_3
    sget v5, LlZ0/h;->ic_glyph_cards_ny:I

    .line 70
    .line 71
    sget p0, Lpb/k;->casino_chip:I

    .line 72
    .line 73
    new-array p2, v0, [Ljava/lang/Object;

    .line 74
    .line 75
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 76
    .line 77
    .line 78
    move-result-object v4

    .line 79
    new-instance v3, LH01/h;

    .line 80
    .line 81
    const/4 v7, 0x4

    .line 82
    const/4 v8, 0x0

    .line 83
    const/4 v6, 0x0

    .line 84
    invoke-direct/range {v3 .. v8}, LH01/h;-><init>(Ljava/lang/String;ILjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 85
    .line 86
    .line 87
    return-object v3

    .line 88
    :pswitch_4
    sget v6, LlZ0/h;->ic_glyph_sport_ny:I

    .line 89
    .line 90
    sget p0, Lpb/k;->sport:I

    .line 91
    .line 92
    new-array p2, v0, [Ljava/lang/Object;

    .line 93
    .line 94
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 95
    .line 96
    .line 97
    move-result-object v5

    .line 98
    new-instance v4, LH01/h;

    .line 99
    .line 100
    const/4 v8, 0x4

    .line 101
    const/4 v9, 0x0

    .line 102
    const/4 v7, 0x0

    .line 103
    invoke-direct/range {v4 .. v9}, LH01/h;-><init>(Ljava/lang/String;ILjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 104
    .line 105
    .line 106
    return-object v4

    .line 107
    :pswitch_5
    sget v7, LlZ0/h;->ic_glyph_top_ny:I

    .line 108
    .line 109
    sget p0, Lpb/k;->top:I

    .line 110
    .line 111
    new-array p2, v0, [Ljava/lang/Object;

    .line 112
    .line 113
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 114
    .line 115
    .line 116
    move-result-object v6

    .line 117
    new-instance v5, LH01/h;

    .line 118
    .line 119
    const/4 v9, 0x4

    .line 120
    const/4 v10, 0x0

    .line 121
    const/4 v8, 0x0

    .line 122
    invoke-direct/range {v5 .. v10}, LH01/h;-><init>(Ljava/lang/String;ILjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 123
    .line 124
    .line 125
    return-object v5

    .line 126
    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_5
        :pswitch_5
        :pswitch_4
        :pswitch_4
        :pswitch_3
        :pswitch_3
        :pswitch_2
        :pswitch_2
        :pswitch_1
        :pswitch_1
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method
