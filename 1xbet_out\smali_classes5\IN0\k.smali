.class public final LIN0/k;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0004\u001a!\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u0002H\u0007\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u001a9\u0010\r\u001a\u00020\u00042\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u00072\u0006\u0010\u000c\u001a\u00020\u000b2\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u0002H\u0007\u00a2\u0006\u0004\u0008\r\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;",
        "headerState",
        "Landroidx/compose/ui/l;",
        "modifier",
        "",
        "d",
        "(Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V",
        "",
        "title",
        "description",
        "gameDateInfo",
        "",
        "isLoading",
        "c",
        "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V",
        "statistic_core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, LIN0/k;->e(Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p8}, LIN0/k;->f(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V
    .locals 42
    .param p0    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move/from16 v4, p3

    .line 2
    .line 3
    move/from16 v6, p6

    .line 4
    .line 5
    const/16 v0, 0x10

    .line 6
    .line 7
    const/4 v1, 0x2

    .line 8
    const/4 v2, 0x4

    .line 9
    const/16 v3, 0x30

    .line 10
    .line 11
    const v5, 0x458efc0a

    .line 12
    .line 13
    .line 14
    move-object/from16 v7, p5

    .line 15
    .line 16
    invoke-interface {v7, v5}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 17
    .line 18
    .line 19
    move-result-object v7

    .line 20
    const/4 v8, 0x1

    .line 21
    and-int/lit8 v9, p7, 0x1

    .line 22
    .line 23
    if-eqz v9, :cond_0

    .line 24
    .line 25
    or-int/lit8 v9, v6, 0x6

    .line 26
    .line 27
    move v10, v9

    .line 28
    move-object/from16 v9, p0

    .line 29
    .line 30
    goto :goto_1

    .line 31
    :cond_0
    and-int/lit8 v9, v6, 0x6

    .line 32
    .line 33
    if-nez v9, :cond_2

    .line 34
    .line 35
    move-object/from16 v9, p0

    .line 36
    .line 37
    invoke-interface {v7, v9}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 38
    .line 39
    .line 40
    move-result v10

    .line 41
    if-eqz v10, :cond_1

    .line 42
    .line 43
    const/4 v10, 0x4

    .line 44
    goto :goto_0

    .line 45
    :cond_1
    const/4 v10, 0x2

    .line 46
    :goto_0
    or-int/2addr v10, v6

    .line 47
    goto :goto_1

    .line 48
    :cond_2
    move-object/from16 v9, p0

    .line 49
    .line 50
    move v10, v6

    .line 51
    :goto_1
    and-int/lit8 v1, p7, 0x2

    .line 52
    .line 53
    if-eqz v1, :cond_4

    .line 54
    .line 55
    or-int/2addr v10, v3

    .line 56
    :cond_3
    move-object/from16 v1, p1

    .line 57
    .line 58
    goto :goto_3

    .line 59
    :cond_4
    and-int/lit8 v1, v6, 0x30

    .line 60
    .line 61
    if-nez v1, :cond_3

    .line 62
    .line 63
    move-object/from16 v1, p1

    .line 64
    .line 65
    invoke-interface {v7, v1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 66
    .line 67
    .line 68
    move-result v11

    .line 69
    if-eqz v11, :cond_5

    .line 70
    .line 71
    const/16 v11, 0x20

    .line 72
    .line 73
    goto :goto_2

    .line 74
    :cond_5
    const/16 v11, 0x10

    .line 75
    .line 76
    :goto_2
    or-int/2addr v10, v11

    .line 77
    :goto_3
    and-int/lit8 v2, p7, 0x4

    .line 78
    .line 79
    if-eqz v2, :cond_7

    .line 80
    .line 81
    or-int/lit16 v10, v10, 0x180

    .line 82
    .line 83
    :cond_6
    move-object/from16 v2, p2

    .line 84
    .line 85
    goto :goto_5

    .line 86
    :cond_7
    and-int/lit16 v2, v6, 0x180

    .line 87
    .line 88
    if-nez v2, :cond_6

    .line 89
    .line 90
    move-object/from16 v2, p2

    .line 91
    .line 92
    invoke-interface {v7, v2}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 93
    .line 94
    .line 95
    move-result v11

    .line 96
    if-eqz v11, :cond_8

    .line 97
    .line 98
    const/16 v11, 0x100

    .line 99
    .line 100
    goto :goto_4

    .line 101
    :cond_8
    const/16 v11, 0x80

    .line 102
    .line 103
    :goto_4
    or-int/2addr v10, v11

    .line 104
    :goto_5
    and-int/lit8 v11, p7, 0x8

    .line 105
    .line 106
    if-eqz v11, :cond_9

    .line 107
    .line 108
    or-int/lit16 v10, v10, 0xc00

    .line 109
    .line 110
    goto :goto_7

    .line 111
    :cond_9
    and-int/lit16 v11, v6, 0xc00

    .line 112
    .line 113
    if-nez v11, :cond_b

    .line 114
    .line 115
    invoke-interface {v7, v4}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 116
    .line 117
    .line 118
    move-result v11

    .line 119
    if-eqz v11, :cond_a

    .line 120
    .line 121
    const/16 v11, 0x800

    .line 122
    .line 123
    goto :goto_6

    .line 124
    :cond_a
    const/16 v11, 0x400

    .line 125
    .line 126
    :goto_6
    or-int/2addr v10, v11

    .line 127
    :cond_b
    :goto_7
    and-int/lit8 v0, p7, 0x10

    .line 128
    .line 129
    if-eqz v0, :cond_d

    .line 130
    .line 131
    or-int/lit16 v10, v10, 0x6000

    .line 132
    .line 133
    :cond_c
    move-object/from16 v11, p4

    .line 134
    .line 135
    goto :goto_9

    .line 136
    :cond_d
    and-int/lit16 v11, v6, 0x6000

    .line 137
    .line 138
    if-nez v11, :cond_c

    .line 139
    .line 140
    move-object/from16 v11, p4

    .line 141
    .line 142
    invoke-interface {v7, v11}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 143
    .line 144
    .line 145
    move-result v12

    .line 146
    if-eqz v12, :cond_e

    .line 147
    .line 148
    const/16 v12, 0x4000

    .line 149
    .line 150
    goto :goto_8

    .line 151
    :cond_e
    const/16 v12, 0x2000

    .line 152
    .line 153
    :goto_8
    or-int/2addr v10, v12

    .line 154
    :goto_9
    and-int/lit16 v12, v10, 0x2493

    .line 155
    .line 156
    const/16 v13, 0x2492

    .line 157
    .line 158
    if-ne v12, v13, :cond_10

    .line 159
    .line 160
    invoke-interface {v7}, Landroidx/compose/runtime/j;->c()Z

    .line 161
    .line 162
    .line 163
    move-result v12

    .line 164
    if-nez v12, :cond_f

    .line 165
    .line 166
    goto :goto_a

    .line 167
    :cond_f
    invoke-interface {v7}, Landroidx/compose/runtime/j;->n()V

    .line 168
    .line 169
    .line 170
    move-object v5, v11

    .line 171
    goto/16 :goto_e

    .line 172
    .line 173
    :cond_10
    :goto_a
    if-eqz v0, :cond_11

    .line 174
    .line 175
    sget-object v0, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 176
    .line 177
    goto :goto_b

    .line 178
    :cond_11
    move-object v0, v11

    .line 179
    :goto_b
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 180
    .line 181
    .line 182
    move-result v11

    .line 183
    if-eqz v11, :cond_12

    .line 184
    .line 185
    const/4 v11, -0x1

    .line 186
    const-string v12, "org.xbet.statistic.statistic_core.presentation.composable.ListWidgetsHeaderComponent (ListWidgetsHeaderComponent.kt:66)"

    .line 187
    .line 188
    invoke-static {v5, v10, v11, v12}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 189
    .line 190
    .line 191
    :cond_12
    sget-object v5, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 192
    .line 193
    invoke-virtual {v5}, Landroidx/compose/ui/e$a;->g()Landroidx/compose/ui/e$b;

    .line 194
    .line 195
    .line 196
    move-result-object v11

    .line 197
    sget-object v12, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 198
    .line 199
    invoke-virtual {v12}, Landroidx/compose/foundation/layout/Arrangement;->h()Landroidx/compose/foundation/layout/Arrangement$m;

    .line 200
    .line 201
    .line 202
    move-result-object v12

    .line 203
    invoke-static {v12, v11, v7, v3}, Landroidx/compose/foundation/layout/k;->a(Landroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 204
    .line 205
    .line 206
    move-result-object v3

    .line 207
    const/4 v11, 0x0

    .line 208
    invoke-static {v7, v11}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 209
    .line 210
    .line 211
    move-result v12

    .line 212
    invoke-interface {v7}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 213
    .line 214
    .line 215
    move-result-object v13

    .line 216
    invoke-static {v7, v0}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 217
    .line 218
    .line 219
    move-result-object v14

    .line 220
    sget-object v15, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 221
    .line 222
    invoke-virtual {v15}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 223
    .line 224
    .line 225
    move-result-object v8

    .line 226
    invoke-interface {v7}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 227
    .line 228
    .line 229
    move-result-object v16

    .line 230
    invoke-static/range {v16 .. v16}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 231
    .line 232
    .line 233
    move-result v16

    .line 234
    if-nez v16, :cond_13

    .line 235
    .line 236
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 237
    .line 238
    .line 239
    :cond_13
    invoke-interface {v7}, Landroidx/compose/runtime/j;->l()V

    .line 240
    .line 241
    .line 242
    invoke-interface {v7}, Landroidx/compose/runtime/j;->B()Z

    .line 243
    .line 244
    .line 245
    move-result v16

    .line 246
    if-eqz v16, :cond_14

    .line 247
    .line 248
    invoke-interface {v7, v8}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 249
    .line 250
    .line 251
    goto :goto_c

    .line 252
    :cond_14
    invoke-interface {v7}, Landroidx/compose/runtime/j;->h()V

    .line 253
    .line 254
    .line 255
    :goto_c
    invoke-static {v7}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 256
    .line 257
    .line 258
    move-result-object v8

    .line 259
    invoke-virtual {v15}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 260
    .line 261
    .line 262
    move-result-object v11

    .line 263
    invoke-static {v8, v3, v11}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 264
    .line 265
    .line 266
    invoke-virtual {v15}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 267
    .line 268
    .line 269
    move-result-object v3

    .line 270
    invoke-static {v8, v13, v3}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 271
    .line 272
    .line 273
    invoke-virtual {v15}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 274
    .line 275
    .line 276
    move-result-object v3

    .line 277
    invoke-interface {v8}, Landroidx/compose/runtime/j;->B()Z

    .line 278
    .line 279
    .line 280
    move-result v11

    .line 281
    if-nez v11, :cond_15

    .line 282
    .line 283
    invoke-interface {v8}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 284
    .line 285
    .line 286
    move-result-object v11

    .line 287
    invoke-static {v12}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 288
    .line 289
    .line 290
    move-result-object v13

    .line 291
    invoke-static {v11, v13}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 292
    .line 293
    .line 294
    move-result v11

    .line 295
    if-nez v11, :cond_16

    .line 296
    .line 297
    :cond_15
    invoke-static {v12}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 298
    .line 299
    .line 300
    move-result-object v11

    .line 301
    invoke-interface {v8, v11}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 302
    .line 303
    .line 304
    invoke-static {v12}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 305
    .line 306
    .line 307
    move-result-object v11

    .line 308
    invoke-interface {v8, v11, v3}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 309
    .line 310
    .line 311
    :cond_16
    invoke-virtual {v15}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 312
    .line 313
    .line 314
    move-result-object v3

    .line 315
    invoke-static {v8, v14, v3}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 316
    .line 317
    .line 318
    sget-object v3, Landroidx/compose/foundation/layout/n;->a:Landroidx/compose/foundation/layout/n;

    .line 319
    .line 320
    if-nez v4, :cond_18

    .line 321
    .line 322
    const v3, -0x71b97328

    .line 323
    .line 324
    .line 325
    invoke-interface {v7, v3}, Landroidx/compose/runtime/j;->t(I)V

    .line 326
    .line 327
    .line 328
    invoke-virtual {v5}, Landroidx/compose/ui/e$a;->e()Landroidx/compose/ui/e;

    .line 329
    .line 330
    .line 331
    move-result-object v25

    .line 332
    sget-object v3, LA11/a;->a:LA11/a;

    .line 333
    .line 334
    invoke-virtual {v3}, LA11/a;->P1()J

    .line 335
    .line 336
    .line 337
    move-result-wide v15

    .line 338
    invoke-virtual {v3}, LA11/a;->R1()J

    .line 339
    .line 340
    .line 341
    move-result-wide v17

    .line 342
    sget-object v38, LC11/a;->a:LC11/a;

    .line 343
    .line 344
    invoke-virtual/range {v38 .. v38}, LC11/a;->n()Landroidx/compose/ui/text/a0;

    .line 345
    .line 346
    .line 347
    move-result-object v8

    .line 348
    const/4 v11, 0x0

    .line 349
    invoke-static {v8, v7, v11}, LB11/a;->b(Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/text/a0;

    .line 350
    .line 351
    .line 352
    move-result-object v31

    .line 353
    sget-object v39, Landroidx/compose/ui/text/style/s;->b:Landroidx/compose/ui/text/style/s$a;

    .line 354
    .line 355
    invoke-virtual/range {v39 .. v39}, Landroidx/compose/ui/text/style/s$a;->b()I

    .line 356
    .line 357
    .line 358
    move-result v26

    .line 359
    and-int/lit8 v34, v10, 0xe

    .line 360
    .line 361
    const/16 v36, 0x0

    .line 362
    .line 363
    const v37, 0x169f3e

    .line 364
    .line 365
    .line 366
    const/4 v8, 0x0

    .line 367
    move v12, v10

    .line 368
    const-wide/16 v9, 0x0

    .line 369
    .line 370
    const/4 v13, 0x0

    .line 371
    const/4 v11, 0x0

    .line 372
    move v14, v12

    .line 373
    const/4 v12, 0x0

    .line 374
    move/from16 v19, v14

    .line 375
    .line 376
    const/16 v20, 0x0

    .line 377
    .line 378
    const-wide/16 v13, 0x0

    .line 379
    .line 380
    move/from16 v21, v19

    .line 381
    .line 382
    const/16 v19, 0x0

    .line 383
    .line 384
    const/16 v22, 0x0

    .line 385
    .line 386
    const/16 v20, 0x0

    .line 387
    .line 388
    move/from16 v23, v21

    .line 389
    .line 390
    const/16 v21, 0x0

    .line 391
    .line 392
    move/from16 v24, v23

    .line 393
    .line 394
    const/16 v27, 0x0

    .line 395
    .line 396
    const-wide/16 v22, 0x0

    .line 397
    .line 398
    move/from16 v28, v24

    .line 399
    .line 400
    const/16 v24, 0x0

    .line 401
    .line 402
    const/16 v29, 0x0

    .line 403
    .line 404
    const/16 v27, 0x0

    .line 405
    .line 406
    move/from16 v30, v28

    .line 407
    .line 408
    const/16 v28, 0x2

    .line 409
    .line 410
    const/16 v32, 0x0

    .line 411
    .line 412
    const/16 v29, 0x0

    .line 413
    .line 414
    move/from16 v33, v30

    .line 415
    .line 416
    const/16 v30, 0x0

    .line 417
    .line 418
    const/16 v35, 0x0

    .line 419
    .line 420
    const/16 v32, 0x0

    .line 421
    .line 422
    const/16 v40, 0x0

    .line 423
    .line 424
    const v35, 0x186c00

    .line 425
    .line 426
    .line 427
    move-object/from16 p4, v0

    .line 428
    .line 429
    move/from16 v40, v33

    .line 430
    .line 431
    const/4 v0, 0x0

    .line 432
    move-object/from16 v33, v7

    .line 433
    .line 434
    move-object/from16 v7, p0

    .line 435
    .line 436
    invoke-static/range {v7 .. v37}, Lorg/xbet/uikit/compose/utils/a;->f(Ljava/lang/String;Landroidx/compose/ui/l;JLjava/util/List;Lorg/xbet/uikit/compose/utils/SuggestedFontSizesStatus;JJJLandroidx/compose/ui/text/font/t;Landroidx/compose/ui/text/font/y;Landroidx/compose/ui/text/font/j;JLandroidx/compose/ui/text/style/j;Landroidx/compose/ui/e;IZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/a0;FLandroidx/compose/runtime/j;IIII)V

    .line 437
    .line 438
    .line 439
    move-object/from16 v7, v33

    .line 440
    .line 441
    sget-object v8, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 442
    .line 443
    invoke-virtual {v3}, LA11/a;->t1()F

    .line 444
    .line 445
    .line 446
    move-result v10

    .line 447
    const/16 v13, 0xd

    .line 448
    .line 449
    const/4 v14, 0x0

    .line 450
    const/4 v9, 0x0

    .line 451
    const/4 v11, 0x0

    .line 452
    const/4 v12, 0x0

    .line 453
    invoke-static/range {v8 .. v14}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 454
    .line 455
    .line 456
    move-result-object v9

    .line 457
    move-object/from16 v41, v8

    .line 458
    .line 459
    invoke-virtual {v5}, Landroidx/compose/ui/e$a;->e()Landroidx/compose/ui/e;

    .line 460
    .line 461
    .line 462
    move-result-object v25

    .line 463
    invoke-virtual {v3}, LA11/a;->Q1()J

    .line 464
    .line 465
    .line 466
    move-result-wide v15

    .line 467
    invoke-virtual {v3}, LA11/a;->S1()J

    .line 468
    .line 469
    .line 470
    move-result-wide v17

    .line 471
    invoke-virtual/range {v38 .. v38}, LC11/a;->j()Landroidx/compose/ui/text/a0;

    .line 472
    .line 473
    .line 474
    move-result-object v8

    .line 475
    invoke-static {v8, v7, v0}, LB11/a;->d(Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/text/a0;

    .line 476
    .line 477
    .line 478
    move-result-object v31

    .line 479
    invoke-virtual/range {v39 .. v39}, Landroidx/compose/ui/text/style/s$a;->b()I

    .line 480
    .line 481
    .line 482
    move-result v26

    .line 483
    shr-int/lit8 v8, v40, 0x3

    .line 484
    .line 485
    and-int/lit8 v34, v8, 0xe

    .line 486
    .line 487
    const v37, 0x169f3c

    .line 488
    .line 489
    .line 490
    move-object v8, v9

    .line 491
    const-wide/16 v9, 0x0

    .line 492
    .line 493
    const/4 v11, 0x0

    .line 494
    const/4 v12, 0x0

    .line 495
    const-wide/16 v13, 0x0

    .line 496
    .line 497
    const/16 v28, 0x4

    .line 498
    .line 499
    move-object v7, v1

    .line 500
    invoke-static/range {v7 .. v37}, Lorg/xbet/uikit/compose/utils/a;->f(Ljava/lang/String;Landroidx/compose/ui/l;JLjava/util/List;Lorg/xbet/uikit/compose/utils/SuggestedFontSizesStatus;JJJLandroidx/compose/ui/text/font/t;Landroidx/compose/ui/text/font/y;Landroidx/compose/ui/text/font/j;JLandroidx/compose/ui/text/style/j;Landroidx/compose/ui/e;IZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/a0;FLandroidx/compose/runtime/j;IIII)V

    .line 501
    .line 502
    .line 503
    move-object/from16 v7, v33

    .line 504
    .line 505
    invoke-interface {v2}, Ljava/lang/CharSequence;->length()I

    .line 506
    .line 507
    .line 508
    move-result v1

    .line 509
    if-lez v1, :cond_17

    .line 510
    .line 511
    invoke-virtual {v3}, LA11/a;->t1()F

    .line 512
    .line 513
    .line 514
    move-result v10

    .line 515
    const/16 v13, 0xd

    .line 516
    .line 517
    const/4 v14, 0x0

    .line 518
    const/4 v9, 0x0

    .line 519
    const/4 v11, 0x0

    .line 520
    const/4 v12, 0x0

    .line 521
    move-object/from16 v8, v41

    .line 522
    .line 523
    invoke-static/range {v8 .. v14}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 524
    .line 525
    .line 526
    move-result-object v8

    .line 527
    invoke-virtual {v5}, Landroidx/compose/ui/e$a;->e()Landroidx/compose/ui/e;

    .line 528
    .line 529
    .line 530
    move-result-object v25

    .line 531
    invoke-virtual {v3}, LA11/a;->P1()J

    .line 532
    .line 533
    .line 534
    move-result-wide v15

    .line 535
    invoke-virtual {v3}, LA11/a;->Q1()J

    .line 536
    .line 537
    .line 538
    move-result-wide v17

    .line 539
    invoke-virtual/range {v38 .. v38}, LC11/a;->g()Landroidx/compose/ui/text/a0;

    .line 540
    .line 541
    .line 542
    move-result-object v1

    .line 543
    invoke-static {v1, v7, v0}, LB11/a;->b(Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/text/a0;

    .line 544
    .line 545
    .line 546
    move-result-object v31

    .line 547
    invoke-virtual/range {v39 .. v39}, Landroidx/compose/ui/text/style/s$a;->b()I

    .line 548
    .line 549
    .line 550
    move-result v26

    .line 551
    shr-int/lit8 v0, v40, 0x6

    .line 552
    .line 553
    and-int/lit8 v34, v0, 0xe

    .line 554
    .line 555
    const/16 v36, 0x0

    .line 556
    .line 557
    const v37, 0x169f3c

    .line 558
    .line 559
    .line 560
    const-wide/16 v9, 0x0

    .line 561
    .line 562
    const/4 v11, 0x0

    .line 563
    const/4 v12, 0x0

    .line 564
    const-wide/16 v13, 0x0

    .line 565
    .line 566
    const/16 v19, 0x0

    .line 567
    .line 568
    const/16 v20, 0x0

    .line 569
    .line 570
    const/16 v21, 0x0

    .line 571
    .line 572
    const-wide/16 v22, 0x0

    .line 573
    .line 574
    const/16 v24, 0x0

    .line 575
    .line 576
    const/16 v27, 0x0

    .line 577
    .line 578
    const/16 v28, 0x1

    .line 579
    .line 580
    const/16 v29, 0x0

    .line 581
    .line 582
    const/16 v30, 0x0

    .line 583
    .line 584
    const/16 v32, 0x0

    .line 585
    .line 586
    const v35, 0x186c00

    .line 587
    .line 588
    .line 589
    move-object/from16 v33, v7

    .line 590
    .line 591
    move-object v7, v2

    .line 592
    invoke-static/range {v7 .. v37}, Lorg/xbet/uikit/compose/utils/a;->f(Ljava/lang/String;Landroidx/compose/ui/l;JLjava/util/List;Lorg/xbet/uikit/compose/utils/SuggestedFontSizesStatus;JJJLandroidx/compose/ui/text/font/t;Landroidx/compose/ui/text/font/y;Landroidx/compose/ui/text/font/j;JLandroidx/compose/ui/text/style/j;Landroidx/compose/ui/e;IZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/a0;FLandroidx/compose/runtime/j;IIII)V

    .line 593
    .line 594
    .line 595
    move-object/from16 v7, v33

    .line 596
    .line 597
    :cond_17
    invoke-interface {v7}, Landroidx/compose/runtime/j;->q()V

    .line 598
    .line 599
    .line 600
    goto :goto_d

    .line 601
    :cond_18
    move-object/from16 p4, v0

    .line 602
    .line 603
    const/4 v0, 0x0

    .line 604
    const v1, -0x71a72ee1

    .line 605
    .line 606
    .line 607
    invoke-interface {v7, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 608
    .line 609
    .line 610
    const/4 v1, 0x0

    .line 611
    const/4 v2, 0x1

    .line 612
    invoke-static {v1, v7, v0, v2}, LKN0/i;->b(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 613
    .line 614
    .line 615
    invoke-interface {v7}, Landroidx/compose/runtime/j;->q()V

    .line 616
    .line 617
    .line 618
    :goto_d
    invoke-interface {v7}, Landroidx/compose/runtime/j;->j()V

    .line 619
    .line 620
    .line 621
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 622
    .line 623
    .line 624
    move-result v0

    .line 625
    if-eqz v0, :cond_19

    .line 626
    .line 627
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 628
    .line 629
    .line 630
    :cond_19
    move-object/from16 v5, p4

    .line 631
    .line 632
    :goto_e
    invoke-interface {v7}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 633
    .line 634
    .line 635
    move-result-object v8

    .line 636
    if-eqz v8, :cond_1a

    .line 637
    .line 638
    new-instance v0, LIN0/j;

    .line 639
    .line 640
    move-object/from16 v1, p0

    .line 641
    .line 642
    move-object/from16 v2, p1

    .line 643
    .line 644
    move-object/from16 v3, p2

    .line 645
    .line 646
    move/from16 v7, p7

    .line 647
    .line 648
    invoke-direct/range {v0 .. v7}, LIN0/j;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;II)V

    .line 649
    .line 650
    .line 651
    invoke-interface {v8, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 652
    .line 653
    .line 654
    :cond_1a
    return-void
.end method

.method public static final d(Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V
    .locals 18
    .param p0    # Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move/from16 v1, p3

    .line 4
    .line 5
    move/from16 v2, p4

    .line 6
    .line 7
    const v3, 0x53ee81c9

    .line 8
    .line 9
    .line 10
    move-object/from16 v4, p2

    .line 11
    .line 12
    invoke-interface {v4, v3}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 13
    .line 14
    .line 15
    move-result-object v7

    .line 16
    and-int/lit8 v4, v2, 0x1

    .line 17
    .line 18
    const/4 v10, 0x2

    .line 19
    if-eqz v4, :cond_0

    .line 20
    .line 21
    or-int/lit8 v4, v1, 0x6

    .line 22
    .line 23
    goto :goto_2

    .line 24
    :cond_0
    and-int/lit8 v4, v1, 0x6

    .line 25
    .line 26
    if-nez v4, :cond_3

    .line 27
    .line 28
    and-int/lit8 v4, v1, 0x8

    .line 29
    .line 30
    if-nez v4, :cond_1

    .line 31
    .line 32
    invoke-interface {v7, v0}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 33
    .line 34
    .line 35
    move-result v4

    .line 36
    goto :goto_0

    .line 37
    :cond_1
    invoke-interface {v7, v0}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 38
    .line 39
    .line 40
    move-result v4

    .line 41
    :goto_0
    if-eqz v4, :cond_2

    .line 42
    .line 43
    const/4 v4, 0x4

    .line 44
    goto :goto_1

    .line 45
    :cond_2
    const/4 v4, 0x2

    .line 46
    :goto_1
    or-int/2addr v4, v1

    .line 47
    goto :goto_2

    .line 48
    :cond_3
    move v4, v1

    .line 49
    :goto_2
    and-int/lit8 v5, v2, 0x2

    .line 50
    .line 51
    if-eqz v5, :cond_5

    .line 52
    .line 53
    or-int/lit8 v4, v4, 0x30

    .line 54
    .line 55
    :cond_4
    move-object/from16 v6, p1

    .line 56
    .line 57
    goto :goto_4

    .line 58
    :cond_5
    and-int/lit8 v6, v1, 0x30

    .line 59
    .line 60
    if-nez v6, :cond_4

    .line 61
    .line 62
    move-object/from16 v6, p1

    .line 63
    .line 64
    invoke-interface {v7, v6}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 65
    .line 66
    .line 67
    move-result v8

    .line 68
    if-eqz v8, :cond_6

    .line 69
    .line 70
    const/16 v8, 0x20

    .line 71
    .line 72
    goto :goto_3

    .line 73
    :cond_6
    const/16 v8, 0x10

    .line 74
    .line 75
    :goto_3
    or-int/2addr v4, v8

    .line 76
    :goto_4
    and-int/lit8 v8, v4, 0x13

    .line 77
    .line 78
    const/16 v9, 0x12

    .line 79
    .line 80
    if-ne v8, v9, :cond_8

    .line 81
    .line 82
    invoke-interface {v7}, Landroidx/compose/runtime/j;->c()Z

    .line 83
    .line 84
    .line 85
    move-result v8

    .line 86
    if-nez v8, :cond_7

    .line 87
    .line 88
    goto :goto_5

    .line 89
    :cond_7
    invoke-interface {v7}, Landroidx/compose/runtime/j;->n()V

    .line 90
    .line 91
    .line 92
    move-object v11, v6

    .line 93
    goto/16 :goto_9

    .line 94
    .line 95
    :cond_8
    :goto_5
    if-eqz v5, :cond_9

    .line 96
    .line 97
    sget-object v5, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 98
    .line 99
    move-object v11, v5

    .line 100
    goto :goto_6

    .line 101
    :cond_9
    move-object v11, v6

    .line 102
    :goto_6
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 103
    .line 104
    .line 105
    move-result v5

    .line 106
    if-eqz v5, :cond_a

    .line 107
    .line 108
    const/4 v5, -0x1

    .line 109
    const-string v6, "org.xbet.statistic.statistic_core.presentation.composable.ListWidgetsHeaderComponent (ListWidgetsHeaderComponent.kt:36)"

    .line 110
    .line 111
    invoke-static {v3, v4, v5, v6}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 112
    .line 113
    .line 114
    :cond_a
    sget-object v3, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 115
    .line 116
    invoke-virtual {v3}, Landroidx/compose/ui/e$a;->g()Landroidx/compose/ui/e$b;

    .line 117
    .line 118
    .line 119
    move-result-object v4

    .line 120
    const/4 v12, 0x0

    .line 121
    invoke-static {v7, v12}, LA11/d;->c(Landroidx/compose/runtime/j;I)LA11/b;

    .line 122
    .line 123
    .line 124
    move-result-object v5

    .line 125
    invoke-virtual {v5}, LA11/b;->a()F

    .line 126
    .line 127
    .line 128
    move-result v5

    .line 129
    const/4 v6, 0x0

    .line 130
    const/4 v13, 0x0

    .line 131
    invoke-static {v11, v5, v6, v10, v13}, Landroidx/compose/foundation/layout/PaddingKt;->k(Landroidx/compose/ui/l;FFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 132
    .line 133
    .line 134
    move-result-object v5

    .line 135
    sget-object v6, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 136
    .line 137
    invoke-virtual {v6}, Landroidx/compose/foundation/layout/Arrangement;->h()Landroidx/compose/foundation/layout/Arrangement$m;

    .line 138
    .line 139
    .line 140
    move-result-object v6

    .line 141
    const/16 v8, 0x30

    .line 142
    .line 143
    invoke-static {v6, v4, v7, v8}, Landroidx/compose/foundation/layout/k;->a(Landroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 144
    .line 145
    .line 146
    move-result-object v4

    .line 147
    invoke-static {v7, v12}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 148
    .line 149
    .line 150
    move-result v6

    .line 151
    invoke-interface {v7}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 152
    .line 153
    .line 154
    move-result-object v8

    .line 155
    invoke-static {v7, v5}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 156
    .line 157
    .line 158
    move-result-object v5

    .line 159
    sget-object v9, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 160
    .line 161
    invoke-virtual {v9}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 162
    .line 163
    .line 164
    move-result-object v14

    .line 165
    invoke-interface {v7}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 166
    .line 167
    .line 168
    move-result-object v15

    .line 169
    invoke-static {v15}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 170
    .line 171
    .line 172
    move-result v15

    .line 173
    if-nez v15, :cond_b

    .line 174
    .line 175
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 176
    .line 177
    .line 178
    :cond_b
    invoke-interface {v7}, Landroidx/compose/runtime/j;->l()V

    .line 179
    .line 180
    .line 181
    invoke-interface {v7}, Landroidx/compose/runtime/j;->B()Z

    .line 182
    .line 183
    .line 184
    move-result v15

    .line 185
    if-eqz v15, :cond_c

    .line 186
    .line 187
    invoke-interface {v7, v14}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 188
    .line 189
    .line 190
    goto :goto_7

    .line 191
    :cond_c
    invoke-interface {v7}, Landroidx/compose/runtime/j;->h()V

    .line 192
    .line 193
    .line 194
    :goto_7
    invoke-static {v7}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 195
    .line 196
    .line 197
    move-result-object v14

    .line 198
    invoke-virtual {v9}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 199
    .line 200
    .line 201
    move-result-object v15

    .line 202
    invoke-static {v14, v4, v15}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 203
    .line 204
    .line 205
    invoke-virtual {v9}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 206
    .line 207
    .line 208
    move-result-object v4

    .line 209
    invoke-static {v14, v8, v4}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 210
    .line 211
    .line 212
    invoke-virtual {v9}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 213
    .line 214
    .line 215
    move-result-object v4

    .line 216
    invoke-interface {v14}, Landroidx/compose/runtime/j;->B()Z

    .line 217
    .line 218
    .line 219
    move-result v8

    .line 220
    if-nez v8, :cond_d

    .line 221
    .line 222
    invoke-interface {v14}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 223
    .line 224
    .line 225
    move-result-object v8

    .line 226
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 227
    .line 228
    .line 229
    move-result-object v15

    .line 230
    invoke-static {v8, v15}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 231
    .line 232
    .line 233
    move-result v8

    .line 234
    if-nez v8, :cond_e

    .line 235
    .line 236
    :cond_d
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 237
    .line 238
    .line 239
    move-result-object v8

    .line 240
    invoke-interface {v14, v8}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 241
    .line 242
    .line 243
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 244
    .line 245
    .line 246
    move-result-object v6

    .line 247
    invoke-interface {v14, v6, v4}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 248
    .line 249
    .line 250
    :cond_e
    invoke-virtual {v9}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 251
    .line 252
    .line 253
    move-result-object v4

    .line 254
    invoke-static {v14, v5, v4}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 255
    .line 256
    .line 257
    sget-object v14, Landroidx/compose/foundation/layout/n;->a:Landroidx/compose/foundation/layout/n;

    .line 258
    .line 259
    instance-of v4, v0, Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b$c;

    .line 260
    .line 261
    if-eqz v4, :cond_f

    .line 262
    .line 263
    const v4, -0x32f942db    # -1.4128392E8f

    .line 264
    .line 265
    .line 266
    invoke-interface {v7, v4}, Landroidx/compose/runtime/j;->t(I)V

    .line 267
    .line 268
    .line 269
    move-object v15, v0

    .line 270
    check-cast v15, Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b$c;

    .line 271
    .line 272
    invoke-virtual {v15}, Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b$c;->a()LNN0/n;

    .line 273
    .line 274
    .line 275
    move-result-object v4

    .line 276
    invoke-virtual {v4}, LNN0/n;->a()Ljava/lang/String;

    .line 277
    .line 278
    .line 279
    move-result-object v4

    .line 280
    invoke-virtual {v15}, Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b$c;->a()LNN0/n;

    .line 281
    .line 282
    .line 283
    move-result-object v5

    .line 284
    invoke-virtual {v5}, LNN0/n;->k()Ljava/lang/String;

    .line 285
    .line 286
    .line 287
    move-result-object v5

    .line 288
    sget-object v6, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 289
    .line 290
    invoke-virtual {v3}, Landroidx/compose/ui/e$a;->g()Landroidx/compose/ui/e$b;

    .line 291
    .line 292
    .line 293
    move-result-object v8

    .line 294
    invoke-interface {v14, v6, v8}, Landroidx/compose/foundation/layout/m;->b(Landroidx/compose/ui/l;Landroidx/compose/ui/e$b;)Landroidx/compose/ui/l;

    .line 295
    .line 296
    .line 297
    move-result-object v8

    .line 298
    move-object v9, v6

    .line 299
    move-object v6, v8

    .line 300
    const/4 v8, 0x0

    .line 301
    move-object/from16 v16, v9

    .line 302
    .line 303
    const/4 v9, 0x0

    .line 304
    move-object/from16 v17, v16

    .line 305
    .line 306
    invoke-static/range {v4 .. v9}, LKN0/d;->b(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 307
    .line 308
    .line 309
    invoke-virtual {v15}, Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b$c;->a()LNN0/n;

    .line 310
    .line 311
    .line 312
    move-result-object v4

    .line 313
    invoke-static {v4, v13, v7, v12, v10}, LKN0/s;->b(LNN0/n;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 314
    .line 315
    .line 316
    invoke-virtual {v15}, Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b$c;->a()LNN0/n;

    .line 317
    .line 318
    .line 319
    move-result-object v4

    .line 320
    invoke-virtual {v4}, LNN0/n;->b()LNN0/i;

    .line 321
    .line 322
    .line 323
    move-result-object v4

    .line 324
    invoke-virtual {v3}, Landroidx/compose/ui/e$a;->g()Landroidx/compose/ui/e$b;

    .line 325
    .line 326
    .line 327
    move-result-object v3

    .line 328
    move-object/from16 v9, v17

    .line 329
    .line 330
    invoke-interface {v14, v9, v3}, Landroidx/compose/foundation/layout/m;->b(Landroidx/compose/ui/l;Landroidx/compose/ui/e$b;)Landroidx/compose/ui/l;

    .line 331
    .line 332
    .line 333
    move-result-object v3

    .line 334
    invoke-static {v4, v3, v7, v12, v12}, LKN0/b;->b(LNN0/i;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 335
    .line 336
    .line 337
    invoke-interface {v7}, Landroidx/compose/runtime/j;->q()V

    .line 338
    .line 339
    .line 340
    goto :goto_8

    .line 341
    :cond_f
    const v3, -0x32f1ab6c

    .line 342
    .line 343
    .line 344
    invoke-interface {v7, v3}, Landroidx/compose/runtime/j;->t(I)V

    .line 345
    .line 346
    .line 347
    const/4 v3, 0x1

    .line 348
    invoke-static {v13, v7, v12, v3}, LKN0/i;->b(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 349
    .line 350
    .line 351
    invoke-interface {v7}, Landroidx/compose/runtime/j;->q()V

    .line 352
    .line 353
    .line 354
    :goto_8
    invoke-interface {v7}, Landroidx/compose/runtime/j;->j()V

    .line 355
    .line 356
    .line 357
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 358
    .line 359
    .line 360
    move-result v3

    .line 361
    if-eqz v3, :cond_10

    .line 362
    .line 363
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 364
    .line 365
    .line 366
    :cond_10
    :goto_9
    invoke-interface {v7}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 367
    .line 368
    .line 369
    move-result-object v3

    .line 370
    if-eqz v3, :cond_11

    .line 371
    .line 372
    new-instance v4, LIN0/i;

    .line 373
    .line 374
    invoke-direct {v4, v0, v11, v1, v2}, LIN0/i;-><init>(Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;Landroidx/compose/ui/l;II)V

    .line 375
    .line 376
    .line 377
    invoke-interface {v3, v4}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 378
    .line 379
    .line 380
    :cond_11
    return-void
.end method

.method public static final e(Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    or-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    invoke-static {p2}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    invoke-static {p0, p1, p4, p2, p3}, LIN0/k;->d(Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method

.method public static final f(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 8

    .line 1
    or-int/lit8 p5, p5, 0x1

    .line 2
    .line 3
    invoke-static {p5}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result v6

    .line 7
    move-object v0, p0

    .line 8
    move-object v1, p1

    .line 9
    move-object v2, p2

    .line 10
    move v3, p3

    .line 11
    move-object v4, p4

    .line 12
    move v7, p6

    .line 13
    move-object v5, p7

    .line 14
    invoke-static/range {v0 .. v7}, LIN0/k;->c(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 15
    .line 16
    .line 17
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 18
    .line 19
    return-object p0
.end method
