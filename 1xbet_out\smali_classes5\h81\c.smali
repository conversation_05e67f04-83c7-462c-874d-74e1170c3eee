.class public final Lh81/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0003\u001a!\u0010\u0006\u001a\u00020\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;",
        "Lorg/xplatform/aggregator/api/navigation/TournamentsPage;",
        "page",
        "Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;",
        "buttonStatus",
        "",
        "a",
        "(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;)Z",
        "api_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;)Z
    .locals 2
    .param p0    # Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xplatform/aggregator/api/navigation/TournamentsPage;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;->NotActive:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-ne p2, v0, :cond_0

    .line 5
    .line 6
    return v1

    .line 7
    :cond_0
    sget-object p2, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->Games:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 8
    .line 9
    if-eq p0, p2, :cond_1

    .line 10
    .line 11
    sget-object p2, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->Game:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 12
    .line 13
    if-ne p0, p2, :cond_2

    .line 14
    .line 15
    :cond_1
    sget-object p0, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->GAMES:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 16
    .line 17
    if-eq p1, p0, :cond_3

    .line 18
    .line 19
    :cond_2
    const/4 p0, 0x1

    .line 20
    return p0

    .line 21
    :cond_3
    return v1
.end method
