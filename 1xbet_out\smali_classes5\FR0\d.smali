.class public final synthetic LFR0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Ltc1/a;


# direct methods
.method public synthetic constructor <init>(Ltc1/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LFR0/d;->a:Ltc1/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LFR0/d;->a:Ltc1/a;

    check-cast p1, LNN0/h;

    invoke-static {v0, p1}, LFR0/b$b;->c(Ltc1/a;LNN0/h;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
