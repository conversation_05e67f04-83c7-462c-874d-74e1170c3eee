.class public interface abstract LE91/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000b\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0015\u0008f\u0018\u00002\u00020\u0001JU\u0010\u0011\u001a\u0008\u0012\u0004\u0012\u00020\u00100\u000f2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\u0004H&\u00a2\u0006\u0004\u0008\u0011\u0010\u0012Jk\u0010\u0018\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00170\u00160\u000f2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\u0013\u001a\u00020\u000b2\u0006\u0010\u0014\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\u00042\u0006\u0010\u0015\u001a\u00020\u0004H&\u00a2\u0006\u0004\u0008\u0018\u0010\u0019Jk\u0010\u001a\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00170\u00160\u000f2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\u0013\u001a\u00020\u000b2\u0006\u0010\u0014\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\u00042\u0006\u0010\u0015\u001a\u00020\u0004H&\u00a2\u0006\u0004\u0008\u001a\u0010\u0019J%\u0010\u001c\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u000f2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u000e\u001a\u00020\u0004H&\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJS\u0010\u001e\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00170\u00160\u000f2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004H&\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ#\u0010!\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020 0\u00160\u000f2\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008!\u0010\"J#\u0010$\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020#0\u00160\u000f2\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008$\u0010\"J\u0017\u0010\'\u001a\u00020&2\u0006\u0010%\u001a\u00020\u0010H&\u00a2\u0006\u0004\u0008\'\u0010(J\u0017\u0010*\u001a\u00020&2\u0006\u0010)\u001a\u00020\u001bH&\u00a2\u0006\u0004\u0008*\u0010+J\u000f\u0010,\u001a\u00020&H&\u00a2\u0006\u0004\u0008,\u0010-J\u0017\u0010.\u001a\u00020&2\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008.\u0010/J\u0017\u00101\u001a\u00020&2\u0006\u00100\u001a\u00020\u0010H&\u00a2\u0006\u0004\u00081\u0010(J\u000f\u00102\u001a\u00020&H&\u00a2\u0006\u0004\u00082\u0010-J\u000f\u00103\u001a\u00020&H&\u00a2\u0006\u0004\u00083\u0010-J\u000f\u00104\u001a\u00020\u0004H&\u00a2\u0006\u0004\u00084\u00105J\u0017\u00107\u001a\u00020&2\u0006\u00106\u001a\u00020\u0004H&\u00a2\u0006\u0004\u00087\u00108J\u000f\u00109\u001a\u00020\u0004H&\u00a2\u0006\u0004\u00089\u00105J\u000f\u0010:\u001a\u00020&H&\u00a2\u0006\u0004\u0008:\u0010-\u00a8\u0006;"
    }
    d2 = {
        "LE91/a;",
        "",
        "",
        "partitionId",
        "",
        "nightMode",
        "",
        "limit",
        "skip",
        "Lorg/xplatform/aggregator/api/model/ProductSortType;",
        "sortType",
        "",
        "searchSubstr",
        "fromPopular",
        "test",
        "Lkotlinx/coroutines/flow/e;",
        "LD91/a;",
        "j",
        "(JZIILorg/xplatform/aggregator/api/model/ProductSortType;Ljava/lang/String;ZZ)Lkotlinx/coroutines/flow/e;",
        "brandsId",
        "hasAggregatorBrandsFullInfo",
        "needGamesCount",
        "",
        "Lg81/j;",
        "e",
        "(JZIILjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZZ)Lkotlinx/coroutines/flow/e;",
        "l",
        "LB91/h;",
        "n",
        "(JZ)Lkotlinx/coroutines/flow/e;",
        "i",
        "(JLjava/lang/String;Ljava/lang/String;IIZZ)Lkotlinx/coroutines/flow/e;",
        "LD91/b;",
        "f",
        "(J)Lkotlinx/coroutines/flow/e;",
        "LD91/e;",
        "k",
        "filtersModel",
        "",
        "p",
        "(LD91/a;)V",
        "categories",
        "o",
        "(LB91/h;)V",
        "r",
        "()V",
        "c",
        "(J)V",
        "cache",
        "a",
        "q",
        "g",
        "d",
        "()Z",
        "hasFilterType",
        "h",
        "(Z)V",
        "b",
        "m",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(LD91/a;)V
    .param p1    # LD91/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract b()Z
.end method

.method public abstract c(J)V
.end method

.method public abstract d()Z
.end method

.method public abstract e(JZIILjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZZ)Lkotlinx/coroutines/flow/e;
    .param p6    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JZII",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "ZZZ)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Lg81/j;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract f(J)Lkotlinx/coroutines/flow/e;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "LD91/b;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract g()V
.end method

.method public abstract h(Z)V
.end method

.method public abstract i(JLjava/lang/String;Ljava/lang/String;IIZZ)Lkotlinx/coroutines/flow/e;
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "IIZZ)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Lg81/j;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract j(JZIILorg/xplatform/aggregator/api/model/ProductSortType;Ljava/lang/String;ZZ)Lkotlinx/coroutines/flow/e;
    .param p6    # Lorg/xplatform/aggregator/api/model/ProductSortType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JZII",
            "Lorg/xplatform/aggregator/api/model/ProductSortType;",
            "Ljava/lang/String;",
            "ZZ)",
            "Lkotlinx/coroutines/flow/e<",
            "LD91/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract k(J)Lkotlinx/coroutines/flow/e;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "LD91/e;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract l(JZIILjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZZ)Lkotlinx/coroutines/flow/e;
    .param p6    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JZII",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "ZZZ)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Lg81/j;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract m()V
.end method

.method public abstract n(JZ)Lkotlinx/coroutines/flow/e;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JZ)",
            "Lkotlinx/coroutines/flow/e<",
            "LB91/h;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract o(LB91/h;)V
    .param p1    # LB91/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract p(LD91/a;)V
    .param p1    # LD91/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract q()V
.end method

.method public abstract r()V
.end method
