.class final Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.main_menu.impl.presentation.list.line.ListLineItemsViewModel$onOneXGameClicked$2"
    f = "ListLineItemsViewModel.kt"
    l = {
        0x147
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;->I4(Ln41/m;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $gameCollectionItemModel:Ln41/m;

.field J$0:J

.field label:I

.field final synthetic this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;


# direct methods
.method public constructor <init>(Ln41/m;Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ln41/m;",
            "Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->$gameCollectionItemModel:Ln41/m;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    .line 4
    .line 5
    const/4 p1, 0x2

    .line 6
    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;

    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->$gameCollectionItemModel:Ln41/m;

    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;-><init>(Ln41/m;Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 13

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    iget-wide v0, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->J$0:J

    .line 13
    .line 14
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    move-wide v6, v0

    .line 18
    goto :goto_0

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->$gameCollectionItemModel:Ln41/m;

    .line 31
    .line 32
    invoke-virtual {p1}, Ln41/m;->e()J

    .line 33
    .line 34
    .line 35
    move-result-wide v3

    .line 36
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    .line 37
    .line 38
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;->N3(Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;)LDg/c;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->MenuOneXGames:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 43
    .line 44
    invoke-virtual {p1, v3, v4, v1}, LDg/c;->p(JLorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;)V

    .line 45
    .line 46
    .line 47
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    .line 48
    .line 49
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;->O3(Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;)LpS/b;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    long-to-int v1, v3

    .line 54
    sget-object v5, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->MENU_ONE_XGAMES:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 55
    .line 56
    const-string v6, "ListLineItemsFragment"

    .line 57
    .line 58
    invoke-interface {p1, v6, v1, v5}, LpS/b;->e(Ljava/lang/String;ILorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;)V

    .line 59
    .line 60
    .line 61
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    .line 62
    .line 63
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;->y3(Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;)LJT/c;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    iput-wide v3, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->J$0:J

    .line 68
    .line 69
    iput v2, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->label:I

    .line 70
    .line 71
    invoke-interface {p1, v3, v4, p0}, LJT/c;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    if-ne p1, v0, :cond_2

    .line 76
    .line 77
    return-object v0

    .line 78
    :cond_2
    move-wide v6, v3

    .line 79
    :goto_0
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    .line 80
    .line 81
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;->Q3(Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;)LwX0/c;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel$onOneXGameClicked$2;->this$0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;

    .line 86
    .line 87
    invoke-static {v0}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;->G3(Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsViewModel;)Lt30/b;

    .line 88
    .line 89
    .line 90
    move-result-object v5

    .line 91
    const/16 v11, 0xe

    .line 92
    .line 93
    const/4 v12, 0x0

    .line 94
    const/4 v8, 0x0

    .line 95
    const/4 v9, 0x0

    .line 96
    const/4 v10, 0x0

    .line 97
    invoke-static/range {v5 .. v12}, Lt30/b$a;->b(Lt30/b;JLorg/xbet/games_section/api/models/OneXGamesPromoType;ILorg/xbet/games_section/api/models/OneXGamesScreenType;ILjava/lang/Object;)LwX0/B;

    .line 98
    .line 99
    .line 100
    move-result-object v0

    .line 101
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 102
    .line 103
    .line 104
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 105
    .line 106
    return-object p1
.end method
