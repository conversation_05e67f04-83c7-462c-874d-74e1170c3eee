.class public final LmQ0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LmQ0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LmQ0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LmQ0/a$b$a;
    }
.end annotation


# instance fields
.field public final a:LmQ0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/l;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LjQ0/c;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LjQ0/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/tennis/impl/player_menu/data/repository/PlayerTennisMenuRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LpQ0/c;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LpQ0/a;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LpQ0/e;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/m;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public r:Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/b;

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LmQ0/h;",
            ">;"
        }
    .end annotation
.end field

.field public t:Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/a;

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LmQ0/f;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Ljava/lang/String;Ljava/lang/Boolean;LjQ0/a;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LHX0/e;Li8/l;Li8/m;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LmQ0/a$b;->a:LmQ0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p13}, LmQ0/a$b;->c(LQW0/c;Ljava/lang/String;Ljava/lang/Boolean;LjQ0/a;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LHX0/e;Li8/l;Li8/m;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Ljava/lang/String;Ljava/lang/Boolean;LjQ0/a;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LHX0/e;Li8/l;Li8/m;Lc8/h;LmQ0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p13}, LmQ0/a$b;-><init>(LQW0/c;Ljava/lang/String;Ljava/lang/Boolean;LjQ0/a;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LHX0/e;Li8/l;Li8/m;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/tennis/impl/player_menu/presentation/fragment/PlayersStatisticFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LmQ0/a$b;->e(Lorg/xbet/statistic/tennis/impl/player_menu/presentation/fragment/PlayersStatisticFragment;)Lorg/xbet/statistic/tennis/impl/player_menu/presentation/fragment/PlayersStatisticFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/statistic/tennis/impl/player_menu/presentation/fragment/PlayersMenuFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LmQ0/a$b;->d(Lorg/xbet/statistic/tennis/impl/player_menu/presentation/fragment/PlayersMenuFragment;)Lorg/xbet/statistic/tennis/impl/player_menu/presentation/fragment/PlayersMenuFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final c(LQW0/c;Ljava/lang/String;Ljava/lang/Boolean;LjQ0/a;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LHX0/e;Li8/l;Li8/m;Lc8/h;)V
    .locals 0

    .line 1
    invoke-static {p2}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    iput-object p2, p0, LmQ0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    iput-object p2, p0, LmQ0/a$b;->c:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 14
    .line 15
    .line 16
    move-result-object p2

    .line 17
    iput-object p2, p0, LmQ0/a$b;->d:Ldagger/internal/h;

    .line 18
    .line 19
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    iput-object p2, p0, LmQ0/a$b;->e:Ldagger/internal/h;

    .line 24
    .line 25
    invoke-static {p2}, LjQ0/d;->a(LBc/a;)LjQ0/d;

    .line 26
    .line 27
    .line 28
    move-result-object p2

    .line 29
    iput-object p2, p0, LmQ0/a$b;->f:Ldagger/internal/h;

    .line 30
    .line 31
    invoke-static {p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 32
    .line 33
    .line 34
    move-result-object p2

    .line 35
    iput-object p2, p0, LmQ0/a$b;->g:Ldagger/internal/h;

    .line 36
    .line 37
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 38
    .line 39
    .line 40
    move-result-object p2

    .line 41
    iput-object p2, p0, LmQ0/a$b;->h:Ldagger/internal/h;

    .line 42
    .line 43
    new-instance p2, LmQ0/a$b$a;

    .line 44
    .line 45
    invoke-direct {p2, p1}, LmQ0/a$b$a;-><init>(LQW0/c;)V

    .line 46
    .line 47
    .line 48
    iput-object p2, p0, LmQ0/a$b;->i:Ldagger/internal/h;

    .line 49
    .line 50
    iget-object p1, p0, LmQ0/a$b;->f:Ldagger/internal/h;

    .line 51
    .line 52
    iget-object p3, p0, LmQ0/a$b;->g:Ldagger/internal/h;

    .line 53
    .line 54
    iget-object p4, p0, LmQ0/a$b;->h:Ldagger/internal/h;

    .line 55
    .line 56
    invoke-static {p1, p3, p4, p2}, Lorg/xbet/statistic/tennis/impl/player_menu/data/repository/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/tennis/impl/player_menu/data/repository/a;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    iput-object p1, p0, LmQ0/a$b;->j:Ldagger/internal/h;

    .line 61
    .line 62
    invoke-static {p1}, LpQ0/d;->a(LBc/a;)LpQ0/d;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    iput-object p1, p0, LmQ0/a$b;->k:Ldagger/internal/h;

    .line 67
    .line 68
    iget-object p1, p0, LmQ0/a$b;->j:Ldagger/internal/h;

    .line 69
    .line 70
    invoke-static {p1}, LpQ0/b;->a(LBc/a;)LpQ0/b;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    iput-object p1, p0, LmQ0/a$b;->l:Ldagger/internal/h;

    .line 75
    .line 76
    iget-object p1, p0, LmQ0/a$b;->j:Ldagger/internal/h;

    .line 77
    .line 78
    invoke-static {p1}, LpQ0/f;->a(LBc/a;)LpQ0/f;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    iput-object p1, p0, LmQ0/a$b;->m:Ldagger/internal/h;

    .line 83
    .line 84
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    iput-object p1, p0, LmQ0/a$b;->n:Ldagger/internal/h;

    .line 89
    .line 90
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    iput-object p1, p0, LmQ0/a$b;->o:Ldagger/internal/h;

    .line 95
    .line 96
    invoke-static {p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    iput-object p1, p0, LmQ0/a$b;->p:Ldagger/internal/h;

    .line 101
    .line 102
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 103
    .line 104
    .line 105
    move-result-object p12

    .line 106
    iput-object p12, p0, LmQ0/a$b;->q:Ldagger/internal/h;

    .line 107
    .line 108
    iget-object p2, p0, LmQ0/a$b;->b:Ldagger/internal/h;

    .line 109
    .line 110
    iget-object p3, p0, LmQ0/a$b;->c:Ldagger/internal/h;

    .line 111
    .line 112
    iget-object p4, p0, LmQ0/a$b;->d:Ldagger/internal/h;

    .line 113
    .line 114
    iget-object p5, p0, LmQ0/a$b;->k:Ldagger/internal/h;

    .line 115
    .line 116
    iget-object p6, p0, LmQ0/a$b;->l:Ldagger/internal/h;

    .line 117
    .line 118
    iget-object p7, p0, LmQ0/a$b;->m:Ldagger/internal/h;

    .line 119
    .line 120
    iget-object p8, p0, LmQ0/a$b;->n:Ldagger/internal/h;

    .line 121
    .line 122
    iget-object p9, p0, LmQ0/a$b;->o:Ldagger/internal/h;

    .line 123
    .line 124
    iget-object p10, p0, LmQ0/a$b;->i:Ldagger/internal/h;

    .line 125
    .line 126
    iget-object p11, p0, LmQ0/a$b;->p:Ldagger/internal/h;

    .line 127
    .line 128
    invoke-static/range {p2 .. p12}, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/b;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/b;

    .line 129
    .line 130
    .line 131
    move-result-object p1

    .line 132
    iput-object p1, p0, LmQ0/a$b;->r:Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/b;

    .line 133
    .line 134
    invoke-static {p1}, LmQ0/i;->c(Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/b;)Ldagger/internal/h;

    .line 135
    .line 136
    .line 137
    move-result-object p1

    .line 138
    iput-object p1, p0, LmQ0/a$b;->s:Ldagger/internal/h;

    .line 139
    .line 140
    iget-object p2, p0, LmQ0/a$b;->b:Ldagger/internal/h;

    .line 141
    .line 142
    iget-object p3, p0, LmQ0/a$b;->k:Ldagger/internal/h;

    .line 143
    .line 144
    iget-object p4, p0, LmQ0/a$b;->l:Ldagger/internal/h;

    .line 145
    .line 146
    iget-object p5, p0, LmQ0/a$b;->n:Ldagger/internal/h;

    .line 147
    .line 148
    iget-object p6, p0, LmQ0/a$b;->o:Ldagger/internal/h;

    .line 149
    .line 150
    iget-object p7, p0, LmQ0/a$b;->i:Ldagger/internal/h;

    .line 151
    .line 152
    iget-object p8, p0, LmQ0/a$b;->q:Ldagger/internal/h;

    .line 153
    .line 154
    invoke-static/range {p2 .. p8}, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/a;

    .line 155
    .line 156
    .line 157
    move-result-object p1

    .line 158
    iput-object p1, p0, LmQ0/a$b;->t:Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/a;

    .line 159
    .line 160
    invoke-static {p1}, LmQ0/g;->c(Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/a;)Ldagger/internal/h;

    .line 161
    .line 162
    .line 163
    move-result-object p1

    .line 164
    iput-object p1, p0, LmQ0/a$b;->u:Ldagger/internal/h;

    .line 165
    .line 166
    return-void
.end method

.method public final d(Lorg/xbet/statistic/tennis/impl/player_menu/presentation/fragment/PlayersMenuFragment;)Lorg/xbet/statistic/tennis/impl/player_menu/presentation/fragment/PlayersMenuFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LmQ0/a$b;->u:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LmQ0/f;

    .line 8
    .line 9
    invoke-static {p1, v0}, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/fragment/d;->a(Lorg/xbet/statistic/tennis/impl/player_menu/presentation/fragment/PlayersMenuFragment;LmQ0/f;)V

    .line 10
    .line 11
    .line 12
    return-object p1
.end method

.method public final e(Lorg/xbet/statistic/tennis/impl/player_menu/presentation/fragment/PlayersStatisticFragment;)Lorg/xbet/statistic/tennis/impl/player_menu/presentation/fragment/PlayersStatisticFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LmQ0/a$b;->s:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LmQ0/h;

    .line 8
    .line 9
    invoke-static {p1, v0}, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/fragment/i;->a(Lorg/xbet/statistic/tennis/impl/player_menu/presentation/fragment/PlayersStatisticFragment;LmQ0/h;)V

    .line 10
    .line 11
    .line 12
    return-object p1
.end method
