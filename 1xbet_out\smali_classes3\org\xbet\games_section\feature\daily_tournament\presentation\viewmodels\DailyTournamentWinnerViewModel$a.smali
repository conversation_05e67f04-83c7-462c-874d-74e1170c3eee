.class public interface abstract Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$a;,
        Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$b;,
        Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$c;,
        Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$d;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0008v\u0018\u00002\u00020\u0001:\u0004\u0002\u0003\u0004\u0005\u0082\u0001\u0004\u0006\u0007\u0008\t\u00a8\u0006\n"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;",
        "",
        "c",
        "d",
        "b",
        "a",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$a;",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$b;",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$c;",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$d;",
        "daily_tournament_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation
