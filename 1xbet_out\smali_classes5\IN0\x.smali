.class public final synthetic LIN0/x;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Ljava/util/List;

.field public final synthetic b:I


# direct methods
.method public synthetic constructor <init>(L<PERSON><PERSON>/util/List;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LIN0/x;->a:Ljava/util/List;

    iput p2, p0, LIN0/x;->b:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LIN0/x;->a:Ljava/util/List;

    iget v1, p0, LIN0/x;->b:I

    check-cast p1, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    invoke-static {v0, v1, p1}, LIN0/z;->a(<PERSON><PERSON><PERSON>/util/List;ILorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
