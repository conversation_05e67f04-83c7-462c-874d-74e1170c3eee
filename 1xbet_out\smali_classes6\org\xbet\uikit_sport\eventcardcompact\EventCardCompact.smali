.class public final Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000r\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0007\n\u0002\u0010\r\n\u0002\u0008\u001a\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008$\u0008\u0007\u0018\u00002\u00020\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\u000c\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u0011\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0017\u0010\u0013\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0012J\u000f\u0010\u0014\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u000f\u0010\u0016\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0015J\u001f\u0010\u001b\u001a\u00020\r2\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u001a\u001a\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0011\u0010\u001e\u001a\u0004\u0018\u00010\u001dH\u0016\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u000f\u0010 \u001a\u00020\rH\u0014\u00a2\u0006\u0004\u0008 \u0010\u0015J\u0017\u0010\"\u001a\u00020\r2\u0008\u0008\u0001\u0010!\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\"\u0010#J\u0017\u0010\"\u001a\u00020\r2\u0008\u0010%\u001a\u0004\u0018\u00010$\u00a2\u0006\u0004\u0008\"\u0010&J\u0015\u0010\"\u001a\u00020\r2\u0006\u0010(\u001a\u00020\'\u00a2\u0006\u0004\u0008\"\u0010)J\u001f\u0010\"\u001a\u00020\r2\u0006\u0010(\u001a\u00020\'2\u0008\u0010*\u001a\u0004\u0018\u00010$\u00a2\u0006\u0004\u0008\"\u0010+J\u0017\u0010,\u001a\u00020\r2\u0008\u0008\u0001\u0010!\u001a\u00020\u0006\u00a2\u0006\u0004\u0008,\u0010#J\u0017\u0010,\u001a\u00020\r2\u0008\u0010%\u001a\u0004\u0018\u00010$\u00a2\u0006\u0004\u0008,\u0010&J\u0015\u0010,\u001a\u00020\r2\u0006\u0010(\u001a\u00020\'\u00a2\u0006\u0004\u0008,\u0010)J\u001f\u0010,\u001a\u00020\r2\u0006\u0010(\u001a\u00020\'2\u0008\u0010*\u001a\u0004\u0018\u00010$\u00a2\u0006\u0004\u0008,\u0010+J\u0017\u0010.\u001a\u00020\r2\u0008\u0008\u0001\u0010-\u001a\u00020\u0006\u00a2\u0006\u0004\u0008.\u0010#J\u0017\u0010.\u001a\u00020\r2\u0008\u0010-\u001a\u0004\u0018\u00010/\u00a2\u0006\u0004\u0008.\u00100J\u0015\u00102\u001a\u00020\r2\u0006\u00101\u001a\u00020\n\u00a2\u0006\u0004\u00082\u0010\u0012J!\u00103\u001a\u00020\r2\u0008\u0008\u0001\u0010-\u001a\u00020\u00062\u0008\u0008\u0002\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u00083\u00104J!\u00103\u001a\u00020\r2\u0008\u0010-\u001a\u0004\u0018\u00010/2\u0008\u0008\u0002\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u00083\u00105J!\u00106\u001a\u00020\r2\u0008\u0008\u0001\u0010-\u001a\u00020\u00062\u0008\u0008\u0002\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u00086\u00104J!\u00106\u001a\u00020\r2\u0008\u0010-\u001a\u0004\u0018\u00010/2\u0008\u0008\u0002\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u00086\u00105J!\u00107\u001a\u00020\r2\u0008\u0008\u0001\u0010-\u001a\u00020\u00062\u0008\u0008\u0002\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u00087\u00104J\u001d\u0010:\u001a\u00020\r2\u0006\u00108\u001a\u00020\u00062\u0006\u00109\u001a\u00020\u0006\u00a2\u0006\u0004\u0008:\u0010;J!\u00107\u001a\u00020\r2\u0008\u0010-\u001a\u0004\u0018\u00010/2\u0008\u0008\u0002\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u00087\u00105J\u0017\u0010<\u001a\u00020\r2\u0008\u0008\u0001\u0010!\u001a\u00020\u0006\u00a2\u0006\u0004\u0008<\u0010#J\u0017\u0010<\u001a\u00020\r2\u0008\u0010%\u001a\u0004\u0018\u00010$\u00a2\u0006\u0004\u0008<\u0010&J\u0015\u0010<\u001a\u00020\r2\u0006\u0010(\u001a\u00020\'\u00a2\u0006\u0004\u0008<\u0010)J\u001f\u0010<\u001a\u00020\r2\u0006\u0010(\u001a\u00020\'2\u0008\u0010*\u001a\u0004\u0018\u00010$\u00a2\u0006\u0004\u0008<\u0010+J\u0017\u0010=\u001a\u00020\r2\u0008\u0008\u0001\u0010!\u001a\u00020\u0006\u00a2\u0006\u0004\u0008=\u0010#J\u0017\u0010=\u001a\u00020\r2\u0008\u0010%\u001a\u0004\u0018\u00010$\u00a2\u0006\u0004\u0008=\u0010&J\u0015\u0010=\u001a\u00020\r2\u0006\u0010(\u001a\u00020\'\u00a2\u0006\u0004\u0008=\u0010)J\u001f\u0010=\u001a\u00020\r2\u0006\u0010(\u001a\u00020\'2\u0008\u0010*\u001a\u0004\u0018\u00010$\u00a2\u0006\u0004\u0008=\u0010+J\u0017\u0010>\u001a\u00020\r2\u0008\u0008\u0001\u0010-\u001a\u00020\u0006\u00a2\u0006\u0004\u0008>\u0010#J\u0017\u0010>\u001a\u00020\r2\u0008\u0010-\u001a\u0004\u0018\u00010/\u00a2\u0006\u0004\u0008>\u00100J\u0015\u0010?\u001a\u00020\r2\u0006\u00101\u001a\u00020\n\u00a2\u0006\u0004\u0008?\u0010\u0012J!\u0010@\u001a\u00020\r2\u0008\u0008\u0001\u0010-\u001a\u00020\u00062\u0008\u0008\u0002\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u0008@\u00104J!\u0010@\u001a\u00020\r2\u0008\u0010-\u001a\u0004\u0018\u00010/2\u0008\u0008\u0002\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u0008@\u00105J!\u0010A\u001a\u00020\r2\u0008\u0008\u0001\u0010-\u001a\u00020\u00062\u0008\u0008\u0002\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u0008A\u00104J!\u0010A\u001a\u00020\r2\u0008\u0010-\u001a\u0004\u0018\u00010/2\u0008\u0008\u0002\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u0008A\u00105J!\u0010B\u001a\u00020\r2\u0008\u0008\u0001\u0010-\u001a\u00020\u00062\u0008\u0008\u0002\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u0008B\u00104J\u001d\u0010C\u001a\u00020\r2\u0006\u00108\u001a\u00020\u00062\u0006\u00109\u001a\u00020\u0006\u00a2\u0006\u0004\u0008C\u0010;J!\u0010B\u001a\u00020\r2\u0008\u0010-\u001a\u0004\u0018\u00010/2\u0008\u0008\u0002\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u0008B\u00105J\u0017\u0010D\u001a\u00020\r2\u0008\u0008\u0001\u0010-\u001a\u00020\u0006\u00a2\u0006\u0004\u0008D\u0010#J\u0017\u0010D\u001a\u00020\r2\u0008\u0010-\u001a\u0004\u0018\u00010/\u00a2\u0006\u0004\u0008D\u00100J\u0017\u0010E\u001a\u00020\r2\u0008\u0008\u0001\u0010-\u001a\u00020\u0006\u00a2\u0006\u0004\u0008E\u0010#J\u0017\u0010E\u001a\u00020\r2\u0008\u0010-\u001a\u0004\u0018\u00010/\u00a2\u0006\u0004\u0008E\u00100J\u0017\u0010F\u001a\u00020\r2\u0008\u0008\u0001\u0010-\u001a\u00020\u0006\u00a2\u0006\u0004\u0008F\u0010#J\u0017\u0010F\u001a\u00020\r2\u0008\u0010-\u001a\u0004\u0018\u00010/\u00a2\u0006\u0004\u0008F\u00100J\u0017\u0010G\u001a\u00020\r2\u0008\u0008\u0001\u0010-\u001a\u00020\u0006\u00a2\u0006\u0004\u0008G\u0010#J\u0017\u0010G\u001a\u00020\r2\u0008\u0010-\u001a\u0004\u0018\u00010/\u00a2\u0006\u0004\u0008G\u00100J\u0017\u0010H\u001a\u00020\r2\u0008\u0008\u0001\u0010-\u001a\u00020\u0006\u00a2\u0006\u0004\u0008H\u0010#J\u0017\u0010H\u001a\u00020\r2\u0008\u0010-\u001a\u0004\u0018\u00010/\u00a2\u0006\u0004\u0008H\u00100J\u0017\u0010I\u001a\u00020\r2\u0008\u0008\u0001\u0010-\u001a\u00020\u0006\u00a2\u0006\u0004\u0008I\u0010#J\u0017\u0010I\u001a\u00020\r2\u0008\u0010-\u001a\u0004\u0018\u00010/\u00a2\u0006\u0004\u0008I\u00100R\u001b\u0010O\u001a\u00020J8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008K\u0010L\u001a\u0004\u0008M\u0010NR\u0014\u0010S\u001a\u00020P8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Q\u0010RR\u001c\u0010X\u001a\n U*\u0004\u0018\u00010T0T8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008V\u0010WR\u0016\u0010[\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008Y\u0010ZR\u0016\u0010]\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\\\u0010ZR\u0016\u0010_\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008^\u0010ZR\u0016\u0010a\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008`\u0010ZR\u0016\u0010c\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008b\u0010ZR\u0016\u0010e\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008d\u0010ZR\u0016\u0010g\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008f\u0010ZR\u0016\u0010i\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008h\u0010ZR\u0016\u0010k\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008j\u0010ZR\u0016\u0010m\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008l\u0010ZR\u0016\u0010o\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008n\u0010ZR\u0016\u0010q\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008p\u0010ZR\u0016\u0010s\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008r\u0010ZR\u0016\u0010u\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008t\u0010ZR\u0016\u0010w\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008v\u0010Z\u00a8\u0006x"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "columnGroupVisible",
        "scoreGroupVisible",
        "",
        "y",
        "(ZZ)V",
        "visible",
        "x",
        "(Z)V",
        "z",
        "u",
        "()V",
        "t",
        "Landroid/widget/TextView;",
        "textView",
        "Lorg/xbet/uikit/core/eventcard/ScoreState;",
        "scoreState",
        "w",
        "(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V",
        "Landroid/content/res/ColorStateList;",
        "getBackgroundTintList",
        "()Landroid/content/res/ColorStateList;",
        "onDetachedFromWindow",
        "resId",
        "setTopFirstTeamLogo",
        "(I)V",
        "Landroid/graphics/drawable/Drawable;",
        "drawable",
        "(Landroid/graphics/drawable/Drawable;)V",
        "",
        "url",
        "(Ljava/lang/String;)V",
        "placeholder",
        "(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V",
        "setTopSecondTeamLogo",
        "text",
        "setTopTeamName",
        "",
        "(Ljava/lang/CharSequence;)V",
        "show",
        "setTopGameIndicator",
        "setFirstColumnTopScore",
        "(ILorg/xbet/uikit/core/eventcard/ScoreState;)V",
        "(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V",
        "setSecondColumnTopScore",
        "setThirdColumnTopScore",
        "totalCount",
        "winCount",
        "setTopVictoryIndicator",
        "(II)V",
        "setBotFirstTeamLogo",
        "setBotSecondTeamLogo",
        "setBotTeamName",
        "setBotGameIndicator",
        "setFirstColumnBotScore",
        "setSecondColumnBotScore",
        "setThirdColumnBotScore",
        "setBotVictoryIndicator",
        "setInfoText",
        "setTopScore",
        "setBotScore",
        "setGameText",
        "setSetText",
        "setResultText",
        "Lorg/xbet/uikit/utils/e;",
        "a",
        "Lkotlin/j;",
        "getBackgroundTintHelper",
        "()Lorg/xbet/uikit/utils/e;",
        "backgroundTintHelper",
        "LC31/k;",
        "b",
        "LC31/k;",
        "binding",
        "Landroid/view/animation/Animation;",
        "kotlin.jvm.PlatformType",
        "c",
        "Landroid/view/animation/Animation;",
        "rotateAnimation",
        "d",
        "Z",
        "topGameIndicatorVisible",
        "e",
        "botGameIndicatorVisible",
        "f",
        "firstColumnTopScoreVisible",
        "g",
        "firstColumnBotScoreVisible",
        "h",
        "secondColumnTopScoreVisible",
        "i",
        "secondColumnBotScoreVisible",
        "j",
        "thirdColumnTopScoreVisible",
        "k",
        "thirdColumnBotScoreVisible",
        "l",
        "gameTextVisible",
        "m",
        "setTextVisible",
        "n",
        "resultTextVisible",
        "o",
        "topVictoryIndicatorVisible",
        "p",
        "botVictoryIndicatorVisible",
        "q",
        "topScoreVisible",
        "r",
        "botScoreVisible",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LC31/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Landroid/view/animation/Animation;

.field public d:Z

.field public e:Z

.field public f:Z

.field public g:Z

.field public h:Z

.field public i:Z

.field public j:Z

.field public k:Z

.field public l:Z

.field public m:Z

.field public n:Z

.field public o:Z

.field public p:Z

.field public q:Z

.field public r:Z


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    new-instance v0, LF31/a;

    invoke-direct {v0, p0}, LF31/a;-><init>(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->a:Lkotlin/j;

    .line 7
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object v0

    invoke-static {v0, p0}, LC31/k;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/k;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 8
    sget v0, Lm31/a;->game_indicator_rotate_animation:I

    invoke-static {p1, v0}, Landroid/view/animation/AnimationUtils;->loadAnimation(Landroid/content/Context;I)Landroid/view/animation/Animation;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->c:Landroid/view/animation/Animation;

    .line 9
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    move-result-object p1

    invoke-virtual {p1, p2, p3}, Lorg/xbet/uikit/utils/e;->a(Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->eventCardCompactStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method private final getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->a:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/utils/e;

    .line 8
    .line 9
    return-object v0
.end method

.method public static synthetic s(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;)Lorg/xbet/uikit/utils/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->v(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;)Lorg/xbet/uikit/utils/e;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic setFirstColumnBotScore$default(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;ILorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 1
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setFirstColumnBotScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setFirstColumnBotScore$default(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 2
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setFirstColumnBotScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setFirstColumnTopScore$default(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;ILorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 1
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setFirstColumnTopScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setFirstColumnTopScore$default(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 2
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setFirstColumnTopScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setSecondColumnBotScore$default(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;ILorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 1
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setSecondColumnBotScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setSecondColumnBotScore$default(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 2
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setSecondColumnBotScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setSecondColumnTopScore$default(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;ILorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 1
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setSecondColumnTopScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setSecondColumnTopScore$default(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 2
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setSecondColumnTopScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setThirdColumnBotScore$default(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;ILorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 1
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setThirdColumnBotScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setThirdColumnBotScore$default(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 2
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setThirdColumnBotScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setThirdColumnTopScore$default(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;ILorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 1
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setThirdColumnTopScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setThirdColumnTopScore$default(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 2
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setThirdColumnTopScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static final v(Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;)Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/uikit/utils/e;-><init>(Landroid/view/View;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method private final w(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p2}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    aget p2, v0, p2

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p2, v0, :cond_3

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p2, v0, :cond_2

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-eq p2, v0, :cond_1

    .line 17
    .line 18
    const/4 v0, 0x4

    .line 19
    if-ne p2, v0, :cond_0

    .line 20
    .line 21
    sget p2, LlZ0/n;->TextStyle_Caption_Regular_L_StaticGreen:I

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 25
    .line 26
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 27
    .line 28
    .line 29
    throw p1

    .line 30
    :cond_1
    sget p2, LlZ0/n;->TextStyle_Caption_Medium_L_StaticGreen:I

    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_2
    sget p2, LlZ0/n;->TextStyle_Caption_Medium_L_Secondary:I

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_3
    sget p2, LlZ0/n;->TextStyle_Caption_Medium_L_TextPrimary:I

    .line 37
    .line 38
    :goto_0
    invoke-static {p1, p2}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 39
    .line 40
    .line 41
    return-void
.end method


# virtual methods
.method public getBackgroundTintList()Landroid/content/res/ColorStateList;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/e;->b()Landroid/content/res/ColorStateList;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public onDetachedFromWindow()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroid/view/ViewGroup;->onDetachedFromWindow()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 5
    .line 6
    iget-object v0, v0, LC31/k;->w:Landroid/widget/ImageView;

    .line 7
    .line 8
    invoke-virtual {v0}, Landroid/view/View;->clearAnimation()V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 12
    .line 13
    iget-object v0, v0, LC31/k;->c:Landroid/widget/ImageView;

    .line 14
    .line 15
    invoke-virtual {v0}, Landroid/view/View;->clearAnimation()V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public final setBotFirstTeamLogo(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setBotFirstTeamLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setBotFirstTeamLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v0, v0, LC31/k;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/4 v1, 0x0

    if-nez p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    const/16 v1, 0x8

    .line 3
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v0, v0, LC31/k;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setBotFirstTeamLogo(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v1, v0, LC31/k;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xe

    const/4 v7, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setBotFirstTeamLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v1, v0, LC31/k;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xc

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    move-object v3, p2

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setBotGameIndicator(Z)V
    .locals 1

    .line 1
    iput-boolean p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->e:Z

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->u()V

    .line 6
    .line 7
    .line 8
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 9
    .line 10
    iget-object p1, p1, LC31/k;->c:Landroid/widget/ImageView;

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->c:Landroid/view/animation/Animation;

    .line 13
    .line 14
    invoke-virtual {p1, v0}, Landroid/view/View;->startAnimation(Landroid/view/animation/Animation;)V

    .line 15
    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->t()V

    .line 19
    .line 20
    .line 21
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 22
    .line 23
    iget-object p1, p1, LC31/k;->c:Landroid/widget/ImageView;

    .line 24
    .line 25
    invoke-virtual {p1}, Landroid/view/View;->clearAnimation()V

    .line 26
    .line 27
    .line 28
    :goto_0
    const/4 p1, 0x1

    .line 29
    const/4 v0, 0x0

    .line 30
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final setBotScore(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setBotScore(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setBotScore(Ljava/lang/CharSequence;)V
    .locals 3

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    .line 2
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    iput-boolean v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->r:Z

    .line 3
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v2, v2, LC31/k;->e:Landroid/widget/TextView;

    invoke-virtual {v2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object p1, p1, LC31/k;->e:Landroid/widget/TextView;

    sget-object v2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    invoke-direct {p0, p1, v2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->w(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    .line 5
    invoke-virtual {p0, v1, v0}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    return-void
.end method

.method public final setBotSecondTeamLogo(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setBotSecondTeamLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setBotSecondTeamLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v0, v0, LC31/k;->f:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/4 v1, 0x0

    if-nez p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    const/16 v1, 0x8

    .line 3
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v0, v0, LC31/k;->f:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setBotSecondTeamLogo(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v1, v0, LC31/k;->f:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xe

    const/4 v7, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setBotSecondTeamLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v1, v0, LC31/k;->f:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xc

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    move-object v3, p2

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setBotTeamName(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setBotTeamName(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setBotTeamName(Ljava/lang/CharSequence;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v0, v0, LC31/k;->h:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 3
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v0, v0, LC31/k;->h:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setBotVictoryIndicator(II)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 2
    .line 3
    iget-object v0, v0, LC31/k;->i:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    const/4 v2, 0x1

    .line 7
    if-lez p1, :cond_0

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    const/4 v3, 0x0

    .line 12
    :goto_0
    iput-boolean v3, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->p:Z

    .line 13
    .line 14
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setTotalAndWinCounts(II)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0, v1, v2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final setFirstColumnBotScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setFirstColumnBotScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setFirstColumnBotScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 3
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    .line 2
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    iput-boolean v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->g:Z

    .line 3
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v2, v2, LC31/k;->k:Landroid/widget/TextView;

    invoke-virtual {v2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object p1, p1, LC31/k;->k:Landroid/widget/TextView;

    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->w(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    .line 5
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    return-void
.end method

.method public final setFirstColumnTopScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setFirstColumnTopScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setFirstColumnTopScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 3
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    .line 2
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    iput-boolean v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->f:Z

    .line 3
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v2, v2, LC31/k;->l:Landroid/widget/TextView;

    invoke-virtual {v2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object p1, p1, LC31/k;->l:Landroid/widget/TextView;

    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->w(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    .line 5
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    return-void
.end method

.method public final setGameText(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setGameText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setGameText(Ljava/lang/CharSequence;)V
    .locals 3

    const/4 v0, 0x0

    const/4 v1, 0x1

    if-eqz p1, :cond_1

    .line 2
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    xor-int/2addr v2, v1

    iput-boolean v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->l:Z

    .line 3
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v2, v2, LC31/k;->m:Landroid/widget/TextView;

    invoke-virtual {v2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    invoke-virtual {p0, v1, v0}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    return-void
.end method

.method public final setInfoText(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setInfoText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setInfoText(Ljava/lang/CharSequence;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v0, v0, LC31/k;->n:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/4 v1, 0x4

    .line 3
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v0, v0, LC31/k;->n:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setResultText(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setResultText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setResultText(Ljava/lang/CharSequence;)V
    .locals 3

    const/4 v0, 0x0

    const/4 v1, 0x1

    if-eqz p1, :cond_1

    .line 2
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    xor-int/2addr v2, v1

    iput-boolean v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->n:Z

    .line 3
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v2, v2, LC31/k;->o:Landroid/widget/TextView;

    invoke-virtual {v2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    invoke-virtual {p0, v1, v0}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    return-void
.end method

.method public final setSecondColumnBotScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setSecondColumnBotScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setSecondColumnBotScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 3
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    .line 2
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    iput-boolean v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->i:Z

    .line 3
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v2, v2, LC31/k;->q:Landroid/widget/TextView;

    invoke-virtual {v2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object p1, p1, LC31/k;->q:Landroid/widget/TextView;

    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->w(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    .line 5
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    return-void
.end method

.method public final setSecondColumnTopScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setSecondColumnTopScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setSecondColumnTopScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 3
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    .line 2
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    iput-boolean v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->h:Z

    .line 3
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v2, v2, LC31/k;->r:Landroid/widget/TextView;

    invoke-virtual {v2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object p1, p1, LC31/k;->r:Landroid/widget/TextView;

    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->w(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    .line 5
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    return-void
.end method

.method public final setSetText(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setSetText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSetText(Ljava/lang/CharSequence;)V
    .locals 3

    const/4 v0, 0x0

    const/4 v1, 0x1

    if-eqz p1, :cond_1

    .line 2
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    xor-int/2addr v2, v1

    iput-boolean v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->m:Z

    .line 3
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v2, v2, LC31/k;->s:Landroid/widget/TextView;

    invoke-virtual {v2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    invoke-virtual {p0, v1, v0}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    return-void
.end method

.method public final setThirdColumnBotScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setThirdColumnBotScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setThirdColumnBotScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 3
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    .line 2
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    iput-boolean v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->k:Z

    .line 3
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v2, v2, LC31/k;->t:Landroid/widget/TextView;

    invoke-virtual {v2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object p1, p1, LC31/k;->t:Landroid/widget/TextView;

    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->w(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    .line 5
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    return-void
.end method

.method public final setThirdColumnTopScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setThirdColumnTopScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setThirdColumnTopScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 3
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    .line 2
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    iput-boolean v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->j:Z

    .line 3
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v2, v2, LC31/k;->u:Landroid/widget/TextView;

    invoke-virtual {v2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object p1, p1, LC31/k;->u:Landroid/widget/TextView;

    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->w(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    .line 5
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    return-void
.end method

.method public final setTopFirstTeamLogo(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setTopFirstTeamLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTopFirstTeamLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v0, v0, LC31/k;->v:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/4 v1, 0x0

    if-nez p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    const/16 v1, 0x8

    .line 3
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v0, v0, LC31/k;->v:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTopFirstTeamLogo(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v1, v0, LC31/k;->v:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xe

    const/4 v7, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setTopFirstTeamLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v1, v0, LC31/k;->v:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xc

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    move-object v3, p2

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setTopGameIndicator(Z)V
    .locals 1

    .line 1
    iput-boolean p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->d:Z

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->u()V

    .line 6
    .line 7
    .line 8
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 9
    .line 10
    iget-object p1, p1, LC31/k;->w:Landroid/widget/ImageView;

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->c:Landroid/view/animation/Animation;

    .line 13
    .line 14
    invoke-virtual {p1, v0}, Landroid/view/View;->startAnimation(Landroid/view/animation/Animation;)V

    .line 15
    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->t()V

    .line 19
    .line 20
    .line 21
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 22
    .line 23
    iget-object p1, p1, LC31/k;->w:Landroid/widget/ImageView;

    .line 24
    .line 25
    invoke-virtual {p1}, Landroid/view/View;->clearAnimation()V

    .line 26
    .line 27
    .line 28
    :goto_0
    const/4 p1, 0x1

    .line 29
    const/4 v0, 0x0

    .line 30
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final setTopScore(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setTopScore(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTopScore(Ljava/lang/CharSequence;)V
    .locals 3

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    .line 2
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    iput-boolean v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->q:Z

    .line 3
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v2, v2, LC31/k;->y:Landroid/widget/TextView;

    invoke-virtual {v2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object p1, p1, LC31/k;->y:Landroid/widget/TextView;

    sget-object v2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    invoke-direct {p0, p1, v2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->w(Landroid/widget/TextView;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    .line 5
    invoke-virtual {p0, v1, v0}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    return-void
.end method

.method public final setTopSecondTeamLogo(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setTopSecondTeamLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTopSecondTeamLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v0, v0, LC31/k;->z:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/4 v1, 0x0

    if-nez p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    const/16 v1, 0x8

    .line 3
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v0, v0, LC31/k;->z:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTopSecondTeamLogo(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v1, v0, LC31/k;->z:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xe

    const/4 v7, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setTopSecondTeamLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v1, v0, LC31/k;->z:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xc

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    move-object v3, p2

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setTopTeamName(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->setTopTeamName(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTopTeamName(Ljava/lang/CharSequence;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v0, v0, LC31/k;->B:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 3
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    iget-object v0, v0, LC31/k;->B:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTopVictoryIndicator(II)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 2
    .line 3
    iget-object v0, v0, LC31/k;->C:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    const/4 v2, 0x1

    .line 7
    if-lez p1, :cond_0

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    const/4 v3, 0x0

    .line 12
    :goto_0
    iput-boolean v3, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->o:Z

    .line 13
    .line 14
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setTotalAndWinCounts(II)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0, v1, v2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->y(ZZ)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final t()V
    .locals 5

    .line 1
    new-instance v0, Landroidx/constraintlayout/widget/b;

    .line 2
    .line 3
    invoke-direct {v0}, Landroidx/constraintlayout/widget/b;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0, p0}, Landroidx/constraintlayout/widget/b;->p(Landroidx/constraintlayout/widget/ConstraintLayout;)V

    .line 7
    .line 8
    .line 9
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 10
    .line 11
    iget-object v1, v1, LC31/k;->n:Landroid/widget/TextView;

    .line 12
    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getId()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 18
    .line 19
    iget-object v2, v2, LC31/k;->m:Landroid/widget/TextView;

    .line 20
    .line 21
    invoke-virtual {v2}, Landroid/view/View;->getId()I

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    const/4 v3, 0x6

    .line 26
    const/4 v4, 0x7

    .line 27
    invoke-virtual {v0, v1, v4, v2, v3}, Landroidx/constraintlayout/widget/b;->s(IIII)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {v0, p0}, Landroidx/constraintlayout/widget/b;->i(Landroidx/constraintlayout/widget/ConstraintLayout;)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final u()V
    .locals 5

    .line 1
    new-instance v0, Landroidx/constraintlayout/widget/b;

    .line 2
    .line 3
    invoke-direct {v0}, Landroidx/constraintlayout/widget/b;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0, p0}, Landroidx/constraintlayout/widget/b;->p(Landroidx/constraintlayout/widget/ConstraintLayout;)V

    .line 7
    .line 8
    .line 9
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 10
    .line 11
    iget-object v1, v1, LC31/k;->n:Landroid/widget/TextView;

    .line 12
    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getId()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 18
    .line 19
    iget-object v2, v2, LC31/k;->D:Landroidx/constraintlayout/widget/Barrier;

    .line 20
    .line 21
    invoke-virtual {v2}, Landroid/view/View;->getId()I

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    const/4 v3, 0x6

    .line 26
    const/4 v4, 0x7

    .line 27
    invoke-virtual {v0, v1, v4, v2, v3}, Landroidx/constraintlayout/widget/b;->s(IIII)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {v0, p0}, Landroidx/constraintlayout/widget/b;->i(Landroidx/constraintlayout/widget/ConstraintLayout;)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final x(Z)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 2
    .line 3
    iget-object v0, v0, LC31/k;->j:Landroidx/constraintlayout/widget/Group;

    .line 4
    .line 5
    const/16 v1, 0x8

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz p1, :cond_0

    .line 9
    .line 10
    const/4 v3, 0x0

    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/16 v3, 0x8

    .line 13
    .line 14
    :goto_0
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 15
    .line 16
    .line 17
    if-eqz p1, :cond_14

    .line 18
    .line 19
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 20
    .line 21
    iget-object p1, p1, LC31/k;->w:Landroid/widget/ImageView;

    .line 22
    .line 23
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->d:Z

    .line 24
    .line 25
    if-eqz v0, :cond_1

    .line 26
    .line 27
    const/4 v0, 0x0

    .line 28
    goto :goto_1

    .line 29
    :cond_1
    const/16 v0, 0x8

    .line 30
    .line 31
    :goto_1
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 32
    .line 33
    .line 34
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 35
    .line 36
    iget-object p1, p1, LC31/k;->l:Landroid/widget/TextView;

    .line 37
    .line 38
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->f:Z

    .line 39
    .line 40
    if-eqz v0, :cond_2

    .line 41
    .line 42
    const/16 v0, 0x8

    .line 43
    .line 44
    goto :goto_2

    .line 45
    :cond_2
    const/4 v0, 0x0

    .line 46
    :goto_2
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 47
    .line 48
    .line 49
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 50
    .line 51
    iget-object p1, p1, LC31/k;->r:Landroid/widget/TextView;

    .line 52
    .line 53
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->h:Z

    .line 54
    .line 55
    if-eqz v0, :cond_3

    .line 56
    .line 57
    const/16 v0, 0x8

    .line 58
    .line 59
    goto :goto_3

    .line 60
    :cond_3
    const/4 v0, 0x0

    .line 61
    :goto_3
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 62
    .line 63
    .line 64
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 65
    .line 66
    iget-object p1, p1, LC31/k;->u:Landroid/widget/TextView;

    .line 67
    .line 68
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->j:Z

    .line 69
    .line 70
    if-eqz v0, :cond_4

    .line 71
    .line 72
    const/16 v0, 0x8

    .line 73
    .line 74
    goto :goto_4

    .line 75
    :cond_4
    const/4 v0, 0x0

    .line 76
    :goto_4
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 77
    .line 78
    .line 79
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 80
    .line 81
    iget-object p1, p1, LC31/k;->c:Landroid/widget/ImageView;

    .line 82
    .line 83
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->e:Z

    .line 84
    .line 85
    if-eqz v0, :cond_5

    .line 86
    .line 87
    const/4 v0, 0x0

    .line 88
    goto :goto_5

    .line 89
    :cond_5
    const/16 v0, 0x8

    .line 90
    .line 91
    :goto_5
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 92
    .line 93
    .line 94
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 95
    .line 96
    iget-object p1, p1, LC31/k;->k:Landroid/widget/TextView;

    .line 97
    .line 98
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->g:Z

    .line 99
    .line 100
    if-eqz v0, :cond_6

    .line 101
    .line 102
    const/16 v0, 0x8

    .line 103
    .line 104
    goto :goto_6

    .line 105
    :cond_6
    const/4 v0, 0x0

    .line 106
    :goto_6
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 107
    .line 108
    .line 109
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 110
    .line 111
    iget-object p1, p1, LC31/k;->q:Landroid/widget/TextView;

    .line 112
    .line 113
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->i:Z

    .line 114
    .line 115
    if-eqz v0, :cond_7

    .line 116
    .line 117
    const/16 v0, 0x8

    .line 118
    .line 119
    goto :goto_7

    .line 120
    :cond_7
    const/4 v0, 0x0

    .line 121
    :goto_7
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 122
    .line 123
    .line 124
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 125
    .line 126
    iget-object p1, p1, LC31/k;->t:Landroid/widget/TextView;

    .line 127
    .line 128
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->k:Z

    .line 129
    .line 130
    if-eqz v0, :cond_8

    .line 131
    .line 132
    const/16 v0, 0x8

    .line 133
    .line 134
    goto :goto_8

    .line 135
    :cond_8
    const/4 v0, 0x0

    .line 136
    :goto_8
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 137
    .line 138
    .line 139
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 140
    .line 141
    iget-object p1, p1, LC31/k;->x:Lorg/xbet/uikit/components/separator/Separator;

    .line 142
    .line 143
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->h:Z

    .line 144
    .line 145
    const/4 v3, 0x1

    .line 146
    if-eqz v0, :cond_9

    .line 147
    .line 148
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->j:Z

    .line 149
    .line 150
    if-eqz v0, :cond_9

    .line 151
    .line 152
    const/4 v0, 0x1

    .line 153
    goto :goto_9

    .line 154
    :cond_9
    const/4 v0, 0x0

    .line 155
    :goto_9
    if-eqz v0, :cond_a

    .line 156
    .line 157
    const/4 v0, 0x0

    .line 158
    goto :goto_a

    .line 159
    :cond_a
    const/16 v0, 0x8

    .line 160
    .line 161
    :goto_a
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 162
    .line 163
    .line 164
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 165
    .line 166
    iget-object p1, p1, LC31/k;->A:Lorg/xbet/uikit/components/separator/Separator;

    .line 167
    .line 168
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->f:Z

    .line 169
    .line 170
    if-eqz v0, :cond_b

    .line 171
    .line 172
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->h:Z

    .line 173
    .line 174
    if-eqz v0, :cond_b

    .line 175
    .line 176
    const/4 v0, 0x1

    .line 177
    goto :goto_b

    .line 178
    :cond_b
    const/4 v0, 0x0

    .line 179
    :goto_b
    if-eqz v0, :cond_c

    .line 180
    .line 181
    const/4 v0, 0x0

    .line 182
    goto :goto_c

    .line 183
    :cond_c
    const/16 v0, 0x8

    .line 184
    .line 185
    :goto_c
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 186
    .line 187
    .line 188
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 189
    .line 190
    iget-object p1, p1, LC31/k;->d:Lorg/xbet/uikit/components/separator/Separator;

    .line 191
    .line 192
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->i:Z

    .line 193
    .line 194
    if-eqz v0, :cond_d

    .line 195
    .line 196
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->k:Z

    .line 197
    .line 198
    if-eqz v0, :cond_d

    .line 199
    .line 200
    const/4 v0, 0x1

    .line 201
    goto :goto_d

    .line 202
    :cond_d
    const/4 v0, 0x0

    .line 203
    :goto_d
    if-eqz v0, :cond_e

    .line 204
    .line 205
    const/4 v0, 0x0

    .line 206
    goto :goto_e

    .line 207
    :cond_e
    const/16 v0, 0x8

    .line 208
    .line 209
    :goto_e
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 210
    .line 211
    .line 212
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 213
    .line 214
    iget-object p1, p1, LC31/k;->g:Lorg/xbet/uikit/components/separator/Separator;

    .line 215
    .line 216
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->g:Z

    .line 217
    .line 218
    if-eqz v0, :cond_f

    .line 219
    .line 220
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->i:Z

    .line 221
    .line 222
    if-eqz v0, :cond_f

    .line 223
    .line 224
    goto :goto_f

    .line 225
    :cond_f
    const/4 v3, 0x0

    .line 226
    :goto_f
    if-eqz v3, :cond_10

    .line 227
    .line 228
    const/4 v0, 0x0

    .line 229
    goto :goto_10

    .line 230
    :cond_10
    const/16 v0, 0x8

    .line 231
    .line 232
    :goto_10
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 233
    .line 234
    .line 235
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 236
    .line 237
    iget-object p1, p1, LC31/k;->o:Landroid/widget/TextView;

    .line 238
    .line 239
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->n:Z

    .line 240
    .line 241
    if-eqz v0, :cond_11

    .line 242
    .line 243
    const/4 v0, 0x0

    .line 244
    goto :goto_11

    .line 245
    :cond_11
    const/16 v0, 0x8

    .line 246
    .line 247
    :goto_11
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 248
    .line 249
    .line 250
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 251
    .line 252
    iget-object p1, p1, LC31/k;->s:Landroid/widget/TextView;

    .line 253
    .line 254
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->m:Z

    .line 255
    .line 256
    if-eqz v0, :cond_12

    .line 257
    .line 258
    const/4 v0, 0x0

    .line 259
    goto :goto_12

    .line 260
    :cond_12
    const/16 v0, 0x8

    .line 261
    .line 262
    :goto_12
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 263
    .line 264
    .line 265
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 266
    .line 267
    iget-object p1, p1, LC31/k;->m:Landroid/widget/TextView;

    .line 268
    .line 269
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->l:Z

    .line 270
    .line 271
    if-eqz v0, :cond_13

    .line 272
    .line 273
    const/4 v1, 0x0

    .line 274
    :cond_13
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 275
    .line 276
    .line 277
    :cond_14
    return-void
.end method

.method public final y(ZZ)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->x(Z)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->z(Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public final z(Z)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 2
    .line 3
    iget-object v0, v0, LC31/k;->p:Landroidx/constraintlayout/widget/Group;

    .line 4
    .line 5
    const/16 v1, 0x8

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz p1, :cond_0

    .line 9
    .line 10
    const/4 v3, 0x0

    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/16 v3, 0x8

    .line 13
    .line 14
    :goto_0
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 15
    .line 16
    .line 17
    if-eqz p1, :cond_5

    .line 18
    .line 19
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 20
    .line 21
    iget-object p1, p1, LC31/k;->C:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 22
    .line 23
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->o:Z

    .line 24
    .line 25
    if-eqz v0, :cond_1

    .line 26
    .line 27
    const/4 v0, 0x0

    .line 28
    goto :goto_1

    .line 29
    :cond_1
    const/16 v0, 0x8

    .line 30
    .line 31
    :goto_1
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 32
    .line 33
    .line 34
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 35
    .line 36
    iget-object p1, p1, LC31/k;->i:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 37
    .line 38
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->p:Z

    .line 39
    .line 40
    if-eqz v0, :cond_2

    .line 41
    .line 42
    const/4 v0, 0x0

    .line 43
    goto :goto_2

    .line 44
    :cond_2
    const/16 v0, 0x8

    .line 45
    .line 46
    :goto_2
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 47
    .line 48
    .line 49
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 50
    .line 51
    iget-object p1, p1, LC31/k;->y:Landroid/widget/TextView;

    .line 52
    .line 53
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->q:Z

    .line 54
    .line 55
    if-eqz v0, :cond_3

    .line 56
    .line 57
    const/16 v0, 0x8

    .line 58
    .line 59
    goto :goto_3

    .line 60
    :cond_3
    const/4 v0, 0x0

    .line 61
    :goto_3
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 62
    .line 63
    .line 64
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->b:LC31/k;

    .line 65
    .line 66
    iget-object p1, p1, LC31/k;->e:Landroid/widget/TextView;

    .line 67
    .line 68
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcardcompact/EventCardCompact;->r:Z

    .line 69
    .line 70
    if-eqz v0, :cond_4

    .line 71
    .line 72
    goto :goto_4

    .line 73
    :cond_4
    const/4 v1, 0x0

    .line 74
    :goto_4
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 75
    .line 76
    .line 77
    :cond_5
    return-void
.end method
