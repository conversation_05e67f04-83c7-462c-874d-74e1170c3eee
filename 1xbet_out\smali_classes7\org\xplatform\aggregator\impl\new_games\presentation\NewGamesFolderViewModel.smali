.class public final Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;
.super Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;,
        Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$b;,
        Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u008e\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0010 \n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008.\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010%\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\"\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u000e\u0008\u0000\u0018\u0000 \u00e1\u00012\u00020\u0001:\u0006\u00e2\u0001\u00e3\u0001\u00e4\u0001B\u0091\u0002\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u0012\u0006\u0010/\u001a\u00020.\u0012\u0006\u00101\u001a\u000200\u0012\u0006\u00103\u001a\u000202\u0012\u0006\u00105\u001a\u000204\u0012\u0006\u00107\u001a\u000206\u0012\u0006\u00109\u001a\u000208\u0012\u0006\u0010;\u001a\u00020:\u0012\u0006\u0010=\u001a\u00020<\u0012\u0006\u0010?\u001a\u00020>\u0012\u0006\u0010A\u001a\u00020@\u0012\u0006\u0010C\u001a\u00020B\u00a2\u0006\u0004\u0008D\u0010EJ\u000f\u0010G\u001a\u00020FH\u0016\u00a2\u0006\u0004\u0008G\u0010HJ\u000f\u0010I\u001a\u00020FH\u0016\u00a2\u0006\u0004\u0008I\u0010HJ\u0017\u0010L\u001a\u00020F2\u0006\u0010K\u001a\u00020JH\u0016\u00a2\u0006\u0004\u0008L\u0010MJ\u000f\u0010O\u001a\u00020NH\u0007\u00a2\u0006\u0004\u0008O\u0010PJ\u0013\u0010S\u001a\u0008\u0012\u0004\u0012\u00020R0Q\u00a2\u0006\u0004\u0008S\u0010TJ\u0013\u0010V\u001a\u0008\u0012\u0004\u0012\u00020U0Q\u00a2\u0006\u0004\u0008V\u0010TJ\u0013\u0010X\u001a\u0008\u0012\u0004\u0012\u00020W0Q\u00a2\u0006\u0004\u0008X\u0010TJ\u0013\u0010[\u001a\u0008\u0012\u0004\u0012\u00020Z0Y\u00a2\u0006\u0004\u0008[\u0010\\J\u0013\u0010^\u001a\u0008\u0012\u0004\u0012\u00020]0Y\u00a2\u0006\u0004\u0008^\u0010\\J\u0015\u0010`\u001a\u00020F2\u0006\u0010_\u001a\u00020J\u00a2\u0006\u0004\u0008`\u0010MJ\r\u0010b\u001a\u00020a\u00a2\u0006\u0004\u0008b\u0010cJ\r\u0010d\u001a\u00020a\u00a2\u0006\u0004\u0008d\u0010cJ\u001d\u0010g\u001a\u00020F2\u0006\u0010e\u001a\u00020N2\u0006\u0010f\u001a\u00020N\u00a2\u0006\u0004\u0008g\u0010hJ\u001d\u0010m\u001a\u00020F2\u0006\u0010j\u001a\u00020i2\u0006\u0010l\u001a\u00020k\u00a2\u0006\u0004\u0008m\u0010nJ\u001d\u0010q\u001a\u00020F2\u0006\u0010j\u001a\u00020i2\u0006\u0010p\u001a\u00020o\u00a2\u0006\u0004\u0008q\u0010rJ\u0013\u0010s\u001a\u0008\u0012\u0004\u0012\u00020W0Q\u00a2\u0006\u0004\u0008s\u0010TJ\u0015\u0010u\u001a\u00020F2\u0006\u0010p\u001a\u00020t\u00a2\u0006\u0004\u0008u\u0010vJ\u000f\u0010w\u001a\u00020FH\u0002\u00a2\u0006\u0004\u0008w\u0010HJ\u000f\u0010x\u001a\u00020iH\u0002\u00a2\u0006\u0004\u0008x\u0010yJ\u000f\u0010z\u001a\u00020FH\u0002\u00a2\u0006\u0004\u0008z\u0010HJ\u000f\u0010{\u001a\u00020FH\u0002\u00a2\u0006\u0004\u0008{\u0010HJ\u000f\u0010|\u001a\u00020FH\u0002\u00a2\u0006\u0004\u0008|\u0010HJ\u000f\u0010}\u001a\u00020FH\u0002\u00a2\u0006\u0004\u0008}\u0010HJO\u0010\u0084\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0004\u0012\u00020o0\u0083\u00010Q2\u0006\u0010~\u001a\u00020k2\u0006\u0010\u007f\u001a\u00020i2\u000e\u0010\u0081\u0001\u001a\t\u0012\u0004\u0012\u00020i0\u0080\u00012\u000e\u0010\u0082\u0001\u001a\t\u0012\u0004\u0012\u00020i0\u0080\u0001H\u0002\u00a2\u0006\u0006\u0008\u0084\u0001\u0010\u0085\u0001J\u0011\u0010\u0086\u0001\u001a\u00020FH\u0002\u00a2\u0006\u0005\u0008\u0086\u0001\u0010HJ!\u0010\u0087\u0001\u001a\u00020F2\u0006\u0010j\u001a\u00020i2\u0006\u0010p\u001a\u00020oH\u0002\u00a2\u0006\u0005\u0008\u0087\u0001\u0010rJ4\u0010\u008a\u0001\u001a\u00020F2\u0006\u0010j\u001a\u00020i2\u0006\u0010p\u001a\u00020o2\u0007\u0010\u0088\u0001\u001a\u00020N2\u0007\u0010\u0089\u0001\u001a\u00020iH\u0002\u00a2\u0006\u0006\u0008\u008a\u0001\u0010\u008b\u0001J\u0012\u0010\u008c\u0001\u001a\u00020UH\u0002\u00a2\u0006\u0006\u0008\u008c\u0001\u0010\u008d\u0001R\u0016\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008e\u0001\u0010\u008f\u0001R\u0016\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0090\u0001\u0010\u0091\u0001R\u0016\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0092\u0001\u0010\u0093\u0001R\u0016\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0094\u0001\u0010\u0095\u0001R\u0016\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0096\u0001\u0010\u0097\u0001R\u0016\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0098\u0001\u0010\u0099\u0001R\u0016\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009a\u0001\u0010\u009b\u0001R\u0016\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009c\u0001\u0010\u009d\u0001R\u0016\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009e\u0001\u0010\u009f\u0001R\u0016\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a0\u0001\u0010\u00a1\u0001R\u0016\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a2\u0001\u0010\u00a3\u0001R\u0016\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a4\u0001\u0010\u00a5\u0001R\u0016\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a6\u0001\u0010\u00a7\u0001R\u0016\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a8\u0001\u0010\u00a9\u0001R\u0016\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00aa\u0001\u0010\u00ab\u0001R\u0016\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ac\u0001\u0010\u00ad\u0001R\u0016\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ae\u0001\u0010\u00af\u0001R\u0016\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b0\u0001\u0010\u00b1\u0001R\u001e\u0010\u00b5\u0001\u001a\t\u0012\u0004\u0012\u00020F0\u00b2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b3\u0001\u0010\u00b4\u0001R\u001e\u0010\u00b9\u0001\u001a\t\u0012\u0004\u0012\u00020W0\u00b6\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b7\u0001\u0010\u00b8\u0001R\u001f\u0010\u00be\u0001\u001a\n\u0012\u0005\u0012\u00030\u00bb\u00010\u00ba\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bc\u0001\u0010\u00bd\u0001R$\u0010\u00c2\u0001\u001a\u000f\u0012\u0004\u0012\u00020k\u0012\u0004\u0012\u00020o0\u00bf\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c0\u0001\u0010\u00c1\u0001R\u001e\u0010\u00c4\u0001\u001a\t\u0012\u0004\u0012\u00020W0\u00b6\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c3\u0001\u0010\u00b8\u0001R\u0018\u0010\u00c8\u0001\u001a\u00030\u00c5\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c6\u0001\u0010\u00c7\u0001R\u0017\u0010\u00cb\u0001\u001a\u00020W8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c9\u0001\u0010\u00ca\u0001R\u0018\u0010\u00cf\u0001\u001a\u00030\u00cc\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00cd\u0001\u0010\u00ce\u0001R%\u0010\u00d2\u0001\u001a\u0010\u0012\u000b\u0012\t\u0012\u0004\u0012\u00020k0\u00d0\u00010\u00b6\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d1\u0001\u0010\u00b8\u0001R\u001e\u0010\u00d4\u0001\u001a\t\u0012\u0004\u0012\u00020U0\u00b6\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d3\u0001\u0010\u00b8\u0001R\u001e\u0010\u00d6\u0001\u001a\t\u0012\u0004\u0012\u00020R0\u00b6\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d5\u0001\u0010\u00b8\u0001R\u001c\u0010\u00da\u0001\u001a\u0005\u0018\u00010\u00d7\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00d8\u0001\u0010\u00d9\u0001R\u001c\u0010\u00dc\u0001\u001a\u0005\u0018\u00010\u00d7\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00db\u0001\u0010\u00d9\u0001R+\u0010\u00e0\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0004\u0012\u00020o0\u0083\u00010Q8\u0002X\u0082\u0004\u00a2\u0006\u000f\n\u0006\u0008\u00dd\u0001\u0010\u00de\u0001\u0012\u0005\u0008\u00df\u0001\u0010H\u00a8\u0006\u00e5\u0001"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
        "Lp9/g;",
        "observeLoginStateUseCase",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
        "setNeedFavoritesReUpdateUseCase",
        "Lorg/xplatform/aggregator/impl/category/domain/scenarios/GetItemCategoryPagesScenario;",
        "getItemCategoryPages",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "openGameDelegate",
        "Lf81/d;",
        "removeFavoriteUseCase",
        "Lf81/a;",
        "addFavoriteUseCase",
        "Lorg/xbet/analytics/domain/scope/g0;",
        "myAggregatorAnalytics",
        "Lkc1/b;",
        "getAggregatorBannerListByCategoryScenario",
        "Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;",
        "aggregatorScreenModel",
        "LHX0/e;",
        "resourceManager",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;",
        "aggregatorBannersDelegate",
        "Le81/c;",
        "getFavoriteGamesFlowScenario",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "LnR/a;",
        "aggregatorGamesFatmanLogger",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "Lm8/a;",
        "dispatchers",
        "LGg/a;",
        "searchAnalytics",
        "Lorg/xbet/analytics/domain/scope/I;",
        "depositAnalytics",
        "Lek/d;",
        "getScreenBalanceByTypeScenario",
        "LP91/b;",
        "aggregatorNavigator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "LxX0/a;",
        "blockPaymentNavigator",
        "LwX0/C;",
        "routerHolder",
        "LAR/a;",
        "depositFatmanLogger",
        "LZR/a;",
        "searchFatmanLogger",
        "Lfk/o;",
        "observeScreenBalanceUseCase",
        "Lfk/s;",
        "hasUserScreenBalanceUseCase",
        "Lgk0/a;",
        "getAccountSelectionStyleConfigTypeScenario",
        "Lek/f;",
        "updateWithCheckGamesAggregatorScenario",
        "Lfk/l;",
        "getLastBalanceUseCase",
        "LC81/f;",
        "setDailyTaskRefreshScenario",
        "<init>",
        "(Lp9/g;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lorg/xplatform/aggregator/impl/category/domain/scenarios/GetItemCategoryPagesScenario;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lf81/d;Lf81/a;Lorg/xbet/analytics/domain/scope/g0;Lkc1/b;Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;LHX0/e;Lp9/c;Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;Le81/c;Lorg/xbet/remoteconfig/domain/usecases/i;LnR/a;Lorg/xbet/ui_common/utils/M;LSX0/c;Lm8/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lek/d;LP91/b;Lorg/xbet/ui_common/utils/internet/a;LxX0/a;LwX0/C;LAR/a;LZR/a;Lfk/o;Lfk/s;Lgk0/a;Lek/f;Lfk/l;LC81/f;)V",
        "",
        "R3",
        "()V",
        "d4",
        "",
        "throwable",
        "e4",
        "(Ljava/lang/Throwable;)V",
        "",
        "b5",
        "()I",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;",
        "a5",
        "()Lkotlinx/coroutines/flow/e;",
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;",
        "T4",
        "",
        "W4",
        "Lkotlinx/coroutines/flow/Z;",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
        "P4",
        "()Lkotlinx/coroutines/flow/Z;",
        "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;",
        "S4",
        "error",
        "e5",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "X4",
        "()Lorg/xbet/uikit/components/lottie_empty/n;",
        "V4",
        "bannerId",
        "position",
        "k5",
        "(II)V",
        "",
        "screenName",
        "",
        "gameId",
        "o5",
        "(Ljava/lang/String;J)V",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "p5",
        "(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;)V",
        "Q4",
        "LN21/k;",
        "m5",
        "(LN21/k;)V",
        "f5",
        "R4",
        "()Ljava/lang/String;",
        "q5",
        "c5",
        "d5",
        "s5",
        "partitionId",
        "subStringValue",
        "",
        "filtersList",
        "providersList",
        "Landroidx/paging/PagingData;",
        "Y4",
        "(JLjava/lang/String;Ljava/util/List;Ljava/util/List;)Lkotlinx/coroutines/flow/e;",
        "h5",
        "j5",
        "categoryId",
        "screen",
        "i5",
        "(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/String;)V",
        "U4",
        "()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;",
        "y5",
        "Lp9/g;",
        "z5",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
        "A5",
        "Lorg/xplatform/aggregator/impl/category/domain/scenarios/GetItemCategoryPagesScenario;",
        "B5",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "C5",
        "Lf81/d;",
        "D5",
        "Lf81/a;",
        "E5",
        "Lorg/xbet/analytics/domain/scope/g0;",
        "F5",
        "Lkc1/b;",
        "G5",
        "Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;",
        "H5",
        "LHX0/e;",
        "I5",
        "Lp9/c;",
        "J5",
        "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;",
        "K5",
        "Le81/c;",
        "L5",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "M5",
        "LnR/a;",
        "N5",
        "Lorg/xbet/ui_common/utils/M;",
        "O5",
        "LSX0/c;",
        "P5",
        "Lm8/a;",
        "Lkotlinx/coroutines/flow/U;",
        "Q5",
        "Lkotlinx/coroutines/flow/U;",
        "refreshSharedFlow",
        "Lkotlinx/coroutines/flow/V;",
        "R5",
        "Lkotlinx/coroutines/flow/V;",
        "errorFlow",
        "",
        "Lorg/xplatform/banners/api/domain/models/BannerModel;",
        "S5",
        "Ljava/util/List;",
        "bannersList",
        "",
        "T5",
        "Ljava/util/Map;",
        "gamesMap",
        "U5",
        "authorizedStateFlow",
        "Lek0/o;",
        "V5",
        "Lek0/o;",
        "remoteConfigModel",
        "W5",
        "Z",
        "virtual",
        "Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;",
        "X5",
        "Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;",
        "bannerCollectionStyle",
        "",
        "Y5",
        "favoriteGamesFlow",
        "Z5",
        "bannersUiState",
        "a6",
        "gamesUiState",
        "Lkotlinx/coroutines/x0;",
        "b6",
        "Lkotlinx/coroutines/x0;",
        "getFavoriteGamesJob",
        "c6",
        "handleContentErrorsJob",
        "d6",
        "Lkotlinx/coroutines/flow/e;",
        "getGamesStream$annotations",
        "gamesStream",
        "e6",
        "c",
        "a",
        "b",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final e6:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final A5:Lorg/xplatform/aggregator/impl/category/domain/scenarios/GetItemCategoryPagesScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C5:Lf81/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final D5:Lf81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final E5:Lorg/xbet/analytics/domain/scope/g0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F5:Lkc1/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final G5:Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H5:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I5:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final J5:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final K5:Le81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final L5:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final M5:LnR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final N5:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final O5:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P5:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final Q5:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final R5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S5:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final T5:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Long;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final U5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V5:Lek0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final W5:Z

.field public final X5:Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final Y5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/util/Set<",
            "Ljava/lang/Long;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final Z5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final a6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b6:Lkotlinx/coroutines/x0;

.field public c6:Lkotlinx/coroutines/x0;

.field public final d6:Lkotlinx/coroutines/flow/e;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/e<",
            "Landroidx/paging/PagingData<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y5:Lp9/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->e6:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$b;

    return-void
.end method

.method public constructor <init>(Lp9/g;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lorg/xplatform/aggregator/impl/category/domain/scenarios/GetItemCategoryPagesScenario;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lf81/d;Lf81/a;Lorg/xbet/analytics/domain/scope/g0;Lkc1/b;Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;LHX0/e;Lp9/c;Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;Le81/c;Lorg/xbet/remoteconfig/domain/usecases/i;LnR/a;Lorg/xbet/ui_common/utils/M;LSX0/c;Lm8/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lek/d;LP91/b;Lorg/xbet/ui_common/utils/internet/a;LxX0/a;LwX0/C;LAR/a;LZR/a;Lfk/o;Lfk/s;Lgk0/a;Lek/f;Lfk/l;LC81/f;)V
    .locals 20
    .param p1    # Lp9/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xplatform/aggregator/impl/category/domain/scenarios/GetItemCategoryPagesScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lf81/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lf81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/analytics/domain/scope/g0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lkc1/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Le81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # LnR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # LGg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Lorg/xbet/analytics/domain/scope/I;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # Lek/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # LP91/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p26    # LAR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p27    # LZR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p28    # Lfk/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p29    # Lfk/s;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p30    # Lgk0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p31    # Lek/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p32    # Lfk/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p33    # LC81/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v13, p10

    .line 4
    .line 5
    move-object/from16 v5, p11

    .line 6
    .line 7
    move-object/from16 v3, p16

    .line 8
    .line 9
    move-object/from16 v9, p18

    .line 10
    .line 11
    move-object/from16 v6, p19

    .line 12
    .line 13
    move-object/from16 v7, p20

    .line 14
    .line 15
    move-object/from16 v12, p21

    .line 16
    .line 17
    move-object/from16 v1, p22

    .line 18
    .line 19
    move-object/from16 v2, p23

    .line 20
    .line 21
    move-object/from16 v4, p24

    .line 22
    .line 23
    move-object/from16 v8, p25

    .line 24
    .line 25
    move-object/from16 v14, p26

    .line 26
    .line 27
    move-object/from16 v15, p27

    .line 28
    .line 29
    move-object/from16 v18, p28

    .line 30
    .line 31
    move-object/from16 v17, p29

    .line 32
    .line 33
    move-object/from16 v16, p30

    .line 34
    .line 35
    move-object/from16 v10, p31

    .line 36
    .line 37
    move-object/from16 v11, p32

    .line 38
    .line 39
    move-object/from16 v19, p33

    .line 40
    .line 41
    invoke-direct/range {v0 .. v19}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;-><init>(LP91/b;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LxX0/a;Lp9/c;LGg/a;Lorg/xbet/analytics/domain/scope/I;LwX0/C;Lm8/a;Lek/f;Lfk/l;Lek/d;LHX0/e;LAR/a;LZR/a;Lgk0/a;Lfk/s;Lfk/o;LC81/f;)V

    .line 42
    .line 43
    .line 44
    move-object/from16 v1, p1

    .line 45
    .line 46
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->y5:Lp9/g;

    .line 47
    .line 48
    move-object/from16 v1, p2

    .line 49
    .line 50
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->z5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;

    .line 51
    .line 52
    move-object/from16 v1, p3

    .line 53
    .line 54
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->A5:Lorg/xplatform/aggregator/impl/category/domain/scenarios/GetItemCategoryPagesScenario;

    .line 55
    .line 56
    move-object/from16 v1, p4

    .line 57
    .line 58
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->B5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 59
    .line 60
    move-object/from16 v1, p5

    .line 61
    .line 62
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->C5:Lf81/d;

    .line 63
    .line 64
    move-object/from16 v1, p6

    .line 65
    .line 66
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->D5:Lf81/a;

    .line 67
    .line 68
    move-object/from16 v1, p7

    .line 69
    .line 70
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->E5:Lorg/xbet/analytics/domain/scope/g0;

    .line 71
    .line 72
    move-object/from16 v1, p8

    .line 73
    .line 74
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->F5:Lkc1/b;

    .line 75
    .line 76
    move-object/from16 v1, p9

    .line 77
    .line 78
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->G5:Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 79
    .line 80
    iput-object v13, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->H5:LHX0/e;

    .line 81
    .line 82
    iput-object v5, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->I5:Lp9/c;

    .line 83
    .line 84
    move-object/from16 v1, p12

    .line 85
    .line 86
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->J5:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    .line 87
    .line 88
    move-object/from16 v1, p13

    .line 89
    .line 90
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->K5:Le81/c;

    .line 91
    .line 92
    move-object/from16 v1, p14

    .line 93
    .line 94
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->L5:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 95
    .line 96
    move-object/from16 v2, p15

    .line 97
    .line 98
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->M5:LnR/a;

    .line 99
    .line 100
    iput-object v3, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->N5:Lorg/xbet/ui_common/utils/M;

    .line 101
    .line 102
    move-object/from16 v2, p17

    .line 103
    .line 104
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->O5:LSX0/c;

    .line 105
    .line 106
    iput-object v9, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->P5:Lm8/a;

    .line 107
    .line 108
    const/4 v2, 0x6

    .line 109
    const/4 v3, 0x1

    .line 110
    const/4 v4, 0x0

    .line 111
    const/4 v5, 0x0

    .line 112
    invoke-static {v3, v4, v5, v2, v5}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    .line 113
    .line 114
    .line 115
    move-result-object v2

    .line 116
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Q5:Lkotlinx/coroutines/flow/U;

    .line 117
    .line 118
    sget-object v3, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 119
    .line 120
    invoke-static {v3}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 121
    .line 122
    .line 123
    move-result-object v4

    .line 124
    iput-object v4, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->R5:Lkotlinx/coroutines/flow/V;

    .line 125
    .line 126
    new-instance v4, Ljava/util/ArrayList;

    .line 127
    .line 128
    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 129
    .line 130
    .line 131
    iput-object v4, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->S5:Ljava/util/List;

    .line 132
    .line 133
    new-instance v4, Ljava/util/LinkedHashMap;

    .line 134
    .line 135
    invoke-direct {v4}, Ljava/util/LinkedHashMap;-><init>()V

    .line 136
    .line 137
    .line 138
    iput-object v4, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->T5:Ljava/util/Map;

    .line 139
    .line 140
    invoke-static {v3}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 141
    .line 142
    .line 143
    move-result-object v3

    .line 144
    iput-object v3, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->U5:Lkotlinx/coroutines/flow/V;

    .line 145
    .line 146
    invoke-interface {v1}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 147
    .line 148
    .line 149
    move-result-object v1

    .line 150
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->V5:Lek0/o;

    .line 151
    .line 152
    invoke-virtual {v1}, Lek0/o;->y1()Z

    .line 153
    .line 154
    .line 155
    move-result v1

    .line 156
    iput-boolean v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->W5:Z

    .line 157
    .line 158
    sget-object v1, Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;->Companion:Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle$a;

    .line 159
    .line 160
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->R4()Ljava/lang/String;

    .line 161
    .line 162
    .line 163
    move-result-object v3

    .line 164
    invoke-virtual {v1, v3}, Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle$a;->a(Ljava/lang/String;)Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;

    .line 165
    .line 166
    .line 167
    move-result-object v1

    .line 168
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->X5:Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;

    .line 169
    .line 170
    invoke-static {}, Lkotlin/collections/Z;->e()Ljava/util/Set;

    .line 171
    .line 172
    .line 173
    move-result-object v1

    .line 174
    invoke-static {v1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 175
    .line 176
    .line 177
    move-result-object v1

    .line 178
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Y5:Lkotlinx/coroutines/flow/V;

    .line 179
    .line 180
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->U4()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;

    .line 181
    .line 182
    .line 183
    move-result-object v1

    .line 184
    invoke-static {v1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 185
    .line 186
    .line 187
    move-result-object v1

    .line 188
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Z5:Lkotlinx/coroutines/flow/V;

    .line 189
    .line 190
    sget-object v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$c;->a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$c;

    .line 191
    .line 192
    invoke-static {v1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 193
    .line 194
    .line 195
    move-result-object v1

    .line 196
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->a6:Lkotlinx/coroutines/flow/V;

    .line 197
    .line 198
    new-instance v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$gamesStream$1;

    .line 199
    .line 200
    invoke-direct {v1, v0, v5}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$gamesStream$1;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V

    .line 201
    .line 202
    .line 203
    invoke-static {v2, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 204
    .line 205
    .line 206
    move-result-object v1

    .line 207
    new-instance v2, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$special$$inlined$flatMapLatest$1;

    .line 208
    .line 209
    invoke-direct {v2, v5, v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$special$$inlined$flatMapLatest$1;-><init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)V

    .line 210
    .line 211
    .line 212
    invoke-static {v1, v2}, Lkotlinx/coroutines/flow/g;->C0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 213
    .line 214
    .line 215
    move-result-object v1

    .line 216
    new-instance v2, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$gamesStream$3;

    .line 217
    .line 218
    invoke-direct {v2, v0, v5}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$gamesStream$3;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V

    .line 219
    .line 220
    .line 221
    invoke-static {v1, v2}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 222
    .line 223
    .line 224
    move-result-object v1

    .line 225
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 226
    .line 227
    .line 228
    move-result-object v2

    .line 229
    invoke-interface {v9}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 230
    .line 231
    .line 232
    move-result-object v3

    .line 233
    invoke-static {v2, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 234
    .line 235
    .line 236
    move-result-object v2

    .line 237
    invoke-static {v1, v2}, Landroidx/paging/CachedPagingDataKt;->a(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/flow/e;

    .line 238
    .line 239
    .line 240
    move-result-object v1

    .line 241
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->d6:Lkotlinx/coroutines/flow/e;

    .line 242
    .line 243
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->s5()V

    .line 244
    .line 245
    .line 246
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->d5()V

    .line 247
    .line 248
    .line 249
    return-void
.end method

.method public static final synthetic A4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->a6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lkc1/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->F5:Lkc1/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lp9/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->I5:Lp9/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic D4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lorg/xbet/remoteconfig/domain/usecases/i;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->L5:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Z
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->O3()Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic F4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Q5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic G4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lek0/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->V5:Lek0/o;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic H4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lf81/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->C5:Lf81/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic I4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->H5:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic J4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->W5:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic K4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->c5()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic L4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/lang/Throwable;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->Q3(Ljava/lang/Throwable;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic M4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->f5()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic N4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->h5()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic O4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->q5()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final Z4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Landroidx/paging/PagingSource;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/category/presentation/pager/CategoryPagingSource;

    .line 2
    .line 3
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->A5:Lorg/xplatform/aggregator/impl/category/domain/scenarios/GetItemCategoryPagesScenario;

    .line 4
    .line 5
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/category/presentation/pager/CategoryPagingSource;-><init>(Lorg/xplatform/aggregator/impl/category/domain/scenarios/GetItemCategoryPagesScenario;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method private final f5()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->P5:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xplatform/aggregator/impl/new_games/presentation/o;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/o;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadBanners$2;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public static final g5(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Z5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$b;

    .line 4
    .line 5
    invoke-direct {v0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$b;-><init>(Ljava/lang/Throwable;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {p0, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method private final h5()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->b6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    const/4 v2, 0x1

    .line 7
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->U5:Lkotlinx/coroutines/flow/V;

    .line 11
    .line 12
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    check-cast v0, Ljava/lang/Boolean;

    .line 17
    .line 18
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 19
    .line 20
    .line 21
    move-result v0

    .line 22
    if-eqz v0, :cond_1

    .line 23
    .line 24
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->K5:Le81/c;

    .line 25
    .line 26
    invoke-interface {v0}, Le81/c;->invoke()Lkotlinx/coroutines/flow/e;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    new-instance v2, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadFavoriteGames$1;

    .line 31
    .line 32
    invoke-direct {v2, p0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadFavoriteGames$1;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V

    .line 33
    .line 34
    .line 35
    invoke-static {v0, v2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->P5:Lm8/a;

    .line 44
    .line 45
    invoke-interface {v3}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 46
    .line 47
    .line 48
    move-result-object v3

    .line 49
    invoke-static {v2, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    new-instance v3, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadFavoriteGames$2;

    .line 54
    .line 55
    invoke-direct {v3, p0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$loadFavoriteGames$2;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V

    .line 56
    .line 57
    .line 58
    invoke-static {v0, v2, v3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->b6:Lkotlinx/coroutines/x0;

    .line 63
    .line 64
    return-void

    .line 65
    :cond_1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Y5:Lkotlinx/coroutines/flow/V;

    .line 66
    .line 67
    invoke-static {}, Lkotlin/collections/Z;->e()Ljava/util/Set;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 72
    .line 73
    .line 74
    return-void
.end method

.method private final i5(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/String;)V
    .locals 3

    .line 1
    if-lez p3, :cond_0

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->M5:LnR/a;

    .line 4
    .line 5
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 6
    .line 7
    .line 8
    move-result-wide v1

    .line 9
    long-to-int p2, v1

    .line 10
    invoke-interface {v0, p1, p2, p3, p4}, LnR/a;->d(Ljava/lang/String;IILjava/lang/String;)V

    .line 11
    .line 12
    .line 13
    return-void

    .line 14
    :cond_0
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->M5:LnR/a;

    .line 15
    .line 16
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 17
    .line 18
    .line 19
    move-result-wide v0

    .line 20
    long-to-int p2, v0

    .line 21
    invoke-interface {p3, p1, p2, p4}, LnR/a;->h(Ljava/lang/String;ILjava/lang/String;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method private final j5(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->G5:Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->d()J

    .line 4
    .line 5
    .line 6
    move-result-wide v0

    .line 7
    sget-object v2, Lorg/xplatform/aggregator/api/model/PartitionType;->TV_GAMES:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 8
    .line 9
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 10
    .line 11
    .line 12
    move-result-wide v2

    .line 13
    cmp-long v4, v0, v2

    .line 14
    .line 15
    if-nez v4, :cond_0

    .line 16
    .line 17
    const-string v0, "cas_tvgames"

    .line 18
    .line 19
    :goto_0
    move-object v2, v0

    .line 20
    goto/16 :goto_1

    .line 21
    .line 22
    :cond_0
    sget-object v2, Lorg/xplatform/aggregator/api/model/PartitionType;->FISHING:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 23
    .line 24
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 25
    .line 26
    .line 27
    move-result-wide v2

    .line 28
    cmp-long v4, v0, v2

    .line 29
    .line 30
    if-nez v4, :cond_1

    .line 31
    .line 32
    const-string v0, "cas_fish_hunt"

    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_1
    sget-object v2, Lorg/xplatform/aggregator/api/model/PartitionType;->CRASH:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 36
    .line 37
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 38
    .line 39
    .line 40
    move-result-wide v2

    .line 41
    cmp-long v4, v0, v2

    .line 42
    .line 43
    if-nez v4, :cond_2

    .line 44
    .line 45
    const-string v0, "cas_crush"

    .line 46
    .line 47
    goto :goto_0

    .line 48
    :cond_2
    sget-object v2, Lorg/xplatform/aggregator/api/model/PartitionType;->KENO:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 49
    .line 50
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 51
    .line 52
    .line 53
    move-result-wide v2

    .line 54
    cmp-long v4, v0, v2

    .line 55
    .line 56
    if-nez v4, :cond_3

    .line 57
    .line 58
    const-string v0, "cas_keno"

    .line 59
    .line 60
    goto :goto_0

    .line 61
    :cond_3
    sget-object v2, Lorg/xplatform/aggregator/api/model/PartitionType;->TV_BET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 62
    .line 63
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 64
    .line 65
    .line 66
    move-result-wide v2

    .line 67
    cmp-long v4, v0, v2

    .line 68
    .line 69
    if-nez v4, :cond_4

    .line 70
    .line 71
    const-string v0, "cas_tvbet"

    .line 72
    .line 73
    goto :goto_0

    .line 74
    :cond_4
    sget-object v2, Lorg/xplatform/aggregator/api/model/PartitionType;->BINGO:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 75
    .line 76
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 77
    .line 78
    .line 79
    move-result-wide v2

    .line 80
    cmp-long v4, v0, v2

    .line 81
    .line 82
    if-nez v4, :cond_5

    .line 83
    .line 84
    const-string v0, "cas_bingo"

    .line 85
    .line 86
    goto :goto_0

    .line 87
    :cond_5
    sget-object v2, Lorg/xplatform/aggregator/api/model/PartitionType;->SCRATCH_CARDS:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 88
    .line 89
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 90
    .line 91
    .line 92
    move-result-wide v2

    .line 93
    cmp-long v4, v0, v2

    .line 94
    .line 95
    if-nez v4, :cond_6

    .line 96
    .line 97
    const-string v0, "cas_scratch"

    .line 98
    .line 99
    goto :goto_0

    .line 100
    :cond_6
    sget-object v2, Lorg/xplatform/aggregator/api/model/PartitionType;->SPORT:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 101
    .line 102
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 103
    .line 104
    .line 105
    move-result-wide v2

    .line 106
    cmp-long v4, v0, v2

    .line 107
    .line 108
    if-nez v4, :cond_7

    .line 109
    .line 110
    const-string v0, "cas_virtual"

    .line 111
    .line 112
    goto :goto_0

    .line 113
    :cond_7
    sget-object v2, Lorg/xplatform/aggregator/api/model/PartitionType;->ASIAN:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 114
    .line 115
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 116
    .line 117
    .line 118
    move-result-wide v2

    .line 119
    cmp-long v4, v0, v2

    .line 120
    .line 121
    if-nez v4, :cond_8

    .line 122
    .line 123
    const-string v0, "cas_asian"

    .line 124
    .line 125
    goto :goto_0

    .line 126
    :cond_8
    sget-object v2, Lorg/xplatform/aggregator/api/model/PartitionType;->VULKAN:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 127
    .line 128
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 129
    .line 130
    .line 131
    move-result-wide v2

    .line 132
    cmp-long v4, v0, v2

    .line 133
    .line 134
    if-nez v4, :cond_9

    .line 135
    .line 136
    const-string v0, "cas_volcano"

    .line 137
    .line 138
    goto :goto_0

    .line 139
    :goto_1
    const-wide/16 v3, -0x1

    .line 140
    .line 141
    long-to-int v0, v3

    .line 142
    invoke-direct {p0, p1, p2, v0, v2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->i5(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/String;)V

    .line 143
    .line 144
    .line 145
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->E5:Lorg/xbet/analytics/domain/scope/g0;

    .line 146
    .line 147
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 148
    .line 149
    .line 150
    move-result-wide v5

    .line 151
    invoke-virtual/range {v1 .. v6}, Lorg/xbet/analytics/domain/scope/g0;->X(Ljava/lang/String;JJ)V

    .line 152
    .line 153
    .line 154
    :cond_9
    return-void
.end method

.method public static synthetic l4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Landroidx/paging/PagingSource;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Z4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Landroidx/paging/PagingSource;

    move-result-object p0

    return-object p0
.end method

.method public static final l5(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    invoke-interface {p0}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    invoke-interface {v0, p0, p1}, Lkotlinx/coroutines/CoroutineExceptionHandler;->handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method

.method public static synthetic m4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->l5(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic n4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->r5(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final n5(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->N5:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$1$1;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$1$1;-><init>(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static synthetic o4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->g5(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic p4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->n5(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic q4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lf81/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->D5:Lf81/a;

    .line 2
    .line 3
    return-object p0
.end method

.method private final q5()V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->z5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;->a()V

    .line 4
    .line 5
    .line 6
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 11
    .line 12
    .line 13
    move-result-object v2

    .line 14
    new-instance v4, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$refresh$1;

    .line 15
    .line 16
    const/4 v0, 0x0

    .line 17
    invoke-direct {v4, p0, v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$refresh$1;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    const/4 v5, 0x2

    .line 21
    const/4 v6, 0x0

    .line 22
    const/4 v3, 0x0

    .line 23
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public static final synthetic r4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->G5:Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final r5(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->d4()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic s4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->U5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method private final s5()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->y5:Lp9/g;

    .line 2
    .line 3
    invoke-virtual {v0}, Lp9/g;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->B(Lkotlinx/coroutines/flow/e;)Lkotlinx/coroutines/flow/e;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    new-instance v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$subscribeToAuthState$1;

    .line 12
    .line 13
    const/4 v2, 0x0

    .line 14
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$subscribeToAuthState$1;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V

    .line 15
    .line 16
    .line 17
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public static final synthetic t4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->X5:Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Ljava/util/List;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->S5:Ljava/util/List;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Z5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lkotlinx/coroutines/CoroutineExceptionHandler;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic x4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Y5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Ljava/util/Map;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->T5:Ljava/util/Map;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic z4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;JLjava/lang/String;Ljava/util/List;Ljava/util/List;)Lkotlinx/coroutines/flow/e;
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p5}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Y4(JLjava/lang/String;Ljava/util/List;Ljava/util/List;)Lkotlinx/coroutines/flow/e;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public final P4()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->B5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->q()Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final Q4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->U5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public R3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->c6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const/4 v1, 0x1

    .line 6
    const/4 v2, 0x0

    .line 7
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->R5:Lkotlinx/coroutines/flow/V;

    .line 11
    .line 12
    sget-object v1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 13
    .line 14
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Z5:Lkotlinx/coroutines/flow/V;

    .line 18
    .line 19
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    instance-of v0, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$b;

    .line 24
    .line 25
    if-eqz v0, :cond_1

    .line 26
    .line 27
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Z5:Lkotlinx/coroutines/flow/V;

    .line 28
    .line 29
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->U4()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    :cond_1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->a6:Lkotlinx/coroutines/flow/V;

    .line 37
    .line 38
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    instance-of v0, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$b;

    .line 43
    .line 44
    if-eqz v0, :cond_2

    .line 45
    .line 46
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->a6:Lkotlinx/coroutines/flow/V;

    .line 47
    .line 48
    sget-object v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$c;->a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$c;

    .line 49
    .line 50
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    :cond_2
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->q5()V

    .line 54
    .line 55
    .line 56
    return-void
.end method

.method public final R4()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->L5:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 2
    .line 3
    invoke-interface {v0}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lek0/o;->P1()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0
.end method

.method public final S4()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->J5:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->d()Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final T4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Z5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final U4()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;
    .locals 3

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$a;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/uikit/components/bannercollection/a$a;

    .line 4
    .line 5
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-direct {v1, v2}, Lorg/xbet/uikit/components/bannercollection/a$a;-><init>(Ljava/util/List;)V

    .line 10
    .line 11
    .line 12
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$a;-><init>(Lorg/xbet/uikit/components/bannercollection/a$a;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method public final V4()Lorg/xbet/uikit/components/lottie_empty/n;
    .locals 12
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->O5:LSX0/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->SEARCH:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 4
    .line 5
    sget v6, Lpb/k;->nothing_found:I

    .line 6
    .line 7
    const/16 v10, 0x1de

    .line 8
    .line 9
    const/4 v11, 0x0

    .line 10
    const/4 v2, 0x0

    .line 11
    const/4 v3, 0x0

    .line 12
    const/4 v4, 0x0

    .line 13
    const/4 v5, 0x0

    .line 14
    const/4 v7, 0x0

    .line 15
    const/4 v8, 0x0

    .line 16
    const/4 v9, 0x0

    .line 17
    invoke-static/range {v0 .. v11}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    return-object v0
.end method

.method public final W4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->R5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final X4()Lorg/xbet/uikit/components/lottie_empty/n;
    .locals 12
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->O5:LSX0/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 4
    .line 5
    sget v8, Lpb/k;->try_again_text:I

    .line 6
    .line 7
    sget v6, Lpb/k;->data_retrieval_error:I

    .line 8
    .line 9
    new-instance v9, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$getErrorState$1;

    .line 10
    .line 11
    invoke-direct {v9, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$getErrorState$1;-><init>(Ljava/lang/Object;)V

    .line 12
    .line 13
    .line 14
    const/16 v10, 0x5e

    .line 15
    .line 16
    const/4 v11, 0x0

    .line 17
    const/4 v2, 0x0

    .line 18
    const/4 v3, 0x0

    .line 19
    const/4 v4, 0x0

    .line 20
    const/4 v5, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    invoke-static/range {v0 .. v11}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    return-object v0
.end method

.method public final Y4(JLjava/lang/String;Ljava/util/List;Ljava/util/List;)Lkotlinx/coroutines/flow/e;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)",
            "Lkotlinx/coroutines/flow/e<",
            "Landroidx/paging/PagingData<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .line 1
    new-instance v0, Landroidx/paging/C;

    .line 2
    .line 3
    const/16 v7, 0x38

    .line 4
    .line 5
    const/4 v8, 0x0

    .line 6
    const/16 v1, 0x10

    .line 7
    .line 8
    const/4 v2, 0x1

    .line 9
    const/4 v3, 0x0

    .line 10
    const/4 v4, 0x0

    .line 11
    const/4 v5, 0x0

    .line 12
    const/4 v6, 0x0

    .line 13
    invoke-direct/range {v0 .. v8}, Landroidx/paging/C;-><init>(IIZIIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 14
    .line 15
    .line 16
    new-instance v1, Lorg/xplatform/aggregator/impl/category/presentation/pager/a;

    .line 17
    .line 18
    const/16 v11, 0x40

    .line 19
    .line 20
    const/4 v12, 0x0

    .line 21
    const/4 v8, 0x0

    .line 22
    const-wide/16 v9, 0x0

    .line 23
    .line 24
    move-wide v2, p1

    .line 25
    move-object/from16 v7, p3

    .line 26
    .line 27
    move-object/from16 v4, p4

    .line 28
    .line 29
    move-object/from16 v5, p5

    .line 30
    .line 31
    invoke-direct/range {v1 .. v12}, Lorg/xplatform/aggregator/impl/category/presentation/pager/a;-><init>(JLjava/util/List;Ljava/util/List;ZLjava/lang/String;IJILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 32
    .line 33
    .line 34
    new-instance p1, Landroidx/paging/Pager;

    .line 35
    .line 36
    new-instance p2, Lorg/xplatform/aggregator/impl/new_games/presentation/m;

    .line 37
    .line 38
    invoke-direct {p2, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/m;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)V

    .line 39
    .line 40
    .line 41
    invoke-direct {p1, v0, v1, p2}, Landroidx/paging/Pager;-><init>(Landroidx/paging/C;Ljava/lang/Object;Lkotlin/jvm/functions/Function0;)V

    .line 42
    .line 43
    .line 44
    invoke-virtual {p1}, Landroidx/paging/Pager;->a()Lkotlinx/coroutines/flow/e;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    return-object p1
.end method

.method public final a5()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->a6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b5()I
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->V5:Lek0/o;

    .line 2
    .line 3
    invoke-virtual {v0}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-static {v0, v1}, Ls81/b;->b(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Z)I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    return v0
.end method

.method public final c5()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->c6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->a6:Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Z5:Lkotlinx/coroutines/flow/V;

    .line 16
    .line 17
    new-instance v2, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;

    .line 18
    .line 19
    const/4 v3, 0x0

    .line 20
    invoke-direct {v2, p0, v3}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleContentErrors$1;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V

    .line 21
    .line 22
    .line 23
    invoke-static {v0, v1, v2}, Lkotlinx/coroutines/flow/g;->o(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->P5:Lm8/a;

    .line 32
    .line 33
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->c6:Lkotlinx/coroutines/x0;

    .line 46
    .line 47
    return-void
.end method

.method public d4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->R5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final d5()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->d6:Lkotlinx/coroutines/flow/e;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Y5:Lkotlinx/coroutines/flow/V;

    .line 4
    .line 5
    new-instance v2, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;

    .line 6
    .line 7
    const/4 v3, 0x0

    .line 8
    invoke-direct {v2, p0, v3}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V

    .line 9
    .line 10
    .line 11
    invoke-static {v0, v1, v2}, Lkotlinx/coroutines/flow/g;->o(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    new-instance v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$2;

    .line 16
    .line 17
    invoke-direct {v1, p0, v3}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$2;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->P5:Lm8/a;

    .line 29
    .line 30
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    new-instance v2, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$3;

    .line 39
    .line 40
    invoke-direct {v2, p0, v3}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$3;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V

    .line 41
    .line 42
    .line 43
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    .line 46
    return-void
.end method

.method public e4(Ljava/lang/Throwable;)V
    .locals 2
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->N5:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/new_games/presentation/p;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/p;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final e5(Ljava/lang/Throwable;)V
    .locals 2
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->a6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$b;

    .line 4
    .line 5
    invoke-direct {v1, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$c$b;-><init>(Ljava/lang/Throwable;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final k5(II)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->S5:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_1

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    move-object v2, v1

    .line 18
    check-cast v2, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 19
    .line 20
    invoke-virtual {v2}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 21
    .line 22
    .line 23
    move-result v2

    .line 24
    if-ne v2, p1, :cond_0

    .line 25
    .line 26
    goto :goto_0

    .line 27
    :cond_1
    const/4 v1, 0x0

    .line 28
    :goto_0
    check-cast v1, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 29
    .line 30
    if-eqz v1, :cond_2

    .line 31
    .line 32
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->J5:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    .line 33
    .line 34
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    new-instance v2, Lorg/xplatform/aggregator/impl/new_games/presentation/q;

    .line 39
    .line 40
    invoke-direct {v2, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/q;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p1, v1, p2, v0, v2}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->f(Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V

    .line 44
    .line 45
    .line 46
    :cond_2
    return-void
.end method

.method public final m5(LN21/k;)V
    .locals 8
    .param p1    # LN21/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->P5:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xplatform/aggregator/impl/new_games/presentation/n;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/n;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, v2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onFavoriteClick$2;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;LN21/k;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final o5(Ljava/lang/String;J)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->T5:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p2, p3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 4
    .line 5
    .line 6
    move-result-object p2

    .line 7
    invoke-interface {v0, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    check-cast p2, Lorg/xplatform/aggregator/api/model/Game;

    .line 12
    .line 13
    if-eqz p2, :cond_0

    .line 14
    .line 15
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->p5(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;)V

    .line 16
    .line 17
    .line 18
    :cond_0
    return-void
.end method

.method public final p5(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->j5(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->B5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 5
    .line 6
    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onGameClick$1;

    .line 7
    .line 8
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->N5:Lorg/xbet/ui_common/utils/M;

    .line 9
    .line 10
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$onGameClick$1;-><init>(Ljava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    const/4 v1, 0x0

    .line 14
    invoke-virtual {p1, p2, v1, v0}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->u(Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;)V

    .line 15
    .line 16
    .line 17
    return-void
.end method
