.class final Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.daily_tournament.presentation.viewmodels.DailyTournamentPagerViewModel$observeUserPlace$1"
    f = "DailyTournamentPagerViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->x3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lp40/b;",
        "Ljava/lang/<PERSON>olean;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\n\u00a2\u0006\u0004\u0008\u0005\u0010\u0006"
    }
    d2 = {
        "Lp40/b;",
        "userPlace",
        "",
        "showResult",
        "",
        "<anonymous>",
        "(Lp40/b;Z)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field synthetic Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;

    const/4 p1, 0x3

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lp40/b;

    check-cast p2, Ljava/lang/Boolean;

    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p2

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;->invoke(Lp40/b;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lp40/b;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lp40/b;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;

    iget-object v1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;

    invoke-direct {v0, v1, p3}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;->L$0:Ljava/lang/Object;

    iput-boolean p2, v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;->Z$0:Z

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_1

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lp40/b;

    .line 14
    .line 15
    iget-boolean v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;->Z$0:Z

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    sget-object p1, Lp40/b;->d:Lp40/b$a;

    .line 21
    .line 22
    invoke-virtual {p1}, Lp40/b$a;->a()Lp40/b;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    :goto_0
    iget-object v1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;

    .line 27
    .line 28
    new-instance v2, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$a$b;

    .line 29
    .line 30
    invoke-direct {v2, p1, v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$a$b;-><init>(Lp40/b;Z)V

    .line 31
    .line 32
    .line 33
    invoke-static {v1, v2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->t3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$a;)V

    .line 34
    .line 35
    .line 36
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 37
    .line 38
    return-object p1

    .line 39
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 40
    .line 41
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 42
    .line 43
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 44
    .line 45
    .line 46
    throw p1
.end method
