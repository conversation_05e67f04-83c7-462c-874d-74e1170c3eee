.class public final LoC0/a;
.super Ljava/lang/Object;


# static fields
.field public static buttonSubGameFilter:I = 0x7f0a0344

.field public static eighthView:I = 0x7f0a06c4

.field public static eighthView1:I = 0x7f0a06c5

.field public static eighthView2:I = 0x7f0a06c6

.field public static eighthView3:I = 0x7f0a06c7

.field public static fifthView:I = 0x7f0a07cc

.field public static fifthView1:I = 0x7f0a07cd

.field public static fifthView2:I = 0x7f0a07ce

.field public static fifthView3:I = 0x7f0a07cf

.field public static fifthView4:I = 0x7f0a07d0

.field public static filterButtonGroup:I = 0x7f0a07de

.field public static filterGradient:I = 0x7f0a07df

.field public static firstView:I = 0x7f0a084a

.field public static firstView1:I = 0x7f0a084b

.field public static firstView2:I = 0x7f0a084c

.field public static firstView3:I = 0x7f0a084d

.field public static firstView4:I = 0x7f0a084e

.field public static firstView5:I = 0x7f0a084f

.field public static fourthView:I = 0x7f0a08d7

.field public static fourthView1:I = 0x7f0a08d8

.field public static fourthView2:I = 0x7f0a08d9

.field public static fourthView3:I = 0x7f0a08da

.field public static iTabContainerShimmer:I = 0x7f0a0ac3

.field public static ivEmptySearch:I = 0x7f0a0c3f

.field public static loadingErrorContainer:I = 0x7f0a0ece

.field public static secondView:I = 0x7f0a1383

.field public static secondView1:I = 0x7f0a1384

.field public static secondView2:I = 0x7f0a1385

.field public static secondView3:I = 0x7f0a1386

.field public static seventhView:I = 0x7f0a13ee

.field public static seventhView1:I = 0x7f0a13ef

.field public static seventhView2:I = 0x7f0a13f0

.field public static seventhView3:I = 0x7f0a13f1

.field public static seventhView4:I = 0x7f0a13f2

.field public static shimmerBackground:I = 0x7f0a1407

.field public static shimmerForeground:I = 0x7f0a1429

.field public static shimmerGroup:I = 0x7f0a143a

.field public static shimmerView:I = 0x7f0a147d

.field public static sixthView:I = 0x7f0a14ae

.field public static sixthView1:I = 0x7f0a14af

.field public static sixthView2:I = 0x7f0a14b0

.field public static sixthView3:I = 0x7f0a14b1

.field public static tabLayout:I = 0x7f0a1676

.field public static textError:I = 0x7f0a171a

.field public static thirdView:I = 0x7f0a17b0

.field public static thirdView1:I = 0x7f0a17b1

.field public static thirdView2:I = 0x7f0a17b2

.field public static thirdView3:I = 0x7f0a17b3

.field public static thirdView4:I = 0x7f0a17b4

.field public static vEmptyChipOne:I = 0x7f0a1e43

.field public static vEmptyChipThree:I = 0x7f0a1e44

.field public static vEmptyChipTwo:I = 0x7f0a1e45

.field public static viewLoadingErrorContainer:I = 0x7f0a1f5e

.field public static viewPager:I = 0x7f0a1f60

.field public static viewPagerContainer:I = 0x7f0a1f61


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
