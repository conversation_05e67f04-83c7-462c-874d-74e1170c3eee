.class public final synthetic LI1/w;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/util/Comparator;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 0

    .line 1
    check-cast p1, LI1/n$j;

    check-cast p2, LI1/n$j;

    invoke-static {p1, p2}, LI1/n$j;->g(LI1/n$j;LI1/n$j;)I

    move-result p1

    return p1
.end method
