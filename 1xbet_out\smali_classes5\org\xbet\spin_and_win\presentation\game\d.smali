.class public final synthetic Lorg/xbet/spin_and_win/presentation/game/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/d;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/d;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    check-cast p1, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    invoke-static {v0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->z2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
