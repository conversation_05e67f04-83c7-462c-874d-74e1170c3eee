.class public final Lh1/c;
.super Ljava/lang/Object;


# static fields
.field public static Fragment:[I = null

.field public static FragmentContainerView:[I = null

.field public static FragmentContainerView_android_name:I = 0x0

.field public static FragmentContainerView_android_tag:I = 0x1

.field public static Fragment_android_id:I = 0x1

.field public static Fragment_android_name:I = 0x0

.field public static Fragment_android_tag:I = 0x2


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 1
    const v0, 0x1010003

    .line 2
    .line 3
    .line 4
    const v1, 0x10100d0

    .line 5
    .line 6
    .line 7
    const v2, 0x10100d1

    .line 8
    .line 9
    .line 10
    filled-new-array {v0, v1, v2}, [I

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    sput-object v1, Lh1/c;->Fragment:[I

    .line 15
    .line 16
    filled-new-array {v0, v2}, [I

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    sput-object v0, Lh1/c;->FragmentContainerView:[I

    .line 21
    .line 22
    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
