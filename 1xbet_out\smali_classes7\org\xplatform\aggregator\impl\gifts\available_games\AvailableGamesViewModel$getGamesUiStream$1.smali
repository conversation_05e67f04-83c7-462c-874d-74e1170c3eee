.class final Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.available_games.AvailableGamesViewModel$getGamesUiStream$1"
    f = "AvailableGamesViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->L3()Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Landroidx/paging/PagingData<",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;",
        "Ljava/util/Set<",
        "+",
        "Ljava/lang/Long;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Landroidx/paging/PagingData<",
        "LN21/d;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\"\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u00002\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u00002\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u0003H\n\u00a2\u0006\u0004\u0008\u0007\u0010\u0008"
    }
    d2 = {
        "Landroidx/paging/PagingData;",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "pagingData",
        "",
        "",
        "favorites",
        "LN21/d;",
        "<anonymous>",
        "(Landroidx/paging/PagingData;Ljava/util/Set;)Landroidx/paging/PagingData;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    const/4 p1, 0x3

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final invoke(Landroidx/paging/PagingData;Ljava/util/Set;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/paging/PagingData<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Ljava/util/Set<",
            "Ljava/lang/Long;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Landroidx/paging/PagingData<",
            "LN21/d;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    invoke-direct {v0, v1, p3}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, Landroidx/paging/PagingData;

    check-cast p2, Ljava/util/Set;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;->invoke(Landroidx/paging/PagingData;Ljava/util/Set;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Landroidx/paging/PagingData;

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;->L$1:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, Ljava/util/Set;

    .line 18
    .line 19
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 20
    .line 21
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->x3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lp9/c;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-virtual {v1}, Lp9/c;->a()Z

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;

    .line 30
    .line 31
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 32
    .line 33
    const/4 v4, 0x0

    .line 34
    invoke-direct {v2, v3, v1, v0, v4}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;ZLjava/util/Set;Lkotlin/coroutines/e;)V

    .line 35
    .line 36
    .line 37
    invoke-static {p1, v2}, Landroidx/paging/E;->c(Landroidx/paging/PagingData;Lkotlin/jvm/functions/Function2;)Landroidx/paging/PagingData;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    return-object p1

    .line 42
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1
.end method
