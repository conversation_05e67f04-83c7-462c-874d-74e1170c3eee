.class public final Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lv81/b;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0018\u00002\u00020\u0001B!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0010\u0010\u000b\u001a\u00020\nH\u0096B\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u000f\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\u0011R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0012R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0014\u00a8\u0006\u0015"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl;",
        "Lv81/b;",
        "Lfk/l;",
        "getLastBalanceUseCase",
        "Lfk/n;",
        "getSavedBalanceIdUseCase",
        "Lcom/xbet/onexuser/domain/user/c;",
        "userInteractor",
        "<init>",
        "(Lfk/l;Lfk/n;Lcom/xbet/onexuser/domain/user/c;)V",
        "",
        "a",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xbet/balance/model/BalanceModel;",
        "balance",
        "b",
        "(Lorg/xbet/balance/model/BalanceModel;)Z",
        "Lfk/l;",
        "Lfk/n;",
        "c",
        "Lcom/xbet/onexuser/domain/user/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lfk/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lfk/n;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lcom/xbet/onexuser/domain/user/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lfk/l;Lfk/n;Lcom/xbet/onexuser/domain/user/c;)V
    .locals 0
    .param p1    # Lfk/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lfk/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lcom/xbet/onexuser/domain/user/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl;->a:Lfk/l;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl;->b:Lfk/n;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl;->c:Lcom/xbet/onexuser/domain/user/c;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public a(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/lang/Boolean;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl$invoke$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl$invoke$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl$invoke$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl$invoke$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl$invoke$1;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl$invoke$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl$invoke$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl;->c:Lcom/xbet/onexuser/domain/user/c;

    .line 54
    .line 55
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/user/c;->j()Z

    .line 56
    .line 57
    .line 58
    move-result p1

    .line 59
    if-eqz p1, :cond_5

    .line 60
    .line 61
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl;->a:Lfk/l;

    .line 62
    .line 63
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl;->b:Lfk/n;

    .line 64
    .line 65
    sget-object v4, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 66
    .line 67
    invoke-interface {v2, v4}, Lfk/n;->a(Lorg/xbet/balance/model/BalanceScreenType;)J

    .line 68
    .line 69
    .line 70
    move-result-wide v5

    .line 71
    const-wide/16 v7, 0x0

    .line 72
    .line 73
    cmp-long v2, v5, v7

    .line 74
    .line 75
    if-gtz v2, :cond_3

    .line 76
    .line 77
    sget-object v4, Lorg/xbet/balance/model/BalanceScreenType;->MULTI:Lorg/xbet/balance/model/BalanceScreenType;

    .line 78
    .line 79
    :cond_3
    iput v3, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl$invoke$1;->label:I

    .line 80
    .line 81
    invoke-interface {p1, v4, v0}, Lfk/l;->a(Lorg/xbet/balance/model/BalanceScreenType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    if-ne p1, v1, :cond_4

    .line 86
    .line 87
    return-object v1

    .line 88
    :cond_4
    :goto_1
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 89
    .line 90
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/domain/usecases/CheckBalanceForAggregatorCatalogScenarioImpl;->b(Lorg/xbet/balance/model/BalanceModel;)Z

    .line 91
    .line 92
    .line 93
    move-result v3

    .line 94
    goto :goto_2

    .line 95
    :cond_5
    if-eqz p1, :cond_6

    .line 96
    .line 97
    goto :goto_2

    .line 98
    :cond_6
    const/4 v3, 0x0

    .line 99
    :goto_2
    invoke-static {v3}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 100
    .line 101
    .line 102
    move-result-object p1

    .line 103
    return-object p1
.end method

.method public final b(Lorg/xbet/balance/model/BalanceModel;)Z
    .locals 2

    .line 1
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lcom/xbet/onexcore/data/configs/TypeAccount;->SPORT_BONUS:Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 6
    .line 7
    if-eq v0, v1, :cond_1

    .line 8
    .line 9
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    sget-object v0, Lcom/xbet/onexcore/data/configs/TypeAccount;->GAME_BONUS:Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 14
    .line 15
    if-ne p1, v0, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 p1, 0x0

    .line 19
    return p1

    .line 20
    :cond_1
    :goto_0
    const/4 p1, 0x1

    .line 21
    return p1
.end method
