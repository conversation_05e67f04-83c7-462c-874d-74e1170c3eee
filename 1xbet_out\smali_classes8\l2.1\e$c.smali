.class public final Ll2/e$c;
.super Lk2/p;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ll2/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation


# instance fields
.field public g:Lv1/h$a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lv1/h$a<",
            "Ll2/e$c;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lv1/h$a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lv1/h$a<",
            "Ll2/e$c;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lk2/p;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Ll2/e$c;->g:Lv1/h$a;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final u()V
    .locals 1

    .line 1
    iget-object v0, p0, Ll2/e$c;->g:Lv1/h$a;

    .line 2
    .line 3
    invoke-interface {v0, p0}, Lv1/h$a;->a(Lv1/h;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
