.class public final Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0008\n\u0002\u0008\u000b\u0008\u0001\u0018\u0000 72\u00020\u0001:\u00018B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u0019\u0010\u0008\u001a\u00020\u00042\u0008\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u0014\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\n\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\n\u0010\u0003J\u0017\u0010\r\u001a\u00020\u00042\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eR\u001a\u0010\u0014\u001a\u00020\u000f8\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008\u0010\u0010\u0011\u001a\u0004\u0008\u0012\u0010\u0013R\"\u0010\u001c\u001a\u00020\u00158\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u0016\u0010\u0017\u001a\u0004\u0008\u0018\u0010\u0019\"\u0004\u0008\u001a\u0010\u001bR\u001b\u0010\"\u001a\u00020\u001d8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u001e\u0010\u001f\u001a\u0004\u0008 \u0010!R\u001b\u0010(\u001a\u00020#8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008$\u0010%\u001a\u0004\u0008&\u0010\'R\u001b\u0010-\u001a\u00020)8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008*\u0010%\u001a\u0004\u0008+\u0010,R+\u00106\u001a\u00020.2\u0006\u0010/\u001a\u00020.8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u00080\u00101\u001a\u0004\u00082\u00103\"\u0004\u00084\u00105\u00a8\u00069"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "Lorg/xbet/special_event/impl/venues/presentation/g;",
        "uiState",
        "J2",
        "(Lorg/xbet/special_event/impl/venues/presentation/g;)V",
        "",
        "i0",
        "Z",
        "r2",
        "()Z",
        "showNavBar",
        "Lqy0/f;",
        "j0",
        "Lqy0/f;",
        "I2",
        "()Lqy0/f;",
        "setViewModelFactory",
        "(Lqy0/f;)V",
        "viewModelFactory",
        "LGq0/G;",
        "k0",
        "LRc/c;",
        "G2",
        "()LGq0/G;",
        "viewBinding",
        "Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;",
        "l0",
        "Lkotlin/j;",
        "H2",
        "()Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;",
        "viewModel",
        "Lsy0/f;",
        "m0",
        "F2",
        "()Lsy0/f;",
        "venuesAdapter",
        "",
        "<set-?>",
        "n0",
        "LeX0/d;",
        "E2",
        "()I",
        "M2",
        "(I)V",
        "eventId",
        "o0",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final synthetic b1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final k1:I

.field public static final o0:Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final v1:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final i0:Z

.field public j0:Lqy0/f;

.field public final k0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:LeX0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;

    .line 4
    .line 5
    const-string v2, "viewBinding"

    .line 6
    .line 7
    const-string v3, "getViewBinding()Lorg/xbet/special_event/impl/databinding/FragmentVenuesBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "eventId"

    .line 20
    .line 21
    const-string v5, "getEventId()I"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    const/4 v3, 0x2

    .line 31
    new-array v3, v3, [Lkotlin/reflect/m;

    .line 32
    .line 33
    aput-object v0, v3, v4

    .line 34
    .line 35
    const/4 v0, 0x1

    .line 36
    aput-object v2, v3, v0

    .line 37
    .line 38
    sput-object v3, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->b1:[Lkotlin/reflect/m;

    .line 39
    .line 40
    new-instance v0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$a;

    .line 41
    .line 42
    const/4 v2, 0x0

    .line 43
    invoke-direct {v0, v2}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    sput-object v0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->o0:Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$a;

    .line 47
    .line 48
    const/16 v0, 0x8

    .line 49
    .line 50
    sput v0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->k1:I

    .line 51
    .line 52
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    sput-object v0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->v1:Ljava/lang/String;

    .line 57
    .line 58
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LUo0/c;->fragment_venues:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    const/4 v0, 0x1

    .line 7
    iput-boolean v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->i0:Z

    .line 8
    .line 9
    sget-object v0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$viewBinding$2;->INSTANCE:Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$viewBinding$2;

    .line 10
    .line 11
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    iput-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->k0:LRc/c;

    .line 16
    .line 17
    new-instance v0, Lorg/xbet/special_event/impl/venues/presentation/d;

    .line 18
    .line 19
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/venues/presentation/d;-><init>(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)V

    .line 20
    .line 21
    .line 22
    new-instance v1, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$special$$inlined$viewModels$default$1;

    .line 23
    .line 24
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 25
    .line 26
    .line 27
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 28
    .line 29
    new-instance v3, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$special$$inlined$viewModels$default$2;

    .line 30
    .line 31
    invoke-direct {v3, v1}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 32
    .line 33
    .line 34
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    const-class v2, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;

    .line 39
    .line 40
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 41
    .line 42
    .line 43
    move-result-object v2

    .line 44
    new-instance v3, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$special$$inlined$viewModels$default$3;

    .line 45
    .line 46
    invoke-direct {v3, v1}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 47
    .line 48
    .line 49
    new-instance v4, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$special$$inlined$viewModels$default$4;

    .line 50
    .line 51
    const/4 v5, 0x0

    .line 52
    invoke-direct {v4, v5, v1}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 53
    .line 54
    .line 55
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    iput-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->l0:Lkotlin/j;

    .line 60
    .line 61
    new-instance v0, Lorg/xbet/special_event/impl/venues/presentation/e;

    .line 62
    .line 63
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/venues/presentation/e;-><init>(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)V

    .line 64
    .line 65
    .line 66
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    iput-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->m0:Lkotlin/j;

    .line 71
    .line 72
    new-instance v0, LeX0/d;

    .line 73
    .line 74
    const/4 v1, 0x0

    .line 75
    const/4 v2, 0x2

    .line 76
    const-string v3, "SPECIAL_EVENT_ID_BUNDLE_KEY"

    .line 77
    .line 78
    invoke-direct {v0, v3, v1, v2, v5}, LeX0/d;-><init>(Ljava/lang/String;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 79
    .line 80
    .line 81
    iput-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->n0:LeX0/d;

    .line 82
    .line 83
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)Lsy0/f;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->N2(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)Lsy0/f;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic B2()Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->v1:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final synthetic C2(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;Lorg/xbet/special_event/impl/venues/presentation/g;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->L2(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;Lorg/xbet/special_event/impl/venues/presentation/g;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic D2(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;I)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->M2(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final E2()I
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->n0:LeX0/d;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/d;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Integer;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method public static final K2(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->H2()Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->onBackPressed()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final synthetic L2(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;Lorg/xbet/special_event/impl/venues/presentation/g;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->J2(Lorg/xbet/special_event/impl/venues/presentation/g;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final M2(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->n0:LeX0/d;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/d;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static final N2(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)Lsy0/f;
    .locals 2

    .line 1
    new-instance v0, Lsy0/f;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$venuesAdapter$2$1;

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->H2()Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$venuesAdapter$2$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    invoke-direct {v0, v1}, Lsy0/f;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method public static final O2(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)Landroidx/lifecycle/e0$c;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/f;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->I2()Lqy0/f;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    const/4 v4, 0x4

    .line 8
    const/4 v5, 0x0

    .line 9
    const/4 v3, 0x0

    .line 10
    move-object v2, p0

    .line 11
    invoke-direct/range {v0 .. v5}, Lorg/xbet/ui_common/viewmodel/core/f;-><init>(Lorg/xbet/ui_common/viewmodel/core/e;Landroidx/savedstate/f;Landroid/os/Bundle;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->O2(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->K2(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final F2()Lsy0/f;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->m0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lsy0/f;

    .line 8
    .line 9
    return-object v0
.end method

.method public final G2()LGq0/G;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->k0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LGq0/G;

    .line 13
    .line 14
    return-object v0
.end method

.method public final H2()Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->l0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final I2()Lqy0/f;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->j0:Lqy0/f;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final J2(Lorg/xbet/special_event/impl/venues/presentation/g;)V
    .locals 3

    .line 1
    instance-of v0, p1, Lorg/xbet/special_event/impl/venues/presentation/g$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/16 v2, 0x8

    .line 5
    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->F2()Lsy0/f;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast p1, Lorg/xbet/special_event/impl/venues/presentation/g$a;

    .line 13
    .line 14
    invoke-virtual {p1}, Lorg/xbet/special_event/impl/venues/presentation/g$a;->a()Ljava/util/List;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    invoke-virtual {v0, p1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->G2()LGq0/G;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    iget-object p1, p1, LGq0/G;->c:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 26
    .line 27
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->G2()LGq0/G;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    iget-object p1, p1, LGq0/G;->e:Landroidx/recyclerview/widget/RecyclerView;

    .line 35
    .line 36
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->G2()LGq0/G;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    iget-object p1, p1, LGq0/G;->b:LGq0/L;

    .line 44
    .line 45
    invoke-virtual {p1}, LGq0/L;->b()Landroid/widget/LinearLayout;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 50
    .line 51
    .line 52
    return-void

    .line 53
    :cond_0
    instance-of v0, p1, Lorg/xbet/special_event/impl/venues/presentation/g$b;

    .line 54
    .line 55
    if-eqz v0, :cond_1

    .line 56
    .line 57
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->G2()LGq0/G;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    iget-object v0, v0, LGq0/G;->c:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 62
    .line 63
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 64
    .line 65
    .line 66
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->G2()LGq0/G;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    iget-object v0, v0, LGq0/G;->c:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 71
    .line 72
    check-cast p1, Lorg/xbet/special_event/impl/venues/presentation/g$b;

    .line 73
    .line 74
    invoke-virtual {p1}, Lorg/xbet/special_event/impl/venues/presentation/g$b;->a()Lorg/xbet/uikit/components/lottie/a;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/lottie/LottieView;->L(Lorg/xbet/uikit/components/lottie/a;)V

    .line 79
    .line 80
    .line 81
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->G2()LGq0/G;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    iget-object p1, p1, LGq0/G;->e:Landroidx/recyclerview/widget/RecyclerView;

    .line 86
    .line 87
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 88
    .line 89
    .line 90
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->G2()LGq0/G;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    iget-object p1, p1, LGq0/G;->b:LGq0/L;

    .line 95
    .line 96
    invoke-virtual {p1}, LGq0/L;->b()Landroid/widget/LinearLayout;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 101
    .line 102
    .line 103
    return-void

    .line 104
    :cond_1
    sget-object v0, Lorg/xbet/special_event/impl/venues/presentation/g$c;->a:Lorg/xbet/special_event/impl/venues/presentation/g$c;

    .line 105
    .line 106
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 107
    .line 108
    .line 109
    move-result p1

    .line 110
    if-eqz p1, :cond_2

    .line 111
    .line 112
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->G2()LGq0/G;

    .line 113
    .line 114
    .line 115
    move-result-object p1

    .line 116
    iget-object p1, p1, LGq0/G;->c:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 117
    .line 118
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 119
    .line 120
    .line 121
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->G2()LGq0/G;

    .line 122
    .line 123
    .line 124
    move-result-object p1

    .line 125
    iget-object p1, p1, LGq0/G;->e:Landroidx/recyclerview/widget/RecyclerView;

    .line 126
    .line 127
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 128
    .line 129
    .line 130
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->G2()LGq0/G;

    .line 131
    .line 132
    .line 133
    move-result-object p1

    .line 134
    iget-object p1, p1, LGq0/G;->b:LGq0/L;

    .line 135
    .line 136
    invoke-virtual {p1}, LGq0/L;->b()Landroid/widget/LinearLayout;

    .line 137
    .line 138
    .line 139
    move-result-object p1

    .line 140
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 141
    .line 142
    .line 143
    return-void

    .line 144
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 145
    .line 146
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 147
    .line 148
    .line 149
    throw p1
.end method

.method public r2()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->i0:Z

    .line 2
    .line 3
    return v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 12

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->G2()LGq0/G;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object p1, p1, LGq0/G;->d:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 9
    .line 10
    new-instance v0, Lorg/xbet/special_event/impl/venues/presentation/c;

    .line 11
    .line 12
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/venues/presentation/c;-><init>(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)V

    .line 13
    .line 14
    .line 15
    const/4 v1, 0x1

    .line 16
    const/4 v2, 0x0

    .line 17
    const/4 v3, 0x0

    .line 18
    invoke-static {p1, v3, v0, v1, v2}, LK01/d$a;->a(LK01/d;ZLkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->G2()LGq0/G;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    iget-object p1, p1, LGq0/G;->e:Landroidx/recyclerview/widget/RecyclerView;

    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->F2()Lsy0/f;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->G2()LGq0/G;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    iget-object p1, p1, LGq0/G;->e:Landroidx/recyclerview/widget/RecyclerView;

    .line 39
    .line 40
    new-instance v0, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;

    .line 41
    .line 42
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    sget v2, Lpb/f;->space_8:I

    .line 47
    .line 48
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 49
    .line 50
    .line 51
    move-result v1

    .line 52
    const/16 v10, 0x1de

    .line 53
    .line 54
    const/4 v11, 0x0

    .line 55
    const/4 v2, 0x0

    .line 56
    const/4 v4, 0x0

    .line 57
    const/4 v5, 0x0

    .line 58
    const/4 v6, 0x1

    .line 59
    const/4 v7, 0x0

    .line 60
    const/4 v8, 0x0

    .line 61
    const/4 v9, 0x0

    .line 62
    invoke-direct/range {v0 .. v11}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;-><init>(IIIIIILkotlin/jvm/functions/Function1;Lorg/xbet/ui_common/viewcomponents/recycler/decorators/SpacingItemDecorationBias;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 63
    .line 64
    .line 65
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 66
    .line 67
    .line 68
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    instance-of v1, v0, LQW0/b;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    check-cast v0, LQW0/b;

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    move-object v0, v2

    .line 21
    :goto_0
    const-class v1, Lqy0/d;

    .line 22
    .line 23
    if-eqz v0, :cond_3

    .line 24
    .line 25
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, LBc/a;

    .line 34
    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, LQW0/a;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object v0, v2

    .line 45
    :goto_1
    instance-of v3, v0, Lqy0/d;

    .line 46
    .line 47
    if-nez v3, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    move-object v2, v0

    .line 51
    :goto_2
    check-cast v2, Lqy0/d;

    .line 52
    .line 53
    if-eqz v2, :cond_3

    .line 54
    .line 55
    invoke-direct {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->E2()I

    .line 56
    .line 57
    .line 58
    move-result v0

    .line 59
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    const-class v3, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;

    .line 64
    .line 65
    invoke-virtual {v3}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 66
    .line 67
    .line 68
    move-result-object v3

    .line 69
    invoke-virtual {v2, v1, v0, v3}, Lqy0/d;->a(LwX0/c;ILjava/lang/String;)Lqy0/c;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    invoke-interface {v0, p0}, Lqy0/c;->a(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)V

    .line 74
    .line 75
    .line 76
    return-void

    .line 77
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 78
    .line 79
    new-instance v2, Ljava/lang/StringBuilder;

    .line 80
    .line 81
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 82
    .line 83
    .line 84
    const-string v3, "Cannot create dependency "

    .line 85
    .line 86
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 87
    .line 88
    .line 89
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 90
    .line 91
    .line 92
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 93
    .line 94
    .line 95
    move-result-object v1

    .line 96
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 97
    .line 98
    .line 99
    move-result-object v1

    .line 100
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 101
    .line 102
    .line 103
    throw v0
.end method

.method public v2()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->H2()Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;->v3()Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$onObserveData$1;

    .line 10
    .line 11
    invoke-direct {v5, p0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$onObserveData$1;-><init>(Ljava/lang/Object;)V

    .line 12
    .line 13
    .line 14
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 15
    .line 16
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 17
    .line 18
    .line 19
    move-result-object v3

    .line 20
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    new-instance v1, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 25
    .line 26
    const/4 v6, 0x0

    .line 27
    invoke-direct/range {v1 .. v6}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    const/4 v10, 0x3

    .line 31
    const/4 v11, 0x0

    .line 32
    const/4 v7, 0x0

    .line 33
    const/4 v8, 0x0

    .line 34
    move-object v6, v0

    .line 35
    move-object v9, v1

    .line 36
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 37
    .line 38
    .line 39
    return-void
.end method
