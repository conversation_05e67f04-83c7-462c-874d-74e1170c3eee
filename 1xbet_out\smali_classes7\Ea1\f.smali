.class public final LEa1/f;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a+\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;",
        "style",
        "LHX0/e;",
        "resourceManager",
        "",
        "Lk21/b;",
        "listAggregatorTournamentCardContentDSModel",
        "Lk21/h;",
        "a",
        "(Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;LHX0/e;Ljava/util/List;)Lk21/h;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;LHX0/e;Ljava/util/List;)Lk21/h;
    .locals 4
    .param p0    # Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;",
            "LHX0/e;",
            "Ljava/util/List<",
            "Lk21/b;",
            ">;)",
            "Lk21/h;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lk21/h;

    .line 2
    .line 3
    sget v1, Lpb/k;->tournaments:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v3, v2, [Ljava/lang/Object;

    .line 7
    .line 8
    invoke-interface {p1, v1, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    sget v3, Lpb/k;->all:I

    .line 13
    .line 14
    new-array v2, v2, [Ljava/lang/Object;

    .line 15
    .line 16
    invoke-interface {p1, v3, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    invoke-direct {v0, v1, p1, p0, p2}, Lk21/h;-><init>(Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;Ljava/util/List;)V

    .line 21
    .line 22
    .line 23
    return-object v0
.end method
