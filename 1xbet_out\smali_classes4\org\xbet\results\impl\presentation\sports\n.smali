.class public final Lorg/xbet/results/impl/presentation/sports/n;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/s;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/o;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/A;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/u;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/GetSportsLiveResultsUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final l:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lgl0/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/s;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/o;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/A;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/u;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/GetSportsLiveResultsUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LSX0/c;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "Lgl0/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/results/impl/presentation/sports/n;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/results/impl/presentation/sports/n;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/results/impl/presentation/sports/n;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/results/impl/presentation/sports/n;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/results/impl/presentation/sports/n;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/results/impl/presentation/sports/n;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/results/impl/presentation/sports/n;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/results/impl/presentation/sports/n;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/results/impl/presentation/sports/n;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/results/impl/presentation/sports/n;->j:LBc/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/results/impl/presentation/sports/n;->k:LBc/a;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/results/impl/presentation/sports/n;->l:LBc/a;

    .line 27
    .line 28
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/results/impl/presentation/sports/n;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/s;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/o;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/A;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/u;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/results/impl/domain/usecases/GetSportsLiveResultsUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LSX0/c;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "Lgl0/a;",
            ">;)",
            "Lorg/xbet/results/impl/presentation/sports/n;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/results/impl/presentation/sports/n;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object/from16 v4, p3

    .line 7
    .line 8
    move-object/from16 v5, p4

    .line 9
    .line 10
    move-object/from16 v6, p5

    .line 11
    .line 12
    move-object/from16 v7, p6

    .line 13
    .line 14
    move-object/from16 v8, p7

    .line 15
    .line 16
    move-object/from16 v9, p8

    .line 17
    .line 18
    move-object/from16 v10, p9

    .line 19
    .line 20
    move-object/from16 v11, p10

    .line 21
    .line 22
    move-object/from16 v12, p11

    .line 23
    .line 24
    invoke-direct/range {v0 .. v12}, Lorg/xbet/results/impl/presentation/sports/n;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 25
    .line 26
    .line 27
    return-object v0
.end method

.method public static c(Landroidx/lifecycle/Q;Lorg/xbet/results/impl/domain/usecases/s;Lorg/xbet/results/impl/domain/usecases/o;Lorg/xbet/results/impl/domain/usecases/A;Lorg/xbet/results/impl/domain/usecases/u;Lorg/xbet/results/impl/domain/usecases/GetSportsLiveResultsUseCase;Lorg/xbet/ui_common/utils/internet/a;Lm8/a;Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/c;Lgl0/a;)Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;
    .locals 14

    .line 1
    new-instance v0, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object/from16 v3, p2

    .line 6
    .line 7
    move-object/from16 v4, p3

    .line 8
    .line 9
    move-object/from16 v5, p4

    .line 10
    .line 11
    move-object/from16 v6, p5

    .line 12
    .line 13
    move-object/from16 v7, p6

    .line 14
    .line 15
    move-object/from16 v8, p7

    .line 16
    .line 17
    move-object/from16 v9, p8

    .line 18
    .line 19
    move-object/from16 v10, p9

    .line 20
    .line 21
    move-object/from16 v11, p10

    .line 22
    .line 23
    move-object/from16 v12, p11

    .line 24
    .line 25
    move-object/from16 v13, p12

    .line 26
    .line 27
    invoke-direct/range {v0 .. v13}, Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;-><init>(Landroidx/lifecycle/Q;Lorg/xbet/results/impl/domain/usecases/s;Lorg/xbet/results/impl/domain/usecases/o;Lorg/xbet/results/impl/domain/usecases/A;Lorg/xbet/results/impl/domain/usecases/u;Lorg/xbet/results/impl/domain/usecases/GetSportsLiveResultsUseCase;Lorg/xbet/ui_common/utils/internet/a;Lm8/a;Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/c;Lgl0/a;)V

    .line 28
    .line 29
    .line 30
    return-object v0
.end method


# virtual methods
.method public b(Landroidx/lifecycle/Q;)Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;
    .locals 14

    .line 1
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/n;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    move-object v2, v0

    .line 8
    check-cast v2, Lorg/xbet/results/impl/domain/usecases/s;

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/n;->b:LBc/a;

    .line 11
    .line 12
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    move-object v3, v0

    .line 17
    check-cast v3, Lorg/xbet/results/impl/domain/usecases/o;

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/n;->c:LBc/a;

    .line 20
    .line 21
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    move-object v4, v0

    .line 26
    check-cast v4, Lorg/xbet/results/impl/domain/usecases/A;

    .line 27
    .line 28
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/n;->d:LBc/a;

    .line 29
    .line 30
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    move-object v5, v0

    .line 35
    check-cast v5, Lorg/xbet/results/impl/domain/usecases/u;

    .line 36
    .line 37
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/n;->e:LBc/a;

    .line 38
    .line 39
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    move-object v6, v0

    .line 44
    check-cast v6, Lorg/xbet/results/impl/domain/usecases/GetSportsLiveResultsUseCase;

    .line 45
    .line 46
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/n;->f:LBc/a;

    .line 47
    .line 48
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    move-object v7, v0

    .line 53
    check-cast v7, Lorg/xbet/ui_common/utils/internet/a;

    .line 54
    .line 55
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/n;->g:LBc/a;

    .line 56
    .line 57
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    move-object v8, v0

    .line 62
    check-cast v8, Lm8/a;

    .line 63
    .line 64
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/n;->h:LBc/a;

    .line 65
    .line 66
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    move-object v9, v0

    .line 71
    check-cast v9, Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;

    .line 72
    .line 73
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/n;->i:LBc/a;

    .line 74
    .line 75
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object v0

    .line 79
    move-object v10, v0

    .line 80
    check-cast v10, Lorg/xbet/ui_common/utils/M;

    .line 81
    .line 82
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/n;->j:LBc/a;

    .line 83
    .line 84
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    move-object v11, v0

    .line 89
    check-cast v11, LSX0/c;

    .line 90
    .line 91
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/n;->k:LBc/a;

    .line 92
    .line 93
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    move-object v12, v0

    .line 98
    check-cast v12, LwX0/c;

    .line 99
    .line 100
    iget-object v0, p0, Lorg/xbet/results/impl/presentation/sports/n;->l:LBc/a;

    .line 101
    .line 102
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 103
    .line 104
    .line 105
    move-result-object v0

    .line 106
    move-object v13, v0

    .line 107
    check-cast v13, Lgl0/a;

    .line 108
    .line 109
    move-object v1, p1

    .line 110
    invoke-static/range {v1 .. v13}, Lorg/xbet/results/impl/presentation/sports/n;->c(Landroidx/lifecycle/Q;Lorg/xbet/results/impl/domain/usecases/s;Lorg/xbet/results/impl/domain/usecases/o;Lorg/xbet/results/impl/domain/usecases/A;Lorg/xbet/results/impl/domain/usecases/u;Lorg/xbet/results/impl/domain/usecases/GetSportsLiveResultsUseCase;Lorg/xbet/ui_common/utils/internet/a;Lm8/a;Lorg/xbet/domain/betting/api/models/result/ResultsScreenType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/c;Lgl0/a;)Lorg/xbet/results/impl/presentation/sports/SportsResultsViewModel;

    .line 111
    .line 112
    .line 113
    move-result-object p1

    .line 114
    return-object p1
.end method
