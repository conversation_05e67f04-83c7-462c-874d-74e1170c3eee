.class public final synthetic Lorg/xbet/swipex/impl/presentation/swipex/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/e;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/e;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->I2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
