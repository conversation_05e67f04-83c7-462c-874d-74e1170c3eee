.class final Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.core.presentation.BaseAggregatorViewModel$updateBalance$2"
    f = "BaseAggregatorViewModel.kt"
    l = {
        0xd6
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->p0()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->z3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lek/d;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 34
    .line 35
    sget-object v3, Lorg/xbet/balance/model/BalanceRefreshType;->NOW:Lorg/xbet/balance/model/BalanceRefreshType;

    .line 36
    .line 37
    iput v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;->label:I

    .line 38
    .line 39
    invoke-interface {p1, v1, v3, p0}, Lek/d;->a(Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceRefreshType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    if-ne p1, v0, :cond_2

    .line 44
    .line 45
    return-object v0

    .line 46
    :cond_2
    :goto_0
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 47
    .line 48
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 49
    .line 50
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->F3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lek/f;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 55
    .line 56
    invoke-interface {v0, v1, p1}, Lek/f;->a(Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceModel;)V

    .line 57
    .line 58
    .line 59
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$updateBalance$2;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 60
    .line 61
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 62
    .line 63
    .line 64
    move-result-wide v1

    .line 65
    invoke-static {v0, v1, v2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->H3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;J)V

    .line 66
    .line 67
    .line 68
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 69
    .line 70
    return-object p1
.end method
