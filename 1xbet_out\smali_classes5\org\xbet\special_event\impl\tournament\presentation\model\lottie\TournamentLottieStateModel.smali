.class public final Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\n\u0008\u0081\u0008\u0018\u00002\u00020\u0001:\u0001\u001aB\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J$\u0010\u0008\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u0004H\u00c6\u0001\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0010\u0010\u000b\u001a\u00020\nH\u00d6\u0001\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0010\u0010\u000e\u001a\u00020\rH\u00d6\u0001\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u001a\u0010\u0012\u001a\u00020\u00112\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0012\u0010\u0013R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0008\u0010\u0014\u001a\u0004\u0008\u0015\u0010\u0016R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0017\u0010\u0018\u001a\u0004\u0008\u0017\u0010\u0019\u00a8\u0006\u001b"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;",
        "",
        "Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;",
        "lottieErrorType",
        "Lorg/xbet/uikit/components/lottie/a;",
        "lottieConfig",
        "<init>",
        "(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;Lorg/xbet/uikit/components/lottie/a;)V",
        "a",
        "(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;Lorg/xbet/uikit/components/lottie/a;)Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;",
        "",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;",
        "c",
        "()Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;",
        "b",
        "Lorg/xbet/uikit/components/lottie/a;",
        "()Lorg/xbet/uikit/components/lottie/a;",
        "TournamentLottieErrorType",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final c:I


# instance fields
.field public final a:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/uikit/components/lottie/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget v0, Lorg/xbet/uikit/components/lottie/a;->f:I

    sput v0, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->c:I

    return-void
.end method

.method public constructor <init>(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;Lorg/xbet/uikit/components/lottie/a;)V
    .locals 0
    .param p1    # Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit/components/lottie/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->a:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->b:Lorg/xbet/uikit/components/lottie/a;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;Lorg/xbet/uikit/components/lottie/a;)Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;
    .locals 1
    .param p1    # Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit/components/lottie/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;

    invoke-direct {v0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;Lorg/xbet/uikit/components/lottie/a;)V

    return-object v0
.end method

.method public final b()Lorg/xbet/uikit/components/lottie/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->b:Lorg/xbet/uikit/components/lottie/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->a:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;

    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->a:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    iget-object v3, p1, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->a:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    if-eq v1, v3, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->b:Lorg/xbet/uikit/components/lottie/a;

    iget-object p1, p1, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->b:Lorg/xbet/uikit/components/lottie/a;

    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_3

    return v2

    :cond_3
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->a:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->b:Lorg/xbet/uikit/components/lottie/a;

    invoke-virtual {v1}, Lorg/xbet/uikit/components/lottie/a;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 4
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->a:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->b:Lorg/xbet/uikit/components/lottie/a;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "TournamentLottieStateModel(lottieErrorType="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", lottieConfig="

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
