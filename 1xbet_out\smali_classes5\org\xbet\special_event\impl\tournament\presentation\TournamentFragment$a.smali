.class public final Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u001d\u0010\t\u001a\u00020\u00082\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\t\u0010\nR\u0017\u0010\u000b\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008\u000b\u0010\u000c\u001a\u0004\u0008\r\u0010\u000eR\u0014\u0010\u000f\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u000cR\u0014\u0010\u0010\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u000c\u00a8\u0006\u0011"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$a;",
        "",
        "<init>",
        "()V",
        "",
        "eventId",
        "",
        "title",
        "Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;",
        "b",
        "(ILjava/lang/String;)Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;",
        "SCREEN_NAME",
        "Ljava/lang/String;",
        "a",
        "()Ljava/lang/String;",
        "SPECIAL_EVENT_TITLE_BUNDLE_KEY",
        "SPECIAL_EVENT_ID_BUNDLE_KEY",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->B2()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final b(ILjava/lang/String;)Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;
    .locals 1
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->F2(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Ljava/lang/String;)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->E2(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;I)V

    .line 10
    .line 11
    .line 12
    return-object v0
.end method
