.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"

# interfaces
.implements Lg31/j;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Lg31/j<",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0001\u0018\u0000 12\u00020\u00012\u0008\u0012\u0004\u0012\u00020\u00030\u0002:\u0001\u0019B\'\u0008\u0007\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\n\u0008\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u0012\u0008\u0008\u0003\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0017\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000c\u001a\u00020\u0003H\u0016\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u000f\u0010\u0011\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0011\u0010\u0014\u001a\u0004\u0018\u00010\u0013H\u0016\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0011\u0010\u0016\u001a\u0004\u0018\u00010\u0013H\u0016\u00a2\u0006\u0004\u0008\u0016\u0010\u0015J\u000f\u0010\u0017\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018R\u0014\u0010\u001b\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u001aR\u0014\u0010\u001f\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\u001eR*\u0010(\u001a\u00020 2\u0006\u0010!\u001a\u00020 8\u0016@VX\u0096\u000e\u00a2\u0006\u0012\n\u0004\u0008\"\u0010#\u001a\u0004\u0008$\u0010%\"\u0004\u0008&\u0010\'R*\u0010,\u001a\u00020 2\u0006\u0010!\u001a\u00020 8\u0016@VX\u0096\u000e\u00a2\u0006\u0012\n\u0004\u0008)\u0010#\u001a\u0004\u0008*\u0010%\"\u0004\u0008+\u0010\'R\u0014\u00100\u001a\u00020-8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u0008.\u0010/\u00a8\u00062"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Lg31/j;",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "state",
        "",
        "setState",
        "(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;)V",
        "Landroid/widget/ProgressBar;",
        "getProgressBar",
        "()Landroid/widget/ProgressBar;",
        "Landroid/widget/TextView;",
        "getCurrentProgressTextView",
        "()Landroid/widget/TextView;",
        "getMaxProgressTextView",
        "s",
        "()V",
        "a",
        "I",
        "space12",
        "Ll31/I;",
        "b",
        "Ll31/I;",
        "binding",
        "",
        "value",
        "c",
        "J",
        "getProgress",
        "()J",
        "setProgress",
        "(J)V",
        "progress",
        "d",
        "getMaxProgress",
        "setMaxProgress",
        "maxProgress",
        "Landroid/view/View;",
        "getView",
        "()Landroid/view/View;",
        "view",
        "e",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final e:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final f:I


# instance fields
.field public final a:I

.field public final b:Ll31/I;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:J

.field public d:J


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->e:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->f:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 4
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_12:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->a:I

    .line 6
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p3

    invoke-static {p3, p0}, Ll31/I;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Ll31/I;

    move-result-object p3

    iput-object p3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->b:Ll31/I;

    const/4 v0, 0x1

    .line 7
    invoke-virtual {p0, v0}, Landroid/view/View;->setClipToOutline(Z)V

    .line 8
    sget v1, LlZ0/h;->rounded_full:I

    invoke-static {p1, v1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v1

    invoke-virtual {p0, v1}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 9
    sget v1, LlZ0/d;->uikitBackgroundContent:I

    const/4 v2, 0x0

    const/4 v3, 0x2

    invoke-static {p1, v1, v2, v3, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v1

    .line 10
    invoke-static {v1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v1

    invoke-static {p0, v1}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 11
    new-instance v1, Landroid/widget/FrameLayout$LayoutParams;

    const/4 v2, -0x1

    const/4 v3, -0x2

    invoke-direct {v1, v2, v3}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 12
    invoke-virtual {p0, p2, p2, p2, p2}, Landroid/view/View;->setPadding(IIII)V

    .line 13
    invoke-virtual {p0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 14
    iget-object p2, p3, Ll31/I;->e:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;

    const/16 v1, 0x64

    invoke-virtual {p2, v1}, Landroid/widget/ProgressBar;->setMax(I)V

    .line 15
    iget-object p2, p3, Ll31/I;->e:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;

    .line 16
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    invoke-virtual {p1}, Landroid/content/res/Resources;->getConfiguration()Landroid/content/res/Configuration;

    move-result-object p1

    invoke-virtual {p1}, Landroid/content/res/Configuration;->getLayoutDirection()I

    move-result p1

    if-ne p1, v0, :cond_0

    .line 17
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;->COUNTER_CLOCKWISE:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;

    goto :goto_0

    .line 18
    :cond_0
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;->CLOCKWISE:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;

    .line 19
    :goto_0
    invoke-virtual {p2, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->setProgressDirection(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public getCurrentProgressTextView()Landroid/widget/TextView;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getMaxProgress()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->d:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public getMaxProgressTextView()Landroid/widget/TextView;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getProgress()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->c:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public getProgressBar()Landroid/widget/ProgressBar;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->b:Ll31/I;

    .line 2
    .line 3
    iget-object v0, v0, Ll31/I;->e:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;

    .line 4
    .line 5
    return-object v0
.end method

.method public getView()Landroid/view/View;
    .locals 0
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    return-object p0
.end method

.method public final s()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->getProgress()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    long-to-float v0, v0

    .line 6
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->getMaxProgress()J

    .line 7
    .line 8
    .line 9
    move-result-wide v1

    .line 10
    long-to-float v1, v1

    .line 11
    div-float/2addr v0, v1

    .line 12
    const/16 v1, 0x64

    .line 13
    .line 14
    int-to-float v1, v1

    .line 15
    mul-float v0, v0, v1

    .line 16
    .line 17
    const/high16 v1, 0x40000000    # 2.0f

    .line 18
    .line 19
    cmpg-float v1, v0, v1

    .line 20
    .line 21
    if-gez v1, :cond_0

    .line 22
    .line 23
    const/4 v1, 0x0

    .line 24
    cmpl-float v1, v0, v1

    .line 25
    .line 26
    if-lez v1, :cond_0

    .line 27
    .line 28
    const/4 v0, 0x2

    .line 29
    goto :goto_0

    .line 30
    :cond_0
    const/high16 v1, 0x42c40000    # 98.0f

    .line 31
    .line 32
    cmpl-float v1, v0, v1

    .line 33
    .line 34
    if-lez v1, :cond_1

    .line 35
    .line 36
    const/high16 v1, 0x42c80000    # 100.0f

    .line 37
    .line 38
    cmpg-float v1, v0, v1

    .line 39
    .line 40
    if-gez v1, :cond_1

    .line 41
    .line 42
    const/16 v0, 0x62

    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_1
    float-to-int v0, v0

    .line 46
    :goto_0
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->b:Ll31/I;

    .line 47
    .line 48
    iget-object v1, v1, Ll31/I;->e:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;

    .line 49
    .line 50
    invoke-virtual {v1, v0}, Landroid/widget/ProgressBar;->setProgress(I)V

    .line 51
    .line 52
    .line 53
    return-void
.end method

.method public setMaxProgress(J)V
    .locals 0

    .line 1
    iput-wide p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->d:J

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->s()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setProgress(J)V
    .locals 0

    .line 1
    iput-wide p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->c:J

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->s()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;)V
    .locals 7
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->b:Ll31/I;

    .line 3
    iget-object v1, v0, Ll31/I;->h:Landroid/widget/TextView;

    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;->a()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object v1, v0, Ll31/I;->i:Landroid/widget/TextView;

    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;->b()Lg31/b;

    move-result-object v2

    invoke-virtual {v2}, Lg31/b;->e()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 5
    sget-object v1, Lg31/i;->a:Lg31/i;

    iget-object v2, v0, Ll31/I;->c:Landroid/widget/ImageView;

    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;->b()Lg31/b;

    move-result-object v3

    invoke-virtual {v3}, Lg31/b;->d()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lg31/i;->a(Landroid/widget/ImageView;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;)V

    .line 6
    iget-object v2, v0, Ll31/I;->f:Lorg/xbet/uikit/components/tag/Tag;

    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;->b()Lg31/b;

    move-result-object v3

    invoke-virtual {v3}, Lg31/b;->a()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 7
    iget-object v2, v0, Ll31/I;->g:Lorg/xbet/uikit/components/tag/Tag;

    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;->b()Lg31/b;

    move-result-object v3

    invoke-virtual {v3}, Lg31/b;->b()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 8
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;->e()J

    move-result-wide v2

    invoke-virtual {p0, v2, v3}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->setProgress(J)V

    .line 9
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;->c()J

    move-result-wide v2

    invoke-virtual {p0, v2, v3}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->setMaxProgress(J)V

    .line 10
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;->d()Lg31/b;

    move-result-object p1

    .line 11
    iget-object v2, v0, Ll31/I;->b:Landroid/widget/ImageView;

    const/4 v3, 0x1

    const/4 v4, 0x0

    if-eqz p1, :cond_0

    const/4 v5, 0x1

    goto :goto_0

    :cond_0
    const/4 v5, 0x0

    :goto_0
    const/16 v6, 0x8

    if-eqz v5, :cond_1

    const/4 v5, 0x0

    goto :goto_1

    :cond_1
    const/16 v5, 0x8

    .line 12
    :goto_1
    invoke-virtual {v2, v5}, Landroid/view/View;->setVisibility(I)V

    .line 13
    iget-object v2, v0, Ll31/I;->d:Landroid/widget/ImageView;

    if-eqz p1, :cond_2

    const/4 v5, 0x1

    goto :goto_2

    :cond_2
    const/4 v5, 0x0

    :goto_2
    if-eqz v5, :cond_3

    const/4 v5, 0x0

    goto :goto_3

    :cond_3
    const/16 v5, 0x8

    .line 14
    :goto_3
    invoke-virtual {v2, v5}, Landroid/view/View;->setVisibility(I)V

    .line 15
    iget-object v2, v0, Ll31/I;->e:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;

    if-eqz p1, :cond_4

    goto :goto_4

    :cond_4
    const/4 v3, 0x0

    :goto_4
    if-eqz v3, :cond_5

    goto :goto_5

    :cond_5
    const/16 v4, 0x8

    .line 16
    :goto_5
    invoke-virtual {v2, v4}, Landroid/view/View;->setVisibility(I)V

    if-eqz p1, :cond_6

    .line 17
    iget-object v0, v0, Ll31/I;->d:Landroid/widget/ImageView;

    invoke-virtual {p1}, Lg31/b;->d()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;

    move-result-object p1

    invoke-virtual {v1, v0, p1}, Lg31/i;->a(Landroid/widget/ImageView;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;)V

    :cond_6
    return-void
.end method

.method public bridge synthetic setState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c;)V
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->setState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;)V

    return-void
.end method
