.class public final synthetic Lorg/xbet/ui_common/moxy/activities/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/ui_common/moxy/activities/IntellijActivity;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/ui_common/moxy/activities/e;->a:Lorg/xbet/ui_common/moxy/activities/IntellijActivity;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/moxy/activities/e;->a:Lorg/xbet/ui_common/moxy/activities/IntellijActivity;

    check-cast p1, <PERSON>ja<PERSON>/lang/Boolean;

    invoke-static {v0, p1}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->O(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;Ljava/lang/Boolean;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
