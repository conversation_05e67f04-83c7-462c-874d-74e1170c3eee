.class public final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.my_aggregator.presentation.viewmodels.MyAggregatorViewModel$special$$inlined$combine$2$3"
    f = "MyAggregatorViewModel.kt"
    l = {
        0xea
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2;->collect(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lkotlinx/coroutines/flow/f<",
        "-",
        "Ljava/util/List<",
        "+",
        "LVX0/i;",
        ">;>;[",
        "Ljava/lang/Object;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0011\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u0010\u0006\u001a\u00020\u0005\"\u0004\u0008\u0000\u0010\u0000\"\u0006\u0008\u0001\u0010\u0001\u0018\u0001*\u0008\u0012\u0004\u0012\u00028\u00000\u00022\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00028\u00010\u0003H\n\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "R",
        "T",
        "Lkotlinx/coroutines/flow/f;",
        "",
        "it",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/flow/f;Lkotlin/Array;)V",
        "com/xbet/onexcore/utils/flows/FlowBuilderKt$combine$$inlined$combine$1$3"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field private synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;


# direct methods
.method public constructor <init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V
    .locals 0

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    const/4 p2, 0x3

    invoke-direct {p0, p2, p1}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/flow/f;

    check-cast p2, [Ljava/lang/Object;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->invoke(Lkotlinx/coroutines/flow/f;[Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/flow/f;[Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/f<",
            "-",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;>;[",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    invoke-direct {v0, p3, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;-><init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    move-result-object v2

    .line 8
    iget v3, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->label:I

    .line 9
    .line 10
    if-eqz v3, :cond_1

    .line 11
    .line 12
    if-ne v3, v1, :cond_0

    .line 13
    .line 14
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    goto/16 :goto_c

    .line 18
    .line 19
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw v1

    .line 27
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    iget-object v3, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->L$0:Ljava/lang/Object;

    .line 31
    .line 32
    check-cast v3, Lkotlinx/coroutines/flow/f;

    .line 33
    .line 34
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->L$1:Ljava/lang/Object;

    .line 35
    .line 36
    check-cast v4, [Ljava/lang/Object;

    .line 37
    .line 38
    const/4 v5, 0x0

    .line 39
    aget-object v6, v4, v5

    .line 40
    .line 41
    aget-object v7, v4, v1

    .line 42
    .line 43
    const/4 v8, 0x2

    .line 44
    aget-object v8, v4, v8

    .line 45
    .line 46
    const/4 v9, 0x3

    .line 47
    aget-object v9, v4, v9

    .line 48
    .line 49
    const/4 v10, 0x4

    .line 50
    aget-object v10, v4, v10

    .line 51
    .line 52
    const/4 v11, 0x5

    .line 53
    aget-object v4, v4, v11

    .line 54
    .line 55
    move-object v12, v4

    .line 56
    check-cast v12, LG81/e$b;

    .line 57
    .line 58
    check-cast v10, Ljava/lang/Boolean;

    .line 59
    .line 60
    invoke-virtual {v10}, Ljava/lang/Boolean;->booleanValue()Z

    .line 61
    .line 62
    .line 63
    move-result v4

    .line 64
    check-cast v9, Ljava/lang/Boolean;

    .line 65
    .line 66
    invoke-virtual {v9}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 67
    .line 68
    .line 69
    check-cast v8, Ljava/lang/Boolean;

    .line 70
    .line 71
    invoke-virtual {v8}, Ljava/lang/Boolean;->booleanValue()Z

    .line 72
    .line 73
    .line 74
    move-result v8

    .line 75
    check-cast v7, Ljava/util/List;

    .line 76
    .line 77
    check-cast v6, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b;

    .line 78
    .line 79
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 80
    .line 81
    invoke-static {v9}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->E4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 82
    .line 83
    .line 84
    move-result-object v9

    .line 85
    instance-of v10, v12, LG81/e$b$c;

    .line 86
    .line 87
    xor-int/2addr v10, v1

    .line 88
    invoke-static {v10}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 89
    .line 90
    .line 91
    move-result-object v10

    .line 92
    invoke-interface {v9, v10}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 93
    .line 94
    .line 95
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 96
    .line 97
    invoke-static {v9}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->L4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lp9/c;

    .line 98
    .line 99
    .line 100
    move-result-object v9

    .line 101
    invoke-virtual {v9}, Lp9/c;->a()Z

    .line 102
    .line 103
    .line 104
    move-result v9

    .line 105
    if-eqz v9, :cond_4

    .line 106
    .line 107
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 108
    .line 109
    invoke-static {v10}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->x4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/util/List;

    .line 110
    .line 111
    .line 112
    move-result-object v11

    .line 113
    invoke-static {v10, v8, v11}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->I5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;ZLjava/util/List;)V

    .line 114
    .line 115
    .line 116
    if-nez v8, :cond_2

    .line 117
    .line 118
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 119
    .line 120
    invoke-static {v10}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->b5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 121
    .line 122
    .line 123
    move-result-object v10

    .line 124
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 125
    .line 126
    invoke-static {v11}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->K4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/util/List;

    .line 127
    .line 128
    .line 129
    move-result-object v11

    .line 130
    invoke-interface {v10, v11}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 131
    .line 132
    .line 133
    goto/16 :goto_2

    .line 134
    .line 135
    :cond_2
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 136
    .line 137
    invoke-static {v10}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->b5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 138
    .line 139
    .line 140
    move-result-object v10

    .line 141
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 142
    .line 143
    invoke-static {v11}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->I4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 144
    .line 145
    .line 146
    move-result-object v11

    .line 147
    invoke-interface {v11}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 148
    .line 149
    .line 150
    move-result-object v11

    .line 151
    check-cast v11, Ljava/lang/Boolean;

    .line 152
    .line 153
    invoke-virtual {v11}, Ljava/lang/Boolean;->booleanValue()Z

    .line 154
    .line 155
    .line 156
    move-result v11

    .line 157
    if-eqz v11, :cond_3

    .line 158
    .line 159
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 160
    .line 161
    .line 162
    move-result-object v11

    .line 163
    goto :goto_0

    .line 164
    :cond_3
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 165
    .line 166
    invoke-static {v11}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->o5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)[Lra1/b;

    .line 167
    .line 168
    .line 169
    move-result-object v11

    .line 170
    invoke-static {v11}, Lkotlin/collections/r;->f0([Ljava/lang/Object;)Ljava/util/List;

    .line 171
    .line 172
    .line 173
    move-result-object v11

    .line 174
    invoke-static {v11}, Lkotlin/collections/CollectionsKt;->z1(Ljava/lang/Iterable;)Ljava/util/List;

    .line 175
    .line 176
    .line 177
    move-result-object v11

    .line 178
    :goto_0
    invoke-interface {v10, v11}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 179
    .line 180
    .line 181
    goto :goto_2

    .line 182
    :cond_4
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 183
    .line 184
    invoke-static {v10}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->x4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/util/List;

    .line 185
    .line 186
    .line 187
    move-result-object v11

    .line 188
    invoke-static {v10, v4, v11}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->I5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;ZLjava/util/List;)V

    .line 189
    .line 190
    .line 191
    if-nez v4, :cond_5

    .line 192
    .line 193
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 194
    .line 195
    invoke-static {v10}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->b5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 196
    .line 197
    .line 198
    move-result-object v10

    .line 199
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 200
    .line 201
    invoke-static {v11}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->K4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/util/List;

    .line 202
    .line 203
    .line 204
    move-result-object v11

    .line 205
    invoke-interface {v10, v11}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 206
    .line 207
    .line 208
    goto :goto_2

    .line 209
    :cond_5
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 210
    .line 211
    invoke-static {v10}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->b5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 212
    .line 213
    .line 214
    move-result-object v10

    .line 215
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 216
    .line 217
    invoke-static {v11}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->I4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 218
    .line 219
    .line 220
    move-result-object v11

    .line 221
    invoke-interface {v11}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 222
    .line 223
    .line 224
    move-result-object v11

    .line 225
    check-cast v11, Ljava/lang/Boolean;

    .line 226
    .line 227
    invoke-virtual {v11}, Ljava/lang/Boolean;->booleanValue()Z

    .line 228
    .line 229
    .line 230
    move-result v11

    .line 231
    if-eqz v11, :cond_6

    .line 232
    .line 233
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 234
    .line 235
    .line 236
    move-result-object v11

    .line 237
    goto :goto_1

    .line 238
    :cond_6
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 239
    .line 240
    invoke-static {v11}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->o5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)[Lra1/b;

    .line 241
    .line 242
    .line 243
    move-result-object v11

    .line 244
    invoke-static {v11}, Lkotlin/collections/r;->f0([Ljava/lang/Object;)Ljava/util/List;

    .line 245
    .line 246
    .line 247
    move-result-object v11

    .line 248
    invoke-static {v11}, Lkotlin/collections/CollectionsKt;->z1(Ljava/lang/Iterable;)Ljava/util/List;

    .line 249
    .line 250
    .line 251
    move-result-object v11

    .line 252
    :goto_1
    invoke-interface {v10, v11}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 253
    .line 254
    .line 255
    :goto_2
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 256
    .line 257
    invoke-static {v10}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->U4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Z

    .line 258
    .line 259
    .line 260
    move-result v10

    .line 261
    if-eqz v10, :cond_13

    .line 262
    .line 263
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 264
    .line 265
    .line 266
    move-result-object v10

    .line 267
    if-eqz v8, :cond_7

    .line 268
    .line 269
    if-eqz v9, :cond_7

    .line 270
    .line 271
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 272
    .line 273
    const/4 v15, 0x3

    .line 274
    const/16 v16, 0x0

    .line 275
    .line 276
    const/4 v13, 0x0

    .line 277
    const/4 v14, 0x0

    .line 278
    invoke-static/range {v11 .. v16}, LG81/e$c;->a(LG81/e;LG81/e$b;ZZILjava/lang/Object;)LVX0/i;

    .line 279
    .line 280
    .line 281
    move-result-object v11

    .line 282
    if-eqz v11, :cond_8

    .line 283
    .line 284
    invoke-interface {v10, v11}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 285
    .line 286
    .line 287
    move-result v11

    .line 288
    invoke-static {v11}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 289
    .line 290
    .line 291
    goto :goto_3

    .line 292
    :cond_7
    if-eqz v4, :cond_8

    .line 293
    .line 294
    if-nez v9, :cond_8

    .line 295
    .line 296
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 297
    .line 298
    const/4 v15, 0x3

    .line 299
    const/16 v16, 0x0

    .line 300
    .line 301
    const/4 v13, 0x0

    .line 302
    const/4 v14, 0x0

    .line 303
    invoke-static/range {v11 .. v16}, LG81/e$c;->a(LG81/e;LG81/e$b;ZZILjava/lang/Object;)LVX0/i;

    .line 304
    .line 305
    .line 306
    move-result-object v11

    .line 307
    if-eqz v11, :cond_8

    .line 308
    .line 309
    invoke-interface {v10, v11}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 310
    .line 311
    .line 312
    move-result v11

    .line 313
    invoke-static {v11}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 314
    .line 315
    .line 316
    :cond_8
    :goto_3
    invoke-interface {v10, v7}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 317
    .line 318
    .line 319
    invoke-interface {v10}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 320
    .line 321
    .line 322
    move-result-object v7

    .line 323
    const/4 v11, 0x0

    .line 324
    :goto_4
    invoke-interface {v7}, Ljava/util/Iterator;->hasNext()Z

    .line 325
    .line 326
    .line 327
    move-result v12

    .line 328
    const/4 v13, -0x1

    .line 329
    if-eqz v12, :cond_a

    .line 330
    .line 331
    invoke-interface {v7}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 332
    .line 333
    .line 334
    move-result-object v12

    .line 335
    check-cast v12, LVX0/i;

    .line 336
    .line 337
    instance-of v12, v12, LF81/c;

    .line 338
    .line 339
    if-eqz v12, :cond_9

    .line 340
    .line 341
    goto :goto_5

    .line 342
    :cond_9
    add-int/2addr v11, v1

    .line 343
    goto :goto_4

    .line 344
    :cond_a
    const/4 v11, -0x1

    .line 345
    :goto_5
    invoke-interface {v10}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 346
    .line 347
    .line 348
    move-result-object v7

    .line 349
    :goto_6
    invoke-interface {v7}, Ljava/util/Iterator;->hasNext()Z

    .line 350
    .line 351
    .line 352
    move-result v12

    .line 353
    if-eqz v12, :cond_c

    .line 354
    .line 355
    invoke-interface {v7}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 356
    .line 357
    .line 358
    move-result-object v12

    .line 359
    check-cast v12, LVX0/i;

    .line 360
    .line 361
    instance-of v14, v12, Lra1/b;

    .line 362
    .line 363
    if-eqz v14, :cond_b

    .line 364
    .line 365
    check-cast v12, Lra1/b;

    .line 366
    .line 367
    invoke-virtual {v12}, Lra1/b;->e()Lra1/c;

    .line 368
    .line 369
    .line 370
    move-result-object v12

    .line 371
    sget-object v14, Lra1/c$f;->c:Lra1/c$f;

    .line 372
    .line 373
    invoke-static {v12, v14}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 374
    .line 375
    .line 376
    move-result v12

    .line 377
    if-eqz v12, :cond_b

    .line 378
    .line 379
    goto :goto_7

    .line 380
    :cond_b
    add-int/2addr v5, v1

    .line 381
    goto :goto_6

    .line 382
    :cond_c
    const/4 v5, -0x1

    .line 383
    :goto_7
    if-eq v5, v13, :cond_d

    .line 384
    .line 385
    add-int/2addr v5, v1

    .line 386
    goto :goto_8

    .line 387
    :cond_d
    add-int/lit8 v5, v11, 0x1

    .line 388
    .line 389
    :goto_8
    if-eqz v9, :cond_12

    .line 390
    .line 391
    instance-of v7, v6, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$b;

    .line 392
    .line 393
    if-eqz v7, :cond_f

    .line 394
    .line 395
    if-eqz v8, :cond_e

    .line 396
    .line 397
    check-cast v6, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$b;

    .line 398
    .line 399
    invoke-virtual {v6}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$b;->a()Lra1/a;

    .line 400
    .line 401
    .line 402
    move-result-object v6

    .line 403
    invoke-interface {v10, v5, v6}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 404
    .line 405
    .line 406
    :cond_e
    sget-object v5, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 407
    .line 408
    goto :goto_9

    .line 409
    :cond_f
    sget-object v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$a;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$a;

    .line 410
    .line 411
    invoke-static {v6, v5}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 412
    .line 413
    .line 414
    move-result v5

    .line 415
    if-eqz v5, :cond_10

    .line 416
    .line 417
    sget-object v5, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 418
    .line 419
    goto :goto_9

    .line 420
    :cond_10
    sget-object v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$c;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$c;

    .line 421
    .line 422
    invoke-static {v6, v5}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 423
    .line 424
    .line 425
    move-result v5

    .line 426
    if-eqz v5, :cond_11

    .line 427
    .line 428
    new-instance v5, Lra1/a$c;

    .line 429
    .line 430
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 431
    .line 432
    invoke-static {v6}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->q5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 433
    .line 434
    .line 435
    move-result-object v6

    .line 436
    invoke-direct {v5, v6}, Lra1/a$c;-><init>(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;)V

    .line 437
    .line 438
    .line 439
    goto :goto_9

    .line 440
    :cond_11
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    .line 441
    .line 442
    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 443
    .line 444
    .line 445
    throw v1

    .line 446
    :cond_12
    :goto_9
    invoke-static {v10}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 447
    .line 448
    .line 449
    move-result-object v5

    .line 450
    goto :goto_b

    .line 451
    :cond_13
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 452
    .line 453
    .line 454
    move-result-object v5

    .line 455
    if-eqz v9, :cond_18

    .line 456
    .line 457
    instance-of v10, v6, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$b;

    .line 458
    .line 459
    if-eqz v10, :cond_15

    .line 460
    .line 461
    if-eqz v8, :cond_14

    .line 462
    .line 463
    check-cast v6, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$b;

    .line 464
    .line 465
    invoke-virtual {v6}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$b;->a()Lra1/a;

    .line 466
    .line 467
    .line 468
    move-result-object v6

    .line 469
    invoke-interface {v5, v6}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 470
    .line 471
    .line 472
    :cond_14
    sget-object v6, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 473
    .line 474
    goto :goto_a

    .line 475
    :cond_15
    sget-object v10, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$a;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$a;

    .line 476
    .line 477
    invoke-static {v6, v10}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 478
    .line 479
    .line 480
    move-result v10

    .line 481
    if-eqz v10, :cond_16

    .line 482
    .line 483
    sget-object v6, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 484
    .line 485
    goto :goto_a

    .line 486
    :cond_16
    sget-object v10, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$c;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$c;

    .line 487
    .line 488
    invoke-static {v6, v10}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 489
    .line 490
    .line 491
    move-result v6

    .line 492
    if-eqz v6, :cond_17

    .line 493
    .line 494
    new-instance v6, Lra1/a$c;

    .line 495
    .line 496
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 497
    .line 498
    invoke-static {v10}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->q5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 499
    .line 500
    .line 501
    move-result-object v10

    .line 502
    invoke-direct {v6, v10}, Lra1/a$c;-><init>(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;)V

    .line 503
    .line 504
    .line 505
    goto :goto_a

    .line 506
    :cond_17
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    .line 507
    .line 508
    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 509
    .line 510
    .line 511
    throw v1

    .line 512
    :cond_18
    :goto_a
    invoke-interface {v5, v7}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 513
    .line 514
    .line 515
    invoke-static {v5}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 516
    .line 517
    .line 518
    move-result-object v5

    .line 519
    :goto_b
    if-eqz v9, :cond_19

    .line 520
    .line 521
    if-nez v8, :cond_1a

    .line 522
    .line 523
    :cond_19
    if-nez v9, :cond_1b

    .line 524
    .line 525
    if-eqz v4, :cond_1b

    .line 526
    .line 527
    :cond_1a
    invoke-interface {v5}, Ljava/util/List;->isEmpty()Z

    .line 528
    .line 529
    .line 530
    move-result v4

    .line 531
    if-eqz v4, :cond_1b

    .line 532
    .line 533
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 534
    .line 535
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->x4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/util/List;

    .line 536
    .line 537
    .line 538
    move-result-object v4

    .line 539
    invoke-interface {v4}, Ljava/util/List;->isEmpty()Z

    .line 540
    .line 541
    .line 542
    move-result v4

    .line 543
    if-eqz v4, :cond_1b

    .line 544
    .line 545
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 546
    .line 547
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->I4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 548
    .line 549
    .line 550
    move-result-object v4

    .line 551
    invoke-static {v1}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 552
    .line 553
    .line 554
    move-result-object v6

    .line 555
    invoke-interface {v4, v6}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 556
    .line 557
    .line 558
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 559
    .line 560
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->y4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 561
    .line 562
    .line 563
    move-result-object v4

    .line 564
    invoke-static {v1}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 565
    .line 566
    .line 567
    move-result-object v6

    .line 568
    invoke-interface {v4, v6}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 569
    .line 570
    .line 571
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 572
    .line 573
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->v4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 574
    .line 575
    .line 576
    move-result-object v4

    .line 577
    invoke-static {v1}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 578
    .line 579
    .line 580
    move-result-object v6

    .line 581
    invoke-interface {v4, v6}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 582
    .line 583
    .line 584
    :cond_1b
    iput v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2$3;->label:I

    .line 585
    .line 586
    invoke-interface {v3, v5, v0}, Lkotlinx/coroutines/flow/f;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 587
    .line 588
    .line 589
    move-result-object v1

    .line 590
    if-ne v1, v2, :cond_1c

    .line 591
    .line 592
    return-object v2

    .line 593
    :cond_1c
    :goto_c
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 594
    .line 595
    return-object v1
.end method
