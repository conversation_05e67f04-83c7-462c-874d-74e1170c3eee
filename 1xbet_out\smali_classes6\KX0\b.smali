.class public final synthetic LKX0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Ljava/util/List;

.field public final synthetic b:I

.field public final synthetic c:J

.field public final synthetic d:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Ljava/util/List;IJLjava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LKX0/b;->a:Ljava/util/List;

    iput p2, p0, LKX0/b;->b:I

    iput-wide p3, p0, LKX0/b;->c:J

    iput-object p5, p0, LKX0/b;->d:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    iget-object v0, p0, LKX0/b;->a:Ljava/util/List;

    iget v1, p0, LKX0/b;->b:I

    iget-wide v2, p0, LKX0/b;->c:J

    iget-object v4, p0, LKX0/b;->d:Ljava/lang/String;

    move-object v5, p1

    check-cast v5, Lrc/g;

    invoke-static/range {v0 .. v5}, LKX0/m;->d(Ljava/util/List;IJLjava/lang/String;Lrc/g;)LRe/b;

    move-result-object p1

    return-object p1
.end method
