.class public final Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportLarge;
.super Lorg/xbet/uikit_sport/sport_cell/DsSportCell;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportLarge$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0007\u0018\u00002\u00020\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0015\u0010\u000f\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0015\u0010\u0013\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\u0011\u00a2\u0006\u0004\u0008\u0013\u0010\u0014R\u0018\u0010\u0012\u001a\u0004\u0018\u00010\u00118\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0016\u00a8\u0006\u0017"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportLarge;",
        "Lorg/xbet/uikit_sport/sport_cell/DsSportCell;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "j",
        "()V",
        "",
        "showListCheckBox",
        "setSportStyle",
        "(Z)V",
        "Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;",
        "componentStyle",
        "setComponentStyle",
        "(Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;)V",
        "l",
        "Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public l:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportLarge;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportLarge;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportLarge;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method private final j()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportLarge;->l:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    sget-object v1, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportLarge$a;->a:[I

    .line 7
    .line 8
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    aget v0, v1, v0

    .line 13
    .line 14
    const/4 v1, 0x1

    .line 15
    const/4 v2, 0x0

    .line 16
    if-eq v0, v1, :cond_6

    .line 17
    .line 18
    const/4 v1, 0x2

    .line 19
    if-eq v0, v1, :cond_5

    .line 20
    .line 21
    const/4 v1, 0x3

    .line 22
    if-eq v0, v1, :cond_3

    .line 23
    .line 24
    const/4 v1, 0x4

    .line 25
    if-ne v0, v1, :cond_2

    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    if-eqz v0, :cond_1

    .line 32
    .line 33
    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->LIST_CHECKBOX:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 34
    .line 35
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 36
    .line 37
    .line 38
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    if-eqz v0, :cond_8

    .line 43
    .line 44
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 45
    .line 46
    .line 47
    return-void

    .line 48
    :cond_2
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 49
    .line 50
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 51
    .line 52
    .line 53
    throw v0

    .line 54
    :cond_3
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    if-eqz v0, :cond_4

    .line 59
    .line 60
    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->ACCORDION:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 61
    .line 62
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 63
    .line 64
    .line 65
    :cond_4
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    if-eqz v0, :cond_8

    .line 70
    .line 71
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 72
    .line 73
    .line 74
    return-void

    .line 75
    :cond_5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 76
    .line 77
    .line 78
    move-result-object v0

    .line 79
    if-eqz v0, :cond_8

    .line 80
    .line 81
    const/16 v1, 0x8

    .line 82
    .line 83
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 84
    .line 85
    .line 86
    return-void

    .line 87
    :cond_6
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    if-eqz v0, :cond_7

    .line 92
    .line 93
    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->LIST_CHECKBOX:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 94
    .line 95
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 96
    .line 97
    .line 98
    :cond_7
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 99
    .line 100
    .line 101
    move-result-object v0

    .line 102
    if-eqz v0, :cond_8

    .line 103
    .line 104
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 105
    .line 106
    .line 107
    :cond_8
    :goto_0
    return-void
.end method


# virtual methods
.method public final setComponentStyle(Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportLarge;->l:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportLarge;->j()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setSportStyle(Z)V
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    sget-object p1, Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;->SPORT_COUNTER_WITH_LIST_CHECKBOX:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    sget-object p1, Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;->SPORT_COUNTER:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;

    .line 7
    .line 8
    :goto_0
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportLarge;->setComponentStyle(Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method
