.class public Lorg/spongycastle/util/test/TestFailedException;
.super Ljava/lang/RuntimeException;
.source "SourceFile"


# instance fields
.field private _result:Lorg/spongycastle/util/test/a;


# direct methods
.method public constructor <init>(Lorg/spongycastle/util/test/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/RuntimeException;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public getResult()Lorg/spongycastle/util/test/a;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method
