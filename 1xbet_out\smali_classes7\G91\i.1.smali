.class public final synthetic LG91/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LOc/n;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;

    check-cast p2, Ljava/util/List;

    check-cast p3, Ljava/lang/Integer;

    invoke-virtual {p3}, Ljava/lang/Integer;->intValue()I

    move-result p3

    invoke-static {p1, p2, p3}, Lorg/xplatform/aggregator/impl/category/presentation/filters/delegates/FiltersChipsTypeProviderChipsAdapterDelegateKt;->c(Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;Ljava/util/List;I)Z

    move-result p1

    invoke-static {p1}, <PERSON>java/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method
