.class public final LtW0/c$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LtW0/m$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LtW0/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LtW0/d;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LtW0/c$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lak/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;LwW0/o;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;LyW0/a;LwW0/c;Lorg/xbet/remoteconfig/domain/usecases/i;LzX0/k;)LtW0/m;
    .locals 16

    .line 1
    invoke-static/range {p1 .. p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static/range {p2 .. p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    invoke-static/range {p10 .. p10}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    invoke-static/range {p12 .. p12}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    invoke-static/range {p13 .. p13}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    invoke-static/range {p14 .. p14}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 41
    .line 42
    .line 43
    new-instance v0, LtW0/c$b;

    .line 44
    .line 45
    const/4 v15, 0x0

    .line 46
    move-object/from16 v1, p1

    .line 47
    .line 48
    move-object/from16 v2, p2

    .line 49
    .line 50
    move-object/from16 v3, p3

    .line 51
    .line 52
    move-object/from16 v4, p4

    .line 53
    .line 54
    move-object/from16 v5, p5

    .line 55
    .line 56
    move-object/from16 v6, p6

    .line 57
    .line 58
    move-object/from16 v7, p7

    .line 59
    .line 60
    move-object/from16 v8, p8

    .line 61
    .line 62
    move-object/from16 v9, p9

    .line 63
    .line 64
    move-object/from16 v10, p10

    .line 65
    .line 66
    move-object/from16 v11, p11

    .line 67
    .line 68
    move-object/from16 v12, p12

    .line 69
    .line 70
    move-object/from16 v13, p13

    .line 71
    .line 72
    move-object/from16 v14, p14

    .line 73
    .line 74
    invoke-direct/range {v0 .. v15}, LtW0/c$b;-><init>(Lak/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;LwW0/o;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;LyW0/a;LwW0/c;Lorg/xbet/remoteconfig/domain/usecases/i;LzX0/k;LtW0/d;)V

    .line 75
    .line 76
    .line 77
    return-object v0
.end method
