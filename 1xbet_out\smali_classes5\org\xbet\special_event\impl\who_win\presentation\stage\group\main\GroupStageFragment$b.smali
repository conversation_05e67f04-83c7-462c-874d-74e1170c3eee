.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$b;
.super Landroidx/viewpager2/widget/ViewPager2$i;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;-><init>()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0017\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "org/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$b",
        "Landroidx/viewpager2/widget/ViewPager2$i;",
        "",
        "position",
        "",
        "onPageSelected",
        "(I)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic b:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$b;->b:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    .line 2
    .line 3
    invoke-direct {p0}, Landroidx/viewpager2/widget/ViewPager2$i;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onPageSelected(I)V
    .locals 2

    .line 1
    invoke-super {p0, p1}, Landroidx/viewpager2/widget/ViewPager2$i;->onPageSelected(I)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$b;->b:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    .line 5
    .line 6
    invoke-static {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->J2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-virtual {v0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->t3(I)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$b;->b:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    .line 14
    .line 15
    invoke-static {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->G2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$b;->b:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    .line 20
    .line 21
    invoke-static {v1, p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->F2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;I)I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    invoke-virtual {v0, p1, v1}, Landroidx/recyclerview/widget/LinearLayoutManager;->scrollToPositionWithOffset(II)V

    .line 26
    .line 27
    .line 28
    return-void
.end method
