.class final Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.promo.presentation.OneXGamesPromoFragment$onObserveData$3"
    f = "OneXGamesPromoFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "<PERSON>kotlin/jvm/functions/Function2<",
        "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;

    iget-object v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;->invoke(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_4

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$d;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;

    .line 20
    .line 21
    check-cast p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$d;

    .line 22
    .line 23
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$d;->a()Ljava/util/List;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-static {v0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->K2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Ljava/util/List;)V

    .line 28
    .line 29
    .line 30
    goto :goto_0

    .line 31
    :cond_0
    instance-of v0, p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$c;

    .line 32
    .line 33
    if-eqz v0, :cond_1

    .line 34
    .line 35
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;

    .line 36
    .line 37
    invoke-static {v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->G2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Lm50/a;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    iget-object v0, v0, Lm50/a;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 42
    .line 43
    check-cast p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$c;

    .line 44
    .line 45
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$c;->a()Ljava/lang/String;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setAccountTitle(Ljava/lang/CharSequence;)V

    .line 50
    .line 51
    .line 52
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;

    .line 53
    .line 54
    invoke-static {v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->G2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Lm50/a;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    iget-object v0, v0, Lm50/a;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 59
    .line 60
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$c;->c()Ljava/lang/String;

    .line 61
    .line 62
    .line 63
    move-result-object v1

    .line 64
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$c;->b()Ljava/lang/String;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    invoke-virtual {v0, v1, p1}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setBalanceValue(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 69
    .line 70
    .line 71
    goto :goto_0

    .line 72
    :cond_1
    instance-of v0, p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$b;

    .line 73
    .line 74
    if-eqz v0, :cond_2

    .line 75
    .line 76
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;

    .line 77
    .line 78
    check-cast p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$b;

    .line 79
    .line 80
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$b;->a()Lorg/xbet/uikit/components/lottie/a;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    const/4 v1, 0x1

    .line 85
    invoke-static {v0, v1, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->J2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;ZLorg/xbet/uikit/components/lottie/a;)V

    .line 86
    .line 87
    .line 88
    goto :goto_0

    .line 89
    :cond_2
    instance-of p1, p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$a;

    .line 90
    .line 91
    if-eqz p1, :cond_3

    .line 92
    .line 93
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 94
    .line 95
    return-object p1

    .line 96
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 97
    .line 98
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 99
    .line 100
    .line 101
    throw p1

    .line 102
    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 103
    .line 104
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 105
    .line 106
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 107
    .line 108
    .line 109
    throw p1
.end method
