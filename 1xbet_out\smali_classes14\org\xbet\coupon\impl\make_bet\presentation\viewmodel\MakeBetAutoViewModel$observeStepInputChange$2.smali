.class final Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/q;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.coupon.impl.make_bet.presentation.viewmodel.MakeBetAutoViewModel$observeStepInputChange$2"
    f = "MakeBetAutoViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->F5()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/q<",
        "<PERSON><PERSON><PERSON>/Pair<",
        "+",
        "Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;",
        "+",
        "Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;",
        ">;",
        "Ljava/lang/Boolean;",
        "Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;",
        "Ljava/util/List<",
        "+",
        "Lorg/xbet/betting/core/zip/model/bet/BetInfo;",
        ">;",
        "Ljava/lang/Boolean;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\u0010\u0000\u001a\u00020\u00012\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u00032\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\u0008\u001a\u00020\t2\u000c\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000b2\u0006\u0010\r\u001a\u00020\u0007H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "<destruct>",
        "Lkotlin/Pair;",
        "Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;",
        "Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;",
        "isConnected",
        "",
        "couponType",
        "Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;",
        "<unused var>",
        "",
        "Lorg/xbet/betting/core/zip/model/bet/BetInfo;",
        "isTaxInitialized"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field synthetic Z$0:Z

.field synthetic Z$1:Z

.field label:I

.field final synthetic this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    const/4 p1, 0x6

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    move-object v1, p1

    check-cast v1, Lkotlin/Pair;

    check-cast p2, Ljava/lang/Boolean;

    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v2

    move-object v3, p3

    check-cast v3, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    move-object v4, p4

    check-cast v4, Ljava/util/List;

    check-cast p5, Ljava/lang/Boolean;

    invoke-virtual {p5}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v5

    move-object v6, p6

    check-cast v6, Lkotlin/coroutines/e;

    move-object v0, p0

    invoke-virtual/range {v0 .. v6}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->invoke(Lkotlin/Pair;ZLorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Ljava/util/List;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlin/Pair;ZLorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Ljava/util/List;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/Pair<",
            "Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;",
            "Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;",
            ">;Z",
            "Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;",
            "Ljava/util/List<",
            "Lorg/xbet/betting/core/zip/model/bet/BetInfo;",
            ">;Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance p4, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;

    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    invoke-direct {p4, v0, p6}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, p4, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->L$0:Ljava/lang/Object;

    iput-boolean p2, p4, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->Z$0:Z

    iput-object p3, p4, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->L$1:Ljava/lang/Object;

    iput-boolean p5, p4, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->Z$1:Z

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p4, p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 31

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    iget v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->label:I

    .line 7
    .line 8
    if-nez v1, :cond_4

    .line 9
    .line 10
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->L$0:Ljava/lang/Object;

    .line 14
    .line 15
    check-cast v1, Lkotlin/Pair;

    .line 16
    .line 17
    iget-boolean v2, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->Z$0:Z

    .line 18
    .line 19
    iget-object v3, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->L$1:Ljava/lang/Object;

    .line 20
    .line 21
    check-cast v3, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 22
    .line 23
    iget-boolean v4, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->Z$1:Z

    .line 24
    .line 25
    invoke-virtual {v1}, Lkotlin/Pair;->component1()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v5

    .line 29
    move-object v6, v5

    .line 30
    check-cast v6, Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;

    .line 31
    .line 32
    invoke-virtual {v1}, Lkotlin/Pair;->component2()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    check-cast v1, Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    .line 37
    .line 38
    iget-object v5, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 39
    .line 40
    invoke-static {v5}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->n4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Landroidx/lifecycle/Q;

    .line 41
    .line 42
    .line 43
    move-result-object v5

    .line 44
    const/16 v16, 0xff

    .line 45
    .line 46
    const/16 v17, 0x0

    .line 47
    .line 48
    const/4 v7, 0x0

    .line 49
    const/4 v8, 0x0

    .line 50
    const/4 v9, 0x0

    .line 51
    const/4 v10, 0x0

    .line 52
    const/4 v11, 0x0

    .line 53
    const/4 v12, 0x0

    .line 54
    const/4 v13, 0x0

    .line 55
    const/4 v14, 0x0

    .line 56
    const/4 v15, 0x1

    .line 57
    invoke-static/range {v6 .. v17}, Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;->b(Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;ZZZZILjava/lang/Object;)Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;

    .line 58
    .line 59
    .line 60
    move-result-object v7

    .line 61
    const-string v8, "SAVED_STATE_COEF_KEY"

    .line 62
    .line 63
    invoke-virtual {v5, v8, v7}, Landroidx/lifecycle/Q;->k(Ljava/lang/String;Ljava/lang/Object;)V

    .line 64
    .line 65
    .line 66
    iget-object v5, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 67
    .line 68
    invoke-static {v5}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->n4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Landroidx/lifecycle/Q;

    .line 69
    .line 70
    .line 71
    move-result-object v5

    .line 72
    const v29, 0xbfff

    .line 73
    .line 74
    .line 75
    const/16 v30, 0x0

    .line 76
    .line 77
    const-wide/16 v8, 0x0

    .line 78
    .line 79
    const-wide/16 v10, 0x0

    .line 80
    .line 81
    const-wide/16 v12, 0x0

    .line 82
    .line 83
    const-wide/16 v14, 0x0

    .line 84
    .line 85
    const-wide/16 v16, 0x0

    .line 86
    .line 87
    const/16 v18, 0x0

    .line 88
    .line 89
    const/16 v19, 0x0

    .line 90
    .line 91
    const/16 v20, 0x0

    .line 92
    .line 93
    const/16 v21, 0x0

    .line 94
    .line 95
    const/16 v22, 0x0

    .line 96
    .line 97
    const/16 v23, 0x0

    .line 98
    .line 99
    const/16 v24, 0x0

    .line 100
    .line 101
    const/16 v25, 0x0

    .line 102
    .line 103
    const/16 v26, 0x0

    .line 104
    .line 105
    const/16 v27, 0x1

    .line 106
    .line 107
    const/16 v28, 0x0

    .line 108
    .line 109
    move-object v7, v1

    .line 110
    invoke-static/range {v7 .. v30}, Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;->b(Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;DDDDDLjava/lang/String;ZZLorg/xbet/coupon/impl/make_bet/presentation/model/AutoMaxUiModel;ZZZZZZZILjava/lang/Object;)Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    .line 111
    .line 112
    .line 113
    move-result-object v1

    .line 114
    const-string v8, "SAVED_STATE_BET_SUM_KEY"

    .line 115
    .line 116
    invoke-virtual {v5, v8, v1}, Landroidx/lifecycle/Q;->k(Ljava/lang/String;Ljava/lang/Object;)V

    .line 117
    .line 118
    .line 119
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 120
    .line 121
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->r4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lkotlinx/coroutines/x0;

    .line 122
    .line 123
    .line 124
    move-result-object v1

    .line 125
    const-string v5, "New config"

    .line 126
    .line 127
    if-eqz v1, :cond_0

    .line 128
    .line 129
    new-instance v8, Ljava/util/concurrent/CancellationException;

    .line 130
    .line 131
    invoke-direct {v8, v5}, Ljava/util/concurrent/CancellationException;-><init>(Ljava/lang/String;)V

    .line 132
    .line 133
    .line 134
    invoke-interface {v1, v8}, Lkotlinx/coroutines/x0;->g(Ljava/util/concurrent/CancellationException;)V

    .line 135
    .line 136
    .line 137
    :cond_0
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 138
    .line 139
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->j4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lkotlinx/coroutines/x0;

    .line 140
    .line 141
    .line 142
    move-result-object v1

    .line 143
    if-eqz v1, :cond_1

    .line 144
    .line 145
    new-instance v8, Ljava/util/concurrent/CancellationException;

    .line 146
    .line 147
    invoke-direct {v8, v5}, Ljava/util/concurrent/CancellationException;-><init>(Ljava/lang/String;)V

    .line 148
    .line 149
    .line 150
    invoke-interface {v1, v8}, Lkotlinx/coroutines/x0;->g(Ljava/util/concurrent/CancellationException;)V

    .line 151
    .line 152
    .line 153
    :cond_1
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 154
    .line 155
    invoke-static {v1, v7, v6, v2, v4}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->D4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;ZZ)Z

    .line 156
    .line 157
    .line 158
    move-result v1

    .line 159
    if-eqz v1, :cond_2

    .line 160
    .line 161
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 162
    .line 163
    invoke-static {v1, v7, v6, v3}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->E4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)V

    .line 164
    .line 165
    .line 166
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 167
    .line 168
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->q4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lkotlinx/coroutines/flow/V;

    .line 169
    .line 170
    .line 171
    move-result-object v1

    .line 172
    iget-object v2, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 173
    .line 174
    invoke-static {v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->C4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/betting/core/tax/domain/usecase/i;

    .line 175
    .line 176
    .line 177
    move-result-object v2

    .line 178
    invoke-interface {v2}, Lorg/xbet/betting/core/tax/domain/usecase/i;->invoke()Z

    .line 179
    .line 180
    .line 181
    move-result v2

    .line 182
    invoke-static {v2}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 183
    .line 184
    .line 185
    move-result-object v2

    .line 186
    invoke-interface {v1, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 187
    .line 188
    .line 189
    goto :goto_0

    .line 190
    :cond_2
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 191
    .line 192
    invoke-static {v1, v7, v6}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->A4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;)Z

    .line 193
    .line 194
    .line 195
    move-result v1

    .line 196
    if-eqz v1, :cond_3

    .line 197
    .line 198
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 199
    .line 200
    sget-object v2, Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;->Companion:Lorg/xbet/betting/core/tax/domain/models/GetTaxModel$a;

    .line 201
    .line 202
    invoke-virtual {v2}, Lorg/xbet/betting/core/tax/domain/models/GetTaxModel$a;->a()Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;

    .line 203
    .line 204
    .line 205
    move-result-object v2

    .line 206
    invoke-static {v1, v7, v6, v2, v3}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->D3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)V

    .line 207
    .line 208
    .line 209
    goto :goto_0

    .line 210
    :cond_3
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 211
    .line 212
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->u4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)V

    .line 213
    .line 214
    .line 215
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$observeStepInputChange$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 216
    .line 217
    sget-object v2, LHx/f$a;->a:LHx/f$a;

    .line 218
    .line 219
    invoke-static {v1, v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->G4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;LHx/f;)V

    .line 220
    .line 221
    .line 222
    :goto_0
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 223
    .line 224
    return-object v1

    .line 225
    :cond_4
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 226
    .line 227
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 228
    .line 229
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 230
    .line 231
    .line 232
    throw v1
.end method
