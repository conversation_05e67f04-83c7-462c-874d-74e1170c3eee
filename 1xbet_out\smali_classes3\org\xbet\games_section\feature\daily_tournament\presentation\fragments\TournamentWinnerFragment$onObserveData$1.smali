.class final Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.daily_tournament.presentation.fragments.TournamentWinnerFragment$onObserveData$1"
    f = "TournamentWinnerFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;

    iget-object v1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;->invoke(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_4

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$c;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;

    .line 20
    .line 21
    check-cast p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$c;

    .line 22
    .line 23
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$c;->a()Ljava/util/List;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-static {v0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;->D2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;Ljava/util/List;)V

    .line 28
    .line 29
    .line 30
    goto :goto_0

    .line 31
    :cond_0
    instance-of v0, p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$d;

    .line 32
    .line 33
    if-eqz v0, :cond_1

    .line 34
    .line 35
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;

    .line 36
    .line 37
    check-cast p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$d;

    .line 38
    .line 39
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$d;->a()Ljava/util/List;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    invoke-static {v0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;->F2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;Ljava/util/List;)V

    .line 44
    .line 45
    .line 46
    goto :goto_0

    .line 47
    :cond_1
    instance-of v0, p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$b;

    .line 48
    .line 49
    if-eqz v0, :cond_2

    .line 50
    .line 51
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$onObserveData$1;->this$0:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;

    .line 52
    .line 53
    check-cast p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$b;

    .line 54
    .line 55
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$b;->a()Lorg/xbet/uikit/components/lottie/a;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    invoke-static {v0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;->E2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;Lorg/xbet/uikit/components/lottie/a;)V

    .line 60
    .line 61
    .line 62
    goto :goto_0

    .line 63
    :cond_2
    instance-of p1, p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$a;

    .line 64
    .line 65
    if-eqz p1, :cond_3

    .line 66
    .line 67
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 68
    .line 69
    return-object p1

    .line 70
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 71
    .line 72
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 73
    .line 74
    .line 75
    throw p1

    .line 76
    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 77
    .line 78
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 79
    .line 80
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 81
    .line 82
    .line 83
    throw p1
.end method
