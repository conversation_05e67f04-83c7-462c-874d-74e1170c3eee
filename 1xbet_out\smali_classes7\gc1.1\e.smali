.class public final Lgc1/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgc1/e$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000n\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0010!\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u00c5\u0001\u0010\u001d\u001a\u00020\u00192\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0007\u001a\u00020\u00002\u0006\u0010\u0008\u001a\u00020\u00002\u0006\u0010\n\u001a\u00020\t2\u0012\u0010\r\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000c0\u000b0\u00042\u0012\u0010\u000f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\u000b0\u00042\u0012\u0010\u0011\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00100\u000b0\u00042\u0006\u0010\u0013\u001a\u00020\u00122\u000c\u0010\u0016\u001a\u0008\u0012\u0004\u0012\u00020\u00150\u00142\u000c\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u00150\u00142\u0006\u0010\u0018\u001a\u00020\u00002\u0006\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u001b\u001a\u00020\u00002\u0006\u0010\u001c\u001a\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u001d\u0010\u001e\u001a\u00b5\u0001\u0010\u001f\u001a\u00020\u00192\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0007\u001a\u00020\u00002\u0006\u0010\u0008\u001a\u00020\u00002\u0006\u0010\n\u001a\u00020\t2\u0012\u0010\r\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000c0\u000b0\u00042\u0012\u0010\u000f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\u000b0\u00042\u0012\u0010\u0011\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00100\u000b0\u00042\u0006\u0010\u0013\u001a\u00020\u00122\u000c\u0010\u0016\u001a\u0008\u0012\u0004\u0012\u00020\u00150\u00142\u000c\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u00150\u00142\u0006\u0010\u001b\u001a\u00020\u00002\u0006\u0010\u001c\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 \u001a\u0097\u0001\u0010\"\u001a\u0008\u0012\u0004\u0012\u00020!0\u000b2\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0007\u001a\u00020\u00002\u0006\u0010\u001c\u001a\u00020\u00002\u0006\u0010\u0008\u001a\u00020\u00002\u0006\u0010\n\u001a\u00020\t2\u0012\u0010\r\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000c0\u000b0\u00042\u0012\u0010\u000f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\u000b0\u00042\u0012\u0010\u0011\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00100\u000b0\u00042\u0006\u0010\u001b\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\"\u0010#\u001a\u0091\u0001\u0010$\u001a\u00020\u00192\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\n\u001a\u00020\t2\u0012\u0010\r\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000c0\u000b0\u00042\u0012\u0010\u000f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\u000b0\u00042\u0012\u0010\u0011\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00100\u000b0\u00042\u0006\u0010\u0007\u001a\u00020\u00002\u0006\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u001c\u001a\u00020\u00002\u0006\u0010\u0008\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008$\u0010%\u001a7\u0010&\u001a\u00020\u00002\u0012\u0010\u000f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\u000b0\u00042\u0012\u0010\u0011\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00100\u000b0\u0004H\u0002\u00a2\u0006\u0004\u0008&\u0010\'\u001a\u0087\u0001\u0010(\u001a\u0008\u0012\u0004\u0012\u00020!0\u000b2\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\n\u001a\u00020\t2\u0012\u0010\r\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000c0\u000b0\u00042\u0012\u0010\u000f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\u000b0\u00042\u0012\u0010\u0011\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00100\u000b0\u00042\u0006\u0010\u0007\u001a\u00020\u00002\u0006\u0010\u0008\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008(\u0010)\u001a/\u0010,\u001a\u00020\u0015*\u0008\u0012\u0004\u0012\u00020!0*2\u0006\u0010+\u001a\u00020\t2\u000c\u0010\r\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000bH\u0002\u00a2\u0006\u0004\u0008,\u0010-\u001as\u0010.\u001a\u0008\u0012\u0004\u0012\u00020!0\u000b2\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u00002\u0012\u0010\u000f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\u000b0\u00042\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0012\u0010\u0011\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00100\u000b0\u00042\u0006\u0010\u0018\u001a\u00020\u00002\u0006\u0010\u0008\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008.\u0010/\u001aW\u00100\u001a\u0008\u0012\u0004\u0012\u00020!0\u000b2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u00002\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0012\u0010\u0011\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00100\u000b0\u00042\u0006\u0010\u0018\u001a\u00020\u00002\u0006\u0010\u0008\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u00080\u00101\u001aW\u00102\u001a\u0008\u0012\u0004\u0012\u00020!0\u000b2\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u00002\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0012\u0010\u0011\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00100\u000b0\u00042\u0006\u0010\u0018\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u00082\u00103\u001a7\u00104\u001a\u00020\u0015*\u0008\u0012\u0004\u0012\u00020!0*2\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0007\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\u0002\u00a2\u0006\u0004\u00084\u00105\u001a-\u00107\u001a\u0002062\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0013\u001a\u00020\u00122\u000c\u0010\u0016\u001a\u0008\u0012\u0004\u0012\u00020\u00150\u0014H\u0002\u00a2\u0006\u0004\u00087\u00108\u001a%\u00109\u001a\u0002062\u0006\u0010\u0013\u001a\u00020\u00122\u000c\u0010\u0016\u001a\u0008\u0012\u0004\u0012\u00020\u00150\u0014H\u0002\u00a2\u0006\u0004\u00089\u0010:\u001a\'\u0010<\u001a\u0008\u0012\u0004\u0012\u00020;0\u000b*\u0008\u0012\u0004\u0012\u00020\u000c0\u000b2\u0006\u0010\n\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008<\u0010=\u00a8\u0006>"
    }
    d2 = {
        "",
        "isVirtual",
        "Lcom/xbet/onexcore/themes/Theme;",
        "theme",
        "Ldc1/a;",
        "LTb1/b;",
        "promoEntities",
        "hasTournamentsAggregator",
        "hasProvidersAggregator",
        "Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;",
        "bannerStyle",
        "",
        "Lorg/xplatform/banners/api/domain/models/BannerModel;",
        "banners",
        "Lhc1/d;",
        "games",
        "LHZ0/a;",
        "categories",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "Lkotlin/Function0;",
        "",
        "onLottieButtonClick",
        "onError",
        "lottieRequest",
        "Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;",
        "currentViewState",
        "bannersEnabled",
        "isCountryBlocking",
        "c",
        "(ZLcom/xbet/onexcore/themes/Theme;Ldc1/a;ZZLorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ldc1/a;Ldc1/a;Ldc1/a;LSX0/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;ZLorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;ZZ)Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;",
        "l",
        "(ZLcom/xbet/onexcore/themes/Theme;Ldc1/a;ZZLorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ldc1/a;Ldc1/a;Ldc1/a;LSX0/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;ZZ)Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;",
        "LVX0/i;",
        "d",
        "(ZLcom/xbet/onexcore/themes/Theme;Ldc1/a;ZZZLorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ldc1/a;Ldc1/a;Ldc1/a;Z)Ljava/util/List;",
        "m",
        "(ZLcom/xbet/onexcore/themes/Theme;Ldc1/a;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ldc1/a;Ldc1/a;Ldc1/a;ZLorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;ZZ)Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;",
        "b",
        "(Ldc1/a;Ldc1/a;)Z",
        "e",
        "(ZLcom/xbet/onexcore/themes/Theme;Ldc1/a;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ldc1/a;Ldc1/a;Ldc1/a;ZZ)Ljava/util/List;",
        "",
        "style",
        "a",
        "(Ljava/util/List;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ljava/util/List;)V",
        "g",
        "(ZLcom/xbet/onexcore/themes/Theme;ZLdc1/a;Ldc1/a;Ldc1/a;ZZ)Ljava/util/List;",
        "h",
        "(Lcom/xbet/onexcore/themes/Theme;ZLdc1/a;Ldc1/a;ZZ)Ljava/util/List;",
        "f",
        "(ZLcom/xbet/onexcore/themes/Theme;ZLdc1/a;Ldc1/a;Z)Ljava/util/List;",
        "i",
        "(Ljava/util/List;Ldc1/a;ZLcom/xbet/onexcore/themes/Theme;)V",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "k",
        "(ZLSX0/c;Lkotlin/jvm/functions/Function0;)Lorg/xbet/uikit/components/lottie_empty/n;",
        "j",
        "(LSX0/c;Lkotlin/jvm/functions/Function0;)Lorg/xbet/uikit/components/lottie_empty/n;",
        "LrZ0/b;",
        "n",
        "(Ljava/util/List;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;)Ljava/util/List;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ljava/util/List;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;",
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    new-instance v0, Lhc1/b;

    .line 8
    .line 9
    new-instance v1, Lorg/xbet/uikit/components/bannercollection/a$a;

    .line 10
    .line 11
    invoke-static {p2, p1}, Lgc1/e;->n(Ljava/util/List;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;)Ljava/util/List;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    invoke-direct {v1, p1}, Lorg/xbet/uikit/components/bannercollection/a$a;-><init>(Ljava/util/List;)V

    .line 16
    .line 17
    .line 18
    invoke-direct {v0, v1}, Lhc1/b;-><init>(Lorg/xbet/uikit/components/bannercollection/a;)V

    .line 19
    .line 20
    .line 21
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 22
    .line 23
    .line 24
    :cond_0
    return-void
.end method

.method public static final b(Ldc1/a;Ldc1/a;)Z
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "Lhc1/d;",
            ">;>;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "LHZ0/a;",
            ">;>;)Z"
        }
    .end annotation

    .line 1
    const/4 v0, 0x2

    .line 2
    new-array v1, v0, [Ldc1/a;

    .line 3
    .line 4
    const/4 v2, 0x0

    .line 5
    aput-object p0, v1, v2

    .line 6
    .line 7
    const/4 v3, 0x1

    .line 8
    aput-object p1, v1, v3

    .line 9
    .line 10
    invoke-static {v1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    invoke-static {v1}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 15
    .line 16
    .line 17
    move-result v4

    .line 18
    if-eqz v4, :cond_0

    .line 19
    .line 20
    invoke-interface {v1}, Ljava/util/Collection;->isEmpty()Z

    .line 21
    .line 22
    .line 23
    move-result v4

    .line 24
    if-eqz v4, :cond_0

    .line 25
    .line 26
    goto :goto_0

    .line 27
    :cond_0
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    :cond_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 32
    .line 33
    .line 34
    move-result v4

    .line 35
    if-eqz v4, :cond_2

    .line 36
    .line 37
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v4

    .line 41
    check-cast v4, Ldc1/a;

    .line 42
    .line 43
    instance-of v4, v4, Ldc1/a$d;

    .line 44
    .line 45
    if-eqz v4, :cond_1

    .line 46
    .line 47
    goto :goto_2

    .line 48
    :cond_2
    :goto_0
    new-array v0, v0, [Ldc1/a;

    .line 49
    .line 50
    aput-object p0, v0, v2

    .line 51
    .line 52
    aput-object p1, v0, v3

    .line 53
    .line 54
    invoke-static {v0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 55
    .line 56
    .line 57
    move-result-object p0

    .line 58
    invoke-static {p0}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 59
    .line 60
    .line 61
    move-result p1

    .line 62
    if-eqz p1, :cond_3

    .line 63
    .line 64
    invoke-interface {p0}, Ljava/util/Collection;->isEmpty()Z

    .line 65
    .line 66
    .line 67
    move-result p1

    .line 68
    if-eqz p1, :cond_3

    .line 69
    .line 70
    goto :goto_2

    .line 71
    :cond_3
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 72
    .line 73
    .line 74
    move-result-object p0

    .line 75
    :cond_4
    :goto_1
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 76
    .line 77
    .line 78
    move-result p1

    .line 79
    if-eqz p1, :cond_6

    .line 80
    .line 81
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    check-cast p1, Ldc1/a;

    .line 86
    .line 87
    instance-of v0, p1, Ldc1/a$b;

    .line 88
    .line 89
    if-nez v0, :cond_4

    .line 90
    .line 91
    instance-of p1, p1, Ldc1/a$a;

    .line 92
    .line 93
    if-eqz p1, :cond_5

    .line 94
    .line 95
    goto :goto_1

    .line 96
    :cond_5
    return v3

    .line 97
    :cond_6
    :goto_2
    return v2
.end method

.method public static final c(ZLcom/xbet/onexcore/themes/Theme;Ldc1/a;ZZLorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ldc1/a;Ldc1/a;Ldc1/a;LSX0/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;ZLorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;ZZ)Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;
    .locals 14
    .param p1    # Lcom/xbet/onexcore/themes/Theme;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ldc1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Ldc1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Ldc1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Ldc1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lcom/xbet/onexcore/themes/Theme;",
            "Ldc1/a<",
            "LTb1/b;",
            ">;ZZ",
            "Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;>;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "Lhc1/d;",
            ">;>;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "LHZ0/a;",
            ">;>;",
            "LSX0/c;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;Z",
            "Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;",
            "ZZ)",
            "Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    if-eqz p12, :cond_0

    .line 2
    .line 3
    move v0, p0

    .line 4
    move-object v1, p1

    .line 5
    move-object/from16 v2, p2

    .line 6
    .line 7
    move/from16 v7, p3

    .line 8
    .line 9
    move/from16 v10, p4

    .line 10
    .line 11
    move-object/from16 v3, p5

    .line 12
    .line 13
    move-object/from16 v4, p6

    .line 14
    .line 15
    move-object/from16 v5, p7

    .line 16
    .line 17
    move-object/from16 v6, p8

    .line 18
    .line 19
    move-object/from16 v8, p13

    .line 20
    .line 21
    move/from16 v9, p15

    .line 22
    .line 23
    invoke-static/range {v0 .. v10}, Lgc1/e;->m(ZLcom/xbet/onexcore/themes/Theme;Ldc1/a;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ldc1/a;Ldc1/a;Ldc1/a;ZLorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;ZZ)Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    return-object p0

    .line 28
    :cond_0
    move v0, p0

    .line 29
    move-object v1, p1

    .line 30
    move-object/from16 v2, p2

    .line 31
    .line 32
    move/from16 v3, p3

    .line 33
    .line 34
    move/from16 v4, p4

    .line 35
    .line 36
    move-object/from16 v5, p5

    .line 37
    .line 38
    move-object/from16 v6, p6

    .line 39
    .line 40
    move-object/from16 v7, p7

    .line 41
    .line 42
    move-object/from16 v8, p8

    .line 43
    .line 44
    move-object/from16 v9, p9

    .line 45
    .line 46
    move-object/from16 v10, p10

    .line 47
    .line 48
    move-object/from16 v11, p11

    .line 49
    .line 50
    move/from16 v12, p14

    .line 51
    .line 52
    move/from16 v13, p15

    .line 53
    .line 54
    invoke-static/range {v0 .. v13}, Lgc1/e;->l(ZLcom/xbet/onexcore/themes/Theme;Ldc1/a;ZZLorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ldc1/a;Ldc1/a;Ldc1/a;LSX0/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;ZZ)Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;

    .line 55
    .line 56
    .line 57
    move-result-object p0

    .line 58
    return-object p0
.end method

.method public static final d(ZLcom/xbet/onexcore/themes/Theme;Ldc1/a;ZZZLorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ldc1/a;Ldc1/a;Ldc1/a;Z)Ljava/util/List;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lcom/xbet/onexcore/themes/Theme;",
            "Ldc1/a<",
            "LTb1/b;",
            ">;ZZZ",
            "Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;>;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "Lhc1/d;",
            ">;>;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "LHZ0/a;",
            ">;>;Z)",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p6

    .line 2
    .line 3
    move-object/from16 v1, p7

    .line 4
    .line 5
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    const/4 v9, 0x0

    .line 10
    move v3, p0

    .line 11
    move-object v4, p1

    .line 12
    move-object v7, p2

    .line 13
    move v5, p3

    .line 14
    move/from16 v10, p5

    .line 15
    .line 16
    move-object/from16 v6, p8

    .line 17
    .line 18
    move-object/from16 v8, p9

    .line 19
    .line 20
    invoke-static/range {v3 .. v10}, Lgc1/e;->g(ZLcom/xbet/onexcore/themes/Theme;ZLdc1/a;Ldc1/a;Ldc1/a;ZZ)Ljava/util/List;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    new-instance p2, Lhc1/b;

    .line 25
    .line 26
    new-instance p3, Lorg/xbet/uikit/components/bannercollection/a$b;

    .line 27
    .line 28
    new-instance v3, LAZ0/c;

    .line 29
    .line 30
    sget-object v4, LAZ0/c;->c:LAZ0/c$a;

    .line 31
    .line 32
    invoke-virtual {v4, v0}, LAZ0/c$a;->a(Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;)I

    .line 33
    .line 34
    .line 35
    move-result v4

    .line 36
    invoke-direct {v3, v0, v4}, LAZ0/c;-><init>(Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;I)V

    .line 37
    .line 38
    .line 39
    invoke-direct {p3, v3}, Lorg/xbet/uikit/components/bannercollection/a$b;-><init>(LAZ0/c;)V

    .line 40
    .line 41
    .line 42
    invoke-direct {p2, p3}, Lhc1/b;-><init>(Lorg/xbet/uikit/components/bannercollection/a;)V

    .line 43
    .line 44
    .line 45
    if-nez p0, :cond_5

    .line 46
    .line 47
    instance-of p0, v1, Ldc1/a$c;

    .line 48
    .line 49
    if-eqz p0, :cond_1

    .line 50
    .line 51
    if-nez p4, :cond_1

    .line 52
    .line 53
    if-eqz p10, :cond_0

    .line 54
    .line 55
    move-object p0, v1

    .line 56
    check-cast p0, Ldc1/a$c;

    .line 57
    .line 58
    invoke-virtual {p0}, Ldc1/a$c;->a()Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object p0

    .line 62
    check-cast p0, Ljava/util/List;

    .line 63
    .line 64
    invoke-static {v2, v0, p0}, Lgc1/e;->a(Ljava/util/List;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ljava/util/List;)V

    .line 65
    .line 66
    .line 67
    :cond_0
    invoke-interface {v2, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 68
    .line 69
    .line 70
    goto :goto_2

    .line 71
    :cond_1
    instance-of p0, v1, Ldc1/a$b;

    .line 72
    .line 73
    if-nez p0, :cond_4

    .line 74
    .line 75
    instance-of p0, v1, Ldc1/a$a;

    .line 76
    .line 77
    if-nez p0, :cond_4

    .line 78
    .line 79
    if-eqz p4, :cond_2

    .line 80
    .line 81
    goto :goto_0

    .line 82
    :cond_2
    instance-of p0, v1, Ldc1/a$d;

    .line 83
    .line 84
    if-eqz p0, :cond_b

    .line 85
    .line 86
    if-eqz p10, :cond_3

    .line 87
    .line 88
    invoke-interface {v2, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 89
    .line 90
    .line 91
    :cond_3
    invoke-interface {v2, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 92
    .line 93
    .line 94
    goto :goto_2

    .line 95
    :cond_4
    :goto_0
    invoke-interface {v2, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 96
    .line 97
    .line 98
    goto :goto_2

    .line 99
    :cond_5
    instance-of p0, v1, Ldc1/a$c;

    .line 100
    .line 101
    if-eqz p0, :cond_6

    .line 102
    .line 103
    move-object p0, v1

    .line 104
    check-cast p0, Ldc1/a$c;

    .line 105
    .line 106
    invoke-virtual {p0}, Ldc1/a$c;->a()Ljava/lang/Object;

    .line 107
    .line 108
    .line 109
    move-result-object p0

    .line 110
    check-cast p0, Ljava/util/List;

    .line 111
    .line 112
    invoke-static {v2, v0, p0}, Lgc1/e;->a(Ljava/util/List;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ljava/util/List;)V

    .line 113
    .line 114
    .line 115
    invoke-interface {v2, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 116
    .line 117
    .line 118
    goto :goto_2

    .line 119
    :cond_6
    instance-of p0, v1, Ldc1/a$b;

    .line 120
    .line 121
    if-nez p0, :cond_a

    .line 122
    .line 123
    instance-of p0, v1, Ldc1/a$a;

    .line 124
    .line 125
    if-eqz p0, :cond_7

    .line 126
    .line 127
    goto :goto_1

    .line 128
    :cond_7
    instance-of p0, v1, Ldc1/a$d;

    .line 129
    .line 130
    if-eqz p0, :cond_9

    .line 131
    .line 132
    if-eqz p10, :cond_8

    .line 133
    .line 134
    invoke-interface {v2, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 135
    .line 136
    .line 137
    :cond_8
    invoke-interface {v2, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 138
    .line 139
    .line 140
    goto :goto_2

    .line 141
    :cond_9
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 142
    .line 143
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 144
    .line 145
    .line 146
    throw p0

    .line 147
    :cond_a
    :goto_1
    invoke-interface {v2, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 148
    .line 149
    .line 150
    :cond_b
    :goto_2
    invoke-static {v2}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 151
    .line 152
    .line 153
    move-result-object p0

    .line 154
    return-object p0
.end method

.method public static final e(ZLcom/xbet/onexcore/themes/Theme;Ldc1/a;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ldc1/a;Ldc1/a;Ldc1/a;ZZ)Ljava/util/List;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lcom/xbet/onexcore/themes/Theme;",
            "Ldc1/a<",
            "LTb1/b;",
            ">;",
            "Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;>;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "Lhc1/d;",
            ">;>;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "LHZ0/a;",
            ">;>;ZZ)",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p4

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    const/4 v2, 0x0

    .line 5
    const/4 v3, 0x2

    .line 6
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 7
    .line 8
    .line 9
    move-result-object v4

    .line 10
    const/4 v11, 0x1

    .line 11
    move v5, p0

    .line 12
    move-object v6, p1

    .line 13
    move-object v9, p2

    .line 14
    move-object/from16 v8, p5

    .line 15
    .line 16
    move-object/from16 v10, p6

    .line 17
    .line 18
    move/from16 v7, p7

    .line 19
    .line 20
    move/from16 v12, p8

    .line 21
    .line 22
    invoke-static/range {v5 .. v12}, Lgc1/e;->g(ZLcom/xbet/onexcore/themes/Theme;ZLdc1/a;Ldc1/a;Ldc1/a;ZZ)Ljava/util/List;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    instance-of v5, v0, Ldc1/a$c;

    .line 27
    .line 28
    if-eqz v5, :cond_6

    .line 29
    .line 30
    if-eqz p0, :cond_2

    .line 31
    .line 32
    new-array p0, v3, [Ldc1/a;

    .line 33
    .line 34
    aput-object p5, p0, v2

    .line 35
    .line 36
    aput-object p6, p0, v1

    .line 37
    .line 38
    invoke-static {p0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 39
    .line 40
    .line 41
    move-result-object p0

    .line 42
    invoke-static {p0}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 43
    .line 44
    .line 45
    move-result p2

    .line 46
    if-eqz p2, :cond_0

    .line 47
    .line 48
    invoke-interface {p0}, Ljava/util/Collection;->isEmpty()Z

    .line 49
    .line 50
    .line 51
    move-result p2

    .line 52
    if-eqz p2, :cond_0

    .line 53
    .line 54
    goto :goto_2

    .line 55
    :cond_0
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 56
    .line 57
    .line 58
    move-result-object p0

    .line 59
    :cond_1
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 60
    .line 61
    .line 62
    move-result p2

    .line 63
    if-eqz p2, :cond_5

    .line 64
    .line 65
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object p2

    .line 69
    check-cast p2, Ldc1/a;

    .line 70
    .line 71
    instance-of p2, p2, Ldc1/a$c;

    .line 72
    .line 73
    if-eqz p2, :cond_1

    .line 74
    .line 75
    goto :goto_1

    .line 76
    :cond_2
    const/4 p0, 0x3

    .line 77
    new-array p0, p0, [Ldc1/a;

    .line 78
    .line 79
    aput-object p2, p0, v2

    .line 80
    .line 81
    aput-object p5, p0, v1

    .line 82
    .line 83
    aput-object p6, p0, v3

    .line 84
    .line 85
    invoke-static {p0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 86
    .line 87
    .line 88
    move-result-object p0

    .line 89
    invoke-static {p0}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 90
    .line 91
    .line 92
    move-result p2

    .line 93
    if-eqz p2, :cond_3

    .line 94
    .line 95
    invoke-interface {p0}, Ljava/util/Collection;->isEmpty()Z

    .line 96
    .line 97
    .line 98
    move-result p2

    .line 99
    if-eqz p2, :cond_3

    .line 100
    .line 101
    goto :goto_2

    .line 102
    :cond_3
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 103
    .line 104
    .line 105
    move-result-object p0

    .line 106
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 107
    .line 108
    .line 109
    move-result p2

    .line 110
    if-eqz p2, :cond_5

    .line 111
    .line 112
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 113
    .line 114
    .line 115
    move-result-object p2

    .line 116
    check-cast p2, Ldc1/a;

    .line 117
    .line 118
    instance-of p2, p2, Ldc1/a$c;

    .line 119
    .line 120
    if-eqz p2, :cond_4

    .line 121
    .line 122
    :goto_1
    move-object p0, v0

    .line 123
    check-cast p0, Ldc1/a$c;

    .line 124
    .line 125
    invoke-virtual {p0}, Ldc1/a$c;->a()Ljava/lang/Object;

    .line 126
    .line 127
    .line 128
    move-result-object p0

    .line 129
    check-cast p0, Ljava/util/List;

    .line 130
    .line 131
    move-object/from16 p2, p3

    .line 132
    .line 133
    invoke-static {v4, p2, p0}, Lgc1/e;->a(Ljava/util/List;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ljava/util/List;)V

    .line 134
    .line 135
    .line 136
    goto :goto_2

    .line 137
    :cond_4
    move-object/from16 p2, p3

    .line 138
    .line 139
    goto :goto_0

    .line 140
    :cond_5
    :goto_2
    invoke-interface {v4, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 141
    .line 142
    .line 143
    goto :goto_4

    .line 144
    :cond_6
    instance-of p0, v0, Ldc1/a$b;

    .line 145
    .line 146
    if-nez p0, :cond_8

    .line 147
    .line 148
    instance-of p0, v0, Ldc1/a$a;

    .line 149
    .line 150
    if-nez p0, :cond_8

    .line 151
    .line 152
    instance-of p0, v0, Ldc1/a$d;

    .line 153
    .line 154
    if-eqz p0, :cond_7

    .line 155
    .line 156
    goto :goto_3

    .line 157
    :cond_7
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 158
    .line 159
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 160
    .line 161
    .line 162
    throw p0

    .line 163
    :cond_8
    :goto_3
    invoke-interface {v4, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 164
    .line 165
    .line 166
    :goto_4
    invoke-static {v4}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 167
    .line 168
    .line 169
    move-result-object p0

    .line 170
    return-object p0
.end method

.method public static final f(ZLcom/xbet/onexcore/themes/Theme;ZLdc1/a;Ldc1/a;Z)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lcom/xbet/onexcore/themes/Theme;",
            "Z",
            "Ldc1/a<",
            "LTb1/b;",
            ">;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "LHZ0/a;",
            ">;>;Z)",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, p4, Ldc1/a$c;

    .line 6
    .line 7
    if-eqz v1, :cond_2

    .line 8
    .line 9
    check-cast p4, Ldc1/a$c;

    .line 10
    .line 11
    invoke-virtual {p4}, Ldc1/a$c;->a()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p5

    .line 15
    check-cast p5, Ljava/util/Collection;

    .line 16
    .line 17
    invoke-interface {p5}, Ljava/util/Collection;->isEmpty()Z

    .line 18
    .line 19
    .line 20
    move-result p5

    .line 21
    if-nez p5, :cond_1

    .line 22
    .line 23
    new-instance p5, Lhc1/a;

    .line 24
    .line 25
    new-instance v1, LHZ0/f$a;

    .line 26
    .line 27
    invoke-virtual {p4}, Ldc1/a$c;->a()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object p4

    .line 31
    check-cast p4, Ljava/util/List;

    .line 32
    .line 33
    if-eqz p0, :cond_0

    .line 34
    .line 35
    sget v2, LWb1/a;->category_virtual_placeholder:I

    .line 36
    .line 37
    invoke-static {v2}, LL11/c$c;->d(I)I

    .line 38
    .line 39
    .line 40
    move-result v2

    .line 41
    goto :goto_0

    .line 42
    :cond_0
    sget v2, LWb1/a;->category_aggregator_placeholder:I

    .line 43
    .line 44
    invoke-static {v2}, LL11/c$c;->d(I)I

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    :goto_0
    invoke-static {v2}, LL11/c$c;->c(I)LL11/c$c;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    invoke-direct {v1, p4, v2}, LHZ0/f$a;-><init>(Ljava/util/List;LL11/c;)V

    .line 53
    .line 54
    .line 55
    invoke-direct {p5, v1}, Lhc1/a;-><init>(LHZ0/f;)V

    .line 56
    .line 57
    .line 58
    invoke-interface {v0, p5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 59
    .line 60
    .line 61
    :cond_1
    if-nez p0, :cond_7

    .line 62
    .line 63
    invoke-static {v0, p3, p2, p1}, Lgc1/e;->i(Ljava/util/List;Ldc1/a;ZLcom/xbet/onexcore/themes/Theme;)V

    .line 64
    .line 65
    .line 66
    goto :goto_2

    .line 67
    :cond_2
    instance-of v1, p4, Ldc1/a$b;

    .line 68
    .line 69
    if-nez v1, :cond_6

    .line 70
    .line 71
    instance-of v1, p4, Ldc1/a$a;

    .line 72
    .line 73
    if-eqz v1, :cond_3

    .line 74
    .line 75
    goto :goto_1

    .line 76
    :cond_3
    instance-of p1, p4, Ldc1/a$d;

    .line 77
    .line 78
    if-eqz p1, :cond_5

    .line 79
    .line 80
    if-nez p5, :cond_7

    .line 81
    .line 82
    if-eqz p0, :cond_4

    .line 83
    .line 84
    new-instance p0, Lhc1/a;

    .line 85
    .line 86
    sget-object p1, LHZ0/f$b;->a:LHZ0/f$b;

    .line 87
    .line 88
    invoke-direct {p0, p1}, Lhc1/a;-><init>(LHZ0/f;)V

    .line 89
    .line 90
    .line 91
    invoke-interface {v0, p0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 92
    .line 93
    .line 94
    goto :goto_2

    .line 95
    :cond_4
    const/4 p0, 0x2

    .line 96
    new-array p0, p0, [LVX0/i;

    .line 97
    .line 98
    sget-object p1, Lhc1/g;->a:Lhc1/g;

    .line 99
    .line 100
    const/4 p2, 0x0

    .line 101
    aput-object p1, p0, p2

    .line 102
    .line 103
    sget-object p1, Lhc1/i;->a:Lhc1/i;

    .line 104
    .line 105
    const/4 p2, 0x1

    .line 106
    aput-object p1, p0, p2

    .line 107
    .line 108
    invoke-static {p0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 109
    .line 110
    .line 111
    move-result-object p0

    .line 112
    invoke-interface {v0, p0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 113
    .line 114
    .line 115
    goto :goto_2

    .line 116
    :cond_5
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 117
    .line 118
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 119
    .line 120
    .line 121
    throw p0

    .line 122
    :cond_6
    :goto_1
    if-nez p0, :cond_7

    .line 123
    .line 124
    invoke-static {v0, p3, p2, p1}, Lgc1/e;->i(Ljava/util/List;Ldc1/a;ZLcom/xbet/onexcore/themes/Theme;)V

    .line 125
    .line 126
    .line 127
    :cond_7
    :goto_2
    invoke-static {v0}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 128
    .line 129
    .line 130
    move-result-object p0

    .line 131
    return-object p0
.end method

.method public static final g(ZLcom/xbet/onexcore/themes/Theme;ZLdc1/a;Ldc1/a;Ldc1/a;ZZ)Ljava/util/List;
    .locals 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lcom/xbet/onexcore/themes/Theme;",
            "Z",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "Lhc1/d;",
            ">;>;",
            "Ldc1/a<",
            "LTb1/b;",
            ">;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "LHZ0/a;",
            ">;>;ZZ)",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p3

    .line 2
    .line 3
    const/4 v1, 0x4

    .line 4
    const/4 v2, 0x3

    .line 5
    const/4 v3, 0x2

    .line 6
    const/4 v4, 0x1

    .line 7
    const/4 v5, 0x0

    .line 8
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 9
    .line 10
    .line 11
    move-result-object v6

    .line 12
    new-instance v7, Lhc1/c;

    .line 13
    .line 14
    sget v8, Lpb/k;->my_virtual:I

    .line 15
    .line 16
    sget v9, LWb1/a;->virtual_popular_banner_rtl:I

    .line 17
    .line 18
    invoke-direct {v7, v8, v9}, Lhc1/c;-><init>(II)V

    .line 19
    .line 20
    .line 21
    new-instance v8, Lhc1/c;

    .line 22
    .line 23
    sget v9, Lpb/k;->my_casino:I

    .line 24
    .line 25
    sget v10, LWb1/a;->aggregator_popular_banner_rtl:I

    .line 26
    .line 27
    invoke-direct {v8, v9, v10}, Lhc1/c;-><init>(II)V

    .line 28
    .line 29
    .line 30
    move-object/from16 v11, p1

    .line 31
    .line 32
    move/from16 v12, p2

    .line 33
    .line 34
    move-object/from16 v13, p4

    .line 35
    .line 36
    move-object/from16 v14, p5

    .line 37
    .line 38
    move/from16 v15, p6

    .line 39
    .line 40
    move/from16 v16, p7

    .line 41
    .line 42
    invoke-static/range {v11 .. v16}, Lgc1/e;->h(Lcom/xbet/onexcore/themes/Theme;ZLdc1/a;Ldc1/a;ZZ)Ljava/util/List;

    .line 43
    .line 44
    .line 45
    move-result-object v9

    .line 46
    const/4 v11, 0x1

    .line 47
    move-object/from16 v12, p1

    .line 48
    .line 49
    move/from16 v13, p2

    .line 50
    .line 51
    move-object/from16 v14, p4

    .line 52
    .line 53
    move-object/from16 v15, p5

    .line 54
    .line 55
    move/from16 v16, p6

    .line 56
    .line 57
    invoke-static/range {v11 .. v16}, Lgc1/e;->f(ZLcom/xbet/onexcore/themes/Theme;ZLdc1/a;Ldc1/a;Z)Ljava/util/List;

    .line 58
    .line 59
    .line 60
    move-result-object v10

    .line 61
    if-eqz p0, :cond_4

    .line 62
    .line 63
    instance-of v8, v0, Ldc1/a$c;

    .line 64
    .line 65
    if-eqz v8, :cond_0

    .line 66
    .line 67
    invoke-interface {v6, v7}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 68
    .line 69
    .line 70
    check-cast v0, Ldc1/a$c;

    .line 71
    .line 72
    invoke-virtual {v0}, Ldc1/a$c;->a()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    check-cast v0, Ljava/util/Collection;

    .line 77
    .line 78
    invoke-interface {v6, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 79
    .line 80
    .line 81
    invoke-interface {v6, v10}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 82
    .line 83
    .line 84
    goto/16 :goto_2

    .line 85
    .line 86
    :cond_0
    instance-of v8, v0, Ldc1/a$a;

    .line 87
    .line 88
    if-eqz v8, :cond_1

    .line 89
    .line 90
    invoke-interface {v6, v7}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 91
    .line 92
    .line 93
    invoke-interface {v6, v10}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 94
    .line 95
    .line 96
    goto/16 :goto_2

    .line 97
    .line 98
    :cond_1
    instance-of v7, v0, Ldc1/a$b;

    .line 99
    .line 100
    if-eqz v7, :cond_2

    .line 101
    .line 102
    invoke-interface {v6, v10}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 103
    .line 104
    .line 105
    goto/16 :goto_2

    .line 106
    .line 107
    :cond_2
    instance-of v0, v0, Ldc1/a$d;

    .line 108
    .line 109
    if-eqz v0, :cond_3

    .line 110
    .line 111
    if-nez p6, :cond_c

    .line 112
    .line 113
    const/4 v0, 0x5

    .line 114
    new-array v0, v0, [LVX0/i;

    .line 115
    .line 116
    sget-object v7, Lhc1/f;->a:Lhc1/f;

    .line 117
    .line 118
    aput-object v7, v0, v5

    .line 119
    .line 120
    sget-object v5, Lhc1/h;->a:Lhc1/h;

    .line 121
    .line 122
    aput-object v5, v0, v4

    .line 123
    .line 124
    aput-object v5, v0, v3

    .line 125
    .line 126
    aput-object v5, v0, v2

    .line 127
    .line 128
    sget-object v2, Lhc1/g;->a:Lhc1/g;

    .line 129
    .line 130
    aput-object v2, v0, v1

    .line 131
    .line 132
    invoke-static {v0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 133
    .line 134
    .line 135
    move-result-object v0

    .line 136
    invoke-interface {v6, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 137
    .line 138
    .line 139
    goto/16 :goto_2

    .line 140
    .line 141
    :cond_3
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 142
    .line 143
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 144
    .line 145
    .line 146
    throw v0

    .line 147
    :cond_4
    instance-of v7, v0, Ldc1/a$d;

    .line 148
    .line 149
    if-eqz v7, :cond_5

    .line 150
    .line 151
    if-nez p6, :cond_c

    .line 152
    .line 153
    sget-object v0, Lhc1/f;->a:Lhc1/f;

    .line 154
    .line 155
    invoke-interface {v6, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 156
    .line 157
    .line 158
    new-array v1, v1, [LVX0/i;

    .line 159
    .line 160
    sget-object v7, Lhc1/h;->a:Lhc1/h;

    .line 161
    .line 162
    aput-object v7, v1, v5

    .line 163
    .line 164
    aput-object v0, v1, v4

    .line 165
    .line 166
    sget-object v0, Lhc1/g;->a:Lhc1/g;

    .line 167
    .line 168
    aput-object v0, v1, v3

    .line 169
    .line 170
    sget-object v0, Lhc1/i;->a:Lhc1/i;

    .line 171
    .line 172
    aput-object v0, v1, v2

    .line 173
    .line 174
    invoke-static {v1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 175
    .line 176
    .line 177
    move-result-object v0

    .line 178
    invoke-interface {v6, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 179
    .line 180
    .line 181
    goto :goto_2

    .line 182
    :cond_5
    instance-of v1, v0, Ldc1/a$c;

    .line 183
    .line 184
    if-eqz v1, :cond_6

    .line 185
    .line 186
    invoke-interface {v6, v8}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 187
    .line 188
    .line 189
    check-cast v0, Ldc1/a$c;

    .line 190
    .line 191
    invoke-virtual {v0}, Ldc1/a$c;->a()Ljava/lang/Object;

    .line 192
    .line 193
    .line 194
    move-result-object v0

    .line 195
    check-cast v0, Ljava/util/Collection;

    .line 196
    .line 197
    invoke-interface {v6, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 198
    .line 199
    .line 200
    invoke-interface {v6, v9}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 201
    .line 202
    .line 203
    goto :goto_2

    .line 204
    :cond_6
    instance-of v1, v0, Ldc1/a$b;

    .line 205
    .line 206
    if-nez v1, :cond_8

    .line 207
    .line 208
    instance-of v0, v0, Ldc1/a$a;

    .line 209
    .line 210
    if-eqz v0, :cond_7

    .line 211
    .line 212
    goto :goto_0

    .line 213
    :cond_7
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 214
    .line 215
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 216
    .line 217
    .line 218
    throw v0

    .line 219
    :cond_8
    :goto_0
    new-array v0, v3, [Ldc1/a;

    .line 220
    .line 221
    aput-object p5, v0, v5

    .line 222
    .line 223
    aput-object p4, v0, v4

    .line 224
    .line 225
    invoke-static {v0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 226
    .line 227
    .line 228
    move-result-object v0

    .line 229
    invoke-static {v0}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 230
    .line 231
    .line 232
    move-result v1

    .line 233
    if-eqz v1, :cond_9

    .line 234
    .line 235
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 236
    .line 237
    .line 238
    move-result v1

    .line 239
    if-eqz v1, :cond_9

    .line 240
    .line 241
    goto :goto_1

    .line 242
    :cond_9
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 243
    .line 244
    .line 245
    move-result-object v0

    .line 246
    :cond_a
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 247
    .line 248
    .line 249
    move-result v1

    .line 250
    if-eqz v1, :cond_b

    .line 251
    .line 252
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 253
    .line 254
    .line 255
    move-result-object v1

    .line 256
    check-cast v1, Ldc1/a;

    .line 257
    .line 258
    instance-of v1, v1, Ldc1/a$c;

    .line 259
    .line 260
    if-eqz v1, :cond_a

    .line 261
    .line 262
    invoke-interface {v6, v8}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 263
    .line 264
    .line 265
    :cond_b
    :goto_1
    invoke-interface {v6, v9}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 266
    .line 267
    .line 268
    :cond_c
    :goto_2
    invoke-static {v6}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 269
    .line 270
    .line 271
    move-result-object v0

    .line 272
    return-object v0
.end method

.method public static final h(Lcom/xbet/onexcore/themes/Theme;ZLdc1/a;Ldc1/a;ZZ)Ljava/util/List;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/xbet/onexcore/themes/Theme;",
            "Z",
            "Ldc1/a<",
            "LTb1/b;",
            ">;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "LHZ0/a;",
            ">;>;ZZ)",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, p2, Ldc1/a$c;

    .line 6
    .line 7
    if-eqz v1, :cond_1

    .line 8
    .line 9
    if-eqz p5, :cond_0

    .line 10
    .line 11
    move-object p5, p2

    .line 12
    check-cast p5, Ldc1/a$c;

    .line 13
    .line 14
    invoke-virtual {p5}, Ldc1/a$c;->a()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    check-cast v1, LTb1/b;

    .line 19
    .line 20
    invoke-virtual {v1}, LTb1/b;->b()LTb1/d;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-virtual {v1}, LTb1/d;->a()Z

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    if-eqz v1, :cond_0

    .line 29
    .line 30
    invoke-virtual {p5}, Ldc1/a$c;->a()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p5

    .line 34
    check-cast p5, LTb1/b;

    .line 35
    .line 36
    invoke-virtual {p5}, LTb1/b;->b()LTb1/d;

    .line 37
    .line 38
    .line 39
    move-result-object p5

    .line 40
    sget-object v1, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 41
    .line 42
    invoke-virtual {v1, p0}, Lcom/xbet/onexcore/themes/Theme$a;->e(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    invoke-static {p5, v1}, Lgc1/g;->b(LTb1/d;Z)Lhc1/e;

    .line 47
    .line 48
    .line 49
    move-result-object p5

    .line 50
    invoke-interface {v0, p5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 51
    .line 52
    .line 53
    :cond_0
    move-object v4, p2

    .line 54
    check-cast v4, Ldc1/a$c;

    .line 55
    .line 56
    const/4 v1, 0x0

    .line 57
    move-object v2, p0

    .line 58
    move v3, p1

    .line 59
    move-object v5, p3

    .line 60
    move v6, p4

    .line 61
    invoke-static/range {v1 .. v6}, Lgc1/e;->f(ZLcom/xbet/onexcore/themes/Theme;ZLdc1/a;Ldc1/a;Z)Ljava/util/List;

    .line 62
    .line 63
    .line 64
    move-result-object p0

    .line 65
    invoke-interface {v0, p0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 66
    .line 67
    .line 68
    goto :goto_1

    .line 69
    :cond_1
    move-object v2, p0

    .line 70
    move v3, p1

    .line 71
    move-object v5, p3

    .line 72
    move v6, p4

    .line 73
    instance-of p0, p2, Ldc1/a$b;

    .line 74
    .line 75
    if-nez p0, :cond_4

    .line 76
    .line 77
    instance-of p0, p2, Ldc1/a$a;

    .line 78
    .line 79
    if-eqz p0, :cond_2

    .line 80
    .line 81
    goto :goto_0

    .line 82
    :cond_2
    instance-of p0, p2, Ldc1/a$d;

    .line 83
    .line 84
    if-eqz p0, :cond_3

    .line 85
    .line 86
    if-nez v6, :cond_5

    .line 87
    .line 88
    const/4 p0, 0x3

    .line 89
    new-array p0, p0, [LVX0/i;

    .line 90
    .line 91
    sget-object p1, Lhc1/f;->a:Lhc1/f;

    .line 92
    .line 93
    const/4 p2, 0x0

    .line 94
    aput-object p1, p0, p2

    .line 95
    .line 96
    sget-object p1, Lhc1/g;->a:Lhc1/g;

    .line 97
    .line 98
    const/4 p2, 0x1

    .line 99
    aput-object p1, p0, p2

    .line 100
    .line 101
    sget-object p1, Lhc1/i;->a:Lhc1/i;

    .line 102
    .line 103
    const/4 p2, 0x2

    .line 104
    aput-object p1, p0, p2

    .line 105
    .line 106
    invoke-static {p0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 107
    .line 108
    .line 109
    move-result-object p0

    .line 110
    invoke-interface {v0, p0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 111
    .line 112
    .line 113
    goto :goto_1

    .line 114
    :cond_3
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 115
    .line 116
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 117
    .line 118
    .line 119
    throw p0

    .line 120
    :cond_4
    :goto_0
    const/4 v1, 0x0

    .line 121
    move-object v4, p2

    .line 122
    invoke-static/range {v1 .. v6}, Lgc1/e;->f(ZLcom/xbet/onexcore/themes/Theme;ZLdc1/a;Ldc1/a;Z)Ljava/util/List;

    .line 123
    .line 124
    .line 125
    move-result-object p0

    .line 126
    invoke-interface {v0, p0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 127
    .line 128
    .line 129
    :cond_5
    :goto_1
    invoke-static {v0}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 130
    .line 131
    .line 132
    move-result-object p0

    .line 133
    return-object p0
.end method

.method public static final i(Ljava/util/List;Ldc1/a;ZLcom/xbet/onexcore/themes/Theme;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "Ldc1/a<",
            "LTb1/b;",
            ">;Z",
            "Lcom/xbet/onexcore/themes/Theme;",
            ")V"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Ldc1/a$c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, Ldc1/a$c;

    .line 6
    .line 7
    invoke-virtual {p1}, Ldc1/a$c;->a()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    check-cast p1, LTb1/b;

    .line 12
    .line 13
    sget-object v0, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 14
    .line 15
    invoke-virtual {v0, p3}, Lcom/xbet/onexcore/themes/Theme$a;->e(Lcom/xbet/onexcore/themes/Theme;)Z

    .line 16
    .line 17
    .line 18
    move-result p3

    .line 19
    invoke-static {p1, p2, p3}, Lgc1/f;->a(LTb1/b;ZZ)Lt81/d;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-interface {p0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    return-void

    .line 27
    :cond_0
    if-eqz p2, :cond_1

    .line 28
    .line 29
    new-instance p1, Lt81/d;

    .line 30
    .line 31
    sget-object p2, Lt81/e;->a:Lt81/e;

    .line 32
    .line 33
    invoke-static {p2}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 34
    .line 35
    .line 36
    move-result-object p2

    .line 37
    invoke-direct {p1, p2}, Lt81/d;-><init>(Ljava/util/List;)V

    .line 38
    .line 39
    .line 40
    invoke-interface {p0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 41
    .line 42
    .line 43
    :cond_1
    return-void
.end method

.method public static final j(LSX0/c;Lkotlin/jvm/functions/Function0;)Lorg/xbet/uikit/components/lottie_empty/n;
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LSX0/c;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)",
            "Lorg/xbet/uikit/components/lottie_empty/n;"
        }
    .end annotation

    .line 1
    sget v6, Lpb/k;->country_blocking:I

    .line 2
    .line 3
    sget v8, Lpb/k;->refresh_data:I

    .line 4
    .line 5
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 6
    .line 7
    const/16 v10, 0x5e

    .line 8
    .line 9
    const/4 v11, 0x0

    .line 10
    const/4 v2, 0x0

    .line 11
    const/4 v3, 0x0

    .line 12
    const/4 v4, 0x0

    .line 13
    const/4 v5, 0x0

    .line 14
    const/4 v7, 0x0

    .line 15
    move-object v0, p0

    .line 16
    move-object v9, p1

    .line 17
    invoke-static/range {v0 .. v11}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    return-object p0
.end method

.method public static final k(ZLSX0/c;Lkotlin/jvm/functions/Function0;)Lorg/xbet/uikit/components/lottie_empty/n;
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "LSX0/c;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)",
            "Lorg/xbet/uikit/components/lottie_empty/n;"
        }
    .end annotation

    .line 1
    sget v6, Lpb/k;->data_retrieval_error:I

    .line 2
    .line 3
    if-eqz p0, :cond_0

    .line 4
    .line 5
    sget p0, Lpb/k;->refresh_data:I

    .line 6
    .line 7
    :goto_0
    move v8, p0

    .line 8
    goto :goto_1

    .line 9
    :cond_0
    sget p0, Lpb/k;->try_again_text:I

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :goto_1
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 13
    .line 14
    const/16 v10, 0x5e

    .line 15
    .line 16
    const/4 v11, 0x0

    .line 17
    const/4 v2, 0x0

    .line 18
    const/4 v3, 0x0

    .line 19
    const/4 v4, 0x0

    .line 20
    const/4 v5, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    move-object v0, p1

    .line 23
    move-object v9, p2

    .line 24
    invoke-static/range {v0 .. v11}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 25
    .line 26
    .line 27
    move-result-object p0

    .line 28
    return-object p0
.end method

.method public static final l(ZLcom/xbet/onexcore/themes/Theme;Ldc1/a;ZZLorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ldc1/a;Ldc1/a;Ldc1/a;LSX0/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;ZZ)Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lcom/xbet/onexcore/themes/Theme;",
            "Ldc1/a<",
            "LTb1/b;",
            ">;ZZ",
            "Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;>;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "Lhc1/d;",
            ">;>;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "LHZ0/a;",
            ">;>;",
            "LSX0/c;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;ZZ)",
            "Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p9

    .line 2
    .line 3
    move-object/from16 v1, p10

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    const/4 v3, 0x1

    .line 7
    const/4 v4, 0x0

    .line 8
    if-eqz p13, :cond_0

    .line 9
    .line 10
    invoke-static/range {p9 .. p10}, Lgc1/e;->j(LSX0/c;Lkotlin/jvm/functions/Function0;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 11
    .line 12
    .line 13
    move-result-object p0

    .line 14
    invoke-interface/range {p11 .. p11}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    new-instance p1, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b$a;

    .line 18
    .line 19
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 20
    .line 21
    .line 22
    return-object p1

    .line 23
    :cond_0
    if-nez p0, :cond_4

    .line 24
    .line 25
    const/4 v5, 0x3

    .line 26
    new-array v5, v5, [Ldc1/a;

    .line 27
    .line 28
    aput-object p2, v5, v4

    .line 29
    .line 30
    aput-object p7, v5, v3

    .line 31
    .line 32
    aput-object p8, v5, v2

    .line 33
    .line 34
    invoke-static {v5}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 35
    .line 36
    .line 37
    move-result-object v5

    .line 38
    invoke-static {v5}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 39
    .line 40
    .line 41
    move-result v6

    .line 42
    if-eqz v6, :cond_1

    .line 43
    .line 44
    invoke-interface {v5}, Ljava/util/Collection;->isEmpty()Z

    .line 45
    .line 46
    .line 47
    move-result v6

    .line 48
    if-eqz v6, :cond_1

    .line 49
    .line 50
    goto :goto_1

    .line 51
    :cond_1
    invoke-interface {v5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 52
    .line 53
    .line 54
    move-result-object v5

    .line 55
    :cond_2
    :goto_0
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    .line 56
    .line 57
    .line 58
    move-result v6

    .line 59
    if-eqz v6, :cond_3

    .line 60
    .line 61
    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object v6

    .line 65
    check-cast v6, Ldc1/a;

    .line 66
    .line 67
    instance-of v7, v6, Ldc1/a$b;

    .line 68
    .line 69
    if-nez v7, :cond_2

    .line 70
    .line 71
    instance-of v6, v6, Ldc1/a$a;

    .line 72
    .line 73
    if-eqz v6, :cond_4

    .line 74
    .line 75
    goto :goto_0

    .line 76
    :cond_3
    :goto_1
    invoke-static {v4, v0, v1}, Lgc1/e;->k(ZLSX0/c;Lkotlin/jvm/functions/Function0;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 77
    .line 78
    .line 79
    move-result-object p0

    .line 80
    invoke-interface/range {p11 .. p11}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 81
    .line 82
    .line 83
    new-instance p1, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b$a;

    .line 84
    .line 85
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 86
    .line 87
    .line 88
    return-object p1

    .line 89
    :cond_4
    if-eqz p0, :cond_8

    .line 90
    .line 91
    new-array v2, v2, [Ldc1/a;

    .line 92
    .line 93
    aput-object p7, v2, v4

    .line 94
    .line 95
    aput-object p8, v2, v3

    .line 96
    .line 97
    invoke-static {v2}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 98
    .line 99
    .line 100
    move-result-object v2

    .line 101
    invoke-static {v2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 102
    .line 103
    .line 104
    move-result v4

    .line 105
    if-eqz v4, :cond_5

    .line 106
    .line 107
    invoke-interface {v2}, Ljava/util/Collection;->isEmpty()Z

    .line 108
    .line 109
    .line 110
    move-result v4

    .line 111
    if-eqz v4, :cond_5

    .line 112
    .line 113
    goto :goto_3

    .line 114
    :cond_5
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 115
    .line 116
    .line 117
    move-result-object v2

    .line 118
    :cond_6
    :goto_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 119
    .line 120
    .line 121
    move-result v4

    .line 122
    if-eqz v4, :cond_7

    .line 123
    .line 124
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 125
    .line 126
    .line 127
    move-result-object v4

    .line 128
    check-cast v4, Ldc1/a;

    .line 129
    .line 130
    instance-of v5, v4, Ldc1/a$b;

    .line 131
    .line 132
    if-nez v5, :cond_6

    .line 133
    .line 134
    instance-of v4, v4, Ldc1/a$a;

    .line 135
    .line 136
    if-eqz v4, :cond_8

    .line 137
    .line 138
    goto :goto_2

    .line 139
    :cond_7
    :goto_3
    invoke-static {v3, v0, v1}, Lgc1/e;->k(ZLSX0/c;Lkotlin/jvm/functions/Function0;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 140
    .line 141
    .line 142
    move-result-object p0

    .line 143
    invoke-interface/range {p11 .. p11}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 144
    .line 145
    .line 146
    new-instance p1, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b$a;

    .line 147
    .line 148
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 149
    .line 150
    .line 151
    return-object p1

    .line 152
    :cond_8
    new-instance v0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b$b;

    .line 153
    .line 154
    move v1, p0

    .line 155
    move-object v2, p1

    .line 156
    move-object v3, p2

    .line 157
    move v4, p3

    .line 158
    move/from16 v6, p4

    .line 159
    .line 160
    move-object/from16 v7, p5

    .line 161
    .line 162
    move-object/from16 v8, p6

    .line 163
    .line 164
    move-object/from16 v9, p7

    .line 165
    .line 166
    move-object/from16 v10, p8

    .line 167
    .line 168
    move/from16 v11, p12

    .line 169
    .line 170
    move/from16 v5, p13

    .line 171
    .line 172
    invoke-static/range {v1 .. v11}, Lgc1/e;->d(ZLcom/xbet/onexcore/themes/Theme;Ldc1/a;ZZZLorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ldc1/a;Ldc1/a;Ldc1/a;Z)Ljava/util/List;

    .line 173
    .line 174
    .line 175
    move-result-object p0

    .line 176
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b$b;-><init>(Ljava/util/List;)V

    .line 177
    .line 178
    .line 179
    return-object v0
.end method

.method public static final m(ZLcom/xbet/onexcore/themes/Theme;Ldc1/a;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ldc1/a;Ldc1/a;Ldc1/a;ZLorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;ZZ)Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lcom/xbet/onexcore/themes/Theme;",
            "Ldc1/a<",
            "LTb1/b;",
            ">;",
            "Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;>;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "Lhc1/d;",
            ">;>;",
            "Ldc1/a<",
            "+",
            "Ljava/util/List<",
            "LHZ0/a;",
            ">;>;Z",
            "Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;",
            "ZZ)",
            "Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b;"
        }
    .end annotation

    .line 1
    const/4 v9, 0x1

    .line 2
    const/4 v10, 0x0

    .line 3
    move v0, p0

    .line 4
    move-object v1, p1

    .line 5
    move-object v2, p2

    .line 6
    move-object v3, p3

    .line 7
    move-object v4, p4

    .line 8
    move-object/from16 v5, p5

    .line 9
    .line 10
    move-object/from16 v6, p6

    .line 11
    .line 12
    move/from16 v7, p7

    .line 13
    .line 14
    move/from16 v8, p10

    .line 15
    .line 16
    invoke-static/range {v0 .. v8}, Lgc1/e;->e(ZLcom/xbet/onexcore/themes/Theme;Ldc1/a;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ldc1/a;Ldc1/a;Ldc1/a;ZZ)Ljava/util/List;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    if-eqz p0, :cond_0

    .line 21
    .line 22
    invoke-static/range {p5 .. p6}, Lgc1/e;->b(Ldc1/a;Ldc1/a;)Z

    .line 23
    .line 24
    .line 25
    move-result p0

    .line 26
    if-eqz p0, :cond_a

    .line 27
    .line 28
    new-instance p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b$b;

    .line 29
    .line 30
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b$b;-><init>(Ljava/util/List;)V

    .line 31
    .line 32
    .line 33
    return-object p0

    .line 34
    :cond_0
    const/4 p0, 0x2

    .line 35
    new-array p0, p0, [Ldc1/a;

    .line 36
    .line 37
    aput-object p2, p0, v10

    .line 38
    .line 39
    aput-object p6, p0, v9

    .line 40
    .line 41
    invoke-static {p0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object p0

    .line 45
    instance-of p2, v5, Ldc1/a$d;

    .line 46
    .line 47
    if-nez p2, :cond_3

    .line 48
    .line 49
    instance-of p2, v5, Ldc1/a$c;

    .line 50
    .line 51
    if-eqz p2, :cond_2

    .line 52
    .line 53
    move-object p2, v5

    .line 54
    check-cast p2, Ldc1/a$c;

    .line 55
    .line 56
    invoke-virtual {p2}, Ldc1/a$c;->a()Ljava/lang/Object;

    .line 57
    .line 58
    .line 59
    move-result-object p2

    .line 60
    check-cast p2, Ljava/lang/Iterable;

    .line 61
    .line 62
    instance-of p3, p2, Ljava/util/Collection;

    .line 63
    .line 64
    if-eqz p3, :cond_1

    .line 65
    .line 66
    move-object p3, p2

    .line 67
    check-cast p3, Ljava/util/Collection;

    .line 68
    .line 69
    invoke-interface {p3}, Ljava/util/Collection;->isEmpty()Z

    .line 70
    .line 71
    .line 72
    move-result p3

    .line 73
    if-eqz p3, :cond_1

    .line 74
    .line 75
    goto :goto_1

    .line 76
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 77
    .line 78
    .line 79
    move-result-object p2

    .line 80
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 81
    .line 82
    .line 83
    move-result p3

    .line 84
    if-eqz p3, :cond_3

    .line 85
    .line 86
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object p3

    .line 90
    check-cast p3, Lhc1/d;

    .line 91
    .line 92
    invoke-virtual {p3}, Lhc1/d;->getId()J

    .line 93
    .line 94
    .line 95
    move-result-wide p3

    .line 96
    const-wide/16 v0, -0x1

    .line 97
    .line 98
    cmp-long v2, p3, v0

    .line 99
    .line 100
    if-nez v2, :cond_2

    .line 101
    .line 102
    goto :goto_0

    .line 103
    :cond_2
    const/4 v9, 0x0

    .line 104
    :cond_3
    :goto_1
    invoke-static {p0}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 105
    .line 106
    .line 107
    move-result p2

    .line 108
    if-eqz p2, :cond_4

    .line 109
    .line 110
    invoke-interface {p0}, Ljava/util/Collection;->isEmpty()Z

    .line 111
    .line 112
    .line 113
    move-result p2

    .line 114
    if-eqz p2, :cond_4

    .line 115
    .line 116
    goto :goto_2

    .line 117
    :cond_4
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 118
    .line 119
    .line 120
    move-result-object p2

    .line 121
    :cond_5
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 122
    .line 123
    .line 124
    move-result p3

    .line 125
    if-eqz p3, :cond_6

    .line 126
    .line 127
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 128
    .line 129
    .line 130
    move-result-object p3

    .line 131
    check-cast p3, Ldc1/a;

    .line 132
    .line 133
    instance-of p3, p3, Ldc1/a$d;

    .line 134
    .line 135
    if-eqz p3, :cond_5

    .line 136
    .line 137
    return-object p8

    .line 138
    :cond_6
    :goto_2
    if-nez v9, :cond_a

    .line 139
    .line 140
    invoke-static {p0}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 141
    .line 142
    .line 143
    move-result p2

    .line 144
    if-eqz p2, :cond_7

    .line 145
    .line 146
    invoke-interface {p0}, Ljava/util/Collection;->isEmpty()Z

    .line 147
    .line 148
    .line 149
    move-result p2

    .line 150
    if-eqz p2, :cond_7

    .line 151
    .line 152
    return-object p8

    .line 153
    :cond_7
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 154
    .line 155
    .line 156
    move-result-object p0

    .line 157
    :cond_8
    :goto_3
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 158
    .line 159
    .line 160
    move-result p2

    .line 161
    if-eqz p2, :cond_a

    .line 162
    .line 163
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 164
    .line 165
    .line 166
    move-result-object p2

    .line 167
    check-cast p2, Ldc1/a;

    .line 168
    .line 169
    instance-of p3, p2, Ldc1/a$b;

    .line 170
    .line 171
    if-nez p3, :cond_8

    .line 172
    .line 173
    instance-of p2, p2, Ldc1/a$a;

    .line 174
    .line 175
    if-nez p2, :cond_8

    .line 176
    .line 177
    if-eqz p9, :cond_9

    .line 178
    .line 179
    goto :goto_3

    .line 180
    :cond_9
    new-instance p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b$b;

    .line 181
    .line 182
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/PopularAggregatorViewModel$b$b;-><init>(Ljava/util/List;)V

    .line 183
    .line 184
    .line 185
    return-object p0

    .line 186
    :cond_a
    return-object p8
.end method

.method public static final n(Ljava/util/List;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;)Ljava/util/List;
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;",
            "Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;",
            ")",
            "Ljava/util/List<",
            "LrZ0/b;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    const/16 v1, 0xa

    .line 4
    .line 5
    invoke-static {p0, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 10
    .line 11
    .line 12
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    if-eqz v1, :cond_1

    .line 21
    .line 22
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    check-cast v1, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 27
    .line 28
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 29
    .line 30
    .line 31
    move-result v3

    .line 32
    sget-object v2, Lgc1/e$a;->a:[I

    .line 33
    .line 34
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 35
    .line 36
    .line 37
    move-result v4

    .line 38
    aget v2, v2, v4

    .line 39
    .line 40
    packed-switch v2, :pswitch_data_0

    .line 41
    .line 42
    .line 43
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 44
    .line 45
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 46
    .line 47
    .line 48
    throw p0

    .line 49
    :pswitch_0
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getHorizontalImage()Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    invoke-interface {v2}, Ljava/lang/CharSequence;->length()I

    .line 54
    .line 55
    .line 56
    move-result v4

    .line 57
    if-nez v4, :cond_0

    .line 58
    .line 59
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getUrl()Ljava/lang/String;

    .line 60
    .line 61
    .line 62
    move-result-object v2

    .line 63
    goto :goto_1

    .line 64
    :pswitch_1
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getSquareImageUrl()Ljava/lang/String;

    .line 65
    .line 66
    .line 67
    move-result-object v2

    .line 68
    invoke-interface {v2}, Ljava/lang/CharSequence;->length()I

    .line 69
    .line 70
    .line 71
    move-result v4

    .line 72
    if-nez v4, :cond_0

    .line 73
    .line 74
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getPreviewUrl()Ljava/lang/String;

    .line 75
    .line 76
    .line 77
    move-result-object v2

    .line 78
    goto :goto_1

    .line 79
    :pswitch_2
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getVerticalImageUrl()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object v2

    .line 83
    invoke-interface {v2}, Ljava/lang/CharSequence;->length()I

    .line 84
    .line 85
    .line 86
    move-result v4

    .line 87
    if-nez v4, :cond_0

    .line 88
    .line 89
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getPreviewUrl()Ljava/lang/String;

    .line 90
    .line 91
    .line 92
    move-result-object v2

    .line 93
    :cond_0
    :goto_1
    invoke-static {v2}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object v2

    .line 97
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getTitle()Ljava/lang/String;

    .line 98
    .line 99
    .line 100
    move-result-object v6

    .line 101
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getDescription()Ljava/lang/String;

    .line 102
    .line 103
    .line 104
    move-result-object v7

    .line 105
    move-object v1, v2

    .line 106
    new-instance v2, LrZ0/b;

    .line 107
    .line 108
    invoke-static {v1}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 109
    .line 110
    .line 111
    move-result-object v4

    .line 112
    const/16 v10, 0x60

    .line 113
    .line 114
    const/4 v11, 0x0

    .line 115
    const/4 v8, 0x0

    .line 116
    const/4 v9, 0x0

    .line 117
    move-object v5, p1

    .line 118
    invoke-direct/range {v2 .. v11}, LrZ0/b;-><init>(ILL11/c;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;Ljava/lang/String;Ljava/lang/String;LL11/c;LL11/c;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 119
    .line 120
    .line 121
    invoke-interface {v0, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 122
    .line 123
    .line 124
    goto :goto_0

    .line 125
    :cond_1
    return-object v0

    .line 126
    nop

    .line 127
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_2
        :pswitch_2
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method
