.class public final Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$a;,
        Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$b;,
        Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$c;,
        Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$d;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00c8\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\u0007\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u0007\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008=\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u000f\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u0000 \u00d3\u00012\u00020\u0001:\u0006\u00d4\u0001\u00d5\u0001\u00d6\u0001B\u00e9\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u0012\u0006\u0010/\u001a\u00020.\u0012\u0006\u00101\u001a\u000200\u0012\u0006\u00103\u001a\u000202\u0012\u0006\u00105\u001a\u000204\u0012\u0006\u00107\u001a\u000206\u0012\u0006\u00109\u001a\u000208\u00a2\u0006\u0004\u0008:\u0010;J\u000f\u0010=\u001a\u00020<H\u0002\u00a2\u0006\u0004\u0008=\u0010>J\u000f\u0010?\u001a\u00020<H\u0002\u00a2\u0006\u0004\u0008?\u0010>J\u000f\u0010@\u001a\u00020<H\u0002\u00a2\u0006\u0004\u0008@\u0010>J\u000f\u0010A\u001a\u00020<H\u0002\u00a2\u0006\u0004\u0008A\u0010>J\u0017\u0010D\u001a\u00020<2\u0006\u0010C\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008D\u0010EJ\u0018\u0010F\u001a\u00020<2\u0006\u0010C\u001a\u00020BH\u0082@\u00a2\u0006\u0004\u0008F\u0010GJ\u001f\u0010J\u001a\u00020H2\u0006\u0010I\u001a\u00020H2\u0006\u0010C\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008J\u0010KJ-\u0010O\u001a\u00020H2\u0006\u0010I\u001a\u00020H2\u000c\u0010N\u001a\u0008\u0012\u0004\u0012\u00020M0L2\u0006\u0010C\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008O\u0010PJ\u000f\u0010Q\u001a\u00020<H\u0002\u00a2\u0006\u0004\u0008Q\u0010>J\u000f\u0010R\u001a\u00020<H\u0002\u00a2\u0006\u0004\u0008R\u0010>J\u001f\u0010V\u001a\u00020<2\u0006\u0010T\u001a\u00020S2\u0006\u0010U\u001a\u00020HH\u0002\u00a2\u0006\u0004\u0008V\u0010WJ\u000f\u0010Y\u001a\u00020XH\u0002\u00a2\u0006\u0004\u0008Y\u0010ZJ\u000f\u0010[\u001a\u00020<H\u0002\u00a2\u0006\u0004\u0008[\u0010>J\u000f\u0010\\\u001a\u00020HH\u0002\u00a2\u0006\u0004\u0008\\\u0010]J\u000f\u0010^\u001a\u00020<H\u0002\u00a2\u0006\u0004\u0008^\u0010>J\u000f\u0010_\u001a\u00020<H\u0002\u00a2\u0006\u0004\u0008_\u0010>J\u0017\u0010b\u001a\u00020<2\u0006\u0010a\u001a\u00020`H\u0002\u00a2\u0006\u0004\u0008b\u0010cJ\u0013\u0010f\u001a\u0008\u0012\u0004\u0012\u00020e0d\u00a2\u0006\u0004\u0008f\u0010gJ\u0013\u0010j\u001a\u0008\u0012\u0004\u0012\u00020i0h\u00a2\u0006\u0004\u0008j\u0010kJ\u0013\u0010m\u001a\u0008\u0012\u0004\u0012\u00020l0d\u00a2\u0006\u0004\u0008m\u0010gJ\u0019\u0010o\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020n0L0d\u00a2\u0006\u0004\u0008o\u0010gJ\u0013\u0010q\u001a\u0008\u0012\u0004\u0012\u00020p0h\u00a2\u0006\u0004\u0008q\u0010kJ\u0013\u0010s\u001a\u0008\u0012\u0004\u0012\u00020<0r\u00a2\u0006\u0004\u0008s\u0010tJ\r\u0010u\u001a\u00020<\u00a2\u0006\u0004\u0008u\u0010>J\u0015\u0010w\u001a\u00020<2\u0006\u0010v\u001a\u00020X\u00a2\u0006\u0004\u0008w\u0010xJ\u0015\u0010{\u001a\u00020<2\u0006\u0010z\u001a\u00020y\u00a2\u0006\u0004\u0008{\u0010|J\u0016\u0010\u007f\u001a\u00020<2\u0006\u0010~\u001a\u00020}\u00a2\u0006\u0005\u0008\u007f\u0010\u0080\u0001J\u000f\u0010\u0081\u0001\u001a\u00020<\u00a2\u0006\u0005\u0008\u0081\u0001\u0010>J\u000f\u0010\u0082\u0001\u001a\u00020<\u00a2\u0006\u0005\u0008\u0082\u0001\u0010>R\u0016\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0083\u0001\u0010\u0084\u0001R\u0016\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0085\u0001\u0010\u0086\u0001R\u0016\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0087\u0001\u0010\u0088\u0001R\u0016\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0089\u0001\u0010\u008a\u0001R\u0016\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008b\u0001\u0010\u008c\u0001R\u0016\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008d\u0001\u0010\u008e\u0001R\u0016\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008f\u0001\u0010\u0090\u0001R\u0016\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0091\u0001\u0010\u0092\u0001R\u0016\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0093\u0001\u0010\u0094\u0001R\u0016\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0095\u0001\u0010\u0096\u0001R\u0016\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0097\u0001\u0010\u0098\u0001R\u0016\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0099\u0001\u0010\u009a\u0001R\u0016\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009b\u0001\u0010\u009c\u0001R\u0016\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009d\u0001\u0010\u009e\u0001R\u0016\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009f\u0001\u0010\u00a0\u0001R\u0016\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a1\u0001\u0010\u00a2\u0001R\u0016\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a3\u0001\u0010\u00a4\u0001R\u0016\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a5\u0001\u0010\u00a6\u0001R\u0016\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a7\u0001\u0010\u00a8\u0001R\u0016\u0010)\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a9\u0001\u0010\u00aa\u0001R\u0016\u0010+\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ab\u0001\u0010\u00ac\u0001R\u0016\u0010-\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ad\u0001\u0010\u00ae\u0001R\u0016\u0010/\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00af\u0001\u0010\u00b0\u0001R\u0016\u00101\u001a\u0002008\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b1\u0001\u0010\u00b2\u0001R\u0016\u00103\u001a\u0002028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b3\u0001\u0010\u00b4\u0001R\u0016\u00105\u001a\u0002048\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b5\u0001\u0010\u00b6\u0001R\u0016\u00107\u001a\u0002068\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b7\u0001\u0010\u00b8\u0001R\u0016\u00109\u001a\u0002088\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b9\u0001\u0010\u00ba\u0001R\u001e\u0010\u00be\u0001\u001a\t\u0012\u0004\u0012\u00020e0\u00bb\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bc\u0001\u0010\u00bd\u0001R\u001f\u0010\u00c1\u0001\u001a\n\u0012\u0005\u0012\u00030\u00bf\u00010\u00bb\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c0\u0001\u0010\u00bd\u0001R\u001e\u0010\u00c3\u0001\u001a\t\u0012\u0004\u0012\u00020l0\u00bb\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c2\u0001\u0010\u00bd\u0001R\u001d\u0010\u00c6\u0001\u001a\u0008\u0012\u0004\u0012\u00020B0r8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c4\u0001\u0010\u00c5\u0001R\u001e\u0010\u00c8\u0001\u001a\t\u0012\u0004\u0012\u00020H0\u00bb\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c7\u0001\u0010\u00bd\u0001R$\u0010\u00ca\u0001\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020n0L0\u00bb\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c9\u0001\u0010\u00bd\u0001R\u001d\u0010\u00cc\u0001\u001a\u0008\u0012\u0004\u0012\u00020p0r8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00cb\u0001\u0010\u00c5\u0001R\u001d\u0010\u00ce\u0001\u001a\u0008\u0012\u0004\u0012\u00020<0r8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00cd\u0001\u0010\u00c5\u0001R\u001c\u0010\u00d2\u0001\u001a\u0005\u0018\u00010\u00cf\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00d0\u0001\u0010\u00d1\u0001\u00a8\u0006\u00d7\u0001"
    }
    d2 = {
        "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/M0;",
        "getCurrentCoefViewStreamUseCase",
        "Ltw/g;",
        "observeBetEventCountUseCase",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/d2;",
        "isBlockedEventsExistUseCase",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/d0;",
        "getChangesTypeScenario",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/j0;",
        "getCoefStateScenario",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/y0;",
        "getCouponCoefUseCase",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/h0;",
        "getCoefCheckUseCase",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/G2;",
        "observeCurrentBetSystemChangedUseCase",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/w1;",
        "getMultiBetGroupCountUseCase",
        "LHX0/e;",
        "resourceManager",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/I;",
        "getAllBetEventModelsUseCase",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "Lxx/g;",
        "observeLoginStateUseCase",
        "Lorg/xbet/remoteconfig/domain/usecases/k;",
        "isBettingDisabledUseCase",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "Ltw/d;",
        "getCouponTypeUseCase",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/W;",
        "getBetSystemItemsScenario",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/w2;",
        "notifyCurrentBetSystemChangedUseCase",
        "Lzg/g;",
        "couponBetAnalytics",
        "LB90/a;",
        "settingsMakeBetFactory",
        "LwX0/c;",
        "router",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/C2;",
        "observeCouponTypeChangedUseCase",
        "LwX0/a;",
        "appScreensProvider",
        "Ltw/i;",
        "removeCouponCodePreferenceUseCase",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/l0;",
        "getCoefViewTypeUseCase",
        "LqR/a;",
        "betFatmanLogger",
        "<init>",
        "(Lorg/xbet/coupon/impl/coupon/domain/usecases/M0;Ltw/g;Lorg/xbet/coupon/impl/coupon/domain/usecases/d2;Lorg/xbet/coupon/impl/coupon/domain/usecases/d0;Lorg/xbet/coupon/impl/coupon/domain/usecases/j0;Lorg/xbet/coupon/impl/coupon/domain/usecases/y0;Lorg/xbet/coupon/impl/coupon/domain/usecases/h0;Lorg/xbet/coupon/impl/coupon/domain/usecases/G2;Lorg/xbet/coupon/impl/coupon/domain/usecases/w1;LHX0/e;Lorg/xbet/coupon/impl/coupon/domain/usecases/I;Lp9/c;Lxx/g;Lorg/xbet/remoteconfig/domain/usecases/k;Lorg/xbet/remoteconfig/domain/usecases/i;Ltw/d;Lm8/a;Lorg/xbet/coupon/impl/coupon/domain/usecases/W;Lorg/xbet/coupon/impl/coupon/domain/usecases/w2;Lzg/g;LB90/a;LwX0/c;Lorg/xbet/ui_common/utils/M;Lorg/xbet/coupon/impl/coupon/domain/usecases/C2;LwX0/a;Ltw/i;Lorg/xbet/coupon/impl/coupon/domain/usecases/l0;LqR/a;)V",
        "",
        "U3",
        "()V",
        "m4",
        "q4",
        "C4",
        "Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;",
        "couponType",
        "D4",
        "(Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)V",
        "Z3",
        "(Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "isHiddenBetting",
        "Y3",
        "(ZLorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)Z",
        "",
        "LWn/a;",
        "betEventModels",
        "X3",
        "(ZLjava/util/List;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)Z",
        "j4",
        "u4",
        "Luw/a;",
        "betSystemModel",
        "changedByUser",
        "w4",
        "(Luw/a;Z)V",
        "",
        "a4",
        "()Ljava/lang/String;",
        "k4",
        "W3",
        "()Z",
        "s4",
        "o4",
        "",
        "throwable",
        "h4",
        "(Ljava/lang/Throwable;)V",
        "Lkotlinx/coroutines/flow/f0;",
        "LAx/c;",
        "b4",
        "()Lkotlinx/coroutines/flow/f0;",
        "Lkotlinx/coroutines/flow/e;",
        "LAx/e;",
        "c4",
        "()Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$c;",
        "f4",
        "LAx/g;",
        "d4",
        "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$b;",
        "e4",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "g4",
        "()Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "x4",
        "screenName",
        "B4",
        "(Ljava/lang/String;)V",
        "",
        "offset",
        "A4",
        "(F)V",
        "",
        "position",
        "y4",
        "(I)V",
        "V3",
        "z4",
        "v1",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/M0;",
        "x1",
        "Ltw/g;",
        "y1",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/d2;",
        "F1",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/d0;",
        "H1",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/j0;",
        "I1",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/y0;",
        "P1",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/h0;",
        "S1",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/G2;",
        "V1",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/w1;",
        "b2",
        "LHX0/e;",
        "v2",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/I;",
        "x2",
        "Lp9/c;",
        "y2",
        "Lxx/g;",
        "F2",
        "Lorg/xbet/remoteconfig/domain/usecases/k;",
        "H2",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "I2",
        "Ltw/d;",
        "P2",
        "Lm8/a;",
        "S2",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/W;",
        "V2",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/w2;",
        "X2",
        "Lzg/g;",
        "F3",
        "LB90/a;",
        "H3",
        "LwX0/c;",
        "I3",
        "Lorg/xbet/ui_common/utils/M;",
        "S3",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/C2;",
        "H4",
        "LwX0/a;",
        "X4",
        "Ltw/i;",
        "v5",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/l0;",
        "w5",
        "LqR/a;",
        "Lkotlinx/coroutines/flow/V;",
        "x5",
        "Lkotlinx/coroutines/flow/V;",
        "betInfoUiModelStream",
        "LAx/d;",
        "y5",
        "betSettingsModelStream",
        "z5",
        "makeBetStateStream",
        "A5",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "currentCouponTypeModelStream",
        "B5",
        "coefAnimateStateStream",
        "C5",
        "betTypesListStream",
        "D5",
        "makeBetScreenActionStream",
        "E5",
        "resetSelectedBetTypeActionStream",
        "Lkotlinx/coroutines/x0;",
        "F5",
        "Lkotlinx/coroutines/x0;",
        "coefShimmerJob",
        "G5",
        "c",
        "b",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final G5:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final A5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/util/List<",
            "LAx/g;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final D5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final E5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F1:Lorg/xbet/coupon/impl/coupon/domain/usecases/d0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:Lorg/xbet/remoteconfig/domain/usecases/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F3:LB90/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public F5:Lkotlinx/coroutines/x0;

.field public final H1:Lorg/xbet/coupon/impl/coupon/domain/usecases/j0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H3:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H4:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lorg/xbet/coupon/impl/coupon/domain/usecases/y0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Ltw/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I3:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lorg/xbet/coupon/impl/coupon/domain/usecases/h0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/coupon/impl/coupon/domain/usecases/G2;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:Lorg/xbet/coupon/impl/coupon/domain/usecases/W;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S3:Lorg/xbet/coupon/impl/coupon/domain/usecases/C2;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lorg/xbet/coupon/impl/coupon/domain/usecases/w1;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:Lorg/xbet/coupon/impl/coupon/domain/usecases/w2;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X2:Lzg/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X4:Ltw/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lorg/xbet/coupon/impl/coupon/domain/usecases/M0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lorg/xbet/coupon/impl/coupon/domain/usecases/I;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v5:Lorg/xbet/coupon/impl/coupon/domain/usecases/l0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w5:LqR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Ltw/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LAx/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/coupon/impl/coupon/domain/usecases/d2;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:Lxx/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LAx/d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->G5:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$a;

    return-void
.end method

.method public constructor <init>(Lorg/xbet/coupon/impl/coupon/domain/usecases/M0;Ltw/g;Lorg/xbet/coupon/impl/coupon/domain/usecases/d2;Lorg/xbet/coupon/impl/coupon/domain/usecases/d0;Lorg/xbet/coupon/impl/coupon/domain/usecases/j0;Lorg/xbet/coupon/impl/coupon/domain/usecases/y0;Lorg/xbet/coupon/impl/coupon/domain/usecases/h0;Lorg/xbet/coupon/impl/coupon/domain/usecases/G2;Lorg/xbet/coupon/impl/coupon/domain/usecases/w1;LHX0/e;Lorg/xbet/coupon/impl/coupon/domain/usecases/I;Lp9/c;Lxx/g;Lorg/xbet/remoteconfig/domain/usecases/k;Lorg/xbet/remoteconfig/domain/usecases/i;Ltw/d;Lm8/a;Lorg/xbet/coupon/impl/coupon/domain/usecases/W;Lorg/xbet/coupon/impl/coupon/domain/usecases/w2;Lzg/g;LB90/a;LwX0/c;Lorg/xbet/ui_common/utils/M;Lorg/xbet/coupon/impl/coupon/domain/usecases/C2;LwX0/a;Ltw/i;Lorg/xbet/coupon/impl/coupon/domain/usecases/l0;LqR/a;)V
    .locals 11
    .param p1    # Lorg/xbet/coupon/impl/coupon/domain/usecases/M0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ltw/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/coupon/impl/coupon/domain/usecases/d2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/coupon/impl/coupon/domain/usecases/d0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/coupon/impl/coupon/domain/usecases/j0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/coupon/impl/coupon/domain/usecases/y0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/coupon/impl/coupon/domain/usecases/h0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/coupon/impl/coupon/domain/usecases/G2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/coupon/impl/coupon/domain/usecases/w1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/coupon/impl/coupon/domain/usecases/I;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lxx/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Lorg/xbet/remoteconfig/domain/usecases/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Ltw/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lorg/xbet/coupon/impl/coupon/domain/usecases/W;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # Lorg/xbet/coupon/impl/coupon/domain/usecases/w2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Lzg/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # LB90/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # Lorg/xbet/coupon/impl/coupon/domain/usecases/C2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p26    # Ltw/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p27    # Lorg/xbet/coupon/impl/coupon/domain/usecases/l0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p28    # LqR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->v1:Lorg/xbet/coupon/impl/coupon/domain/usecases/M0;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->x1:Ltw/g;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->y1:Lorg/xbet/coupon/impl/coupon/domain/usecases/d2;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->F1:Lorg/xbet/coupon/impl/coupon/domain/usecases/d0;

    .line 11
    .line 12
    move-object/from16 p1, p5

    .line 13
    .line 14
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->H1:Lorg/xbet/coupon/impl/coupon/domain/usecases/j0;

    .line 15
    .line 16
    move-object/from16 p1, p6

    .line 17
    .line 18
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->I1:Lorg/xbet/coupon/impl/coupon/domain/usecases/y0;

    .line 19
    .line 20
    move-object/from16 p1, p7

    .line 21
    .line 22
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->P1:Lorg/xbet/coupon/impl/coupon/domain/usecases/h0;

    .line 23
    .line 24
    move-object/from16 p1, p8

    .line 25
    .line 26
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->S1:Lorg/xbet/coupon/impl/coupon/domain/usecases/G2;

    .line 27
    .line 28
    move-object/from16 p1, p9

    .line 29
    .line 30
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->V1:Lorg/xbet/coupon/impl/coupon/domain/usecases/w1;

    .line 31
    .line 32
    move-object/from16 p1, p10

    .line 33
    .line 34
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->b2:LHX0/e;

    .line 35
    .line 36
    move-object/from16 p1, p11

    .line 37
    .line 38
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->v2:Lorg/xbet/coupon/impl/coupon/domain/usecases/I;

    .line 39
    .line 40
    move-object/from16 p1, p12

    .line 41
    .line 42
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->x2:Lp9/c;

    .line 43
    .line 44
    move-object/from16 p1, p13

    .line 45
    .line 46
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->y2:Lxx/g;

    .line 47
    .line 48
    move-object/from16 p1, p14

    .line 49
    .line 50
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->F2:Lorg/xbet/remoteconfig/domain/usecases/k;

    .line 51
    .line 52
    move-object/from16 p1, p15

    .line 53
    .line 54
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->H2:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 55
    .line 56
    move-object/from16 p1, p16

    .line 57
    .line 58
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->I2:Ltw/d;

    .line 59
    .line 60
    move-object/from16 p1, p17

    .line 61
    .line 62
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->P2:Lm8/a;

    .line 63
    .line 64
    move-object/from16 p1, p18

    .line 65
    .line 66
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->S2:Lorg/xbet/coupon/impl/coupon/domain/usecases/W;

    .line 67
    .line 68
    move-object/from16 p1, p19

    .line 69
    .line 70
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->V2:Lorg/xbet/coupon/impl/coupon/domain/usecases/w2;

    .line 71
    .line 72
    move-object/from16 p1, p20

    .line 73
    .line 74
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->X2:Lzg/g;

    .line 75
    .line 76
    move-object/from16 p1, p21

    .line 77
    .line 78
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->F3:LB90/a;

    .line 79
    .line 80
    move-object/from16 p1, p22

    .line 81
    .line 82
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->H3:LwX0/c;

    .line 83
    .line 84
    move-object/from16 p1, p23

    .line 85
    .line 86
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->I3:Lorg/xbet/ui_common/utils/M;

    .line 87
    .line 88
    move-object/from16 p1, p24

    .line 89
    .line 90
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->S3:Lorg/xbet/coupon/impl/coupon/domain/usecases/C2;

    .line 91
    .line 92
    move-object/from16 p1, p25

    .line 93
    .line 94
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->H4:LwX0/a;

    .line 95
    .line 96
    move-object/from16 p1, p26

    .line 97
    .line 98
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->X4:Ltw/i;

    .line 99
    .line 100
    move-object/from16 p1, p27

    .line 101
    .line 102
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->v5:Lorg/xbet/coupon/impl/coupon/domain/usecases/l0;

    .line 103
    .line 104
    move-object/from16 p2, p28

    .line 105
    .line 106
    iput-object p2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->w5:LqR/a;

    .line 107
    .line 108
    sget-object p2, Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefChangeTypeModel;->NONE:Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefChangeTypeModel;

    .line 109
    .line 110
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/coupon/domain/usecases/l0;->a()Lorg/xbet/coupon/impl/coupon/domain/models/CoefViewTypeModel;

    .line 111
    .line 112
    .line 113
    move-result-object p1

    .line 114
    sget-object v0, Lorg/xbet/ui_common/CoefficientState;->SAME:Lorg/xbet/ui_common/CoefficientState;

    .line 115
    .line 116
    new-instance v1, LAx/c;

    .line 117
    .line 118
    const/4 v2, 0x0

    .line 119
    const/4 v3, 0x0

    .line 120
    const-wide/16 v4, 0x0

    .line 121
    .line 122
    const-wide/16 v6, 0x0

    .line 123
    .line 124
    const-string v8, ""

    .line 125
    .line 126
    const/4 v9, 0x0

    .line 127
    const/4 v10, 0x0

    .line 128
    move-object/from16 p12, p1

    .line 129
    .line 130
    move-object/from16 p8, p2

    .line 131
    .line 132
    move-object/from16 p9, v0

    .line 133
    .line 134
    move-object p1, v1

    .line 135
    move-wide p2, v4

    .line 136
    move-wide p4, v6

    .line 137
    move-object/from16 p6, v8

    .line 138
    .line 139
    move-object/from16 p7, v9

    .line 140
    .line 141
    const/16 p10, 0x0

    .line 142
    .line 143
    const/16 p11, 0x0

    .line 144
    .line 145
    const/16 p13, 0x0

    .line 146
    .line 147
    invoke-direct/range {p1 .. p13}, LAx/c;-><init>(JDLjava/lang/String;Ljava/lang/String;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefChangeTypeModel;Lorg/xbet/ui_common/CoefficientState;ZZLorg/xbet/coupon/impl/coupon/domain/models/CoefViewTypeModel;Z)V

    .line 148
    .line 149
    .line 150
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 151
    .line 152
    .line 153
    move-result-object p1

    .line 154
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->x5:Lkotlinx/coroutines/flow/V;

    .line 155
    .line 156
    new-instance p1, LAx/d;

    .line 157
    .line 158
    const-string p2, ""

    .line 159
    .line 160
    const/4 v0, 0x0

    .line 161
    const/4 v1, 0x0

    .line 162
    invoke-direct {p1, p2, v0, v0, v1}, LAx/d;-><init>(Ljava/lang/String;ZZLuw/a;)V

    .line 163
    .line 164
    .line 165
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 166
    .line 167
    .line 168
    move-result-object p1

    .line 169
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->y5:Lkotlinx/coroutines/flow/V;

    .line 170
    .line 171
    sget-object p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$c$a;->a:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$c$a;

    .line 172
    .line 173
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 174
    .line 175
    .line 176
    move-result-object p1

    .line 177
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->z5:Lkotlinx/coroutines/flow/V;

    .line 178
    .line 179
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 180
    .line 181
    const/4 p2, 0x3

    .line 182
    invoke-direct {p1, v0, v1, p2, v1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 183
    .line 184
    .line 185
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->A5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 186
    .line 187
    sget-object p1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 188
    .line 189
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 190
    .line 191
    .line 192
    move-result-object p1

    .line 193
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 194
    .line 195
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 196
    .line 197
    .line 198
    move-result-object p1

    .line 199
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 200
    .line 201
    .line 202
    move-result-object p1

    .line 203
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->C5:Lkotlinx/coroutines/flow/V;

    .line 204
    .line 205
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 206
    .line 207
    invoke-direct {p1, v0, v1, p2, v1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 208
    .line 209
    .line 210
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->D5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 211
    .line 212
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 213
    .line 214
    invoke-direct {p1, v0, v1, p2, v1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 215
    .line 216
    .line 217
    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->E5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 218
    .line 219
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->m4()V

    .line 220
    .line 221
    .line 222
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->k4()V

    .line 223
    .line 224
    .line 225
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->s4()V

    .line 226
    .line 227
    .line 228
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->o4()V

    .line 229
    .line 230
    .line 231
    return-void
.end method

.method public static final synthetic A3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/l0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->v5:Lorg/xbet/coupon/impl/coupon/domain/usecases/l0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/y0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->I1:Lorg/xbet/coupon/impl/coupon/domain/usecases/y0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Ltw/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->I2:Ltw/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->D5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->z5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic F3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->b2:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic G3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->h4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic H3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/d2;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->y1:Lorg/xbet/coupon/impl/coupon/domain/usecases/d2;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic I3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->j4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic J3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->l4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic K3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->n4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic L3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->p4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic M3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->q4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic N3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->r4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic O3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->t4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic P3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->u4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic Q3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->v4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic R3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Luw/a;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->w4(Luw/a;Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic S3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->C4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic T3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->D4(Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final h4(Ljava/lang/Throwable;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->I3:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/a;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/a;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static final i4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->D5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    sget-object p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$b$a;->a:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$b$a;

    .line 4
    .line 5
    invoke-virtual {p0, p1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final synthetic l4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->h4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic n4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->h4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic p3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->i4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic p4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->h4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic q3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Z
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->W3()Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic r3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->Z3(Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic r4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->h4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic s3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->x5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->y5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->h4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->F5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->h4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->A5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/W;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->S2:Lorg/xbet/coupon/impl/coupon/domain/usecases/W;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/d0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->F1:Lorg/xbet/coupon/impl/coupon/domain/usecases/d0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/j0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->H1:Lorg/xbet/coupon/impl/coupon/domain/usecases/j0;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final A4(F)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->z5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    instance-of v0, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$c$b;

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->z5:Lkotlinx/coroutines/flow/V;

    .line 12
    .line 13
    new-instance v1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$c$b;

    .line 14
    .line 15
    invoke-direct {v1, p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$c$b;-><init>(F)V

    .line 16
    .line 17
    .line 18
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    :cond_0
    return-void
.end method

.method public final B4(Ljava/lang/String;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->X2:Lzg/g;

    .line 2
    .line 3
    invoke-virtual {v0}, Lzg/g;->a()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->w5:LqR/a;

    .line 7
    .line 8
    invoke-interface {v0, p1}, LqR/a;->f(Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->H3:LwX0/c;

    .line 12
    .line 13
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->F3:LB90/a;

    .line 14
    .line 15
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->COUPON:Lorg/xbet/balance/model/BalanceScreenType;

    .line 16
    .line 17
    invoke-interface {v0, v1}, LB90/a;->a(Lorg/xbet/balance/model/BalanceScreenType;)Lq4/q;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final C4()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->W3()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_1

    .line 6
    .line 7
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->F5:Lkotlinx/coroutines/x0;

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    const/4 v1, 0x1

    .line 16
    if-ne v0, v1, :cond_0

    .line 17
    .line 18
    return-void

    .line 19
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    sget-object v5, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 24
    .line 25
    sget-object v7, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$setCoefLoadingState$1;->INSTANCE:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$setCoefLoadingState$1;

    .line 26
    .line 27
    new-instance v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$setCoefLoadingState$2;

    .line 28
    .line 29
    const/4 v0, 0x0

    .line 30
    invoke-direct {v8, p0, v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$setCoefLoadingState$2;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V

    .line 31
    .line 32
    .line 33
    const/16 v10, 0x24

    .line 34
    .line 35
    const/4 v11, 0x0

    .line 36
    const-wide/16 v3, 0x12c

    .line 37
    .line 38
    const/4 v6, 0x0

    .line 39
    const/4 v9, 0x0

    .line 40
    invoke-static/range {v2 .. v11}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->P(Lkotlinx/coroutines/N;JLjava/util/concurrent/TimeUnit;Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    iput-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->F5:Lkotlinx/coroutines/x0;

    .line 45
    .line 46
    :cond_1
    return-void
.end method

.method public final D4(Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)V
    .locals 10

    .line 1
    sget-object v0, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->MULTI_BET:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x1

    .line 5
    if-ne p1, v0, :cond_0

    .line 6
    .line 7
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->V1:Lorg/xbet/coupon/impl/coupon/domain/usecases/w1;

    .line 8
    .line 9
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/coupon/domain/usecases/w1;->a()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    const/4 v3, 0x2

    .line 14
    if-gt v0, v3, :cond_1

    .line 15
    .line 16
    :cond_0
    sget-object v0, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->SYSTEM:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 17
    .line 18
    if-ne p1, v0, :cond_2

    .line 19
    .line 20
    :cond_1
    const/4 v5, 0x1

    .line 21
    goto :goto_0

    .line 22
    :cond_2
    const/4 v5, 0x0

    .line 23
    :goto_0
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->y5:Lkotlinx/coroutines/flow/V;

    .line 24
    .line 25
    :cond_3
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    move-object v3, v0

    .line 30
    check-cast v3, LAx/d;

    .line 31
    .line 32
    invoke-virtual {v3}, LAx/d;->f()Luw/a;

    .line 33
    .line 34
    .line 35
    move-result-object v4

    .line 36
    if-eqz v5, :cond_5

    .line 37
    .line 38
    if-eqz v4, :cond_4

    .line 39
    .line 40
    sget-object v6, Luw/a;->e:Luw/a$a;

    .line 41
    .line 42
    invoke-virtual {v6}, Luw/a$a;->a()Luw/a;

    .line 43
    .line 44
    .line 45
    move-result-object v6

    .line 46
    invoke-static {v4, v6}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 47
    .line 48
    .line 49
    move-result v4

    .line 50
    if-eqz v4, :cond_5

    .line 51
    .line 52
    :cond_4
    const/4 v6, 0x1

    .line 53
    goto :goto_1

    .line 54
    :cond_5
    const/4 v6, 0x0

    .line 55
    :goto_1
    const/16 v8, 0x9

    .line 56
    .line 57
    const/4 v9, 0x0

    .line 58
    const/4 v4, 0x0

    .line 59
    const/4 v7, 0x0

    .line 60
    invoke-static/range {v3 .. v9}, LAx/d;->b(LAx/d;Ljava/lang/String;ZZLuw/a;ILjava/lang/Object;)LAx/d;

    .line 61
    .line 62
    .line 63
    move-result-object v3

    .line 64
    invoke-interface {p1, v0, v3}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 65
    .line 66
    .line 67
    move-result v0

    .line 68
    if-eqz v0, :cond_3

    .line 69
    .line 70
    return-void
.end method

.method public final U3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->P2:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$changeBetSystem$2;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final V3()V
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->y5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    move-object v2, v1

    .line 8
    check-cast v2, LAx/d;

    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->a4()Ljava/lang/String;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    const/16 v7, 0xe

    .line 15
    .line 16
    const/4 v8, 0x0

    .line 17
    const/4 v4, 0x0

    .line 18
    const/4 v5, 0x0

    .line 19
    const/4 v6, 0x0

    .line 20
    invoke-static/range {v2 .. v8}, LAx/d;->b(LAx/d;Ljava/lang/String;ZZLuw/a;ILjava/lang/Object;)LAx/d;

    .line 21
    .line 22
    .line 23
    move-result-object v2

    .line 24
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    if-eqz v1, :cond_0

    .line 29
    .line 30
    return-void
.end method

.method public final W3()Z
    .locals 4

    .line 1
    const/4 v0, 0x6

    .line 2
    new-array v0, v0, [Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 3
    .line 4
    sget-object v1, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->MULTI_BET:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 5
    .line 6
    const/4 v2, 0x0

    .line 7
    aput-object v1, v0, v2

    .line 8
    .line 9
    sget-object v1, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->CEPOCHKA:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 10
    .line 11
    const/4 v2, 0x1

    .line 12
    aput-object v1, v0, v2

    .line 13
    .line 14
    sget-object v1, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->CONDITION_BET:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 15
    .line 16
    const/4 v3, 0x2

    .line 17
    aput-object v1, v0, v3

    .line 18
    .line 19
    sget-object v1, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->LUCKY:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 20
    .line 21
    const/4 v3, 0x3

    .line 22
    aput-object v1, v0, v3

    .line 23
    .line 24
    sget-object v1, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->PATENT:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 25
    .line 26
    const/4 v3, 0x4

    .line 27
    aput-object v1, v0, v3

    .line 28
    .line 29
    sget-object v1, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->MULTI_SINGLE:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 30
    .line 31
    const/4 v3, 0x5

    .line 32
    aput-object v1, v0, v3

    .line 33
    .line 34
    invoke-static {v0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    iget-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->I2:Ltw/d;

    .line 39
    .line 40
    invoke-interface {v1}, Ltw/d;->invoke()Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    invoke-interface {v0, v1}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 45
    .line 46
    .line 47
    move-result v0

    .line 48
    xor-int/2addr v0, v2

    .line 49
    return v0
.end method

.method public final X3(ZLjava/util/List;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)Z
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/util/List<",
            "LWn/a;",
            ">;",
            "Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;",
            ")Z"
        }
    .end annotation

    .line 1
    const/4 v0, 0x1

    .line 2
    const/4 v1, 0x0

    .line 3
    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->H2:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 4
    .line 5
    invoke-interface {v2}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-virtual {v2}, Lek0/o;->L()Lek0/c;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-virtual {v2}, Lek0/c;->m()Z

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    if-eqz v2, :cond_3

    .line 18
    .line 19
    if-eqz p1, :cond_3

    .line 20
    .line 21
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 22
    .line 23
    .line 24
    move-result p1

    .line 25
    if-eqz p1, :cond_0

    .line 26
    .line 27
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 28
    .line 29
    .line 30
    move-result p1

    .line 31
    if-eqz p1, :cond_0

    .line 32
    .line 33
    goto :goto_0

    .line 34
    :cond_0
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    :cond_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 39
    .line 40
    .line 41
    move-result p2

    .line 42
    if-eqz p2, :cond_2

    .line 43
    .line 44
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    move-result-object p2

    .line 48
    check-cast p2, LWn/a;

    .line 49
    .line 50
    invoke-virtual {p2}, LWn/a;->g()J

    .line 51
    .line 52
    .line 53
    move-result-wide v2

    .line 54
    const-wide/16 v4, 0x2c3

    .line 55
    .line 56
    cmp-long p2, v2, v4

    .line 57
    .line 58
    if-nez p2, :cond_1

    .line 59
    .line 60
    goto :goto_1

    .line 61
    :cond_2
    :goto_0
    const/4 p1, 0x2

    .line 62
    new-array p1, p1, [Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 63
    .line 64
    sget-object p2, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->SINGLE:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 65
    .line 66
    aput-object p2, p1, v1

    .line 67
    .line 68
    sget-object p2, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->EXPRESS:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 69
    .line 70
    aput-object p2, p1, v0

    .line 71
    .line 72
    invoke-static {p1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    invoke-interface {p1, p3}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 77
    .line 78
    .line 79
    move-result p1

    .line 80
    if-eqz p1, :cond_3

    .line 81
    .line 82
    return v0

    .line 83
    :cond_3
    :goto_1
    return v1
.end method

.method public final Y3(ZLorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)Z
    .locals 3

    .line 1
    const/4 v0, 0x1

    .line 2
    const/4 v1, 0x0

    .line 3
    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->H2:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 4
    .line 5
    invoke-interface {v2}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-virtual {v2}, Lek0/o;->e2()Lek0/m;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-virtual {v2}, Lek0/m;->o()Z

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    if-eqz v2, :cond_0

    .line 18
    .line 19
    if-eqz p1, :cond_0

    .line 20
    .line 21
    const/4 p1, 0x2

    .line 22
    new-array p1, p1, [Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 23
    .line 24
    sget-object v2, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->CONDITION_BET:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 25
    .line 26
    aput-object v2, p1, v1

    .line 27
    .line 28
    sget-object v2, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->MULTI_SINGLE:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 29
    .line 30
    aput-object v2, p1, v0

    .line 31
    .line 32
    invoke-static {p1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    invoke-interface {p1, p2}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 37
    .line 38
    .line 39
    move-result p1

    .line 40
    if-nez p1, :cond_0

    .line 41
    .line 42
    return v0

    .line 43
    :cond_0
    return v1
.end method

.method public final Z3(Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x2

    .line 2
    const/4 v1, 0x1

    .line 3
    instance-of v2, p2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$configureBetTypes$1;

    .line 4
    .line 5
    if-eqz v2, :cond_0

    .line 6
    .line 7
    move-object v2, p2

    .line 8
    check-cast v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$configureBetTypes$1;

    .line 9
    .line 10
    iget v3, v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$configureBetTypes$1;->label:I

    .line 11
    .line 12
    const/high16 v4, -0x80000000

    .line 13
    .line 14
    and-int v5, v3, v4

    .line 15
    .line 16
    if-eqz v5, :cond_0

    .line 17
    .line 18
    sub-int/2addr v3, v4

    .line 19
    iput v3, v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$configureBetTypes$1;->label:I

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    new-instance v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$configureBetTypes$1;

    .line 23
    .line 24
    invoke-direct {v2, p0, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$configureBetTypes$1;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    :goto_0
    iget-object p2, v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$configureBetTypes$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v3

    .line 33
    iget v4, v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$configureBetTypes$1;->label:I

    .line 34
    .line 35
    if-eqz v4, :cond_2

    .line 36
    .line 37
    if-ne v4, v1, :cond_1

    .line 38
    .line 39
    iget-object p1, v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$configureBetTypes$1;->L$0:Ljava/lang/Object;

    .line 40
    .line 41
    check-cast p1, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 42
    .line 43
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 44
    .line 45
    .line 46
    goto :goto_1

    .line 47
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 48
    .line 49
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 50
    .line 51
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    throw p1

    .line 55
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    iget-object p2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->v2:Lorg/xbet/coupon/impl/coupon/domain/usecases/I;

    .line 59
    .line 60
    iput-object p1, v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$configureBetTypes$1;->L$0:Ljava/lang/Object;

    .line 61
    .line 62
    iput v1, v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$configureBetTypes$1;->label:I

    .line 63
    .line 64
    invoke-virtual {p2, v2}, Lorg/xbet/coupon/impl/coupon/domain/usecases/I;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object p2

    .line 68
    if-ne p2, v3, :cond_3

    .line 69
    .line 70
    return-object v3

    .line 71
    :cond_3
    :goto_1
    check-cast p2, Ljava/util/List;

    .line 72
    .line 73
    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->F2:Lorg/xbet/remoteconfig/domain/usecases/k;

    .line 74
    .line 75
    invoke-interface {v2}, Lorg/xbet/remoteconfig/domain/usecases/k;->invoke()Z

    .line 76
    .line 77
    .line 78
    move-result v2

    .line 79
    xor-int/2addr v2, v1

    .line 80
    invoke-virtual {p0, v2, p2, p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->X3(ZLjava/util/List;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)Z

    .line 81
    .line 82
    .line 83
    move-result p2

    .line 84
    invoke-virtual {p0, v2, p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->Y3(ZLorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)Z

    .line 85
    .line 86
    .line 87
    move-result p1

    .line 88
    sget-object v2, LAx/g$b;->c:LAx/g$b;

    .line 89
    .line 90
    const/4 v3, 0x0

    .line 91
    if-eqz p1, :cond_4

    .line 92
    .line 93
    goto :goto_2

    .line 94
    :cond_4
    move-object v2, v3

    .line 95
    :goto_2
    new-instance v4, LAx/g$a;

    .line 96
    .line 97
    if-eqz p1, :cond_5

    .line 98
    .line 99
    const/4 p1, 0x2

    .line 100
    goto :goto_3

    .line 101
    :cond_5
    const/4 p1, 0x1

    .line 102
    :goto_3
    invoke-direct {v4, p1}, LAx/g$a;-><init>(I)V

    .line 103
    .line 104
    .line 105
    if-eqz p2, :cond_6

    .line 106
    .line 107
    move-object v3, v4

    .line 108
    :cond_6
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->C5:Lkotlinx/coroutines/flow/V;

    .line 109
    .line 110
    const/4 p2, 0x3

    .line 111
    new-array p2, p2, [LAx/g;

    .line 112
    .line 113
    sget-object v4, LAx/g$c;->c:LAx/g$c;

    .line 114
    .line 115
    const/4 v5, 0x0

    .line 116
    aput-object v4, p2, v5

    .line 117
    .line 118
    aput-object v2, p2, v1

    .line 119
    .line 120
    aput-object v3, p2, v0

    .line 121
    .line 122
    invoke-static {p2}, Lkotlin/collections/v;->s([Ljava/lang/Object;)Ljava/util/List;

    .line 123
    .line 124
    .line 125
    move-result-object p2

    .line 126
    invoke-interface {p1, p2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 127
    .line 128
    .line 129
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->E5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 130
    .line 131
    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 132
    .line 133
    invoke-virtual {p1, p2}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 134
    .line 135
    .line 136
    return-object p2
.end method

.method public final a4()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->b2:LHX0/e;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->P1:Lorg/xbet/coupon/impl/coupon/domain/usecases/h0;

    .line 4
    .line 5
    invoke-virtual {v1}, Lorg/xbet/coupon/impl/coupon/domain/usecases/h0;->a()Lorg/xbet/coupon/impl/coupon/domain/models/CoefCheckTypeModel;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    sget-object v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$d;->a:[I

    .line 10
    .line 11
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    aget v1, v2, v1

    .line 16
    .line 17
    const/4 v2, 0x1

    .line 18
    if-eq v1, v2, :cond_2

    .line 19
    .line 20
    const/4 v2, 0x2

    .line 21
    if-eq v1, v2, :cond_1

    .line 22
    .line 23
    const/4 v2, 0x3

    .line 24
    if-ne v1, v2, :cond_0

    .line 25
    .line 26
    sget v1, Lpb/k;->to_up_accept:I

    .line 27
    .line 28
    goto :goto_0

    .line 29
    :cond_0
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 30
    .line 31
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 32
    .line 33
    .line 34
    throw v0

    .line 35
    :cond_1
    sget v1, Lpb/k;->to_any_accept:I

    .line 36
    .line 37
    goto :goto_0

    .line 38
    :cond_2
    sget v1, Lpb/k;->to_confirm:I

    .line 39
    .line 40
    :goto_0
    const/4 v2, 0x0

    .line 41
    new-array v2, v2, [Ljava/lang/Object;

    .line 42
    .line 43
    invoke-interface {v0, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    return-object v0
.end method

.method public final b4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "LAx/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->x5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final c4()Lkotlinx/coroutines/flow/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LAx/e;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->y5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$getBetSettingsUiModelStream$$inlined$map$1;

    .line 4
    .line 5
    invoke-direct {v1, v0, p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$getBetSettingsUiModelStream$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)V

    .line 6
    .line 7
    .line 8
    return-object v1
.end method

.method public final d4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/util/List<",
            "LAx/g;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->C5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final e4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->D5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final f4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->z5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final g4()Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->E5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final j4()V
    .locals 9

    .line 1
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->a4()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    iget-object v7, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->y5:Lkotlinx/coroutines/flow/V;

    .line 6
    .line 7
    :cond_0
    invoke-interface {v7}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v8

    .line 11
    move-object v0, v8

    .line 12
    check-cast v0, LAx/d;

    .line 13
    .line 14
    const/16 v5, 0xe

    .line 15
    .line 16
    const/4 v6, 0x0

    .line 17
    const/4 v2, 0x0

    .line 18
    const/4 v3, 0x0

    .line 19
    const/4 v4, 0x0

    .line 20
    invoke-static/range {v0 .. v6}, LAx/d;->b(LAx/d;Ljava/lang/String;ZZLuw/a;ILjava/lang/Object;)LAx/d;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-interface {v7, v8, v0}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 25
    .line 26
    .line 27
    move-result v0

    .line 28
    if-eqz v0, :cond_0

    .line 29
    .line 30
    return-void
.end method

.method public final k4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->v1:Lorg/xbet/coupon/impl/coupon/domain/usecases/M0;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/coupon/domain/usecases/M0;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->P2:Lm8/a;

    .line 22
    .line 23
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    new-instance v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$2;

    .line 32
    .line 33
    invoke-direct {v2, p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$2;-><init>(Ljava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method public final m4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->y2:Lxx/g;

    .line 2
    .line 3
    invoke-virtual {v0}, Lxx/g;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->P2:Lm8/a;

    .line 22
    .line 23
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    new-instance v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$2;

    .line 32
    .line 33
    invoke-direct {v2, p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$2;-><init>(Ljava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method public final o4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->S3:Lorg/xbet/coupon/impl/coupon/domain/usecases/C2;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/coupon/domain/usecases/C2;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeCouponTypeChange$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeCouponTypeChange$1;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeCouponTypeChange$2;

    .line 18
    .line 19
    invoke-direct {v1, p0, v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeCouponTypeChange$2;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->P2:Lm8/a;

    .line 31
    .line 32
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 37
    .line 38
    .line 39
    move-result-object v1

    .line 40
    new-instance v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeCouponTypeChange$3;

    .line 41
    .line 42
    invoke-direct {v2, p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeCouponTypeChange$3;-><init>(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 46
    .line 47
    .line 48
    return-void
.end method

.method public final q4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->A5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeCurrentCouponType$1;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v1, p0, v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeCurrentCouponType$1;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->P2:Lm8/a;

    .line 18
    .line 19
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    new-instance v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeCurrentCouponType$2;

    .line 28
    .line 29
    invoke-direct {v2, p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeCurrentCouponType$2;-><init>(Ljava/lang/Object;)V

    .line 30
    .line 31
    .line 32
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public final s4()V
    .locals 4

    .line 1
    new-instance v0, Lkotlin/jvm/internal/Ref$BooleanRef;

    .line 2
    .line 3
    invoke-direct {v0}, Lkotlin/jvm/internal/Ref$BooleanRef;-><init>()V

    .line 4
    .line 5
    .line 6
    iget-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->x1:Ltw/g;

    .line 7
    .line 8
    invoke-interface {v1}, Ltw/g;->invoke()Lkotlinx/coroutines/flow/e;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    new-instance v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeEventCount$1;

    .line 13
    .line 14
    const/4 v3, 0x0

    .line 15
    invoke-direct {v2, p0, v0, v3}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeEventCount$1;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/jvm/internal/Ref$BooleanRef;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    invoke-static {v1, v2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->P2:Lm8/a;

    .line 27
    .line 28
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    new-instance v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeEventCount$2;

    .line 37
    .line 38
    invoke-direct {v2, p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeEventCount$2;-><init>(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method public final u4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->S1:Lorg/xbet/coupon/impl/coupon/domain/usecases/G2;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/coupon/domain/usecases/G2;->a()Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeSystemChange$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeSystemChange$1;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->P2:Lm8/a;

    .line 22
    .line 23
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    new-instance v2, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeSystemChange$2;

    .line 32
    .line 33
    invoke-direct {v2, p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeSystemChange$2;-><init>(Ljava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method public final w4(Luw/a;Z)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->y5:Lkotlinx/coroutines/flow/V;

    .line 4
    .line 5
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    check-cast v1, LAx/d;

    .line 10
    .line 11
    invoke-virtual {v1}, LAx/d;->f()Luw/a;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    move-object/from16 v2, p1

    .line 16
    .line 17
    invoke-static {v2, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    if-nez v1, :cond_2

    .line 22
    .line 23
    const/4 v7, 0x7

    .line 24
    const/4 v8, 0x0

    .line 25
    const/4 v3, 0x0

    .line 26
    const/4 v4, 0x0

    .line 27
    const/4 v5, 0x0

    .line 28
    move/from16 v6, p2

    .line 29
    .line 30
    invoke-static/range {v2 .. v8}, Luw/a;->b(Luw/a;IIIZILjava/lang/Object;)Luw/a;

    .line 31
    .line 32
    .line 33
    move-result-object v13

    .line 34
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->y5:Lkotlinx/coroutines/flow/V;

    .line 35
    .line 36
    :cond_0
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 37
    .line 38
    .line 39
    move-result-object v2

    .line 40
    move-object v9, v2

    .line 41
    check-cast v9, LAx/d;

    .line 42
    .line 43
    const/4 v14, 0x3

    .line 44
    const/4 v15, 0x0

    .line 45
    const/4 v10, 0x0

    .line 46
    const/4 v11, 0x0

    .line 47
    const/4 v12, 0x0

    .line 48
    invoke-static/range {v9 .. v15}, LAx/d;->b(LAx/d;Ljava/lang/String;ZZLuw/a;ILjava/lang/Object;)LAx/d;

    .line 49
    .line 50
    .line 51
    move-result-object v3

    .line 52
    invoke-interface {v1, v2, v3}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 53
    .line 54
    .line 55
    move-result v2

    .line 56
    if-eqz v2, :cond_0

    .line 57
    .line 58
    if-eqz p2, :cond_1

    .line 59
    .line 60
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->X4:Ltw/i;

    .line 61
    .line 62
    invoke-interface {v1}, Ltw/i;->invoke()V

    .line 63
    .line 64
    .line 65
    :cond_1
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->V2:Lorg/xbet/coupon/impl/coupon/domain/usecases/w2;

    .line 66
    .line 67
    invoke-virtual {v1, v13}, Lorg/xbet/coupon/impl/coupon/domain/usecases/w2;->a(Luw/a;)V

    .line 68
    .line 69
    .line 70
    :cond_2
    return-void
.end method

.method public final x4()V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->U3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final y4(I)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$onBetSystemTypeSelected$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$onBetSystemTypeSelected$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->P2:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$onBetSystemTypeSelected$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$onBetSystemTypeSelected$2;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;ILkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final z4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->x2:Lp9/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Lp9/c;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->D5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 10
    .line 11
    sget-object v1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$b$b;->a:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$b$b;

    .line 12
    .line 13
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    return-void

    .line 17
    :cond_0
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->H3:LwX0/c;

    .line 18
    .line 19
    iget-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->H4:LwX0/a;

    .line 20
    .line 21
    invoke-interface {v1}, LwX0/a;->l()Lq4/q;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 26
    .line 27
    .line 28
    return-void
.end method
