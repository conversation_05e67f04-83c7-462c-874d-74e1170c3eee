.class public final Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0006\u0010\u0007\u001a\u0004\u0008\u0006\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$b;",
        "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a;",
        "",
        "loading",
        "<init>",
        "(Z)V",
        "a",
        "Z",
        "()Z",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Z


# direct methods
.method public constructor <init>(Z)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-boolean p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$b;->a:Z

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$b;->a:Z

    .line 2
    .line 3
    return v0
.end method
