.class public interface abstract LGM0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation build LNc/c;
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LGM0/a$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008a\u0018\u00002\u00020\u0001JH\u0010\u000b\u001a\u0008\u0012\u0004\u0012\u00020\n0\t2\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0001\u0010\u0004\u001a\u00020\u00022\u0008\u0008\u0001\u0010\u0006\u001a\u00020\u00052\u0008\u0008\u0001\u0010\u0007\u001a\u00020\u00052\u0008\u0008\u0003\u0010\u0008\u001a\u00020\u0002H\u00a7@\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "LGM0/a;",
        "",
        "",
        "stageId",
        "lng",
        "",
        "refId",
        "countryId",
        "acceptHeader",
        "Le8/b;",
        "LDM0/i;",
        "a",
        "(Ljava/lang/String;Ljava/lang/String;IILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Ljava/lang/String;Ljava/lang/String;IILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "id"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "lng"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # I
        .annotation runtime Lbd1/t;
            value = "ref"
        .end annotation
    .end param
    .param p4    # I
        .annotation runtime Lbd1/t;
            value = "fcountry"
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "Accept"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "/statisticGame/v2/StageTTable/champ"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "II",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "Le8/b<",
            "LDM0/i;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method
