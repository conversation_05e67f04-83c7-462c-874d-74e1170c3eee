.class final Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.crystal.data.repositories.CrystalRepository$makeBetGame$2"
    f = "CrystalRepository.kt"
    l = {
        0x18
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/crystal/data/repositories/CrystalRepository;->g(DJLorg/xbet/games_section/api/models/GameBonus;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/String;",
        "Lkotlin/coroutines/e<",
        "-",
        "LZx/b;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "",
        "token",
        "LZx/b;",
        "<anonymous>",
        "(Ljava/lang/String;)LZx/b;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $activeId:J

.field final synthetic $betSum:D

.field final synthetic $bonus:Lorg/xbet/games_section/api/models/GameBonus;

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/crystal/data/repositories/CrystalRepository;


# direct methods
.method public constructor <init>(Lorg/xbet/crystal/data/repositories/CrystalRepository;DJLorg/xbet/games_section/api/models/GameBonus;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/crystal/data/repositories/CrystalRepository;",
            "DJ",
            "Lorg/xbet/games_section/api/models/GameBonus;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->this$0:Lorg/xbet/crystal/data/repositories/CrystalRepository;

    iput-wide p2, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->$betSum:D

    iput-wide p4, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->$activeId:J

    iput-object p6, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->$bonus:Lorg/xbet/games_section/api/models/GameBonus;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p7}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;

    iget-object v1, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->this$0:Lorg/xbet/crystal/data/repositories/CrystalRepository;

    iget-wide v2, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->$betSum:D

    iget-wide v4, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->$activeId:J

    iget-object v6, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->$bonus:Lorg/xbet/games_section/api/models/GameBonus;

    move-object v7, p2

    invoke-direct/range {v0 .. v7}, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;-><init>(Lorg/xbet/crystal/data/repositories/CrystalRepository;DJLorg/xbet/games_section/api/models/GameBonus;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "LZx/b;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 11

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->L$0:Ljava/lang/Object;

    .line 13
    .line 14
    check-cast v0, LUx/c;

    .line 15
    .line 16
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    move-object v10, p0

    .line 20
    goto :goto_0

    .line 21
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 22
    .line 23
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 24
    .line 25
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 26
    .line 27
    .line 28
    throw p1

    .line 29
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 30
    .line 31
    .line 32
    iget-object p1, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->L$0:Ljava/lang/Object;

    .line 33
    .line 34
    move-object v9, p1

    .line 35
    check-cast v9, Ljava/lang/String;

    .line 36
    .line 37
    iget-object p1, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->this$0:Lorg/xbet/crystal/data/repositories/CrystalRepository;

    .line 38
    .line 39
    invoke-static {p1}, Lorg/xbet/crystal/data/repositories/CrystalRepository;->c(Lorg/xbet/crystal/data/repositories/CrystalRepository;)LUx/c;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    iget-object v1, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->this$0:Lorg/xbet/crystal/data/repositories/CrystalRepository;

    .line 44
    .line 45
    invoke-static {v1}, Lorg/xbet/crystal/data/repositories/CrystalRepository;->d(Lorg/xbet/crystal/data/repositories/CrystalRepository;)Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;

    .line 46
    .line 47
    .line 48
    move-result-object v3

    .line 49
    iget-wide v4, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->$betSum:D

    .line 50
    .line 51
    iget-wide v6, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->$activeId:J

    .line 52
    .line 53
    iget-object v8, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->$bonus:Lorg/xbet/games_section/api/models/GameBonus;

    .line 54
    .line 55
    iput-object p1, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->L$0:Ljava/lang/Object;

    .line 56
    .line 57
    iput v2, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->label:I

    .line 58
    .line 59
    move-object v10, p0

    .line 60
    invoke-virtual/range {v3 .. v10}, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;->d(DJLorg/xbet/games_section/api/models/GameBonus;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object v1

    .line 64
    if-ne v1, v0, :cond_2

    .line 65
    .line 66
    return-object v0

    .line 67
    :cond_2
    move-object v0, p1

    .line 68
    move-object p1, v1

    .line 69
    :goto_0
    check-cast p1, LVx/a;

    .line 70
    .line 71
    invoke-virtual {v0, p1}, LUx/c;->a(LVx/a;)LZx/b;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    iget-object v0, v10, Lorg/xbet/crystal/data/repositories/CrystalRepository$makeBetGame$2;->this$0:Lorg/xbet/crystal/data/repositories/CrystalRepository;

    .line 76
    .line 77
    invoke-static {v0}, Lorg/xbet/crystal/data/repositories/CrystalRepository;->b(Lorg/xbet/crystal/data/repositories/CrystalRepository;)Lorg/xbet/crystal/data/datasources/a;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    invoke-virtual {v0, p1}, Lorg/xbet/crystal/data/datasources/a;->c(LZx/b;)V

    .line 82
    .line 83
    .line 84
    return-object p1
.end method
