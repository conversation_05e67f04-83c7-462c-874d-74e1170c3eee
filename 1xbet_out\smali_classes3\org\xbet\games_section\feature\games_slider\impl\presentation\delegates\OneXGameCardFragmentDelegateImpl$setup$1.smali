.class final Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.games_slider.impl.presentation.delegates.OneXGameCardFragmentDelegateImpl$setup$1"
    f = "OneXGameCardFragmentDelegateImpl.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl;->a(Landroidx/fragment/app/Fragment;LF40/c;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "LF40/d;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "LF40/d;",
        "state",
        "",
        "<anonymous>",
        "(LF40/d;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $fragment:Landroidx/fragment/app/Fragment;

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl;Landroidx/fragment/app/Fragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl;",
            "Landroidx/fragment/app/Fragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl;

    iput-object p2, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;->$fragment:Landroidx/fragment/app/Fragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;

    iget-object v1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl;

    iget-object v2, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;->$fragment:Landroidx/fragment/app/Fragment;

    invoke-direct {v0, v1, v2, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;-><init>(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl;Landroidx/fragment/app/Fragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public final invoke(LF40/d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LF40/d;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, LF40/d;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;->invoke(LF40/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_1

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, LF40/d;

    .line 14
    .line 15
    instance-of p1, p1, LF40/d$a;

    .line 16
    .line 17
    if-eqz p1, :cond_0

    .line 18
    .line 19
    iget-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;->this$0:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl;

    .line 20
    .line 21
    iget-object v0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl$setup$1;->$fragment:Landroidx/fragment/app/Fragment;

    .line 22
    .line 23
    invoke-static {p1, v0}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl;->b(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardFragmentDelegateImpl;Landroidx/fragment/app/Fragment;)V

    .line 24
    .line 25
    .line 26
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 27
    .line 28
    return-object p1

    .line 29
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 30
    .line 31
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 32
    .line 33
    .line 34
    throw p1

    .line 35
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 36
    .line 37
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 38
    .line 39
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 40
    .line 41
    .line 42
    throw p1
.end method
