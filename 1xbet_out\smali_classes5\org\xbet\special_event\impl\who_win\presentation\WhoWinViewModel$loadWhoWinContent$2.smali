.class final Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.who_win.presentation.WhoWinViewModel$loadWhoWinContent$2"
    f = "WhoWinViewModel.kt"
    l = {
        0x5c,
        0x63
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->B3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;

    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    goto :goto_3

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    goto :goto_0

    .line 31
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 35
    .line 36
    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->s3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)Lp9/c;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    invoke-virtual {p1}, Lp9/c;->a()Z

    .line 41
    .line 42
    .line 43
    move-result p1

    .line 44
    if-eqz p1, :cond_4

    .line 45
    .line 46
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 47
    .line 48
    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->t3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iput v3, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;->label:I

    .line 53
    .line 54
    const/4 v1, 0x0

    .line 55
    invoke-virtual {p1, v1, p0}, Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;->c(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    if-ne p1, v0, :cond_3

    .line 60
    .line 61
    goto :goto_2

    .line 62
    :cond_3
    :goto_0
    check-cast p1, Le9/a;

    .line 63
    .line 64
    invoke-virtual {p1}, Le9/a;->w()Ljava/lang/String;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    invoke-static {p1}, Ll8/a;->e(Ljava/lang/String;)I

    .line 69
    .line 70
    .line 71
    move-result p1

    .line 72
    invoke-static {p1}, LHc/a;->e(I)Ljava/lang/Integer;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    goto :goto_1

    .line 77
    :cond_4
    const/4 p1, 0x0

    .line 78
    :goto_1
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 79
    .line 80
    invoke-static {v1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->u3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    iget-object v3, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 85
    .line 86
    invoke-static {v3}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->r3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)I

    .line 87
    .line 88
    .line 89
    move-result v3

    .line 90
    invoke-virtual {v1, v3, p1}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->g(ILjava/lang/Integer;)Lkotlinx/coroutines/flow/e;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    iput v2, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;->label:I

    .line 95
    .line 96
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/g;->m(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    if-ne p1, v0, :cond_5

    .line 101
    .line 102
    :goto_2
    return-object v0

    .line 103
    :cond_5
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 104
    .line 105
    return-object p1
.end method
