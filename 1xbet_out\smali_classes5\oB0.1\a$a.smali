.class public final LoB0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LoB0/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LoB0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LoB0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LoB0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LKA0/c;LQW0/c;LSX0/a;)LoB0/c;
    .locals 2

    .line 1
    invoke-static {p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static {p2}, Ldagger/internal/g;->b(<PERSON>ja<PERSON>/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static {p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    new-instance v0, LoB0/a$b;

    .line 11
    .line 12
    const/4 v1, 0x0

    .line 13
    invoke-direct {v0, p1, p2, p3, v1}, LoB0/a$b;-><init>(LKA0/c;LQW0/c;LSX0/a;LoB0/b;)V

    .line 14
    .line 15
    .line 16
    return-object v0
.end method
