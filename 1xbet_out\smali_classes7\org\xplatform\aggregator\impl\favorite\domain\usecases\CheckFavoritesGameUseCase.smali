.class public final Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0007\u0008\u0000\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J(\u0010\u000e\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000cH\u0086B\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u0011R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u0012\u00a8\u0006\u0013"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;",
        "",
        "Lu81/b;",
        "repository",
        "Lm8/a;",
        "dispatchers",
        "<init>",
        "(Lu81/b;Lm8/a;)V",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "",
        "brandsApi",
        "",
        "endPoint",
        "b",
        "(Lorg/xplatform/aggregator/api/model/Game;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "a",
        "Lu81/b;",
        "Lm8/a;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lu81/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lu81/b;Lm8/a;)V
    .locals 0
    .param p1    # Lu81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;->a:Lu81/b;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;->b:Lm8/a;

    .line 7
    .line 8
    return-void
.end method

.method public static final synthetic a(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;)Lu81/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;->a:Lu81/b;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final b(Lorg/xplatform/aggregator/api/model/Game;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "Z",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/lang/Boolean;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;->b:Lm8/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;

    .line 8
    .line 9
    const/4 v6, 0x0

    .line 10
    move-object v2, p0

    .line 11
    move-object v3, p1

    .line 12
    move v4, p2

    .line 13
    move-object v5, p3

    .line 14
    invoke-direct/range {v1 .. v6}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;-><init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;Lorg/xplatform/aggregator/api/model/Game;ZLjava/lang/String;Lkotlin/coroutines/e;)V

    .line 15
    .line 16
    .line 17
    invoke-static {v0, v1, p4}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    return-object p1
.end method
