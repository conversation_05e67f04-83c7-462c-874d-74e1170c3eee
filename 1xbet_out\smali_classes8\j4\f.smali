.class public interface abstract Lj4/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lj4/g;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lj4/g<",
        "Lcom/github/mikephil/charting/data/Entry;",
        ">;"
    }
.end annotation


# virtual methods
.method public abstract A0()F
.end method

.method public abstract B()I
.end method

.method public abstract F()Lg4/d;
.end method

.method public abstract J()Landroid/graphics/DashPathEffect;
.end method

.method public abstract S(I)I
.end method

.method public abstract S0()Z
.end method

.method public abstract T()Z
.end method

.method public abstract V()F
.end method

.method public abstract e0()F
.end method

.method public abstract u()Lcom/github/mikephil/charting/data/LineDataSet$Mode;
.end method

.method public abstract v()Z
.end method

.method public abstract w()I
.end method
