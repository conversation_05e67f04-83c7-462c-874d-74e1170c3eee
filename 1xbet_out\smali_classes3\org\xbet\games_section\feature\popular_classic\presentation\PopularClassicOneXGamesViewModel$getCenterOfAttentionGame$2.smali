.class final Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.popular_classic.presentation.PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2"
    f = "PopularClassicOneXGamesViewModel.kt"
    l = {
        0x153
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->u4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field private synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;

    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;->L$0:Ljava/lang/Object;

    .line 13
    .line 14
    check-cast v0, Lkotlinx/coroutines/N;

    .line 15
    .line 16
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;->L$0:Ljava/lang/Object;

    .line 32
    .line 33
    check-cast p1, Lkotlinx/coroutines/N;

    .line 34
    .line 35
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 36
    .line 37
    invoke-static {v1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->J3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lv30/a;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;->L$0:Ljava/lang/Object;

    .line 42
    .line 43
    iput v2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;->label:I

    .line 44
    .line 45
    invoke-interface {v1, p0}, Lv30/a;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    if-ne p1, v0, :cond_2

    .line 50
    .line 51
    return-object v0

    .line 52
    :cond_2
    :goto_0
    check-cast p1, Ls30/a;

    .line 53
    .line 54
    if-eqz p1, :cond_3

    .line 55
    .line 56
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 57
    .line 58
    invoke-static {v0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->D3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 63
    .line 64
    new-instance v2, Lk50/a$a;

    .line 65
    .line 66
    invoke-direct {v2, p1}, Lk50/a$a;-><init>(Ls30/a;)V

    .line 67
    .line 68
    .line 69
    invoke-direct {v1, v2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;-><init>(Ljava/lang/Object;)V

    .line 70
    .line 71
    .line 72
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 73
    .line 74
    .line 75
    goto :goto_1

    .line 76
    :cond_3
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getCenterOfAttentionGame$2;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 77
    .line 78
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->D3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    sget-object v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;

    .line 83
    .line 84
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 85
    .line 86
    .line 87
    :goto_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 88
    .line 89
    return-object p1
.end method
