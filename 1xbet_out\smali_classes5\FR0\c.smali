.class public final synthetic LFR0/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Landroidx/compose/runtime/h0;


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/runtime/h0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LFR0/c;->a:Landroidx/compose/runtime/h0;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LFR0/c;->a:Landroidx/compose/runtime/h0;

    check-cast p1, Lt0/t;

    invoke-static {v0, p1}, LFR0/b$b;->d(Landroidx/compose/runtime/h0;Lt0/t;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
