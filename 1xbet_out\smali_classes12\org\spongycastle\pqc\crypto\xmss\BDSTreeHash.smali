.class Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/io/Serializable;


# static fields
.field private static final serialVersionUID:J = 0x1L


# instance fields
.field private finished:Z

.field private height:I

.field private final initialHeight:I

.field private initialized:Z

.field private nextIndex:I

.field private tailNode:Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;


# direct methods
.method public constructor <init>(I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput p1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->initialHeight:I

    .line 5
    .line 6
    const/4 p1, 0x0

    .line 7
    iput-boolean p1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->initialized:Z

    .line 8
    .line 9
    iput-boolean p1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->finished:Z

    .line 10
    .line 11
    return-void
.end method


# virtual methods
.method public getHeight()I
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->initialized:Z

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    iget-boolean v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->finished:Z

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    iget v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->height:I

    .line 11
    .line 12
    return v0

    .line 13
    :cond_1
    :goto_0
    const v0, 0x7fffffff

    .line 14
    .line 15
    .line 16
    return v0
.end method

.method public getIndexLeaf()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->nextIndex:I

    .line 2
    .line 3
    return v0
.end method

.method public getTailNode()Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->tailNode:Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;->clone()Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public initialize(I)V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->tailNode:Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    .line 3
    .line 4
    iget v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->initialHeight:I

    .line 5
    .line 6
    iput v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->height:I

    .line 7
    .line 8
    iput p1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->nextIndex:I

    .line 9
    .line 10
    const/4 p1, 0x1

    .line 11
    iput-boolean p1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->initialized:Z

    .line 12
    .line 13
    const/4 p1, 0x0

    .line 14
    iput-boolean p1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->finished:Z

    .line 15
    .line 16
    return-void
.end method

.method public isFinished()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->finished:Z

    .line 2
    .line 3
    return v0
.end method

.method public isInitialized()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->initialized:Z

    .line 2
    .line 3
    return v0
.end method

.method public setNode(Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;)V
    .locals 1

    .line 1
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->tailNode:Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    .line 2
    .line 3
    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;->getHeight()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    iput p1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->height:I

    .line 8
    .line 9
    iget v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->initialHeight:I

    .line 10
    .line 11
    if-ne p1, v0, :cond_0

    .line 12
    .line 13
    const/4 p1, 0x1

    .line 14
    iput-boolean p1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->finished:Z

    .line 15
    .line 16
    :cond_0
    return-void
.end method

.method public update(Ljava/util/Stack;Lorg/spongycastle/pqc/crypto/xmss/g;[B[BLorg/spongycastle/pqc/crypto/xmss/f;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Stack<",
            "Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;",
            ">;",
            "Lorg/spongycastle/pqc/crypto/xmss/g;",
            "[B[B",
            "Lorg/spongycastle/pqc/crypto/xmss/f;",
            ")V"
        }
    .end annotation

    if-eqz p5, :cond_5

    .line 1
    iget-boolean v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->finished:Z

    if-nez v0, :cond_4

    iget-boolean v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->initialized:Z

    if-eqz v0, :cond_4

    .line 2
    new-instance v0, Lorg/spongycastle/pqc/crypto/xmss/f$b;

    invoke-direct {v0}, Lorg/spongycastle/pqc/crypto/xmss/f$b;-><init>()V

    .line 3
    invoke-virtual {p5}, Lorg/spongycastle/pqc/crypto/xmss/k;->b()I

    move-result v1

    invoke-virtual {v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->g(I)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object v0

    check-cast v0, Lorg/spongycastle/pqc/crypto/xmss/f$b;

    invoke-virtual {p5}, Lorg/spongycastle/pqc/crypto/xmss/k;->c()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->h(J)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object v0

    check-cast v0, Lorg/spongycastle/pqc/crypto/xmss/f$b;

    iget v1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->nextIndex:I

    .line 4
    invoke-virtual {v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/f$b;->p(I)Lorg/spongycastle/pqc/crypto/xmss/f$b;

    move-result-object v0

    invoke-virtual {p5}, Lorg/spongycastle/pqc/crypto/xmss/f;->e()I

    move-result v1

    invoke-virtual {v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/f$b;->n(I)Lorg/spongycastle/pqc/crypto/xmss/f$b;

    move-result-object v0

    .line 5
    invoke-virtual {p5}, Lorg/spongycastle/pqc/crypto/xmss/f;->f()I

    move-result v1

    invoke-virtual {v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/f$b;->o(I)Lorg/spongycastle/pqc/crypto/xmss/f$b;

    move-result-object v0

    invoke-virtual {p5}, Lorg/spongycastle/pqc/crypto/xmss/k;->a()I

    move-result p5

    invoke-virtual {v0, p5}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->f(I)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object p5

    check-cast p5, Lorg/spongycastle/pqc/crypto/xmss/f$b;

    .line 6
    invoke-virtual {p5}, Lorg/spongycastle/pqc/crypto/xmss/f$b;->l()Lorg/spongycastle/pqc/crypto/xmss/k;

    move-result-object p5

    check-cast p5, Lorg/spongycastle/pqc/crypto/xmss/f;

    .line 7
    new-instance v0, Lorg/spongycastle/pqc/crypto/xmss/e$b;

    invoke-direct {v0}, Lorg/spongycastle/pqc/crypto/xmss/e$b;-><init>()V

    .line 8
    invoke-virtual {p5}, Lorg/spongycastle/pqc/crypto/xmss/k;->b()I

    move-result v1

    invoke-virtual {v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->g(I)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object v0

    check-cast v0, Lorg/spongycastle/pqc/crypto/xmss/e$b;

    invoke-virtual {p5}, Lorg/spongycastle/pqc/crypto/xmss/k;->c()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->h(J)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object v0

    check-cast v0, Lorg/spongycastle/pqc/crypto/xmss/e$b;

    iget v1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->nextIndex:I

    .line 9
    invoke-virtual {v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/e$b;->n(I)Lorg/spongycastle/pqc/crypto/xmss/e$b;

    move-result-object v0

    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/e$b;->l()Lorg/spongycastle/pqc/crypto/xmss/k;

    move-result-object v0

    check-cast v0, Lorg/spongycastle/pqc/crypto/xmss/e;

    .line 10
    new-instance v1, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    invoke-direct {v1}, Lorg/spongycastle/pqc/crypto/xmss/c$b;-><init>()V

    .line 11
    invoke-virtual {p5}, Lorg/spongycastle/pqc/crypto/xmss/k;->b()I

    move-result v2

    invoke-virtual {v1, v2}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->g(I)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object v1

    check-cast v1, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    invoke-virtual {p5}, Lorg/spongycastle/pqc/crypto/xmss/k;->c()J

    move-result-wide v2

    invoke-virtual {v1, v2, v3}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->h(J)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object v1

    check-cast v1, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    iget v2, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->nextIndex:I

    .line 12
    invoke-virtual {v1, v2}, Lorg/spongycastle/pqc/crypto/xmss/c$b;->n(I)Lorg/spongycastle/pqc/crypto/xmss/c$b;

    move-result-object v1

    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/c$b;->k()Lorg/spongycastle/pqc/crypto/xmss/k;

    move-result-object v1

    check-cast v1, Lorg/spongycastle/pqc/crypto/xmss/c;

    .line 13
    invoke-virtual {p2, p4, p5}, Lorg/spongycastle/pqc/crypto/xmss/g;->g([BLorg/spongycastle/pqc/crypto/xmss/f;)[B

    move-result-object p4

    invoke-virtual {p2, p4, p3}, Lorg/spongycastle/pqc/crypto/xmss/g;->h([B[B)V

    .line 14
    invoke-virtual {p2, p5}, Lorg/spongycastle/pqc/crypto/xmss/g;->e(Lorg/spongycastle/pqc/crypto/xmss/f;)Lorg/spongycastle/pqc/crypto/xmss/j;

    move-result-object p3

    .line 15
    invoke-static {p2, p3, v0}, Lorg/spongycastle/pqc/crypto/xmss/o;->a(Lorg/spongycastle/pqc/crypto/xmss/g;Lorg/spongycastle/pqc/crypto/xmss/j;Lorg/spongycastle/pqc/crypto/xmss/e;)Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    move-result-object p3

    .line 16
    :goto_0
    invoke-virtual {p1}, Ljava/util/AbstractCollection;->isEmpty()Z

    move-result p4

    const/4 p5, 0x1

    if-nez p4, :cond_0

    invoke-virtual {p1}, Ljava/util/Stack;->peek()Ljava/lang/Object;

    move-result-object p4

    check-cast p4, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    invoke-virtual {p4}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;->getHeight()I

    move-result p4

    invoke-virtual {p3}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;->getHeight()I

    move-result v0

    if-ne p4, v0, :cond_0

    .line 17
    invoke-virtual {p1}, Ljava/util/Stack;->peek()Ljava/lang/Object;

    move-result-object p4

    check-cast p4, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    invoke-virtual {p4}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;->getHeight()I

    move-result p4

    iget v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->initialHeight:I

    if-eq p4, v0, :cond_0

    .line 18
    new-instance p4, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    invoke-direct {p4}, Lorg/spongycastle/pqc/crypto/xmss/c$b;-><init>()V

    .line 19
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/k;->b()I

    move-result v0

    invoke-virtual {p4, v0}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->g(I)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object p4

    check-cast p4, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    .line 20
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/k;->c()J

    move-result-wide v2

    invoke-virtual {p4, v2, v3}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->h(J)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object p4

    check-cast p4, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    .line 21
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/c;->e()I

    move-result v0

    invoke-virtual {p4, v0}, Lorg/spongycastle/pqc/crypto/xmss/c$b;->m(I)Lorg/spongycastle/pqc/crypto/xmss/c$b;

    move-result-object p4

    .line 22
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/c;->f()I

    move-result v0

    sub-int/2addr v0, p5

    div-int/lit8 v0, v0, 0x2

    invoke-virtual {p4, v0}, Lorg/spongycastle/pqc/crypto/xmss/c$b;->n(I)Lorg/spongycastle/pqc/crypto/xmss/c$b;

    move-result-object p4

    .line 23
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/k;->a()I

    move-result v0

    invoke-virtual {p4, v0}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->f(I)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object p4

    check-cast p4, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    invoke-virtual {p4}, Lorg/spongycastle/pqc/crypto/xmss/c$b;->k()Lorg/spongycastle/pqc/crypto/xmss/k;

    move-result-object p4

    check-cast p4, Lorg/spongycastle/pqc/crypto/xmss/c;

    .line 24
    invoke-virtual {p1}, Ljava/util/Stack;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    invoke-static {p2, v0, p3, p4}, Lorg/spongycastle/pqc/crypto/xmss/o;->b(Lorg/spongycastle/pqc/crypto/xmss/g;Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;Lorg/spongycastle/pqc/crypto/xmss/k;)Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    move-result-object p3

    .line 25
    new-instance v0, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    invoke-virtual {p3}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;->getHeight()I

    move-result v1

    add-int/2addr v1, p5

    invoke-virtual {p3}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;->getValue()[B

    move-result-object p3

    invoke-direct {v0, v1, p3}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;-><init>(I[B)V

    .line 26
    new-instance p3, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    invoke-direct {p3}, Lorg/spongycastle/pqc/crypto/xmss/c$b;-><init>()V

    .line 27
    invoke-virtual {p4}, Lorg/spongycastle/pqc/crypto/xmss/k;->b()I

    move-result v1

    invoke-virtual {p3, v1}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->g(I)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object p3

    check-cast p3, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    .line 28
    invoke-virtual {p4}, Lorg/spongycastle/pqc/crypto/xmss/k;->c()J

    move-result-wide v1

    invoke-virtual {p3, v1, v2}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->h(J)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object p3

    check-cast p3, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    .line 29
    invoke-virtual {p4}, Lorg/spongycastle/pqc/crypto/xmss/c;->e()I

    move-result v1

    add-int/2addr v1, p5

    invoke-virtual {p3, v1}, Lorg/spongycastle/pqc/crypto/xmss/c$b;->m(I)Lorg/spongycastle/pqc/crypto/xmss/c$b;

    move-result-object p3

    .line 30
    invoke-virtual {p4}, Lorg/spongycastle/pqc/crypto/xmss/c;->f()I

    move-result p5

    invoke-virtual {p3, p5}, Lorg/spongycastle/pqc/crypto/xmss/c$b;->n(I)Lorg/spongycastle/pqc/crypto/xmss/c$b;

    move-result-object p3

    invoke-virtual {p4}, Lorg/spongycastle/pqc/crypto/xmss/k;->a()I

    move-result p4

    invoke-virtual {p3, p4}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->f(I)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object p3

    check-cast p3, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    .line 31
    invoke-virtual {p3}, Lorg/spongycastle/pqc/crypto/xmss/c$b;->k()Lorg/spongycastle/pqc/crypto/xmss/k;

    move-result-object p3

    move-object v1, p3

    check-cast v1, Lorg/spongycastle/pqc/crypto/xmss/c;

    move-object p3, v0

    goto/16 :goto_0

    .line 32
    :cond_0
    iget-object p4, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->tailNode:Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    if-nez p4, :cond_1

    .line 33
    iput-object p3, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->tailNode:Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    goto/16 :goto_1

    .line 34
    :cond_1
    invoke-virtual {p4}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;->getHeight()I

    move-result p4

    invoke-virtual {p3}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;->getHeight()I

    move-result v0

    if-ne p4, v0, :cond_2

    .line 35
    new-instance p1, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    invoke-direct {p1}, Lorg/spongycastle/pqc/crypto/xmss/c$b;-><init>()V

    .line 36
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/k;->b()I

    move-result p4

    invoke-virtual {p1, p4}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->g(I)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object p1

    check-cast p1, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    .line 37
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/k;->c()J

    move-result-wide v2

    invoke-virtual {p1, v2, v3}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->h(J)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object p1

    check-cast p1, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    .line 38
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/c;->e()I

    move-result p4

    invoke-virtual {p1, p4}, Lorg/spongycastle/pqc/crypto/xmss/c$b;->m(I)Lorg/spongycastle/pqc/crypto/xmss/c$b;

    move-result-object p1

    .line 39
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/c;->f()I

    move-result p4

    sub-int/2addr p4, p5

    div-int/lit8 p4, p4, 0x2

    invoke-virtual {p1, p4}, Lorg/spongycastle/pqc/crypto/xmss/c$b;->n(I)Lorg/spongycastle/pqc/crypto/xmss/c$b;

    move-result-object p1

    .line 40
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/k;->a()I

    move-result p4

    invoke-virtual {p1, p4}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->f(I)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object p1

    check-cast p1, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/c$b;->k()Lorg/spongycastle/pqc/crypto/xmss/k;

    move-result-object p1

    check-cast p1, Lorg/spongycastle/pqc/crypto/xmss/c;

    .line 41
    iget-object p4, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->tailNode:Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    invoke-static {p2, p4, p3, p1}, Lorg/spongycastle/pqc/crypto/xmss/o;->b(Lorg/spongycastle/pqc/crypto/xmss/g;Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;Lorg/spongycastle/pqc/crypto/xmss/k;)Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    move-result-object p2

    .line 42
    new-instance p3, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    iget-object p4, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->tailNode:Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    invoke-virtual {p4}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;->getHeight()I

    move-result p4

    add-int/2addr p4, p5

    invoke-virtual {p2}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;->getValue()[B

    move-result-object p2

    invoke-direct {p3, p4, p2}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;-><init>(I[B)V

    .line 43
    iput-object p3, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->tailNode:Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    .line 44
    new-instance p2, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    invoke-direct {p2}, Lorg/spongycastle/pqc/crypto/xmss/c$b;-><init>()V

    .line 45
    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/k;->b()I

    move-result p4

    invoke-virtual {p2, p4}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->g(I)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object p2

    check-cast p2, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    .line 46
    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/k;->c()J

    move-result-wide v0

    invoke-virtual {p2, v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->h(J)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object p2

    check-cast p2, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    .line 47
    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/c;->e()I

    move-result p4

    add-int/2addr p4, p5

    invoke-virtual {p2, p4}, Lorg/spongycastle/pqc/crypto/xmss/c$b;->m(I)Lorg/spongycastle/pqc/crypto/xmss/c$b;

    move-result-object p2

    .line 48
    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/c;->f()I

    move-result p4

    invoke-virtual {p2, p4}, Lorg/spongycastle/pqc/crypto/xmss/c$b;->n(I)Lorg/spongycastle/pqc/crypto/xmss/c$b;

    move-result-object p2

    .line 49
    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/k;->a()I

    move-result p1

    invoke-virtual {p2, p1}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->f(I)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    move-result-object p1

    check-cast p1, Lorg/spongycastle/pqc/crypto/xmss/c$b;

    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/c$b;->k()Lorg/spongycastle/pqc/crypto/xmss/k;

    move-result-object p1

    check-cast p1, Lorg/spongycastle/pqc/crypto/xmss/c;

    goto :goto_1

    .line 50
    :cond_2
    invoke-virtual {p1, p3}, Ljava/util/Stack;->push(Ljava/lang/Object;)Ljava/lang/Object;

    .line 51
    :goto_1
    iget-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->tailNode:Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;

    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;->getHeight()I

    move-result p1

    iget p2, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->initialHeight:I

    if-ne p1, p2, :cond_3

    .line 52
    iput-boolean p5, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->finished:Z

    return-void

    .line 53
    :cond_3
    invoke-virtual {p3}, Lorg/spongycastle/pqc/crypto/xmss/XMSSNode;->getHeight()I

    move-result p1

    iput p1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->height:I

    .line 54
    iget p1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->nextIndex:I

    add-int/2addr p1, p5

    iput p1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSTreeHash;->nextIndex:I

    return-void

    .line 55
    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "finished or not initialized"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 56
    :cond_5
    new-instance p1, Ljava/lang/NullPointerException;

    const-string p2, "otsHashAddress == null"

    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
