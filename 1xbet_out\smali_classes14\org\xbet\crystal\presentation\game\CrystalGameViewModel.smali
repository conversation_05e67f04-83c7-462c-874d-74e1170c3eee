.class public final Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008 \n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0018\u00002\u00020\u0001:\u0001SBk\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0017\u0010\u001f\u001a\u00020\u001e2\u0006\u0010\u001d\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 J\u000f\u0010!\u001a\u00020\u001eH\u0002\u00a2\u0006\u0004\u0008!\u0010\"J\u0017\u0010%\u001a\u00020\u001e2\u0006\u0010$\u001a\u00020#H\u0002\u00a2\u0006\u0004\u0008%\u0010&J\u0015\u0010)\u001a\u0008\u0012\u0004\u0012\u00020(0\'H\u0000\u00a2\u0006\u0004\u0008)\u0010*J\u000f\u0010+\u001a\u00020\u001eH\u0000\u00a2\u0006\u0004\u0008+\u0010\"J\u000f\u0010,\u001a\u00020\u001eH\u0000\u00a2\u0006\u0004\u0008,\u0010\"J\u000f\u0010-\u001a\u00020\u001eH\u0002\u00a2\u0006\u0004\u0008-\u0010\"J\u000f\u0010.\u001a\u00020\u001eH\u0002\u00a2\u0006\u0004\u0008.\u0010\"J\u0013\u0010/\u001a\u00020\u001e*\u00020(H\u0002\u00a2\u0006\u0004\u0008/\u00100R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00081\u00102R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u00104R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00085\u00106R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00087\u00108R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u0010:R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008;\u0010<R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u0010>R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008?\u0010@R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008A\u0010BR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008C\u0010DR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008E\u0010FR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010HR\u0018\u0010L\u001a\u0004\u0018\u00010I8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR\u0018\u0010N\u001a\u0004\u0018\u00010I8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008M\u0010KR\u001a\u0010R\u001a\u0008\u0012\u0004\u0012\u00020(0O8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010Q\u00a8\u0006T"
    }
    d2 = {
        "Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lay/e;",
        "makeBetGameUseCase",
        "Lay/f;",
        "restoreGameFieldUseCase",
        "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
        "startGameIfPossibleScenario",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "addCommandScenario",
        "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
        "getCurrencyUseCase",
        "LWv/b;",
        "getConnectionStatusUseCase",
        "Lorg/xbet/core/domain/usecases/u;",
        "observeCommandUseCase",
        "Lorg/xbet/core/domain/usecases/game_info/q;",
        "getGameStateUseCase",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lay/a;",
        "clearGameResultUseCase",
        "LwX0/c;",
        "router",
        "Lorg/xbet/core/domain/usecases/d;",
        "choiceErrorActionScenario",
        "<init>",
        "(Lay/e;Lay/f;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;LWv/b;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/game_info/q;Lm8/a;Lay/a;LwX0/c;Lorg/xbet/core/domain/usecases/d;)V",
        "LTv/d;",
        "command",
        "",
        "D3",
        "(LTv/d;)V",
        "I3",
        "()V",
        "",
        "throwable",
        "E3",
        "(Ljava/lang/Throwable;)V",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;",
        "C3",
        "()Lkotlinx/coroutines/flow/e;",
        "G3",
        "F3",
        "A3",
        "H3",
        "J3",
        "(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;)V",
        "v1",
        "Lay/e;",
        "x1",
        "Lay/f;",
        "y1",
        "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
        "F1",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "H1",
        "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
        "I1",
        "LWv/b;",
        "P1",
        "Lorg/xbet/core/domain/usecases/u;",
        "S1",
        "Lorg/xbet/core/domain/usecases/game_info/q;",
        "V1",
        "Lm8/a;",
        "b2",
        "Lay/a;",
        "v2",
        "LwX0/c;",
        "x2",
        "Lorg/xbet/core/domain/usecases/d;",
        "Lkotlinx/coroutines/x0;",
        "y2",
        "Lkotlinx/coroutines/x0;",
        "makeBetJob",
        "F2",
        "playIfPossibleJob",
        "Lkotlinx/coroutines/channels/g;",
        "H2",
        "Lkotlinx/coroutines/channels/g;",
        "viewActions",
        "a",
        "crystal_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final F1:Lorg/xbet/core/domain/usecases/AddCommandScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public F2:Lkotlinx/coroutines/x0;

.field public final H1:Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lkotlinx/coroutines/channels/g;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/channels/g<",
            "Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:LWv/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lorg/xbet/core/domain/usecases/u;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/core/domain/usecases/game_info/q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:Lay/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lay/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Lay/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lorg/xbet/core/domain/usecases/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public y2:Lkotlinx/coroutines/x0;


# direct methods
.method public constructor <init>(Lay/e;Lay/f;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;LWv/b;Lorg/xbet/core/domain/usecases/u;Lorg/xbet/core/domain/usecases/game_info/q;Lm8/a;Lay/a;LwX0/c;Lorg/xbet/core/domain/usecases/d;)V
    .locals 0
    .param p1    # Lay/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lay/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/core/domain/usecases/AddCommandScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LWv/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/core/domain/usecases/u;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/core/domain/usecases/game_info/q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lay/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lorg/xbet/core/domain/usecases/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->v1:Lay/e;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->x1:Lay/f;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->y1:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->F1:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->H1:Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->I1:LWv/b;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->P1:Lorg/xbet/core/domain/usecases/u;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->S1:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->V1:Lm8/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->b2:Lay/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->v2:LwX0/c;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->x2:Lorg/xbet/core/domain/usecases/d;

    .line 27
    .line 28
    const/4 p1, 0x0

    .line 29
    const/4 p2, 0x7

    .line 30
    const/4 p3, 0x0

    .line 31
    invoke-static {p3, p1, p1, p2, p1}, Lkotlinx/coroutines/channels/i;->b(ILkotlinx/coroutines/channels/BufferOverflow;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lkotlinx/coroutines/channels/g;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    iput-object p1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->H2:Lkotlinx/coroutines/channels/g;

    .line 36
    .line 37
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->A3()V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public static final synthetic B3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->D3(LTv/d;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final D3(LTv/d;)V
    .locals 1

    .line 1
    instance-of v0, p1, LTv/a$d;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->I1:LWv/b;

    .line 6
    .line 7
    invoke-virtual {p1}, LWv/b;->a()Z

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    if-nez p1, :cond_0

    .line 12
    .line 13
    goto :goto_0

    .line 14
    :cond_0
    invoke-direct {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->I3()V

    .line 15
    .line 16
    .line 17
    return-void

    .line 18
    :cond_1
    instance-of v0, p1, LTv/a$w;

    .line 19
    .line 20
    if-eqz v0, :cond_2

    .line 21
    .line 22
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->H3()V

    .line 23
    .line 24
    .line 25
    return-void

    .line 26
    :cond_2
    instance-of v0, p1, LTv/a$p;

    .line 27
    .line 28
    if-eqz v0, :cond_3

    .line 29
    .line 30
    sget-object p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$a;->a:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$a;

    .line 31
    .line 32
    invoke-virtual {p0, p1}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->J3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;)V

    .line 33
    .line 34
    .line 35
    return-void

    .line 36
    :cond_3
    instance-of p1, p1, LTv/a$r;

    .line 37
    .line 38
    if-eqz p1, :cond_4

    .line 39
    .line 40
    iget-object p1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->b2:Lay/a;

    .line 41
    .line 42
    invoke-virtual {p1}, Lay/a;->a()V

    .line 43
    .line 44
    .line 45
    sget-object p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$b;->a:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$b;

    .line 46
    .line 47
    invoke-virtual {p0, p1}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->J3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;)V

    .line 48
    .line 49
    .line 50
    :cond_4
    :goto_0
    return-void
.end method

.method private final E3(Ljava/lang/Throwable;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->V1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    sget-object v1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$handleGameError$1;->INSTANCE:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$handleGameError$1;

    .line 12
    .line 13
    new-instance v5, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$handleGameError$2;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$handleGameError$2;-><init>(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/16 v6, 0xa

    .line 20
    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method private final I3()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->F2:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    new-instance v3, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playIfPossible$1;

    .line 18
    .line 19
    invoke-direct {v3, p0}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playIfPossible$1;-><init>(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->V1:Lm8/a;

    .line 23
    .line 24
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 25
    .line 26
    .line 27
    move-result-object v5

    .line 28
    new-instance v7, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playIfPossible$2;

    .line 29
    .line 30
    const/4 v0, 0x0

    .line 31
    invoke-direct {v7, p0, v0}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playIfPossible$2;-><init>(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/16 v8, 0xa

    .line 35
    .line 36
    const/4 v9, 0x0

    .line 37
    const/4 v4, 0x0

    .line 38
    const/4 v6, 0x0

    .line 39
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    iput-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->F2:Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    return-void
.end method

.method public static final K3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic p3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->K3(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic q3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->B3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic r3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;)Lorg/xbet/core/domain/usecases/AddCommandScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->F1:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;)Lorg/xbet/core/domain/usecases/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->x2:Lorg/xbet/core/domain/usecases/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;)Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->H1:Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;)Lay/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->v1:Lay/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;)Lay/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->x1:Lay/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;)Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->y1:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;)Lkotlinx/coroutines/channels/g;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->H2:Lkotlinx/coroutines/channels/g;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->E3(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic z3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->J3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final A3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->P1:Lorg/xbet/core/domain/usecases/u;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/u;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$attachToCommands$1;

    .line 8
    .line 9
    invoke-direct {v1, p0}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$attachToCommands$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    new-instance v1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$attachToCommands$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v1, p0, v2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$attachToCommands$2;-><init>(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final C3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->H2:Lkotlinx/coroutines/channels/g;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->m0(Lkotlinx/coroutines/channels/ReceiveChannel;)Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final F3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->V1:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;-><init>(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final G3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->x1:Lay/f;

    .line 2
    .line 3
    invoke-virtual {v0}, Lay/f;->a()LZx/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, LZx/b;->h()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-nez v1, :cond_0

    .line 12
    .line 13
    new-instance v1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$c;

    .line 14
    .line 15
    iget-object v2, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->S1:Lorg/xbet/core/domain/usecases/game_info/q;

    .line 16
    .line 17
    invoke-virtual {v2}, Lorg/xbet/core/domain/usecases/game_info/q;->a()Lorg/xbet/core/domain/GameState;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    invoke-virtual {v2}, Lorg/xbet/core/domain/GameState;->gameIsInProcess()Z

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    invoke-direct {v1, v0, v2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$c;-><init>(LZx/b;Z)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0, v1}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->J3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;)V

    .line 29
    .line 30
    .line 31
    :cond_0
    return-void
.end method

.method public final H3()V
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->y2:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    sget-object v0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$b;->a:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$b;

    .line 14
    .line 15
    invoke-virtual {p0, v0}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->J3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;)V

    .line 16
    .line 17
    .line 18
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->V1:Lm8/a;

    .line 23
    .line 24
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 25
    .line 26
    .line 27
    move-result-object v4

    .line 28
    new-instance v2, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$1;

    .line 29
    .line 30
    invoke-direct {v2, p0}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$1;-><init>(Ljava/lang/Object;)V

    .line 31
    .line 32
    .line 33
    new-instance v6, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;

    .line 34
    .line 35
    const/4 v0, 0x0

    .line 36
    invoke-direct {v6, p0, v0}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$playGame$2;-><init>(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;Lkotlin/coroutines/e;)V

    .line 37
    .line 38
    .line 39
    const/16 v7, 0xa

    .line 40
    .line 41
    const/4 v8, 0x0

    .line 42
    const/4 v3, 0x0

    .line 43
    const/4 v5, 0x0

    .line 44
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->y2:Lkotlinx/coroutines/x0;

    .line 49
    .line 50
    return-void
.end method

.method public final J3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/crystal/presentation/game/d;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/crystal/presentation/game/d;-><init>()V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$send$2;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$send$2;-><init>(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method
