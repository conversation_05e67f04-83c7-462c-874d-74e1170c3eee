.class public final synthetic Leb1/p0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LUX0/k;

.field public final synthetic b:LB4/a;


# direct methods
.method public synthetic constructor <init>(LUX0/k;LB4/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Leb1/p0;->a:LUX0/k;

    iput-object p2, p0, Leb1/p0;->b:LB4/a;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Leb1/p0;->a:LUX0/k;

    iget-object v1, p0, Leb1/p0;->b:LB4/a;

    invoke-static {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentTopGamesDelegateKt;->a(LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
