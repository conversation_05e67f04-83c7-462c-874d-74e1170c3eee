.class public final synthetic Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/n;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/n;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/n;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;

    invoke-static {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->m3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)Landroidx/lifecycle/e0$c;

    move-result-object v0

    return-object v0
.end method
