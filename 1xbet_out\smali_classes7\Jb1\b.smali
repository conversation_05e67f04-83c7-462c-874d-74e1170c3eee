.class public final LJb1/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LJb1/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u009c\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u00081\u0008\u0000\u0018\u00002\u00020\u0001B\u00c1\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u0012\u0006\u0010/\u001a\u00020.\u00a2\u0006\u0004\u00080\u00101J\u0010\u00103\u001a\u000202H\u0096\u0001\u00a2\u0006\u0004\u00083\u00104R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00085\u00106R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00087\u00108R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u0010:R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008;\u0010<R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u0010>R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008?\u0010@R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008A\u0010BR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008C\u0010DR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008E\u0010FR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010HR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008I\u0010JR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008K\u0010LR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008M\u0010NR\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008O\u0010PR\u0014\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Q\u0010RR\u0014\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008S\u0010TR\u0014\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008U\u0010VR\u0014\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008W\u0010XR\u0014\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Y\u0010ZR\u0014\u0010)\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008[\u0010\\R\u0014\u0010+\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008]\u0010^R\u0014\u0010-\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008_\u0010`R\u0014\u0010/\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008a\u0010b\u00a8\u0006c"
    }
    d2 = {
        "LJb1/b;",
        "LJb1/a;",
        "LJb1/e;",
        "aggregatorPopularComponentFactory",
        "LwX0/C;",
        "rootRouterHolder",
        "LfX/b;",
        "testRepository",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
        "popularClassicAggregatorDelegate",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lf8/g;",
        "serviceGenerator",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "Lcom/xbet/onexcore/utils/ext/c;",
        "iNetworkConnectionUtil",
        "Lcom/xbet/onexuser/domain/user/c;",
        "userInteractor",
        "Lc8/h;",
        "requestParamsDataSource",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "tokenRefresher",
        "Lak/b;",
        "changeBalanceFeature",
        "Li8/j;",
        "getServiceUseCase",
        "LHX0/e;",
        "resourceManager",
        "Lak/a;",
        "balanceFeature",
        "LS8/a;",
        "profileLocalDataSource",
        "Lc81/a;",
        "aggregatorCoreFeature",
        "LTZ0/a;",
        "actionDialogManager",
        "LVg0/a;",
        "promotionsNewsScreenFactory",
        "LzX0/k;",
        "snackbarManager",
        "Lau/a;",
        "countryInfoRepository",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "<init>",
        "(LJb1/e;LwX0/C;LfX/b;Lorg/xbet/ui_common/utils/M;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lm8/a;Lf8/g;Lorg/xbet/remoteconfig/domain/usecases/i;Lcom/xbet/onexcore/utils/ext/c;Lcom/xbet/onexuser/domain/user/c;Lc8/h;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lak/b;Li8/j;LHX0/e;Lak/a;LS8/a;Lc81/a;LTZ0/a;LVg0/a;LzX0/k;Lau/a;Lp9/c;)V",
        "Lzb1/b;",
        "a",
        "()Lzb1/b;",
        "b",
        "LJb1/e;",
        "c",
        "LwX0/C;",
        "d",
        "LfX/b;",
        "e",
        "Lorg/xbet/ui_common/utils/M;",
        "f",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
        "g",
        "Lm8/a;",
        "h",
        "Lf8/g;",
        "i",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "j",
        "Lcom/xbet/onexcore/utils/ext/c;",
        "k",
        "Lcom/xbet/onexuser/domain/user/c;",
        "l",
        "Lc8/h;",
        "m",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "n",
        "Lak/b;",
        "o",
        "Li8/j;",
        "p",
        "LHX0/e;",
        "q",
        "Lak/a;",
        "r",
        "LS8/a;",
        "s",
        "Lc81/a;",
        "t",
        "LTZ0/a;",
        "u",
        "LVg0/a;",
        "v",
        "LzX0/k;",
        "w",
        "Lau/a;",
        "x",
        "Lp9/c;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LJb1/a;

.field public final b:LJb1/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:LfX/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lcom/xbet/onexcore/utils/ext/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lcom/xbet/onexuser/domain/user/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:Lak/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:Li8/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q:Lak/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final r:LS8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final s:Lc81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final t:LTZ0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final u:LVg0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v:LzX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w:Lau/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LJb1/e;LwX0/C;LfX/b;Lorg/xbet/ui_common/utils/M;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lm8/a;Lf8/g;Lorg/xbet/remoteconfig/domain/usecases/i;Lcom/xbet/onexcore/utils/ext/c;Lcom/xbet/onexuser/domain/user/c;Lc8/h;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lak/b;Li8/j;LHX0/e;Lak/a;LS8/a;Lc81/a;LTZ0/a;LVg0/a;LzX0/k;Lau/a;Lp9/c;)V
    .locals 24
    .param p1    # LJb1/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LfX/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lcom/xbet/onexcore/utils/ext/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lcom/xbet/onexuser/domain/user/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lcom/xbet/onexuser/domain/managers/TokenRefresher;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lak/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lak/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # LS8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lc81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # LTZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # LVg0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # LzX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # Lau/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct/range {p0 .. p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    move-object/from16 v1, p1

    .line 5
    .line 6
    move-object/from16 v2, p2

    .line 7
    .line 8
    move-object/from16 v3, p3

    .line 9
    .line 10
    move-object/from16 v4, p4

    .line 11
    .line 12
    move-object/from16 v5, p5

    .line 13
    .line 14
    move-object/from16 v6, p6

    .line 15
    .line 16
    move-object/from16 v7, p7

    .line 17
    .line 18
    move-object/from16 v8, p8

    .line 19
    .line 20
    move-object/from16 v9, p9

    .line 21
    .line 22
    move-object/from16 v11, p10

    .line 23
    .line 24
    move-object/from16 v12, p11

    .line 25
    .line 26
    move-object/from16 v13, p12

    .line 27
    .line 28
    move-object/from16 v19, p13

    .line 29
    .line 30
    move-object/from16 v14, p14

    .line 31
    .line 32
    move-object/from16 v15, p15

    .line 33
    .line 34
    move-object/from16 v10, p16

    .line 35
    .line 36
    move-object/from16 v17, p17

    .line 37
    .line 38
    move-object/from16 v18, p18

    .line 39
    .line 40
    move-object/from16 v16, p19

    .line 41
    .line 42
    move-object/from16 v20, p20

    .line 43
    .line 44
    move-object/from16 v21, p21

    .line 45
    .line 46
    move-object/from16 v22, p22

    .line 47
    .line 48
    move-object/from16 v23, p23

    .line 49
    .line 50
    invoke-virtual/range {v1 .. v23}, LJb1/e;->a(LwX0/C;LfX/b;Lorg/xbet/ui_common/utils/M;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lm8/a;Lf8/g;Lorg/xbet/remoteconfig/domain/usecases/i;Lcom/xbet/onexcore/utils/ext/c;Lak/a;Lcom/xbet/onexuser/domain/user/c;Lc8/h;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Li8/j;LHX0/e;LTZ0/a;LS8/a;Lc81/a;Lak/b;LVg0/a;LzX0/k;Lau/a;Lp9/c;)LJb1/a;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    move-object/from16 v1, p0

    .line 55
    .line 56
    iput-object v0, v1, LJb1/b;->a:LJb1/a;

    .line 57
    .line 58
    move-object/from16 v0, p1

    .line 59
    .line 60
    iput-object v0, v1, LJb1/b;->b:LJb1/e;

    .line 61
    .line 62
    iput-object v2, v1, LJb1/b;->c:LwX0/C;

    .line 63
    .line 64
    iput-object v3, v1, LJb1/b;->d:LfX/b;

    .line 65
    .line 66
    iput-object v4, v1, LJb1/b;->e:Lorg/xbet/ui_common/utils/M;

    .line 67
    .line 68
    iput-object v5, v1, LJb1/b;->f:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 69
    .line 70
    iput-object v6, v1, LJb1/b;->g:Lm8/a;

    .line 71
    .line 72
    iput-object v7, v1, LJb1/b;->h:Lf8/g;

    .line 73
    .line 74
    iput-object v8, v1, LJb1/b;->i:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 75
    .line 76
    iput-object v9, v1, LJb1/b;->j:Lcom/xbet/onexcore/utils/ext/c;

    .line 77
    .line 78
    iput-object v11, v1, LJb1/b;->k:Lcom/xbet/onexuser/domain/user/c;

    .line 79
    .line 80
    iput-object v12, v1, LJb1/b;->l:Lc8/h;

    .line 81
    .line 82
    iput-object v13, v1, LJb1/b;->m:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 83
    .line 84
    move-object/from16 v0, p13

    .line 85
    .line 86
    iput-object v0, v1, LJb1/b;->n:Lak/b;

    .line 87
    .line 88
    iput-object v14, v1, LJb1/b;->o:Li8/j;

    .line 89
    .line 90
    iput-object v15, v1, LJb1/b;->p:LHX0/e;

    .line 91
    .line 92
    iput-object v10, v1, LJb1/b;->q:Lak/a;

    .line 93
    .line 94
    move-object/from16 v0, p17

    .line 95
    .line 96
    iput-object v0, v1, LJb1/b;->r:LS8/a;

    .line 97
    .line 98
    move-object/from16 v0, p18

    .line 99
    .line 100
    iput-object v0, v1, LJb1/b;->s:Lc81/a;

    .line 101
    .line 102
    move-object/from16 v0, p19

    .line 103
    .line 104
    iput-object v0, v1, LJb1/b;->t:LTZ0/a;

    .line 105
    .line 106
    move-object/from16 v0, p20

    .line 107
    .line 108
    iput-object v0, v1, LJb1/b;->u:LVg0/a;

    .line 109
    .line 110
    move-object/from16 v0, p21

    .line 111
    .line 112
    iput-object v0, v1, LJb1/b;->v:LzX0/k;

    .line 113
    .line 114
    move-object/from16 v0, p22

    .line 115
    .line 116
    iput-object v0, v1, LJb1/b;->w:Lau/a;

    .line 117
    .line 118
    move-object/from16 v0, p23

    .line 119
    .line 120
    iput-object v0, v1, LJb1/b;->x:Lp9/c;

    .line 121
    .line 122
    return-void
.end method


# virtual methods
.method public a()Lzb1/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LJb1/b;->a:LJb1/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lzb1/a;->a()Lzb1/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method
