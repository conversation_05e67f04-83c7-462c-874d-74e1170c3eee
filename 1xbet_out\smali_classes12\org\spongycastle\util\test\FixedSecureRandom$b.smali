.class public Lorg/spongycastle/util/test/FixedSecureRandom$b;
.super Lorg/spongycastle/util/test/FixedSecureRandom$c;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/spongycastle/util/test/FixedSecureRandom;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "b"
.end annotation


# direct methods
.method public constructor <init>([B)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/spongycastle/util/test/FixedSecureRandom$c;-><init>([B)V

    .line 2
    .line 3
    .line 4
    return-void
.end method
