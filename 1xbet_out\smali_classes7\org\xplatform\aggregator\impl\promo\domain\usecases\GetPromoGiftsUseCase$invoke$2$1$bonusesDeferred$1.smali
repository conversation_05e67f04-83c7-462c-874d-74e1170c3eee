.class final Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.promo.domain.usecases.GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1"
    f = "GetPromoGiftsUseCase.kt"
    l = {
        0x1b,
        0x57
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lg81/e;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "Lg81/e;",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)Lg81/e;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $accountId:J

.field final synthetic $token:Ljava/lang/String;

.field I$0:I

.field I$1:I

.field J$0:J

.field private synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;Ljava/lang/String;JLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;",
            "Ljava/lang/String;",
            "J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->$token:Ljava/lang/String;

    iput-wide p3, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->$accountId:J

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->$token:Ljava/lang/String;

    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->$accountId:J

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;-><init>(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;Ljava/lang/String;JLkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lg81/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 13

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x0

    .line 9
    const/4 v4, 0x1

    .line 10
    if-eqz v1, :cond_2

    .line 11
    .line 12
    if-eq v1, v4, :cond_1

    .line 13
    .line 14
    if-ne v1, v2, :cond_0

    .line 15
    .line 16
    iget v1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->I$1:I

    .line 17
    .line 18
    iget-wide v5, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->J$0:J

    .line 19
    .line 20
    iget v7, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->I$0:I

    .line 21
    .line 22
    iget-object v8, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->L$2:Ljava/lang/Object;

    .line 23
    .line 24
    check-cast v8, Ljava/lang/String;

    .line 25
    .line 26
    iget-object v9, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->L$1:Ljava/lang/Object;

    .line 27
    .line 28
    check-cast v9, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    .line 29
    .line 30
    iget-object v10, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->L$0:Ljava/lang/Object;

    .line 31
    .line 32
    check-cast v10, Lkotlinx/coroutines/N;

    .line 33
    .line 34
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 35
    .line 36
    .line 37
    goto :goto_0

    .line 38
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 39
    .line 40
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 41
    .line 42
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 43
    .line 44
    .line 45
    throw p1

    .line 46
    :cond_1
    iget v1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->I$1:I

    .line 47
    .line 48
    iget-wide v5, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->J$0:J

    .line 49
    .line 50
    iget v7, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->I$0:I

    .line 51
    .line 52
    iget-object v8, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->L$2:Ljava/lang/Object;

    .line 53
    .line 54
    check-cast v8, Ljava/lang/String;

    .line 55
    .line 56
    iget-object v9, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->L$1:Ljava/lang/Object;

    .line 57
    .line 58
    check-cast v9, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    .line 59
    .line 60
    iget-object v10, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->L$0:Ljava/lang/Object;

    .line 61
    .line 62
    check-cast v10, Lkotlinx/coroutines/N;

    .line 63
    .line 64
    :try_start_0
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 65
    .line 66
    .line 67
    goto :goto_1

    .line 68
    :catchall_0
    move-exception p1

    .line 69
    goto :goto_2

    .line 70
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 71
    .line 72
    .line 73
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->L$0:Ljava/lang/Object;

    .line 74
    .line 75
    check-cast p1, Lkotlinx/coroutines/N;

    .line 76
    .line 77
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    .line 78
    .line 79
    iget-object v5, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->$token:Ljava/lang/String;

    .line 80
    .line 81
    iget-wide v6, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->$accountId:J

    .line 82
    .line 83
    move-object v10, p1

    .line 84
    move-object v9, v1

    .line 85
    move-object v8, v5

    .line 86
    move-wide v5, v6

    .line 87
    const/4 v1, 0x0

    .line 88
    const/4 v7, 0x0

    .line 89
    :cond_3
    :goto_0
    :try_start_1
    sget-object p1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 90
    .line 91
    iput-object v10, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->L$0:Ljava/lang/Object;

    .line 92
    .line 93
    iput-object v9, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->L$1:Ljava/lang/Object;

    .line 94
    .line 95
    iput-object v8, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->L$2:Ljava/lang/Object;

    .line 96
    .line 97
    iput v7, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->I$0:I

    .line 98
    .line 99
    iput-wide v5, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->J$0:J

    .line 100
    .line 101
    iput v1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->I$1:I

    .line 102
    .line 103
    iput v4, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->label:I

    .line 104
    .line 105
    invoke-static {v9, v8, v5, v6, p0}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;->b(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 106
    .line 107
    .line 108
    move-result-object p1

    .line 109
    if-ne p1, v0, :cond_4

    .line 110
    .line 111
    goto/16 :goto_6

    .line 112
    .line 113
    :cond_4
    :goto_1
    check-cast p1, Lg81/e;

    .line 114
    .line 115
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 116
    .line 117
    .line 118
    move-result-object p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 119
    goto/16 :goto_8

    .line 120
    .line 121
    :goto_2
    if-eqz v7, :cond_5

    .line 122
    .line 123
    instance-of v11, p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 124
    .line 125
    if-eqz v11, :cond_5

    .line 126
    .line 127
    move-object v11, p1

    .line 128
    check-cast v11, Lcom/xbet/onexcore/data/model/ServerException;

    .line 129
    .line 130
    invoke-virtual {v11}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 131
    .line 132
    .line 133
    move-result v11

    .line 134
    if-eqz v11, :cond_5

    .line 135
    .line 136
    const/4 v11, 0x1

    .line 137
    goto :goto_3

    .line 138
    :cond_5
    const/4 v11, 0x0

    .line 139
    :goto_3
    instance-of v12, p1, Ljava/util/concurrent/CancellationException;

    .line 140
    .line 141
    if-nez v12, :cond_b

    .line 142
    .line 143
    instance-of v12, p1, Ljava/net/ConnectException;

    .line 144
    .line 145
    if-nez v12, :cond_b

    .line 146
    .line 147
    if-nez v11, :cond_b

    .line 148
    .line 149
    instance-of v11, p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 150
    .line 151
    if-eqz v11, :cond_8

    .line 152
    .line 153
    move-object v11, p1

    .line 154
    check-cast v11, Lcom/xbet/onexcore/data/model/ServerException;

    .line 155
    .line 156
    invoke-virtual {v11}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 157
    .line 158
    .line 159
    move-result v12

    .line 160
    if-nez v12, :cond_7

    .line 161
    .line 162
    invoke-virtual {v11}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 163
    .line 164
    .line 165
    move-result v11

    .line 166
    if-eqz v11, :cond_6

    .line 167
    .line 168
    goto :goto_4

    .line 169
    :cond_6
    const/4 v11, 0x0

    .line 170
    goto :goto_5

    .line 171
    :cond_7
    :goto_4
    const/4 v11, 0x1

    .line 172
    goto :goto_5

    .line 173
    :cond_8
    invoke-static {p1}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 174
    .line 175
    .line 176
    move-result v11

    .line 177
    if-nez v11, :cond_6

    .line 178
    .line 179
    goto :goto_4

    .line 180
    :goto_5
    add-int/2addr v1, v4

    .line 181
    const/4 v12, 0x3

    .line 182
    if-gt v1, v12, :cond_a

    .line 183
    .line 184
    if-eqz v11, :cond_9

    .line 185
    .line 186
    goto :goto_7

    .line 187
    :cond_9
    new-instance v11, Ljava/lang/StringBuilder;

    .line 188
    .line 189
    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    .line 190
    .line 191
    .line 192
    const-string v12, "error ("

    .line 193
    .line 194
    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 195
    .line 196
    .line 197
    invoke-virtual {v11, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 198
    .line 199
    .line 200
    const-string v12, "): "

    .line 201
    .line 202
    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 203
    .line 204
    .line 205
    invoke-virtual {v11, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 206
    .line 207
    .line 208
    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 209
    .line 210
    .line 211
    move-result-object p1

    .line 212
    sget-object v11, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 213
    .line 214
    invoke-virtual {v11, p1}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 215
    .line 216
    .line 217
    iput-object v10, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->L$0:Ljava/lang/Object;

    .line 218
    .line 219
    iput-object v9, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->L$1:Ljava/lang/Object;

    .line 220
    .line 221
    iput-object v8, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->L$2:Ljava/lang/Object;

    .line 222
    .line 223
    iput v7, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->I$0:I

    .line 224
    .line 225
    iput-wide v5, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->J$0:J

    .line 226
    .line 227
    iput v1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->I$1:I

    .line 228
    .line 229
    iput v2, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;->label:I

    .line 230
    .line 231
    const-wide/16 v11, 0xbb8

    .line 232
    .line 233
    invoke-static {v11, v12, p0}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 234
    .line 235
    .line 236
    move-result-object p1

    .line 237
    if-ne p1, v0, :cond_3

    .line 238
    .line 239
    :goto_6
    return-object v0

    .line 240
    :cond_a
    :goto_7
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 241
    .line 242
    invoke-static {p1}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 243
    .line 244
    .line 245
    move-result-object p1

    .line 246
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 247
    .line 248
    .line 249
    move-result-object p1

    .line 250
    :goto_8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 251
    .line 252
    .line 253
    return-object p1

    .line 254
    :cond_b
    throw p1
.end method
