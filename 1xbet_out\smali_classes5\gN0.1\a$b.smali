.class public final LgN0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LgN0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LgN0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LgN0/a$b$a;
    }
.end annotation


# instance fields
.field public final a:LHX0/e;

.field public final b:LgN0/a$b;

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stat_results/impl/results/races/data/d;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stat_results/impl/results/races/data/b;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stat_results/impl/results/races/data/RacesResultsRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LhN0/c;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LhN0/e;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LhN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stat_results/impl/results/races/presentation/RacesResultsViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LwX0/c;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/M;LHX0/e;LSX0/a;Lorg/xbet/statistic/stat_results/impl/results/races/data/b;Lc8/h;Lorg/xbet/ui_common/utils/internet/a;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LgN0/a$b;->b:LgN0/a$b;

    .line 4
    iput-object p6, p0, LgN0/a$b;->a:LHX0/e;

    .line 5
    invoke-virtual/range {p0 .. p10}, LgN0/a$b;->b(LQW0/c;LwX0/c;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/M;LHX0/e;LSX0/a;Lorg/xbet/statistic/stat_results/impl/results/races/data/b;Lc8/h;Lorg/xbet/ui_common/utils/internet/a;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LwX0/c;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/M;LHX0/e;LSX0/a;Lorg/xbet/statistic/stat_results/impl/results/races/data/b;Lc8/h;Lorg/xbet/ui_common/utils/internet/a;LgN0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p10}, LgN0/a$b;-><init>(LQW0/c;LwX0/c;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/M;LHX0/e;LSX0/a;Lorg/xbet/statistic/stat_results/impl/results/races/data/b;Lc8/h;Lorg/xbet/ui_common/utils/internet/a;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/stat_results/impl/results/races/presentation/RacesResultsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LgN0/a$b;->c(Lorg/xbet/statistic/stat_results/impl/results/races/presentation/RacesResultsFragment;)Lorg/xbet/statistic/stat_results/impl/results/races/presentation/RacesResultsFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;LwX0/c;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/M;LHX0/e;LSX0/a;Lorg/xbet/statistic/stat_results/impl/results/races/data/b;Lc8/h;Lorg/xbet/ui_common/utils/internet/a;)V
    .locals 10

    .line 1
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p4

    .line 5
    iput-object p4, p0, LgN0/a$b;->c:Ldagger/internal/h;

    .line 6
    .line 7
    new-instance p4, LgN0/a$b$a;

    .line 8
    .line 9
    invoke-direct {p4, p1}, LgN0/a$b$a;-><init>(LQW0/c;)V

    .line 10
    .line 11
    .line 12
    iput-object p4, p0, LgN0/a$b;->d:Ldagger/internal/h;

    .line 13
    .line 14
    invoke-static {p2}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    iput-object p1, p0, LgN0/a$b;->e:Ldagger/internal/h;

    .line 19
    .line 20
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iput-object p1, p0, LgN0/a$b;->f:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iput-object p1, p0, LgN0/a$b;->g:Ldagger/internal/h;

    .line 31
    .line 32
    invoke-static/range {p7 .. p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iput-object p1, p0, LgN0/a$b;->h:Ldagger/internal/h;

    .line 37
    .line 38
    invoke-static/range {p6 .. p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    iput-object p1, p0, LgN0/a$b;->i:Ldagger/internal/h;

    .line 43
    .line 44
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput-object p1, p0, LgN0/a$b;->j:Ldagger/internal/h;

    .line 49
    .line 50
    invoke-static {p1}, Lorg/xbet/statistic/stat_results/impl/results/races/data/e;->a(LBc/a;)Lorg/xbet/statistic/stat_results/impl/results/races/data/e;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    iput-object p1, p0, LgN0/a$b;->k:Ldagger/internal/h;

    .line 55
    .line 56
    invoke-static/range {p8 .. p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    iput-object p1, p0, LgN0/a$b;->l:Ldagger/internal/h;

    .line 61
    .line 62
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    iput-object p1, p0, LgN0/a$b;->m:Ldagger/internal/h;

    .line 67
    .line 68
    iget-object p2, p0, LgN0/a$b;->k:Ldagger/internal/h;

    .line 69
    .line 70
    iget-object p3, p0, LgN0/a$b;->l:Ldagger/internal/h;

    .line 71
    .line 72
    invoke-static {p2, p3, p1}, Lorg/xbet/statistic/stat_results/impl/results/races/data/f;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/stat_results/impl/results/races/data/f;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    iput-object p1, p0, LgN0/a$b;->n:Ldagger/internal/h;

    .line 77
    .line 78
    invoke-static {p1}, LhN0/d;->a(LBc/a;)LhN0/d;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    iput-object p1, p0, LgN0/a$b;->o:Ldagger/internal/h;

    .line 83
    .line 84
    iget-object p1, p0, LgN0/a$b;->n:Ldagger/internal/h;

    .line 85
    .line 86
    invoke-static {p1}, LhN0/f;->a(LBc/a;)LhN0/f;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    iput-object p1, p0, LgN0/a$b;->p:Ldagger/internal/h;

    .line 91
    .line 92
    iget-object p1, p0, LgN0/a$b;->n:Ldagger/internal/h;

    .line 93
    .line 94
    invoke-static {p1}, LhN0/b;->a(LBc/a;)LhN0/b;

    .line 95
    .line 96
    .line 97
    move-result-object v9

    .line 98
    iput-object v9, p0, LgN0/a$b;->q:Ldagger/internal/h;

    .line 99
    .line 100
    iget-object v0, p0, LgN0/a$b;->c:Ldagger/internal/h;

    .line 101
    .line 102
    iget-object v1, p0, LgN0/a$b;->d:Ldagger/internal/h;

    .line 103
    .line 104
    iget-object v2, p0, LgN0/a$b;->e:Ldagger/internal/h;

    .line 105
    .line 106
    iget-object v3, p0, LgN0/a$b;->f:Ldagger/internal/h;

    .line 107
    .line 108
    iget-object v4, p0, LgN0/a$b;->g:Ldagger/internal/h;

    .line 109
    .line 110
    iget-object v5, p0, LgN0/a$b;->h:Ldagger/internal/h;

    .line 111
    .line 112
    iget-object v6, p0, LgN0/a$b;->i:Ldagger/internal/h;

    .line 113
    .line 114
    iget-object v7, p0, LgN0/a$b;->o:Ldagger/internal/h;

    .line 115
    .line 116
    iget-object v8, p0, LgN0/a$b;->p:Ldagger/internal/h;

    .line 117
    .line 118
    invoke-static/range {v0 .. v9}, Lorg/xbet/statistic/stat_results/impl/results/races/presentation/i;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/stat_results/impl/results/races/presentation/i;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    iput-object p1, p0, LgN0/a$b;->r:Ldagger/internal/h;

    .line 123
    .line 124
    return-void
.end method

.method public final c(Lorg/xbet/statistic/stat_results/impl/results/races/presentation/RacesResultsFragment;)Lorg/xbet/statistic/stat_results/impl/results/races/presentation/RacesResultsFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LgN0/a$b;->e()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/stat_results/impl/results/races/presentation/d;->b(Lorg/xbet/statistic/stat_results/impl/results/races/presentation/RacesResultsFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LgN0/a$b;->a:LHX0/e;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xbet/statistic/stat_results/impl/results/races/presentation/d;->a(Lorg/xbet/statistic/stat_results/impl/results/races/presentation/RacesResultsFragment;LHX0/e;)V

    .line 11
    .line 12
    .line 13
    return-object p1
.end method

.method public final d()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/statistic/stat_results/impl/results/races/presentation/RacesResultsViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LgN0/a$b;->r:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final e()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LgN0/a$b;->d()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
