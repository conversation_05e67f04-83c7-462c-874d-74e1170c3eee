.class public final Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0001\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J.\u0010\u000c\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0004\u0008\u000c\u0010\rJ.\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0004\u0008\u000f\u0010\rR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u0011R\u001a\u0010\u0016\u001a\u0008\u0012\u0004\u0012\u00020\u00130\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0015\u00a8\u0006\u0017"
    }
    d2 = {
        "Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;",
        "",
        "Lf8/g;",
        "serviceGenerator",
        "<init>",
        "(Lf8/g;)V",
        "",
        "teamOne",
        "teamTwo",
        "startTime",
        "",
        "Luy/b;",
        "d",
        "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lvy/e;",
        "c",
        "a",
        "Lf8/g;",
        "Lkotlin/Function0;",
        "Lorg/xbet/cyber/cyberstatistic/impl/data/a;",
        "b",
        "Lkotlin/jvm/functions/Function0;",
        "api",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lorg/xbet/cyber/cyberstatistic/impl/data/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lf8/g;)V
    .locals 0
    .param p1    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;->a:Lf8/g;

    .line 5
    .line 6
    new-instance p1, Lorg/xbet/cyber/cyberstatistic/impl/data/b;

    .line 7
    .line 8
    invoke-direct {p1, p0}, Lorg/xbet/cyber/cyberstatistic/impl/data/b;-><init>(Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;)V

    .line 9
    .line 10
    .line 11
    iput-object p1, p0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;->b:Lkotlin/jvm/functions/Function0;

    .line 12
    .line 13
    return-void
.end method

.method public static synthetic a(Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;)Lorg/xbet/cyber/cyberstatistic/impl/data/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;->b(Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;)Lorg/xbet/cyber/cyberstatistic/impl/data/a;

    move-result-object p0

    return-object p0
.end method

.method public static final b(Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;)Lorg/xbet/cyber/cyberstatistic/impl/data/a;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;->a:Lf8/g;

    .line 2
    .line 3
    const-class v0, Lorg/xbet/cyber/cyberstatistic/impl/data/a;

    .line 4
    .line 5
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p0, v0}, Lf8/g;->c(Lkotlin/reflect/d;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, Lorg/xbet/cyber/cyberstatistic/impl/data/a;

    .line 14
    .line 15
    return-object p0
.end method


# virtual methods
.method public final c(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lvy/e;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p4, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberCSStatisticMatches$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p4

    .line 6
    check-cast v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberCSStatisticMatches$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberCSStatisticMatches$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberCSStatisticMatches$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberCSStatisticMatches$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p4}, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberCSStatisticMatches$1;-><init>(Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p4, v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberCSStatisticMatches$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberCSStatisticMatches$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p4, p0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;->b:Lkotlin/jvm/functions/Function0;

    .line 54
    .line 55
    invoke-interface {p4}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object p4

    .line 59
    check-cast p4, Lorg/xbet/cyber/cyberstatistic/impl/data/a;

    .line 60
    .line 61
    iput v3, v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberCSStatisticMatches$1;->label:I

    .line 62
    .line 63
    invoke-interface {p4, p1, p2, p3, v0}, Lorg/xbet/cyber/cyberstatistic/impl/data/a;->a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p4

    .line 67
    if-ne p4, v1, :cond_3

    .line 68
    .line 69
    return-object v1

    .line 70
    :cond_3
    :goto_1
    check-cast p4, Le8/b;

    .line 71
    .line 72
    invoke-virtual {p4}, Le8/b;->a()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    return-object p1
.end method

.method public final d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Luy/b;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p4, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberDotaStatisticMatches$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p4

    .line 6
    check-cast v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberDotaStatisticMatches$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberDotaStatisticMatches$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberDotaStatisticMatches$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberDotaStatisticMatches$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p4}, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberDotaStatisticMatches$1;-><init>(Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p4, v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberDotaStatisticMatches$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberDotaStatisticMatches$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p4, p0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource;->b:Lkotlin/jvm/functions/Function0;

    .line 54
    .line 55
    invoke-interface {p4}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object p4

    .line 59
    check-cast p4, Lorg/xbet/cyber/cyberstatistic/impl/data/a;

    .line 60
    .line 61
    iput v3, v0, Lorg/xbet/cyber/cyberstatistic/impl/data/CyberGameStatisticRemoteDataSource$getCyberDotaStatisticMatches$1;->label:I

    .line 62
    .line 63
    invoke-interface {p4, p1, p2, p3, v0}, Lorg/xbet/cyber/cyberstatistic/impl/data/a;->b(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p4

    .line 67
    if-ne p4, v1, :cond_3

    .line 68
    .line 69
    return-object v1

    .line 70
    :cond_3
    :goto_1
    check-cast p4, Le8/b;

    .line 71
    .line 72
    invoke-virtual {p4}, Le8/b;->a()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    return-object p1
.end method
