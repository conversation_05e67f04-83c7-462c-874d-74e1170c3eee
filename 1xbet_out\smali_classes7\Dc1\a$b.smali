.class public final LDc1/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LDc1/g;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LDc1/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final A:LlV/a;

.field public final B:Ltk0/b;

.field public final C:Ltk0/a;

.field public final D:LXa0/c;

.field public final E:Lnl/q;

.field public final F:LD81/a;

.field public final G:LDc1/a$b;

.field public final a:LHn0/a;

.field public final b:Lmo/f;

.field public final c:Lo9/a;

.field public final d:LX8/a;

.field public final e:LKg/a;

.field public final f:LAi0/a;

.field public final g:Lorg/xbet/feed/subscriptions/domain/usecases/c;

.field public final h:Llc1/b;

.field public final i:Lxg/h;

.field public final j:Ly5/a;

.field public final k:LHX/a;

.field public final l:Lw30/e;

.field public final m:Lorg/xplatform/aggregator/api/domain/a;

.field public final n:Lorg/xbet/consultantchat/domain/usecases/y0;

.field public final o:Lorg/xbet/analytics/domain/b;

.field public final p:Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;

.field public final q:LHt/a;

.field public final r:Lcom/xbet/onexcore/domain/usecase/a;

.field public final s:Leu/a;

.field public final t:LQl0/a;

.field public final u:LJT/d;

.field public final v:LVT/g;

.field public final w:Lv81/e;

.field public final x:Lc81/a;

.field public final y:Lak/a;

.field public final z:Lcom/xbet/onexuser/data/profile/b;


# direct methods
.method public constructor <init>(LQW0/c;Lmo/f;LKg/a;LAi0/a;LHX/a;LVp/a;Lak/a;Lc81/a;LlV/a;LQl0/a;Lorg/xbet/feed/subscriptions/domain/usecases/c;Lw30/e;Lorg/xplatform/aggregator/api/domain/a;Lorg/xbet/consultantchat/domain/usecases/y0;Lorg/xbet/analytics/domain/b;Lcom/xbet/onexcore/domain/usecase/a;Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;LJT/d;LHt/a;Leu/a;Lv81/e;LVT/g;Ltk0/b;Ltk0/a;LXa0/c;Lnl/q;LD81/a;LHn0/a;Lo9/a;LX8/a;Llc1/b;Lxg/h;Lx5/a;Lcom/xbet/onexuser/data/profile/b;Ly5/a;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LDc1/a$b;->G:LDc1/a$b;

    move-object/from16 p1, p28

    .line 4
    iput-object p1, p0, LDc1/a$b;->a:LHn0/a;

    .line 5
    iput-object p2, p0, LDc1/a$b;->b:Lmo/f;

    move-object/from16 p1, p29

    .line 6
    iput-object p1, p0, LDc1/a$b;->c:Lo9/a;

    move-object/from16 p1, p30

    .line 7
    iput-object p1, p0, LDc1/a$b;->d:LX8/a;

    .line 8
    iput-object p3, p0, LDc1/a$b;->e:LKg/a;

    .line 9
    iput-object p4, p0, LDc1/a$b;->f:LAi0/a;

    .line 10
    iput-object p11, p0, LDc1/a$b;->g:Lorg/xbet/feed/subscriptions/domain/usecases/c;

    move-object/from16 p1, p31

    .line 11
    iput-object p1, p0, LDc1/a$b;->h:Llc1/b;

    move-object/from16 p1, p32

    .line 12
    iput-object p1, p0, LDc1/a$b;->i:Lxg/h;

    move-object/from16 p1, p35

    .line 13
    iput-object p1, p0, LDc1/a$b;->j:Ly5/a;

    .line 14
    iput-object p5, p0, LDc1/a$b;->k:LHX/a;

    .line 15
    iput-object p12, p0, LDc1/a$b;->l:Lw30/e;

    .line 16
    iput-object p13, p0, LDc1/a$b;->m:Lorg/xplatform/aggregator/api/domain/a;

    .line 17
    iput-object p14, p0, LDc1/a$b;->n:Lorg/xbet/consultantchat/domain/usecases/y0;

    .line 18
    iput-object p15, p0, LDc1/a$b;->o:Lorg/xbet/analytics/domain/b;

    move-object/from16 p1, p17

    .line 19
    iput-object p1, p0, LDc1/a$b;->p:Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;

    move-object/from16 p1, p19

    .line 20
    iput-object p1, p0, LDc1/a$b;->q:LHt/a;

    move-object/from16 p1, p16

    .line 21
    iput-object p1, p0, LDc1/a$b;->r:Lcom/xbet/onexcore/domain/usecase/a;

    move-object/from16 p1, p20

    .line 22
    iput-object p1, p0, LDc1/a$b;->s:Leu/a;

    .line 23
    iput-object p10, p0, LDc1/a$b;->t:LQl0/a;

    move-object/from16 p1, p18

    .line 24
    iput-object p1, p0, LDc1/a$b;->u:LJT/d;

    move-object/from16 p1, p22

    .line 25
    iput-object p1, p0, LDc1/a$b;->v:LVT/g;

    move-object/from16 p1, p21

    .line 26
    iput-object p1, p0, LDc1/a$b;->w:Lv81/e;

    .line 27
    iput-object p8, p0, LDc1/a$b;->x:Lc81/a;

    .line 28
    iput-object p7, p0, LDc1/a$b;->y:Lak/a;

    move-object/from16 p1, p34

    .line 29
    iput-object p1, p0, LDc1/a$b;->z:Lcom/xbet/onexuser/data/profile/b;

    .line 30
    iput-object p9, p0, LDc1/a$b;->A:LlV/a;

    move-object/from16 p1, p23

    .line 31
    iput-object p1, p0, LDc1/a$b;->B:Ltk0/b;

    move-object/from16 p1, p24

    .line 32
    iput-object p1, p0, LDc1/a$b;->C:Ltk0/a;

    move-object/from16 p1, p25

    .line 33
    iput-object p1, p0, LDc1/a$b;->D:LXa0/c;

    move-object/from16 p1, p26

    .line 34
    iput-object p1, p0, LDc1/a$b;->E:Lnl/q;

    move-object/from16 p1, p27

    .line 35
    iput-object p1, p0, LDc1/a$b;->F:LD81/a;

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Lmo/f;LKg/a;LAi0/a;LHX/a;LVp/a;Lak/a;Lc81/a;LlV/a;LQl0/a;Lorg/xbet/feed/subscriptions/domain/usecases/c;Lw30/e;Lorg/xplatform/aggregator/api/domain/a;Lorg/xbet/consultantchat/domain/usecases/y0;Lorg/xbet/analytics/domain/b;Lcom/xbet/onexcore/domain/usecase/a;Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;LJT/d;LHt/a;Leu/a;Lv81/e;LVT/g;Ltk0/b;Ltk0/a;LXa0/c;Lnl/q;LD81/a;LHn0/a;Lo9/a;LX8/a;Llc1/b;Lxg/h;Lx5/a;Lcom/xbet/onexuser/data/profile/b;Ly5/a;LDc1/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p35}, LDc1/a$b;-><init>(LQW0/c;Lmo/f;LKg/a;LAi0/a;LHX/a;LVp/a;Lak/a;Lc81/a;LlV/a;LQl0/a;Lorg/xbet/feed/subscriptions/domain/usecases/c;Lw30/e;Lorg/xplatform/aggregator/api/domain/a;Lorg/xbet/consultantchat/domain/usecases/y0;Lorg/xbet/analytics/domain/b;Lcom/xbet/onexcore/domain/usecase/a;Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;LJT/d;LHt/a;Leu/a;Lv81/e;LVT/g;Ltk0/b;Ltk0/a;LXa0/c;Lnl/q;LD81/a;LHn0/a;Lo9/a;LX8/a;Llc1/b;Lxg/h;Lx5/a;Lcom/xbet/onexuser/data/profile/b;Ly5/a;)V

    return-void
.end method


# virtual methods
.method public a()Lzc1/a;
    .locals 1

    .line 1
    new-instance v0, LEc1/a;

    .line 2
    .line 3
    invoke-direct {v0}, LEc1/a;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public b()Lyc1/a;
    .locals 1

    .line 1
    invoke-virtual {p0}, LDc1/a$b;->c()Lorg/xplatform/logout/impl/domain/scenarios/ClearAllDataScenarioImpl;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final c()Lorg/xplatform/logout/impl/domain/scenarios/ClearAllDataScenarioImpl;
    .locals 39

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/logout/impl/domain/scenarios/ClearAllDataScenarioImpl;

    .line 4
    .line 5
    invoke-virtual {v0}, LDc1/a$b;->e()LIn0/a;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    iget-object v3, v0, LDc1/a$b;->b:Lmo/f;

    .line 10
    .line 11
    invoke-interface {v3}, Lmo/f;->e()Lorg/xbet/betting/core/tax/domain/usecase/a;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    invoke-static {v3}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v3

    .line 19
    check-cast v3, Lorg/xbet/betting/core/tax/domain/usecase/a;

    .line 20
    .line 21
    invoke-virtual {v0}, LDc1/a$b;->i()Lp9/c;

    .line 22
    .line 23
    .line 24
    move-result-object v4

    .line 25
    invoke-virtual {v0}, LDc1/a$b;->j()Lr9/c;

    .line 26
    .line 27
    .line 28
    move-result-object v5

    .line 29
    iget-object v6, v0, LDc1/a$b;->e:LKg/a;

    .line 30
    .line 31
    invoke-interface {v6}, LKg/a;->d()LOg/a;

    .line 32
    .line 33
    .line 34
    move-result-object v6

    .line 35
    invoke-static {v6}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v6

    .line 39
    check-cast v6, LOg/a;

    .line 40
    .line 41
    iget-object v7, v0, LDc1/a$b;->f:LAi0/a;

    .line 42
    .line 43
    invoke-interface {v7}, LAi0/a;->c()LBi0/b;

    .line 44
    .line 45
    .line 46
    move-result-object v7

    .line 47
    invoke-static {v7}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    move-result-object v7

    .line 51
    check-cast v7, LBi0/b;

    .line 52
    .line 53
    iget-object v8, v0, LDc1/a$b;->g:Lorg/xbet/feed/subscriptions/domain/usecases/c;

    .line 54
    .line 55
    iget-object v9, v0, LDc1/a$b;->h:Llc1/b;

    .line 56
    .line 57
    invoke-virtual {v0}, LDc1/a$b;->f()LIg/e;

    .line 58
    .line 59
    .line 60
    move-result-object v10

    .line 61
    iget-object v11, v0, LDc1/a$b;->j:Ly5/a;

    .line 62
    .line 63
    iget-object v12, v0, LDc1/a$b;->k:LHX/a;

    .line 64
    .line 65
    invoke-interface {v12}, LHX/a;->f()LSX/a;

    .line 66
    .line 67
    .line 68
    move-result-object v12

    .line 69
    invoke-static {v12}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object v12

    .line 73
    check-cast v12, LSX/a;

    .line 74
    .line 75
    iget-object v13, v0, LDc1/a$b;->l:Lw30/e;

    .line 76
    .line 77
    iget-object v14, v0, LDc1/a$b;->m:Lorg/xplatform/aggregator/api/domain/a;

    .line 78
    .line 79
    iget-object v15, v0, LDc1/a$b;->n:Lorg/xbet/consultantchat/domain/usecases/y0;

    .line 80
    .line 81
    move-object/from16 v16, v1

    .line 82
    .line 83
    iget-object v1, v0, LDc1/a$b;->o:Lorg/xbet/analytics/domain/b;

    .line 84
    .line 85
    move-object/from16 v17, v1

    .line 86
    .line 87
    iget-object v1, v0, LDc1/a$b;->p:Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;

    .line 88
    .line 89
    move-object/from16 v18, v1

    .line 90
    .line 91
    iget-object v1, v0, LDc1/a$b;->q:LHt/a;

    .line 92
    .line 93
    move-object/from16 v19, v1

    .line 94
    .line 95
    iget-object v1, v0, LDc1/a$b;->r:Lcom/xbet/onexcore/domain/usecase/a;

    .line 96
    .line 97
    move-object/from16 v20, v1

    .line 98
    .line 99
    iget-object v1, v0, LDc1/a$b;->s:Leu/a;

    .line 100
    .line 101
    move-object/from16 v21, v1

    .line 102
    .line 103
    iget-object v1, v0, LDc1/a$b;->t:LQl0/a;

    .line 104
    .line 105
    move-object/from16 v22, v1

    .line 106
    .line 107
    iget-object v1, v0, LDc1/a$b;->b:Lmo/f;

    .line 108
    .line 109
    invoke-interface {v1}, Lmo/f;->a()Lorg/xbet/betting/core/tax/domain/usecase/n;

    .line 110
    .line 111
    .line 112
    move-result-object v1

    .line 113
    invoke-static {v1}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object v1

    .line 117
    check-cast v1, Lorg/xbet/betting/core/tax/domain/usecase/n;

    .line 118
    .line 119
    move-object/from16 v23, v1

    .line 120
    .line 121
    iget-object v1, v0, LDc1/a$b;->u:LJT/d;

    .line 122
    .line 123
    move-object/from16 v24, v1

    .line 124
    .line 125
    iget-object v1, v0, LDc1/a$b;->v:LVT/g;

    .line 126
    .line 127
    move-object/from16 v25, v1

    .line 128
    .line 129
    iget-object v1, v0, LDc1/a$b;->w:Lv81/e;

    .line 130
    .line 131
    move-object/from16 v26, v1

    .line 132
    .line 133
    iget-object v1, v0, LDc1/a$b;->x:Lc81/a;

    .line 134
    .line 135
    invoke-interface {v1}, Lc81/a;->k()Lv81/f;

    .line 136
    .line 137
    .line 138
    move-result-object v1

    .line 139
    invoke-static {v1}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 140
    .line 141
    .line 142
    move-result-object v1

    .line 143
    check-cast v1, Lv81/f;

    .line 144
    .line 145
    move-object/from16 v27, v1

    .line 146
    .line 147
    iget-object v1, v0, LDc1/a$b;->y:Lak/a;

    .line 148
    .line 149
    invoke-interface {v1}, Lak/a;->f()Lfk/e;

    .line 150
    .line 151
    .line 152
    move-result-object v1

    .line 153
    invoke-static {v1}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 154
    .line 155
    .line 156
    move-result-object v1

    .line 157
    check-cast v1, Lfk/e;

    .line 158
    .line 159
    move-object/from16 v28, v1

    .line 160
    .line 161
    iget-object v1, v0, LDc1/a$b;->y:Lak/a;

    .line 162
    .line 163
    invoke-interface {v1}, Lak/a;->C()Lfk/f;

    .line 164
    .line 165
    .line 166
    move-result-object v1

    .line 167
    invoke-static {v1}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 168
    .line 169
    .line 170
    move-result-object v1

    .line 171
    check-cast v1, Lfk/f;

    .line 172
    .line 173
    invoke-virtual {v0}, LDc1/a$b;->g()Lp9/a;

    .line 174
    .line 175
    .line 176
    move-result-object v29

    .line 177
    invoke-virtual {v0}, LDc1/a$b;->d()Lcom/xbet/onexuser/domain/profile/a;

    .line 178
    .line 179
    .line 180
    move-result-object v30

    .line 181
    move-object/from16 v31, v1

    .line 182
    .line 183
    iget-object v1, v0, LDc1/a$b;->A:LlV/a;

    .line 184
    .line 185
    invoke-interface {v1}, LlV/a;->g()Lorg/xbet/feature/coeftrack/domain/usecases/a;

    .line 186
    .line 187
    .line 188
    move-result-object v1

    .line 189
    invoke-static {v1}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 190
    .line 191
    .line 192
    move-result-object v1

    .line 193
    check-cast v1, Lorg/xbet/feature/coeftrack/domain/usecases/a;

    .line 194
    .line 195
    invoke-virtual {v0}, LDc1/a$b;->h()Lr9/a;

    .line 196
    .line 197
    .line 198
    move-result-object v32

    .line 199
    move-object/from16 v33, v1

    .line 200
    .line 201
    iget-object v1, v0, LDc1/a$b;->B:Ltk0/b;

    .line 202
    .line 203
    move-object/from16 v34, v1

    .line 204
    .line 205
    iget-object v1, v0, LDc1/a$b;->C:Ltk0/a;

    .line 206
    .line 207
    move-object/from16 v35, v1

    .line 208
    .line 209
    iget-object v1, v0, LDc1/a$b;->D:LXa0/c;

    .line 210
    .line 211
    move-object/from16 v36, v1

    .line 212
    .line 213
    iget-object v1, v0, LDc1/a$b;->E:Lnl/q;

    .line 214
    .line 215
    move-object/from16 v37, v1

    .line 216
    .line 217
    iget-object v1, v0, LDc1/a$b;->F:LD81/a;

    .line 218
    .line 219
    move-object/from16 v38, v37

    .line 220
    .line 221
    move-object/from16 v37, v1

    .line 222
    .line 223
    move-object/from16 v1, v16

    .line 224
    .line 225
    move-object/from16 v16, v17

    .line 226
    .line 227
    move-object/from16 v17, v18

    .line 228
    .line 229
    move-object/from16 v18, v19

    .line 230
    .line 231
    move-object/from16 v19, v20

    .line 232
    .line 233
    move-object/from16 v20, v21

    .line 234
    .line 235
    move-object/from16 v21, v22

    .line 236
    .line 237
    move-object/from16 v22, v23

    .line 238
    .line 239
    move-object/from16 v23, v24

    .line 240
    .line 241
    move-object/from16 v24, v25

    .line 242
    .line 243
    move-object/from16 v25, v26

    .line 244
    .line 245
    move-object/from16 v26, v27

    .line 246
    .line 247
    move-object/from16 v27, v28

    .line 248
    .line 249
    move-object/from16 v28, v31

    .line 250
    .line 251
    move-object/from16 v31, v33

    .line 252
    .line 253
    move-object/from16 v33, v34

    .line 254
    .line 255
    move-object/from16 v34, v35

    .line 256
    .line 257
    move-object/from16 v35, v36

    .line 258
    .line 259
    move-object/from16 v36, v38

    .line 260
    .line 261
    invoke-direct/range {v1 .. v37}, Lorg/xplatform/logout/impl/domain/scenarios/ClearAllDataScenarioImpl;-><init>(LIn0/a;Lorg/xbet/betting/core/tax/domain/usecase/a;Lp9/c;Lr9/c;LOg/a;LBi0/b;Lorg/xbet/feed/subscriptions/domain/usecases/c;Llc1/b;LIg/e;Ly5/a;LSX/a;Lw30/e;Lorg/xplatform/aggregator/api/domain/a;Lorg/xbet/consultantchat/domain/usecases/y0;Lorg/xbet/analytics/domain/b;Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;LHt/a;Lcom/xbet/onexcore/domain/usecase/a;Leu/a;LQl0/a;Lorg/xbet/betting/core/tax/domain/usecase/n;LJT/d;LVT/g;Lv81/e;Lv81/f;Lfk/e;Lfk/f;Lp9/a;Lcom/xbet/onexuser/domain/profile/a;Lorg/xbet/feature/coeftrack/domain/usecases/a;Lr9/a;Ltk0/b;Ltk0/a;LXa0/c;Lnl/q;LD81/a;)V

    .line 262
    .line 263
    .line 264
    move-object/from16 v16, v1

    .line 265
    .line 266
    return-object v16
.end method

.method public final d()Lcom/xbet/onexuser/domain/profile/a;
    .locals 2

    .line 1
    new-instance v0, Lcom/xbet/onexuser/domain/profile/a;

    .line 2
    .line 3
    iget-object v1, p0, LDc1/a$b;->z:Lcom/xbet/onexuser/data/profile/b;

    .line 4
    .line 5
    invoke-direct {v0, v1}, Lcom/xbet/onexuser/domain/profile/a;-><init>(Lcom/xbet/onexuser/data/profile/b;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final e()LIn0/a;
    .locals 2

    .line 1
    new-instance v0, LIn0/a;

    .line 2
    .line 3
    iget-object v1, p0, LDc1/a$b;->a:LHn0/a;

    .line 4
    .line 5
    invoke-direct {v0, v1}, LIn0/a;-><init>(LHn0/a;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final f()LIg/e;
    .locals 2

    .line 1
    new-instance v0, LIg/e;

    .line 2
    .line 3
    iget-object v1, p0, LDc1/a$b;->i:Lxg/h;

    .line 4
    .line 5
    invoke-direct {v0, v1}, LIg/e;-><init>(Lxg/h;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final g()Lp9/a;
    .locals 2

    .line 1
    new-instance v0, Lp9/a;

    .line 2
    .line 3
    iget-object v1, p0, LDc1/a$b;->c:Lo9/a;

    .line 4
    .line 5
    invoke-direct {v0, v1}, Lp9/a;-><init>(Lo9/a;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final h()Lr9/a;
    .locals 2

    .line 1
    new-instance v0, Lr9/a;

    .line 2
    .line 3
    iget-object v1, p0, LDc1/a$b;->d:LX8/a;

    .line 4
    .line 5
    invoke-direct {v0, v1}, Lr9/a;-><init>(LX8/a;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final i()Lp9/c;
    .locals 2

    .line 1
    new-instance v0, Lp9/c;

    .line 2
    .line 3
    iget-object v1, p0, LDc1/a$b;->c:Lo9/a;

    .line 4
    .line 5
    invoke-direct {v0, v1}, Lp9/c;-><init>(Lo9/a;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final j()Lr9/c;
    .locals 2

    .line 1
    new-instance v0, Lr9/c;

    .line 2
    .line 3
    iget-object v1, p0, LDc1/a$b;->d:LX8/a;

    .line 4
    .line 5
    invoke-direct {v0, v1}, Lr9/c;-><init>(LX8/a;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method
