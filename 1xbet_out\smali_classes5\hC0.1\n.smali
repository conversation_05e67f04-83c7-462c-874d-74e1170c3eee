.class public final synthetic LhC0/n;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnTouchListener;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:LB4/a;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;LB4/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LhC0/n;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, LhC0/n;->b:LB4/a;

    return-void
.end method


# virtual methods
.method public final onTouch(Landroid/view/View;Landroid/view/MotionEvent;)Z
    .locals 2

    .line 1
    iget-object v0, p0, LhC0/n;->a:Lkotlin/jvm/functions/Function1;

    iget-object v1, p0, LhC0/n;->b:LB4/a;

    invoke-static {v0, v1, p1, p2}, Lorg/xbet/sportgame/markets_settings/impl/presentation/adapters/MarketSettingDelegateKt;->e(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;Landroid/view/MotionEvent;)Z

    move-result p1

    return p1
.end method
