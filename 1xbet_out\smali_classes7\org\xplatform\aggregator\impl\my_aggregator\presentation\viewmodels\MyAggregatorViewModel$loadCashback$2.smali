.class final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.my_aggregator.presentation.viewmodels.MyAggregatorViewModel$loadCashback$2"
    f = "MyAggregatorViewModel.kt"
    l = {
        0x462,
        0x46e,
        0x473
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->n6()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 11

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x3

    .line 9
    const/4 v4, 0x2

    .line 10
    const/4 v5, 0x1

    .line 11
    if-eqz v1, :cond_3

    .line 12
    .line 13
    if-eq v1, v5, :cond_2

    .line 14
    .line 15
    if-eq v1, v4, :cond_1

    .line 16
    .line 17
    if-ne v1, v3, :cond_0

    .line 18
    .line 19
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    goto/16 :goto_4

    .line 23
    .line 24
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 25
    .line 26
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 27
    .line 28
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 29
    .line 30
    .line 31
    throw p1

    .line 32
    :cond_1
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->L$1:Ljava/lang/Object;

    .line 33
    .line 34
    check-cast v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 35
    .line 36
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->L$0:Ljava/lang/Object;

    .line 37
    .line 38
    check-cast v4, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 39
    .line 40
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    goto/16 :goto_1

    .line 44
    .line 45
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 46
    .line 47
    .line 48
    goto :goto_0

    .line 49
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 50
    .line 51
    .line 52
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 53
    .line 54
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->R4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    iput v5, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->label:I

    .line 59
    .line 60
    invoke-virtual {p1, v2, p0}, Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;->c(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    if-ne p1, v0, :cond_4

    .line 65
    .line 66
    goto/16 :goto_3

    .line 67
    .line 68
    :cond_4
    :goto_0
    check-cast p1, Le9/a;

    .line 69
    .line 70
    invoke-virtual {p1}, Le9/a;->e()Z

    .line 71
    .line 72
    .line 73
    move-result p1

    .line 74
    if-eqz p1, :cond_e

    .line 75
    .line 76
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 77
    .line 78
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->h5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lek0/o;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    invoke-virtual {p1}, Lek0/o;->e2()Lek0/m;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    invoke-virtual {p1}, Lek0/m;->A()Z

    .line 87
    .line 88
    .line 89
    move-result p1

    .line 90
    if-nez p1, :cond_5

    .line 91
    .line 92
    goto/16 :goto_6

    .line 93
    .line 94
    :cond_5
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 95
    .line 96
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Z4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    instance-of p1, p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$b;

    .line 105
    .line 106
    if-eqz p1, :cond_6

    .line 107
    .line 108
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 109
    .line 110
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->u5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Z

    .line 111
    .line 112
    .line 113
    move-result p1

    .line 114
    if-eqz p1, :cond_7

    .line 115
    .line 116
    :cond_6
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 117
    .line 118
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Z4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$c;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$c;

    .line 123
    .line 124
    invoke-interface {p1, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 125
    .line 126
    .line 127
    :cond_7
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 128
    .line 129
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->A4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/util/List;

    .line 130
    .line 131
    .line 132
    move-result-object p1

    .line 133
    iget-object v6, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 134
    .line 135
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 136
    .line 137
    .line 138
    move-result v7

    .line 139
    if-eqz v7, :cond_a

    .line 140
    .line 141
    invoke-static {v6}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->P4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)LQ51/b;

    .line 142
    .line 143
    .line 144
    move-result-object p1

    .line 145
    iput-object v6, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->L$0:Ljava/lang/Object;

    .line 146
    .line 147
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->L$1:Ljava/lang/Object;

    .line 148
    .line 149
    iput v4, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->label:I

    .line 150
    .line 151
    invoke-interface {p1, p0}, LQ51/b;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 152
    .line 153
    .line 154
    move-result-object p1

    .line 155
    if-ne p1, v0, :cond_8

    .line 156
    .line 157
    goto :goto_3

    .line 158
    :cond_8
    move-object v4, v6

    .line 159
    :goto_1
    check-cast p1, Ljava/lang/Iterable;

    .line 160
    .line 161
    new-instance v6, Ljava/util/ArrayList;

    .line 162
    .line 163
    const/16 v7, 0xa

    .line 164
    .line 165
    invoke-static {p1, v7}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 166
    .line 167
    .line 168
    move-result v7

    .line 169
    invoke-direct {v6, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 170
    .line 171
    .line 172
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 173
    .line 174
    .line 175
    move-result-object p1

    .line 176
    :goto_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 177
    .line 178
    .line 179
    move-result v7

    .line 180
    if-eqz v7, :cond_9

    .line 181
    .line 182
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 183
    .line 184
    .line 185
    move-result-object v7

    .line 186
    check-cast v7, LP51/b;

    .line 187
    .line 188
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)LHX0/e;

    .line 189
    .line 190
    .line 191
    move-result-object v8

    .line 192
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->T4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/text/DecimalFormat;

    .line 193
    .line 194
    .line 195
    move-result-object v9

    .line 196
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->q5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 197
    .line 198
    .line 199
    move-result-object v10

    .line 200
    invoke-static {v7, v8, v9, v10}, Lqa1/a;->b(LP51/b;LHX0/e;Ljava/text/DecimalFormat;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;)Lg31/b;

    .line 201
    .line 202
    .line 203
    move-result-object v7

    .line 204
    invoke-interface {v6, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 205
    .line 206
    .line 207
    goto :goto_2

    .line 208
    :cond_9
    move-object p1, v6

    .line 209
    :cond_a
    invoke-static {v1, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->D5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/util/List;)V

    .line 210
    .line 211
    .line 212
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 213
    .line 214
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->B4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lra1/a;

    .line 215
    .line 216
    .line 217
    move-result-object p1

    .line 218
    if-nez p1, :cond_c

    .line 219
    .line 220
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 221
    .line 222
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->M4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)LQ51/a;

    .line 223
    .line 224
    .line 225
    move-result-object p1

    .line 226
    const/4 v1, 0x0

    .line 227
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->L$0:Ljava/lang/Object;

    .line 228
    .line 229
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->L$1:Ljava/lang/Object;

    .line 230
    .line 231
    iput v3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->label:I

    .line 232
    .line 233
    invoke-interface {p1, p0}, LQ51/a;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 234
    .line 235
    .line 236
    move-result-object p1

    .line 237
    if-ne p1, v0, :cond_b

    .line 238
    .line 239
    :goto_3
    return-object v0

    .line 240
    :cond_b
    :goto_4
    check-cast p1, LP51/a;

    .line 241
    .line 242
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 243
    .line 244
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->A4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/util/List;

    .line 245
    .line 246
    .line 247
    move-result-object v0

    .line 248
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 249
    .line 250
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->q5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 251
    .line 252
    .line 253
    move-result-object v1

    .line 254
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 255
    .line 256
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)LHX0/e;

    .line 257
    .line 258
    .line 259
    move-result-object v3

    .line 260
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 261
    .line 262
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->T4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/text/DecimalFormat;

    .line 263
    .line 264
    .line 265
    move-result-object v4

    .line 266
    invoke-static {p1, v0, v1, v3, v4}, Lqa1/a;->d(LP51/a;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;LHX0/e;Ljava/text/DecimalFormat;)Lra1/a;

    .line 267
    .line 268
    .line 269
    move-result-object p1

    .line 270
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 271
    .line 272
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->E5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lra1/a;)V

    .line 273
    .line 274
    .line 275
    :cond_c
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 276
    .line 277
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Z4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 278
    .line 279
    .line 280
    move-result-object v0

    .line 281
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 282
    .line 283
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->A4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/util/List;

    .line 284
    .line 285
    .line 286
    move-result-object v1

    .line 287
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    .line 288
    .line 289
    .line 290
    move-result v1

    .line 291
    if-eqz v1, :cond_d

    .line 292
    .line 293
    sget-object p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$a;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$a;

    .line 294
    .line 295
    goto :goto_5

    .line 296
    :cond_d
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 297
    .line 298
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 299
    .line 300
    .line 301
    move-result-object v1

    .line 302
    invoke-static {v5}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 303
    .line 304
    .line 305
    move-result-object v3

    .line 306
    invoke-interface {v1, v3}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 307
    .line 308
    .line 309
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$b;

    .line 310
    .line 311
    invoke-direct {v1, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$b;-><init>(Lra1/a;)V

    .line 312
    .line 313
    .line 314
    move-object p1, v1

    .line 315
    :goto_5
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 316
    .line 317
    .line 318
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 319
    .line 320
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->y4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 321
    .line 322
    .line 323
    move-result-object p1

    .line 324
    invoke-static {v2}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 325
    .line 326
    .line 327
    move-result-object v0

    .line 328
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 329
    .line 330
    .line 331
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 332
    .line 333
    return-object p1

    .line 334
    :cond_e
    :goto_6
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 335
    .line 336
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->y4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 337
    .line 338
    .line 339
    move-result-object p1

    .line 340
    invoke-static {v5}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 341
    .line 342
    .line 343
    move-result-object v0

    .line 344
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 345
    .line 346
    .line 347
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 348
    .line 349
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 350
    .line 351
    .line 352
    move-result-object p1

    .line 353
    invoke-static {v5}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 354
    .line 355
    .line 356
    move-result-object v0

    .line 357
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 358
    .line 359
    .line 360
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 361
    .line 362
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Z4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 363
    .line 364
    .line 365
    move-result-object p1

    .line 366
    sget-object v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$a;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$a;

    .line 367
    .line 368
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 369
    .line 370
    .line 371
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 372
    .line 373
    return-object p1
.end method
