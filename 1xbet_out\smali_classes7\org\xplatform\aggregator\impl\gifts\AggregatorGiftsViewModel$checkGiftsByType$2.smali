.class final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.AggregatorGiftsViewModel$checkGiftsByType$2"
    f = "AggregatorGiftsViewModel.kt"
    l = {
        0x126
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->h5(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $type:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lorg/xplatform/aggregator/api/navigation/GiftsChipType;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;",
            "Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->$type:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->$type:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    invoke-direct {p1, v0, v1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lorg/xplatform/aggregator/api/navigation/GiftsChipType;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->F4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->$type:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 34
    .line 35
    iput v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->label:I

    .line 36
    .line 37
    invoke-virtual {p1, v1, p0}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->h(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    if-ne p1, v0, :cond_2

    .line 42
    .line 43
    return-object v0

    .line 44
    :cond_2
    :goto_0
    check-cast p1, Ljava/util/List;

    .line 45
    .line 46
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 47
    .line 48
    .line 49
    move-result v0

    .line 50
    if-eqz v0, :cond_3

    .line 51
    .line 52
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 53
    .line 54
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->e5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)V

    .line 55
    .line 56
    .line 57
    goto :goto_1

    .line 58
    :cond_3
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 59
    .line 60
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->O4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    const/4 v1, 0x0

    .line 65
    invoke-static {v1}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 70
    .line 71
    .line 72
    :goto_1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 73
    .line 74
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->G4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lha1/c;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    if-eqz v0, :cond_4

    .line 79
    .line 80
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 81
    .line 82
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->P4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 83
    .line 84
    .line 85
    move-result-object v0

    .line 86
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 87
    .line 88
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->G4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lha1/c;

    .line 89
    .line 90
    .line 91
    move-result-object v1

    .line 92
    invoke-static {v1}, Lkotlin/collections/v;->r(Ljava/lang/Object;)Ljava/util/List;

    .line 93
    .line 94
    .line 95
    move-result-object v1

    .line 96
    invoke-static {v1, p1}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 101
    .line 102
    .line 103
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 104
    .line 105
    return-object p1
.end method
