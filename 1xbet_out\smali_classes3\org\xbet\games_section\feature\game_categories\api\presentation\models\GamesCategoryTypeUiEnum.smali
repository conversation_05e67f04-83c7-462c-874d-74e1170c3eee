.class public final enum Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0015\u0008\u0086\u0081\u0002\u0018\u0000 \u000e2\u0008\u0012\u0004\u0012\u00020\u00000\u0001:\u0001\u000fB\u0019\u0008\u0002\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0003\u0010\u0008\u001a\u0004\u0008\t\u0010\nR\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0005\u0010\u000b\u001a\u0004\u0008\u000c\u0010\rj\u0002\u0008\u0010j\u0002\u0008\u0011j\u0002\u0008\u0012j\u0002\u0008\u0013j\u0002\u0008\u0014j\u0002\u0008\u0015j\u0002\u0008\u0016j\u0002\u0008\u0017j\u0002\u0008\u0018\u00a8\u0006\u0019"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;",
        "",
        "",
        "id",
        "",
        "logoUrl",
        "<init>",
        "(Ljava/lang/String;IILjava/lang/String;)V",
        "I",
        "getId",
        "()I",
        "Ljava/lang/String;",
        "getLogoUrl",
        "()Ljava/lang/String;",
        "Companion",
        "a",
        "FOR_YOU",
        "BEST",
        "NEW",
        "LOTTERIES",
        "SLOTS",
        "STAIRS",
        "DICES",
        "CARD_GAMES",
        "OTHER",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

.field public static final enum BEST:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

.field public static final enum CARD_GAMES:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

.field public static final Companion:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final enum DICES:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

.field public static final enum FOR_YOU:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

.field public static final enum LOTTERIES:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

.field public static final enum NEW:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

.field public static final enum OTHER:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

.field public static final enum SLOTS:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

.field public static final enum STAIRS:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;


# instance fields
.field private final id:I

.field private final logoUrl:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 2
    .line 3
    const v1, 0x27093

    .line 4
    .line 5
    .line 6
    const-string v2, "static/img/android/games_categories/for_you.webp"

    .line 7
    .line 8
    const-string v3, "FOR_YOU"

    .line 9
    .line 10
    const/4 v4, 0x0

    .line 11
    invoke-direct {v0, v3, v4, v1, v2}, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;-><init>(Ljava/lang/String;IILjava/lang/String;)V

    .line 12
    .line 13
    .line 14
    sput-object v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->FOR_YOU:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 15
    .line 16
    new-instance v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 17
    .line 18
    const v1, 0x26d08

    .line 19
    .line 20
    .line 21
    const-string v2, "static/img/android/games_categories/best.webp"

    .line 22
    .line 23
    const-string v3, "BEST"

    .line 24
    .line 25
    const/4 v4, 0x1

    .line 26
    invoke-direct {v0, v3, v4, v1, v2}, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;-><init>(Ljava/lang/String;IILjava/lang/String;)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->BEST:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 30
    .line 31
    new-instance v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 32
    .line 33
    const v1, 0x26d07

    .line 34
    .line 35
    .line 36
    const-string v2, "static/img/android/games_categories/new.webp"

    .line 37
    .line 38
    const-string v3, "NEW"

    .line 39
    .line 40
    const/4 v4, 0x2

    .line 41
    invoke-direct {v0, v3, v4, v1, v2}, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;-><init>(Ljava/lang/String;IILjava/lang/String;)V

    .line 42
    .line 43
    .line 44
    sput-object v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->NEW:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 45
    .line 46
    new-instance v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 47
    .line 48
    const v1, 0x2652d

    .line 49
    .line 50
    .line 51
    const-string v2, "static/img/android/games_categories/lottery.webp"

    .line 52
    .line 53
    const-string v3, "LOTTERIES"

    .line 54
    .line 55
    const/4 v4, 0x3

    .line 56
    invoke-direct {v0, v3, v4, v1, v2}, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;-><init>(Ljava/lang/String;IILjava/lang/String;)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->LOTTERIES:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 60
    .line 61
    new-instance v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 62
    .line 63
    const v1, 0x19521

    .line 64
    .line 65
    .line 66
    const-string v2, "static/img/android/games_categories/slots.webp"

    .line 67
    .line 68
    const-string v3, "SLOTS"

    .line 69
    .line 70
    const/4 v4, 0x4

    .line 71
    invoke-direct {v0, v3, v4, v1, v2}, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;-><init>(Ljava/lang/String;IILjava/lang/String;)V

    .line 72
    .line 73
    .line 74
    sput-object v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->SLOTS:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 75
    .line 76
    new-instance v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 77
    .line 78
    const v1, 0x26524

    .line 79
    .line 80
    .line 81
    const-string v2, "static/img/android/games_categories/straits.webp"

    .line 82
    .line 83
    const-string v3, "STAIRS"

    .line 84
    .line 85
    const/4 v4, 0x5

    .line 86
    invoke-direct {v0, v3, v4, v1, v2}, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;-><init>(Ljava/lang/String;IILjava/lang/String;)V

    .line 87
    .line 88
    .line 89
    sput-object v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->STAIRS:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 90
    .line 91
    new-instance v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 92
    .line 93
    const v1, 0x26525

    .line 94
    .line 95
    .line 96
    const-string v2, "static/img/android/games_categories/dice.webp"

    .line 97
    .line 98
    const-string v3, "DICES"

    .line 99
    .line 100
    const/4 v4, 0x6

    .line 101
    invoke-direct {v0, v3, v4, v1, v2}, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;-><init>(Ljava/lang/String;IILjava/lang/String;)V

    .line 102
    .line 103
    .line 104
    sput-object v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->DICES:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 105
    .line 106
    new-instance v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 107
    .line 108
    const v1, 0x2651d

    .line 109
    .line 110
    .line 111
    const-string v2, "static/img/android/games_categories/cards.webp"

    .line 112
    .line 113
    const-string v3, "CARD_GAMES"

    .line 114
    .line 115
    const/4 v4, 0x7

    .line 116
    invoke-direct {v0, v3, v4, v1, v2}, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;-><init>(Ljava/lang/String;IILjava/lang/String;)V

    .line 117
    .line 118
    .line 119
    sput-object v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->CARD_GAMES:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 120
    .line 121
    new-instance v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 122
    .line 123
    const v1, 0x2669f

    .line 124
    .line 125
    .line 126
    const-string v2, "static/img/android/games_categories/other_games.webp"

    .line 127
    .line 128
    const-string v3, "OTHER"

    .line 129
    .line 130
    const/16 v4, 0x8

    .line 131
    .line 132
    invoke-direct {v0, v3, v4, v1, v2}, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;-><init>(Ljava/lang/String;IILjava/lang/String;)V

    .line 133
    .line 134
    .line 135
    sput-object v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->OTHER:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 136
    .line 137
    invoke-static {}, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->a()[Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 138
    .line 139
    .line 140
    move-result-object v0

    .line 141
    sput-object v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->$VALUES:[Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 142
    .line 143
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 144
    .line 145
    .line 146
    move-result-object v0

    .line 147
    sput-object v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->$ENTRIES:Lkotlin/enums/a;

    .line 148
    .line 149
    new-instance v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum$a;

    .line 150
    .line 151
    const/4 v1, 0x0

    .line 152
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 153
    .line 154
    .line 155
    sput-object v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->Companion:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum$a;

    .line 156
    .line 157
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;IILjava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    iput p3, p0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->id:I

    .line 5
    .line 6
    iput-object p4, p0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->logoUrl:Ljava/lang/String;

    .line 7
    .line 8
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;
    .locals 3

    .line 1
    const/16 v0, 0x9

    new-array v0, v0, [Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    sget-object v1, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->FOR_YOU:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->BEST:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->NEW:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->LOTTERIES:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->SLOTS:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->STAIRS:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->DICES:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->CARD_GAMES:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->OTHER:Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    const/16 v2, 0x8

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->$VALUES:[Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getId()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->id:I

    .line 2
    .line 3
    return v0
.end method

.method public final getLogoUrl()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/game_categories/api/presentation/models/GamesCategoryTypeUiEnum;->logoUrl:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method
