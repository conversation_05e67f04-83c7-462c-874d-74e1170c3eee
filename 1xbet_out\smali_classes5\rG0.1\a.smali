.class public final LrG0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u001a\u0019\u0010\u0006\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0004\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u001a\u0019\u0010\u0008\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0004\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\u0007\u001a\u0019\u0010\t\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0004\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\t\u0010\u0007\u00a8\u0006\n"
    }
    d2 = {
        "LsG0/c;",
        "LvG0/h;",
        "d",
        "(LsG0/c;)LvG0/h;",
        "teamGames",
        "LvG0/g;",
        "a",
        "(LsG0/c;)LvG0/g;",
        "c",
        "b",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LsG0/c;)LvG0/g;
    .locals 5

    .line 1
    invoke-virtual {p0}, LsG0/c;->g()LsG0/d;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-virtual {v0}, LsG0/d;->a()Ljava/lang/Integer;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object v0, v1

    .line 14
    :goto_0
    if-eqz v0, :cond_2

    .line 15
    .line 16
    invoke-virtual {p0}, LsG0/c;->h()LsG0/d;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    if-eqz v0, :cond_1

    .line 21
    .line 22
    invoke-virtual {v0}, LsG0/d;->a()Ljava/lang/Integer;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    goto :goto_1

    .line 27
    :cond_1
    move-object v0, v1

    .line 28
    :goto_1
    if-eqz v0, :cond_2

    .line 29
    .line 30
    new-instance v0, LvG0/g;

    .line 31
    .line 32
    sget-object v1, Lorg/xbet/statistic/lastgames/domain/entities/DescriptionModel;->GOALS:Lorg/xbet/statistic/lastgames/domain/entities/DescriptionModel;

    .line 33
    .line 34
    invoke-virtual {p0}, LsG0/c;->g()LsG0/d;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    invoke-virtual {v2}, LsG0/d;->a()Ljava/lang/Integer;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    .line 43
    .line 44
    .line 45
    move-result v2

    .line 46
    invoke-virtual {p0}, LsG0/c;->h()LsG0/d;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    invoke-virtual {v3}, LsG0/d;->a()Ljava/lang/Integer;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 55
    .line 56
    .line 57
    move-result v3

    .line 58
    invoke-virtual {p0}, LsG0/c;->g()LsG0/d;

    .line 59
    .line 60
    .line 61
    move-result-object v4

    .line 62
    invoke-virtual {v4}, LsG0/d;->a()Ljava/lang/Integer;

    .line 63
    .line 64
    .line 65
    move-result-object v4

    .line 66
    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    .line 67
    .line 68
    .line 69
    move-result v4

    .line 70
    invoke-virtual {p0}, LsG0/c;->h()LsG0/d;

    .line 71
    .line 72
    .line 73
    move-result-object p0

    .line 74
    invoke-virtual {p0}, LsG0/d;->a()Ljava/lang/Integer;

    .line 75
    .line 76
    .line 77
    move-result-object p0

    .line 78
    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    .line 79
    .line 80
    .line 81
    move-result p0

    .line 82
    add-int/2addr v4, p0

    .line 83
    invoke-direct {v0, v1, v2, v3, v4}, LvG0/g;-><init>(Lorg/xbet/statistic/lastgames/domain/entities/DescriptionModel;III)V

    .line 84
    .line 85
    .line 86
    return-object v0

    .line 87
    :cond_2
    return-object v1
.end method

.method public static final b(LsG0/c;)LvG0/g;
    .locals 5

    .line 1
    invoke-virtual {p0}, LsG0/c;->g()LsG0/d;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-virtual {v0}, LsG0/d;->b()Ljava/lang/Integer;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object v0, v1

    .line 14
    :goto_0
    if-eqz v0, :cond_2

    .line 15
    .line 16
    invoke-virtual {p0}, LsG0/c;->h()LsG0/d;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    if-eqz v0, :cond_1

    .line 21
    .line 22
    invoke-virtual {v0}, LsG0/d;->b()Ljava/lang/Integer;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    goto :goto_1

    .line 27
    :cond_1
    move-object v0, v1

    .line 28
    :goto_1
    if-eqz v0, :cond_2

    .line 29
    .line 30
    new-instance v0, LvG0/g;

    .line 31
    .line 32
    sget-object v1, Lorg/xbet/statistic/lastgames/domain/entities/DescriptionModel;->RED_CARD:Lorg/xbet/statistic/lastgames/domain/entities/DescriptionModel;

    .line 33
    .line 34
    invoke-virtual {p0}, LsG0/c;->g()LsG0/d;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    invoke-virtual {v2}, LsG0/d;->b()Ljava/lang/Integer;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    .line 43
    .line 44
    .line 45
    move-result v2

    .line 46
    invoke-virtual {p0}, LsG0/c;->h()LsG0/d;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    invoke-virtual {v3}, LsG0/d;->b()Ljava/lang/Integer;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 55
    .line 56
    .line 57
    move-result v3

    .line 58
    invoke-virtual {p0}, LsG0/c;->g()LsG0/d;

    .line 59
    .line 60
    .line 61
    move-result-object v4

    .line 62
    invoke-virtual {v4}, LsG0/d;->b()Ljava/lang/Integer;

    .line 63
    .line 64
    .line 65
    move-result-object v4

    .line 66
    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    .line 67
    .line 68
    .line 69
    move-result v4

    .line 70
    invoke-virtual {p0}, LsG0/c;->h()LsG0/d;

    .line 71
    .line 72
    .line 73
    move-result-object p0

    .line 74
    invoke-virtual {p0}, LsG0/d;->b()Ljava/lang/Integer;

    .line 75
    .line 76
    .line 77
    move-result-object p0

    .line 78
    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    .line 79
    .line 80
    .line 81
    move-result p0

    .line 82
    add-int/2addr v4, p0

    .line 83
    invoke-direct {v0, v1, v2, v3, v4}, LvG0/g;-><init>(Lorg/xbet/statistic/lastgames/domain/entities/DescriptionModel;III)V

    .line 84
    .line 85
    .line 86
    return-object v0

    .line 87
    :cond_2
    return-object v1
.end method

.method public static final c(LsG0/c;)LvG0/g;
    .locals 5

    .line 1
    invoke-virtual {p0}, LsG0/c;->g()LsG0/d;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-virtual {v0}, LsG0/d;->c()Ljava/lang/Integer;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object v0, v1

    .line 14
    :goto_0
    if-eqz v0, :cond_2

    .line 15
    .line 16
    invoke-virtual {p0}, LsG0/c;->h()LsG0/d;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    if-eqz v0, :cond_1

    .line 21
    .line 22
    invoke-virtual {v0}, LsG0/d;->c()Ljava/lang/Integer;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    goto :goto_1

    .line 27
    :cond_1
    move-object v0, v1

    .line 28
    :goto_1
    if-eqz v0, :cond_2

    .line 29
    .line 30
    new-instance v0, LvG0/g;

    .line 31
    .line 32
    sget-object v1, Lorg/xbet/statistic/lastgames/domain/entities/DescriptionModel;->YELLOW_CARD:Lorg/xbet/statistic/lastgames/domain/entities/DescriptionModel;

    .line 33
    .line 34
    invoke-virtual {p0}, LsG0/c;->g()LsG0/d;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    invoke-virtual {v2}, LsG0/d;->c()Ljava/lang/Integer;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    .line 43
    .line 44
    .line 45
    move-result v2

    .line 46
    invoke-virtual {p0}, LsG0/c;->h()LsG0/d;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    invoke-virtual {v3}, LsG0/d;->c()Ljava/lang/Integer;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 55
    .line 56
    .line 57
    move-result v3

    .line 58
    invoke-virtual {p0}, LsG0/c;->g()LsG0/d;

    .line 59
    .line 60
    .line 61
    move-result-object v4

    .line 62
    invoke-virtual {v4}, LsG0/d;->c()Ljava/lang/Integer;

    .line 63
    .line 64
    .line 65
    move-result-object v4

    .line 66
    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    .line 67
    .line 68
    .line 69
    move-result v4

    .line 70
    invoke-virtual {p0}, LsG0/c;->h()LsG0/d;

    .line 71
    .line 72
    .line 73
    move-result-object p0

    .line 74
    invoke-virtual {p0}, LsG0/d;->c()Ljava/lang/Integer;

    .line 75
    .line 76
    .line 77
    move-result-object p0

    .line 78
    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    .line 79
    .line 80
    .line 81
    move-result p0

    .line 82
    add-int/2addr v4, p0

    .line 83
    invoke-direct {v0, v1, v2, v3, v4}, LvG0/g;-><init>(Lorg/xbet/statistic/lastgames/domain/entities/DescriptionModel;III)V

    .line 84
    .line 85
    .line 86
    return-object v0

    .line 87
    :cond_2
    return-object v1
.end method

.method public static final d(LsG0/c;)LvG0/h;
    .locals 9
    .param p0    # LsG0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LsG0/c;->a()Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    const/4 v0, 0x0

    .line 14
    :goto_0
    invoke-virtual {p0}, LsG0/c;->i()Ljava/lang/Integer;

    .line 15
    .line 16
    .line 17
    move-result-object v2

    .line 18
    if-eqz v2, :cond_1

    .line 19
    .line 20
    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    .line 21
    .line 22
    .line 23
    move-result v2

    .line 24
    goto :goto_1

    .line 25
    :cond_1
    const/4 v2, 0x0

    .line 26
    :goto_1
    invoke-virtual {p0}, LsG0/c;->j()Ljava/lang/Integer;

    .line 27
    .line 28
    .line 29
    move-result-object v3

    .line 30
    if-eqz v3, :cond_2

    .line 31
    .line 32
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 33
    .line 34
    .line 35
    move-result v3

    .line 36
    goto :goto_2

    .line 37
    :cond_2
    const/4 v3, 0x0

    .line 38
    :goto_2
    add-int/2addr v2, v3

    .line 39
    invoke-virtual {p0}, LsG0/c;->a()Ljava/lang/Integer;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    if-eqz v3, :cond_3

    .line 44
    .line 45
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 46
    .line 47
    .line 48
    move-result v3

    .line 49
    goto :goto_3

    .line 50
    :cond_3
    const/4 v3, 0x0

    .line 51
    :goto_3
    add-int/2addr v2, v3

    .line 52
    new-instance v3, LvG0/g;

    .line 53
    .line 54
    sget-object v4, Lorg/xbet/statistic/lastgames/domain/entities/DescriptionModel;->WIN:Lorg/xbet/statistic/lastgames/domain/entities/DescriptionModel;

    .line 55
    .line 56
    invoke-virtual {p0}, LsG0/c;->i()Ljava/lang/Integer;

    .line 57
    .line 58
    .line 59
    move-result-object v5

    .line 60
    if-eqz v5, :cond_4

    .line 61
    .line 62
    invoke-virtual {v5}, Ljava/lang/Integer;->intValue()I

    .line 63
    .line 64
    .line 65
    move-result v5

    .line 66
    goto :goto_4

    .line 67
    :cond_4
    const/4 v5, 0x0

    .line 68
    :goto_4
    invoke-virtual {p0}, LsG0/c;->j()Ljava/lang/Integer;

    .line 69
    .line 70
    .line 71
    move-result-object v6

    .line 72
    if-eqz v6, :cond_5

    .line 73
    .line 74
    invoke-virtual {v6}, Ljava/lang/Integer;->intValue()I

    .line 75
    .line 76
    .line 77
    move-result v6

    .line 78
    goto :goto_5

    .line 79
    :cond_5
    const/4 v6, 0x0

    .line 80
    :goto_5
    invoke-virtual {p0}, LsG0/c;->i()Ljava/lang/Integer;

    .line 81
    .line 82
    .line 83
    move-result-object v7

    .line 84
    if-eqz v7, :cond_6

    .line 85
    .line 86
    invoke-virtual {v7}, Ljava/lang/Integer;->intValue()I

    .line 87
    .line 88
    .line 89
    move-result v7

    .line 90
    goto :goto_6

    .line 91
    :cond_6
    const/4 v7, 0x0

    .line 92
    :goto_6
    invoke-virtual {p0}, LsG0/c;->j()Ljava/lang/Integer;

    .line 93
    .line 94
    .line 95
    move-result-object v8

    .line 96
    if-eqz v8, :cond_7

    .line 97
    .line 98
    invoke-virtual {v8}, Ljava/lang/Integer;->intValue()I

    .line 99
    .line 100
    .line 101
    move-result v8

    .line 102
    goto :goto_7

    .line 103
    :cond_7
    const/4 v8, 0x0

    .line 104
    :goto_7
    add-int/2addr v7, v8

    .line 105
    invoke-virtual {p0}, LsG0/c;->a()Ljava/lang/Integer;

    .line 106
    .line 107
    .line 108
    move-result-object v8

    .line 109
    if-eqz v8, :cond_8

    .line 110
    .line 111
    invoke-virtual {v8}, Ljava/lang/Integer;->intValue()I

    .line 112
    .line 113
    .line 114
    move-result v8

    .line 115
    goto :goto_8

    .line 116
    :cond_8
    const/4 v8, 0x0

    .line 117
    :goto_8
    add-int/2addr v7, v8

    .line 118
    invoke-direct {v3, v4, v5, v6, v7}, LvG0/g;-><init>(Lorg/xbet/statistic/lastgames/domain/entities/DescriptionModel;III)V

    .line 119
    .line 120
    .line 121
    invoke-static {p0}, LrG0/a;->a(LsG0/c;)LvG0/g;

    .line 122
    .line 123
    .line 124
    move-result-object v4

    .line 125
    invoke-static {p0}, LrG0/a;->c(LsG0/c;)LvG0/g;

    .line 126
    .line 127
    .line 128
    move-result-object v5

    .line 129
    invoke-static {p0}, LrG0/a;->b(LsG0/c;)LvG0/g;

    .line 130
    .line 131
    .line 132
    move-result-object p0

    .line 133
    const/4 v6, 0x4

    .line 134
    new-array v6, v6, [LvG0/g;

    .line 135
    .line 136
    aput-object v3, v6, v1

    .line 137
    .line 138
    const/4 v1, 0x1

    .line 139
    aput-object v4, v6, v1

    .line 140
    .line 141
    const/4 v1, 0x2

    .line 142
    aput-object v5, v6, v1

    .line 143
    .line 144
    const/4 v1, 0x3

    .line 145
    aput-object p0, v6, v1

    .line 146
    .line 147
    invoke-static {v6}, Lkotlin/collections/v;->s([Ljava/lang/Object;)Ljava/util/List;

    .line 148
    .line 149
    .line 150
    move-result-object p0

    .line 151
    new-instance v1, LvG0/h;

    .line 152
    .line 153
    invoke-direct {v1, v2, v0, p0}, LvG0/h;-><init>(IILjava/util/List;)V

    .line 154
    .line 155
    .line 156
    return-object v1
.end method
