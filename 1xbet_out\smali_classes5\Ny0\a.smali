.class public final LNy0/a;
.super LkY0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "LkY0/a<",
        "LOy0/b;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u000c\u0008\u0001\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B=\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u000c\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000c\u001a\u00020\u000b\u0012\u0006\u0010\r\u001a\u00020\t\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0010\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u0012\u0010\u0013R\u0017\u0010\n\u001a\u00020\t8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0014\u0010\u0015\u001a\u0004\u0008\u0016\u0010\u0017R\u0017\u0010\u000c\u001a\u00020\u000b8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0012\u0010\u0018\u001a\u0004\u0008\u0019\u0010\u001aR\u0017\u0010\r\u001a\u00020\t8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001b\u0010\u0015\u001a\u0004\u0008\u001c\u0010\u0017\u00a8\u0006\u001d"
    }
    d2 = {
        "LNy0/a;",
        "LkY0/a;",
        "LOy0/b;",
        "Landroidx/fragment/app/FragmentManager;",
        "fragmentManager",
        "Landroidx/lifecycle/Lifecycle;",
        "lifecycle",
        "",
        "items",
        "",
        "eventId",
        "",
        "sportId",
        "champId",
        "<init>",
        "(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;IJI)V",
        "position",
        "Landroidx/fragment/app/Fragment;",
        "p",
        "(I)Landroidx/fragment/app/Fragment;",
        "o",
        "I",
        "getEventId",
        "()I",
        "J",
        "getSportId",
        "()J",
        "q",
        "getChampId",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final r:I


# instance fields
.field public final o:I

.field public final p:J

.field public final q:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget v0, LkY0/a;->n:I

    .line 2
    .line 3
    sput v0, LNy0/a;->r:I

    .line 4
    .line 5
    return-void
.end method

.method public constructor <init>(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;IJI)V
    .locals 0
    .param p1    # Landroidx/fragment/app/FragmentManager;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroidx/lifecycle/Lifecycle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/fragment/app/FragmentManager;",
            "Landroidx/lifecycle/Lifecycle;",
            "Ljava/util/List<",
            "LOy0/b;",
            ">;IJI)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2, p3}, LkY0/a;-><init>(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    iput p4, p0, LNy0/a;->o:I

    .line 5
    .line 6
    iput-wide p5, p0, LNy0/a;->p:J

    .line 7
    .line 8
    iput p7, p0, LNy0/a;->q:I

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public p(I)Landroidx/fragment/app/Fragment;
    .locals 6
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0, p1}, LkY0/a;->I(I)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LOy0/b;

    .line 6
    .line 7
    invoke-virtual {p1}, LOy0/b;->f()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    sget-object p1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->b1:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$a;

    .line 12
    .line 13
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;

    .line 14
    .line 15
    iget v1, p0, LNy0/a;->o:I

    .line 16
    .line 17
    iget-wide v3, p0, LNy0/a;->p:J

    .line 18
    .line 19
    iget v5, p0, LNy0/a;->q:I

    .line 20
    .line 21
    invoke-direct/range {v0 .. v5}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;-><init>(ILjava/lang/String;JI)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p1, v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$a;->a(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    return-object p1
.end method
