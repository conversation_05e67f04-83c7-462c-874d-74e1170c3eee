.class public final Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_sport/eventcard/middle/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000p\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0010\r\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ+\u0010\u0011\u001a\u00020\u00102\u000c\u0010\r\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000b2\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u000bH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u001f\u0010\u0015\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u000c2\u0006\u0010\u0014\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0017\u0010\u0019\u001a\u00020\u00102\u0008\u0010\u0018\u001a\u0004\u0018\u00010\u0017\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u0017\u0010\u0019\u001a\u00020\u00102\u0008\u0008\u0001\u0010\u0018\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0019\u0010\u001bJ\u0017\u0010\u001c\u001a\u00020\u00102\u0008\u0010\u0018\u001a\u0004\u0018\u00010\u0017\u00a2\u0006\u0004\u0008\u001c\u0010\u001aJ\u0017\u0010\u001c\u001a\u00020\u00102\u0008\u0008\u0001\u0010\u0018\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001c\u0010\u001bJ\u0017\u0010\u001d\u001a\u00020\u00102\u0008\u0010\u0018\u001a\u0004\u0018\u00010\u0017\u00a2\u0006\u0004\u0008\u001d\u0010\u001aJ\u0017\u0010\u001d\u001a\u00020\u00102\u0008\u0008\u0001\u0010\u0018\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001d\u0010\u001bJ\u001b\u0010\u001e\u001a\u00020\u00102\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u000b\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u001b\u0010 \u001a\u00020\u00102\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u000b\u00a2\u0006\u0004\u0008 \u0010\u001fJ\u0015\u0010#\u001a\u00020\u00102\u0006\u0010\"\u001a\u00020!\u00a2\u0006\u0004\u0008#\u0010$J\u0017\u0010%\u001a\u00020\u00102\u0008\u0010\u0018\u001a\u0004\u0018\u00010\u0017\u00a2\u0006\u0004\u0008%\u0010\u001aJ\u0017\u0010%\u001a\u00020\u00102\u0008\u0008\u0001\u0010\u0018\u001a\u00020\u0007\u00a2\u0006\u0004\u0008%\u0010\u001bJ\u0017\u0010&\u001a\u00020\u00102\u0008\u0010\u0018\u001a\u0004\u0018\u00010\u0017\u00a2\u0006\u0004\u0008&\u0010\u001aJ\u0017\u0010&\u001a\u00020\u00102\u0008\u0008\u0001\u0010\u0018\u001a\u00020\u0007\u00a2\u0006\u0004\u0008&\u0010\u001bJ%\u0010)\u001a\u00020\u00102\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u000b2\u0008\u0008\u0002\u0010(\u001a\u00020\'\u00a2\u0006\u0004\u0008)\u0010*J\u0017\u0010+\u001a\u00020\u00102\u0008\u0010\u0018\u001a\u0004\u0018\u00010\u0017\u00a2\u0006\u0004\u0008+\u0010\u001aJ\u0017\u0010+\u001a\u00020\u00102\u0008\u0008\u0001\u0010\u0018\u001a\u00020\u0007\u00a2\u0006\u0004\u0008+\u0010\u001bJ\u0015\u0010.\u001a\u00020\u00102\u0006\u0010-\u001a\u00020,\u00a2\u0006\u0004\u0008.\u0010/J\u0015\u00102\u001a\u00020\u00102\u0006\u00101\u001a\u000200\u00a2\u0006\u0004\u00082\u00103J\u0015\u00105\u001a\u00020\u00102\u0006\u00104\u001a\u00020\'\u00a2\u0006\u0004\u00085\u00106J\u0015\u00109\u001a\u00020\u00102\u0006\u00108\u001a\u000207\u00a2\u0006\u0004\u00089\u0010:J\u0015\u0010<\u001a\u00020\u00102\u0006\u0010;\u001a\u00020\'\u00a2\u0006\u0004\u0008<\u00106J\r\u0010=\u001a\u00020\u0010\u00a2\u0006\u0004\u0008=\u0010>J\r\u0010?\u001a\u00020\u0010\u00a2\u0006\u0004\u0008?\u0010>J\u0017\u0010@\u001a\u00020\u00102\u0008\u0010\u0018\u001a\u0004\u0018\u00010\u0017\u00a2\u0006\u0004\u0008@\u0010\u001aJ\u0017\u0010@\u001a\u00020\u00102\u0008\u0008\u0001\u0010\u0018\u001a\u00020\u0007\u00a2\u0006\u0004\u0008@\u0010\u001bR\u0014\u0010D\u001a\u00020A8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010CR\u001a\u0010G\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000b8BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\u0008E\u0010FR\u001a\u0010I\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000b8BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\u0008H\u0010FR\u001a\u0010K\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000b8BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\u0008J\u0010F\u00a8\u0006L"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Lorg/xbet/uikit_sport/eventcard/middle/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "Landroid/widget/ImageView;",
        "cardViews",
        "LJ11/K;",
        "cards",
        "",
        "t",
        "(Ljava/util/List;Ljava/util/List;)V",
        "view",
        "card",
        "s",
        "(Landroid/widget/ImageView;LJ11/K;)V",
        "",
        "text",
        "setFirstPlayerName",
        "(Ljava/lang/CharSequence;)V",
        "(I)V",
        "setSecondPlayerName",
        "setInformation",
        "setFirstPlayerCards",
        "(Ljava/util/List;)V",
        "setSecondPlayerCards",
        "Lorg/xbet/uikit_sport/score/a;",
        "scoreModel",
        "setScoreModel",
        "(Lorg/xbet/uikit_sport/score/a;)V",
        "setFirstPlayerCombination",
        "setSecondPlayerCombination",
        "",
        "showShirts",
        "setDealerCards",
        "(Ljava/util/List;Z)V",
        "setBottomInfo",
        "Lorg/xbet/uikit/components/timer/TimerType;",
        "timerType",
        "setTimerType",
        "(Lorg/xbet/uikit/components/timer/TimerType;)V",
        "",
        "milliSeconds",
        "setTime",
        "(J)V",
        "visible",
        "setTimerVisibility",
        "(Z)V",
        "Lorg/xbet/uikit/components/timer/Timer$TimeDirection;",
        "timeDirection",
        "setTimeDirection",
        "(Lorg/xbet/uikit/components/timer/Timer$TimeDirection;)V",
        "hideAfterFinished",
        "setHideAfterFinished",
        "u",
        "()V",
        "v",
        "setCaption",
        "LC31/u;",
        "a",
        "LC31/u;",
        "binding",
        "getDealerCardViews",
        "()Ljava/util/List;",
        "dealerCardViews",
        "getFirstPlayerCardViews",
        "firstPlayerCardViews",
        "getSecondPlayerCardViews",
        "secondPlayerCardViews",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:LC31/u;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    invoke-static {p1, p0}, LC31/u;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/u;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    const/4 p1, 0x0

    .line 7
    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutDirection(I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->eventCardMiddleCyberPokerStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method private final getDealerCardViews()Ljava/util/List;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Landroid/widget/ImageView;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    .line 2
    .line 3
    iget-object v1, v0, LC31/u;->f:Landroid/widget/ImageView;

    .line 4
    .line 5
    iget-object v2, v0, LC31/u;->h:Landroid/widget/ImageView;

    .line 6
    .line 7
    iget-object v3, v0, LC31/u;->i:Landroid/widget/ImageView;

    .line 8
    .line 9
    iget-object v4, v0, LC31/u;->g:Landroid/widget/ImageView;

    .line 10
    .line 11
    iget-object v0, v0, LC31/u;->e:Landroid/widget/ImageView;

    .line 12
    .line 13
    const/4 v5, 0x5

    .line 14
    new-array v5, v5, [Landroid/widget/ImageView;

    .line 15
    .line 16
    const/4 v6, 0x0

    .line 17
    aput-object v1, v5, v6

    .line 18
    .line 19
    const/4 v1, 0x1

    .line 20
    aput-object v2, v5, v1

    .line 21
    .line 22
    const/4 v1, 0x2

    .line 23
    aput-object v3, v5, v1

    .line 24
    .line 25
    const/4 v1, 0x3

    .line 26
    aput-object v4, v5, v1

    .line 27
    .line 28
    const/4 v1, 0x4

    .line 29
    aput-object v0, v5, v1

    .line 30
    .line 31
    invoke-static {v5}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    return-object v0
.end method

.method private final getFirstPlayerCardViews()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Landroid/widget/ImageView;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    .line 2
    .line 3
    iget-object v1, v0, LC31/u;->k:Landroid/widget/ImageView;

    .line 4
    .line 5
    iget-object v2, v0, LC31/u;->m:Landroid/widget/ImageView;

    .line 6
    .line 7
    iget-object v0, v0, LC31/u;->n:Landroid/widget/ImageView;

    .line 8
    .line 9
    const/4 v3, 0x3

    .line 10
    new-array v3, v3, [Landroid/widget/ImageView;

    .line 11
    .line 12
    const/4 v4, 0x0

    .line 13
    aput-object v1, v3, v4

    .line 14
    .line 15
    const/4 v1, 0x1

    .line 16
    aput-object v2, v3, v1

    .line 17
    .line 18
    const/4 v1, 0x2

    .line 19
    aput-object v0, v3, v1

    .line 20
    .line 21
    invoke-static {v3}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    return-object v0
.end method

.method private final getSecondPlayerCardViews()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Landroid/widget/ImageView;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    .line 2
    .line 3
    iget-object v1, v0, LC31/u;->r:Landroid/widget/ImageView;

    .line 4
    .line 5
    iget-object v2, v0, LC31/u;->t:Landroid/widget/ImageView;

    .line 6
    .line 7
    iget-object v0, v0, LC31/u;->u:Landroid/widget/ImageView;

    .line 8
    .line 9
    const/4 v3, 0x3

    .line 10
    new-array v3, v3, [Landroid/widget/ImageView;

    .line 11
    .line 12
    const/4 v4, 0x0

    .line 13
    aput-object v1, v3, v4

    .line 14
    .line 15
    const/4 v1, 0x1

    .line 16
    aput-object v2, v3, v1

    .line 17
    .line 18
    const/4 v1, 0x2

    .line 19
    aput-object v0, v3, v1

    .line 20
    .line 21
    invoke-static {v3}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    return-object v0
.end method

.method public static synthetic setDealerCards$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;Ljava/util/List;ZILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x2

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    const/4 p2, 0x0

    .line 6
    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->setDealerCards(Ljava/util/List;Z)V

    .line 7
    .line 8
    .line 9
    return-void
.end method


# virtual methods
.method public final s(Landroid/widget/ImageView;LJ11/K;)V
    .locals 0

    .line 1
    invoke-virtual {p2}, LJ11/K;->a()I

    .line 2
    .line 3
    .line 4
    move-result p2

    .line 5
    invoke-virtual {p1, p2}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setBottomInfo(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->setBottomInfo(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setBottomInfo(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    iget-object v0, v0, LC31/u;->b:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/4 v1, 0x4

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    iget-object v0, v0, LC31/u;->b:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setCaption(I)V
    .locals 1

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->setCaption(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setCaption(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    iget-object v0, v0, LC31/u;->c:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    .line 2
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 3
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setDealerCards(Ljava/util/List;Z)V
    .locals 6
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LJ11/K;",
            ">;Z)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->getDealerCardViews()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    const/4 v1, 0x0

    .line 10
    const/4 v2, 0x0

    .line 11
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 12
    .line 13
    .line 14
    move-result v3

    .line 15
    if-eqz v3, :cond_3

    .line 16
    .line 17
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    add-int/lit8 v4, v2, 0x1

    .line 22
    .line 23
    if-gez v2, :cond_0

    .line 24
    .line 25
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 26
    .line 27
    .line 28
    :cond_0
    check-cast v3, Landroid/widget/ImageView;

    .line 29
    .line 30
    invoke-static {p1}, Lkotlin/collections/v;->p(Ljava/util/List;)I

    .line 31
    .line 32
    .line 33
    move-result v5

    .line 34
    if-gt v2, v5, :cond_1

    .line 35
    .line 36
    invoke-interface {p1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 37
    .line 38
    .line 39
    move-result-object v2

    .line 40
    check-cast v2, LJ11/K;

    .line 41
    .line 42
    invoke-virtual {v2}, LJ11/K;->a()I

    .line 43
    .line 44
    .line 45
    move-result v2

    .line 46
    invoke-virtual {v3, v2}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {v3, v1}, Landroid/view/View;->setVisibility(I)V

    .line 50
    .line 51
    .line 52
    goto :goto_2

    .line 53
    :cond_1
    sget v2, LlZ0/h;->ic_card_shirt:I

    .line 54
    .line 55
    invoke-virtual {v3, v2}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 56
    .line 57
    .line 58
    if-eqz p2, :cond_2

    .line 59
    .line 60
    const/4 v2, 0x0

    .line 61
    goto :goto_1

    .line 62
    :cond_2
    const/16 v2, 0x8

    .line 63
    .line 64
    :goto_1
    invoke-virtual {v3, v2}, Landroid/view/View;->setVisibility(I)V

    .line 65
    .line 66
    .line 67
    :goto_2
    move v2, v4

    .line 68
    goto :goto_0

    .line 69
    :cond_3
    return-void
.end method

.method public final setFirstPlayerCards(Ljava/util/List;)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LJ11/K;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->getFirstPlayerCardViews()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0, v0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->t(Ljava/util/List;Ljava/util/List;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setFirstPlayerCombination(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->setFirstPlayerCombination(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setFirstPlayerCombination(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    iget-object v0, v0, LC31/u;->j:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/4 v1, 0x4

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    iget-object v0, v0, LC31/u;->j:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setFirstPlayerName(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->setFirstPlayerName(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setFirstPlayerName(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    iget-object v0, v0, LC31/u;->l:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setHideAfterFinished(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    .line 2
    .line 3
    iget-object v0, v0, LC31/u;->v:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/timer/Timer;->setHideAfterFinished(Z)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setInformation(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->setInformation(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setInformation(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    iget-object v0, v0, LC31/u;->o:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    iget-object v0, v0, LC31/u;->o:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setScoreModel(Lorg/xbet/uikit_sport/score/a;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit_sport/score/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    .line 2
    .line 3
    iget-object v0, v0, LC31/u;->p:Lorg/xbet/uikit_sport/score/SportScore;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/score/SportScore;->setScore(Lorg/xbet/uikit_sport/score/a;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setSecondPlayerCards(Ljava/util/List;)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LJ11/K;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->getSecondPlayerCardViews()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0, v0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->t(Ljava/util/List;Ljava/util/List;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setSecondPlayerCombination(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->setSecondPlayerCombination(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSecondPlayerCombination(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    iget-object v0, v0, LC31/u;->q:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/4 v1, 0x4

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    iget-object v0, v0, LC31/u;->q:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSecondPlayerName(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->setSecondPlayerName(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSecondPlayerName(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    iget-object v0, v0, LC31/u;->s:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTime(J)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    .line 2
    .line 3
    iget-object v0, v0, LC31/u;->v:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit/components/timer/Timer;->setTime(J)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setTimeDirection(Lorg/xbet/uikit/components/timer/Timer$TimeDirection;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit/components/timer/Timer$TimeDirection;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    .line 2
    .line 3
    iget-object v0, v0, LC31/u;->v:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/timer/Timer;->setTimeDirection(Lorg/xbet/uikit/components/timer/Timer$TimeDirection;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setTimerType(Lorg/xbet/uikit/components/timer/TimerType;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit/components/timer/TimerType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    .line 2
    .line 3
    iget-object v0, v0, LC31/u;->v:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/timer/Timer;->setTimerType(Lorg/xbet/uikit/components/timer/TimerType;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setTimerVisibility(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    .line 2
    .line 3
    iget-object v0, v0, LC31/u;->v:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    const/4 p1, 0x0

    .line 8
    goto :goto_0

    .line 9
    :cond_0
    const/16 p1, 0x8

    .line 10
    .line 11
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final t(Ljava/util/List;Ljava/util/List;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Landroid/widget/ImageView;",
            ">;",
            "Ljava/util/List<",
            "+",
            "LJ11/K;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    const/4 v0, 0x0

    .line 6
    const/4 v1, 0x0

    .line 7
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    if-eqz v2, :cond_4

    .line 12
    .line 13
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    add-int/lit8 v3, v1, 0x1

    .line 18
    .line 19
    if-gez v1, :cond_0

    .line 20
    .line 21
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 22
    .line 23
    .line 24
    :cond_0
    check-cast v2, Landroid/widget/ImageView;

    .line 25
    .line 26
    invoke-interface {p2}, Ljava/util/List;->size()I

    .line 27
    .line 28
    .line 29
    move-result v4

    .line 30
    if-ge v1, v4, :cond_1

    .line 31
    .line 32
    invoke-interface {p2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v4

    .line 36
    check-cast v4, LJ11/K;

    .line 37
    .line 38
    invoke-virtual {p0, v2, v4}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->s(Landroid/widget/ImageView;LJ11/K;)V

    .line 39
    .line 40
    .line 41
    :cond_1
    invoke-interface {p2}, Ljava/util/List;->size()I

    .line 42
    .line 43
    .line 44
    move-result v4

    .line 45
    if-ge v1, v4, :cond_2

    .line 46
    .line 47
    const/4 v1, 0x1

    .line 48
    goto :goto_1

    .line 49
    :cond_2
    const/4 v1, 0x0

    .line 50
    :goto_1
    if-eqz v1, :cond_3

    .line 51
    .line 52
    const/4 v1, 0x0

    .line 53
    goto :goto_2

    .line 54
    :cond_3
    const/16 v1, 0x8

    .line 55
    .line 56
    :goto_2
    invoke-virtual {v2, v1}, Landroid/view/View;->setVisibility(I)V

    .line 57
    .line 58
    .line 59
    move v1, v3

    .line 60
    goto :goto_0

    .line 61
    :cond_4
    return-void
.end method

.method public final u()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    .line 2
    .line 3
    iget-object v0, v0, LC31/u;->v:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/components/timer/Timer;->t()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final v()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyberPoker;->a:LC31/u;

    .line 2
    .line 3
    iget-object v0, v0, LC31/u;->v:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/components/timer/Timer;->u()V

    .line 6
    .line 7
    .line 8
    return-void
.end method
