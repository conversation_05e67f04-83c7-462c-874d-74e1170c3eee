.class public interface abstract LH1/h;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract r(Ljava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ls1/a;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract v(Ls1/b;)V
.end method
