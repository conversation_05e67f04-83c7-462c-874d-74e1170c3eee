.class public final Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;
.super Lorg/xbet/ui_common/paging/BasePagingSource;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/xbet/ui_common/paging/BasePagingSource<",
        "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;",
        "LL91/d;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0010\u0003\n\u0002\u0008\u0008\u0008\u0000\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ%\u0010\u000c\u001a\u0004\u0018\u00010\u00022\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\nH\u0016\u00a2\u0006\u0004\u0008\u000c\u0010\rJ8\u0010\u0012\u001a\u001c\u0012\u0006\u0012\u0004\u0018\u00010\u0002\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u00110\u00102\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u000eH\u0096@\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u001f\u0010\u0016\u001a\u00020\u0015*\u0004\u0018\u00010\u00022\u0008\u0010\u0014\u001a\u0004\u0018\u00010\u0002H\u0016\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J#\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u00112\u0006\u0010\u0019\u001a\u00020\u0018H\u0016\u00a2\u0006\u0004\u0008\u001a\u0010\u001bR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010\u001dR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001f\u00a8\u0006 "
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;",
        "Lorg/xbet/ui_common/paging/BasePagingSource;",
        "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;",
        "LL91/d;",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "tokenRefresher",
        "Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;",
        "aggregatorRemoteDataSource",
        "<init>",
        "(Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;)V",
        "Landroidx/paging/M;",
        "state",
        "n",
        "(Landroidx/paging/M;)Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;",
        "Landroidx/paging/PagingSource$a;",
        "params",
        "Lkotlin/Pair;",
        "Landroidx/paging/PagingSource$b;",
        "l",
        "(Landroidx/paging/PagingSource$a;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "nextKey",
        "",
        "o",
        "(Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;)Z",
        "",
        "throwable",
        "j",
        "(Ljava/lang/Throwable;)Landroidx/paging/PagingSource$b;",
        "b",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "c",
        "Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;)V
    .locals 0
    .param p1    # Lcom/xbet/onexuser/domain/managers/TokenRefresher;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/paging/BasePagingSource;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;->b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;->c:Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 7
    .line 8
    return-void
.end method

.method public static final synthetic m(Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;)Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;->c:Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public bridge synthetic e(Landroidx/paging/M;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;->n(Landroidx/paging/M;)Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public j(Ljava/lang/Throwable;)Landroidx/paging/PagingSource$b;
    .locals 1
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Throwable;",
            ")",
            "Landroidx/paging/PagingSource$b<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;",
            "LL91/d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Landroidx/paging/PagingSource$b$a;

    .line 2
    .line 3
    invoke-direct {v0, p1}, Landroidx/paging/PagingSource$b$a;-><init>(Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public bridge synthetic k(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;

    .line 2
    .line 3
    check-cast p2, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;->o(Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public l(Landroidx/paging/PagingSource$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 11
    .param p1    # Landroidx/paging/PagingSource$a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/paging/PagingSource$a<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Pair<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;",
            "+",
            "Landroidx/paging/PagingSource$b<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;",
            "LL91/d;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x1

    .line 35
    const/4 v5, 0x0

    .line 36
    if-eqz v2, :cond_3

    .line 37
    .line 38
    if-eq v2, v4, :cond_2

    .line 39
    .line 40
    if-ne v2, v3, :cond_1

    .line 41
    .line 42
    iget p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;->I$0:I

    .line 43
    .line 44
    iget-object v0, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 45
    .line 46
    check-cast v0, Landroidx/paging/PagingSource$a;

    .line 47
    .line 48
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 49
    .line 50
    .line 51
    move-object v10, p2

    .line 52
    move p2, p1

    .line 53
    move-object p1, v0

    .line 54
    move-object v0, v10

    .line 55
    goto/16 :goto_5

    .line 56
    .line 57
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 58
    .line 59
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 60
    .line 61
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 62
    .line 63
    .line 64
    throw p1

    .line 65
    :cond_2
    iget p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;->I$0:I

    .line 66
    .line 67
    iget-object v0, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 68
    .line 69
    check-cast v0, Landroidx/paging/PagingSource$a;

    .line 70
    .line 71
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 72
    .line 73
    .line 74
    move-object v10, p2

    .line 75
    move p2, p1

    .line 76
    move-object p1, v0

    .line 77
    move-object v0, v10

    .line 78
    goto :goto_2

    .line 79
    :cond_3
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 80
    .line 81
    .line 82
    invoke-virtual {p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 83
    .line 84
    .line 85
    move-result-object p2

    .line 86
    check-cast p2, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;

    .line 87
    .line 88
    if-eqz p2, :cond_4

    .line 89
    .line 90
    invoke-virtual {p2}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;->b()I

    .line 91
    .line 92
    .line 93
    move-result p2

    .line 94
    goto :goto_1

    .line 95
    :cond_4
    const/4 p2, 0x0

    .line 96
    :goto_1
    invoke-virtual {p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 97
    .line 98
    .line 99
    move-result-object v2

    .line 100
    check-cast v2, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;

    .line 101
    .line 102
    if-eqz v2, :cond_7

    .line 103
    .line 104
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;->c()Ljava/lang/Long;

    .line 105
    .line 106
    .line 107
    move-result-object v2

    .line 108
    sget-object v6, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 109
    .line 110
    invoke-virtual {v6}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 111
    .line 112
    .line 113
    move-result-wide v6

    .line 114
    if-nez v2, :cond_5

    .line 115
    .line 116
    goto :goto_3

    .line 117
    :cond_5
    invoke-virtual {v2}, Ljava/lang/Long;->longValue()J

    .line 118
    .line 119
    .line 120
    move-result-wide v8

    .line 121
    cmp-long v2, v8, v6

    .line 122
    .line 123
    if-nez v2, :cond_7

    .line 124
    .line 125
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;->b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 126
    .line 127
    new-instance v3, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;

    .line 128
    .line 129
    invoke-direct {v3, p0, p1, p2, v5}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;Landroidx/paging/PagingSource$a;ILkotlin/coroutines/e;)V

    .line 130
    .line 131
    .line 132
    iput-object p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 133
    .line 134
    iput p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;->I$0:I

    .line 135
    .line 136
    iput v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;->label:I

    .line 137
    .line 138
    invoke-virtual {v2, v3, v0}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 139
    .line 140
    .line 141
    move-result-object v0

    .line 142
    if-ne v0, v1, :cond_6

    .line 143
    .line 144
    goto :goto_4

    .line 145
    :cond_6
    :goto_2
    check-cast v0, Ljava/util/List;

    .line 146
    .line 147
    goto :goto_6

    .line 148
    :cond_7
    :goto_3
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;->b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 149
    .line 150
    new-instance v4, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$2;

    .line 151
    .line 152
    invoke-direct {v4, p0, p1, p2, v5}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$games$2;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;Landroidx/paging/PagingSource$a;ILkotlin/coroutines/e;)V

    .line 153
    .line 154
    .line 155
    iput-object p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 156
    .line 157
    iput p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;->I$0:I

    .line 158
    .line 159
    iput v3, v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource$loadNextPage$1;->label:I

    .line 160
    .line 161
    invoke-virtual {v2, v4, v0}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 162
    .line 163
    .line 164
    move-result-object v0

    .line 165
    if-ne v0, v1, :cond_8

    .line 166
    .line 167
    :goto_4
    return-object v1

    .line 168
    :cond_8
    :goto_5
    check-cast v0, Ljava/util/List;

    .line 169
    .line 170
    :goto_6
    if-eqz v0, :cond_d

    .line 171
    .line 172
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 173
    .line 174
    .line 175
    move-result v1

    .line 176
    if-eqz v1, :cond_9

    .line 177
    .line 178
    goto :goto_8

    .line 179
    :cond_9
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;

    .line 180
    .line 181
    invoke-virtual {p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 182
    .line 183
    .line 184
    move-result-object v2

    .line 185
    check-cast v2, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;

    .line 186
    .line 187
    if-eqz v2, :cond_a

    .line 188
    .line 189
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;->c()Ljava/lang/Long;

    .line 190
    .line 191
    .line 192
    move-result-object v2

    .line 193
    if-eqz v2, :cond_a

    .line 194
    .line 195
    invoke-virtual {v2}, Ljava/lang/Long;->longValue()J

    .line 196
    .line 197
    .line 198
    move-result-wide v2

    .line 199
    goto :goto_7

    .line 200
    :cond_a
    sget-object v2, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 201
    .line 202
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 203
    .line 204
    .line 205
    move-result-wide v2

    .line 206
    :goto_7
    invoke-static {v2, v3}, LHc/a;->f(J)Ljava/lang/Long;

    .line 207
    .line 208
    .line 209
    move-result-object v2

    .line 210
    add-int/lit8 v3, p2, 0x1e

    .line 211
    .line 212
    invoke-virtual {p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 213
    .line 214
    .line 215
    move-result-object v4

    .line 216
    check-cast v4, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;

    .line 217
    .line 218
    if-eqz v4, :cond_b

    .line 219
    .line 220
    invoke-virtual {v4}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;->a()Ljava/lang/String;

    .line 221
    .line 222
    .line 223
    move-result-object v4

    .line 224
    if-nez v4, :cond_c

    .line 225
    .line 226
    :cond_b
    const-string v4, ""

    .line 227
    .line 228
    :cond_c
    invoke-direct {v1, v2, v3, v4}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;-><init>(Ljava/lang/Long;ILjava/lang/String;)V

    .line 229
    .line 230
    .line 231
    goto :goto_9

    .line 232
    :cond_d
    :goto_8
    move-object v1, v5

    .line 233
    :goto_9
    new-instance v2, Landroidx/paging/PagingSource$b$c;

    .line 234
    .line 235
    if-eqz v0, :cond_e

    .line 236
    .line 237
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->u0(Ljava/lang/Iterable;)Ljava/util/List;

    .line 238
    .line 239
    .line 240
    move-result-object v0

    .line 241
    goto :goto_a

    .line 242
    :cond_e
    move-object v0, v5

    .line 243
    :goto_a
    if-nez v0, :cond_f

    .line 244
    .line 245
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 246
    .line 247
    .line 248
    move-result-object v0

    .line 249
    :cond_f
    if-nez p2, :cond_10

    .line 250
    .line 251
    goto :goto_b

    .line 252
    :cond_10
    invoke-virtual {p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 253
    .line 254
    .line 255
    move-result-object p1

    .line 256
    move-object v5, p1

    .line 257
    check-cast v5, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;

    .line 258
    .line 259
    :goto_b
    invoke-direct {v2, v0, v5, v1}, Landroidx/paging/PagingSource$b$c;-><init>(Ljava/util/List;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 260
    .line 261
    .line 262
    invoke-static {v1, v2}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 263
    .line 264
    .line 265
    move-result-object p1

    .line 266
    return-object p1
.end method

.method public n(Landroidx/paging/M;)Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;
    .locals 5
    .param p1    # Landroidx/paging/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/paging/M<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;",
            "LL91/d;",
            ">;)",
            "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, Landroidx/paging/M;->d()Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_a

    .line 7
    .line 8
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    invoke-virtual {p1, v0}, Landroidx/paging/M;->c(I)Landroidx/paging/PagingSource$b$c;

    .line 13
    .line 14
    .line 15
    move-result-object v2

    .line 16
    if-eqz v2, :cond_0

    .line 17
    .line 18
    invoke-virtual {v2}, Landroidx/paging/PagingSource$b$c;->i()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    check-cast v2, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_0
    move-object v2, v1

    .line 26
    :goto_0
    invoke-virtual {p1, v0}, Landroidx/paging/M;->c(I)Landroidx/paging/PagingSource$b$c;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    if-eqz v0, :cond_1

    .line 31
    .line 32
    invoke-virtual {v0}, Landroidx/paging/PagingSource$b$c;->f()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    check-cast v0, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;

    .line 37
    .line 38
    goto :goto_1

    .line 39
    :cond_1
    move-object v0, v1

    .line 40
    :goto_1
    new-instance v3, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;

    .line 41
    .line 42
    if-eqz v2, :cond_3

    .line 43
    .line 44
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;->c()Ljava/lang/Long;

    .line 45
    .line 46
    .line 47
    move-result-object v4

    .line 48
    if-nez v4, :cond_2

    .line 49
    .line 50
    goto :goto_2

    .line 51
    :cond_2
    move-object v1, v4

    .line 52
    goto :goto_3

    .line 53
    :cond_3
    :goto_2
    if-eqz v0, :cond_4

    .line 54
    .line 55
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;->c()Ljava/lang/Long;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    :cond_4
    :goto_3
    if-eqz v2, :cond_5

    .line 60
    .line 61
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;->b()I

    .line 62
    .line 63
    .line 64
    move-result v4

    .line 65
    invoke-virtual {p1}, Landroidx/paging/M;->e()Landroidx/paging/C;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    iget p1, p1, Landroidx/paging/C;->a:I

    .line 70
    .line 71
    add-int/2addr v4, p1

    .line 72
    goto :goto_4

    .line 73
    :cond_5
    if-eqz v0, :cond_6

    .line 74
    .line 75
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;->b()I

    .line 76
    .line 77
    .line 78
    move-result v4

    .line 79
    invoke-virtual {p1}, Landroidx/paging/M;->e()Landroidx/paging/C;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    iget p1, p1, Landroidx/paging/C;->a:I

    .line 84
    .line 85
    sub-int/2addr v4, p1

    .line 86
    goto :goto_4

    .line 87
    :cond_6
    const/4 v4, 0x0

    .line 88
    :goto_4
    if-eqz v2, :cond_7

    .line 89
    .line 90
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;->a()Ljava/lang/String;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    if-nez p1, :cond_9

    .line 95
    .line 96
    :cond_7
    if-eqz v0, :cond_8

    .line 97
    .line 98
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;->a()Ljava/lang/String;

    .line 99
    .line 100
    .line 101
    move-result-object p1

    .line 102
    goto :goto_5

    .line 103
    :cond_8
    const-string p1, ""

    .line 104
    .line 105
    :cond_9
    :goto_5
    invoke-direct {v3, v1, v4, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;-><init>(Ljava/lang/Long;ILjava/lang/String;)V

    .line 106
    .line 107
    .line 108
    return-object v3

    .line 109
    :cond_a
    return-object v1
.end method

.method public o(Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;)Z
    .locals 0

    .line 1
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method
