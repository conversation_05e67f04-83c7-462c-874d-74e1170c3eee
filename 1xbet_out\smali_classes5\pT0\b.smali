.class public final LpT0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LmT0/c;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\r\u0008\u0000\u0018\u00002\u00020\u0001B1\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0010\u0010\u000f\u001a\u00020\u000eH\u0096\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0012R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0014R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0016R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010\u0018R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u001a\u00a8\u0006\u001b"
    }
    d2 = {
        "LpT0/b;",
        "LmT0/c;",
        "LqT0/f;",
        "updateWorkerUseCase",
        "LqT0/d;",
        "isTimeTableEnabledUseCase",
        "LqT0/b;",
        "getOnTimeUseCase",
        "LqT0/a;",
        "getOffTimeUseCase",
        "Li8/m;",
        "getThemeUseCase",
        "<init>",
        "(LqT0/f;LqT0/d;LqT0/b;LqT0/a;Li8/m;)V",
        "",
        "invoke",
        "()V",
        "a",
        "LqT0/f;",
        "b",
        "LqT0/d;",
        "c",
        "LqT0/b;",
        "d",
        "LqT0/a;",
        "e",
        "Li8/m;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LqT0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LqT0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LqT0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:LqT0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Li8/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LqT0/f;LqT0/d;LqT0/b;LqT0/a;Li8/m;)V
    .locals 0
    .param p1    # LqT0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LqT0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LqT0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LqT0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Li8/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LpT0/b;->a:LqT0/f;

    .line 5
    .line 6
    iput-object p2, p0, LpT0/b;->b:LqT0/d;

    .line 7
    .line 8
    iput-object p3, p0, LpT0/b;->c:LqT0/b;

    .line 9
    .line 10
    iput-object p4, p0, LpT0/b;->d:LqT0/a;

    .line 11
    .line 12
    iput-object p5, p0, LpT0/b;->e:Li8/m;

    .line 13
    .line 14
    return-void
.end method


# virtual methods
.method public invoke()V
    .locals 7

    .line 1
    iget-object v0, p0, LpT0/b;->b:LqT0/d;

    .line 2
    .line 3
    invoke-virtual {v0}, LqT0/d;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v5

    .line 7
    iget-object v0, p0, LpT0/b;->e:Li8/m;

    .line 8
    .line 9
    invoke-interface {v0}, Li8/m;->invoke()Lcom/xbet/onexcore/themes/Theme;

    .line 10
    .line 11
    .line 12
    move-result-object v6

    .line 13
    iget-object v1, p0, LpT0/b;->a:LqT0/f;

    .line 14
    .line 15
    iget-object v0, p0, LpT0/b;->c:LqT0/b;

    .line 16
    .line 17
    invoke-virtual {v0}, LqT0/b;->b()J

    .line 18
    .line 19
    .line 20
    move-result-wide v3

    .line 21
    const-string v2, "theme_on_worker"

    .line 22
    .line 23
    invoke-virtual/range {v1 .. v6}, LqT0/f;->a(Ljava/lang/String;JZLcom/xbet/onexcore/themes/Theme;)V

    .line 24
    .line 25
    .line 26
    iget-object v1, p0, LpT0/b;->a:LqT0/f;

    .line 27
    .line 28
    iget-object v0, p0, LpT0/b;->d:LqT0/a;

    .line 29
    .line 30
    invoke-virtual {v0}, LqT0/a;->b()J

    .line 31
    .line 32
    .line 33
    move-result-wide v3

    .line 34
    const-string v2, "theme_off_worker"

    .line 35
    .line 36
    invoke-virtual/range {v1 .. v6}, LqT0/f;->a(Ljava/lang/String;JZLcom/xbet/onexcore/themes/Theme;)V

    .line 37
    .line 38
    .line 39
    return-void
.end method
