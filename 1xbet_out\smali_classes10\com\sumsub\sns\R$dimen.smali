.class public final Lcom/sumsub/sns/R$dimen;
.super Ljava/lang/Object;


# static fields
.field public static abc_action_bar_content_inset_material:I = 0x7f070000

.field public static abc_action_bar_content_inset_with_nav:I = 0x7f070001

.field public static abc_action_bar_default_height_material:I = 0x7f070002

.field public static abc_action_bar_default_padding_end_material:I = 0x7f070003

.field public static abc_action_bar_default_padding_start_material:I = 0x7f070004

.field public static abc_action_bar_elevation_material:I = 0x7f070005

.field public static abc_action_bar_icon_vertical_padding_material:I = 0x7f070006

.field public static abc_action_bar_overflow_padding_end_material:I = 0x7f070007

.field public static abc_action_bar_overflow_padding_start_material:I = 0x7f070008

.field public static abc_action_bar_stacked_max_height:I = 0x7f070009

.field public static abc_action_bar_stacked_tab_max_width:I = 0x7f07000a

.field public static abc_action_bar_subtitle_bottom_margin_material:I = 0x7f07000b

.field public static abc_action_bar_subtitle_top_margin_material:I = 0x7f07000c

.field public static abc_action_button_min_height_material:I = 0x7f07000d

.field public static abc_action_button_min_width_material:I = 0x7f07000e

.field public static abc_action_button_min_width_overflow_material:I = 0x7f07000f

.field public static abc_alert_dialog_button_bar_height:I = 0x7f070010

.field public static abc_alert_dialog_button_dimen:I = 0x7f070011

.field public static abc_button_inset_horizontal_material:I = 0x7f070012

.field public static abc_button_inset_vertical_material:I = 0x7f070013

.field public static abc_button_padding_horizontal_material:I = 0x7f070014

.field public static abc_button_padding_vertical_material:I = 0x7f070015

.field public static abc_cascading_menus_min_smallest_width:I = 0x7f070016

.field public static abc_config_prefDialogWidth:I = 0x7f070017

.field public static abc_control_corner_material:I = 0x7f070018

.field public static abc_control_inset_material:I = 0x7f070019

.field public static abc_control_padding_material:I = 0x7f07001a

.field public static abc_dialog_corner_radius_material:I = 0x7f07001b

.field public static abc_dialog_fixed_height_major:I = 0x7f07001c

.field public static abc_dialog_fixed_height_minor:I = 0x7f07001d

.field public static abc_dialog_fixed_width_major:I = 0x7f07001e

.field public static abc_dialog_fixed_width_minor:I = 0x7f07001f

.field public static abc_dialog_list_padding_bottom_no_buttons:I = 0x7f070020

.field public static abc_dialog_list_padding_top_no_title:I = 0x7f070021

.field public static abc_dialog_min_width_major:I = 0x7f070022

.field public static abc_dialog_min_width_minor:I = 0x7f070023

.field public static abc_dialog_padding_material:I = 0x7f070024

.field public static abc_dialog_padding_top_material:I = 0x7f070025

.field public static abc_dialog_title_divider_material:I = 0x7f070026

.field public static abc_disabled_alpha_material_dark:I = 0x7f070027

.field public static abc_disabled_alpha_material_light:I = 0x7f070028

.field public static abc_dropdownitem_icon_width:I = 0x7f070029

.field public static abc_dropdownitem_text_padding_left:I = 0x7f07002a

.field public static abc_dropdownitem_text_padding_right:I = 0x7f07002b

.field public static abc_edit_text_inset_bottom_material:I = 0x7f07002c

.field public static abc_edit_text_inset_horizontal_material:I = 0x7f07002d

.field public static abc_edit_text_inset_top_material:I = 0x7f07002e

.field public static abc_floating_window_z:I = 0x7f07002f

.field public static abc_list_item_height_large_material:I = 0x7f070030

.field public static abc_list_item_height_material:I = 0x7f070031

.field public static abc_list_item_height_small_material:I = 0x7f070032

.field public static abc_list_item_padding_horizontal_material:I = 0x7f070033

.field public static abc_panel_menu_list_width:I = 0x7f070034

.field public static abc_progress_bar_height_material:I = 0x7f070035

.field public static abc_search_view_preferred_height:I = 0x7f070036

.field public static abc_search_view_preferred_width:I = 0x7f070037

.field public static abc_seekbar_track_background_height_material:I = 0x7f070038

.field public static abc_seekbar_track_progress_height_material:I = 0x7f070039

.field public static abc_select_dialog_padding_start_material:I = 0x7f07003a

.field public static abc_star_big:I = 0x7f07003b

.field public static abc_star_medium:I = 0x7f07003c

.field public static abc_star_small:I = 0x7f07003d

.field public static abc_switch_padding:I = 0x7f07003e

.field public static abc_text_size_body_1_material:I = 0x7f07003f

.field public static abc_text_size_body_2_material:I = 0x7f070040

.field public static abc_text_size_button_material:I = 0x7f070041

.field public static abc_text_size_caption_material:I = 0x7f070042

.field public static abc_text_size_display_1_material:I = 0x7f070043

.field public static abc_text_size_display_2_material:I = 0x7f070044

.field public static abc_text_size_display_3_material:I = 0x7f070045

.field public static abc_text_size_display_4_material:I = 0x7f070046

.field public static abc_text_size_headline_material:I = 0x7f070047

.field public static abc_text_size_large_material:I = 0x7f070048

.field public static abc_text_size_medium_material:I = 0x7f070049

.field public static abc_text_size_menu_header_material:I = 0x7f07004a

.field public static abc_text_size_menu_material:I = 0x7f07004b

.field public static abc_text_size_small_material:I = 0x7f07004c

.field public static abc_text_size_subhead_material:I = 0x7f07004d

.field public static abc_text_size_subtitle_material_toolbar:I = 0x7f07004e

.field public static abc_text_size_title_material:I = 0x7f07004f

.field public static abc_text_size_title_material_toolbar:I = 0x7f070050

.field public static appcompat_dialog_background_inset:I = 0x7f07005f

.field public static cardview_compat_inset_shadow:I = 0x7f070082

.field public static cardview_default_elevation:I = 0x7f070083

.field public static cardview_default_radius:I = 0x7f070084

.field public static clock_face_margin_start:I = 0x7f070096

.field public static compat_button_inset_horizontal_material:I = 0x7f0700a2

.field public static compat_button_inset_vertical_material:I = 0x7f0700a3

.field public static compat_button_padding_horizontal_material:I = 0x7f0700a4

.field public static compat_button_padding_vertical_material:I = 0x7f0700a5

.field public static compat_control_corner_material:I = 0x7f0700a6

.field public static compat_notification_large_icon_max_height:I = 0x7f0700a7

.field public static compat_notification_large_icon_max_width:I = 0x7f0700a8

.field public static def_drawer_elevation:I = 0x7f0700f5

.field public static design_appbar_elevation:I = 0x7f0700f7

.field public static design_bottom_navigation_active_item_max_width:I = 0x7f0700f8

.field public static design_bottom_navigation_active_item_min_width:I = 0x7f0700f9

.field public static design_bottom_navigation_active_text_size:I = 0x7f0700fa

.field public static design_bottom_navigation_elevation:I = 0x7f0700fb

.field public static design_bottom_navigation_height:I = 0x7f0700fc

.field public static design_bottom_navigation_icon_size:I = 0x7f0700fd

.field public static design_bottom_navigation_item_max_width:I = 0x7f0700fe

.field public static design_bottom_navigation_item_min_width:I = 0x7f0700ff

.field public static design_bottom_navigation_label_padding:I = 0x7f070100

.field public static design_bottom_navigation_margin:I = 0x7f070101

.field public static design_bottom_navigation_shadow_height:I = 0x7f070102

.field public static design_bottom_navigation_text_size:I = 0x7f070103

.field public static design_bottom_sheet_elevation:I = 0x7f070104

.field public static design_bottom_sheet_modal_elevation:I = 0x7f070105

.field public static design_bottom_sheet_peek_height_min:I = 0x7f070106

.field public static design_fab_border_width:I = 0x7f070107

.field public static design_fab_elevation:I = 0x7f070108

.field public static design_fab_image_size:I = 0x7f070109

.field public static design_fab_size_mini:I = 0x7f07010a

.field public static design_fab_size_normal:I = 0x7f07010b

.field public static design_fab_translation_z_hovered_focused:I = 0x7f07010c

.field public static design_fab_translation_z_pressed:I = 0x7f07010d

.field public static design_navigation_elevation:I = 0x7f07010e

.field public static design_navigation_icon_padding:I = 0x7f07010f

.field public static design_navigation_icon_size:I = 0x7f070110

.field public static design_navigation_item_horizontal_padding:I = 0x7f070111

.field public static design_navigation_item_icon_padding:I = 0x7f070112

.field public static design_navigation_item_vertical_padding:I = 0x7f070113

.field public static design_navigation_max_width:I = 0x7f070114

.field public static design_navigation_padding_bottom:I = 0x7f070115

.field public static design_navigation_separator_vertical_padding:I = 0x7f070116

.field public static design_snackbar_action_inline_max_width:I = 0x7f070117

.field public static design_snackbar_action_text_color_alpha:I = 0x7f070118

.field public static design_snackbar_background_corner_radius:I = 0x7f070119

.field public static design_snackbar_elevation:I = 0x7f07011a

.field public static design_snackbar_extra_spacing_horizontal:I = 0x7f07011b

.field public static design_snackbar_max_width:I = 0x7f07011c

.field public static design_snackbar_min_width:I = 0x7f07011d

.field public static design_snackbar_padding_horizontal:I = 0x7f07011e

.field public static design_snackbar_padding_vertical:I = 0x7f07011f

.field public static design_snackbar_padding_vertical_2lines:I = 0x7f070120

.field public static design_snackbar_text_size:I = 0x7f070121

.field public static design_tab_max_width:I = 0x7f070122

.field public static design_tab_scrollable_min_width:I = 0x7f070123

.field public static design_tab_text_size:I = 0x7f070124

.field public static design_tab_text_size_2line:I = 0x7f070125

.field public static design_textinput_caption_translate_y:I = 0x7f070126

.field public static disabled_alpha_material_dark:I = 0x7f070127

.field public static disabled_alpha_material_light:I = 0x7f070128

.field public static fastscroll_default_thickness:I = 0x7f070166

.field public static fastscroll_margin:I = 0x7f070167

.field public static fastscroll_minimum_range:I = 0x7f070168

.field public static highlight_alpha_material_colored:I = 0x7f070198

.field public static highlight_alpha_material_dark:I = 0x7f070199

.field public static highlight_alpha_material_light:I = 0x7f07019a

.field public static hint_alpha_material_dark:I = 0x7f07019b

.field public static hint_alpha_material_light:I = 0x7f07019c

.field public static hint_pressed_alpha_material_dark:I = 0x7f07019d

.field public static hint_pressed_alpha_material_light:I = 0x7f07019e

.field public static item_touch_helper_max_drag_scroll_per_frame:I = 0x7f0701c0

.field public static item_touch_helper_swipe_escape_max_velocity:I = 0x7f0701c1

.field public static item_touch_helper_swipe_escape_velocity:I = 0x7f0701c2

.field public static m3_alert_dialog_action_bottom_padding:I = 0x7f0701e1

.field public static m3_alert_dialog_action_top_padding:I = 0x7f0701e2

.field public static m3_alert_dialog_corner_size:I = 0x7f0701e3

.field public static m3_alert_dialog_elevation:I = 0x7f0701e4

.field public static m3_alert_dialog_icon_margin:I = 0x7f0701e5

.field public static m3_alert_dialog_icon_size:I = 0x7f0701e6

.field public static m3_alert_dialog_title_bottom_margin:I = 0x7f0701e7

.field public static m3_appbar_expanded_title_margin_bottom:I = 0x7f0701e8

.field public static m3_appbar_expanded_title_margin_horizontal:I = 0x7f0701e9

.field public static m3_appbar_scrim_height_trigger:I = 0x7f0701ea

.field public static m3_appbar_scrim_height_trigger_large:I = 0x7f0701eb

.field public static m3_appbar_scrim_height_trigger_medium:I = 0x7f0701ec

.field public static m3_appbar_size_compact:I = 0x7f0701ed

.field public static m3_appbar_size_large:I = 0x7f0701ee

.field public static m3_appbar_size_medium:I = 0x7f0701ef

.field public static m3_back_progress_bottom_container_max_scale_x_distance:I = 0x7f0701f0

.field public static m3_back_progress_bottom_container_max_scale_y_distance:I = 0x7f0701f1

.field public static m3_back_progress_main_container_max_translation_y:I = 0x7f0701f2

.field public static m3_back_progress_main_container_min_edge_gap:I = 0x7f0701f3

.field public static m3_back_progress_side_container_max_scale_x_distance_grow:I = 0x7f0701f4

.field public static m3_back_progress_side_container_max_scale_x_distance_shrink:I = 0x7f0701f5

.field public static m3_back_progress_side_container_max_scale_y_distance:I = 0x7f0701f6

.field public static m3_badge_horizontal_offset:I = 0x7f0701f7

.field public static m3_badge_offset:I = 0x7f0701f8

.field public static m3_badge_size:I = 0x7f0701f9

.field public static m3_badge_vertical_offset:I = 0x7f0701fa

.field public static m3_badge_with_text_horizontal_offset:I = 0x7f0701fb

.field public static m3_badge_with_text_offset:I = 0x7f0701fc

.field public static m3_badge_with_text_size:I = 0x7f0701fd

.field public static m3_badge_with_text_vertical_offset:I = 0x7f0701fe

.field public static m3_badge_with_text_vertical_padding:I = 0x7f0701ff

.field public static m3_bottom_nav_item_active_indicator_height:I = 0x7f070200

.field public static m3_bottom_nav_item_active_indicator_margin_horizontal:I = 0x7f070201

.field public static m3_bottom_nav_item_active_indicator_width:I = 0x7f070202

.field public static m3_bottom_nav_item_padding_bottom:I = 0x7f070203

.field public static m3_bottom_nav_item_padding_top:I = 0x7f070204

.field public static m3_bottom_nav_min_height:I = 0x7f070205

.field public static m3_bottom_sheet_drag_handle_bottom_padding:I = 0x7f070206

.field public static m3_bottom_sheet_elevation:I = 0x7f070207

.field public static m3_bottom_sheet_modal_elevation:I = 0x7f070208

.field public static m3_bottomappbar_fab_cradle_margin:I = 0x7f070209

.field public static m3_bottomappbar_fab_cradle_rounded_corner_radius:I = 0x7f07020a

.field public static m3_bottomappbar_fab_cradle_vertical_offset:I = 0x7f07020b

.field public static m3_bottomappbar_fab_end_margin:I = 0x7f07020c

.field public static m3_bottomappbar_height:I = 0x7f07020d

.field public static m3_bottomappbar_horizontal_padding:I = 0x7f07020e

.field public static m3_btn_dialog_btn_min_width:I = 0x7f07020f

.field public static m3_btn_dialog_btn_spacing:I = 0x7f070210

.field public static m3_btn_disabled_elevation:I = 0x7f070211

.field public static m3_btn_disabled_translation_z:I = 0x7f070212

.field public static m3_btn_elevated_btn_elevation:I = 0x7f070213

.field public static m3_btn_elevation:I = 0x7f070214

.field public static m3_btn_icon_btn_padding_left:I = 0x7f070215

.field public static m3_btn_icon_btn_padding_right:I = 0x7f070216

.field public static m3_btn_icon_only_default_padding:I = 0x7f070217

.field public static m3_btn_icon_only_default_size:I = 0x7f070218

.field public static m3_btn_icon_only_icon_padding:I = 0x7f070219

.field public static m3_btn_icon_only_min_width:I = 0x7f07021a

.field public static m3_btn_inset:I = 0x7f07021b

.field public static m3_btn_max_width:I = 0x7f07021c

.field public static m3_btn_padding_bottom:I = 0x7f07021d

.field public static m3_btn_padding_left:I = 0x7f07021e

.field public static m3_btn_padding_right:I = 0x7f07021f

.field public static m3_btn_padding_top:I = 0x7f070220

.field public static m3_btn_stroke_size:I = 0x7f070221

.field public static m3_btn_text_btn_icon_padding_left:I = 0x7f070222

.field public static m3_btn_text_btn_icon_padding_right:I = 0x7f070223

.field public static m3_btn_text_btn_padding_left:I = 0x7f070224

.field public static m3_btn_text_btn_padding_right:I = 0x7f070225

.field public static m3_btn_translation_z_base:I = 0x7f070226

.field public static m3_btn_translation_z_hovered:I = 0x7f070227

.field public static m3_card_disabled_z:I = 0x7f070228

.field public static m3_card_dragged_z:I = 0x7f070229

.field public static m3_card_elevated_disabled_z:I = 0x7f07022a

.field public static m3_card_elevated_dragged_z:I = 0x7f07022b

.field public static m3_card_elevated_elevation:I = 0x7f07022c

.field public static m3_card_elevated_hovered_z:I = 0x7f07022d

.field public static m3_card_elevation:I = 0x7f07022e

.field public static m3_card_hovered_z:I = 0x7f07022f

.field public static m3_card_stroke_width:I = 0x7f070230

.field public static m3_carousel_debug_keyline_width:I = 0x7f070231

.field public static m3_carousel_extra_small_item_size:I = 0x7f070232

.field public static m3_carousel_gone_size:I = 0x7f070233

.field public static m3_carousel_small_item_default_corner_size:I = 0x7f070234

.field public static m3_carousel_small_item_size_max:I = 0x7f070235

.field public static m3_carousel_small_item_size_min:I = 0x7f070236

.field public static m3_chip_checked_hovered_translation_z:I = 0x7f070237

.field public static m3_chip_corner_size:I = 0x7f070238

.field public static m3_chip_disabled_translation_z:I = 0x7f070239

.field public static m3_chip_dragged_translation_z:I = 0x7f07023a

.field public static m3_chip_elevated_elevation:I = 0x7f07023b

.field public static m3_chip_hovered_translation_z:I = 0x7f07023c

.field public static m3_chip_icon_size:I = 0x7f07023d

.field public static m3_comp_assist_chip_container_height:I = 0x7f07023e

.field public static m3_comp_assist_chip_elevated_container_elevation:I = 0x7f07023f

.field public static m3_comp_assist_chip_flat_container_elevation:I = 0x7f070240

.field public static m3_comp_assist_chip_flat_outline_width:I = 0x7f070241

.field public static m3_comp_assist_chip_with_icon_icon_size:I = 0x7f070242

.field public static m3_comp_badge_large_size:I = 0x7f070243

.field public static m3_comp_badge_size:I = 0x7f070244

.field public static m3_comp_bottom_app_bar_container_elevation:I = 0x7f070245

.field public static m3_comp_bottom_app_bar_container_height:I = 0x7f070246

.field public static m3_comp_checkbox_selected_disabled_container_opacity:I = 0x7f070247

.field public static m3_comp_date_picker_modal_date_today_container_outline_width:I = 0x7f070248

.field public static m3_comp_date_picker_modal_header_container_height:I = 0x7f070249

.field public static m3_comp_date_picker_modal_range_selection_header_container_height:I = 0x7f07024a

.field public static m3_comp_divider_thickness:I = 0x7f07024b

.field public static m3_comp_elevated_button_container_elevation:I = 0x7f07024c

.field public static m3_comp_elevated_button_disabled_container_elevation:I = 0x7f07024d

.field public static m3_comp_elevated_card_container_elevation:I = 0x7f07024e

.field public static m3_comp_elevated_card_icon_size:I = 0x7f07024f

.field public static m3_comp_extended_fab_primary_container_elevation:I = 0x7f070250

.field public static m3_comp_extended_fab_primary_container_height:I = 0x7f070251

.field public static m3_comp_extended_fab_primary_focus_container_elevation:I = 0x7f070252

.field public static m3_comp_extended_fab_primary_focus_state_layer_opacity:I = 0x7f070253

.field public static m3_comp_extended_fab_primary_hover_container_elevation:I = 0x7f070254

.field public static m3_comp_extended_fab_primary_hover_state_layer_opacity:I = 0x7f070255

.field public static m3_comp_extended_fab_primary_icon_size:I = 0x7f070256

.field public static m3_comp_extended_fab_primary_pressed_container_elevation:I = 0x7f070257

.field public static m3_comp_extended_fab_primary_pressed_state_layer_opacity:I = 0x7f070258

.field public static m3_comp_fab_primary_container_elevation:I = 0x7f070259

.field public static m3_comp_fab_primary_container_height:I = 0x7f07025a

.field public static m3_comp_fab_primary_focus_state_layer_opacity:I = 0x7f07025b

.field public static m3_comp_fab_primary_hover_container_elevation:I = 0x7f07025c

.field public static m3_comp_fab_primary_hover_state_layer_opacity:I = 0x7f07025d

.field public static m3_comp_fab_primary_icon_size:I = 0x7f07025e

.field public static m3_comp_fab_primary_large_container_height:I = 0x7f07025f

.field public static m3_comp_fab_primary_large_icon_size:I = 0x7f070260

.field public static m3_comp_fab_primary_pressed_container_elevation:I = 0x7f070261

.field public static m3_comp_fab_primary_pressed_state_layer_opacity:I = 0x7f070262

.field public static m3_comp_fab_primary_small_container_height:I = 0x7f070263

.field public static m3_comp_fab_primary_small_icon_size:I = 0x7f070264

.field public static m3_comp_filled_autocomplete_menu_container_elevation:I = 0x7f070265

.field public static m3_comp_filled_button_container_elevation:I = 0x7f070266

.field public static m3_comp_filled_button_with_icon_icon_size:I = 0x7f070267

.field public static m3_comp_filled_card_container_elevation:I = 0x7f070268

.field public static m3_comp_filled_card_dragged_state_layer_opacity:I = 0x7f070269

.field public static m3_comp_filled_card_focus_state_layer_opacity:I = 0x7f07026a

.field public static m3_comp_filled_card_hover_state_layer_opacity:I = 0x7f07026b

.field public static m3_comp_filled_card_icon_size:I = 0x7f07026c

.field public static m3_comp_filled_card_pressed_state_layer_opacity:I = 0x7f07026d

.field public static m3_comp_filled_text_field_disabled_active_indicator_opacity:I = 0x7f07026e

.field public static m3_comp_filter_chip_container_height:I = 0x7f07026f

.field public static m3_comp_filter_chip_elevated_container_elevation:I = 0x7f070270

.field public static m3_comp_filter_chip_flat_container_elevation:I = 0x7f070271

.field public static m3_comp_filter_chip_flat_unselected_outline_width:I = 0x7f070272

.field public static m3_comp_filter_chip_with_icon_icon_size:I = 0x7f070273

.field public static m3_comp_input_chip_container_elevation:I = 0x7f070274

.field public static m3_comp_input_chip_container_height:I = 0x7f070275

.field public static m3_comp_input_chip_unselected_outline_width:I = 0x7f070276

.field public static m3_comp_input_chip_with_avatar_avatar_size:I = 0x7f070277

.field public static m3_comp_input_chip_with_leading_icon_leading_icon_size:I = 0x7f070278

.field public static m3_comp_menu_container_elevation:I = 0x7f070279

.field public static m3_comp_navigation_bar_active_indicator_height:I = 0x7f07027a

.field public static m3_comp_navigation_bar_active_indicator_width:I = 0x7f07027b

.field public static m3_comp_navigation_bar_container_elevation:I = 0x7f07027c

.field public static m3_comp_navigation_bar_container_height:I = 0x7f07027d

.field public static m3_comp_navigation_bar_focus_state_layer_opacity:I = 0x7f07027e

.field public static m3_comp_navigation_bar_hover_state_layer_opacity:I = 0x7f07027f

.field public static m3_comp_navigation_bar_icon_size:I = 0x7f070280

.field public static m3_comp_navigation_bar_pressed_state_layer_opacity:I = 0x7f070281

.field public static m3_comp_navigation_drawer_container_width:I = 0x7f070282

.field public static m3_comp_navigation_drawer_focus_state_layer_opacity:I = 0x7f070283

.field public static m3_comp_navigation_drawer_hover_state_layer_opacity:I = 0x7f070284

.field public static m3_comp_navigation_drawer_icon_size:I = 0x7f070285

.field public static m3_comp_navigation_drawer_modal_container_elevation:I = 0x7f070286

.field public static m3_comp_navigation_drawer_pressed_state_layer_opacity:I = 0x7f070287

.field public static m3_comp_navigation_drawer_standard_container_elevation:I = 0x7f070288

.field public static m3_comp_navigation_rail_active_indicator_height:I = 0x7f070289

.field public static m3_comp_navigation_rail_active_indicator_width:I = 0x7f07028a

.field public static m3_comp_navigation_rail_container_elevation:I = 0x7f07028b

.field public static m3_comp_navigation_rail_container_width:I = 0x7f07028c

.field public static m3_comp_navigation_rail_focus_state_layer_opacity:I = 0x7f07028d

.field public static m3_comp_navigation_rail_hover_state_layer_opacity:I = 0x7f07028e

.field public static m3_comp_navigation_rail_icon_size:I = 0x7f07028f

.field public static m3_comp_navigation_rail_pressed_state_layer_opacity:I = 0x7f070290

.field public static m3_comp_outlined_autocomplete_menu_container_elevation:I = 0x7f070291

.field public static m3_comp_outlined_button_disabled_outline_opacity:I = 0x7f070292

.field public static m3_comp_outlined_button_outline_width:I = 0x7f070293

.field public static m3_comp_outlined_card_container_elevation:I = 0x7f070294

.field public static m3_comp_outlined_card_disabled_outline_opacity:I = 0x7f070295

.field public static m3_comp_outlined_card_icon_size:I = 0x7f070296

.field public static m3_comp_outlined_card_outline_width:I = 0x7f070297

.field public static m3_comp_outlined_icon_button_unselected_outline_width:I = 0x7f070298

.field public static m3_comp_outlined_text_field_disabled_input_text_opacity:I = 0x7f070299

.field public static m3_comp_outlined_text_field_disabled_label_text_opacity:I = 0x7f07029a

.field public static m3_comp_outlined_text_field_disabled_supporting_text_opacity:I = 0x7f07029b

.field public static m3_comp_outlined_text_field_focus_outline_width:I = 0x7f07029c

.field public static m3_comp_outlined_text_field_outline_width:I = 0x7f07029d

.field public static m3_comp_primary_navigation_tab_active_focus_state_layer_opacity:I = 0x7f07029e

.field public static m3_comp_primary_navigation_tab_active_hover_state_layer_opacity:I = 0x7f07029f

.field public static m3_comp_primary_navigation_tab_active_indicator_height:I = 0x7f0702a0

.field public static m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity:I = 0x7f0702a1

.field public static m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity:I = 0x7f0702a2

.field public static m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity:I = 0x7f0702a3

.field public static m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity:I = 0x7f0702a4

.field public static m3_comp_primary_navigation_tab_with_icon_icon_size:I = 0x7f0702a5

.field public static m3_comp_radio_button_disabled_selected_icon_opacity:I = 0x7f0702a9

.field public static m3_comp_radio_button_disabled_unselected_icon_opacity:I = 0x7f0702aa

.field public static m3_comp_radio_button_selected_focus_state_layer_opacity:I = 0x7f0702ab

.field public static m3_comp_radio_button_selected_hover_state_layer_opacity:I = 0x7f0702ac

.field public static m3_comp_radio_button_selected_pressed_state_layer_opacity:I = 0x7f0702ad

.field public static m3_comp_radio_button_unselected_focus_state_layer_opacity:I = 0x7f0702ae

.field public static m3_comp_radio_button_unselected_hover_state_layer_opacity:I = 0x7f0702af

.field public static m3_comp_radio_button_unselected_pressed_state_layer_opacity:I = 0x7f0702b0

.field public static m3_comp_search_bar_avatar_size:I = 0x7f0702b2

.field public static m3_comp_search_bar_container_elevation:I = 0x7f0702b3

.field public static m3_comp_search_bar_container_height:I = 0x7f0702b4

.field public static m3_comp_search_bar_hover_state_layer_opacity:I = 0x7f0702b5

.field public static m3_comp_search_bar_pressed_state_layer_opacity:I = 0x7f0702b6

.field public static m3_comp_search_view_container_elevation:I = 0x7f0702b7

.field public static m3_comp_search_view_docked_header_container_height:I = 0x7f0702b8

.field public static m3_comp_search_view_full_screen_header_container_height:I = 0x7f0702b9

.field public static m3_comp_secondary_navigation_tab_active_indicator_height:I = 0x7f0702ba

.field public static m3_comp_secondary_navigation_tab_focus_state_layer_opacity:I = 0x7f0702bb

.field public static m3_comp_secondary_navigation_tab_hover_state_layer_opacity:I = 0x7f0702bc

.field public static m3_comp_secondary_navigation_tab_pressed_state_layer_opacity:I = 0x7f0702bd

.field public static m3_comp_sheet_bottom_docked_drag_handle_height:I = 0x7f0702be

.field public static m3_comp_sheet_bottom_docked_drag_handle_width:I = 0x7f0702bf

.field public static m3_comp_sheet_bottom_docked_modal_container_elevation:I = 0x7f0702c0

.field public static m3_comp_sheet_bottom_docked_standard_container_elevation:I = 0x7f0702c1

.field public static m3_comp_sheet_side_docked_container_width:I = 0x7f0702c2

.field public static m3_comp_sheet_side_docked_modal_container_elevation:I = 0x7f0702c3

.field public static m3_comp_sheet_side_docked_standard_container_elevation:I = 0x7f0702c4

.field public static m3_comp_slider_disabled_active_track_opacity:I = 0x7f0702c8

.field public static m3_comp_slider_disabled_handle_opacity:I = 0x7f0702c9

.field public static m3_comp_slider_disabled_inactive_track_opacity:I = 0x7f0702ca

.field public static m3_comp_slider_inactive_track_height:I = 0x7f0702cb

.field public static m3_comp_snackbar_container_elevation:I = 0x7f0702cd

.field public static m3_comp_suggestion_chip_container_height:I = 0x7f0702ce

.field public static m3_comp_suggestion_chip_elevated_container_elevation:I = 0x7f0702cf

.field public static m3_comp_suggestion_chip_flat_container_elevation:I = 0x7f0702d0

.field public static m3_comp_suggestion_chip_flat_outline_width:I = 0x7f0702d1

.field public static m3_comp_suggestion_chip_with_leading_icon_leading_icon_size:I = 0x7f0702d2

.field public static m3_comp_switch_disabled_selected_handle_opacity:I = 0x7f0702d3

.field public static m3_comp_switch_disabled_selected_icon_opacity:I = 0x7f0702d4

.field public static m3_comp_switch_disabled_track_opacity:I = 0x7f0702d5

.field public static m3_comp_switch_disabled_unselected_handle_opacity:I = 0x7f0702d6

.field public static m3_comp_switch_disabled_unselected_icon_opacity:I = 0x7f0702d7

.field public static m3_comp_switch_selected_focus_state_layer_opacity:I = 0x7f0702d8

.field public static m3_comp_switch_selected_hover_state_layer_opacity:I = 0x7f0702d9

.field public static m3_comp_switch_selected_pressed_state_layer_opacity:I = 0x7f0702da

.field public static m3_comp_switch_track_height:I = 0x7f0702db

.field public static m3_comp_switch_track_width:I = 0x7f0702dc

.field public static m3_comp_switch_unselected_focus_state_layer_opacity:I = 0x7f0702dd

.field public static m3_comp_switch_unselected_hover_state_layer_opacity:I = 0x7f0702de

.field public static m3_comp_switch_unselected_pressed_state_layer_opacity:I = 0x7f0702df

.field public static m3_comp_text_button_focus_state_layer_opacity:I = 0x7f0702e0

.field public static m3_comp_text_button_hover_state_layer_opacity:I = 0x7f0702e1

.field public static m3_comp_text_button_pressed_state_layer_opacity:I = 0x7f0702e2

.field public static m3_comp_time_input_time_input_field_focus_outline_width:I = 0x7f0702e3

.field public static m3_comp_time_picker_container_elevation:I = 0x7f0702e4

.field public static m3_comp_time_picker_period_selector_focus_state_layer_opacity:I = 0x7f0702e5

.field public static m3_comp_time_picker_period_selector_hover_state_layer_opacity:I = 0x7f0702e6

.field public static m3_comp_time_picker_period_selector_outline_width:I = 0x7f0702e7

.field public static m3_comp_time_picker_period_selector_pressed_state_layer_opacity:I = 0x7f0702e8

.field public static m3_comp_time_picker_time_selector_focus_state_layer_opacity:I = 0x7f0702e9

.field public static m3_comp_time_picker_time_selector_hover_state_layer_opacity:I = 0x7f0702ea

.field public static m3_comp_time_picker_time_selector_pressed_state_layer_opacity:I = 0x7f0702eb

.field public static m3_comp_top_app_bar_large_container_height:I = 0x7f0702ec

.field public static m3_comp_top_app_bar_medium_container_height:I = 0x7f0702ed

.field public static m3_comp_top_app_bar_small_container_elevation:I = 0x7f0702ee

.field public static m3_comp_top_app_bar_small_container_height:I = 0x7f0702ef

.field public static m3_comp_top_app_bar_small_on_scroll_container_elevation:I = 0x7f0702f0

.field public static m3_datepicker_elevation:I = 0x7f0702f1

.field public static m3_divider_heavy_thickness:I = 0x7f0702f2

.field public static m3_extended_fab_bottom_padding:I = 0x7f0702f3

.field public static m3_extended_fab_end_padding:I = 0x7f0702f4

.field public static m3_extended_fab_icon_padding:I = 0x7f0702f5

.field public static m3_extended_fab_min_height:I = 0x7f0702f6

.field public static m3_extended_fab_start_padding:I = 0x7f0702f7

.field public static m3_extended_fab_top_padding:I = 0x7f0702f8

.field public static m3_fab_border_width:I = 0x7f0702f9

.field public static m3_fab_corner_size:I = 0x7f0702fa

.field public static m3_fab_translation_z_hovered_focused:I = 0x7f0702fb

.field public static m3_fab_translation_z_pressed:I = 0x7f0702fc

.field public static m3_large_fab_max_image_size:I = 0x7f0702fd

.field public static m3_large_fab_size:I = 0x7f0702fe

.field public static m3_large_text_vertical_offset_adjustment:I = 0x7f0702ff

.field public static m3_menu_elevation:I = 0x7f070300

.field public static m3_nav_badge_with_text_vertical_offset:I = 0x7f070301

.field public static m3_navigation_drawer_layout_corner_size:I = 0x7f070302

.field public static m3_navigation_item_active_indicator_label_padding:I = 0x7f070303

.field public static m3_navigation_item_horizontal_padding:I = 0x7f070304

.field public static m3_navigation_item_icon_padding:I = 0x7f070305

.field public static m3_navigation_item_shape_inset_bottom:I = 0x7f070306

.field public static m3_navigation_item_shape_inset_end:I = 0x7f070307

.field public static m3_navigation_item_shape_inset_start:I = 0x7f070308

.field public static m3_navigation_item_shape_inset_top:I = 0x7f070309

.field public static m3_navigation_item_vertical_padding:I = 0x7f07030a

.field public static m3_navigation_menu_divider_horizontal_padding:I = 0x7f07030b

.field public static m3_navigation_menu_headline_horizontal_padding:I = 0x7f07030c

.field public static m3_navigation_rail_default_width:I = 0x7f07030d

.field public static m3_navigation_rail_elevation:I = 0x7f07030e

.field public static m3_navigation_rail_icon_size:I = 0x7f07030f

.field public static m3_navigation_rail_item_active_indicator_height:I = 0x7f070310

.field public static m3_navigation_rail_item_active_indicator_margin_horizontal:I = 0x7f070311

.field public static m3_navigation_rail_item_active_indicator_width:I = 0x7f070312

.field public static m3_navigation_rail_item_min_height:I = 0x7f070313

.field public static m3_navigation_rail_item_padding_bottom:I = 0x7f070314

.field public static m3_navigation_rail_item_padding_bottom_with_large_font:I = 0x7f070315

.field public static m3_navigation_rail_item_padding_top:I = 0x7f070316

.field public static m3_navigation_rail_item_padding_top_with_large_font:I = 0x7f070317

.field public static m3_ripple_default_alpha:I = 0x7f070319

.field public static m3_ripple_focused_alpha:I = 0x7f07031a

.field public static m3_ripple_hovered_alpha:I = 0x7f07031b

.field public static m3_ripple_pressed_alpha:I = 0x7f07031c

.field public static m3_ripple_selectable_pressed_alpha:I = 0x7f07031d

.field public static m3_searchbar_elevation:I = 0x7f07031e

.field public static m3_searchbar_height:I = 0x7f07031f

.field public static m3_searchbar_margin_horizontal:I = 0x7f070320

.field public static m3_searchbar_margin_vertical:I = 0x7f070321

.field public static m3_searchbar_outlined_stroke_width:I = 0x7f070322

.field public static m3_searchbar_padding_start:I = 0x7f070323

.field public static m3_searchbar_text_margin_start_no_navigation_icon:I = 0x7f070324

.field public static m3_searchbar_text_size:I = 0x7f070325

.field public static m3_searchview_divider_size:I = 0x7f070326

.field public static m3_searchview_elevation:I = 0x7f070327

.field public static m3_searchview_height:I = 0x7f070328

.field public static m3_side_sheet_margin_detached:I = 0x7f070329

.field public static m3_side_sheet_modal_elevation:I = 0x7f07032a

.field public static m3_side_sheet_standard_elevation:I = 0x7f07032b

.field public static m3_side_sheet_width:I = 0x7f07032c

.field public static m3_simple_item_color_hovered_alpha:I = 0x7f07032d

.field public static m3_simple_item_color_selected_alpha:I = 0x7f07032e

.field public static m3_slider_thumb_elevation:I = 0x7f07032f

.field public static m3_small_fab_max_image_size:I = 0x7f070330

.field public static m3_small_fab_size:I = 0x7f070331

.field public static m3_snackbar_action_text_color_alpha:I = 0x7f070332

.field public static m3_snackbar_margin:I = 0x7f070333

.field public static m3_sys_elevation_level0:I = 0x7f070334

.field public static m3_sys_elevation_level1:I = 0x7f070335

.field public static m3_sys_elevation_level2:I = 0x7f070336

.field public static m3_sys_elevation_level3:I = 0x7f070337

.field public static m3_sys_elevation_level4:I = 0x7f070338

.field public static m3_sys_elevation_level5:I = 0x7f070339

.field public static m3_sys_motion_easing_emphasized_accelerate_control_x1:I = 0x7f07033a

.field public static m3_sys_motion_easing_emphasized_accelerate_control_x2:I = 0x7f07033b

.field public static m3_sys_motion_easing_emphasized_accelerate_control_y1:I = 0x7f07033c

.field public static m3_sys_motion_easing_emphasized_accelerate_control_y2:I = 0x7f07033d

.field public static m3_sys_motion_easing_emphasized_decelerate_control_x1:I = 0x7f07033e

.field public static m3_sys_motion_easing_emphasized_decelerate_control_x2:I = 0x7f07033f

.field public static m3_sys_motion_easing_emphasized_decelerate_control_y1:I = 0x7f070340

.field public static m3_sys_motion_easing_emphasized_decelerate_control_y2:I = 0x7f070341

.field public static m3_sys_motion_easing_legacy_accelerate_control_x1:I = 0x7f070342

.field public static m3_sys_motion_easing_legacy_accelerate_control_x2:I = 0x7f070343

.field public static m3_sys_motion_easing_legacy_accelerate_control_y1:I = 0x7f070344

.field public static m3_sys_motion_easing_legacy_accelerate_control_y2:I = 0x7f070345

.field public static m3_sys_motion_easing_legacy_control_x1:I = 0x7f070346

.field public static m3_sys_motion_easing_legacy_control_x2:I = 0x7f070347

.field public static m3_sys_motion_easing_legacy_control_y1:I = 0x7f070348

.field public static m3_sys_motion_easing_legacy_control_y2:I = 0x7f070349

.field public static m3_sys_motion_easing_legacy_decelerate_control_x1:I = 0x7f07034a

.field public static m3_sys_motion_easing_legacy_decelerate_control_x2:I = 0x7f07034b

.field public static m3_sys_motion_easing_legacy_decelerate_control_y1:I = 0x7f07034c

.field public static m3_sys_motion_easing_legacy_decelerate_control_y2:I = 0x7f07034d

.field public static m3_sys_motion_easing_linear_control_x1:I = 0x7f07034e

.field public static m3_sys_motion_easing_linear_control_x2:I = 0x7f07034f

.field public static m3_sys_motion_easing_linear_control_y1:I = 0x7f070350

.field public static m3_sys_motion_easing_linear_control_y2:I = 0x7f070351

.field public static m3_sys_motion_easing_standard_accelerate_control_x1:I = 0x7f070352

.field public static m3_sys_motion_easing_standard_accelerate_control_x2:I = 0x7f070353

.field public static m3_sys_motion_easing_standard_accelerate_control_y1:I = 0x7f070354

.field public static m3_sys_motion_easing_standard_accelerate_control_y2:I = 0x7f070355

.field public static m3_sys_motion_easing_standard_control_x1:I = 0x7f070356

.field public static m3_sys_motion_easing_standard_control_x2:I = 0x7f070357

.field public static m3_sys_motion_easing_standard_control_y1:I = 0x7f070358

.field public static m3_sys_motion_easing_standard_control_y2:I = 0x7f070359

.field public static m3_sys_motion_easing_standard_decelerate_control_x1:I = 0x7f07035a

.field public static m3_sys_motion_easing_standard_decelerate_control_x2:I = 0x7f07035b

.field public static m3_sys_motion_easing_standard_decelerate_control_y1:I = 0x7f07035c

.field public static m3_sys_motion_easing_standard_decelerate_control_y2:I = 0x7f07035d

.field public static m3_sys_state_dragged_state_layer_opacity:I = 0x7f07035e

.field public static m3_sys_state_focus_state_layer_opacity:I = 0x7f07035f

.field public static m3_sys_state_hover_state_layer_opacity:I = 0x7f070360

.field public static m3_sys_state_pressed_state_layer_opacity:I = 0x7f070361

.field public static m3_timepicker_display_stroke_width:I = 0x7f070362

.field public static m3_timepicker_window_elevation:I = 0x7f070363

.field public static m3_toolbar_text_size_title:I = 0x7f070364

.field public static material_bottom_sheet_max_width:I = 0x7f07036e

.field public static material_clock_display_height:I = 0x7f07036f

.field public static material_clock_display_padding:I = 0x7f070370

.field public static material_clock_display_width:I = 0x7f070371

.field public static material_clock_face_margin_bottom:I = 0x7f070372

.field public static material_clock_face_margin_top:I = 0x7f070373

.field public static material_clock_hand_center_dot_radius:I = 0x7f070374

.field public static material_clock_hand_padding:I = 0x7f070375

.field public static material_clock_hand_stroke_width:I = 0x7f070376

.field public static material_clock_number_text_size:I = 0x7f070377

.field public static material_clock_period_toggle_height:I = 0x7f070378

.field public static material_clock_period_toggle_horizontal_gap:I = 0x7f070379

.field public static material_clock_period_toggle_vertical_gap:I = 0x7f07037a

.field public static material_clock_period_toggle_width:I = 0x7f07037b

.field public static material_clock_size:I = 0x7f07037c

.field public static material_cursor_inset:I = 0x7f07037d

.field public static material_cursor_width:I = 0x7f07037e

.field public static material_divider_thickness:I = 0x7f07037f

.field public static material_emphasis_disabled:I = 0x7f070380

.field public static material_emphasis_disabled_background:I = 0x7f070381

.field public static material_emphasis_high_type:I = 0x7f070382

.field public static material_emphasis_medium:I = 0x7f070383

.field public static material_filled_edittext_font_1_3_padding_bottom:I = 0x7f070384

.field public static material_filled_edittext_font_1_3_padding_top:I = 0x7f070385

.field public static material_filled_edittext_font_2_0_padding_bottom:I = 0x7f070386

.field public static material_filled_edittext_font_2_0_padding_top:I = 0x7f070387

.field public static material_font_1_3_box_collapsed_padding_top:I = 0x7f070388

.field public static material_font_2_0_box_collapsed_padding_top:I = 0x7f070389

.field public static material_helper_text_default_padding_top:I = 0x7f07038a

.field public static material_helper_text_font_1_3_padding_horizontal:I = 0x7f07038b

.field public static material_helper_text_font_1_3_padding_top:I = 0x7f07038c

.field public static material_input_text_to_prefix_suffix_padding:I = 0x7f07038d

.field public static material_textinput_default_width:I = 0x7f07038e

.field public static material_textinput_max_width:I = 0x7f07038f

.field public static material_textinput_min_width:I = 0x7f070390

.field public static material_time_picker_minimum_screen_height:I = 0x7f070391

.field public static material_time_picker_minimum_screen_width:I = 0x7f070392

.field public static mtrl_alert_dialog_background_inset_bottom:I = 0x7f070399

.field public static mtrl_alert_dialog_background_inset_end:I = 0x7f07039a

.field public static mtrl_alert_dialog_background_inset_start:I = 0x7f07039b

.field public static mtrl_alert_dialog_background_inset_top:I = 0x7f07039c

.field public static mtrl_alert_dialog_picker_background_inset:I = 0x7f07039d

.field public static mtrl_badge_horizontal_edge_offset:I = 0x7f07039e

.field public static mtrl_badge_long_text_horizontal_padding:I = 0x7f07039f

.field public static mtrl_badge_size:I = 0x7f0703a0

.field public static mtrl_badge_text_horizontal_edge_offset:I = 0x7f0703a1

.field public static mtrl_badge_text_size:I = 0x7f0703a2

.field public static mtrl_badge_toolbar_action_menu_item_horizontal_offset:I = 0x7f0703a3

.field public static mtrl_badge_toolbar_action_menu_item_vertical_offset:I = 0x7f0703a4

.field public static mtrl_badge_with_text_size:I = 0x7f0703a5

.field public static mtrl_bottomappbar_fabOffsetEndMode:I = 0x7f0703a6

.field public static mtrl_bottomappbar_fab_bottom_margin:I = 0x7f0703a7

.field public static mtrl_bottomappbar_fab_cradle_margin:I = 0x7f0703a8

.field public static mtrl_bottomappbar_fab_cradle_rounded_corner_radius:I = 0x7f0703a9

.field public static mtrl_bottomappbar_fab_cradle_vertical_offset:I = 0x7f0703aa

.field public static mtrl_bottomappbar_height:I = 0x7f0703ab

.field public static mtrl_btn_corner_radius:I = 0x7f0703ac

.field public static mtrl_btn_dialog_btn_min_width:I = 0x7f0703ad

.field public static mtrl_btn_disabled_elevation:I = 0x7f0703ae

.field public static mtrl_btn_disabled_z:I = 0x7f0703af

.field public static mtrl_btn_elevation:I = 0x7f0703b0

.field public static mtrl_btn_focused_z:I = 0x7f0703b1

.field public static mtrl_btn_hovered_z:I = 0x7f0703b2

.field public static mtrl_btn_icon_btn_padding_left:I = 0x7f0703b3

.field public static mtrl_btn_icon_padding:I = 0x7f0703b4

.field public static mtrl_btn_inset:I = 0x7f0703b5

.field public static mtrl_btn_letter_spacing:I = 0x7f0703b6

.field public static mtrl_btn_max_width:I = 0x7f0703b7

.field public static mtrl_btn_padding_bottom:I = 0x7f0703b8

.field public static mtrl_btn_padding_left:I = 0x7f0703b9

.field public static mtrl_btn_padding_right:I = 0x7f0703ba

.field public static mtrl_btn_padding_top:I = 0x7f0703bb

.field public static mtrl_btn_pressed_z:I = 0x7f0703bc

.field public static mtrl_btn_snackbar_margin_horizontal:I = 0x7f0703bd

.field public static mtrl_btn_stroke_size:I = 0x7f0703be

.field public static mtrl_btn_text_btn_icon_padding:I = 0x7f0703bf

.field public static mtrl_btn_text_btn_padding_left:I = 0x7f0703c0

.field public static mtrl_btn_text_btn_padding_right:I = 0x7f0703c1

.field public static mtrl_btn_text_size:I = 0x7f0703c2

.field public static mtrl_btn_z:I = 0x7f0703c3

.field public static mtrl_calendar_action_confirm_button_min_width:I = 0x7f0703c4

.field public static mtrl_calendar_action_height:I = 0x7f0703c5

.field public static mtrl_calendar_action_padding:I = 0x7f0703c6

.field public static mtrl_calendar_bottom_padding:I = 0x7f0703c7

.field public static mtrl_calendar_content_padding:I = 0x7f0703c8

.field public static mtrl_calendar_day_corner:I = 0x7f0703c9

.field public static mtrl_calendar_day_height:I = 0x7f0703ca

.field public static mtrl_calendar_day_horizontal_padding:I = 0x7f0703cb

.field public static mtrl_calendar_day_today_stroke:I = 0x7f0703cc

.field public static mtrl_calendar_day_vertical_padding:I = 0x7f0703cd

.field public static mtrl_calendar_day_width:I = 0x7f0703ce

.field public static mtrl_calendar_days_of_week_height:I = 0x7f0703cf

.field public static mtrl_calendar_dialog_background_inset:I = 0x7f0703d0

.field public static mtrl_calendar_header_content_padding:I = 0x7f0703d1

.field public static mtrl_calendar_header_content_padding_fullscreen:I = 0x7f0703d2

.field public static mtrl_calendar_header_divider_thickness:I = 0x7f0703d3

.field public static mtrl_calendar_header_height:I = 0x7f0703d4

.field public static mtrl_calendar_header_height_fullscreen:I = 0x7f0703d5

.field public static mtrl_calendar_header_selection_line_height:I = 0x7f0703d6

.field public static mtrl_calendar_header_text_padding:I = 0x7f0703d7

.field public static mtrl_calendar_header_toggle_margin_bottom:I = 0x7f0703d8

.field public static mtrl_calendar_header_toggle_margin_top:I = 0x7f0703d9

.field public static mtrl_calendar_landscape_header_width:I = 0x7f0703da

.field public static mtrl_calendar_maximum_default_fullscreen_minor_axis:I = 0x7f0703db

.field public static mtrl_calendar_month_horizontal_padding:I = 0x7f0703dc

.field public static mtrl_calendar_month_vertical_padding:I = 0x7f0703dd

.field public static mtrl_calendar_navigation_bottom_padding:I = 0x7f0703de

.field public static mtrl_calendar_navigation_height:I = 0x7f0703df

.field public static mtrl_calendar_navigation_top_padding:I = 0x7f0703e0

.field public static mtrl_calendar_pre_l_text_clip_padding:I = 0x7f0703e1

.field public static mtrl_calendar_selection_baseline_to_top_fullscreen:I = 0x7f0703e2

.field public static mtrl_calendar_selection_text_baseline_to_bottom:I = 0x7f0703e3

.field public static mtrl_calendar_selection_text_baseline_to_bottom_fullscreen:I = 0x7f0703e4

.field public static mtrl_calendar_selection_text_baseline_to_top:I = 0x7f0703e5

.field public static mtrl_calendar_text_input_padding_top:I = 0x7f0703e6

.field public static mtrl_calendar_title_baseline_to_top:I = 0x7f0703e7

.field public static mtrl_calendar_title_baseline_to_top_fullscreen:I = 0x7f0703e8

.field public static mtrl_calendar_year_corner:I = 0x7f0703e9

.field public static mtrl_calendar_year_height:I = 0x7f0703ea

.field public static mtrl_calendar_year_horizontal_padding:I = 0x7f0703eb

.field public static mtrl_calendar_year_vertical_padding:I = 0x7f0703ec

.field public static mtrl_calendar_year_width:I = 0x7f0703ed

.field public static mtrl_card_checked_icon_margin:I = 0x7f0703ee

.field public static mtrl_card_checked_icon_size:I = 0x7f0703ef

.field public static mtrl_card_corner_radius:I = 0x7f0703f0

.field public static mtrl_card_dragged_z:I = 0x7f0703f1

.field public static mtrl_card_elevation:I = 0x7f0703f2

.field public static mtrl_card_spacing:I = 0x7f0703f3

.field public static mtrl_chip_pressed_translation_z:I = 0x7f0703f4

.field public static mtrl_chip_text_size:I = 0x7f0703f5

.field public static mtrl_exposed_dropdown_menu_popup_elevation:I = 0x7f0703f6

.field public static mtrl_exposed_dropdown_menu_popup_vertical_offset:I = 0x7f0703f7

.field public static mtrl_exposed_dropdown_menu_popup_vertical_padding:I = 0x7f0703f8

.field public static mtrl_extended_fab_bottom_padding:I = 0x7f0703f9

.field public static mtrl_extended_fab_disabled_elevation:I = 0x7f0703fa

.field public static mtrl_extended_fab_disabled_translation_z:I = 0x7f0703fb

.field public static mtrl_extended_fab_elevation:I = 0x7f0703fc

.field public static mtrl_extended_fab_end_padding:I = 0x7f0703fd

.field public static mtrl_extended_fab_end_padding_icon:I = 0x7f0703fe

.field public static mtrl_extended_fab_icon_size:I = 0x7f0703ff

.field public static mtrl_extended_fab_icon_text_spacing:I = 0x7f070400

.field public static mtrl_extended_fab_min_height:I = 0x7f070401

.field public static mtrl_extended_fab_min_width:I = 0x7f070402

.field public static mtrl_extended_fab_start_padding:I = 0x7f070403

.field public static mtrl_extended_fab_start_padding_icon:I = 0x7f070404

.field public static mtrl_extended_fab_top_padding:I = 0x7f070405

.field public static mtrl_extended_fab_translation_z_base:I = 0x7f070406

.field public static mtrl_extended_fab_translation_z_hovered_focused:I = 0x7f070407

.field public static mtrl_extended_fab_translation_z_pressed:I = 0x7f070408

.field public static mtrl_fab_elevation:I = 0x7f070409

.field public static mtrl_fab_min_touch_target:I = 0x7f07040a

.field public static mtrl_fab_translation_z_hovered_focused:I = 0x7f07040b

.field public static mtrl_fab_translation_z_pressed:I = 0x7f07040c

.field public static mtrl_high_ripple_default_alpha:I = 0x7f07040d

.field public static mtrl_high_ripple_focused_alpha:I = 0x7f07040e

.field public static mtrl_high_ripple_hovered_alpha:I = 0x7f07040f

.field public static mtrl_high_ripple_pressed_alpha:I = 0x7f070410

.field public static mtrl_low_ripple_default_alpha:I = 0x7f070411

.field public static mtrl_low_ripple_focused_alpha:I = 0x7f070412

.field public static mtrl_low_ripple_hovered_alpha:I = 0x7f070413

.field public static mtrl_low_ripple_pressed_alpha:I = 0x7f070414

.field public static mtrl_min_touch_target_size:I = 0x7f070415

.field public static mtrl_navigation_bar_item_default_icon_size:I = 0x7f070416

.field public static mtrl_navigation_bar_item_default_margin:I = 0x7f070417

.field public static mtrl_navigation_elevation:I = 0x7f070418

.field public static mtrl_navigation_item_horizontal_padding:I = 0x7f070419

.field public static mtrl_navigation_item_icon_padding:I = 0x7f07041a

.field public static mtrl_navigation_item_icon_size:I = 0x7f07041b

.field public static mtrl_navigation_item_shape_horizontal_margin:I = 0x7f07041c

.field public static mtrl_navigation_item_shape_vertical_margin:I = 0x7f07041d

.field public static mtrl_navigation_rail_active_text_size:I = 0x7f07041e

.field public static mtrl_navigation_rail_compact_width:I = 0x7f07041f

.field public static mtrl_navigation_rail_default_width:I = 0x7f070420

.field public static mtrl_navigation_rail_elevation:I = 0x7f070421

.field public static mtrl_navigation_rail_icon_margin:I = 0x7f070422

.field public static mtrl_navigation_rail_icon_size:I = 0x7f070423

.field public static mtrl_navigation_rail_margin:I = 0x7f070424

.field public static mtrl_navigation_rail_text_bottom_margin:I = 0x7f070425

.field public static mtrl_navigation_rail_text_size:I = 0x7f070426

.field public static mtrl_progress_circular_inset:I = 0x7f070427

.field public static mtrl_progress_circular_inset_extra_small:I = 0x7f070428

.field public static mtrl_progress_circular_inset_medium:I = 0x7f070429

.field public static mtrl_progress_circular_inset_small:I = 0x7f07042a

.field public static mtrl_progress_circular_radius:I = 0x7f07042b

.field public static mtrl_progress_circular_size:I = 0x7f07042c

.field public static mtrl_progress_circular_size_extra_small:I = 0x7f07042d

.field public static mtrl_progress_circular_size_medium:I = 0x7f07042e

.field public static mtrl_progress_circular_size_small:I = 0x7f07042f

.field public static mtrl_progress_circular_track_thickness_extra_small:I = 0x7f070430

.field public static mtrl_progress_circular_track_thickness_medium:I = 0x7f070431

.field public static mtrl_progress_circular_track_thickness_small:I = 0x7f070432

.field public static mtrl_progress_indicator_full_rounded_corner_radius:I = 0x7f070433

.field public static mtrl_progress_track_thickness:I = 0x7f070434

.field public static mtrl_shape_corner_size_large_component:I = 0x7f070435

.field public static mtrl_shape_corner_size_medium_component:I = 0x7f070436

.field public static mtrl_shape_corner_size_small_component:I = 0x7f070437

.field public static mtrl_slider_halo_radius:I = 0x7f070438

.field public static mtrl_slider_label_padding:I = 0x7f070439

.field public static mtrl_slider_label_radius:I = 0x7f07043a

.field public static mtrl_slider_label_square_side:I = 0x7f07043b

.field public static mtrl_slider_thumb_elevation:I = 0x7f07043c

.field public static mtrl_slider_thumb_radius:I = 0x7f07043d

.field public static mtrl_slider_tick_radius:I = 0x7f07043f

.field public static mtrl_slider_track_height:I = 0x7f070440

.field public static mtrl_slider_track_side_padding:I = 0x7f070441

.field public static mtrl_slider_widget_height:I = 0x7f070442

.field public static mtrl_snackbar_action_text_color_alpha:I = 0x7f070443

.field public static mtrl_snackbar_background_corner_radius:I = 0x7f070444

.field public static mtrl_snackbar_background_overlay_color_alpha:I = 0x7f070445

.field public static mtrl_snackbar_margin:I = 0x7f070446

.field public static mtrl_snackbar_message_margin_horizontal:I = 0x7f070447

.field public static mtrl_snackbar_padding_horizontal:I = 0x7f070448

.field public static mtrl_switch_text_padding:I = 0x7f070449

.field public static mtrl_switch_thumb_elevation:I = 0x7f07044a

.field public static mtrl_switch_thumb_icon_size:I = 0x7f07044b

.field public static mtrl_switch_thumb_size:I = 0x7f07044c

.field public static mtrl_switch_track_height:I = 0x7f07044d

.field public static mtrl_switch_track_width:I = 0x7f07044e

.field public static mtrl_textinput_box_corner_radius_medium:I = 0x7f07044f

.field public static mtrl_textinput_box_corner_radius_small:I = 0x7f070450

.field public static mtrl_textinput_box_label_cutout_padding:I = 0x7f070451

.field public static mtrl_textinput_box_stroke_width_default:I = 0x7f070452

.field public static mtrl_textinput_box_stroke_width_focused:I = 0x7f070453

.field public static mtrl_textinput_counter_margin_start:I = 0x7f070454

.field public static mtrl_textinput_end_icon_margin_start:I = 0x7f070455

.field public static mtrl_textinput_outline_box_expanded_padding:I = 0x7f070456

.field public static mtrl_textinput_start_icon_margin_end:I = 0x7f070457

.field public static mtrl_toolbar_default_height:I = 0x7f070458

.field public static mtrl_tooltip_arrowSize:I = 0x7f070459

.field public static mtrl_tooltip_cornerSize:I = 0x7f07045a

.field public static mtrl_tooltip_minHeight:I = 0x7f07045b

.field public static mtrl_tooltip_minWidth:I = 0x7f07045c

.field public static mtrl_tooltip_padding:I = 0x7f07045d

.field public static mtrl_transition_shared_axis_slide_distance:I = 0x7f07045e

.field public static notification_action_icon_size:I = 0x7f070469

.field public static notification_action_text_size:I = 0x7f07046a

.field public static notification_big_circle_margin:I = 0x7f07046b

.field public static notification_content_margin_start:I = 0x7f07046c

.field public static notification_large_icon_height:I = 0x7f07046d

.field public static notification_large_icon_width:I = 0x7f07046e

.field public static notification_main_column_padding_top:I = 0x7f07046f

.field public static notification_media_narrow_margin:I = 0x7f070470

.field public static notification_right_icon_size:I = 0x7f070471

.field public static notification_right_side_padding_top:I = 0x7f070472

.field public static notification_small_icon_background_padding:I = 0x7f070473

.field public static notification_small_icon_size_as_large:I = 0x7f070474

.field public static notification_subtext_size:I = 0x7f070475

.field public static notification_top_pad:I = 0x7f070476

.field public static notification_top_pad_large_text:I = 0x7f070477

.field public static sns_agreement_card_corner_radius:I = 0x7f0705b6

.field public static sns_agreement_card_stroke_width:I = 0x7f0705b7

.field public static sns_anchor_margin:I = 0x7f0705b8

.field public static sns_autocapture_hint_min_height:I = 0x7f0705b9

.field public static sns_autocapture_switch_min_height:I = 0x7f0705ba

.field public static sns_bottom_sheet_corner_radius:I = 0x7f0705bb

.field public static sns_bottom_sheet_top_padding:I = 0x7f0705bc

.field public static sns_checkbox_min_height:I = 0x7f0705bd

.field public static sns_checkbox_top_bottom_padding:I = 0x7f0705be

.field public static sns_collapsed_intro_height:I = 0x7f0705bf

.field public static sns_dounded_divider_height:I = 0x7f0705c0

.field public static sns_eid_image_size:I = 0x7f0705c1

.field public static sns_file_attachment_corner_radius:I = 0x7f0705c2

.field public static sns_file_attachment_preview_corner_radius:I = 0x7f0705c3

.field public static sns_frame_stroke_interval:I = 0x7f0705c4

.field public static sns_frame_stroke_width:I = 0x7f0705c5

.field public static sns_icon_size_huge:I = 0x7f0705c6

.field public static sns_icon_size_large:I = 0x7f0705c7

.field public static sns_icon_size_medium:I = 0x7f0705c8

.field public static sns_icon_size_normal:I = 0x7f0705c9

.field public static sns_icon_size_normal_large:I = 0x7f0705ca

.field public static sns_icon_size_small:I = 0x7f0705cb

.field public static sns_image_button_padding:I = 0x7f0705cc

.field public static sns_margin_flag:I = 0x7f0705cd

.field public static sns_margin_huge:I = 0x7f0705ce

.field public static sns_margin_large:I = 0x7f0705cf

.field public static sns_margin_medium:I = 0x7f0705d0

.field public static sns_margin_medium_small:I = 0x7f0705d1

.field public static sns_margin_small:I = 0x7f0705d2

.field public static sns_margin_small_tiny:I = 0x7f0705d3

.field public static sns_margin_tiny:I = 0x7f0705d4

.field public static sns_margin_tiny_micro:I = 0x7f0705d5

.field public static sns_min_button_size:I = 0x7f0705d6

.field public static sns_normal_button_size:I = 0x7f0705d7

.field public static sns_pin_view_item_padding:I = 0x7f0705d8

.field public static sns_pin_view_item_spacing:I = 0x7f0705d9

.field public static sns_progress_bar_size_large:I = 0x7f0705da

.field public static sns_progress_bar_size_medium:I = 0x7f0705db

.field public static sns_progress_bar_size_small:I = 0x7f0705dc

.field public static sns_radiobutton_min_height:I = 0x7f0705dd

.field public static sns_radiobutton_top_bottom_padding:I = 0x7f0705de

.field public static sns_state_frame_radius:I = 0x7f0705df

.field public static sns_viewport_border_width:I = 0x7f0705e0

.field public static tooltip_corner_radius:I = 0x7f070664

.field public static tooltip_horizontal_padding:I = 0x7f070665

.field public static tooltip_margin:I = 0x7f070666

.field public static tooltip_precise_anchor_extra_offset:I = 0x7f070667

.field public static tooltip_precise_anchor_threshold:I = 0x7f070668

.field public static tooltip_vertical_padding:I = 0x7f070669

.field public static tooltip_y_offset_non_touch:I = 0x7f07066a

.field public static tooltip_y_offset_touch:I = 0x7f07066b


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
