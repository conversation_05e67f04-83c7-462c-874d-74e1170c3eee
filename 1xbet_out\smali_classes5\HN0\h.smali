.class public final synthetic LHN0/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/statistic/statistic_core/presentation/adapters/TeamNetBottomSheetAdapter;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/statistic/statistic_core/presentation/adapters/TeamNetBottomSheetAdapter;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LHN0/h;->a:Lorg/xbet/statistic/statistic_core/presentation/adapters/TeamNetBottomSheetAdapter;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LHN0/h;->a:Lorg/xbet/statistic/statistic_core/presentation/adapters/TeamNetBottomSheetAdapter;

    check-cast p1, LB4/a;

    invoke-static {v0, p1}, Lorg/xbet/statistic/statistic_core/presentation/adapters/TeamNetBottomSheetAdapter;->r(Lorg/xbet/statistic/statistic_core/presentation/adapters/TeamNetBottomSheetAdapter;LB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
