.class public Lgd1/a;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public a:I

.field public b:I

.field public c:I

.field public d:I

.field public e:I

.field public f:Z


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(III)V
    .locals 1

    .line 1
    iput p1, p0, Lgd1/a;->a:I

    .line 2
    .line 3
    const/4 p1, 0x0

    .line 4
    iput p1, p0, Lgd1/a;->c:I

    .line 5
    .line 6
    iput p1, p0, Lgd1/a;->d:I

    .line 7
    .line 8
    iput p1, p0, Lgd1/a;->b:I

    .line 9
    .line 10
    const/4 v0, -0x1

    .line 11
    iput v0, p0, Lgd1/a;->e:I

    .line 12
    .line 13
    const/4 v0, 0x1

    .line 14
    if-lez p3, :cond_0

    .line 15
    .line 16
    iput v0, p0, Lgd1/a;->d:I

    .line 17
    .line 18
    iput p3, p0, Lgd1/a;->b:I

    .line 19
    .line 20
    :cond_0
    if-lez p2, :cond_1

    .line 21
    .line 22
    iget p3, p0, Lgd1/a;->d:I

    .line 23
    .line 24
    or-int/lit8 p3, p3, 0x2

    .line 25
    .line 26
    iput p3, p0, Lgd1/a;->d:I

    .line 27
    .line 28
    iput p2, p0, Lgd1/a;->c:I

    .line 29
    .line 30
    :cond_1
    iget p2, p0, Lgd1/a;->b:I

    .line 31
    .line 32
    if-lez p2, :cond_2

    .line 33
    .line 34
    iget p3, p0, Lgd1/a;->c:I

    .line 35
    .line 36
    if-lez p3, :cond_2

    .line 37
    .line 38
    if-ge p2, p3, :cond_2

    .line 39
    .line 40
    const/4 p1, 0x1

    .line 41
    :cond_2
    iput-boolean p1, p0, Lgd1/a;->f:Z

    .line 42
    .line 43
    return-void
.end method

.method public b()I
    .locals 1

    .line 1
    iget v0, p0, Lgd1/a;->e:I

    .line 2
    .line 3
    return v0
.end method

.method public c()I
    .locals 2

    .line 1
    iget v0, p0, Lgd1/a;->a:I

    .line 2
    .line 3
    iget v1, p0, Lgd1/a;->b:I

    .line 4
    .line 5
    add-int/2addr v0, v1

    .line 6
    return v0
.end method

.method public d()I
    .locals 2

    .line 1
    iget v0, p0, Lgd1/a;->a:I

    .line 2
    .line 3
    iget v1, p0, Lgd1/a;->c:I

    .line 4
    .line 5
    add-int/2addr v0, v1

    .line 6
    add-int/lit8 v0, v0, -0x1

    .line 7
    .line 8
    return v0
.end method

.method public e()I
    .locals 1

    .line 1
    iget v0, p0, Lgd1/a;->c:I

    .line 2
    .line 3
    return v0
.end method

.method public f()I
    .locals 1

    .line 1
    iget v0, p0, Lgd1/a;->a:I

    .line 2
    .line 3
    return v0
.end method

.method public g()Z
    .locals 2

    .line 1
    iget v0, p0, Lgd1/a;->d:I

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    and-int/2addr v0, v1

    .line 5
    if-ne v0, v1, :cond_0

    .line 6
    .line 7
    return v1

    .line 8
    :cond_0
    const/4 v0, 0x0

    .line 9
    return v0
.end method

.method public h()Z
    .locals 2

    .line 1
    iget v0, p0, Lgd1/a;->d:I

    .line 2
    .line 3
    const/4 v1, 0x2

    .line 4
    and-int/2addr v0, v1

    .line 5
    if-ne v0, v1, :cond_0

    .line 6
    .line 7
    const/4 v0, 0x1

    .line 8
    return v0

    .line 9
    :cond_0
    const/4 v0, 0x0

    .line 10
    return v0
.end method

.method public i()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lgd1/a;->f:Z

    .line 2
    .line 3
    return v0
.end method

.method public j(I)V
    .locals 2

    .line 1
    iget v0, p0, Lgd1/a;->c:I

    .line 2
    .line 3
    iget v1, p0, Lgd1/a;->b:I

    .line 4
    .line 5
    sub-int/2addr v0, v1

    .line 6
    iput v0, p0, Lgd1/a;->c:I

    .line 7
    .line 8
    iget v0, p0, Lgd1/a;->a:I

    .line 9
    .line 10
    add-int/2addr v0, p1

    .line 11
    iput v0, p0, Lgd1/a;->a:I

    .line 12
    .line 13
    iget p1, p0, Lgd1/a;->d:I

    .line 14
    .line 15
    and-int/lit8 p1, p1, -0x2

    .line 16
    .line 17
    iput p1, p0, Lgd1/a;->d:I

    .line 18
    .line 19
    return-void
.end method

.method public k(I)V
    .locals 0

    .line 1
    iput p1, p0, Lgd1/a;->e:I

    .line 2
    .line 3
    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 11

    .line 1
    const/4 v0, 0x2

    .line 2
    const/4 v1, 0x1

    .line 3
    iget v2, p0, Lgd1/a;->d:I

    .line 4
    .line 5
    const/4 v3, 0x3

    .line 6
    and-int/lit8 v4, v2, 0x3

    .line 7
    .line 8
    if-ne v4, v3, :cond_0

    .line 9
    .line 10
    const-string v2, "both"

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    and-int/lit8 v4, v2, 0x1

    .line 14
    .line 15
    if-ne v4, v1, :cond_1

    .line 16
    .line 17
    const-string v2, "insert"

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_1
    and-int/lit8 v4, v2, 0x2

    .line 21
    .line 22
    if-ne v4, v0, :cond_2

    .line 23
    .line 24
    const-string v2, "remove"

    .line 25
    .line 26
    goto :goto_0

    .line 27
    :cond_2
    if-nez v2, :cond_3

    .line 28
    .line 29
    const-string v2, "none"

    .line 30
    .line 31
    goto :goto_0

    .line 32
    :cond_3
    const/4 v2, 0x0

    .line 33
    :goto_0
    if-eqz v2, :cond_4

    .line 34
    .line 35
    invoke-static {}, Ljava/util/Locale;->getDefault()Ljava/util/Locale;

    .line 36
    .line 37
    .line 38
    move-result-object v4

    .line 39
    iget v5, p0, Lgd1/a;->a:I

    .line 40
    .line 41
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 42
    .line 43
    .line 44
    move-result-object v5

    .line 45
    iget v6, p0, Lgd1/a;->b:I

    .line 46
    .line 47
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 48
    .line 49
    .line 50
    move-result-object v6

    .line 51
    iget v7, p0, Lgd1/a;->c:I

    .line 52
    .line 53
    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 54
    .line 55
    .line 56
    move-result-object v7

    .line 57
    iget v8, p0, Lgd1/a;->e:I

    .line 58
    .line 59
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 60
    .line 61
    .line 62
    move-result-object v8

    .line 63
    const/4 v9, 0x5

    .line 64
    new-array v9, v9, [Ljava/lang/Object;

    .line 65
    .line 66
    const/4 v10, 0x0

    .line 67
    aput-object v2, v9, v10

    .line 68
    .line 69
    aput-object v5, v9, v1

    .line 70
    .line 71
    aput-object v6, v9, v0

    .line 72
    .line 73
    aput-object v7, v9, v3

    .line 74
    .line 75
    const/4 v0, 0x4

    .line 76
    aput-object v8, v9, v0

    .line 77
    .line 78
    const-string v0, "[ DiffMeasures type=%s, diffStartPosition=%d, diffInsertLength=%d, diffRemoveLength=%d, cursor: %d ]"

    .line 79
    .line 80
    invoke-static {v4, v0, v9}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    return-object v0

    .line 85
    :cond_4
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 86
    .line 87
    new-instance v1, Ljava/lang/StringBuilder;

    .line 88
    .line 89
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 90
    .line 91
    .line 92
    const-string v2, "unknown behaviour for diffType "

    .line 93
    .line 94
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 95
    .line 96
    .line 97
    iget v2, p0, Lgd1/a;->d:I

    .line 98
    .line 99
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 100
    .line 101
    .line 102
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 103
    .line 104
    .line 105
    move-result-object v1

    .line 106
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 107
    .line 108
    .line 109
    throw v0
.end method
