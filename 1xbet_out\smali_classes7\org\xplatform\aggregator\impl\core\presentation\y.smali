.class public final synthetic Lorg/xplatform/aggregator/impl/core/presentation/y;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

.field public final synthetic b:L<PERSON><PERSON>/jvm/functions/Function1;

.field public final synthetic c:Lorg/xplatform/aggregator/api/model/Game;

.field public final synthetic d:I


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/y;->a:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/y;->b:<PERSON><PERSON><PERSON>/jvm/functions/Function1;

    iput-object p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/y;->c:Lorg/xplatform/aggregator/api/model/Game;

    iput p4, p0, Lorg/xplatform/aggregator/impl/core/presentation/y;->d:I

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/y;->a:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/y;->b:Lkotlin/jvm/functions/Function1;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/y;->c:Lorg/xplatform/aggregator/api/model/Game;

    iget v3, p0, Lorg/xplatform/aggregator/impl/core/presentation/y;->d:I

    invoke-static {v0, v1, v2, v3}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->a(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;I)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
