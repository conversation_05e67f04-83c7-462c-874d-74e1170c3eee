.class public final synthetic Lorg/xbet/spin_and_win/data/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/spin_and_win/data/b;->a:Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/b;->a:Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;

    invoke-static {v0}, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;->a(Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;)LXy0/a;

    move-result-object v0

    return-object v0
.end method
