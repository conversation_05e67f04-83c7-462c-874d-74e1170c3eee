.class public final LmP0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lr4/d;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\n\u0008\u0000\u0018\u00002\u00020\u0001B\'\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\u0008\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000c\u001a\u00020\u000bH\u0016\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u0011R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u0013R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0015R\u0014\u0010\u0008\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0015\u00a8\u0006\u0017"
    }
    d2 = {
        "LmP0/a;",
        "Lr4/d;",
        "",
        "teamId",
        "",
        "sportId",
        "",
        "eventId",
        "teamClId",
        "<init>",
        "(Ljava/lang/String;JII)V",
        "Landroidx/fragment/app/u;",
        "factory",
        "Landroidx/fragment/app/Fragment;",
        "createFragment",
        "(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;",
        "a",
        "Ljava/lang/String;",
        "b",
        "J",
        "c",
        "I",
        "d",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:J

.field public final c:I

.field public final d:I


# direct methods
.method public constructor <init>(Ljava/lang/String;JII)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LmP0/a;->a:Ljava/lang/String;

    .line 5
    .line 6
    iput-wide p2, p0, LmP0/a;->b:J

    .line 7
    .line 8
    iput p4, p0, LmP0/a;->c:I

    .line 9
    .line 10
    iput p5, p0, LmP0/a;->d:I

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public createFragment(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;
    .locals 6
    .param p1    # Landroidx/fragment/app/u;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/OneTeamStatisticMenuFragment;->F1:Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/OneTeamStatisticMenuFragment$a;

    .line 2
    .line 3
    iget-object v1, p0, LmP0/a;->a:Ljava/lang/String;

    .line 4
    .line 5
    iget-wide v2, p0, LmP0/a;->b:J

    .line 6
    .line 7
    iget v4, p0, LmP0/a;->c:I

    .line 8
    .line 9
    iget v5, p0, LmP0/a;->d:I

    .line 10
    .line 11
    invoke-virtual/range {v0 .. v5}, Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/OneTeamStatisticMenuFragment$a;->a(Ljava/lang/String;JII)Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/OneTeamStatisticMenuFragment;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    return-object p1
.end method

.method public getClearContainer()Z
    .locals 1

    .line 1
    invoke-static {p0}, Lr4/d$b;->a(Lr4/d;)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    return v0
.end method

.method public getScreenKey()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {p0}, Lr4/d$b;->b(Lr4/d;)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
