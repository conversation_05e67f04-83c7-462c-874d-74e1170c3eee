.class public final Lorg/xbet/crystal/presentation/views/CrystalWinLineView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation build Landroid/annotation/SuppressLint;
    value = {
        "ViewConstructor"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0012\u0008\u0007\u0018\u00002\u00020\u0001J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0002\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u001f\u0010\n\u001a\u00020\u00042\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\n\u0010\u000bJ7\u0010\u0012\u001a\u00020\u00042\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000e\u001a\u00020\u00072\u0006\u0010\u000f\u001a\u00020\u00072\u0006\u0010\u0010\u001a\u00020\u00072\u0006\u0010\u0011\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0012\u0010\u0013R\u0016\u0010\u0017\u001a\u00020\u00148\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0016R\u0016\u0010\u001a\u001a\u00020\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010\u0019R\u0016\u0010\u001c\u001a\u00020\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010\u0019R\u0016\u0010\u001f\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\u001eR\u0016\u0010!\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008 \u0010\u001eR\u0016\u0010#\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\"\u0010\u001eR\u0016\u0010%\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008$\u0010\u001e\u00a8\u0006&"
    }
    d2 = {
        "Lorg/xbet/crystal/presentation/views/CrystalWinLineView;",
        "Landroid/widget/FrameLayout;",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "textView",
        "",
        "setupCommonTextStyle",
        "(Landroidx/appcompat/widget/AppCompatTextView;)V",
        "",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "Lorg/xbet/crystal/presentation/views/Crystal;",
        "a",
        "Lorg/xbet/crystal/presentation/views/Crystal;",
        "iconView",
        "b",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "coeffView",
        "c",
        "sumView",
        "d",
        "I",
        "iconSize",
        "e",
        "coeffSize",
        "f",
        "sumSize",
        "g",
        "textHeight",
        "crystal_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public a:Lorg/xbet/crystal/presentation/views/Crystal;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:I

.field public e:I

.field public f:I

.field public g:I


# direct methods
.method private final setupCommonTextStyle(Landroidx/appcompat/widget/AppCompatTextView;)V
    .locals 4

    .line 1
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget v1, Lpb/e;->white:I

    .line 6
    .line 7
    invoke-static {v0, v1}, LF0/b;->getColor(Landroid/content/Context;I)I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setTextColor(I)V

    .line 12
    .line 13
    .line 14
    const/4 v0, 0x0

    .line 15
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 16
    .line 17
    .line 18
    const/4 v0, 0x1

    .line 19
    const/4 v1, 0x2

    .line 20
    const/16 v2, 0x8

    .line 21
    .line 22
    const/16 v3, 0x70

    .line 23
    .line 24
    invoke-static {p1, v2, v3, v0, v1}, LX0/o;->h(Landroid/widget/TextView;IIII)V

    .line 25
    .line 26
    .line 27
    return-void
.end method


# virtual methods
.method public onLayout(ZIIII)V
    .locals 2

    .line 1
    invoke-super/range {p0 .. p5}, Landroid/widget/FrameLayout;->onLayout(ZIIII)V

    .line 2
    .line 3
    .line 4
    move-object p1, p0

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    int-to-double p2, p2

    .line 10
    const-wide p4, 0x3fb999999999999aL    # 0.1

    .line 11
    .line 12
    .line 13
    .line 14
    .line 15
    mul-double p2, p2, p4

    .line 16
    .line 17
    double-to-int p2, p2

    .line 18
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 19
    .line 20
    .line 21
    move-result p3

    .line 22
    int-to-double p3, p3

    .line 23
    const-wide/high16 v0, 0x3fd0000000000000L    # 0.25

    .line 24
    .line 25
    mul-double p3, p3, v0

    .line 26
    .line 27
    double-to-int p3, p3

    .line 28
    iget-object p4, p1, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 29
    .line 30
    iget-object p5, p1, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 31
    .line 32
    invoke-virtual {p5}, Landroid/widget/TextView;->getTextSize()F

    .line 33
    .line 34
    .line 35
    move-result p5

    .line 36
    const/4 v0, 0x0

    .line 37
    invoke-virtual {p4, v0, p5}, Landroidx/appcompat/widget/AppCompatTextView;->setTextSize(IF)V

    .line 38
    .line 39
    .line 40
    iget-object p4, p1, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->a:Lorg/xbet/crystal/presentation/views/Crystal;

    .line 41
    .line 42
    iget p5, p1, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->d:I

    .line 43
    .line 44
    invoke-virtual {p4, v0, v0, p5, p5}, Landroid/view/View;->layout(IIII)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 48
    .line 49
    .line 50
    move-result p4

    .line 51
    iget p5, p1, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->g:I

    .line 52
    .line 53
    sub-int/2addr p4, p5

    .line 54
    div-int/lit8 p4, p4, 0x2

    .line 55
    .line 56
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 57
    .line 58
    .line 59
    move-result p5

    .line 60
    iget v0, p1, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->g:I

    .line 61
    .line 62
    add-int/2addr p5, v0

    .line 63
    div-int/lit8 p5, p5, 0x2

    .line 64
    .line 65
    iget v0, p1, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->d:I

    .line 66
    .line 67
    add-int/2addr v0, p2

    .line 68
    iget-object p2, p1, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 69
    .line 70
    iget v1, p1, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->e:I

    .line 71
    .line 72
    add-int/2addr v1, v0

    .line 73
    invoke-virtual {p2, v0, p4, v1, p5}, Landroid/view/View;->layout(IIII)V

    .line 74
    .line 75
    .line 76
    iget p2, p1, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->e:I

    .line 77
    .line 78
    add-int/2addr p2, p3

    .line 79
    add-int/2addr v0, p2

    .line 80
    iget-object p2, p1, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 81
    .line 82
    iget p3, p1, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->f:I

    .line 83
    .line 84
    add-int/2addr p3, v0

    .line 85
    invoke-virtual {p2, v0, p4, p3, p5}, Landroid/view/View;->layout(IIII)V

    .line 86
    .line 87
    .line 88
    return-void
.end method

.method public onMeasure(II)V
    .locals 3

    .line 1
    invoke-super {p0, p1, p2}, Landroid/widget/FrameLayout;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    int-to-double p1, p1

    .line 9
    const-wide v0, 0x3fb999999999999aL    # 0.1

    .line 10
    .line 11
    .line 12
    .line 13
    .line 14
    mul-double p1, p1, v0

    .line 15
    .line 16
    double-to-int p1, p1

    .line 17
    iput p1, p0, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->d:I

    .line 18
    .line 19
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    int-to-double p1, p1

    .line 24
    const-wide v0, 0x3fcccccccccccccdL    # 0.225

    .line 25
    .line 26
    .line 27
    .line 28
    .line 29
    mul-double p1, p1, v0

    .line 30
    .line 31
    double-to-int p1, p1

    .line 32
    iput p1, p0, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->e:I

    .line 33
    .line 34
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 35
    .line 36
    .line 37
    move-result p1

    .line 38
    int-to-double p1, p1

    .line 39
    const-wide v0, 0x3fd4cccccccccccdL    # 0.325

    .line 40
    .line 41
    .line 42
    .line 43
    .line 44
    mul-double p1, p1, v0

    .line 45
    .line 46
    double-to-int p1, p1

    .line 47
    iput p1, p0, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->f:I

    .line 48
    .line 49
    iget p1, p0, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->d:I

    .line 50
    .line 51
    div-int/lit8 p2, p1, 0x2

    .line 52
    .line 53
    iput p2, p0, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->g:I

    .line 54
    .line 55
    const/high16 p2, 0x40000000    # 2.0f

    .line 56
    .line 57
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 58
    .line 59
    .line 60
    move-result p1

    .line 61
    iget v0, p0, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->e:I

    .line 62
    .line 63
    invoke-static {v0, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 64
    .line 65
    .line 66
    move-result v0

    .line 67
    iget v1, p0, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->f:I

    .line 68
    .line 69
    invoke-static {v1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 70
    .line 71
    .line 72
    move-result v1

    .line 73
    iget v2, p0, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->g:I

    .line 74
    .line 75
    invoke-static {v2, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 76
    .line 77
    .line 78
    move-result p2

    .line 79
    iget-object v2, p0, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->a:Lorg/xbet/crystal/presentation/views/Crystal;

    .line 80
    .line 81
    invoke-virtual {v2, p1, p1}, Landroid/view/View;->measure(II)V

    .line 82
    .line 83
    .line 84
    iget-object p1, p0, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 85
    .line 86
    invoke-virtual {p1, v0, p2}, Landroid/view/View;->measure(II)V

    .line 87
    .line 88
    .line 89
    iget-object p1, p0, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->c:Landroidx/appcompat/widget/AppCompatTextView;

    .line 90
    .line 91
    invoke-virtual {p1, v1, p2}, Landroid/view/View;->measure(II)V

    .line 92
    .line 93
    .line 94
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 95
    .line 96
    .line 97
    move-result p1

    .line 98
    iget p2, p0, Lorg/xbet/crystal/presentation/views/CrystalWinLineView;->d:I

    .line 99
    .line 100
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 101
    .line 102
    .line 103
    return-void
.end method
