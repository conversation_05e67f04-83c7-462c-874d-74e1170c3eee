.class final Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.analytics.data.repositories.SysLogRepositoryImpl$logAppsFlyer$1"
    f = "SysLogRepositoryImpl.kt"
    l = {
        0x7a
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->i(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function1<",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0006\n\u0000\n\u0002\u0010\u0002\u0010\u0000\u001a\u00020\u0001H\n"
    }
    d2 = {
        "<anonymous>",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field final synthetic $request:Lvg/c;

.field label:I

.field final synthetic this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;


# direct methods
.method public constructor <init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Lvg/c;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;",
            "Lvg/c;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;->this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;->$request:Lvg/c;

    .line 4
    .line 5
    const/4 p1, 0x1

    .line 6
    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method


# virtual methods
.method public final create(Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;

    iget-object v1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;->this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;

    iget-object v2, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;->$request:Lvg/c;

    invoke-direct {v0, v1, v2, p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Lvg/c;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;->invoke(Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;->create(Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;

    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;->this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;

    .line 28
    .line 29
    invoke-virtual {p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->l()Z

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    if-nez p1, :cond_3

    .line 34
    .line 35
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;->this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;

    .line 36
    .line 37
    invoke-static {p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->x(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;)Z

    .line 38
    .line 39
    .line 40
    move-result p1

    .line 41
    if-nez p1, :cond_3

    .line 42
    .line 43
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;->this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;

    .line 44
    .line 45
    invoke-static {p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->u(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;)Lug/u;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    iget-object v1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;->$request:Lvg/c;

    .line 50
    .line 51
    iput v2, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;->label:I

    .line 52
    .line 53
    const-string v2, "Basic MXhiZXRtb2JpbGU6dUNwVFMxWVZQYjBoUEQ1Rnd3Mjg="

    .line 54
    .line 55
    invoke-virtual {p1, v1, v2, p0}, Lug/u;->e(Lvg/c;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    if-ne p1, v0, :cond_2

    .line 60
    .line 61
    return-object v0

    .line 62
    :cond_2
    :goto_0
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;->this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;

    .line 63
    .line 64
    invoke-static {p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->y(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;)V

    .line 65
    .line 66
    .line 67
    :cond_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 68
    .line 69
    return-object p1
.end method
