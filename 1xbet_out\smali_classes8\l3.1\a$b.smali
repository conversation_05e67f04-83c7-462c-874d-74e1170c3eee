.class public final Ll3/a$b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ll3/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ll3/m;",
            ">;"
        }
    .end annotation
.end field

.field public final b:Ll3/u;


# direct methods
.method public constructor <init>(Ll3/u;)V
    .locals 1

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Ll3/a$b;->a:Ljava/util/List;

    .line 4
    iput-object p1, p0, Ll3/a$b;->b:Ll3/u;

    return-void
.end method

.method public synthetic constructor <init>(Ll3/u;Ll3/a$a;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Ll3/a$b;-><init>(Ll3/u;)V

    return-void
.end method

.method public static synthetic a(Ll3/a$b;)Ljava/util/List;
    .locals 0

    .line 1
    iget-object p0, p0, Ll3/a$b;->a:Ljava/util/List;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic b(Ll3/a$b;)Ll3/u;
    .locals 0

    .line 1
    iget-object p0, p0, Ll3/a$b;->b:Ll3/u;

    .line 2
    .line 3
    return-object p0
.end method
