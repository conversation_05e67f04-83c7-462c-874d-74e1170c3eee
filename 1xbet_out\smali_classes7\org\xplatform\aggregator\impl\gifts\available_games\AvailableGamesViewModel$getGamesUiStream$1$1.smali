.class final Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.available_games.AvailableGamesViewModel$getGamesUiStream$1$1"
    f = "AvailableGamesViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "Lkotlin/coroutines/e<",
        "-",
        "LN21/d;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "LN21/d;",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/api/model/Game;)LN21/d;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $authorized:Z

.field final synthetic $favorites:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;ZLjava/util/Set;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;",
            "Z",
            "Ljava/util/Set<",
            "Ljava/lang/Long;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    iput-boolean p2, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->$authorized:Z

    iput-object p3, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->$favorites:Ljava/util/Set;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->$authorized:Z

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->$favorites:Ljava/util/Set;

    invoke-direct {v0, v1, v2, v3, p2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;ZLjava/util/Set;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/api/model/Game;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->invoke(Lorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "Lkotlin/coroutines/e<",
            "-",
            "LN21/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    move-object v0, p1

    .line 14
    check-cast v0, Lorg/xplatform/aggregator/api/model/Game;

    .line 15
    .line 16
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 17
    .line 18
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->w3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Ljava/util/Map;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 23
    .line 24
    .line 25
    move-result-wide v1

    .line 26
    invoke-static {v1, v2}, LHc/a;->f(J)Ljava/lang/Long;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-interface {p1, v1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 34
    .line 35
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->D3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)LHX0/e;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->$authorized:Z

    .line 40
    .line 41
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 42
    .line 43
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->F3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Z

    .line 44
    .line 45
    .line 46
    move-result v3

    .line 47
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->$favorites:Ljava/util/Set;

    .line 48
    .line 49
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 50
    .line 51
    .line 52
    move-result-wide v4

    .line 53
    invoke-static {v4, v5}, LHc/a;->f(J)Ljava/lang/Long;

    .line 54
    .line 55
    .line 56
    move-result-object v4

    .line 57
    invoke-interface {p1, v4}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    .line 58
    .line 59
    .line 60
    move-result v4

    .line 61
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$getGamesUiStream$1$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 62
    .line 63
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->B3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;)Lek0/o;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    invoke-virtual {p1}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 68
    .line 69
    .line 70
    move-result-object v5

    .line 71
    const/16 v7, 0x20

    .line 72
    .line 73
    const/4 v8, 0x0

    .line 74
    const/4 v6, 0x0

    .line 75
    invoke-static/range {v0 .. v8}, LQ91/c;->b(Lorg/xplatform/aggregator/api/model/Game;LHX0/e;ZZZLorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Ljava/lang/Integer;ILjava/lang/Object;)LN21/k;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    return-object p1

    .line 80
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 81
    .line 82
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 83
    .line 84
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 85
    .line 86
    .line 87
    throw p1
.end method
