.class public final LIN0/n;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u001a!\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u0002H\u0007\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "",
        "shimmerCount",
        "Landroidx/compose/ui/l;",
        "modifier",
        "",
        "c",
        "(ILandroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V",
        "statistic_core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(ILandroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, LIN0/n;->e(ILandroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(ILandroidx/compose/foundation/lazy/t;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LIN0/n;->d(ILandroidx/compose/foundation/lazy/t;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(ILandroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V
    .locals 17

    .line 1
    move/from16 v0, p0

    .line 2
    .line 3
    move/from16 v1, p3

    .line 4
    .line 5
    move/from16 v2, p4

    .line 6
    .line 7
    const v3, -0x21f548b4

    .line 8
    .line 9
    .line 10
    move-object/from16 v4, p2

    .line 11
    .line 12
    invoke-interface {v4, v3}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 13
    .line 14
    .line 15
    move-result-object v14

    .line 16
    and-int/lit8 v4, v2, 0x1

    .line 17
    .line 18
    const/4 v5, 0x4

    .line 19
    if-eqz v4, :cond_0

    .line 20
    .line 21
    or-int/lit8 v4, v1, 0x6

    .line 22
    .line 23
    goto :goto_1

    .line 24
    :cond_0
    and-int/lit8 v4, v1, 0x6

    .line 25
    .line 26
    if-nez v4, :cond_2

    .line 27
    .line 28
    invoke-interface {v14, v0}, Landroidx/compose/runtime/j;->x(I)Z

    .line 29
    .line 30
    .line 31
    move-result v4

    .line 32
    if-eqz v4, :cond_1

    .line 33
    .line 34
    const/4 v4, 0x4

    .line 35
    goto :goto_0

    .line 36
    :cond_1
    const/4 v4, 0x2

    .line 37
    :goto_0
    or-int/2addr v4, v1

    .line 38
    goto :goto_1

    .line 39
    :cond_2
    move v4, v1

    .line 40
    :goto_1
    and-int/lit8 v6, v2, 0x2

    .line 41
    .line 42
    if-eqz v6, :cond_4

    .line 43
    .line 44
    or-int/lit8 v4, v4, 0x30

    .line 45
    .line 46
    :cond_3
    move-object/from16 v7, p1

    .line 47
    .line 48
    goto :goto_3

    .line 49
    :cond_4
    and-int/lit8 v7, v1, 0x30

    .line 50
    .line 51
    if-nez v7, :cond_3

    .line 52
    .line 53
    move-object/from16 v7, p1

    .line 54
    .line 55
    invoke-interface {v14, v7}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 56
    .line 57
    .line 58
    move-result v8

    .line 59
    if-eqz v8, :cond_5

    .line 60
    .line 61
    const/16 v8, 0x20

    .line 62
    .line 63
    goto :goto_2

    .line 64
    :cond_5
    const/16 v8, 0x10

    .line 65
    .line 66
    :goto_2
    or-int/2addr v4, v8

    .line 67
    :goto_3
    and-int/lit8 v8, v4, 0x13

    .line 68
    .line 69
    const/16 v9, 0x12

    .line 70
    .line 71
    if-ne v8, v9, :cond_7

    .line 72
    .line 73
    invoke-interface {v14}, Landroidx/compose/runtime/j;->c()Z

    .line 74
    .line 75
    .line 76
    move-result v8

    .line 77
    if-nez v8, :cond_6

    .line 78
    .line 79
    goto :goto_4

    .line 80
    :cond_6
    invoke-interface {v14}, Landroidx/compose/runtime/j;->n()V

    .line 81
    .line 82
    .line 83
    move-object v4, v7

    .line 84
    goto :goto_7

    .line 85
    :cond_7
    :goto_4
    if-eqz v6, :cond_8

    .line 86
    .line 87
    sget-object v6, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 88
    .line 89
    goto :goto_5

    .line 90
    :cond_8
    move-object v6, v7

    .line 91
    :goto_5
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 92
    .line 93
    .line 94
    move-result v7

    .line 95
    if-eqz v7, :cond_9

    .line 96
    .line 97
    const/4 v7, -0x1

    .line 98
    const-string v8, "org.xbet.statistic.statistic_core.presentation.composable.ShimmerContent (ShimmerContent.kt:28)"

    .line 99
    .line 100
    invoke-static {v3, v4, v7, v8}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 101
    .line 102
    .line 103
    :cond_9
    const v3, 0x4c5de2

    .line 104
    .line 105
    .line 106
    invoke-interface {v14, v3}, Landroidx/compose/runtime/j;->t(I)V

    .line 107
    .line 108
    .line 109
    and-int/lit8 v3, v4, 0xe

    .line 110
    .line 111
    if-ne v3, v5, :cond_a

    .line 112
    .line 113
    const/4 v3, 0x1

    .line 114
    goto :goto_6

    .line 115
    :cond_a
    const/4 v3, 0x0

    .line 116
    :goto_6
    invoke-interface {v14}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 117
    .line 118
    .line 119
    move-result-object v5

    .line 120
    if-nez v3, :cond_b

    .line 121
    .line 122
    sget-object v3, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 123
    .line 124
    invoke-virtual {v3}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 125
    .line 126
    .line 127
    move-result-object v3

    .line 128
    if-ne v5, v3, :cond_c

    .line 129
    .line 130
    :cond_b
    new-instance v5, LIN0/l;

    .line 131
    .line 132
    invoke-direct {v5, v0}, LIN0/l;-><init>(I)V

    .line 133
    .line 134
    .line 135
    invoke-interface {v14, v5}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 136
    .line 137
    .line 138
    :cond_c
    move-object v13, v5

    .line 139
    check-cast v13, Lkotlin/jvm/functions/Function1;

    .line 140
    .line 141
    invoke-interface {v14}, Landroidx/compose/runtime/j;->q()V

    .line 142
    .line 143
    .line 144
    shr-int/lit8 v3, v4, 0x3

    .line 145
    .line 146
    and-int/lit8 v3, v3, 0xe

    .line 147
    .line 148
    const/high16 v4, 0xc00000

    .line 149
    .line 150
    or-int v15, v3, v4

    .line 151
    .line 152
    const/16 v16, 0x17e

    .line 153
    .line 154
    const/4 v5, 0x0

    .line 155
    move-object v4, v6

    .line 156
    const/4 v6, 0x0

    .line 157
    const/4 v7, 0x0

    .line 158
    const/4 v8, 0x0

    .line 159
    const/4 v9, 0x0

    .line 160
    const/4 v10, 0x0

    .line 161
    const/4 v11, 0x0

    .line 162
    const/4 v12, 0x0

    .line 163
    invoke-static/range {v4 .. v16}, Landroidx/compose/foundation/lazy/LazyDslKt;->b(Landroidx/compose/ui/l;Landroidx/compose/foundation/lazy/LazyListState;Landroidx/compose/foundation/layout/Y;ZLandroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/foundation/gestures/q;ZLandroidx/compose/foundation/O;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 164
    .line 165
    .line 166
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 167
    .line 168
    .line 169
    move-result v3

    .line 170
    if-eqz v3, :cond_d

    .line 171
    .line 172
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 173
    .line 174
    .line 175
    :cond_d
    :goto_7
    invoke-interface {v14}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 176
    .line 177
    .line 178
    move-result-object v3

    .line 179
    if-eqz v3, :cond_e

    .line 180
    .line 181
    new-instance v5, LIN0/m;

    .line 182
    .line 183
    invoke-direct {v5, v0, v4, v1, v2}, LIN0/m;-><init>(ILandroidx/compose/ui/l;II)V

    .line 184
    .line 185
    .line 186
    invoke-interface {v3, v5}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 187
    .line 188
    .line 189
    :cond_e
    return-void
.end method

.method public static final d(ILandroidx/compose/foundation/lazy/t;)Lkotlin/Unit;
    .locals 8

    .line 1
    sget-object v0, LIN0/a;->a:LIN0/a;

    .line 2
    .line 3
    invoke-virtual {v0}, LIN0/a;->b()LOc/o;

    .line 4
    .line 5
    .line 6
    move-result-object v5

    .line 7
    const/4 v6, 0x6

    .line 8
    const/4 v7, 0x0

    .line 9
    const/4 v3, 0x0

    .line 10
    const/4 v4, 0x0

    .line 11
    move v2, p0

    .line 12
    move-object v1, p1

    .line 13
    invoke-static/range {v1 .. v7}, Landroidx/compose/foundation/lazy/LazyListScope$-CC;->b(Landroidx/compose/foundation/lazy/t;ILkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;LOc/o;ILjava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method

.method public static final e(ILandroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    or-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    invoke-static {p2}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    invoke-static {p0, p1, p4, p2, p3}, LIN0/n;->c(ILandroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method
