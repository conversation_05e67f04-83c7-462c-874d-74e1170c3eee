.class public final Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lx40/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J3\u0010\r\u001a\u00020\u000c2\u0006\u0010\u0005\u001a\u00020\u00042\u0012\u0010\t\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00080\u00070\u00062\u0006\u0010\u000b\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008\r\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/a;",
        "Lx40/a;",
        "<init>",
        "()V",
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "LA4/d;",
        "",
        "LVX0/i;",
        "delegatesManager",
        "Lw40/a;",
        "oneXGameCategoryCardClickListener",
        "",
        "a",
        "(LUX0/k;LA4/d;Lw40/a;)V",
        "impl_games_section_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(LUX0/k;LA4/d;Lw40/a;)V
    .locals 1
    .param p1    # LUX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LA4/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lw40/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LUX0/k;",
            "LA4/d<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;",
            "Lw40/a;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-static {p3}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryWithGameItemAdapterDelegateKt;->e(Lw40/a;)LA4/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p2, v0}, LA4/d;->c(LA4/c;)LA4/d;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    invoke-static {p1, p3}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryListItemAdapterDelegateKt;->g(LUX0/k;Lw40/a;)LA4/c;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {p2, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 14
    .line 15
    .line 16
    return-void
.end method
