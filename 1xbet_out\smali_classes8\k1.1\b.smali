.class public Lk1/b;
.super Lk1/d;
.source "SourceFile"


# static fields
.field public static final c:[F


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const/16 v0, 0xc9

    .line 2
    .line 3
    new-array v0, v0, [F

    .line 4
    .line 5
    fill-array-data v0, :array_0

    .line 6
    .line 7
    .line 8
    sput-object v0, Lk1/b;->c:[F

    .line 9
    .line 10
    return-void

    .line 11
    :array_0
    .array-data 4
        0x0
        0x38d1b717
        0x3951b717
        0x3a03126f
        0x3a6bedfa
        0x3ab78034
        0x3b03126f
        0x3b30f27c
        0x3b6bedfa
        0x3b96bb99
        0x3bbe0ded
        0x3be8a71e
        0x3c0b4396
        0x3c257a78
        0x3c4154ca
        0x3c6075f7
        0x3c816f00
        0x3c9374bc
        0x3ca7ef9e
        0x3cbd3c36
        0x3cd42c3d
        0x3cecbfb1
        0x3d037b4a
        0x3d116873
        0x3d202752
        0x3d3020c5
        0x3d40ebee
        0x3d5288ce
        0x3d656042
        0x3d79096c
        0x3d872b02
        0x3d923a2a
        0x3d9de69b
        0x3da9fbe7
        0x3db6e2eb
        0x3dc46738
        0x3dd2bd3c
        0x3de17c1c
        0x3df10cb3
        0x3e00b780
        0x3e091d15
        0x3e1205bc
        0x3e1b3d08
        0x3e24dd2f
        0x3e2ecbfb
        0x3e3923a3
        0x3e43e426
        0x3e4ef34d
        0x3e5a6b51
        0x3e6631f9
        0x3e724745
        0x3e7eab36
        0x3e85aee6
        0x3e8c2f83
        0x3e92ca58
        0x3e997f63
        0x3ea04ea5
        0x3ea72b02
        0x3eae147b
        0x3eb50b0f
        0x3ebc0ebf
        0x3ec3126f
        0x3eca0903
        0x3ed0ff97
        0x3ed7e910
        0x3eded289
        0x3ee5a1cb
        0x3eec56d6
        0x3ef2fec5
        0x3ef98c7e
        0x3f000000    # 0.5f
        0x3f032ca5
        0x3f064c30
        0x3f095810
        0x3f0c56d6
        0x3f0f4880
        0x3f122d0e
        0x3f14fdf4
        0x3f17c1be
        0x3f1a71de
        0x3f1d14e4
        0x3f1faace
        0x3f222d0e
        0x3f24a234
        0x3f270a3d
        0x3f295e9e
        0x3f2bac71
        0x3f2de69b
        0x3f3013a9
        0x3f32339c
        0x3f344d01
        0x3f3652bd
        0x3f384b5e
        0x3f3a3d71
        0x3f3c2268
        0x3f3dfa44
        0x3f3fc505
        0x3f4182aa
        0x3f4339c1
        0x3f44ea4b
        0x3f468db9
        0x3f48240b
        0x3f49b3d0
        0x3f4b367a
        0x3f4cb296
        0x3f4e2824
        0x3f4f9097
        0x3f50f27c
        0x3f524dd3
        0x3f539c0f
        0x3f54ea4b
        0x3f562b6b
        0x3f5765fe
        0x3f589a02
        0x3f59c77a
        0x3f5ae7d5
        0x3f5c0831
        0x3f5d21ff
        0x3f5e2eb2
        0x3f5f3b64
        0x3f604189
        0x3f613a93
        0x3f62339c
        0x3f632618
        0x3f641206
        0x3f64fdf4
        0x3f65dcc6
        0x3f66bb99
        0x3f6793de
        0x3f686595
        0x3f6930be
        0x3f69f55a
        0x3f6ab9f5
        0x3f6b7803
        0x3f6c3611
        0x3f6ce704
        0x3f6d97f6
        0x3f6e425b
        0x3f6eecc0
        0x3f6f9097
        0x3f702de0
        0x3f70cb29
        0x3f7161e5
        0x3f71f8a1
        0x3f7288ce
        0x3f73126f
        0x3f739c0f
        0x3f741f21
        0x3f74a234
        0x3f751eb8
        0x3f759b3d
        0x3f761134
        0x3f76809d
        0x3f76f007
        0x3f775f70
        0x3f77c84b
        0x3f783127
        0x3f789375
        0x3f78f5c3
        0x3f795183
        0x3f79ad43
        0x3f7a0275
        0x3f7a57a8
        0x3f7aa64c
        0x3f7af4f1
        0x3f7b4396
        0x3f7b8bac
        0x3f7bd3c3
        0x3f7c1bda
        0x3f7c5d64
        0x3f7c985f
        0x3f7cd9e8
        0x3f7d0e56
        0x3f7d4952
        0x3f7d7dbf
        0x3f7db22d
        0x3f7de00d
        0x3f7e0ded
        0x3f7e3bcd
        0x3f7e6320
        0x3f7e9100
        0x3f7eb1c4
        0x3f7ed917
        0x3f7ef9db
        0x3f7f1412
        0x3f7f34d7
        0x3f7f4f0e
        0x3f7f6944
        0x3f7f7cee
        0x3f7f9724
        0x3f7fa440
        0x3f7fb7e9
        0x3f7fc505
        0x3f7fd220
        0x3f7fdf3b
        0x3f7fec57
        0x3f7ff2e5
        0x3f7ff972
        0x3f7ff972
        0x3f800000    # 1.0f
        0x3f800000    # 1.0f
    .end array-data
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    sget-object v0, Lk1/b;->c:[F

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lk1/d;-><init>([F)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public bridge synthetic getInterpolation(F)F
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lk1/d;->getInterpolation(F)F

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method
