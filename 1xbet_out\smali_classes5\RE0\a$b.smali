.class public final LRE0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LRE0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LRE0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LRE0/a$b$a;
    }
.end annotation


# instance fields
.field public final a:LRE0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LOE0/c;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LOE0/a;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/grand_prix/data/repositories/GrandPrixStatisticRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/grand_prix/domain/usecases/LoadGrandPrixStatisticUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/grand_prix/domain/usecases/c;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/grand_prix/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/grand_prix/domain/usecases/a;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/grand_prix/domain/usecases/UpdateGrandPrixStagesStatisticUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/grand_prix/domain/usecases/e;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/grand_prix/presentation/viewmodels/GrandPrixStatisticViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/grand_prix/presentation/viewmodels/SeasonsBottomSheetViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;LOE0/a;LSX0/a;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LRE0/a$b;->a:LRE0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p10}, LRE0/a$b;->c(LQW0/c;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;LOE0/a;LSX0/a;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;LOE0/a;LSX0/a;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lc8/h;LRE0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p10}, LRE0/a$b;-><init>(LQW0/c;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;LOE0/a;LSX0/a;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/grand_prix/presentation/fragments/GrandPrixStatisticFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LRE0/a$b;->d(Lorg/xbet/statistic/grand_prix/presentation/fragments/GrandPrixStatisticFragment;)Lorg/xbet/statistic/grand_prix/presentation/fragments/GrandPrixStatisticFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/statistic/grand_prix/presentation/dialogs/SeasonsBottomSheetFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LRE0/a$b;->e(Lorg/xbet/statistic/grand_prix/presentation/dialogs/SeasonsBottomSheetFragment;)Lorg/xbet/statistic/grand_prix/presentation/dialogs/SeasonsBottomSheetFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final c(LQW0/c;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;LOE0/a;LSX0/a;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lc8/h;)V
    .locals 12

    .line 1
    new-instance v0, LRE0/a$b$a;

    .line 2
    .line 3
    invoke-direct {v0, p1}, LRE0/a$b$a;-><init>(LQW0/c;)V

    .line 4
    .line 5
    .line 6
    iput-object v0, p0, LRE0/a$b;->b:Ldagger/internal/h;

    .line 7
    .line 8
    invoke-static/range {p4 .. p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    iput-object p1, p0, LRE0/a$b;->c:Ldagger/internal/h;

    .line 13
    .line 14
    invoke-static {p1}, LOE0/d;->a(LBc/a;)LOE0/d;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    iput-object p1, p0, LRE0/a$b;->d:Ldagger/internal/h;

    .line 19
    .line 20
    invoke-static/range {p5 .. p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iput-object p1, p0, LRE0/a$b;->e:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iput-object p1, p0, LRE0/a$b;->f:Ldagger/internal/h;

    .line 31
    .line 32
    iget-object v0, p0, LRE0/a$b;->b:Ldagger/internal/h;

    .line 33
    .line 34
    iget-object v1, p0, LRE0/a$b;->d:Ldagger/internal/h;

    .line 35
    .line 36
    iget-object v2, p0, LRE0/a$b;->e:Ldagger/internal/h;

    .line 37
    .line 38
    invoke-static {v0, v1, v2, p1}, Lorg/xbet/statistic/grand_prix/data/repositories/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/grand_prix/data/repositories/a;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    iput-object p1, p0, LRE0/a$b;->g:Ldagger/internal/h;

    .line 43
    .line 44
    invoke-static {p1}, Lorg/xbet/statistic/grand_prix/domain/usecases/g;->a(LBc/a;)Lorg/xbet/statistic/grand_prix/domain/usecases/g;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput-object p1, p0, LRE0/a$b;->h:Ldagger/internal/h;

    .line 49
    .line 50
    iget-object p1, p0, LRE0/a$b;->g:Ldagger/internal/h;

    .line 51
    .line 52
    invoke-static {p1}, Lorg/xbet/statistic/grand_prix/domain/usecases/d;->a(LBc/a;)Lorg/xbet/statistic/grand_prix/domain/usecases/d;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    iput-object p1, p0, LRE0/a$b;->i:Ldagger/internal/h;

    .line 57
    .line 58
    iget-object p1, p0, LRE0/a$b;->g:Ldagger/internal/h;

    .line 59
    .line 60
    invoke-static {p1}, Lorg/xbet/statistic/grand_prix/domain/usecases/j;->a(LBc/a;)Lorg/xbet/statistic/grand_prix/domain/usecases/j;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    iput-object p1, p0, LRE0/a$b;->j:Ldagger/internal/h;

    .line 65
    .line 66
    iget-object p1, p0, LRE0/a$b;->g:Ldagger/internal/h;

    .line 67
    .line 68
    invoke-static {p1}, Lorg/xbet/statistic/grand_prix/domain/usecases/b;->a(LBc/a;)Lorg/xbet/statistic/grand_prix/domain/usecases/b;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iput-object p1, p0, LRE0/a$b;->k:Ldagger/internal/h;

    .line 73
    .line 74
    iget-object p1, p0, LRE0/a$b;->g:Ldagger/internal/h;

    .line 75
    .line 76
    invoke-static {p1}, Lorg/xbet/statistic/grand_prix/domain/usecases/h;->a(LBc/a;)Lorg/xbet/statistic/grand_prix/domain/usecases/h;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    iput-object p1, p0, LRE0/a$b;->l:Ldagger/internal/h;

    .line 81
    .line 82
    iget-object p1, p0, LRE0/a$b;->g:Ldagger/internal/h;

    .line 83
    .line 84
    invoke-static {p1}, Lorg/xbet/statistic/grand_prix/domain/usecases/f;->a(LBc/a;)Lorg/xbet/statistic/grand_prix/domain/usecases/f;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    iput-object p1, p0, LRE0/a$b;->m:Ldagger/internal/h;

    .line 89
    .line 90
    invoke-static/range {p7 .. p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    iput-object p1, p0, LRE0/a$b;->n:Ldagger/internal/h;

    .line 95
    .line 96
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    iput-object p1, p0, LRE0/a$b;->o:Ldagger/internal/h;

    .line 101
    .line 102
    invoke-static/range {p6 .. p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 103
    .line 104
    .line 105
    move-result-object p1

    .line 106
    iput-object p1, p0, LRE0/a$b;->p:Ldagger/internal/h;

    .line 107
    .line 108
    invoke-static/range {p8 .. p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    iput-object p1, p0, LRE0/a$b;->q:Ldagger/internal/h;

    .line 113
    .line 114
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 115
    .line 116
    .line 117
    move-result-object p1

    .line 118
    iput-object p1, p0, LRE0/a$b;->r:Ldagger/internal/h;

    .line 119
    .line 120
    invoke-static {p2}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 121
    .line 122
    .line 123
    move-result-object v11

    .line 124
    iput-object v11, p0, LRE0/a$b;->s:Ldagger/internal/h;

    .line 125
    .line 126
    iget-object v0, p0, LRE0/a$b;->h:Ldagger/internal/h;

    .line 127
    .line 128
    iget-object v1, p0, LRE0/a$b;->i:Ldagger/internal/h;

    .line 129
    .line 130
    iget-object v2, p0, LRE0/a$b;->j:Ldagger/internal/h;

    .line 131
    .line 132
    iget-object v3, p0, LRE0/a$b;->k:Ldagger/internal/h;

    .line 133
    .line 134
    iget-object v4, p0, LRE0/a$b;->l:Ldagger/internal/h;

    .line 135
    .line 136
    iget-object v5, p0, LRE0/a$b;->m:Ldagger/internal/h;

    .line 137
    .line 138
    iget-object v6, p0, LRE0/a$b;->n:Ldagger/internal/h;

    .line 139
    .line 140
    iget-object v7, p0, LRE0/a$b;->o:Ldagger/internal/h;

    .line 141
    .line 142
    iget-object v8, p0, LRE0/a$b;->p:Ldagger/internal/h;

    .line 143
    .line 144
    iget-object v9, p0, LRE0/a$b;->q:Ldagger/internal/h;

    .line 145
    .line 146
    iget-object v10, p0, LRE0/a$b;->r:Ldagger/internal/h;

    .line 147
    .line 148
    invoke-static/range {v0 .. v11}, Lorg/xbet/statistic/grand_prix/presentation/viewmodels/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/grand_prix/presentation/viewmodels/a;

    .line 149
    .line 150
    .line 151
    move-result-object p1

    .line 152
    iput-object p1, p0, LRE0/a$b;->t:Ldagger/internal/h;

    .line 153
    .line 154
    iget-object p1, p0, LRE0/a$b;->m:Ldagger/internal/h;

    .line 155
    .line 156
    invoke-static {p1}, Lorg/xbet/statistic/grand_prix/presentation/viewmodels/b;->a(LBc/a;)Lorg/xbet/statistic/grand_prix/presentation/viewmodels/b;

    .line 157
    .line 158
    .line 159
    move-result-object p1

    .line 160
    iput-object p1, p0, LRE0/a$b;->u:Ldagger/internal/h;

    .line 161
    .line 162
    return-void
.end method

.method public final d(Lorg/xbet/statistic/grand_prix/presentation/fragments/GrandPrixStatisticFragment;)Lorg/xbet/statistic/grand_prix/presentation/fragments/GrandPrixStatisticFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LRE0/a$b;->g()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/grand_prix/presentation/fragments/f;->a(Lorg/xbet/statistic/grand_prix/presentation/fragments/GrandPrixStatisticFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final e(Lorg/xbet/statistic/grand_prix/presentation/dialogs/SeasonsBottomSheetFragment;)Lorg/xbet/statistic/grand_prix/presentation/dialogs/SeasonsBottomSheetFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LRE0/a$b;->g()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/grand_prix/presentation/dialogs/c;->a(Lorg/xbet/statistic/grand_prix/presentation/dialogs/SeasonsBottomSheetFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final f()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x2

    .line 2
    invoke-static {v0}, Ldagger/internal/f;->b(I)Ldagger/internal/f;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    const-class v1, Lorg/xbet/statistic/grand_prix/presentation/viewmodels/GrandPrixStatisticViewModel;

    .line 7
    .line 8
    iget-object v2, p0, LRE0/a$b;->t:Ldagger/internal/h;

    .line 9
    .line 10
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    const-class v1, Lorg/xbet/statistic/grand_prix/presentation/viewmodels/SeasonsBottomSheetViewModel;

    .line 15
    .line 16
    iget-object v2, p0, LRE0/a$b;->u:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {v0}, Ldagger/internal/f;->a()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    return-object v0
.end method

.method public final g()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LRE0/a$b;->f()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
