.class public final Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0003\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u001f\u0010\t\u001a\u00020\u00082\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\t\u0010\nR\u0014\u0010\u000c\u001a\u00020\u000b8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$a;",
        "",
        "<init>",
        "()V",
        "Lorg/xbet/games_section/api/models/GameBonus;",
        "gameBonus",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
        "gameType",
        "Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;",
        "a",
        "(Lorg/xbet/games_section/api/models/GameBonus;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;",
        "",
        "GAME_TYPE_EXTRA",
        "Ljava/lang/String;",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lorg/xbet/games_section/api/models/GameBonus;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;
    .locals 1
    .param p1    # Lorg/xbet/games_section/api/models/GameBonus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0, p1}, Lorg/xbet/core/presentation/holder/OnexGamesHolderFragment;->L3(Lorg/xbet/games_section/api/models/GameBonus;)V

    .line 7
    .line 8
    .line 9
    invoke-virtual {p2}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->getGameId()J

    .line 10
    .line 11
    .line 12
    move-result-wide p1

    .line 13
    invoke-static {v0, p1, p2}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->h4(Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;J)V

    .line 14
    .line 15
    .line 16
    return-object v0
.end method
