.class public interface abstract LN1/t;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final q0:LN1/t;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LN1/t$a;

    .line 2
    .line 3
    invoke-direct {v0}, LN1/t$a;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LN1/t;->q0:LN1/t;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public abstract l()V
.end method

.method public abstract n(II)LN1/T;
.end method

.method public abstract q(LN1/M;)V
.end method
