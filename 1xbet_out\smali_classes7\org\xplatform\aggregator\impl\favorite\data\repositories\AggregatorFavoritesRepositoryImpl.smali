.class public final Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lu81/b;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0080\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0010\"\n\u0002\u0008\u0018\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0001\u0018\u00002\u00020\u0001B9\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJF\u0010\u001c\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u001a2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0019\u001a\u00020\u0014H\u0096@\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\\\u0010!\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u001a2\u0006\u0010\u001e\u001a\u00020\u00102\u000c\u0010\u001f\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u001a2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010 \u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0019\u001a\u00020\u0014H\u0096@\u00a2\u0006\u0004\u0008!\u0010\"J\u0015\u0010%\u001a\u0008\u0012\u0004\u0012\u00020$0#H\u0016\u00a2\u0006\u0004\u0008%\u0010&J\u000f\u0010\'\u001a\u00020$H\u0016\u00a2\u0006\u0004\u0008\'\u0010(J<\u0010+\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u001a2\u000c\u0010*\u001a\u0008\u0012\u0004\u0012\u00020\u00100)2\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0016H\u0096@\u00a2\u0006\u0004\u0008+\u0010,J6\u0010.\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u001a2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u001e\u001a\u00020\u00102\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010-\u001a\u00020\u0014H\u0096@\u00a2\u0006\u0004\u0008.\u0010/J>\u00100\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u001a2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u001e\u001a\u00020\u00102\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010-\u001a\u00020\u00142\u0006\u0010\u0019\u001a\u00020\u0014H\u0096@\u00a2\u0006\u0004\u00080\u00101J&\u00102\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u001a2\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0016H\u0096@\u00a2\u0006\u0004\u00082\u00103J,\u00104\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u001b0\u001a0#2\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0016H\u0096@\u00a2\u0006\u0004\u00084\u00103J+\u00105\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u001b0\u001a0#2\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0016H\u0016\u00a2\u0006\u0004\u00085\u00106J(\u00108\u001a\u00020\u00142\u0006\u00107\u001a\u00020\u001b2\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0016H\u0096@\u00a2\u0006\u0004\u00088\u00109J(\u0010;\u001a\u00020$2\u0006\u00107\u001a\u00020\u001b2\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010:\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0004\u0008;\u0010<J(\u0010>\u001a\u00020$2\u0006\u0010=\u001a\u00020\u00102\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010:\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0004\u0008>\u0010?J\u0010\u0010@\u001a\u00020$H\u0096@\u00a2\u0006\u0004\u0008@\u0010AJ(\u0010D\u001a\u00020$2\u0006\u0010C\u001a\u00020B2\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010:\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0004\u0008D\u0010EJ\u0017\u0010F\u001a\u00020$2\u0006\u00107\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008F\u0010GJ\u0017\u0010H\u001a\u00020$2\u0006\u0010=\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008H\u0010IJ \u0010J\u001a\u00020$2\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0016H\u0082@\u00a2\u0006\u0004\u0008J\u00103J \u0010K\u001a\u00020$2\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0016H\u0082@\u00a2\u0006\u0004\u0008K\u00103J\u0013\u0010M\u001a\u00020L*\u00020BH\u0002\u00a2\u0006\u0004\u0008M\u0010NR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u0010OR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008;\u0010PR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010QR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010RR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010SR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u0010TR\u0014\u0010W\u001a\u00020U8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010V\u00a8\u0006X"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;",
        "Lu81/b;",
        "Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;",
        "remoteDataSource",
        "Lorg/xplatform/aggregator/impl/core/data/datasources/a;",
        "localDataSource",
        "LT91/a;",
        "favoritesLocalDataSource",
        "Lc8/h;",
        "requestParamsDataSource",
        "LS8/a;",
        "profileLocalDataSource",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "tokenRefresher",
        "<init>",
        "(Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;Lorg/xplatform/aggregator/impl/core/data/datasources/a;LT91/a;Lc8/h;LS8/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)V",
        "",
        "categoryId",
        "",
        "limit",
        "",
        "test",
        "",
        "endPoint",
        "brandsApi",
        "isForceUpdate",
        "",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "g",
        "(JIZLjava/lang/String;ZZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "partitionId",
        "filtersList",
        "filterType",
        "d",
        "(JLjava/util/List;IZLjava/lang/String;ZLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "o",
        "()Lkotlinx/coroutines/flow/e;",
        "e",
        "()V",
        "",
        "gamesId",
        "j",
        "(Ljava/util/Set;ZZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "hasAggregatorBrands",
        "c",
        "(IJLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "i",
        "(IJLjava/lang/String;ZZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "n",
        "(ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "f",
        "h",
        "(ZLjava/lang/String;)Lkotlinx/coroutines/flow/e;",
        "game",
        "a",
        "(Lorg/xplatform/aggregator/api/model/Game;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "subcategoryId",
        "b",
        "(Lorg/xplatform/aggregator/api/model/Game;ZILkotlin/coroutines/e;)Ljava/lang/Object;",
        "gameId",
        "k",
        "(JZILkotlin/coroutines/e;)Ljava/lang/Object;",
        "m",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xplatform/aggregator/api/model/FavoriteClearSource;",
        "source",
        "l",
        "(Lorg/xplatform/aggregator/api/model/FavoriteClearSource;ZILkotlin/coroutines/e;)Ljava/lang/Object;",
        "y",
        "(Lorg/xplatform/aggregator/api/model/Game;)V",
        "B",
        "(J)V",
        "z",
        "A",
        "Lorg/xplatform/aggregator/impl/favorite/data/models/FavoriteClearSourceRequest;",
        "C",
        "(Lorg/xplatform/aggregator/api/model/FavoriteClearSource;)Lorg/xplatform/aggregator/impl/favorite/data/models/FavoriteClearSourceRequest;",
        "Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;",
        "Lorg/xplatform/aggregator/impl/core/data/datasources/a;",
        "LT91/a;",
        "Lc8/h;",
        "LS8/a;",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "Lkotlinx/coroutines/sync/a;",
        "Lkotlinx/coroutines/sync/a;",
        "mutex",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xplatform/aggregator/impl/core/data/datasources/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LT91/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LS8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lkotlinx/coroutines/sync/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;Lorg/xplatform/aggregator/impl/core/data/datasources/a;LT91/a;Lc8/h;LS8/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/core/data/datasources/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LT91/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LS8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lcom/xbet/onexuser/domain/managers/TokenRefresher;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/core/data/datasources/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->e:LS8/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->f:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 15
    .line 16
    const/4 p1, 0x1

    .line 17
    const/4 p2, 0x0

    .line 18
    const/4 p3, 0x0

    .line 19
    invoke-static {p3, p1, p2}, Lkotlinx/coroutines/sync/MutexKt;->b(ZILjava/lang/Object;)Lkotlinx/coroutines/sync/a;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->g:Lkotlinx/coroutines/sync/a;

    .line 24
    .line 25
    return-void
.end method

.method public static final synthetic p(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->y(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic q(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->z(ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic r(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->A(ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic s(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)LT91/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)Lc8/h;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->f:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;J)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->B(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic x(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lorg/xplatform/aggregator/api/model/FavoriteClearSource;)Lorg/xplatform/aggregator/impl/favorite/data/models/FavoriteClearSourceRequest;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->C(Lorg/xplatform/aggregator/api/model/FavoriteClearSource;)Lorg/xplatform/aggregator/impl/favorite/data/models/FavoriteClearSourceRequest;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public final A(ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p3

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p3}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p3, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x0

    .line 34
    const/4 v4, 0x2

    .line 35
    const/4 v5, 0x1

    .line 36
    const/4 v6, 0x0

    .line 37
    if-eqz v2, :cond_3

    .line 38
    .line 39
    if-eq v2, v5, :cond_2

    .line 40
    .line 41
    if-ne v2, v4, :cond_1

    .line 42
    .line 43
    iget-object p1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->L$0:Ljava/lang/Object;

    .line 44
    .line 45
    check-cast p1, Lkotlinx/coroutines/sync/a;

    .line 46
    .line 47
    :try_start_0
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 48
    .line 49
    .line 50
    goto :goto_3

    .line 51
    :catchall_0
    move-exception p2

    .line 52
    goto/16 :goto_5

    .line 53
    .line 54
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 55
    .line 56
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 57
    .line 58
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 59
    .line 60
    .line 61
    throw p1

    .line 62
    :cond_2
    iget-boolean p1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->Z$0:Z

    .line 63
    .line 64
    iget-object p2, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->L$1:Ljava/lang/Object;

    .line 65
    .line 66
    check-cast p2, Lkotlinx/coroutines/sync/a;

    .line 67
    .line 68
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->L$0:Ljava/lang/Object;

    .line 69
    .line 70
    check-cast v2, Ljava/lang/String;

    .line 71
    .line 72
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 73
    .line 74
    .line 75
    goto :goto_1

    .line 76
    :cond_3
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 77
    .line 78
    .line 79
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->g:Lkotlinx/coroutines/sync/a;

    .line 80
    .line 81
    iput-object p2, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->L$0:Ljava/lang/Object;

    .line 82
    .line 83
    iput-object p3, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->L$1:Ljava/lang/Object;

    .line 84
    .line 85
    iput-boolean p1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->Z$0:Z

    .line 86
    .line 87
    iput v5, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->label:I

    .line 88
    .line 89
    invoke-interface {p3, v6, v0}, Lkotlinx/coroutines/sync/a;->g(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 90
    .line 91
    .line 92
    move-result-object v2

    .line 93
    if-ne v2, v1, :cond_4

    .line 94
    .line 95
    goto :goto_2

    .line 96
    :cond_4
    move-object v2, p2

    .line 97
    move-object p2, p3

    .line 98
    :goto_1
    :try_start_1
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 99
    .line 100
    invoke-virtual {p3}, LT91/a;->l()Z

    .line 101
    .line 102
    .line 103
    move-result p3

    .line 104
    if-eqz p3, :cond_6

    .line 105
    .line 106
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->f:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 107
    .line 108
    new-instance v5, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$2$games$1;

    .line 109
    .line 110
    invoke-direct {v5, p1, p0, v2, v6}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$2$games$1;-><init>(ZLorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 111
    .line 112
    .line 113
    iput-object p2, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->L$0:Ljava/lang/Object;

    .line 114
    .line 115
    iput-object v6, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->L$1:Ljava/lang/Object;

    .line 116
    .line 117
    iput v4, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavoritesWithoutCatching$1;->label:I

    .line 118
    .line 119
    invoke-virtual {p3, v5, v0}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 120
    .line 121
    .line 122
    move-result-object p3
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 123
    if-ne p3, v1, :cond_5

    .line 124
    .line 125
    :goto_2
    return-object v1

    .line 126
    :cond_5
    move-object p1, p2

    .line 127
    :goto_3
    :try_start_2
    check-cast p3, Ljava/util/List;

    .line 128
    .line 129
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 130
    .line 131
    invoke-virtual {p2, v3}, LT91/a;->q(Z)V

    .line 132
    .line 133
    .line 134
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 135
    .line 136
    invoke-virtual {p2, p3}, LT91/a;->b(Ljava/util/List;)V

    .line 137
    .line 138
    .line 139
    goto :goto_4

    .line 140
    :catchall_1
    move-exception p1

    .line 141
    move-object v7, p2

    .line 142
    move-object p2, p1

    .line 143
    move-object p1, v7

    .line 144
    goto :goto_5

    .line 145
    :cond_6
    move-object p1, p2

    .line 146
    :goto_4
    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 147
    .line 148
    invoke-interface {p1, v6}, Lkotlinx/coroutines/sync/a;->h(Ljava/lang/Object;)V

    .line 149
    .line 150
    .line 151
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 152
    .line 153
    return-object p1

    .line 154
    :goto_5
    invoke-interface {p1, v6}, Lkotlinx/coroutines/sync/a;->h(Ljava/lang/Object;)V

    .line 155
    .line 156
    .line 157
    throw p2
.end method

.method public final B(J)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, LT91/a;->m(J)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final C(Lorg/xplatform/aggregator/api/model/FavoriteClearSource;)Lorg/xplatform/aggregator/impl/favorite/data/models/FavoriteClearSourceRequest;
    .locals 1

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p1, v0, :cond_2

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p1, v0, :cond_1

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-ne p1, v0, :cond_0

    .line 17
    .line 18
    sget-object p1, Lorg/xplatform/aggregator/impl/favorite/data/models/FavoriteClearSourceRequest;->ONE_XGAMES:Lorg/xplatform/aggregator/impl/favorite/data/models/FavoriteClearSourceRequest;

    .line 19
    .line 20
    return-object p1

    .line 21
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 22
    .line 23
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    sget-object p1, Lorg/xplatform/aggregator/impl/favorite/data/models/FavoriteClearSourceRequest;->AGGREGATOR:Lorg/xplatform/aggregator/impl/favorite/data/models/FavoriteClearSourceRequest;

    .line 28
    .line 29
    return-object p1

    .line 30
    :cond_2
    sget-object p1, Lorg/xplatform/aggregator/impl/favorite/data/models/FavoriteClearSourceRequest;->ALL:Lorg/xplatform/aggregator/impl/favorite/data/models/FavoriteClearSourceRequest;

    .line 31
    .line 32
    return-object p1
.end method

.method public a(Lorg/xplatform/aggregator/api/model/Game;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "Z",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/lang/Boolean;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p4, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$isFavorite$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p4

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$isFavorite$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$isFavorite$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$isFavorite$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$isFavorite$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p4}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$isFavorite$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p4, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$isFavorite$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$isFavorite$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-object p1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$isFavorite$1;->L$0:Ljava/lang/Object;

    .line 39
    .line 40
    check-cast p1, Lorg/xplatform/aggregator/api/model/Game;

    .line 41
    .line 42
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    goto :goto_1

    .line 46
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 47
    .line 48
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 49
    .line 50
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    throw p1

    .line 54
    :cond_2
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    iput-object p1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$isFavorite$1;->L$0:Ljava/lang/Object;

    .line 58
    .line 59
    iput v3, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$isFavorite$1;->label:I

    .line 60
    .line 61
    invoke-virtual {p0, p2, p3, v0}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->z(ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object p2

    .line 65
    if-ne p2, v1, :cond_3

    .line 66
    .line 67
    return-object v1

    .line 68
    :cond_3
    :goto_1
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 69
    .line 70
    invoke-virtual {p2, p1}, LT91/a;->k(Lorg/xplatform/aggregator/api/model/Game;)Z

    .line 71
    .line 72
    .line 73
    move-result p1

    .line 74
    invoke-static {p1}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    return-object p1
.end method

.method public b(Lorg/xplatform/aggregator/api/model/Game;ZILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "ZI",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 2
    .line 3
    invoke-virtual {v0}, LT91/a;->c()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_2

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 10
    .line 11
    invoke-virtual {v0}, LT91/a;->d()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_1

    .line 16
    .line 17
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2;

    .line 18
    .line 19
    const/4 v6, 0x0

    .line 20
    move-object v2, p0

    .line 21
    move-object v4, p1

    .line 22
    move v3, p2

    .line 23
    move v5, p3

    .line 24
    invoke-direct/range {v1 .. v6}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ZLorg/xplatform/aggregator/api/model/Game;ILkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    invoke-static {v1, p4}, Lkotlinx/coroutines/O;->f(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object p2

    .line 35
    if-ne p1, p2, :cond_0

    .line 36
    .line 37
    return-object p1

    .line 38
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 39
    .line 40
    return-object p1

    .line 41
    :cond_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 42
    .line 43
    return-object p1

    .line 44
    :cond_2
    new-instance p1, Lorg/xplatform/aggregator/api/domain/exceptions/FavoritesLimitException;

    .line 45
    .line 46
    invoke-direct {p1}, Lorg/xplatform/aggregator/api/domain/exceptions/FavoritesLimitException;-><init>()V

    .line 47
    .line 48
    .line 49
    throw p1
.end method

.method public c(IJLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 11
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(IJ",
            "Ljava/lang/String;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p6

    .line 2
    .line 3
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$1;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$1;

    .line 9
    .line 10
    iget v2, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$1;->label:I

    .line 11
    .line 12
    const/high16 v3, -0x80000000

    .line 13
    .line 14
    and-int v4, v2, v3

    .line 15
    .line 16
    if-eqz v4, :cond_0

    .line 17
    .line 18
    sub-int/2addr v2, v3

    .line 19
    iput v2, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$1;->label:I

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$1;

    .line 23
    .line 24
    invoke-direct {v1, p0, v0}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    :goto_0
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    iget v3, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$1;->label:I

    .line 34
    .line 35
    const/4 v4, 0x1

    .line 36
    if-eqz v3, :cond_2

    .line 37
    .line 38
    if-ne v3, v4, :cond_1

    .line 39
    .line 40
    iget-boolean p1, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$1;->Z$0:Z

    .line 41
    .line 42
    iget-object p2, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$1;->L$0:Ljava/lang/Object;

    .line 43
    .line 44
    move-object p4, p2

    .line 45
    check-cast p4, Ljava/lang/String;

    .line 46
    .line 47
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 48
    .line 49
    .line 50
    goto :goto_1

    .line 51
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 52
    .line 53
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 54
    .line 55
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 56
    .line 57
    .line 58
    throw p1

    .line 59
    :cond_2
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 60
    .line 61
    .line 62
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->f:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 63
    .line 64
    new-instance v5, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;

    .line 65
    .line 66
    const/4 v10, 0x0

    .line 67
    move-object v8, p0

    .line 68
    move v9, p1

    .line 69
    move-wide v6, p2

    .line 70
    invoke-direct/range {v5 .. v10}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;-><init>(JLorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ILkotlin/coroutines/e;)V

    .line 71
    .line 72
    .line 73
    iput-object p4, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$1;->L$0:Ljava/lang/Object;

    .line 74
    .line 75
    move/from16 p1, p5

    .line 76
    .line 77
    iput-boolean p1, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$1;->Z$0:Z

    .line 78
    .line 79
    iput v4, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$1;->label:I

    .line 80
    .line 81
    invoke-virtual {v0, v5, v1}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object v0

    .line 85
    if-ne v0, v2, :cond_3

    .line 86
    .line 87
    return-object v2

    .line 88
    :cond_3
    :goto_1
    check-cast v0, Le8/b;

    .line 89
    .line 90
    invoke-virtual {v0}, Le8/b;->a()Ljava/lang/Object;

    .line 91
    .line 92
    .line 93
    move-result-object p2

    .line 94
    check-cast p2, LL91/e;

    .line 95
    .line 96
    invoke-virtual {p2}, LL91/e;->b()Ljava/util/List;

    .line 97
    .line 98
    .line 99
    move-result-object p2

    .line 100
    if-eqz p2, :cond_4

    .line 101
    .line 102
    new-instance p3, Ljava/util/ArrayList;

    .line 103
    .line 104
    const/16 v0, 0xa

    .line 105
    .line 106
    invoke-static {p2, v0}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 107
    .line 108
    .line 109
    move-result v0

    .line 110
    invoke-direct {p3, v0}, Ljava/util/ArrayList;-><init>(I)V

    .line 111
    .line 112
    .line 113
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 114
    .line 115
    .line 116
    move-result-object p2

    .line 117
    :goto_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 118
    .line 119
    .line 120
    move-result v0

    .line 121
    if-eqz v0, :cond_5

    .line 122
    .line 123
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 124
    .line 125
    .line 126
    move-result-object v0

    .line 127
    check-cast v0, LL91/d;

    .line 128
    .line 129
    invoke-static {v0, p4, p1}, LA91/d;->c(LL91/d;Ljava/lang/String;Z)Lorg/xplatform/aggregator/api/model/Game;

    .line 130
    .line 131
    .line 132
    move-result-object v0

    .line 133
    invoke-interface {p3, v0}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 134
    .line 135
    .line 136
    goto :goto_2

    .line 137
    :cond_4
    const/4 p3, 0x0

    .line 138
    :cond_5
    if-nez p3, :cond_6

    .line 139
    .line 140
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 141
    .line 142
    .line 143
    move-result-object p3

    .line 144
    :cond_6
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/core/data/datasources/a;

    .line 145
    .line 146
    invoke-virtual {p1, p3}, Lorg/xplatform/aggregator/impl/core/data/datasources/a;->i(Ljava/util/List;)V

    .line 147
    .line 148
    .line 149
    return-object p3
.end method

.method public d(JLjava/util/List;IZLjava/lang/String;ZLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 18
    .param p3    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;IZ",
            "Ljava/lang/String;",
            "Z",
            "Ljava/lang/String;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v14, p8

    .line 4
    .line 5
    move-object/from16 v1, p10

    .line 6
    .line 7
    instance-of v2, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;

    .line 8
    .line 9
    if-eqz v2, :cond_0

    .line 10
    .line 11
    move-object v2, v1

    .line 12
    check-cast v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;

    .line 13
    .line 14
    iget v3, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->label:I

    .line 15
    .line 16
    const/high16 v4, -0x80000000

    .line 17
    .line 18
    and-int v5, v3, v4

    .line 19
    .line 20
    if-eqz v5, :cond_0

    .line 21
    .line 22
    sub-int/2addr v3, v4

    .line 23
    iput v3, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->label:I

    .line 24
    .line 25
    :goto_0
    move-object v13, v2

    .line 26
    goto :goto_1

    .line 27
    :cond_0
    new-instance v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;

    .line 28
    .line 29
    invoke-direct {v2, v0, v1}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 30
    .line 31
    .line 32
    goto :goto_0

    .line 33
    :goto_1
    iget-object v1, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->result:Ljava/lang/Object;

    .line 34
    .line 35
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v15

    .line 39
    iget v2, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->label:I

    .line 40
    .line 41
    const/4 v8, 0x1

    .line 42
    const/4 v9, 0x2

    .line 43
    if-eqz v2, :cond_3

    .line 44
    .line 45
    if-eq v2, v8, :cond_2

    .line 46
    .line 47
    if-ne v2, v9, :cond_1

    .line 48
    .line 49
    iget-boolean v2, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->Z$0:Z

    .line 50
    .line 51
    iget v3, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->I$0:I

    .line 52
    .line 53
    iget-wide v4, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->J$0:J

    .line 54
    .line 55
    iget-object v6, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->L$1:Ljava/lang/Object;

    .line 56
    .line 57
    check-cast v6, Ljava/lang/String;

    .line 58
    .line 59
    iget-object v8, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->L$0:Ljava/lang/Object;

    .line 60
    .line 61
    check-cast v8, Ljava/util/List;

    .line 62
    .line 63
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 64
    .line 65
    .line 66
    const/4 v14, 0x2

    .line 67
    goto/16 :goto_6

    .line 68
    .line 69
    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 70
    .line 71
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 72
    .line 73
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 74
    .line 75
    .line 76
    throw v1

    .line 77
    :cond_2
    iget-boolean v2, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->Z$0:Z

    .line 78
    .line 79
    iget v3, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->I$0:I

    .line 80
    .line 81
    iget-wide v4, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->J$0:J

    .line 82
    .line 83
    iget-object v6, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->L$1:Ljava/lang/Object;

    .line 84
    .line 85
    check-cast v6, Ljava/lang/String;

    .line 86
    .line 87
    iget-object v8, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->L$0:Ljava/lang/Object;

    .line 88
    .line 89
    check-cast v8, Ljava/util/List;

    .line 90
    .line 91
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 92
    .line 93
    .line 94
    const/16 v14, 0xa

    .line 95
    .line 96
    move-wide/from16 v16, v4

    .line 97
    .line 98
    move-object v4, v1

    .line 99
    move v5, v3

    .line 100
    move-object v1, v6

    .line 101
    move v6, v2

    .line 102
    move-wide/from16 v2, v16

    .line 103
    .line 104
    goto/16 :goto_3

    .line 105
    .line 106
    :cond_3
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 107
    .line 108
    .line 109
    if-eqz p9, :cond_4

    .line 110
    .line 111
    move-wide/from16 v2, p1

    .line 112
    .line 113
    move-object/from16 v4, p3

    .line 114
    .line 115
    move/from16 v5, p4

    .line 116
    .line 117
    move/from16 v6, p5

    .line 118
    .line 119
    const/4 v1, 0x0

    .line 120
    goto :goto_2

    .line 121
    :cond_4
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/core/data/datasources/a;

    .line 122
    .line 123
    move-wide/from16 v2, p1

    .line 124
    .line 125
    move-object/from16 v4, p3

    .line 126
    .line 127
    move/from16 v5, p4

    .line 128
    .line 129
    move/from16 v6, p5

    .line 130
    .line 131
    invoke-virtual/range {v1 .. v6}, Lorg/xplatform/aggregator/impl/core/data/datasources/a;->e(JLjava/util/List;IZ)Ljava/util/List;

    .line 132
    .line 133
    .line 134
    move-result-object v1

    .line 135
    :goto_2
    if-nez v1, :cond_d

    .line 136
    .line 137
    if-eqz p7, :cond_7

    .line 138
    .line 139
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 140
    .line 141
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 142
    .line 143
    invoke-interface {v9}, Lc8/h;->f()I

    .line 144
    .line 145
    .line 146
    move-result v9

    .line 147
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 148
    .line 149
    invoke-interface {v11}, Lc8/h;->b()I

    .line 150
    .line 151
    .line 152
    move-result v11

    .line 153
    iget-object v12, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 154
    .line 155
    invoke-interface {v12}, Lc8/h;->getGroupId()I

    .line 156
    .line 157
    .line 158
    move-result v12

    .line 159
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 160
    .line 161
    invoke-interface {v7}, Lc8/h;->d()I

    .line 162
    .line 163
    .line 164
    move-result v7

    .line 165
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 166
    .line 167
    invoke-interface {v10}, Lc8/h;->c()Ljava/lang/String;

    .line 168
    .line 169
    .line 170
    move-result-object v10

    .line 171
    iput-object v4, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->L$0:Ljava/lang/Object;

    .line 172
    .line 173
    iput-object v14, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->L$1:Ljava/lang/Object;

    .line 174
    .line 175
    iput-wide v2, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->J$0:J

    .line 176
    .line 177
    iput v5, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->I$0:I

    .line 178
    .line 179
    iput-boolean v6, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->Z$0:Z

    .line 180
    .line 181
    iput v8, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->label:I

    .line 182
    .line 183
    move v8, v11

    .line 184
    move v11, v7

    .line 185
    move v7, v9

    .line 186
    move v9, v8

    .line 187
    move v8, v12

    .line 188
    move-object v12, v10

    .line 189
    move v10, v8

    .line 190
    move-object/from16 v8, p6

    .line 191
    .line 192
    const/16 v14, 0xa

    .line 193
    .line 194
    invoke-virtual/range {v1 .. v13}, Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;->s(JLjava/util/List;IZILjava/lang/String;IIILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 195
    .line 196
    .line 197
    move-result-object v1

    .line 198
    if-ne v1, v15, :cond_5

    .line 199
    .line 200
    goto/16 :goto_5

    .line 201
    .line 202
    :cond_5
    move-object v8, v4

    .line 203
    move-object v4, v1

    .line 204
    move-object/from16 v1, p8

    .line 205
    .line 206
    :goto_3
    check-cast v4, Le8/b;

    .line 207
    .line 208
    invoke-virtual {v4}, Le8/b;->a()Ljava/lang/Object;

    .line 209
    .line 210
    .line 211
    move-result-object v4

    .line 212
    check-cast v4, LL91/b;

    .line 213
    .line 214
    invoke-virtual {v4}, LL91/b;->a()Ljava/util/List;

    .line 215
    .line 216
    .line 217
    move-result-object v4

    .line 218
    if-eqz v4, :cond_6

    .line 219
    .line 220
    new-instance v10, Ljava/util/ArrayList;

    .line 221
    .line 222
    invoke-static {v4, v14}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 223
    .line 224
    .line 225
    move-result v7

    .line 226
    invoke-direct {v10, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 227
    .line 228
    .line 229
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 230
    .line 231
    .line 232
    move-result-object v4

    .line 233
    :goto_4
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 234
    .line 235
    .line 236
    move-result v7

    .line 237
    if-eqz v7, :cond_b

    .line 238
    .line 239
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 240
    .line 241
    .line 242
    move-result-object v7

    .line 243
    check-cast v7, LL91/a;

    .line 244
    .line 245
    invoke-static {v7, v1}, LA91/d;->b(LL91/a;Ljava/lang/String;)Lorg/xplatform/aggregator/api/model/Game;

    .line 246
    .line 247
    .line 248
    move-result-object v7

    .line 249
    invoke-interface {v10, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 250
    .line 251
    .line 252
    goto :goto_4

    .line 253
    :cond_6
    const/4 v10, 0x0

    .line 254
    goto/16 :goto_9

    .line 255
    .line 256
    :cond_7
    const/16 v14, 0xa

    .line 257
    .line 258
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 259
    .line 260
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 261
    .line 262
    invoke-interface {v7}, Lc8/h;->f()I

    .line 263
    .line 264
    .line 265
    move-result v7

    .line 266
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 267
    .line 268
    invoke-interface {v8}, Lc8/h;->b()I

    .line 269
    .line 270
    .line 271
    move-result v8

    .line 272
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 273
    .line 274
    invoke-interface {v10}, Lc8/h;->getGroupId()I

    .line 275
    .line 276
    .line 277
    move-result v10

    .line 278
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 279
    .line 280
    invoke-interface {v11}, Lc8/h;->d()I

    .line 281
    .line 282
    .line 283
    move-result v11

    .line 284
    iget-object v12, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 285
    .line 286
    invoke-interface {v12}, Lc8/h;->c()Ljava/lang/String;

    .line 287
    .line 288
    .line 289
    move-result-object v12

    .line 290
    iput-object v4, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->L$0:Ljava/lang/Object;

    .line 291
    .line 292
    move-object/from16 v14, p8

    .line 293
    .line 294
    iput-object v14, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->L$1:Ljava/lang/Object;

    .line 295
    .line 296
    iput-wide v2, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->J$0:J

    .line 297
    .line 298
    iput v5, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->I$0:I

    .line 299
    .line 300
    iput-boolean v6, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->Z$0:Z

    .line 301
    .line 302
    iput v9, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesByFilters$1;->label:I

    .line 303
    .line 304
    move v9, v8

    .line 305
    const/4 v14, 0x2

    .line 306
    move-object/from16 v8, p6

    .line 307
    .line 308
    invoke-virtual/range {v1 .. v13}, Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;->r(JLjava/util/List;IZILjava/lang/String;IIILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 309
    .line 310
    .line 311
    move-result-object v1

    .line 312
    if-ne v1, v15, :cond_8

    .line 313
    .line 314
    :goto_5
    return-object v15

    .line 315
    :cond_8
    move-wide/from16 v4, p1

    .line 316
    .line 317
    move-object/from16 v8, p3

    .line 318
    .line 319
    move/from16 v3, p4

    .line 320
    .line 321
    move/from16 v2, p5

    .line 322
    .line 323
    move-object/from16 v6, p8

    .line 324
    .line 325
    :goto_6
    check-cast v1, Le8/b;

    .line 326
    .line 327
    invoke-virtual {v1}, Le8/b;->a()Ljava/lang/Object;

    .line 328
    .line 329
    .line 330
    move-result-object v1

    .line 331
    check-cast v1, LL91/e;

    .line 332
    .line 333
    invoke-virtual {v1}, LL91/e;->b()Ljava/util/List;

    .line 334
    .line 335
    .line 336
    move-result-object v1

    .line 337
    if-eqz v1, :cond_a

    .line 338
    .line 339
    new-instance v10, Ljava/util/ArrayList;

    .line 340
    .line 341
    const/16 v7, 0xa

    .line 342
    .line 343
    invoke-static {v1, v7}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 344
    .line 345
    .line 346
    move-result v7

    .line 347
    invoke-direct {v10, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 348
    .line 349
    .line 350
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 351
    .line 352
    .line 353
    move-result-object v1

    .line 354
    :goto_7
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 355
    .line 356
    .line 357
    move-result v7

    .line 358
    if-eqz v7, :cond_9

    .line 359
    .line 360
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 361
    .line 362
    .line 363
    move-result-object v7

    .line 364
    check-cast v7, LL91/d;

    .line 365
    .line 366
    const/4 v9, 0x0

    .line 367
    const/4 v11, 0x0

    .line 368
    invoke-static {v7, v6, v9, v14, v11}, LA91/d;->d(LL91/d;Ljava/lang/String;ZILjava/lang/Object;)Lorg/xplatform/aggregator/api/model/Game;

    .line 369
    .line 370
    .line 371
    move-result-object v7

    .line 372
    invoke-interface {v10, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 373
    .line 374
    .line 375
    goto :goto_7

    .line 376
    :cond_9
    move v6, v2

    .line 377
    :goto_8
    move-wide/from16 v16, v4

    .line 378
    .line 379
    move v5, v3

    .line 380
    move-wide/from16 v2, v16

    .line 381
    .line 382
    goto :goto_9

    .line 383
    :cond_a
    const/4 v11, 0x0

    .line 384
    move v6, v2

    .line 385
    move-object v10, v11

    .line 386
    goto :goto_8

    .line 387
    :cond_b
    :goto_9
    if-nez v10, :cond_c

    .line 388
    .line 389
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 390
    .line 391
    .line 392
    move-result-object v10

    .line 393
    :cond_c
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/core/data/datasources/a;

    .line 394
    .line 395
    move-object/from16 p1, v1

    .line 396
    .line 397
    move-wide/from16 p2, v2

    .line 398
    .line 399
    move/from16 p5, v5

    .line 400
    .line 401
    move/from16 p6, v6

    .line 402
    .line 403
    move-object/from16 p4, v8

    .line 404
    .line 405
    move-object/from16 p7, v10

    .line 406
    .line 407
    invoke-virtual/range {p1 .. p7}, Lorg/xplatform/aggregator/impl/core/data/datasources/a;->k(JLjava/util/List;IZLjava/util/List;)V

    .line 408
    .line 409
    .line 410
    move-object/from16 v10, p7

    .line 411
    .line 412
    return-object v10

    .line 413
    :cond_d
    return-object v1
.end method

.method public e()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-virtual {v0, v1}, LT91/a;->q(Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public f(ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlinx/coroutines/flow/e<",
            "+",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 2
    .line 3
    invoke-virtual {p3}, LT91/a;->j()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object p3

    .line 7
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getFavoriteGamesFlowWithoutCatching$2;

    .line 8
    .line 9
    const/4 v1, 0x0

    .line 10
    invoke-direct {v0, p0, p1, p2, v1}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getFavoriteGamesFlowWithoutCatching$2;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ZLjava/lang/String;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {p3, v0}, Lkotlinx/coroutines/flow/g;->j0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    return-object p1
.end method

.method public g(JIZLjava/lang/String;ZZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 26
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JIZ",
            "Ljava/lang/String;",
            "ZZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-wide/from16 v2, p1

    .line 4
    .line 5
    move/from16 v4, p3

    .line 6
    .line 7
    move/from16 v5, p4

    .line 8
    .line 9
    move-object/from16 v1, p5

    .line 10
    .line 11
    move-object/from16 v6, p8

    .line 12
    .line 13
    instance-of v7, v6, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;

    .line 14
    .line 15
    if-eqz v7, :cond_0

    .line 16
    .line 17
    move-object v7, v6

    .line 18
    check-cast v7, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;

    .line 19
    .line 20
    iget v8, v7, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->label:I

    .line 21
    .line 22
    const/high16 v9, -0x80000000

    .line 23
    .line 24
    and-int v10, v8, v9

    .line 25
    .line 26
    if-eqz v10, :cond_0

    .line 27
    .line 28
    sub-int/2addr v8, v9

    .line 29
    iput v8, v7, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->label:I

    .line 30
    .line 31
    :goto_0
    move-object v11, v7

    .line 32
    goto :goto_1

    .line 33
    :cond_0
    new-instance v7, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;

    .line 34
    .line 35
    invoke-direct {v7, v0, v6}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 36
    .line 37
    .line 38
    goto :goto_0

    .line 39
    :goto_1
    iget-object v6, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->result:Ljava/lang/Object;

    .line 40
    .line 41
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object v7

    .line 45
    iget v8, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->label:I

    .line 46
    .line 47
    const/16 v9, 0xa

    .line 48
    .line 49
    const/4 v10, 0x1

    .line 50
    const/4 v12, 0x2

    .line 51
    const/4 v13, 0x0

    .line 52
    if-eqz v8, :cond_3

    .line 53
    .line 54
    if-eq v8, v10, :cond_2

    .line 55
    .line 56
    if-ne v8, v12, :cond_1

    .line 57
    .line 58
    iget-boolean v1, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->Z$0:Z

    .line 59
    .line 60
    iget v2, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->I$0:I

    .line 61
    .line 62
    iget-wide v3, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->J$0:J

    .line 63
    .line 64
    iget-object v5, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->L$0:Ljava/lang/Object;

    .line 65
    .line 66
    check-cast v5, Ljava/lang/String;

    .line 67
    .line 68
    invoke-static {v6}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 69
    .line 70
    .line 71
    const/16 v14, 0xa

    .line 72
    .line 73
    goto/16 :goto_7

    .line 74
    .line 75
    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 76
    .line 77
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 78
    .line 79
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 80
    .line 81
    .line 82
    throw v1

    .line 83
    :cond_2
    iget-boolean v1, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->Z$0:Z

    .line 84
    .line 85
    iget v2, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->I$0:I

    .line 86
    .line 87
    iget-wide v3, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->J$0:J

    .line 88
    .line 89
    iget-object v5, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->L$0:Ljava/lang/Object;

    .line 90
    .line 91
    check-cast v5, Ljava/lang/String;

    .line 92
    .line 93
    invoke-static {v6}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 94
    .line 95
    .line 96
    move-object v7, v6

    .line 97
    move-object v6, v5

    .line 98
    move v5, v1

    .line 99
    move-wide/from16 v24, v3

    .line 100
    .line 101
    move v4, v2

    .line 102
    move-wide/from16 v1, v24

    .line 103
    .line 104
    const/16 v3, 0xa

    .line 105
    .line 106
    goto/16 :goto_4

    .line 107
    .line 108
    :cond_3
    invoke-static {v6}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 109
    .line 110
    .line 111
    if-eqz p7, :cond_4

    .line 112
    .line 113
    move-object v6, v13

    .line 114
    goto :goto_2

    .line 115
    :cond_4
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/core/data/datasources/a;

    .line 116
    .line 117
    invoke-virtual {v6, v2, v3, v4, v5}, Lorg/xplatform/aggregator/impl/core/data/datasources/a;->f(JIZ)Ljava/util/List;

    .line 118
    .line 119
    .line 120
    move-result-object v6

    .line 121
    :goto_2
    if-nez v6, :cond_e

    .line 122
    .line 123
    if-eqz p6, :cond_8

    .line 124
    .line 125
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 126
    .line 127
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 128
    .line 129
    invoke-interface {v8}, Lc8/h;->f()I

    .line 130
    .line 131
    .line 132
    move-result v12

    .line 133
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 134
    .line 135
    invoke-interface {v8}, Lc8/h;->b()I

    .line 136
    .line 137
    .line 138
    move-result v14

    .line 139
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 140
    .line 141
    invoke-interface {v8}, Lc8/h;->getGroupId()I

    .line 142
    .line 143
    .line 144
    move-result v15

    .line 145
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 146
    .line 147
    invoke-interface {v8}, Lc8/h;->d()I

    .line 148
    .line 149
    .line 150
    move-result v16

    .line 151
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 152
    .line 153
    invoke-interface {v8}, Lc8/h;->c()Ljava/lang/String;

    .line 154
    .line 155
    .line 156
    move-result-object v20

    .line 157
    sget-object v8, Lorg/xplatform/aggregator/api/model/PartitionType;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 158
    .line 159
    invoke-virtual {v8}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 160
    .line 161
    .line 162
    move-result-wide v17

    .line 163
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->e:LS8/a;

    .line 164
    .line 165
    invoke-virtual {v8}, LS8/a;->b()Le9/a;

    .line 166
    .line 167
    .line 168
    move-result-object v8

    .line 169
    if-eqz v8, :cond_5

    .line 170
    .line 171
    invoke-virtual {v8}, Le9/a;->w()Ljava/lang/String;

    .line 172
    .line 173
    .line 174
    move-result-object v8

    .line 175
    if-eqz v8, :cond_5

    .line 176
    .line 177
    invoke-static {v8}, Lkotlin/text/StringsKt;->toIntOrNull(Ljava/lang/String;)Ljava/lang/Integer;

    .line 178
    .line 179
    .line 180
    move-result-object v8

    .line 181
    goto :goto_3

    .line 182
    :cond_5
    move-object v8, v13

    .line 183
    :goto_3
    invoke-static {v2, v3}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 184
    .line 185
    .line 186
    move-result-object v19

    .line 187
    invoke-static/range {v19 .. v19}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 188
    .line 189
    .line 190
    move-result-object v19

    .line 191
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 192
    .line 193
    .line 194
    move-result-object v21

    .line 195
    iput-object v1, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->L$0:Ljava/lang/Object;

    .line 196
    .line 197
    iput-wide v2, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->J$0:J

    .line 198
    .line 199
    iput v4, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->I$0:I

    .line 200
    .line 201
    iput-boolean v5, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->Z$0:Z

    .line 202
    .line 203
    iput v10, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->label:I

    .line 204
    .line 205
    move-object v10, v7

    .line 206
    move-object v7, v8

    .line 207
    const-string v8, ""

    .line 208
    .line 209
    move-object/from16 v22, v10

    .line 210
    .line 211
    move-object/from16 v10, v21

    .line 212
    .line 213
    move-object/from16 v21, v11

    .line 214
    .line 215
    const-string v11, ""

    .line 216
    .line 217
    move-object/from16 v23, v13

    .line 218
    .line 219
    const/4 v13, 0x0

    .line 220
    move-object v4, v6

    .line 221
    move-wide/from16 v5, v17

    .line 222
    .line 223
    const/16 v17, 0x0

    .line 224
    .line 225
    move/from16 v18, p3

    .line 226
    .line 227
    move-object/from16 v9, v19

    .line 228
    .line 229
    move-object/from16 v2, v22

    .line 230
    .line 231
    const/16 v3, 0xa

    .line 232
    .line 233
    move/from16 v19, p4

    .line 234
    .line 235
    invoke-virtual/range {v4 .. v21}, Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;->q(JLjava/lang/Integer;Ljava/lang/String;Ljava/util/List;Ljava/util/List;Ljava/lang/String;IZIIIIIZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 236
    .line 237
    .line 238
    move-result-object v6

    .line 239
    move/from16 v4, v18

    .line 240
    .line 241
    move/from16 v5, v19

    .line 242
    .line 243
    if-ne v6, v2, :cond_6

    .line 244
    .line 245
    move-object v13, v2

    .line 246
    goto/16 :goto_6

    .line 247
    .line 248
    :cond_6
    move-object v7, v6

    .line 249
    move-object v6, v1

    .line 250
    move-wide/from16 v1, p1

    .line 251
    .line 252
    :goto_4
    check-cast v7, Le8/b;

    .line 253
    .line 254
    invoke-virtual {v7}, Le8/b;->a()Ljava/lang/Object;

    .line 255
    .line 256
    .line 257
    move-result-object v7

    .line 258
    check-cast v7, LL91/b;

    .line 259
    .line 260
    invoke-virtual {v7}, LL91/b;->a()Ljava/util/List;

    .line 261
    .line 262
    .line 263
    move-result-object v7

    .line 264
    if-eqz v7, :cond_7

    .line 265
    .line 266
    new-instance v13, Ljava/util/ArrayList;

    .line 267
    .line 268
    invoke-static {v7, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 269
    .line 270
    .line 271
    move-result v3

    .line 272
    invoke-direct {v13, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 273
    .line 274
    .line 275
    invoke-interface {v7}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 276
    .line 277
    .line 278
    move-result-object v3

    .line 279
    :goto_5
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 280
    .line 281
    .line 282
    move-result v7

    .line 283
    if-eqz v7, :cond_c

    .line 284
    .line 285
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 286
    .line 287
    .line 288
    move-result-object v7

    .line 289
    check-cast v7, LL91/a;

    .line 290
    .line 291
    invoke-static {v7, v6}, LA91/d;->b(LL91/a;Ljava/lang/String;)Lorg/xplatform/aggregator/api/model/Game;

    .line 292
    .line 293
    .line 294
    move-result-object v7

    .line 295
    invoke-interface {v13, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 296
    .line 297
    .line 298
    goto :goto_5

    .line 299
    :cond_7
    const/4 v13, 0x0

    .line 300
    goto/16 :goto_a

    .line 301
    .line 302
    :cond_8
    move-object v2, v7

    .line 303
    const/16 v3, 0xa

    .line 304
    .line 305
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 306
    .line 307
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 308
    .line 309
    invoke-interface {v7}, Lc8/h;->f()I

    .line 310
    .line 311
    .line 312
    move-result v7

    .line 313
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 314
    .line 315
    invoke-interface {v8}, Lc8/h;->b()I

    .line 316
    .line 317
    .line 318
    move-result v8

    .line 319
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 320
    .line 321
    invoke-interface {v9}, Lc8/h;->getGroupId()I

    .line 322
    .line 323
    .line 324
    move-result v9

    .line 325
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 326
    .line 327
    invoke-interface {v10}, Lc8/h;->d()I

    .line 328
    .line 329
    .line 330
    move-result v10

    .line 331
    iget-object v13, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 332
    .line 333
    invoke-interface {v13}, Lc8/h;->c()Ljava/lang/String;

    .line 334
    .line 335
    .line 336
    move-result-object v13

    .line 337
    iput-object v1, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->L$0:Ljava/lang/Object;

    .line 338
    .line 339
    move-wide/from16 v14, p1

    .line 340
    .line 341
    iput-wide v14, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->J$0:J

    .line 342
    .line 343
    iput v4, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->I$0:I

    .line 344
    .line 345
    iput-boolean v5, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->Z$0:Z

    .line 346
    .line 347
    iput v12, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGamesForCategory$1;->label:I

    .line 348
    .line 349
    move-object v1, v6

    .line 350
    move v6, v7

    .line 351
    move v7, v8

    .line 352
    move v8, v9

    .line 353
    move v9, v10

    .line 354
    move-object v10, v13

    .line 355
    move-object v13, v2

    .line 356
    move-wide v2, v14

    .line 357
    const/16 v14, 0xa

    .line 358
    .line 359
    invoke-virtual/range {v1 .. v11}, Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;->t(JIZIIIILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 360
    .line 361
    .line 362
    move-result-object v6

    .line 363
    if-ne v6, v13, :cond_9

    .line 364
    .line 365
    :goto_6
    return-object v13

    .line 366
    :cond_9
    move-wide/from16 v3, p1

    .line 367
    .line 368
    move/from16 v2, p3

    .line 369
    .line 370
    move/from16 v1, p4

    .line 371
    .line 372
    move-object/from16 v5, p5

    .line 373
    .line 374
    :goto_7
    check-cast v6, Le8/b;

    .line 375
    .line 376
    invoke-virtual {v6}, Le8/b;->a()Ljava/lang/Object;

    .line 377
    .line 378
    .line 379
    move-result-object v6

    .line 380
    check-cast v6, LL91/e;

    .line 381
    .line 382
    invoke-virtual {v6}, LL91/e;->b()Ljava/util/List;

    .line 383
    .line 384
    .line 385
    move-result-object v6

    .line 386
    if-eqz v6, :cond_b

    .line 387
    .line 388
    new-instance v13, Ljava/util/ArrayList;

    .line 389
    .line 390
    invoke-static {v6, v14}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 391
    .line 392
    .line 393
    move-result v7

    .line 394
    invoke-direct {v13, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 395
    .line 396
    .line 397
    invoke-interface {v6}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 398
    .line 399
    .line 400
    move-result-object v6

    .line 401
    :goto_8
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    .line 402
    .line 403
    .line 404
    move-result v7

    .line 405
    if-eqz v7, :cond_a

    .line 406
    .line 407
    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 408
    .line 409
    .line 410
    move-result-object v7

    .line 411
    check-cast v7, LL91/d;

    .line 412
    .line 413
    const/4 v8, 0x0

    .line 414
    const/4 v9, 0x0

    .line 415
    invoke-static {v7, v5, v8, v12, v9}, LA91/d;->d(LL91/d;Ljava/lang/String;ZILjava/lang/Object;)Lorg/xplatform/aggregator/api/model/Game;

    .line 416
    .line 417
    .line 418
    move-result-object v7

    .line 419
    invoke-interface {v13, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 420
    .line 421
    .line 422
    goto :goto_8

    .line 423
    :cond_a
    move v5, v1

    .line 424
    :goto_9
    move-wide/from16 v24, v3

    .line 425
    .line 426
    move v4, v2

    .line 427
    move-wide/from16 v1, v24

    .line 428
    .line 429
    goto :goto_a

    .line 430
    :cond_b
    const/4 v9, 0x0

    .line 431
    move v5, v1

    .line 432
    move-object v13, v9

    .line 433
    goto :goto_9

    .line 434
    :cond_c
    :goto_a
    if-nez v13, :cond_d

    .line 435
    .line 436
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 437
    .line 438
    .line 439
    move-result-object v13

    .line 440
    :cond_d
    iget-object v3, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/core/data/datasources/a;

    .line 441
    .line 442
    move-wide/from16 p2, v1

    .line 443
    .line 444
    move-object/from16 p1, v3

    .line 445
    .line 446
    move/from16 p4, v4

    .line 447
    .line 448
    move/from16 p5, v5

    .line 449
    .line 450
    move-object/from16 p6, v13

    .line 451
    .line 452
    invoke-virtual/range {p1 .. p6}, Lorg/xplatform/aggregator/impl/core/data/datasources/a;->l(JIZLjava/util/List;)V

    .line 453
    .line 454
    .line 455
    move-object/from16 v13, p6

    .line 456
    .line 457
    return-object v13

    .line 458
    :cond_e
    return-object v6
.end method

.method public h(ZLjava/lang/String;)Lkotlinx/coroutines/flow/e;
    .locals 3
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/lang/String;",
            ")",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 2
    .line 3
    invoke-virtual {v0}, LT91/a;->j()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getFavoriteGamesFlow$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, p1, p2, v2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getFavoriteGamesFlow$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ZLjava/lang/String;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    return-object p1
.end method

.method public i(IJLjava/lang/String;ZZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 12
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(IJ",
            "Ljava/lang/String;",
            "ZZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p7

    .line 2
    .line 3
    instance-of v5, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$1;

    .line 4
    .line 5
    if-eqz v5, :cond_0

    .line 6
    .line 7
    move-object v5, v0

    .line 8
    check-cast v5, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$1;

    .line 9
    .line 10
    iget v6, v5, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$1;->label:I

    .line 11
    .line 12
    const/high16 v7, -0x80000000

    .line 13
    .line 14
    and-int v8, v6, v7

    .line 15
    .line 16
    if-eqz v8, :cond_0

    .line 17
    .line 18
    sub-int/2addr v6, v7

    .line 19
    iput v6, v5, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$1;->label:I

    .line 20
    .line 21
    :goto_0
    move-object v8, v5

    .line 22
    goto :goto_1

    .line 23
    :cond_0
    new-instance v5, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$1;

    .line 24
    .line 25
    invoke-direct {v5, p0, v0}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 26
    .line 27
    .line 28
    goto :goto_0

    .line 29
    :goto_1
    iget-object v0, v8, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$1;->result:Ljava/lang/Object;

    .line 30
    .line 31
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v9

    .line 35
    iget v5, v8, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$1;->label:I

    .line 36
    .line 37
    const/4 v10, 0x1

    .line 38
    if-eqz v5, :cond_2

    .line 39
    .line 40
    if-ne v5, v10, :cond_1

    .line 41
    .line 42
    iget-wide v1, v8, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$1;->J$0:J

    .line 43
    .line 44
    iget v4, v8, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$1;->I$0:I

    .line 45
    .line 46
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 47
    .line 48
    .line 49
    goto :goto_3

    .line 50
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 51
    .line 52
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 53
    .line 54
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    throw v0

    .line 58
    :cond_2
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 59
    .line 60
    .line 61
    if-eqz p6, :cond_3

    .line 62
    .line 63
    const/4 v0, 0x0

    .line 64
    goto :goto_2

    .line 65
    :cond_3
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/core/data/datasources/a;

    .line 66
    .line 67
    invoke-virtual {v0, p2, p3, p1}, Lorg/xplatform/aggregator/impl/core/data/datasources/a;->g(JI)Ljava/util/List;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    :goto_2
    if-nez v0, :cond_6

    .line 72
    .line 73
    iget-object v11, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->f:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 74
    .line 75
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;

    .line 76
    .line 77
    const/4 v7, 0x0

    .line 78
    move-object v3, p0

    .line 79
    move v4, p1

    .line 80
    move-wide v1, p2

    .line 81
    move-object/from16 v5, p4

    .line 82
    .line 83
    move/from16 v6, p5

    .line 84
    .line 85
    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;-><init>(JLorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ILjava/lang/String;ZLkotlin/coroutines/e;)V

    .line 86
    .line 87
    .line 88
    iput p1, v8, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$1;->I$0:I

    .line 89
    .line 90
    iput-wide p2, v8, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$1;->J$0:J

    .line 91
    .line 92
    iput v10, v8, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$1;->label:I

    .line 93
    .line 94
    invoke-virtual {v11, v0, v8}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    if-ne v0, v9, :cond_4

    .line 99
    .line 100
    return-object v9

    .line 101
    :cond_4
    move v4, p1

    .line 102
    move-wide v1, p2

    .line 103
    :goto_3
    check-cast v0, Ljava/util/List;

    .line 104
    .line 105
    if-nez v0, :cond_5

    .line 106
    .line 107
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 108
    .line 109
    .line 110
    move-result-object v0

    .line 111
    :cond_5
    iget-object v5, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/core/data/datasources/a;

    .line 112
    .line 113
    invoke-virtual {v5, v1, v2, v4, v0}, Lorg/xplatform/aggregator/impl/core/data/datasources/a;->m(JILjava/util/List;)V

    .line 114
    .line 115
    .line 116
    :cond_6
    return-object v0
.end method

.method public j(Ljava/util/Set;ZZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 22
    .param p1    # Ljava/util/Set;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Set<",
            "Ljava/lang/Long;",
            ">;ZZ",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v2, p1

    .line 4
    .line 5
    move/from16 v3, p2

    .line 6
    .line 7
    move-object/from16 v10, p4

    .line 8
    .line 9
    move-object/from16 v1, p5

    .line 10
    .line 11
    instance-of v4, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;

    .line 12
    .line 13
    if-eqz v4, :cond_0

    .line 14
    .line 15
    move-object v4, v1

    .line 16
    check-cast v4, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;

    .line 17
    .line 18
    iget v5, v4, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->label:I

    .line 19
    .line 20
    const/high16 v6, -0x80000000

    .line 21
    .line 22
    and-int v7, v5, v6

    .line 23
    .line 24
    if-eqz v7, :cond_0

    .line 25
    .line 26
    sub-int/2addr v5, v6

    .line 27
    iput v5, v4, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->label:I

    .line 28
    .line 29
    :goto_0
    move-object v9, v4

    .line 30
    goto :goto_1

    .line 31
    :cond_0
    new-instance v4, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;

    .line 32
    .line 33
    invoke-direct {v4, v0, v1}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 34
    .line 35
    .line 36
    goto :goto_0

    .line 37
    :goto_1
    iget-object v1, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->result:Ljava/lang/Object;

    .line 38
    .line 39
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v11

    .line 43
    iget v4, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->label:I

    .line 44
    .line 45
    const/16 v12, 0xa

    .line 46
    .line 47
    const/4 v5, 0x1

    .line 48
    const/4 v13, 0x0

    .line 49
    const/4 v14, 0x0

    .line 50
    const/4 v15, 0x2

    .line 51
    if-eqz v4, :cond_3

    .line 52
    .line 53
    if-eq v4, v5, :cond_2

    .line 54
    .line 55
    if-ne v4, v15, :cond_1

    .line 56
    .line 57
    iget v2, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->I$0:I

    .line 58
    .line 59
    iget-boolean v3, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->Z$0:Z

    .line 60
    .line 61
    iget-object v4, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->L$1:Ljava/lang/Object;

    .line 62
    .line 63
    check-cast v4, Ljava/lang/String;

    .line 64
    .line 65
    iget-object v5, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->L$0:Ljava/lang/Object;

    .line 66
    .line 67
    check-cast v5, Ljava/util/Set;

    .line 68
    .line 69
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 70
    .line 71
    .line 72
    move-object v10, v4

    .line 73
    goto/16 :goto_6

    .line 74
    .line 75
    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 76
    .line 77
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 78
    .line 79
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 80
    .line 81
    .line 82
    throw v1

    .line 83
    :cond_2
    iget v2, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->I$0:I

    .line 84
    .line 85
    iget-boolean v3, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->Z$0:Z

    .line 86
    .line 87
    iget-object v4, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->L$1:Ljava/lang/Object;

    .line 88
    .line 89
    check-cast v4, Ljava/lang/String;

    .line 90
    .line 91
    iget-object v5, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->L$0:Ljava/lang/Object;

    .line 92
    .line 93
    check-cast v5, Ljava/util/Set;

    .line 94
    .line 95
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 96
    .line 97
    .line 98
    move-object v10, v4

    .line 99
    move v4, v2

    .line 100
    move-object v2, v5

    .line 101
    goto :goto_3

    .line 102
    :cond_3
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 103
    .line 104
    .line 105
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 106
    .line 107
    invoke-interface {v1}, Lc8/h;->f()I

    .line 108
    .line 109
    .line 110
    move-result v4

    .line 111
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/core/data/datasources/a;

    .line 112
    .line 113
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->e:LS8/a;

    .line 114
    .line 115
    invoke-virtual {v6}, LS8/a;->b()Le9/a;

    .line 116
    .line 117
    .line 118
    move-result-object v6

    .line 119
    if-eqz v6, :cond_4

    .line 120
    .line 121
    invoke-virtual {v6}, Le9/a;->w()Ljava/lang/String;

    .line 122
    .line 123
    .line 124
    move-result-object v6

    .line 125
    if-eqz v6, :cond_4

    .line 126
    .line 127
    invoke-static {v6}, Ll8/a;->e(Ljava/lang/String;)I

    .line 128
    .line 129
    .line 130
    move-result v6

    .line 131
    goto :goto_2

    .line 132
    :cond_4
    const/4 v6, 0x0

    .line 133
    :goto_2
    invoke-virtual {v1, v2, v3, v4, v6}, Lorg/xplatform/aggregator/impl/core/data/datasources/a;->d(Ljava/util/Set;ZII)Ljava/util/List;

    .line 134
    .line 135
    .line 136
    move-result-object v1

    .line 137
    if-nez v1, :cond_d

    .line 138
    .line 139
    if-eqz p3, :cond_7

    .line 140
    .line 141
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 142
    .line 143
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 144
    .line 145
    invoke-interface {v6}, Lc8/h;->b()I

    .line 146
    .line 147
    .line 148
    move-result v6

    .line 149
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 150
    .line 151
    invoke-interface {v7}, Lc8/h;->getGroupId()I

    .line 152
    .line 153
    .line 154
    move-result v7

    .line 155
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 156
    .line 157
    invoke-interface {v8}, Lc8/h;->d()I

    .line 158
    .line 159
    .line 160
    move-result v8

    .line 161
    iget-object v15, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 162
    .line 163
    invoke-interface {v15}, Lc8/h;->c()Ljava/lang/String;

    .line 164
    .line 165
    .line 166
    move-result-object v15

    .line 167
    iput-object v2, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->L$0:Ljava/lang/Object;

    .line 168
    .line 169
    iput-object v10, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->L$1:Ljava/lang/Object;

    .line 170
    .line 171
    iput-boolean v3, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->Z$0:Z

    .line 172
    .line 173
    iput v4, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->I$0:I

    .line 174
    .line 175
    iput v5, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->label:I

    .line 176
    .line 177
    move v5, v6

    .line 178
    move v6, v7

    .line 179
    move v7, v8

    .line 180
    move-object v8, v15

    .line 181
    invoke-virtual/range {v1 .. v9}, Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;->p(Ljava/util/Set;ZIIIILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 182
    .line 183
    .line 184
    move-result-object v1

    .line 185
    if-ne v1, v11, :cond_5

    .line 186
    .line 187
    goto :goto_5

    .line 188
    :cond_5
    :goto_3
    check-cast v1, Le8/b;

    .line 189
    .line 190
    invoke-virtual {v1}, Le8/b;->a()Ljava/lang/Object;

    .line 191
    .line 192
    .line 193
    move-result-object v1

    .line 194
    check-cast v1, LL91/b;

    .line 195
    .line 196
    invoke-virtual {v1}, LL91/b;->a()Ljava/util/List;

    .line 197
    .line 198
    .line 199
    move-result-object v1

    .line 200
    if-eqz v1, :cond_6

    .line 201
    .line 202
    new-instance v13, Ljava/util/ArrayList;

    .line 203
    .line 204
    invoke-static {v1, v12}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 205
    .line 206
    .line 207
    move-result v5

    .line 208
    invoke-direct {v13, v5}, Ljava/util/ArrayList;-><init>(I)V

    .line 209
    .line 210
    .line 211
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 212
    .line 213
    .line 214
    move-result-object v1

    .line 215
    :goto_4
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 216
    .line 217
    .line 218
    move-result v5

    .line 219
    if-eqz v5, :cond_6

    .line 220
    .line 221
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 222
    .line 223
    .line 224
    move-result-object v5

    .line 225
    check-cast v5, LL91/a;

    .line 226
    .line 227
    invoke-static {v5, v10}, LA91/d;->b(LL91/a;Ljava/lang/String;)Lorg/xplatform/aggregator/api/model/Game;

    .line 228
    .line 229
    .line 230
    move-result-object v5

    .line 231
    invoke-interface {v13, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 232
    .line 233
    .line 234
    goto :goto_4

    .line 235
    :cond_6
    move-object/from16 v17, v2

    .line 236
    .line 237
    move/from16 v18, v3

    .line 238
    .line 239
    move/from16 v19, v4

    .line 240
    .line 241
    goto/16 :goto_9

    .line 242
    .line 243
    :cond_7
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 244
    .line 245
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 246
    .line 247
    invoke-interface {v5}, Lc8/h;->b()I

    .line 248
    .line 249
    .line 250
    move-result v5

    .line 251
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 252
    .line 253
    invoke-interface {v6}, Lc8/h;->getGroupId()I

    .line 254
    .line 255
    .line 256
    move-result v6

    .line 257
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 258
    .line 259
    invoke-interface {v7}, Lc8/h;->d()I

    .line 260
    .line 261
    .line 262
    move-result v7

    .line 263
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->d:Lc8/h;

    .line 264
    .line 265
    invoke-interface {v8}, Lc8/h;->c()Ljava/lang/String;

    .line 266
    .line 267
    .line 268
    move-result-object v8

    .line 269
    iput-object v2, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->L$0:Ljava/lang/Object;

    .line 270
    .line 271
    iput-object v10, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->L$1:Ljava/lang/Object;

    .line 272
    .line 273
    iput-boolean v3, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->Z$0:Z

    .line 274
    .line 275
    iput v4, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->I$0:I

    .line 276
    .line 277
    iput v15, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getGames$1;->label:I

    .line 278
    .line 279
    invoke-virtual/range {v1 .. v9}, Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;->o(Ljava/util/Set;ZIIIILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 280
    .line 281
    .line 282
    move-result-object v1

    .line 283
    if-ne v1, v11, :cond_8

    .line 284
    .line 285
    :goto_5
    return-object v11

    .line 286
    :cond_8
    move-object/from16 v5, p1

    .line 287
    .line 288
    move/from16 v3, p2

    .line 289
    .line 290
    move v2, v4

    .line 291
    :goto_6
    check-cast v1, Le8/b;

    .line 292
    .line 293
    invoke-virtual {v1}, Le8/b;->a()Ljava/lang/Object;

    .line 294
    .line 295
    .line 296
    move-result-object v1

    .line 297
    check-cast v1, LL91/e;

    .line 298
    .line 299
    invoke-virtual {v1}, LL91/e;->b()Ljava/util/List;

    .line 300
    .line 301
    .line 302
    move-result-object v1

    .line 303
    if-eqz v1, :cond_a

    .line 304
    .line 305
    new-instance v4, Ljava/util/ArrayList;

    .line 306
    .line 307
    invoke-static {v1, v12}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 308
    .line 309
    .line 310
    move-result v6

    .line 311
    invoke-direct {v4, v6}, Ljava/util/ArrayList;-><init>(I)V

    .line 312
    .line 313
    .line 314
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 315
    .line 316
    .line 317
    move-result-object v1

    .line 318
    :goto_7
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 319
    .line 320
    .line 321
    move-result v6

    .line 322
    if-eqz v6, :cond_9

    .line 323
    .line 324
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 325
    .line 326
    .line 327
    move-result-object v6

    .line 328
    check-cast v6, LL91/d;

    .line 329
    .line 330
    invoke-static {v6, v10, v14, v15, v13}, LA91/d;->d(LL91/d;Ljava/lang/String;ZILjava/lang/Object;)Lorg/xplatform/aggregator/api/model/Game;

    .line 331
    .line 332
    .line 333
    move-result-object v6

    .line 334
    invoke-interface {v4, v6}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 335
    .line 336
    .line 337
    goto :goto_7

    .line 338
    :cond_9
    move/from16 v19, v2

    .line 339
    .line 340
    move/from16 v18, v3

    .line 341
    .line 342
    move-object v13, v4

    .line 343
    :goto_8
    move-object/from16 v17, v5

    .line 344
    .line 345
    goto :goto_9

    .line 346
    :cond_a
    move/from16 v19, v2

    .line 347
    .line 348
    move/from16 v18, v3

    .line 349
    .line 350
    goto :goto_8

    .line 351
    :goto_9
    if-nez v13, :cond_b

    .line 352
    .line 353
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 354
    .line 355
    .line 356
    move-result-object v13

    .line 357
    :cond_b
    move-object/from16 v21, v13

    .line 358
    .line 359
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/core/data/datasources/a;

    .line 360
    .line 361
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->e:LS8/a;

    .line 362
    .line 363
    invoke-virtual {v2}, LS8/a;->b()Le9/a;

    .line 364
    .line 365
    .line 366
    move-result-object v2

    .line 367
    if-eqz v2, :cond_c

    .line 368
    .line 369
    invoke-virtual {v2}, Le9/a;->w()Ljava/lang/String;

    .line 370
    .line 371
    .line 372
    move-result-object v2

    .line 373
    if-eqz v2, :cond_c

    .line 374
    .line 375
    invoke-static {v2}, Ll8/a;->e(Ljava/lang/String;)I

    .line 376
    .line 377
    .line 378
    move-result v14

    .line 379
    move/from16 v20, v14

    .line 380
    .line 381
    :goto_a
    move-object/from16 v16, v1

    .line 382
    .line 383
    goto :goto_b

    .line 384
    :cond_c
    const/16 v20, 0x0

    .line 385
    .line 386
    goto :goto_a

    .line 387
    :goto_b
    invoke-virtual/range {v16 .. v21}, Lorg/xplatform/aggregator/impl/core/data/datasources/a;->j(Ljava/util/Set;ZIILjava/util/List;)V

    .line 388
    .line 389
    .line 390
    return-object v21

    .line 391
    :cond_d
    return-object v1
.end method

.method public k(JZILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JZI",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 2
    .line 3
    invoke-virtual {v0}, LT91/a;->e()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_1

    .line 8
    .line 9
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;

    .line 10
    .line 11
    const/4 v7, 0x0

    .line 12
    move-object v2, p0

    .line 13
    move-wide v4, p1

    .line 14
    move v3, p3

    .line 15
    move v6, p4

    .line 16
    invoke-direct/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$removeFavorite$2;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ZJILkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    invoke-static {v1, p5}, Lkotlinx/coroutines/O;->f(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object p2

    .line 27
    if-ne p1, p2, :cond_0

    .line 28
    .line 29
    return-object p1

    .line 30
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 31
    .line 32
    return-object p1

    .line 33
    :cond_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 34
    .line 35
    return-object p1
.end method

.method public l(Lorg/xplatform/aggregator/api/model/FavoriteClearSource;ZILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 11
    .param p1    # Lorg/xplatform/aggregator/api/model/FavoriteClearSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/api/model/FavoriteClearSource;",
            "ZI",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p4, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$clearFavorites$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p4

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$clearFavorites$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$clearFavorites$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$clearFavorites$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$clearFavorites$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p4}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$clearFavorites$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p4, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$clearFavorites$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$clearFavorites$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x1

    .line 35
    if-eqz v2, :cond_3

    .line 36
    .line 37
    if-eq v2, v4, :cond_2

    .line 38
    .line 39
    if-ne v2, v3, :cond_1

    .line 40
    .line 41
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    move-object v7, p0

    .line 45
    goto :goto_3

    .line 46
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 47
    .line 48
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 49
    .line 50
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    throw p1

    .line 54
    :cond_2
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    move-object v7, p0

    .line 58
    goto :goto_1

    .line 59
    :cond_3
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 60
    .line 61
    .line 62
    iget-object p4, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->f:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 63
    .line 64
    new-instance v5, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$clearFavorites$2;

    .line 65
    .line 66
    const/4 v10, 0x0

    .line 67
    move-object v7, p0

    .line 68
    move-object v8, p1

    .line 69
    move v6, p2

    .line 70
    move v9, p3

    .line 71
    invoke-direct/range {v5 .. v10}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$clearFavorites$2;-><init>(ZLorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lorg/xplatform/aggregator/api/model/FavoriteClearSource;ILkotlin/coroutines/e;)V

    .line 72
    .line 73
    .line 74
    iput v4, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$clearFavorites$1;->label:I

    .line 75
    .line 76
    invoke-virtual {p4, v5, v0}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    if-ne p1, v1, :cond_4

    .line 81
    .line 82
    goto :goto_2

    .line 83
    :cond_4
    :goto_1
    iput v3, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$clearFavorites$1;->label:I

    .line 84
    .line 85
    invoke-virtual {p0, v0}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->m(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    if-ne p1, v1, :cond_5

    .line 90
    .line 91
    :goto_2
    return-object v1

    .line 92
    :cond_5
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 93
    .line 94
    return-object p1
.end method

.method public m(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 2
    .line 3
    invoke-virtual {p1}, LT91/a;->f()V

    .line 4
    .line 5
    .line 6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p1
.end method

.method public n(ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getFavoriteGames$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p3

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getFavoriteGames$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getFavoriteGames$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getFavoriteGames$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getFavoriteGames$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p3}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getFavoriteGames$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p3, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getFavoriteGames$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getFavoriteGames$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iput v3, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getFavoriteGames$1;->label:I

    .line 54
    .line 55
    invoke-virtual {p0, p1, p2, v0}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->z(ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    if-ne p1, v1, :cond_3

    .line 60
    .line 61
    return-object v1

    .line 62
    :cond_3
    :goto_1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 63
    .line 64
    invoke-virtual {p1}, LT91/a;->i()Ljava/util/List;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    return-object p1
.end method

.method public o()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 2
    .line 3
    invoke-virtual {v0}, LT91/a;->h()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final y(Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LT91/a;->a(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final z(ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    move-object/from16 v0, p3

    .line 4
    .line 5
    instance-of v2, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    move-object v2, v0

    .line 10
    check-cast v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;

    .line 11
    .line 12
    iget v3, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->label:I

    .line 13
    .line 14
    const/high16 v4, -0x80000000

    .line 15
    .line 16
    and-int v5, v3, v4

    .line 17
    .line 18
    if-eqz v5, :cond_0

    .line 19
    .line 20
    sub-int/2addr v3, v4

    .line 21
    iput v3, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->label:I

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;

    .line 25
    .line 26
    invoke-direct {v2, v1, v0}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    :goto_0
    iget-object v0, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->result:Ljava/lang/Object;

    .line 30
    .line 31
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    iget v4, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->label:I

    .line 36
    .line 37
    const/4 v5, 0x2

    .line 38
    const/4 v6, 0x3

    .line 39
    const/4 v7, 0x0

    .line 40
    const/4 v8, 0x1

    .line 41
    const/4 v9, 0x0

    .line 42
    if-eqz v4, :cond_4

    .line 43
    .line 44
    if-eq v4, v8, :cond_3

    .line 45
    .line 46
    if-eq v4, v5, :cond_2

    .line 47
    .line 48
    if-ne v4, v6, :cond_1

    .line 49
    .line 50
    iget v4, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->I$1:I

    .line 51
    .line 52
    iget v10, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->I$0:I

    .line 53
    .line 54
    iget-boolean v11, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->Z$0:Z

    .line 55
    .line 56
    iget-object v12, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$3:Ljava/lang/Object;

    .line 57
    .line 58
    check-cast v12, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;

    .line 59
    .line 60
    iget-object v13, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$2:Ljava/lang/Object;

    .line 61
    .line 62
    check-cast v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 63
    .line 64
    iget-object v14, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$1:Ljava/lang/Object;

    .line 65
    .line 66
    check-cast v14, Lkotlinx/coroutines/sync/a;

    .line 67
    .line 68
    iget-object v15, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$0:Ljava/lang/Object;

    .line 69
    .line 70
    check-cast v15, Ljava/lang/String;

    .line 71
    .line 72
    :try_start_0
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 73
    .line 74
    .line 75
    move/from16 p3, v10

    .line 76
    .line 77
    move v10, v4

    .line 78
    move-object v4, v12

    .line 79
    move v12, v11

    .line 80
    move/from16 v11, p3

    .line 81
    .line 82
    const/16 p3, 0x1

    .line 83
    .line 84
    goto/16 :goto_a

    .line 85
    .line 86
    :catchall_0
    move-exception v0

    .line 87
    goto/16 :goto_d

    .line 88
    .line 89
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 90
    .line 91
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 92
    .line 93
    invoke-direct {v0, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 94
    .line 95
    .line 96
    throw v0

    .line 97
    :cond_2
    iget v4, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->I$1:I

    .line 98
    .line 99
    iget v10, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->I$0:I

    .line 100
    .line 101
    iget-boolean v11, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->Z$0:Z

    .line 102
    .line 103
    iget-object v12, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$3:Ljava/lang/Object;

    .line 104
    .line 105
    check-cast v12, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;

    .line 106
    .line 107
    iget-object v13, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$2:Ljava/lang/Object;

    .line 108
    .line 109
    check-cast v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 110
    .line 111
    iget-object v14, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$1:Ljava/lang/Object;

    .line 112
    .line 113
    check-cast v14, Lkotlinx/coroutines/sync/a;

    .line 114
    .line 115
    iget-object v15, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$0:Ljava/lang/Object;

    .line 116
    .line 117
    check-cast v15, Ljava/lang/String;

    .line 118
    .line 119
    :try_start_1
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 120
    .line 121
    .line 122
    const/16 p3, 0x1

    .line 123
    .line 124
    goto/16 :goto_3

    .line 125
    .line 126
    :catchall_1
    move-exception v0

    .line 127
    const/16 p3, 0x1

    .line 128
    .line 129
    goto/16 :goto_5

    .line 130
    .line 131
    :cond_3
    iget-boolean v4, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->Z$0:Z

    .line 132
    .line 133
    iget-object v10, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$1:Ljava/lang/Object;

    .line 134
    .line 135
    check-cast v10, Lkotlinx/coroutines/sync/a;

    .line 136
    .line 137
    iget-object v11, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$0:Ljava/lang/Object;

    .line 138
    .line 139
    check-cast v11, Ljava/lang/String;

    .line 140
    .line 141
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 142
    .line 143
    .line 144
    move-object v14, v10

    .line 145
    goto :goto_1

    .line 146
    :cond_4
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 147
    .line 148
    .line 149
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->g:Lkotlinx/coroutines/sync/a;

    .line 150
    .line 151
    move-object/from16 v4, p2

    .line 152
    .line 153
    iput-object v4, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$0:Ljava/lang/Object;

    .line 154
    .line 155
    iput-object v0, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$1:Ljava/lang/Object;

    .line 156
    .line 157
    move/from16 v10, p1

    .line 158
    .line 159
    iput-boolean v10, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->Z$0:Z

    .line 160
    .line 161
    iput v8, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->label:I

    .line 162
    .line 163
    invoke-interface {v0, v9, v2}, Lkotlinx/coroutines/sync/a;->g(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 164
    .line 165
    .line 166
    move-result-object v11

    .line 167
    if-ne v11, v3, :cond_5

    .line 168
    .line 169
    goto/16 :goto_9

    .line 170
    .line 171
    :cond_5
    move-object v14, v0

    .line 172
    move-object v11, v4

    .line 173
    move v4, v10

    .line 174
    :goto_1
    :try_start_2
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 175
    .line 176
    invoke-virtual {v0}, LT91/a;->l()Z

    .line 177
    .line 178
    .line 179
    move-result v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_6

    .line 180
    if-eqz v0, :cond_11

    .line 181
    .line 182
    :try_start_3
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 183
    .line 184
    move-object v13, v1

    .line 185
    move v12, v4

    .line 186
    move-object v15, v11

    .line 187
    const/4 v10, 0x0

    .line 188
    const/4 v11, 0x0

    .line 189
    move-object v4, v2

    .line 190
    :goto_2
    :try_start_4
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 191
    .line 192
    iget-object v0, v13, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->f:Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_4

    .line 193
    .line 194
    const/16 p3, 0x1

    .line 195
    .line 196
    :try_start_5
    new-instance v8, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$2$games$1$1$1;

    .line 197
    .line 198
    invoke-direct {v8, v12, v13, v15, v9}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$2$games$1$1$1;-><init>(ZLorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 199
    .line 200
    .line 201
    iput-object v15, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$0:Ljava/lang/Object;

    .line 202
    .line 203
    iput-object v14, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$1:Ljava/lang/Object;

    .line 204
    .line 205
    iput-object v13, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$2:Ljava/lang/Object;

    .line 206
    .line 207
    iput-object v4, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$3:Ljava/lang/Object;

    .line 208
    .line 209
    iput-boolean v12, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->Z$0:Z

    .line 210
    .line 211
    iput v11, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->I$0:I

    .line 212
    .line 213
    iput v10, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->I$1:I

    .line 214
    .line 215
    iput v5, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->label:I

    .line 216
    .line 217
    invoke-virtual {v0, v8, v2}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 218
    .line 219
    .line 220
    move-result-object v0
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_3

    .line 221
    if-ne v0, v3, :cond_6

    .line 222
    .line 223
    goto/16 :goto_9

    .line 224
    .line 225
    :cond_6
    move/from16 v16, v12

    .line 226
    .line 227
    move-object v12, v4

    .line 228
    move v4, v10

    .line 229
    move v10, v11

    .line 230
    move/from16 v11, v16

    .line 231
    .line 232
    :goto_3
    :try_start_6
    check-cast v0, Ljava/util/List;

    .line 233
    .line 234
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 235
    .line 236
    .line 237
    move-result-object v0
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_2

    .line 238
    goto/16 :goto_c

    .line 239
    .line 240
    :catchall_2
    move-exception v0

    .line 241
    goto :goto_5

    .line 242
    :catchall_3
    move-exception v0

    .line 243
    :goto_4
    move/from16 v16, v12

    .line 244
    .line 245
    move-object v12, v4

    .line 246
    move v4, v10

    .line 247
    move v10, v11

    .line 248
    move/from16 v11, v16

    .line 249
    .line 250
    goto :goto_5

    .line 251
    :catchall_4
    move-exception v0

    .line 252
    const/16 p3, 0x1

    .line 253
    .line 254
    goto :goto_4

    .line 255
    :goto_5
    if-eqz v10, :cond_7

    .line 256
    .line 257
    :try_start_7
    instance-of v8, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 258
    .line 259
    if-eqz v8, :cond_7

    .line 260
    .line 261
    move-object v8, v0

    .line 262
    check-cast v8, Lcom/xbet/onexcore/data/model/ServerException;

    .line 263
    .line 264
    invoke-virtual {v8}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 265
    .line 266
    .line 267
    move-result v8

    .line 268
    if-eqz v8, :cond_7

    .line 269
    .line 270
    const/4 v8, 0x1

    .line 271
    goto :goto_6

    .line 272
    :cond_7
    const/4 v8, 0x0

    .line 273
    :goto_6
    instance-of v5, v0, Ljava/util/concurrent/CancellationException;

    .line 274
    .line 275
    if-nez v5, :cond_f

    .line 276
    .line 277
    instance-of v5, v0, Ljava/net/ConnectException;

    .line 278
    .line 279
    if-nez v5, :cond_f

    .line 280
    .line 281
    if-nez v8, :cond_f

    .line 282
    .line 283
    instance-of v5, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 284
    .line 285
    if-eqz v5, :cond_a

    .line 286
    .line 287
    move-object v5, v0

    .line 288
    check-cast v5, Lcom/xbet/onexcore/data/model/ServerException;

    .line 289
    .line 290
    invoke-virtual {v5}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 291
    .line 292
    .line 293
    move-result v5

    .line 294
    if-nez v5, :cond_9

    .line 295
    .line 296
    move-object v5, v0

    .line 297
    check-cast v5, Lcom/xbet/onexcore/data/model/ServerException;

    .line 298
    .line 299
    invoke-virtual {v5}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 300
    .line 301
    .line 302
    move-result v5

    .line 303
    if-eqz v5, :cond_8

    .line 304
    .line 305
    goto :goto_7

    .line 306
    :cond_8
    const/4 v5, 0x0

    .line 307
    goto :goto_8

    .line 308
    :cond_9
    :goto_7
    const/4 v5, 0x1

    .line 309
    goto :goto_8

    .line 310
    :cond_a
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 311
    .line 312
    .line 313
    move-result v5

    .line 314
    if-nez v5, :cond_8

    .line 315
    .line 316
    goto :goto_7

    .line 317
    :goto_8
    add-int/lit8 v4, v4, 0x1

    .line 318
    .line 319
    if-gt v4, v6, :cond_d

    .line 320
    .line 321
    if-eqz v5, :cond_b

    .line 322
    .line 323
    goto :goto_b

    .line 324
    :cond_b
    new-instance v5, Ljava/lang/StringBuilder;

    .line 325
    .line 326
    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    .line 327
    .line 328
    .line 329
    const-string v8, "error ("

    .line 330
    .line 331
    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 332
    .line 333
    .line 334
    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 335
    .line 336
    .line 337
    const-string v8, "): "

    .line 338
    .line 339
    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 340
    .line 341
    .line 342
    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 343
    .line 344
    .line 345
    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 346
    .line 347
    .line 348
    move-result-object v0

    .line 349
    sget-object v5, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 350
    .line 351
    invoke-virtual {v5, v0}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 352
    .line 353
    .line 354
    iput-object v15, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$0:Ljava/lang/Object;

    .line 355
    .line 356
    iput-object v14, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$1:Ljava/lang/Object;

    .line 357
    .line 358
    iput-object v13, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$2:Ljava/lang/Object;

    .line 359
    .line 360
    iput-object v12, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->L$3:Ljava/lang/Object;

    .line 361
    .line 362
    iput-boolean v11, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->Z$0:Z

    .line 363
    .line 364
    iput v10, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->I$0:I

    .line 365
    .line 366
    iput v4, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->I$1:I

    .line 367
    .line 368
    iput v6, v2, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$ensureFavorites$1;->label:I

    .line 369
    .line 370
    move/from16 p1, v10

    .line 371
    .line 372
    const-wide/16 v9, 0xbb8

    .line 373
    .line 374
    invoke-static {v9, v10, v12}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 375
    .line 376
    .line 377
    move-result-object v0

    .line 378
    if-ne v0, v3, :cond_c

    .line 379
    .line 380
    :goto_9
    return-object v3

    .line 381
    :cond_c
    move v10, v4

    .line 382
    move-object v4, v12

    .line 383
    move v12, v11

    .line 384
    move/from16 v11, p1

    .line 385
    .line 386
    :goto_a
    const/4 v5, 0x2

    .line 387
    const/4 v8, 0x1

    .line 388
    const/4 v9, 0x0

    .line 389
    goto/16 :goto_2

    .line 390
    .line 391
    :cond_d
    :goto_b
    sget-object v2, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 392
    .line 393
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 394
    .line 395
    .line 396
    move-result-object v0

    .line 397
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 398
    .line 399
    .line 400
    move-result-object v0

    .line 401
    :goto_c
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 402
    .line 403
    .line 404
    move-result-object v2

    .line 405
    invoke-static {v0}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 406
    .line 407
    .line 408
    move-result v3

    .line 409
    if-eqz v3, :cond_e

    .line 410
    .line 411
    move-object v0, v2

    .line 412
    :cond_e
    check-cast v0, Ljava/util/List;

    .line 413
    .line 414
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 415
    .line 416
    .line 417
    move-result-object v0

    .line 418
    goto :goto_e

    .line 419
    :cond_f
    throw v0
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_0

    .line 420
    :goto_d
    :try_start_8
    sget-object v2, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 421
    .line 422
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 423
    .line 424
    .line 425
    move-result-object v0

    .line 426
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 427
    .line 428
    .line 429
    move-result-object v0

    .line 430
    :goto_e
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 431
    .line 432
    .line 433
    move-result-object v2

    .line 434
    invoke-static {v0}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 435
    .line 436
    .line 437
    move-result v3

    .line 438
    if-eqz v3, :cond_10

    .line 439
    .line 440
    move-object v0, v2

    .line 441
    :cond_10
    check-cast v0, Ljava/util/List;

    .line 442
    .line 443
    iget-object v2, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 444
    .line 445
    invoke-virtual {v2, v7}, LT91/a;->q(Z)V

    .line 446
    .line 447
    .line 448
    iget-object v2, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c:LT91/a;

    .line 449
    .line 450
    invoke-virtual {v2, v0}, LT91/a;->b(Ljava/util/List;)V

    .line 451
    .line 452
    .line 453
    goto :goto_f

    .line 454
    :catchall_5
    move-exception v0

    .line 455
    const/4 v5, 0x0

    .line 456
    goto :goto_10

    .line 457
    :cond_11
    :goto_f
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_5

    .line 458
    .line 459
    const/4 v5, 0x0

    .line 460
    invoke-interface {v14, v5}, Lkotlinx/coroutines/sync/a;->h(Ljava/lang/Object;)V

    .line 461
    .line 462
    .line 463
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 464
    .line 465
    return-object v0

    .line 466
    :catchall_6
    move-exception v0

    .line 467
    move-object v5, v9

    .line 468
    :goto_10
    invoke-interface {v14, v5}, Lkotlinx/coroutines/sync/a;->h(Ljava/lang/Object;)V

    .line 469
    .line 470
    .line 471
    throw v0
.end method
