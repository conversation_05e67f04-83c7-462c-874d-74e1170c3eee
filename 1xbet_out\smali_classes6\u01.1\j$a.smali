.class public final Lu01/j$a;
.super Landroidx/recyclerview/widget/RecyclerView$i;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lu01/j;-><init>(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Landroidx/viewpager2/widget/ViewPager2;Lkotlin/jvm/functions/Function1;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0019\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u000b*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u000f\u0010\u0003\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u001f\u0010\u0008\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\n\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\n\u0010\tJ\u001f\u0010\u000b\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\tJ\'\u0010\u000e\u001a\u00020\u00022\u0006\u0010\u000c\u001a\u00020\u00052\u0006\u0010\r\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u00a8\u0006\u0010"
    }
    d2 = {
        "u01/j$a",
        "Landroidx/recyclerview/widget/RecyclerView$i;",
        "",
        "onChanged",
        "()V",
        "",
        "positionStart",
        "itemCount",
        "onItemRangeChanged",
        "(II)V",
        "onItemRangeInserted",
        "onItemRangeRemoved",
        "fromPosition",
        "toPosition",
        "onItemRangeMoved",
        "(III)V",
        "uikit_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lu01/j;


# direct methods
.method public constructor <init>(Lu01/j;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lu01/j$a;->a:Lu01/j;

    .line 2
    .line 3
    invoke-direct {p0}, Landroidx/recyclerview/widget/RecyclerView$i;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onChanged()V
    .locals 1

    .line 1
    iget-object v0, p0, Lu01/j$a;->a:Lu01/j;

    .line 2
    .line 3
    invoke-static {v0}, Lu01/j;->c(Lu01/j;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public onItemRangeChanged(II)V
    .locals 0

    .line 1
    iget-object p1, p0, Lu01/j$a;->a:Lu01/j;

    .line 2
    .line 3
    invoke-static {p1}, Lu01/j;->c(Lu01/j;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public onItemRangeInserted(II)V
    .locals 0

    .line 1
    iget-object p1, p0, Lu01/j$a;->a:Lu01/j;

    .line 2
    .line 3
    invoke-static {p1}, Lu01/j;->c(Lu01/j;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public onItemRangeMoved(III)V
    .locals 0

    .line 1
    iget-object p1, p0, Lu01/j$a;->a:Lu01/j;

    .line 2
    .line 3
    invoke-static {p1}, Lu01/j;->c(Lu01/j;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public onItemRangeRemoved(II)V
    .locals 0

    .line 1
    iget-object p1, p0, Lu01/j$a;->a:Lu01/j;

    .line 2
    .line 3
    invoke-static {p1}, Lu01/j;->c(Lu01/j;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
