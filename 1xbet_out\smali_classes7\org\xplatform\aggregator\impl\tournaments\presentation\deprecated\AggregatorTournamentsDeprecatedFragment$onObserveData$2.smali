.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.deprecated.AggregatorTournamentsDeprecatedFragment$onObserveData$2"
    f = "AggregatorTournamentsDeprecatedFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a;",
        "banners",
        "",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->invoke(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_4

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$c;

    .line 16
    .line 17
    const/4 v1, 0x0

    .line 18
    const/16 v2, 0x8

    .line 19
    .line 20
    if-eqz v0, :cond_0

    .line 21
    .line 22
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    .line 23
    .line 24
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)LS91/M;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    iget-object v0, v0, LS91/M;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 29
    .line 30
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 31
    .line 32
    .line 33
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    .line 34
    .line 35
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)LS91/M;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    iget-object v0, v0, LS91/M;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 40
    .line 41
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 42
    .line 43
    .line 44
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    .line 45
    .line 46
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->v3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)Ldb1/e;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$c;

    .line 51
    .line 52
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$c;->a()Ljava/util/List;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    invoke-virtual {v0, p1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 57
    .line 58
    .line 59
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    .line 60
    .line 61
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)LS91/M;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    iget-object p1, p1, LS91/M;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 66
    .line 67
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 68
    .line 69
    .line 70
    goto :goto_0

    .line 71
    :cond_0
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$b;

    .line 72
    .line 73
    if-eqz v0, :cond_1

    .line 74
    .line 75
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    .line 76
    .line 77
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$b;

    .line 78
    .line 79
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$b;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->D3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 84
    .line 85
    .line 86
    goto :goto_0

    .line 87
    :cond_1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$d;

    .line 88
    .line 89
    if-eqz v0, :cond_2

    .line 90
    .line 91
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    .line 92
    .line 93
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)LS91/M;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    iget-object v0, v0, LS91/M;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 98
    .line 99
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 100
    .line 101
    .line 102
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    .line 103
    .line 104
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)LS91/M;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    iget-object v0, v0, LS91/M;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 109
    .line 110
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 111
    .line 112
    .line 113
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    .line 114
    .line 115
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->v3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;)Ldb1/e;

    .line 116
    .line 117
    .line 118
    move-result-object v0

    .line 119
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$d;

    .line 120
    .line 121
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$d;->a()Ljava/util/List;

    .line 122
    .line 123
    .line 124
    move-result-object v1

    .line 125
    invoke-virtual {v0, v1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 126
    .line 127
    .line 128
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    .line 129
    .line 130
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$d;->a()Ljava/util/List;

    .line 131
    .line 132
    .line 133
    move-result-object p1

    .line 134
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->y3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Ljava/util/List;)V

    .line 135
    .line 136
    .line 137
    goto :goto_0

    .line 138
    :cond_2
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$a;

    .line 139
    .line 140
    if-eqz v0, :cond_3

    .line 141
    .line 142
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment$onObserveData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    .line 143
    .line 144
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$a;

    .line 145
    .line 146
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$a;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 147
    .line 148
    .line 149
    move-result-object p1

    .line 150
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->D3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 151
    .line 152
    .line 153
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 154
    .line 155
    return-object p1

    .line 156
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 157
    .line 158
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 159
    .line 160
    .line 161
    throw p1

    .line 162
    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 163
    .line 164
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 165
    .line 166
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 167
    .line 168
    .line 169
    throw p1
.end method
