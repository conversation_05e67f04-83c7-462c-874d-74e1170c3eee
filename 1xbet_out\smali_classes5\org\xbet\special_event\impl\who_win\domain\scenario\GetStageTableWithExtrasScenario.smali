.class public final Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0008\u0008\u0001\u0018\u00002\u00020\u0001B!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\u000e\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\r0\u000c0\u000b0\n\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ/\u0010\u0014\u001a\u00020\r*\u00020\r2\u000c\u0010\u0011\u001a\u0008\u0012\u0004\u0012\u00020\u00100\u000c2\u000c\u0010\u0013\u001a\u0008\u0012\u0004\u0012\u00020\u00120\u000cH\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J;\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u000c*\u0008\u0012\u0004\u0012\u00020\u00160\u000c2\u000c\u0010\u0011\u001a\u0008\u0012\u0004\u0012\u00020\u00100\u000c2\u000c\u0010\u0013\u001a\u0008\u0012\u0004\u0012\u00020\u00120\u000cH\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J!\u0010\u001c\u001a\u00020\u001b*\u0008\u0012\u0004\u0012\u00020\u00100\u000c2\u0006\u0010\u001a\u001a\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ!\u0010\u001e\u001a\u00020\u001b*\u0008\u0012\u0004\u0012\u00020\u00120\u000c2\u0006\u0010\u001a\u001a\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001dR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001f\u0010 R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010!R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\"\u00a8\u0006#"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
        "",
        "Lorg/xbet/special_event/impl/who_win/domain/usecase/a;",
        "getStageTableResultStreamUseCase",
        "Lorg/xbet/feature/coeftrack/domain/usecases/m;",
        "getUpdatedTrackCoefStreamUseCase",
        "LNP/a;",
        "getAllBetEventsUseCase",
        "<init>",
        "(Lorg/xbet/special_event/impl/who_win/domain/usecase/a;Lorg/xbet/feature/coeftrack/domain/usecases/m;LNP/a;)V",
        "Lkotlinx/coroutines/flow/e;",
        "LKo0/a;",
        "",
        "LDy0/a;",
        "d",
        "()Lkotlinx/coroutines/flow/e;",
        "LWn/a;",
        "addedToCouponEvents",
        "LDP/a;",
        "trackedEvents",
        "e",
        "(LDy0/a;Ljava/util/List;Ljava/util/List;)LDy0/a;",
        "LDy0/d;",
        "f",
        "(Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;",
        "LDy0/b;",
        "eventModel",
        "",
        "b",
        "(Ljava/util/List;LDy0/b;)Z",
        "c",
        "a",
        "Lorg/xbet/special_event/impl/who_win/domain/usecase/a;",
        "Lorg/xbet/feature/coeftrack/domain/usecases/m;",
        "LNP/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/special_event/impl/who_win/domain/usecase/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/feature/coeftrack/domain/usecases/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LNP/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lorg/xbet/special_event/impl/who_win/domain/usecase/a;Lorg/xbet/feature/coeftrack/domain/usecases/m;LNP/a;)V
    .locals 0
    .param p1    # Lorg/xbet/special_event/impl/who_win/domain/usecase/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/feature/coeftrack/domain/usecases/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LNP/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->a:Lorg/xbet/special_event/impl/who_win/domain/usecase/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->b:Lorg/xbet/feature/coeftrack/domain/usecases/m;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->c:LNP/a;

    .line 9
    .line 10
    return-void
.end method

.method public static final synthetic a(Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;LDy0/a;Ljava/util/List;Ljava/util/List;)LDy0/a;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->e(LDy0/a;Ljava/util/List;Ljava/util/List;)LDy0/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public final b(Ljava/util/List;LDy0/b;)Z
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LWn/a;",
            ">;",
            "LDy0/b;",
            ")Z"
        }
    .end annotation

    .line 1
    invoke-static {p1}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    if-eqz v0, :cond_0

    .line 13
    .line 14
    return v1

    .line 15
    :cond_0
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    :cond_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    if-eqz v0, :cond_2

    .line 24
    .line 25
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    check-cast v0, LWn/a;

    .line 30
    .line 31
    invoke-virtual {v0}, LWn/a;->b()J

    .line 32
    .line 33
    .line 34
    move-result-wide v2

    .line 35
    invoke-virtual {p2}, LDy0/b;->f()I

    .line 36
    .line 37
    .line 38
    move-result v4

    .line 39
    int-to-long v4, v4

    .line 40
    cmp-long v6, v2, v4

    .line 41
    .line 42
    if-nez v6, :cond_1

    .line 43
    .line 44
    invoke-virtual {v0}, LWn/a;->g()J

    .line 45
    .line 46
    .line 47
    move-result-wide v2

    .line 48
    invoke-virtual {p2}, LDy0/b;->k()I

    .line 49
    .line 50
    .line 51
    move-result v4

    .line 52
    int-to-long v4, v4

    .line 53
    cmp-long v6, v2, v4

    .line 54
    .line 55
    if-nez v6, :cond_1

    .line 56
    .line 57
    invoke-virtual {v0}, LWn/a;->e()J

    .line 58
    .line 59
    .line 60
    move-result-wide v2

    .line 61
    invoke-virtual {p2}, LDy0/b;->i()LDy0/b$a;

    .line 62
    .line 63
    .line 64
    move-result-object v4

    .line 65
    invoke-virtual {v4}, LDy0/b$a;->a()J

    .line 66
    .line 67
    .line 68
    move-result-wide v4

    .line 69
    cmp-long v6, v2, v4

    .line 70
    .line 71
    if-nez v6, :cond_1

    .line 72
    .line 73
    invoke-virtual {v0}, LWn/a;->d()D

    .line 74
    .line 75
    .line 76
    move-result-wide v2

    .line 77
    invoke-virtual {p2}, LDy0/b;->h()D

    .line 78
    .line 79
    .line 80
    move-result-wide v4

    .line 81
    cmpg-double v0, v2, v4

    .line 82
    .line 83
    if-nez v0, :cond_1

    .line 84
    .line 85
    const/4 p1, 0x1

    .line 86
    return p1

    .line 87
    :cond_2
    return v1
.end method

.method public final c(Ljava/util/List;LDy0/b;)Z
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LDP/a;",
            ">;",
            "LDy0/b;",
            ")Z"
        }
    .end annotation

    .line 1
    invoke-static {p1}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    if-eqz v0, :cond_0

    .line 13
    .line 14
    return v1

    .line 15
    :cond_0
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    :cond_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    if-eqz v0, :cond_2

    .line 24
    .line 25
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    check-cast v0, LDP/a;

    .line 30
    .line 31
    invoke-virtual {v0}, LDP/a;->c()Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    .line 32
    .line 33
    .line 34
    move-result-object v2

    .line 35
    invoke-virtual {v2}, Lorg/xbet/betting/core/zip/model/bet/BetInfo;->getGameId()J

    .line 36
    .line 37
    .line 38
    move-result-wide v2

    .line 39
    invoke-virtual {p2}, LDy0/b;->f()I

    .line 40
    .line 41
    .line 42
    move-result v4

    .line 43
    int-to-long v4, v4

    .line 44
    cmp-long v6, v2, v4

    .line 45
    .line 46
    if-nez v6, :cond_1

    .line 47
    .line 48
    invoke-virtual {v0}, LDP/a;->c()Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    invoke-virtual {v2}, Lorg/xbet/betting/core/zip/model/bet/BetInfo;->getBetId()J

    .line 53
    .line 54
    .line 55
    move-result-wide v2

    .line 56
    invoke-virtual {p2}, LDy0/b;->k()I

    .line 57
    .line 58
    .line 59
    move-result v4

    .line 60
    int-to-long v4, v4

    .line 61
    cmp-long v6, v2, v4

    .line 62
    .line 63
    if-nez v6, :cond_1

    .line 64
    .line 65
    invoke-virtual {v0}, LDP/a;->c()Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    .line 66
    .line 67
    .line 68
    move-result-object v2

    .line 69
    invoke-virtual {v2}, Lorg/xbet/betting/core/zip/model/bet/BetInfo;->getPlayerId()J

    .line 70
    .line 71
    .line 72
    move-result-wide v2

    .line 73
    invoke-virtual {p2}, LDy0/b;->i()LDy0/b$a;

    .line 74
    .line 75
    .line 76
    move-result-object v4

    .line 77
    invoke-virtual {v4}, LDy0/b$a;->a()J

    .line 78
    .line 79
    .line 80
    move-result-wide v4

    .line 81
    cmp-long v6, v2, v4

    .line 82
    .line 83
    if-nez v6, :cond_1

    .line 84
    .line 85
    invoke-virtual {v0}, LDP/a;->c()Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    .line 86
    .line 87
    .line 88
    move-result-object v0

    .line 89
    invoke-virtual {v0}, Lorg/xbet/betting/core/zip/model/bet/BetInfo;->getParam()D

    .line 90
    .line 91
    .line 92
    move-result-wide v2

    .line 93
    invoke-virtual {p2}, LDy0/b;->h()D

    .line 94
    .line 95
    .line 96
    move-result-wide v4

    .line 97
    cmpg-double v0, v2, v4

    .line 98
    .line 99
    if-nez v0, :cond_1

    .line 100
    .line 101
    const/4 p1, 0x1

    .line 102
    return p1

    .line 103
    :cond_2
    return v1
.end method

.method public final d()Lkotlinx/coroutines/flow/e;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LKo0/a<",
            "Ljava/util/List<",
            "LDy0/a;",
            ">;>;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->a:Lorg/xbet/special_event/impl/who_win/domain/usecase/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/who_win/domain/usecase/a;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->b:Lorg/xbet/feature/coeftrack/domain/usecases/m;

    .line 8
    .line 9
    invoke-interface {v1}, Lorg/xbet/feature/coeftrack/domain/usecases/m;->invoke()Lkotlinx/coroutines/flow/e;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    iget-object v2, p0, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->c:LNP/a;

    .line 14
    .line 15
    invoke-interface {v2}, LNP/a;->invoke()Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    new-instance v3, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;

    .line 20
    .line 21
    const/4 v4, 0x0

    .line 22
    invoke-direct {v3, p0, v4}, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario$invoke$1;-><init>(Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    invoke-static {v0, v1, v2, v3}, Lkotlinx/coroutines/flow/g;->p(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/o;)Lkotlinx/coroutines/flow/e;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    return-object v0
.end method

.method public final e(LDy0/a;Ljava/util/List;Ljava/util/List;)LDy0/a;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LDy0/a;",
            "Ljava/util/List<",
            "LWn/a;",
            ">;",
            "Ljava/util/List<",
            "LDP/a;",
            ">;)",
            "LDy0/a;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, LDy0/a$a;

    .line 2
    .line 3
    const/4 v1, 0x2

    .line 4
    const/4 v2, 0x0

    .line 5
    if-eqz v0, :cond_1

    .line 6
    .line 7
    check-cast p1, LDy0/a$a;

    .line 8
    .line 9
    invoke-virtual {p1}, LDy0/a$a;->e()Ljava/util/List;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    new-instance v3, Ljava/util/ArrayList;

    .line 14
    .line 15
    const/16 v4, 0xa

    .line 16
    .line 17
    invoke-static {v0, v4}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 18
    .line 19
    .line 20
    move-result v4

    .line 21
    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 22
    .line 23
    .line 24
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 29
    .line 30
    .line 31
    move-result v4

    .line 32
    if-eqz v4, :cond_0

    .line 33
    .line 34
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v4

    .line 38
    check-cast v4, LDy0/a$a$a;

    .line 39
    .line 40
    invoke-virtual {v4}, LDy0/a$a$a;->c()Ljava/util/List;

    .line 41
    .line 42
    .line 43
    move-result-object v5

    .line 44
    invoke-virtual {p0, v5, p2, p3}, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->f(Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;

    .line 45
    .line 46
    .line 47
    move-result-object v5

    .line 48
    const/4 v6, 0x1

    .line 49
    invoke-static {v4, v2, v5, v6, v2}, LDy0/a$a$a;->b(LDy0/a$a$a;Ljava/lang/String;Ljava/util/List;ILjava/lang/Object;)LDy0/a$a$a;

    .line 50
    .line 51
    .line 52
    move-result-object v4

    .line 53
    invoke-interface {v3, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 54
    .line 55
    .line 56
    goto :goto_0

    .line 57
    :cond_0
    invoke-static {p1, v3, v2, v1, v2}, LDy0/a$a;->d(LDy0/a$a;Ljava/util/List;Ljava/util/List;ILjava/lang/Object;)LDy0/a$a;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    return-object p1

    .line 62
    :cond_1
    instance-of v0, p1, LDy0/a$b$a;

    .line 63
    .line 64
    if-eqz v0, :cond_2

    .line 65
    .line 66
    check-cast p1, LDy0/a$b$a;

    .line 67
    .line 68
    invoke-virtual {p1}, LDy0/a$b$a;->b()Ljava/util/List;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    invoke-virtual {p0, v0, p2, p3}, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->f(Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;

    .line 73
    .line 74
    .line 75
    move-result-object p2

    .line 76
    invoke-static {p1, p2, v2, v1, v2}, LDy0/a$b$a;->d(LDy0/a$b$a;Ljava/util/List;Ljava/util/List;ILjava/lang/Object;)LDy0/a$b$a;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    return-object p1

    .line 81
    :cond_2
    instance-of v0, p1, LDy0/a$b$b;

    .line 82
    .line 83
    if-eqz v0, :cond_3

    .line 84
    .line 85
    check-cast p1, LDy0/a$b$b;

    .line 86
    .line 87
    invoke-virtual {p1}, LDy0/a$b$b;->b()Ljava/util/List;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    invoke-virtual {p0, v0, p2, p3}, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->f(Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;

    .line 92
    .line 93
    .line 94
    move-result-object p2

    .line 95
    invoke-static {p1, p2, v2, v1, v2}, LDy0/a$b$b;->d(LDy0/a$b$b;Ljava/util/List;Ljava/util/List;ILjava/lang/Object;)LDy0/a$b$b;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    return-object p1

    .line 100
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 101
    .line 102
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 103
    .line 104
    .line 105
    throw p1
.end method

.method public final f(Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;
    .locals 21
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LDy0/d;",
            ">;",
            "Ljava/util/List<",
            "LWn/a;",
            ">;",
            "Ljava/util/List<",
            "LDP/a;",
            ">;)",
            "Ljava/util/List<",
            "LDy0/d;",
            ">;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    new-instance v1, Ljava/util/ArrayList;

    .line 4
    .line 5
    const/16 v2, 0xa

    .line 6
    .line 7
    move-object/from16 v3, p1

    .line 8
    .line 9
    invoke-static {v3, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 14
    .line 15
    .line 16
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 21
    .line 22
    .line 23
    move-result v3

    .line 24
    if-eqz v3, :cond_0

    .line 25
    .line 26
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v3

    .line 30
    move-object v4, v3

    .line 31
    check-cast v4, LDy0/d;

    .line 32
    .line 33
    invoke-virtual {v4}, LDy0/d;->c()LDy0/b;

    .line 34
    .line 35
    .line 36
    move-result-object v5

    .line 37
    invoke-virtual {v4}, LDy0/d;->c()LDy0/b;

    .line 38
    .line 39
    .line 40
    move-result-object v3

    .line 41
    move-object/from16 v6, p2

    .line 42
    .line 43
    invoke-virtual {v0, v6, v3}, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->b(Ljava/util/List;LDy0/b;)Z

    .line 44
    .line 45
    .line 46
    move-result v17

    .line 47
    invoke-virtual {v4}, LDy0/d;->c()LDy0/b;

    .line 48
    .line 49
    .line 50
    move-result-object v3

    .line 51
    move-object/from16 v7, p3

    .line 52
    .line 53
    invoke-virtual {v0, v7, v3}, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->c(Ljava/util/List;LDy0/b;)Z

    .line 54
    .line 55
    .line 56
    move-result v18

    .line 57
    const/16 v19, 0x1ff

    .line 58
    .line 59
    const/16 v20, 0x0

    .line 60
    .line 61
    const/4 v6, 0x0

    .line 62
    const/4 v7, 0x0

    .line 63
    const-wide/16 v8, 0x0

    .line 64
    .line 65
    const/4 v10, 0x0

    .line 66
    const-wide/16 v11, 0x0

    .line 67
    .line 68
    const/4 v13, 0x0

    .line 69
    const/4 v14, 0x0

    .line 70
    const/4 v15, 0x0

    .line 71
    const/16 v16, 0x0

    .line 72
    .line 73
    invoke-static/range {v5 .. v20}, LDy0/b;->b(LDy0/b;IIDLDy0/b$a;DLjava/lang/Double;Ljava/lang/String;ZIZZILjava/lang/Object;)LDy0/b;

    .line 74
    .line 75
    .line 76
    move-result-object v8

    .line 77
    const/4 v9, 0x7

    .line 78
    const/4 v5, 0x0

    .line 79
    const/4 v6, 0x0

    .line 80
    const/4 v7, 0x0

    .line 81
    invoke-static/range {v4 .. v10}, LDy0/d;->b(LDy0/d;ILjava/lang/String;Ljava/lang/String;LDy0/b;ILjava/lang/Object;)LDy0/d;

    .line 82
    .line 83
    .line 84
    move-result-object v3

    .line 85
    invoke-interface {v1, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 86
    .line 87
    .line 88
    goto :goto_0

    .line 89
    :cond_0
    return-object v1
.end method
