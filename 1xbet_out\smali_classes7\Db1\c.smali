.class public final LDb1/c;
.super Ljava/lang/Object;


# static fields
.field public static fragment_aggregator_popular_classic:I = 0x7f0d0332

.field public static item_classic_popular_category_card:I = 0x7f0d05a9

.field public static item_classic_popular_category_container:I = 0x7f0d05aa

.field public static item_popular_category:I = 0x7f0d0653

.field public static item_popular_classic_aggregator_banner:I = 0x7f0d0656

.field public static item_popular_classic_category_game:I = 0x7f0d0657

.field public static item_popular_classic_game:I = 0x7f0d0658

.field public static item_popular_classic_selection_container:I = 0x7f0d0659

.field public static item_popular_classic_shimmer_promo:I = 0x7f0d065a

.field public static item_popular_classic_shimmer_provider:I = 0x7f0d065b

.field public static item_promo_game:I = 0x7f0d0672

.field public static item_promo_games_container:I = 0x7f0d0673


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
