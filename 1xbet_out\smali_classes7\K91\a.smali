.class public final LK91/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0002\u0008\u0003\u001am\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u000f0\u000e2\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u00022\u0008\u0010\u0005\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\u00022\u0006\u0010\n\u001a\u00020\u00022\u0006\u0010\u000b\u001a\u00020\u00022\u0006\u0010\r\u001a\u00020\u000cH\u0000\u00a2\u0006\u0004\u0008\u0010\u0010\u0011\u00a8\u0006\u0012"
    }
    d2 = {
        "",
        "categoryId",
        "",
        "whence",
        "countryIdBlocking",
        "countryId",
        "ref",
        "",
        "lang",
        "groupId",
        "limit",
        "skip",
        "",
        "test",
        "",
        "",
        "a",
        "(JIILjava/lang/Integer;ILjava/lang/String;IIIZ)Ljava/util/Map;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(JIILjava/lang/Integer;ILjava/lang/String;IIIZ)Ljava/util/Map;
    .locals 1
    .param p6    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JII",
            "Ljava/lang/Integer;",
            "I",
            "Ljava/lang/String;",
            "IIIZ)",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {p0, p1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-string p1, "categoryId"

    .line 6
    .line 7
    invoke-static {p1, p0}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    const-string p2, "whence"

    .line 16
    .line 17
    invoke-static {p2, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-static {p5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 22
    .line 23
    .line 24
    move-result-object p2

    .line 25
    const-string p5, "ref"

    .line 26
    .line 27
    invoke-static {p5, p2}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 28
    .line 29
    .line 30
    move-result-object p2

    .line 31
    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 32
    .line 33
    .line 34
    move-result-object p3

    .line 35
    const-string p5, "fcountry"

    .line 36
    .line 37
    invoke-static {p5, p3}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 38
    .line 39
    .line 40
    move-result-object p3

    .line 41
    const-string p5, "lng"

    .line 42
    .line 43
    invoke-static {p5, p6}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 44
    .line 45
    .line 46
    move-result-object p5

    .line 47
    invoke-static {p7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 48
    .line 49
    .line 50
    move-result-object p6

    .line 51
    const-string p7, "gr"

    .line 52
    .line 53
    invoke-static {p7, p6}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 54
    .line 55
    .line 56
    move-result-object p6

    .line 57
    invoke-static {p8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 58
    .line 59
    .line 60
    move-result-object p7

    .line 61
    const-string p8, "limit"

    .line 62
    .line 63
    invoke-static {p8, p7}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 64
    .line 65
    .line 66
    move-result-object p7

    .line 67
    invoke-static {p9}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 68
    .line 69
    .line 70
    move-result-object p8

    .line 71
    const-string p9, "skip"

    .line 72
    .line 73
    invoke-static {p9, p8}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 74
    .line 75
    .line 76
    move-result-object p8

    .line 77
    const/16 p9, 0x8

    .line 78
    .line 79
    new-array p9, p9, [Lkotlin/Pair;

    .line 80
    .line 81
    const/4 v0, 0x0

    .line 82
    aput-object p0, p9, v0

    .line 83
    .line 84
    const/4 p0, 0x1

    .line 85
    aput-object p1, p9, p0

    .line 86
    .line 87
    const/4 p0, 0x2

    .line 88
    aput-object p2, p9, p0

    .line 89
    .line 90
    const/4 p0, 0x3

    .line 91
    aput-object p3, p9, p0

    .line 92
    .line 93
    const/4 p0, 0x4

    .line 94
    aput-object p5, p9, p0

    .line 95
    .line 96
    const/4 p0, 0x5

    .line 97
    aput-object p6, p9, p0

    .line 98
    .line 99
    const/4 p0, 0x6

    .line 100
    aput-object p7, p9, p0

    .line 101
    .line 102
    const/4 p0, 0x7

    .line 103
    aput-object p8, p9, p0

    .line 104
    .line 105
    invoke-static {p9}, Lkotlin/collections/Q;->o([Lkotlin/Pair;)Ljava/util/Map;

    .line 106
    .line 107
    .line 108
    move-result-object p0

    .line 109
    if-eqz p4, :cond_0

    .line 110
    .line 111
    const-string p1, "country"

    .line 112
    .line 113
    invoke-interface {p0, p1, p4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    :cond_0
    if-eqz p10, :cond_1

    .line 117
    .line 118
    invoke-static {p10}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    const-string p2, "test"

    .line 123
    .line 124
    invoke-interface {p0, p2, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 125
    .line 126
    .line 127
    :cond_1
    return-object p0
.end method
