.class public final synthetic LU01/t;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:Landroid/graphics/drawable/Drawable;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;Landroid/graphics/drawable/Drawable;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LU01/t;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, LU01/t;->b:Landroid/graphics/drawable/Drawable;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, LU01/t;->a:Lkotlin/jvm/functions/Function1;

    iget-object v1, p0, LU01/t;->b:Landroid/graphics/drawable/Drawable;

    invoke-static {v0, v1}, Lorg/xbet/uikit/components/views/LoadableImageView;->x(Lkotlin/jvm/functions/Function1;Landroid/graphics/drawable/Drawable;)V

    return-void
.end method
