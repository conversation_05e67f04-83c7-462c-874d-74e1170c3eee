.class public final synthetic Leb1/H;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lhb1/a;

.field public final synthetic b:LB4/a;


# direct methods
.method public synthetic constructor <init>(Lhb1/a;LB4/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Leb1/H;->a:Lhb1/a;

    iput-object p2, p0, Leb1/H;->b:LB4/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Leb1/H;->a:Lhb1/a;

    iget-object v1, p0, Leb1/H;->b:LB4/a;

    check-cast p1, Ljava/util/List;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentStagesDelegateKt;->a(Lhb1/a;LB4/a;Ljava/util/List;)L<PERSON>lin/Unit;

    move-result-object p1

    return-object p1
.end method
