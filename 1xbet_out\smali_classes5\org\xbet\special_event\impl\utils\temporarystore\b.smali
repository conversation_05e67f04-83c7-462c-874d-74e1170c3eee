.class public final synthetic Lorg/xbet/special_event/impl/utils/temporarystore/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lm8/a;


# direct methods
.method public synthetic constructor <init>(Lm8/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/special_event/impl/utils/temporarystore/b;->a:Lm8/a;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/utils/temporarystore/b;->a:Lm8/a;

    invoke-static {v0}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->b(Lm8/a;)Lkotlinx/coroutines/N;

    move-result-object v0

    return-object v0
.end method
