.class public final Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0015\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$a;",
        "",
        "<init>",
        "()V",
        "Lorg/xbet/uikit/components/dialog/DialogFields;",
        "dialogFields",
        "Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;",
        "a",
        "(Lorg/xbet/uikit/components/dialog/DialogFields;)Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lorg/xbet/uikit/components/dialog/DialogFields;)Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;
    .locals 1
    .param p1    # Lorg/xbet/uikit/components/dialog/DialogFields;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->O2(Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;Lorg/xbet/uikit/components/dialog/DialogFields;)V

    .line 7
    .line 8
    .line 9
    return-object v0
.end method
