.class public final LOJ0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0015\u0010\u0002\u001a\u00020\u0001*\u0004\u0018\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LIJ0/e;",
        "LaZ0/c;",
        "a",
        "(LIJ0/e;)LaZ0/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LIJ0/e;)LaZ0/c;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LaZ0/c;

    .line 2
    .line 3
    if-eqz p0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, LIJ0/e;->b()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 p0, 0x0

    .line 11
    :goto_0
    if-nez p0, :cond_1

    .line 12
    .line 13
    const-string p0, ""

    .line 14
    .line 15
    :cond_1
    new-instance v1, LaZ0/d$b;

    .line 16
    .line 17
    sget v2, Lpb/f;->size_100:I

    .line 18
    .line 19
    invoke-direct {v1, v2}, LaZ0/d$b;-><init>(I)V

    .line 20
    .line 21
    .line 22
    sget-object v2, Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;->START:Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;

    .line 23
    .line 24
    invoke-direct {v0, p0, v1, v2}, LaZ0/c;-><init>(Ljava/lang/String;LaZ0/d;Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;)V

    .line 25
    .line 26
    .line 27
    return-object v0
.end method
