.class public final Ll71/a;
.super Ljava/lang/Object;


# static fields
.field public static buttonRefresh:I = 0x7f0a033a

.field public static cellLeftIcon:I = 0x7f0a03bc

.field public static container:I = 0x7f0a0561

.field public static containerGameStatus:I = 0x7f0a0569

.field public static containerImagesTeamOne:I = 0x7f0a056b

.field public static containerImagesTeamTwo:I = 0x7f0a056c

.field public static divider:I = 0x7f0a0674

.field public static gameContainer:I = 0x7f0a0917

.field public static gridQuick:I = 0x7f0a0990

.field public static header:I = 0x7f0a0a67

.field public static imageSection:I = 0x7f0a0af0

.field public static imageViewEmptyNotification:I = 0x7f0a0afb

.field public static imageViewError:I = 0x7f0a0afc

.field public static imageViewFour:I = 0x7f0a0afe

.field public static imageViewIcon:I = 0x7f0a0b00

.field public static imageViewLive:I = 0x7f0a0b01

.field public static imageViewLogo:I = 0x7f0a0b02

.field public static imageViewOne:I = 0x7f0a0b04

.field public static imageViewProphylaxis:I = 0x7f0a0b08

.field public static imageViewTeamOne:I = 0x7f0a0b0e

.field public static imageViewTeamOneFirst:I = 0x7f0a0b0f

.field public static imageViewTeamOneQuarter:I = 0x7f0a0b10

.field public static imageViewTeamOneSecond:I = 0x7f0a0b11

.field public static imageViewTeamOneThird:I = 0x7f0a0b12

.field public static imageViewTeamTwo:I = 0x7f0a0b13

.field public static imageViewTeamTwoFirst:I = 0x7f0a0b14

.field public static imageViewTeamTwoQuarter:I = 0x7f0a0b15

.field public static imageViewTeamTwoSecond:I = 0x7f0a0b16

.field public static imageViewTeamTwoThird:I = 0x7f0a0b17

.field public static imageViewThree:I = 0x7f0a0b19

.field public static imageViewThreeDouble:I = 0x7f0a0b1a

.field public static imageViewTwo:I = 0x7f0a0b1b

.field public static innerContainer:I = 0x7f0a0ba7

.field public static labelImage:I = 0x7f0a0dc7

.field public static layoutError:I = 0x7f0a0dda

.field public static layoutProphylaxis:I = 0x7f0a0ddb

.field public static listGames:I = 0x7f0a0e4a

.field public static mainContainer:I = 0x7f0a0efd

.field public static matchContainer:I = 0x7f0a0f25

.field public static navigationBar:I = 0x7f0a0faf

.field public static progressBar:I = 0x7f0a111f

.field public static recyclerCategoryChoice:I = 0x7f0a119d

.field public static renewContainer:I = 0x7f0a11e3

.field public static renewFlip:I = 0x7f0a11e4

.field public static renewImage:I = 0x7f0a11e5

.field public static renewImageStatic:I = 0x7f0a11e6

.field public static renewTime:I = 0x7f0a11e7

.field public static scSection:I = 0x7f0a12ec

.field public static section:I = 0x7f0a1394

.field public static sectionFour:I = 0x7f0a1395

.field public static sectionOne:I = 0x7f0a1396

.field public static sectionThree:I = 0x7f0a1397

.field public static sectionTwo:I = 0x7f0a1398

.field public static settings:I = 0x7f0a13e1

.field public static tabFour:I = 0x7f0a1673

.field public static tabOne:I = 0x7f0a167c

.field public static tabThree:I = 0x7f0a167d

.field public static tabThreeDouble:I = 0x7f0a167e

.field public static tabTwo:I = 0x7f0a1680

.field public static textViewDate:I = 0x7f0a1738

.field public static textViewError:I = 0x7f0a173e

.field public static textViewFour:I = 0x7f0a173f

.field public static textViewNotification:I = 0x7f0a1743

.field public static textViewOne:I = 0x7f0a1744

.field public static textViewPoints:I = 0x7f0a1746

.field public static textViewProphylaxis:I = 0x7f0a1747

.field public static textViewTeamOne:I = 0x7f0a174c

.field public static textViewTeamTwo:I = 0x7f0a174d

.field public static textViewThree:I = 0x7f0a174e

.field public static textViewThreeDouble:I = 0x7f0a174f

.field public static textViewTitle:I = 0x7f0a1751

.field public static textViewTwo:I = 0x7f0a1753

.field public static title:I = 0x7f0a1808

.field public static titlePreview:I = 0x7f0a1818

.field public static titleSection:I = 0x7f0a181a

.field public static widgetItemContainer:I = 0x7f0a1fed

.field public static widgetPreviewLayout:I = 0x7f0a1fee


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
