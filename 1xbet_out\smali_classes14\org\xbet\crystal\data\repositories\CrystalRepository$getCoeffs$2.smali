.class final Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.crystal.data.repositories.CrystalRepository$getCoeffs$2"
    f = "CrystalRepository.kt"
    l = {
        0x22
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/String;",
        "Lkotlin/coroutines/e<",
        "-",
        "LZx/a;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "",
        "token",
        "LZx/a;",
        "<anonymous>",
        "(Ljava/lang/String;)LZx/a;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/crystal/data/repositories/CrystalRepository;


# direct methods
.method public constructor <init>(Lorg/xbet/crystal/data/repositories/CrystalRepository;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/crystal/data/repositories/CrystalRepository;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;->this$0:Lorg/xbet/crystal/data/repositories/CrystalRepository;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;

    iget-object v1, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;->this$0:Lorg/xbet/crystal/data/repositories/CrystalRepository;

    invoke-direct {v0, v1, p2}, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;-><init>(Lorg/xbet/crystal/data/repositories/CrystalRepository;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;->invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "LZx/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;->L$0:Ljava/lang/Object;

    .line 13
    .line 14
    check-cast v0, LUx/a;

    .line 15
    .line 16
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;->L$0:Ljava/lang/Object;

    .line 32
    .line 33
    check-cast p1, Ljava/lang/String;

    .line 34
    .line 35
    iget-object v1, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;->this$0:Lorg/xbet/crystal/data/repositories/CrystalRepository;

    .line 36
    .line 37
    invoke-static {v1}, Lorg/xbet/crystal/data/repositories/CrystalRepository;->a(Lorg/xbet/crystal/data/repositories/CrystalRepository;)LUx/a;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    iget-object v3, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;->this$0:Lorg/xbet/crystal/data/repositories/CrystalRepository;

    .line 42
    .line 43
    invoke-static {v3}, Lorg/xbet/crystal/data/repositories/CrystalRepository;->d(Lorg/xbet/crystal/data/repositories/CrystalRepository;)Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;

    .line 44
    .line 45
    .line 46
    move-result-object v3

    .line 47
    iput-object v1, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;->L$0:Ljava/lang/Object;

    .line 48
    .line 49
    iput v2, p0, Lorg/xbet/crystal/data/repositories/CrystalRepository$getCoeffs$2;->label:I

    .line 50
    .line 51
    invoke-virtual {v3, p1, p0}, Lorg/xbet/crystal/data/datasources/CrystalRemoteDataSource;->c(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    if-ne p1, v0, :cond_2

    .line 56
    .line 57
    return-object v0

    .line 58
    :cond_2
    move-object v0, v1

    .line 59
    :goto_0
    check-cast p1, Ljava/util/Map;

    .line 60
    .line 61
    invoke-virtual {v0, p1}, LUx/a;->a(Ljava/util/Map;)LZx/a;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    return-object p1
.end method
