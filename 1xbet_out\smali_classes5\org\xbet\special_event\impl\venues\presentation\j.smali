.class public final Lorg/xbet/special_event/impl/venues/presentation/j;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LIu0/b;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LWo0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHg/d;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LfS/a;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Ljava/lang/Integer;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "LIu0/b;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "LSX0/a;",
            ">;",
            "LBc/a<",
            "LWo0/a;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "LHg/d;",
            ">;",
            "LBc/a<",
            "LfS/a;",
            ">;",
            "LBc/a<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->j:LBc/a;

    .line 23
    .line 24
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/special_event/impl/venues/presentation/j;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Ljava/lang/Integer;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "LIu0/b;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "LSX0/a;",
            ">;",
            "LBc/a<",
            "LWo0/a;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "LHg/d;",
            ">;",
            "LBc/a<",
            "LfS/a;",
            ">;",
            "LBc/a<",
            "Ljava/lang/String;",
            ">;)",
            "Lorg/xbet/special_event/impl/venues/presentation/j;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/venues/presentation/j;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    move-object/from16 v6, p5

    .line 9
    .line 10
    move-object/from16 v7, p6

    .line 11
    .line 12
    move-object/from16 v8, p7

    .line 13
    .line 14
    move-object/from16 v9, p8

    .line 15
    .line 16
    move-object/from16 v10, p9

    .line 17
    .line 18
    invoke-direct/range {v0 .. v10}, Lorg/xbet/special_event/impl/venues/presentation/j;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 19
    .line 20
    .line 21
    return-object v0
.end method

.method public static c(Landroidx/lifecycle/Q;ILHX0/e;LIu0/b;Lm8/a;LSX0/a;LWo0/a;LwX0/c;LHg/d;LfS/a;Ljava/lang/String;)Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;
    .locals 12

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object/from16 v5, p4

    .line 8
    .line 9
    move-object/from16 v6, p5

    .line 10
    .line 11
    move-object/from16 v7, p6

    .line 12
    .line 13
    move-object/from16 v8, p7

    .line 14
    .line 15
    move-object/from16 v9, p8

    .line 16
    .line 17
    move-object/from16 v10, p9

    .line 18
    .line 19
    move-object/from16 v11, p10

    .line 20
    .line 21
    invoke-direct/range {v0 .. v11}, Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;-><init>(Landroidx/lifecycle/Q;ILHX0/e;LIu0/b;Lm8/a;LSX0/a;LWo0/a;LwX0/c;LHg/d;LfS/a;Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    return-object v0
.end method


# virtual methods
.method public b(Landroidx/lifecycle/Q;)Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;
    .locals 12

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/lang/Integer;

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->b:LBc/a;

    .line 14
    .line 15
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    move-object v3, v0

    .line 20
    check-cast v3, LHX0/e;

    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->c:LBc/a;

    .line 23
    .line 24
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    move-object v4, v0

    .line 29
    check-cast v4, LIu0/b;

    .line 30
    .line 31
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->d:LBc/a;

    .line 32
    .line 33
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    move-object v5, v0

    .line 38
    check-cast v5, Lm8/a;

    .line 39
    .line 40
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->e:LBc/a;

    .line 41
    .line 42
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    move-object v6, v0

    .line 47
    check-cast v6, LSX0/a;

    .line 48
    .line 49
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->f:LBc/a;

    .line 50
    .line 51
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    move-object v7, v0

    .line 56
    check-cast v7, LWo0/a;

    .line 57
    .line 58
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->g:LBc/a;

    .line 59
    .line 60
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    move-object v8, v0

    .line 65
    check-cast v8, LwX0/c;

    .line 66
    .line 67
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->h:LBc/a;

    .line 68
    .line 69
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    move-object v9, v0

    .line 74
    check-cast v9, LHg/d;

    .line 75
    .line 76
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->i:LBc/a;

    .line 77
    .line 78
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    move-result-object v0

    .line 82
    move-object v10, v0

    .line 83
    check-cast v10, LfS/a;

    .line 84
    .line 85
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/j;->j:LBc/a;

    .line 86
    .line 87
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    move-object v11, v0

    .line 92
    check-cast v11, Ljava/lang/String;

    .line 93
    .line 94
    move-object v1, p1

    .line 95
    invoke-static/range {v1 .. v11}, Lorg/xbet/special_event/impl/venues/presentation/j;->c(Landroidx/lifecycle/Q;ILHX0/e;LIu0/b;Lm8/a;LSX0/a;LWo0/a;LwX0/c;LHg/d;LfS/a;Ljava/lang/String;)Lorg/xbet/special_event/impl/venues/presentation/VenuesViewModel;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    return-object p1
.end method
