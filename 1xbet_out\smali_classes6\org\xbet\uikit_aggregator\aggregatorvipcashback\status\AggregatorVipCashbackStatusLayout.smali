.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"

# interfaces
.implements Lg31/j;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Lg31/j<",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000d\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0001\u0018\u00002\u00020\u00012\u0008\u0012\u0004\u0012\u00020\u00030\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\n\u0008\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u0012\u0008\u0008\u0003\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0017\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000c\u001a\u00020\u0003H\u0016\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u000f\u0010\u0011\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u000f\u0010\u0014\u001a\u00020\u0013H\u0016\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u000f\u0010\u0016\u001a\u00020\u0013H\u0016\u00a2\u0006\u0004\u0008\u0016\u0010\u0015J\u0015\u0010\u0019\u001a\u00020\r2\u0006\u0010\u0018\u001a\u00020\u0017\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u0015\u0010\u001b\u001a\u00020\r2\u0006\u0010\u0018\u001a\u00020\u0017\u00a2\u0006\u0004\u0008\u001b\u0010\u001aJ\u0015\u0010\u001d\u001a\u00020\r2\u0006\u0010\u001c\u001a\u00020\u0017\u00a2\u0006\u0004\u0008\u001d\u0010\u001aJ\u0015\u0010 \u001a\u00020\r2\u0006\u0010\u001f\u001a\u00020\u001e\u00a2\u0006\u0004\u0008 \u0010!R\u0014\u0010$\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010#R\u0014\u0010(\u001a\u00020%8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R*\u00101\u001a\u00020)2\u0006\u0010*\u001a\u00020)8\u0016@VX\u0096\u000e\u00a2\u0006\u0012\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.\"\u0004\u0008/\u00100R*\u00105\u001a\u00020)2\u0006\u0010*\u001a\u00020)8\u0016@VX\u0096\u000e\u00a2\u0006\u0012\n\u0004\u00082\u0010,\u001a\u0004\u00083\u0010.\"\u0004\u00084\u00100R\u0014\u00109\u001a\u0002068VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u00087\u00108\u00a8\u0006:"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Lg31/j;",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "state",
        "",
        "setState",
        "(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;)V",
        "Landroid/widget/ProgressBar;",
        "getProgressBar",
        "()Landroid/widget/ProgressBar;",
        "Landroid/widget/TextView;",
        "getCurrentProgressTextView",
        "()Landroid/widget/TextView;",
        "getMaxProgressTextView",
        "",
        "text",
        "setTitle",
        "(Ljava/lang/String;)V",
        "setCaption",
        "cashback",
        "setCashback",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;",
        "level",
        "setStatus",
        "(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;)V",
        "a",
        "I",
        "space16",
        "Ll31/L;",
        "b",
        "Ll31/L;",
        "binding",
        "",
        "value",
        "c",
        "J",
        "getProgress",
        "()J",
        "setProgress",
        "(J)V",
        "progress",
        "d",
        "getMaxProgress",
        "setMaxProgress",
        "maxProgress",
        "Landroid/view/View;",
        "getView",
        "()Landroid/view/View;",
        "view",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:I

.field public final b:Ll31/L;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:J

.field public d:J


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_16:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->a:I

    .line 6
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p3

    invoke-static {p3, p0}, Ll31/L;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Ll31/L;

    move-result-object p3

    iput-object p3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->b:Ll31/L;

    const/4 p3, 0x1

    .line 7
    invoke-virtual {p0, p3}, Landroid/view/View;->setClipToOutline(Z)V

    .line 8
    sget p3, LlZ0/h;->rounded_background_16_content:I

    invoke-static {p1, p3}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 9
    new-instance p1, Landroid/widget/FrameLayout$LayoutParams;

    const/4 p3, -0x1

    const/4 v0, -0x2

    invoke-direct {p1, p3, v0}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 10
    invoke-virtual {p0, p2, p2, p2, p2}, Landroid/view/View;->setPadding(IIII)V

    .line 11
    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public getCurrentProgressTextView()Landroid/widget/TextView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->b:Ll31/L;

    .line 2
    .line 3
    iget-object v0, v0, Ll31/L;->d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;

    .line 4
    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->getCurrentProgressTextView()Landroid/widget/TextView;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public getMaxProgress()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->d:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public getMaxProgressTextView()Landroid/widget/TextView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->b:Ll31/L;

    .line 2
    .line 3
    iget-object v0, v0, Ll31/L;->d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;

    .line 4
    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->getMaxProgressTextView()Landroid/widget/TextView;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public getProgress()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->c:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public getProgressBar()Landroid/widget/ProgressBar;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->b:Ll31/L;

    .line 2
    .line 3
    iget-object v0, v0, Ll31/L;->d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;

    .line 4
    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->getProgressBar()Landroid/widget/ProgressBar;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public getView()Landroid/view/View;
    .locals 0
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    return-object p0
.end method

.method public final setCaption(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->b:Ll31/L;

    .line 2
    .line 3
    iget-object v0, v0, Ll31/L;->f:Landroid/widget/TextView;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setCashback(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->b:Ll31/L;

    .line 2
    .line 3
    iget-object v0, v0, Ll31/L;->e:Lorg/xbet/uikit/components/tag/Tag;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public setMaxProgress(J)V
    .locals 1

    .line 1
    iput-wide p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->d:J

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->b:Ll31/L;

    .line 4
    .line 5
    iget-object v0, v0, Ll31/L;->d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;

    .line 6
    .line 7
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->setMaxProgress(J)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public setProgress(J)V
    .locals 1

    .line 1
    iput-wide p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->c:J

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->b:Ll31/L;

    .line 4
    .line 5
    iget-object v0, v0, Ll31/L;->d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;

    .line 6
    .line 7
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->setProgress(J)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public setState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;)V
    .locals 3
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->b:Ll31/L;

    .line 3
    iget-object v1, v0, Ll31/L;->g:Landroid/widget/TextView;

    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->f()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    iget-object v1, v0, Ll31/L;->f:Landroid/widget/TextView;

    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->a()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 5
    iget-object v0, v0, Ll31/L;->e:Lorg/xbet/uikit/components/tag/Tag;

    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->b()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 6
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->e()J

    move-result-wide v0

    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->setProgress(J)V

    .line 7
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->d()J

    move-result-wide v0

    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->setMaxProgress(J)V

    .line 8
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;->c()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->setStatus(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;)V

    return-void
.end method

.method public bridge synthetic setState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c;)V
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->setState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;)V

    return-void
.end method

.method public final setStatus(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;)V
    .locals 2
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lg31/i;->a:Lg31/i;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->b:Ll31/L;

    .line 4
    .line 5
    iget-object v1, v1, Ll31/L;->c:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 6
    .line 7
    invoke-virtual {v0, v1, p1}, Lg31/i;->a(Landroid/widget/ImageView;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final setTitle(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->b:Ll31/L;

    .line 2
    .line 3
    iget-object v0, v0, Ll31/L;->g:Landroid/widget/TextView;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method
