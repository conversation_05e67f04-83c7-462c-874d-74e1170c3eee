.class public final LDc1/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LDc1/g$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LDc1/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LDc1/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LDc1/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LQW0/c;Lmo/f;LKg/a;LAi0/a;LVp/a;Lak/a;Lc81/a;LlV/a;LHX/a;LQl0/a;Lorg/xbet/feed/subscriptions/domain/usecases/c;Lw30/e;Lorg/xplatform/aggregator/api/domain/a;Lorg/xbet/consultantchat/domain/usecases/y0;Lorg/xbet/analytics/domain/b;Lcom/xbet/onexcore/domain/usecase/a;Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;LJT/d;LHt/a;Leu/a;Lv81/e;LVT/g;Ltk0/b;Ltk0/a;LXa0/c;Lnl/q;LD81/a;LHn0/a;Lo9/a;LX8/a;Llc1/b;Lxg/h;Lx5/a;Lcom/xbet/onexuser/data/profile/b;Ly5/a;)LDc1/g;
    .locals 37

    .line 1
    invoke-static/range {p1 .. p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static/range {p2 .. p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    invoke-static/range {p10 .. p10}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    invoke-static/range {p12 .. p12}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    invoke-static/range {p13 .. p13}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    invoke-static/range {p14 .. p14}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 41
    .line 42
    .line 43
    invoke-static/range {p15 .. p15}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    invoke-static/range {p16 .. p16}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 47
    .line 48
    .line 49
    invoke-static/range {p17 .. p17}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    invoke-static/range {p18 .. p18}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 53
    .line 54
    .line 55
    invoke-static/range {p19 .. p19}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    invoke-static/range {p20 .. p20}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    invoke-static/range {p21 .. p21}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    invoke-static/range {p22 .. p22}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    invoke-static/range {p23 .. p23}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    invoke-static/range {p24 .. p24}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    invoke-static/range {p25 .. p25}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    invoke-static/range {p26 .. p26}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    invoke-static/range {p27 .. p27}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    invoke-static/range {p28 .. p28}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 83
    .line 84
    .line 85
    invoke-static/range {p29 .. p29}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    invoke-static/range {p30 .. p30}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 89
    .line 90
    .line 91
    invoke-static/range {p31 .. p31}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 92
    .line 93
    .line 94
    invoke-static/range {p32 .. p32}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 95
    .line 96
    .line 97
    invoke-static/range {p33 .. p33}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 98
    .line 99
    .line 100
    invoke-static/range {p34 .. p34}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 101
    .line 102
    .line 103
    invoke-static/range {p35 .. p35}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 104
    .line 105
    .line 106
    new-instance v0, LDc1/a$b;

    .line 107
    .line 108
    const/16 v36, 0x0

    .line 109
    .line 110
    move-object/from16 v1, p1

    .line 111
    .line 112
    move-object/from16 v2, p2

    .line 113
    .line 114
    move-object/from16 v3, p3

    .line 115
    .line 116
    move-object/from16 v4, p4

    .line 117
    .line 118
    move-object/from16 v6, p5

    .line 119
    .line 120
    move-object/from16 v7, p6

    .line 121
    .line 122
    move-object/from16 v8, p7

    .line 123
    .line 124
    move-object/from16 v9, p8

    .line 125
    .line 126
    move-object/from16 v5, p9

    .line 127
    .line 128
    move-object/from16 v10, p10

    .line 129
    .line 130
    move-object/from16 v11, p11

    .line 131
    .line 132
    move-object/from16 v12, p12

    .line 133
    .line 134
    move-object/from16 v13, p13

    .line 135
    .line 136
    move-object/from16 v14, p14

    .line 137
    .line 138
    move-object/from16 v15, p15

    .line 139
    .line 140
    move-object/from16 v16, p16

    .line 141
    .line 142
    move-object/from16 v17, p17

    .line 143
    .line 144
    move-object/from16 v18, p18

    .line 145
    .line 146
    move-object/from16 v19, p19

    .line 147
    .line 148
    move-object/from16 v20, p20

    .line 149
    .line 150
    move-object/from16 v21, p21

    .line 151
    .line 152
    move-object/from16 v22, p22

    .line 153
    .line 154
    move-object/from16 v23, p23

    .line 155
    .line 156
    move-object/from16 v24, p24

    .line 157
    .line 158
    move-object/from16 v25, p25

    .line 159
    .line 160
    move-object/from16 v26, p26

    .line 161
    .line 162
    move-object/from16 v27, p27

    .line 163
    .line 164
    move-object/from16 v28, p28

    .line 165
    .line 166
    move-object/from16 v29, p29

    .line 167
    .line 168
    move-object/from16 v30, p30

    .line 169
    .line 170
    move-object/from16 v31, p31

    .line 171
    .line 172
    move-object/from16 v32, p32

    .line 173
    .line 174
    move-object/from16 v33, p33

    .line 175
    .line 176
    move-object/from16 v34, p34

    .line 177
    .line 178
    move-object/from16 v35, p35

    .line 179
    .line 180
    invoke-direct/range {v0 .. v36}, LDc1/a$b;-><init>(LQW0/c;Lmo/f;LKg/a;LAi0/a;LHX/a;LVp/a;Lak/a;Lc81/a;LlV/a;LQl0/a;Lorg/xbet/feed/subscriptions/domain/usecases/c;Lw30/e;Lorg/xplatform/aggregator/api/domain/a;Lorg/xbet/consultantchat/domain/usecases/y0;Lorg/xbet/analytics/domain/b;Lcom/xbet/onexcore/domain/usecase/a;Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;LJT/d;LHt/a;Leu/a;Lv81/e;LVT/g;Ltk0/b;Ltk0/a;LXa0/c;Lnl/q;LD81/a;LHn0/a;Lo9/a;LX8/a;Llc1/b;Lxg/h;Lx5/a;Lcom/xbet/onexuser/data/profile/b;Ly5/a;LDc1/b;)V

    .line 181
    .line 182
    .line 183
    return-object v0
.end method
