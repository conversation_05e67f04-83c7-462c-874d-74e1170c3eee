.class public final Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J \u0010\r\u001a\u00020\u000c2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\nH\u0086B\u00a2\u0006\u0004\u0008\r\u0010\u000eJ \u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u0008H\u0082@\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J(\u0010\u0015\u001a\u00020\u00122\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\nH\u0082@\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J \u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u0008H\u0082@\u00a2\u0006\u0004\u0008\u0018\u0010\u0014R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u001aR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010\u001c\u00a8\u0006\u001d"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;",
        "",
        "Lya1/a;",
        "promoRepository",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "tokenRefresher",
        "<init>",
        "(Lya1/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)V",
        "",
        "currentAccountId",
        "",
        "onlyActive",
        "Lg81/h;",
        "h",
        "(JZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "token",
        "accountId",
        "Lg81/e;",
        "f",
        "(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;",
        "g",
        "(Ljava/lang/String;JZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lg81/a;",
        "e",
        "a",
        "Lya1/a;",
        "b",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lya1/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lya1/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)V
    .locals 0
    .param p1    # Lya1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lcom/xbet/onexuser/domain/managers/TokenRefresher;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;->a:Lya1/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;->b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 7
    .line 8
    return-void
.end method

.method public static final synthetic a(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;->e(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic b(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;->f(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic c(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;Ljava/lang/String;JZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p5}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;->g(Ljava/lang/String;JZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic d(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;)Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;->b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final e(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lg81/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;->a:Lya1/a;

    .line 2
    .line 3
    invoke-interface {v0, p1, p2, p3, p4}, Lya1/a;->e(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public final f(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lg81/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;->a:Lya1/a;

    .line 2
    .line 3
    invoke-interface {v0, p1, p2, p3, p4}, Lya1/a;->m(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public final g(Ljava/lang/String;JZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "JZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Lg81/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;->a:Lya1/a;

    .line 2
    .line 3
    move-object v1, p1

    .line 4
    move-wide v2, p2

    .line 5
    move v4, p4

    .line 6
    move-object v5, p5

    .line 7
    invoke-interface/range {v0 .. v5}, Lya1/a;->b(Ljava/lang/String;JZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    return-object p1
.end method

.method public final h(JZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 6
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Lg81/h;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;

    .line 2
    .line 3
    const/4 v5, 0x0

    .line 4
    move-object v1, p0

    .line 5
    move-wide v2, p1

    .line 6
    move v4, p3

    .line 7
    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;-><init>(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;JZLkotlin/coroutines/e;)V

    .line 8
    .line 9
    .line 10
    invoke-static {v0, p4}, Lkotlinx/coroutines/O;->f(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    return-object p1
.end method
