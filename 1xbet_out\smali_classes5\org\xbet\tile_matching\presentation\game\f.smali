.class public final Lorg/xbet/tile_matching/presentation/game/f;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/u;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LxX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lak/a;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/l;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/d;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/h;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/o;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LWv/b;",
            ">;"
        }
    .end annotation
.end field

.field public final l:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/d;",
            ">;"
        }
    .end annotation
.end field

.field public final m:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/a;",
            ">;"
        }
    .end annotation
.end field

.field public final n:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/a;",
            ">;"
        }
    .end annotation
.end field

.field public final o:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LVv/d;",
            ">;"
        }
    .end annotation
.end field

.field public final p:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/tile_matching/domain/usecases/c;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/u;",
            ">;",
            "LBc/a<",
            "LxX0/a;",
            ">;",
            "LBc/a<",
            "Lak/a;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/l;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/d;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/h;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/o;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
            ">;",
            "LBc/a<",
            "LWv/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/d;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/a;",
            ">;",
            "LBc/a<",
            "LVv/d;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/tile_matching/domain/usecases/c;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/f;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/tile_matching/presentation/game/f;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/tile_matching/presentation/game/f;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/tile_matching/presentation/game/f;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/tile_matching/presentation/game/f;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/tile_matching/presentation/game/f;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/tile_matching/presentation/game/f;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/tile_matching/presentation/game/f;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/tile_matching/presentation/game/f;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/tile_matching/presentation/game/f;->j:LBc/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/tile_matching/presentation/game/f;->k:LBc/a;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/tile_matching/presentation/game/f;->l:LBc/a;

    .line 27
    .line 28
    iput-object p13, p0, Lorg/xbet/tile_matching/presentation/game/f;->m:LBc/a;

    .line 29
    .line 30
    iput-object p14, p0, Lorg/xbet/tile_matching/presentation/game/f;->n:LBc/a;

    .line 31
    .line 32
    iput-object p15, p0, Lorg/xbet/tile_matching/presentation/game/f;->o:LBc/a;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/f;->p:LBc/a;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/f;->q:LBc/a;

    .line 41
    .line 42
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/tile_matching/presentation/game/f;
    .locals 18
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/u;",
            ">;",
            "LBc/a<",
            "LxX0/a;",
            ">;",
            "LBc/a<",
            "Lak/a;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/l;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/d;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/h;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/bet/o;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
            ">;",
            "LBc/a<",
            "LWv/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/d;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/game_state/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/a;",
            ">;",
            "LBc/a<",
            "LVv/d;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/tile_matching/domain/usecases/c;",
            ">;)",
            "Lorg/xbet/tile_matching/presentation/game/f;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/f;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    invoke-direct/range {v0 .. v17}, Lorg/xbet/tile_matching/presentation/game/f;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 38
    .line 39
    .line 40
    return-object v0
.end method

.method public static c(Lorg/xbet/core/domain/usecases/u;LwX0/c;LxX0/a;Lak/a;Lm8/a;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/bet/h;Lorg/xbet/core/domain/usecases/bet/o;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;LWv/b;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/a;Lorg/xbet/core/domain/usecases/balance/a;LVv/d;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;Lorg/xbet/tile_matching/domain/usecases/c;)Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;
    .locals 19

    .line 1
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    invoke-direct/range {v0 .. v18}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;-><init>(Lorg/xbet/core/domain/usecases/u;LwX0/c;LxX0/a;Lak/a;Lm8/a;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/bet/h;Lorg/xbet/core/domain/usecases/bet/o;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;LWv/b;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/a;Lorg/xbet/core/domain/usecases/balance/a;LVv/d;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;Lorg/xbet/tile_matching/domain/usecases/c;)V

    .line 40
    .line 41
    .line 42
    return-object v0
.end method


# virtual methods
.method public b(LwX0/c;)Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;
    .locals 20

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->a:LBc/a;

    .line 4
    .line 5
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    move-object v2, v1

    .line 10
    check-cast v2, Lorg/xbet/core/domain/usecases/u;

    .line 11
    .line 12
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->b:LBc/a;

    .line 13
    .line 14
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    move-object v4, v1

    .line 19
    check-cast v4, LxX0/a;

    .line 20
    .line 21
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->c:LBc/a;

    .line 22
    .line 23
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    move-object v5, v1

    .line 28
    check-cast v5, Lak/a;

    .line 29
    .line 30
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->d:LBc/a;

    .line 31
    .line 32
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    move-object v6, v1

    .line 37
    check-cast v6, Lm8/a;

    .line 38
    .line 39
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->e:LBc/a;

    .line 40
    .line 41
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    move-object v7, v1

    .line 46
    check-cast v7, Lorg/xbet/core/domain/usecases/game_state/l;

    .line 47
    .line 48
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->f:LBc/a;

    .line 49
    .line 50
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    move-object v8, v1

    .line 55
    check-cast v8, Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 56
    .line 57
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->g:LBc/a;

    .line 58
    .line 59
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    move-object v9, v1

    .line 64
    check-cast v9, Lorg/xbet/core/domain/usecases/bet/d;

    .line 65
    .line 66
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->h:LBc/a;

    .line 67
    .line 68
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    move-object v10, v1

    .line 73
    check-cast v10, Lorg/xbet/core/domain/usecases/bet/h;

    .line 74
    .line 75
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->i:LBc/a;

    .line 76
    .line 77
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    move-object v11, v1

    .line 82
    check-cast v11, Lorg/xbet/core/domain/usecases/bet/o;

    .line 83
    .line 84
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->j:LBc/a;

    .line 85
    .line 86
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    move-object v12, v1

    .line 91
    check-cast v12, Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;

    .line 92
    .line 93
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->k:LBc/a;

    .line 94
    .line 95
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object v1

    .line 99
    move-object v13, v1

    .line 100
    check-cast v13, LWv/b;

    .line 101
    .line 102
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->l:LBc/a;

    .line 103
    .line 104
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    move-object v14, v1

    .line 109
    check-cast v14, Lorg/xbet/core/domain/usecases/d;

    .line 110
    .line 111
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->m:LBc/a;

    .line 112
    .line 113
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object v1

    .line 117
    move-object v15, v1

    .line 118
    check-cast v15, Lorg/xbet/core/domain/usecases/game_state/a;

    .line 119
    .line 120
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->n:LBc/a;

    .line 121
    .line 122
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v1

    .line 126
    move-object/from16 v16, v1

    .line 127
    .line 128
    check-cast v16, Lorg/xbet/core/domain/usecases/balance/a;

    .line 129
    .line 130
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->o:LBc/a;

    .line 131
    .line 132
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 133
    .line 134
    .line 135
    move-result-object v1

    .line 136
    move-object/from16 v17, v1

    .line 137
    .line 138
    check-cast v17, LVv/d;

    .line 139
    .line 140
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->p:LBc/a;

    .line 141
    .line 142
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 143
    .line 144
    .line 145
    move-result-object v1

    .line 146
    move-object/from16 v18, v1

    .line 147
    .line 148
    check-cast v18, Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 149
    .line 150
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/f;->q:LBc/a;

    .line 151
    .line 152
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 153
    .line 154
    .line 155
    move-result-object v1

    .line 156
    move-object/from16 v19, v1

    .line 157
    .line 158
    check-cast v19, Lorg/xbet/tile_matching/domain/usecases/c;

    .line 159
    .line 160
    move-object/from16 v3, p1

    .line 161
    .line 162
    invoke-static/range {v2 .. v19}, Lorg/xbet/tile_matching/presentation/game/f;->c(Lorg/xbet/core/domain/usecases/u;LwX0/c;LxX0/a;Lak/a;Lm8/a;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/bet/h;Lorg/xbet/core/domain/usecases/bet/o;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;LWv/b;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/a;Lorg/xbet/core/domain/usecases/balance/a;LVv/d;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;Lorg/xbet/tile_matching/domain/usecases/c;)Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 163
    .line 164
    .line 165
    move-result-object v1

    .line 166
    return-object v1
.end method
