.class public final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;
.super Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$a;,
        Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00a6\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0006\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008$\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010%\n\u0002\u0008\u0005\n\u0002\u0010\"\n\u0002\u0008\u0006\u0008\u0000\u0018\u0000 \u0096\u00012\u00020\u0001:\u0004\u0097\u0001\u0098\u0001B\u00e1\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u0012\u0006\u0010/\u001a\u00020.\u0012\u0006\u00101\u001a\u000200\u0012\u0006\u00103\u001a\u000202\u0012\u0006\u00105\u001a\u000204\u0012\u0006\u00107\u001a\u000206\u00a2\u0006\u0004\u00088\u00109J#\u0010?\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020>0=0<2\u0006\u0010;\u001a\u00020:H\u0002\u00a2\u0006\u0004\u0008?\u0010@J\u000f\u0010B\u001a\u00020AH\u0002\u00a2\u0006\u0004\u0008B\u0010CJ \u0010G\u001a\u00020A2\u0006\u0010E\u001a\u00020D2\u0006\u0010F\u001a\u00020>H\u0082@\u00a2\u0006\u0004\u0008G\u0010HJ\u000f\u0010I\u001a\u00020AH\u0016\u00a2\u0006\u0004\u0008I\u0010CJ\u000f\u0010J\u001a\u00020AH\u0016\u00a2\u0006\u0004\u0008J\u0010CJ\u0017\u0010M\u001a\u00020A2\u0006\u0010L\u001a\u00020KH\u0016\u00a2\u0006\u0004\u0008M\u0010NJ\u000f\u0010P\u001a\u00020OH\u0007\u00a2\u0006\u0004\u0008P\u0010QJ!\u0010S\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020R0=0<2\u0006\u0010;\u001a\u00020:\u00a2\u0006\u0004\u0008S\u0010@J\r\u0010T\u001a\u00020A\u00a2\u0006\u0004\u0008T\u0010CJ\u0013\u0010W\u001a\u0008\u0012\u0004\u0012\u00020V0U\u00a2\u0006\u0004\u0008W\u0010XJ\u0013\u0010Z\u001a\u0008\u0012\u0004\u0012\u00020Y0<\u00a2\u0006\u0004\u0008Z\u0010[J\u0013\u0010\\\u001a\u0008\u0012\u0004\u0012\u00020D0<\u00a2\u0006\u0004\u0008\\\u0010[J\u0015\u0010^\u001a\u00020A2\u0006\u0010]\u001a\u00020K\u00a2\u0006\u0004\u0008^\u0010NJ\r\u0010_\u001a\u00020A\u00a2\u0006\u0004\u0008_\u0010CJ\u0015\u0010a\u001a\u00020A2\u0006\u0010`\u001a\u00020:\u00a2\u0006\u0004\u0008a\u0010bJ\u0015\u0010c\u001a\u00020A2\u0006\u0010F\u001a\u00020>\u00a2\u0006\u0004\u0008c\u0010dJ\u001d\u0010f\u001a\u00020A2\u0006\u0010`\u001a\u00020:2\u0006\u0010e\u001a\u00020D\u00a2\u0006\u0004\u0008f\u0010gR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008h\u0010iR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008j\u0010kR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008l\u0010mR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008n\u0010oR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008p\u0010qR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008r\u0010sR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008t\u0010uR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008v\u0010wR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008x\u0010yR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008z\u0010{R\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008|\u0010}R\u001c\u0010\u0081\u0001\u001a\u0008\u0012\u0004\u0012\u00020A0~8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u007f\u0010\u0080\u0001R \u0010\u0085\u0001\u001a\t\u0012\u0004\u0012\u00020Y0\u0082\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0083\u0001\u0010\u0084\u0001R\u0018\u0010\u0089\u0001\u001a\u00030\u0086\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0087\u0001\u0010\u0088\u0001R\u0017\u0010\u008c\u0001\u001a\u00020D8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008a\u0001\u0010\u008b\u0001R$\u0010\u0090\u0001\u001a\u000f\u0012\u0004\u0012\u00020:\u0012\u0004\u0012\u00020>0\u008d\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008e\u0001\u0010\u008f\u0001R\u001e\u0010\u0092\u0001\u001a\t\u0012\u0004\u0012\u00020D0\u0082\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0091\u0001\u0010\u0084\u0001R$\u0010\u0095\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0004\u0012\u00020:0\u0093\u00010~8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0094\u0001\u0010\u0080\u0001\u00a8\u0006\u0099\u0001"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
        "setNeedFavoritesReUpdateUseCase",
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/d;",
        "recommendedGamesPagesScenario",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "openGameDelegate",
        "Lf81/a;",
        "addFavoriteUseCase",
        "Lf81/d;",
        "removeFavoriteUseCase",
        "LHX0/e;",
        "resourceManager",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "Le81/c;",
        "getFavoriteGamesFlowScenario",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LGg/a;",
        "searchAnalytics",
        "Lorg/xbet/analytics/domain/scope/I;",
        "depositAnalytics",
        "Lek/d;",
        "getScreenBalanceByTypeScenario",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "LP91/b;",
        "aggregatorNavigator",
        "LxX0/a;",
        "blockPaymentNavigator",
        "LwX0/C;",
        "routerHolder",
        "Lm8/a;",
        "dispatchers",
        "LAR/a;",
        "depositFatmanLogger",
        "Lfk/o;",
        "observeScreenBalanceUseCase",
        "Lfk/s;",
        "hasUserScreenBalanceUseCase",
        "LZR/a;",
        "searchFatmanLogger",
        "Lgk0/a;",
        "getAccountSelectionStyleConfigTypeScenario",
        "Lek/f;",
        "updateWithCheckGamesAggregatorScenario",
        "Lfk/l;",
        "getLastBalanceUseCase",
        "LC81/f;",
        "setDailyTaskRefreshScenario",
        "<init>",
        "(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/d;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lf81/a;Lf81/d;LHX0/e;LSX0/c;Le81/c;Lorg/xbet/remoteconfig/domain/usecases/i;Lp9/c;Lorg/xbet/ui_common/utils/M;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lek/d;Lorg/xbet/ui_common/utils/internet/a;LP91/b;LxX0/a;LwX0/C;Lm8/a;LAR/a;Lfk/o;Lfk/s;LZR/a;Lgk0/a;Lek/f;Lfk/l;LC81/f;)V",
        "",
        "partitionId",
        "Lkotlinx/coroutines/flow/e;",
        "Landroidx/paging/PagingData;",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "B4",
        "(J)Lkotlinx/coroutines/flow/e;",
        "",
        "H4",
        "()V",
        "",
        "favorite",
        "game",
        "I4",
        "(ZLorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "R3",
        "d4",
        "",
        "throwable",
        "e4",
        "(Ljava/lang/Throwable;)V",
        "",
        "D4",
        "()I",
        "LN21/d;",
        "C4",
        "M4",
        "Lkotlinx/coroutines/flow/Z;",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
        "z4",
        "()Lkotlinx/coroutines/flow/Z;",
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$b;",
        "A4",
        "()Lkotlinx/coroutines/flow/e;",
        "E4",
        "error",
        "G4",
        "F4",
        "gameId",
        "J4",
        "(J)V",
        "K4",
        "(Lorg/xplatform/aggregator/api/model/Game;)V",
        "isFavorite",
        "N4",
        "(JZ)V",
        "y5",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
        "z5",
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/d;",
        "A5",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "B5",
        "Lf81/a;",
        "C5",
        "Lf81/d;",
        "D5",
        "LHX0/e;",
        "E5",
        "LSX0/c;",
        "F5",
        "Le81/c;",
        "G5",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "H5",
        "Lp9/c;",
        "I5",
        "Lorg/xbet/ui_common/utils/M;",
        "Lkotlinx/coroutines/flow/U;",
        "J5",
        "Lkotlinx/coroutines/flow/U;",
        "refreshSharedFlow",
        "Lkotlinx/coroutines/flow/V;",
        "K5",
        "Lkotlinx/coroutines/flow/V;",
        "errorFlow",
        "Lek0/o;",
        "L5",
        "Lek0/o;",
        "remoteConfigModel",
        "M5",
        "Z",
        "virtual",
        "",
        "N5",
        "Ljava/util/Map;",
        "gamesMap",
        "O5",
        "loadingStateFlow",
        "",
        "P5",
        "favoriteGamesFlow",
        "Q5",
        "a",
        "b",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final Q5:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final A5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B5:Lf81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C5:Lf81/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final D5:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final E5:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F5:Le81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final G5:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H5:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I5:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final J5:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public K5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final L5:Lek0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final M5:Z

.field public final N5:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Long;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final O5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P5:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Ljava/util/Set<",
            "Ljava/lang/Long;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z5:Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->Q5:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$a;

    return-void
.end method

.method public constructor <init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/d;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lf81/a;Lf81/d;LHX0/e;LSX0/c;Le81/c;Lorg/xbet/remoteconfig/domain/usecases/i;Lp9/c;Lorg/xbet/ui_common/utils/M;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lek/d;Lorg/xbet/ui_common/utils/internet/a;LP91/b;LxX0/a;LwX0/C;Lm8/a;LAR/a;Lfk/o;Lfk/s;LZR/a;Lgk0/a;Lek/f;Lfk/l;LC81/f;)V
    .locals 20
    .param p1    # Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lf81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lf81/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Le81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LGg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lorg/xbet/analytics/domain/scope/I;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Lek/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # LP91/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # LAR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # Lfk/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # Lfk/s;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # LZR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # Lgk0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # Lek/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p26    # Lfk/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p27    # LC81/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v13, p6

    .line 4
    .line 5
    move-object/from16 v5, p10

    .line 6
    .line 7
    move-object/from16 v3, p11

    .line 8
    .line 9
    move-object/from16 v6, p12

    .line 10
    .line 11
    move-object/from16 v7, p13

    .line 12
    .line 13
    move-object/from16 v12, p14

    .line 14
    .line 15
    move-object/from16 v2, p15

    .line 16
    .line 17
    move-object/from16 v1, p16

    .line 18
    .line 19
    move-object/from16 v4, p17

    .line 20
    .line 21
    move-object/from16 v8, p18

    .line 22
    .line 23
    move-object/from16 v9, p19

    .line 24
    .line 25
    move-object/from16 v14, p20

    .line 26
    .line 27
    move-object/from16 v18, p21

    .line 28
    .line 29
    move-object/from16 v17, p22

    .line 30
    .line 31
    move-object/from16 v15, p23

    .line 32
    .line 33
    move-object/from16 v16, p24

    .line 34
    .line 35
    move-object/from16 v10, p25

    .line 36
    .line 37
    move-object/from16 v11, p26

    .line 38
    .line 39
    move-object/from16 v19, p27

    .line 40
    .line 41
    invoke-direct/range {v0 .. v19}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;-><init>(LP91/b;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LxX0/a;Lp9/c;LGg/a;Lorg/xbet/analytics/domain/scope/I;LwX0/C;Lm8/a;Lek/f;Lfk/l;Lek/d;LHX0/e;LAR/a;LZR/a;Lgk0/a;Lfk/s;Lfk/o;LC81/f;)V

    .line 42
    .line 43
    .line 44
    move-object/from16 v1, p1

    .line 45
    .line 46
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->y5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;

    .line 47
    .line 48
    move-object/from16 v1, p2

    .line 49
    .line 50
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->z5:Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/d;

    .line 51
    .line 52
    move-object/from16 v1, p3

    .line 53
    .line 54
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->A5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 55
    .line 56
    move-object/from16 v1, p4

    .line 57
    .line 58
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->B5:Lf81/a;

    .line 59
    .line 60
    move-object/from16 v1, p5

    .line 61
    .line 62
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->C5:Lf81/d;

    .line 63
    .line 64
    iput-object v13, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->D5:LHX0/e;

    .line 65
    .line 66
    move-object/from16 v1, p7

    .line 67
    .line 68
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->E5:LSX0/c;

    .line 69
    .line 70
    move-object/from16 v1, p8

    .line 71
    .line 72
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->F5:Le81/c;

    .line 73
    .line 74
    move-object/from16 v1, p9

    .line 75
    .line 76
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->G5:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 77
    .line 78
    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->H5:Lp9/c;

    .line 79
    .line 80
    iput-object v3, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->I5:Lorg/xbet/ui_common/utils/M;

    .line 81
    .line 82
    sget-object v2, Lkotlinx/coroutines/channels/BufferOverflow;->DROP_OLDEST:Lkotlinx/coroutines/channels/BufferOverflow;

    .line 83
    .line 84
    const/4 v3, 0x1

    .line 85
    const/4 v4, 0x0

    .line 86
    const/4 v5, 0x2

    .line 87
    const/4 v6, 0x0

    .line 88
    invoke-static {v3, v4, v2, v5, v6}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    .line 89
    .line 90
    .line 91
    move-result-object v2

    .line 92
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->J5:Lkotlinx/coroutines/flow/U;

    .line 93
    .line 94
    sget-object v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$b$a;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$b$a;

    .line 95
    .line 96
    invoke-static {v3}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 97
    .line 98
    .line 99
    move-result-object v3

    .line 100
    iput-object v3, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 101
    .line 102
    invoke-interface {v1}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 103
    .line 104
    .line 105
    move-result-object v1

    .line 106
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->L5:Lek0/o;

    .line 107
    .line 108
    invoke-virtual {v1}, Lek0/o;->y1()Z

    .line 109
    .line 110
    .line 111
    move-result v1

    .line 112
    iput-boolean v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->M5:Z

    .line 113
    .line 114
    new-instance v1, Ljava/util/LinkedHashMap;

    .line 115
    .line 116
    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 117
    .line 118
    .line 119
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->N5:Ljava/util/Map;

    .line 120
    .line 121
    sget-object v1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 122
    .line 123
    invoke-static {v1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 124
    .line 125
    .line 126
    move-result-object v1

    .line 127
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->O5:Lkotlinx/coroutines/flow/V;

    .line 128
    .line 129
    const/4 v1, 0x7

    .line 130
    invoke-static {v4, v4, v6, v1, v6}, Lkotlinx/coroutines/flow/a0;->b(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/U;

    .line 131
    .line 132
    .line 133
    move-result-object v1

    .line 134
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->P5:Lkotlinx/coroutines/flow/U;

    .line 135
    .line 136
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 137
    .line 138
    invoke-interface {v2, v1}, Lkotlinx/coroutines/flow/U;->d(Ljava/lang/Object;)Z

    .line 139
    .line 140
    .line 141
    return-void
.end method

.method private final H4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->H5:Lp9/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Lp9/c;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->F5:Le81/c;

    .line 10
    .line 11
    invoke-interface {v0}, Le81/c;->invoke()Lkotlinx/coroutines/flow/e;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    goto :goto_0

    .line 16
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->X(Ljava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    :goto_0
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$loadFavoriteGames$1;

    .line 25
    .line 26
    const/4 v2, 0x0

    .line 27
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$loadFavoriteGames$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;Lkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 47
    .line 48
    .line 49
    return-void
.end method

.method public static final L4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    invoke-interface {p0}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    invoke-interface {v0, p0, p1}, Lkotlinx/coroutines/CoroutineExceptionHandler;->handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method

.method public static synthetic l4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->L4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic m4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->P5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic n4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;)Ljava/util/Map;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->N5:Ljava/util/Map;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic o4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;)Lp9/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->H5:Lp9/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic p4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;)Z
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->O3()Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic q4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->O5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic r4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;)Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->z5:Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;)Lek0/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->L5:Lek0/o;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->D5:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->M5:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic v4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;Ljava/lang/Throwable;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->Q3(Ljava/lang/Throwable;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic w4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->H4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic x4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;ZLorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->I4(ZLorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic y4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->a4(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final A4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final B4(J)Lkotlinx/coroutines/flow/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J)",
            "Lkotlinx/coroutines/flow/e<",
            "Landroidx/paging/PagingData<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->J5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$getGames$$inlined$flatMapLatest$1;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v1, v2, p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$getGames$$inlined$flatMapLatest$1;-><init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;J)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->C0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    new-instance p2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$getGames$2;

    .line 14
    .line 15
    invoke-direct {p2, p0, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$getGames$2;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->h0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 23
    .line 24
    .line 25
    move-result-object p2

    .line 26
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-static {p2, v0}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 31
    .line 32
    .line 33
    move-result-object p2

    .line 34
    invoke-static {p1, p2}, Landroidx/paging/CachedPagingDataKt;->a(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/flow/e;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    return-object p1
.end method

.method public final C4(J)Lkotlinx/coroutines/flow/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J)",
            "Lkotlinx/coroutines/flow/e<",
            "Landroidx/paging/PagingData<",
            "LN21/d;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->B4(J)Lkotlinx/coroutines/flow/e;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->P5:Lkotlinx/coroutines/flow/U;

    .line 6
    .line 7
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$getGamesUiStream$1;

    .line 8
    .line 9
    const/4 v1, 0x0

    .line 10
    invoke-direct {v0, p0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$getGamesUiStream$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {p1, p2, v0}, Lkotlinx/coroutines/flow/g;->W(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object p2

    .line 21
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-static {p2, v0}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 26
    .line 27
    .line 28
    move-result-object p2

    .line 29
    invoke-static {p1, p2}, Landroidx/paging/CachedPagingDataKt;->a(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/flow/e;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    return-object p1
.end method

.method public final D4()I
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->L5:Lek0/o;

    .line 2
    .line 3
    invoke-virtual {v0}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-static {v0, v1}, Ls81/b;->b(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Z)I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    return v0
.end method

.method public final E4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->O5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final F4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$b$a;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$b$a;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final G4(Ljava/lang/Throwable;)V
    .locals 2
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-interface {v1}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-interface {v0, v1, p1}, Lkotlinx/coroutines/CoroutineExceptionHandler;->handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final I4(ZLorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->G5:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 2
    .line 3
    invoke-interface {v0}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lek0/o;->o()Lek0/a;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Lek0/a;->c()Z

    .line 12
    .line 13
    .line 14
    move-result v4

    .line 15
    if-eqz p1, :cond_1

    .line 16
    .line 17
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->C5:Lf81/d;

    .line 18
    .line 19
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 20
    .line 21
    .line 22
    move-result-wide v2

    .line 23
    const/16 v5, 0x13f7

    .line 24
    .line 25
    move-object v6, p3

    .line 26
    invoke-interface/range {v1 .. v6}, Lf81/d;->a(JZILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p2

    .line 34
    if-ne p1, p2, :cond_0

    .line 35
    .line 36
    return-object p1

    .line 37
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 38
    .line 39
    return-object p1

    .line 40
    :cond_1
    move-object v6, p3

    .line 41
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->B5:Lf81/a;

    .line 42
    .line 43
    const/16 p3, 0x13f7

    .line 44
    .line 45
    invoke-interface {p1, p2, v4, p3, v6}, Lf81/a;->a(Lorg/xplatform/aggregator/api/model/Game;ZILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object p2

    .line 53
    if-ne p1, p2, :cond_2

    .line 54
    .line 55
    return-object p1

    .line 56
    :cond_2
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 57
    .line 58
    return-object p1
.end method

.method public final J4(J)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->N5:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    check-cast p1, Lorg/xplatform/aggregator/api/model/Game;

    .line 12
    .line 13
    if-eqz p1, :cond_0

    .line 14
    .line 15
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->K4(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 16
    .line 17
    .line 18
    :cond_0
    return-void
.end method

.method public final K4(Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 3
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->A5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/l;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/l;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;)V

    .line 6
    .line 7
    .line 8
    const/16 v2, 0x13f7

    .line 9
    .line 10
    invoke-virtual {v0, p1, v2, v1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->u(Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public final M4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->y5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;->a()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->J5:Lkotlinx/coroutines/flow/U;

    .line 7
    .line 8
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$onInit$1;

    .line 9
    .line 10
    const/4 v2, 0x0

    .line 11
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$onInit$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;Lkotlin/coroutines/e;)V

    .line 12
    .line 13
    .line 14
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 23
    .line 24
    .line 25
    move-result-object v2

    .line 26
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final N4(JZ)V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->N5:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    check-cast p1, Lorg/xplatform/aggregator/api/model/Game;

    .line 12
    .line 13
    if-eqz p1, :cond_0

    .line 14
    .line 15
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    new-instance v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$onUpdateFavoriteCLick$1$1;

    .line 24
    .line 25
    const/4 p2, 0x0

    .line 26
    invoke-direct {v3, p0, p3, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$onUpdateFavoriteCLick$1$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;ZLorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    const/4 v4, 0x2

    .line 30
    const/4 v5, 0x0

    .line 31
    const/4 v2, 0x0

    .line 32
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 33
    .line 34
    .line 35
    :cond_0
    return-void
.end method

.method public R3()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$b$a;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$b$a;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->J5:Lkotlinx/coroutines/flow/U;

    .line 9
    .line 10
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/U;->d(Ljava/lang/Object;)Z

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public d4()V
    .locals 14

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$b$b;

    .line 4
    .line 5
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->E5:LSX0/c;

    .line 6
    .line 7
    sget-object v3, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 8
    .line 9
    sget v8, Lpb/k;->data_retrieval_error:I

    .line 10
    .line 11
    const/16 v12, 0x1de

    .line 12
    .line 13
    const/4 v13, 0x0

    .line 14
    const/4 v4, 0x0

    .line 15
    const/4 v5, 0x0

    .line 16
    const/4 v6, 0x0

    .line 17
    const/4 v7, 0x0

    .line 18
    const/4 v9, 0x0

    .line 19
    const/4 v10, 0x0

    .line 20
    const/4 v11, 0x0

    .line 21
    invoke-static/range {v2 .. v13}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$b$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 26
    .line 27
    .line 28
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public e4(Ljava/lang/Throwable;)V
    .locals 14
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$b$b;

    .line 4
    .line 5
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->E5:LSX0/c;

    .line 6
    .line 7
    sget-object v3, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 8
    .line 9
    sget v8, Lpb/k;->data_retrieval_error:I

    .line 10
    .line 11
    const/16 v12, 0x1de

    .line 12
    .line 13
    const/4 v13, 0x0

    .line 14
    const/4 v4, 0x0

    .line 15
    const/4 v5, 0x0

    .line 16
    const/4 v6, 0x0

    .line 17
    const/4 v7, 0x0

    .line 18
    const/4 v9, 0x0

    .line 19
    const/4 v10, 0x0

    .line 20
    const/4 v11, 0x0

    .line 21
    invoke-static/range {v2 .. v13}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$b$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 26
    .line 27
    .line 28
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->I5:Lorg/xbet/ui_common/utils/M;

    .line 32
    .line 33
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$showCustomError$1;

    .line 34
    .line 35
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel$showCustomError$1;-><init>(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 39
    .line 40
    .line 41
    return-void
.end method

.method public final z4()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->A5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->q()Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method
