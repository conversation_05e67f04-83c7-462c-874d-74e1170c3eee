.class public final LQI0/a;
.super LA4/f;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "LA4/f<",
        "Ljava/util/List<",
        "+",
        "LSI0/a;",
        ">;>;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00030\u00020\u0001B#\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0012\u0010\u0008\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\u0004\u0008\t\u0010\n\u00a8\u0006\u000b"
    }
    d2 = {
        "LQI0/a;",
        "LA4/f;",
        "",
        "LSI0/a;",
        "",
        "sportId",
        "Lkotlin/Function1;",
        "",
        "onGameClickListener",
        "<init>",
        "(JLkotlin/jvm/functions/Function1;)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>(JLkotlin/jvm/functions/Function1;)V
    .locals 1
    .param p3    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LSI0/a;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, LA4/f;-><init>()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, LA4/a;->d:LA4/d;

    .line 5
    .line 6
    invoke-static {p1, p2, p3}, Lorg/xbet/statistic/player/impl/player/player_lastgame/presentation/adapter/PlayerLastGameAdapterDelegateKt;->e(JLkotlin/jvm/functions/Function1;)LA4/c;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 11
    .line 12
    .line 13
    return-void
.end method
