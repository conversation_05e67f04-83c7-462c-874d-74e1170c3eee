.class public final Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0000\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u000f\u0010\t\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\t\u0010\u0003J\u000f\u0010\n\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u0003J\u000f\u0010\u000b\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u000b\u0010\u0003J\u0019\u0010\u000e\u001a\u00020\u00062\u0008\u0010\r\u001a\u0004\u0018\u00010\u000cH\u0014\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u000f\u0010\u0010\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0010\u0010\u0003J\u000f\u0010\u0011\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0011\u0010\u0003J\u000f\u0010\u0012\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0012\u0010\u0003J\u0017\u0010\u0015\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u000f\u0010\u0017\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0003J\u0017\u0010\u001a\u001a\u00020\u00062\u0006\u0010\u0019\u001a\u00020\u0018H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0017\u0010\u001c\u001a\u00020\u00062\u0006\u0010\u0019\u001a\u00020\u0018H\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001bJ\u0017\u0010\u001d\u001a\u00020\u00062\u0006\u0010\u0019\u001a\u00020\u0018H\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001bJ\u0017\u0010\u001e\u001a\u00020\u00062\u0006\u0010\u0019\u001a\u00020\u0018H\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001bJ\u0017\u0010\u001f\u001a\u00020\u00062\u0006\u0010\u0019\u001a\u00020\u0018H\u0002\u00a2\u0006\u0004\u0008\u001f\u0010\u001bR\"\u0010\'\u001a\u00020 8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008!\u0010\"\u001a\u0004\u0008#\u0010$\"\u0004\u0008%\u0010&R\u001b\u0010-\u001a\u00020(8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008)\u0010*\u001a\u0004\u0008+\u0010,R\u001b\u00103\u001a\u00020.8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008/\u00100\u001a\u0004\u00081\u00102\u00a8\u00064"
    }
    d2 = {
        "Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "connected",
        "",
        "J2",
        "(Z)V",
        "X2",
        "K2",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "onPause",
        "onDestroyView",
        "v2",
        "Lmi/a;",
        "screenState",
        "I2",
        "(Lmi/a;)V",
        "W2",
        "Lmi/b;",
        "result",
        "P2",
        "(Lmi/b;)V",
        "Q2",
        "S2",
        "R2",
        "V2",
        "Landroidx/lifecycle/e0$c;",
        "i0",
        "Landroidx/lifecycle/e0$c;",
        "F2",
        "()Landroidx/lifecycle/e0$c;",
        "setAppleFortuneViewModelFactory",
        "(Landroidx/lifecycle/e0$c;)V",
        "appleFortuneViewModelFactory",
        "Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;",
        "j0",
        "Lkotlin/j;",
        "H2",
        "()Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;",
        "viewModel",
        "Lgi/b;",
        "k0",
        "LRc/c;",
        "G2",
        "()Lgi/b;",
        "binding",
        "apple_fortune_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final synthetic l0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public i0:Landroidx/lifecycle/e0$c;

.field public final j0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getBinding()Lorg/xbet/apple_fortune/databinding/FragmentAppleFortuneBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;

    .line 7
    .line 8
    const-string v4, "binding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->l0:[Lkotlin/reflect/m;

    .line 23
    .line 24
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, Lai/c;->fragment_apple_fortune:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xbet/apple_fortune/presentation/game/e;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lorg/xbet/apple_fortune/presentation/game/e;-><init>(Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)V

    .line 9
    .line 10
    .line 11
    new-instance v1, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$special$$inlined$viewModels$default$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 17
    .line 18
    new-instance v3, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$special$$inlined$viewModels$default$2;

    .line 19
    .line 20
    invoke-direct {v3, v1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 21
    .line 22
    .line 23
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const-class v2, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;

    .line 28
    .line 29
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    new-instance v3, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$special$$inlined$viewModels$default$3;

    .line 34
    .line 35
    invoke-direct {v3, v1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 36
    .line 37
    .line 38
    new-instance v4, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$special$$inlined$viewModels$default$4;

    .line 39
    .line 40
    const/4 v5, 0x0

    .line 41
    invoke-direct {v4, v5, v1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->j0:Lkotlin/j;

    .line 49
    .line 50
    sget-object v0, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$binding$2;->INSTANCE:Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$binding$2;

    .line 51
    .line 52
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->k0:LRc/c;

    .line 57
    .line 58
    return-void
.end method

.method public static synthetic A2(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->M2(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->L2(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic C2(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;I)Z
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->O2(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;I)Z

    move-result p0

    return p0
.end method

.method public static final synthetic D2(Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;Lmi/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->T2(Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;Lmi/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic E2(Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->U2(Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final J2(Z)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->G2()Lgi/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->u(Z)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method private final K2()V
    .locals 6

    .line 1
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->G2()Lgi/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 6
    .line 7
    new-instance v2, Lorg/xbet/apple_fortune/presentation/game/a;

    .line 8
    .line 9
    invoke-direct {v2, v0, p0}, Lorg/xbet/apple_fortune/presentation/game/a;-><init>(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)V

    .line 10
    .line 11
    .line 12
    new-instance v3, Lorg/xbet/apple_fortune/presentation/game/b;

    .line 13
    .line 14
    invoke-direct {v3, v0, p0}, Lorg/xbet/apple_fortune/presentation/game/b;-><init>(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)V

    .line 15
    .line 16
    .line 17
    new-instance v4, Lorg/xbet/apple_fortune/presentation/game/c;

    .line 18
    .line 19
    invoke-direct {v4, v0, p0}, Lorg/xbet/apple_fortune/presentation/game/c;-><init>(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)V

    .line 20
    .line 21
    .line 22
    new-instance v5, Lorg/xbet/apple_fortune/presentation/game/d;

    .line 23
    .line 24
    invoke-direct {v5, v0, p0}, Lorg/xbet/apple_fortune/presentation/game/d;-><init>(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)V

    .line 25
    .line 26
    .line 27
    invoke-virtual {v1, v2, v3, v4, v5}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->w(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public static final L2(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 2
    .line 3
    const/4 v0, 0x0

    .line 4
    invoke-virtual {p0, v0}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->C(Z)V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->H2()Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;->k4()V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final M2(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-virtual {v0, v1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->E(Z)V

    .line 5
    .line 6
    .line 7
    iget-object p0, p0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 8
    .line 9
    invoke-virtual {p0, v1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->u(Z)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->H2()Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;->l4()V

    .line 17
    .line 18
    .line 19
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 20
    .line 21
    return-object p0
.end method

.method public static final N2(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {v0, v1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->u(Z)V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->H2()Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-virtual {p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;->p4()V

    .line 12
    .line 13
    .line 14
    iget-object p0, p0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 15
    .line 16
    invoke-virtual {p0, v1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->E(Z)V

    .line 17
    .line 18
    .line 19
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 20
    .line 21
    return-object p0
.end method

.method public static final O2(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;I)Z
    .locals 2

    .line 1
    iget-object v0, p0, Lgi/b;->h:Landroid/widget/FrameLayout;

    .line 2
    .line 3
    const/16 v1, 0x8

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 9
    .line 10
    const/4 v1, 0x0

    .line 11
    invoke-virtual {v0, v1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->u(Z)V

    .line 12
    .line 13
    .line 14
    iget-object p0, p0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 15
    .line 16
    invoke-virtual {p0, v1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->E(Z)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->H2()Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;

    .line 20
    .line 21
    .line 22
    move-result-object p0

    .line 23
    invoke-virtual {p0, p2}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;->q4(I)V

    .line 24
    .line 25
    .line 26
    const/4 p0, 0x1

    .line 27
    return p0
.end method

.method public static final synthetic T2(Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;Lmi/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->I2(Lmi/a;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic U2(Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->J2(Z)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final X2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->G2()Lgi/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lgi/b;->h:Landroid/widget/FrameLayout;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static final Y2(Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->F2()Landroidx/lifecycle/e0$c;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic y2(Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->Y2(Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->N2(Lgi/b;Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final F2()Landroidx/lifecycle/e0$c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->i0:Landroidx/lifecycle/e0$c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final G2()Lgi/b;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->k0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->l0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lgi/b;

    .line 13
    .line 14
    return-object v0
.end method

.method public final H2()Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->j0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final I2(Lmi/a;)V
    .locals 1

    .line 1
    instance-of v0, p1, Lmi/a$g;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->W2()V

    .line 6
    .line 7
    .line 8
    return-void

    .line 9
    :cond_0
    instance-of v0, p1, Lmi/a$e;

    .line 10
    .line 11
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->X2()V

    .line 14
    .line 15
    .line 16
    return-void

    .line 17
    :cond_1
    instance-of v0, p1, Lmi/a$d;

    .line 18
    .line 19
    if-eqz v0, :cond_2

    .line 20
    .line 21
    check-cast p1, Lmi/a$d;

    .line 22
    .line 23
    invoke-virtual {p1}, Lmi/a$d;->a()Lmi/b;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-virtual {p0, p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->S2(Lmi/b;)V

    .line 28
    .line 29
    .line 30
    return-void

    .line 31
    :cond_2
    instance-of v0, p1, Lmi/a$a;

    .line 32
    .line 33
    if-eqz v0, :cond_3

    .line 34
    .line 35
    check-cast p1, Lmi/a$a;

    .line 36
    .line 37
    invoke-virtual {p1}, Lmi/a$a;->a()Lmi/b;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    invoke-virtual {p0, p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->P2(Lmi/b;)V

    .line 42
    .line 43
    .line 44
    return-void

    .line 45
    :cond_3
    instance-of v0, p1, Lmi/a$b;

    .line 46
    .line 47
    if-eqz v0, :cond_4

    .line 48
    .line 49
    check-cast p1, Lmi/a$b;

    .line 50
    .line 51
    invoke-virtual {p1}, Lmi/a$b;->a()Lmi/b;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    invoke-virtual {p0, p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->Q2(Lmi/b;)V

    .line 56
    .line 57
    .line 58
    return-void

    .line 59
    :cond_4
    instance-of v0, p1, Lmi/a$c;

    .line 60
    .line 61
    if-eqz v0, :cond_5

    .line 62
    .line 63
    check-cast p1, Lmi/a$c;

    .line 64
    .line 65
    invoke-virtual {p1}, Lmi/a$c;->a()Lmi/b;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    invoke-virtual {p0, p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->R2(Lmi/b;)V

    .line 70
    .line 71
    .line 72
    return-void

    .line 73
    :cond_5
    instance-of v0, p1, Lmi/a$f;

    .line 74
    .line 75
    if-eqz v0, :cond_6

    .line 76
    .line 77
    check-cast p1, Lmi/a$f;

    .line 78
    .line 79
    invoke-virtual {p1}, Lmi/a$f;->a()Lmi/b;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    invoke-virtual {p0, p1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->V2(Lmi/b;)V

    .line 84
    .line 85
    .line 86
    return-void

    .line 87
    :cond_6
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 88
    .line 89
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 90
    .line 91
    .line 92
    throw p1
.end method

.method public final P2(Lmi/b;)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->G2()Lgi/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, Lgi/b;->h:Landroid/widget/FrameLayout;

    .line 6
    .line 7
    const/16 v2, 0x8

    .line 8
    .line 9
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    iget-object v1, v0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    invoke-virtual {v1, v2}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->E(Z)V

    .line 16
    .line 17
    .line 18
    iget-object v0, v0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 19
    .line 20
    invoke-virtual {v0, p1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->z(Lmi/b;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public final Q2(Lmi/b;)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->G2()Lgi/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, Lgi/b;->h:Landroid/widget/FrameLayout;

    .line 6
    .line 7
    const/16 v2, 0x8

    .line 8
    .line 9
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    iget-object v0, v0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 13
    .line 14
    const/4 v1, 0x1

    .line 15
    invoke-virtual {v0, v1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->E(Z)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {v0, p1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->D(Lmi/b;)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {v0, v1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->u(Z)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final R2(Lmi/b;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->G2()Lgi/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lgi/b;->h:Landroid/widget/FrameLayout;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->G2()Lgi/b;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iget-object v0, v0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 17
    .line 18
    const/4 v1, 0x0

    .line 19
    invoke-virtual {v0, v1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->C(Z)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {v0, v1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->u(Z)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {v0, p1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->A(Lmi/b;)V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public final S2(Lmi/b;)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->G2()Lgi/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, Lgi/b;->h:Landroid/widget/FrameLayout;

    .line 6
    .line 7
    const/16 v2, 0x8

    .line 8
    .line 9
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    iget-object v0, v0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 13
    .line 14
    invoke-virtual {v0}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->t()V

    .line 15
    .line 16
    .line 17
    invoke-virtual {v0, p1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->D(Lmi/b;)V

    .line 18
    .line 19
    .line 20
    const/4 p1, 0x1

    .line 21
    invoke-virtual {v0, p1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->u(Z)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final V2(Lmi/b;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->G2()Lgi/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lgi/b;->h:Landroid/widget/FrameLayout;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->G2()Lgi/b;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iget-object v0, v0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 17
    .line 18
    invoke-virtual {v0, p1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->D(Lmi/b;)V

    .line 19
    .line 20
    .line 21
    const/4 v1, 0x0

    .line 22
    invoke-virtual {v0, v1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->C(Z)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {v0, v1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->u(Z)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {v0}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->B()V

    .line 29
    .line 30
    .line 31
    invoke-virtual {v0, p1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->A(Lmi/b;)V

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public final W2()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->G2()Lgi/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, Lgi/b;->h:Landroid/widget/FrameLayout;

    .line 6
    .line 7
    const/16 v2, 0x8

    .line 8
    .line 9
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    iget-object v1, v0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 13
    .line 14
    invoke-virtual {v1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->y()V

    .line 15
    .line 16
    .line 17
    iget-object v0, v0, Lgi/b;->g:Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;

    .line 18
    .line 19
    const/4 v1, 0x0

    .line 20
    invoke-virtual {v0, v1}, Lorg/xbet/apple_fortune/presentation/views/AppleFortuneCellGameView;->u(Z)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public onDestroyView()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->H2()Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;->g4()V

    .line 6
    .line 7
    .line 8
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroyView()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onPause()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onPause()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->H2()Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;->h4()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->K2()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public u2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lorg/xbet/apple_fortune/presentation/holder/AppleFortuneFragment;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    check-cast v0, Lorg/xbet/apple_fortune/presentation/holder/AppleFortuneFragment;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {v0}, Lorg/xbet/apple_fortune/presentation/holder/AppleFortuneFragment;->i4()Lhi/a;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    invoke-interface {v0, p0}, Lhi/a;->c(Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;)V

    .line 22
    .line 23
    .line 24
    :cond_1
    return-void
.end method

.method public v2()V
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->H2()Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;->Y3()Lkotlinx/coroutines/flow/e;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v6, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$onObserveData$1;

    .line 12
    .line 13
    invoke-direct {v6, v0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$onObserveData$1;-><init>(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    sget-object v5, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 17
    .line 18
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 19
    .line 20
    .line 21
    move-result-object v4

    .line 22
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    new-instance v2, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 27
    .line 28
    const/4 v7, 0x0

    .line 29
    invoke-direct/range {v2 .. v7}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 30
    .line 31
    .line 32
    const/4 v11, 0x3

    .line 33
    const/4 v12, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    const/4 v9, 0x0

    .line 36
    move-object v7, v1

    .line 37
    move-object v10, v2

    .line 38
    invoke-static/range {v7 .. v12}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 39
    .line 40
    .line 41
    invoke-virtual {v0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment;->H2()Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    invoke-virtual {v1}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameViewModel;->X3()Lkotlinx/coroutines/flow/e;

    .line 46
    .line 47
    .line 48
    move-result-object v8

    .line 49
    new-instance v11, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$onObserveData$2;

    .line 50
    .line 51
    invoke-direct {v11, v0}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$onObserveData$2;-><init>(Ljava/lang/Object;)V

    .line 52
    .line 53
    .line 54
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 55
    .line 56
    .line 57
    move-result-object v9

    .line 58
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 59
    .line 60
    .line 61
    move-result-object v1

    .line 62
    new-instance v15, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 63
    .line 64
    move-object v10, v5

    .line 65
    move-object v7, v15

    .line 66
    invoke-direct/range {v7 .. v12}, Lorg/xbet/apple_fortune/presentation/game/AppleFortuneGameFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 67
    .line 68
    .line 69
    const/16 v16, 0x3

    .line 70
    .line 71
    const/16 v17, 0x0

    .line 72
    .line 73
    const/4 v13, 0x0

    .line 74
    const/4 v14, 0x0

    .line 75
    move-object v12, v1

    .line 76
    invoke-static/range {v12 .. v17}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 77
    .line 78
    .line 79
    return-void
.end method
