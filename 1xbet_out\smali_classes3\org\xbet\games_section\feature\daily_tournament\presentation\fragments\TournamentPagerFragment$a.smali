.class public final Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u000b\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J-\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u000b\u0010\u000cR\u0014\u0010\u000e\u001a\u00020\r8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0010\u001a\u00020\r8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u000fR\u0014\u0010\u0011\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0012R\u0014\u0010\u0013\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0012R\u0014\u0010\u0014\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0012R\u0014\u0010\u0015\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0012R\u0014\u0010\u0016\u001a\u00020\r8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u000fR\u0014\u0010\u0017\u001a\u00020\r8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010\u000f\u00a8\u0006\u0018"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$a;",
        "",
        "<init>",
        "()V",
        "",
        "fromGames",
        "",
        "tournamentTitle",
        "tournamentBgName",
        "tournamentPrizeName",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;",
        "a",
        "(ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;",
        "",
        "offset",
        "I",
        "coefAnimation",
        "FROM_GAMES",
        "Ljava/lang/String;",
        "TOURNAMENT_TITLE",
        "TOURNAMENT_BG_NAME",
        "TOURNAMENT_PRIZE_NAME",
        "PAGE_ZERO",
        "PAGE_FIRST",
        "daily_tournament_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;
    .locals 1
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->J2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Z)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->M2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Ljava/lang/String;)V

    .line 10
    .line 11
    .line 12
    invoke-static {v0, p3}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->K2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Ljava/lang/String;)V

    .line 13
    .line 14
    .line 15
    invoke-static {v0, p4}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->L2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    return-object v0
.end method
