.class public final Lorg/spongycastle/pqc/crypto/xmss/l;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:Lorg/spongycastle/pqc/crypto/xmss/p;

.field public final b:Lorg/spongycastle/pqc/crypto/xmss/q;

.field public final c:I

.field public final d:I


# direct methods
.method public constructor <init>(IILorg/spongycastle/crypto/e;)V
    .locals 6

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput p1, p0, Lorg/spongycastle/pqc/crypto/xmss/l;->c:I

    .line 5
    .line 6
    iput p2, p0, Lorg/spongycastle/pqc/crypto/xmss/l;->d:I

    .line 7
    .line 8
    new-instance v0, Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 9
    .line 10
    invoke-static {p1, p2}, Lorg/spongycastle/pqc/crypto/xmss/l;->h(II)I

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    invoke-direct {v0, p1, p3}, Lorg/spongycastle/pqc/crypto/xmss/q;-><init>(ILorg/spongycastle/crypto/e;)V

    .line 15
    .line 16
    .line 17
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/l;->b:Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 18
    .line 19
    invoke-virtual {p0}, Lorg/spongycastle/pqc/crypto/xmss/l;->a()Lorg/spongycastle/crypto/e;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-interface {p1}, Lorg/spongycastle/crypto/e;->c()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    invoke-virtual {p0}, Lorg/spongycastle/pqc/crypto/xmss/l;->b()I

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    invoke-virtual {p0}, Lorg/spongycastle/pqc/crypto/xmss/l;->f()I

    .line 32
    .line 33
    .line 34
    move-result v2

    .line 35
    invoke-virtual {p0}, Lorg/spongycastle/pqc/crypto/xmss/l;->e()I

    .line 36
    .line 37
    .line 38
    move-result v3

    .line 39
    invoke-virtual {p0}, Lorg/spongycastle/pqc/crypto/xmss/l;->c()I

    .line 40
    .line 41
    .line 42
    move-result v4

    .line 43
    move v5, p2

    .line 44
    invoke-static/range {v0 .. v5}, Lorg/spongycastle/pqc/crypto/xmss/a;->b(Ljava/lang/String;IIIII)Lorg/spongycastle/pqc/crypto/xmss/a;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/l;->a:Lorg/spongycastle/pqc/crypto/xmss/p;

    .line 49
    .line 50
    return-void
.end method

.method public static h(II)I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/IllegalArgumentException;
        }
    .end annotation

    .line 1
    const/4 v0, 0x2

    .line 2
    if-lt p0, v0, :cond_2

    .line 3
    .line 4
    rem-int v0, p0, p1

    .line 5
    .line 6
    if-nez v0, :cond_1

    .line 7
    .line 8
    div-int/2addr p0, p1

    .line 9
    const/4 p1, 0x1

    .line 10
    if-eq p0, p1, :cond_0

    .line 11
    .line 12
    return p0

    .line 13
    :cond_0
    new-instance p0, Ljava/lang/IllegalArgumentException;

    .line 14
    .line 15
    const-string p1, "height / layers must be greater than 1"

    .line 16
    .line 17
    invoke-direct {p0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    throw p0

    .line 21
    :cond_1
    new-instance p0, Ljava/lang/IllegalArgumentException;

    .line 22
    .line 23
    const-string p1, "layers must divide totalHeight without remainder"

    .line 24
    .line 25
    invoke-direct {p0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 26
    .line 27
    .line 28
    throw p0

    .line 29
    :cond_2
    new-instance p0, Ljava/lang/IllegalArgumentException;

    .line 30
    .line 31
    const-string p1, "totalHeight must be > 1"

    .line 32
    .line 33
    invoke-direct {p0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 34
    .line 35
    .line 36
    throw p0
.end method


# virtual methods
.method public a()Lorg/spongycastle/crypto/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/l;->b:Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/q;->b()Lorg/spongycastle/crypto/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public b()I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/l;->b:Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/q;->c()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public c()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/spongycastle/pqc/crypto/xmss/l;->c:I

    .line 2
    .line 3
    return v0
.end method

.method public d()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/spongycastle/pqc/crypto/xmss/l;->d:I

    .line 2
    .line 3
    return v0
.end method

.method public e()I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/l;->b:Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/q;->f()Lorg/spongycastle/pqc/crypto/xmss/g;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/g;->d()Lorg/spongycastle/pqc/crypto/xmss/i;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/i;->c()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    return v0
.end method

.method public f()I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/l;->b:Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/q;->g()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public g()Lorg/spongycastle/pqc/crypto/xmss/q;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/l;->b:Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 2
    .line 3
    return-object v0
.end method
