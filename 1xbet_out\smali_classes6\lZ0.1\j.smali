.class public final LlZ0/j;
.super Ljava/lang/Object;


# static fields
.field public static COUPON:I = 0x7f0a0006

.field public static FAVORITE:I = 0x7f0a0009

.field public static POPULAR:I = 0x7f0a000f

.field public static accordion:I = 0x7f0a0046

.field public static accountAmount:I = 0x7f0a0049

.field public static accountAndIconFlow:I = 0x7f0a004a

.field public static accountInfoFlow:I = 0x7f0a004f

.field public static accountMainFlow:I = 0x7f0a0053

.field public static accountSelectionTitle:I = 0x7f0a0056

.field public static accountTitle:I = 0x7f0a0057

.field public static accountsButton:I = 0x7f0a005a

.field public static action:I = 0x7f0a005d

.field public static active:I = 0x7f0a0095

.field public static additionalTopContainer:I = 0x7f0a00ad

.field public static aggregatorDailyMissionsRecyclerView:I = 0x7f0a00bf

.field public static alertDialogRoot:I = 0x7f0a00df

.field public static amount:I = 0x7f0a00f3

.field public static amountCurrency:I = 0x7f0a00f4

.field public static amountCurrencyContainer:I = 0x7f0a00f5

.field public static appCompatImageView:I = 0x7f0a0103

.field public static authorizationButton:I = 0x7f0a0126

.field public static bButton:I = 0x7f0a013a

.field public static background:I = 0x7f0a0153

.field public static badge:I = 0x7f0a0161

.field public static badgeSelected:I = 0x7f0a0163

.field public static barrier:I = 0x7f0a0196

.field public static baseBottomSheetView:I = 0x7f0a01a7

.field public static basic:I = 0x7f0a01aa

.field public static basicMultilineTextField:I = 0x7f0a01ac

.field public static basicStatic:I = 0x7f0a01ad

.field public static basicTextField:I = 0x7f0a01ae

.field public static betNumberTv:I = 0x7f0a01c3

.field public static betSumContainer:I = 0x7f0a01cd

.field public static betSumSymbolTv:I = 0x7f0a01d2

.field public static betSumTv:I = 0x7f0a01d5

.field public static betSumValueTv:I = 0x7f0a01d7

.field public static betTypeTv:I = 0x7f0a01dc

.field public static bottomBarFirstButton:I = 0x7f0a024a

.field public static bottomBarSecondButton:I = 0x7f0a024d

.field public static bottomBarThirdButton:I = 0x7f0a024e

.field public static bottomContainer:I = 0x7f0a0253

.field public static bottomSeparator:I = 0x7f0a0263

.field public static btnBottom:I = 0x7f0a029f

.field public static btnCashBack:I = 0x7f0a02a3

.field public static btnLeft:I = 0x7f0a02bd

.field public static btnMiddle:I = 0x7f0a02c5

.field public static btnRight:I = 0x7f0a02df

.field public static btnTop:I = 0x7f0a02f5

.field public static button:I = 0x7f0a0322

.field public static buttonContainer:I = 0x7f0a032c

.field public static cancelIcon:I = 0x7f0a035f

.field public static caption:I = 0x7f0a0363

.field public static cardActionButton:I = 0x7f0a036d

.field public static cardHorizontal:I = 0x7f0a0373

.field public static cardImage:I = 0x7f0a0374

.field public static cardStatus:I = 0x7f0a0377

.field public static cardTitleWithSubtitle:I = 0x7f0a0378

.field public static cardView:I = 0x7f0a0379

.field public static cellArrow:I = 0x7f0a03a2

.field public static cellSocialNetwork:I = 0x7f0a03d1

.field public static center:I = 0x7f0a03f4

.field public static centralIcon:I = 0x7f0a0400

.field public static championship_new:I = 0x7f0a041c

.field public static championship_popular:I = 0x7f0a041d

.field public static check:I = 0x7f0a042d

.field public static checkbox:I = 0x7f0a0433

.field public static checked:I = 0x7f0a0437

.field public static checker:I = 0x7f0a0438

.field public static checking:I = 0x7f0a0439

.field public static chevron:I = 0x7f0a043f

.field public static chipsContainer:I = 0x7f0a044f

.field public static circleSocialNetwork:I = 0x7f0a0469

.field public static clear:I = 0x7f0a04ee

.field public static coefficientContainer:I = 0x7f0a0527

.field public static coefficientTv:I = 0x7f0a0528

.field public static coefficientValueTv:I = 0x7f0a052a

.field public static commerce:I = 0x7f0a054c

.field public static composeView:I = 0x7f0a054f

.field public static constraintLayout:I = 0x7f0a055a

.field public static container:I = 0x7f0a0561

.field public static containerButton:I = 0x7f0a0562

.field public static containerFlow:I = 0x7f0a0568

.field public static content:I = 0x7f0a057b

.field public static contentContainer:I = 0x7f0a0580

.field public static continueB:I = 0x7f0a058c

.field public static counter:I = 0x7f0a05a0

.field public static countryCodeIcon:I = 0x7f0a05ab

.field public static countryCodeTextField:I = 0x7f0a05ac

.field public static countryCodeTextView:I = 0x7f0a05ad

.field public static coupon:I = 0x7f0a05b2

.field public static cross:I = 0x7f0a05c9

.field public static currency:I = 0x7f0a05db

.field public static custom:I = 0x7f0a05e2

.field public static dateOrOnlyYear:I = 0x7f0a0601

.field public static dateTv:I = 0x7f0a0608

.field public static defaultType:I = 0x7f0a0633

.field public static description:I = 0x7f0a063d

.field public static descriptionTextView:I = 0x7f0a0640

.field public static divider:I = 0x7f0a0674

.field public static dividerBottom:I = 0x7f0a0677

.field public static dividerTop:I = 0x7f0a067a

.field public static dot:I = 0x7f0a0687

.field public static down:I = 0x7f0a068c

.field public static drawable:I = 0x7f0a06a1

.field public static dsAccountInfoLarge:I = 0x7f0a06a5

.field public static dsAmountCurrency:I = 0x7f0a06a7

.field public static dsButton:I = 0x7f0a06a8

.field public static dsButtonFirst:I = 0x7f0a06aa

.field public static dsButtonSecond:I = 0x7f0a06ab

.field public static dsButtonThird:I = 0x7f0a06ac

.field public static dsFilterContainer:I = 0x7f0a06ad

.field public static dsNavigationBarTitle:I = 0x7f0a06af

.field public static dsToolbarSearchField:I = 0x7f0a06b0

.field public static dsToolbarSearchFieldStatic:I = 0x7f0a06b1

.field public static empty:I = 0x7f0a06cf

.field public static errorIcon:I = 0x7f0a073e

.field public static extraSmall:I = 0x7f0a07a4

.field public static favorite_button_uitest:I = 0x7f0a07bd

.field public static filled:I = 0x7f0a07d7

.field public static filledIcon:I = 0x7f0a07d8

.field public static filledMultilineTextField:I = 0x7f0a07d9

.field public static filledStepper:I = 0x7f0a07da

.field public static filledTextField:I = 0x7f0a07db

.field public static fivefold:I = 0x7f0a0862

.field public static flAction:I = 0x7f0a086b

.field public static guideline:I = 0x7f0a0a05

.field public static headerLarge:I = 0x7f0a0a72

.field public static headerSmall:I = 0x7f0a0a79

.field public static headline:I = 0x7f0a0a83

.field public static higher:I = 0x7f0a0a99

.field public static historyB:I = 0x7f0a0a9f

.field public static icSocial:I = 0x7f0a0ac6

.field public static ic_promocode:I = 0x7f0a0ac7

.field public static icon:I = 0x7f0a0ac8

.field public static iconCircle:I = 0x7f0a0ace

.field public static iconImageView:I = 0x7f0a0acf

.field public static iconIv:I = 0x7f0a0ad1

.field public static iconLayout:I = 0x7f0a0ad2

.field public static iconRectangle:I = 0x7f0a0ad3

.field public static iconStyleContainer:I = 0x7f0a0ad5

.field public static iconView:I = 0x7f0a0ad6

.field public static image:I = 0x7f0a0ae2

.field public static imageView:I = 0x7f0a0af6

.field public static inactive:I = 0x7f0a0b88

.field public static indeterminate:I = 0x7f0a0b8c

.field public static infoIcon:I = 0x7f0a0b99

.field public static islandContainer:I = 0x7f0a0bbc

.field public static ivIcon:I = 0x7f0a0c83

.field public static ivInfo:I = 0x7f0a0c88

.field public static ivPromo:I = 0x7f0a0cd7

.field public static ivSocial:I = 0x7f0a0d15

.field public static label:I = 0x7f0a0dc4

.field public static labelIconLeft:I = 0x7f0a0dc5

.field public static labelIconRight:I = 0x7f0a0dc6

.field public static large:I = 0x7f0a0dcb

.field public static left:I = 0x7f0a0de4

.field public static leftIcon:I = 0x7f0a0de7

.field public static live:I = 0x7f0a0e4f

.field public static live_header:I = 0x7f0a0e59

.field public static loaded:I = 0x7f0a0ec2

.field public static loader:I = 0x7f0a0ec3

.field public static logo:I = 0x7f0a0ee3

.field public static logoCenter:I = 0x7f0a0ee4

.field public static logoLeft:I = 0x7f0a0ee6

.field public static logoRight:I = 0x7f0a0ee7

.field public static longTitle:I = 0x7f0a0ee8

.field public static lottie:I = 0x7f0a0eeb

.field public static lottieAnimationView:I = 0x7f0a0eec

.field public static lottieAv:I = 0x7f0a0eed

.field public static lottieEmptyView:I = 0x7f0a0eef

.field public static lottieView:I = 0x7f0a0ef4

.field public static lower:I = 0x7f0a0ef8

.field public static market_block:I = 0x7f0a0f1a

.field public static market_popular:I = 0x7f0a0f1c

.field public static market_track:I = 0x7f0a0f1d

.field public static masked:I = 0x7f0a0f24

.field public static mcSocialTitle:I = 0x7f0a0f4a

.field public static mcvPromocode:I = 0x7f0a0f4b

.field public static medium:I = 0x7f0a0f4e

.field public static middleContainer:I = 0x7f0a0f5f

.field public static middleSeparator:I = 0x7f0a0f61

.field public static minus:I = 0x7f0a0f6a

.field public static month:I = 0x7f0a0f76

.field public static name:I = 0x7f0a0fac

.field public static navigationBar:I = 0x7f0a0faf

.field public static navigationTabContainer:I = 0x7f0a0fb1

.field public static nestedScroll:I = 0x7f0a0fbd

.field public static nestedSeparator:I = 0x7f0a0fbf

.field public static no_background:I = 0x7f0a0fd8

.field public static none:I = 0x7f0a0fd9

.field public static not_loaded:I = 0x7f0a0fdf

.field public static notification_button_uitest:I = 0x7f0a0fe3

.field public static oneButton:I = 0x7f0a1005

.field public static overlay:I = 0x7f0a1043

.field public static overlayValue:I = 0x7f0a1044

.field public static pageControl:I = 0x7f0a1048

.field public static password:I = 0x7f0a105d

.field public static plus:I = 0x7f0a10d6

.field public static possibleWinContainer:I = 0x7f0a10e7

.field public static possibleWinSymbolTv:I = 0x7f0a10ed

.field public static possibleWinTv:I = 0x7f0a10ee

.field public static possibleWinValueTv:I = 0x7f0a10f0

.field public static preTitle:I = 0x7f0a10f2

.field public static pretitle:I = 0x7f0a10fe

.field public static primary:I = 0x7f0a1104

.field public static primaryValue:I = 0x7f0a1106

.field public static profileIcon:I = 0x7f0a1118

.field public static progressView:I = 0x7f0a112a

.field public static prominent_l:I = 0x7f0a1133

.field public static prominent_s:I = 0x7f0a1134

.field public static promocodeHeader:I = 0x7f0a114b

.field public static quaternary:I = 0x7f0a115f

.field public static quinary:I = 0x7f0a116d

.field public static rectangleHorizontal:I = 0x7f0a1191

.field public static rectangleHorizontalNoTitle:I = 0x7f0a1192

.field public static rectangleHorizontalSocialNetwork:I = 0x7f0a1193

.field public static rectangleVertical:I = 0x7f0a1194

.field public static rectangleVerticalNoTitle:I = 0x7f0a1195

.field public static rectangleVerticalSocialNetwork:I = 0x7f0a1196

.field public static refreshMainIcon:I = 0x7f0a11d1

.field public static refreshSmallIcon:I = 0x7f0a11d2

.field public static registrationButton:I = 0x7f0a11d5

.field public static rejected:I = 0x7f0a11da

.field public static requirements:I = 0x7f0a11ec

.field public static right:I = 0x7f0a1202

.field public static rightIcon:I = 0x7f0a1206

.field public static round_0:I = 0x7f0a1235

.field public static round_12:I = 0x7f0a1236

.field public static round_16:I = 0x7f0a1237

.field public static round_8:I = 0x7f0a1238

.field public static round_full:I = 0x7f0a1239

.field public static search:I = 0x7f0a130f

.field public static searchField:I = 0x7f0a1310

.field public static searchFieldStatic:I = 0x7f0a1311

.field public static searchFiled:I = 0x7f0a1312

.field public static secondary:I = 0x7f0a138e

.field public static secondaryText:I = 0x7f0a1390

.field public static secondaryValue:I = 0x7f0a1391

.field public static segmentedGroup:I = 0x7f0a13a6

.field public static senary:I = 0x7f0a13c3

.field public static separated_backgrounds:I = 0x7f0a13c6

.field public static separator:I = 0x7f0a13c7

.field public static shimmer:I = 0x7f0a1400

.field public static shimmerContent:I = 0x7f0a1417

.field public static shimmerViewButton:I = 0x7f0a1482

.field public static shimmerViewTitle:I = 0x7f0a1487

.field public static singleTitle:I = 0x7f0a14a8

.field public static skeletonOverlay:I = 0x7f0a14b2

.field public static skeletonTheme:I = 0x7f0a14b3

.field public static small:I = 0x7f0a14c8

.field public static snackBarView:I = 0x7f0a14d3

.field public static space:I = 0x7f0a158e

.field public static squareL:I = 0x7f0a15c0

.field public static squareLNoTitle:I = 0x7f0a15c1

.field public static squareS:I = 0x7f0a15c2

.field public static squareSNoTitle:I = 0x7f0a15c3

.field public static squareSocialNetwork:I = 0x7f0a15c4

.field public static standard:I = 0x7f0a15d6

.field public static staticColors:I = 0x7f0a15f3

.field public static static_white:I = 0x7f0a15f7

.field public static statical:I = 0x7f0a15f8

.field public static status:I = 0x7f0a160f

.field public static statusIcon:I = 0x7f0a1614

.field public static stepper:I = 0x7f0a1619

.field public static stream_button_uitest:I = 0x7f0a161f

.field public static subTitleTv:I = 0x7f0a162a

.field public static subtitle:I = 0x7f0a163b

.field public static successBetInfoView:I = 0x7f0a163d

.field public static successBetRootV:I = 0x7f0a163e

.field public static tabContainer:I = 0x7f0a1670

.field public static tag:I = 0x7f0a168e

.field public static tertiary:I = 0x7f0a1709

.field public static tertiaryValue:I = 0x7f0a170a

.field public static text:I = 0x7f0a170c

.field public static textButton:I = 0x7f0a1715

.field public static textFieldBasic:I = 0x7f0a171c

.field public static textFieldFilled:I = 0x7f0a171d

.field public static textInputEditText:I = 0x7f0a1727

.field public static textInputLayout:I = 0x7f0a1728

.field public static textViewCenter:I = 0x7f0a1733

.field public static textViewLeft:I = 0x7f0a1742

.field public static textViewRight:I = 0x7f0a1749

.field public static texts:I = 0x7f0a1764

.field public static theme:I = 0x7f0a1791

.field public static themeColors:I = 0x7f0a1792

.field public static threeTime:I = 0x7f0a17ba

.field public static threeVerticalButtons:I = 0x7f0a17bb

.field public static title:I = 0x7f0a1808

.field public static titleTextView:I = 0x7f0a181d

.field public static titleTv:I = 0x7f0a181f

.field public static titleValue:I = 0x7f0a1820

.field public static topContainer:I = 0x7f0a185b

.field public static topSeparator:I = 0x7f0a1880

.field public static topSpace:I = 0x7f0a1884

.field public static topSubtitle:I = 0x7f0a1886

.field public static topUpButton:I = 0x7f0a188e

.field public static topView:I = 0x7f0a1897

.field public static tvActionLabel:I = 0x7f0a1922

.field public static tvAdd:I = 0x7f0a1927

.field public static tvCaption:I = 0x7f0a1995

.field public static tvCountDownMessage:I = 0x7f0a19e8

.field public static tvMessage:I = 0x7f0a1b1e

.field public static tvPromoText:I = 0x7f0a1bb2

.field public static tvSubTitle:I = 0x7f0a1c5e

.field public static tvTitle:I = 0x7f0a1cac

.field public static twoHorizontalButtons:I = 0x7f0a1db2

.field public static twoTime:I = 0x7f0a1db4

.field public static twoTimeExtended:I = 0x7f0a1db5

.field public static twoVerticalButtons:I = 0x7f0a1db6

.field public static uiKitBannerShapeableImageViewBackground:I = 0x7f0a1dd4

.field public static uiKitBannerShimmerView:I = 0x7f0a1dd5

.field public static uiKitPromoBannerData:I = 0x7f0a1dd6

.field public static uiKitPromoBannerLabelText:I = 0x7f0a1dd7

.field public static uiKitPromoBannerValueText:I = 0x7f0a1dd8

.field public static uikitCategoryCardLabel:I = 0x7f0a1ddd

.field public static uikitCategoryCardPicture:I = 0x7f0a1dde

.field public static unchecked:I = 0x7f0a1ddf

.field public static up:I = 0x7f0a1de7

.field public static update:I = 0x7f0a1de9

.field public static update_small:I = 0x7f0a1dec

.field public static value:I = 0x7f0a1ef3

.field public static viewBackground:I = 0x7f0a1f2a

.field public static visible:I = 0x7f0a1fb2

.field public static wallet:I = 0x7f0a1fc4

.field public static warning:I = 0x7f0a1fca

.field public static warningStatic:I = 0x7f0a1fcb

.field public static warning_orange:I = 0x7f0a1fcd

.field public static warning_red:I = 0x7f0a1fce

.field public static warning_yellow:I = 0x7f0a1fcf

.field public static whole_background:I = 0x7f0a1fea

.field public static year:I = 0x7f0a201a


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
