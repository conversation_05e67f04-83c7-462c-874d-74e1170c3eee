.class public Lh4/g;
.super Lh4/h;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lh4/h<",
        "Lcom/github/mikephil/charting/charts/Pie<PERSON>hart;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>(Lcom/github/mikephil/charting/charts/PieChart;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lh4/h;-><init>(Lcom/github/mikephil/charting/charts/PieRadarChartBase;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public b(IFF)Lh4/d;
    .locals 9

    .line 1
    iget-object v0, p0, Lh4/h;->a:Lcom/github/mikephil/charting/charts/PieRadarChartBase;

    .line 2
    .line 3
    check-cast v0, Lcom/github/mikephil/charting/charts/PieChart;

    .line 4
    .line 5
    invoke-virtual {v0}, Lcom/github/mikephil/charting/charts/Chart;->getData()Lf4/h;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    check-cast v0, Lf4/m;

    .line 10
    .line 11
    invoke-virtual {v0}, Lf4/m;->y()Lj4/i;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-interface {v0, p1}, Lj4/e;->i(I)Lcom/github/mikephil/charting/data/Entry;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    new-instance v2, Lh4/d;

    .line 20
    .line 21
    int-to-float v3, p1

    .line 22
    invoke-virtual {v1}, Lf4/e;->c()F

    .line 23
    .line 24
    .line 25
    move-result v4

    .line 26
    const/4 v7, 0x0

    .line 27
    invoke-interface {v0}, Lj4/e;->n0()Lcom/github/mikephil/charting/components/YAxis$AxisDependency;

    .line 28
    .line 29
    .line 30
    move-result-object v8

    .line 31
    move v5, p2

    .line 32
    move v6, p3

    .line 33
    invoke-direct/range {v2 .. v8}, Lh4/d;-><init>(FFFFILcom/github/mikephil/charting/components/YAxis$AxisDependency;)V

    .line 34
    .line 35
    .line 36
    return-object v2
.end method
