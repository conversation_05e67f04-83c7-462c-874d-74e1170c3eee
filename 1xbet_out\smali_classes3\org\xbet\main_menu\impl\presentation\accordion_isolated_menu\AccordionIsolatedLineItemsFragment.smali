.class public final Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0000\u0018\u0000 32\u00020\u0001:\u00014B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u001f\u0010\n\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0013\u0010\r\u001a\u00020\u0004*\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0019\u0010\u0011\u001a\u00020\u00042\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u000fH\u0016\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u000f\u0010\u0013\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0013\u0010\u0003J\u000f\u0010\u0014\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0014\u0010\u0003J\u0019\u0010\u0015\u001a\u00020\u00042\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u000fH\u0014\u00a2\u0006\u0004\u0008\u0015\u0010\u0012J\u000f\u0010\u0016\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0003R\u001a\u0010\u001c\u001a\u00020\u00178\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008\u0018\u0010\u0019\u001a\u0004\u0008\u001a\u0010\u001bR\u001b\u0010\"\u001a\u00020\u001d8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u001e\u0010\u001f\u001a\u0004\u0008 \u0010!R\u001b\u0010(\u001a\u00020#8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008$\u0010%\u001a\u0004\u0008&\u0010\'R\u001b\u0010-\u001a\u00020)8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008*\u0010%\u001a\u0004\u0008+\u0010,R\u001b\u00102\u001a\u00020.8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008/\u0010%\u001a\u0004\u00080\u00101\u00a8\u00065"
    }
    d2 = {
        "Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "Z2",
        "",
        "message",
        "Ly01/i;",
        "type",
        "a3",
        "(Ljava/lang/String;Ly01/i;)V",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "R2",
        "(Landroidx/recyclerview/widget/RecyclerView;)V",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onCreate",
        "(Landroid/os/Bundle;)V",
        "onResume",
        "onDestroyView",
        "t2",
        "v2",
        "",
        "i0",
        "Z",
        "r2",
        "()Z",
        "showNavBar",
        "Lv80/b;",
        "j0",
        "LRc/c;",
        "U2",
        "()Lv80/b;",
        "binding",
        "LJ80/a;",
        "k0",
        "Lkotlin/j;",
        "T2",
        "()LJ80/a;",
        "adapter",
        "Lx80/c;",
        "l0",
        "V2",
        "()Lx80/c;",
        "component",
        "Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;",
        "m0",
        "W2",
        "()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;",
        "viewModel",
        "n0",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final n0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic o0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final i0:Z

.field public final j0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getBinding()Lorg/xbet/main_menu/impl/databinding/FragmentAccordionMenuBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    .line 7
    .line 8
    const-string v4, "binding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->o0:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->n0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$a;

    .line 31
    .line 32
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, Ls80/b;->fragment_accordion_menu:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    const/4 v0, 0x1

    .line 7
    iput-boolean v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->i0:Z

    .line 8
    .line 9
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$binding$2;->INSTANCE:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$binding$2;

    .line 10
    .line 11
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->j0:LRc/c;

    .line 16
    .line 17
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/a;

    .line 18
    .line 19
    invoke-direct {v0, p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/a;-><init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)V

    .line 20
    .line 21
    .line 22
    sget-object v1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 23
    .line 24
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->k0:Lkotlin/j;

    .line 29
    .line 30
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/b;

    .line 31
    .line 32
    invoke-direct {v0, p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/b;-><init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)V

    .line 33
    .line 34
    .line 35
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->l0:Lkotlin/j;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/c;

    .line 42
    .line 43
    invoke-direct {v0, p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/c;-><init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)V

    .line 44
    .line 45
    .line 46
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$b;

    .line 47
    .line 48
    invoke-direct {v2, p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$b;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 49
    .line 50
    .line 51
    new-instance v3, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$c;

    .line 52
    .line 53
    invoke-direct {v3, v0, v2}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$c;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V

    .line 54
    .line 55
    .line 56
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$special$$inlined$savedStateViewModels$default$3;

    .line 57
    .line 58
    invoke-direct {v0, p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$special$$inlined$savedStateViewModels$default$3;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 59
    .line 60
    .line 61
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$special$$inlined$savedStateViewModels$default$4;

    .line 62
    .line 63
    invoke-direct {v2, v0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$special$$inlined$savedStateViewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 64
    .line 65
    .line 66
    invoke-static {v1, v2}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    const-class v1, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 71
    .line 72
    invoke-static {v1}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 73
    .line 74
    .line 75
    move-result-object v1

    .line 76
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$special$$inlined$savedStateViewModels$default$5;

    .line 77
    .line 78
    invoke-direct {v2, v0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$special$$inlined$savedStateViewModels$default$5;-><init>(Lkotlin/j;)V

    .line 79
    .line 80
    .line 81
    new-instance v4, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$special$$inlined$savedStateViewModels$default$6;

    .line 82
    .line 83
    const/4 v5, 0x0

    .line 84
    invoke-direct {v4, v5, v0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$special$$inlined$savedStateViewModels$default$6;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 85
    .line 86
    .line 87
    invoke-static {p0, v1, v2, v4, v3}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->m0:Lkotlin/j;

    .line 92
    .line 93
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;LN80/c$v;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->Q2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;LN80/c$v;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;Ln41/m;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->N2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;Ln41/m;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic C2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)LJ80/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->L2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)LJ80/a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic D2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;LN80/c;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->M2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;LN80/c;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic E2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;LN80/c$w;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->P2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;LN80/c$w;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic F2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)Lx80/c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->S2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)Lx80/c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic G2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;LN80/c$u;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->O2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;LN80/c$u;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic H2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)Lv80/b;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->U2()Lv80/b;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic I2(LJ80/a;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->Y2(LJ80/a;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic J2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->Z2()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic K2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;Ljava/lang/String;Ly01/i;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->a3(Ljava/lang/String;Ly01/i;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final L2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)LJ80/a;
    .locals 8

    .line 1
    new-instance v0, LJ80/a;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/e;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/e;-><init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)V

    .line 6
    .line 7
    .line 8
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/f;

    .line 9
    .line 10
    invoke-direct {v2, p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/f;-><init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)V

    .line 11
    .line 12
    .line 13
    new-instance v3, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/g;

    .line 14
    .line 15
    invoke-direct {v3, p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/g;-><init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)V

    .line 16
    .line 17
    .line 18
    new-instance v4, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/h;

    .line 19
    .line 20
    invoke-direct {v4, p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/h;-><init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)V

    .line 21
    .line 22
    .line 23
    new-instance v5, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/i;

    .line 24
    .line 25
    invoke-direct {v5, p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/i;-><init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)V

    .line 26
    .line 27
    .line 28
    new-instance v6, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$adapter$2$6;

    .line 29
    .line 30
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->W2()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 31
    .line 32
    .line 33
    move-result-object v7

    .line 34
    invoke-direct {v6, v7}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$adapter$2$6;-><init>(Ljava/lang/Object;)V

    .line 35
    .line 36
    .line 37
    new-instance v7, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$adapter$2$7;

    .line 38
    .line 39
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->W2()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 40
    .line 41
    .line 42
    move-result-object p0

    .line 43
    invoke-direct {v7, p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$adapter$2$7;-><init>(Ljava/lang/Object;)V

    .line 44
    .line 45
    .line 46
    invoke-direct/range {v0 .. v7}, LJ80/a;-><init>(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V

    .line 47
    .line 48
    .line 49
    return-object v0
.end method

.method public static final M2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;LN80/c;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->W2()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    .line 6
    .line 7
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, v0, p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->B4(Lkotlin/reflect/d;LN80/c;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final N2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;Ln41/m;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->W2()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    .line 6
    .line 7
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, v0, p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->N4(Lkotlin/reflect/d;Ln41/m;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final O2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;LN80/c$u;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->W2()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    .line 6
    .line 7
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, v0, p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->J4(Lkotlin/reflect/d;LN80/c$u;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final P2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;LN80/c$w;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->W2()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    .line 6
    .line 7
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, v0, p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->M4(Lkotlin/reflect/d;LN80/c$w;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final Q2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;LN80/c$v;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->W2()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    .line 6
    .line 7
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, v0, p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->L4(Lkotlin/reflect/d;LN80/c$v;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method private final R2(Landroidx/recyclerview/widget/RecyclerView;)V
    .locals 18

    .line 1
    move-object/from16 v0, p1

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget v2, LlZ0/g;->space_8:I

    .line 8
    .line 9
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 10
    .line 11
    .line 12
    move-result v5

    .line 13
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    sget v2, LlZ0/g;->radius_16:I

    .line 18
    .line 19
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 20
    .line 21
    .line 22
    move-result v12

    .line 23
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    sget v2, LlZ0/g;->large_horizontal_margin_dynamic:I

    .line 28
    .line 29
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 30
    .line 31
    .line 32
    move-result v6

    .line 33
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    sget v2, LlZ0/g;->space_12:I

    .line 38
    .line 39
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 40
    .line 41
    .line 42
    move-result v8

    .line 43
    new-instance v1, LQZ0/a;

    .line 44
    .line 45
    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 46
    .line 47
    .line 48
    move-result-object v2

    .line 49
    sget v3, LlZ0/d;->uikitBackgroundContent:I

    .line 50
    .line 51
    const/4 v4, 0x0

    .line 52
    const/4 v15, 0x2

    .line 53
    invoke-static {v2, v3, v4, v15, v4}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 54
    .line 55
    .line 56
    move-result v10

    .line 57
    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 58
    .line 59
    .line 60
    move-result-object v2

    .line 61
    sget v3, LlZ0/d;->uikitBackground:I

    .line 62
    .line 63
    invoke-static {v2, v3, v4, v15, v4}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 64
    .line 65
    .line 66
    move-result v11

    .line 67
    const/4 v2, 0x3

    .line 68
    new-array v3, v2, [Ljava/lang/Class;

    .line 69
    .line 70
    const-class v4, LN80/c$f;

    .line 71
    .line 72
    const/16 v16, 0x0

    .line 73
    .line 74
    aput-object v4, v3, v16

    .line 75
    .line 76
    const-class v7, LN80/c$e;

    .line 77
    .line 78
    const/16 v17, 0x1

    .line 79
    .line 80
    aput-object v7, v3, v17

    .line 81
    .line 82
    const-class v9, LN80/c$g;

    .line 83
    .line 84
    aput-object v9, v3, v15

    .line 85
    .line 86
    invoke-static {v3}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 87
    .line 88
    .line 89
    move-result-object v13

    .line 90
    const/16 v3, 0xb

    .line 91
    .line 92
    new-array v3, v3, [Ljava/lang/Class;

    .line 93
    .line 94
    const-class v14, LN80/c$a;

    .line 95
    .line 96
    aput-object v14, v3, v16

    .line 97
    .line 98
    const-class v14, LN80/c$r;

    .line 99
    .line 100
    aput-object v14, v3, v17

    .line 101
    .line 102
    const-class v14, LN80/c$w;

    .line 103
    .line 104
    aput-object v14, v3, v15

    .line 105
    .line 106
    const-class v14, LN80/c$i;

    .line 107
    .line 108
    aput-object v14, v3, v2

    .line 109
    .line 110
    const/4 v2, 0x4

    .line 111
    aput-object v7, v3, v2

    .line 112
    .line 113
    const-class v2, LN80/c$x;

    .line 114
    .line 115
    const/4 v7, 0x5

    .line 116
    aput-object v2, v3, v7

    .line 117
    .line 118
    const/4 v2, 0x6

    .line 119
    aput-object v4, v3, v2

    .line 120
    .line 121
    const-class v2, LN80/c$b;

    .line 122
    .line 123
    const/4 v4, 0x7

    .line 124
    aput-object v2, v3, v4

    .line 125
    .line 126
    const-class v2, LN80/c$v;

    .line 127
    .line 128
    const/16 v4, 0x8

    .line 129
    .line 130
    aput-object v2, v3, v4

    .line 131
    .line 132
    const/16 v2, 0x9

    .line 133
    .line 134
    aput-object v9, v3, v2

    .line 135
    .line 136
    const-class v2, LN80/c$d;

    .line 137
    .line 138
    const/16 v4, 0xa

    .line 139
    .line 140
    aput-object v2, v3, v4

    .line 141
    .line 142
    invoke-static {v3}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 143
    .line 144
    .line 145
    move-result-object v4

    .line 146
    sget-object v14, Lorg/xbet/uikit/components/cells/BaseCell$RoundCornersType;->ROUND_12:Lorg/xbet/uikit/components/cells/BaseCell$RoundCornersType;

    .line 147
    .line 148
    new-instance v3, LSZ0/a$a;

    .line 149
    .line 150
    move v7, v5

    .line 151
    move v9, v8

    .line 152
    invoke-direct/range {v3 .. v14}, LSZ0/a$a;-><init>(Ljava/util/List;IIIIIIIILjava/util/List;Lorg/xbet/uikit/components/cells/BaseCell$RoundCornersType;)V

    .line 153
    .line 154
    .line 155
    invoke-direct {v1, v3}, LQZ0/a;-><init>(LSZ0/a$a;)V

    .line 156
    .line 157
    .line 158
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 159
    .line 160
    .line 161
    new-instance v3, LL80/a;

    .line 162
    .line 163
    new-array v1, v15, [Ljava/lang/Class;

    .line 164
    .line 165
    const-class v2, LN80/c$c;

    .line 166
    .line 167
    aput-object v2, v1, v16

    .line 168
    .line 169
    const-class v2, LN80/c$s;

    .line 170
    .line 171
    aput-object v2, v1, v17

    .line 172
    .line 173
    invoke-static {v1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 174
    .line 175
    .line 176
    move-result-object v4

    .line 177
    const/4 v8, 0x0

    .line 178
    move v7, v6

    .line 179
    invoke-direct/range {v3 .. v8}, LL80/a;-><init>(Ljava/util/List;IIII)V

    .line 180
    .line 181
    .line 182
    invoke-virtual {v0, v3}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 183
    .line 184
    .line 185
    return-void
.end method

.method public static final S2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)Lx80/c;
    .locals 4

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    instance-of v1, v0, LQW0/b;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    check-cast v0, LQW0/b;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    move-object v0, v2

    .line 18
    :goto_0
    const-class v1, Lx80/a;

    .line 19
    .line 20
    if-eqz v0, :cond_3

    .line 21
    .line 22
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, LBc/a;

    .line 31
    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, LQW0/a;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-object v0, v2

    .line 42
    :goto_1
    instance-of v3, v0, Lx80/a;

    .line 43
    .line 44
    if-nez v3, :cond_2

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_2
    move-object v2, v0

    .line 48
    :goto_2
    check-cast v2, Lx80/a;

    .line 49
    .line 50
    if-eqz v2, :cond_3

    .line 51
    .line 52
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 53
    .line 54
    .line 55
    move-result-object p0

    .line 56
    const/4 v0, 0x1

    .line 57
    invoke-virtual {v2, p0, v0}, Lx80/a;->a(LwX0/c;Z)Lx80/c;

    .line 58
    .line 59
    .line 60
    move-result-object p0

    .line 61
    return-object p0

    .line 62
    :cond_3
    new-instance p0, Ljava/lang/IllegalStateException;

    .line 63
    .line 64
    new-instance v0, Ljava/lang/StringBuilder;

    .line 65
    .line 66
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 67
    .line 68
    .line 69
    const-string v2, "Cannot create dependency "

    .line 70
    .line 71
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 72
    .line 73
    .line 74
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 75
    .line 76
    .line 77
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 82
    .line 83
    .line 84
    move-result-object v0

    .line 85
    invoke-direct {p0, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 86
    .line 87
    .line 88
    throw p0
.end method

.method private final U2()Lv80/b;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->j0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->o0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lv80/b;

    .line 13
    .line 14
    return-object v0
.end method

.method private final V2()Lx80/c;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->l0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lx80/c;

    .line 8
    .line 9
    return-object v0
.end method

.method private final W2()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->m0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final X2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->W2()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->K4()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public static final synthetic Y2(LJ80/a;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final Z2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->V2()Lx80/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-interface {v1}, Lx80/c;->c()LTZ0/a;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    new-instance v2, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 12
    .line 13
    sget v3, Lpb/k;->attention:I

    .line 14
    .line 15
    invoke-virtual {v0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v3

    .line 19
    sget v4, Lpb/k;->country_blocking:I

    .line 20
    .line 21
    invoke-virtual {v0, v4}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v4

    .line 25
    sget v5, Lpb/k;->ok_new:I

    .line 26
    .line 27
    invoke-virtual {v0, v5}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object v5

    .line 31
    sget-object v13, Lorg/xbet/uikit/components/dialog/AlertType;->WARNING:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 32
    .line 33
    const/16 v15, 0xbf8

    .line 34
    .line 35
    const/16 v16, 0x0

    .line 36
    .line 37
    const/4 v6, 0x0

    .line 38
    const/4 v7, 0x0

    .line 39
    const/4 v8, 0x0

    .line 40
    const/4 v9, 0x0

    .line 41
    const/4 v10, 0x0

    .line 42
    const/4 v11, 0x0

    .line 43
    const/4 v12, 0x0

    .line 44
    const/4 v14, 0x0

    .line 45
    invoke-direct/range {v2 .. v16}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 46
    .line 47
    .line 48
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 49
    .line 50
    .line 51
    move-result-object v3

    .line 52
    invoke-virtual {v1, v2, v3}, LTZ0/a;->d(Lorg/xbet/uikit/components/dialog/DialogFields;Landroidx/fragment/app/FragmentManager;)V

    .line 53
    .line 54
    .line 55
    return-void
.end method

.method private final a3(Ljava/lang/String;Ly01/i;)V
    .locals 12

    .line 1
    new-instance v0, Ly01/g;

    .line 2
    .line 3
    const/16 v7, 0x3c

    .line 4
    .line 5
    const/4 v8, 0x0

    .line 6
    const/4 v3, 0x0

    .line 7
    const/4 v4, 0x0

    .line 8
    const/4 v5, 0x0

    .line 9
    const/4 v6, 0x0

    .line 10
    move-object v2, p1

    .line 11
    move-object v1, p2

    .line 12
    invoke-direct/range {v0 .. v8}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 13
    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->V2()Lx80/c;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-interface {p1}, Lx80/c;->b()LzX0/k;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    const/16 v10, 0x1fc

    .line 24
    .line 25
    const/4 v11, 0x0

    .line 26
    const/4 v5, 0x0

    .line 27
    const/4 v6, 0x0

    .line 28
    const/4 v7, 0x0

    .line 29
    const/4 v8, 0x0

    .line 30
    const/4 v9, 0x0

    .line 31
    move-object v2, p0

    .line 32
    move-object v1, v0

    .line 33
    move-object v0, p1

    .line 34
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method public static final b3(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)Lorg/xbet/ui_common/viewmodel/core/e;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->V2()Lx80/c;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-interface {p0}, Lx80/c;->a()Lx80/M;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    return-object p0
.end method

.method public static synthetic y2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)Lorg/xbet/ui_common/viewmodel/core/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->b3(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)Lorg/xbet/ui_common/viewmodel/core/e;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->X2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;Ljava/lang/String;Landroid/os/Bundle;)V

    return-void
.end method


# virtual methods
.method public final T2()LJ80/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LJ80/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .locals 2

    .line 1
    invoke-super {p0, p1}, LXW0/a;->onCreate(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/d;

    .line 9
    .line 10
    invoke-direct {v0, p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/d;-><init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)V

    .line 11
    .line 12
    .line 13
    const-string v1, "UPDATED_BALANCE_KEY"

    .line 14
    .line 15
    invoke-virtual {p1, v1, p0, v0}, Landroidx/fragment/app/FragmentManager;->L1(Ljava/lang/String;Landroidx/lifecycle/w;Landroidx/fragment/app/J;)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public onDestroyView()V
    .locals 2

    .line 1
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->U2()Lv80/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lv80/b;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 9
    .line 10
    .line 11
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroyView()V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public onResume()V
    .locals 1

    .line 1
    invoke-super {p0}, LXW0/a;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->W2()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->K4()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public r2()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->i0:Z

    .line 2
    .line 3
    return v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 1

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->U2()Lv80/b;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object p1, p1, Lv80/b;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 9
    .line 10
    const/4 v0, 0x0

    .line 11
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setItemAnimator(Landroidx/recyclerview/widget/RecyclerView$m;)V

    .line 12
    .line 13
    .line 14
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->U2()Lv80/b;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    iget-object p1, p1, Lv80/b;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 19
    .line 20
    const/4 v0, 0x1

    .line 21
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setHasFixedSize(Z)V

    .line 22
    .line 23
    .line 24
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->U2()Lv80/b;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    iget-object p1, p1, Lv80/b;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 29
    .line 30
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->T2()LJ80/a;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 35
    .line 36
    .line 37
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->U2()Lv80/b;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    iget-object p1, p1, Lv80/b;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 42
    .line 43
    invoke-direct {p0, p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->R2(Landroidx/recyclerview/widget/RecyclerView;)V

    .line 44
    .line 45
    .line 46
    return-void
.end method

.method public v2()V
    .locals 19

    .line 1
    invoke-super/range {p0 .. p0}, LXW0/a;->v2()V

    .line 2
    .line 3
    .line 4
    invoke-direct/range {p0 .. p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->W2()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    const/16 v1, 0xc

    .line 9
    .line 10
    new-array v1, v1, [Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 11
    .line 12
    sget-object v2, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;->SPORTS:Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 13
    .line 14
    const/4 v3, 0x0

    .line 15
    aput-object v2, v1, v3

    .line 16
    .line 17
    sget-object v2, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;->SINGLE_SPORTS:Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 18
    .line 19
    const/4 v3, 0x1

    .line 20
    aput-object v2, v1, v3

    .line 21
    .line 22
    sget-object v2, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;->GAMES:Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 23
    .line 24
    const/4 v3, 0x2

    .line 25
    aput-object v2, v1, v3

    .line 26
    .line 27
    sget-object v2, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;->SINGLE_GAMES:Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 28
    .line 29
    const/4 v3, 0x3

    .line 30
    aput-object v2, v1, v3

    .line 31
    .line 32
    sget-object v2, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;->AGGREGATOR:Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 33
    .line 34
    const/4 v3, 0x4

    .line 35
    aput-object v2, v1, v3

    .line 36
    .line 37
    sget-object v2, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;->SINGLE_AGGREGATOR:Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 38
    .line 39
    const/4 v3, 0x5

    .line 40
    aput-object v2, v1, v3

    .line 41
    .line 42
    sget-object v2, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;->TOP:Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 43
    .line 44
    const/4 v3, 0x6

    .line 45
    aput-object v2, v1, v3

    .line 46
    .line 47
    sget-object v2, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;->SINGLE_TOP:Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 48
    .line 49
    const/4 v3, 0x7

    .line 50
    aput-object v2, v1, v3

    .line 51
    .line 52
    sget-object v2, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;->VIRTUAL:Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 53
    .line 54
    const/16 v3, 0x8

    .line 55
    .line 56
    aput-object v2, v1, v3

    .line 57
    .line 58
    sget-object v2, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;->SINGLE_VIRTUAL:Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 59
    .line 60
    const/16 v3, 0x9

    .line 61
    .line 62
    aput-object v2, v1, v3

    .line 63
    .line 64
    sget-object v2, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;->OTHER:Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 65
    .line 66
    const/16 v3, 0xa

    .line 67
    .line 68
    aput-object v2, v1, v3

    .line 69
    .line 70
    sget-object v2, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;->SINGLE_OTHER:Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 71
    .line 72
    const/16 v3, 0xb

    .line 73
    .line 74
    aput-object v2, v1, v3

    .line 75
    .line 76
    invoke-static {v1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 77
    .line 78
    .line 79
    move-result-object v1

    .line 80
    invoke-virtual {v0, v1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->f4(Ljava/util/List;)Lkotlinx/coroutines/flow/e;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    sget-object v3, Landroidx/lifecycle/Lifecycle$State;->CREATED:Landroidx/lifecycle/Lifecycle$State;

    .line 85
    .line 86
    new-instance v4, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$1;

    .line 87
    .line 88
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->T2()LJ80/a;

    .line 89
    .line 90
    .line 91
    move-result-object v0

    .line 92
    invoke-direct {v4, v0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$1;-><init>(Ljava/lang/Object;)V

    .line 93
    .line 94
    .line 95
    invoke-static/range {p0 .. p0}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 96
    .line 97
    .line 98
    move-result-object v6

    .line 99
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$$inlined$observeWithLifecycle$1;

    .line 100
    .line 101
    const/4 v5, 0x0

    .line 102
    move-object/from16 v2, p0

    .line 103
    .line 104
    invoke-direct/range {v0 .. v5}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$$inlined$observeWithLifecycle$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 105
    .line 106
    .line 107
    const/4 v9, 0x3

    .line 108
    const/4 v10, 0x0

    .line 109
    move-object v5, v6

    .line 110
    const/4 v6, 0x0

    .line 111
    const/4 v7, 0x0

    .line 112
    move-object v8, v0

    .line 113
    invoke-static/range {v5 .. v10}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 114
    .line 115
    .line 116
    invoke-direct {v2}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->W2()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 117
    .line 118
    .line 119
    move-result-object v0

    .line 120
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->E0()Lkotlinx/coroutines/flow/e;

    .line 121
    .line 122
    .line 123
    move-result-object v4

    .line 124
    new-instance v7, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;

    .line 125
    .line 126
    const/4 v0, 0x0

    .line 127
    invoke-direct {v7, v2, v0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;-><init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;Lkotlin/coroutines/e;)V

    .line 128
    .line 129
    .line 130
    sget-object v6, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 131
    .line 132
    invoke-static {v2}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 133
    .line 134
    .line 135
    move-result-object v5

    .line 136
    invoke-static {v5}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 137
    .line 138
    .line 139
    move-result-object v1

    .line 140
    new-instance v3, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 141
    .line 142
    const/4 v8, 0x0

    .line 143
    invoke-direct/range {v3 .. v8}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 144
    .line 145
    .line 146
    const/4 v12, 0x3

    .line 147
    const/4 v13, 0x0

    .line 148
    const/4 v9, 0x0

    .line 149
    move-object v8, v1

    .line 150
    move-object v11, v3

    .line 151
    invoke-static/range {v8 .. v13}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 152
    .line 153
    .line 154
    invoke-direct {v2}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->W2()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;

    .line 155
    .line 156
    .line 157
    move-result-object v1

    .line 158
    invoke-virtual {v1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel;->g4()Lkotlinx/coroutines/flow/e;

    .line 159
    .line 160
    .line 161
    move-result-object v9

    .line 162
    new-instance v12, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$3;

    .line 163
    .line 164
    invoke-direct {v12, v2, v0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$3;-><init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;Lkotlin/coroutines/e;)V

    .line 165
    .line 166
    .line 167
    invoke-static {v2}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 168
    .line 169
    .line 170
    move-result-object v10

    .line 171
    invoke-static {v10}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 172
    .line 173
    .line 174
    move-result-object v0

    .line 175
    new-instance v16, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 176
    .line 177
    move-object v11, v6

    .line 178
    move-object/from16 v8, v16

    .line 179
    .line 180
    invoke-direct/range {v8 .. v13}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 181
    .line 182
    .line 183
    const/16 v17, 0x3

    .line 184
    .line 185
    const/16 v18, 0x0

    .line 186
    .line 187
    const/4 v14, 0x0

    .line 188
    const/4 v15, 0x0

    .line 189
    move-object v13, v0

    .line 190
    invoke-static/range {v13 .. v18}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 191
    .line 192
    .line 193
    return-void
.end method
