.class public final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;LF81/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->x1:LF81/a;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->y1:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    return-void
.end method
