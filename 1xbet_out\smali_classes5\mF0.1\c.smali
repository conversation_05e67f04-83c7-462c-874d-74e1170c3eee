.class public final LmF0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LmF0/c$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "LiF0/c;",
        "Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;",
        "teamPagerModel",
        "LnF0/c;",
        "a",
        "(LiF0/c;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;)LnF0/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LiF0/c;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;)LnF0/c;
    .locals 3
    .param p0    # LiF0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LiF0/c;->a()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, LiF0/c;->b()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    new-instance v1, Ljava/util/ArrayList;

    .line 10
    .line 11
    const/16 v2, 0xa

    .line 12
    .line 13
    invoke-static {p0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 18
    .line 19
    .line 20
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 21
    .line 22
    .line 23
    move-result-object p0

    .line 24
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    if-eqz v2, :cond_0

    .line 29
    .line 30
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    check-cast v2, LiF0/a;

    .line 35
    .line 36
    invoke-static {v2, p1}, LmF0/b;->a(LiF0/a;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;)LnF0/b;

    .line 37
    .line 38
    .line 39
    move-result-object v2

    .line 40
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 41
    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_0
    sget-object p0, LmF0/c$a;->a:[I

    .line 45
    .line 46
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 47
    .line 48
    .line 49
    move-result p1

    .line 50
    aget p0, p0, p1

    .line 51
    .line 52
    const/4 p1, 0x1

    .line 53
    if-ne p0, p1, :cond_1

    .line 54
    .line 55
    sget p0, Lpb/g;->ic_arrow_team_right_direction:I

    .line 56
    .line 57
    goto :goto_1

    .line 58
    :cond_1
    sget p0, Lpb/g;->ic_arrow_team_left_direction:I

    .line 59
    .line 60
    :goto_1
    new-instance p1, LnF0/c;

    .line 61
    .line 62
    invoke-direct {p1, v0, v1, p0}, LnF0/c;-><init>(Ljava/lang/String;Ljava/util/List;I)V

    .line 63
    .line 64
    .line 65
    return-object p1
.end method
