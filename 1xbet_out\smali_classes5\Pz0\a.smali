.class public final LPz0/a;
.super LA4/e;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "LA4/e<",
        "LQz0/a;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0000\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "LPz0/a;",
        "LA4/e;",
        "LQz0/a;",
        "<init>",
        "()V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 1
    sget-object v0, LQz0/a;->b:LQz0/a$b;

    .line 2
    .line 3
    invoke-virtual {v0}, LQz0/a$b;->a()Landroidx/recyclerview/widget/i$f;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-direct {p0, v0}, LA4/e;-><init>(Landroidx/recyclerview/widget/i$f;)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 11
    .line 12
    invoke-static {}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/compressedcard/main/viewholders/CompressedCardCommonViewHolderKt;->g()LA4/c;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 17
    .line 18
    .line 19
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 20
    .line 21
    invoke-static {}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/compressedcard/main/viewholders/CompressedCardSingleGameViewHolderKt;->e()LA4/c;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 26
    .line 27
    .line 28
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 29
    .line 30
    invoke-static {}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/compressedcard/main/viewholders/CompressedCardMultiTeamsViewHolderKt;->e()LA4/c;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 35
    .line 36
    .line 37
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 38
    .line 39
    invoke-static {}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/compressedcard/main/viewholders/CompressedCardPeriodsViewHolderKt;->g()LA4/c;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 44
    .line 45
    .line 46
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 47
    .line 48
    invoke-static {}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/compressedcard/main/viewholders/CompressedFootballPeriodsViewHolderKt;->e()LA4/c;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 53
    .line 54
    .line 55
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 56
    .line 57
    invoke-static {}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/compressedcard/main/viewholders/CompressedMainCricketViewHolderKt;->g()LA4/c;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 62
    .line 63
    .line 64
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 65
    .line 66
    invoke-static {}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/compressedcard/main/viewholders/CompressedCardErrorViewHolderKt;->e()LA4/c;

    .line 67
    .line 68
    .line 69
    move-result-object v1

    .line 70
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 71
    .line 72
    .line 73
    return-void
.end method
