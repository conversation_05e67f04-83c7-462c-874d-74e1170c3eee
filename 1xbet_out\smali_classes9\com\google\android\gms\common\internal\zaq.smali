.class final Lcom/google/android/gms/common/internal/zaq;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/google/android/gms/common/internal/PendingResultUtil$ResultConverter;


# instance fields
.field public final synthetic a:Lcom/google/android/gms/common/api/Response;


# virtual methods
.method public final bridge synthetic a(Lcom/google/android/gms/common/api/Result;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/google/android/gms/common/internal/zaq;->a:Lcom/google/android/gms/common/api/Response;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lcom/google/android/gms/common/api/Response;->i(Lcom/google/android/gms/common/api/Result;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method
