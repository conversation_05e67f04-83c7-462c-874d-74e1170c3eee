.class public final synthetic LU01/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/views/LoadableImageView;

.field public final synthetic b:L<PERSON>lin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/views/LoadableImageView;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LU01/k;->a:Lorg/xbet/uikit/components/views/LoadableImageView;

    iput-object p2, p0, LU01/k;->b:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LU01/k;->a:Lorg/xbet/uikit/components/views/LoadableImageView;

    iget-object v1, p0, LU01/k;->b:Lkotlin/jvm/functions/Function1;

    check-cast p1, Landroid/graphics/drawable/Drawable;

    invoke-static {v0, v1, p1}, Lorg/xbet/uikit/components/views/LoadableImageView;->C(Lorg/xbet/uikit/components/views/LoadableImageView;Lkotlin/jvm/functions/Function1;Landroid/graphics/drawable/Drawable;)Z

    move-result p1

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method
