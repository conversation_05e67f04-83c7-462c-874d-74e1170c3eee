.class public final Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;
.super Landroid/view/ViewGroup;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u001f\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0017\n\u0002\u0010!\n\u0002\u0008\n\u0018\u0000 d2\u00020\u0001:\u0001TB\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u001d\u0010\u0010\u001a\u00020\n2\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\rH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u001d\u0010\u0014\u001a\u00020\n2\u000c\u0010\u0013\u001a\u0008\u0012\u0004\u0012\u00020\n0\u0012H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u001d\u0010\u0016\u001a\u00020\n2\u000c\u0010\u0013\u001a\u0008\u0012\u0004\u0012\u00020\n0\u0012H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0015J\u000f\u0010\u0017\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u000cJ\u0017\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u0018\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u000f\u0010\u001c\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u000cJ\u001f\u0010\u001f\u001a\u00020\u00192\u0006\u0010\u001d\u001a\u00020\u00062\u0006\u0010\u001e\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 J+\u0010\"\u001a\u00020\n2\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\r2\u000c\u0010!\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\rH\u0002\u00a2\u0006\u0004\u0008\"\u0010#J+\u0010$\u001a\u00020\n2\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\r2\u000c\u0010!\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\rH\u0002\u00a2\u0006\u0004\u0008$\u0010#J\u001d\u0010%\u001a\u00020\n2\u000c\u0010!\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\rH\u0002\u00a2\u0006\u0004\u0008%\u0010\u0011J\u000f\u0010&\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008&\u0010\u000cJ\u0017\u0010(\u001a\u00020\n2\u0006\u0010\'\u001a\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008(\u0010)J\u0019\u0010+\u001a\u0004\u0018\u00010\u00192\u0006\u0010*\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008+\u0010,J#\u0010.\u001a\u0008\u0012\u0004\u0012\u00020\u00190\r2\u000c\u0010-\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\rH\u0002\u00a2\u0006\u0004\u0008.\u0010/J\u001d\u00100\u001a\u0008\u0012\u0004\u0012\u00020\u00190\r2\u0006\u0010\u001e\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u00080\u00101J\u000f\u00102\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u00082\u0010\u000cJ\u000f\u00103\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u00083\u0010\u000cJ\u000f\u00104\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u00084\u0010\u000cJ\u001f\u00107\u001a\u00020\n2\u0006\u00105\u001a\u00020\u00062\u0006\u00106\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u00087\u00108J7\u0010?\u001a\u00020\n2\u0006\u0010:\u001a\u0002092\u0006\u0010;\u001a\u00020\u00062\u0006\u0010<\u001a\u00020\u00062\u0006\u0010=\u001a\u00020\u00062\u0006\u0010>\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008?\u0010@J?\u0010F\u001a\u00020\n2\u0006\u0010B\u001a\u00020A2\u0018\u0010D\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\n0C2\u000c\u0010E\u001a\u0008\u0012\u0004\u0012\u00020\n0\u0012H\u0000\u00a2\u0006\u0004\u0008F\u0010GJ\u0017\u0010I\u001a\u00020\n2\u0006\u0010H\u001a\u000209H\u0000\u00a2\u0006\u0004\u0008I\u0010JJ?\u0010M\u001a\u00020\n2\u000c\u0010K\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\r2\u0012\u0010L\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\r0\r2\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\rH\u0000\u00a2\u0006\u0004\u0008M\u0010NJ1\u0010O\u001a\u00020\n2\u000c\u0010K\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\r2\u0012\u0010L\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\r0\rH\u0000\u00a2\u0006\u0004\u0008O\u0010#J\u001d\u0010Q\u001a\u00020\n2\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\rH\u0000\u00a2\u0006\u0004\u0008P\u0010\u0011J#\u0010S\u001a\u00020\n2\u0012\u0010L\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\r0\rH\u0000\u00a2\u0006\u0004\u0008R\u0010\u0011R\u0016\u0010V\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008T\u0010UR\u001c\u0010K\u001a\u0008\u0012\u0004\u0012\u00020\u00190\r8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008W\u0010XR\"\u0010Z\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\r0\r8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008Y\u0010XR\u001c\u0010]\u001a\u0008\u0012\u0004\u0012\u00020\u00190[8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\\\u0010XR\u001c\u0010E\u001a\u0008\u0012\u0004\u0012\u00020\n0\u00128\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008^\u0010_R\u0016\u0010B\u001a\u00020A8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008`\u0010aR(\u0010D\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\n0C8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008b\u0010c\u00a8\u0006e"
    }
    d2 = {
        "Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;",
        "Landroid/view/ViewGroup;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "M",
        "()V",
        "",
        "LzT0/b;",
        "newCells",
        "G",
        "(Ljava/util/List;)V",
        "Lkotlin/Function0;",
        "onEnd",
        "j",
        "(Lkotlin/jvm/functions/Function0;)V",
        "r",
        "N",
        "index",
        "Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;",
        "l",
        "(I)Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;",
        "L",
        "row",
        "column",
        "n",
        "(II)Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;",
        "allCells",
        "B",
        "(Ljava/util/List;Ljava/util/List;)V",
        "E",
        "O",
        "D",
        "cellView",
        "w",
        "(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)V",
        "coordinate",
        "u",
        "(LzT0/b;)Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;",
        "coordinates",
        "v",
        "(Ljava/util/List;)Ljava/util/List;",
        "x",
        "(I)Ljava/util/List;",
        "q",
        "m",
        "z",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
        "gameType",
        "Lkotlin/Function2;",
        "onCellClick",
        "onEndAnimation",
        "y",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;)V",
        "enable",
        "t",
        "(Z)V",
        "cells",
        "winCells",
        "A",
        "(Ljava/util/List;Ljava/util/List;Ljava/util/List;)V",
        "p",
        "setCells$tile_matching_release",
        "setCells",
        "setWinCells$tile_matching_release",
        "setWinCells",
        "a",
        "I",
        "childSize",
        "b",
        "Ljava/util/List;",
        "c",
        "wins",
        "",
        "d",
        "toDisappearCells",
        "e",
        "Lkotlin/jvm/functions/Function0;",
        "f",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
        "g",
        "Lkotlin/jvm/functions/Function2;",
        "h",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final h:Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public a:I

.field public b:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public g:Lkotlin/jvm/functions/Function2;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->h:Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView$a;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/view/ViewGroup;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 6
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->c:Ljava/util/List;

    .line 7
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->d:Ljava/util/List;

    .line 8
    new-instance p1, LCT0/o;

    invoke-direct {p1}, LCT0/o;-><init>()V

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->e:Lkotlin/jvm/functions/Function0;

    .line 9
    sget-object p1, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->FRUIT_BLAST:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->f:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 10
    new-instance p1, LCT0/p;

    invoke-direct {p1}, LCT0/p;-><init>()V

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->g:Lkotlin/jvm/functions/Function2;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static final C(Lkotlin/jvm/internal/Ref$IntRef;Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;Ljava/util/List;Ljava/util/List;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget v0, p0, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 2
    .line 3
    add-int/lit8 v0, v0, 0x1

    .line 4
    .line 5
    iput v0, p0, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 6
    .line 7
    iget-object v0, p1, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->d:Ljava/util/List;

    .line 8
    .line 9
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    iget p0, p0, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 14
    .line 15
    if-ne v0, p0, :cond_0

    .line 16
    .line 17
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->D()V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p1, p2, p3}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->E(Ljava/util/List;Ljava/util/List;)V

    .line 21
    .line 22
    .line 23
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 24
    .line 25
    return-object p0
.end method

.method public static final F(Lkotlin/jvm/internal/Ref$IntRef;Lkotlin/jvm/internal/Ref$IntRef;Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget v0, p0, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 2
    .line 3
    add-int/lit8 v0, v0, 0x1

    .line 4
    .line 5
    iput v0, p0, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 6
    .line 7
    iget p0, p1, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 8
    .line 9
    if-ne p0, v0, :cond_0

    .line 10
    .line 11
    invoke-virtual {p2}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->N()V

    .line 12
    .line 13
    .line 14
    iget-object p0, p2, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->e:Lkotlin/jvm/functions/Function0;

    .line 15
    .line 16
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 20
    .line 21
    return-object p0
.end method

.method public static final H(Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->M()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->O(Ljava/util/List;)V

    .line 5
    .line 6
    .line 7
    new-instance p1, LCT0/s;

    .line 8
    .line 9
    invoke-direct {p1, p0}, LCT0/s;-><init>(Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->j(Lkotlin/jvm/functions/Function0;)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final I(Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->N()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->e:Lkotlin/jvm/functions/Function0;

    .line 5
    .line 6
    invoke-interface {v0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 7
    .line 8
    .line 9
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->m()V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final J(II)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final K()Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object v0
.end method

.method public static synthetic a(Lkotlin/jvm/internal/Ref$IntRef;Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;Ljava/util/List;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->C(Lkotlin/jvm/internal/Ref$IntRef;Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;Ljava/util/List;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/internal/Ref$IntRef;Lkotlin/jvm/internal/Ref$IntRef;Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->F(Lkotlin/jvm/internal/Ref$IntRef;Lkotlin/jvm/internal/Ref$IntRef;Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->I(Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lkotlin/jvm/internal/Ref$IntRef;Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->k(Lkotlin/jvm/internal/Ref$IntRef;Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->H(Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->o(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;FLkotlin/jvm/internal/Ref$IntRef;Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->s(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;FLkotlin/jvm/internal/Ref$IntRef;Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic h()Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->K()Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic i(II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->J(II)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final k(Lkotlin/jvm/internal/Ref$IntRef;Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget v0, p0, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 2
    .line 3
    add-int/lit8 v0, v0, 0x1

    .line 4
    .line 5
    iput v0, p0, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 6
    .line 7
    const/16 p0, 0x19

    .line 8
    .line 9
    if-ne v0, p0, :cond_0

    .line 10
    .line 11
    invoke-interface {p1}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final o(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getType$tile_matching_release()Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->L()Z

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->q()V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p1, p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->w(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)V

    .line 19
    .line 20
    .line 21
    iget-object p1, p1, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->g:Lkotlin/jvm/functions/Function2;

    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getRow$tile_matching_release()I

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getColumn$tile_matching_release()I

    .line 32
    .line 33
    .line 34
    move-result p0

    .line 35
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 36
    .line 37
    .line 38
    move-result-object p0

    .line 39
    invoke-interface {p1, v0, p0}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    :cond_1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 43
    .line 44
    return-object p0
.end method

.method public static final s(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;FLkotlin/jvm/internal/Ref$IntRef;Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Landroid/view/View;->setY(F)V

    .line 2
    .line 3
    .line 4
    const/16 p1, 0x8

    .line 5
    .line 6
    invoke-virtual {p0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 7
    .line 8
    .line 9
    iget p0, p2, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 10
    .line 11
    add-int/lit8 p0, p0, 0x1

    .line 12
    .line 13
    iput p0, p2, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 14
    .line 15
    const/16 p1, 0x19

    .line 16
    .line 17
    if-ne p0, p1, :cond_0

    .line 18
    .line 19
    invoke-interface {p3}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 23
    .line 24
    return-object p0
.end method


# virtual methods
.method public final A(Ljava/util/List;Ljava/util/List;Ljava/util/List;)V
    .locals 0
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;>;",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p2, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->c:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {p3}, Ljava/util/Collection;->isEmpty()Z

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    if-nez p2, :cond_0

    .line 8
    .line 9
    iget-object p2, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->d:Ljava/util/List;

    .line 10
    .line 11
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 12
    .line 13
    .line 14
    move-result p2

    .line 15
    if-nez p2, :cond_0

    .line 16
    .line 17
    invoke-virtual {p0, p3, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->B(Ljava/util/List;Ljava/util/List;)V

    .line 18
    .line 19
    .line 20
    return-void

    .line 21
    :cond_0
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->G(Ljava/util/List;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final B(Ljava/util/List;Ljava/util/List;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, Lkotlin/jvm/internal/Ref$IntRef;

    .line 2
    .line 3
    invoke-direct {v0}, Lkotlin/jvm/internal/Ref$IntRef;-><init>()V

    .line 4
    .line 5
    .line 6
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->d:Ljava/util/List;

    .line 7
    .line 8
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 13
    .line 14
    .line 15
    move-result v2

    .line 16
    if-eqz v2, :cond_0

    .line 17
    .line 18
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    check-cast v2, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 23
    .line 24
    new-instance v3, LCT0/n;

    .line 25
    .line 26
    invoke-direct {v3, v0, p0, p1, p2}, LCT0/n;-><init>(Lkotlin/jvm/internal/Ref$IntRef;Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;Ljava/util/List;Ljava/util/List;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {v2, v3}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->I(Lkotlin/jvm/functions/Function0;)V

    .line 30
    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_0
    return-void
.end method

.method public final D()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->d:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 18
    .line 19
    invoke-virtual {v1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getRow$tile_matching_release()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    add-int/lit8 v2, v2, -0x5

    .line 24
    .line 25
    invoke-virtual {v1, v2}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->setRow$tile_matching_release(I)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {v1}, Landroid/view/View;->getY()F

    .line 29
    .line 30
    .line 31
    move-result v2

    .line 32
    invoke-virtual {p0}, Landroid/view/View;->getHeight()I

    .line 33
    .line 34
    .line 35
    move-result v3

    .line 36
    mul-int/lit8 v3, v3, 0x5

    .line 37
    .line 38
    int-to-float v3, v3

    .line 39
    sub-float/2addr v2, v3

    .line 40
    invoke-virtual {v1, v2}, Landroid/view/View;->setY(F)V

    .line 41
    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_0
    return-void
.end method

.method public final E(Ljava/util/List;Ljava/util/List;)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, Lkotlin/jvm/internal/Ref$IntRef;

    .line 2
    .line 3
    invoke-direct {v0}, Lkotlin/jvm/internal/Ref$IntRef;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lkotlin/jvm/internal/Ref$IntRef;

    .line 7
    .line 8
    invoke-direct {v1}, Lkotlin/jvm/internal/Ref$IntRef;-><init>()V

    .line 9
    .line 10
    .line 11
    new-instance v2, Ljava/util/ArrayList;

    .line 12
    .line 13
    const/16 v3, 0xa

    .line 14
    .line 15
    invoke-static {p1, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 16
    .line 17
    .line 18
    move-result v3

    .line 19
    invoke-direct {v2, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 20
    .line 21
    .line 22
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 27
    .line 28
    .line 29
    move-result v3

    .line 30
    if-eqz v3, :cond_0

    .line 31
    .line 32
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v3

    .line 36
    check-cast v3, LzT0/b;

    .line 37
    .line 38
    invoke-virtual {v3}, LzT0/b;->a()I

    .line 39
    .line 40
    .line 41
    move-result v3

    .line 42
    invoke-virtual {p0, v3}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->x(I)Ljava/util/List;

    .line 43
    .line 44
    .line 45
    move-result-object v3

    .line 46
    invoke-interface {v2, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 47
    .line 48
    .line 49
    goto :goto_0

    .line 50
    :cond_0
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    :cond_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 55
    .line 56
    .line 57
    move-result v2

    .line 58
    if-eqz v2, :cond_7

    .line 59
    .line 60
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object v2

    .line 64
    check-cast v2, Ljava/util/List;

    .line 65
    .line 66
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    const/4 v3, 0x0

    .line 71
    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 72
    .line 73
    .line 74
    move-result v4

    .line 75
    if-eqz v4, :cond_1

    .line 76
    .line 77
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object v4

    .line 81
    add-int/lit8 v5, v3, 0x1

    .line 82
    .line 83
    if-gez v3, :cond_2

    .line 84
    .line 85
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 86
    .line 87
    .line 88
    :cond_2
    check-cast v4, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 89
    .line 90
    invoke-virtual {v4}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getRow$tile_matching_release()I

    .line 91
    .line 92
    .line 93
    move-result v6

    .line 94
    if-eq v6, v3, :cond_6

    .line 95
    .line 96
    iget v6, v0, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 97
    .line 98
    add-int/lit8 v6, v6, 0x1

    .line 99
    .line 100
    iput v6, v0, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 101
    .line 102
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 103
    .line 104
    .line 105
    move-result-object v6

    .line 106
    :cond_3
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    .line 107
    .line 108
    .line 109
    move-result v7

    .line 110
    if-eqz v7, :cond_4

    .line 111
    .line 112
    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 113
    .line 114
    .line 115
    move-result-object v7

    .line 116
    move-object v8, v7

    .line 117
    check-cast v8, LzT0/b;

    .line 118
    .line 119
    invoke-virtual {v8}, LzT0/b;->b()I

    .line 120
    .line 121
    .line 122
    move-result v9

    .line 123
    if-ne v9, v3, :cond_3

    .line 124
    .line 125
    invoke-virtual {v8}, LzT0/b;->a()I

    .line 126
    .line 127
    .line 128
    move-result v8

    .line 129
    invoke-virtual {v4}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getColumn$tile_matching_release()I

    .line 130
    .line 131
    .line 132
    move-result v9

    .line 133
    if-ne v8, v9, :cond_3

    .line 134
    .line 135
    goto :goto_2

    .line 136
    :cond_4
    const/4 v7, 0x0

    .line 137
    :goto_2
    check-cast v7, LzT0/b;

    .line 138
    .line 139
    if-eqz v7, :cond_5

    .line 140
    .line 141
    invoke-virtual {v7}, LzT0/b;->c()Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 142
    .line 143
    .line 144
    move-result-object v6

    .line 145
    invoke-virtual {v4, v6}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->setType$tile_matching_release(Lorg/xbet/tile_matching/domain/models/TileMatchingType;)V

    .line 146
    .line 147
    .line 148
    :cond_5
    new-instance v6, LCT0/r;

    .line 149
    .line 150
    invoke-direct {v6, v1, v0, p0}, LCT0/r;-><init>(Lkotlin/jvm/internal/Ref$IntRef;Lkotlin/jvm/internal/Ref$IntRef;Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;)V

    .line 151
    .line 152
    .line 153
    invoke-virtual {v4, v3, v6}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->Q(ILkotlin/jvm/functions/Function0;)V

    .line 154
    .line 155
    .line 156
    :cond_6
    move v3, v5

    .line 157
    goto :goto_1

    .line 158
    :cond_7
    return-void
.end method

.method public final G(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, LCT0/m;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, LCT0/m;-><init>(Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->r(Lkotlin/jvm/functions/Function0;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final L()V
    .locals 6

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x5

    .line 3
    invoke-static {v0, v1}, Lkotlin/ranges/f;->z(II)Lkotlin/ranges/IntRange;

    .line 4
    .line 5
    .line 6
    move-result-object v2

    .line 7
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    :cond_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 12
    .line 13
    .line 14
    move-result v3

    .line 15
    if-eqz v3, :cond_1

    .line 16
    .line 17
    move-object v3, v2

    .line 18
    check-cast v3, Lkotlin/collections/L;

    .line 19
    .line 20
    invoke-virtual {v3}, Lkotlin/collections/L;->b()I

    .line 21
    .line 22
    .line 23
    move-result v3

    .line 24
    invoke-static {v0, v1}, Lkotlin/ranges/f;->z(II)Lkotlin/ranges/IntRange;

    .line 25
    .line 26
    .line 27
    move-result-object v4

    .line 28
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 29
    .line 30
    .line 31
    move-result-object v4

    .line 32
    :goto_0
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 33
    .line 34
    .line 35
    move-result v5

    .line 36
    if-eqz v5, :cond_0

    .line 37
    .line 38
    move-object v5, v4

    .line 39
    check-cast v5, Lkotlin/collections/L;

    .line 40
    .line 41
    invoke-virtual {v5}, Lkotlin/collections/L;->b()I

    .line 42
    .line 43
    .line 44
    move-result v5

    .line 45
    invoke-virtual {p0, v3, v5}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->n(II)Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 46
    .line 47
    .line 48
    move-result-object v5

    .line 49
    invoke-virtual {p0, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 50
    .line 51
    .line 52
    goto :goto_0

    .line 53
    :cond_1
    return-void
.end method

.method public final M()V
    .locals 8

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x5

    .line 3
    invoke-static {v0, v1}, Lkotlin/ranges/f;->z(II)Lkotlin/ranges/IntRange;

    .line 4
    .line 5
    .line 6
    move-result-object v2

    .line 7
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    const/4 v3, 0x0

    .line 12
    :cond_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 13
    .line 14
    .line 15
    move-result v4

    .line 16
    if-eqz v4, :cond_1

    .line 17
    .line 18
    move-object v4, v2

    .line 19
    check-cast v4, Lkotlin/collections/L;

    .line 20
    .line 21
    invoke-virtual {v4}, Lkotlin/collections/L;->b()I

    .line 22
    .line 23
    .line 24
    move-result v4

    .line 25
    invoke-static {v0, v1}, Lkotlin/ranges/f;->z(II)Lkotlin/ranges/IntRange;

    .line 26
    .line 27
    .line 28
    move-result-object v5

    .line 29
    invoke-interface {v5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 30
    .line 31
    .line 32
    move-result-object v5

    .line 33
    :goto_0
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    .line 34
    .line 35
    .line 36
    move-result v6

    .line 37
    if-eqz v6, :cond_0

    .line 38
    .line 39
    move-object v6, v5

    .line 40
    check-cast v6, Lkotlin/collections/L;

    .line 41
    .line 42
    invoke-virtual {v6}, Lkotlin/collections/L;->b()I

    .line 43
    .line 44
    .line 45
    move-result v6

    .line 46
    iget-object v7, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 47
    .line 48
    invoke-interface {v7, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v7

    .line 52
    check-cast v7, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 53
    .line 54
    invoke-virtual {v7, v4}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->setRow$tile_matching_release(I)V

    .line 55
    .line 56
    .line 57
    invoke-virtual {v7, v6}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->setColumn$tile_matching_release(I)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {v7, v4}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->setYByLine$tile_matching_release(I)V

    .line 61
    .line 62
    .line 63
    add-int/lit8 v3, v3, 0x1

    .line 64
    .line 65
    goto :goto_0

    .line 66
    :cond_1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->d:Ljava/util/List;

    .line 67
    .line 68
    invoke-interface {v0}, Ljava/util/List;->clear()V

    .line 69
    .line 70
    .line 71
    return-void
.end method

.method public final N()V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 18
    .line 19
    invoke-virtual {v1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->setDefault$tile_matching_release()V

    .line 20
    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->c:Ljava/util/List;

    .line 24
    .line 25
    invoke-static {v0}, Lkotlin/collections/w;->A(Ljava/lang/Iterable;)Ljava/util/List;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    :cond_1
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    if-eqz v1, :cond_4

    .line 38
    .line 39
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    check-cast v1, LzT0/b;

    .line 44
    .line 45
    iget-object v2, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 46
    .line 47
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 48
    .line 49
    .line 50
    move-result-object v2

    .line 51
    :cond_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 52
    .line 53
    .line 54
    move-result v3

    .line 55
    if-eqz v3, :cond_3

    .line 56
    .line 57
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v3

    .line 61
    move-object v4, v3

    .line 62
    check-cast v4, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 63
    .line 64
    invoke-virtual {v4}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getRow$tile_matching_release()I

    .line 65
    .line 66
    .line 67
    move-result v5

    .line 68
    invoke-virtual {v1}, LzT0/b;->b()I

    .line 69
    .line 70
    .line 71
    move-result v6

    .line 72
    if-ne v5, v6, :cond_2

    .line 73
    .line 74
    invoke-virtual {v4}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getColumn$tile_matching_release()I

    .line 75
    .line 76
    .line 77
    move-result v4

    .line 78
    invoke-virtual {v1}, LzT0/b;->a()I

    .line 79
    .line 80
    .line 81
    move-result v5

    .line 82
    if-ne v4, v5, :cond_2

    .line 83
    .line 84
    goto :goto_2

    .line 85
    :cond_3
    const/4 v3, 0x0

    .line 86
    :goto_2
    check-cast v3, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 87
    .line 88
    if-eqz v3, :cond_1

    .line 89
    .line 90
    invoke-virtual {v3}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->M()V

    .line 91
    .line 92
    .line 93
    goto :goto_1

    .line 94
    :cond_4
    return-void
.end method

.method public final O(Ljava/util/List;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_3

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 18
    .line 19
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    :cond_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v3

    .line 27
    if-eqz v3, :cond_2

    .line 28
    .line 29
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v3

    .line 33
    move-object v4, v3

    .line 34
    check-cast v4, LzT0/b;

    .line 35
    .line 36
    invoke-virtual {v4}, LzT0/b;->b()I

    .line 37
    .line 38
    .line 39
    move-result v5

    .line 40
    invoke-virtual {v1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getRow$tile_matching_release()I

    .line 41
    .line 42
    .line 43
    move-result v6

    .line 44
    if-ne v5, v6, :cond_1

    .line 45
    .line 46
    invoke-virtual {v4}, LzT0/b;->a()I

    .line 47
    .line 48
    .line 49
    move-result v4

    .line 50
    invoke-virtual {v1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getColumn$tile_matching_release()I

    .line 51
    .line 52
    .line 53
    move-result v5

    .line 54
    if-ne v4, v5, :cond_1

    .line 55
    .line 56
    goto :goto_1

    .line 57
    :cond_2
    const/4 v3, 0x0

    .line 58
    :goto_1
    check-cast v3, LzT0/b;

    .line 59
    .line 60
    if-eqz v3, :cond_0

    .line 61
    .line 62
    invoke-virtual {v3}, LzT0/b;->c()Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 63
    .line 64
    .line 65
    move-result-object v2

    .line 66
    invoke-virtual {v1, v2}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->setType$tile_matching_release(Lorg/xbet/tile_matching/domain/models/TileMatchingType;)V

    .line 67
    .line 68
    .line 69
    goto :goto_0

    .line 70
    :cond_3
    return-void
.end method

.method public final j(Lkotlin/jvm/functions/Function0;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, Lkotlin/jvm/internal/Ref$IntRef;

    .line 2
    .line 3
    invoke-direct {v0}, Lkotlin/jvm/internal/Ref$IntRef;-><init>()V

    .line 4
    .line 5
    .line 6
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 7
    .line 8
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 13
    .line 14
    .line 15
    move-result v2

    .line 16
    if-eqz v2, :cond_0

    .line 17
    .line 18
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    check-cast v2, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 23
    .line 24
    invoke-virtual {v2}, Landroid/view/View;->getY()F

    .line 25
    .line 26
    .line 27
    move-result v3

    .line 28
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 29
    .line 30
    .line 31
    move-result v4

    .line 32
    int-to-float v4, v4

    .line 33
    sub-float/2addr v3, v4

    .line 34
    invoke-virtual {v2, v3}, Landroid/view/View;->setY(F)V

    .line 35
    .line 36
    .line 37
    const/4 v3, 0x0

    .line 38
    invoke-virtual {v2, v3}, Landroid/view/View;->setVisibility(I)V

    .line 39
    .line 40
    .line 41
    invoke-virtual {v2}, Landroid/view/View;->getY()F

    .line 42
    .line 43
    .line 44
    move-result v3

    .line 45
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 46
    .line 47
    .line 48
    move-result v4

    .line 49
    int-to-float v4, v4

    .line 50
    add-float/2addr v3, v4

    .line 51
    new-instance v4, LCT0/u;

    .line 52
    .line 53
    invoke-direct {v4, v0, p1}, LCT0/u;-><init>(Lkotlin/jvm/internal/Ref$IntRef;Lkotlin/jvm/functions/Function0;)V

    .line 54
    .line 55
    .line 56
    invoke-virtual {v2, v3, v4}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->P(FLkotlin/jvm/functions/Function0;)V

    .line 57
    .line 58
    .line 59
    goto :goto_0

    .line 60
    :cond_0
    return-void
.end method

.method public final l(I)Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 6
    .line 7
    return-object p1
.end method

.method public final m()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 10
    .line 11
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->I0(Ljava/util/List;)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    check-cast v0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 16
    .line 17
    invoke-virtual {v0}, Landroid/view/View;->getY()F

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    const/4 v1, 0x0

    .line 22
    cmpg-float v0, v0, v1

    .line 23
    .line 24
    if-nez v0, :cond_0

    .line 25
    .line 26
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->z()V

    .line 27
    .line 28
    .line 29
    :cond_0
    return-void
.end method

.method public final n(II)Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    const/4 v4, 0x6

    .line 8
    const/4 v5, 0x0

    .line 9
    const/4 v2, 0x0

    .line 10
    const/4 v3, 0x0

    .line 11
    invoke-direct/range {v0 .. v5}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 12
    .line 13
    .line 14
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->f:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 15
    .line 16
    invoke-static {v1}, LBT0/a;->a(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)I

    .line 17
    .line 18
    .line 19
    move-result v2

    .line 20
    new-instance v3, LCT0/t;

    .line 21
    .line 22
    invoke-direct {v3, v0, p0}, LCT0/t;-><init>(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {v0, v1, v2, v3}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->K(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;ILkotlin/jvm/functions/Function0;)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {v0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->setRow$tile_matching_release(I)V

    .line 29
    .line 30
    .line 31
    invoke-virtual {v0, p2}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->setColumn$tile_matching_release(I)V

    .line 32
    .line 33
    .line 34
    return-object v0
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->m()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public onMeasure(II)V
    .locals 1

    .line 1
    invoke-super {p0, p1, p2}, Landroid/view/ViewGroup;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 9
    .line 10
    .line 11
    move-result p2

    .line 12
    sub-int/2addr p1, p2

    .line 13
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 14
    .line 15
    .line 16
    move-result p2

    .line 17
    sub-int/2addr p1, p2

    .line 18
    div-int/lit8 p1, p1, 0x5

    .line 19
    .line 20
    iput p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->a:I

    .line 21
    .line 22
    mul-int/lit8 p1, p1, 0x5

    .line 23
    .line 24
    invoke-virtual {p0}, Landroid/view/View;->getPaddingTop()I

    .line 25
    .line 26
    .line 27
    move-result p2

    .line 28
    add-int/2addr p1, p2

    .line 29
    invoke-virtual {p0}, Landroid/view/View;->getPaddingBottom()I

    .line 30
    .line 31
    .line 32
    move-result p2

    .line 33
    add-int/2addr p1, p2

    .line 34
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 35
    .line 36
    .line 37
    move-result p2

    .line 38
    invoke-virtual {p0, p2, p1}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 39
    .line 40
    .line 41
    iget p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->a:I

    .line 42
    .line 43
    const/high16 p2, 0x40000000    # 2.0f

    .line 44
    .line 45
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 46
    .line 47
    .line 48
    move-result p1

    .line 49
    iget-object p2, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 50
    .line 51
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 52
    .line 53
    .line 54
    move-result-object p2

    .line 55
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 56
    .line 57
    .line 58
    move-result v0

    .line 59
    if-eqz v0, :cond_0

    .line 60
    .line 61
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    check-cast v0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 66
    .line 67
    invoke-virtual {p0, v0, p1, p1}, Landroid/view/ViewGroup;->measureChild(Landroid/view/View;II)V

    .line 68
    .line 69
    .line 70
    goto :goto_0

    .line 71
    :cond_0
    return-void
.end method

.method public final p(Ljava/util/List;Ljava/util/List;)V
    .locals 0
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;>;)V"
        }
    .end annotation

    .line 1
    iput-object p2, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->c:Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->G(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final q()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 18
    .line 19
    const/4 v2, 0x0

    .line 20
    invoke-virtual {v1, v2}, Landroid/view/View;->setEnabled(Z)V

    .line 21
    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    return-void
.end method

.method public final r(Lkotlin/jvm/functions/Function0;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, Lkotlin/jvm/internal/Ref$IntRef;

    .line 2
    .line 3
    invoke-direct {v0}, Lkotlin/jvm/internal/Ref$IntRef;-><init>()V

    .line 4
    .line 5
    .line 6
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 7
    .line 8
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 13
    .line 14
    .line 15
    move-result v2

    .line 16
    if-eqz v2, :cond_0

    .line 17
    .line 18
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    check-cast v2, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 23
    .line 24
    invoke-virtual {v2}, Landroid/view/View;->getY()F

    .line 25
    .line 26
    .line 27
    move-result v3

    .line 28
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 29
    .line 30
    .line 31
    move-result v4

    .line 32
    int-to-float v4, v4

    .line 33
    add-float/2addr v4, v3

    .line 34
    new-instance v5, LCT0/q;

    .line 35
    .line 36
    invoke-direct {v5, v2, v3, v0, p1}, LCT0/q;-><init>(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;FLkotlin/jvm/internal/Ref$IntRef;Lkotlin/jvm/functions/Function0;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {v2, v4, v5}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->P(FLkotlin/jvm/functions/Function0;)V

    .line 40
    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_0
    return-void
.end method

.method public final setCells$tile_matching_release(Ljava/util/List;)V
    .locals 0
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->O(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final setWinCells$tile_matching_release(Ljava/util/List;)V
    .locals 0
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;>;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->c:Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->N()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final t(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 18
    .line 19
    invoke-virtual {v1, p1}, Landroid/view/View;->setEnabled(Z)V

    .line 20
    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    return-void
.end method

.method public final u(LzT0/b;)Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_1

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    move-object v2, v1

    .line 18
    check-cast v2, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 19
    .line 20
    invoke-virtual {v2}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getRow$tile_matching_release()I

    .line 21
    .line 22
    .line 23
    move-result v3

    .line 24
    invoke-virtual {p1}, LzT0/b;->b()I

    .line 25
    .line 26
    .line 27
    move-result v4

    .line 28
    if-ne v3, v4, :cond_0

    .line 29
    .line 30
    invoke-virtual {v2}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getColumn$tile_matching_release()I

    .line 31
    .line 32
    .line 33
    move-result v2

    .line 34
    invoke-virtual {p1}, LzT0/b;->a()I

    .line 35
    .line 36
    .line 37
    move-result v3

    .line 38
    if-ne v2, v3, :cond_0

    .line 39
    .line 40
    goto :goto_0

    .line 41
    :cond_1
    const/4 v1, 0x0

    .line 42
    :goto_0
    check-cast v1, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 43
    .line 44
    return-object v1
.end method

.method public final v(Ljava/util/List;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;)",
            "Ljava/util/List<",
            "Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    if-eqz v1, :cond_1

    .line 15
    .line 16
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    check-cast v1, LzT0/b;

    .line 21
    .line 22
    invoke-virtual {p0, v1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->u(LzT0/b;)Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    if-eqz v1, :cond_0

    .line 27
    .line 28
    invoke-interface {v0, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 29
    .line 30
    .line 31
    goto :goto_0

    .line 32
    :cond_1
    return-object v0
.end method

.method public final w(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->c:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_5

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    move-object v2, v1

    .line 18
    check-cast v2, Ljava/util/List;

    .line 19
    .line 20
    invoke-static {v2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 21
    .line 22
    .line 23
    move-result v3

    .line 24
    if-eqz v3, :cond_1

    .line 25
    .line 26
    invoke-interface {v2}, Ljava/util/Collection;->isEmpty()Z

    .line 27
    .line 28
    .line 29
    move-result v3

    .line 30
    if-eqz v3, :cond_1

    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_1
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    :cond_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 38
    .line 39
    .line 40
    move-result v3

    .line 41
    if-eqz v3, :cond_0

    .line 42
    .line 43
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object v3

    .line 47
    check-cast v3, LzT0/b;

    .line 48
    .line 49
    invoke-virtual {v3}, LzT0/b;->b()I

    .line 50
    .line 51
    .line 52
    move-result v4

    .line 53
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getRow$tile_matching_release()I

    .line 54
    .line 55
    .line 56
    move-result v5

    .line 57
    const/4 v6, 0x0

    .line 58
    const/4 v7, 0x1

    .line 59
    if-ne v4, v5, :cond_3

    .line 60
    .line 61
    const/4 v4, 0x1

    .line 62
    goto :goto_1

    .line 63
    :cond_3
    const/4 v4, 0x0

    .line 64
    :goto_1
    invoke-virtual {v3}, LzT0/b;->a()I

    .line 65
    .line 66
    .line 67
    move-result v3

    .line 68
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getColumn$tile_matching_release()I

    .line 69
    .line 70
    .line 71
    move-result v5

    .line 72
    if-ne v3, v5, :cond_4

    .line 73
    .line 74
    const/4 v6, 0x1

    .line 75
    :cond_4
    and-int v3, v4, v6

    .line 76
    .line 77
    if-eqz v3, :cond_2

    .line 78
    .line 79
    goto :goto_2

    .line 80
    :cond_5
    const/4 v1, 0x0

    .line 81
    :goto_2
    check-cast v1, Ljava/util/List;

    .line 82
    .line 83
    if-eqz v1, :cond_6

    .line 84
    .line 85
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->d:Ljava/util/List;

    .line 86
    .line 87
    invoke-interface {p1}, Ljava/util/List;->clear()V

    .line 88
    .line 89
    .line 90
    invoke-virtual {p0, v1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->v(Ljava/util/List;)Ljava/util/List;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    invoke-interface {p1, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 95
    .line 96
    .line 97
    :cond_6
    return-void
.end method

.method public final x(I)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Ljava/util/List<",
            "Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 2
    .line 3
    new-instance v1, Ljava/util/ArrayList;

    .line 4
    .line 5
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 13
    .line 14
    .line 15
    move-result v2

    .line 16
    if-eqz v2, :cond_1

    .line 17
    .line 18
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    move-object v3, v2

    .line 23
    check-cast v3, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 24
    .line 25
    invoke-virtual {v3}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getColumn$tile_matching_release()I

    .line 26
    .line 27
    .line 28
    move-result v3

    .line 29
    if-ne v3, p1, :cond_0

    .line 30
    .line 31
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 32
    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_1
    new-instance p1, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView$b;

    .line 36
    .line 37
    invoke-direct {p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView$b;-><init>()V

    .line 38
    .line 39
    .line 40
    invoke-static {v1, p1}, Lkotlin/collections/CollectionsKt;->l1(Ljava/lang/Iterable;Ljava/util/Comparator;)Ljava/util/List;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    return-object p1
.end method

.method public final y(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;)V
    .locals 0
    .param p1    # Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->f:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->g:Lkotlin/jvm/functions/Function2;

    .line 4
    .line 5
    iput-object p3, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->e:Lkotlin/jvm/functions/Function0;

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->L()V

    .line 8
    .line 9
    .line 10
    const/4 p1, 0x0

    .line 11
    invoke-virtual {p0}, Landroid/view/ViewGroup;->getChildCount()I

    .line 12
    .line 13
    .line 14
    move-result p2

    .line 15
    invoke-static {p1, p2}, Lkotlin/ranges/f;->z(II)Lkotlin/ranges/IntRange;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    new-instance p2, Ljava/util/ArrayList;

    .line 20
    .line 21
    const/16 p3, 0xa

    .line 22
    .line 23
    invoke-static {p1, p3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 24
    .line 25
    .line 26
    move-result p3

    .line 27
    invoke-direct {p2, p3}, Ljava/util/ArrayList;-><init>(I)V

    .line 28
    .line 29
    .line 30
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 35
    .line 36
    .line 37
    move-result p3

    .line 38
    if-eqz p3, :cond_0

    .line 39
    .line 40
    move-object p3, p1

    .line 41
    check-cast p3, Lkotlin/collections/L;

    .line 42
    .line 43
    invoke-virtual {p3}, Lkotlin/collections/L;->b()I

    .line 44
    .line 45
    .line 46
    move-result p3

    .line 47
    invoke-virtual {p0, p3}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->l(I)Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 48
    .line 49
    .line 50
    move-result-object p3

    .line 51
    invoke-interface {p2, p3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 52
    .line 53
    .line 54
    goto :goto_0

    .line 55
    :cond_0
    iput-object p2, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 56
    .line 57
    return-void
.end method

.method public final z()V
    .locals 10

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getPaddingTop()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    const/4 v2, 0x0

    .line 10
    const/4 v3, 0x5

    .line 11
    invoke-static {v2, v3}, Lkotlin/ranges/f;->z(II)Lkotlin/ranges/IntRange;

    .line 12
    .line 13
    .line 14
    move-result-object v4

    .line 15
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 16
    .line 17
    .line 18
    move-result-object v4

    .line 19
    const/4 v5, 0x0

    .line 20
    :goto_0
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 21
    .line 22
    .line 23
    move-result v6

    .line 24
    if-eqz v6, :cond_1

    .line 25
    .line 26
    move-object v6, v4

    .line 27
    check-cast v6, Lkotlin/collections/L;

    .line 28
    .line 29
    invoke-virtual {v6}, Lkotlin/collections/L;->b()I

    .line 30
    .line 31
    .line 32
    invoke-static {v2, v3}, Lkotlin/ranges/f;->z(II)Lkotlin/ranges/IntRange;

    .line 33
    .line 34
    .line 35
    move-result-object v6

    .line 36
    invoke-interface {v6}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 37
    .line 38
    .line 39
    move-result-object v6

    .line 40
    :goto_1
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    .line 41
    .line 42
    .line 43
    move-result v7

    .line 44
    if-eqz v7, :cond_0

    .line 45
    .line 46
    move-object v7, v6

    .line 47
    check-cast v7, Lkotlin/collections/L;

    .line 48
    .line 49
    invoke-virtual {v7}, Lkotlin/collections/L;->b()I

    .line 50
    .line 51
    .line 52
    iget-object v7, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->b:Ljava/util/List;

    .line 53
    .line 54
    invoke-interface {v7, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 55
    .line 56
    .line 57
    move-result-object v7

    .line 58
    check-cast v7, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;

    .line 59
    .line 60
    iget v8, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->a:I

    .line 61
    .line 62
    add-int v9, v0, v8

    .line 63
    .line 64
    add-int/2addr v8, v1

    .line 65
    invoke-virtual {v7, v0, v1, v9, v8}, Landroid/view/View;->layout(IIII)V

    .line 66
    .line 67
    .line 68
    int-to-float v8, v1

    .line 69
    invoke-virtual {v7, v8}, Landroid/view/View;->setY(F)V

    .line 70
    .line 71
    .line 72
    iget v7, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->a:I

    .line 73
    .line 74
    add-int/2addr v0, v7

    .line 75
    add-int/lit8 v5, v5, 0x1

    .line 76
    .line 77
    goto :goto_1

    .line 78
    :cond_0
    iget v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->a:I

    .line 79
    .line 80
    add-int/2addr v1, v0

    .line 81
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 82
    .line 83
    .line 84
    move-result v0

    .line 85
    goto :goto_0

    .line 86
    :cond_1
    return-void
.end method
