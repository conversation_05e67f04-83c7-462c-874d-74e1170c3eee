.class public final Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001au\u0010\u000c\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000b0\n0\t2\u001e\u0010\u0004\u001a\u001a\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u00002\u0018\u0010\u0006\u001a\u0014\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00030\u00052\u001e\u0010\u0008\u001a\u001a\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00030\u0000H\u0000\u00a2\u0006\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Lkotlin/Function3;",
        "",
        "",
        "",
        "onCategoryAllClick",
        "Lkotlin/Function2;",
        "onCategoryItemClick",
        "",
        "onCategoryItemFavoriteClick",
        "LA4/c;",
        "",
        "LVX0/i;",
        "g",
        "(LOc/n;Lkotlin/jvm/functions/Function2;LOc/n;)LA4/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lkotlin/jvm/functions/Function2;LB4/a;LN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt;->l(Lkotlin/jvm/functions/Function2;LB4/a;LN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/v0;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt;->h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/v0;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LOc/n;LB4/a;LN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt;->m(LOc/n;LB4/a;LN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LOc/n;Lkotlin/jvm/functions/Function2;LOc/n;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt;->i(LOc/n;Lkotlin/jvm/functions/Function2;LOc/n;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(LOc/n;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt;->k(LOc/n;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic f(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt;->j(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final g(LOc/n;Lkotlin/jvm/functions/Function2;LOc/n;)LA4/c;
    .locals 2
    .param p0    # LOc/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LOc/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LOc/n<",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/Long;",
            "Lkotlin/Unit;",
            ">;",
            "LOc/n<",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lba1/u;

    .line 2
    .line 3
    invoke-direct {v0}, Lba1/u;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lba1/v;

    .line 7
    .line 8
    invoke-direct {v1, p0, p1, p2}, Lba1/v;-><init>(LOc/n;Lkotlin/jvm/functions/Function2;LOc/n;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$giftsCategoryAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$giftsCategoryAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$giftsCategoryAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$giftsCategoryAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance p2, LB4/b;

    .line 19
    .line 20
    invoke-direct {p2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object p2
.end method

.method public static final h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/v0;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/v0;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/v0;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final i(LOc/n;Lkotlin/jvm/functions/Function2;LOc/n;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p3}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LS91/v0;

    .line 6
    .line 7
    iget-object v0, v0, LS91/v0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 8
    .line 9
    const/4 v1, 0x0

    .line 10
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setItemAnimator(Landroidx/recyclerview/widget/RecyclerView$m;)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p3}, LB4/a;->e()LL2/a;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    check-cast v0, LS91/v0;

    .line 18
    .line 19
    iget-object v0, v0, LS91/v0;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 20
    .line 21
    new-instance v2, Lba1/w;

    .line 22
    .line 23
    invoke-direct {v2, p0, p3}, Lba1/w;-><init>(LOc/n;LB4/a;)V

    .line 24
    .line 25
    .line 26
    const/4 p0, 0x1

    .line 27
    invoke-static {v1, v2, p0, v1}, LN11/f;->k(Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 28
    .line 29
    .line 30
    move-result-object p0

    .line 31
    invoke-virtual {v0, p0}, Lorg/xbet/uikit/components/header/DSHeader;->setButtonClickListener(Landroid/view/View$OnClickListener;)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p3}, LB4/a;->e()LL2/a;

    .line 35
    .line 36
    .line 37
    move-result-object p0

    .line 38
    check-cast p0, LS91/v0;

    .line 39
    .line 40
    iget-object p0, p0, LS91/v0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 41
    .line 42
    new-instance v0, Lba1/x;

    .line 43
    .line 44
    invoke-direct {v0, p1, p3}, Lba1/x;-><init>(Lkotlin/jvm/functions/Function2;LB4/a;)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {p3}, LB4/a;->e()LL2/a;

    .line 51
    .line 52
    .line 53
    move-result-object p0

    .line 54
    check-cast p0, LS91/v0;

    .line 55
    .line 56
    iget-object p0, p0, LS91/v0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 57
    .line 58
    new-instance p1, Lba1/y;

    .line 59
    .line 60
    invoke-direct {p1, p2, p3}, Lba1/y;-><init>(LOc/n;LB4/a;)V

    .line 61
    .line 62
    .line 63
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnActionIconClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 64
    .line 65
    .line 66
    new-instance p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$a;

    .line 67
    .line 68
    invoke-direct {p0, p3, p3}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$a;-><init>(LB4/a;LB4/a;)V

    .line 69
    .line 70
    .line 71
    invoke-virtual {p3, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 72
    .line 73
    .line 74
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 75
    .line 76
    return-object p0
.end method

.method public static final j(LB4/a;)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lma1/b;",
            "LS91/v0;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LS91/v0;

    .line 6
    .line 7
    iget-object v0, v0, LS91/v0;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    check-cast v1, Lma1/b;

    .line 14
    .line 15
    invoke-virtual {v1}, Lma1/b;->s()Z

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/header/DSHeader;->c(Z)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    check-cast v0, LS91/v0;

    .line 27
    .line 28
    iget-object v0, v0, LS91/v0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 29
    .line 30
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    check-cast v1, Lma1/b;

    .line 35
    .line 36
    invoke-virtual {v1}, Lma1/b;->o()I

    .line 37
    .line 38
    .line 39
    move-result v1

    .line 40
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setStyle(I)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    check-cast v0, Lma1/b;

    .line 48
    .line 49
    invoke-virtual {v0}, Lma1/b;->s()Z

    .line 50
    .line 51
    .line 52
    move-result v0

    .line 53
    if-eqz v0, :cond_0

    .line 54
    .line 55
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    check-cast v0, LS91/v0;

    .line 60
    .line 61
    iget-object v0, v0, LS91/v0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 62
    .line 63
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->o()V

    .line 64
    .line 65
    .line 66
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 67
    .line 68
    .line 69
    move-result-object p0

    .line 70
    check-cast p0, LS91/v0;

    .line 71
    .line 72
    invoke-virtual {p0}, LS91/v0;->b()Landroid/widget/LinearLayout;

    .line 73
    .line 74
    .line 75
    move-result-object p0

    .line 76
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 77
    .line 78
    .line 79
    return-void

    .line 80
    :cond_0
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    check-cast v0, LS91/v0;

    .line 85
    .line 86
    iget-object v0, v0, LS91/v0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 87
    .line 88
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 89
    .line 90
    .line 91
    move-result-object v1

    .line 92
    check-cast v1, Lma1/b;

    .line 93
    .line 94
    invoke-virtual {v1}, Lma1/b;->f()Ljava/util/List;

    .line 95
    .line 96
    .line 97
    move-result-object v1

    .line 98
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setItems(Ljava/util/List;)V

    .line 99
    .line 100
    .line 101
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 102
    .line 103
    .line 104
    move-result-object v0

    .line 105
    check-cast v0, LS91/v0;

    .line 106
    .line 107
    iget-object v0, v0, LS91/v0;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 108
    .line 109
    new-instance v1, Lorg/xbet/uikit/components/header/a$a;

    .line 110
    .line 111
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 112
    .line 113
    .line 114
    move-result-object v2

    .line 115
    check-cast v2, Lma1/b;

    .line 116
    .line 117
    invoke-virtual {v2}, Lma1/b;->getTitle()Ljava/lang/String;

    .line 118
    .line 119
    .line 120
    move-result-object v2

    .line 121
    sget v3, Lpb/k;->all:I

    .line 122
    .line 123
    invoke-virtual {p0, v3}, LB4/a;->j(I)Ljava/lang/String;

    .line 124
    .line 125
    .line 126
    move-result-object v5

    .line 127
    const/16 v11, 0x1f6

    .line 128
    .line 129
    const/4 v12, 0x0

    .line 130
    const/4 v3, 0x0

    .line 131
    const/4 v4, 0x0

    .line 132
    const/4 v6, 0x0

    .line 133
    const/4 v7, 0x0

    .line 134
    const/4 v8, 0x0

    .line 135
    const/4 v9, 0x0

    .line 136
    const/4 v10, 0x0

    .line 137
    invoke-direct/range {v1 .. v12}, Lorg/xbet/uikit/components/header/a$a;-><init>(Ljava/lang/CharSequence;ZLjava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/Integer;Landroid/graphics/drawable/Drawable;LL11/c;LL11/c;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 138
    .line 139
    .line 140
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/header/DSHeader;->setModel(Lorg/xbet/uikit/components/header/a;)V

    .line 141
    .line 142
    .line 143
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 144
    .line 145
    .line 146
    move-result-object p0

    .line 147
    check-cast p0, LS91/v0;

    .line 148
    .line 149
    invoke-virtual {p0}, LS91/v0;->b()Landroid/widget/LinearLayout;

    .line 150
    .line 151
    .line 152
    move-result-object p0

    .line 153
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 154
    .line 155
    .line 156
    return-void
.end method

.method public static final k(LOc/n;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, Lma1/b;

    .line 6
    .line 7
    invoke-virtual {p2}, Lma1/b;->getId()J

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 12
    .line 13
    .line 14
    move-result-object p2

    .line 15
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    check-cast v0, Lma1/b;

    .line 20
    .line 21
    invoke-virtual {v0}, Lma1/b;->j()J

    .line 22
    .line 23
    .line 24
    move-result-wide v0

    .line 25
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    check-cast p1, Lma1/b;

    .line 34
    .line 35
    invoke-virtual {p1}, Lma1/b;->getTitle()Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    invoke-interface {p0, p2, v0, p1}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 43
    .line 44
    return-object p0
.end method

.method public static final l(Lkotlin/jvm/functions/Function2;LB4/a;LN21/k;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p2}, LN21/k;->e()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, Lma1/b;

    .line 14
    .line 15
    invoke-virtual {p1}, Lma1/b;->getId()J

    .line 16
    .line 17
    .line 18
    move-result-wide v0

    .line 19
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-interface {p0, p2, p1}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 27
    .line 28
    return-object p0
.end method

.method public static final m(LOc/n;LB4/a;LN21/k;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p2}, LN21/k;->e()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, Lma1/b;

    .line 14
    .line 15
    invoke-virtual {p1}, Lma1/b;->getId()J

    .line 16
    .line 17
    .line 18
    move-result-wide v1

    .line 19
    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-virtual {p2}, LN21/k;->c()LN21/m;

    .line 24
    .line 25
    .line 26
    move-result-object p2

    .line 27
    invoke-virtual {p2}, LN21/m;->b()Z

    .line 28
    .line 29
    .line 30
    move-result p2

    .line 31
    invoke-static {p2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 32
    .line 33
    .line 34
    move-result-object p2

    .line 35
    invoke-interface {p0, v0, p1, p2}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 39
    .line 40
    return-object p0
.end method
