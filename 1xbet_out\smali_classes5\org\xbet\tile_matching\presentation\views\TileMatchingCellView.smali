.class public final Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;
.super Landroidx/appcompat/widget/AppCompatImageView;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\\\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0010\u0007\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u001f\u0018\u0000 a2\u00020\u0001:\u0001LB\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\r\u001a\u00020\u000c2\u0006\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u000f\u0010\u000f\u001a\u00020\u000cH\u0014\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J-\u0010\u0016\u001a\u00020\u000c2\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0013\u001a\u00020\u00062\u000c\u0010\u0015\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u0014H\u0000\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u000f\u0010\u0018\u001a\u00020\u000cH\u0000\u00a2\u0006\u0004\u0008\u0018\u0010\u0010J\u0017\u0010\u001c\u001a\u00020\u000c2\u0006\u0010\u0019\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0017\u0010\u001f\u001a\u00020\u000c2\u0006\u0010\u001d\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008\u001e\u0010\u001bJ\u0017\u0010$\u001a\u00020\u000c2\u0006\u0010!\u001a\u00020 H\u0000\u00a2\u0006\u0004\u0008\"\u0010#J\u000f\u0010\'\u001a\u00020 H\u0000\u00a2\u0006\u0004\u0008%\u0010&J\u000f\u0010*\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008(\u0010)J\u000f\u0010,\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008+\u0010)J\u000f\u0010.\u001a\u00020-H\u0000\u00a2\u0006\u0004\u0008.\u0010/J\u001f\u00101\u001a\u00020\u000c2\u000e\u0008\u0002\u00100\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u0014H\u0000\u00a2\u0006\u0004\u00081\u00102J\'\u00103\u001a\u00020\u000c2\u0006\u0010\u0019\u001a\u00020\u00062\u000e\u0008\u0002\u00100\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u0014H\u0000\u00a2\u0006\u0004\u00083\u00104J\'\u00107\u001a\u00020\u000c2\u0006\u00106\u001a\u0002052\u000e\u0008\u0002\u00100\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u0014H\u0000\u00a2\u0006\u0004\u00087\u00108J\u0017\u0010;\u001a\u00020\u000c2\u0006\u00109\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008:\u0010\u001bJ\u000f\u0010=\u001a\u00020\u000cH\u0000\u00a2\u0006\u0004\u0008<\u0010\u0010J\u000f\u0010>\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008>\u0010\u0010J\u001d\u0010?\u001a\u00020\u000c2\u000c\u00100\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u0014H\u0002\u00a2\u0006\u0004\u0008?\u00102J\u000f\u0010A\u001a\u00020@H\u0002\u00a2\u0006\u0004\u0008A\u0010BJ\u000f\u00106\u001a\u00020@H\u0002\u00a2\u0006\u0004\u00086\u0010BJ\u000f\u0010D\u001a\u00020CH\u0002\u00a2\u0006\u0004\u0008D\u0010EJ\u000f\u0010F\u001a\u00020CH\u0002\u00a2\u0006\u0004\u0008F\u0010EJ\u000f\u0010G\u001a\u00020CH\u0002\u00a2\u0006\u0004\u0008G\u0010EJ\u000f\u0010H\u001a\u00020CH\u0002\u00a2\u0006\u0004\u0008H\u0010EJ\u000f\u0010I\u001a\u00020CH\u0002\u00a2\u0006\u0004\u0008I\u0010EJ\u000f\u0010J\u001a\u00020CH\u0002\u00a2\u0006\u0004\u0008J\u0010EJ\u000f\u0010K\u001a\u00020CH\u0002\u00a2\u0006\u0004\u0008K\u0010ER\u001b\u0010O\u001a\u00020@8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008L\u0010M\u001a\u0004\u0008N\u0010BR\u001b\u0010R\u001a\u00020@8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008P\u0010M\u001a\u0004\u0008Q\u0010BR\u001c\u0010U\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u00148\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008S\u0010TR\u0016\u0010X\u001a\u00020-8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008V\u0010WR\u001c\u0010\u0015\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u00148\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008Y\u0010TR\u0016\u00109\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008Z\u00101R\u0016\u0010\\\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008[\u00101R\u0016\u0010\u0012\u001a\u00020\u00118\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008]\u0010^R\u0016\u0010!\u001a\u00020 8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008_\u0010`\u00a8\u0006b"
    }
    d2 = {
        "Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "onDetachedFromWindow",
        "()V",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
        "gameType",
        "cellPaddingResId",
        "Lkotlin/Function0;",
        "onCellClick",
        "K",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;ILkotlin/jvm/functions/Function0;)V",
        "M",
        "newRow",
        "setRow$tile_matching_release",
        "(I)V",
        "setRow",
        "newColumn",
        "setColumn$tile_matching_release",
        "setColumn",
        "Lorg/xbet/tile_matching/domain/models/TileMatchingType;",
        "type",
        "setType$tile_matching_release",
        "(Lorg/xbet/tile_matching/domain/models/TileMatchingType;)V",
        "setType",
        "getType$tile_matching_release",
        "()Lorg/xbet/tile_matching/domain/models/TileMatchingType;",
        "getType",
        "getRow$tile_matching_release",
        "()I",
        "getRow",
        "getColumn$tile_matching_release",
        "getColumn",
        "",
        "L",
        "()Z",
        "onEnd",
        "I",
        "(Lkotlin/jvm/functions/Function0;)V",
        "Q",
        "(ILkotlin/jvm/functions/Function0;)V",
        "",
        "y",
        "P",
        "(FLkotlin/jvm/functions/Function0;)V",
        "row",
        "setYByLine$tile_matching_release",
        "setYByLine",
        "setDefault$tile_matching_release",
        "setDefault",
        "V",
        "setDefaultScale",
        "Landroid/animation/Animator;",
        "G",
        "()Landroid/animation/Animator;",
        "Landroid/animation/ObjectAnimator;",
        "B",
        "()Landroid/animation/ObjectAnimator;",
        "E",
        "C",
        "F",
        "A",
        "D",
        "x",
        "a",
        "Lkotlin/j;",
        "getWinAnimator",
        "winAnimator",
        "b",
        "getDisappearAnimator",
        "disappearAnimator",
        "c",
        "Lkotlin/jvm/functions/Function0;",
        "onDisappearEnd",
        "d",
        "Z",
        "win",
        "e",
        "f",
        "g",
        "column",
        "h",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
        "i",
        "Lorg/xbet/tile_matching/domain/models/TileMatchingType;",
        "j",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final j:Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:Z

.field public e:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:I

.field public g:I

.field public h:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public i:Lorg/xbet/tile_matching/domain/models/TileMatchingType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->j:Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView$a;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroidx/appcompat/widget/AppCompatImageView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance p1, LCT0/d;

    invoke-direct {p1, p0}, LCT0/d;-><init>(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)V

    invoke-static {p1}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->a:Lkotlin/j;

    .line 6
    new-instance p1, LCT0/e;

    invoke-direct {p1, p0}, LCT0/e;-><init>(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)V

    invoke-static {p1}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->b:Lkotlin/j;

    .line 7
    new-instance p1, LCT0/f;

    invoke-direct {p1}, LCT0/f;-><init>()V

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->c:Lkotlin/jvm/functions/Function0;

    .line 8
    new-instance p1, LCT0/g;

    invoke-direct {p1}, LCT0/g;-><init>()V

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->e:Lkotlin/jvm/functions/Function0;

    .line 9
    sget-object p1, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->FRUIT_BLAST:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->h:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 10
    sget-object p1, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->CELL_ONE:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->i:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    const/4 p1, 0x0

    .line 11
    invoke-virtual {p0, p1}, Landroid/view/View;->setEnabled(Z)V

    .line 12
    sget-object p1, Landroid/widget/ImageView$ScaleType;->FIT_XY:Landroid/widget/ImageView$ScaleType;

    invoke-virtual {p0, p1}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 13
    new-instance p1, LCT0/h;

    invoke-direct {p1, p0}, LCT0/h;-><init>(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)V

    invoke-virtual {p0, p1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static final H(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getDisappearAnimator()Landroid/animation/Animator;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Landroid/animation/Animator;->start()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final J(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)Landroid/animation/Animator;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->y()Landroid/animation/Animator;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final N(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final O(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final R()Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final S()Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final T(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;Landroid/animation/ValueAnimator;)V
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->getAnimatedValue()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Ljava/lang/Float;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    invoke-virtual {p0, v0}, Landroid/view/View;->setScaleX(F)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->getAnimatedValue()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    check-cast p1, Ljava/lang/Float;

    .line 19
    .line 20
    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    .line 21
    .line 22
    .line 23
    move-result p1

    .line 24
    invoke-virtual {p0, p1}, Landroid/view/View;->setScaleY(F)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public static final U(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final W(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)Landroid/animation/Animator;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->G()Landroid/animation/Animator;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final getDisappearAnimator()Landroid/animation/Animator;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->b:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/animation/Animator;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getWinAnimator()Landroid/animation/Animator;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->a:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/animation/Animator;

    .line 8
    .line 9
    return-object v0
.end method

.method public static synthetic i(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->N(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic k(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->U(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic l(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->O(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic m(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)Landroid/animation/Animator;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->J(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)Landroid/animation/Animator;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic n(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->w(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic o(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)Landroid/animation/Animator;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->W(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)Landroid/animation/Animator;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->H(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s()Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->R()Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method

.method private final setDefaultScale(Lkotlin/jvm/functions/Function0;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getScaleX()F

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/high16 v1, 0x3f800000    # 1.0f

    .line 6
    .line 7
    cmpg-float v0, v0, v1

    .line 8
    .line 9
    if-nez v0, :cond_0

    .line 10
    .line 11
    invoke-interface {p1}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getScaleX()F

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    const/4 v2, 0x2

    .line 20
    new-array v2, v2, [F

    .line 21
    .line 22
    const/4 v3, 0x0

    .line 23
    aput v0, v2, v3

    .line 24
    .line 25
    const/4 v0, 0x1

    .line 26
    aput v1, v2, v0

    .line 27
    .line 28
    invoke-static {v2}, Landroid/animation/ValueAnimator;->ofFloat([F)Landroid/animation/ValueAnimator;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    new-instance v1, LCT0/a;

    .line 33
    .line 34
    invoke-direct {v1, p0}, LCT0/a;-><init>(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)V

    .line 35
    .line 36
    .line 37
    invoke-virtual {v0, v1}, Landroid/animation/ValueAnimator;->addUpdateListener(Landroid/animation/ValueAnimator$AnimatorUpdateListener;)V

    .line 38
    .line 39
    .line 40
    new-instance v2, Lqb/m;

    .line 41
    .line 42
    new-instance v5, LCT0/c;

    .line 43
    .line 44
    invoke-direct {v5, p1}, LCT0/c;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 45
    .line 46
    .line 47
    const/16 v7, 0xb

    .line 48
    .line 49
    const/4 v8, 0x0

    .line 50
    const/4 v3, 0x0

    .line 51
    const/4 v4, 0x0

    .line 52
    const/4 v6, 0x0

    .line 53
    invoke-direct/range {v2 .. v8}, Lqb/m;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 54
    .line 55
    .line 56
    invoke-virtual {v0, v2}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 57
    .line 58
    .line 59
    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->start()V

    .line 60
    .line 61
    .line 62
    return-void
.end method

.method public static synthetic t()Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->S()Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic u(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;Landroid/animation/ValueAnimator;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->T(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;Landroid/animation/ValueAnimator;)V

    return-void
.end method

.method public static synthetic v(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->z(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final w(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;Landroid/view/View;)V
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->e:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public static final z(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->c:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p0
.end method


# virtual methods
.method public final A()Landroid/animation/ObjectAnimator;
    .locals 3

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getScaleX()F

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x2

    .line 6
    new-array v1, v1, [F

    .line 7
    .line 8
    const/4 v2, 0x0

    .line 9
    aput v0, v1, v2

    .line 10
    .line 11
    const v0, 0x3ecccccd

    .line 12
    .line 13
    .line 14
    const/4 v2, 0x1

    .line 15
    aput v0, v1, v2

    .line 16
    .line 17
    sget-object v0, Landroid/view/View;->SCALE_X:Landroid/util/Property;

    .line 18
    .line 19
    invoke-static {p0, v0, v1}, Landroid/animation/ObjectAnimator;->ofFloat(Ljava/lang/Object;Landroid/util/Property;[F)Landroid/animation/ObjectAnimator;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    const-wide/16 v1, 0x28a

    .line 24
    .line 25
    invoke-virtual {v0, v1, v2}, Landroid/animation/ObjectAnimator;->setDuration(J)Landroid/animation/ObjectAnimator;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    return-object v0
.end method

.method public final B()Landroid/animation/ObjectAnimator;
    .locals 4

    .line 1
    const/4 v0, 0x2

    .line 2
    new-array v1, v0, [F

    .line 3
    .line 4
    fill-array-data v1, :array_0

    .line 5
    .line 6
    .line 7
    sget-object v2, Landroid/view/View;->SCALE_X:Landroid/util/Property;

    .line 8
    .line 9
    invoke-static {p0, v2, v1}, Landroid/animation/ObjectAnimator;->ofFloat(Ljava/lang/Object;Landroid/util/Property;[F)Landroid/animation/ObjectAnimator;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    const-wide/16 v2, 0x1f4

    .line 14
    .line 15
    invoke-virtual {v1, v2, v3}, Landroid/animation/ObjectAnimator;->setDuration(J)Landroid/animation/ObjectAnimator;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    const/4 v2, -0x1

    .line 20
    invoke-virtual {v1, v2}, Landroid/animation/ValueAnimator;->setRepeatCount(I)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v1, v0}, Landroid/animation/ValueAnimator;->setRepeatMode(I)V

    .line 24
    .line 25
    .line 26
    return-object v1

    .line 27
    :array_0
    .array-data 4
        0x3f800000    # 1.0f
        0x3f4ccccd
    .end array-data
.end method

.method public final C()Landroid/animation/ObjectAnimator;
    .locals 4

    .line 1
    const/4 v0, 0x2

    .line 2
    new-array v1, v0, [F

    .line 3
    .line 4
    fill-array-data v1, :array_0

    .line 5
    .line 6
    .line 7
    sget-object v2, Landroid/view/View;->SCALE_X:Landroid/util/Property;

    .line 8
    .line 9
    invoke-static {p0, v2, v1}, Landroid/animation/ObjectAnimator;->ofFloat(Ljava/lang/Object;Landroid/util/Property;[F)Landroid/animation/ObjectAnimator;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    const-wide/16 v2, 0x1f4

    .line 14
    .line 15
    invoke-virtual {v1, v2, v3}, Landroid/animation/ObjectAnimator;->setDuration(J)Landroid/animation/ObjectAnimator;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    const/4 v2, -0x1

    .line 20
    invoke-virtual {v1, v2}, Landroid/animation/ValueAnimator;->setRepeatCount(I)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v1, v0}, Landroid/animation/ValueAnimator;->setRepeatMode(I)V

    .line 24
    .line 25
    .line 26
    return-object v1

    .line 27
    :array_0
    .array-data 4
        0x3f4ccccd
        0x3f800000    # 1.0f
    .end array-data
.end method

.method public final D()Landroid/animation/ObjectAnimator;
    .locals 3

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getScaleY()F

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x2

    .line 6
    new-array v1, v1, [F

    .line 7
    .line 8
    const/4 v2, 0x0

    .line 9
    aput v0, v1, v2

    .line 10
    .line 11
    const v0, 0x3ecccccd

    .line 12
    .line 13
    .line 14
    const/4 v2, 0x1

    .line 15
    aput v0, v1, v2

    .line 16
    .line 17
    sget-object v0, Landroid/view/View;->SCALE_Y:Landroid/util/Property;

    .line 18
    .line 19
    invoke-static {p0, v0, v1}, Landroid/animation/ObjectAnimator;->ofFloat(Ljava/lang/Object;Landroid/util/Property;[F)Landroid/animation/ObjectAnimator;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    const-wide/16 v1, 0x28a

    .line 24
    .line 25
    invoke-virtual {v0, v1, v2}, Landroid/animation/ObjectAnimator;->setDuration(J)Landroid/animation/ObjectAnimator;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    return-object v0
.end method

.method public final E()Landroid/animation/ObjectAnimator;
    .locals 4

    .line 1
    const/4 v0, 0x2

    .line 2
    new-array v1, v0, [F

    .line 3
    .line 4
    fill-array-data v1, :array_0

    .line 5
    .line 6
    .line 7
    sget-object v2, Landroid/view/View;->SCALE_Y:Landroid/util/Property;

    .line 8
    .line 9
    invoke-static {p0, v2, v1}, Landroid/animation/ObjectAnimator;->ofFloat(Ljava/lang/Object;Landroid/util/Property;[F)Landroid/animation/ObjectAnimator;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    const-wide/16 v2, 0x1f4

    .line 14
    .line 15
    invoke-virtual {v1, v2, v3}, Landroid/animation/ObjectAnimator;->setDuration(J)Landroid/animation/ObjectAnimator;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    const/4 v2, -0x1

    .line 20
    invoke-virtual {v1, v2}, Landroid/animation/ValueAnimator;->setRepeatCount(I)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v1, v0}, Landroid/animation/ValueAnimator;->setRepeatMode(I)V

    .line 24
    .line 25
    .line 26
    return-object v1

    .line 27
    :array_0
    .array-data 4
        0x3f800000    # 1.0f
        0x3f4ccccd
    .end array-data
.end method

.method public final F()Landroid/animation/ObjectAnimator;
    .locals 4

    .line 1
    const/4 v0, 0x2

    .line 2
    new-array v1, v0, [F

    .line 3
    .line 4
    fill-array-data v1, :array_0

    .line 5
    .line 6
    .line 7
    sget-object v2, Landroid/view/View;->SCALE_Y:Landroid/util/Property;

    .line 8
    .line 9
    invoke-static {p0, v2, v1}, Landroid/animation/ObjectAnimator;->ofFloat(Ljava/lang/Object;Landroid/util/Property;[F)Landroid/animation/ObjectAnimator;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    const-wide/16 v2, 0x1f4

    .line 14
    .line 15
    invoke-virtual {v1, v2, v3}, Landroid/animation/ObjectAnimator;->setDuration(J)Landroid/animation/ObjectAnimator;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    const/4 v2, -0x1

    .line 20
    invoke-virtual {v1, v2}, Landroid/animation/ValueAnimator;->setRepeatCount(I)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v1, v0}, Landroid/animation/ValueAnimator;->setRepeatMode(I)V

    .line 24
    .line 25
    .line 26
    return-object v1

    .line 27
    :array_0
    .array-data 4
        0x3f4ccccd
        0x3f800000    # 1.0f
    .end array-data
.end method

.method public final G()Landroid/animation/Animator;
    .locals 9

    .line 1
    new-instance v0, Landroid/animation/AnimatorSet;

    .line 2
    .line 3
    invoke-direct {v0}, Landroid/animation/AnimatorSet;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Landroid/animation/AnimatorSet;

    .line 7
    .line 8
    invoke-direct {v1}, Landroid/animation/AnimatorSet;-><init>()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->B()Landroid/animation/ObjectAnimator;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->E()Landroid/animation/ObjectAnimator;

    .line 16
    .line 17
    .line 18
    move-result-object v3

    .line 19
    const/4 v4, 0x2

    .line 20
    new-array v5, v4, [Landroid/animation/Animator;

    .line 21
    .line 22
    const/4 v6, 0x0

    .line 23
    aput-object v2, v5, v6

    .line 24
    .line 25
    const/4 v2, 0x1

    .line 26
    aput-object v3, v5, v2

    .line 27
    .line 28
    invoke-virtual {v1, v5}, Landroid/animation/AnimatorSet;->playTogether([Landroid/animation/Animator;)V

    .line 29
    .line 30
    .line 31
    sget-object v3, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 32
    .line 33
    new-instance v3, Landroid/animation/AnimatorSet;

    .line 34
    .line 35
    invoke-direct {v3}, Landroid/animation/AnimatorSet;-><init>()V

    .line 36
    .line 37
    .line 38
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->C()Landroid/animation/ObjectAnimator;

    .line 39
    .line 40
    .line 41
    move-result-object v5

    .line 42
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->F()Landroid/animation/ObjectAnimator;

    .line 43
    .line 44
    .line 45
    move-result-object v7

    .line 46
    new-array v8, v4, [Landroid/animation/Animator;

    .line 47
    .line 48
    aput-object v5, v8, v6

    .line 49
    .line 50
    aput-object v7, v8, v2

    .line 51
    .line 52
    invoke-virtual {v3, v8}, Landroid/animation/AnimatorSet;->playTogether([Landroid/animation/Animator;)V

    .line 53
    .line 54
    .line 55
    new-array v4, v4, [Landroid/animation/Animator;

    .line 56
    .line 57
    aput-object v1, v4, v6

    .line 58
    .line 59
    aput-object v3, v4, v2

    .line 60
    .line 61
    invoke-virtual {v0, v4}, Landroid/animation/AnimatorSet;->playSequentially([Landroid/animation/Animator;)V

    .line 62
    .line 63
    .line 64
    return-object v0
.end method

.method public final I(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getWinAnimator()Landroid/animation/Animator;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/animation/Animator;->cancel()V

    .line 6
    .line 7
    .line 8
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->c:Lkotlin/jvm/functions/Function0;

    .line 9
    .line 10
    new-instance p1, LCT0/j;

    .line 11
    .line 12
    invoke-direct {p1, p0}, LCT0/j;-><init>(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)V

    .line 13
    .line 14
    .line 15
    invoke-direct {p0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->setDefaultScale(Lkotlin/jvm/functions/Function0;)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public final K(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;ILkotlin/jvm/functions/Function0;)V
    .locals 0
    .param p1    # Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
            "I",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->h:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    invoke-virtual {p0, p1, p1, p1, p1}, Landroid/view/View;->setPadding(IIII)V

    .line 12
    .line 13
    .line 14
    iput-object p3, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->e:Lkotlin/jvm/functions/Function0;

    .line 15
    .line 16
    return-void
.end method

.method public final L()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->d:Z

    .line 2
    .line 3
    return v0
.end method

.method public final M()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->h:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 2
    .line 3
    invoke-static {v0}, LBT0/a;->c(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)[I

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->i:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 8
    .line 9
    invoke-virtual {v1}, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->getNumber()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    aget v0, v0, v1

    .line 14
    .line 15
    invoke-virtual {p0, v0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 16
    .line 17
    .line 18
    const/4 v0, 0x1

    .line 19
    iput-boolean v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->d:Z

    .line 20
    .line 21
    invoke-virtual {p0, v0}, Landroid/view/View;->setEnabled(Z)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->V()V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public final P(FLkotlin/jvm/functions/Function0;)V
    .locals 7
    .param p2    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(F",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    sget-object v0, Landroid/view/View;->Y:Landroid/util/Property;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    new-array v1, v1, [F

    .line 5
    .line 6
    const/4 v2, 0x0

    .line 7
    aput p1, v1, v2

    .line 8
    .line 9
    invoke-static {p0, v0, v1}, Landroid/animation/ObjectAnimator;->ofFloat(Ljava/lang/Object;Landroid/util/Property;[F)Landroid/animation/ObjectAnimator;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    new-instance v0, Lqb/m;

    .line 14
    .line 15
    new-instance v3, LCT0/b;

    .line 16
    .line 17
    invoke-direct {v3, p2}, LCT0/b;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 18
    .line 19
    .line 20
    const/16 v5, 0xb

    .line 21
    .line 22
    const/4 v6, 0x0

    .line 23
    const/4 v1, 0x0

    .line 24
    const/4 v2, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-direct/range {v0 .. v6}, Lqb/m;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p1, v0}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 30
    .line 31
    .line 32
    const-wide/16 v0, 0x28a

    .line 33
    .line 34
    invoke-virtual {p1, v0, v1}, Landroid/animation/ObjectAnimator;->setDuration(J)Landroid/animation/ObjectAnimator;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    invoke-virtual {p1}, Landroid/animation/ObjectAnimator;->start()V

    .line 39
    .line 40
    .line 41
    return-void
.end method

.method public final Q(ILkotlin/jvm/functions/Function0;)V
    .locals 7
    .param p2    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    int-to-float v0, p1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getHeight()I

    .line 3
    .line 4
    .line 5
    move-result v1

    .line 6
    int-to-float v1, v1

    .line 7
    mul-float v0, v0, v1

    .line 8
    .line 9
    iput p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->f:I

    .line 10
    .line 11
    sget-object p1, Landroid/view/View;->Y:Landroid/util/Property;

    .line 12
    .line 13
    const/4 v1, 0x1

    .line 14
    new-array v1, v1, [F

    .line 15
    .line 16
    const/4 v2, 0x0

    .line 17
    aput v0, v1, v2

    .line 18
    .line 19
    invoke-static {p0, p1, v1}, Landroid/animation/ObjectAnimator;->ofFloat(Ljava/lang/Object;Landroid/util/Property;[F)Landroid/animation/ObjectAnimator;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    new-instance v0, Lqb/m;

    .line 24
    .line 25
    new-instance v3, LCT0/k;

    .line 26
    .line 27
    invoke-direct {v3, p2}, LCT0/k;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 28
    .line 29
    .line 30
    const/16 v5, 0xb

    .line 31
    .line 32
    const/4 v6, 0x0

    .line 33
    const/4 v1, 0x0

    .line 34
    const/4 v2, 0x0

    .line 35
    const/4 v4, 0x0

    .line 36
    invoke-direct/range {v0 .. v6}, Lqb/m;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p1, v0}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 40
    .line 41
    .line 42
    const-wide/16 v0, 0x28a

    .line 43
    .line 44
    invoke-virtual {p1, v0, v1}, Landroid/animation/ObjectAnimator;->setDuration(J)Landroid/animation/ObjectAnimator;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    invoke-virtual {p1}, Landroid/animation/ObjectAnimator;->start()V

    .line 49
    .line 50
    .line 51
    return-void
.end method

.method public final V()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getDisappearAnimator()Landroid/animation/Animator;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/animation/Animator;->cancel()V

    .line 6
    .line 7
    .line 8
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getWinAnimator()Landroid/animation/Animator;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Landroid/animation/Animator;->start()V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final getColumn$tile_matching_release()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->g:I

    .line 2
    .line 3
    return v0
.end method

.method public final getRow$tile_matching_release()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->f:I

    .line 2
    .line 3
    return v0
.end method

.method public final getType$tile_matching_release()Lorg/xbet/tile_matching/domain/models/TileMatchingType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->i:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 2
    .line 3
    return-object v0
.end method

.method public onDetachedFromWindow()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroid/widget/ImageView;->onDetachedFromWindow()V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getDisappearAnimator()Landroid/animation/Animator;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Landroid/animation/Animator;->cancel()V

    .line 9
    .line 10
    .line 11
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getWinAnimator()Landroid/animation/Animator;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-virtual {v0}, Landroid/animation/Animator;->cancel()V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public onMeasure(II)V
    .locals 0

    .line 1
    invoke-super {p0, p1, p2}, Landroid/widget/ImageView;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 9
    .line 10
    .line 11
    move-result p2

    .line 12
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final setColumn$tile_matching_release(I)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->g:I

    .line 2
    .line 3
    return-void
.end method

.method public final setDefault$tile_matching_release()V
    .locals 2

    .line 1
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getWinAnimator()Landroid/animation/Animator;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/animation/Animator;->cancel()V

    .line 6
    .line 7
    .line 8
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->getDisappearAnimator()Landroid/animation/Animator;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Landroid/animation/Animator;->cancel()V

    .line 13
    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->h:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 16
    .line 17
    invoke-static {v0}, LBT0/a;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)[I

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->i:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 22
    .line 23
    invoke-virtual {v1}, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->getNumber()I

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    aget v0, v0, v1

    .line 28
    .line 29
    invoke-virtual {p0, v0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 30
    .line 31
    .line 32
    const/4 v0, 0x0

    .line 33
    invoke-virtual {p0, v0}, Landroid/view/View;->setEnabled(Z)V

    .line 34
    .line 35
    .line 36
    iput-boolean v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->d:Z

    .line 37
    .line 38
    const/high16 v0, 0x3f800000    # 1.0f

    .line 39
    .line 40
    invoke-virtual {p0, v0}, Landroid/view/View;->setAlpha(F)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p0, v0}, Landroid/view/View;->setScaleX(F)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {p0, v0}, Landroid/view/View;->setScaleY(F)V

    .line 47
    .line 48
    .line 49
    return-void
.end method

.method public final setRow$tile_matching_release(I)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->f:I

    .line 2
    .line 3
    return-void
.end method

.method public final setType$tile_matching_release(Lorg/xbet/tile_matching/domain/models/TileMatchingType;)V
    .locals 0
    .param p1    # Lorg/xbet/tile_matching/domain/models/TileMatchingType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->i:Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->setDefault$tile_matching_release()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setYByLine$tile_matching_release(I)V
    .locals 1

    .line 1
    int-to-float p1, p1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getHeight()I

    .line 3
    .line 4
    .line 5
    move-result v0

    .line 6
    int-to-float v0, v0

    .line 7
    mul-float p1, p1, v0

    .line 8
    .line 9
    invoke-virtual {p0, p1}, Landroid/view/View;->setY(F)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final x()Landroid/animation/ObjectAnimator;
    .locals 3

    .line 1
    const/4 v0, 0x2

    .line 2
    new-array v0, v0, [F

    .line 3
    .line 4
    fill-array-data v0, :array_0

    .line 5
    .line 6
    .line 7
    sget-object v1, Landroid/view/View;->ALPHA:Landroid/util/Property;

    .line 8
    .line 9
    invoke-static {p0, v1, v0}, Landroid/animation/ObjectAnimator;->ofFloat(Ljava/lang/Object;Landroid/util/Property;[F)Landroid/animation/ObjectAnimator;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    const-wide/16 v1, 0x28a

    .line 14
    .line 15
    invoke-virtual {v0, v1, v2}, Landroid/animation/ObjectAnimator;->setDuration(J)Landroid/animation/ObjectAnimator;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    return-object v0

    .line 20
    nop

    .line 21
    :array_0
    .array-data 4
        0x3f800000    # 1.0f
        0x0
    .end array-data
.end method

.method public final y()Landroid/animation/Animator;
    .locals 14

    .line 1
    new-instance v0, Landroid/animation/AnimatorSet;

    .line 2
    .line 3
    invoke-direct {v0}, Landroid/animation/AnimatorSet;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Landroid/animation/AnimatorSet;

    .line 7
    .line 8
    invoke-direct {v1}, Landroid/animation/AnimatorSet;-><init>()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->A()Landroid/animation/ObjectAnimator;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->D()Landroid/animation/ObjectAnimator;

    .line 16
    .line 17
    .line 18
    move-result-object v3

    .line 19
    const/4 v4, 0x2

    .line 20
    new-array v5, v4, [Landroid/animation/Animator;

    .line 21
    .line 22
    const/4 v6, 0x0

    .line 23
    aput-object v2, v5, v6

    .line 24
    .line 25
    const/4 v2, 0x1

    .line 26
    aput-object v3, v5, v2

    .line 27
    .line 28
    invoke-virtual {v1, v5}, Landroid/animation/AnimatorSet;->playTogether([Landroid/animation/Animator;)V

    .line 29
    .line 30
    .line 31
    sget-object v3, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 32
    .line 33
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;->x()Landroid/animation/ObjectAnimator;

    .line 34
    .line 35
    .line 36
    move-result-object v3

    .line 37
    new-array v4, v4, [Landroid/animation/Animator;

    .line 38
    .line 39
    aput-object v1, v4, v6

    .line 40
    .line 41
    aput-object v3, v4, v2

    .line 42
    .line 43
    invoke-virtual {v0, v4}, Landroid/animation/AnimatorSet;->playTogether([Landroid/animation/Animator;)V

    .line 44
    .line 45
    .line 46
    new-instance v7, Lqb/m;

    .line 47
    .line 48
    new-instance v10, LCT0/i;

    .line 49
    .line 50
    invoke-direct {v10, p0}, LCT0/i;-><init>(Lorg/xbet/tile_matching/presentation/views/TileMatchingCellView;)V

    .line 51
    .line 52
    .line 53
    const/16 v12, 0xb

    .line 54
    .line 55
    const/4 v13, 0x0

    .line 56
    const/4 v8, 0x0

    .line 57
    const/4 v9, 0x0

    .line 58
    const/4 v11, 0x0

    .line 59
    invoke-direct/range {v7 .. v13}, Lqb/m;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 60
    .line 61
    .line 62
    invoke-virtual {v0, v7}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 63
    .line 64
    .line 65
    return-object v0
.end method
