.class public final Lcom/sumsub/sns/R$attr;
.super Ljava/lang/Object;


# static fields
.field public static SharedValue:I = 0x7f04000c

.field public static SharedValueId:I = 0x7f04000d

.field public static actionBarDivider:I = 0x7f04001c

.field public static actionBarItemBackground:I = 0x7f04001d

.field public static actionBarPopupTheme:I = 0x7f04001e

.field public static actionBarSize:I = 0x7f04001f

.field public static actionBarSplitStyle:I = 0x7f040020

.field public static actionBarStyle:I = 0x7f040021

.field public static actionBarTabBarStyle:I = 0x7f040022

.field public static actionBarTabStyle:I = 0x7f040023

.field public static actionBarTabTextStyle:I = 0x7f040024

.field public static actionBarTheme:I = 0x7f040025

.field public static actionBarWidgetTheme:I = 0x7f040026

.field public static actionButtonStyle:I = 0x7f040027

.field public static actionDropDownStyle:I = 0x7f040029

.field public static actionLayout:I = 0x7f04002d

.field public static actionMenuTextAppearance:I = 0x7f04002e

.field public static actionMenuTextColor:I = 0x7f04002f

.field public static actionModeBackground:I = 0x7f040030

.field public static actionModeCloseButtonStyle:I = 0x7f040031

.field public static actionModeCloseContentDescription:I = 0x7f040032

.field public static actionModeCloseDrawable:I = 0x7f040033

.field public static actionModeCopyDrawable:I = 0x7f040034

.field public static actionModeCutDrawable:I = 0x7f040035

.field public static actionModeFindDrawable:I = 0x7f040036

.field public static actionModePasteDrawable:I = 0x7f040037

.field public static actionModePopupWindowStyle:I = 0x7f040038

.field public static actionModeSelectAllDrawable:I = 0x7f040039

.field public static actionModeShareDrawable:I = 0x7f04003a

.field public static actionModeSplitBackground:I = 0x7f04003b

.field public static actionModeStyle:I = 0x7f04003c

.field public static actionModeTheme:I = 0x7f04003d

.field public static actionModeWebSearchDrawable:I = 0x7f04003e

.field public static actionOverflowButtonStyle:I = 0x7f04003f

.field public static actionOverflowMenuStyle:I = 0x7f040040

.field public static actionProviderClass:I = 0x7f040041

.field public static actionTextColorAlpha:I = 0x7f040042

.field public static actionViewClass:I = 0x7f040043

.field public static activeIndicatorLabelPadding:I = 0x7f040044

.field public static activityChooserViewStyle:I = 0x7f040047

.field public static addElevationShadow:I = 0x7f04004b

.field public static alertDialogButtonGroupStyle:I = 0x7f040052

.field public static alertDialogCenterButtons:I = 0x7f040053

.field public static alertDialogStyle:I = 0x7f040054

.field public static alertDialogTheme:I = 0x7f040055

.field public static allowStacking:I = 0x7f04005e

.field public static alpha:I = 0x7f04005f

.field public static alphabeticModifiers:I = 0x7f040061

.field public static altSrc:I = 0x7f040064

.field public static animateCircleAngleTo:I = 0x7f04006a

.field public static animateMenuItems:I = 0x7f04006b

.field public static animateNavigationIcon:I = 0x7f04006c

.field public static animateRelativeTo:I = 0x7f04006d

.field public static animationMode:I = 0x7f04006f

.field public static appBarLayoutStyle:I = 0x7f040071

.field public static applyMotionScene:I = 0x7f040076

.field public static arcMode:I = 0x7f040077

.field public static arrowHeadLength:I = 0x7f040078

.field public static arrowShaftLength:I = 0x7f040079

.field public static attributeName:I = 0x7f04007e

.field public static autoAdjustToWithinGrandparentBounds:I = 0x7f04007f

.field public static autoCompleteMode:I = 0x7f040080

.field public static autoCompleteTextViewStyle:I = 0x7f040081

.field public static autoShowKeyboard:I = 0x7f040083

.field public static autoSizeMaxTextSize:I = 0x7f040085

.field public static autoSizeMinTextSize:I = 0x7f040086

.field public static autoSizePresetSizes:I = 0x7f040087

.field public static autoSizeStepGranularity:I = 0x7f040088

.field public static autoSizeTextType:I = 0x7f040089

.field public static autoTransition:I = 0x7f04008b

.field public static backHandlingEnabled:I = 0x7f040094

.field public static background:I = 0x7f040096

.field public static backgroundColor:I = 0x7f040098

.field public static backgroundInsetBottom:I = 0x7f04009a

.field public static backgroundInsetEnd:I = 0x7f04009b

.field public static backgroundInsetStart:I = 0x7f04009c

.field public static backgroundInsetTop:I = 0x7f04009d

.field public static backgroundOverlayColorAlpha:I = 0x7f04009f

.field public static backgroundSplit:I = 0x7f0400a2

.field public static backgroundStacked:I = 0x7f0400a3

.field public static backgroundTint:I = 0x7f0400a5

.field public static backgroundTintMode:I = 0x7f0400a6

.field public static badgeGravity:I = 0x7f0400ab

.field public static badgeHeight:I = 0x7f0400ac

.field public static badgeRadius:I = 0x7f0400ae

.field public static badgeShapeAppearance:I = 0x7f0400b0

.field public static badgeShapeAppearanceOverlay:I = 0x7f0400b1

.field public static badgeStyle:I = 0x7f0400b3

.field public static badgeText:I = 0x7f0400b4

.field public static badgeTextAppearance:I = 0x7f0400b5

.field public static badgeTextColor:I = 0x7f0400b6

.field public static badgeVerticalPadding:I = 0x7f0400b8

.field public static badgeWidePadding:I = 0x7f0400b9

.field public static badgeWidth:I = 0x7f0400ba

.field public static badgeWithTextHeight:I = 0x7f0400bb

.field public static badgeWithTextRadius:I = 0x7f0400bc

.field public static badgeWithTextShapeAppearance:I = 0x7f0400bd

.field public static badgeWithTextShapeAppearanceOverlay:I = 0x7f0400be

.field public static badgeWithTextWidth:I = 0x7f0400bf

.field public static barLength:I = 0x7f0400c4

.field public static barrierAllowsGoneWidgets:I = 0x7f0400c7

.field public static barrierDirection:I = 0x7f0400c8

.field public static barrierMargin:I = 0x7f0400c9

.field public static behavior_autoHide:I = 0x7f0400ca

.field public static behavior_autoShrink:I = 0x7f0400cb

.field public static behavior_draggable:I = 0x7f0400cc

.field public static behavior_expandedOffset:I = 0x7f0400cd

.field public static behavior_fitToContents:I = 0x7f0400ce

.field public static behavior_halfExpandedRatio:I = 0x7f0400cf

.field public static behavior_hideable:I = 0x7f0400d0

.field public static behavior_overlapTop:I = 0x7f0400d1

.field public static behavior_peekHeight:I = 0x7f0400d2

.field public static behavior_saveFlags:I = 0x7f0400d3

.field public static behavior_significantVelocityThreshold:I = 0x7f0400d4

.field public static behavior_skipCollapsed:I = 0x7f0400d5

.field public static blendSrc:I = 0x7f0400d8

.field public static borderRound:I = 0x7f0400e6

.field public static borderRoundPercent:I = 0x7f0400e7

.field public static borderWidth:I = 0x7f0400e8

.field public static borderlessButtonStyle:I = 0x7f0400e9

.field public static bottomAppBarStyle:I = 0x7f0400ea

.field public static bottomInsetScrimEnabled:I = 0x7f0400f1

.field public static bottomNavigationStyle:I = 0x7f0400f3

.field public static bottomSheetDialogTheme:I = 0x7f0400f5

.field public static bottomSheetDragHandleStyle:I = 0x7f0400f6

.field public static bottomSheetStyle:I = 0x7f0400f7

.field public static boxBackgroundColor:I = 0x7f0400fa

.field public static boxBackgroundMode:I = 0x7f0400fb

.field public static boxCollapsedPaddingTop:I = 0x7f0400fc

.field public static boxCornerRadiusBottomEnd:I = 0x7f0400fd

.field public static boxCornerRadiusBottomStart:I = 0x7f0400fe

.field public static boxCornerRadiusTopEnd:I = 0x7f0400ff

.field public static boxCornerRadiusTopStart:I = 0x7f040100

.field public static boxStrokeColor:I = 0x7f040101

.field public static boxStrokeErrorColor:I = 0x7f040102

.field public static boxStrokeWidth:I = 0x7f040103

.field public static boxStrokeWidthFocused:I = 0x7f040104

.field public static brightness:I = 0x7f040105

.field public static buttonBarButtonStyle:I = 0x7f04010a

.field public static buttonBarNegativeButtonStyle:I = 0x7f04010b

.field public static buttonBarNeutralButtonStyle:I = 0x7f04010c

.field public static buttonBarPositiveButtonStyle:I = 0x7f04010d

.field public static buttonBarStyle:I = 0x7f04010e

.field public static buttonCompat:I = 0x7f04010f

.field public static buttonGravity:I = 0x7f040113

.field public static buttonIcon:I = 0x7f040114

.field public static buttonIconDimen:I = 0x7f040116

.field public static buttonIconTint:I = 0x7f040118

.field public static buttonIconTintMode:I = 0x7f040119

.field public static buttonPanelSideLayout:I = 0x7f04011a

.field public static buttonSize:I = 0x7f04011b

.field public static buttonStyle:I = 0x7f04011c

.field public static buttonStyleSmall:I = 0x7f04011d

.field public static buttonTint:I = 0x7f04011f

.field public static buttonTintMode:I = 0x7f040120

.field public static cardBackgroundColor:I = 0x7f040128

.field public static cardCornerRadius:I = 0x7f04012b

.field public static cardElevation:I = 0x7f04012c

.field public static cardForegroundColor:I = 0x7f04012d

.field public static cardMaxElevation:I = 0x7f04012f

.field public static cardPreventCornerOverlap:I = 0x7f040130

.field public static cardUseCompatPadding:I = 0x7f040135

.field public static cardViewStyle:I = 0x7f040136

.field public static carousel_alignment:I = 0x7f04013e

.field public static carousel_backwardTransition:I = 0x7f04013f

.field public static carousel_emptyViewsBehavior:I = 0x7f040140

.field public static carousel_firstView:I = 0x7f040141

.field public static carousel_forwardTransition:I = 0x7f040142

.field public static carousel_infinite:I = 0x7f040143

.field public static carousel_nextState:I = 0x7f040144

.field public static carousel_previousState:I = 0x7f040145

.field public static carousel_touchUpMode:I = 0x7f040146

.field public static carousel_touchUp_dampeningFactor:I = 0x7f040147

.field public static carousel_touchUp_velocityThreshold:I = 0x7f040148

.field public static centerIfNoTextEnabled:I = 0x7f040160

.field public static chainUseRtl:I = 0x7f040161

.field public static checkMarkCompat:I = 0x7f040165

.field public static checkMarkTint:I = 0x7f040166

.field public static checkMarkTintMode:I = 0x7f040167

.field public static checkboxStyle:I = 0x7f04016a

.field public static checkedButton:I = 0x7f04016c

.field public static checkedChip:I = 0x7f04016d

.field public static checkedIcon:I = 0x7f04016e

.field public static checkedIconEnabled:I = 0x7f04016f

.field public static checkedIconGravity:I = 0x7f040170

.field public static checkedIconMargin:I = 0x7f040171

.field public static checkedIconSize:I = 0x7f040172

.field public static checkedIconTint:I = 0x7f040173

.field public static checkedIconVisible:I = 0x7f040174

.field public static checkedState:I = 0x7f040175

.field public static checkedTextViewStyle:I = 0x7f040176

.field public static chipBackgroundColor:I = 0x7f040179

.field public static chipCornerRadius:I = 0x7f04017a

.field public static chipEndPadding:I = 0x7f04017b

.field public static chipGroupStyle:I = 0x7f04017c

.field public static chipIcon:I = 0x7f04017d

.field public static chipIconEnabled:I = 0x7f04017f

.field public static chipIconSize:I = 0x7f040180

.field public static chipIconTint:I = 0x7f040181

.field public static chipIconVisible:I = 0x7f040182

.field public static chipMinHeight:I = 0x7f040184

.field public static chipMinTouchTargetSize:I = 0x7f040185

.field public static chipSpacing:I = 0x7f040186

.field public static chipSpacingHorizontal:I = 0x7f040187

.field public static chipSpacingVertical:I = 0x7f040188

.field public static chipStandaloneStyle:I = 0x7f040189

.field public static chipStartPadding:I = 0x7f04018a

.field public static chipStrokeColor:I = 0x7f04018b

.field public static chipStrokeWidth:I = 0x7f04018c

.field public static chipStyle:I = 0x7f04018d

.field public static chipSurfaceColor:I = 0x7f04018e

.field public static circleCrop:I = 0x7f04019a

.field public static circleRadius:I = 0x7f04019d

.field public static circularProgressIndicatorStyle:I = 0x7f0401a0

.field public static circularflow_angles:I = 0x7f0401a1

.field public static circularflow_defaultAngle:I = 0x7f0401a2

.field public static circularflow_defaultRadius:I = 0x7f0401a3

.field public static circularflow_radiusInDP:I = 0x7f0401a4

.field public static circularflow_viewCenter:I = 0x7f0401a5

.field public static clearsTag:I = 0x7f0401a7

.field public static clickAction:I = 0x7f0401a8

.field public static clockFaceBackgroundColor:I = 0x7f0401a9

.field public static clockHandColor:I = 0x7f0401aa

.field public static clockIcon:I = 0x7f0401ab

.field public static clockNumberTextColor:I = 0x7f0401ac

.field public static closeIcon:I = 0x7f0401ad

.field public static closeIconEnabled:I = 0x7f0401ae

.field public static closeIconEndPadding:I = 0x7f0401af

.field public static closeIconSize:I = 0x7f0401b0

.field public static closeIconStartPadding:I = 0x7f0401b1

.field public static closeIconTint:I = 0x7f0401b2

.field public static closeIconVisible:I = 0x7f0401b3

.field public static closeItemLayout:I = 0x7f0401b4

.field public static collapseContentDescription:I = 0x7f0401c0

.field public static collapseIcon:I = 0x7f0401c1

.field public static collapsedSize:I = 0x7f0401c3

.field public static collapsedTitleGravity:I = 0x7f0401c4

.field public static collapsedTitleTextAppearance:I = 0x7f0401c5

.field public static collapsedTitleTextColor:I = 0x7f0401c6

.field public static collapsingToolbarLayoutLargeSize:I = 0x7f0401c7

.field public static collapsingToolbarLayoutLargeStyle:I = 0x7f0401c8

.field public static collapsingToolbarLayoutMediumSize:I = 0x7f0401c9

.field public static collapsingToolbarLayoutMediumStyle:I = 0x7f0401ca

.field public static collapsingToolbarLayoutStyle:I = 0x7f0401cb

.field public static color:I = 0x7f0401cf

.field public static colorAccent:I = 0x7f0401d0

.field public static colorBackgroundFloating:I = 0x7f0401d1

.field public static colorButtonNormal:I = 0x7f0401d2

.field public static colorContainer:I = 0x7f0401d3

.field public static colorControlActivated:I = 0x7f0401d4

.field public static colorControlHighlight:I = 0x7f0401d5

.field public static colorControlNormal:I = 0x7f0401d6

.field public static colorError:I = 0x7f0401d8

.field public static colorErrorContainer:I = 0x7f0401d9

.field public static colorOnBackground:I = 0x7f0401da

.field public static colorOnContainer:I = 0x7f0401db

.field public static colorOnContainerUnchecked:I = 0x7f0401dc

.field public static colorOnError:I = 0x7f0401dd

.field public static colorOnErrorContainer:I = 0x7f0401de

.field public static colorOnPrimary:I = 0x7f0401df

.field public static colorOnPrimaryContainer:I = 0x7f0401e0

.field public static colorOnPrimaryFixed:I = 0x7f0401e1

.field public static colorOnPrimaryFixedVariant:I = 0x7f0401e2

.field public static colorOnPrimarySurface:I = 0x7f0401e3

.field public static colorOnSecondary:I = 0x7f0401e4

.field public static colorOnSecondaryContainer:I = 0x7f0401e5

.field public static colorOnSecondaryFixed:I = 0x7f0401e6

.field public static colorOnSecondaryFixedVariant:I = 0x7f0401e7

.field public static colorOnSurface:I = 0x7f0401e8

.field public static colorOnSurfaceInverse:I = 0x7f0401e9

.field public static colorOnSurfaceVariant:I = 0x7f0401ea

.field public static colorOnTertiary:I = 0x7f0401eb

.field public static colorOnTertiaryContainer:I = 0x7f0401ec

.field public static colorOnTertiaryFixed:I = 0x7f0401ed

.field public static colorOnTertiaryFixedVariant:I = 0x7f0401ee

.field public static colorOutline:I = 0x7f0401ef

.field public static colorOutlineVariant:I = 0x7f0401f0

.field public static colorPrimary:I = 0x7f0401f1

.field public static colorPrimaryContainer:I = 0x7f0401f2

.field public static colorPrimaryDark:I = 0x7f0401f3

.field public static colorPrimaryFixed:I = 0x7f0401f4

.field public static colorPrimaryFixedDim:I = 0x7f0401f5

.field public static colorPrimaryInverse:I = 0x7f0401f6

.field public static colorPrimarySurface:I = 0x7f0401f7

.field public static colorPrimaryVariant:I = 0x7f0401f8

.field public static colorScheme:I = 0x7f0401f9

.field public static colorSecondary:I = 0x7f0401fa

.field public static colorSecondaryContainer:I = 0x7f0401fb

.field public static colorSecondaryFixed:I = 0x7f0401fc

.field public static colorSecondaryFixedDim:I = 0x7f0401fd

.field public static colorSecondaryVariant:I = 0x7f0401fe

.field public static colorSurface:I = 0x7f0401ff

.field public static colorSurfaceBright:I = 0x7f040200

.field public static colorSurfaceContainer:I = 0x7f040201

.field public static colorSurfaceContainerHigh:I = 0x7f040202

.field public static colorSurfaceContainerHighest:I = 0x7f040203

.field public static colorSurfaceContainerLow:I = 0x7f040204

.field public static colorSurfaceContainerLowest:I = 0x7f040205

.field public static colorSurfaceDim:I = 0x7f040206

.field public static colorSurfaceInverse:I = 0x7f040207

.field public static colorSurfaceVariant:I = 0x7f040208

.field public static colorSwitchThumbNormal:I = 0x7f040209

.field public static colorTertiary:I = 0x7f04020a

.field public static colorTertiaryContainer:I = 0x7f04020b

.field public static colorTertiaryFixed:I = 0x7f04020c

.field public static colorTertiaryFixedDim:I = 0x7f04020d

.field public static commitIcon:I = 0x7f040210

.field public static compatShadowEnabled:I = 0x7f040211

.field public static constraintRotate:I = 0x7f040212

.field public static constraintSet:I = 0x7f040213

.field public static constraintSetEnd:I = 0x7f040214

.field public static constraintSetStart:I = 0x7f040215

.field public static constraint_referenced_ids:I = 0x7f040216

.field public static constraint_referenced_tags:I = 0x7f040217

.field public static constraints:I = 0x7f040218

.field public static content:I = 0x7f040219

.field public static contentDescription:I = 0x7f04021d

.field public static contentInsetEnd:I = 0x7f04021e

.field public static contentInsetEndWithActions:I = 0x7f04021f

.field public static contentInsetLeft:I = 0x7f040220

.field public static contentInsetRight:I = 0x7f040221

.field public static contentInsetStart:I = 0x7f040222

.field public static contentInsetStartWithNavigation:I = 0x7f040223

.field public static contentPadding:I = 0x7f040224

.field public static contentPaddingBottom:I = 0x7f040225

.field public static contentPaddingEnd:I = 0x7f040226

.field public static contentPaddingLeft:I = 0x7f040227

.field public static contentPaddingRight:I = 0x7f040228

.field public static contentPaddingStart:I = 0x7f040229

.field public static contentPaddingTop:I = 0x7f04022a

.field public static contentScrim:I = 0x7f04022b

.field public static contrast:I = 0x7f04022c

.field public static controlBackground:I = 0x7f04022d

.field public static coordinatorLayoutStyle:I = 0x7f040231

.field public static coplanarSiblingViewId:I = 0x7f040232

.field public static cornerFamily:I = 0x7f040233

.field public static cornerFamilyBottomLeft:I = 0x7f040234

.field public static cornerFamilyBottomRight:I = 0x7f040235

.field public static cornerFamilyTopLeft:I = 0x7f040236

.field public static cornerFamilyTopRight:I = 0x7f040237

.field public static cornerRadius:I = 0x7f040238

.field public static cornerSize:I = 0x7f04023a

.field public static cornerSizeBottomLeft:I = 0x7f04023b

.field public static cornerSizeBottomRight:I = 0x7f04023c

.field public static cornerSizeTopLeft:I = 0x7f04023d

.field public static cornerSizeTopRight:I = 0x7f04023e

.field public static counterEnabled:I = 0x7f040248

.field public static counterMaxLength:I = 0x7f04024b

.field public static counterOverflowTextAppearance:I = 0x7f04024c

.field public static counterOverflowTextColor:I = 0x7f04024d

.field public static counterTextAppearance:I = 0x7f04024f

.field public static counterTextColor:I = 0x7f040250

.field public static crossfade:I = 0x7f040254

.field public static currentState:I = 0x7f040258

.field public static cursorColor:I = 0x7f04025a

.field public static cursorErrorColor:I = 0x7f04025b

.field public static curveFit:I = 0x7f04025c

.field public static customBoolean:I = 0x7f040260

.field public static customColorDrawableValue:I = 0x7f040261

.field public static customColorValue:I = 0x7f040262

.field public static customDimension:I = 0x7f040263

.field public static customFloatValue:I = 0x7f040264

.field public static customIntegerValue:I = 0x7f040265

.field public static customNavigationLayout:I = 0x7f040266

.field public static customPixelDimension:I = 0x7f040267

.field public static customReference:I = 0x7f040268

.field public static customStringValue:I = 0x7f040269

.field public static dayInvalidStyle:I = 0x7f040277

.field public static daySelectedStyle:I = 0x7f040278

.field public static dayStyle:I = 0x7f040279

.field public static dayTodayStyle:I = 0x7f04027a

.field public static defaultDuration:I = 0x7f04027b

.field public static defaultMarginsEnabled:I = 0x7f04027c

.field public static defaultQueryHint:I = 0x7f04027e

.field public static defaultScrollFlagsEnabled:I = 0x7f04027f

.field public static defaultState:I = 0x7f040280

.field public static deltaPolarAngle:I = 0x7f040284

.field public static deltaPolarRadius:I = 0x7f040285

.field public static deriveConstraintsFrom:I = 0x7f040287

.field public static dialogCornerRadius:I = 0x7f04028a

.field public static dialogPreferredPadding:I = 0x7f04028f

.field public static dialogTheme:I = 0x7f040290

.field public static displayOptions:I = 0x7f040296

.field public static divider:I = 0x7f040297

.field public static dividerColor:I = 0x7f040298

.field public static dividerHorizontal:I = 0x7f04029c

.field public static dividerInsetEnd:I = 0x7f04029d

.field public static dividerInsetStart:I = 0x7f04029e

.field public static dividerPadding:I = 0x7f04029f

.field public static dividerThickness:I = 0x7f0402a0

.field public static dividerVertical:I = 0x7f0402a1

.field public static dragDirection:I = 0x7f0402a5

.field public static dragScale:I = 0x7f0402a6

.field public static dragThreshold:I = 0x7f0402a7

.field public static drawPath:I = 0x7f0402a8

.field public static drawableBottomCompat:I = 0x7f0402aa

.field public static drawableEndCompat:I = 0x7f0402ab

.field public static drawableLeftCompat:I = 0x7f0402ac

.field public static drawableRightCompat:I = 0x7f0402ad

.field public static drawableSize:I = 0x7f0402af

.field public static drawableStartCompat:I = 0x7f0402b0

.field public static drawableTint:I = 0x7f0402b1

.field public static drawableTintMode:I = 0x7f0402b2

.field public static drawableTopCompat:I = 0x7f0402b3

.field public static drawerArrowStyle:I = 0x7f0402b4

.field public static drawerLayoutCornerSize:I = 0x7f0402b5

.field public static drawerLayoutStyle:I = 0x7f0402b6

.field public static dropDownBackgroundTint:I = 0x7f0402b7

.field public static dropDownListViewStyle:I = 0x7f0402b8

.field public static dropdownListPreferredItemHeight:I = 0x7f0402b9

.field public static duration:I = 0x7f0402de

.field public static dynamicColorThemeOverlay:I = 0x7f0402e0

.field public static editTextBackground:I = 0x7f0402e2

.field public static editTextColor:I = 0x7f0402e3

.field public static editTextStyle:I = 0x7f0402e5

.field public static elevation:I = 0x7f0402e6

.field public static elevationOverlayAccentColor:I = 0x7f0402e7

.field public static elevationOverlayColor:I = 0x7f0402e8

.field public static elevationOverlayEnabled:I = 0x7f0402e9

.field public static emojiCompatEnabled:I = 0x7f0402eb

.field public static enableEdgeToEdge:I = 0x7f0402ed

.field public static endIconCheckable:I = 0x7f0402f8

.field public static endIconContentDescription:I = 0x7f0402f9

.field public static endIconDrawable:I = 0x7f0402fa

.field public static endIconMinSize:I = 0x7f0402fb

.field public static endIconMode:I = 0x7f0402fc

.field public static endIconScaleType:I = 0x7f0402fd

.field public static endIconTint:I = 0x7f0402fe

.field public static endIconTintMode:I = 0x7f0402ff

.field public static enforceMaterialTheme:I = 0x7f040301

.field public static enforceTextAppearance:I = 0x7f040302

.field public static ensureMinTouchTargetSize:I = 0x7f040303

.field public static errorAccessibilityLabel:I = 0x7f040307

.field public static errorAccessibilityLiveRegion:I = 0x7f040308

.field public static errorContentDescription:I = 0x7f040309

.field public static errorEnabled:I = 0x7f04030a

.field public static errorIconDrawable:I = 0x7f04030d

.field public static errorIconTint:I = 0x7f04030e

.field public static errorIconTintMode:I = 0x7f04030f

.field public static errorShown:I = 0x7f040310

.field public static errorTextAppearance:I = 0x7f040312

.field public static errorTextColor:I = 0x7f040313

.field public static expandActivityOverflowButtonDrawable:I = 0x7f04033b

.field public static expanded:I = 0x7f04033c

.field public static expandedHintEnabled:I = 0x7f04033d

.field public static expandedTitleGravity:I = 0x7f04033e

.field public static expandedTitleMargin:I = 0x7f04033f

.field public static expandedTitleMarginBottom:I = 0x7f040340

.field public static expandedTitleMarginEnd:I = 0x7f040341

.field public static expandedTitleMarginStart:I = 0x7f040342

.field public static expandedTitleMarginTop:I = 0x7f040343

.field public static expandedTitleTextAppearance:I = 0x7f040344

.field public static expandedTitleTextColor:I = 0x7f040345

.field public static extendMotionSpec:I = 0x7f040346

.field public static extendStrategy:I = 0x7f040347

.field public static extendedFloatingActionButtonPrimaryStyle:I = 0x7f040349

.field public static extendedFloatingActionButtonSecondaryStyle:I = 0x7f04034a

.field public static extendedFloatingActionButtonStyle:I = 0x7f04034b

.field public static extendedFloatingActionButtonSurfaceStyle:I = 0x7f04034c

.field public static extendedFloatingActionButtonTertiaryStyle:I = 0x7f04034d

.field public static extraMultilineHeightEnabled:I = 0x7f04034f

.field public static fabAlignmentMode:I = 0x7f040350

.field public static fabAlignmentModeEndMargin:I = 0x7f040351

.field public static fabAnchorMode:I = 0x7f040352

.field public static fabAnimationMode:I = 0x7f040353

.field public static fabCradleMargin:I = 0x7f040354

.field public static fabCradleRoundedCornerRadius:I = 0x7f040355

.field public static fabCradleVerticalOffset:I = 0x7f040356

.field public static fabCustomSize:I = 0x7f040357

.field public static fabSize:I = 0x7f040358

.field public static fastScrollEnabled:I = 0x7f04035c

.field public static fastScrollHorizontalThumbDrawable:I = 0x7f04035d

.field public static fastScrollHorizontalTrackDrawable:I = 0x7f04035e

.field public static fastScrollVerticalThumbDrawable:I = 0x7f04035f

.field public static fastScrollVerticalTrackDrawable:I = 0x7f040360

.field public static firstBaselineToTopHeight:I = 0x7f04036b

.field public static floatingActionButtonLargePrimaryStyle:I = 0x7f040373

.field public static floatingActionButtonLargeSecondaryStyle:I = 0x7f040374

.field public static floatingActionButtonLargeStyle:I = 0x7f040375

.field public static floatingActionButtonLargeSurfaceStyle:I = 0x7f040376

.field public static floatingActionButtonLargeTertiaryStyle:I = 0x7f040377

.field public static floatingActionButtonPrimaryStyle:I = 0x7f040378

.field public static floatingActionButtonSecondaryStyle:I = 0x7f040379

.field public static floatingActionButtonSmallPrimaryStyle:I = 0x7f04037a

.field public static floatingActionButtonSmallSecondaryStyle:I = 0x7f04037b

.field public static floatingActionButtonSmallStyle:I = 0x7f04037c

.field public static floatingActionButtonSmallSurfaceStyle:I = 0x7f04037d

.field public static floatingActionButtonSmallTertiaryStyle:I = 0x7f04037e

.field public static floatingActionButtonStyle:I = 0x7f04037f

.field public static floatingActionButtonSurfaceStyle:I = 0x7f040380

.field public static floatingActionButtonTertiaryStyle:I = 0x7f040381

.field public static flow_firstHorizontalBias:I = 0x7f040382

.field public static flow_firstHorizontalStyle:I = 0x7f040383

.field public static flow_firstVerticalBias:I = 0x7f040384

.field public static flow_firstVerticalStyle:I = 0x7f040385

.field public static flow_horizontalAlign:I = 0x7f040386

.field public static flow_horizontalBias:I = 0x7f040387

.field public static flow_horizontalGap:I = 0x7f040388

.field public static flow_horizontalStyle:I = 0x7f040389

.field public static flow_lastHorizontalBias:I = 0x7f04038a

.field public static flow_lastHorizontalStyle:I = 0x7f04038b

.field public static flow_lastVerticalBias:I = 0x7f04038c

.field public static flow_lastVerticalStyle:I = 0x7f04038d

.field public static flow_maxElementsWrap:I = 0x7f04038e

.field public static flow_padding:I = 0x7f04038f

.field public static flow_verticalAlign:I = 0x7f040390

.field public static flow_verticalBias:I = 0x7f040391

.field public static flow_verticalGap:I = 0x7f040392

.field public static flow_verticalStyle:I = 0x7f040393

.field public static flow_wrapMode:I = 0x7f040394

.field public static font:I = 0x7f040395

.field public static fontFamily:I = 0x7f040396

.field public static fontProviderAuthority:I = 0x7f040397

.field public static fontProviderCerts:I = 0x7f040398

.field public static fontProviderFetchStrategy:I = 0x7f04039a

.field public static fontProviderFetchTimeout:I = 0x7f04039b

.field public static fontProviderPackage:I = 0x7f04039c

.field public static fontProviderQuery:I = 0x7f04039d

.field public static fontProviderSystemFontFamily:I = 0x7f04039e

.field public static fontStyle:I = 0x7f04039f

.field public static fontVariationSettings:I = 0x7f0403a0

.field public static fontWeight:I = 0x7f0403a1

.field public static forceApplySystemWindowInsetTop:I = 0x7f0403a3

.field public static forceDefaultNavigationOnClickListener:I = 0x7f0403a4

.field public static foregroundInsidePadding:I = 0x7f0403a5

.field public static framePosition:I = 0x7f0403a8

.field public static gapBetweenBars:I = 0x7f0403b5

.field public static gestureInsetBottomIgnored:I = 0x7f0403b6

.field public static goIcon:I = 0x7f0403b7

.field public static guidelineUseRtl:I = 0x7f0403c7

.field public static haloColor:I = 0x7f0403c9

.field public static haloRadius:I = 0x7f0403ca

.field public static headerLayout:I = 0x7f0403cf

.field public static height:I = 0x7f0403d7

.field public static helperText:I = 0x7f0403dc

.field public static helperTextEnabled:I = 0x7f0403de

.field public static helperTextTextAppearance:I = 0x7f0403e0

.field public static helperTextTextColor:I = 0x7f0403e1

.field public static hideAnimationBehavior:I = 0x7f0403e2

.field public static hideMotionSpec:I = 0x7f0403e3

.field public static hideNavigationIcon:I = 0x7f0403e4

.field public static hideOnContentScroll:I = 0x7f0403e5

.field public static hideOnScroll:I = 0x7f0403e6

.field public static hintAnimationEnabled:I = 0x7f0403eb

.field public static hintEnabled:I = 0x7f0403ec

.field public static hintTextAppearance:I = 0x7f0403ed

.field public static hintTextColor:I = 0x7f0403ee

.field public static homeAsUpIndicator:I = 0x7f0403ef

.field public static homeLayout:I = 0x7f0403f0

.field public static horizontalOffset:I = 0x7f0403f7

.field public static horizontalOffsetWithText:I = 0x7f0403f8

.field public static hoveredFocusedTranslationZ:I = 0x7f0403f9

.field public static icon:I = 0x7f0403fd

.field public static iconEndPadding:I = 0x7f040403

.field public static iconGravity:I = 0x7f040406

.field public static iconPadding:I = 0x7f04040a

.field public static iconSize:I = 0x7f04040e

.field public static iconStartPadding:I = 0x7f040410

.field public static iconTint:I = 0x7f040411

.field public static iconTintMode:I = 0x7f040412

.field public static iconifiedByDefault:I = 0x7f040415

.field public static ifTagNotSet:I = 0x7f040416

.field public static ifTagSet:I = 0x7f040417

.field public static imageAspectRatio:I = 0x7f040419

.field public static imageAspectRatioAdjust:I = 0x7f04041a

.field public static imageButtonStyle:I = 0x7f04041b

.field public static imagePanX:I = 0x7f04041d

.field public static imagePanY:I = 0x7f04041e

.field public static imageRotate:I = 0x7f04041f

.field public static imageZoom:I = 0x7f040421

.field public static implementationMode:I = 0x7f040426

.field public static indeterminateAnimationType:I = 0x7f040428

.field public static indeterminateProgressStyle:I = 0x7f040429

.field public static indicatorColor:I = 0x7f04042a

.field public static indicatorDirectionCircular:I = 0x7f04042b

.field public static indicatorDirectionLinear:I = 0x7f04042c

.field public static indicatorInset:I = 0x7f04042d

.field public static indicatorSize:I = 0x7f04042e

.field public static initialActivityCount:I = 0x7f040433

.field public static insetForeground:I = 0x7f040438

.field public static isLightTheme:I = 0x7f040444

.field public static isMaterial3DynamicColorApplied:I = 0x7f040446

.field public static isMaterial3Theme:I = 0x7f040447

.field public static isMaterialTheme:I = 0x7f040448

.field public static itemActiveIndicatorStyle:I = 0x7f04044f

.field public static itemBackground:I = 0x7f040450

.field public static itemFillColor:I = 0x7f040451

.field public static itemHorizontalPadding:I = 0x7f040453

.field public static itemHorizontalTranslationEnabled:I = 0x7f040454

.field public static itemIconPadding:I = 0x7f040455

.field public static itemIconSize:I = 0x7f040456

.field public static itemIconTint:I = 0x7f040457

.field public static itemMaxLines:I = 0x7f040458

.field public static itemMinHeight:I = 0x7f040459

.field public static itemPadding:I = 0x7f04045a

.field public static itemPaddingBottom:I = 0x7f04045b

.field public static itemPaddingTop:I = 0x7f04045c

.field public static itemRippleColor:I = 0x7f04045d

.field public static itemShapeAppearance:I = 0x7f04045e

.field public static itemShapeAppearanceOverlay:I = 0x7f04045f

.field public static itemShapeFillColor:I = 0x7f040460

.field public static itemShapeInsetBottom:I = 0x7f040461

.field public static itemShapeInsetEnd:I = 0x7f040462

.field public static itemShapeInsetStart:I = 0x7f040463

.field public static itemShapeInsetTop:I = 0x7f040464

.field public static itemSpacing:I = 0x7f040465

.field public static itemStrokeColor:I = 0x7f040466

.field public static itemStrokeWidth:I = 0x7f040467

.field public static itemTextAppearance:I = 0x7f040468

.field public static itemTextAppearanceActive:I = 0x7f040469

.field public static itemTextAppearanceActiveBoldEnabled:I = 0x7f04046a

.field public static itemTextAppearanceInactive:I = 0x7f04046b

.field public static itemTextColor:I = 0x7f04046c

.field public static itemVerticalPadding:I = 0x7f04046d

.field public static keyPositionType:I = 0x7f040473

.field public static keyboardIcon:I = 0x7f040474

.field public static keylines:I = 0x7f040475

.field public static lStar:I = 0x7f040476

.field public static labelBehavior:I = 0x7f040478

.field public static labelStyle:I = 0x7f04047e

.field public static labelVisibilityMode:I = 0x7f040482

.field public static largeFontVerticalOffsetAdjustment:I = 0x7f040487

.field public static lastBaselineToBottomHeight:I = 0x7f040489

.field public static lastItemDecorated:I = 0x7f04048a

.field public static layout:I = 0x7f04048b

.field public static layoutDescription:I = 0x7f04048c

.field public static layoutDuringTransition:I = 0x7f04048d

.field public static layoutManager:I = 0x7f04048e

.field public static layout_anchor:I = 0x7f040490

.field public static layout_anchorGravity:I = 0x7f040491

.field public static layout_behavior:I = 0x7f040492

.field public static layout_collapseMode:I = 0x7f040493

.field public static layout_collapseParallaxMultiplier:I = 0x7f040494

.field public static layout_constrainedHeight:I = 0x7f040495

.field public static layout_constrainedWidth:I = 0x7f040496

.field public static layout_constraintBaseline_creator:I = 0x7f040497

.field public static layout_constraintBaseline_toBaselineOf:I = 0x7f040498

.field public static layout_constraintBaseline_toBottomOf:I = 0x7f040499

.field public static layout_constraintBaseline_toTopOf:I = 0x7f04049a

.field public static layout_constraintBottom_creator:I = 0x7f04049b

.field public static layout_constraintBottom_toBottomOf:I = 0x7f04049c

.field public static layout_constraintBottom_toTopOf:I = 0x7f04049d

.field public static layout_constraintCircle:I = 0x7f04049e

.field public static layout_constraintCircleAngle:I = 0x7f04049f

.field public static layout_constraintCircleRadius:I = 0x7f0404a0

.field public static layout_constraintDimensionRatio:I = 0x7f0404a1

.field public static layout_constraintEnd_toEndOf:I = 0x7f0404a2

.field public static layout_constraintEnd_toStartOf:I = 0x7f0404a3

.field public static layout_constraintGuide_begin:I = 0x7f0404a4

.field public static layout_constraintGuide_end:I = 0x7f0404a5

.field public static layout_constraintGuide_percent:I = 0x7f0404a6

.field public static layout_constraintHeight:I = 0x7f0404a7

.field public static layout_constraintHeight_default:I = 0x7f0404a8

.field public static layout_constraintHeight_max:I = 0x7f0404a9

.field public static layout_constraintHeight_min:I = 0x7f0404aa

.field public static layout_constraintHeight_percent:I = 0x7f0404ab

.field public static layout_constraintHorizontal_bias:I = 0x7f0404ac

.field public static layout_constraintHorizontal_chainStyle:I = 0x7f0404ad

.field public static layout_constraintHorizontal_weight:I = 0x7f0404ae

.field public static layout_constraintLeft_creator:I = 0x7f0404af

.field public static layout_constraintLeft_toLeftOf:I = 0x7f0404b0

.field public static layout_constraintLeft_toRightOf:I = 0x7f0404b1

.field public static layout_constraintRight_creator:I = 0x7f0404b2

.field public static layout_constraintRight_toLeftOf:I = 0x7f0404b3

.field public static layout_constraintRight_toRightOf:I = 0x7f0404b4

.field public static layout_constraintStart_toEndOf:I = 0x7f0404b5

.field public static layout_constraintStart_toStartOf:I = 0x7f0404b6

.field public static layout_constraintTag:I = 0x7f0404b7

.field public static layout_constraintTop_creator:I = 0x7f0404b8

.field public static layout_constraintTop_toBottomOf:I = 0x7f0404b9

.field public static layout_constraintTop_toTopOf:I = 0x7f0404ba

.field public static layout_constraintVertical_bias:I = 0x7f0404bb

.field public static layout_constraintVertical_chainStyle:I = 0x7f0404bc

.field public static layout_constraintVertical_weight:I = 0x7f0404bd

.field public static layout_constraintWidth:I = 0x7f0404be

.field public static layout_constraintWidth_default:I = 0x7f0404bf

.field public static layout_constraintWidth_max:I = 0x7f0404c0

.field public static layout_constraintWidth_min:I = 0x7f0404c1

.field public static layout_constraintWidth_percent:I = 0x7f0404c2

.field public static layout_dodgeInsetEdges:I = 0x7f0404c3

.field public static layout_editor_absoluteX:I = 0x7f0404c4

.field public static layout_editor_absoluteY:I = 0x7f0404c5

.field public static layout_goneMarginBaseline:I = 0x7f0404c9

.field public static layout_goneMarginBottom:I = 0x7f0404ca

.field public static layout_goneMarginEnd:I = 0x7f0404cb

.field public static layout_goneMarginLeft:I = 0x7f0404cc

.field public static layout_goneMarginRight:I = 0x7f0404cd

.field public static layout_goneMarginStart:I = 0x7f0404ce

.field public static layout_goneMarginTop:I = 0x7f0404cf

.field public static layout_insetEdge:I = 0x7f0404d0

.field public static layout_keyline:I = 0x7f0404d1

.field public static layout_marginBaseline:I = 0x7f0404d2

.field public static layout_optimizationLevel:I = 0x7f0404d7

.field public static layout_scrollEffect:I = 0x7f0404d9

.field public static layout_scrollFlags:I = 0x7f0404da

.field public static layout_scrollInterpolator:I = 0x7f0404db

.field public static layout_wrapBehaviorInParent:I = 0x7f0404dd

.field public static liftOnScroll:I = 0x7f0404e7

.field public static liftOnScrollColor:I = 0x7f0404e8

.field public static liftOnScrollTargetViewId:I = 0x7f0404e9

.field public static limitBoundsTo:I = 0x7f0404eb

.field public static lineHeight:I = 0x7f0404f0

.field public static lineSpacing:I = 0x7f0404f1

.field public static linearProgressIndicatorStyle:I = 0x7f0404f4

.field public static listChoiceBackgroundIndicator:I = 0x7f0404f8

.field public static listChoiceIndicatorMultipleAnimated:I = 0x7f0404f9

.field public static listChoiceIndicatorSingleAnimated:I = 0x7f0404fa

.field public static listDividerAlertDialog:I = 0x7f0404fb

.field public static listItemLayout:I = 0x7f0404fc

.field public static listLayout:I = 0x7f0404fd

.field public static listMenuViewStyle:I = 0x7f0404fe

.field public static listPopupWindowStyle:I = 0x7f0404ff

.field public static listPreferredItemHeight:I = 0x7f040500

.field public static listPreferredItemHeightLarge:I = 0x7f040501

.field public static listPreferredItemHeightSmall:I = 0x7f040502

.field public static listPreferredItemPaddingEnd:I = 0x7f040503

.field public static listPreferredItemPaddingLeft:I = 0x7f040504

.field public static listPreferredItemPaddingRight:I = 0x7f040505

.field public static listPreferredItemPaddingStart:I = 0x7f040506

.field public static logo:I = 0x7f04050a

.field public static logoAdjustViewBounds:I = 0x7f04050b

.field public static logoDescription:I = 0x7f04050c

.field public static logoScaleType:I = 0x7f04050d

.field public static marginHorizontal:I = 0x7f040527

.field public static marginLeftSystemWindowInsets:I = 0x7f040528

.field public static marginRightSystemWindowInsets:I = 0x7f040529

.field public static marginTopSystemWindowInsets:I = 0x7f04052c

.field public static materialAlertDialogBodyTextStyle:I = 0x7f040539

.field public static materialAlertDialogButtonSpacerVisibility:I = 0x7f04053a

.field public static materialAlertDialogTheme:I = 0x7f04053b

.field public static materialAlertDialogTitleIconStyle:I = 0x7f04053c

.field public static materialAlertDialogTitlePanelStyle:I = 0x7f04053d

.field public static materialAlertDialogTitleTextStyle:I = 0x7f04053e

.field public static materialButtonOutlinedStyle:I = 0x7f04053f

.field public static materialButtonStyle:I = 0x7f040540

.field public static materialButtonToggleGroupStyle:I = 0x7f040541

.field public static materialCalendarDay:I = 0x7f040542

.field public static materialCalendarDayOfWeekLabel:I = 0x7f040543

.field public static materialCalendarFullscreenTheme:I = 0x7f040544

.field public static materialCalendarHeaderCancelButton:I = 0x7f040545

.field public static materialCalendarHeaderConfirmButton:I = 0x7f040546

.field public static materialCalendarHeaderDivider:I = 0x7f040547

.field public static materialCalendarHeaderLayout:I = 0x7f040548

.field public static materialCalendarHeaderSelection:I = 0x7f040549

.field public static materialCalendarHeaderTitle:I = 0x7f04054a

.field public static materialCalendarHeaderToggleButton:I = 0x7f04054b

.field public static materialCalendarMonth:I = 0x7f04054c

.field public static materialCalendarMonthNavigationButton:I = 0x7f04054d

.field public static materialCalendarStyle:I = 0x7f04054e

.field public static materialCalendarTheme:I = 0x7f04054f

.field public static materialCalendarYearNavigationButton:I = 0x7f040550

.field public static materialCardViewElevatedStyle:I = 0x7f040551

.field public static materialCardViewFilledStyle:I = 0x7f040552

.field public static materialCardViewOutlinedStyle:I = 0x7f040553

.field public static materialCardViewStyle:I = 0x7f040554

.field public static materialCircleRadius:I = 0x7f040555

.field public static materialClockStyle:I = 0x7f040556

.field public static materialDisplayDividerStyle:I = 0x7f040557

.field public static materialDividerHeavyStyle:I = 0x7f040558

.field public static materialDividerStyle:I = 0x7f040559

.field public static materialIconButtonFilledStyle:I = 0x7f04055a

.field public static materialIconButtonFilledTonalStyle:I = 0x7f04055b

.field public static materialIconButtonOutlinedStyle:I = 0x7f04055c

.field public static materialIconButtonStyle:I = 0x7f04055d

.field public static materialSearchBarStyle:I = 0x7f04055e

.field public static materialSearchViewPrefixStyle:I = 0x7f04055f

.field public static materialSearchViewStyle:I = 0x7f040560

.field public static materialSearchViewToolbarHeight:I = 0x7f040561

.field public static materialSearchViewToolbarStyle:I = 0x7f040562

.field public static materialSwitchStyle:I = 0x7f040563

.field public static materialThemeOverlay:I = 0x7f040564

.field public static materialTimePickerStyle:I = 0x7f040565

.field public static materialTimePickerTheme:I = 0x7f040566

.field public static materialTimePickerTitleStyle:I = 0x7f040567

.field public static maxAcceleration:I = 0x7f040568

.field public static maxActionInlineWidth:I = 0x7f040569

.field public static maxButtonHeight:I = 0x7f04056b

.field public static maxCharacterCount:I = 0x7f04056c

.field public static maxHeight:I = 0x7f040570

.field public static maxImageSize:I = 0x7f040572

.field public static maxLines:I = 0x7f040576

.field public static maxNumber:I = 0x7f040577

.field public static maxVelocity:I = 0x7f040580

.field public static maxWidth:I = 0x7f040582

.field public static measureWithLargestChild:I = 0x7f040584

.field public static menu:I = 0x7f040585

.field public static menuAlignmentMode:I = 0x7f040586

.field public static menuGravity:I = 0x7f04058b

.field public static methodName:I = 0x7f04058c

.field public static minHeight:I = 0x7f040592

.field public static minHideDelay:I = 0x7f040593

.field public static minSeparation:I = 0x7f040595

.field public static minTouchTargetSize:I = 0x7f040598

.field public static minWidth:I = 0x7f04059b

.field public static mock_diagonalsColor:I = 0x7f04059d

.field public static mock_label:I = 0x7f04059e

.field public static mock_labelBackgroundColor:I = 0x7f04059f

.field public static mock_labelColor:I = 0x7f0405a0

.field public static mock_showDiagonals:I = 0x7f0405a1

.field public static mock_showLabel:I = 0x7f0405a2

.field public static motionDebug:I = 0x7f0405ac

.field public static motionDurationExtraLong1:I = 0x7f0405ad

.field public static motionDurationExtraLong2:I = 0x7f0405ae

.field public static motionDurationExtraLong3:I = 0x7f0405af

.field public static motionDurationExtraLong4:I = 0x7f0405b0

.field public static motionDurationLong1:I = 0x7f0405b1

.field public static motionDurationLong2:I = 0x7f0405b2

.field public static motionDurationLong3:I = 0x7f0405b3

.field public static motionDurationLong4:I = 0x7f0405b4

.field public static motionDurationMedium1:I = 0x7f0405b5

.field public static motionDurationMedium2:I = 0x7f0405b6

.field public static motionDurationMedium3:I = 0x7f0405b7

.field public static motionDurationMedium4:I = 0x7f0405b8

.field public static motionDurationShort1:I = 0x7f0405b9

.field public static motionDurationShort2:I = 0x7f0405ba

.field public static motionDurationShort3:I = 0x7f0405bb

.field public static motionDurationShort4:I = 0x7f0405bc

.field public static motionEasingAccelerated:I = 0x7f0405bd

.field public static motionEasingDecelerated:I = 0x7f0405be

.field public static motionEasingEmphasized:I = 0x7f0405bf

.field public static motionEasingEmphasizedAccelerateInterpolator:I = 0x7f0405c0

.field public static motionEasingEmphasizedDecelerateInterpolator:I = 0x7f0405c1

.field public static motionEasingEmphasizedInterpolator:I = 0x7f0405c2

.field public static motionEasingLinear:I = 0x7f0405c3

.field public static motionEasingLinearInterpolator:I = 0x7f0405c4

.field public static motionEasingStandard:I = 0x7f0405c5

.field public static motionEasingStandardAccelerateInterpolator:I = 0x7f0405c6

.field public static motionEasingStandardDecelerateInterpolator:I = 0x7f0405c7

.field public static motionEasingStandardInterpolator:I = 0x7f0405c8

.field public static motionEffect_alpha:I = 0x7f0405c9

.field public static motionEffect_end:I = 0x7f0405ca

.field public static motionEffect_move:I = 0x7f0405cb

.field public static motionEffect_start:I = 0x7f0405cc

.field public static motionEffect_strict:I = 0x7f0405cd

.field public static motionEffect_translationX:I = 0x7f0405ce

.field public static motionEffect_translationY:I = 0x7f0405cf

.field public static motionEffect_viewTransition:I = 0x7f0405d0

.field public static motionInterpolator:I = 0x7f0405d1

.field public static motionPath:I = 0x7f0405d2

.field public static motionPathRotate:I = 0x7f0405d3

.field public static motionProgress:I = 0x7f0405d4

.field public static motionStagger:I = 0x7f0405d5

.field public static motionTarget:I = 0x7f0405d6

.field public static motion_postLayoutCollision:I = 0x7f0405d7

.field public static motion_triggerOnCollision:I = 0x7f0405d8

.field public static moveWhenScrollAtTop:I = 0x7f0405d9

.field public static multiChoiceItemLayout:I = 0x7f0405da

.field public static navigationContentDescription:I = 0x7f0405dd

.field public static navigationIcon:I = 0x7f0405de

.field public static navigationIconTint:I = 0x7f0405df

.field public static navigationMode:I = 0x7f0405e0

.field public static navigationRailStyle:I = 0x7f0405e1

.field public static navigationViewStyle:I = 0x7f0405e2

.field public static nestedScrollFlags:I = 0x7f0405e6

.field public static nestedScrollViewStyle:I = 0x7f0405e7

.field public static nestedScrollable:I = 0x7f0405e8

.field public static number:I = 0x7f04060f

.field public static numericModifiers:I = 0x7f040616

.field public static offsetAlignmentMode:I = 0x7f040617

.field public static onCross:I = 0x7f040618

.field public static onHide:I = 0x7f040619

.field public static onNegativeCross:I = 0x7f04061a

.field public static onPositiveCross:I = 0x7f04061b

.field public static onShow:I = 0x7f04061c

.field public static onStateTransition:I = 0x7f04061d

.field public static onTouchUp:I = 0x7f04061e

.field public static overlapAnchor:I = 0x7f040629

.field public static overlay:I = 0x7f04062a

.field public static paddingBottomNoButtons:I = 0x7f04062e

.field public static paddingBottomSystemWindowInsets:I = 0x7f04062f

.field public static paddingEnd:I = 0x7f040630

.field public static paddingLeftSystemWindowInsets:I = 0x7f040631

.field public static paddingRightSystemWindowInsets:I = 0x7f040632

.field public static paddingStart:I = 0x7f040633

.field public static paddingStartSystemWindowInsets:I = 0x7f040634

.field public static paddingTopNoTitle:I = 0x7f040635

.field public static paddingTopSystemWindowInsets:I = 0x7f040636

.field public static panelBackground:I = 0x7f04063a

.field public static panelMenuListTheme:I = 0x7f04063b

.field public static panelMenuListWidth:I = 0x7f04063c

.field public static passwordToggleContentDescription:I = 0x7f040640

.field public static passwordToggleDrawable:I = 0x7f040641

.field public static passwordToggleEnabled:I = 0x7f040642

.field public static passwordToggleTint:I = 0x7f040643

.field public static passwordToggleTintMode:I = 0x7f040644

.field public static pathMotionArc:I = 0x7f040645

.field public static path_percent:I = 0x7f040646

.field public static percentHeight:I = 0x7f040648

.field public static percentWidth:I = 0x7f040649

.field public static percentX:I = 0x7f04064a

.field public static percentY:I = 0x7f04064b

.field public static perpendicularPath_percent:I = 0x7f04064c

.field public static pivotAnchor:I = 0x7f040653

.field public static placeholderText:I = 0x7f04065a

.field public static placeholderTextAppearance:I = 0x7f04065b

.field public static placeholderTextColor:I = 0x7f04065c

.field public static placeholder_emptyVisibility:I = 0x7f04065e

.field public static polarRelativeTo:I = 0x7f04066f

.field public static popupMenuBackground:I = 0x7f040670

.field public static popupMenuStyle:I = 0x7f040671

.field public static popupTheme:I = 0x7f040672

.field public static popupWindowStyle:I = 0x7f040673

.field public static prefixText:I = 0x7f040683

.field public static prefixTextAppearance:I = 0x7f040684

.field public static prefixTextColor:I = 0x7f040685

.field public static preserveIconSpacing:I = 0x7f040686

.field public static pressedTranslationZ:I = 0x7f04068b

.field public static previewCornerRadius:I = 0x7f04068c

.field public static progressBarPadding:I = 0x7f040697

.field public static progressBarStyle:I = 0x7f040698

.field public static quantizeMotionInterpolator:I = 0x7f0406a8

.field public static quantizeMotionPhase:I = 0x7f0406a9

.field public static quantizeMotionSteps:I = 0x7f0406aa

.field public static queryBackground:I = 0x7f0406ab

.field public static queryHint:I = 0x7f0406ac

.field public static queryPatterns:I = 0x7f0406ad

.field public static radioButtonStyle:I = 0x7f0406af

.field public static rangeFillColor:I = 0x7f0406b1

.field public static ratingBarStyle:I = 0x7f0406b2

.field public static ratingBarStyleIndicator:I = 0x7f0406b3

.field public static ratingBarStyleSmall:I = 0x7f0406b4

.field public static reactiveGuide_animateChange:I = 0x7f0406b5

.field public static reactiveGuide_applyToAllConstraintSets:I = 0x7f0406b6

.field public static reactiveGuide_applyToConstraintSet:I = 0x7f0406b7

.field public static reactiveGuide_valueId:I = 0x7f0406b8

.field public static recyclerViewStyle:I = 0x7f0406b9

.field public static region_heightLessThan:I = 0x7f0406bb

.field public static region_heightMoreThan:I = 0x7f0406bc

.field public static region_widthLessThan:I = 0x7f0406bd

.field public static region_widthMoreThan:I = 0x7f0406be

.field public static removeEmbeddedFabElevation:I = 0x7f0406c4

.field public static reverseLayout:I = 0x7f0406cc

.field public static rippleColor:I = 0x7f0406d9

.field public static rotationCenterId:I = 0x7f0406df

.field public static round:I = 0x7f0406e0

.field public static roundPercent:I = 0x7f0406e2

.field public static saturation:I = 0x7f0406e9

.field public static scaleFromTextSize:I = 0x7f0406ea

.field public static scaleType:I = 0x7f0406eb

.field public static scopeUris:I = 0x7f0406ec

.field public static scrimAnimationDuration:I = 0x7f0406f1

.field public static scrimBackground:I = 0x7f0406f2

.field public static scrimVisibleHeightTrigger:I = 0x7f0406f3

.field public static searchHintIcon:I = 0x7f0406fe

.field public static searchIcon:I = 0x7f0406ff

.field public static searchPrefixText:I = 0x7f040700

.field public static searchViewStyle:I = 0x7f040701

.field public static seekBarStyle:I = 0x7f040712

.field public static selectableItemBackground:I = 0x7f040719

.field public static selectableItemBackgroundBorderless:I = 0x7f04071a

.field public static selectionRequired:I = 0x7f040721

.field public static selectorSize:I = 0x7f040724

.field public static setsTag:I = 0x7f040729

.field public static shapeAppearance:I = 0x7f04072d

.field public static shapeAppearanceCornerExtraLarge:I = 0x7f04072e

.field public static shapeAppearanceCornerExtraSmall:I = 0x7f04072f

.field public static shapeAppearanceCornerLarge:I = 0x7f040730

.field public static shapeAppearanceCornerMedium:I = 0x7f040731

.field public static shapeAppearanceCornerSmall:I = 0x7f040732

.field public static shapeAppearanceLargeComponent:I = 0x7f040733

.field public static shapeAppearanceMediumComponent:I = 0x7f040734

.field public static shapeAppearanceOverlay:I = 0x7f040735

.field public static shapeAppearanceSmallComponent:I = 0x7f040736

.field public static shapeCornerFamily:I = 0x7f040737

.field public static shortcutMatchRequired:I = 0x7f04074e

.field public static shouldRemoveExpandedCorners:I = 0x7f040750

.field public static showAnimationBehavior:I = 0x7f040753

.field public static showAsAction:I = 0x7f040754

.field public static showDelay:I = 0x7f04075f

.field public static showDividers:I = 0x7f040763

.field public static showMotionSpec:I = 0x7f04076d

.field public static showPaths:I = 0x7f04076f

.field public static showText:I = 0x7f04077a

.field public static showTitle:I = 0x7f04077c

.field public static shrinkMotionSpec:I = 0x7f04078d

.field public static sideSheetDialogTheme:I = 0x7f040792

.field public static sideSheetModalStyle:I = 0x7f040793

.field public static simpleItemLayout:I = 0x7f040794

.field public static simpleItemSelectedColor:I = 0x7f040795

.field public static simpleItemSelectedRippleColor:I = 0x7f040796

.field public static simpleItems:I = 0x7f040797

.field public static singleChoiceItemLayout:I = 0x7f040798

.field public static singleLine:I = 0x7f040799

.field public static singleSelection:I = 0x7f04079b

.field public static sizePercent:I = 0x7f04079c

.field public static sliderStyle:I = 0x7f0407a0

.field public static snackbarButtonStyle:I = 0x7f0407a2

.field public static snackbarStyle:I = 0x7f0407a3

.field public static snackbarTextViewStyle:I = 0x7f0407a4

.field public static snsFrameViewWithBackgroundStyle:I = 0x7f0407a5

.field public static sns_AutoCompleteTextViewStyle:I = 0x7f0407a6

.field public static sns_BackgroundConstraintLayoutStyle:I = 0x7f0407a7

.field public static sns_BackgroundViewStyle:I = 0x7f0407a8

.field public static sns_BodyTextViewStyle:I = 0x7f0407a9

.field public static sns_BottomSheetHandleStyle:I = 0x7f0407aa

.field public static sns_BottomSheetViewStyle:I = 0x7f0407ab

.field public static sns_CameraBackgroundViewStyle:I = 0x7f0407ac

.field public static sns_CaptionTextViewStyle:I = 0x7f0407ad

.field public static sns_CheckGroupStyle:I = 0x7f0407ae

.field public static sns_CountrySelectorViewStyle:I = 0x7f0407af

.field public static sns_DateInputLayoutStyle:I = 0x7f0407b0

.field public static sns_DateTimeInputLayoutStyle:I = 0x7f0407b1

.field public static sns_DotsProgressViewStyle:I = 0x7f0407b2

.field public static sns_FlagViewStyle:I = 0x7f0407b3

.field public static sns_FlaggedInputLayoutStyle:I = 0x7f0407b4

.field public static sns_FrameDrawable:I = 0x7f0407b5

.field public static sns_FrameFillColor:I = 0x7f0407b6

.field public static sns_FramePaddingBottom:I = 0x7f0407b7

.field public static sns_FramePaddingLeft:I = 0x7f0407b8

.field public static sns_FramePaddingRight:I = 0x7f0407b9

.field public static sns_FramePaddingTop:I = 0x7f0407ba

.field public static sns_FrameViewStyle:I = 0x7f0407bb

.field public static sns_H1TextViewStyle:I = 0x7f0407bc

.field public static sns_H2TextViewStyle:I = 0x7f0407bd

.field public static sns_ImageButtonStyle:I = 0x7f0407be

.field public static sns_ImageViewStyle:I = 0x7f0407bf

.field public static sns_IntroItemViewStyle:I = 0x7f0407c0

.field public static sns_LinkButtonStyle:I = 0x7f0407c1

.field public static sns_ListItemViewStyle:I = 0x7f0407c2

.field public static sns_ModeratorCommentViewStyle:I = 0x7f0407c3

.field public static sns_ProgressViewStyle:I = 0x7f0407c4

.field public static sns_ProofaceCompleteOverlayColor:I = 0x7f0407c5

.field public static sns_ProofaceMarkerActiveColor:I = 0x7f0407c6

.field public static sns_ProofaceMarkerInActiveColor:I = 0x7f0407c7

.field public static sns_ProofaceMarkerPadding:I = 0x7f0407c8

.field public static sns_ProofaceMarkerSize:I = 0x7f0407c9

.field public static sns_ProofaceMarkerStroke:I = 0x7f0407ca

.field public static sns_ProofaceOverlayColor:I = 0x7f0407cb

.field public static sns_ProofaceRecognizingAnimationSpeed:I = 0x7f0407cc

.field public static sns_ProofaceRecognizingColor:I = 0x7f0407cd

.field public static sns_ProofaceRecognizingStroke:I = 0x7f0407ce

.field public static sns_ProofaceViewStyle:I = 0x7f0407cf

.field public static sns_RadioGroupStyle:I = 0x7f0407d0

.field public static sns_RecorderTextViewStyle:I = 0x7f0407d1

.field public static sns_RotationZoomableImageViewStyle:I = 0x7f0407d2

.field public static sns_SNSSegmentedToggleViewStyle:I = 0x7f0407d3

.field public static sns_SelectorItemViewStyle:I = 0x7f0407d4

.field public static sns_StepViewStyle:I = 0x7f0407d5

.field public static sns_Subtitle1TextViewStyle:I = 0x7f0407d6

.field public static sns_Subtitle2PrimaryTextViewStyle:I = 0x7f0407d7

.field public static sns_Subtitle2TextViewStyle:I = 0x7f0407d8

.field public static sns_SupportItemViewStyle:I = 0x7f0407d9

.field public static sns_TextInputEditTextStyle:I = 0x7f0407da

.field public static sns_TextInputLayoutStyle:I = 0x7f0407db

.field public static sns_ToolbarViewStyle:I = 0x7f0407dc

.field public static sns_VideoIdentDocumentViewStyle:I = 0x7f0407dd

.field public static sns_VideoIdentLanguageItemViewStyle:I = 0x7f0407de

.field public static sns_VideoSelfiePhraseViewStyle:I = 0x7f0407df

.field public static sns_WarningViewStyle:I = 0x7f0407e0

.field public static sns_applicantDataBoolFieldLayout:I = 0x7f0407e1

.field public static sns_applicantDataBoolFieldViewStyle:I = 0x7f0407e2

.field public static sns_applicantDataFieldLayout:I = 0x7f0407e3

.field public static sns_applicantDataFieldViewStyle:I = 0x7f0407e4

.field public static sns_applicantDataFileFieldLayout:I = 0x7f0407e5

.field public static sns_applicantDataFileFieldViewStyle:I = 0x7f0407e6

.field public static sns_applicantDataMutilselectFieldViewIconTint:I = 0x7f0407e7

.field public static sns_applicantDataMutilselectFieldViewLayout:I = 0x7f0407e8

.field public static sns_applicantDataMutilselectFieldViewStyle:I = 0x7f0407e9

.field public static sns_applicantDataPhoneFieldLayout:I = 0x7f0407ea

.field public static sns_applicantDataPhoneFieldViewStyle:I = 0x7f0407eb

.field public static sns_applicantDataRadioGroupLayout:I = 0x7f0407ec

.field public static sns_applicantDataRadioGroupViewStyle:I = 0x7f0407ed

.field public static sns_applicantDataSectionLayout:I = 0x7f0407ee

.field public static sns_applicantDataSectionViewStyle:I = 0x7f0407ef

.field public static sns_applicantDataTextAreaFieldLayout:I = 0x7f0407f0

.field public static sns_applicantDataTextAreaFieldViewStyle:I = 0x7f0407f1

.field public static sns_cardRadioButtonBackgroundColor:I = 0x7f0407f2

.field public static sns_cardRadioButtonCornerRadius:I = 0x7f0407f3

.field public static sns_cardRadioButtonStrokeColor:I = 0x7f0407f4

.field public static sns_cardRadioButtonStrokeWidth:I = 0x7f0407f5

.field public static sns_cardRadioButtonViewStyle:I = 0x7f0407f6

.field public static sns_checkBackgroundColor:I = 0x7f0407f7

.field public static sns_colorInit:I = 0x7f0407f8

.field public static sns_colorOnInit:I = 0x7f0407f9

.field public static sns_colorOnPending:I = 0x7f0407fa

.field public static sns_colorOnProcessing:I = 0x7f0407fb

.field public static sns_colorOnRejected:I = 0x7f0407fc

.field public static sns_colorOnSuccess:I = 0x7f0407fd

.field public static sns_colorPending:I = 0x7f0407fe

.field public static sns_colorProcessing:I = 0x7f0407ff

.field public static sns_colorRejected:I = 0x7f040800

.field public static sns_colorSuccess:I = 0x7f040801

.field public static sns_cursorDrawable:I = 0x7f040802

.field public static sns_dimColor:I = 0x7f040803

.field public static sns_dotsProgressDotBackgroundColor:I = 0x7f040804

.field public static sns_dotsProgressDotCompleteColor:I = 0x7f040805

.field public static sns_dotsProgressMinGap:I = 0x7f040806

.field public static sns_editorBackgroundColor:I = 0x7f040807

.field public static sns_endIconTint:I = 0x7f040808

.field public static sns_fileItemViewLayout:I = 0x7f040809

.field public static sns_fileItemViewStyle:I = 0x7f04080a

.field public static sns_flagMarginEnd:I = 0x7f04080b

.field public static sns_flagMarginStart:I = 0x7f04080c

.field public static sns_frameBackgroundColor:I = 0x7f04080d

.field public static sns_iconClose:I = 0x7f04080e

.field public static sns_iconEnd:I = 0x7f04080f

.field public static sns_iconStart:I = 0x7f040810

.field public static sns_introLivenessItemViewStyle:I = 0x7f040811

.field public static sns_itemBackgroundColor:I = 0x7f040812

.field public static sns_itemPadding:I = 0x7f040813

.field public static sns_itemSpacing:I = 0x7f040814

.field public static sns_pinViewStyle:I = 0x7f040815

.field public static sns_progressBackgroundColor:I = 0x7f040816

.field public static sns_progressViewLayout:I = 0x7f040817

.field public static sns_radioBackgroundColor:I = 0x7f040818

.field public static sns_startIconTint:I = 0x7f040819

.field public static sns_stateApproved:I = 0x7f04081a

.field public static sns_stateFrameColor:I = 0x7f04081b

.field public static sns_stateFrameRadius:I = 0x7f04081c

.field public static sns_stateFrameWidth:I = 0x7f04081d

.field public static sns_stateInit:I = 0x7f04081e

.field public static sns_statePending:I = 0x7f04081f

.field public static sns_stateProcessing:I = 0x7f040820

.field public static sns_stateRejected:I = 0x7f040821

.field public static sns_stepBackgroundColor:I = 0x7f040822

.field public static sns_stepIconTintColor:I = 0x7f040823

.field public static sns_stepStrokeColor:I = 0x7f040824

.field public static sns_stepStrokeWidth:I = 0x7f040825

.field public static sns_stepStrokeWidthActivated:I = 0x7f040826

.field public static sns_stepStrokeWidthDefault:I = 0x7f040827

.field public static sns_stepSubtitleTextColor:I = 0x7f040828

.field public static sns_stepTitleTextColor:I = 0x7f040829

.field public static sns_stepViewLayout:I = 0x7f04082a

.field public static sns_subtitle:I = 0x7f04082b

.field public static sns_textColor:I = 0x7f04082c

.field public static sns_title:I = 0x7f04082d

.field public static sns_toolbarIconTint:I = 0x7f04082e

.field public static sns_toolbarViewLayout:I = 0x7f04082f

.field public static sns_zoomEnabled:I = 0x7f040830

.field public static spanCount:I = 0x7f040836

.field public static spinBars:I = 0x7f040837

.field public static spinnerDropDownItemStyle:I = 0x7f040838

.field public static spinnerStyle:I = 0x7f040839

.field public static splitTrack:I = 0x7f040842

.field public static springBoundary:I = 0x7f04086d

.field public static springDamping:I = 0x7f04086e

.field public static springMass:I = 0x7f04086f

.field public static springStiffness:I = 0x7f040870

.field public static springStopThreshold:I = 0x7f040871

.field public static srcCompat:I = 0x7f040873

.field public static stackFromEnd:I = 0x7f040874

.field public static staggered:I = 0x7f040875

.field public static startIconCheckable:I = 0x7f04087d

.field public static startIconContentDescription:I = 0x7f04087e

.field public static startIconDrawable:I = 0x7f04087f

.field public static startIconMinSize:I = 0x7f040880

.field public static startIconScaleType:I = 0x7f040881

.field public static startIconTint:I = 0x7f040882

.field public static startIconTintMode:I = 0x7f040883

.field public static state_above_anchor:I = 0x7f040888

.field public static state_collapsed:I = 0x7f04088b

.field public static state_collapsible:I = 0x7f04088c

.field public static state_dragged:I = 0x7f04088e

.field public static state_error:I = 0x7f04088f

.field public static state_indeterminate:I = 0x7f040892

.field public static state_liftable:I = 0x7f040893

.field public static state_lifted:I = 0x7f040894

.field public static state_with_icon:I = 0x7f040899

.field public static statusBarBackground:I = 0x7f04089c

.field public static statusBarForeground:I = 0x7f04089e

.field public static statusBarScrim:I = 0x7f04089f

.field public static strokeColor:I = 0x7f0408a4

.field public static strokeWidth:I = 0x7f0408a5

.field public static subMenuArrow:I = 0x7f0408a9

.field public static subheaderColor:I = 0x7f0408aa

.field public static subheaderInsetEnd:I = 0x7f0408ab

.field public static subheaderInsetStart:I = 0x7f0408ac

.field public static subheaderTextAppearance:I = 0x7f0408ad

.field public static submitBackground:I = 0x7f0408ae

.field public static subtitle:I = 0x7f0408af

.field public static subtitleCentered:I = 0x7f0408b1

.field public static subtitleTextAppearance:I = 0x7f0408b4

.field public static subtitleTextColor:I = 0x7f0408b5

.field public static subtitleTextStyle:I = 0x7f0408b6

.field public static suffixText:I = 0x7f0408bb

.field public static suffixTextAppearance:I = 0x7f0408bc

.field public static suffixTextColor:I = 0x7f0408bd

.field public static suggestionRowLayout:I = 0x7f0408be

.field public static switchMinWidth:I = 0x7f0408c9

.field public static switchPadding:I = 0x7f0408ca

.field public static switchStyle:I = 0x7f0408cd

.field public static switchTextAppearance:I = 0x7f0408ce

.field public static tabBackground:I = 0x7f0408d7

.field public static tabContentStart:I = 0x7f0408d9

.field public static tabGravity:I = 0x7f0408da

.field public static tabIconTint:I = 0x7f0408db

.field public static tabIconTintMode:I = 0x7f0408dc

.field public static tabIndicator:I = 0x7f0408dd

.field public static tabIndicatorAnimationDuration:I = 0x7f0408de

.field public static tabIndicatorAnimationMode:I = 0x7f0408df

.field public static tabIndicatorColor:I = 0x7f0408e0

.field public static tabIndicatorFullWidth:I = 0x7f0408e1

.field public static tabIndicatorGravity:I = 0x7f0408e2

.field public static tabIndicatorHeight:I = 0x7f0408e3

.field public static tabInlineLabel:I = 0x7f0408e4

.field public static tabMaxWidth:I = 0x7f0408e6

.field public static tabMinWidth:I = 0x7f0408e7

.field public static tabMode:I = 0x7f0408e8

.field public static tabPadding:I = 0x7f0408e9

.field public static tabPaddingBottom:I = 0x7f0408ea

.field public static tabPaddingEnd:I = 0x7f0408eb

.field public static tabPaddingStart:I = 0x7f0408ec

.field public static tabPaddingTop:I = 0x7f0408ed

.field public static tabRippleColor:I = 0x7f0408ee

.field public static tabSecondaryStyle:I = 0x7f0408ef

.field public static tabSelectedTextAppearance:I = 0x7f0408f0

.field public static tabSelectedTextColor:I = 0x7f0408f1

.field public static tabStyle:I = 0x7f0408f2

.field public static tabTextAppearance:I = 0x7f0408f3

.field public static tabTextColor:I = 0x7f0408f4

.field public static tabUnboundedRipple:I = 0x7f0408f6

.field public static targetId:I = 0x7f0408fb

.field public static telltales_tailColor:I = 0x7f040905

.field public static telltales_tailScale:I = 0x7f040906

.field public static telltales_velocityMode:I = 0x7f040907

.field public static textAllCaps:I = 0x7f04090c

.field public static textAppearanceBody1:I = 0x7f04090d

.field public static textAppearanceBody2:I = 0x7f040913

.field public static textAppearanceBodyLarge:I = 0x7f04091b

.field public static textAppearanceBodyMedium:I = 0x7f04091c

.field public static textAppearanceBodySmall:I = 0x7f04091d

.field public static textAppearanceButton:I = 0x7f04091e

.field public static textAppearanceCaption:I = 0x7f04091f

.field public static textAppearanceDisplayLarge:I = 0x7f04092a

.field public static textAppearanceDisplayMedium:I = 0x7f04092b

.field public static textAppearanceDisplaySmall:I = 0x7f04092c

.field public static textAppearanceHeadline1:I = 0x7f04092e

.field public static textAppearanceHeadline2:I = 0x7f04092f

.field public static textAppearanceHeadline3:I = 0x7f040930

.field public static textAppearanceHeadline4:I = 0x7f040931

.field public static textAppearanceHeadline5:I = 0x7f040935

.field public static textAppearanceHeadline6:I = 0x7f04093b

.field public static textAppearanceHeadlineLarge:I = 0x7f040941

.field public static textAppearanceHeadlineMedium:I = 0x7f040942

.field public static textAppearanceHeadlineSmall:I = 0x7f040943

.field public static textAppearanceLabelLarge:I = 0x7f040944

.field public static textAppearanceLabelMedium:I = 0x7f040945

.field public static textAppearanceLabelSmall:I = 0x7f040946

.field public static textAppearanceLargePopupMenu:I = 0x7f040947

.field public static textAppearanceLineHeightEnabled:I = 0x7f040948

.field public static textAppearanceListItem:I = 0x7f040949

.field public static textAppearanceListItemSecondary:I = 0x7f04094a

.field public static textAppearanceListItemSmall:I = 0x7f04094b

.field public static textAppearanceOverline:I = 0x7f04094c

.field public static textAppearancePopupMenuHeader:I = 0x7f04094e

.field public static textAppearanceSearchResultSubtitle:I = 0x7f04094f

.field public static textAppearanceSearchResultTitle:I = 0x7f040950

.field public static textAppearanceSmallPopupMenu:I = 0x7f040951

.field public static textAppearanceSubtitle1:I = 0x7f040952

.field public static textAppearanceSubtitle2:I = 0x7f040958

.field public static textAppearanceTitleLarge:I = 0x7f040961

.field public static textAppearanceTitleMedium:I = 0x7f040962

.field public static textAppearanceTitleSmall:I = 0x7f040963

.field public static textBackground:I = 0x7f040964

.field public static textBackgroundPanX:I = 0x7f040965

.field public static textBackgroundPanY:I = 0x7f040966

.field public static textBackgroundRotate:I = 0x7f040967

.field public static textBackgroundZoom:I = 0x7f040968

.field public static textColorAlertDialogListItem:I = 0x7f04096b

.field public static textColorSearchUrl:I = 0x7f04096f

.field public static textEndPadding:I = 0x7f040974

.field public static textFillColor:I = 0x7f040978

.field public static textInputFilledDenseStyle:I = 0x7f04097a

.field public static textInputFilledExposedDropdownMenuStyle:I = 0x7f04097b

.field public static textInputFilledStyle:I = 0x7f04097c

.field public static textInputLayoutFocusedRectEnabled:I = 0x7f04097d

.field public static textInputOutlinedDenseStyle:I = 0x7f04097e

.field public static textInputOutlinedExposedDropdownMenuStyle:I = 0x7f04097f

.field public static textInputOutlinedStyle:I = 0x7f040980

.field public static textInputStyle:I = 0x7f040981

.field public static textLocale:I = 0x7f040982

.field public static textOutlineColor:I = 0x7f040983

.field public static textOutlineThickness:I = 0x7f040984

.field public static textPanX:I = 0x7f040985

.field public static textPanY:I = 0x7f040986

.field public static textStartPadding:I = 0x7f040987

.field public static textureBlurFactor:I = 0x7f04098b

.field public static textureEffect:I = 0x7f04098c

.field public static textureHeight:I = 0x7f04098d

.field public static textureWidth:I = 0x7f04098e

.field public static theme:I = 0x7f04098f

.field public static thickness:I = 0x7f040990

.field public static thumbColor:I = 0x7f040996

.field public static thumbElevation:I = 0x7f040998

.field public static thumbIcon:I = 0x7f04099a

.field public static thumbIconSize:I = 0x7f04099b

.field public static thumbIconTint:I = 0x7f04099c

.field public static thumbIconTintMode:I = 0x7f04099d

.field public static thumbRadius:I = 0x7f04099f

.field public static thumbStrokeColor:I = 0x7f0409a1

.field public static thumbStrokeWidth:I = 0x7f0409a2

.field public static thumbTextPadding:I = 0x7f0409a3

.field public static thumbTint:I = 0x7f0409a4

.field public static thumbTintMode:I = 0x7f0409a5

.field public static tickColor:I = 0x7f0409a9

.field public static tickColorActive:I = 0x7f0409aa

.field public static tickColorInactive:I = 0x7f0409ab

.field public static tickMark:I = 0x7f0409ac

.field public static tickMarkTint:I = 0x7f0409ad

.field public static tickMarkTintMode:I = 0x7f0409ae

.field public static tickRadiusActive:I = 0x7f0409af

.field public static tickRadiusInactive:I = 0x7f0409b0

.field public static tickVisible:I = 0x7f0409b1

.field public static tint:I = 0x7f0409cc

.field public static tintMode:I = 0x7f0409cd

.field public static tintNavigationIcon:I = 0x7f0409ce

.field public static title:I = 0x7f0409cf

.field public static titleCentered:I = 0x7f0409d1

.field public static titleCollapseMode:I = 0x7f0409d3

.field public static titleEnabled:I = 0x7f0409d5

.field public static titleMargin:I = 0x7f0409d8

.field public static titleMarginBottom:I = 0x7f0409d9

.field public static titleMarginEnd:I = 0x7f0409da

.field public static titleMarginStart:I = 0x7f0409db

.field public static titleMarginTop:I = 0x7f0409dc

.field public static titleMargins:I = 0x7f0409dd

.field public static titlePositionInterpolator:I = 0x7f0409e1

.field public static titleTextAppearance:I = 0x7f0409e7

.field public static titleTextColor:I = 0x7f0409e8

.field public static titleTextEllipsize:I = 0x7f0409e9

.field public static titleTextStyle:I = 0x7f0409ea

.field public static toggleCheckedStateOnClick:I = 0x7f0409f0

.field public static toolbarId:I = 0x7f0409f2

.field public static toolbarNavigationButtonStyle:I = 0x7f0409f3

.field public static toolbarStyle:I = 0x7f0409f4

.field public static toolbarSurfaceStyle:I = 0x7f0409f5

.field public static tooltipForegroundColor:I = 0x7f0409f6

.field public static tooltipFrameBackground:I = 0x7f0409f7

.field public static tooltipStyle:I = 0x7f0409f8

.field public static tooltipText:I = 0x7f0409f9

.field public static topInsetScrimEnabled:I = 0x7f0409fd

.field public static touchAnchorId:I = 0x7f040a06

.field public static touchAnchorSide:I = 0x7f040a07

.field public static touchRegionId:I = 0x7f040a08

.field public static track:I = 0x7f040a0e

.field public static trackColor:I = 0x7f040a10

.field public static trackColorActive:I = 0x7f040a11

.field public static trackColorInactive:I = 0x7f040a12

.field public static trackCornerRadius:I = 0x7f040a13

.field public static trackDecoration:I = 0x7f040a14

.field public static trackDecorationTint:I = 0x7f040a15

.field public static trackDecorationTintMode:I = 0x7f040a16

.field public static trackHeight:I = 0x7f040a17

.field public static trackThickness:I = 0x7f040a1b

.field public static trackTint:I = 0x7f040a1c

.field public static trackTintMode:I = 0x7f040a1d

.field public static transformPivotTarget:I = 0x7f040a1e

.field public static transitionDisable:I = 0x7f040a1f

.field public static transitionEasing:I = 0x7f040a20

.field public static transitionFlags:I = 0x7f040a21

.field public static transitionPathRotate:I = 0x7f040a22

.field public static transitionShapeAppearance:I = 0x7f040a23

.field public static triggerId:I = 0x7f040a26

.field public static triggerReceiver:I = 0x7f040a27

.field public static triggerSlack:I = 0x7f040a28

.field public static ttcIndex:I = 0x7f040a29

.field public static upDuration:I = 0x7f040b73

.field public static useCompatPadding:I = 0x7f040b78

.field public static useDrawerArrowDrawable:I = 0x7f040b79

.field public static useMaterialThemeColors:I = 0x7f040b7b

.field public static values:I = 0x7f040b84

.field public static verticalOffset:I = 0x7f040b89

.field public static verticalOffsetWithText:I = 0x7f040b8a

.field public static viewInflaterClass:I = 0x7f040b8b

.field public static viewTransitionMode:I = 0x7f040b8c

.field public static viewTransitionOnCross:I = 0x7f040b8d

.field public static viewTransitionOnNegativeCross:I = 0x7f040b8e

.field public static viewTransitionOnPositiveCross:I = 0x7f040b8f

.field public static visibilityMode:I = 0x7f040b9b

.field public static voiceIcon:I = 0x7f040b9c

.field public static warmth:I = 0x7f040b9e

.field public static waveDecay:I = 0x7f040b9f

.field public static waveOffset:I = 0x7f040ba0

.field public static wavePeriod:I = 0x7f040ba1

.field public static wavePhase:I = 0x7f040ba2

.field public static waveShape:I = 0x7f040ba3

.field public static waveVariesBy:I = 0x7f040ba4

.field public static windowActionBar:I = 0x7f040bbf

.field public static windowActionBarOverlay:I = 0x7f040bc0

.field public static windowActionModeOverlay:I = 0x7f040bc1

.field public static windowFixedHeightMajor:I = 0x7f040bc2

.field public static windowFixedHeightMinor:I = 0x7f040bc3

.field public static windowFixedWidthMajor:I = 0x7f040bc4

.field public static windowFixedWidthMinor:I = 0x7f040bc5

.field public static windowMinWidthMajor:I = 0x7f040bc6

.field public static windowMinWidthMinor:I = 0x7f040bc7

.field public static windowNoTitle:I = 0x7f040bc8

.field public static yearSelectedStyle:I = 0x7f040bcb

.field public static yearStyle:I = 0x7f040bcc

.field public static yearTodayStyle:I = 0x7f040bcd


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
