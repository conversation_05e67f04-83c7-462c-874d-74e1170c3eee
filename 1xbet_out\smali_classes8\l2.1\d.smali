.class public final synthetic Ll2/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lv1/h$a;


# instance fields
.field public final synthetic a:Ll2/e;


# direct methods
.method public synthetic constructor <init>(Ll2/e;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ll2/d;->a:Ll2/e;

    return-void
.end method


# virtual methods
.method public final a(Lv1/h;)V
    .locals 1

    .line 1
    iget-object v0, p0, Ll2/d;->a:Ll2/e;

    check-cast p1, Ll2/e$c;

    invoke-virtual {v0, p1}, Ll2/e;->p(Lk2/p;)V

    return-void
.end method
