.class public final synthetic Lorg/xplatform/aggregator/impl/favorite/presentation/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

.field public final synthetic b:Ld81/b;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ld81/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/e;->a:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/e;->b:Ld81/b;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/e;->a:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/e;->b:Ld81/b;

    invoke-static {v0, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->m4(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;Ld81/b;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
