.class public final enum Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\u0017\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003j\u0002\u0008\u0004j\u0002\u0008\u0005j\u0002\u0008\u0006j\u0002\u0008\u0007j\u0002\u0008\u0008j\u0002\u0008\tj\u0002\u0008\nj\u0002\u0008\u000bj\u0002\u0008\u000cj\u0002\u0008\rj\u0002\u0008\u000ej\u0002\u0008\u000fj\u0002\u0008\u0010j\u0002\u0008\u0011j\u0002\u0008\u0012j\u0002\u0008\u0013j\u0002\u0008\u0014j\u0002\u0008\u0015j\u0002\u0008\u0016j\u0002\u0008\u0017\u00a8\u0006\u0018"
    }
    d2 = {
        "Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "ZERO",
        "ONE",
        "TWO",
        "THREE",
        "FOUR",
        "FIVE",
        "SIX",
        "SEVEN",
        "EIGHT",
        "NINE",
        "TEN",
        "ELEVEN",
        "TWELVE",
        "FIRST_HALF",
        "LAST_HALF",
        "LOW",
        "MIDDLE",
        "HIGH",
        "RED",
        "BLACK",
        "african_roulette_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

.field public static final enum BLACK:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "19"
    .end annotation
.end field

.field public static final enum EIGHT:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "8"
    .end annotation
.end field

.field public static final enum ELEVEN:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "11"
    .end annotation
.end field

.field public static final enum FIRST_HALF:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "13"
    .end annotation
.end field

.field public static final enum FIVE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "5"
    .end annotation
.end field

.field public static final enum FOUR:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "4"
    .end annotation
.end field

.field public static final enum HIGH:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "17"
    .end annotation
.end field

.field public static final enum LAST_HALF:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "14"
    .end annotation
.end field

.field public static final enum LOW:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "15"
    .end annotation
.end field

.field public static final enum MIDDLE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "16"
    .end annotation
.end field

.field public static final enum NINE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "9"
    .end annotation
.end field

.field public static final enum ONE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "1"
    .end annotation
.end field

.field public static final enum RED:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "18"
    .end annotation
.end field

.field public static final enum SEVEN:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "7"
    .end annotation
.end field

.field public static final enum SIX:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "6"
    .end annotation
.end field

.field public static final enum TEN:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "10"
    .end annotation
.end field

.field public static final enum THREE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "3"
    .end annotation
.end field

.field public static final enum TWELVE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "12"
    .end annotation
.end field

.field public static final enum TWO:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "2"
    .end annotation
.end field

.field public static final enum ZERO:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "0"
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 2
    .line 3
    const-string v1, "ZERO"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->ZERO:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 12
    .line 13
    const-string v1, "ONE"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->ONE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 20
    .line 21
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 22
    .line 23
    const-string v1, "TWO"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->TWO:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 30
    .line 31
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 32
    .line 33
    const-string v1, "THREE"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->THREE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 42
    .line 43
    const-string v1, "FOUR"

    .line 44
    .line 45
    const/4 v2, 0x4

    .line 46
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 47
    .line 48
    .line 49
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->FOUR:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 50
    .line 51
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 52
    .line 53
    const-string v1, "FIVE"

    .line 54
    .line 55
    const/4 v2, 0x5

    .line 56
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->FIVE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 60
    .line 61
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 62
    .line 63
    const-string v1, "SIX"

    .line 64
    .line 65
    const/4 v2, 0x6

    .line 66
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 67
    .line 68
    .line 69
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->SIX:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 70
    .line 71
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 72
    .line 73
    const-string v1, "SEVEN"

    .line 74
    .line 75
    const/4 v2, 0x7

    .line 76
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 77
    .line 78
    .line 79
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->SEVEN:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 80
    .line 81
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 82
    .line 83
    const-string v1, "EIGHT"

    .line 84
    .line 85
    const/16 v2, 0x8

    .line 86
    .line 87
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 88
    .line 89
    .line 90
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->EIGHT:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 91
    .line 92
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 93
    .line 94
    const-string v1, "NINE"

    .line 95
    .line 96
    const/16 v2, 0x9

    .line 97
    .line 98
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 99
    .line 100
    .line 101
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->NINE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 102
    .line 103
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 104
    .line 105
    const-string v1, "TEN"

    .line 106
    .line 107
    const/16 v2, 0xa

    .line 108
    .line 109
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 110
    .line 111
    .line 112
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->TEN:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 113
    .line 114
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 115
    .line 116
    const-string v1, "ELEVEN"

    .line 117
    .line 118
    const/16 v2, 0xb

    .line 119
    .line 120
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 121
    .line 122
    .line 123
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->ELEVEN:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 124
    .line 125
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 126
    .line 127
    const-string v1, "TWELVE"

    .line 128
    .line 129
    const/16 v2, 0xc

    .line 130
    .line 131
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 132
    .line 133
    .line 134
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->TWELVE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 135
    .line 136
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 137
    .line 138
    const-string v1, "FIRST_HALF"

    .line 139
    .line 140
    const/16 v2, 0xd

    .line 141
    .line 142
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 143
    .line 144
    .line 145
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->FIRST_HALF:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 146
    .line 147
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 148
    .line 149
    const-string v1, "LAST_HALF"

    .line 150
    .line 151
    const/16 v2, 0xe

    .line 152
    .line 153
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 154
    .line 155
    .line 156
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->LAST_HALF:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 157
    .line 158
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 159
    .line 160
    const-string v1, "LOW"

    .line 161
    .line 162
    const/16 v2, 0xf

    .line 163
    .line 164
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 165
    .line 166
    .line 167
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->LOW:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 168
    .line 169
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 170
    .line 171
    const-string v1, "MIDDLE"

    .line 172
    .line 173
    const/16 v2, 0x10

    .line 174
    .line 175
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 176
    .line 177
    .line 178
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->MIDDLE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 179
    .line 180
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 181
    .line 182
    const-string v1, "HIGH"

    .line 183
    .line 184
    const/16 v2, 0x11

    .line 185
    .line 186
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 187
    .line 188
    .line 189
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->HIGH:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 190
    .line 191
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 192
    .line 193
    const-string v1, "RED"

    .line 194
    .line 195
    const/16 v2, 0x12

    .line 196
    .line 197
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 198
    .line 199
    .line 200
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->RED:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 201
    .line 202
    new-instance v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 203
    .line 204
    const-string v1, "BLACK"

    .line 205
    .line 206
    const/16 v2, 0x13

    .line 207
    .line 208
    invoke-direct {v0, v1, v2}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;-><init>(Ljava/lang/String;I)V

    .line 209
    .line 210
    .line 211
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->BLACK:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 212
    .line 213
    invoke-static {}, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->a()[Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 214
    .line 215
    .line 216
    move-result-object v0

    .line 217
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->$VALUES:[Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 218
    .line 219
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 220
    .line 221
    .line 222
    move-result-object v0

    .line 223
    sput-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->$ENTRIES:Lkotlin/enums/a;

    .line 224
    .line 225
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .locals 3

    .line 1
    const/16 v0, 0x14

    new-array v0, v0, [Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->ZERO:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->ONE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->TWO:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->THREE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->FOUR:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->FIVE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->SIX:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->SEVEN:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->EIGHT:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/16 v2, 0x8

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->NINE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/16 v2, 0x9

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->TEN:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/16 v2, 0xa

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->ELEVEN:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/16 v2, 0xb

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->TWELVE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/16 v2, 0xc

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->FIRST_HALF:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/16 v2, 0xd

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->LAST_HALF:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/16 v2, 0xe

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->LOW:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/16 v2, 0xf

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->MIDDLE:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/16 v2, 0x10

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->HIGH:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/16 v2, 0x11

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->RED:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/16 v2, 0x12

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->BLACK:Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    const/16 v2, 0x13

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;->$VALUES:[Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/african_roulette/data/request/AfricanRouletteBetTypeRequest;

    .line 8
    .line 9
    return-object v0
.end method
