.class public final enum Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_web_games/game_collection/GameCollectionType$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0007\n\u0002\u0010\u000e\n\u0002\u0008\u0011\n\u0002\u0010\u000b\n\u0002\u0008\u0005\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001BW\u0008\u0002\u0012\u0008\u0008\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0008\u0008\u0001\u0010\u0004\u001a\u00020\u0003\u0012\u0008\u0008\u0001\u0010\u0005\u001a\u00020\u0003\u0012\u0008\u0008\u0001\u0010\u0006\u001a\u00020\u0003\u0012\u0008\u0008\u0001\u0010\u0007\u001a\u00020\u0003\u0012\u0008\u0008\u0001\u0010\u0008\u001a\u00020\u0003\u0012\u0008\u0008\u0001\u0010\t\u001a\u00020\u0003\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0004\u0008\u000c\u0010\rR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u000e\u0010\u000fR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0010\u0010\u000fR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0011\u0010\u000fR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0012\u0010\u000fR\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0013\u0010\u000fR\u0011\u0010\u0008\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0014\u0010\u000fR\u0011\u0010\t\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0015\u0010\u000fR\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0016\u0010\u0017R\u0011\u0010\u001c\u001a\u00020\u001d8F\u00a2\u0006\u0006\u001a\u0004\u0008\u001e\u0010\u001fR\u0011\u0010 \u001a\u00020\u00038G\u00a2\u0006\u0006\u001a\u0004\u0008!\u0010\u000fj\u0002\u0008\u0018j\u0002\u0008\u0019j\u0002\u0008\u001aj\u0002\u0008\u001b\u00a8\u0006\""
    }
    d2 = {
        "Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;",
        "",
        "heightRes",
        "",
        "widthRes",
        "pictureHeightRes",
        "pictureWidthRes",
        "iconSizeRes",
        "shapeAppearanceRes",
        "textStyleRes",
        "configType",
        "",
        "<init>",
        "(Ljava/lang/String;IIIIIIIILjava/lang/String;)V",
        "getHeightRes",
        "()I",
        "getWidthRes",
        "getPictureHeightRes",
        "getPictureWidthRes",
        "getIconSizeRes",
        "getShapeAppearanceRes",
        "getTextStyleRes",
        "getConfigType",
        "()Ljava/lang/String;",
        "Circle",
        "SquareS",
        "SquareL",
        "Rectangle",
        "hasGradient",
        "",
        "getHasGradient",
        "()Z",
        "layoutResId",
        "getLayoutResId",
        "uikit_web_games_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

.field public static final enum Circle:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

.field public static final enum Rectangle:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

.field public static final enum SquareL:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

.field public static final enum SquareS:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;


# instance fields
.field private final configType:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private final heightRes:I

.field private final iconSizeRes:I

.field private final pictureHeightRes:I

.field private final pictureWidthRes:I

.field private final shapeAppearanceRes:I

.field private final textStyleRes:I

.field private final widthRes:I


# direct methods
.method static constructor <clinit>()V
    .locals 14

    .line 1
    sget v4, LlZ0/g;->size_56:I

    .line 2
    .line 3
    sget v3, LlZ0/g;->size_84:I

    .line 4
    .line 5
    sget v7, LlZ0/g;->size_24:I

    .line 6
    .line 7
    sget v8, LlZ0/n;->ShapeAppearance_Circle:I

    .line 8
    .line 9
    sget v9, LlZ0/n;->TextStyle_Caption_Medium_M_TextPrimary:I

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 12
    .line 13
    const/4 v2, 0x0

    .line 14
    const-string v10, "smallCircles"

    .line 15
    .line 16
    const-string v1, "Circle"

    .line 17
    .line 18
    move v5, v4

    .line 19
    move v6, v4

    .line 20
    invoke-direct/range {v0 .. v10}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;-><init>(Ljava/lang/String;IIIIIIIILjava/lang/String;)V

    .line 21
    .line 22
    .line 23
    sput-object v0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->Circle:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 24
    .line 25
    sget v5, LlZ0/g;->size_64:I

    .line 26
    .line 27
    sget v4, LlZ0/g;->size_92:I

    .line 28
    .line 29
    sget v8, LlZ0/g;->size_28:I

    .line 30
    .line 31
    sget v9, LlZ0/n;->ShapeAppearance_Radius16:I

    .line 32
    .line 33
    sget v10, LlZ0/n;->TextStyle_Caption_Medium_M_TextPrimary:I

    .line 34
    .line 35
    new-instance v1, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 36
    .line 37
    const/4 v3, 0x1

    .line 38
    const-string v11, "smallSquares"

    .line 39
    .line 40
    const-string v2, "SquareS"

    .line 41
    .line 42
    move v6, v5

    .line 43
    move v7, v5

    .line 44
    invoke-direct/range {v1 .. v11}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;-><init>(Ljava/lang/String;IIIIIIIILjava/lang/String;)V

    .line 45
    .line 46
    .line 47
    sput-object v1, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->SquareS:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 48
    .line 49
    sget v6, LlZ0/g;->size_96:I

    .line 50
    .line 51
    sget v5, LlZ0/g;->size_128:I

    .line 52
    .line 53
    sget v9, LlZ0/g;->size_40:I

    .line 54
    .line 55
    sget v10, LlZ0/n;->ShapeAppearance_Radius24:I

    .line 56
    .line 57
    sget v11, LlZ0/n;->TextStyle_Caption_Medium_L_TextPrimary:I

    .line 58
    .line 59
    new-instance v2, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 60
    .line 61
    const/4 v4, 0x2

    .line 62
    const-string v12, "largeSquares"

    .line 63
    .line 64
    const-string v3, "SquareL"

    .line 65
    .line 66
    move v7, v6

    .line 67
    move v8, v6

    .line 68
    invoke-direct/range {v2 .. v12}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;-><init>(Ljava/lang/String;IIIIIIIILjava/lang/String;)V

    .line 69
    .line 70
    .line 71
    sput-object v2, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->SquareL:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 72
    .line 73
    sget v7, LlZ0/g;->size_144:I

    .line 74
    .line 75
    sget v6, LlZ0/g;->size_96:I

    .line 76
    .line 77
    sget v10, LlZ0/g;->size_38:I

    .line 78
    .line 79
    sget v11, LlZ0/n;->ShapeAppearance_Radius16:I

    .line 80
    .line 81
    sget v12, LlZ0/n;->TextStyle_Caption_Bold_L_StaticWhite:I

    .line 82
    .line 83
    new-instance v3, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 84
    .line 85
    const/4 v5, 0x3

    .line 86
    const-string v13, "mediumRectangles"

    .line 87
    .line 88
    const-string v4, "Rectangle"

    .line 89
    .line 90
    move v8, v6

    .line 91
    move v9, v7

    .line 92
    invoke-direct/range {v3 .. v13}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;-><init>(Ljava/lang/String;IIIIIIIILjava/lang/String;)V

    .line 93
    .line 94
    .line 95
    sput-object v3, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->Rectangle:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 96
    .line 97
    invoke-static {}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->a()[Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 98
    .line 99
    .line 100
    move-result-object v0

    .line 101
    sput-object v0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->$VALUES:[Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 102
    .line 103
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 104
    .line 105
    .line 106
    move-result-object v0

    .line 107
    sput-object v0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->$ENTRIES:Lkotlin/enums/a;

    .line 108
    .line 109
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;IIIIIIIILjava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(IIIIIII",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    iput p3, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->heightRes:I

    .line 5
    .line 6
    iput p4, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->widthRes:I

    .line 7
    .line 8
    iput p5, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->pictureHeightRes:I

    .line 9
    .line 10
    iput p6, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->pictureWidthRes:I

    .line 11
    .line 12
    iput p7, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->iconSizeRes:I

    .line 13
    .line 14
    iput p8, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->shapeAppearanceRes:I

    .line 15
    .line 16
    iput p9, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->textStyleRes:I

    .line 17
    .line 18
    iput-object p10, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->configType:Ljava/lang/String;

    .line 19
    .line 20
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;
    .locals 3

    .line 1
    const/4 v0, 0x4

    new-array v0, v0, [Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    sget-object v1, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->Circle:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->SquareS:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->SquareL:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->Rectangle:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->$VALUES:[Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getConfigType()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->configType:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final getHasGradient()Z
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->Rectangle:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 2
    .line 3
    if-ne p0, v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x1

    .line 6
    return v0

    .line 7
    :cond_0
    const/4 v0, 0x0

    .line 8
    return v0
.end method

.method public final getHeightRes()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->heightRes:I

    .line 2
    .line 3
    return v0
.end method

.method public final getIconSizeRes()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->iconSizeRes:I

    .line 2
    .line 3
    return v0
.end method

.method public final getLayoutResId()I
    .locals 2

    .line 1
    sget-object v0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    sget v0, Lj41/e;->game_collection_rectangle_view:I

    .line 13
    .line 14
    return v0

    .line 15
    :cond_0
    sget v0, Lj41/e;->game_collection_item_view:I

    .line 16
    .line 17
    return v0
.end method

.method public final getPictureHeightRes()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->pictureHeightRes:I

    .line 2
    .line 3
    return v0
.end method

.method public final getPictureWidthRes()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->pictureWidthRes:I

    .line 2
    .line 3
    return v0
.end method

.method public final getShapeAppearanceRes()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->shapeAppearanceRes:I

    .line 2
    .line 3
    return v0
.end method

.method public final getTextStyleRes()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->textStyleRes:I

    .line 2
    .line 3
    return v0
.end method

.method public final getWidthRes()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->widthRes:I

    .line 2
    .line 3
    return v0
.end method
