.class public final LH91/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "Lg81/b;",
        "LHX0/e;",
        "resourceManager",
        "Lx21/c;",
        "a",
        "(Lg81/b;LHX0/e;)Lx21/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lg81/b;LHX0/e;)Lx21/c;
    .locals 13
    .param p0    # Lg81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lg81/b;->g()J

    .line 2
    .line 3
    .line 4
    move-result-wide v1

    .line 5
    invoke-virtual {p0}, Lg81/b;->n()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v4

    .line 9
    invoke-virtual {p0}, Lg81/b;->c()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v5

    .line 13
    new-instance v0, Ln8/a;

    .line 14
    .line 15
    invoke-direct {v0}, Ln8/a;-><init>()V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0}, Lg81/b;->h()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object p0

    .line 22
    invoke-virtual {v0, p0}, Ln8/a;->c(Ljava/lang/String;)Ln8/a;

    .line 23
    .line 24
    .line 25
    move-result-object p0

    .line 26
    invoke-virtual {p0}, Ln8/a;->a()Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object p0

    .line 30
    invoke-static {p0}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 31
    .line 32
    .line 33
    move-result-object p0

    .line 34
    sget v0, Lpb/k;->aggregator_category_banner_title:I

    .line 35
    .line 36
    const/4 v3, 0x0

    .line 37
    new-array v6, v3, [Ljava/lang/Object;

    .line 38
    .line 39
    invoke-interface {p1, v0, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 40
    .line 41
    .line 42
    move-result-object v6

    .line 43
    sget v0, Lpb/k;->aggregator_category_banner_subtitle:I

    .line 44
    .line 45
    new-array v3, v3, [Ljava/lang/Object;

    .line 46
    .line 47
    invoke-interface {p1, v0, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object v7

    .line 51
    new-instance v0, Lx21/c;

    .line 52
    .line 53
    invoke-static {p0}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 54
    .line 55
    .line 56
    move-result-object v3

    .line 57
    const/16 v11, 0x1c0

    .line 58
    .line 59
    const/4 v12, 0x0

    .line 60
    const/4 v8, 0x0

    .line 61
    const/4 v9, 0x0

    .line 62
    const/4 v10, 0x0

    .line 63
    invoke-direct/range {v0 .. v12}, Lx21/c;-><init>(JLL11/c;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;LL11/c;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 64
    .line 65
    .line 66
    return-object v0
.end method
