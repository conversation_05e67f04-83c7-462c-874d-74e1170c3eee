.class public final LnT0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LnT0/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LnT0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LnT0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LnT0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LRf0/o;Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;Li8/m;Lm8/a;)LnT0/c;
    .locals 6

    .line 1
    invoke-static {p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static {p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static {p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static {p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    new-instance v0, LnT0/a$b;

    .line 14
    .line 15
    const/4 v5, 0x0

    .line 16
    move-object v1, p1

    .line 17
    move-object v2, p2

    .line 18
    move-object v3, p3

    .line 19
    move-object v4, p4

    .line 20
    invoke-direct/range {v0 .. v5}, LnT0/a$b;-><init>(LRf0/o;Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;Li8/m;Lm8/a;LnT0/b;)V

    .line 21
    .line 22
    .line 23
    return-object v0
.end method
