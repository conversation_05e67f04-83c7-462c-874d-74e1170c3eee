.class public final LIR0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0019\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "LJR0/b;",
        "",
        "LMR0/a;",
        "a",
        "(LJR0/b;)Ljava/util/List;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LJR0/b;)Ljava/util/List;
    .locals 30
    .param p0    # LJR0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LJR0/b;",
            ")",
            "Ljava/util/List<",
            "LMR0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p0}, LJR0/b;->b()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_12

    .line 6
    .line 7
    new-instance v1, Ljava/util/ArrayList;

    .line 8
    .line 9
    const/16 v2, 0xa

    .line 10
    .line 11
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 16
    .line 17
    .line 18
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    if-eqz v2, :cond_11

    .line 27
    .line 28
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    check-cast v2, LJR0/a;

    .line 33
    .line 34
    invoke-virtual/range {p0 .. p0}, LJR0/b;->a()Ljava/util/List;

    .line 35
    .line 36
    .line 37
    move-result-object v3

    .line 38
    if-eqz v3, :cond_2

    .line 39
    .line 40
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 41
    .line 42
    .line 43
    move-result-object v3

    .line 44
    :cond_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 45
    .line 46
    .line 47
    move-result v4

    .line 48
    if-eqz v4, :cond_1

    .line 49
    .line 50
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v4

    .line 54
    move-object v5, v4

    .line 55
    check-cast v5, LCN0/i;

    .line 56
    .line 57
    invoke-virtual {v5}, LCN0/i;->b()Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v5

    .line 61
    invoke-virtual {v2}, LJR0/a;->a()Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object v6

    .line 65
    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 66
    .line 67
    .line 68
    move-result v5

    .line 69
    if-eqz v5, :cond_0

    .line 70
    .line 71
    goto :goto_1

    .line 72
    :cond_1
    const/4 v4, 0x0

    .line 73
    :goto_1
    check-cast v4, LCN0/i;

    .line 74
    .line 75
    if-eqz v4, :cond_2

    .line 76
    .line 77
    invoke-static {v4}, LBN0/h;->b(LCN0/i;)LND0/h;

    .line 78
    .line 79
    .line 80
    move-result-object v3

    .line 81
    if-eqz v3, :cond_2

    .line 82
    .line 83
    :goto_2
    move-object v5, v3

    .line 84
    goto :goto_3

    .line 85
    :cond_2
    sget-object v3, LND0/h;->g:LND0/h$a;

    .line 86
    .line 87
    invoke-virtual {v3}, LND0/h$a;->a()LND0/h;

    .line 88
    .line 89
    .line 90
    move-result-object v3

    .line 91
    goto :goto_2

    .line 92
    :goto_3
    invoke-virtual {v2}, LJR0/a;->e()Ljava/lang/String;

    .line 93
    .line 94
    .line 95
    move-result-object v3

    .line 96
    const-string v4, ""

    .line 97
    .line 98
    if-nez v3, :cond_3

    .line 99
    .line 100
    move-object v6, v4

    .line 101
    goto :goto_4

    .line 102
    :cond_3
    move-object v6, v3

    .line 103
    :goto_4
    invoke-virtual {v2}, LJR0/a;->o()Ljava/lang/String;

    .line 104
    .line 105
    .line 106
    move-result-object v3

    .line 107
    if-nez v3, :cond_4

    .line 108
    .line 109
    move-object v7, v4

    .line 110
    goto :goto_5

    .line 111
    :cond_4
    move-object v7, v3

    .line 112
    :goto_5
    invoke-virtual {v2}, LJR0/a;->c()Ljava/lang/String;

    .line 113
    .line 114
    .line 115
    move-result-object v3

    .line 116
    if-nez v3, :cond_5

    .line 117
    .line 118
    move-object v8, v4

    .line 119
    goto :goto_6

    .line 120
    :cond_5
    move-object v8, v3

    .line 121
    :goto_6
    invoke-virtual {v2}, LJR0/a;->l()Ljava/lang/Integer;

    .line 122
    .line 123
    .line 124
    move-result-object v3

    .line 125
    const v9, 0x7fffffff

    .line 126
    .line 127
    .line 128
    if-eqz v3, :cond_6

    .line 129
    .line 130
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 131
    .line 132
    .line 133
    move-result v3

    .line 134
    goto :goto_7

    .line 135
    :cond_6
    const v3, 0x7fffffff

    .line 136
    .line 137
    .line 138
    :goto_7
    invoke-virtual {v2}, LJR0/a;->m()Ljava/lang/Integer;

    .line 139
    .line 140
    .line 141
    move-result-object v10

    .line 142
    if-eqz v10, :cond_7

    .line 143
    .line 144
    invoke-virtual {v10}, Ljava/lang/Integer;->intValue()I

    .line 145
    .line 146
    .line 147
    move-result v10

    .line 148
    goto :goto_8

    .line 149
    :cond_7
    const v10, 0x7fffffff

    .line 150
    .line 151
    .line 152
    :goto_8
    invoke-virtual {v2}, LJR0/a;->b()Ljava/lang/Integer;

    .line 153
    .line 154
    .line 155
    move-result-object v11

    .line 156
    if-eqz v11, :cond_8

    .line 157
    .line 158
    invoke-virtual {v11}, Ljava/lang/Integer;->intValue()I

    .line 159
    .line 160
    .line 161
    move-result v11

    .line 162
    goto :goto_9

    .line 163
    :cond_8
    const v11, 0x7fffffff

    .line 164
    .line 165
    .line 166
    :goto_9
    invoke-virtual {v2}, LJR0/a;->n()Ljava/lang/Double;

    .line 167
    .line 168
    .line 169
    move-result-object v12

    .line 170
    const-wide v13, 0x7fefffffffffffffL    # Double.MAX_VALUE

    .line 171
    .line 172
    .line 173
    .line 174
    .line 175
    if-eqz v12, :cond_9

    .line 176
    .line 177
    invoke-virtual {v12}, Ljava/lang/Double;->doubleValue()D

    .line 178
    .line 179
    .line 180
    move-result-wide v15

    .line 181
    goto :goto_a

    .line 182
    :cond_9
    move-wide v15, v13

    .line 183
    :goto_a
    invoke-virtual {v2}, LJR0/a;->f()Ljava/lang/Double;

    .line 184
    .line 185
    .line 186
    move-result-object v12

    .line 187
    if-eqz v12, :cond_a

    .line 188
    .line 189
    invoke-virtual {v12}, Ljava/lang/Double;->doubleValue()D

    .line 190
    .line 191
    .line 192
    move-result-wide v17

    .line 193
    goto :goto_b

    .line 194
    :cond_a
    move-wide/from16 v17, v13

    .line 195
    .line 196
    :goto_b
    invoke-virtual {v2}, LJR0/a;->h()Ljava/lang/Double;

    .line 197
    .line 198
    .line 199
    move-result-object v12

    .line 200
    if-eqz v12, :cond_b

    .line 201
    .line 202
    invoke-virtual {v12}, Ljava/lang/Double;->doubleValue()D

    .line 203
    .line 204
    .line 205
    move-result-wide v19

    .line 206
    goto :goto_c

    .line 207
    :cond_b
    move-wide/from16 v19, v13

    .line 208
    .line 209
    :goto_c
    invoke-virtual {v2}, LJR0/a;->j()Ljava/lang/Double;

    .line 210
    .line 211
    .line 212
    move-result-object v12

    .line 213
    if-eqz v12, :cond_c

    .line 214
    .line 215
    invoke-virtual {v12}, Ljava/lang/Double;->doubleValue()D

    .line 216
    .line 217
    .line 218
    move-result-wide v21

    .line 219
    goto :goto_d

    .line 220
    :cond_c
    move-wide/from16 v21, v13

    .line 221
    .line 222
    :goto_d
    invoke-virtual {v2}, LJR0/a;->k()Ljava/lang/Double;

    .line 223
    .line 224
    .line 225
    move-result-object v12

    .line 226
    if-eqz v12, :cond_d

    .line 227
    .line 228
    invoke-virtual {v12}, Ljava/lang/Double;->doubleValue()D

    .line 229
    .line 230
    .line 231
    move-result-wide v13

    .line 232
    :cond_d
    invoke-virtual {v2}, LJR0/a;->d()Ljava/lang/Integer;

    .line 233
    .line 234
    .line 235
    move-result-object v12

    .line 236
    if-eqz v12, :cond_e

    .line 237
    .line 238
    invoke-virtual {v12}, Ljava/lang/Integer;->intValue()I

    .line 239
    .line 240
    .line 241
    move-result v9

    .line 242
    :cond_e
    invoke-virtual {v2}, LJR0/a;->g()Ljava/lang/String;

    .line 243
    .line 244
    .line 245
    move-result-object v12

    .line 246
    if-nez v12, :cond_f

    .line 247
    .line 248
    move-object/from16 v23, v4

    .line 249
    .line 250
    goto :goto_e

    .line 251
    :cond_f
    move-object/from16 v23, v12

    .line 252
    .line 253
    :goto_e
    invoke-virtual {v2}, LJR0/a;->i()Ljava/lang/String;

    .line 254
    .line 255
    .line 256
    move-result-object v2

    .line 257
    if-nez v2, :cond_10

    .line 258
    .line 259
    move-object/from16 v24, v4

    .line 260
    .line 261
    goto :goto_f

    .line 262
    :cond_10
    move-object/from16 v24, v2

    .line 263
    .line 264
    :goto_f
    new-instance v4, LMR0/a;

    .line 265
    .line 266
    move/from16 v25, v9

    .line 267
    .line 268
    move v9, v3

    .line 269
    move-wide/from16 v26, v21

    .line 270
    .line 271
    move/from16 v22, v25

    .line 272
    .line 273
    move-wide/from16 v28, v19

    .line 274
    .line 275
    move-wide/from16 v20, v13

    .line 276
    .line 277
    move-wide v12, v15

    .line 278
    move-wide/from16 v14, v17

    .line 279
    .line 280
    move-wide/from16 v16, v28

    .line 281
    .line 282
    move-wide/from16 v18, v26

    .line 283
    .line 284
    invoke-direct/range {v4 .. v24}, LMR0/a;-><init>(LND0/h;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIIDDDDDILjava/lang/String;Ljava/lang/String;)V

    .line 285
    .line 286
    .line 287
    invoke-interface {v1, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 288
    .line 289
    .line 290
    goto/16 :goto_0

    .line 291
    .line 292
    :cond_11
    return-object v1

    .line 293
    :cond_12
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 294
    .line 295
    .line 296
    move-result-object v0

    .line 297
    return-object v0
.end method
