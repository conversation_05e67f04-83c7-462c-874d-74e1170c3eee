.class public interface abstract LON0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u0008f\u0018\u00002\u00020\u0001J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "LON0/b;",
        "",
        "Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;",
        "headerState",
        "",
        "setModel",
        "(Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;)V",
        "statistic_core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract setModel(Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;)V
    .param p1    # Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method
