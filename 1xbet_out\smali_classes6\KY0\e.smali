.class public final synthetic LKY0/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:LKY0/g;

.field public final synthetic b:F

.field public final synthetic c:Lkotlin/jvm/functions/Function1;

.field public final synthetic d:LLY0/b;


# direct methods
.method public synthetic constructor <init>(LKY0/g;FLkotlin/jvm/functions/Function1;LLY0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LKY0/e;->a:LKY0/g;

    iput p2, p0, LKY0/e;->b:F

    iput-object p3, p0, LKY0/e;->c:Lkotlin/jvm/functions/Function1;

    iput-object p4, p0, LKY0/e;->d:LLY0/b;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 4

    .line 1
    iget-object v0, p0, LKY0/e;->a:LKY0/g;

    iget v1, p0, LKY0/e;->b:F

    iget-object v2, p0, LKY0/e;->c:Lkotlin/jvm/functions/Function1;

    iget-object v3, p0, LKY0/e;->d:LLY0/b;

    invoke-static {v0, v1, v2, v3}, LKY0/g;->f(LKY0/g;FLkotlin/jvm/functions/Function1;LLY0/b;)V

    return-void
.end method
