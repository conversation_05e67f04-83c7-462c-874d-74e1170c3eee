.class public interface abstract Lt71/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0008\u0005\u0008f\u0018\u00002\u00020\u0001J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J+\u0010\n\u001a\u00020\u00042\u000c\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u00072\u000c\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0007H&\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "Lt71/b;",
        "",
        "Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;",
        "widgetSectionsType",
        "",
        "b",
        "(Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;)V",
        "",
        "selectSection",
        "unselectSection",
        "a",
        "(Ljava/util/List;Ljava/util/List;)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Ljava/util/List;Ljava/util/List;)V
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract b(Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;)V
    .param p1    # Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method
