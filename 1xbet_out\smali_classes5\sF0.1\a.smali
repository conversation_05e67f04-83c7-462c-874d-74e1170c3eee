.class public final LsF0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u001a\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "LtF0/c;",
        "LvF0/a;",
        "b",
        "(LtF0/c;)LvF0/a;",
        "",
        "gender",
        "Lorg/xbet/statistic/horse_menu/domain/model/HorseGenderModel;",
        "a",
        "(Ljava/lang/String;)Lorg/xbet/statistic/horse_menu/domain/model/HorseGenderModel;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ljava/lang/String;)Lorg/xbet/statistic/horse_menu/domain/model/HorseGenderModel;
    .locals 1

    .line 1
    invoke-virtual {p0}, Ljava/lang/String;->hashCode()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    packed-switch v0, :pswitch_data_0

    .line 6
    .line 7
    .line 8
    goto :goto_0

    .line 9
    :pswitch_0
    const-string v0, "3"

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    move-result p0

    .line 15
    if-nez p0, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    sget-object p0, Lorg/xbet/statistic/horse_menu/domain/model/HorseGenderModel;->GELDING:Lorg/xbet/statistic/horse_menu/domain/model/HorseGenderModel;

    .line 19
    .line 20
    return-object p0

    .line 21
    :pswitch_1
    const-string v0, "2"

    .line 22
    .line 23
    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    move-result p0

    .line 27
    if-nez p0, :cond_1

    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_1
    sget-object p0, Lorg/xbet/statistic/horse_menu/domain/model/HorseGenderModel;->MALE:Lorg/xbet/statistic/horse_menu/domain/model/HorseGenderModel;

    .line 31
    .line 32
    return-object p0

    .line 33
    :pswitch_2
    const-string v0, "1"

    .line 34
    .line 35
    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 36
    .line 37
    .line 38
    move-result p0

    .line 39
    if-nez p0, :cond_2

    .line 40
    .line 41
    :goto_0
    sget-object p0, Lorg/xbet/statistic/horse_menu/domain/model/HorseGenderModel;->UNKNOWN:Lorg/xbet/statistic/horse_menu/domain/model/HorseGenderModel;

    .line 42
    .line 43
    return-object p0

    .line 44
    :cond_2
    sget-object p0, Lorg/xbet/statistic/horse_menu/domain/model/HorseGenderModel;->FEMALE:Lorg/xbet/statistic/horse_menu/domain/model/HorseGenderModel;

    .line 45
    .line 46
    return-object p0

    .line 47
    :pswitch_data_0
    .packed-switch 0x31
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static final b(LtF0/c;)LvF0/a;
    .locals 9
    .param p0    # LtF0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LtF0/c;->a()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LtF0/b;

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    move-object v0, v1

    .line 16
    :goto_0
    if-eqz v0, :cond_a

    .line 17
    .line 18
    invoke-virtual {v0}, LtF0/b;->b()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object v3

    .line 22
    if-eqz v3, :cond_a

    .line 23
    .line 24
    invoke-virtual {v0}, LtF0/b;->c()Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object v2

    .line 28
    const-string v4, ""

    .line 29
    .line 30
    if-nez v2, :cond_1

    .line 31
    .line 32
    move-object v2, v4

    .line 33
    :cond_1
    invoke-virtual {p0}, LtF0/c;->b()LtF0/a;

    .line 34
    .line 35
    .line 36
    move-result-object v5

    .line 37
    if-eqz v5, :cond_2

    .line 38
    .line 39
    invoke-virtual {v5}, LtF0/a;->c()Ljava/lang/String;

    .line 40
    .line 41
    .line 42
    move-result-object v5

    .line 43
    goto :goto_1

    .line 44
    :cond_2
    move-object v5, v1

    .line 45
    :goto_1
    if-nez v5, :cond_3

    .line 46
    .line 47
    move-object v5, v4

    .line 48
    :cond_3
    invoke-virtual {p0}, LtF0/c;->b()LtF0/a;

    .line 49
    .line 50
    .line 51
    move-result-object v6

    .line 52
    if-eqz v6, :cond_4

    .line 53
    .line 54
    invoke-virtual {v6}, LtF0/a;->a()Ljava/lang/String;

    .line 55
    .line 56
    .line 57
    move-result-object v6

    .line 58
    goto :goto_2

    .line 59
    :cond_4
    move-object v6, v1

    .line 60
    :goto_2
    if-nez v6, :cond_5

    .line 61
    .line 62
    move-object v6, v4

    .line 63
    :cond_5
    invoke-virtual {p0}, LtF0/c;->b()LtF0/a;

    .line 64
    .line 65
    .line 66
    move-result-object p0

    .line 67
    if-eqz p0, :cond_7

    .line 68
    .line 69
    invoke-virtual {p0}, LtF0/a;->b()Ljava/util/List;

    .line 70
    .line 71
    .line 72
    move-result-object p0

    .line 73
    if-eqz p0, :cond_7

    .line 74
    .line 75
    new-instance v1, Ljava/util/ArrayList;

    .line 76
    .line 77
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 78
    .line 79
    .line 80
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 81
    .line 82
    .line 83
    move-result-object p0

    .line 84
    :cond_6
    :goto_3
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 85
    .line 86
    .line 87
    move-result v7

    .line 88
    if-eqz v7, :cond_7

    .line 89
    .line 90
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 91
    .line 92
    .line 93
    move-result-object v7

    .line 94
    check-cast v7, LtF0/d;

    .line 95
    .line 96
    invoke-static {v7}, LsF0/b;->a(LtF0/d;)LvF0/b;

    .line 97
    .line 98
    .line 99
    move-result-object v7

    .line 100
    if-eqz v7, :cond_6

    .line 101
    .line 102
    invoke-interface {v1, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 103
    .line 104
    .line 105
    goto :goto_3

    .line 106
    :cond_7
    if-nez v1, :cond_8

    .line 107
    .line 108
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 109
    .line 110
    .line 111
    move-result-object v1

    .line 112
    :cond_8
    move-object v8, v1

    .line 113
    invoke-virtual {v0}, LtF0/b;->a()Ljava/lang/String;

    .line 114
    .line 115
    .line 116
    move-result-object p0

    .line 117
    if-nez p0, :cond_9

    .line 118
    .line 119
    goto :goto_4

    .line 120
    :cond_9
    move-object v4, p0

    .line 121
    :goto_4
    invoke-static {v4}, LsF0/a;->a(Ljava/lang/String;)Lorg/xbet/statistic/horse_menu/domain/model/HorseGenderModel;

    .line 122
    .line 123
    .line 124
    move-result-object v7

    .line 125
    move-object v4, v2

    .line 126
    new-instance v2, LvF0/a;

    .line 127
    .line 128
    invoke-direct/range {v2 .. v8}, LvF0/a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/statistic/horse_menu/domain/model/HorseGenderModel;Ljava/util/List;)V

    .line 129
    .line 130
    .line 131
    return-object v2

    .line 132
    :cond_a
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 133
    .line 134
    const/4 v0, 0x1

    .line 135
    invoke-direct {p0, v1, v0, v1}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 136
    .line 137
    .line 138
    throw p0
.end method
