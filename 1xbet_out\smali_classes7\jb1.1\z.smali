.class public final Ljb1/z;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0006\u001a\u00020\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "Li81/a;",
        "LHX0/e;",
        "resourceManager",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;",
        "aggregatorTournamentProgressStyleType",
        "Lkb1/u;",
        "a",
        "(Li81/a;LHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;)Lkb1/u;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Li81/a;LHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;)Lkb1/u;
    .locals 4
    .param p0    # Li81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {p0, p1, p2}, Ljb1/i;->d(Li81/a;LHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;)Lq21/a;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    if-eqz p2, :cond_0

    .line 6
    .line 7
    new-instance v0, Llb1/e;

    .line 8
    .line 9
    invoke-direct {v0, p2}, Llb1/e;-><init>(Lq21/b;)V

    .line 10
    .line 11
    .line 12
    invoke-static {v0}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 13
    .line 14
    .line 15
    move-result-object p2

    .line 16
    if-eqz p2, :cond_0

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    :goto_0
    invoke-virtual {p0}, Li81/a;->f()Lm81/a;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    invoke-virtual {v0}, Lm81/a;->a()Ljava/util/List;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    if-nez v0, :cond_1

    .line 36
    .line 37
    invoke-virtual {p0}, Li81/a;->f()Lm81/a;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    invoke-virtual {v0}, Lm81/a;->a()Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    new-instance v1, Ljava/util/ArrayList;

    .line 46
    .line 47
    const/16 v2, 0xa

    .line 48
    .line 49
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 50
    .line 51
    .line 52
    move-result v2

    .line 53
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 54
    .line 55
    .line 56
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 61
    .line 62
    .line 63
    move-result v2

    .line 64
    if-eqz v2, :cond_2

    .line 65
    .line 66
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    check-cast v2, Lm81/b;

    .line 71
    .line 72
    new-instance v3, Lkb1/q;

    .line 73
    .line 74
    invoke-static {v2, p1}, Ljb1/C;->b(Lm81/b;LHX0/e;)Lmb1/b;

    .line 75
    .line 76
    .line 77
    move-result-object v2

    .line 78
    invoke-direct {v3, v2}, Lkb1/q;-><init>(Lmb1/c;)V

    .line 79
    .line 80
    .line 81
    invoke-interface {v1, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 82
    .line 83
    .line 84
    goto :goto_1

    .line 85
    :cond_1
    sget-object p1, Lkb1/r;->a:Lkb1/r;

    .line 86
    .line 87
    invoke-static {p1}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    :cond_2
    invoke-virtual {p0}, Li81/a;->t()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 92
    .line 93
    .line 94
    move-result-object p1

    .line 95
    sget-object v0, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->RESULTS:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 96
    .line 97
    invoke-virtual {p0}, Li81/a;->j()Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    .line 98
    .line 99
    .line 100
    move-result-object p0

    .line 101
    invoke-static {p1, v0, p0}, Lh81/c;->a(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;)Z

    .line 102
    .line 103
    .line 104
    move-result p0

    .line 105
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 106
    .line 107
    .line 108
    move-result p1

    .line 109
    if-nez p1, :cond_3

    .line 110
    .line 111
    invoke-interface {v1}, Ljava/util/Collection;->isEmpty()Z

    .line 112
    .line 113
    .line 114
    move-result p1

    .line 115
    if-nez p1, :cond_3

    .line 116
    .line 117
    invoke-static {v1}, Lkotlin/collections/CollectionsKt;->x0(Ljava/util/List;)Ljava/lang/Object;

    .line 118
    .line 119
    .line 120
    move-result-object p1

    .line 121
    instance-of p1, p1, Lkb1/r;

    .line 122
    .line 123
    if-eqz p1, :cond_3

    .line 124
    .line 125
    new-instance p1, Llb1/f;

    .line 126
    .line 127
    invoke-static {p2}, Lkotlin/collections/CollectionsKt;->x0(Ljava/util/List;)Ljava/lang/Object;

    .line 128
    .line 129
    .line 130
    move-result-object p2

    .line 131
    check-cast p2, Llb1/e;

    .line 132
    .line 133
    invoke-virtual {p2}, Llb1/e;->d()Lq21/b;

    .line 134
    .line 135
    .line 136
    move-result-object p2

    .line 137
    invoke-direct {p1, p2}, Llb1/f;-><init>(Lq21/b;)V

    .line 138
    .line 139
    .line 140
    invoke-static {p1}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 141
    .line 142
    .line 143
    move-result-object p1

    .line 144
    goto :goto_2

    .line 145
    :cond_3
    invoke-static {p2, v1}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 146
    .line 147
    .line 148
    move-result-object p1

    .line 149
    :goto_2
    new-instance p2, Lkb1/u;

    .line 150
    .line 151
    invoke-direct {p2, p1, p0}, Lkb1/u;-><init>(Ljava/util/List;Z)V

    .line 152
    .line 153
    .line 154
    return-object p2
.end method
