.class public final Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;
.super LXW0/a;
.source "SourceFile"

# interfaces
.implements LXW0/h;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0082\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u0000 W2\u00020\u00012\u00020\u0002:\u0001XB\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u0017\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0006\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001d\u0010\r\u001a\u00020\u00072\u000c\u0010\u000c\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\nH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u0011\u001a\u00020\u00072\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0017\u0010\u0015\u001a\u00020\u00072\u0006\u0010\u0014\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0017\u0010\u0019\u001a\u00020\u00072\u0006\u0010\u0018\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u000f\u0010\u001b\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u0004J\u0017\u0010\u001e\u001a\u00020\u00072\u0006\u0010\u001d\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u000f\u0010 \u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008 \u0010\u0004J\u000f\u0010!\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008!\u0010\u0004J\u000f\u0010\"\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\"\u0010\u0004J\u0019\u0010%\u001a\u00020\u00072\u0008\u0010$\u001a\u0004\u0018\u00010#H\u0014\u00a2\u0006\u0004\u0008%\u0010&J\u000f\u0010\'\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\'\u0010\u0004J\u000f\u0010(\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u0008(\u0010\u0004J\u000f\u0010)\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u0008)\u0010\u0004J\u000f\u0010*\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u0008*\u0010\u0004J\u000f\u0010+\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u0008+\u0010\u0004J\u000f\u0010,\u001a\u00020\u0017H\u0016\u00a2\u0006\u0004\u0008,\u0010-R\u001a\u00101\u001a\u00020\u00178\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008.\u0010/\u001a\u0004\u00080\u0010-R\"\u00109\u001a\u0002028\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u00083\u00104\u001a\u0004\u00085\u00106\"\u0004\u00087\u00108R\"\u0010A\u001a\u00020:8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008;\u0010<\u001a\u0004\u0008=\u0010>\"\u0004\u0008?\u0010@R\u001b\u0010G\u001a\u00020B8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008C\u0010D\u001a\u0004\u0008E\u0010FR\u001b\u0010M\u001a\u00020H8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008I\u0010J\u001a\u0004\u0008K\u0010LR\u0014\u0010Q\u001a\u00020N8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008O\u0010PR\u001b\u0010V\u001a\u00020R8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008S\u0010D\u001a\u0004\u0008T\u0010U\u00a8\u0006Y"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;",
        "LXW0/a;",
        "LXW0/h;",
        "<init>",
        "()V",
        "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d;",
        "viewState",
        "",
        "M2",
        "(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d;)V",
        "",
        "LVX0/i;",
        "gamesContent",
        "L2",
        "(Ljava/util/List;)V",
        "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a;",
        "event",
        "K2",
        "(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a;)V",
        "",
        "deeplink",
        "R2",
        "(Ljava/lang/String;)V",
        "",
        "show",
        "V2",
        "(Z)V",
        "S2",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "lottieConfig",
        "T2",
        "(Lorg/xbet/uikit/components/lottie_empty/n;)V",
        "N2",
        "U2",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "onResume",
        "onPause",
        "onDestroyView",
        "n2",
        "Y1",
        "()Z",
        "i0",
        "Z",
        "r2",
        "showNavBar",
        "LV40/c$b;",
        "j0",
        "LV40/c$b;",
        "H2",
        "()LV40/c$b;",
        "setPopularOneXGamesViewModelFactory",
        "(LV40/c$b;)V",
        "popularOneXGamesViewModelFactory",
        "LzX0/k;",
        "k0",
        "LzX0/k;",
        "I2",
        "()LzX0/k;",
        "setSnackbarManager",
        "(LzX0/k;)V",
        "snackbarManager",
        "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;",
        "l0",
        "Lkotlin/j;",
        "J2",
        "()Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;",
        "viewModel",
        "LU40/a;",
        "m0",
        "LRc/c;",
        "G2",
        "()LU40/a;",
        "binding",
        "LUX0/k;",
        "n0",
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "LX40/a;",
        "o0",
        "F2",
        "()LX40/a;",
        "adapter",
        "b1",
        "a",
        "popular_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b1:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic k1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final i0:Z

.field public j0:LV40/c$b;

.field public k0:LzX0/k;

.field public final l0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:LUX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getBinding()Lorg/xbet/games_section/feature/popular/databinding/FragmentPopularOneXGamesBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;

    .line 7
    .line 8
    const-string v4, "binding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->k1:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->b1:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$a;

    .line 31
    .line 32
    return-void
.end method

.method public constructor <init>()V
    .locals 7

    .line 1
    sget v0, LT40/c;->fragment_popular_one_x_games:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    const/4 v0, 0x1

    .line 7
    iput-boolean v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->i0:Z

    .line 8
    .line 9
    new-instance v0, Lorg/xbet/games_section/feature/popular/presentation/d;

    .line 10
    .line 11
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/popular/presentation/d;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;)V

    .line 12
    .line 13
    .line 14
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$special$$inlined$viewModels$default$1;

    .line 15
    .line 16
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 17
    .line 18
    .line 19
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 20
    .line 21
    new-instance v3, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$special$$inlined$viewModels$default$2;

    .line 22
    .line 23
    invoke-direct {v3, v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    const-class v3, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 31
    .line 32
    invoke-static {v3}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 33
    .line 34
    .line 35
    move-result-object v3

    .line 36
    new-instance v4, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$special$$inlined$viewModels$default$3;

    .line 37
    .line 38
    invoke-direct {v4, v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 39
    .line 40
    .line 41
    new-instance v5, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$special$$inlined$viewModels$default$4;

    .line 42
    .line 43
    const/4 v6, 0x0

    .line 44
    invoke-direct {v5, v6, v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 45
    .line 46
    .line 47
    invoke-static {p0, v3, v4, v5, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    iput-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->l0:Lkotlin/j;

    .line 52
    .line 53
    sget-object v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$binding$2;->INSTANCE:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$binding$2;

    .line 54
    .line 55
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    iput-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->m0:LRc/c;

    .line 60
    .line 61
    new-instance v0, LUX0/k;

    .line 62
    .line 63
    invoke-direct {v0}, LUX0/k;-><init>()V

    .line 64
    .line 65
    .line 66
    iput-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->n0:LUX0/k;

    .line 67
    .line 68
    new-instance v0, Lorg/xbet/games_section/feature/popular/presentation/e;

    .line 69
    .line 70
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/popular/presentation/e;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;)V

    .line 71
    .line 72
    .line 73
    invoke-static {v2, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    iput-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->o0:Lkotlin/j;

    .line 78
    .line 79
    return-void
.end method

.method public static final synthetic A2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->O2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic B2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->P2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic C2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->Q2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic D2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->U2()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final E2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;)LX40/a;
    .locals 3

    .line 1
    new-instance v0, LX40/a;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->J2()Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->n0:LUX0/k;

    .line 8
    .line 9
    const-class v2, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;

    .line 10
    .line 11
    invoke-virtual {v2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-direct {v0, v1, p0, v2}, LX40/a;-><init>(LZ40/a;LUX0/k;Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    return-object v0
.end method

.method private final N2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->G2()LU40/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LU40/a;->b:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->G2()LU40/a;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iget-object v0, v0, LU40/a;->d:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 17
    .line 18
    const/4 v1, 0x0

    .line 19
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final synthetic O2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->K2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic P2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->L2(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic Q2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->M2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final T2(Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->G2()LU40/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LU40/a;->d:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->G2()LU40/a;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iget-object v0, v0, LU40/a;->b:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 17
    .line 18
    sget v1, Lpb/k;->update_again_after:I

    .line 19
    .line 20
    const-wide/16 v2, 0x2710

    .line 21
    .line 22
    invoke-virtual {v0, p1, v1, v2, v3}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->g(Lorg/xbet/uikit/components/lottie_empty/n;IJ)V

    .line 23
    .line 24
    .line 25
    const/4 p1, 0x0

    .line 26
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method private final U2()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->I2()LzX0/k;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Ly01/g;

    .line 6
    .line 7
    sget-object v2, Ly01/i$c;->a:Ly01/i$c;

    .line 8
    .line 9
    sget v3, Lpb/k;->get_balance_list_error:I

    .line 10
    .line 11
    invoke-virtual {p0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    const/16 v8, 0x3c

    .line 16
    .line 17
    const/4 v9, 0x0

    .line 18
    const/4 v4, 0x0

    .line 19
    const/4 v5, 0x0

    .line 20
    const/4 v6, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 23
    .line 24
    .line 25
    const/16 v10, 0x1fc

    .line 26
    .line 27
    const/4 v11, 0x0

    .line 28
    const/4 v3, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v8, 0x0

    .line 32
    move-object v2, p0

    .line 33
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method private final V2(Z)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->G2()LU40/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LU40/a;->c:LPW0/Y;

    .line 6
    .line 7
    invoke-virtual {v0}, LPW0/Y;->b()Landroid/widget/ProgressBar;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    if-eqz p1, :cond_0

    .line 12
    .line 13
    const/4 p1, 0x0

    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/16 p1, 0x8

    .line 16
    .line 17
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public static final W2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;)Landroidx/lifecycle/e0$c;
    .locals 7

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/g;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->H2()LV40/c$b;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    const/16 v5, 0x8

    .line 12
    .line 13
    const/4 v6, 0x0

    .line 14
    const/4 v4, 0x0

    .line 15
    move-object v3, p0

    .line 16
    invoke-direct/range {v0 .. v6}, Lorg/xbet/ui_common/viewmodel/core/g;-><init>(Lorg/xbet/ui_common/viewmodel/core/d;LwX0/c;Landroidx/savedstate/f;Landroid/os/Bundle;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 17
    .line 18
    .line 19
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->W2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;)LX40/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->E2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;)LX40/a;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final F2()LX40/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->o0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LX40/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public final G2()LU40/a;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->m0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->k1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LU40/a;

    .line 13
    .line 14
    return-object v0
.end method

.method public final H2()LV40/c$b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->j0:LV40/c$b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final I2()LzX0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->k0:LzX0/k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final J2()Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->l0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final K2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a;)V
    .locals 1

    .line 1
    instance-of v0, p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a$b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a$b;

    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a$b;->a()Landroid/content/Intent;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-virtual {p0, p1}, Landroidx/fragment/app/Fragment;->startActivity(Landroid/content/Intent;)V

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    instance-of v0, p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a$a;

    .line 16
    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    check-cast p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a$a;

    .line 20
    .line 21
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a$a;->a()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->R2(Ljava/lang/String;)V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :cond_1
    instance-of p1, p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a$c;

    .line 30
    .line 31
    if-eqz p1, :cond_2

    .line 32
    .line 33
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->S2()V

    .line 34
    .line 35
    .line 36
    return-void

    .line 37
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 38
    .line 39
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 40
    .line 41
    .line 42
    throw p1
.end method

.method public final L2(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->F2()LX40/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final M2(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d;)V
    .locals 1

    .line 1
    instance-of v0, p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$a;

    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$a;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->T2(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    instance-of v0, p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$c;

    .line 16
    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    check-cast p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$c;

    .line 20
    .line 21
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$c;->a()Z

    .line 22
    .line 23
    .line 24
    move-result p1

    .line 25
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->V2(Z)V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :cond_1
    instance-of v0, p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$d;

    .line 30
    .line 31
    if-nez v0, :cond_3

    .line 32
    .line 33
    sget-object v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$b;->a:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$b;

    .line 34
    .line 35
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 36
    .line 37
    .line 38
    move-result p1

    .line 39
    if-eqz p1, :cond_2

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 43
    .line 44
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 45
    .line 46
    .line 47
    throw p1

    .line 48
    :cond_3
    :goto_0
    invoke-direct {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->N2()V

    .line 49
    .line 50
    .line 51
    return-void
.end method

.method public final R2(Ljava/lang/String;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0, p1}, Lorg/xbet/ui_common/utils/h;->l(Landroid/content/Context;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final S2()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->I2()LzX0/k;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Ly01/g;

    .line 6
    .line 7
    sget-object v2, Ly01/i$c;->a:Ly01/i$c;

    .line 8
    .line 9
    sget v3, Lpb/k;->access_denied_with_bonus_currency_message:I

    .line 10
    .line 11
    invoke-virtual {p0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    const/16 v8, 0x3c

    .line 16
    .line 17
    const/4 v9, 0x0

    .line 18
    const/4 v4, 0x0

    .line 19
    const/4 v5, 0x0

    .line 20
    const/4 v6, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 23
    .line 24
    .line 25
    const/16 v10, 0x1fc

    .line 26
    .line 27
    const/4 v11, 0x0

    .line 28
    const/4 v3, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v8, 0x0

    .line 32
    move-object v2, p0

    .line 33
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public Y1()Z
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->G2()LU40/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LU40/a;->d:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 6
    .line 7
    invoke-static {v0}, LUX0/o;->d(Landroidx/recyclerview/widget/RecyclerView;)Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    return v0
.end method

.method public n2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->G2()LU40/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LU40/a;->d:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-static {v0, v1}, LUX0/o;->e(Landroidx/recyclerview/widget/RecyclerView;I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onDestroyView()V
    .locals 2

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroyView()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->G2()LU40/a;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, LU40/a;->d:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 9
    .line 10
    const/4 v1, 0x0

    .line 11
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public onPause()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onPause()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->J2()Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->R4()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onResume()V
    .locals 1

    .line 1
    invoke-super {p0}, LXW0/a;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->J2()Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->Q4()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public r2()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->i0:Z

    .line 2
    .line 3
    return v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 2

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->G2()LU40/a;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object p1, p1, LU40/a;->d:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 9
    .line 10
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onInitView$1;

    .line 15
    .line 16
    invoke-direct {v1, p0, v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onInitView$1;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;Landroid/content/Context;)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p1, v1}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->G2()LU40/a;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    iget-object p1, p1, LU40/a;->d:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 27
    .line 28
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->F2()LX40/a;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-static {}, LV40/a;->a()LV40/c$a;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v1}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    instance-of v2, v1, LQW0/f;

    .line 17
    .line 18
    const-string v3, "Can not find dependencies provider for "

    .line 19
    .line 20
    if-eqz v2, :cond_2

    .line 21
    .line 22
    check-cast v1, LQW0/f;

    .line 23
    .line 24
    invoke-interface {v1}, LQW0/f;->a()Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v2

    .line 28
    instance-of v2, v2, LSv/i;

    .line 29
    .line 30
    if-eqz v2, :cond_1

    .line 31
    .line 32
    invoke-interface {v1}, LQW0/f;->a()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    if-eqz v1, :cond_0

    .line 37
    .line 38
    check-cast v1, LSv/i;

    .line 39
    .line 40
    invoke-interface {v0, v1}, LV40/c$a;->a(LSv/i;)LV40/c;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    invoke-interface {v0, p0}, LV40/c;->a(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;)V

    .line 45
    .line 46
    .line 47
    return-void

    .line 48
    :cond_0
    new-instance v0, Ljava/lang/NullPointerException;

    .line 49
    .line 50
    const-string v1, "null cannot be cast to non-null type org.xbet.core.di.dependencies.PopularOneXGamesDependencies"

    .line 51
    .line 52
    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 53
    .line 54
    .line 55
    throw v0

    .line 56
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 57
    .line 58
    new-instance v1, Ljava/lang/StringBuilder;

    .line 59
    .line 60
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 61
    .line 62
    .line 63
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 64
    .line 65
    .line 66
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 67
    .line 68
    .line 69
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 74
    .line 75
    .line 76
    throw v0

    .line 77
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 78
    .line 79
    new-instance v1, Ljava/lang/StringBuilder;

    .line 80
    .line 81
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 82
    .line 83
    .line 84
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 85
    .line 86
    .line 87
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 88
    .line 89
    .line 90
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 91
    .line 92
    .line 93
    move-result-object v1

    .line 94
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 95
    .line 96
    .line 97
    throw v0
.end method

.method public v2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-super {v0}, LXW0/a;->v2()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->J2()Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->K4()Lkotlinx/coroutines/flow/e;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    new-instance v6, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$1;

    .line 15
    .line 16
    invoke-direct {v6, v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$1;-><init>(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 20
    .line 21
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 22
    .line 23
    .line 24
    move-result-object v4

    .line 25
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 26
    .line 27
    .line 28
    move-result-object v11

    .line 29
    new-instance v2, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 30
    .line 31
    const/4 v7, 0x0

    .line 32
    move-object v5, v10

    .line 33
    invoke-direct/range {v2 .. v7}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 34
    .line 35
    .line 36
    const/4 v15, 0x3

    .line 37
    const/16 v16, 0x0

    .line 38
    .line 39
    const/4 v12, 0x0

    .line 40
    const/4 v13, 0x0

    .line 41
    move-object v14, v2

    .line 42
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 43
    .line 44
    .line 45
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->J2()Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    invoke-virtual {v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->B4()Lkotlinx/coroutines/flow/f0;

    .line 50
    .line 51
    .line 52
    move-result-object v8

    .line 53
    new-instance v11, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$2;

    .line 54
    .line 55
    invoke-direct {v11, v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$2;-><init>(Ljava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 59
    .line 60
    .line 61
    move-result-object v9

    .line 62
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    new-instance v4, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 67
    .line 68
    move-object v7, v4

    .line 69
    invoke-direct/range {v7 .. v12}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 70
    .line 71
    .line 72
    const/4 v5, 0x3

    .line 73
    const/4 v6, 0x0

    .line 74
    const/4 v2, 0x0

    .line 75
    const/4 v3, 0x0

    .line 76
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 77
    .line 78
    .line 79
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->J2()Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    invoke-virtual {v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->J4()Lkotlinx/coroutines/flow/Z;

    .line 84
    .line 85
    .line 86
    move-result-object v8

    .line 87
    new-instance v11, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$3;

    .line 88
    .line 89
    const/4 v1, 0x0

    .line 90
    invoke-direct {v11, v0, v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$3;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;Lkotlin/coroutines/e;)V

    .line 91
    .line 92
    .line 93
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 94
    .line 95
    .line 96
    move-result-object v9

    .line 97
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 98
    .line 99
    .line 100
    move-result-object v1

    .line 101
    new-instance v4, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 102
    .line 103
    move-object v7, v4

    .line 104
    invoke-direct/range {v7 .. v12}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 105
    .line 106
    .line 107
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 108
    .line 109
    .line 110
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment;->J2()Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 111
    .line 112
    .line 113
    move-result-object v1

    .line 114
    invoke-virtual {v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->v4()Lkotlinx/coroutines/flow/Z;

    .line 115
    .line 116
    .line 117
    move-result-object v8

    .line 118
    new-instance v11, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$4;

    .line 119
    .line 120
    invoke-direct {v11, v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$4;-><init>(Ljava/lang/Object;)V

    .line 121
    .line 122
    .line 123
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 124
    .line 125
    .line 126
    move-result-object v9

    .line 127
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 128
    .line 129
    .line 130
    move-result-object v1

    .line 131
    new-instance v4, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$4;

    .line 132
    .line 133
    move-object v7, v4

    .line 134
    invoke-direct/range {v7 .. v12}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$4;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 135
    .line 136
    .line 137
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 138
    .line 139
    .line 140
    return-void
.end method
