.class public final Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;
.super Landroid/view/View;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;,
        Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$b;,
        Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;,
        Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$c;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u009c\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\u0008\u0015\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0013\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008!\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0001\u0018\u0000 \u008f\u00012\u00020\u0001:\u0004KG\u0090\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0011\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\'\u0010\u0016\u001a\u00020\u00122\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0015\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\'\u0010\u0019\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0015\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u0018H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\'\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u001b\u001a\u00020\n2\u0006\u0010\u0015\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\'\u0010#\u001a\u00020!2\u0006\u0010 \u001a\u00020\u001f2\u0006\u0010\"\u001a\u00020!2\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008#\u0010$J?\u0010\'\u001a\u00020\u00122\u0006\u0010 \u001a\u00020\u001f2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\"\u001a\u00020!2\u0006\u0010%\u001a\u00020!2\u0006\u0010&\u001a\u00020!2\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\'\u0010(J\u0017\u0010)\u001a\u00020!2\u0006\u0010\"\u001a\u00020!H\u0002\u00a2\u0006\u0004\u0008)\u0010*J\u0017\u0010+\u001a\u00020\u00122\u0006\u0010\u0011\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008+\u0010\u0014J?\u0010/\u001a\u00020!2\u0006\u0010 \u001a\u00020\u001f2\u0006\u0010,\u001a\u00020\n2\u0006\u0010-\u001a\u00020\n2\u0006\u0010\"\u001a\u00020!2\u0006\u0010.\u001a\u00020!2\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008/\u00100J\u001f\u00103\u001a\u00020\u00122\u0006\u00101\u001a\u00020\u00062\u0006\u00102\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u00083\u00104J\u0017\u00105\u001a\u00020\u00122\u0006\u0010 \u001a\u00020\u001fH\u0014\u00a2\u0006\u0004\u00085\u00106J\u0015\u00109\u001a\u00020\u00122\u0006\u00108\u001a\u000207\u00a2\u0006\u0004\u00089\u0010:J)\u0010?\u001a\u00020\u00122\u000c\u0010<\u001a\u0008\u0012\u0004\u0012\u00020\u001c0;2\u000c\u0010>\u001a\u0008\u0012\u0004\u0012\u00020\u00120=\u00a2\u0006\u0004\u0008?\u0010@J\r\u0010A\u001a\u00020\u0012\u00a2\u0006\u0004\u0008A\u0010BJ\u001b\u0010D\u001a\u00020\u00122\u000c\u0010C\u001a\u0008\u0012\u0004\u0012\u00020\u00120=\u00a2\u0006\u0004\u0008D\u0010ER\u0014\u0010I\u001a\u00020F8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010HR\u001b\u0010O\u001a\u00020J8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008K\u0010L\u001a\u0004\u0008M\u0010NR\u0014\u0010R\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010QR\u0014\u0010U\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008S\u0010TR\u0014\u0010W\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008V\u0010TR\u0014\u0010Y\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008X\u0010TR\u0014\u0010[\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Z\u0010TR\u0014\u0010]\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\\\u0010TR\u0014\u0010a\u001a\u00020^8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008_\u0010`R\u0014\u0010e\u001a\u00020b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008c\u0010dR\u0014\u0010g\u001a\u00020b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008f\u0010dR\u0014\u0010k\u001a\u00020h8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008i\u0010jR\u0016\u0010n\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008l\u0010mR\u0016\u0010p\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008o\u0010mR\u0016\u0010q\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008)\u0010mR\u0016\u0010r\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008A\u0010mR\u0016\u0010t\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008s\u0010mR\u0016\u0010u\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008#\u0010mR\u0016\u0010v\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008/\u0010mR\u0016\u0010w\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\'\u0010mR\u0016\u0010x\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010mR\u0016\u0010y\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010mR\u0016\u0010z\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010mR\u0016\u0010|\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008{\u0010mR\u0016\u0010~\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008}\u0010mR\u0016\u0010\u007f\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010mR\u0017\u0010\u0080\u0001\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008?\u0010mR\u0017\u0010\u0081\u0001\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010mR\u0019\u0010\u0084\u0001\u001a\u00020\u00108\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0082\u0001\u0010\u0083\u0001R\u0018\u0010\u0086\u0001\u001a\u00020!8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008+\u0010\u0085\u0001R\u0019\u0010\u0089\u0001\u001a\u00020\u001c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0087\u0001\u0010\u0088\u0001R\u001c\u0010\u008c\u0001\u001a\u0005\u0018\u00010\u008a\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0085\u0001\u0010\u008b\u0001R\u0019\u0010\u008e\u0001\u001a\u00020!8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u008d\u0001\u0010\u0085\u0001\u00a8\u0006\u0091\u0001"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;",
        "Landroid/view/View;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "unit",
        "Landroid/graphics/Paint;",
        "paint",
        "v",
        "(Ljava/lang/String;Landroid/graphics/Paint;)I",
        "",
        "millisUntilFinished",
        "",
        "w",
        "(J)V",
        "parentWidth",
        "z",
        "(Landroid/graphics/Paint;Ljava/lang/String;I)V",
        "Landroid/text/TextPaint;",
        "u",
        "(Ljava/lang/String;ILandroid/text/TextPaint;)Ljava/lang/String;",
        "text",
        "",
        "B",
        "(Landroid/graphics/Paint;Ljava/lang/String;I)Z",
        "Landroid/graphics/Canvas;",
        "canvas",
        "",
        "startX",
        "r",
        "(Landroid/graphics/Canvas;FLandroid/graphics/Paint;)F",
        "endX",
        "baseline",
        "t",
        "(Landroid/graphics/Canvas;Ljava/lang/String;FFFLandroid/graphics/Paint;)V",
        "o",
        "(F)F",
        "D",
        "original",
        "modified",
        "verticalProgress",
        "s",
        "(Landroid/graphics/Canvas;Ljava/lang/String;Ljava/lang/String;FFLandroid/graphics/Paint;)F",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "Le31/c;",
        "model",
        "setModel",
        "(Le31/c;)V",
        "Lkotlinx/coroutines/flow/e;",
        "stopTimerFlow",
        "Lkotlin/Function0;",
        "timeOutCallback",
        "A",
        "(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;)V",
        "p",
        "()V",
        "callback",
        "setOnTimerExpiredListener",
        "(Lkotlin/jvm/functions/Function0;)V",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;",
        "a",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;",
        "viewStyle",
        "Lkotlinx/coroutines/N;",
        "b",
        "Lkotlin/j;",
        "getScope",
        "()Lkotlinx/coroutines/N;",
        "scope",
        "c",
        "Landroid/graphics/Paint;",
        "separatorPaint",
        "d",
        "Landroid/text/TextPaint;",
        "timeTextPaint",
        "e",
        "daysTimeUnitPaint",
        "f",
        "hoursTimeUnitPaint",
        "g",
        "minutesTimeUnitPaint",
        "h",
        "secondsTimeUnitPaint",
        "Lorg/xbet/uikit/utils/timer/FlowTimer;",
        "i",
        "Lorg/xbet/uikit/utils/timer/FlowTimer;",
        "timer",
        "Landroid/graphics/Rect;",
        "j",
        "Landroid/graphics/Rect;",
        "timeUnitBounds",
        "k",
        "timeCharacterBounds",
        "Landroid/graphics/RectF;",
        "l",
        "Landroid/graphics/RectF;",
        "timeCellBackgroundBound",
        "m",
        "Ljava/lang/String;",
        "daysTitle",
        "n",
        "hoursTitle",
        "minutesTitle",
        "secondsTitle",
        "q",
        "visibleDaysTitle",
        "visibleHoursTitle",
        "visibleMinutesTitle",
        "visibleSecondsTitle",
        "currentDaysLeft",
        "currentHoursLeft",
        "currentMinutesLeft",
        "x",
        "currentSecondsLeft",
        "y",
        "targetDaysLeft",
        "targetHoursLeft",
        "targetMinutesLeft",
        "targetSecondsLeft",
        "C",
        "J",
        "timeExpiredMillis",
        "F",
        "timeTextBaseline",
        "E",
        "Z",
        "sizeChangeNeed",
        "Landroid/animation/ValueAnimator;",
        "Landroid/animation/ValueAnimator;",
        "currentAnimation",
        "G",
        "animationProgress",
        "H",
        "TimeSize",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final H:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final I:I


# instance fields
.field public A:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public B:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public C:J

.field public D:F

.field public E:Z

.field public F:Landroid/animation/ValueAnimator;

.field public G:F

.field public final a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lorg/xbet/uikit/utils/timer/FlowTimer;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Landroid/graphics/Rect;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Landroid/graphics/Rect;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Landroid/graphics/RectF;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public m:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public n:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public o:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public p:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public q:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public r:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public s:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public t:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public u:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public v:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public w:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public x:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public y:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public z:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->H:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$b;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->I:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 8
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/view/View;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance p2, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    invoke-direct {p2, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;-><init>(Landroid/content/Context;)V

    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 6
    new-instance p1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/k;

    invoke-direct {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/k;-><init>()V

    .line 7
    sget-object p3, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    invoke-static {p3, p1}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    .line 8
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->b:Lkotlin/j;

    .line 9
    sget v1, LlZ0/d;->uikitSeparator60:I

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lf31/a;->b(Landroid/view/View;ILandroid/graphics/Paint$Style;IILjava/lang/Object;)Landroid/graphics/Paint;

    move-result-object p1

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->c:Landroid/graphics/Paint;

    .line 10
    sget v1, LlZ0/n;->TextStyle_Title_Medium_XL_TextPrimary:I

    sget v2, LlZ0/d;->uikitTextPrimary:I

    const/4 v4, 0x4

    const/4 v3, 0x0

    invoke-static/range {v0 .. v5}, Lf31/a;->f(Landroid/view/View;IILandroid/graphics/Paint$Style;ILjava/lang/Object;)Landroid/text/TextPaint;

    move-result-object p1

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->d:Landroid/text/TextPaint;

    .line 11
    sget v1, LlZ0/n;->TextStyle_Caption_Bold_Caps_M_TextSecondary:I

    sget v2, LlZ0/d;->uikitSecondary:I

    invoke-static/range {v0 .. v5}, Lf31/a;->f(Landroid/view/View;IILandroid/graphics/Paint$Style;ILjava/lang/Object;)Landroid/text/TextPaint;

    move-result-object p1

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->e:Landroid/text/TextPaint;

    .line 12
    sget v1, LlZ0/n;->TextStyle_Caption_Bold_Caps_M_TextSecondary:I

    sget v2, LlZ0/d;->uikitSecondary:I

    invoke-static/range {v0 .. v5}, Lf31/a;->f(Landroid/view/View;IILandroid/graphics/Paint$Style;ILjava/lang/Object;)Landroid/text/TextPaint;

    move-result-object p1

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->f:Landroid/text/TextPaint;

    .line 13
    sget v1, LlZ0/n;->TextStyle_Caption_Bold_Caps_M_TextSecondary:I

    sget v2, LlZ0/d;->uikitSecondary:I

    invoke-static/range {v0 .. v5}, Lf31/a;->f(Landroid/view/View;IILandroid/graphics/Paint$Style;ILjava/lang/Object;)Landroid/text/TextPaint;

    move-result-object p1

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->g:Landroid/text/TextPaint;

    .line 14
    sget v1, LlZ0/n;->TextStyle_Caption_Bold_Caps_M_TextSecondary:I

    sget v2, LlZ0/d;->uikitSecondary:I

    invoke-static/range {v0 .. v5}, Lf31/a;->f(Landroid/view/View;IILandroid/graphics/Paint$Style;ILjava/lang/Object;)Landroid/text/TextPaint;

    move-result-object p1

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->h:Landroid/text/TextPaint;

    .line 15
    new-instance v1, Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 16
    new-instance v5, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/l;

    invoke-direct {v5, p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/l;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;)V

    const/4 v6, 0x1

    const/4 v7, 0x0

    const-wide/16 v2, 0x0

    const/4 v4, 0x0

    .line 17
    invoke-direct/range {v1 .. v7}, Lorg/xbet/uikit/utils/timer/FlowTimer;-><init>(JZLkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->i:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 18
    new-instance p1, Landroid/graphics/Rect;

    invoke-direct {p1}, Landroid/graphics/Rect;-><init>()V

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->j:Landroid/graphics/Rect;

    .line 19
    new-instance p1, Landroid/graphics/Rect;

    invoke-direct {p1}, Landroid/graphics/Rect;-><init>()V

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->k:Landroid/graphics/Rect;

    .line 20
    new-instance p1, Landroid/graphics/RectF;

    .line 21
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->e()I

    move-result p3

    int-to-float p3, p3

    .line 22
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->d()I

    move-result p2

    int-to-float p2, p2

    const/4 v1, 0x0

    .line 23
    invoke-direct {p1, v1, v1, p3, p2}, Landroid/graphics/RectF;-><init>(FFFF)V

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->l:Landroid/graphics/RectF;

    .line 24
    const-string p1, ""

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->m:Ljava/lang/String;

    .line 25
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->n:Ljava/lang/String;

    .line 26
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->o:Ljava/lang/String;

    .line 27
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->p:Ljava/lang/String;

    .line 28
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->q:Ljava/lang/String;

    .line 29
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->r:Ljava/lang/String;

    .line 30
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->s:Ljava/lang/String;

    .line 31
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->t:Ljava/lang/String;

    .line 32
    const-string p1, "00"

    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->u:Ljava/lang/String;

    .line 33
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->v:Ljava/lang/String;

    .line 34
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->w:Ljava/lang/String;

    .line 35
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->x:Ljava/lang/String;

    .line 36
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->y:Ljava/lang/String;

    .line 37
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->z:Ljava/lang/String;

    .line 38
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->A:Ljava/lang/String;

    .line 39
    iput-object p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->B:Ljava/lang/String;

    const/high16 p1, 0x3f800000    # 1.0f

    .line 40
    iput p1, v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->G:F

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static final C(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;J)Lkotlin/Unit;
    .locals 6

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->getScope()Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v3, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$timer$1$1;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-direct {v3, p0, p1, p2, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$timer$1$1;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;JLkotlin/coroutines/e;)V

    .line 9
    .line 10
    .line 11
    const/4 v4, 0x3

    .line 12
    const/4 v5, 0x0

    .line 13
    const/4 v2, 0x0

    .line 14
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 15
    .line 16
    .line 17
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 18
    .line 19
    return-object p0
.end method

.method public static synthetic a()Lkotlinx/coroutines/N;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->y()Lkotlinx/coroutines/N;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic b()Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->q()Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic c(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;J)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->C(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;J)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;Landroid/animation/ValueAnimator;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->x(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;Landroid/animation/ValueAnimator;)V

    return-void
.end method

.method public static final synthetic e(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->y:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic f(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->z:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic g(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->A:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method

.method private final getScope()Lkotlinx/coroutines/N;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->b:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lkotlinx/coroutines/N;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final synthetic h(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->B:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic i(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;)Lorg/xbet/uikit/utils/timer/FlowTimer;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->i:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic j(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;J)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->w(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic k(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->u:Ljava/lang/String;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic l(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->v:Ljava/lang/String;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic m(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->w:Ljava/lang/String;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic n(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->x:Ljava/lang/String;

    .line 2
    .line 3
    return-void
.end method

.method public static final q()Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final x(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;Landroid/animation/ValueAnimator;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->getAnimatedValue()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Ljava/lang/Float;

    .line 6
    .line 7
    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->G:F

    .line 12
    .line 13
    iget-boolean p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->E:Z

    .line 14
    .line 15
    if-eqz p1, :cond_0

    .line 16
    .line 17
    const/4 p1, 0x0

    .line 18
    iput-boolean p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->E:Z

    .line 19
    .line 20
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 24
    .line 25
    .line 26
    return-void

    .line 27
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public static final y()Lkotlinx/coroutines/N;
    .locals 1

    .line 1
    invoke-static {}, Lkotlinx/coroutines/b0;->c()Lkotlinx/coroutines/E0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lkotlinx/coroutines/E0;->getImmediate()Lkotlinx/coroutines/E0;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-static {v0}, Lkotlinx/coroutines/O;->a(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method


# virtual methods
.method public final A(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;)V
    .locals 5
    .param p1    # Lkotlinx/coroutines/flow/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->i:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/timer/FlowTimer;->o()V

    .line 4
    .line 5
    .line 6
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->C:J

    .line 7
    .line 8
    const-wide/16 v2, 0x3e8

    .line 9
    .line 10
    cmp-long v4, v0, v2

    .line 11
    .line 12
    if-lez v4, :cond_0

    .line 13
    .line 14
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->i:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 15
    .line 16
    invoke-virtual {v2, v0, v1}, Lorg/xbet/uikit/utils/timer/FlowTimer;->m(J)V

    .line 17
    .line 18
    .line 19
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->C:J

    .line 20
    .line 21
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->w(J)V

    .line 22
    .line 23
    .line 24
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->i:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 25
    .line 26
    invoke-virtual {v0, p2}, Lorg/xbet/uikit/utils/timer/FlowTimer;->l(Lkotlin/jvm/functions/Function0;)V

    .line 27
    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_0
    const-string p2, "00"

    .line 31
    .line 32
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->u:Ljava/lang/String;

    .line 33
    .line 34
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->v:Ljava/lang/String;

    .line 35
    .line 36
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->w:Ljava/lang/String;

    .line 37
    .line 38
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->x:Ljava/lang/String;

    .line 39
    .line 40
    :goto_0
    new-instance p2, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$startTimer$1;

    .line 41
    .line 42
    const/4 v0, 0x0

    .line 43
    invoke-direct {p2, p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$startTimer$1;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;Lkotlin/coroutines/e;)V

    .line 44
    .line 45
    .line 46
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->getScope()Lkotlinx/coroutines/N;

    .line 51
    .line 52
    .line 53
    move-result-object p2

    .line 54
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 55
    .line 56
    .line 57
    return-void
.end method

.method public final B(Landroid/graphics/Paint;Ljava/lang/String;I)Z
    .locals 0

    .line 1
    invoke-virtual {p1, p2}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    int-to-float p2, p3

    .line 6
    cmpg-float p1, p1, p2

    .line 7
    .line 8
    if-gtz p1, :cond_0

    .line 9
    .line 10
    const/4 p1, 0x1

    .line 11
    return p1

    .line 12
    :cond_0
    const/4 p1, 0x0

    .line 13
    return p1
.end method

.method public final D(J)V
    .locals 7

    .line 1
    iput-wide p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->C:J

    .line 2
    .line 3
    sget-object v0, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toDays(J)J

    .line 6
    .line 7
    .line 8
    move-result-wide v1

    .line 9
    const-wide/16 v3, 0x64

    .line 10
    .line 11
    const/4 v5, 0x2

    .line 12
    cmp-long v6, v1, v3

    .line 13
    .line 14
    if-ltz v6, :cond_0

    .line 15
    .line 16
    const/4 v3, 0x3

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 v3, 0x2

    .line 19
    :goto_0
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->u:Ljava/lang/String;

    .line 20
    .line 21
    const/16 v6, 0x30

    .line 22
    .line 23
    invoke-static {v4, v3, v6}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    iput-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->u:Ljava/lang/String;

    .line 28
    .line 29
    invoke-static {v1, v2}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    invoke-static {v1, v3, v6}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->y:Ljava/lang/String;

    .line 38
    .line 39
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toHours(J)J

    .line 40
    .line 41
    .line 42
    move-result-wide v1

    .line 43
    const/16 v3, 0x18

    .line 44
    .line 45
    int-to-long v3, v3

    .line 46
    rem-long/2addr v1, v3

    .line 47
    invoke-static {v1, v2}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    invoke-static {v1, v5, v6}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->z:Ljava/lang/String;

    .line 56
    .line 57
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toMinutes(J)J

    .line 58
    .line 59
    .line 60
    move-result-wide v1

    .line 61
    const/16 v3, 0x3c

    .line 62
    .line 63
    int-to-long v3, v3

    .line 64
    rem-long/2addr v1, v3

    .line 65
    invoke-static {v1, v2}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    invoke-static {v1, v5, v6}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->A:Ljava/lang/String;

    .line 74
    .line 75
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toSeconds(J)J

    .line 76
    .line 77
    .line 78
    move-result-wide p1

    .line 79
    rem-long/2addr p1, v3

    .line 80
    invoke-static {p1, p2}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    invoke-static {p1, v5, v6}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->B:Ljava/lang/String;

    .line 89
    .line 90
    return-void
.end method

.method public final o(F)F
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->f()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    int-to-float v0, v0

    .line 8
    add-float/2addr p1, v0

    .line 9
    return p1
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 14
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 2
    .line 3
    .line 4
    move-result v2

    .line 5
    int-to-float v2, v2

    .line 6
    const/high16 v3, 0x40000000    # 2.0f

    .line 7
    .line 8
    div-float/2addr v2, v3

    .line 9
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 10
    .line 11
    invoke-virtual {v4}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->a()I

    .line 12
    .line 13
    .line 14
    move-result v4

    .line 15
    int-to-float v4, v4

    .line 16
    div-float/2addr v4, v3

    .line 17
    sub-float/2addr v2, v4

    .line 18
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 19
    .line 20
    invoke-virtual {v3}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->j()I

    .line 21
    .line 22
    .line 23
    move-result v3

    .line 24
    int-to-float v3, v3

    .line 25
    add-float/2addr v3, v2

    .line 26
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    .line 27
    .line 28
    .line 29
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->l:Landroid/graphics/RectF;

    .line 30
    .line 31
    invoke-virtual {p1, v2}, Landroid/graphics/Canvas;->clipRect(Landroid/graphics/RectF;)Z

    .line 32
    .line 33
    .line 34
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->u:Ljava/lang/String;

    .line 35
    .line 36
    move v4, v3

    .line 37
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->y:Ljava/lang/String;

    .line 38
    .line 39
    iget v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->G:F

    .line 40
    .line 41
    iget-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->d:Landroid/text/TextPaint;

    .line 42
    .line 43
    move-object v0, p0

    .line 44
    move-object v1, p1

    .line 45
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->s(Landroid/graphics/Canvas;Ljava/lang/String;Ljava/lang/String;FFLandroid/graphics/Paint;)F

    .line 46
    .line 47
    .line 48
    move-result v7

    .line 49
    move v8, v4

    .line 50
    invoke-virtual {p0, v7}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->o(F)F

    .line 51
    .line 52
    .line 53
    move-result v3

    .line 54
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->v:Ljava/lang/String;

    .line 55
    .line 56
    move v4, v3

    .line 57
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->z:Ljava/lang/String;

    .line 58
    .line 59
    iget v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->G:F

    .line 60
    .line 61
    iget-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->d:Landroid/text/TextPaint;

    .line 62
    .line 63
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->s(Landroid/graphics/Canvas;Ljava/lang/String;Ljava/lang/String;FFLandroid/graphics/Paint;)F

    .line 64
    .line 65
    .line 66
    move-result v9

    .line 67
    move v10, v4

    .line 68
    invoke-virtual {p0, v9}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->o(F)F

    .line 69
    .line 70
    .line 71
    move-result v3

    .line 72
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->w:Ljava/lang/String;

    .line 73
    .line 74
    move v4, v3

    .line 75
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->A:Ljava/lang/String;

    .line 76
    .line 77
    iget v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->G:F

    .line 78
    .line 79
    iget-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->d:Landroid/text/TextPaint;

    .line 80
    .line 81
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->s(Landroid/graphics/Canvas;Ljava/lang/String;Ljava/lang/String;FFLandroid/graphics/Paint;)F

    .line 82
    .line 83
    .line 84
    move-result v11

    .line 85
    move v12, v4

    .line 86
    invoke-virtual {p0, v11}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->o(F)F

    .line 87
    .line 88
    .line 89
    move-result v3

    .line 90
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->x:Ljava/lang/String;

    .line 91
    .line 92
    move v4, v3

    .line 93
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->B:Ljava/lang/String;

    .line 94
    .line 95
    iget v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->G:F

    .line 96
    .line 97
    iget-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->d:Landroid/text/TextPaint;

    .line 98
    .line 99
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->s(Landroid/graphics/Canvas;Ljava/lang/String;Ljava/lang/String;FFLandroid/graphics/Paint;)F

    .line 100
    .line 101
    .line 102
    move v13, v4

    .line 103
    invoke-virtual {p1}, Landroid/graphics/Canvas;->restore()V

    .line 104
    .line 105
    .line 106
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->c:Landroid/graphics/Paint;

    .line 107
    .line 108
    invoke-virtual {p0, p1, v7, v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->r(Landroid/graphics/Canvas;FLandroid/graphics/Paint;)F

    .line 109
    .line 110
    .line 111
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->c:Landroid/graphics/Paint;

    .line 112
    .line 113
    invoke-virtual {p0, p1, v9, v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->r(Landroid/graphics/Canvas;FLandroid/graphics/Paint;)F

    .line 114
    .line 115
    .line 116
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->c:Landroid/graphics/Paint;

    .line 117
    .line 118
    invoke-virtual {p0, p1, v11, v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->r(Landroid/graphics/Canvas;FLandroid/graphics/Paint;)F

    .line 119
    .line 120
    .line 121
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 122
    .line 123
    invoke-virtual {v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->n()I

    .line 124
    .line 125
    .line 126
    move-result v2

    .line 127
    int-to-float v5, v2

    .line 128
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->d:Landroid/text/TextPaint;

    .line 129
    .line 130
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->u:Ljava/lang/String;

    .line 131
    .line 132
    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 133
    .line 134
    .line 135
    move-result v2

    .line 136
    add-float v4, v8, v2

    .line 137
    .line 138
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->d:Landroid/text/TextPaint;

    .line 139
    .line 140
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->v:Ljava/lang/String;

    .line 141
    .line 142
    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 143
    .line 144
    .line 145
    move-result v2

    .line 146
    add-float v7, v10, v2

    .line 147
    .line 148
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->d:Landroid/text/TextPaint;

    .line 149
    .line 150
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->w:Ljava/lang/String;

    .line 151
    .line 152
    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 153
    .line 154
    .line 155
    move-result v2

    .line 156
    add-float v9, v12, v2

    .line 157
    .line 158
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->d:Landroid/text/TextPaint;

    .line 159
    .line 160
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->x:Ljava/lang/String;

    .line 161
    .line 162
    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 163
    .line 164
    .line 165
    move-result v2

    .line 166
    add-float v11, v13, v2

    .line 167
    .line 168
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->q:Ljava/lang/String;

    .line 169
    .line 170
    iget-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->e:Landroid/text/TextPaint;

    .line 171
    .line 172
    move v3, v8

    .line 173
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->t(Landroid/graphics/Canvas;Ljava/lang/String;FFFLandroid/graphics/Paint;)V

    .line 174
    .line 175
    .line 176
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->r:Ljava/lang/String;

    .line 177
    .line 178
    iget-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->f:Landroid/text/TextPaint;

    .line 179
    .line 180
    move v4, v7

    .line 181
    move v3, v10

    .line 182
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->t(Landroid/graphics/Canvas;Ljava/lang/String;FFFLandroid/graphics/Paint;)V

    .line 183
    .line 184
    .line 185
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->s:Ljava/lang/String;

    .line 186
    .line 187
    iget-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->g:Landroid/text/TextPaint;

    .line 188
    .line 189
    move v4, v9

    .line 190
    move v3, v12

    .line 191
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->t(Landroid/graphics/Canvas;Ljava/lang/String;FFFLandroid/graphics/Paint;)V

    .line 192
    .line 193
    .line 194
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->t:Ljava/lang/String;

    .line 195
    .line 196
    iget-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->h:Landroid/text/TextPaint;

    .line 197
    .line 198
    move v4, v11

    .line 199
    move v3, v13

    .line 200
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->t(Landroid/graphics/Canvas;Ljava/lang/String;FFFLandroid/graphics/Paint;)V

    .line 201
    .line 202
    .line 203
    return-void
.end method

.method public onMeasure(II)V
    .locals 5

    .line 1
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 2
    .line 3
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->n()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->m:Ljava/lang/String;

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->e:Landroid/text/TextPaint;

    .line 10
    .line 11
    invoke-virtual {p0, p2, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->v(Ljava/lang/String;Landroid/graphics/Paint;)I

    .line 12
    .line 13
    .line 14
    move-result p2

    .line 15
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->n:Ljava/lang/String;

    .line 16
    .line 17
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->f:Landroid/text/TextPaint;

    .line 18
    .line 19
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->v(Ljava/lang/String;Landroid/graphics/Paint;)I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->o:Ljava/lang/String;

    .line 24
    .line 25
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->g:Landroid/text/TextPaint;

    .line 26
    .line 27
    invoke-virtual {p0, v1, v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->v(Ljava/lang/String;Landroid/graphics/Paint;)I

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->p:Ljava/lang/String;

    .line 32
    .line 33
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->h:Landroid/text/TextPaint;

    .line 34
    .line 35
    invoke-virtual {p0, v2, v3}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->v(Ljava/lang/String;Landroid/graphics/Paint;)I

    .line 36
    .line 37
    .line 38
    move-result v2

    .line 39
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->e:Landroid/text/TextPaint;

    .line 40
    .line 41
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->m:Ljava/lang/String;

    .line 42
    .line 43
    invoke-virtual {p0, v3, v4, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->z(Landroid/graphics/Paint;Ljava/lang/String;I)V

    .line 44
    .line 45
    .line 46
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->f:Landroid/text/TextPaint;

    .line 47
    .line 48
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->n:Ljava/lang/String;

    .line 49
    .line 50
    invoke-virtual {p0, v3, v4, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->z(Landroid/graphics/Paint;Ljava/lang/String;I)V

    .line 51
    .line 52
    .line 53
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->g:Landroid/text/TextPaint;

    .line 54
    .line 55
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->o:Ljava/lang/String;

    .line 56
    .line 57
    invoke-virtual {p0, v3, v4, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->z(Landroid/graphics/Paint;Ljava/lang/String;I)V

    .line 58
    .line 59
    .line 60
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->h:Landroid/text/TextPaint;

    .line 61
    .line 62
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->p:Ljava/lang/String;

    .line 63
    .line 64
    invoke-virtual {p0, v3, v4, v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->z(Landroid/graphics/Paint;Ljava/lang/String;I)V

    .line 65
    .line 66
    .line 67
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->m:Ljava/lang/String;

    .line 68
    .line 69
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->e:Landroid/text/TextPaint;

    .line 70
    .line 71
    invoke-virtual {p0, v3, p2, v4}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->u(Ljava/lang/String;ILandroid/text/TextPaint;)Ljava/lang/String;

    .line 72
    .line 73
    .line 74
    move-result-object p2

    .line 75
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->q:Ljava/lang/String;

    .line 76
    .line 77
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->n:Ljava/lang/String;

    .line 78
    .line 79
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->f:Landroid/text/TextPaint;

    .line 80
    .line 81
    invoke-virtual {p0, p2, v0, v3}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->u(Ljava/lang/String;ILandroid/text/TextPaint;)Ljava/lang/String;

    .line 82
    .line 83
    .line 84
    move-result-object p2

    .line 85
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->r:Ljava/lang/String;

    .line 86
    .line 87
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->o:Ljava/lang/String;

    .line 88
    .line 89
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->g:Landroid/text/TextPaint;

    .line 90
    .line 91
    invoke-virtual {p0, p2, v1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->u(Ljava/lang/String;ILandroid/text/TextPaint;)Ljava/lang/String;

    .line 92
    .line 93
    .line 94
    move-result-object p2

    .line 95
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->s:Ljava/lang/String;

    .line 96
    .line 97
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->p:Ljava/lang/String;

    .line 98
    .line 99
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->h:Landroid/text/TextPaint;

    .line 100
    .line 101
    invoke-virtual {p0, p2, v2, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->u(Ljava/lang/String;ILandroid/text/TextPaint;)Ljava/lang/String;

    .line 102
    .line 103
    .line 104
    move-result-object p2

    .line 105
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->t:Ljava/lang/String;

    .line 106
    .line 107
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 108
    .line 109
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->h()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;

    .line 110
    .line 111
    .line 112
    move-result-object v0

    .line 113
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;->SHORT:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;

    .line 114
    .line 115
    if-ne v0, v1, :cond_0

    .line 116
    .line 117
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 118
    .line 119
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->c()I

    .line 120
    .line 121
    .line 122
    move-result v0

    .line 123
    goto :goto_0

    .line 124
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 125
    .line 126
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->b()I

    .line 127
    .line 128
    .line 129
    move-result v0

    .line 130
    :goto_0
    invoke-virtual {p2, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->o(I)V

    .line 131
    .line 132
    .line 133
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->e:Landroid/text/TextPaint;

    .line 134
    .line 135
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->m:Ljava/lang/String;

    .line 136
    .line 137
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 138
    .line 139
    .line 140
    move-result v1

    .line 141
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->j:Landroid/graphics/Rect;

    .line 142
    .line 143
    const/4 v3, 0x0

    .line 144
    invoke-virtual {p2, v0, v3, v1, v2}, Landroid/graphics/Paint;->getTextBounds(Ljava/lang/String;IILandroid/graphics/Rect;)V

    .line 145
    .line 146
    .line 147
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->d:Landroid/text/TextPaint;

    .line 148
    .line 149
    const/4 v0, 0x2

    .line 150
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->k:Landroid/graphics/Rect;

    .line 151
    .line 152
    const-string v2, "00"

    .line 153
    .line 154
    invoke-virtual {p2, v2, v3, v0, v1}, Landroid/graphics/Paint;->getTextBounds(Ljava/lang/String;IILandroid/graphics/Rect;)V

    .line 155
    .line 156
    .line 157
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->k:Landroid/graphics/Rect;

    .line 158
    .line 159
    invoke-virtual {p2}, Landroid/graphics/Rect;->height()I

    .line 160
    .line 161
    .line 162
    move-result p2

    .line 163
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->l:Landroid/graphics/RectF;

    .line 164
    .line 165
    invoke-virtual {v0}, Landroid/graphics/RectF;->centerY()F

    .line 166
    .line 167
    .line 168
    move-result v0

    .line 169
    int-to-float p2, p2

    .line 170
    const/high16 v1, 0x40000000    # 2.0f

    .line 171
    .line 172
    div-float/2addr p2, v1

    .line 173
    add-float/2addr v0, p2

    .line 174
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->D:F

    .line 175
    .line 176
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->l:Landroid/graphics/RectF;

    .line 177
    .line 178
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 179
    .line 180
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->a()I

    .line 181
    .line 182
    .line 183
    move-result v0

    .line 184
    int-to-float v0, v0

    .line 185
    iput v0, p2, Landroid/graphics/RectF;->right:F

    .line 186
    .line 187
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 188
    .line 189
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->a()I

    .line 190
    .line 191
    .line 192
    move-result p2

    .line 193
    invoke-virtual {p0, p2, p1}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 194
    .line 195
    .line 196
    return-void
.end method

.method public final p()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->i:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/timer/FlowTimer;->o()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->i:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 7
    .line 8
    new-instance v1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/m;

    .line 9
    .line 10
    invoke-direct {v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/m;-><init>()V

    .line 11
    .line 12
    .line 13
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/utils/timer/FlowTimer;->l(Lkotlin/jvm/functions/Function0;)V

    .line 14
    .line 15
    .line 16
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->getScope()Lkotlinx/coroutines/N;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    invoke-interface {v0}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    const/4 v1, 0x0

    .line 25
    const/4 v2, 0x1

    .line 26
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/z0;->j(Lkotlin/coroutines/CoroutineContext;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->F:Landroid/animation/ValueAnimator;

    .line 30
    .line 31
    if-eqz v0, :cond_0

    .line 32
    .line 33
    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->cancel()V

    .line 34
    .line 35
    .line 36
    :cond_0
    return-void
.end method

.method public final r(Landroid/graphics/Canvas;FLandroid/graphics/Paint;)F
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->g()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    int-to-float v3, v0

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 13
    .line 14
    invoke-virtual {v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->g()I

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    sub-int/2addr v0, v1

    .line 19
    int-to-float v5, v0

    .line 20
    move v4, p2

    .line 21
    move-object v1, p1

    .line 22
    move v2, p2

    .line 23
    move-object v6, p3

    .line 24
    invoke-virtual/range {v1 .. v6}, Landroid/graphics/Canvas;->drawLine(FFFFLandroid/graphics/Paint;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 28
    .line 29
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->f()I

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    int-to-float p1, p1

    .line 34
    add-float p2, v2, p1

    .line 35
    .line 36
    return p2
.end method

.method public final s(Landroid/graphics/Canvas;Ljava/lang/String;Ljava/lang/String;FFLandroid/graphics/Paint;)F
    .locals 8

    .line 1
    invoke-virtual {p2}, Ljava/lang/String;->length()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-virtual {p3}, Ljava/lang/String;->length()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    const/4 v1, 0x0

    .line 14
    :goto_0
    if-ge v1, v0, :cond_8

    .line 15
    .line 16
    invoke-static {p2, v1}, Lkotlin/text/A;->T1(Ljava/lang/CharSequence;I)Ljava/lang/Character;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    const-string v3, ""

    .line 21
    .line 22
    if-eqz v2, :cond_0

    .line 23
    .line 24
    invoke-virtual {v2}, Ljava/lang/Character;->toString()Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object v2

    .line 28
    if-nez v2, :cond_1

    .line 29
    .line 30
    :cond_0
    move-object v2, v3

    .line 31
    :cond_1
    invoke-static {p3, v1}, Lkotlin/text/A;->T1(Ljava/lang/CharSequence;I)Ljava/lang/Character;

    .line 32
    .line 33
    .line 34
    move-result-object v4

    .line 35
    if-eqz v4, :cond_3

    .line 36
    .line 37
    invoke-virtual {v4}, Ljava/lang/Character;->toString()Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object v4

    .line 41
    if-nez v4, :cond_2

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_2
    move-object v3, v4

    .line 45
    :cond_3
    :goto_1
    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 46
    .line 47
    .line 48
    move-result v4

    .line 49
    iget v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->D:F

    .line 50
    .line 51
    if-nez v4, :cond_4

    .line 52
    .line 53
    const/4 v6, 0x1

    .line 54
    int-to-float v6, v6

    .line 55
    sub-float/2addr v6, p5

    .line 56
    mul-float v5, v5, v6

    .line 57
    .line 58
    :cond_4
    iget v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->D:F

    .line 59
    .line 60
    if-nez v4, :cond_5

    .line 61
    .line 62
    const/4 v7, 0x2

    .line 63
    int-to-float v7, v7

    .line 64
    sub-float/2addr v7, p5

    .line 65
    mul-float v6, v6, v7

    .line 66
    .line 67
    :cond_5
    invoke-interface {v2}, Ljava/lang/CharSequence;->length()I

    .line 68
    .line 69
    .line 70
    move-result v7

    .line 71
    if-lez v7, :cond_6

    .line 72
    .line 73
    if-nez v4, :cond_6

    .line 74
    .line 75
    invoke-virtual {p1, v2, p4, v5, p6}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    .line 76
    .line 77
    .line 78
    :cond_6
    invoke-interface {v3}, Ljava/lang/CharSequence;->length()I

    .line 79
    .line 80
    .line 81
    move-result v4

    .line 82
    if-lez v4, :cond_7

    .line 83
    .line 84
    invoke-virtual {p1, v3, p4, v6, p6}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    .line 85
    .line 86
    .line 87
    :cond_7
    invoke-virtual {p6, v2}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 88
    .line 89
    .line 90
    move-result v2

    .line 91
    invoke-virtual {p6, v3}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 92
    .line 93
    .line 94
    move-result v3

    .line 95
    invoke-static {v2, v3}, Ljava/lang/Math;->max(FF)F

    .line 96
    .line 97
    .line 98
    move-result v2

    .line 99
    add-float/2addr p4, v2

    .line 100
    add-int/lit8 v1, v1, 0x1

    .line 101
    .line 102
    goto :goto_0

    .line 103
    :cond_8
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 104
    .line 105
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->f()I

    .line 106
    .line 107
    .line 108
    move-result p1

    .line 109
    int-to-float p1, p1

    .line 110
    add-float/2addr p4, p1

    .line 111
    return p4
.end method

.method public final setModel(Le31/c;)V
    .locals 7
    .param p1    # Le31/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Le31/c;->b()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p1}, Le31/c;->b()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    invoke-static {v0, v1}, LF0/b;->getString(Landroid/content/Context;I)Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    sget-object v1, Ljava/util/Locale;->ROOT:Ljava/util/Locale;

    .line 20
    .line 21
    invoke-virtual {v0, v1}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->m:Ljava/lang/String;

    .line 26
    .line 27
    :cond_0
    invoke-virtual {p1}, Le31/c;->c()I

    .line 28
    .line 29
    .line 30
    move-result v0

    .line 31
    if-eqz v0, :cond_1

    .line 32
    .line 33
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-virtual {p1}, Le31/c;->c()I

    .line 38
    .line 39
    .line 40
    move-result v1

    .line 41
    invoke-static {v0, v1}, LF0/b;->getString(Landroid/content/Context;I)Ljava/lang/String;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    sget-object v1, Ljava/util/Locale;->ROOT:Ljava/util/Locale;

    .line 46
    .line 47
    invoke-virtual {v0, v1}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->n:Ljava/lang/String;

    .line 52
    .line 53
    :cond_1
    invoke-virtual {p1}, Le31/c;->d()I

    .line 54
    .line 55
    .line 56
    move-result v0

    .line 57
    if-eqz v0, :cond_2

    .line 58
    .line 59
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-virtual {p1}, Le31/c;->d()I

    .line 64
    .line 65
    .line 66
    move-result v1

    .line 67
    invoke-static {v0, v1}, LF0/b;->getString(Landroid/content/Context;I)Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    sget-object v1, Ljava/util/Locale;->ROOT:Ljava/util/Locale;

    .line 72
    .line 73
    invoke-virtual {v0, v1}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->o:Ljava/lang/String;

    .line 78
    .line 79
    :cond_2
    invoke-virtual {p1}, Le31/c;->f()I

    .line 80
    .line 81
    .line 82
    move-result v0

    .line 83
    if-eqz v0, :cond_3

    .line 84
    .line 85
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 86
    .line 87
    .line 88
    move-result-object v0

    .line 89
    invoke-virtual {p1}, Le31/c;->f()I

    .line 90
    .line 91
    .line 92
    move-result v1

    .line 93
    invoke-static {v0, v1}, LF0/b;->getString(Landroid/content/Context;I)Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    sget-object v1, Ljava/util/Locale;->ROOT:Ljava/util/Locale;

    .line 98
    .line 99
    invoke-virtual {v0, v1}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    .line 100
    .line 101
    .line 102
    move-result-object v0

    .line 103
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->p:Ljava/lang/String;

    .line 104
    .line 105
    :cond_3
    invoke-virtual {p1}, Le31/c;->e()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerMode;

    .line 106
    .line 107
    .line 108
    move-result-object v0

    .line 109
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$c;->a:[I

    .line 110
    .line 111
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 112
    .line 113
    .line 114
    move-result v0

    .line 115
    aget v0, v1, v0

    .line 116
    .line 117
    const/4 v1, 0x1

    .line 118
    const/4 v2, 0x2

    .line 119
    if-eq v0, v1, :cond_5

    .line 120
    .line 121
    if-ne v0, v2, :cond_4

    .line 122
    .line 123
    invoke-virtual {p1}, Le31/c;->a()Ljava/util/Date;

    .line 124
    .line 125
    .line 126
    move-result-object p1

    .line 127
    invoke-virtual {p1}, Ljava/util/Date;->getTime()J

    .line 128
    .line 129
    .line 130
    move-result-wide v0

    .line 131
    goto :goto_0

    .line 132
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 133
    .line 134
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 135
    .line 136
    .line 137
    throw p1

    .line 138
    :cond_5
    invoke-virtual {p1}, Le31/c;->a()Ljava/util/Date;

    .line 139
    .line 140
    .line 141
    move-result-object p1

    .line 142
    invoke-virtual {p1}, Ljava/util/Date;->getTime()J

    .line 143
    .line 144
    .line 145
    move-result-wide v0

    .line 146
    new-instance p1, Ljava/util/Date;

    .line 147
    .line 148
    invoke-direct {p1}, Ljava/util/Date;-><init>()V

    .line 149
    .line 150
    .line 151
    invoke-virtual {p1}, Ljava/util/Date;->getTime()J

    .line 152
    .line 153
    .line 154
    move-result-wide v3

    .line 155
    sub-long/2addr v0, v3

    .line 156
    :goto_0
    iput-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->C:J

    .line 157
    .line 158
    sget-object p1, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 159
    .line 160
    invoke-virtual {p1, v0, v1}, Ljava/util/concurrent/TimeUnit;->toDays(J)J

    .line 161
    .line 162
    .line 163
    move-result-wide v0

    .line 164
    const-wide/16 v3, 0x64

    .line 165
    .line 166
    cmp-long v5, v0, v3

    .line 167
    .line 168
    if-ltz v5, :cond_6

    .line 169
    .line 170
    const/4 v3, 0x3

    .line 171
    goto :goto_1

    .line 172
    :cond_6
    const/4 v3, 0x2

    .line 173
    :goto_1
    invoke-static {v0, v1}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 174
    .line 175
    .line 176
    move-result-object v0

    .line 177
    const/16 v1, 0x30

    .line 178
    .line 179
    invoke-static {v0, v3, v1}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 180
    .line 181
    .line 182
    move-result-object v0

    .line 183
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->u:Ljava/lang/String;

    .line 184
    .line 185
    iget-wide v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->C:J

    .line 186
    .line 187
    invoke-virtual {p1, v3, v4}, Ljava/util/concurrent/TimeUnit;->toHours(J)J

    .line 188
    .line 189
    .line 190
    move-result-wide v3

    .line 191
    const/16 v0, 0x18

    .line 192
    .line 193
    int-to-long v5, v0

    .line 194
    rem-long/2addr v3, v5

    .line 195
    invoke-static {v3, v4}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 196
    .line 197
    .line 198
    move-result-object v0

    .line 199
    invoke-static {v0, v2, v1}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 200
    .line 201
    .line 202
    move-result-object v0

    .line 203
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->v:Ljava/lang/String;

    .line 204
    .line 205
    iget-wide v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->C:J

    .line 206
    .line 207
    invoke-virtual {p1, v3, v4}, Ljava/util/concurrent/TimeUnit;->toMinutes(J)J

    .line 208
    .line 209
    .line 210
    move-result-wide v3

    .line 211
    const/16 v0, 0x3c

    .line 212
    .line 213
    int-to-long v5, v0

    .line 214
    rem-long/2addr v3, v5

    .line 215
    invoke-static {v3, v4}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 216
    .line 217
    .line 218
    move-result-object v0

    .line 219
    invoke-static {v0, v2, v1}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 220
    .line 221
    .line 222
    move-result-object v0

    .line 223
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->w:Ljava/lang/String;

    .line 224
    .line 225
    iget-wide v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->C:J

    .line 226
    .line 227
    invoke-virtual {p1, v3, v4}, Ljava/util/concurrent/TimeUnit;->toSeconds(J)J

    .line 228
    .line 229
    .line 230
    move-result-wide v3

    .line 231
    rem-long/2addr v3, v5

    .line 232
    invoke-static {v3, v4}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 233
    .line 234
    .line 235
    move-result-object p1

    .line 236
    invoke-static {p1, v2, v1}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 237
    .line 238
    .line 239
    move-result-object p1

    .line 240
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->x:Ljava/lang/String;

    .line 241
    .line 242
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 243
    .line 244
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->u:Ljava/lang/String;

    .line 245
    .line 246
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 247
    .line 248
    .line 249
    move-result v0

    .line 250
    if-gt v0, v2, :cond_7

    .line 251
    .line 252
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;->SHORT:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;

    .line 253
    .line 254
    goto :goto_2

    .line 255
    :cond_7
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;->LONG:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;

    .line 256
    .line 257
    :goto_2
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->p(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;)V

    .line 258
    .line 259
    .line 260
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 261
    .line 262
    .line 263
    return-void
.end method

.method public final setOnTimerExpiredListener(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->i:Lorg/xbet/uikit/utils/timer/FlowTimer;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/utils/timer/FlowTimer;->l(Lkotlin/jvm/functions/Function0;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final t(Landroid/graphics/Canvas;Ljava/lang/String;FFFLandroid/graphics/Paint;)V
    .locals 2

    .line 1
    invoke-virtual {p6, p2}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    sub-float/2addr p4, p3

    .line 6
    const/4 v1, 0x2

    .line 7
    int-to-float v1, v1

    .line 8
    div-float/2addr p4, v1

    .line 9
    add-float/2addr p4, p3

    .line 10
    div-float/2addr v0, v1

    .line 11
    sub-float/2addr p4, v0

    .line 12
    invoke-virtual {p6}, Landroid/graphics/Paint;->getFontMetrics()Landroid/graphics/Paint$FontMetrics;

    .line 13
    .line 14
    .line 15
    move-result-object p3

    .line 16
    iget p3, p3, Landroid/graphics/Paint$FontMetrics;->descent:F

    .line 17
    .line 18
    sub-float/2addr p5, p3

    .line 19
    invoke-virtual {p1, p2, p4, p5, p6}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public final u(Ljava/lang/String;ILandroid/text/TextPaint;)Ljava/lang/String;
    .locals 1

    .line 1
    invoke-virtual {p0, p3, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->B(Landroid/graphics/Paint;Ljava/lang/String;I)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    return-object p1

    .line 8
    :cond_0
    int-to-float p2, p2

    .line 9
    invoke-virtual {p3, p1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    sub-float/2addr p2, v0

    .line 14
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 15
    .line 16
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->i()F

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    invoke-static {p2, v0}, Ljava/lang/Math;->max(FF)F

    .line 21
    .line 22
    .line 23
    move-result p2

    .line 24
    sget-object v0, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    .line 25
    .line 26
    invoke-static {p1, p3, p2, v0}, Landroid/text/TextUtils;->ellipsize(Ljava/lang/CharSequence;Landroid/text/TextPaint;FLandroid/text/TextUtils$TruncateAt;)Ljava/lang/CharSequence;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    return-object p1
.end method

.method public final v(Ljava/lang/String;Landroid/graphics/Paint;)I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->l()F

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    float-to-int v0, v0

    .line 8
    invoke-virtual {p0, p2, p1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->B(Landroid/graphics/Paint;Ljava/lang/String;I)Z

    .line 9
    .line 10
    .line 11
    move-result p1

    .line 12
    if-eqz p1, :cond_0

    .line 13
    .line 14
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 15
    .line 16
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->l()F

    .line 17
    .line 18
    .line 19
    move-result p1

    .line 20
    goto :goto_0

    .line 21
    :cond_0
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 22
    .line 23
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->i()F

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    :goto_0
    float-to-int p1, p1

    .line 28
    return p1
.end method

.method public final w(J)V
    .locals 2

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->D(J)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 5
    .line 6
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->h()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->u:Ljava/lang/String;

    .line 13
    .line 14
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    const/4 v1, 0x2

    .line 19
    if-gt v0, v1, :cond_0

    .line 20
    .line 21
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;->SHORT:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;->LONG:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;

    .line 25
    .line 26
    :goto_0
    invoke-virtual {p2, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->p(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;)V

    .line 27
    .line 28
    .line 29
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 30
    .line 31
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->h()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$TimeSize;

    .line 32
    .line 33
    .line 34
    move-result-object p2

    .line 35
    if-ne p1, p2, :cond_1

    .line 36
    .line 37
    const/4 p1, 0x1

    .line 38
    goto :goto_1

    .line 39
    :cond_1
    const/4 p1, 0x0

    .line 40
    :goto_1
    iput-boolean p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->E:Z

    .line 41
    .line 42
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->F:Landroid/animation/ValueAnimator;

    .line 43
    .line 44
    if-eqz p1, :cond_2

    .line 45
    .line 46
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->cancel()V

    .line 47
    .line 48
    .line 49
    :cond_2
    new-array p1, v1, [F

    .line 50
    .line 51
    fill-array-data p1, :array_0

    .line 52
    .line 53
    .line 54
    invoke-static {p1}, Landroid/animation/ValueAnimator;->ofFloat([F)Landroid/animation/ValueAnimator;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    new-instance p2, Lk1/b;

    .line 59
    .line 60
    invoke-direct {p2}, Lk1/b;-><init>()V

    .line 61
    .line 62
    .line 63
    invoke-virtual {p1, p2}, Landroid/animation/ValueAnimator;->setInterpolator(Landroid/animation/TimeInterpolator;)V

    .line 64
    .line 65
    .line 66
    new-instance p2, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/n;

    .line 67
    .line 68
    invoke-direct {p2, p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/n;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;)V

    .line 69
    .line 70
    .line 71
    invoke-virtual {p1, p2}, Landroid/animation/ValueAnimator;->addUpdateListener(Landroid/animation/ValueAnimator$AnimatorUpdateListener;)V

    .line 72
    .line 73
    .line 74
    new-instance p2, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$d;

    .line 75
    .line 76
    invoke-direct {p2, p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$d;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {p1, p2}, Landroid/animation/Animator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 80
    .line 81
    .line 82
    invoke-virtual {p1}, Landroid/animation/ValueAnimator;->start()V

    .line 83
    .line 84
    .line 85
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->F:Landroid/animation/ValueAnimator;

    .line 86
    .line 87
    return-void

    .line 88
    nop

    .line 89
    :array_0
    .array-data 4
        0x0
        0x3f800000    # 1.0f
    .end array-data
.end method

.method public final z(Landroid/graphics/Paint;Ljava/lang/String;I)V
    .locals 3

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->B(Landroid/graphics/Paint;Ljava/lang/String;I)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    invoke-virtual {p1}, Landroid/graphics/Paint;->getTextSize()F

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 13
    .line 14
    invoke-virtual {v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->k()F

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    cmpg-float v0, v0, v1

    .line 19
    .line 20
    if-gtz v0, :cond_1

    .line 21
    .line 22
    :goto_0
    return-void

    .line 23
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 24
    .line 25
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->k()F

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    invoke-virtual {p1}, Landroid/graphics/Paint;->getTextSize()F

    .line 30
    .line 31
    .line 32
    move-result v1

    .line 33
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;

    .line 34
    .line 35
    invoke-virtual {v2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView$a;->m()F

    .line 36
    .line 37
    .line 38
    move-result v2

    .line 39
    sub-float/2addr v1, v2

    .line 40
    invoke-static {v0, v1}, Ljava/lang/Math;->max(FF)F

    .line 41
    .line 42
    .line 43
    move-result v0

    .line 44
    invoke-virtual {p1, v0}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentVerticalTimerView;->z(Landroid/graphics/Paint;Ljava/lang/String;I)V

    .line 48
    .line 49
    .line 50
    return-void
.end method
