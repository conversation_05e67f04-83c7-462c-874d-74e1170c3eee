.class public final Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupHeaderViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0003\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "LA4/c;",
        "",
        "LVX0/i;",
        "d",
        "()LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupHeaderViewHolderKt;->g(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/t1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupHeaderViewHolderKt;->e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/t1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupHeaderViewHolderKt;->f(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final d()LA4/c;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LJy0/d;

    .line 2
    .line 3
    invoke-direct {v0}, LJy0/d;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LJy0/e;

    .line 7
    .line 8
    invoke-direct {v1}, LJy0/e;-><init>()V

    .line 9
    .line 10
    .line 11
    new-instance v2, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupHeaderViewHolderKt$groupHeaderAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {v2}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupHeaderViewHolderKt$groupHeaderAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v3, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupHeaderViewHolderKt$groupHeaderAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupHeaderViewHolderKt$groupHeaderAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v4, LB4/b;

    .line 19
    .line 20
    invoke-direct {v4, v0, v2, v1, v3}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v4
.end method

.method public static final e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/t1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/t1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/t1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final f(LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, LJy0/f;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LJy0/f;-><init>(LB4/a;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, v0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 7
    .line 8
    .line 9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    return-object p0
.end method

.method public static final g(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LGq0/t1;

    .line 6
    .line 7
    iget-object p1, p1, LGq0/t1;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, LIy0/b;

    .line 14
    .line 15
    invoke-virtual {p0}, LIy0/b;->getTitle()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {p1, p0}, Lorg/xbet/uikit/components/header/DSHeader;->setLabel(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 23
    .line 24
    return-object p0
.end method
