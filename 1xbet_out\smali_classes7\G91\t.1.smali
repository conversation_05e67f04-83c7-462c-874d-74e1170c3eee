.class public final synthetic LG91/t;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:LF91/g;


# direct methods
.method public synthetic constructor <init>(LB4/a;LF91/g;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LG91/t;->a:LB4/a;

    iput-object p2, p0, LG91/t;->b:LF91/g;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LG91/t;->a:LB4/a;

    iget-object v1, p0, LG91/t;->b:LF91/g;

    check-cast p1, Ljava/util/List;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/category/presentation/filters/delegates/ProvidersTypeLargeHeaderAdapterDelegateKt;->e(LB4/a;LF91/g;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
