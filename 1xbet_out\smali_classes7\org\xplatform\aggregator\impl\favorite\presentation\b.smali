.class public final synthetic Lorg/xplatform/aggregator/impl/favorite/presentation/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/google/android/material/tabs/TabLayoutMediator$TabConfigurationStrategy;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/b;->a:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;

    return-void
.end method


# virtual methods
.method public final onConfigureTab(Lcom/google/android/material/tabs/TabLayout$Tab;I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/b;->a:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;

    invoke-static {v0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->l3(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;Lcom/google/android/material/tabs/TabLayout$Tab;I)V

    return-void
.end method
