.class public final Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000d\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u000c\u0008\u0000\u0018\u00002\u00020\u0001B1\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u001e\u0010\u0012\u001a\u0008\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u000f\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J$\u0010\u0017\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00110\u00100\u00162\u0006\u0010\u0015\u001a\u00020\u0014H\u0086@\u00a2\u0006\u0004\u0008\u0017\u0010\u0018Jo\u0010\u001f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00110\u00100\u00162\u0012\u0010\u001a\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00190\u00100\u00162\u0012\u0010\u001c\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u001b0\u00100\u00162*\u0010\u001e\u001a&\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00190\u0010\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u001b0\u0010\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00110\u00100\u001dH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 J\u000f\u0010\"\u001a\u00020!H\u0002\u00a2\u0006\u0004\u0008\"\u0010#R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010%R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010&R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010(R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008)\u0010*R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008+\u0010,\u00a8\u0006-"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;",
        "",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;",
        "aggregatorPromoInteractor",
        "LHX0/e;",
        "resourceManager",
        "Lgk/d;",
        "getCurrencySymbolByCodeUseCase",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "remoteConfigUseCase",
        "Leu/l;",
        "getGeoIpUseCase",
        "<init>",
        "(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;LHX0/e;Lgk/d;Lorg/xbet/remoteconfig/domain/usecases/i;Leu/l;)V",
        "Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
        "type",
        "",
        "LVX0/i;",
        "h",
        "(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "currentAccountId",
        "Lkotlin/Result;",
        "g",
        "(JLkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lxa1/a;",
        "b",
        "Lxa1/c;",
        "fs",
        "Lkotlin/Function2;",
        "transform",
        "f",
        "(Ljava/lang/Object;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;",
        "Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;",
        "i",
        "()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;",
        "a",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;",
        "LHX0/e;",
        "c",
        "Lgk/d;",
        "d",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "e",
        "Leu/l;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lgk/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Leu/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;LHX0/e;Lgk/d;Lorg/xbet/remoteconfig/domain/usecases/i;Leu/l;)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lgk/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Leu/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->a:Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->b:LHX0/e;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->c:Lgk/d;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->d:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->e:Leu/l;

    .line 13
    .line 14
    return-void
.end method

.method public static final synthetic a(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;Ljava/lang/Object;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->f(Ljava/lang/Object;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic b(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;)Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->a:Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic c(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;)Leu/l;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->e:Leu/l;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->b:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic e(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;)Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->i()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public final f(Ljava/lang/Object;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/util/List<",
            "Lxa1/a;",
            ">;-",
            "Ljava/util/List<",
            "Lxa1/c;",
            ">;+",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-static {p1}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_1

    .line 6
    .line 7
    invoke-static {p2}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-static {p1}, Lkotlin/Result;->exceptionOrNull-impl(Ljava/lang/Object;)Ljava/lang/Throwable;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    if-nez p1, :cond_0

    .line 18
    .line 19
    invoke-static {p2}, Lkotlin/Result;->exceptionOrNull-impl(Ljava/lang/Object;)Ljava/lang/Throwable;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    if-nez p1, :cond_0

    .line 24
    .line 25
    new-instance p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 26
    .line 27
    invoke-direct {p1}, Lcom/xbet/onexcore/data/model/ServerException;-><init>()V

    .line 28
    .line 29
    .line 30
    :cond_0
    invoke-static {p1}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    return-object p1

    .line 39
    :cond_1
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    invoke-static {p1}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 44
    .line 45
    .line 46
    move-result v1

    .line 47
    if-eqz v1, :cond_2

    .line 48
    .line 49
    move-object p1, v0

    .line 50
    :cond_2
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    invoke-static {p2}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 55
    .line 56
    .line 57
    move-result v1

    .line 58
    if-eqz v1, :cond_3

    .line 59
    .line 60
    move-object p2, v0

    .line 61
    :cond_3
    invoke-interface {p3, p1, p2}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    return-object p1
.end method

.method public final g(JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p3

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p3}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p3, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    new-instance p3, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;

    .line 54
    .line 55
    const/4 v2, 0x0

    .line 56
    invoke-direct {p3, p1, p2, p0, v2}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;-><init>(JLorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;Lkotlin/coroutines/e;)V

    .line 57
    .line 58
    .line 59
    iput v3, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$1;->label:I

    .line 60
    .line 61
    invoke-static {p3, v0}, Lkotlinx/coroutines/O;->f(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object p3

    .line 65
    if-ne p3, v1, :cond_3

    .line 66
    .line 67
    return-object v1

    .line 68
    :cond_3
    :goto_1
    check-cast p3, Lkotlin/Result;

    .line 69
    .line 70
    invoke-virtual {p3}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    return-object p1
.end method

.method public final h(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 19
    .param p1    # Lorg/xplatform/aggregator/api/navigation/GiftsChipType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p2

    .line 4
    .line 5
    instance-of v2, v1, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    move-object v2, v1

    .line 10
    check-cast v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;

    .line 11
    .line 12
    iget v3, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->label:I

    .line 13
    .line 14
    const/high16 v4, -0x80000000

    .line 15
    .line 16
    and-int v5, v3, v4

    .line 17
    .line 18
    if-eqz v5, :cond_0

    .line 19
    .line 20
    sub-int/2addr v3, v4

    .line 21
    iput v3, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->label:I

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;

    .line 25
    .line 26
    invoke-direct {v2, v0, v1}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    :goto_0
    iget-object v1, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->result:Ljava/lang/Object;

    .line 30
    .line 31
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    iget v4, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->label:I

    .line 36
    .line 37
    const/4 v5, 0x3

    .line 38
    const/4 v6, 0x2

    .line 39
    const/16 v7, 0xa

    .line 40
    .line 41
    packed-switch v4, :pswitch_data_0

    .line 42
    .line 43
    .line 44
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 45
    .line 46
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 47
    .line 48
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 49
    .line 50
    .line 51
    throw v1

    .line 52
    :pswitch_0
    iget-object v2, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$0:Ljava/lang/Object;

    .line 53
    .line 54
    check-cast v2, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 55
    .line 56
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 57
    .line 58
    .line 59
    goto/16 :goto_2

    .line 60
    .line 61
    :pswitch_1
    iget-wide v4, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->J$0:J

    .line 62
    .line 63
    iget-object v6, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$6:Ljava/lang/Object;

    .line 64
    .line 65
    check-cast v6, Ljava/util/Collection;

    .line 66
    .line 67
    iget-object v7, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$5:Ljava/lang/Object;

    .line 68
    .line 69
    check-cast v7, Lxa1/a;

    .line 70
    .line 71
    iget-object v8, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$4:Ljava/lang/Object;

    .line 72
    .line 73
    check-cast v8, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 74
    .line 75
    iget-object v9, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$3:Ljava/lang/Object;

    .line 76
    .line 77
    check-cast v9, LHX0/e;

    .line 78
    .line 79
    iget-object v10, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$2:Ljava/lang/Object;

    .line 80
    .line 81
    check-cast v10, Ljava/util/Iterator;

    .line 82
    .line 83
    iget-object v11, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$1:Ljava/lang/Object;

    .line 84
    .line 85
    check-cast v11, Ljava/util/Collection;

    .line 86
    .line 87
    iget-object v12, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$0:Ljava/lang/Object;

    .line 88
    .line 89
    check-cast v12, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 90
    .line 91
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 92
    .line 93
    .line 94
    move-object v13, v10

    .line 95
    move-object v10, v6

    .line 96
    move-object v6, v9

    .line 97
    move-wide/from16 v17, v4

    .line 98
    .line 99
    move-object v5, v8

    .line 100
    move-wide/from16 v8, v17

    .line 101
    .line 102
    :goto_1
    move-object v4, v7

    .line 103
    goto/16 :goto_6

    .line 104
    .line 105
    :pswitch_2
    iget-wide v4, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->J$0:J

    .line 106
    .line 107
    iget-object v6, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$0:Ljava/lang/Object;

    .line 108
    .line 109
    check-cast v6, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 110
    .line 111
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 112
    .line 113
    .line 114
    goto/16 :goto_4

    .line 115
    .line 116
    :pswitch_3
    iget-object v3, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$1:Ljava/lang/Object;

    .line 117
    .line 118
    check-cast v3, Ljava/util/List;

    .line 119
    .line 120
    iget-object v2, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$0:Ljava/lang/Object;

    .line 121
    .line 122
    check-cast v2, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 123
    .line 124
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 125
    .line 126
    .line 127
    goto/16 :goto_b

    .line 128
    .line 129
    :pswitch_4
    iget-wide v8, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->J$0:J

    .line 130
    .line 131
    iget-object v4, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$6:Ljava/lang/Object;

    .line 132
    .line 133
    check-cast v4, Ljava/util/Collection;

    .line 134
    .line 135
    iget-object v10, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$5:Ljava/lang/Object;

    .line 136
    .line 137
    check-cast v10, Lxa1/a;

    .line 138
    .line 139
    iget-object v11, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$4:Ljava/lang/Object;

    .line 140
    .line 141
    check-cast v11, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 142
    .line 143
    iget-object v12, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$3:Ljava/lang/Object;

    .line 144
    .line 145
    check-cast v12, LHX0/e;

    .line 146
    .line 147
    iget-object v13, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$2:Ljava/lang/Object;

    .line 148
    .line 149
    check-cast v13, Ljava/util/Iterator;

    .line 150
    .line 151
    iget-object v14, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$1:Ljava/lang/Object;

    .line 152
    .line 153
    check-cast v14, Ljava/util/Collection;

    .line 154
    .line 155
    iget-object v15, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$0:Ljava/lang/Object;

    .line 156
    .line 157
    check-cast v15, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 158
    .line 159
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 160
    .line 161
    .line 162
    move-object/from16 v16, v13

    .line 163
    .line 164
    move-wide/from16 v17, v8

    .line 165
    .line 166
    move-object v8, v10

    .line 167
    move-object v9, v11

    .line 168
    move-object v10, v12

    .line 169
    move-wide/from16 v12, v17

    .line 170
    .line 171
    goto/16 :goto_9

    .line 172
    .line 173
    :pswitch_5
    iget-wide v8, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->J$0:J

    .line 174
    .line 175
    iget-object v4, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$0:Ljava/lang/Object;

    .line 176
    .line 177
    check-cast v4, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 178
    .line 179
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 180
    .line 181
    .line 182
    goto/16 :goto_7

    .line 183
    .line 184
    :pswitch_6
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 185
    .line 186
    .line 187
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->i()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 188
    .line 189
    .line 190
    move-result-object v1

    .line 191
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 192
    .line 193
    .line 194
    move-result-wide v8

    .line 195
    sget-object v4, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$a;->a:[I

    .line 196
    .line 197
    invoke-virtual/range {p1 .. p1}, Ljava/lang/Enum;->ordinal()I

    .line 198
    .line 199
    .line 200
    move-result v10

    .line 201
    aget v4, v4, v10

    .line 202
    .line 203
    const/4 v10, 0x1

    .line 204
    if-eq v4, v10, :cond_8

    .line 205
    .line 206
    if-eq v4, v6, :cond_4

    .line 207
    .line 208
    if-ne v4, v5, :cond_3

    .line 209
    .line 210
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->a:Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 211
    .line 212
    iput-object v1, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$0:Ljava/lang/Object;

    .line 213
    .line 214
    const/4 v5, 0x6

    .line 215
    iput v5, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->label:I

    .line 216
    .line 217
    invoke-virtual {v4, v2}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->i(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 218
    .line 219
    .line 220
    move-result-object v2

    .line 221
    if-ne v2, v3, :cond_1

    .line 222
    .line 223
    goto/16 :goto_a

    .line 224
    .line 225
    :cond_1
    move-object/from16 v17, v2

    .line 226
    .line 227
    move-object v2, v1

    .line 228
    move-object/from16 v1, v17

    .line 229
    .line 230
    :goto_2
    check-cast v1, Ljava/lang/Iterable;

    .line 231
    .line 232
    new-instance v3, Ljava/util/ArrayList;

    .line 233
    .line 234
    invoke-static {v1, v7}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 235
    .line 236
    .line 237
    move-result v4

    .line 238
    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 239
    .line 240
    .line 241
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 242
    .line 243
    .line 244
    move-result-object v1

    .line 245
    :goto_3
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 246
    .line 247
    .line 248
    move-result v4

    .line 249
    if-eqz v4, :cond_2

    .line 250
    .line 251
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 252
    .line 253
    .line 254
    move-result-object v4

    .line 255
    check-cast v4, Lxa1/c;

    .line 256
    .line 257
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->b:LHX0/e;

    .line 258
    .line 259
    invoke-static {v4, v2, v5}, Lla1/b;->b(Lxa1/c;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;LHX0/e;)Lma1/e;

    .line 260
    .line 261
    .line 262
    move-result-object v4

    .line 263
    invoke-interface {v3, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 264
    .line 265
    .line 266
    goto :goto_3

    .line 267
    :cond_2
    return-object v3

    .line 268
    :cond_3
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    .line 269
    .line 270
    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 271
    .line 272
    .line 273
    throw v1

    .line 274
    :cond_4
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->a:Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 275
    .line 276
    iput-object v1, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$0:Ljava/lang/Object;

    .line 277
    .line 278
    iput-wide v8, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->J$0:J

    .line 279
    .line 280
    const/4 v5, 0x4

    .line 281
    iput v5, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->label:I

    .line 282
    .line 283
    invoke-virtual {v4, v2}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->h(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 284
    .line 285
    .line 286
    move-result-object v4

    .line 287
    if-ne v4, v3, :cond_5

    .line 288
    .line 289
    goto/16 :goto_a

    .line 290
    .line 291
    :cond_5
    move-object v6, v1

    .line 292
    move-object v1, v4

    .line 293
    move-wide v4, v8

    .line 294
    :goto_4
    check-cast v1, Ljava/lang/Iterable;

    .line 295
    .line 296
    new-instance v8, Ljava/util/ArrayList;

    .line 297
    .line 298
    invoke-static {v1, v7}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 299
    .line 300
    .line 301
    move-result v7

    .line 302
    invoke-direct {v8, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 303
    .line 304
    .line 305
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 306
    .line 307
    .line 308
    move-result-object v1

    .line 309
    move-object v10, v8

    .line 310
    move-object v8, v6

    .line 311
    move-object v6, v10

    .line 312
    move-object v10, v1

    .line 313
    :goto_5
    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    .line 314
    .line 315
    .line 316
    move-result v1

    .line 317
    if-eqz v1, :cond_7

    .line 318
    .line 319
    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 320
    .line 321
    .line 322
    move-result-object v1

    .line 323
    move-object v7, v1

    .line 324
    check-cast v7, Lxa1/a;

    .line 325
    .line 326
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->b:LHX0/e;

    .line 327
    .line 328
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->c:Lgk/d;

    .line 329
    .line 330
    invoke-virtual {v7}, Lxa1/a;->f()Ljava/lang/String;

    .line 331
    .line 332
    .line 333
    move-result-object v11

    .line 334
    iput-object v8, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$0:Ljava/lang/Object;

    .line 335
    .line 336
    iput-object v6, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$1:Ljava/lang/Object;

    .line 337
    .line 338
    iput-object v10, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$2:Ljava/lang/Object;

    .line 339
    .line 340
    iput-object v9, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$3:Ljava/lang/Object;

    .line 341
    .line 342
    iput-object v8, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$4:Ljava/lang/Object;

    .line 343
    .line 344
    iput-object v7, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$5:Ljava/lang/Object;

    .line 345
    .line 346
    iput-object v6, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$6:Ljava/lang/Object;

    .line 347
    .line 348
    iput-wide v4, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->J$0:J

    .line 349
    .line 350
    const/4 v12, 0x5

    .line 351
    iput v12, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->label:I

    .line 352
    .line 353
    invoke-interface {v1, v11, v2}, Lgk/d;->a(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 354
    .line 355
    .line 356
    move-result-object v1

    .line 357
    if-ne v1, v3, :cond_6

    .line 358
    .line 359
    goto/16 :goto_a

    .line 360
    .line 361
    :cond_6
    move-object v11, v6

    .line 362
    move-object v12, v8

    .line 363
    move-object v13, v10

    .line 364
    move-object v10, v11

    .line 365
    move-object v6, v9

    .line 366
    move-wide v8, v4

    .line 367
    move-object v5, v12

    .line 368
    goto/16 :goto_1

    .line 369
    .line 370
    :goto_6
    move-object v7, v1

    .line 371
    check-cast v7, Ljava/lang/String;

    .line 372
    .line 373
    invoke-static/range {v4 .. v9}, Lla1/a;->q(Lxa1/a;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;LHX0/e;Ljava/lang/String;J)Lma1/d;

    .line 374
    .line 375
    .line 376
    move-result-object v1

    .line 377
    invoke-interface {v10, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 378
    .line 379
    .line 380
    move-wide v4, v8

    .line 381
    move-object v6, v11

    .line 382
    move-object v8, v12

    .line 383
    move-object v10, v13

    .line 384
    goto :goto_5

    .line 385
    :cond_7
    check-cast v6, Ljava/util/List;

    .line 386
    .line 387
    return-object v6

    .line 388
    :cond_8
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->a:Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 389
    .line 390
    iput-object v1, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$0:Ljava/lang/Object;

    .line 391
    .line 392
    iput-wide v8, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->J$0:J

    .line 393
    .line 394
    iput v10, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->label:I

    .line 395
    .line 396
    invoke-virtual {v4, v2}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->h(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 397
    .line 398
    .line 399
    move-result-object v4

    .line 400
    if-ne v4, v3, :cond_9

    .line 401
    .line 402
    goto/16 :goto_a

    .line 403
    .line 404
    :cond_9
    move-object/from16 v17, v4

    .line 405
    .line 406
    move-object v4, v1

    .line 407
    move-object/from16 v1, v17

    .line 408
    .line 409
    :goto_7
    check-cast v1, Ljava/lang/Iterable;

    .line 410
    .line 411
    new-instance v10, Ljava/util/ArrayList;

    .line 412
    .line 413
    invoke-static {v1, v7}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 414
    .line 415
    .line 416
    move-result v11

    .line 417
    invoke-direct {v10, v11}, Ljava/util/ArrayList;-><init>(I)V

    .line 418
    .line 419
    .line 420
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 421
    .line 422
    .line 423
    move-result-object v1

    .line 424
    move-object v13, v1

    .line 425
    :goto_8
    invoke-interface {v13}, Ljava/util/Iterator;->hasNext()Z

    .line 426
    .line 427
    .line 428
    move-result v1

    .line 429
    if-eqz v1, :cond_b

    .line 430
    .line 431
    invoke-interface {v13}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 432
    .line 433
    .line 434
    move-result-object v1

    .line 435
    check-cast v1, Lxa1/a;

    .line 436
    .line 437
    iget-object v12, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->b:LHX0/e;

    .line 438
    .line 439
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->c:Lgk/d;

    .line 440
    .line 441
    invoke-virtual {v1}, Lxa1/a;->f()Ljava/lang/String;

    .line 442
    .line 443
    .line 444
    move-result-object v14

    .line 445
    iput-object v4, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$0:Ljava/lang/Object;

    .line 446
    .line 447
    iput-object v10, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$1:Ljava/lang/Object;

    .line 448
    .line 449
    iput-object v13, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$2:Ljava/lang/Object;

    .line 450
    .line 451
    iput-object v12, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$3:Ljava/lang/Object;

    .line 452
    .line 453
    iput-object v4, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$4:Ljava/lang/Object;

    .line 454
    .line 455
    iput-object v1, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$5:Ljava/lang/Object;

    .line 456
    .line 457
    iput-object v10, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$6:Ljava/lang/Object;

    .line 458
    .line 459
    iput-wide v8, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->J$0:J

    .line 460
    .line 461
    iput v6, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->label:I

    .line 462
    .line 463
    invoke-interface {v11, v14, v2}, Lgk/d;->a(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 464
    .line 465
    .line 466
    move-result-object v11

    .line 467
    if-ne v11, v3, :cond_a

    .line 468
    .line 469
    goto :goto_a

    .line 470
    :cond_a
    move-object v15, v4

    .line 471
    move-object v14, v10

    .line 472
    move-object/from16 v16, v13

    .line 473
    .line 474
    move-object v4, v14

    .line 475
    move-object v10, v12

    .line 476
    move-wide v12, v8

    .line 477
    move-object v8, v1

    .line 478
    move-object v9, v15

    .line 479
    move-object v1, v11

    .line 480
    :goto_9
    move-object v11, v1

    .line 481
    check-cast v11, Ljava/lang/String;

    .line 482
    .line 483
    invoke-static/range {v8 .. v13}, Lla1/a;->q(Lxa1/a;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;LHX0/e;Ljava/lang/String;J)Lma1/d;

    .line 484
    .line 485
    .line 486
    move-result-object v1

    .line 487
    invoke-interface {v4, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 488
    .line 489
    .line 490
    move-wide v8, v12

    .line 491
    move-object v10, v14

    .line 492
    move-object v4, v15

    .line 493
    move-object/from16 v13, v16

    .line 494
    .line 495
    goto :goto_8

    .line 496
    :cond_b
    move-object v1, v10

    .line 497
    check-cast v1, Ljava/util/List;

    .line 498
    .line 499
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->a:Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 500
    .line 501
    iput-object v4, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$0:Ljava/lang/Object;

    .line 502
    .line 503
    iput-object v1, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$1:Ljava/lang/Object;

    .line 504
    .line 505
    const/4 v8, 0x0

    .line 506
    iput-object v8, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$2:Ljava/lang/Object;

    .line 507
    .line 508
    iput-object v8, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$3:Ljava/lang/Object;

    .line 509
    .line 510
    iput-object v8, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$4:Ljava/lang/Object;

    .line 511
    .line 512
    iput-object v8, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$5:Ljava/lang/Object;

    .line 513
    .line 514
    iput-object v8, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->L$6:Ljava/lang/Object;

    .line 515
    .line 516
    iput v5, v2, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getLocalBonusesUiModels$1;->label:I

    .line 517
    .line 518
    invoke-virtual {v6, v2}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->i(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 519
    .line 520
    .line 521
    move-result-object v2

    .line 522
    if-ne v2, v3, :cond_c

    .line 523
    .line 524
    :goto_a
    return-object v3

    .line 525
    :cond_c
    move-object v3, v1

    .line 526
    move-object v1, v2

    .line 527
    move-object v2, v4

    .line 528
    :goto_b
    check-cast v1, Ljava/lang/Iterable;

    .line 529
    .line 530
    new-instance v4, Ljava/util/ArrayList;

    .line 531
    .line 532
    invoke-static {v1, v7}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 533
    .line 534
    .line 535
    move-result v5

    .line 536
    invoke-direct {v4, v5}, Ljava/util/ArrayList;-><init>(I)V

    .line 537
    .line 538
    .line 539
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 540
    .line 541
    .line 542
    move-result-object v1

    .line 543
    :goto_c
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 544
    .line 545
    .line 546
    move-result v5

    .line 547
    if-eqz v5, :cond_d

    .line 548
    .line 549
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 550
    .line 551
    .line 552
    move-result-object v5

    .line 553
    check-cast v5, Lxa1/c;

    .line 554
    .line 555
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->b:LHX0/e;

    .line 556
    .line 557
    invoke-static {v5, v2, v6}, Lla1/b;->b(Lxa1/c;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;LHX0/e;)Lma1/e;

    .line 558
    .line 559
    .line 560
    move-result-object v5

    .line 561
    invoke-interface {v4, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 562
    .line 563
    .line 564
    goto :goto_c

    .line 565
    :cond_d
    invoke-static {v3, v4}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 566
    .line 567
    .line 568
    move-result-object v1

    .line 569
    return-object v1

    .line 570
    nop

    .line 571
    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final i()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;
    .locals 4

    .line 1
    invoke-static {}, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;->getEntries()Lkotlin/enums/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    if-eqz v1, :cond_1

    .line 14
    .line 15
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    move-object v2, v1

    .line 20
    check-cast v2, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 21
    .line 22
    invoke-virtual {v2}, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;->getStyle()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v2

    .line 26
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->d:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 27
    .line 28
    invoke-interface {v3}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 29
    .line 30
    .line 31
    move-result-object v3

    .line 32
    invoke-virtual {v3}, Lek0/o;->n()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object v3

    .line 36
    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    if-eqz v2, :cond_0

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_1
    const/4 v1, 0x0

    .line 44
    :goto_0
    check-cast v1, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 45
    .line 46
    if-nez v1, :cond_2

    .line 47
    .line 48
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;->BACKGROUND_PICTURE_STYLE:Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 49
    .line 50
    return-object v0

    .line 51
    :cond_2
    return-object v1
.end method
