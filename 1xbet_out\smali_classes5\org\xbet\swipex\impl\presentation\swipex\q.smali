.class public final Lorg/xbet/swipex/impl/presentation/swipex/q;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final A:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lfk/i;",
            ">;"
        }
    .end annotation
.end field

.field public final B:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lfk/j;",
            ">;"
        }
    .end annotation
.end field

.field public final C:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public final D:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lh9/a;",
            ">;"
        }
    .end annotation
.end field

.field public final E:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/feed/subscriptions/domain/usecases/r;",
            ">;"
        }
    .end annotation
.end field

.field public final F:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lek/c;",
            ">;"
        }
    .end annotation
.end field

.field public final G:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/b;",
            ">;"
        }
    .end annotation
.end field

.field public final H:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/P;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/f1;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/LoadSwipexCardListScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/GetSportsFromLocaleUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/h;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/GetEventModelGroupByIdUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/j;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LGS0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/GetCurrencyModelScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/GetEventModelByIdUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Li8/m;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/MakeBetScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final l:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final m:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field public final n:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final o:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/UpdateLineCardUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final p:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/UpdateLiveCardUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/LoadAllFilterSportsAndChampsScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final r:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/GetSwipexBetSettingsScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final s:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LxX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final t:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final u:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljo/a;",
            ">;"
        }
    .end annotation
.end field

.field public final v:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final w:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/n;",
            ">;"
        }
    .end annotation
.end field

.field public final x:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/N;",
            ">;"
        }
    .end annotation
.end field

.field public final y:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LkS/a;",
            ">;"
        }
    .end annotation
.end field

.field public final z:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/L;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/f1;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/LoadSwipexCardListScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/GetSportsFromLocaleUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/h;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/GetEventModelGroupByIdUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/j;",
            ">;",
            "LBc/a<",
            "LGS0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/GetCurrencyModelScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/GetEventModelByIdUseCase;",
            ">;",
            "LBc/a<",
            "Li8/m;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/MakeBetScenario;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/UpdateLineCardUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/UpdateLiveCardUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/LoadAllFilterSportsAndChampsScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/GetSwipexBetSettingsScenario;",
            ">;",
            "LBc/a<",
            "LxX0/a;",
            ">;",
            "LBc/a<",
            "LSX0/a;",
            ">;",
            "LBc/a<",
            "Ljo/a;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/n;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/N;",
            ">;",
            "LBc/a<",
            "LkS/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/L;",
            ">;",
            "LBc/a<",
            "Lfk/i;",
            ">;",
            "LBc/a<",
            "Lfk/j;",
            ">;",
            "LBc/a<",
            "Ljava/lang/String;",
            ">;",
            "LBc/a<",
            "Lh9/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/feed/subscriptions/domain/usecases/r;",
            ">;",
            "LBc/a<",
            "Lek/c;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/P;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->j:LBc/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->k:LBc/a;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->l:LBc/a;

    .line 27
    .line 28
    iput-object p13, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->m:LBc/a;

    .line 29
    .line 30
    iput-object p14, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->n:LBc/a;

    .line 31
    .line 32
    iput-object p15, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->o:LBc/a;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->p:LBc/a;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->q:LBc/a;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->r:LBc/a;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->s:LBc/a;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->t:LBc/a;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->u:LBc/a;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->v:LBc/a;

    .line 61
    .line 62
    move-object/from16 p1, p23

    .line 63
    .line 64
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->w:LBc/a;

    .line 65
    .line 66
    move-object/from16 p1, p24

    .line 67
    .line 68
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->x:LBc/a;

    .line 69
    .line 70
    move-object/from16 p1, p25

    .line 71
    .line 72
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->y:LBc/a;

    .line 73
    .line 74
    move-object/from16 p1, p26

    .line 75
    .line 76
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->z:LBc/a;

    .line 77
    .line 78
    move-object/from16 p1, p27

    .line 79
    .line 80
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->A:LBc/a;

    .line 81
    .line 82
    move-object/from16 p1, p28

    .line 83
    .line 84
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->B:LBc/a;

    .line 85
    .line 86
    move-object/from16 p1, p29

    .line 87
    .line 88
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->C:LBc/a;

    .line 89
    .line 90
    move-object/from16 p1, p30

    .line 91
    .line 92
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->D:LBc/a;

    .line 93
    .line 94
    move-object/from16 p1, p31

    .line 95
    .line 96
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->E:LBc/a;

    .line 97
    .line 98
    move-object/from16 p1, p32

    .line 99
    .line 100
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->F:LBc/a;

    .line 101
    .line 102
    move-object/from16 p1, p33

    .line 103
    .line 104
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->G:LBc/a;

    .line 105
    .line 106
    move-object/from16 p1, p34

    .line 107
    .line 108
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/q;->H:LBc/a;

    .line 109
    .line 110
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/swipex/impl/presentation/swipex/q;
    .locals 35
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/f1;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/LoadSwipexCardListScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/GetSportsFromLocaleUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/h;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/GetEventModelGroupByIdUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/j;",
            ">;",
            "LBc/a<",
            "LGS0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/GetCurrencyModelScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/GetEventModelByIdUseCase;",
            ">;",
            "LBc/a<",
            "Li8/m;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/MakeBetScenario;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/UpdateLineCardUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/UpdateLiveCardUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/LoadAllFilterSportsAndChampsScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/GetSwipexBetSettingsScenario;",
            ">;",
            "LBc/a<",
            "LxX0/a;",
            ">;",
            "LBc/a<",
            "LSX0/a;",
            ">;",
            "LBc/a<",
            "Ljo/a;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/n;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/N;",
            ">;",
            "LBc/a<",
            "LkS/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/L;",
            ">;",
            "LBc/a<",
            "Lfk/i;",
            ">;",
            "LBc/a<",
            "Lfk/j;",
            ">;",
            "LBc/a<",
            "Ljava/lang/String;",
            ">;",
            "LBc/a<",
            "Lh9/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/feed/subscriptions/domain/usecases/r;",
            ">;",
            "LBc/a<",
            "Lek/c;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/scenario/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/swipex/impl/domain/usecases/P;",
            ">;)",
            "Lorg/xbet/swipex/impl/presentation/swipex/q;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/q;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    move-object/from16 v31, p30

    .line 64
    .line 65
    move-object/from16 v32, p31

    .line 66
    .line 67
    move-object/from16 v33, p32

    .line 68
    .line 69
    move-object/from16 v34, p33

    .line 70
    .line 71
    invoke-direct/range {v0 .. v34}, Lorg/xbet/swipex/impl/presentation/swipex/q;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 72
    .line 73
    .line 74
    return-object v0
.end method

.method public static c(Landroidx/lifecycle/Q;Lorg/xbet/analytics/domain/scope/f1;Lorg/xbet/swipex/impl/domain/scenario/LoadSwipexCardListScenario;Lorg/xbet/swipex/impl/domain/usecases/GetSportsFromLocaleUseCase;Lorg/xbet/swipex/impl/domain/usecases/h;Lorg/xbet/swipex/impl/domain/usecases/GetEventModelGroupByIdUseCase;Lorg/xbet/swipex/impl/domain/usecases/j;LGS0/a;Lorg/xbet/swipex/impl/domain/scenario/GetCurrencyModelScenario;Lorg/xbet/swipex/impl/domain/usecases/GetEventModelByIdUseCase;Li8/m;Lorg/xbet/swipex/impl/domain/scenario/MakeBetScenario;LwX0/c;ZLm8/a;Lorg/xbet/swipex/impl/domain/usecases/UpdateLineCardUseCase;Lorg/xbet/swipex/impl/domain/usecases/UpdateLiveCardUseCase;Lorg/xbet/swipex/impl/domain/scenario/LoadAllFilterSportsAndChampsScenario;Lorg/xbet/swipex/impl/domain/scenario/GetSwipexBetSettingsScenario;LxX0/a;LSX0/a;Ljo/a;LHX0/e;Lorg/xbet/swipex/impl/domain/usecases/n;Lorg/xbet/swipex/impl/domain/usecases/N;LkS/a;Lorg/xbet/swipex/impl/domain/usecases/L;Lfk/i;Lfk/j;Ljava/lang/String;Lh9/a;Lorg/xbet/feed/subscriptions/domain/usecases/r;Lek/c;Lorg/xbet/swipex/impl/domain/scenario/b;Lorg/xbet/swipex/impl/domain/usecases/P;)Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;
    .locals 36

    .line 1
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    move-object/from16 v31, p30

    .line 64
    .line 65
    move-object/from16 v32, p31

    .line 66
    .line 67
    move-object/from16 v33, p32

    .line 68
    .line 69
    move-object/from16 v34, p33

    .line 70
    .line 71
    move-object/from16 v35, p34

    .line 72
    .line 73
    invoke-direct/range {v0 .. v35}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;-><init>(Landroidx/lifecycle/Q;Lorg/xbet/analytics/domain/scope/f1;Lorg/xbet/swipex/impl/domain/scenario/LoadSwipexCardListScenario;Lorg/xbet/swipex/impl/domain/usecases/GetSportsFromLocaleUseCase;Lorg/xbet/swipex/impl/domain/usecases/h;Lorg/xbet/swipex/impl/domain/usecases/GetEventModelGroupByIdUseCase;Lorg/xbet/swipex/impl/domain/usecases/j;LGS0/a;Lorg/xbet/swipex/impl/domain/scenario/GetCurrencyModelScenario;Lorg/xbet/swipex/impl/domain/usecases/GetEventModelByIdUseCase;Li8/m;Lorg/xbet/swipex/impl/domain/scenario/MakeBetScenario;LwX0/c;ZLm8/a;Lorg/xbet/swipex/impl/domain/usecases/UpdateLineCardUseCase;Lorg/xbet/swipex/impl/domain/usecases/UpdateLiveCardUseCase;Lorg/xbet/swipex/impl/domain/scenario/LoadAllFilterSportsAndChampsScenario;Lorg/xbet/swipex/impl/domain/scenario/GetSwipexBetSettingsScenario;LxX0/a;LSX0/a;Ljo/a;LHX0/e;Lorg/xbet/swipex/impl/domain/usecases/n;Lorg/xbet/swipex/impl/domain/usecases/N;LkS/a;Lorg/xbet/swipex/impl/domain/usecases/L;Lfk/i;Lfk/j;Ljava/lang/String;Lh9/a;Lorg/xbet/feed/subscriptions/domain/usecases/r;Lek/c;Lorg/xbet/swipex/impl/domain/scenario/b;Lorg/xbet/swipex/impl/domain/usecases/P;)V

    .line 74
    .line 75
    .line 76
    return-object v0
.end method


# virtual methods
.method public b(Landroidx/lifecycle/Q;)Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;
    .locals 37

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->a:LBc/a;

    .line 4
    .line 5
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    move-object v3, v1

    .line 10
    check-cast v3, Lorg/xbet/analytics/domain/scope/f1;

    .line 11
    .line 12
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->b:LBc/a;

    .line 13
    .line 14
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    move-object v4, v1

    .line 19
    check-cast v4, Lorg/xbet/swipex/impl/domain/scenario/LoadSwipexCardListScenario;

    .line 20
    .line 21
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->c:LBc/a;

    .line 22
    .line 23
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    move-object v5, v1

    .line 28
    check-cast v5, Lorg/xbet/swipex/impl/domain/usecases/GetSportsFromLocaleUseCase;

    .line 29
    .line 30
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->d:LBc/a;

    .line 31
    .line 32
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    move-object v6, v1

    .line 37
    check-cast v6, Lorg/xbet/swipex/impl/domain/usecases/h;

    .line 38
    .line 39
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->e:LBc/a;

    .line 40
    .line 41
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    move-object v7, v1

    .line 46
    check-cast v7, Lorg/xbet/swipex/impl/domain/usecases/GetEventModelGroupByIdUseCase;

    .line 47
    .line 48
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->f:LBc/a;

    .line 49
    .line 50
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    move-object v8, v1

    .line 55
    check-cast v8, Lorg/xbet/swipex/impl/domain/usecases/j;

    .line 56
    .line 57
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->g:LBc/a;

    .line 58
    .line 59
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    move-object v9, v1

    .line 64
    check-cast v9, LGS0/a;

    .line 65
    .line 66
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->h:LBc/a;

    .line 67
    .line 68
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    move-object v10, v1

    .line 73
    check-cast v10, Lorg/xbet/swipex/impl/domain/scenario/GetCurrencyModelScenario;

    .line 74
    .line 75
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->i:LBc/a;

    .line 76
    .line 77
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    move-object v11, v1

    .line 82
    check-cast v11, Lorg/xbet/swipex/impl/domain/usecases/GetEventModelByIdUseCase;

    .line 83
    .line 84
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->j:LBc/a;

    .line 85
    .line 86
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    move-object v12, v1

    .line 91
    check-cast v12, Li8/m;

    .line 92
    .line 93
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->k:LBc/a;

    .line 94
    .line 95
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object v1

    .line 99
    move-object v13, v1

    .line 100
    check-cast v13, Lorg/xbet/swipex/impl/domain/scenario/MakeBetScenario;

    .line 101
    .line 102
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->l:LBc/a;

    .line 103
    .line 104
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    move-object v14, v1

    .line 109
    check-cast v14, LwX0/c;

    .line 110
    .line 111
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->m:LBc/a;

    .line 112
    .line 113
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object v1

    .line 117
    check-cast v1, Ljava/lang/Boolean;

    .line 118
    .line 119
    invoke-virtual {v1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 120
    .line 121
    .line 122
    move-result v15

    .line 123
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->n:LBc/a;

    .line 124
    .line 125
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 126
    .line 127
    .line 128
    move-result-object v1

    .line 129
    move-object/from16 v16, v1

    .line 130
    .line 131
    check-cast v16, Lm8/a;

    .line 132
    .line 133
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->o:LBc/a;

    .line 134
    .line 135
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 136
    .line 137
    .line 138
    move-result-object v1

    .line 139
    move-object/from16 v17, v1

    .line 140
    .line 141
    check-cast v17, Lorg/xbet/swipex/impl/domain/usecases/UpdateLineCardUseCase;

    .line 142
    .line 143
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->p:LBc/a;

    .line 144
    .line 145
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 146
    .line 147
    .line 148
    move-result-object v1

    .line 149
    move-object/from16 v18, v1

    .line 150
    .line 151
    check-cast v18, Lorg/xbet/swipex/impl/domain/usecases/UpdateLiveCardUseCase;

    .line 152
    .line 153
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->q:LBc/a;

    .line 154
    .line 155
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 156
    .line 157
    .line 158
    move-result-object v1

    .line 159
    move-object/from16 v19, v1

    .line 160
    .line 161
    check-cast v19, Lorg/xbet/swipex/impl/domain/scenario/LoadAllFilterSportsAndChampsScenario;

    .line 162
    .line 163
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->r:LBc/a;

    .line 164
    .line 165
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 166
    .line 167
    .line 168
    move-result-object v1

    .line 169
    move-object/from16 v20, v1

    .line 170
    .line 171
    check-cast v20, Lorg/xbet/swipex/impl/domain/scenario/GetSwipexBetSettingsScenario;

    .line 172
    .line 173
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->s:LBc/a;

    .line 174
    .line 175
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 176
    .line 177
    .line 178
    move-result-object v1

    .line 179
    move-object/from16 v21, v1

    .line 180
    .line 181
    check-cast v21, LxX0/a;

    .line 182
    .line 183
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->t:LBc/a;

    .line 184
    .line 185
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 186
    .line 187
    .line 188
    move-result-object v1

    .line 189
    move-object/from16 v22, v1

    .line 190
    .line 191
    check-cast v22, LSX0/a;

    .line 192
    .line 193
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->u:LBc/a;

    .line 194
    .line 195
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 196
    .line 197
    .line 198
    move-result-object v1

    .line 199
    move-object/from16 v23, v1

    .line 200
    .line 201
    check-cast v23, Ljo/a;

    .line 202
    .line 203
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->v:LBc/a;

    .line 204
    .line 205
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 206
    .line 207
    .line 208
    move-result-object v1

    .line 209
    move-object/from16 v24, v1

    .line 210
    .line 211
    check-cast v24, LHX0/e;

    .line 212
    .line 213
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->w:LBc/a;

    .line 214
    .line 215
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 216
    .line 217
    .line 218
    move-result-object v1

    .line 219
    move-object/from16 v25, v1

    .line 220
    .line 221
    check-cast v25, Lorg/xbet/swipex/impl/domain/usecases/n;

    .line 222
    .line 223
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->x:LBc/a;

    .line 224
    .line 225
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 226
    .line 227
    .line 228
    move-result-object v1

    .line 229
    move-object/from16 v26, v1

    .line 230
    .line 231
    check-cast v26, Lorg/xbet/swipex/impl/domain/usecases/N;

    .line 232
    .line 233
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->y:LBc/a;

    .line 234
    .line 235
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 236
    .line 237
    .line 238
    move-result-object v1

    .line 239
    move-object/from16 v27, v1

    .line 240
    .line 241
    check-cast v27, LkS/a;

    .line 242
    .line 243
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->z:LBc/a;

    .line 244
    .line 245
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 246
    .line 247
    .line 248
    move-result-object v1

    .line 249
    move-object/from16 v28, v1

    .line 250
    .line 251
    check-cast v28, Lorg/xbet/swipex/impl/domain/usecases/L;

    .line 252
    .line 253
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->A:LBc/a;

    .line 254
    .line 255
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 256
    .line 257
    .line 258
    move-result-object v1

    .line 259
    move-object/from16 v29, v1

    .line 260
    .line 261
    check-cast v29, Lfk/i;

    .line 262
    .line 263
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->B:LBc/a;

    .line 264
    .line 265
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 266
    .line 267
    .line 268
    move-result-object v1

    .line 269
    move-object/from16 v30, v1

    .line 270
    .line 271
    check-cast v30, Lfk/j;

    .line 272
    .line 273
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->C:LBc/a;

    .line 274
    .line 275
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 276
    .line 277
    .line 278
    move-result-object v1

    .line 279
    move-object/from16 v31, v1

    .line 280
    .line 281
    check-cast v31, Ljava/lang/String;

    .line 282
    .line 283
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->D:LBc/a;

    .line 284
    .line 285
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 286
    .line 287
    .line 288
    move-result-object v1

    .line 289
    move-object/from16 v32, v1

    .line 290
    .line 291
    check-cast v32, Lh9/a;

    .line 292
    .line 293
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->E:LBc/a;

    .line 294
    .line 295
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 296
    .line 297
    .line 298
    move-result-object v1

    .line 299
    move-object/from16 v33, v1

    .line 300
    .line 301
    check-cast v33, Lorg/xbet/feed/subscriptions/domain/usecases/r;

    .line 302
    .line 303
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->F:LBc/a;

    .line 304
    .line 305
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 306
    .line 307
    .line 308
    move-result-object v1

    .line 309
    move-object/from16 v34, v1

    .line 310
    .line 311
    check-cast v34, Lek/c;

    .line 312
    .line 313
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->G:LBc/a;

    .line 314
    .line 315
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 316
    .line 317
    .line 318
    move-result-object v1

    .line 319
    move-object/from16 v35, v1

    .line 320
    .line 321
    check-cast v35, Lorg/xbet/swipex/impl/domain/scenario/b;

    .line 322
    .line 323
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/q;->H:LBc/a;

    .line 324
    .line 325
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 326
    .line 327
    .line 328
    move-result-object v1

    .line 329
    move-object/from16 v36, v1

    .line 330
    .line 331
    check-cast v36, Lorg/xbet/swipex/impl/domain/usecases/P;

    .line 332
    .line 333
    move-object/from16 v2, p1

    .line 334
    .line 335
    invoke-static/range {v2 .. v36}, Lorg/xbet/swipex/impl/presentation/swipex/q;->c(Landroidx/lifecycle/Q;Lorg/xbet/analytics/domain/scope/f1;Lorg/xbet/swipex/impl/domain/scenario/LoadSwipexCardListScenario;Lorg/xbet/swipex/impl/domain/usecases/GetSportsFromLocaleUseCase;Lorg/xbet/swipex/impl/domain/usecases/h;Lorg/xbet/swipex/impl/domain/usecases/GetEventModelGroupByIdUseCase;Lorg/xbet/swipex/impl/domain/usecases/j;LGS0/a;Lorg/xbet/swipex/impl/domain/scenario/GetCurrencyModelScenario;Lorg/xbet/swipex/impl/domain/usecases/GetEventModelByIdUseCase;Li8/m;Lorg/xbet/swipex/impl/domain/scenario/MakeBetScenario;LwX0/c;ZLm8/a;Lorg/xbet/swipex/impl/domain/usecases/UpdateLineCardUseCase;Lorg/xbet/swipex/impl/domain/usecases/UpdateLiveCardUseCase;Lorg/xbet/swipex/impl/domain/scenario/LoadAllFilterSportsAndChampsScenario;Lorg/xbet/swipex/impl/domain/scenario/GetSwipexBetSettingsScenario;LxX0/a;LSX0/a;Ljo/a;LHX0/e;Lorg/xbet/swipex/impl/domain/usecases/n;Lorg/xbet/swipex/impl/domain/usecases/N;LkS/a;Lorg/xbet/swipex/impl/domain/usecases/L;Lfk/i;Lfk/j;Ljava/lang/String;Lh9/a;Lorg/xbet/feed/subscriptions/domain/usecases/r;Lek/c;Lorg/xbet/swipex/impl/domain/scenario/b;Lorg/xbet/swipex/impl/domain/usecases/P;)Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 336
    .line 337
    .line 338
    move-result-object v1

    .line 339
    return-object v1
.end method
