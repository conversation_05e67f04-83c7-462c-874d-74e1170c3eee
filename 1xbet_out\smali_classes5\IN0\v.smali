.class public final LIN0/v;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0007\n\u0002\u0008\u0002\u001aW\u0010\r\u001a\u00020\u00062\u0008\u0008\u0001\u0010\u0001\u001a\u00020\u00002\u0008\u0008\u0001\u0010\u0002\u001a\u00020\u00002\u0006\u0010\u0004\u001a\u00020\u00032\u000c\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u00052\u0008\u0008\u0002\u0010\t\u001a\u00020\u00082\u0012\u0010\u000c\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00060\nH\u0007\u00a2\u0006\u0004\u0008\r\u0010\u000e\u00a8\u0006\u0011\u00b2\u0006\u000c\u0010\u0010\u001a\u00020\u000f8\nX\u008a\u0084\u0002"
    }
    d2 = {
        "",
        "title",
        "icon",
        "",
        "expandableState",
        "Lkotlin/Function0;",
        "",
        "onClickArrow",
        "Landroidx/compose/ui/l;",
        "modifier",
        "Lkotlin/Function1;",
        "Landroidx/compose/foundation/layout/m;",
        "content",
        "b",
        "(IIZLkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;LOc/n;Landroidx/compose/runtime/j;II)V",
        "",
        "rotationState",
        "statistic_core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(IIZLkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;LOc/n;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p9}, LIN0/v;->d(IIZLkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;LOc/n;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final b(IIZLkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;LOc/n;Landroidx/compose/runtime/j;II)V
    .locals 25
    .param p3    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LOc/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(IIZ",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/ui/l;",
            "LOc/n<",
            "-",
            "Landroidx/compose/foundation/layout/m;",
            "-",
            "Landroidx/compose/runtime/j;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    move/from16 v2, p2

    .line 2
    .line 3
    move/from16 v7, p7

    .line 4
    .line 5
    const v0, -0x530c973f

    .line 6
    .line 7
    .line 8
    move-object/from16 v1, p6

    .line 9
    .line 10
    invoke-interface {v1, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 11
    .line 12
    .line 13
    move-result-object v13

    .line 14
    and-int/lit8 v1, p8, 0x1

    .line 15
    .line 16
    if-eqz v1, :cond_0

    .line 17
    .line 18
    or-int/lit8 v1, v7, 0x6

    .line 19
    .line 20
    move v3, v1

    .line 21
    move/from16 v1, p0

    .line 22
    .line 23
    goto :goto_1

    .line 24
    :cond_0
    and-int/lit8 v1, v7, 0x6

    .line 25
    .line 26
    if-nez v1, :cond_2

    .line 27
    .line 28
    move/from16 v1, p0

    .line 29
    .line 30
    invoke-interface {v13, v1}, Landroidx/compose/runtime/j;->x(I)Z

    .line 31
    .line 32
    .line 33
    move-result v3

    .line 34
    if-eqz v3, :cond_1

    .line 35
    .line 36
    const/4 v3, 0x4

    .line 37
    goto :goto_0

    .line 38
    :cond_1
    const/4 v3, 0x2

    .line 39
    :goto_0
    or-int/2addr v3, v7

    .line 40
    goto :goto_1

    .line 41
    :cond_2
    move/from16 v1, p0

    .line 42
    .line 43
    move v3, v7

    .line 44
    :goto_1
    and-int/lit8 v4, p8, 0x2

    .line 45
    .line 46
    if-eqz v4, :cond_4

    .line 47
    .line 48
    or-int/lit8 v3, v3, 0x30

    .line 49
    .line 50
    :cond_3
    move/from16 v4, p1

    .line 51
    .line 52
    goto :goto_3

    .line 53
    :cond_4
    and-int/lit8 v4, v7, 0x30

    .line 54
    .line 55
    if-nez v4, :cond_3

    .line 56
    .line 57
    move/from16 v4, p1

    .line 58
    .line 59
    invoke-interface {v13, v4}, Landroidx/compose/runtime/j;->x(I)Z

    .line 60
    .line 61
    .line 62
    move-result v5

    .line 63
    if-eqz v5, :cond_5

    .line 64
    .line 65
    const/16 v5, 0x20

    .line 66
    .line 67
    goto :goto_2

    .line 68
    :cond_5
    const/16 v5, 0x10

    .line 69
    .line 70
    :goto_2
    or-int/2addr v3, v5

    .line 71
    :goto_3
    and-int/lit8 v5, p8, 0x4

    .line 72
    .line 73
    if-eqz v5, :cond_6

    .line 74
    .line 75
    or-int/lit16 v3, v3, 0x180

    .line 76
    .line 77
    goto :goto_5

    .line 78
    :cond_6
    and-int/lit16 v5, v7, 0x180

    .line 79
    .line 80
    if-nez v5, :cond_8

    .line 81
    .line 82
    invoke-interface {v13, v2}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 83
    .line 84
    .line 85
    move-result v5

    .line 86
    if-eqz v5, :cond_7

    .line 87
    .line 88
    const/16 v5, 0x100

    .line 89
    .line 90
    goto :goto_4

    .line 91
    :cond_7
    const/16 v5, 0x80

    .line 92
    .line 93
    :goto_4
    or-int/2addr v3, v5

    .line 94
    :cond_8
    :goto_5
    and-int/lit8 v5, p8, 0x8

    .line 95
    .line 96
    if-eqz v5, :cond_a

    .line 97
    .line 98
    or-int/lit16 v3, v3, 0xc00

    .line 99
    .line 100
    :cond_9
    move-object/from16 v5, p3

    .line 101
    .line 102
    goto :goto_7

    .line 103
    :cond_a
    and-int/lit16 v5, v7, 0xc00

    .line 104
    .line 105
    if-nez v5, :cond_9

    .line 106
    .line 107
    move-object/from16 v5, p3

    .line 108
    .line 109
    invoke-interface {v13, v5}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 110
    .line 111
    .line 112
    move-result v6

    .line 113
    if-eqz v6, :cond_b

    .line 114
    .line 115
    const/16 v6, 0x800

    .line 116
    .line 117
    goto :goto_6

    .line 118
    :cond_b
    const/16 v6, 0x400

    .line 119
    .line 120
    :goto_6
    or-int/2addr v3, v6

    .line 121
    :goto_7
    and-int/lit8 v6, p8, 0x10

    .line 122
    .line 123
    if-eqz v6, :cond_d

    .line 124
    .line 125
    or-int/lit16 v3, v3, 0x6000

    .line 126
    .line 127
    :cond_c
    move-object/from16 v8, p4

    .line 128
    .line 129
    goto :goto_9

    .line 130
    :cond_d
    and-int/lit16 v8, v7, 0x6000

    .line 131
    .line 132
    if-nez v8, :cond_c

    .line 133
    .line 134
    move-object/from16 v8, p4

    .line 135
    .line 136
    invoke-interface {v13, v8}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 137
    .line 138
    .line 139
    move-result v9

    .line 140
    if-eqz v9, :cond_e

    .line 141
    .line 142
    const/16 v9, 0x4000

    .line 143
    .line 144
    goto :goto_8

    .line 145
    :cond_e
    const/16 v9, 0x2000

    .line 146
    .line 147
    :goto_8
    or-int/2addr v3, v9

    .line 148
    :goto_9
    and-int/lit8 v9, p8, 0x20

    .line 149
    .line 150
    const/high16 v10, 0x30000

    .line 151
    .line 152
    if-eqz v9, :cond_10

    .line 153
    .line 154
    or-int/2addr v3, v10

    .line 155
    :cond_f
    move-object/from16 v9, p5

    .line 156
    .line 157
    goto :goto_b

    .line 158
    :cond_10
    and-int v9, v7, v10

    .line 159
    .line 160
    if-nez v9, :cond_f

    .line 161
    .line 162
    move-object/from16 v9, p5

    .line 163
    .line 164
    invoke-interface {v13, v9}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 165
    .line 166
    .line 167
    move-result v10

    .line 168
    if-eqz v10, :cond_11

    .line 169
    .line 170
    const/high16 v10, 0x20000

    .line 171
    .line 172
    goto :goto_a

    .line 173
    :cond_11
    const/high16 v10, 0x10000

    .line 174
    .line 175
    :goto_a
    or-int/2addr v3, v10

    .line 176
    :goto_b
    const v10, 0x12493

    .line 177
    .line 178
    .line 179
    and-int/2addr v10, v3

    .line 180
    const v11, 0x12492

    .line 181
    .line 182
    .line 183
    if-ne v10, v11, :cond_13

    .line 184
    .line 185
    invoke-interface {v13}, Landroidx/compose/runtime/j;->c()Z

    .line 186
    .line 187
    .line 188
    move-result v10

    .line 189
    if-nez v10, :cond_12

    .line 190
    .line 191
    goto :goto_c

    .line 192
    :cond_12
    invoke-interface {v13}, Landroidx/compose/runtime/j;->n()V

    .line 193
    .line 194
    .line 195
    move-object v5, v8

    .line 196
    goto/16 :goto_f

    .line 197
    .line 198
    :cond_13
    :goto_c
    if-eqz v6, :cond_14

    .line 199
    .line 200
    sget-object v6, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 201
    .line 202
    goto :goto_d

    .line 203
    :cond_14
    move-object v6, v8

    .line 204
    :goto_d
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 205
    .line 206
    .line 207
    move-result v8

    .line 208
    if-eqz v8, :cond_15

    .line 209
    .line 210
    const/4 v8, -0x1

    .line 211
    const-string v10, "org.xbet.statistic.statistic_core.presentation.composable.StatisticBoxCardComponent (StatisticBoxCardComponent.kt:51)"

    .line 212
    .line 213
    invoke-static {v0, v3, v8, v10}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 214
    .line 215
    .line 216
    :cond_15
    const/4 v0, 0x0

    .line 217
    if-eqz v2, :cond_16

    .line 218
    .line 219
    const/high16 v3, 0x43340000    # 180.0f

    .line 220
    .line 221
    const/high16 v8, 0x43340000    # 180.0f

    .line 222
    .line 223
    goto :goto_e

    .line 224
    :cond_16
    const/4 v8, 0x0

    .line 225
    :goto_e
    const/16 v14, 0xc00

    .line 226
    .line 227
    const/16 v15, 0x16

    .line 228
    .line 229
    const/4 v9, 0x0

    .line 230
    const/4 v10, 0x0

    .line 231
    const-string v11, "Rotate animation"

    .line 232
    .line 233
    const/4 v12, 0x0

    .line 234
    invoke-static/range {v8 .. v15}, Landroidx/compose/animation/core/AnimateAsStateKt;->d(FLandroidx/compose/animation/core/g;FLjava/lang/String;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)Landroidx/compose/runtime/r1;

    .line 235
    .line 236
    .line 237
    move-result-object v3

    .line 238
    const/4 v8, 0x0

    .line 239
    const/4 v9, 0x1

    .line 240
    invoke-static {v6, v0, v9, v8}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 241
    .line 242
    .line 243
    move-result-object v8

    .line 244
    sget-object v0, Landroidx/compose/material3/r;->a:Landroidx/compose/material3/r;

    .line 245
    .line 246
    sget v10, Landroidx/compose/material3/r;->b:I

    .line 247
    .line 248
    invoke-virtual {v0, v13, v10}, Landroidx/compose/material3/r;->a(Landroidx/compose/runtime/j;I)Landroidx/compose/material3/q;

    .line 249
    .line 250
    .line 251
    move-result-object v14

    .line 252
    sget-object v0, LB11/e;->a:LB11/e;

    .line 253
    .line 254
    sget v10, LB11/e;->b:I

    .line 255
    .line 256
    invoke-virtual {v0, v13, v10}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 257
    .line 258
    .line 259
    move-result-object v0

    .line 260
    invoke-virtual {v0}, Lorg/xbet/uikit/compose/color/ThemeColors;->getBackgroundContent-0d7_KjU()J

    .line 261
    .line 262
    .line 263
    move-result-wide v15

    .line 264
    const/16 v23, 0xe

    .line 265
    .line 266
    const/16 v24, 0x0

    .line 267
    .line 268
    const-wide/16 v17, 0x0

    .line 269
    .line 270
    const-wide/16 v19, 0x0

    .line 271
    .line 272
    const-wide/16 v21, 0x0

    .line 273
    .line 274
    invoke-static/range {v14 .. v24}, Landroidx/compose/material3/q;->d(Landroidx/compose/material3/q;JJJJILjava/lang/Object;)Landroidx/compose/material3/q;

    .line 275
    .line 276
    .line 277
    move-result-object v10

    .line 278
    sget-object v0, LA11/a;->a:LA11/a;

    .line 279
    .line 280
    invoke-virtual {v0}, LA11/a;->U()F

    .line 281
    .line 282
    .line 283
    move-result v0

    .line 284
    invoke-static {v0}, LR/i;->f(F)LR/h;

    .line 285
    .line 286
    .line 287
    move-result-object v11

    .line 288
    new-instance v0, LIN0/v$a;

    .line 289
    .line 290
    move/from16 v17, v4

    .line 291
    .line 292
    move v4, v1

    .line 293
    move-object v1, v5

    .line 294
    move-object v5, v3

    .line 295
    move/from16 v3, v17

    .line 296
    .line 297
    move-object/from16 v17, v6

    .line 298
    .line 299
    move-object/from16 v6, p5

    .line 300
    .line 301
    invoke-direct/range {v0 .. v6}, LIN0/v$a;-><init>(Lkotlin/jvm/functions/Function0;ZIILandroidx/compose/runtime/r1;LOc/n;)V

    .line 302
    .line 303
    .line 304
    const/16 v1, 0x36

    .line 305
    .line 306
    const v2, 0x4f58e433    # 3.63883392E9f

    .line 307
    .line 308
    .line 309
    invoke-static {v2, v9, v0, v13, v1}, Landroidx/compose/runtime/internal/b;->d(IZLjava/lang/Object;Landroidx/compose/runtime/j;I)Landroidx/compose/runtime/internal/a;

    .line 310
    .line 311
    .line 312
    move-result-object v0

    .line 313
    const/high16 v15, 0x30000

    .line 314
    .line 315
    const/16 v16, 0x18

    .line 316
    .line 317
    move-object v9, v11

    .line 318
    const/4 v11, 0x0

    .line 319
    move-object v14, v13

    .line 320
    move-object v13, v0

    .line 321
    invoke-static/range {v8 .. v16}, Landroidx/compose/material3/CardKt;->a(Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/S1;Landroidx/compose/material3/q;Landroidx/compose/material3/CardElevation;Landroidx/compose/foundation/l;LOc/n;Landroidx/compose/runtime/j;II)V

    .line 322
    .line 323
    .line 324
    move-object v13, v14

    .line 325
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 326
    .line 327
    .line 328
    move-result v0

    .line 329
    if-eqz v0, :cond_17

    .line 330
    .line 331
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 332
    .line 333
    .line 334
    :cond_17
    move-object/from16 v5, v17

    .line 335
    .line 336
    :goto_f
    invoke-interface {v13}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 337
    .line 338
    .line 339
    move-result-object v9

    .line 340
    if-eqz v9, :cond_18

    .line 341
    .line 342
    new-instance v0, LIN0/u;

    .line 343
    .line 344
    move/from16 v1, p0

    .line 345
    .line 346
    move/from16 v2, p1

    .line 347
    .line 348
    move/from16 v3, p2

    .line 349
    .line 350
    move-object/from16 v4, p3

    .line 351
    .line 352
    move-object/from16 v6, p5

    .line 353
    .line 354
    move/from16 v8, p8

    .line 355
    .line 356
    invoke-direct/range {v0 .. v8}, LIN0/u;-><init>(IIZLkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;LOc/n;II)V

    .line 357
    .line 358
    .line 359
    invoke-interface {v9, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 360
    .line 361
    .line 362
    :cond_18
    return-void
.end method

.method public static final c(Landroidx/compose/runtime/r1;)F
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/runtime/r1<",
            "Ljava/lang/Float;",
            ">;)F"
        }
    .end annotation

    .line 1
    invoke-interface {p0}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Ljava/lang/Number;

    .line 6
    .line 7
    invoke-virtual {p0}, Ljava/lang/Number;->floatValue()F

    .line 8
    .line 9
    .line 10
    move-result p0

    .line 11
    return p0
.end method

.method public static final d(IIZLkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;LOc/n;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 10

    .line 1
    or-int/lit8 v0, p6, 0x1

    .line 2
    .line 3
    invoke-static {v0}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result v8

    .line 7
    move v1, p0

    .line 8
    move v2, p1

    .line 9
    move v3, p2

    .line 10
    move-object v4, p3

    .line 11
    move-object v5, p4

    .line 12
    move-object v6, p5

    .line 13
    move/from16 v9, p7

    .line 14
    .line 15
    move-object/from16 v7, p8

    .line 16
    .line 17
    invoke-static/range {v1 .. v9}, LIN0/v;->b(IIZLkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;LOc/n;Landroidx/compose/runtime/j;II)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method

.method public static final synthetic e(Landroidx/compose/runtime/r1;)F
    .locals 0

    .line 1
    invoke-static {p0}, LIN0/v;->c(Landroidx/compose/runtime/r1;)F

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method
