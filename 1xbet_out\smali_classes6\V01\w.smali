.class public final LV01/w;
.super LV01/t;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u00c7\u0002\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LV01/w;",
        "LV01/t;",
        "<init>",
        "()V",
        "uikit_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final f:LV01/w;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LV01/w;

    .line 2
    .line 3
    invoke-direct {v0}, LV01/w;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LV01/w;->f:LV01/w;

    .line 7
    .line 8
    return-void
.end method

.method private constructor <init>()V
    .locals 14

    .line 1
    const-wide v0, 0xff000000L

    .line 2
    .line 3
    .line 4
    .line 5
    .line 6
    invoke-static {v0, v1}, Landroidx/compose/ui/graphics/x0;->d(J)J

    .line 7
    .line 8
    .line 9
    move-result-wide v3

    .line 10
    const-wide v0, 0xff1c1c1cL

    .line 11
    .line 12
    .line 13
    .line 14
    .line 15
    invoke-static {v0, v1}, Landroidx/compose/ui/graphics/x0;->d(J)J

    .line 16
    .line 17
    .line 18
    move-result-wide v5

    .line 19
    const-wide v0, 0xffffffffL

    .line 20
    .line 21
    .line 22
    .line 23
    .line 24
    invoke-static {v0, v1}, Landroidx/compose/ui/graphics/x0;->d(J)J

    .line 25
    .line 26
    .line 27
    move-result-wide v7

    .line 28
    const-wide v0, 0xff989898L

    .line 29
    .line 30
    .line 31
    .line 32
    .line 33
    invoke-static {v0, v1}, Landroidx/compose/ui/graphics/x0;->d(J)J

    .line 34
    .line 35
    .line 36
    move-result-wide v9

    .line 37
    const-wide v0, 0xff313131L

    .line 38
    .line 39
    .line 40
    .line 41
    .line 42
    invoke-static {v0, v1}, Landroidx/compose/ui/graphics/x0;->d(J)J

    .line 43
    .line 44
    .line 45
    move-result-wide v11

    .line 46
    const/4 v13, 0x0

    .line 47
    move-object v2, p0

    .line 48
    invoke-direct/range {v2 .. v13}, LV01/t;-><init>(JJJJJLkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 49
    .line 50
    .line 51
    return-void
.end method
