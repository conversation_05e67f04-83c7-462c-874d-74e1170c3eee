.class public final LhI0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LhI0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LhI0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LhI0/a$b$a;,
        LhI0/a$b$b;
    }
.end annotation


# instance fields
.field public final a:LhI0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LeI0/b;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/player/impl/player/kabaddi_top_players/data/repositories/StatisticKabaddiTopPlayersRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LiI0/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/f;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTn/a;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/GetSportUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/l;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/l;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/player/impl/player/kabaddi_top_players/presentation/fragments/StatisticKabaddiTopPlayersViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LEN0/f;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LHX0/e;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;Li8/l;Ljava/lang/Long;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LhI0/a$b;->a:LhI0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p14}, LhI0/a$b;->b(LQW0/c;LEN0/f;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LHX0/e;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;Li8/l;Ljava/lang/Long;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LEN0/f;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LHX0/e;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;Li8/l;Ljava/lang/Long;Lc8/h;LhI0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p14}, LhI0/a$b;-><init>(LQW0/c;LEN0/f;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LHX0/e;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;Li8/l;Ljava/lang/Long;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/player/impl/player/kabaddi_top_players/presentation/fragments/StatisticKabaddiTopPlayersFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LhI0/a$b;->c(Lorg/xbet/statistic/player/impl/player/kabaddi_top_players/presentation/fragments/StatisticKabaddiTopPlayersFragment;)Lorg/xbet/statistic/player/impl/player/kabaddi_top_players/presentation/fragments/StatisticKabaddiTopPlayersFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;LEN0/f;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LHX0/e;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;Li8/l;Ljava/lang/Long;Lc8/h;)V
    .locals 0

    .line 1
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    iput-object p3, p0, LhI0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    iput-object p3, p0, LhI0/a$b;->c:Ldagger/internal/h;

    .line 12
    .line 13
    new-instance p3, LhI0/a$b$a;

    .line 14
    .line 15
    invoke-direct {p3, p1}, LhI0/a$b$a;-><init>(LQW0/c;)V

    .line 16
    .line 17
    .line 18
    iput-object p3, p0, LhI0/a$b;->d:Ldagger/internal/h;

    .line 19
    .line 20
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iput-object p1, p0, LhI0/a$b;->e:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static {p1}, LeI0/c;->a(LBc/a;)LeI0/c;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iput-object p1, p0, LhI0/a$b;->f:Ldagger/internal/h;

    .line 31
    .line 32
    invoke-static {p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iput-object p1, p0, LhI0/a$b;->g:Ldagger/internal/h;

    .line 37
    .line 38
    iget-object p3, p0, LhI0/a$b;->d:Ldagger/internal/h;

    .line 39
    .line 40
    iget-object p4, p0, LhI0/a$b;->f:Ldagger/internal/h;

    .line 41
    .line 42
    invoke-static {p3, p4, p1}, Lorg/xbet/statistic/player/impl/player/kabaddi_top_players/data/repositories/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/player/impl/player/kabaddi_top_players/data/repositories/a;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    iput-object p1, p0, LhI0/a$b;->h:Ldagger/internal/h;

    .line 47
    .line 48
    invoke-static {p1}, LiI0/b;->a(LBc/a;)LiI0/b;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iput-object p1, p0, LhI0/a$b;->i:Ldagger/internal/h;

    .line 53
    .line 54
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    iput-object p1, p0, LhI0/a$b;->j:Ldagger/internal/h;

    .line 59
    .line 60
    invoke-static {p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    iput-object p1, p0, LhI0/a$b;->k:Ldagger/internal/h;

    .line 65
    .line 66
    invoke-static {p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    iput-object p1, p0, LhI0/a$b;->l:Ldagger/internal/h;

    .line 71
    .line 72
    new-instance p1, LhI0/a$b$b;

    .line 73
    .line 74
    invoke-direct {p1, p2}, LhI0/a$b$b;-><init>(LEN0/f;)V

    .line 75
    .line 76
    .line 77
    iput-object p1, p0, LhI0/a$b;->m:Ldagger/internal/h;

    .line 78
    .line 79
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/g;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/g;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    iput-object p1, p0, LhI0/a$b;->n:Ldagger/internal/h;

    .line 84
    .line 85
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    iput-object p1, p0, LhI0/a$b;->o:Ldagger/internal/h;

    .line 90
    .line 91
    iget-object p2, p0, LhI0/a$b;->d:Ldagger/internal/h;

    .line 92
    .line 93
    invoke-static {p2, p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/h;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/h;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    iput-object p1, p0, LhI0/a$b;->p:Ldagger/internal/h;

    .line 98
    .line 99
    iget-object p1, p0, LhI0/a$b;->m:Ldagger/internal/h;

    .line 100
    .line 101
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/j;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/j;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    iput-object p1, p0, LhI0/a$b;->q:Ldagger/internal/h;

    .line 106
    .line 107
    iget-object p1, p0, LhI0/a$b;->m:Ldagger/internal/h;

    .line 108
    .line 109
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/m;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/m;

    .line 110
    .line 111
    .line 112
    move-result-object p1

    .line 113
    iput-object p1, p0, LhI0/a$b;->r:Ldagger/internal/h;

    .line 114
    .line 115
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 116
    .line 117
    .line 118
    move-result-object p7

    .line 119
    iput-object p7, p0, LhI0/a$b;->s:Ldagger/internal/h;

    .line 120
    .line 121
    iget-object p2, p0, LhI0/a$b;->n:Ldagger/internal/h;

    .line 122
    .line 123
    iget-object p3, p0, LhI0/a$b;->p:Ldagger/internal/h;

    .line 124
    .line 125
    iget-object p4, p0, LhI0/a$b;->q:Ldagger/internal/h;

    .line 126
    .line 127
    iget-object p5, p0, LhI0/a$b;->b:Ldagger/internal/h;

    .line 128
    .line 129
    iget-object p6, p0, LhI0/a$b;->r:Ldagger/internal/h;

    .line 130
    .line 131
    iget-object p8, p0, LhI0/a$b;->c:Ldagger/internal/h;

    .line 132
    .line 133
    invoke-static/range {p2 .. p8}, Lorg/xbet/statistic/statistic_core/presentation/delegates/o;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/o;

    .line 134
    .line 135
    .line 136
    move-result-object p1

    .line 137
    iput-object p1, p0, LhI0/a$b;->t:Ldagger/internal/h;

    .line 138
    .line 139
    invoke-static {p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 140
    .line 141
    .line 142
    move-result-object p9

    .line 143
    iput-object p9, p0, LhI0/a$b;->u:Ldagger/internal/h;

    .line 144
    .line 145
    iget-object p2, p0, LhI0/a$b;->b:Ldagger/internal/h;

    .line 146
    .line 147
    iget-object p3, p0, LhI0/a$b;->c:Ldagger/internal/h;

    .line 148
    .line 149
    iget-object p4, p0, LhI0/a$b;->i:Ldagger/internal/h;

    .line 150
    .line 151
    iget-object p5, p0, LhI0/a$b;->j:Ldagger/internal/h;

    .line 152
    .line 153
    iget-object p6, p0, LhI0/a$b;->k:Ldagger/internal/h;

    .line 154
    .line 155
    iget-object p7, p0, LhI0/a$b;->l:Ldagger/internal/h;

    .line 156
    .line 157
    iget-object p8, p0, LhI0/a$b;->t:Ldagger/internal/h;

    .line 158
    .line 159
    iget-object p10, p0, LhI0/a$b;->d:Ldagger/internal/h;

    .line 160
    .line 161
    invoke-static/range {p2 .. p10}, Lorg/xbet/statistic/player/impl/player/kabaddi_top_players/presentation/fragments/c;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/player/impl/player/kabaddi_top_players/presentation/fragments/c;

    .line 162
    .line 163
    .line 164
    move-result-object p1

    .line 165
    iput-object p1, p0, LhI0/a$b;->v:Ldagger/internal/h;

    .line 166
    .line 167
    return-void
.end method

.method public final c(Lorg/xbet/statistic/player/impl/player/kabaddi_top_players/presentation/fragments/StatisticKabaddiTopPlayersFragment;)Lorg/xbet/statistic/player/impl/player/kabaddi_top_players/presentation/fragments/StatisticKabaddiTopPlayersFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LhI0/a$b;->e()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/player/impl/player/kabaddi_top_players/presentation/fragments/b;->a(Lorg/xbet/statistic/player/impl/player/kabaddi_top_players/presentation/fragments/StatisticKabaddiTopPlayersFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final d()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/statistic/player/impl/player/kabaddi_top_players/presentation/fragments/StatisticKabaddiTopPlayersViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LhI0/a$b;->v:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final e()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LhI0/a$b;->d()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
