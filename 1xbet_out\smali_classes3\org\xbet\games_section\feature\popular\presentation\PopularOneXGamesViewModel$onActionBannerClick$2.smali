.class final Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.popular.presentation.PopularOneXGamesViewModel$onActionBannerClick$2"
    f = "PopularOneXGamesViewModel.kt"
    l = {
        0x11a
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->M1(Ljava/lang/String;II)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

.field final synthetic $position:I

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/banners/api/domain/models/BannerModel;Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    iput p3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->$position:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;

    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    iget v2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->$position:I

    invoke-direct {p1, v0, v1, v2, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;-><init>(Lorg/xplatform/banners/api/domain/models/BannerModel;Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;ILkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto/16 :goto_3

    .line 16
    .line 17
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 18
    .line 19
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 20
    .line 21
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    throw p1

    .line 25
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 29
    .line 30
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getAction()Z

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    if-eqz p1, :cond_2

    .line 35
    .line 36
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 37
    .line 38
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getDeeplink()Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 43
    .line 44
    .line 45
    move-result p1

    .line 46
    if-lez p1, :cond_2

    .line 47
    .line 48
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 49
    .line 50
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->D3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/U;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a$a;

    .line 55
    .line 56
    iget-object v3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 57
    .line 58
    invoke-virtual {v3}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getDeeplink()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v3

    .line 62
    invoke-direct {v1, v3}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a$a;-><init>(Ljava/lang/String;)V

    .line 63
    .line 64
    .line 65
    iput v2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->label:I

    .line 66
    .line 67
    invoke-interface {p1, v1, p0}, Lkotlinx/coroutines/flow/U;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    if-ne p1, v0, :cond_7

    .line 72
    .line 73
    return-object v0

    .line 74
    :cond_2
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 75
    .line 76
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getAction()Z

    .line 77
    .line 78
    .line 79
    move-result p1

    .line 80
    if-eqz p1, :cond_3

    .line 81
    .line 82
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 83
    .line 84
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getSiteLink()Ljava/lang/String;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 89
    .line 90
    .line 91
    move-result p1

    .line 92
    if-lez p1, :cond_3

    .line 93
    .line 94
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 95
    .line 96
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->b4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)LwX0/c;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 101
    .line 102
    invoke-static {v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->F3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)LwX0/a;

    .line 103
    .line 104
    .line 105
    move-result-object v0

    .line 106
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 107
    .line 108
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getSiteLink()Ljava/lang/String;

    .line 109
    .line 110
    .line 111
    move-result-object v1

    .line 112
    invoke-interface {v0, v1}, LwX0/a;->J(Ljava/lang/String;)Lq4/q;

    .line 113
    .line 114
    .line 115
    move-result-object v0

    .line 116
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 117
    .line 118
    .line 119
    goto :goto_3

    .line 120
    :cond_3
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 121
    .line 122
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->G3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Ljava/util/List;

    .line 123
    .line 124
    .line 125
    move-result-object p1

    .line 126
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 127
    .line 128
    .line 129
    move-result-object p1

    .line 130
    const/4 v0, 0x0

    .line 131
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 132
    .line 133
    .line 134
    move-result v1

    .line 135
    const/4 v3, -0x1

    .line 136
    if-eqz v1, :cond_5

    .line 137
    .line 138
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 139
    .line 140
    .line 141
    move-result-object v1

    .line 142
    check-cast v1, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 143
    .line 144
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 145
    .line 146
    .line 147
    move-result v1

    .line 148
    const/16 v4, 0x1081

    .line 149
    .line 150
    if-ne v1, v4, :cond_4

    .line 151
    .line 152
    goto :goto_1

    .line 153
    :cond_4
    add-int/lit8 v0, v0, 0x1

    .line 154
    .line 155
    goto :goto_0

    .line 156
    :cond_5
    const/4 v0, -0x1

    .line 157
    :goto_1
    if-eq v0, v3, :cond_6

    .line 158
    .line 159
    iget p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->$position:I

    .line 160
    .line 161
    sub-int/2addr p1, v2

    .line 162
    goto :goto_2

    .line 163
    :cond_6
    iget p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->$position:I

    .line 164
    .line 165
    :goto_2
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 166
    .line 167
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 168
    .line 169
    invoke-static {v0, v1, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->h4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lorg/xplatform/banners/api/domain/models/BannerModel;I)V

    .line 170
    .line 171
    .line 172
    :cond_7
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 173
    .line 174
    return-object p1
.end method
