.class final Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.core.domain.usecases.GetGamesForNonAuthScenarioImpl$loadVirtualGames$2"
    f = "GetGamesForNonAuthScenarioImpl.kt"
    l = {
        0x6e,
        0x87
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->u(IZLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Result<",
        "+",
        "Ljava/util/List<",
        "+",
        "Ld81/b;",
        ">;>;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00030\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "Lkotlin/Result;",
        "",
        "Ld81/b;",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)Lkotlin/Result;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $isForceUpdate:Z

.field final synthetic $limitLoadGames:I

.field private synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;IZLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;",
            "IZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    iput p2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->$limitLoadGames:I

    iput-boolean p3, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->$isForceUpdate:Z

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    iget v2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->$limitLoadGames:I

    iget-boolean v3, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->$isForceUpdate:Z

    invoke-direct {v0, v1, v2, v3, p2}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;IZLkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+",
            "Ljava/util/List<",
            "Ld81/b;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 19

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x0

    .line 10
    const/4 v4, 0x2

    .line 11
    const/4 v5, 0x1

    .line 12
    if-eqz v2, :cond_3

    .line 13
    .line 14
    if-eq v2, v5, :cond_1

    .line 15
    .line 16
    if-ne v2, v4, :cond_0

    .line 17
    .line 18
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    move-object/from16 v2, p1

    .line 22
    .line 23
    goto/16 :goto_4

    .line 24
    .line 25
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 26
    .line 27
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 28
    .line 29
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 30
    .line 31
    .line 32
    throw v1

    .line 33
    :cond_1
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->L$0:Ljava/lang/Object;

    .line 34
    .line 35
    check-cast v2, Lkotlinx/coroutines/N;

    .line 36
    .line 37
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 38
    .line 39
    .line 40
    move-object/from16 v5, p1

    .line 41
    .line 42
    :cond_2
    move-object v6, v2

    .line 43
    goto :goto_0

    .line 44
    :cond_3
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->L$0:Ljava/lang/Object;

    .line 48
    .line 49
    check-cast v2, Lkotlinx/coroutines/N;

    .line 50
    .line 51
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 52
    .line 53
    invoke-static {v6}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->b(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;)Lv81/j;

    .line 54
    .line 55
    .line 56
    move-result-object v6

    .line 57
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->L$0:Ljava/lang/Object;

    .line 58
    .line 59
    iput v5, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->label:I

    .line 60
    .line 61
    invoke-interface {v6, v0}, Lv81/j;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object v5

    .line 65
    if-ne v5, v1, :cond_2

    .line 66
    .line 67
    goto/16 :goto_3

    .line 68
    .line 69
    :goto_0
    check-cast v5, Lg81/c;

    .line 70
    .line 71
    invoke-virtual {v5}, Lg81/c;->c()Ljava/util/List;

    .line 72
    .line 73
    .line 74
    move-result-object v2

    .line 75
    new-instance v5, Ljava/util/ArrayList;

    .line 76
    .line 77
    invoke-direct {v5}, Ljava/util/ArrayList;-><init>()V

    .line 78
    .line 79
    .line 80
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    :cond_4
    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 85
    .line 86
    .line 87
    move-result v7

    .line 88
    if-eqz v7, :cond_6

    .line 89
    .line 90
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 91
    .line 92
    .line 93
    move-result-object v7

    .line 94
    move-object v8, v7

    .line 95
    check-cast v8, Lg81/b;

    .line 96
    .line 97
    invoke-virtual {v8}, Lg81/b;->l()J

    .line 98
    .line 99
    .line 100
    move-result-wide v9

    .line 101
    const-wide/16 v11, 0x2

    .line 102
    .line 103
    cmp-long v13, v9, v11

    .line 104
    .line 105
    if-eqz v13, :cond_5

    .line 106
    .line 107
    invoke-virtual {v8}, Lg81/b;->l()J

    .line 108
    .line 109
    .line 110
    move-result-wide v8

    .line 111
    const-wide/16 v10, 0x0

    .line 112
    .line 113
    cmp-long v12, v8, v10

    .line 114
    .line 115
    if-nez v12, :cond_4

    .line 116
    .line 117
    :cond_5
    invoke-interface {v5, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 118
    .line 119
    .line 120
    goto :goto_1

    .line 121
    :cond_6
    iget-object v14, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 122
    .line 123
    iget v2, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->$limitLoadGames:I

    .line 124
    .line 125
    iget-boolean v7, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->$isForceUpdate:Z

    .line 126
    .line 127
    new-instance v12, Ljava/util/ArrayList;

    .line 128
    .line 129
    const/16 v8, 0xa

    .line 130
    .line 131
    invoke-static {v5, v8}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 132
    .line 133
    .line 134
    move-result v8

    .line 135
    invoke-direct {v12, v8}, Ljava/util/ArrayList;-><init>(I)V

    .line 136
    .line 137
    .line 138
    invoke-interface {v5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 139
    .line 140
    .line 141
    move-result-object v5

    .line 142
    :goto_2
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    .line 143
    .line 144
    .line 145
    move-result v8

    .line 146
    if-eqz v8, :cond_7

    .line 147
    .line 148
    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 149
    .line 150
    .line 151
    move-result-object v8

    .line 152
    move-object v15, v8

    .line 153
    check-cast v15, Lg81/b;

    .line 154
    .line 155
    new-instance v9, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2$deferredCategoryList$2$1;

    .line 156
    .line 157
    const/16 v18, 0x0

    .line 158
    .line 159
    move/from16 v16, v2

    .line 160
    .line 161
    move/from16 v17, v7

    .line 162
    .line 163
    move-object v13, v9

    .line 164
    invoke-direct/range {v13 .. v18}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2$deferredCategoryList$2$1;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;Lg81/b;IZLkotlin/coroutines/e;)V

    .line 165
    .line 166
    .line 167
    const/4 v10, 0x3

    .line 168
    const/4 v11, 0x0

    .line 169
    const/4 v7, 0x0

    .line 170
    const/4 v8, 0x0

    .line 171
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->b(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/T;

    .line 172
    .line 173
    .line 174
    move-result-object v2

    .line 175
    invoke-interface {v12, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 176
    .line 177
    .line 178
    move/from16 v2, v16

    .line 179
    .line 180
    move/from16 v7, v17

    .line 181
    .line 182
    goto :goto_2

    .line 183
    :cond_7
    iput-object v3, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->L$0:Ljava/lang/Object;

    .line 184
    .line 185
    iput v4, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;->label:I

    .line 186
    .line 187
    invoke-static {v12, v0}, Lkotlinx/coroutines/AwaitKt;->a(Ljava/util/Collection;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 188
    .line 189
    .line 190
    move-result-object v2

    .line 191
    if-ne v2, v1, :cond_8

    .line 192
    .line 193
    :goto_3
    return-object v1

    .line 194
    :cond_8
    :goto_4
    check-cast v2, Ljava/util/List;

    .line 195
    .line 196
    invoke-static {v2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 197
    .line 198
    .line 199
    move-result v1

    .line 200
    if-eqz v1, :cond_9

    .line 201
    .line 202
    invoke-interface {v2}, Ljava/util/Collection;->isEmpty()Z

    .line 203
    .line 204
    .line 205
    move-result v1

    .line 206
    if-eqz v1, :cond_9

    .line 207
    .line 208
    goto :goto_7

    .line 209
    :cond_9
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 210
    .line 211
    .line 212
    move-result-object v1

    .line 213
    :cond_a
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 214
    .line 215
    .line 216
    move-result v4

    .line 217
    if-eqz v4, :cond_10

    .line 218
    .line 219
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 220
    .line 221
    .line 222
    move-result-object v4

    .line 223
    check-cast v4, Lkotlin/Result;

    .line 224
    .line 225
    invoke-virtual {v4}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 226
    .line 227
    .line 228
    move-result-object v4

    .line 229
    invoke-static {v4}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 230
    .line 231
    .line 232
    move-result v4

    .line 233
    if-nez v4, :cond_a

    .line 234
    .line 235
    new-instance v1, Ljava/util/ArrayList;

    .line 236
    .line 237
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 238
    .line 239
    .line 240
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 241
    .line 242
    .line 243
    move-result-object v2

    .line 244
    :cond_b
    :goto_5
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 245
    .line 246
    .line 247
    move-result v4

    .line 248
    if-eqz v4, :cond_d

    .line 249
    .line 250
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 251
    .line 252
    .line 253
    move-result-object v4

    .line 254
    check-cast v4, Lkotlin/Result;

    .line 255
    .line 256
    invoke-virtual {v4}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 257
    .line 258
    .line 259
    move-result-object v4

    .line 260
    invoke-static {v4}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 261
    .line 262
    .line 263
    move-result v5

    .line 264
    if-eqz v5, :cond_c

    .line 265
    .line 266
    move-object v4, v3

    .line 267
    :cond_c
    check-cast v4, Ld81/b;

    .line 268
    .line 269
    if-eqz v4, :cond_b

    .line 270
    .line 271
    invoke-interface {v1, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 272
    .line 273
    .line 274
    goto :goto_5

    .line 275
    :cond_d
    new-instance v2, Ljava/util/ArrayList;

    .line 276
    .line 277
    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 278
    .line 279
    .line 280
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 281
    .line 282
    .line 283
    move-result-object v1

    .line 284
    :cond_e
    :goto_6
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 285
    .line 286
    .line 287
    move-result v3

    .line 288
    if-eqz v3, :cond_f

    .line 289
    .line 290
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 291
    .line 292
    .line 293
    move-result-object v3

    .line 294
    move-object v4, v3

    .line 295
    check-cast v4, Ld81/b;

    .line 296
    .line 297
    invoke-virtual {v4}, Ld81/b;->c()Ljava/util/List;

    .line 298
    .line 299
    .line 300
    move-result-object v4

    .line 301
    invoke-interface {v4}, Ljava/util/Collection;->isEmpty()Z

    .line 302
    .line 303
    .line 304
    move-result v4

    .line 305
    if-nez v4, :cond_e

    .line 306
    .line 307
    invoke-interface {v2, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 308
    .line 309
    .line 310
    goto :goto_6

    .line 311
    :cond_f
    invoke-static {v2}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 312
    .line 313
    .line 314
    move-result-object v1

    .line 315
    goto :goto_8

    .line 316
    :cond_10
    :goto_7
    sget-object v1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 317
    .line 318
    invoke-static {v2}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 319
    .line 320
    .line 321
    move-result-object v1

    .line 322
    check-cast v1, Lkotlin/Result;

    .line 323
    .line 324
    if-eqz v1, :cond_11

    .line 325
    .line 326
    invoke-virtual {v1}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 327
    .line 328
    .line 329
    move-result-object v1

    .line 330
    invoke-static {v1}, Lkotlin/Result;->exceptionOrNull-impl(Ljava/lang/Object;)Ljava/lang/Throwable;

    .line 331
    .line 332
    .line 333
    move-result-object v1

    .line 334
    if-nez v1, :cond_12

    .line 335
    .line 336
    :cond_11
    new-instance v1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 337
    .line 338
    invoke-direct {v1}, Lcom/xbet/onexcore/data/model/ServerException;-><init>()V

    .line 339
    .line 340
    .line 341
    :cond_12
    invoke-static {v1}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 342
    .line 343
    .line 344
    move-result-object v1

    .line 345
    invoke-static {v1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 346
    .line 347
    .line 348
    move-result-object v1

    .line 349
    :goto_8
    invoke-static {v1}, Lkotlin/Result;->box-impl(Ljava/lang/Object;)Lkotlin/Result;

    .line 350
    .line 351
    .line 352
    move-result-object v1

    .line 353
    return-object v1
.end method
