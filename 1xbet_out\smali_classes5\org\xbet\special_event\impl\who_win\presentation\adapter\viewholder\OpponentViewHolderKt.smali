.class public final Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0004\u001a#\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u001a+\u0010\u000c\u001a\u00020\u000b*\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007j\u0002`\n2\u0006\u0010\u0001\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\r*$\u0008\u0000\u0010\u000e\"\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u00072\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007\u00a8\u0006\u000f"
    }
    d2 = {
        "LHy0/b;",
        "whoWinCardClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "(LHy0/b;)LA4/c;",
        "LB4/a;",
        "LIy0/f;",
        "LGq0/u1;",
        "Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolder;",
        "",
        "d",
        "(LB4/a;LHy0/b;)V",
        "OpponentViewHolder",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LHy0/b;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt;->g(LHy0/b;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/u1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/u1;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic c(LB4/a;LHy0/b;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt;->d(LB4/a;LHy0/b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final d(LB4/a;LHy0/b;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "LIy0/f;",
            "LGq0/u1;",
            ">;",
            "LHy0/b;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LGq0/u1;

    .line 6
    .line 7
    iget-object v0, v0, LGq0/u1;->b:Landroidx/compose/ui/platform/ComposeView;

    .line 8
    .line 9
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;

    .line 10
    .line 11
    invoke-direct {v1, p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$a;-><init>(LB4/a;LHy0/b;)V

    .line 12
    .line 13
    .line 14
    const p0, 0x4f5aaba2

    .line 15
    .line 16
    .line 17
    const/4 p1, 0x1

    .line 18
    invoke-static {p0, p1, v1}, Landroidx/compose/runtime/internal/b;->b(IZLjava/lang/Object;)Landroidx/compose/runtime/internal/a;

    .line 19
    .line 20
    .line 21
    move-result-object p0

    .line 22
    invoke-static {v0, p0}, LF11/j;->h(Landroidx/compose/ui/platform/ComposeView;Lkotlin/jvm/functions/Function2;)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public static final e(LHy0/b;)LA4/c;
    .locals 4
    .param p0    # LHy0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LHy0/b;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LJy0/n;

    .line 2
    .line 3
    invoke-direct {v0}, LJy0/n;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LJy0/o;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LJy0/o;-><init>(LHy0/b;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$opponentAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$opponentAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$opponentAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$opponentAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/u1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/u1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/u1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(LHy0/b;LB4/a;)Lkotlin/Unit;
    .locals 8

    .line 1
    invoke-virtual {p1}, LB4/a;->g()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget v1, LlZ0/h;->ic_glyph_language:I

    .line 6
    .line 7
    invoke-static {v0, v1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 8
    .line 9
    .line 10
    move-result-object v4

    .line 11
    invoke-virtual {p1}, LB4/a;->g()Landroid/content/Context;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    sget v1, LlZ0/d;->uikitSecondary40:I

    .line 16
    .line 17
    invoke-static {v4, v0, v1}, Lorg/xbet/ui_common/utils/ExtensionsKt;->d0(Landroid/graphics/drawable/Drawable;Landroid/content/Context;I)V

    .line 18
    .line 19
    .line 20
    new-instance v2, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;

    .line 21
    .line 22
    move-object v6, p1

    .line 23
    move-object v7, p0

    .line 24
    move-object v5, p0

    .line 25
    move-object v3, p1

    .line 26
    invoke-direct/range {v2 .. v7}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;-><init>(LB4/a;Landroid/graphics/drawable/Drawable;LHy0/b;LB4/a;LHy0/b;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {v3, v2}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 30
    .line 31
    .line 32
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 33
    .line 34
    return-object p0
.end method
