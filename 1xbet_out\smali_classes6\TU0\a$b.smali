.class public final LTU0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LTU0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LTU0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LTU0/a$b$f;,
        LTU0/a$b$d;,
        LTU0/a$b$c;,
        LTU0/a$b$g;,
        LTU0/a$b$b;,
        LTU0/a$b$i;,
        LTU0/a$b$j;,
        LTU0/a$b$a;,
        LTU0/a$b$h;,
        LTU0/a$b$e;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFU0/c;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFU0/a;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/core/data/repository/TotoBetTaxRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/core/domain/usecase/a;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LfX/b;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/core/domain/usecase/c;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/makebet/domain/usecase/h;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public I:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;",
            ">;"
        }
    .end annotation
.end field

.field public J:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/w;",
            ">;"
        }
    .end annotation
.end field

.field public K:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lek/f;",
            ">;"
        }
    .end annotation
.end field

.field public L:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lll/a;",
            ">;"
        }
    .end annotation
.end field

.field public M:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/b;",
            ">;"
        }
    .end annotation
.end field

.field public N:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/b;",
            ">;"
        }
    .end annotation
.end field

.field public O:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/c;",
            ">;"
        }
    .end annotation
.end field

.field public P:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lo9/a;",
            ">;"
        }
    .end annotation
.end field

.field public Q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lzg/a;",
            ">;"
        }
    .end annotation
.end field

.field public R:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/makebet/domain/usecase/e;",
            ">;"
        }
    .end annotation
.end field

.field public S:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public T:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public U:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/o;",
            ">;"
        }
    .end annotation
.end field

.field public V:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public W:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/makebet/domain/usecase/l;",
            ">;"
        }
    .end annotation
.end field

.field public X:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/m;",
            ">;"
        }
    .end annotation
.end field

.field public Y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public Z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LTZ0/a;

.field public final b:LzX0/k;

.field public final c:LAX0/b;

.field public final d:Lak/b;

.field public final e:LTU0/a$b;

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lek/d;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/l;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/profile/ProfileInteractor;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LPU0/b;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVV0/b;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVV0/a;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/makebet/domain/usecase/a;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/makebet/domain/usecase/j;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/makebet/domain/usecase/c;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/makebet/domain/usecase/p;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lgk/b;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/r;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/makebet/domain/usecase/n;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Ldk0/p;Lll/a;Lak/b;Lak/a;LAX0/b;LTZ0/a;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LxX0/a;Lo9/a;Lorg/xbet/ui_common/utils/M;LwX0/c;Lorg/xbet/analytics/domain/b;LrP/a;LVV0/b;LVV0/a;LHX0/e;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lc8/h;Li8/c;LzX0/k;LFU0/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;LfX/b;)V
    .locals 1

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LTU0/a$b;->e:LTU0/a$b;

    .line 4
    iput-object p7, p0, LTU0/a$b;->a:LTZ0/a;

    move-object/from16 v0, p23

    .line 5
    iput-object v0, p0, LTU0/a$b;->b:LzX0/k;

    .line 6
    iput-object p6, p0, LTU0/a$b;->c:LAX0/b;

    .line 7
    iput-object p4, p0, LTU0/a$b;->d:Lak/b;

    .line 8
    invoke-virtual/range {p0 .. p26}, LTU0/a$b;->e(LQW0/c;Ldk0/p;Lll/a;Lak/b;Lak/a;LAX0/b;LTZ0/a;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LxX0/a;Lo9/a;Lorg/xbet/ui_common/utils/M;LwX0/c;Lorg/xbet/analytics/domain/b;LrP/a;LVV0/b;LVV0/a;LHX0/e;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lc8/h;Li8/c;LzX0/k;LFU0/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;LfX/b;)V

    .line 9
    invoke-virtual/range {p0 .. p26}, LTU0/a$b;->f(LQW0/c;Ldk0/p;Lll/a;Lak/b;Lak/a;LAX0/b;LTZ0/a;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LxX0/a;Lo9/a;Lorg/xbet/ui_common/utils/M;LwX0/c;Lorg/xbet/analytics/domain/b;LrP/a;LVV0/b;LVV0/a;LHX0/e;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lc8/h;Li8/c;LzX0/k;LFU0/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;LfX/b;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Ldk0/p;Lll/a;Lak/b;Lak/a;LAX0/b;LTZ0/a;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LxX0/a;Lo9/a;Lorg/xbet/ui_common/utils/M;LwX0/c;Lorg/xbet/analytics/domain/b;LrP/a;LVV0/b;LVV0/a;LHX0/e;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lc8/h;Li8/c;LzX0/k;LFU0/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;LfX/b;LTU0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p26}, LTU0/a$b;-><init>(LQW0/c;Ldk0/p;Lll/a;Lak/b;Lak/a;LAX0/b;LTZ0/a;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LxX0/a;Lo9/a;Lorg/xbet/ui_common/utils/M;LwX0/c;Lorg/xbet/analytics/domain/b;LrP/a;LVV0/b;LVV0/a;LHX0/e;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lc8/h;Li8/c;LzX0/k;LFU0/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;LfX/b;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LTU0/a$b;->g(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LTU0/a$b;->h(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public c(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LTU0/a$b;->i(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public d(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LTU0/a$b;->j(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final e(LQW0/c;Ldk0/p;Lll/a;Lak/b;Lak/a;LAX0/b;LTZ0/a;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LxX0/a;Lo9/a;Lorg/xbet/ui_common/utils/M;LwX0/c;Lorg/xbet/analytics/domain/b;LrP/a;LVV0/b;LVV0/a;LHX0/e;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lc8/h;Li8/c;LzX0/k;LFU0/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;LfX/b;)V
    .locals 0

    .line 1
    invoke-static {p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    iput-object p2, p0, LTU0/a$b;->f:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    iput-object p2, p0, LTU0/a$b;->g:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static/range {p19 .. p19}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 14
    .line 15
    .line 16
    move-result-object p2

    .line 17
    iput-object p2, p0, LTU0/a$b;->h:Ldagger/internal/h;

    .line 18
    .line 19
    new-instance p2, LTU0/a$b$f;

    .line 20
    .line 21
    invoke-direct {p2, p5}, LTU0/a$b$f;-><init>(Lak/a;)V

    .line 22
    .line 23
    .line 24
    iput-object p2, p0, LTU0/a$b;->i:Ldagger/internal/h;

    .line 25
    .line 26
    new-instance p2, LTU0/a$b$d;

    .line 27
    .line 28
    invoke-direct {p2, p5}, LTU0/a$b$d;-><init>(Lak/a;)V

    .line 29
    .line 30
    .line 31
    iput-object p2, p0, LTU0/a$b;->j:Ldagger/internal/h;

    .line 32
    .line 33
    invoke-static/range {p20 .. p20}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 34
    .line 35
    .line 36
    move-result-object p2

    .line 37
    iput-object p2, p0, LTU0/a$b;->k:Ldagger/internal/h;

    .line 38
    .line 39
    invoke-static {p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 40
    .line 41
    .line 42
    move-result-object p2

    .line 43
    iput-object p2, p0, LTU0/a$b;->l:Ldagger/internal/h;

    .line 44
    .line 45
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 46
    .line 47
    .line 48
    move-result-object p2

    .line 49
    iput-object p2, p0, LTU0/a$b;->m:Ldagger/internal/h;

    .line 50
    .line 51
    invoke-static {p2}, LPU0/c;->a(LBc/a;)LPU0/c;

    .line 52
    .line 53
    .line 54
    move-result-object p2

    .line 55
    iput-object p2, p0, LTU0/a$b;->n:Ldagger/internal/h;

    .line 56
    .line 57
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 58
    .line 59
    .line 60
    move-result-object p2

    .line 61
    iput-object p2, p0, LTU0/a$b;->o:Ldagger/internal/h;

    .line 62
    .line 63
    invoke-static/range {p18 .. p18}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 64
    .line 65
    .line 66
    move-result-object p2

    .line 67
    iput-object p2, p0, LTU0/a$b;->p:Ldagger/internal/h;

    .line 68
    .line 69
    invoke-static/range {p21 .. p21}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 70
    .line 71
    .line 72
    move-result-object p2

    .line 73
    iput-object p2, p0, LTU0/a$b;->q:Ldagger/internal/h;

    .line 74
    .line 75
    iget-object p3, p0, LTU0/a$b;->l:Ldagger/internal/h;

    .line 76
    .line 77
    iget-object p4, p0, LTU0/a$b;->n:Ldagger/internal/h;

    .line 78
    .line 79
    iget-object p6, p0, LTU0/a$b;->o:Ldagger/internal/h;

    .line 80
    .line 81
    iget-object p7, p0, LTU0/a$b;->p:Ldagger/internal/h;

    .line 82
    .line 83
    invoke-static {p3, p4, p6, p7, p2}, Lorg/xbet/toto_bet/makebet/data/repository/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_bet/makebet/data/repository/a;

    .line 84
    .line 85
    .line 86
    move-result-object p2

    .line 87
    iput-object p2, p0, LTU0/a$b;->r:Ldagger/internal/h;

    .line 88
    .line 89
    invoke-static {p2}, Lorg/xbet/toto_bet/makebet/domain/usecase/b;->a(LBc/a;)Lorg/xbet/toto_bet/makebet/domain/usecase/b;

    .line 90
    .line 91
    .line 92
    move-result-object p2

    .line 93
    iput-object p2, p0, LTU0/a$b;->s:Ldagger/internal/h;

    .line 94
    .line 95
    iget-object p2, p0, LTU0/a$b;->r:Ldagger/internal/h;

    .line 96
    .line 97
    invoke-static {p2}, Lorg/xbet/toto_bet/makebet/domain/usecase/k;->a(LBc/a;)Lorg/xbet/toto_bet/makebet/domain/usecase/k;

    .line 98
    .line 99
    .line 100
    move-result-object p2

    .line 101
    iput-object p2, p0, LTU0/a$b;->t:Ldagger/internal/h;

    .line 102
    .line 103
    iget-object p3, p0, LTU0/a$b;->r:Ldagger/internal/h;

    .line 104
    .line 105
    invoke-static {p3, p2}, Lorg/xbet/toto_bet/makebet/domain/usecase/d;->a(LBc/a;LBc/a;)Lorg/xbet/toto_bet/makebet/domain/usecase/d;

    .line 106
    .line 107
    .line 108
    move-result-object p2

    .line 109
    iput-object p2, p0, LTU0/a$b;->u:Ldagger/internal/h;

    .line 110
    .line 111
    iget-object p2, p0, LTU0/a$b;->r:Ldagger/internal/h;

    .line 112
    .line 113
    invoke-static {p2}, Lorg/xbet/toto_bet/makebet/domain/usecase/q;->a(LBc/a;)Lorg/xbet/toto_bet/makebet/domain/usecase/q;

    .line 114
    .line 115
    .line 116
    move-result-object p2

    .line 117
    iput-object p2, p0, LTU0/a$b;->v:Ldagger/internal/h;

    .line 118
    .line 119
    new-instance p2, LTU0/a$b$c;

    .line 120
    .line 121
    invoke-direct {p2, p5}, LTU0/a$b$c;-><init>(Lak/a;)V

    .line 122
    .line 123
    .line 124
    iput-object p2, p0, LTU0/a$b;->w:Ldagger/internal/h;

    .line 125
    .line 126
    new-instance p2, LTU0/a$b$g;

    .line 127
    .line 128
    invoke-direct {p2, p5}, LTU0/a$b$g;-><init>(Lak/a;)V

    .line 129
    .line 130
    .line 131
    iput-object p2, p0, LTU0/a$b;->x:Ldagger/internal/h;

    .line 132
    .line 133
    iget-object p2, p0, LTU0/a$b;->r:Ldagger/internal/h;

    .line 134
    .line 135
    invoke-static {p2}, Lorg/xbet/toto_bet/makebet/domain/usecase/o;->a(LBc/a;)Lorg/xbet/toto_bet/makebet/domain/usecase/o;

    .line 136
    .line 137
    .line 138
    move-result-object p2

    .line 139
    iput-object p2, p0, LTU0/a$b;->y:Ldagger/internal/h;

    .line 140
    .line 141
    new-instance p2, LTU0/a$b$b;

    .line 142
    .line 143
    invoke-direct {p2, p1}, LTU0/a$b$b;-><init>(LQW0/c;)V

    .line 144
    .line 145
    .line 146
    iput-object p2, p0, LTU0/a$b;->z:Ldagger/internal/h;

    .line 147
    .line 148
    iget-object p1, p0, LTU0/a$b;->m:Ldagger/internal/h;

    .line 149
    .line 150
    invoke-static {p1}, LFU0/d;->a(LBc/a;)LFU0/d;

    .line 151
    .line 152
    .line 153
    move-result-object p1

    .line 154
    iput-object p1, p0, LTU0/a$b;->A:Ldagger/internal/h;

    .line 155
    .line 156
    invoke-static/range {p24 .. p24}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 157
    .line 158
    .line 159
    move-result-object p1

    .line 160
    iput-object p1, p0, LTU0/a$b;->B:Ldagger/internal/h;

    .line 161
    .line 162
    iget-object p2, p0, LTU0/a$b;->A:Ldagger/internal/h;

    .line 163
    .line 164
    iget-object p3, p0, LTU0/a$b;->q:Ldagger/internal/h;

    .line 165
    .line 166
    invoke-static {p2, p1, p3}, Lorg/xbet/toto_bet/core/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_bet/core/data/repository/a;

    .line 167
    .line 168
    .line 169
    move-result-object p1

    .line 170
    iput-object p1, p0, LTU0/a$b;->C:Ldagger/internal/h;

    .line 171
    .line 172
    invoke-static {p1}, Lorg/xbet/toto_bet/core/domain/usecase/b;->a(LBc/a;)Lorg/xbet/toto_bet/core/domain/usecase/b;

    .line 173
    .line 174
    .line 175
    move-result-object p1

    .line 176
    iput-object p1, p0, LTU0/a$b;->D:Ldagger/internal/h;

    .line 177
    .line 178
    return-void
.end method

.method public final f(LQW0/c;Ldk0/p;Lll/a;Lak/b;Lak/a;LAX0/b;LTZ0/a;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LxX0/a;Lo9/a;Lorg/xbet/ui_common/utils/M;LwX0/c;Lorg/xbet/analytics/domain/b;LrP/a;LVV0/b;LVV0/a;LHX0/e;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lc8/h;Li8/c;LzX0/k;LFU0/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;LfX/b;)V
    .locals 27

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p5

    .line 4
    .line 5
    invoke-static/range {p26 .. p26}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    iput-object v2, v0, LTU0/a$b;->E:Ldagger/internal/h;

    .line 10
    .line 11
    invoke-static {v2}, Lorg/xbet/toto_bet/core/domain/usecase/d;->a(LBc/a;)Lorg/xbet/toto_bet/core/domain/usecase/d;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    iput-object v2, v0, LTU0/a$b;->F:Ldagger/internal/h;

    .line 16
    .line 17
    iget-object v2, v0, LTU0/a$b;->r:Ldagger/internal/h;

    .line 18
    .line 19
    invoke-static {v2}, Lorg/xbet/toto_bet/makebet/domain/usecase/i;->a(LBc/a;)Lorg/xbet/toto_bet/makebet/domain/usecase/i;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    iput-object v2, v0, LTU0/a$b;->G:Ldagger/internal/h;

    .line 24
    .line 25
    invoke-static/range {p25 .. p25}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    iput-object v2, v0, LTU0/a$b;->H:Ldagger/internal/h;

    .line 30
    .line 31
    iget-object v3, v0, LTU0/a$b;->G:Ldagger/internal/h;

    .line 32
    .line 33
    invoke-static {v3, v2}, Lorg/xbet/toto_bet/makebet/domain/usecase/g;->a(LBc/a;LBc/a;)Lorg/xbet/toto_bet/makebet/domain/usecase/g;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    iput-object v2, v0, LTU0/a$b;->I:Ldagger/internal/h;

    .line 38
    .line 39
    new-instance v2, LTU0/a$b$i;

    .line 40
    .line 41
    invoke-direct {v2, v1}, LTU0/a$b$i;-><init>(Lak/a;)V

    .line 42
    .line 43
    .line 44
    iput-object v2, v0, LTU0/a$b;->J:Ldagger/internal/h;

    .line 45
    .line 46
    new-instance v2, LTU0/a$b$j;

    .line 47
    .line 48
    invoke-direct {v2, v1}, LTU0/a$b$j;-><init>(Lak/a;)V

    .line 49
    .line 50
    .line 51
    iput-object v2, v0, LTU0/a$b;->K:Ldagger/internal/h;

    .line 52
    .line 53
    invoke-static/range {p3 .. p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 54
    .line 55
    .line 56
    move-result-object v2

    .line 57
    iput-object v2, v0, LTU0/a$b;->L:Ldagger/internal/h;

    .line 58
    .line 59
    new-instance v2, LTU0/a$b$a;

    .line 60
    .line 61
    invoke-direct {v2, v1}, LTU0/a$b$a;-><init>(Lak/a;)V

    .line 62
    .line 63
    .line 64
    iput-object v2, v0, LTU0/a$b;->M:Ldagger/internal/h;

    .line 65
    .line 66
    invoke-static/range {p15 .. p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    iput-object v2, v0, LTU0/a$b;->N:Ldagger/internal/h;

    .line 71
    .line 72
    invoke-static/range {p22 .. p22}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 73
    .line 74
    .line 75
    move-result-object v2

    .line 76
    iput-object v2, v0, LTU0/a$b;->O:Ldagger/internal/h;

    .line 77
    .line 78
    invoke-static/range {p12 .. p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 79
    .line 80
    .line 81
    move-result-object v2

    .line 82
    iput-object v2, v0, LTU0/a$b;->P:Ldagger/internal/h;

    .line 83
    .line 84
    iget-object v3, v0, LTU0/a$b;->N:Ldagger/internal/h;

    .line 85
    .line 86
    iget-object v4, v0, LTU0/a$b;->q:Ldagger/internal/h;

    .line 87
    .line 88
    iget-object v5, v0, LTU0/a$b;->O:Ldagger/internal/h;

    .line 89
    .line 90
    invoke-static {v3, v4, v5, v2}, Lzg/b;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lzg/b;

    .line 91
    .line 92
    .line 93
    move-result-object v2

    .line 94
    iput-object v2, v0, LTU0/a$b;->Q:Ldagger/internal/h;

    .line 95
    .line 96
    iget-object v2, v0, LTU0/a$b;->r:Ldagger/internal/h;

    .line 97
    .line 98
    invoke-static {v2}, Lorg/xbet/toto_bet/makebet/domain/usecase/f;->a(LBc/a;)Lorg/xbet/toto_bet/makebet/domain/usecase/f;

    .line 99
    .line 100
    .line 101
    move-result-object v2

    .line 102
    iput-object v2, v0, LTU0/a$b;->R:Ldagger/internal/h;

    .line 103
    .line 104
    iget-object v3, v0, LTU0/a$b;->f:Ldagger/internal/h;

    .line 105
    .line 106
    iget-object v4, v0, LTU0/a$b;->g:Ldagger/internal/h;

    .line 107
    .line 108
    iget-object v5, v0, LTU0/a$b;->h:Ldagger/internal/h;

    .line 109
    .line 110
    iget-object v6, v0, LTU0/a$b;->i:Ldagger/internal/h;

    .line 111
    .line 112
    iget-object v7, v0, LTU0/a$b;->j:Ldagger/internal/h;

    .line 113
    .line 114
    iget-object v8, v0, LTU0/a$b;->k:Ldagger/internal/h;

    .line 115
    .line 116
    iget-object v9, v0, LTU0/a$b;->s:Ldagger/internal/h;

    .line 117
    .line 118
    iget-object v10, v0, LTU0/a$b;->u:Ldagger/internal/h;

    .line 119
    .line 120
    iget-object v11, v0, LTU0/a$b;->v:Ldagger/internal/h;

    .line 121
    .line 122
    iget-object v12, v0, LTU0/a$b;->w:Ldagger/internal/h;

    .line 123
    .line 124
    iget-object v13, v0, LTU0/a$b;->x:Ldagger/internal/h;

    .line 125
    .line 126
    iget-object v14, v0, LTU0/a$b;->y:Ldagger/internal/h;

    .line 127
    .line 128
    iget-object v15, v0, LTU0/a$b;->z:Ldagger/internal/h;

    .line 129
    .line 130
    move-object/from16 v24, v2

    .line 131
    .line 132
    iget-object v2, v0, LTU0/a$b;->D:Ldagger/internal/h;

    .line 133
    .line 134
    move-object/from16 v16, v2

    .line 135
    .line 136
    iget-object v2, v0, LTU0/a$b;->F:Ldagger/internal/h;

    .line 137
    .line 138
    move-object/from16 v17, v2

    .line 139
    .line 140
    iget-object v2, v0, LTU0/a$b;->I:Ldagger/internal/h;

    .line 141
    .line 142
    move-object/from16 v18, v2

    .line 143
    .line 144
    iget-object v2, v0, LTU0/a$b;->J:Ldagger/internal/h;

    .line 145
    .line 146
    move-object/from16 v19, v2

    .line 147
    .line 148
    iget-object v2, v0, LTU0/a$b;->K:Ldagger/internal/h;

    .line 149
    .line 150
    move-object/from16 v20, v2

    .line 151
    .line 152
    iget-object v2, v0, LTU0/a$b;->L:Ldagger/internal/h;

    .line 153
    .line 154
    move-object/from16 v21, v2

    .line 155
    .line 156
    iget-object v2, v0, LTU0/a$b;->M:Ldagger/internal/h;

    .line 157
    .line 158
    move-object/from16 v22, v2

    .line 159
    .line 160
    iget-object v2, v0, LTU0/a$b;->Q:Ldagger/internal/h;

    .line 161
    .line 162
    move-object/from16 v23, v2

    .line 163
    .line 164
    invoke-static/range {v3 .. v24}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/l;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/l;

    .line 165
    .line 166
    .line 167
    move-result-object v2

    .line 168
    iput-object v2, v0, LTU0/a$b;->S:Ldagger/internal/h;

    .line 169
    .line 170
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 171
    .line 172
    .line 173
    move-result-object v2

    .line 174
    iput-object v2, v0, LTU0/a$b;->T:Ldagger/internal/h;

    .line 175
    .line 176
    new-instance v2, LTU0/a$b$h;

    .line 177
    .line 178
    invoke-direct {v2, v1}, LTU0/a$b$h;-><init>(Lak/a;)V

    .line 179
    .line 180
    .line 181
    iput-object v2, v0, LTU0/a$b;->U:Ldagger/internal/h;

    .line 182
    .line 183
    iget-object v3, v0, LTU0/a$b;->f:Ldagger/internal/h;

    .line 184
    .line 185
    iget-object v4, v0, LTU0/a$b;->g:Ldagger/internal/h;

    .line 186
    .line 187
    iget-object v5, v0, LTU0/a$b;->h:Ldagger/internal/h;

    .line 188
    .line 189
    iget-object v6, v0, LTU0/a$b;->i:Ldagger/internal/h;

    .line 190
    .line 191
    iget-object v7, v0, LTU0/a$b;->j:Ldagger/internal/h;

    .line 192
    .line 193
    iget-object v8, v0, LTU0/a$b;->k:Ldagger/internal/h;

    .line 194
    .line 195
    iget-object v9, v0, LTU0/a$b;->s:Ldagger/internal/h;

    .line 196
    .line 197
    iget-object v10, v0, LTU0/a$b;->u:Ldagger/internal/h;

    .line 198
    .line 199
    iget-object v11, v0, LTU0/a$b;->v:Ldagger/internal/h;

    .line 200
    .line 201
    iget-object v12, v0, LTU0/a$b;->y:Ldagger/internal/h;

    .line 202
    .line 203
    iget-object v13, v0, LTU0/a$b;->z:Ldagger/internal/h;

    .line 204
    .line 205
    iget-object v14, v0, LTU0/a$b;->L:Ldagger/internal/h;

    .line 206
    .line 207
    iget-object v15, v0, LTU0/a$b;->J:Ldagger/internal/h;

    .line 208
    .line 209
    move-object/from16 v23, v2

    .line 210
    .line 211
    iget-object v2, v0, LTU0/a$b;->K:Ldagger/internal/h;

    .line 212
    .line 213
    move-object/from16 v16, v2

    .line 214
    .line 215
    iget-object v2, v0, LTU0/a$b;->x:Ldagger/internal/h;

    .line 216
    .line 217
    move-object/from16 v17, v2

    .line 218
    .line 219
    iget-object v2, v0, LTU0/a$b;->M:Ldagger/internal/h;

    .line 220
    .line 221
    move-object/from16 v18, v2

    .line 222
    .line 223
    iget-object v2, v0, LTU0/a$b;->Q:Ldagger/internal/h;

    .line 224
    .line 225
    move-object/from16 v19, v2

    .line 226
    .line 227
    iget-object v2, v0, LTU0/a$b;->w:Ldagger/internal/h;

    .line 228
    .line 229
    move-object/from16 v20, v2

    .line 230
    .line 231
    iget-object v2, v0, LTU0/a$b;->R:Ldagger/internal/h;

    .line 232
    .line 233
    move-object/from16 v21, v2

    .line 234
    .line 235
    iget-object v2, v0, LTU0/a$b;->T:Ldagger/internal/h;

    .line 236
    .line 237
    move-object/from16 v22, v2

    .line 238
    .line 239
    iget-object v2, v0, LTU0/a$b;->D:Ldagger/internal/h;

    .line 240
    .line 241
    move-object/from16 v24, v2

    .line 242
    .line 243
    iget-object v2, v0, LTU0/a$b;->I:Ldagger/internal/h;

    .line 244
    .line 245
    move-object/from16 v25, v2

    .line 246
    .line 247
    iget-object v2, v0, LTU0/a$b;->F:Ldagger/internal/h;

    .line 248
    .line 249
    move-object/from16 v26, v2

    .line 250
    .line 251
    invoke-static/range {v3 .. v26}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/c;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/c;

    .line 252
    .line 253
    .line 254
    move-result-object v2

    .line 255
    iput-object v2, v0, LTU0/a$b;->V:Ldagger/internal/h;

    .line 256
    .line 257
    iget-object v2, v0, LTU0/a$b;->r:Ldagger/internal/h;

    .line 258
    .line 259
    invoke-static {v2}, Lorg/xbet/toto_bet/makebet/domain/usecase/m;->a(LBc/a;)Lorg/xbet/toto_bet/makebet/domain/usecase/m;

    .line 260
    .line 261
    .line 262
    move-result-object v2

    .line 263
    iput-object v2, v0, LTU0/a$b;->W:Ldagger/internal/h;

    .line 264
    .line 265
    new-instance v2, LTU0/a$b$e;

    .line 266
    .line 267
    invoke-direct {v2, v1}, LTU0/a$b$e;-><init>(Lak/a;)V

    .line 268
    .line 269
    .line 270
    iput-object v2, v0, LTU0/a$b;->X:Ldagger/internal/h;

    .line 271
    .line 272
    iget-object v1, v0, LTU0/a$b;->W:Ldagger/internal/h;

    .line 273
    .line 274
    iget-object v3, v0, LTU0/a$b;->z:Ldagger/internal/h;

    .line 275
    .line 276
    iget-object v4, v0, LTU0/a$b;->h:Ldagger/internal/h;

    .line 277
    .line 278
    iget-object v5, v0, LTU0/a$b;->x:Ldagger/internal/h;

    .line 279
    .line 280
    iget-object v6, v0, LTU0/a$b;->R:Ldagger/internal/h;

    .line 281
    .line 282
    iget-object v7, v0, LTU0/a$b;->T:Ldagger/internal/h;

    .line 283
    .line 284
    move-object/from16 p1, v1

    .line 285
    .line 286
    move-object/from16 p4, v2

    .line 287
    .line 288
    move-object/from16 p2, v3

    .line 289
    .line 290
    move-object/from16 p3, v4

    .line 291
    .line 292
    move-object/from16 p5, v5

    .line 293
    .line 294
    move-object/from16 p6, v6

    .line 295
    .line 296
    move-object/from16 p7, v7

    .line 297
    .line 298
    invoke-static/range {p1 .. p7}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/c;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/c;

    .line 299
    .line 300
    .line 301
    move-result-object v1

    .line 302
    iput-object v1, v0, LTU0/a$b;->Y:Ldagger/internal/h;

    .line 303
    .line 304
    iget-object v1, v0, LTU0/a$b;->W:Ldagger/internal/h;

    .line 305
    .line 306
    iget-object v2, v0, LTU0/a$b;->h:Ldagger/internal/h;

    .line 307
    .line 308
    iget-object v3, v0, LTU0/a$b;->X:Ldagger/internal/h;

    .line 309
    .line 310
    iget-object v4, v0, LTU0/a$b;->R:Ldagger/internal/h;

    .line 311
    .line 312
    invoke-static {v1, v2, v3, v4}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/e;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/e;

    .line 313
    .line 314
    .line 315
    move-result-object v1

    .line 316
    iput-object v1, v0, LTU0/a$b;->Z:Ldagger/internal/h;

    .line 317
    .line 318
    return-void
.end method

.method public final g(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LTU0/a$b;->l()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/d;->c(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LTU0/a$b;->b:LzX0/k;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/d;->a(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;LzX0/k;)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, LTU0/a$b;->c:LAX0/b;

    .line 14
    .line 15
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/d;->b(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;LAX0/b;)V

    .line 16
    .line 17
    .line 18
    return-object p1
.end method

.method public final h(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LTU0/a$b;->l()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/g;->c(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LTU0/a$b;->b:LzX0/k;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/g;->a(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;LzX0/k;)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, LTU0/a$b;->c:LAX0/b;

    .line 14
    .line 15
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/g;->b(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;LAX0/b;)V

    .line 16
    .line 17
    .line 18
    return-object p1
.end method

.method public final i(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LTU0/a$b;->l()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/g;->e(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LTU0/a$b;->a:LTZ0/a;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/g;->a(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;LTZ0/a;)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, LTU0/a$b;->b:LzX0/k;

    .line 14
    .line 15
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/g;->c(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;LzX0/k;)V

    .line 16
    .line 17
    .line 18
    iget-object v0, p0, LTU0/a$b;->d:Lak/b;

    .line 19
    .line 20
    invoke-interface {v0}, Lak/b;->a()Lck/a;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    check-cast v0, Lck/a;

    .line 29
    .line 30
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/g;->b(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;Lck/a;)V

    .line 31
    .line 32
    .line 33
    iget-object v0, p0, LTU0/a$b;->c:LAX0/b;

    .line 34
    .line 35
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/g;->d(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;LAX0/b;)V

    .line 36
    .line 37
    .line 38
    return-object p1
.end method

.method public final j(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LTU0/a$b;->l()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/p;->e(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LTU0/a$b;->a:LTZ0/a;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/p;->a(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;LTZ0/a;)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, LTU0/a$b;->b:LzX0/k;

    .line 14
    .line 15
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/p;->c(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;LzX0/k;)V

    .line 16
    .line 17
    .line 18
    iget-object v0, p0, LTU0/a$b;->c:LAX0/b;

    .line 19
    .line 20
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/p;->d(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;LAX0/b;)V

    .line 21
    .line 22
    .line 23
    iget-object v0, p0, LTU0/a$b;->d:Lak/b;

    .line 24
    .line 25
    invoke-interface {v0}, Lak/b;->a()Lck/a;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, Lck/a;

    .line 34
    .line 35
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/p;->b(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Lck/a;)V

    .line 36
    .line 37
    .line 38
    return-object p1
.end method

.method public final k()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x4

    .line 2
    invoke-static {v0}, Ldagger/internal/f;->b(I)Ldagger/internal/f;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    const-class v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 7
    .line 8
    iget-object v2, p0, LTU0/a$b;->S:Ldagger/internal/h;

    .line 9
    .line 10
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    const-class v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 15
    .line 16
    iget-object v2, p0, LTU0/a$b;->V:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    const-class v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 23
    .line 24
    iget-object v2, p0, LTU0/a$b;->Y:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    const-class v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;

    .line 31
    .line 32
    iget-object v2, p0, LTU0/a$b;->Z:Ldagger/internal/h;

    .line 33
    .line 34
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    invoke-virtual {v0}, Ldagger/internal/f;->a()Ljava/util/Map;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    return-object v0
.end method

.method public final l()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LTU0/a$b;->k()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
