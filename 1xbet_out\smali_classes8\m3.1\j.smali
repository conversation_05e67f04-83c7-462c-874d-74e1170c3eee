.class public Lm3/j;
.super Lm3/g;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lm3/g<",
        "Landroid/graphics/PointF;",
        ">;"
    }
.end annotation


# instance fields
.field public final i:Landroid/graphics/PointF;

.field public final j:[F

.field public final k:[F

.field public final l:Landroid/graphics/PathMeasure;

.field public m:Lm3/i;


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lv3/a<",
            "Landroid/graphics/PointF;",
            ">;>;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1}, Lm3/g;-><init>(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    new-instance p1, Landroid/graphics/PointF;

    .line 5
    .line 6
    invoke-direct {p1}, Landroid/graphics/PointF;-><init>()V

    .line 7
    .line 8
    .line 9
    iput-object p1, p0, Lm3/j;->i:Landroid/graphics/PointF;

    .line 10
    .line 11
    const/4 p1, 0x2

    .line 12
    new-array v0, p1, [F

    .line 13
    .line 14
    iput-object v0, p0, Lm3/j;->j:[F

    .line 15
    .line 16
    new-array p1, p1, [F

    .line 17
    .line 18
    iput-object p1, p0, Lm3/j;->k:[F

    .line 19
    .line 20
    new-instance p1, Landroid/graphics/PathMeasure;

    .line 21
    .line 22
    invoke-direct {p1}, Landroid/graphics/PathMeasure;-><init>()V

    .line 23
    .line 24
    .line 25
    iput-object p1, p0, Lm3/j;->l:Landroid/graphics/PathMeasure;

    .line 26
    .line 27
    return-void
.end method


# virtual methods
.method public bridge synthetic i(Lv3/a;F)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lm3/j;->q(Lv3/a;F)Landroid/graphics/PointF;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public q(Lv3/a;F)Landroid/graphics/PointF;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lv3/a<",
            "Landroid/graphics/PointF;",
            ">;F)",
            "Landroid/graphics/PointF;"
        }
    .end annotation

    .line 1
    move-object v0, p1

    .line 2
    check-cast v0, Lm3/i;

    .line 3
    .line 4
    invoke-virtual {v0}, Lm3/i;->k()Landroid/graphics/Path;

    .line 5
    .line 6
    .line 7
    move-result-object v1

    .line 8
    if-nez v1, :cond_0

    .line 9
    .line 10
    iget-object p1, p1, Lv3/a;->b:Ljava/lang/Object;

    .line 11
    .line 12
    check-cast p1, Landroid/graphics/PointF;

    .line 13
    .line 14
    return-object p1

    .line 15
    :cond_0
    iget-object v2, p0, Lm3/a;->e:Lv3/c;

    .line 16
    .line 17
    if-eqz v2, :cond_1

    .line 18
    .line 19
    iget v3, v0, Lv3/a;->g:F

    .line 20
    .line 21
    iget-object p1, v0, Lv3/a;->h:Ljava/lang/Float;

    .line 22
    .line 23
    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    .line 24
    .line 25
    .line 26
    move-result v4

    .line 27
    iget-object p1, v0, Lv3/a;->b:Ljava/lang/Object;

    .line 28
    .line 29
    move-object v5, p1

    .line 30
    check-cast v5, Landroid/graphics/PointF;

    .line 31
    .line 32
    iget-object p1, v0, Lv3/a;->c:Ljava/lang/Object;

    .line 33
    .line 34
    move-object v6, p1

    .line 35
    check-cast v6, Landroid/graphics/PointF;

    .line 36
    .line 37
    invoke-virtual {p0}, Lm3/a;->e()F

    .line 38
    .line 39
    .line 40
    move-result v7

    .line 41
    invoke-virtual {p0}, Lm3/a;->f()F

    .line 42
    .line 43
    .line 44
    move-result v9

    .line 45
    move v8, p2

    .line 46
    invoke-virtual/range {v2 .. v9}, Lv3/c;->b(FFLjava/lang/Object;Ljava/lang/Object;FFF)Ljava/lang/Object;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    check-cast p1, Landroid/graphics/PointF;

    .line 51
    .line 52
    if-eqz p1, :cond_2

    .line 53
    .line 54
    return-object p1

    .line 55
    :cond_1
    move v8, p2

    .line 56
    :cond_2
    iget-object p1, p0, Lm3/j;->m:Lm3/i;

    .line 57
    .line 58
    const/4 p2, 0x0

    .line 59
    if-eq p1, v0, :cond_3

    .line 60
    .line 61
    iget-object p1, p0, Lm3/j;->l:Landroid/graphics/PathMeasure;

    .line 62
    .line 63
    invoke-virtual {p1, v1, p2}, Landroid/graphics/PathMeasure;->setPath(Landroid/graphics/Path;Z)V

    .line 64
    .line 65
    .line 66
    iput-object v0, p0, Lm3/j;->m:Lm3/i;

    .line 67
    .line 68
    :cond_3
    iget-object p1, p0, Lm3/j;->l:Landroid/graphics/PathMeasure;

    .line 69
    .line 70
    invoke-virtual {p1}, Landroid/graphics/PathMeasure;->getLength()F

    .line 71
    .line 72
    .line 73
    move-result p1

    .line 74
    mul-float v0, v8, p1

    .line 75
    .line 76
    iget-object v1, p0, Lm3/j;->l:Landroid/graphics/PathMeasure;

    .line 77
    .line 78
    iget-object v2, p0, Lm3/j;->j:[F

    .line 79
    .line 80
    iget-object v3, p0, Lm3/j;->k:[F

    .line 81
    .line 82
    invoke-virtual {v1, v0, v2, v3}, Landroid/graphics/PathMeasure;->getPosTan(F[F[F)Z

    .line 83
    .line 84
    .line 85
    iget-object v1, p0, Lm3/j;->i:Landroid/graphics/PointF;

    .line 86
    .line 87
    iget-object v2, p0, Lm3/j;->j:[F

    .line 88
    .line 89
    aget v3, v2, p2

    .line 90
    .line 91
    const/4 v4, 0x1

    .line 92
    aget v2, v2, v4

    .line 93
    .line 94
    invoke-virtual {v1, v3, v2}, Landroid/graphics/PointF;->set(FF)V

    .line 95
    .line 96
    .line 97
    const/4 v1, 0x0

    .line 98
    cmpg-float v1, v0, v1

    .line 99
    .line 100
    if-gez v1, :cond_4

    .line 101
    .line 102
    iget-object p1, p0, Lm3/j;->i:Landroid/graphics/PointF;

    .line 103
    .line 104
    iget-object v1, p0, Lm3/j;->k:[F

    .line 105
    .line 106
    aget p2, v1, p2

    .line 107
    .line 108
    mul-float p2, p2, v0

    .line 109
    .line 110
    aget v1, v1, v4

    .line 111
    .line 112
    mul-float v1, v1, v0

    .line 113
    .line 114
    invoke-virtual {p1, p2, v1}, Landroid/graphics/PointF;->offset(FF)V

    .line 115
    .line 116
    .line 117
    goto :goto_0

    .line 118
    :cond_4
    cmpl-float v1, v0, p1

    .line 119
    .line 120
    if-lez v1, :cond_5

    .line 121
    .line 122
    iget-object v1, p0, Lm3/j;->i:Landroid/graphics/PointF;

    .line 123
    .line 124
    iget-object v2, p0, Lm3/j;->k:[F

    .line 125
    .line 126
    aget p2, v2, p2

    .line 127
    .line 128
    sub-float/2addr v0, p1

    .line 129
    mul-float p2, p2, v0

    .line 130
    .line 131
    aget p1, v2, v4

    .line 132
    .line 133
    mul-float p1, p1, v0

    .line 134
    .line 135
    invoke-virtual {v1, p2, p1}, Landroid/graphics/PointF;->offset(FF)V

    .line 136
    .line 137
    .line 138
    :cond_5
    :goto_0
    iget-object p1, p0, Lm3/j;->i:Landroid/graphics/PointF;

    .line 139
    .line 140
    return-object p1
.end method
