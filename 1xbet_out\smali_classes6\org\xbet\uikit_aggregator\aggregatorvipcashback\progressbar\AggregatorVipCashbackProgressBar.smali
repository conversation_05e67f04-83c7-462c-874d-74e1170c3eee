.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements Lg31/c;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u000e\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0010\r\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\t\n\u0002\u0008\u000e\u0008\u0001\u0018\u0000 X2\u00020\u00012\u00020\u0002:\u00013B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0003\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ!\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\u0004\u001a\u00020\u00032\u0008\u0010\u0006\u001a\u0004\u0018\u00010\u0005H\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u000f\u0010\u000e\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ/\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\u0010\u001a\u00020\u00072\u0006\u0010\u0011\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u00072\u0006\u0010\u0013\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u001f\u0010\u0018\u001a\u00020\u000b2\u0006\u0010\u0016\u001a\u00020\u00072\u0006\u0010\u0017\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J7\u0010 \u001a\u00020\u000b2\u0006\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001c\u001a\u00020\u00072\u0006\u0010\u001d\u001a\u00020\u00072\u0006\u0010\u001e\u001a\u00020\u00072\u0006\u0010\u001f\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008 \u0010!J\r\u0010#\u001a\u00020\"\u00a2\u0006\u0004\u0008#\u0010$J\r\u0010&\u001a\u00020%\u00a2\u0006\u0004\u0008&\u0010\'J\r\u0010(\u001a\u00020%\u00a2\u0006\u0004\u0008(\u0010\'J\u0017\u0010\u0011\u001a\u00020\u000b2\u0006\u0010)\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0011\u0010*J\u0017\u0010+\u001a\u00020\u000b2\u0006\u0010)\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008+\u0010*J\u0017\u0010,\u001a\u00020\u000b2\u0006\u0010)\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008,\u0010*J\u000f\u0010-\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008-\u0010\u000fJ\u000f\u0010.\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008.\u0010\u000fJ\u000f\u0010/\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008/\u0010\u000fJ#\u00103\u001a\u00020\u000b2\u0006\u00100\u001a\u00020\u00072\n\u0008\u0002\u00102\u001a\u0004\u0018\u000101H\u0002\u00a2\u0006\u0004\u00083\u00104R\u0014\u00106\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u00105R\u0014\u00108\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00087\u00105R\u0014\u00109\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u00105R\u0014\u0010:\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u00105R\u0014\u0010;\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u00105R\u0014\u0010=\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010<R\u0014\u0010@\u001a\u00020>8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010?R\u0014\u0010A\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u00105R\u0016\u0010B\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008+\u00105R\u0014\u0010E\u001a\u00020C8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010DR\u0014\u0010G\u001a\u00020C8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010DR\u0014\u0010J\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR*\u0010S\u001a\u00020K2\u0006\u0010L\u001a\u00020K8\u0016@VX\u0096\u000e\u00a2\u0006\u0012\n\u0004\u0008M\u0010N\u001a\u0004\u0008O\u0010P\"\u0004\u0008Q\u0010RR*\u0010W\u001a\u00020K2\u0006\u0010L\u001a\u00020K8\u0016@VX\u0096\u000e\u00a2\u0006\u0012\n\u0004\u0008T\u0010N\u001a\u0004\u0008U\u0010P\"\u0004\u0008V\u0010R\u00a8\u0006Y"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;",
        "Landroid/widget/FrameLayout;",
        "Lg31/c;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "c",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "j",
        "()V",
        "w",
        "h",
        "oldw",
        "oldh",
        "onSizeChanged",
        "(IIII)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "Landroid/widget/ProgressBar;",
        "getProgressBar",
        "()Landroid/widget/ProgressBar;",
        "Landroid/widget/TextView;",
        "getCurrentProgressTextView",
        "()Landroid/widget/TextView;",
        "getMaxProgressTextView",
        "parentWidth",
        "(I)V",
        "i",
        "g",
        "e",
        "f",
        "d",
        "maxWidth",
        "",
        "text",
        "a",
        "(ILjava/lang/CharSequence;)V",
        "I",
        "size6",
        "b",
        "size22",
        "size36",
        "space4",
        "space8",
        "Z",
        "isRtl",
        "Ljava/text/DecimalFormat;",
        "Ljava/text/DecimalFormat;",
        "groupingNumberFormatter",
        "progressBarMinDisplayableWidth",
        "minDisplayableProgress",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "tvCurrentScore",
        "k",
        "tvMaxScore",
        "l",
        "Landroid/widget/ProgressBar;",
        "progressBarScore",
        "",
        "value",
        "m",
        "J",
        "getProgress",
        "()J",
        "setProgress",
        "(J)V",
        "progress",
        "n",
        "getMaxProgress",
        "setMaxProgress",
        "maxProgress",
        "o",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final o:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final p:I


# instance fields
.field public final a:I

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:Z

.field public final g:Ljava/text/DecimalFormat;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:I

.field public i:I

.field public final j:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Landroid/widget/ProgressBar;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public m:J

.field public n:J


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->o:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->p:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 7
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->size_6:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->a:I

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->size_22:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->b:I

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->size_36:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->c:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->space_4:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->d:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->space_8:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->e:I

    .line 10
    invoke-static {}, LQ0/a;->c()LQ0/a;

    move-result-object p3

    invoke-virtual {p3}, LQ0/a;->h()Z

    move-result p3

    iput-boolean p3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->f:Z

    .line 11
    sget-object v0, Lorg/xbet/uikit/utils/C;->a:Lorg/xbet/uikit/utils/C;

    const/4 v1, 0x0

    const/4 v2, 0x1

    const/4 v3, 0x0

    invoke-static {v0, v1, v2, v3}, Lorg/xbet/uikit/utils/C;->b(Lorg/xbet/uikit/utils/C;CILjava/lang/Object;)Ljava/text/DecimalFormat;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->g:Ljava/text/DecimalFormat;

    .line 12
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v4, LlZ0/g;->size_6:I

    invoke-virtual {v0, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->h:I

    .line 13
    new-instance v0, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v0, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 14
    sget v4, LlZ0/n;->TextStyle_Title_Bold_S_TextPrimary:I

    invoke-static {v0, v4}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 15
    sget-object v4, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {v0, v4}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 16
    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 17
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    const v5, 0x800053

    .line 18
    invoke-virtual {v0, v5}, Landroid/widget/TextView;->setGravity(I)V

    .line 19
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 20
    new-instance v5, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v5, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 21
    sget v6, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    invoke-static {v5, v6}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 22
    invoke-virtual {v5, v4}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 23
    invoke-virtual {v5, v2}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 24
    invoke-virtual {v5, v1}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    const v2, 0x800055

    .line 25
    invoke-virtual {v5, v2}, Landroid/widget/TextView;->setGravity(I)V

    .line 26
    iput-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->k:Landroidx/appcompat/widget/AppCompatTextView;

    .line 27
    new-instance v2, Landroid/widget/ProgressBar;

    const v4, 0x103001f

    invoke-direct {v2, p1, v3, v4}, Landroid/widget/ProgressBar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 28
    invoke-virtual {v2, v1, v1, v1, v1}, Landroid/view/View;->setPadding(IIII)V

    .line 29
    sget v3, LS11/c;->aggregator_vip_cashback_progress_bar_background:I

    .line 30
    invoke-static {p1, v3}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v3

    invoke-virtual {v2, v3}, Landroid/widget/ProgressBar;->setProgressDrawable(Landroid/graphics/drawable/Drawable;)V

    if-eqz p3, :cond_0

    const/high16 p3, -0x40800000    # -1.0f

    goto :goto_0

    :cond_0
    const/high16 p3, 0x3f800000    # 1.0f

    .line 31
    :goto_0
    invoke-virtual {v2, p3}, Landroid/view/View;->setScaleX(F)V

    .line 32
    invoke-virtual {v2, v1}, Landroid/widget/ProgressBar;->setIndeterminate(Z)V

    .line 33
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->setClipToPadding(Z)V

    .line 34
    iput-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->l:Landroid/widget/ProgressBar;

    .line 35
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 36
    invoke-virtual {p0, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 37
    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    const/16 p3, 0x3e8

    .line 38
    invoke-virtual {v2, p3}, Landroid/widget/ProgressBar;->setMax(I)V

    .line 39
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->c(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic b(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;ILjava/lang/CharSequence;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x2

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 6
    .line 7
    invoke-virtual {p2}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->a(ILjava/lang/CharSequence;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method private final c(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 4

    .line 1
    sget-object v0, LS11/h;->AggregatorVipCashbackProgressBar:[I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {p1, p2, v0, v1, v1}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    sget p2, LS11/h;->AggregatorVipCashbackProgressBar_vipCashbackProgressBarProgress:I

    .line 9
    .line 10
    invoke-virtual {p1, p2, v1}, Landroid/content/res/TypedArray;->getInt(II)I

    .line 11
    .line 12
    .line 13
    move-result p2

    .line 14
    int-to-long v2, p2

    .line 15
    invoke-virtual {p0, v2, v3}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->setProgress(J)V

    .line 16
    .line 17
    .line 18
    sget p2, LS11/h;->AggregatorVipCashbackProgressBar_vipCashbackProgressBarMaxProgress:I

    .line 19
    .line 20
    invoke-virtual {p1, p2, v1}, Landroid/content/res/TypedArray;->getInt(II)I

    .line 21
    .line 22
    .line 23
    move-result p2

    .line 24
    int-to-long v0, p2

    .line 25
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->setMaxProgress(J)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method private final j()V
    .locals 7

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->getProgress()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    long-to-float v0, v0

    .line 6
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->getMaxProgress()J

    .line 7
    .line 8
    .line 9
    move-result-wide v1

    .line 10
    long-to-float v1, v1

    .line 11
    div-float/2addr v0, v1

    .line 12
    const/16 v1, 0x3e8

    .line 13
    .line 14
    int-to-float v2, v1

    .line 15
    mul-float v0, v0, v2

    .line 16
    .line 17
    float-to-int v0, v0

    .line 18
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->i:I

    .line 19
    .line 20
    if-ge v0, v2, :cond_0

    .line 21
    .line 22
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->getProgress()J

    .line 23
    .line 24
    .line 25
    move-result-wide v2

    .line 26
    const-wide/16 v4, 0x0

    .line 27
    .line 28
    cmp-long v6, v2, v4

    .line 29
    .line 30
    if-lez v6, :cond_0

    .line 31
    .line 32
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->l:Landroid/widget/ProgressBar;

    .line 33
    .line 34
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->i:I

    .line 35
    .line 36
    invoke-virtual {v0, v1}, Landroid/widget/ProgressBar;->setProgress(I)V

    .line 37
    .line 38
    .line 39
    return-void

    .line 40
    :cond_0
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->i:I

    .line 41
    .line 42
    rsub-int v3, v2, 0x3e8

    .line 43
    .line 44
    if-gt v3, v0, :cond_1

    .line 45
    .line 46
    if-ge v0, v1, :cond_1

    .line 47
    .line 48
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->l:Landroid/widget/ProgressBar;

    .line 49
    .line 50
    sub-int/2addr v1, v2

    .line 51
    invoke-virtual {v0, v1}, Landroid/widget/ProgressBar;->setProgress(I)V

    .line 52
    .line 53
    .line 54
    return-void

    .line 55
    :cond_1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->l:Landroid/widget/ProgressBar;

    .line 56
    .line 57
    invoke-virtual {v1, v0}, Landroid/widget/ProgressBar;->setProgress(I)V

    .line 58
    .line 59
    .line 60
    return-void
.end method


# virtual methods
.method public final a(ILjava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    sget v1, LlZ0/g;->text_12:I

    .line 4
    .line 5
    sget v2, LlZ0/g;->text_18:I

    .line 6
    .line 7
    if-eqz p2, :cond_0

    .line 8
    .line 9
    invoke-virtual {p2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p2

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/4 p2, 0x0

    .line 15
    :goto_0
    if-nez p2, :cond_1

    .line 16
    .line 17
    const-string p2, ""

    .line 18
    .line 19
    :cond_1
    invoke-static {v0, p1, v1, v2, p2}, Lorg/xbet/uikit/utils/M;->d(Landroid/widget/TextView;IIILjava/lang/String;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public final d()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->l:Landroid/widget/ProgressBar;

    .line 2
    .line 3
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->b:I

    .line 4
    .line 5
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->e:I

    .line 6
    .line 7
    add-int v3, v0, v2

    .line 8
    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 10
    .line 11
    .line 12
    move-result v4

    .line 13
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->b:I

    .line 14
    .line 15
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->e:I

    .line 16
    .line 17
    add-int/2addr v0, v2

    .line 18
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->l:Landroid/widget/ProgressBar;

    .line 19
    .line 20
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 21
    .line 22
    .line 23
    move-result v2

    .line 24
    add-int v5, v0, v2

    .line 25
    .line 26
    const/4 v2, 0x0

    .line 27
    move-object v0, p0

    .line 28
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public final e()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v4

    .line 7
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 8
    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 10
    .line 11
    .line 12
    move-result v5

    .line 13
    const/4 v2, 0x0

    .line 14
    const/4 v3, 0x0

    .line 15
    move-object v0, p0

    .line 16
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public final f()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->k:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->k:Landroidx/appcompat/widget/AppCompatTextView;

    .line 8
    .line 9
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    sub-int v2, v0, v2

    .line 14
    .line 15
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 16
    .line 17
    .line 18
    move-result v4

    .line 19
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->k:Landroidx/appcompat/widget/AppCompatTextView;

    .line 20
    .line 21
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 22
    .line 23
    .line 24
    move-result v5

    .line 25
    const/4 v3, 0x0

    .line 26
    move-object v0, p0

    .line 27
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public final g(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->l:Landroid/widget/ProgressBar;

    .line 2
    .line 3
    const/high16 v1, 0x40000000    # 2.0f

    .line 4
    .line 5
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->a:I

    .line 10
    .line 11
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public final getCurrentProgressTextView()Landroid/widget/TextView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    return-object v0
.end method

.method public getMaxProgress()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->n:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final getMaxProgressTextView()Landroid/widget/TextView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->k:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    return-object v0
.end method

.method public getProgress()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->m:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final getProgressBar()Landroid/widget/ProgressBar;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->l:Landroid/widget/ProgressBar;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h(I)V
    .locals 3

    .line 1
    const/4 v0, 0x2

    .line 2
    div-int/2addr p1, v0

    .line 3
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->d:I

    .line 4
    .line 5
    sub-int v1, p1, v1

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    invoke-static {p0, v1, v2, v0, v2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->b(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;ILjava/lang/CharSequence;ILjava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 12
    .line 13
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->d:I

    .line 14
    .line 15
    sub-int/2addr p1, v1

    .line 16
    const/high16 v1, -0x80000000

    .line 17
    .line 18
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->b:I

    .line 23
    .line 24
    const/high16 v2, 0x40000000    # 2.0f

    .line 25
    .line 26
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final i(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->k:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 4
    .line 5
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    sub-int/2addr p1, v1

    .line 10
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->e:I

    .line 11
    .line 12
    sub-int/2addr p1, v1

    .line 13
    const/high16 v1, 0x40000000    # 2.0f

    .line 14
    .line 15
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 16
    .line 17
    .line 18
    move-result p1

    .line 19
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->b:I

    .line 20
    .line 21
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->e()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->f()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->d()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public onMeasure(II)V
    .locals 1

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->h(I)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->i(I)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->g(I)V

    .line 12
    .line 13
    .line 14
    const/high16 p2, 0x40000000    # 2.0f

    .line 15
    .line 16
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 17
    .line 18
    .line 19
    move-result p1

    .line 20
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->c:I

    .line 21
    .line 22
    invoke-static {v0, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 23
    .line 24
    .line 25
    move-result p2

    .line 26
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public onSizeChanged(IIII)V
    .locals 0

    .line 1
    invoke-super {p0, p1, p2, p3, p4}, Landroid/widget/FrameLayout;->onSizeChanged(IIII)V

    .line 2
    .line 3
    .line 4
    iget p2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->h:I

    .line 5
    .line 6
    int-to-float p2, p2

    .line 7
    int-to-float p1, p1

    .line 8
    div-float/2addr p2, p1

    .line 9
    const/16 p1, 0x3e8

    .line 10
    .line 11
    int-to-float p1, p1

    .line 12
    mul-float p2, p2, p1

    .line 13
    .line 14
    float-to-int p1, p2

    .line 15
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->i:I

    .line 16
    .line 17
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->j()V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public setMaxProgress(J)V
    .locals 3

    .line 1
    iput-wide p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->n:J

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->k:Landroidx/appcompat/widget/AppCompatTextView;

    .line 4
    .line 5
    sget-object v1, Lkotlin/jvm/internal/D;->a:Lkotlin/jvm/internal/D;

    .line 6
    .line 7
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->g:Ljava/text/DecimalFormat;

    .line 8
    .line 9
    invoke-virtual {v1, p1, p2}, Ljava/text/NumberFormat;->format(J)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    const/4 p2, 0x1

    .line 14
    new-array v1, p2, [Ljava/lang/Object;

    .line 15
    .line 16
    const/4 v2, 0x0

    .line 17
    aput-object p1, v1, v2

    .line 18
    .line 19
    invoke-static {v1, p2}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    const-string p2, "/ %s"

    .line 24
    .line 25
    invoke-static {p2, p1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 30
    .line 31
    .line 32
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->k:Landroidx/appcompat/widget/AppCompatTextView;

    .line 33
    .line 34
    invoke-virtual {p1}, Landroid/view/View;->requestLayout()V

    .line 35
    .line 36
    .line 37
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->j()V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public setProgress(J)V
    .locals 1

    .line 1
    iput-wide p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->m:J

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->g:Ljava/text/DecimalFormat;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Ljava/text/NumberFormat;->format(J)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 10
    .line 11
    .line 12
    move-result p2

    .line 13
    div-int/lit8 p2, p2, 0x2

    .line 14
    .line 15
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->d:I

    .line 16
    .line 17
    sub-int/2addr p2, v0

    .line 18
    invoke-virtual {p0, p2, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->a(ILjava/lang/CharSequence;)V

    .line 19
    .line 20
    .line 21
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 22
    .line 23
    invoke-virtual {p2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 24
    .line 25
    .line 26
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->j:Landroidx/appcompat/widget/AppCompatTextView;

    .line 27
    .line 28
    invoke-virtual {p1}, Landroid/view/View;->requestLayout()V

    .line 29
    .line 30
    .line 31
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->j()V

    .line 32
    .line 33
    .line 34
    return-void
.end method
