.class public final synthetic Leb1/l0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:LUX0/k;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;LUX0/k;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Leb1/l0;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, Leb1/l0;->b:LUX0/k;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Leb1/l0;->a:Lkotlin/jvm/functions/Function1;

    iget-object v1, p0, Leb1/l0;->b:LUX0/k;

    check-cast p1, LB4/a;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentTopGamesDelegateKt;->g(Lkotlin/jvm/functions/Function1;LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
