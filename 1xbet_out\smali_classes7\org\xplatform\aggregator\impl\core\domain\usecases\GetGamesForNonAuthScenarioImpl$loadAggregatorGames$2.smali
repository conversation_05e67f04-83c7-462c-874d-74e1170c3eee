.class final Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.core.domain.usecases.GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2"
    f = "GetGamesForNonAuthScenarioImpl.kt"
    l = {
        0x4d
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->p(ZZZLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Result<",
        "+",
        "Ljava/util/List<",
        "+",
        "Ld81/b;",
        ">;>;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00030\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "Lkotlin/Result;",
        "",
        "Ld81/b;",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)Lkotlin/Result;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $fromPopular:Z

.field final synthetic $isForceUpdate:Z

.field final synthetic $isLoggedIn:Z

.field private synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZZLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;",
            "ZZZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    iput-boolean p2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->$isLoggedIn:Z

    iput-boolean p3, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->$isForceUpdate:Z

    iput-boolean p4, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->$fromPopular:Z

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->$isLoggedIn:Z

    iget-boolean v3, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->$isForceUpdate:Z

    iget-boolean v4, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->$fromPopular:Z

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZZLkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+",
            "Ljava/util/List<",
            "Ld81/b;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 23

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    const/4 v1, 0x4

    .line 4
    const/4 v2, 0x3

    .line 5
    const/4 v3, 0x2

    .line 6
    const/4 v4, 0x0

    .line 7
    const/4 v5, 0x1

    .line 8
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v6

    .line 12
    iget v7, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->label:I

    .line 13
    .line 14
    if-eqz v7, :cond_1

    .line 15
    .line 16
    if-ne v7, v5, :cond_0

    .line 17
    .line 18
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    move-object/from16 v7, p1

    .line 22
    .line 23
    goto/16 :goto_0

    .line 24
    .line 25
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 26
    .line 27
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 28
    .line 29
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 30
    .line 31
    .line 32
    throw v1

    .line 33
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->L$0:Ljava/lang/Object;

    .line 37
    .line 38
    move-object v8, v7

    .line 39
    check-cast v8, Lkotlinx/coroutines/N;

    .line 40
    .line 41
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 42
    .line 43
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->g(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;)LfX/b;

    .line 44
    .line 45
    .line 46
    move-result-object v7

    .line 47
    invoke-interface {v7}, LfX/b;->B0()Z

    .line 48
    .line 49
    .line 50
    move-result v11

    .line 51
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 52
    .line 53
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->e(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;)Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 54
    .line 55
    .line 56
    move-result-object v7

    .line 57
    invoke-interface {v7}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 58
    .line 59
    .line 60
    move-result-object v7

    .line 61
    invoke-virtual {v7}, Lek0/o;->o()Lek0/a;

    .line 62
    .line 63
    .line 64
    move-result-object v7

    .line 65
    invoke-virtual {v7}, Lek0/a;->c()Z

    .line 66
    .line 67
    .line 68
    move-result v12

    .line 69
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 70
    .line 71
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->f(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;)Li8/j;

    .line 72
    .line 73
    .line 74
    move-result-object v7

    .line 75
    invoke-interface {v7}, Li8/j;->invoke()Ljava/lang/String;

    .line 76
    .line 77
    .line 78
    move-result-object v13

    .line 79
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 80
    .line 81
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->c(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;)Lorg/xplatform/aggregator/impl/category/domain/usecases/p;

    .line 82
    .line 83
    .line 84
    move-result-object v7

    .line 85
    invoke-virtual {v7, v5}, Lorg/xplatform/aggregator/impl/category/domain/usecases/p;->a(Z)Ljava/lang/String;

    .line 86
    .line 87
    .line 88
    move-result-object v7

    .line 89
    new-instance v9, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$1;

    .line 90
    .line 91
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 92
    .line 93
    iget-boolean v14, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->$isForceUpdate:Z

    .line 94
    .line 95
    const/4 v15, 0x0

    .line 96
    invoke-direct/range {v9 .. v15}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$1;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZLjava/lang/String;ZLkotlin/coroutines/e;)V

    .line 97
    .line 98
    .line 99
    move v14, v11

    .line 100
    move/from16 v16, v12

    .line 101
    .line 102
    move-object/from16 v17, v13

    .line 103
    .line 104
    const/4 v12, 0x3

    .line 105
    const/4 v13, 0x0

    .line 106
    move-object v11, v9

    .line 107
    const/4 v9, 0x0

    .line 108
    const/4 v10, 0x0

    .line 109
    invoke-static/range {v8 .. v13}, Lkotlinx/coroutines/h;->b(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/T;

    .line 110
    .line 111
    .line 112
    move-result-object v20

    .line 113
    new-instance v9, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$2;

    .line 114
    .line 115
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 116
    .line 117
    iget-boolean v15, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->$isForceUpdate:Z

    .line 118
    .line 119
    move/from16 v12, v16

    .line 120
    .line 121
    const/16 v16, 0x0

    .line 122
    .line 123
    move v11, v14

    .line 124
    move-object/from16 v13, v17

    .line 125
    .line 126
    move-object v14, v7

    .line 127
    invoke-direct/range {v9 .. v16}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$2;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZLjava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/e;)V

    .line 128
    .line 129
    .line 130
    move v7, v11

    .line 131
    move/from16 v16, v12

    .line 132
    .line 133
    move-object/from16 v21, v14

    .line 134
    .line 135
    const/4 v12, 0x3

    .line 136
    const/4 v13, 0x0

    .line 137
    move-object v11, v9

    .line 138
    const/4 v9, 0x0

    .line 139
    const/4 v10, 0x0

    .line 140
    invoke-static/range {v8 .. v13}, Lkotlinx/coroutines/h;->b(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/T;

    .line 141
    .line 142
    .line 143
    move-result-object v22

    .line 144
    new-instance v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$3;

    .line 145
    .line 146
    iget-object v13, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 147
    .line 148
    iget-boolean v14, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->$isLoggedIn:Z

    .line 149
    .line 150
    iget-boolean v15, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->$fromPopular:Z

    .line 151
    .line 152
    iget-boolean v9, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->$isForceUpdate:Z

    .line 153
    .line 154
    const/16 v19, 0x0

    .line 155
    .line 156
    move/from16 v18, v9

    .line 157
    .line 158
    move-object v12, v11

    .line 159
    invoke-direct/range {v12 .. v19}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$3;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZZLjava/lang/String;ZLkotlin/coroutines/e;)V

    .line 160
    .line 161
    .line 162
    const/4 v12, 0x3

    .line 163
    const/4 v13, 0x0

    .line 164
    const/4 v9, 0x0

    .line 165
    const/4 v10, 0x0

    .line 166
    invoke-static/range {v8 .. v13}, Lkotlinx/coroutines/h;->b(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/T;

    .line 167
    .line 168
    .line 169
    move-result-object v18

    .line 170
    new-instance v9, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;

    .line 171
    .line 172
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 173
    .line 174
    iget-boolean v15, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->$isForceUpdate:Z

    .line 175
    .line 176
    move/from16 v12, v16

    .line 177
    .line 178
    const/16 v16, 0x0

    .line 179
    .line 180
    move v11, v7

    .line 181
    move-object/from16 v13, v17

    .line 182
    .line 183
    move-object/from16 v14, v21

    .line 184
    .line 185
    invoke-direct/range {v9 .. v16}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZLjava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/e;)V

    .line 186
    .line 187
    .line 188
    const/4 v12, 0x3

    .line 189
    const/4 v13, 0x0

    .line 190
    move-object v11, v9

    .line 191
    const/4 v9, 0x0

    .line 192
    const/4 v10, 0x0

    .line 193
    invoke-static/range {v8 .. v13}, Lkotlinx/coroutines/h;->b(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/T;

    .line 194
    .line 195
    .line 196
    move-result-object v7

    .line 197
    new-array v8, v1, [Lkotlinx/coroutines/T;

    .line 198
    .line 199
    aput-object v20, v8, v4

    .line 200
    .line 201
    aput-object v22, v8, v5

    .line 202
    .line 203
    aput-object v18, v8, v3

    .line 204
    .line 205
    aput-object v7, v8, v2

    .line 206
    .line 207
    iput v5, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->label:I

    .line 208
    .line 209
    invoke-static {v8, v0}, Lkotlinx/coroutines/AwaitKt;->b([Lkotlinx/coroutines/T;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 210
    .line 211
    .line 212
    move-result-object v7

    .line 213
    if-ne v7, v6, :cond_2

    .line 214
    .line 215
    return-object v6

    .line 216
    :cond_2
    :goto_0
    check-cast v7, Ljava/util/List;

    .line 217
    .line 218
    invoke-interface {v7, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 219
    .line 220
    .line 221
    move-result-object v6

    .line 222
    check-cast v6, Lkotlin/Result;

    .line 223
    .line 224
    invoke-virtual {v6}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 225
    .line 226
    .line 227
    move-result-object v10

    .line 228
    invoke-interface {v7, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 229
    .line 230
    .line 231
    move-result-object v6

    .line 232
    check-cast v6, Lkotlin/Result;

    .line 233
    .line 234
    invoke-virtual {v6}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 235
    .line 236
    .line 237
    move-result-object v6

    .line 238
    invoke-interface {v7, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 239
    .line 240
    .line 241
    move-result-object v8

    .line 242
    check-cast v8, Lkotlin/Result;

    .line 243
    .line 244
    invoke-virtual {v8}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 245
    .line 246
    .line 247
    move-result-object v19

    .line 248
    invoke-interface {v7, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 249
    .line 250
    .line 251
    move-result-object v7

    .line 252
    check-cast v7, Lkotlin/Result;

    .line 253
    .line 254
    invoke-virtual {v7}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 255
    .line 256
    .line 257
    move-result-object v7

    .line 258
    invoke-static {v10}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 259
    .line 260
    .line 261
    move-result v8

    .line 262
    if-eqz v8, :cond_5

    .line 263
    .line 264
    invoke-static {v6}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 265
    .line 266
    .line 267
    move-result v8

    .line 268
    if-eqz v8, :cond_5

    .line 269
    .line 270
    invoke-static {v7}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 271
    .line 272
    .line 273
    move-result v8

    .line 274
    if-eqz v8, :cond_5

    .line 275
    .line 276
    invoke-static/range {v19 .. v19}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 277
    .line 278
    .line 279
    move-result v8

    .line 280
    if-nez v8, :cond_3

    .line 281
    .line 282
    iget-boolean v8, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->$isLoggedIn:Z

    .line 283
    .line 284
    if-nez v8, :cond_5

    .line 285
    .line 286
    :cond_3
    invoke-static {v10}, Lkotlin/Result;->exceptionOrNull-impl(Ljava/lang/Object;)Ljava/lang/Throwable;

    .line 287
    .line 288
    .line 289
    move-result-object v1

    .line 290
    if-nez v1, :cond_4

    .line 291
    .line 292
    new-instance v1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 293
    .line 294
    invoke-direct {v1}, Lcom/xbet/onexcore/data/model/ServerException;-><init>()V

    .line 295
    .line 296
    .line 297
    :cond_4
    invoke-static {v1}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 298
    .line 299
    .line 300
    move-result-object v1

    .line 301
    invoke-static {v1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 302
    .line 303
    .line 304
    move-result-object v1

    .line 305
    goto :goto_2

    .line 306
    :cond_5
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 307
    .line 308
    sget-object v9, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->ONE_X_LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 309
    .line 310
    const/16 v14, 0xc

    .line 311
    .line 312
    const/4 v15, 0x0

    .line 313
    const/4 v11, 0x0

    .line 314
    const-wide/16 v12, 0x0

    .line 315
    .line 316
    invoke-static/range {v8 .. v15}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->o(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;Ljava/lang/Object;Lorg/xplatform/aggregator/api/model/PartitionType;JILjava/lang/Object;)Ld81/b;

    .line 317
    .line 318
    .line 319
    move-result-object v8

    .line 320
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 321
    .line 322
    sget-object v12, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->POPULAR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 323
    .line 324
    sget-object v14, Lorg/xplatform/aggregator/api/model/PartitionType;->SLOTS:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 325
    .line 326
    const/16 v17, 0x8

    .line 327
    .line 328
    const/16 v18, 0x0

    .line 329
    .line 330
    const-wide/16 v15, 0x0

    .line 331
    .line 332
    move-object v13, v6

    .line 333
    invoke-static/range {v11 .. v18}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->o(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;Ljava/lang/Object;Lorg/xplatform/aggregator/api/model/PartitionType;JILjava/lang/Object;)Ld81/b;

    .line 334
    .line 335
    .line 336
    move-result-object v6

    .line 337
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 338
    .line 339
    sget-object v12, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->RECOMMENDED:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 340
    .line 341
    const/16 v17, 0x4

    .line 342
    .line 343
    const/4 v14, 0x0

    .line 344
    const-wide/16 v15, 0x2

    .line 345
    .line 346
    move-object/from16 v13, v19

    .line 347
    .line 348
    invoke-static/range {v11 .. v18}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->o(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;Ljava/lang/Object;Lorg/xplatform/aggregator/api/model/PartitionType;JILjava/lang/Object;)Ld81/b;

    .line 349
    .line 350
    .line 351
    move-result-object v9

    .line 352
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 353
    .line 354
    sget-object v12, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 355
    .line 356
    const/16 v17, 0xc

    .line 357
    .line 358
    const-wide/16 v15, 0x0

    .line 359
    .line 360
    move-object v13, v7

    .line 361
    invoke-static/range {v11 .. v18}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->o(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;Ljava/lang/Object;Lorg/xplatform/aggregator/api/model/PartitionType;JILjava/lang/Object;)Ld81/b;

    .line 362
    .line 363
    .line 364
    move-result-object v7

    .line 365
    new-array v1, v1, [Ld81/b;

    .line 366
    .line 367
    aput-object v8, v1, v4

    .line 368
    .line 369
    aput-object v6, v1, v5

    .line 370
    .line 371
    aput-object v9, v1, v3

    .line 372
    .line 373
    aput-object v7, v1, v2

    .line 374
    .line 375
    invoke-static {v1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 376
    .line 377
    .line 378
    move-result-object v1

    .line 379
    new-instance v2, Ljava/util/ArrayList;

    .line 380
    .line 381
    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 382
    .line 383
    .line 384
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 385
    .line 386
    .line 387
    move-result-object v1

    .line 388
    :cond_6
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 389
    .line 390
    .line 391
    move-result v3

    .line 392
    if-eqz v3, :cond_7

    .line 393
    .line 394
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 395
    .line 396
    .line 397
    move-result-object v3

    .line 398
    move-object v4, v3

    .line 399
    check-cast v4, Ld81/b;

    .line 400
    .line 401
    invoke-virtual {v4}, Ld81/b;->d()Z

    .line 402
    .line 403
    .line 404
    move-result v4

    .line 405
    if-nez v4, :cond_6

    .line 406
    .line 407
    invoke-interface {v2, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 408
    .line 409
    .line 410
    goto :goto_1

    .line 411
    :cond_7
    invoke-static {v2}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 412
    .line 413
    .line 414
    move-result-object v1

    .line 415
    :goto_2
    invoke-static {v1}, Lkotlin/Result;->box-impl(Ljava/lang/Object;)Lkotlin/Result;

    .line 416
    .line 417
    .line 418
    move-result-object v1

    .line 419
    return-object v1
.end method
