.class public final Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->g(ILjava/lang/Integer;)Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function1<",
        "Ljava/lang/Throwable;",
        "Ljava/lang/Boolean;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lkotlin/jvm/internal/Ref$BooleanRef;

.field public final synthetic b:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;


# direct methods
.method public constructor <init>(Lkotlin/jvm/internal/Ref$BooleanRef;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;)V
    .locals 0

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$c;->a:Lkotlin/jvm/internal/Ref$BooleanRef;

    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$c;->b:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Throwable;)Ljava/lang/Boolean;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$c;->a:Lkotlin/jvm/internal/Ref$BooleanRef;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    iput-boolean v1, v0, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$c;->b:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 7
    .line 8
    invoke-static {v0, p1}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->d(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Ljava/lang/Throwable;)Z

    .line 9
    .line 10
    .line 11
    move-result p1

    .line 12
    if-eqz p1, :cond_0

    .line 13
    .line 14
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$c;->b:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 15
    .line 16
    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->c(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;)Lo9/a;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    invoke-interface {p1, v1}, Lo9/a;->p(Z)V

    .line 21
    .line 22
    .line 23
    :cond_0
    sget-object p1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 24
    .line 25
    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Throwable;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$c;->a(Ljava/lang/Throwable;)Ljava/lang/Boolean;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method
