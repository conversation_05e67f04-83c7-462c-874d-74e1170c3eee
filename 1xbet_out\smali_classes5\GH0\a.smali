.class public final LGH0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a!\u0010\u0005\u001a\u00020\u0004*\u00020\u00002\u000c\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "LHH0/b;",
        "",
        "LND0/k;",
        "teamModelList",
        "LJH0/a;",
        "a",
        "(LHH0/b;Ljava/util/List;)LJH0/a;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LHH0/b;Ljava/util/List;)LJH0/a;
    .locals 5
    .param p0    # LHH0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LHH0/b;",
            "Ljava/util/List<",
            "LND0/k;",
            ">;)",
            "LJH0/a;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x2

    .line 3
    invoke-virtual {p0}, LHH0/b;->a()Ljava/util/List;

    .line 4
    .line 5
    .line 6
    move-result-object v2

    .line 7
    const/4 v3, 0x1

    .line 8
    if-eqz v2, :cond_0

    .line 9
    .line 10
    invoke-interface {v2}, Ljava/util/List;->size()I

    .line 11
    .line 12
    .line 13
    move-result v2

    .line 14
    if-ne v2, v1, :cond_0

    .line 15
    .line 16
    invoke-virtual {p0}, LHH0/b;->a()Ljava/util/List;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 21
    .line 22
    .line 23
    move-result-object v2

    .line 24
    check-cast v2, LHH0/a;

    .line 25
    .line 26
    invoke-virtual {p0}, LHH0/b;->a()Ljava/util/List;

    .line 27
    .line 28
    .line 29
    move-result-object p0

    .line 30
    invoke-interface {p0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p0

    .line 34
    check-cast p0, LHH0/a;

    .line 35
    .line 36
    new-instance v4, LJH0/a;

    .line 37
    .line 38
    invoke-static {v2, p1}, LGH0/b;->a(LHH0/a;Ljava/util/List;)LJH0/b;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    invoke-static {p0, p1}, LGH0/b;->a(LHH0/a;Ljava/util/List;)LJH0/b;

    .line 43
    .line 44
    .line 45
    move-result-object p0

    .line 46
    new-array p1, v1, [LJH0/b;

    .line 47
    .line 48
    aput-object v2, p1, v0

    .line 49
    .line 50
    aput-object p0, p1, v3

    .line 51
    .line 52
    invoke-static {p1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 53
    .line 54
    .line 55
    move-result-object p0

    .line 56
    invoke-direct {v4, p0}, LJH0/a;-><init>(Ljava/util/List;)V

    .line 57
    .line 58
    .line 59
    return-object v4

    .line 60
    :cond_0
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 61
    .line 62
    const/4 p1, 0x0

    .line 63
    invoke-direct {p0, p1, v3, p1}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 64
    .line 65
    .line 66
    throw p0
.end method
