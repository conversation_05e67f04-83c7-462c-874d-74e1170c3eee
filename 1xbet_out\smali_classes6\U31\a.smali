.class public interface abstract LU31/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\r\n\u0002\u0008\u0005\n\u0002\u0010\u0008\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008g\u0018\u00002\u00020\u0001J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J%\u0010\n\u001a\u00020\u00042\u0014\u0010\t\u001a\u0010\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0007H&\u00a2\u0006\u0004\u0008\n\u0010\u000bJ%\u0010\u000c\u001a\u00020\u00042\u0014\u0010\t\u001a\u0010\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0007H&\u00a2\u0006\u0004\u0008\u000c\u0010\u000bJ\u0019\u0010\u000f\u001a\u00020\u00042\u0008\u0010\u000e\u001a\u0004\u0018\u00010\rH&\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0019\u0010\u0012\u001a\u00020\u00042\u0008\u0010\u0011\u001a\u0004\u0018\u00010\rH&\u00a2\u0006\u0004\u0008\u0012\u0010\u0010J\u0019\u0010\u0015\u001a\u00020\u00042\u0008\u0008\u0001\u0010\u0014\u001a\u00020\u0013H&\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0019\u0010\u0018\u001a\u00020\u00042\u0008\u0010\u0017\u001a\u0004\u0018\u00010\rH&\u00a2\u0006\u0004\u0008\u0018\u0010\u0010J\u001b\u0010\u001a\u001a\u00020\u00042\n\u0008\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u0013H&\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0019\u0010\u001d\u001a\u00020\u00042\u0008\u0010\u001c\u001a\u0004\u0018\u00010\rH&\u00a2\u0006\u0004\u0008\u001d\u0010\u0010J!\u0010!\u001a\u00020\u00042\u0008\u0010\u001e\u001a\u0004\u0018\u00010\r2\u0006\u0010 \u001a\u00020\u001fH&\u00a2\u0006\u0004\u0008!\u0010\"J\u0019\u0010$\u001a\u00020\u00042\u0008\u0010#\u001a\u0004\u0018\u00010\rH&\u00a2\u0006\u0004\u0008$\u0010\u0010\u00a8\u0006%"
    }
    d2 = {
        "LU31/a;",
        "",
        "LX31/c;",
        "couponCardUiModel",
        "",
        "setModel",
        "(LX31/c;)V",
        "Lkotlin/Function1;",
        "Landroid/view/View;",
        "listener",
        "setCancelButtonClickListener",
        "(Lkotlin/jvm/functions/Function1;)V",
        "setMoveButtonClickListener",
        "",
        "subTitle",
        "setSubTitle",
        "(Ljava/lang/CharSequence;)V",
        "tagText",
        "setTagText",
        "",
        "tagColor",
        "setTagColor",
        "(I)V",
        "error",
        "setError",
        "marketStyle",
        "setMarketStyle",
        "(Ljava/lang/Integer;)V",
        "description",
        "setMarketDescription",
        "coef",
        "Lorg/xbet/uikit/components/market/base/CoefficientState;",
        "coefficientState",
        "setMarketCoefficient",
        "(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V",
        "bonusTitle",
        "setCouponBonusTitle",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# virtual methods
.method public abstract setCancelButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract setCouponBonusTitle(Ljava/lang/CharSequence;)V
.end method

.method public abstract setError(Ljava/lang/CharSequence;)V
.end method

.method public abstract setMarketCoefficient(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V
    .param p2    # Lorg/xbet/uikit/components/market/base/CoefficientState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract setMarketDescription(Ljava/lang/CharSequence;)V
.end method

.method public abstract setMarketStyle(Ljava/lang/Integer;)V
.end method

.method public abstract setModel(LX31/c;)V
    .param p1    # LX31/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract setMoveButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract setSubTitle(Ljava/lang/CharSequence;)V
.end method

.method public abstract setTagColor(I)V
.end method

.method public abstract setTagText(Ljava/lang/CharSequence;)V
.end method
