.class public final LKX0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LRc/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "LRc/d<",
        "Ljava/lang/Object;",
        "Lio/reactivex/disposables/b;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0007\u0008\u0007\u0018\u00002\u0010\u0012\u0004\u0012\u00020\u0002\u0012\u0006\u0012\u0004\u0018\u00010\u00030\u0001B\u000f\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J&\u0010\u000b\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0008\u001a\u00020\u00022\n\u0010\n\u001a\u0006\u0012\u0002\u0008\u00030\tH\u0096\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ.\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0008\u001a\u00020\u00022\n\u0010\n\u001a\u0006\u0012\u0002\u0008\u00030\t2\u0008\u0010\r\u001a\u0004\u0018\u00010\u0003H\u0096\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0012R\u0018\u0010\u0014\u001a\u0004\u0018\u00010\u00038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\u0013\u00a8\u0006\u0015"
    }
    d2 = {
        "LKX0/a;",
        "LRc/d;",
        "",
        "Lio/reactivex/disposables/b;",
        "Lio/reactivex/disposables/a;",
        "compositeDisposable",
        "<init>",
        "(Lio/reactivex/disposables/a;)V",
        "thisRef",
        "Lkotlin/reflect/m;",
        "property",
        "b",
        "(Ljava/lang/Object;Lkotlin/reflect/m;)Lio/reactivex/disposables/b;",
        "value",
        "",
        "c",
        "(Ljava/lang/Object;Lkotlin/reflect/m;Lio/reactivex/disposables/b;)V",
        "a",
        "Lio/reactivex/disposables/a;",
        "Lio/reactivex/disposables/b;",
        "field",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lio/reactivex/disposables/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:Lio/reactivex/disposables/b;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Lio/reactivex/disposables/a;)V
    .locals 0
    .param p1    # Lio/reactivex/disposables/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LKX0/a;->a:Lio/reactivex/disposables/a;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;Lkotlin/reflect/m;Ljava/lang/Object;)V
    .locals 0

    .line 1
    check-cast p3, Lio/reactivex/disposables/b;

    .line 2
    .line 3
    invoke-virtual {p0, p1, p2, p3}, LKX0/a;->c(Ljava/lang/Object;Lkotlin/reflect/m;Lio/reactivex/disposables/b;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public b(Ljava/lang/Object;Lkotlin/reflect/m;)Lio/reactivex/disposables/b;
    .locals 0
    .param p1    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/reflect/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/reflect/m<",
            "*>;)",
            "Lio/reactivex/disposables/b;"
        }
    .end annotation

    .line 1
    iget-object p1, p0, LKX0/a;->b:Lio/reactivex/disposables/b;

    .line 2
    .line 3
    return-object p1
.end method

.method public c(Ljava/lang/Object;Lkotlin/reflect/m;Lio/reactivex/disposables/b;)V
    .locals 0
    .param p1    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/reflect/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/reflect/m<",
            "*>;",
            "Lio/reactivex/disposables/b;",
            ")V"
        }
    .end annotation

    .line 1
    iget-object p1, p0, LKX0/a;->b:Lio/reactivex/disposables/b;

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    invoke-interface {p1}, Lio/reactivex/disposables/b;->isDisposed()Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    const/4 p2, 0x1

    .line 10
    if-ne p1, p2, :cond_0

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    iget-object p1, p0, LKX0/a;->b:Lio/reactivex/disposables/b;

    .line 14
    .line 15
    if-eqz p1, :cond_1

    .line 16
    .line 17
    invoke-interface {p1}, Lio/reactivex/disposables/b;->dispose()V

    .line 18
    .line 19
    .line 20
    :cond_1
    :goto_0
    iput-object p3, p0, LKX0/a;->b:Lio/reactivex/disposables/b;

    .line 21
    .line 22
    if-eqz p3, :cond_2

    .line 23
    .line 24
    iget-object p1, p0, LKX0/a;->a:Lio/reactivex/disposables/a;

    .line 25
    .line 26
    invoke-virtual {p1, p3}, Lio/reactivex/disposables/a;->c(Lio/reactivex/disposables/b;)Z

    .line 27
    .line 28
    .line 29
    :cond_2
    return-void
.end method

.method public bridge synthetic getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, LKX0/a;->b(Ljava/lang/Object;Lkotlin/reflect/m;)Lio/reactivex/disposables/b;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method
