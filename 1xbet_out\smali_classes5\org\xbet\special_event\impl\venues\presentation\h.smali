.class public final Lorg/xbet/special_event/impl/venues/presentation/h;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/venues/presentation/b;",
        "Lorg/xbet/special_event/impl/venues/presentation/g;",
        "a",
        "(Lorg/xbet/special_event/impl/venues/presentation/b;)Lorg/xbet/special_event/impl/venues/presentation/g;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xbet/special_event/impl/venues/presentation/b;)Lorg/xbet/special_event/impl/venues/presentation/g;
    .locals 1
    .param p0    # Lorg/xbet/special_event/impl/venues/presentation/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/b;->e()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    sget-object p0, Lorg/xbet/special_event/impl/venues/presentation/g$c;->a:Lorg/xbet/special_event/impl/venues/presentation/g$c;

    .line 8
    .line 9
    return-object p0

    .line 10
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/b;->d()Ljava/util/List;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    if-nez v0, :cond_1

    .line 19
    .line 20
    new-instance v0, Lorg/xbet/special_event/impl/venues/presentation/g$a;

    .line 21
    .line 22
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/b;->d()Ljava/util/List;

    .line 23
    .line 24
    .line 25
    move-result-object p0

    .line 26
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/venues/presentation/g$a;-><init>(Ljava/util/List;)V

    .line 27
    .line 28
    .line 29
    return-object v0

    .line 30
    :cond_1
    new-instance v0, Lorg/xbet/special_event/impl/venues/presentation/g$b;

    .line 31
    .line 32
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/venues/presentation/b;->c()Lorg/xbet/uikit/components/lottie/a;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/venues/presentation/g$b;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 37
    .line 38
    .line 39
    return-object v0
.end method
