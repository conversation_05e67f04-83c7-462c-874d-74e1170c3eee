.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournaments_full_info.TournamentsFullInfoSharedViewModel$tournamentResultState$1"
    f = "TournamentsFullInfoSharedViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;-><init>(Lw81/c;Lgk/b;Lm8/a;Lorg/xbet/ui_common/utils/internet/a;Lw81/g;Lw81/d;Lw81/e;Lorg/xbet/ui_common/utils/M;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LSX0/c;JLHX0/e;Ljava/lang/String;LwX0/C;LP91/b;Lorg/xbet/analytics/domain/scope/g;Lorg/xbet/analytics/domain/scope/g0;LnR/a;LnR/d;Leu/i;Lorg/xbet/remoteconfig/domain/usecases/i;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;",
        "Lkb1/E;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkb1/F<",
        "+",
        "Lkb1/u;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\n\u00a2\u0006\u0004\u0008\u0006\u0010\u0007"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;",
        "fullInfo",
        "Lkb1/E;",
        "error",
        "Lkb1/F;",
        "Lkb1/u;",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;Lkb1/E;)Lkb1/F;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    const/4 p1, 0x3

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;

    check-cast p2, Lkb1/E;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;->invoke(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;Lkb1/E;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;Lkb1/E;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;",
            "Lkb1/E;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkb1/F<",
            "Lkb1/u;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    invoke-direct {v0, v1, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 14

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_8

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;->L$1:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, Lkb1/E;

    .line 18
    .line 19
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentResultState$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 20
    .line 21
    instance-of v2, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$a;

    .line 22
    .line 23
    if-eqz v2, :cond_5

    .line 24
    .line 25
    :try_start_0
    sget-object v2, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 26
    .line 27
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$a;

    .line 28
    .line 29
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$a;->a()Li81/a;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    invoke-static {p1, v2}, Ljb1/A;->f(Li81/a;LHX0/e;)Lkb1/u;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    invoke-virtual {p1}, Lkb1/u;->b()Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object v2

    .line 45
    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    .line 46
    .line 47
    .line 48
    move-result v2

    .line 49
    instance-of v3, v0, Lkb1/E$c;

    .line 50
    .line 51
    if-eqz v3, :cond_1

    .line 52
    .line 53
    if-eqz v2, :cond_0

    .line 54
    .line 55
    new-instance p1, Lkb1/F$c;

    .line 56
    .line 57
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    invoke-direct {p1, v0}, Lkb1/F$c;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 62
    .line 63
    .line 64
    goto :goto_0

    .line 65
    :catchall_0
    move-exception v0

    .line 66
    move-object p1, v0

    .line 67
    goto :goto_1

    .line 68
    :cond_0
    new-instance v0, Lkb1/F$d;

    .line 69
    .line 70
    invoke-direct {v0, p1}, Lkb1/F$d;-><init>(Ljava/lang/Object;)V

    .line 71
    .line 72
    .line 73
    move-object p1, v0

    .line 74
    goto :goto_0

    .line 75
    :cond_1
    instance-of p1, v0, Lkb1/E$b;

    .line 76
    .line 77
    if-eqz p1, :cond_2

    .line 78
    .line 79
    new-instance p1, Lkb1/F$b;

    .line 80
    .line 81
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 82
    .line 83
    .line 84
    move-result-object v0

    .line 85
    invoke-direct {p1, v0}, Lkb1/F$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 86
    .line 87
    .line 88
    goto :goto_0

    .line 89
    :cond_2
    instance-of p1, v0, Lkb1/E$a;

    .line 90
    .line 91
    if-eqz p1, :cond_3

    .line 92
    .line 93
    new-instance p1, Lkb1/F$a;

    .line 94
    .line 95
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 96
    .line 97
    .line 98
    move-result-object v0

    .line 99
    invoke-direct {p1, v0}, Lkb1/F$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 100
    .line 101
    .line 102
    :goto_0
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 103
    .line 104
    .line 105
    move-result-object p1

    .line 106
    goto :goto_2

    .line 107
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 108
    .line 109
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 110
    .line 111
    .line 112
    throw p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 113
    :goto_1
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 114
    .line 115
    invoke-static {p1}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 120
    .line 121
    .line 122
    move-result-object p1

    .line 123
    :goto_2
    invoke-static {p1}, Lkotlin/Result;->exceptionOrNull-impl(Ljava/lang/Object;)Ljava/lang/Throwable;

    .line 124
    .line 125
    .line 126
    move-result-object v0

    .line 127
    if-nez v0, :cond_4

    .line 128
    .line 129
    goto :goto_3

    .line 130
    :cond_4
    invoke-static {v1, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->W3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/Throwable;)V

    .line 131
    .line 132
    .line 133
    new-instance p1, Lkb1/F$c;

    .line 134
    .line 135
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LSX0/c;

    .line 136
    .line 137
    .line 138
    move-result-object v2

    .line 139
    sget-object v3, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 140
    .line 141
    sget v8, Lpb/k;->data_retrieval_error:I

    .line 142
    .line 143
    const/16 v12, 0x1de

    .line 144
    .line 145
    const/4 v13, 0x0

    .line 146
    const/4 v4, 0x0

    .line 147
    const/4 v5, 0x0

    .line 148
    const/4 v6, 0x0

    .line 149
    const/4 v7, 0x0

    .line 150
    const/4 v9, 0x0

    .line 151
    const/4 v10, 0x0

    .line 152
    const/4 v11, 0x0

    .line 153
    invoke-static/range {v2 .. v13}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 154
    .line 155
    .line 156
    move-result-object v0

    .line 157
    invoke-direct {p1, v0}, Lkb1/F$c;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 158
    .line 159
    .line 160
    :goto_3
    check-cast p1, Lkb1/F;

    .line 161
    .line 162
    return-object p1

    .line 163
    :cond_5
    instance-of p1, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$b;

    .line 164
    .line 165
    if-eqz p1, :cond_6

    .line 166
    .line 167
    instance-of v2, v0, Lkb1/E$a;

    .line 168
    .line 169
    if-eqz v2, :cond_6

    .line 170
    .line 171
    new-instance p1, Lkb1/F$a;

    .line 172
    .line 173
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 174
    .line 175
    .line 176
    move-result-object v0

    .line 177
    invoke-direct {p1, v0}, Lkb1/F$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 178
    .line 179
    .line 180
    return-object p1

    .line 181
    :cond_6
    if-eqz p1, :cond_7

    .line 182
    .line 183
    instance-of p1, v0, Lkb1/E$b;

    .line 184
    .line 185
    if-eqz p1, :cond_7

    .line 186
    .line 187
    new-instance p1, Lkb1/F$b;

    .line 188
    .line 189
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 190
    .line 191
    .line 192
    move-result-object v0

    .line 193
    invoke-direct {p1, v0}, Lkb1/F$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 194
    .line 195
    .line 196
    return-object p1

    .line 197
    :cond_7
    sget-object p1, Lkb1/F$e;->a:Lkb1/F$e;

    .line 198
    .line 199
    return-object p1

    .line 200
    :cond_8
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 201
    .line 202
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 203
    .line 204
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 205
    .line 206
    .line 207
    throw p1
.end method
