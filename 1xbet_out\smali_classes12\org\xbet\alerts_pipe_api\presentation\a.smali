.class public final Lorg/xbet/alerts_pipe_api/presentation/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0003\u001a\u0011\u0010\u0002\u001a\u00020\u0001*\u00020\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;",
        "",
        "a",
        "(Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)I",
        "api_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)I
    .locals 1
    .param p0    # Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p0, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const/4 p0, 0x0

    .line 6
    return p0

    .line 7
    :cond_0
    sget-object v0, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$ActivatePhoneKzAlert;->INSTANCE:Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$ActivatePhoneKzAlert;

    .line 8
    .line 9
    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-eqz v0, :cond_1

    .line 14
    .line 15
    const/4 p0, 0x1

    .line 16
    return p0

    .line 17
    :cond_1
    sget-object v0, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;->INSTANCE:Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;

    .line 18
    .line 19
    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    if-eqz v0, :cond_2

    .line 24
    .line 25
    const/4 p0, 0x2

    .line 26
    return p0

    .line 27
    :cond_2
    sget-object v0, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$KzFirstDepositBottom;->INSTANCE:Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$KzFirstDepositBottom;

    .line 28
    .line 29
    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    if-eqz v0, :cond_3

    .line 34
    .line 35
    const/4 p0, 0x3

    .line 36
    return p0

    .line 37
    :cond_3
    instance-of p0, p0, Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$NotIdentifiedKzAlert;

    .line 38
    .line 39
    if-eqz p0, :cond_4

    .line 40
    .line 41
    const/4 p0, 0x4

    .line 42
    return p0

    .line 43
    :cond_4
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 44
    .line 45
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 46
    .line 47
    .line 48
    throw p0
.end method
