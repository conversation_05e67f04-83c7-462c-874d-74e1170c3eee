.class public final synthetic LL21/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentActive;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentActive;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LL21/a;->a:Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentActive;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LL21/a;->a:Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentActive;

    invoke-static {v0}, Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentActive;->f(Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentActive;)Lorg/xbet/uikit/utils/z;

    move-result-object v0

    return-object v0
.end method
