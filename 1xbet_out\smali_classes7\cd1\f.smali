.class public final synthetic Lcd1/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/content/DialogInterface$OnClickListener;


# instance fields
.field public final synthetic a:Lru/ok/android/sdk/OkAuthActivity;

.field public final synthetic b:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Lru/ok/android/sdk/OkAuthActivity;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcd1/f;->a:Lru/ok/android/sdk/OkAuthActivity;

    iput-object p2, p0, Lcd1/f;->b:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/content/DialogInterface;I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcd1/f;->a:Lru/ok/android/sdk/OkAuthActivity;

    iget-object v1, p0, Lcd1/f;->b:<PERSON>java/lang/String;

    invoke-static {v0, v1, p1, p2}, Lru/ok/android/sdk/OkAuthActivity;->b(Lru/ok/android/sdk/OkAuthActivity;Ljava/lang/String;Landroid/content/DialogInterface;I)V

    return-void
.end method
