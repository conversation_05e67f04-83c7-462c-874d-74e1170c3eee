.class final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.AggregatorGiftsViewModel$openGame$3"
    f = "AggregatorGiftsViewModel.kt"
    l = {
        0x1dd
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->L5(Lorg/xplatform/aggregator/core/AggregatorGame;J)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $balanceId:J

.field final synthetic $game:Lorg/xplatform/aggregator/core/AggregatorGame;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lorg/xplatform/aggregator/core/AggregatorGame;JLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;",
            "Lorg/xplatform/aggregator/core/AggregatorGame;",
            "J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->$game:Lorg/xplatform/aggregator/core/AggregatorGame;

    iput-wide p3, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->$balanceId:J

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->$game:Lorg/xplatform/aggregator/core/AggregatorGame;

    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->$balanceId:J

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lorg/xplatform/aggregator/core/AggregatorGame;JLkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 28
    .line 29
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Z5()V

    .line 30
    .line 31
    .line 32
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 33
    .line 34
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->q4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xplatform/aggregator/impl/gifts/usecases/a;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->$game:Lorg/xplatform/aggregator/core/AggregatorGame;

    .line 39
    .line 40
    invoke-virtual {v1}, Lorg/xplatform/aggregator/core/AggregatorGame;->getId()J

    .line 41
    .line 42
    .line 43
    move-result-wide v3

    .line 44
    iput v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->label:I

    .line 45
    .line 46
    invoke-virtual {p1, v3, v4, p0}, Lorg/xplatform/aggregator/impl/gifts/usecases/a;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    if-ne p1, v0, :cond_2

    .line 51
    .line 52
    return-object v0

    .line 53
    :cond_2
    :goto_0
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 54
    .line 55
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->x4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$c;

    .line 60
    .line 61
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->$game:Lorg/xplatform/aggregator/core/AggregatorGame;

    .line 62
    .line 63
    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;->$balanceId:J

    .line 64
    .line 65
    invoke-static {v2, v3}, LHc/a;->f(J)Ljava/lang/Long;

    .line 66
    .line 67
    .line 68
    move-result-object v2

    .line 69
    invoke-static {v1, v2}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$c;-><init>(Lkotlin/Pair;)V

    .line 74
    .line 75
    .line 76
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 77
    .line 78
    .line 79
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 80
    .line 81
    return-object p1
.end method
