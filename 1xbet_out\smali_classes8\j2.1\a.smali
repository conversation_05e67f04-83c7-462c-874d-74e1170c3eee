.class public final Lj2/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LN1/r;


# instance fields
.field public final a:LN1/O;


# direct methods
.method public constructor <init>()V
    .locals 4

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, LN1/O;

    .line 5
    .line 6
    const/4 v1, 0x2

    .line 7
    const-string v2, "image/png"

    .line 8
    .line 9
    const v3, 0x8950

    .line 10
    .line 11
    .line 12
    invoke-direct {v0, v3, v1, v2}, LN1/O;-><init>(IILjava/lang/String;)V

    .line 13
    .line 14
    .line 15
    iput-object v0, p0, Lj2/a;->a:LN1/O;

    .line 16
    .line 17
    return-void
.end method


# virtual methods
.method public a(JJ)V
    .locals 1

    .line 1
    iget-object v0, p0, Lj2/a;->a:LN1/O;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2, p3, p4}, LN1/O;->a(JJ)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public b(LN1/t;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lj2/a;->a:LN1/O;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LN1/O;->b(LN1/t;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public synthetic d()LN1/r;
    .locals 1

    .line 1
    invoke-static {p0}, LN1/q;->b(LN1/r;)LN1/r;

    move-result-object v0

    return-object v0
.end method

.method public synthetic f()Ljava/util/List;
    .locals 1

    .line 1
    invoke-static {p0}, LN1/q;->a(LN1/r;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public h(LN1/s;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lj2/a;->a:LN1/O;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LN1/O;->h(LN1/s;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public j(LN1/s;LN1/L;)I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lj2/a;->a:LN1/O;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, LN1/O;->j(LN1/s;LN1/L;)I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public release()V
    .locals 0

    .line 1
    return-void
.end method
