.class public interface abstract Lk2/s;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lk2/s$b;,
        Lk2/s$a;
    }
.end annotation


# virtual methods
.method public abstract a()I
.end method

.method public abstract b([BII)Lk2/k;
.end method

.method public abstract c([BIILk2/s$b;Lt1/l;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([BII",
            "Lk2/s$b;",
            "Lt1/l<",
            "Lk2/e;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract reset()V
.end method
