.class public final Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt;->g(LOc/n;Lkotlin/jvm/functions/Function2;LOc/n;)LA4/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function1<",
        "Ljava/util/List<",
        "+",
        "Ljava/lang/Object;",
        ">;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:LB4/a;


# direct methods
.method public constructor <init>(LB4/a;LB4/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$a;->a:LB4/a;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$a;->b:LB4/a;

    .line 4
    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$a;->a:LB4/a;

    .line 8
    .line 9
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt;->f(LB4/a;)V

    .line 10
    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    .line 14
    .line 15
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 16
    .line 17
    .line 18
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v1

    .line 26
    if-eqz v1, :cond_1

    .line 27
    .line 28
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    check-cast v1, Ljava/util/Collection;

    .line 33
    .line 34
    check-cast v1, Ljava/lang/Iterable;

    .line 35
    .line 36
    invoke-static {v0, v1}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 37
    .line 38
    .line 39
    goto :goto_0

    .line 40
    :cond_1
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 45
    .line 46
    .line 47
    move-result v0

    .line 48
    if-eqz v0, :cond_5

    .line 49
    .line 50
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    check-cast v0, Lma1/b$a;

    .line 55
    .line 56
    sget-object v1, Lma1/b$a$a;->a:Lma1/b$a$a;

    .line 57
    .line 58
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 59
    .line 60
    .line 61
    move-result v1

    .line 62
    if-eqz v1, :cond_2

    .line 63
    .line 64
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$a;->b:LB4/a;

    .line 65
    .line 66
    invoke-virtual {v0}, LB4/a;->e()LL2/a;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    check-cast v0, LS91/v0;

    .line 71
    .line 72
    iget-object v0, v0, LS91/v0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 73
    .line 74
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$a;->b:LB4/a;

    .line 75
    .line 76
    invoke-virtual {v1}, LB4/a;->i()Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object v1

    .line 80
    check-cast v1, Lma1/b;

    .line 81
    .line 82
    invoke-virtual {v1}, Lma1/b;->f()Ljava/util/List;

    .line 83
    .line 84
    .line 85
    move-result-object v1

    .line 86
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setItems(Ljava/util/List;)V

    .line 87
    .line 88
    .line 89
    goto :goto_1

    .line 90
    :cond_2
    sget-object v1, Lma1/b$a$c;->a:Lma1/b$a$c;

    .line 91
    .line 92
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 93
    .line 94
    .line 95
    move-result v1

    .line 96
    if-eqz v1, :cond_3

    .line 97
    .line 98
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$a;->b:LB4/a;

    .line 99
    .line 100
    invoke-virtual {v0}, LB4/a;->e()LL2/a;

    .line 101
    .line 102
    .line 103
    move-result-object v0

    .line 104
    check-cast v0, LS91/v0;

    .line 105
    .line 106
    iget-object v0, v0, LS91/v0;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 107
    .line 108
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$a;->b:LB4/a;

    .line 109
    .line 110
    invoke-virtual {v1}, LB4/a;->i()Ljava/lang/Object;

    .line 111
    .line 112
    .line 113
    move-result-object v1

    .line 114
    check-cast v1, Lma1/b;

    .line 115
    .line 116
    invoke-virtual {v1}, Lma1/b;->getTitle()Ljava/lang/String;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/header/DSHeader;->setLabel(Ljava/lang/CharSequence;)V

    .line 121
    .line 122
    .line 123
    goto :goto_1

    .line 124
    :cond_3
    sget-object v1, Lma1/b$a$b;->a:Lma1/b$a$b;

    .line 125
    .line 126
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 127
    .line 128
    .line 129
    move-result v0

    .line 130
    if-eqz v0, :cond_4

    .line 131
    .line 132
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$a;->b:LB4/a;

    .line 133
    .line 134
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt;->f(LB4/a;)V

    .line 135
    .line 136
    .line 137
    goto :goto_1

    .line 138
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 139
    .line 140
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 141
    .line 142
    .line 143
    throw p1

    .line 144
    :cond_5
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/GiftsCategoryAdapterDelegateKt$a;->a(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p1
.end method
