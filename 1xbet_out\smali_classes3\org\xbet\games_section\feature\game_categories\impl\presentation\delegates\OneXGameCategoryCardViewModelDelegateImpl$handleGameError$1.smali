.class final synthetic Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$handleGameError$1;
.super Lkotlin/jvm/internal/FunctionReferenceImpl;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->L(Ljava/lang/Throwable;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1001
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/FunctionReferenceImpl;",
        "Lkotlin/jvm/functions/Function1<",
        "Ljava/lang/Throwable;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final INSTANCE:Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$handleGameError$1;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$handleGameError$1;

    invoke-direct {v0}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$handleGameError$1;-><init>()V

    sput-object v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$handleGameError$1;->INSTANCE:Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$handleGameError$1;

    return-void
.end method

.method public constructor <init>()V
    .locals 6

    const-string v4, "printStackTrace()V"

    const/4 v5, 0x0

    const/4 v1, 0x1

    const-class v2, Ljava/lang/Throwable;

    const-string v3, "printStackTrace"

    move-object v0, p0

    invoke-direct/range {v0 .. v5}, Lkotlin/jvm/internal/FunctionReferenceImpl;-><init>(ILjava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Throwable;

    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$handleGameError$1;->invoke(Ljava/lang/Throwable;)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1
.end method

.method public final invoke(Ljava/lang/Throwable;)V
    .locals 0

    .line 2
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    return-void
.end method
