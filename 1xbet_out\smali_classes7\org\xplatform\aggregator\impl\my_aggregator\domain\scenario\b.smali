.class public final Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Le81/d;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0018\u00002\u00020\u0001B!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001a\u0010\r\u001a\u0004\u0018\u00010\u000c2\u0006\u0010\u000b\u001a\u00020\nH\u0096B\u00a2\u0006\u0004\u0008\r\u0010\u000eR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\u000fR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u0011R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u0013\u00a8\u0006\u0014"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/b;",
        "Le81/d;",
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/GetGameToOpenUseCase;",
        "getGameToOpenUseCase",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "remoteConfigUseCase",
        "Li8/j;",
        "getServiceUseCase",
        "<init>",
        "(Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/GetGameToOpenUseCase;Lorg/xbet/remoteconfig/domain/usecases/i;Li8/j;)V",
        "",
        "gameId",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "a",
        "(JLkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/GetGameToOpenUseCase;",
        "b",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "c",
        "Li8/j;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/GetGameToOpenUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Li8/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/GetGameToOpenUseCase;Lorg/xbet/remoteconfig/domain/usecases/i;Li8/j;)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/GetGameToOpenUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/b;->a:Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/GetGameToOpenUseCase;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/b;->b:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/b;->c:Li8/j;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public a(JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 6
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/b;->a:Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/GetGameToOpenUseCase;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/b;->b:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 4
    .line 5
    invoke-interface {v1}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v1}, Lek0/o;->o()Lek0/a;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v1}, Lek0/a;->c()Z

    .line 14
    .line 15
    .line 16
    move-result v3

    .line 17
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/b;->c:Li8/j;

    .line 18
    .line 19
    invoke-interface {v1}, Li8/j;->invoke()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v4

    .line 23
    move-wide v1, p1

    .line 24
    move-object v5, p3

    .line 25
    invoke-virtual/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/GetGameToOpenUseCase;->a(JZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    return-object p1
.end method
