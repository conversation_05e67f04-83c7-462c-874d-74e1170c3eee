.class public final synthetic LIB0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;

.field public final synthetic b:Lorg/xbet/ui_common/router/NavBarScreenTypes;

.field public final synthetic c:Landroid/app/Application;

.field public final synthetic d:LKA0/c;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;Lorg/xbet/ui_common/router/NavBarScreenTypes;Landroid/app/Application;LKA0/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LIB0/d;->a:Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;

    iput-object p2, p0, LIB0/d;->b:Lorg/xbet/ui_common/router/NavBarScreenTypes;

    iput-object p3, p0, LIB0/d;->c:Landroid/app/Application;

    iput-object p4, p0, LIB0/d;->d:LKA0/c;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 4

    .line 1
    iget-object v0, p0, LIB0/d;->a:Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;

    iget-object v1, p0, LIB0/d;->b:Lorg/xbet/ui_common/router/NavBarScreenTypes;

    iget-object v2, p0, LIB0/d;->c:Landroid/app/Application;

    iget-object v3, p0, LIB0/d;->d:LKA0/c;

    invoke-static {v0, v1, v2, v3}, LIB0/e;->a(Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;Lorg/xbet/ui_common/router/NavBarScreenTypes;Landroid/app/Application;LKA0/c;)LGB0/d;

    move-result-object v0

    return-object v0
.end method
