.class public final Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$a;,
        Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000n\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u0000 !2\u00020\u0001:\u0002$\"B1\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0013\u0010\u0010\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u000e\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J9\u0010\u001c\u001a\u00020\u001a2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u00162\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u001a0\u0018\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u0017\u0010\u001f\u001a\u00020\u001e2\u0006\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 J;\u0010!\u001a\u00020\u001a2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u00162\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u001a0\u0018H\u0002\u00a2\u0006\u0004\u0008!\u0010\u001dR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010#R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010%R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010(R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001f\u0010)R\u001a\u0010,\u001a\u0008\u0012\u0004\u0012\u00020\u000f0*8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010+R\u0018\u00100\u001a\u0004\u0018\u00010-8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008.\u0010/\u00a8\u00061"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;",
        "",
        "LwX0/C;",
        "routerHolder",
        "Lc81/c;",
        "aggregatorScreenProvider",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "openGameDelegate",
        "LVg0/a;",
        "promotionsNewsScreenFactory",
        "Lm8/a;",
        "dispatchers",
        "<init>",
        "(LwX0/C;Lc81/c;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LVg0/a;Lm8/a;)V",
        "Lkotlinx/coroutines/flow/Z;",
        "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;",
        "d",
        "()Lkotlinx/coroutines/flow/Z;",
        "Lorg/xplatform/banners/api/domain/models/BannerModel;",
        "banner",
        "",
        "position",
        "Lkotlinx/coroutines/N;",
        "coroutineScope",
        "Lkotlin/Function1;",
        "",
        "",
        "errorHandler",
        "f",
        "(Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V",
        "",
        "e",
        "(Lorg/xplatform/banners/api/domain/models/BannerModel;)Z",
        "h",
        "a",
        "LwX0/C;",
        "b",
        "Lc81/c;",
        "c",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "LVg0/a;",
        "Lm8/a;",
        "Lkotlinx/coroutines/flow/U;",
        "Lkotlinx/coroutines/flow/U;",
        "bannersSharedFlow",
        "Lkotlinx/coroutines/x0;",
        "g",
        "Lkotlinx/coroutines/x0;",
        "openBannerJob",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final h:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lc81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:LVg0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public g:Lkotlinx/coroutines/x0;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->h:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$a;

    return-void
.end method

.method public constructor <init>(LwX0/C;Lc81/c;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LVg0/a;Lm8/a;)V
    .locals 0
    .param p1    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lc81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LVg0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->a:LwX0/C;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->b:Lc81/c;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->c:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->d:LVg0/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->e:Lm8/a;

    .line 13
    .line 14
    invoke-static {}, Lorg/xbet/ui_common/utils/flows/c;->a()Lkotlinx/coroutines/flow/U;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->f:Lkotlinx/coroutines/flow/U;

    .line 19
    .line 20
    return-void
.end method

.method public static synthetic a(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->g(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic b(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->f:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic c(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;)LVg0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->d:LVg0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final g(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->h(Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method


# virtual methods
.method public final d()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->f:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->d(Lkotlinx/coroutines/flow/U;)Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final e(Lorg/xplatform/banners/api/domain/models/BannerModel;)Z
    .locals 4

    .line 1
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getAction()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getDeeplink()Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-lez v0, :cond_0

    .line 17
    .line 18
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getDeeplink()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    const/4 v2, 0x2

    .line 31
    const/4 v3, 0x0

    .line 32
    invoke-static {v0, p1, v1, v2, v3}, Lkotlin/text/StringsKt;->i0(Ljava/lang/CharSequence;Ljava/lang/CharSequence;ZILjava/lang/Object;)Z

    .line 33
    .line 34
    .line 35
    move-result p1

    .line 36
    if-nez p1, :cond_0

    .line 37
    .line 38
    const/4 p1, 0x1

    .line 39
    return p1

    .line 40
    :cond_0
    return v1
.end method

.method public final f(Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V
    .locals 12
    .param p1    # Lorg/xplatform/banners/api/domain/models/BannerModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlinx/coroutines/N;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            "I",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-static {p1}, Lq81/a;->b(Lorg/xplatform/banners/api/domain/models/BannerModel;)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1}, Lq81/a;->a(Lorg/xplatform/banners/api/domain/models/BannerModel;)Z

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    if-eqz v2, :cond_1

    .line 10
    .line 11
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    if-lez v2, :cond_1

    .line 16
    .line 17
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->c:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 18
    .line 19
    invoke-static {v0}, Lkotlin/text/StringsKt;->y(Ljava/lang/String;)Ljava/lang/Long;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    if-eqz v0, :cond_0

    .line 24
    .line 25
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 26
    .line 27
    .line 28
    move-result-wide v4

    .line 29
    goto :goto_0

    .line 30
    :cond_0
    const-wide/high16 v4, -0x8000000000000000L

    .line 31
    .line 32
    :goto_0
    const/16 v10, 0x10

    .line 33
    .line 34
    const/4 v11, 0x0

    .line 35
    const/16 v6, 0x1fb8

    .line 36
    .line 37
    const/4 v9, 0x0

    .line 38
    move-object v7, p3

    .line 39
    move-object/from16 v8, p4

    .line 40
    .line 41
    invoke-static/range {v3 .. v11}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->v(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;JILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    return-void

    .line 45
    :cond_1
    invoke-virtual/range {p0 .. p1}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->e(Lorg/xplatform/banners/api/domain/models/BannerModel;)Z

    .line 46
    .line 47
    .line 48
    move-result v0

    .line 49
    if-eqz v0, :cond_2

    .line 50
    .line 51
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->f:Lkotlinx/coroutines/flow/U;

    .line 52
    .line 53
    new-instance v2, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b$a;

    .line 54
    .line 55
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getDeeplink()Ljava/lang/String;

    .line 56
    .line 57
    .line 58
    move-result-object v3

    .line 59
    invoke-direct {v2, v3}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b$a;-><init>(Ljava/lang/String;)V

    .line 60
    .line 61
    .line 62
    invoke-interface {v0, v2}, Lkotlinx/coroutines/flow/U;->d(Ljava/lang/Object;)Z

    .line 63
    .line 64
    .line 65
    return-void

    .line 66
    :cond_2
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getAction()Z

    .line 67
    .line 68
    .line 69
    move-result v0

    .line 70
    if-eqz v0, :cond_3

    .line 71
    .line 72
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getSiteLink()Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 77
    .line 78
    .line 79
    move-result v0

    .line 80
    if-lez v0, :cond_3

    .line 81
    .line 82
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->a:LwX0/C;

    .line 83
    .line 84
    invoke-virtual {v0}, LwX0/D;->a()LwX0/c;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    if-eqz v0, :cond_4

    .line 89
    .line 90
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->b:Lc81/c;

    .line 91
    .line 92
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getSiteLink()Ljava/lang/String;

    .line 93
    .line 94
    .line 95
    move-result-object v3

    .line 96
    invoke-interface {v2, v3}, Lc81/c;->a(Ljava/lang/String;)Lq4/q;

    .line 97
    .line 98
    .line 99
    move-result-object v2

    .line 100
    invoke-virtual {v0, v2}, LwX0/c;->m(Lq4/q;)V

    .line 101
    .line 102
    .line 103
    return-void

    .line 104
    :cond_3
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getActionType()Lorg/xplatform/banners/api/domain/models/BannerActionType;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    sget-object v2, Lorg/xplatform/banners/api/domain/models/BannerActionType;->ACTION_ONE_X_GAME:Lorg/xplatform/banners/api/domain/models/BannerActionType;

    .line 109
    .line 110
    if-ne v0, v2, :cond_5

    .line 111
    .line 112
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->a:LwX0/C;

    .line 113
    .line 114
    invoke-virtual {v0}, LwX0/D;->a()LwX0/c;

    .line 115
    .line 116
    .line 117
    move-result-object v6

    .line 118
    if-eqz v6, :cond_4

    .line 119
    .line 120
    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/a;

    .line 121
    .line 122
    move-object v1, p0

    .line 123
    move-object v2, p1

    .line 124
    move v3, p2

    .line 125
    move-object v4, p3

    .line 126
    move-object/from16 v5, p4

    .line 127
    .line 128
    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/core/presentation/a;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V

    .line 129
    .line 130
    .line 131
    invoke-virtual {v6, v0}, LwX0/c;->l(Lkotlin/jvm/functions/Function0;)V

    .line 132
    .line 133
    .line 134
    :cond_4
    return-void

    .line 135
    :cond_5
    invoke-virtual/range {p0 .. p4}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->h(Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V

    .line 136
    .line 137
    .line 138
    return-void
.end method

.method public final h(Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            "I",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->g:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    const/4 v2, 0x1

    .line 7
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    new-instance v4, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$1;

    .line 11
    .line 12
    invoke-direct {v4, p4}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$1;-><init>(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    iget-object p4, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->e:Lm8/a;

    .line 16
    .line 17
    invoke-interface {p4}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 18
    .line 19
    .line 20
    move-result-object v6

    .line 21
    new-instance v8, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;

    .line 22
    .line 23
    invoke-direct {v8, p0, p1, p2, v1}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    const/16 v9, 0xa

    .line 27
    .line 28
    const/4 v10, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v7, 0x0

    .line 31
    move-object v3, p3

    .line 32
    invoke-static/range {v3 .. v10}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->g:Lkotlinx/coroutines/x0;

    .line 37
    .line 38
    return-void
.end method
