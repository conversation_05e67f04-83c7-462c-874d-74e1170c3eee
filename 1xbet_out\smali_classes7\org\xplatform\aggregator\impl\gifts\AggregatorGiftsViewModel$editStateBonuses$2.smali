.class final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.AggregatorGiftsViewModel$editStateBonuses$2"
    f = "AggregatorGiftsViewModel.kt"
    l = {
        0x1e4
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->l5(Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $bonusId:I

.field final synthetic $state:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;",
            "Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->$state:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    iput p3, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->$bonusId:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->$state:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    iget v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->$bonusId:I

    invoke-direct {p1, v0, v1, v2, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;ILkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    move-object v8, p0

    .line 16
    goto :goto_0

    .line 17
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 18
    .line 19
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 20
    .line 21
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    throw p1

    .line 25
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 29
    .line 30
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->w4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xplatform/aggregator/impl/gifts/usecases/h;

    .line 31
    .line 32
    .line 33
    move-result-object v3

    .line 34
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 35
    .line 36
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->u4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)J

    .line 37
    .line 38
    .line 39
    move-result-wide v4

    .line 40
    iget-object v6, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->$state:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 41
    .line 42
    iget v7, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->$bonusId:I

    .line 43
    .line 44
    iput v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->label:I

    .line 45
    .line 46
    move-object v8, p0

    .line 47
    invoke-virtual/range {v3 .. v8}, Lorg/xplatform/aggregator/impl/gifts/usecases/h;->a(JLorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;ILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    if-ne p1, v0, :cond_2

    .line 52
    .line 53
    return-object v0

    .line 54
    :cond_2
    :goto_0
    iget v0, v8, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->$bonusId:I

    .line 55
    .line 56
    check-cast p1, Lxa1/b;

    .line 57
    .line 58
    invoke-virtual {p1}, Lxa1/b;->a()Ljava/util/List;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    :cond_3
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 67
    .line 68
    .line 69
    move-result v1

    .line 70
    const/4 v2, 0x0

    .line 71
    if-eqz v1, :cond_4

    .line 72
    .line 73
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    move-result-object v1

    .line 77
    move-object v3, v1

    .line 78
    check-cast v3, Lxa1/a;

    .line 79
    .line 80
    invoke-virtual {v3}, Lxa1/a;->h()I

    .line 81
    .line 82
    .line 83
    move-result v3

    .line 84
    if-ne v3, v0, :cond_3

    .line 85
    .line 86
    goto :goto_1

    .line 87
    :cond_4
    move-object v1, v2

    .line 88
    :goto_1
    check-cast v1, Lxa1/a;

    .line 89
    .line 90
    if-eqz v1, :cond_5

    .line 91
    .line 92
    invoke-virtual {v1}, Lxa1/a;->i()Lxa1/h;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    if-eqz p1, :cond_5

    .line 97
    .line 98
    invoke-virtual {p1}, Lxa1/h;->a()Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 99
    .line 100
    .line 101
    move-result-object v2

    .line 102
    :cond_5
    sget-object p1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->ACTIVE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 103
    .line 104
    if-ne v2, p1, :cond_6

    .line 105
    .line 106
    iget-object p1, v8, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 107
    .line 108
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->E4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xbet/analytics/domain/scope/T;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    iget v0, v8, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->$bonusId:I

    .line 113
    .line 114
    invoke-virtual {p1, v0}, Lorg/xbet/analytics/domain/scope/T;->c(I)V

    .line 115
    .line 116
    .line 117
    iget-object p1, v8, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 118
    .line 119
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->J4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)LUR/a;

    .line 120
    .line 121
    .line 122
    move-result-object p1

    .line 123
    iget v0, v8, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->$bonusId:I

    .line 124
    .line 125
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 126
    .line 127
    .line 128
    move-result-object v0

    .line 129
    const-string v1, "CasinoGiftsFragment"

    .line 130
    .line 131
    invoke-interface {p1, v1, v0}, LUR/a;->I(Ljava/lang/String;Ljava/lang/String;)V

    .line 132
    .line 133
    .line 134
    iget-object p1, v8, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 135
    .line 136
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->x4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 137
    .line 138
    .line 139
    move-result-object p1

    .line 140
    sget-object v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$d;->a:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$d;

    .line 141
    .line 142
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 143
    .line 144
    .line 145
    :cond_6
    iget-object p1, v8, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 146
    .line 147
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->g5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)V

    .line 148
    .line 149
    .line 150
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 151
    .line 152
    return-object p1
.end method
