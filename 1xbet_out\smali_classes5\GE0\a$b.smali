.class public final LGE0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LGE0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LGE0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LGE0/a$b$a;,
        LGE0/a$b$b;
    }
.end annotation


# instance fields
.field public final a:LGE0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/j;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDE0/b;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/game_events/data/repository/GameEventsRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/game_events/domain/c;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/game_events/domain/GetGameEventsScenarioImpl;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/f;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTn/a;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/GetSportUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/l;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/l;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/game_events/presentation/viewmodel/GameEventsViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LEN0/f;Lf8/g;LwX0/c;Ljava/lang/String;Ljava/lang/Long;Lorg/xbet/ui_common/utils/M;LHX0/e;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Li8/j;Lc8/h;LiS/a;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LGE0/a$b;->a:LGE0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p17}, LGE0/a$b;->b(LQW0/c;LEN0/f;Lf8/g;LwX0/c;Ljava/lang/String;Ljava/lang/Long;Lorg/xbet/ui_common/utils/M;LHX0/e;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Li8/j;Lc8/h;LiS/a;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LEN0/f;Lf8/g;LwX0/c;Ljava/lang/String;Ljava/lang/Long;Lorg/xbet/ui_common/utils/M;LHX0/e;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Li8/j;Lc8/h;LiS/a;LGE0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p17}, LGE0/a$b;-><init>(LQW0/c;LEN0/f;Lf8/g;LwX0/c;Ljava/lang/String;Ljava/lang/Long;Lorg/xbet/ui_common/utils/M;LHX0/e;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Li8/j;Lc8/h;LiS/a;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/game_events/presentation/fragment/GameEventsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LGE0/a$b;->c(Lorg/xbet/statistic/game_events/presentation/fragment/GameEventsFragment;)Lorg/xbet/statistic/game_events/presentation/fragment/GameEventsFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;LEN0/f;Lf8/g;LwX0/c;Ljava/lang/String;Ljava/lang/Long;Lorg/xbet/ui_common/utils/M;LHX0/e;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LSQ0/a;Li8/l;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Li8/j;Lc8/h;LiS/a;)V
    .locals 7

    .line 1
    invoke-static/range {p15 .. p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p4

    .line 5
    iput-object p4, p0, LGE0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    iput-object p3, p0, LGE0/a$b;->c:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p3}, LDE0/c;->a(LBc/a;)LDE0/c;

    .line 14
    .line 15
    .line 16
    move-result-object p3

    .line 17
    iput-object p3, p0, LGE0/a$b;->d:Ldagger/internal/h;

    .line 18
    .line 19
    new-instance p3, LGE0/a$b$a;

    .line 20
    .line 21
    invoke-direct {p3, p1}, LGE0/a$b$a;-><init>(LQW0/c;)V

    .line 22
    .line 23
    .line 24
    iput-object p3, p0, LGE0/a$b;->e:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static/range {p16 .. p16}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iput-object p1, p0, LGE0/a$b;->f:Ldagger/internal/h;

    .line 31
    .line 32
    iget-object p3, p0, LGE0/a$b;->d:Ldagger/internal/h;

    .line 33
    .line 34
    iget-object p4, p0, LGE0/a$b;->e:Ldagger/internal/h;

    .line 35
    .line 36
    invoke-static {p3, p4, p1}, Lorg/xbet/statistic/game_events/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/game_events/data/repository/a;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    iput-object p1, p0, LGE0/a$b;->g:Ldagger/internal/h;

    .line 41
    .line 42
    invoke-static {p1}, Lorg/xbet/statistic/game_events/domain/d;->a(LBc/a;)Lorg/xbet/statistic/game_events/domain/d;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    iput-object p1, p0, LGE0/a$b;->h:Ldagger/internal/h;

    .line 47
    .line 48
    iget-object p3, p0, LGE0/a$b;->b:Ldagger/internal/h;

    .line 49
    .line 50
    invoke-static {p3, p1}, Lorg/xbet/statistic/game_events/domain/b;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/game_events/domain/b;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    iput-object p1, p0, LGE0/a$b;->i:Ldagger/internal/h;

    .line 55
    .line 56
    invoke-static/range {p13 .. p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    iput-object p1, p0, LGE0/a$b;->j:Ldagger/internal/h;

    .line 61
    .line 62
    new-instance p1, LGE0/a$b$b;

    .line 63
    .line 64
    invoke-direct {p1, p2}, LGE0/a$b$b;-><init>(LEN0/f;)V

    .line 65
    .line 66
    .line 67
    iput-object p1, p0, LGE0/a$b;->k:Ldagger/internal/h;

    .line 68
    .line 69
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/g;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/g;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    iput-object p1, p0, LGE0/a$b;->l:Ldagger/internal/h;

    .line 74
    .line 75
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    iput-object p1, p0, LGE0/a$b;->m:Ldagger/internal/h;

    .line 80
    .line 81
    iget-object p2, p0, LGE0/a$b;->e:Ldagger/internal/h;

    .line 82
    .line 83
    invoke-static {p2, p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/h;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/h;

    .line 84
    .line 85
    .line 86
    move-result-object p1

    .line 87
    iput-object p1, p0, LGE0/a$b;->n:Ldagger/internal/h;

    .line 88
    .line 89
    iget-object p1, p0, LGE0/a$b;->k:Ldagger/internal/h;

    .line 90
    .line 91
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/j;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/j;

    .line 92
    .line 93
    .line 94
    move-result-object p1

    .line 95
    iput-object p1, p0, LGE0/a$b;->o:Ldagger/internal/h;

    .line 96
    .line 97
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 98
    .line 99
    .line 100
    move-result-object p1

    .line 101
    iput-object p1, p0, LGE0/a$b;->p:Ldagger/internal/h;

    .line 102
    .line 103
    iget-object p1, p0, LGE0/a$b;->k:Ldagger/internal/h;

    .line 104
    .line 105
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/m;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/m;

    .line 106
    .line 107
    .line 108
    move-result-object p1

    .line 109
    iput-object p1, p0, LGE0/a$b;->q:Ldagger/internal/h;

    .line 110
    .line 111
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 112
    .line 113
    .line 114
    move-result-object p1

    .line 115
    iput-object p1, p0, LGE0/a$b;->r:Ldagger/internal/h;

    .line 116
    .line 117
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 118
    .line 119
    .line 120
    move-result-object v6

    .line 121
    iput-object v6, p0, LGE0/a$b;->s:Ldagger/internal/h;

    .line 122
    .line 123
    iget-object v0, p0, LGE0/a$b;->l:Ldagger/internal/h;

    .line 124
    .line 125
    iget-object v1, p0, LGE0/a$b;->n:Ldagger/internal/h;

    .line 126
    .line 127
    iget-object v2, p0, LGE0/a$b;->o:Ldagger/internal/h;

    .line 128
    .line 129
    iget-object v3, p0, LGE0/a$b;->p:Ldagger/internal/h;

    .line 130
    .line 131
    iget-object v4, p0, LGE0/a$b;->q:Ldagger/internal/h;

    .line 132
    .line 133
    iget-object v5, p0, LGE0/a$b;->r:Ldagger/internal/h;

    .line 134
    .line 135
    invoke-static/range {v0 .. v6}, Lorg/xbet/statistic/statistic_core/presentation/delegates/o;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/o;

    .line 136
    .line 137
    .line 138
    move-result-object p1

    .line 139
    iput-object p1, p0, LGE0/a$b;->t:Ldagger/internal/h;

    .line 140
    .line 141
    invoke-static/range {p14 .. p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 142
    .line 143
    .line 144
    move-result-object p1

    .line 145
    iput-object p1, p0, LGE0/a$b;->u:Ldagger/internal/h;

    .line 146
    .line 147
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 148
    .line 149
    .line 150
    move-result-object p1

    .line 151
    iput-object p1, p0, LGE0/a$b;->v:Ldagger/internal/h;

    .line 152
    .line 153
    invoke-static/range {p12 .. p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 154
    .line 155
    .line 156
    move-result-object p1

    .line 157
    iput-object p1, p0, LGE0/a$b;->w:Ldagger/internal/h;

    .line 158
    .line 159
    iget-object p2, p0, LGE0/a$b;->i:Ldagger/internal/h;

    .line 160
    .line 161
    iget-object p3, p0, LGE0/a$b;->j:Ldagger/internal/h;

    .line 162
    .line 163
    iget-object p4, p0, LGE0/a$b;->t:Ldagger/internal/h;

    .line 164
    .line 165
    iget-object p5, p0, LGE0/a$b;->u:Ldagger/internal/h;

    .line 166
    .line 167
    iget-object p6, p0, LGE0/a$b;->p:Ldagger/internal/h;

    .line 168
    .line 169
    iget-object v0, p0, LGE0/a$b;->v:Ldagger/internal/h;

    .line 170
    .line 171
    iget-object v1, p0, LGE0/a$b;->s:Ldagger/internal/h;

    .line 172
    .line 173
    iget-object v2, p0, LGE0/a$b;->e:Ldagger/internal/h;

    .line 174
    .line 175
    move-object/from16 p9, p1

    .line 176
    .line 177
    move-object p7, v0

    .line 178
    move-object p8, v1

    .line 179
    move-object/from16 p10, v2

    .line 180
    .line 181
    invoke-static/range {p2 .. p10}, Lorg/xbet/statistic/game_events/presentation/viewmodel/c;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/game_events/presentation/viewmodel/c;

    .line 182
    .line 183
    .line 184
    move-result-object p1

    .line 185
    iput-object p1, p0, LGE0/a$b;->x:Ldagger/internal/h;

    .line 186
    .line 187
    return-void
.end method

.method public final c(Lorg/xbet/statistic/game_events/presentation/fragment/GameEventsFragment;)Lorg/xbet/statistic/game_events/presentation/fragment/GameEventsFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LGE0/a$b;->e()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/game_events/presentation/fragment/c;->a(Lorg/xbet/statistic/game_events/presentation/fragment/GameEventsFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final d()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/statistic/game_events/presentation/viewmodel/GameEventsViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LGE0/a$b;->x:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final e()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LGE0/a$b;->d()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
