.class public final LfP0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LfP0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LfP0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LfP0/a$b$a;,
        LfP0/a$b$e;,
        LfP0/a$b$b;,
        LfP0/a$b$c;,
        LfP0/a$b$d;,
        LfP0/a$b$g;,
        LfP0/a$b$f;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTn/a;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/GetSportUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/l;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public I:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/l;",
            ">;"
        }
    .end annotation
.end field

.field public J:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/team/impl/team_statistic/presentation/viewmodels/f;",
            ">;"
        }
    .end annotation
.end field

.field public K:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/team/impl/team_statistic/presentation/viewmodels/OneTeamStatisticMenuViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LSX0/a;

.field public final b:LfP0/a$b;

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LcP0/b;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/team/impl/team_statistic/data/repository/TeamStatisticsRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LiP0/a;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/scope/StatisticAnalytics;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LiS/a;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LfS/a;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LLo0/a;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHg/d;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LGL0/b;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDH0/a;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/team/impl/team_statistic/presentation/viewmodels/TeamMenuDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/f;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LiR/a;LJo0/a;LEN0/f;LGL0/a;LLD0/a;Ljava/lang/String;LHX0/e;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;Ljava/lang/Integer;Ljava/lang/Integer;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;Ljava/lang/Long;LSX0/a;Lc8/h;LHg/d;)V
    .locals 1

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LfP0/a$b;->b:LfP0/a$b;

    move-object/from16 v0, p22

    .line 4
    iput-object v0, p0, LfP0/a$b;->a:LSX0/a;

    .line 5
    invoke-virtual/range {p0 .. p24}, LfP0/a$b;->d(LQW0/c;LiR/a;LJo0/a;LEN0/f;LGL0/a;LLD0/a;Ljava/lang/String;LHX0/e;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;Ljava/lang/Integer;Ljava/lang/Integer;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;Ljava/lang/Long;LSX0/a;Lc8/h;LHg/d;)V

    .line 6
    invoke-virtual/range {p0 .. p24}, LfP0/a$b;->e(LQW0/c;LiR/a;LJo0/a;LEN0/f;LGL0/a;LLD0/a;Ljava/lang/String;LHX0/e;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;Ljava/lang/Integer;Ljava/lang/Integer;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;Ljava/lang/Long;LSX0/a;Lc8/h;LHg/d;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LiR/a;LJo0/a;LEN0/f;LGL0/a;LLD0/a;Ljava/lang/String;LHX0/e;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;Ljava/lang/Integer;Ljava/lang/Integer;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;Ljava/lang/Long;LSX0/a;Lc8/h;LHg/d;LfP0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p24}, LfP0/a$b;-><init>(LQW0/c;LiR/a;LJo0/a;LEN0/f;LGL0/a;LLD0/a;Ljava/lang/String;LHX0/e;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;Ljava/lang/Integer;Ljava/lang/Integer;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;Ljava/lang/Long;LSX0/a;Lc8/h;LHg/d;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/TeamStatisticMenuItemFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LfP0/a$b;->h(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/TeamStatisticMenuItemFragment;)Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/TeamStatisticMenuItemFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/TeamStatisticMenuFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LfP0/a$b;->g(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/TeamStatisticMenuFragment;)Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/TeamStatisticMenuFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public c(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/OneTeamStatisticMenuFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LfP0/a$b;->f(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/OneTeamStatisticMenuFragment;)Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/OneTeamStatisticMenuFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final d(LQW0/c;LiR/a;LJo0/a;LEN0/f;LGL0/a;LLD0/a;Ljava/lang/String;LHX0/e;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;Ljava/lang/Integer;Ljava/lang/Integer;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;Ljava/lang/Long;LSX0/a;Lc8/h;LHg/d;)V
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p2

    .line 4
    .line 5
    invoke-static/range {p11 .. p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    iput-object v2, v0, LfP0/a$b;->c:Ldagger/internal/h;

    .line 10
    .line 11
    invoke-static/range {p7 .. p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    iput-object v2, v0, LfP0/a$b;->d:Ldagger/internal/h;

    .line 16
    .line 17
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    iput-object v2, v0, LfP0/a$b;->e:Ldagger/internal/h;

    .line 22
    .line 23
    invoke-static {v2}, LcP0/c;->a(LBc/a;)LcP0/c;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    iput-object v2, v0, LfP0/a$b;->f:Ldagger/internal/h;

    .line 28
    .line 29
    new-instance v2, LfP0/a$b$a;

    .line 30
    .line 31
    move-object/from16 v3, p1

    .line 32
    .line 33
    invoke-direct {v2, v3}, LfP0/a$b$a;-><init>(LQW0/c;)V

    .line 34
    .line 35
    .line 36
    iput-object v2, v0, LfP0/a$b;->g:Ldagger/internal/h;

    .line 37
    .line 38
    invoke-static/range {p23 .. p23}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    iput-object v2, v0, LfP0/a$b;->h:Ldagger/internal/h;

    .line 43
    .line 44
    iget-object v3, v0, LfP0/a$b;->f:Ldagger/internal/h;

    .line 45
    .line 46
    iget-object v4, v0, LfP0/a$b;->g:Ldagger/internal/h;

    .line 47
    .line 48
    invoke-static {v3, v4, v2}, Lorg/xbet/statistic/team/impl/team_statistic/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/team/impl/team_statistic/data/repository/a;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    iput-object v2, v0, LfP0/a$b;->i:Ldagger/internal/h;

    .line 53
    .line 54
    invoke-static {v2}, LiP0/b;->a(LBc/a;)LiP0/b;

    .line 55
    .line 56
    .line 57
    move-result-object v2

    .line 58
    iput-object v2, v0, LfP0/a$b;->j:Ldagger/internal/h;

    .line 59
    .line 60
    invoke-static/range {p15 .. p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 61
    .line 62
    .line 63
    move-result-object v2

    .line 64
    iput-object v2, v0, LfP0/a$b;->k:Ldagger/internal/h;

    .line 65
    .line 66
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    iput-object v2, v0, LfP0/a$b;->l:Ldagger/internal/h;

    .line 71
    .line 72
    invoke-static/range {p20 .. p20}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 73
    .line 74
    .line 75
    move-result-object v2

    .line 76
    iput-object v2, v0, LfP0/a$b;->m:Ldagger/internal/h;

    .line 77
    .line 78
    new-instance v2, LfP0/a$b$e;

    .line 79
    .line 80
    invoke-direct {v2, v1}, LfP0/a$b$e;-><init>(LiR/a;)V

    .line 81
    .line 82
    .line 83
    iput-object v2, v0, LfP0/a$b;->n:Ldagger/internal/h;

    .line 84
    .line 85
    invoke-static/range {p22 .. p22}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 86
    .line 87
    .line 88
    move-result-object v2

    .line 89
    iput-object v2, v0, LfP0/a$b;->o:Ldagger/internal/h;

    .line 90
    .line 91
    invoke-static/range {p21 .. p21}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 92
    .line 93
    .line 94
    move-result-object v2

    .line 95
    iput-object v2, v0, LfP0/a$b;->p:Ldagger/internal/h;

    .line 96
    .line 97
    new-instance v2, LfP0/a$b$b;

    .line 98
    .line 99
    invoke-direct {v2, v1}, LfP0/a$b$b;-><init>(LiR/a;)V

    .line 100
    .line 101
    .line 102
    iput-object v2, v0, LfP0/a$b;->q:Ldagger/internal/h;

    .line 103
    .line 104
    new-instance v1, LfP0/a$b$c;

    .line 105
    .line 106
    move-object/from16 v2, p3

    .line 107
    .line 108
    invoke-direct {v1, v2}, LfP0/a$b$c;-><init>(LJo0/a;)V

    .line 109
    .line 110
    .line 111
    iput-object v1, v0, LfP0/a$b;->r:Ldagger/internal/h;

    .line 112
    .line 113
    invoke-static/range {p16 .. p16}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 114
    .line 115
    .line 116
    move-result-object v1

    .line 117
    iput-object v1, v0, LfP0/a$b;->s:Ldagger/internal/h;

    .line 118
    .line 119
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 120
    .line 121
    .line 122
    move-result-object v1

    .line 123
    iput-object v1, v0, LfP0/a$b;->t:Ldagger/internal/h;

    .line 124
    .line 125
    invoke-static/range {p24 .. p24}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 126
    .line 127
    .line 128
    move-result-object v1

    .line 129
    iput-object v1, v0, LfP0/a$b;->u:Ldagger/internal/h;

    .line 130
    .line 131
    new-instance v1, LfP0/a$b$d;

    .line 132
    .line 133
    move-object/from16 v2, p5

    .line 134
    .line 135
    invoke-direct {v1, v2}, LfP0/a$b$d;-><init>(LGL0/a;)V

    .line 136
    .line 137
    .line 138
    iput-object v1, v0, LfP0/a$b;->v:Ldagger/internal/h;

    .line 139
    .line 140
    new-instance v1, LfP0/a$b$g;

    .line 141
    .line 142
    move-object/from16 v2, p6

    .line 143
    .line 144
    invoke-direct {v1, v2}, LfP0/a$b$g;-><init>(LLD0/a;)V

    .line 145
    .line 146
    .line 147
    iput-object v1, v0, LfP0/a$b;->w:Ldagger/internal/h;

    .line 148
    .line 149
    iget-object v2, v0, LfP0/a$b;->d:Ldagger/internal/h;

    .line 150
    .line 151
    iget-object v3, v0, LfP0/a$b;->j:Ldagger/internal/h;

    .line 152
    .line 153
    iget-object v4, v0, LfP0/a$b;->k:Ldagger/internal/h;

    .line 154
    .line 155
    iget-object v5, v0, LfP0/a$b;->c:Ldagger/internal/h;

    .line 156
    .line 157
    iget-object v6, v0, LfP0/a$b;->l:Ldagger/internal/h;

    .line 158
    .line 159
    iget-object v7, v0, LfP0/a$b;->m:Ldagger/internal/h;

    .line 160
    .line 161
    iget-object v8, v0, LfP0/a$b;->n:Ldagger/internal/h;

    .line 162
    .line 163
    iget-object v9, v0, LfP0/a$b;->o:Ldagger/internal/h;

    .line 164
    .line 165
    iget-object v10, v0, LfP0/a$b;->p:Ldagger/internal/h;

    .line 166
    .line 167
    iget-object v11, v0, LfP0/a$b;->q:Ldagger/internal/h;

    .line 168
    .line 169
    iget-object v12, v0, LfP0/a$b;->r:Ldagger/internal/h;

    .line 170
    .line 171
    iget-object v13, v0, LfP0/a$b;->s:Ldagger/internal/h;

    .line 172
    .line 173
    iget-object v14, v0, LfP0/a$b;->t:Ldagger/internal/h;

    .line 174
    .line 175
    iget-object v15, v0, LfP0/a$b;->u:Ldagger/internal/h;

    .line 176
    .line 177
    move-object/from16 v17, v1

    .line 178
    .line 179
    iget-object v1, v0, LfP0/a$b;->v:Ldagger/internal/h;

    .line 180
    .line 181
    move-object/from16 v16, v1

    .line 182
    .line 183
    invoke-static/range {v2 .. v17}, Lorg/xbet/statistic/team/impl/team_statistic/presentation/viewmodels/d;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/team/impl/team_statistic/presentation/viewmodels/d;

    .line 184
    .line 185
    .line 186
    move-result-object v1

    .line 187
    iput-object v1, v0, LfP0/a$b;->x:Ldagger/internal/h;

    .line 188
    .line 189
    new-instance v1, LfP0/a$b$f;

    .line 190
    .line 191
    move-object/from16 v2, p4

    .line 192
    .line 193
    invoke-direct {v1, v2}, LfP0/a$b$f;-><init>(LEN0/f;)V

    .line 194
    .line 195
    .line 196
    iput-object v1, v0, LfP0/a$b;->y:Ldagger/internal/h;

    .line 197
    .line 198
    invoke-static {v1}, Lorg/xbet/statistic/statistic_core/domain/usecases/g;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/g;

    .line 199
    .line 200
    .line 201
    move-result-object v1

    .line 202
    iput-object v1, v0, LfP0/a$b;->z:Ldagger/internal/h;

    .line 203
    .line 204
    invoke-static/range {p12 .. p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 205
    .line 206
    .line 207
    move-result-object v1

    .line 208
    iput-object v1, v0, LfP0/a$b;->A:Ldagger/internal/h;

    .line 209
    .line 210
    return-void
.end method

.method public final e(LQW0/c;LiR/a;LJo0/a;LEN0/f;LGL0/a;LLD0/a;Ljava/lang/String;LHX0/e;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;Ljava/lang/Integer;Ljava/lang/Integer;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;Ljava/lang/Long;LSX0/a;Lc8/h;LHg/d;)V
    .locals 0

    .line 1
    iget-object p1, p0, LfP0/a$b;->g:Ldagger/internal/h;

    .line 2
    .line 3
    iget-object p2, p0, LfP0/a$b;->A:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {p1, p2}, Lorg/xbet/statistic/statistic_core/domain/usecases/h;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/h;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    iput-object p1, p0, LfP0/a$b;->B:Ldagger/internal/h;

    .line 10
    .line 11
    iget-object p1, p0, LfP0/a$b;->y:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/j;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/j;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iput-object p1, p0, LfP0/a$b;->C:Ldagger/internal/h;

    .line 18
    .line 19
    iget-object p1, p0, LfP0/a$b;->y:Ldagger/internal/h;

    .line 20
    .line 21
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/m;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/m;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    iput-object p1, p0, LfP0/a$b;->D:Ldagger/internal/h;

    .line 26
    .line 27
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    iput-object p1, p0, LfP0/a$b;->E:Ldagger/internal/h;

    .line 32
    .line 33
    invoke-static {p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 34
    .line 35
    .line 36
    move-result-object p8

    .line 37
    iput-object p8, p0, LfP0/a$b;->F:Ldagger/internal/h;

    .line 38
    .line 39
    iget-object p2, p0, LfP0/a$b;->z:Ldagger/internal/h;

    .line 40
    .line 41
    iget-object p3, p0, LfP0/a$b;->B:Ldagger/internal/h;

    .line 42
    .line 43
    iget-object p4, p0, LfP0/a$b;->C:Ldagger/internal/h;

    .line 44
    .line 45
    iget-object p5, p0, LfP0/a$b;->c:Ldagger/internal/h;

    .line 46
    .line 47
    iget-object p6, p0, LfP0/a$b;->D:Ldagger/internal/h;

    .line 48
    .line 49
    iget-object p7, p0, LfP0/a$b;->E:Ldagger/internal/h;

    .line 50
    .line 51
    invoke-static/range {p2 .. p8}, Lorg/xbet/statistic/statistic_core/presentation/delegates/o;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/o;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    iput-object p1, p0, LfP0/a$b;->G:Ldagger/internal/h;

    .line 56
    .line 57
    invoke-static/range {p18 .. p18}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    iput-object p1, p0, LfP0/a$b;->H:Ldagger/internal/h;

    .line 62
    .line 63
    invoke-static/range {p19 .. p19}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 64
    .line 65
    .line 66
    move-result-object p7

    .line 67
    iput-object p7, p0, LfP0/a$b;->I:Ldagger/internal/h;

    .line 68
    .line 69
    iget-object p2, p0, LfP0/a$b;->c:Ldagger/internal/h;

    .line 70
    .line 71
    iget-object p3, p0, LfP0/a$b;->x:Ldagger/internal/h;

    .line 72
    .line 73
    iget-object p4, p0, LfP0/a$b;->G:Ldagger/internal/h;

    .line 74
    .line 75
    iget-object p5, p0, LfP0/a$b;->H:Ldagger/internal/h;

    .line 76
    .line 77
    iget-object p6, p0, LfP0/a$b;->p:Ldagger/internal/h;

    .line 78
    .line 79
    iget-object p8, p0, LfP0/a$b;->g:Ldagger/internal/h;

    .line 80
    .line 81
    invoke-static/range {p2 .. p8}, Lorg/xbet/statistic/team/impl/team_statistic/presentation/viewmodels/g;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/team/impl/team_statistic/presentation/viewmodels/g;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    iput-object p1, p0, LfP0/a$b;->J:Ldagger/internal/h;

    .line 86
    .line 87
    iget-object p2, p0, LfP0/a$b;->B:Ldagger/internal/h;

    .line 88
    .line 89
    iget-object p3, p0, LfP0/a$b;->x:Ldagger/internal/h;

    .line 90
    .line 91
    iget-object p4, p0, LfP0/a$b;->H:Ldagger/internal/h;

    .line 92
    .line 93
    iget-object p5, p0, LfP0/a$b;->g:Ldagger/internal/h;

    .line 94
    .line 95
    iget-object p6, p0, LfP0/a$b;->l:Ldagger/internal/h;

    .line 96
    .line 97
    iget-object p7, p0, LfP0/a$b;->p:Ldagger/internal/h;

    .line 98
    .line 99
    iget-object p8, p0, LfP0/a$b;->I:Ldagger/internal/h;

    .line 100
    .line 101
    invoke-static/range {p2 .. p8}, Lorg/xbet/statistic/team/impl/team_statistic/presentation/viewmodels/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/team/impl/team_statistic/presentation/viewmodels/a;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    iput-object p1, p0, LfP0/a$b;->K:Ldagger/internal/h;

    .line 106
    .line 107
    return-void
.end method

.method public final f(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/OneTeamStatisticMenuFragment;)Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/OneTeamStatisticMenuFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LfP0/a$b;->j()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/f;->b(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/OneTeamStatisticMenuFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LfP0/a$b;->a:LSX0/a;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/f;->a(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/OneTeamStatisticMenuFragment;LSX0/a;)V

    .line 11
    .line 12
    .line 13
    return-object p1
.end method

.method public final g(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/TeamStatisticMenuFragment;)Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/TeamStatisticMenuFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LfP0/a$b;->j()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/j;->b(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/TeamStatisticMenuFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LfP0/a$b;->a:LSX0/a;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/j;->a(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/TeamStatisticMenuFragment;LSX0/a;)V

    .line 11
    .line 12
    .line 13
    return-object p1
.end method

.method public final h(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/TeamStatisticMenuItemFragment;)Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/TeamStatisticMenuItemFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LfP0/a$b;->j()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/n;->b(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/TeamStatisticMenuItemFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LfP0/a$b;->a:LSX0/a;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/n;->a(Lorg/xbet/statistic/team/impl/team_statistic/presentation/fragments/TeamStatisticMenuItemFragment;LSX0/a;)V

    .line 11
    .line 12
    .line 13
    return-object p1
.end method

.method public final i()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x2

    .line 2
    invoke-static {v0}, Ldagger/internal/f;->b(I)Ldagger/internal/f;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    const-class v1, Lorg/xbet/statistic/team/impl/team_statistic/presentation/viewmodels/f;

    .line 7
    .line 8
    iget-object v2, p0, LfP0/a$b;->J:Ldagger/internal/h;

    .line 9
    .line 10
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    const-class v1, Lorg/xbet/statistic/team/impl/team_statistic/presentation/viewmodels/OneTeamStatisticMenuViewModel;

    .line 15
    .line 16
    iget-object v2, p0, LfP0/a$b;->K:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {v0}, Ldagger/internal/f;->a()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    return-object v0
.end method

.method public final j()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LfP0/a$b;->i()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
