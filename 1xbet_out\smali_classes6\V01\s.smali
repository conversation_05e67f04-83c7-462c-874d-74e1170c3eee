.class public final LV01/s;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0004\"#\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u00008\u0006\u00a2\u0006\u0012\n\u0004\u0008\u0002\u0010\u0003\u0012\u0004\u0008\u0006\u0010\u0007\u001a\u0004\u0008\u0004\u0010\u0005\"\u001d\u0010\u000c\u001a\u0008\u0012\u0004\u0012\u00020\t0\u00008\u0006\u00a2\u0006\u000c\n\u0004\u0008\n\u0010\u0003\u001a\u0004\u0008\u000b\u0010\u0005\u00a8\u0006\r"
    }
    d2 = {
        "Landroidx/compose/runtime/x0;",
        "",
        "a",
        "Landroidx/compose/runtime/x0;",
        "e",
        "()Landroidx/compose/runtime/x0;",
        "getLocalSystemUiDarkMode$annotations",
        "()V",
        "LocalSystemUiDarkMode",
        "Lorg/xbet/uikit/compose/color/ThemeColors;",
        "b",
        "f",
        "LocalThemeColors",
        "uikit_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:Landroidx/compose/runtime/x0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/compose/runtime/x0<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final b:Landroidx/compose/runtime/x0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/compose/runtime/x0<",
            "Lorg/xbet/uikit/compose/color/ThemeColors;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LV01/q;

    .line 2
    .line 3
    invoke-direct {v0}, LV01/q;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0}, Landroidx/compose/runtime/CompositionLocalKt;->g(Lkotlin/jvm/functions/Function0;)Landroidx/compose/runtime/x0;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    sput-object v0, LV01/s;->a:Landroidx/compose/runtime/x0;

    .line 11
    .line 12
    new-instance v0, LV01/r;

    .line 13
    .line 14
    invoke-direct {v0}, LV01/r;-><init>()V

    .line 15
    .line 16
    .line 17
    invoke-static {v0}, Landroidx/compose/runtime/CompositionLocalKt;->g(Lkotlin/jvm/functions/Function0;)Landroidx/compose/runtime/x0;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    sput-object v0, LV01/s;->b:Landroidx/compose/runtime/x0;

    .line 22
    .line 23
    return-void
.end method

.method public static synthetic a()Z
    .locals 1

    .line 1
    invoke-static {}, LV01/s;->c()Z

    move-result v0

    return v0
.end method

.method public static synthetic b()Lorg/xbet/uikit/compose/color/ThemeColors;
    .locals 1

    .line 1
    invoke-static {}, LV01/s;->d()Lorg/xbet/uikit/compose/color/ThemeColors;

    move-result-object v0

    return-object v0
.end method

.method public static final c()Z
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    return v0
.end method

.method public static final d()Lorg/xbet/uikit/compose/color/ThemeColors;
    .locals 88

    .line 1
    new-instance v0, Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 2
    .line 3
    sget-object v1, Landroidx/compose/ui/graphics/v0;->b:Landroidx/compose/ui/graphics/v0$a;

    .line 4
    .line 5
    move-object v3, v1

    .line 6
    invoke-virtual {v3}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 7
    .line 8
    .line 9
    move-result-wide v1

    .line 10
    move-object v5, v3

    .line 11
    invoke-virtual {v5}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 12
    .line 13
    .line 14
    move-result-wide v3

    .line 15
    move-object v7, v5

    .line 16
    invoke-virtual {v7}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 17
    .line 18
    .line 19
    move-result-wide v5

    .line 20
    move-object v9, v7

    .line 21
    invoke-virtual {v9}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 22
    .line 23
    .line 24
    move-result-wide v7

    .line 25
    move-object v11, v9

    .line 26
    invoke-virtual {v11}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 27
    .line 28
    .line 29
    move-result-wide v9

    .line 30
    move-object v13, v11

    .line 31
    invoke-virtual {v13}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 32
    .line 33
    .line 34
    move-result-wide v11

    .line 35
    move-object v15, v13

    .line 36
    invoke-virtual {v15}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 37
    .line 38
    .line 39
    move-result-wide v13

    .line 40
    move-object/from16 v17, v15

    .line 41
    .line 42
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 43
    .line 44
    .line 45
    move-result-wide v15

    .line 46
    move-object/from16 v19, v17

    .line 47
    .line 48
    invoke-virtual/range {v19 .. v19}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 49
    .line 50
    .line 51
    move-result-wide v17

    .line 52
    move-object/from16 v21, v19

    .line 53
    .line 54
    invoke-virtual/range {v21 .. v21}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 55
    .line 56
    .line 57
    move-result-wide v19

    .line 58
    move-object/from16 v23, v21

    .line 59
    .line 60
    invoke-virtual/range {v23 .. v23}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 61
    .line 62
    .line 63
    move-result-wide v21

    .line 64
    move-object/from16 v25, v23

    .line 65
    .line 66
    invoke-virtual/range {v25 .. v25}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 67
    .line 68
    .line 69
    move-result-wide v23

    .line 70
    move-object/from16 v27, v25

    .line 71
    .line 72
    invoke-virtual/range {v27 .. v27}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 73
    .line 74
    .line 75
    move-result-wide v25

    .line 76
    move-object/from16 v29, v27

    .line 77
    .line 78
    invoke-virtual/range {v29 .. v29}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 79
    .line 80
    .line 81
    move-result-wide v27

    .line 82
    move-object/from16 v31, v29

    .line 83
    .line 84
    invoke-virtual/range {v31 .. v31}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 85
    .line 86
    .line 87
    move-result-wide v29

    .line 88
    move-object/from16 v33, v31

    .line 89
    .line 90
    invoke-virtual/range {v33 .. v33}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 91
    .line 92
    .line 93
    move-result-wide v31

    .line 94
    move-object/from16 v35, v33

    .line 95
    .line 96
    invoke-virtual/range {v35 .. v35}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 97
    .line 98
    .line 99
    move-result-wide v33

    .line 100
    move-object/from16 v37, v35

    .line 101
    .line 102
    invoke-virtual/range {v37 .. v37}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 103
    .line 104
    .line 105
    move-result-wide v35

    .line 106
    move-object/from16 v39, v37

    .line 107
    .line 108
    invoke-virtual/range {v39 .. v39}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 109
    .line 110
    .line 111
    move-result-wide v37

    .line 112
    move-object/from16 v41, v39

    .line 113
    .line 114
    invoke-virtual/range {v41 .. v41}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 115
    .line 116
    .line 117
    move-result-wide v39

    .line 118
    move-object/from16 v43, v41

    .line 119
    .line 120
    invoke-virtual/range {v43 .. v43}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 121
    .line 122
    .line 123
    move-result-wide v41

    .line 124
    move-object/from16 v45, v43

    .line 125
    .line 126
    invoke-virtual/range {v45 .. v45}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 127
    .line 128
    .line 129
    move-result-wide v43

    .line 130
    move-object/from16 v47, v45

    .line 131
    .line 132
    invoke-virtual/range {v47 .. v47}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 133
    .line 134
    .line 135
    move-result-wide v45

    .line 136
    move-object/from16 v49, v47

    .line 137
    .line 138
    invoke-virtual/range {v49 .. v49}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 139
    .line 140
    .line 141
    move-result-wide v47

    .line 142
    move-object/from16 v51, v49

    .line 143
    .line 144
    invoke-virtual/range {v51 .. v51}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 145
    .line 146
    .line 147
    move-result-wide v49

    .line 148
    move-object/from16 v53, v51

    .line 149
    .line 150
    invoke-virtual/range {v53 .. v53}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 151
    .line 152
    .line 153
    move-result-wide v51

    .line 154
    move-object/from16 v55, v53

    .line 155
    .line 156
    invoke-virtual/range {v55 .. v55}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 157
    .line 158
    .line 159
    move-result-wide v53

    .line 160
    move-object/from16 v57, v55

    .line 161
    .line 162
    invoke-virtual/range {v57 .. v57}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 163
    .line 164
    .line 165
    move-result-wide v55

    .line 166
    move-object/from16 v59, v57

    .line 167
    .line 168
    invoke-virtual/range {v59 .. v59}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 169
    .line 170
    .line 171
    move-result-wide v57

    .line 172
    move-object/from16 v61, v59

    .line 173
    .line 174
    invoke-virtual/range {v61 .. v61}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 175
    .line 176
    .line 177
    move-result-wide v59

    .line 178
    move-object/from16 v63, v61

    .line 179
    .line 180
    invoke-virtual/range {v63 .. v63}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 181
    .line 182
    .line 183
    move-result-wide v61

    .line 184
    move-object/from16 v65, v63

    .line 185
    .line 186
    invoke-virtual/range {v65 .. v65}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 187
    .line 188
    .line 189
    move-result-wide v63

    .line 190
    move-object/from16 v67, v65

    .line 191
    .line 192
    invoke-virtual/range {v67 .. v67}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 193
    .line 194
    .line 195
    move-result-wide v65

    .line 196
    move-object/from16 v69, v67

    .line 197
    .line 198
    invoke-virtual/range {v69 .. v69}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 199
    .line 200
    .line 201
    move-result-wide v67

    .line 202
    move-object/from16 v71, v69

    .line 203
    .line 204
    invoke-virtual/range {v71 .. v71}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 205
    .line 206
    .line 207
    move-result-wide v69

    .line 208
    move-object/from16 v73, v71

    .line 209
    .line 210
    invoke-virtual/range {v73 .. v73}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 211
    .line 212
    .line 213
    move-result-wide v71

    .line 214
    move-object/from16 v75, v73

    .line 215
    .line 216
    invoke-virtual/range {v75 .. v75}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 217
    .line 218
    .line 219
    move-result-wide v73

    .line 220
    move-object/from16 v77, v75

    .line 221
    .line 222
    invoke-virtual/range {v77 .. v77}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 223
    .line 224
    .line 225
    move-result-wide v75

    .line 226
    move-object/from16 v79, v77

    .line 227
    .line 228
    invoke-virtual/range {v79 .. v79}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 229
    .line 230
    .line 231
    move-result-wide v77

    .line 232
    move-object/from16 v81, v79

    .line 233
    .line 234
    invoke-virtual/range {v81 .. v81}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 235
    .line 236
    .line 237
    move-result-wide v79

    .line 238
    move-object/from16 v83, v81

    .line 239
    .line 240
    invoke-virtual/range {v83 .. v83}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 241
    .line 242
    .line 243
    move-result-wide v81

    .line 244
    move-object/from16 v85, v83

    .line 245
    .line 246
    invoke-virtual/range {v85 .. v85}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 247
    .line 248
    .line 249
    move-result-wide v83

    .line 250
    invoke-virtual/range {v85 .. v85}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 251
    .line 252
    .line 253
    move-result-wide v85

    .line 254
    const/16 v87, 0x0

    .line 255
    .line 256
    invoke-direct/range {v0 .. v87}, Lorg/xbet/uikit/compose/color/ThemeColors;-><init>(JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJLkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 257
    .line 258
    .line 259
    return-object v0
.end method

.method public static final e()Landroidx/compose/runtime/x0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Landroidx/compose/runtime/x0<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, LV01/s;->a:Landroidx/compose/runtime/x0;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final f()Landroidx/compose/runtime/x0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Landroidx/compose/runtime/x0<",
            "Lorg/xbet/uikit/compose/color/ThemeColors;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, LV01/s;->b:Landroidx/compose/runtime/x0;

    .line 2
    .line 3
    return-object v0
.end method
