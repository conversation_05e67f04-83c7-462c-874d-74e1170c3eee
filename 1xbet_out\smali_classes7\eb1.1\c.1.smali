.class public final synthetic Leb1/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:Z


# direct methods
.method public synthetic constructor <init>(LB4/a;Z)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Leb1/c;->a:LB4/a;

    iput-boolean p2, p0, Leb1/c;->b:Z

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Leb1/c;->a:LB4/a;

    iget-boolean v1, p0, Leb1/c;->b:Z

    check-cast p1, Ljava/util/List;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentBannerDelegateKt;->b(LB4/a;ZLjava/util/List;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
