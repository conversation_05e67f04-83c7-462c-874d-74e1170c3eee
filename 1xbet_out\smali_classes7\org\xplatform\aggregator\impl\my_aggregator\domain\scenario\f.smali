.class public final Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/e;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Li8/j;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "Li8/j;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/f;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/f;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/f;->c:LBc/a;

    .line 9
    .line 10
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/f;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "Li8/j;",
            ">;)",
            "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/f;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/f;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/f;-><init>(LBc/a;LBc/a;LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/e;Lorg/xbet/remoteconfig/domain/usecases/i;Li8/j;)Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/e;Lorg/xbet/remoteconfig/domain/usecases/i;Li8/j;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/f;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/e;

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/f;->b:LBc/a;

    .line 10
    .line 11
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    check-cast v1, Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 16
    .line 17
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/f;->c:LBc/a;

    .line 18
    .line 19
    invoke-interface {v2}, LBc/a;->get()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    check-cast v2, Li8/j;

    .line 24
    .line 25
    invoke-static {v0, v1, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/f;->c(Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/e;Lorg/xbet/remoteconfig/domain/usecases/i;Li8/j;)Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/f;->b()Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
