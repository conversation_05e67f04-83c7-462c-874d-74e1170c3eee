.class public abstract Lorg/xbet/uikit_sport/sport_collection/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_collection/b$a;,
        Lorg/xbet/uikit_sport/sport_collection/b$b;,
        Lorg/xbet/uikit_sport/sport_collection/b$c;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u00087\u0018\u00002\u00020\u0001:\u0003\t\u0006\nB\u0011\u0008\u0004\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0006\u0010\u0007\u001a\u0004\u0008\u0006\u0010\u0008\u0082\u0001\u0003\u000b\u000c\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_collection/b;",
        "",
        "Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;",
        "type",
        "<init>",
        "(Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;)V",
        "a",
        "Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;",
        "()Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;",
        "c",
        "b",
        "Lorg/xbet/uikit_sport/sport_collection/b$a;",
        "Lorg/xbet/uikit_sport/sport_collection/b$b;",
        "Lorg/xbet/uikit_sport/sport_collection/b$c;",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/b;->a:Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    return-void
.end method

.method public synthetic constructor <init>(Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/b;-><init>(Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;)V

    return-void
.end method


# virtual methods
.method public final a()Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/b;->a:Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    .line 2
    .line 3
    return-object v0
.end method
