.class public final synthetic LkY0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, <PERSON><PERSON><PERSON>/lang/Integer;

    invoke-virtual {p1}, <PERSON><PERSON>va/lang/Integer;->intValue()I

    move-result p1

    invoke-static {p1}, LkY0/g;->a(I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
