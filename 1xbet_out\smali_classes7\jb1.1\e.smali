.class public final Ljb1/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0004\u001a\u0004\u0018\u00010\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;",
        "LHX0/e;",
        "resourceManager",
        "Lv21/t;",
        "a",
        "(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;LHX0/e;)Lv21/t;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;LHX0/e;)Lv21/t;
    .locals 8
    .param p0    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;->Finished:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-eq v0, v1, :cond_1

    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->a()Ljava/util/Date;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    invoke-virtual {v0}, Ljava/util/Date;->getTime()J

    .line 17
    .line 18
    .line 19
    move-result-wide v0

    .line 20
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 21
    .line 22
    .line 23
    move-result-wide v2

    .line 24
    add-long/2addr v0, v2

    .line 25
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    :cond_0
    new-instance v0, Lv21/t;

    .line 30
    .line 31
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->b()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object p0

    .line 35
    new-instance v1, Lv21/u;

    .line 36
    .line 37
    sget v3, Lpb/k;->day_short:I

    .line 38
    .line 39
    const/4 v4, 0x0

    .line 40
    new-array v5, v4, [Ljava/lang/Object;

    .line 41
    .line 42
    invoke-interface {p1, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v3

    .line 46
    sget v5, Lpb/k;->hour_short:I

    .line 47
    .line 48
    new-array v6, v4, [Ljava/lang/Object;

    .line 49
    .line 50
    invoke-interface {p1, v5, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v5

    .line 54
    sget v6, Lpb/k;->minute_short:I

    .line 55
    .line 56
    new-array v7, v4, [Ljava/lang/Object;

    .line 57
    .line 58
    invoke-interface {p1, v6, v7}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v6

    .line 62
    sget v7, Lpb/k;->second_short:I

    .line 63
    .line 64
    new-array v4, v4, [Ljava/lang/Object;

    .line 65
    .line 66
    invoke-interface {p1, v7, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    invoke-direct {v1, v3, v5, v6, p1}, Lv21/u;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 71
    .line 72
    .line 73
    invoke-direct {v0, p0, v2, v1}, Lv21/t;-><init>(Ljava/lang/String;Ljava/lang/Long;Lv21/u;)V

    .line 74
    .line 75
    .line 76
    return-object v0

    .line 77
    :cond_1
    return-object v2
.end method
