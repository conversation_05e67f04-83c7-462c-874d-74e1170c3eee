.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/os/Parcelable$Creator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroid/os/Parcelable$Creator<",
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroid/os/Parcel;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;

    invoke-virtual {p1}, Landroid/os/Parcel;->readInt()I

    move-result v1

    invoke-virtual {p1}, Landroid/os/Parcel;->readLong()J

    move-result-wide v2

    invoke-virtual {p1}, Landroid/os/Parcel;->readInt()I

    move-result p1

    invoke-direct {v0, v1, v2, v3, p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;-><init>(IJI)V

    return-object v0
.end method

.method public final b(I)[Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;
    .locals 0

    .line 1
    new-array p1, p1, [Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;

    return-object p1
.end method

.method public bridge synthetic createFromParcel(Landroid/os/Parcel;)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams$a;->a(Landroid/os/Parcel;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic newArray(I)[Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams$a;->b(I)[Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;

    move-result-object p1

    return-object p1
.end method
