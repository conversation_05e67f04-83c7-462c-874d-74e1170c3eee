.class public final Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/e;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "LSX0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/e;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/e;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/e;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/e;->d:LBc/a;

    .line 11
    .line 12
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "LSX0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;)",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/e;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/e;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1, p2, p3}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/e;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;LwX0/c;Lm8/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;)Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    invoke-direct/range {v0 .. v5}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;-><init>(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;LwX0/c;Lm8/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method


# virtual methods
.method public b(LwX0/c;)Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/e;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/e;->b:LBc/a;

    .line 10
    .line 11
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    check-cast v1, Lm8/a;

    .line 16
    .line 17
    iget-object v2, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/e;->c:LBc/a;

    .line 18
    .line 19
    invoke-interface {v2}, LBc/a;->get()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    check-cast v2, LSX0/a;

    .line 24
    .line 25
    iget-object v3, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/e;->d:LBc/a;

    .line 26
    .line 27
    invoke-interface {v3}, LBc/a;->get()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v3

    .line 31
    check-cast v3, Lorg/xbet/ui_common/utils/internet/a;

    .line 32
    .line 33
    invoke-static {v0, p1, v1, v2, v3}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/e;->c(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;LwX0/c;Lm8/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;)Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    return-object p1
.end method
