.class final Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.main_menu.impl.presentation.container.MainMenuContainerViewModel$loadBalancesWithUserData$2"
    f = "MainMenuContainerViewModel.kt"
    l = {
        0x195,
        0x1a2
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->t4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;

    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 22

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x2

    .line 10
    const/4 v4, 0x0

    .line 11
    const/4 v5, 0x1

    .line 12
    if-eqz v2, :cond_2

    .line 13
    .line 14
    if-eq v2, v5, :cond_1

    .line 15
    .line 16
    if-ne v2, v3, :cond_0

    .line 17
    .line 18
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->L$1:Ljava/lang/Object;

    .line 19
    .line 20
    check-cast v1, Ljava/lang/String;

    .line 21
    .line 22
    iget-object v2, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->L$0:Ljava/lang/Object;

    .line 23
    .line 24
    check-cast v2, Lorg/xbet/balance/model/BalanceModel;

    .line 25
    .line 26
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    move-object/from16 v3, p1

    .line 30
    .line 31
    move-object/from16 v17, v1

    .line 32
    .line 33
    goto :goto_3

    .line 34
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 35
    .line 36
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 37
    .line 38
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 39
    .line 40
    .line 41
    throw v1

    .line 42
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    move-object/from16 v2, p1

    .line 46
    .line 47
    goto :goto_0

    .line 48
    :cond_2
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 49
    .line 50
    .line 51
    iget-object v2, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 52
    .line 53
    invoke-static {v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->A3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lfk/l;

    .line 54
    .line 55
    .line 56
    move-result-object v2

    .line 57
    iput v5, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->label:I

    .line 58
    .line 59
    const/4 v6, 0x0

    .line 60
    invoke-static {v2, v6, v0, v5, v6}, Lfk/l$a;->a(Lfk/l;Lorg/xbet/balance/model/BalanceScreenType;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object v2

    .line 64
    if-ne v2, v1, :cond_3

    .line 65
    .line 66
    goto :goto_2

    .line 67
    :cond_3
    :goto_0
    check-cast v2, Lorg/xbet/balance/model/BalanceModel;

    .line 68
    .line 69
    iget-object v6, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 70
    .line 71
    invoke-static {v6}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->z3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lgk0/a;

    .line 72
    .line 73
    .line 74
    move-result-object v6

    .line 75
    invoke-interface {v6}, Lgk0/a;->invoke()Lorg/xbet/remoteconfig/domain/models/AccountSelectionStyleConfigType;

    .line 76
    .line 77
    .line 78
    move-result-object v6

    .line 79
    sget-object v7, Lorg/xbet/remoteconfig/domain/models/AccountSelectionStyleConfigType;->SECONDARY:Lorg/xbet/remoteconfig/domain/models/AccountSelectionStyleConfigType;

    .line 80
    .line 81
    if-ne v6, v7, :cond_4

    .line 82
    .line 83
    iget-object v6, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 84
    .line 85
    invoke-static {v6}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->J3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)LHX0/e;

    .line 86
    .line 87
    .line 88
    move-result-object v6

    .line 89
    sget v7, Lpb/k;->all_balances:I

    .line 90
    .line 91
    new-array v8, v4, [Ljava/lang/Object;

    .line 92
    .line 93
    invoke-interface {v6, v7, v8}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object v6

    .line 97
    goto :goto_1

    .line 98
    :cond_4
    invoke-virtual {v2}, Lorg/xbet/balance/model/BalanceModel;->getAlias()Ljava/lang/String;

    .line 99
    .line 100
    .line 101
    move-result-object v6

    .line 102
    invoke-interface {v6}, Ljava/lang/CharSequence;->length()I

    .line 103
    .line 104
    .line 105
    move-result v6

    .line 106
    if-lez v6, :cond_5

    .line 107
    .line 108
    invoke-virtual {v2}, Lorg/xbet/balance/model/BalanceModel;->getAlias()Ljava/lang/String;

    .line 109
    .line 110
    .line 111
    move-result-object v6

    .line 112
    goto :goto_1

    .line 113
    :cond_5
    invoke-virtual {v2}, Lorg/xbet/balance/model/BalanceModel;->getAccountName()Ljava/lang/String;

    .line 114
    .line 115
    .line 116
    move-result-object v6

    .line 117
    :goto_1
    iget-object v7, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 118
    .line 119
    iput-object v2, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->L$0:Ljava/lang/Object;

    .line 120
    .line 121
    iput-object v6, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->L$1:Ljava/lang/Object;

    .line 122
    .line 123
    iput v3, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->label:I

    .line 124
    .line 125
    invoke-static {v7, v7, v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->T3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 126
    .line 127
    .line 128
    move-result-object v3

    .line 129
    if-ne v3, v1, :cond_6

    .line 130
    .line 131
    :goto_2
    return-object v1

    .line 132
    :cond_6
    move-object/from16 v17, v6

    .line 133
    .line 134
    :goto_3
    move-object v7, v3

    .line 135
    check-cast v7, Ljava/lang/String;

    .line 136
    .line 137
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 138
    .line 139
    invoke-static {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->J3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)LHX0/e;

    .line 140
    .line 141
    .line 142
    move-result-object v1

    .line 143
    sget v3, Lpb/k;->personal_data:I

    .line 144
    .line 145
    new-array v6, v4, [Ljava/lang/Object;

    .line 146
    .line 147
    invoke-interface {v1, v3, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 148
    .line 149
    .line 150
    move-result-object v8

    .line 151
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 152
    .line 153
    invoke-static {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->w3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)LYU/a;

    .line 154
    .line 155
    .line 156
    move-result-object v1

    .line 157
    invoke-interface {v1}, LYU/a;->a()LZU/a;

    .line 158
    .line 159
    .line 160
    move-result-object v1

    .line 161
    invoke-interface {v1}, LZU/a;->invoke()Lorg/xbet/feature/calendar_event/api/domain/models/CalendarEventType;

    .line 162
    .line 163
    .line 164
    move-result-object v1

    .line 165
    sget-object v3, Lorg/xbet/feature/calendar_event/api/domain/models/CalendarEventType;->NEW_YEAR:Lorg/xbet/feature/calendar_event/api/domain/models/CalendarEventType;

    .line 166
    .line 167
    if-ne v1, v3, :cond_7

    .line 168
    .line 169
    const/16 v16, 0x1

    .line 170
    .line 171
    goto :goto_4

    .line 172
    :cond_7
    const/16 v16, 0x0

    .line 173
    .line 174
    :goto_4
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 175
    .line 176
    invoke-static {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->Q3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/flow/V;

    .line 177
    .line 178
    .line 179
    move-result-object v1

    .line 180
    :cond_8
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 181
    .line 182
    .line 183
    move-result-object v3

    .line 184
    move-object v6, v3

    .line 185
    check-cast v6, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;

    .line 186
    .line 187
    sget-object v9, Ll8/j;->a:Ll8/j;

    .line 188
    .line 189
    invoke-virtual {v2}, Lorg/xbet/balance/model/BalanceModel;->getMoney()D

    .line 190
    .line 191
    .line 192
    move-result-wide v10

    .line 193
    const/4 v13, 0x2

    .line 194
    const/4 v14, 0x0

    .line 195
    const/4 v12, 0x0

    .line 196
    invoke-static/range {v9 .. v14}, Ll8/j;->g(Ll8/j;DLcom/xbet/onexcore/utils/ValueType;ILjava/lang/Object;)Ljava/lang/String;

    .line 197
    .line 198
    .line 199
    move-result-object v18

    .line 200
    invoke-virtual {v2}, Lorg/xbet/balance/model/BalanceModel;->getCurrencySymbol()Ljava/lang/String;

    .line 201
    .line 202
    .line 203
    move-result-object v19

    .line 204
    const/16 v20, 0x1fc

    .line 205
    .line 206
    const/16 v21, 0x0

    .line 207
    .line 208
    const/4 v9, 0x0

    .line 209
    const/4 v10, 0x0

    .line 210
    const/4 v11, 0x0

    .line 211
    const/4 v12, 0x0

    .line 212
    const/4 v13, 0x0

    .line 213
    const/4 v14, 0x0

    .line 214
    const/4 v15, 0x0

    .line 215
    invoke-static/range {v6 .. v21}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->b(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;Ljava/lang/String;Ljava/lang/String;ZZZZZLjava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;

    .line 216
    .line 217
    .line 218
    move-result-object v4

    .line 219
    invoke-interface {v1, v3, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 220
    .line 221
    .line 222
    move-result v3

    .line 223
    if-eqz v3, :cond_8

    .line 224
    .line 225
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 226
    .line 227
    return-object v1
.end method
