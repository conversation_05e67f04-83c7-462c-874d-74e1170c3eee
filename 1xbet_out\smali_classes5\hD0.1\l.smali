.class public final synthetic LhD0/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Landroidx/compose/runtime/r1;


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/runtime/r1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LhD0/l;->a:Landroidx/compose/runtime/r1;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LhD0/l;->a:Landroidx/compose/runtime/r1;

    check-cast p1, Landroid/content/Context;

    invoke-static {v0, p1}, LhD0/h$b;->b(Landroidx/compose/runtime/r1;Landroid/content/Context;)Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    move-result-object p1

    return-object p1
.end method
