.class public final Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/data/datasources/a;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LT91/a;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LS8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/data/datasources/a;",
            ">;",
            "LBc/a<",
            "LT91/a;",
            ">;",
            "LBc/a<",
            "Lc8/h;",
            ">;",
            "LBc/a<",
            "LS8/a;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;->f:LBc/a;

    .line 15
    .line 16
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/data/datasources/a;",
            ">;",
            "LBc/a<",
            "LT91/a;",
            ">;",
            "LBc/a<",
            "Lc8/h;",
            ">;",
            "LBc/a<",
            "LS8/a;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;)",
            "Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    move-object v6, p5

    .line 9
    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 10
    .line 11
    .line 12
    return-object v0
.end method

.method public static c(Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;Lorg/xplatform/aggregator/impl/core/data/datasources/a;LT91/a;Lc8/h;LS8/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;
    .locals 7

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    move-object v6, p5

    .line 9
    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;-><init>(Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;Lorg/xplatform/aggregator/impl/core/data/datasources/a;LT91/a;Lc8/h;LS8/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)V

    .line 10
    .line 11
    .line 12
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;->b:LBc/a;

    .line 11
    .line 12
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    move-object v2, v0

    .line 17
    check-cast v2, Lorg/xplatform/aggregator/impl/core/data/datasources/a;

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;->c:LBc/a;

    .line 20
    .line 21
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    move-object v3, v0

    .line 26
    check-cast v3, LT91/a;

    .line 27
    .line 28
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;->d:LBc/a;

    .line 29
    .line 30
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    move-object v4, v0

    .line 35
    check-cast v4, Lc8/h;

    .line 36
    .line 37
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;->e:LBc/a;

    .line 38
    .line 39
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    move-object v5, v0

    .line 44
    check-cast v5, LS8/a;

    .line 45
    .line 46
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;->f:LBc/a;

    .line 47
    .line 48
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    move-object v6, v0

    .line 53
    check-cast v6, Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 54
    .line 55
    invoke-static/range {v1 .. v6}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;->c(Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;Lorg/xplatform/aggregator/impl/core/data/datasources/a;LT91/a;Lc8/h;LS8/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/c;->b()Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
