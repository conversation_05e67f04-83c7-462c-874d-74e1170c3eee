.class public final Lcom/sumsub/sns/R$color;
.super Ljava/lang/Object;


# static fields
.field public static abc_background_cache_hint_selector_material_dark:I = 0x7f060000

.field public static abc_background_cache_hint_selector_material_light:I = 0x7f060001

.field public static abc_btn_colored_borderless_text_material:I = 0x7f060002

.field public static abc_btn_colored_text_material:I = 0x7f060003

.field public static abc_color_highlight_material:I = 0x7f060004

.field public static abc_decor_view_status_guard:I = 0x7f060005

.field public static abc_decor_view_status_guard_light:I = 0x7f060006

.field public static abc_hint_foreground_material_dark:I = 0x7f060007

.field public static abc_hint_foreground_material_light:I = 0x7f060008

.field public static abc_primary_text_disable_only_material_dark:I = 0x7f060009

.field public static abc_primary_text_disable_only_material_light:I = 0x7f06000a

.field public static abc_primary_text_material_dark:I = 0x7f06000b

.field public static abc_primary_text_material_light:I = 0x7f06000c

.field public static abc_search_url_text:I = 0x7f06000d

.field public static abc_search_url_text_normal:I = 0x7f06000e

.field public static abc_search_url_text_pressed:I = 0x7f06000f

.field public static abc_search_url_text_selected:I = 0x7f060010

.field public static abc_secondary_text_material_dark:I = 0x7f060011

.field public static abc_secondary_text_material_light:I = 0x7f060012

.field public static abc_tint_btn_checkable:I = 0x7f060013

.field public static abc_tint_default:I = 0x7f060014

.field public static abc_tint_edittext:I = 0x7f060015

.field public static abc_tint_seek_thumb:I = 0x7f060016

.field public static abc_tint_spinner:I = 0x7f060017

.field public static abc_tint_switch_track:I = 0x7f060018

.field public static accent_material_dark:I = 0x7f060019

.field public static accent_material_light:I = 0x7f06001a

.field public static androidx_core_ripple_material_light:I = 0x7f060022

.field public static androidx_core_secondary_text_default_material_light:I = 0x7f060023

.field public static background_floating_material_dark:I = 0x7f06025b

.field public static background_floating_material_light:I = 0x7f06025c

.field public static background_material_dark:I = 0x7f06040e

.field public static background_material_light:I = 0x7f06040f

.field public static bright_foreground_disabled_material_dark:I = 0x7f06046f

.field public static bright_foreground_disabled_material_light:I = 0x7f060470

.field public static bright_foreground_inverse_material_dark:I = 0x7f060471

.field public static bright_foreground_inverse_material_light:I = 0x7f060472

.field public static bright_foreground_material_dark:I = 0x7f060473

.field public static bright_foreground_material_light:I = 0x7f060474

.field public static button_material_dark:I = 0x7f06047b

.field public static button_material_light:I = 0x7f06047c

.field public static call_notification_answer_color:I = 0x7f060485

.field public static call_notification_decline_color:I = 0x7f060486

.field public static cardview_dark_background:I = 0x7f0604a0

.field public static cardview_light_background:I = 0x7f0604a1

.field public static cardview_shadow_end_color:I = 0x7f0604a2

.field public static cardview_shadow_start_color:I = 0x7f0604a3

.field public static common_google_signin_btn_text_dark:I = 0x7f0607f2

.field public static common_google_signin_btn_text_dark_default:I = 0x7f0607f3

.field public static common_google_signin_btn_text_dark_disabled:I = 0x7f0607f4

.field public static common_google_signin_btn_text_dark_focused:I = 0x7f0607f5

.field public static common_google_signin_btn_text_dark_pressed:I = 0x7f0607f6

.field public static common_google_signin_btn_text_light:I = 0x7f0607f7

.field public static common_google_signin_btn_text_light_default:I = 0x7f0607f8

.field public static common_google_signin_btn_text_light_disabled:I = 0x7f0607f9

.field public static common_google_signin_btn_text_light_focused:I = 0x7f0607fa

.field public static common_google_signin_btn_text_light_pressed:I = 0x7f0607fb

.field public static common_google_signin_btn_tint:I = 0x7f0607fc

.field public static design_bottom_navigation_shadow_color:I = 0x7f060912

.field public static design_box_stroke_color:I = 0x7f060913

.field public static design_dark_default_color_background:I = 0x7f060914

.field public static design_dark_default_color_error:I = 0x7f060915

.field public static design_dark_default_color_on_background:I = 0x7f060916

.field public static design_dark_default_color_on_error:I = 0x7f060917

.field public static design_dark_default_color_on_primary:I = 0x7f060918

.field public static design_dark_default_color_on_secondary:I = 0x7f060919

.field public static design_dark_default_color_on_surface:I = 0x7f06091a

.field public static design_dark_default_color_primary:I = 0x7f06091b

.field public static design_dark_default_color_primary_dark:I = 0x7f06091c

.field public static design_dark_default_color_primary_variant:I = 0x7f06091d

.field public static design_dark_default_color_secondary:I = 0x7f06091e

.field public static design_dark_default_color_secondary_variant:I = 0x7f06091f

.field public static design_dark_default_color_surface:I = 0x7f060920

.field public static design_default_color_background:I = 0x7f060921

.field public static design_default_color_error:I = 0x7f060922

.field public static design_default_color_on_background:I = 0x7f060923

.field public static design_default_color_on_error:I = 0x7f060924

.field public static design_default_color_on_primary:I = 0x7f060925

.field public static design_default_color_on_secondary:I = 0x7f060926

.field public static design_default_color_on_surface:I = 0x7f060927

.field public static design_default_color_primary:I = 0x7f060928

.field public static design_default_color_primary_dark:I = 0x7f060929

.field public static design_default_color_primary_variant:I = 0x7f06092a

.field public static design_default_color_secondary:I = 0x7f06092b

.field public static design_default_color_secondary_variant:I = 0x7f06092c

.field public static design_default_color_surface:I = 0x7f06092d

.field public static design_error:I = 0x7f06092e

.field public static design_fab_shadow_end_color:I = 0x7f06092f

.field public static design_fab_shadow_mid_color:I = 0x7f060930

.field public static design_fab_shadow_start_color:I = 0x7f060931

.field public static design_fab_stroke_end_inner_color:I = 0x7f060932

.field public static design_fab_stroke_end_outer_color:I = 0x7f060933

.field public static design_fab_stroke_top_inner_color:I = 0x7f060934

.field public static design_fab_stroke_top_outer_color:I = 0x7f060935

.field public static design_icon_tint:I = 0x7f060936

.field public static design_snackbar_background_color:I = 0x7f060937

.field public static dim_foreground_disabled_material_dark:I = 0x7f060939

.field public static dim_foreground_disabled_material_light:I = 0x7f06093a

.field public static dim_foreground_material_dark:I = 0x7f06093b

.field public static dim_foreground_material_light:I = 0x7f06093c

.field public static error_color_material_dark:I = 0x7f060951

.field public static error_color_material_light:I = 0x7f060952

.field public static foreground_material_dark:I = 0x7f06095e

.field public static foreground_material_light:I = 0x7f06095f

.field public static highlighted_text_material_dark:I = 0x7f060997

.field public static highlighted_text_material_light:I = 0x7f060998

.field public static m3_appbar_overlay_color:I = 0x7f060a44

.field public static m3_assist_chip_icon_tint_color:I = 0x7f060a45

.field public static m3_assist_chip_stroke_color:I = 0x7f060a46

.field public static m3_bottom_sheet_drag_handle_color:I = 0x7f060a47

.field public static m3_button_background_color_selector:I = 0x7f060a48

.field public static m3_button_foreground_color_selector:I = 0x7f060a49

.field public static m3_button_outline_color_selector:I = 0x7f060a4a

.field public static m3_button_ripple_color:I = 0x7f060a4b

.field public static m3_button_ripple_color_selector:I = 0x7f060a4c

.field public static m3_calendar_item_disabled_text:I = 0x7f060a4d

.field public static m3_calendar_item_stroke_color:I = 0x7f060a4e

.field public static m3_card_foreground_color:I = 0x7f060a4f

.field public static m3_card_ripple_color:I = 0x7f060a50

.field public static m3_card_stroke_color:I = 0x7f060a51

.field public static m3_checkbox_button_icon_tint:I = 0x7f060a52

.field public static m3_checkbox_button_tint:I = 0x7f060a53

.field public static m3_chip_assist_text_color:I = 0x7f060a54

.field public static m3_chip_background_color:I = 0x7f060a55

.field public static m3_chip_ripple_color:I = 0x7f060a56

.field public static m3_chip_stroke_color:I = 0x7f060a57

.field public static m3_chip_text_color:I = 0x7f060a58

.field public static m3_dark_default_color_primary_text:I = 0x7f060a59

.field public static m3_dark_default_color_secondary_text:I = 0x7f060a5a

.field public static m3_dark_highlighted_text:I = 0x7f060a5b

.field public static m3_dark_hint_foreground:I = 0x7f060a5c

.field public static m3_dark_primary_text_disable_only:I = 0x7f060a5d

.field public static m3_default_color_primary_text:I = 0x7f060a5e

.field public static m3_default_color_secondary_text:I = 0x7f060a5f

.field public static m3_dynamic_dark_default_color_primary_text:I = 0x7f060a60

.field public static m3_dynamic_dark_default_color_secondary_text:I = 0x7f060a61

.field public static m3_dynamic_dark_highlighted_text:I = 0x7f060a62

.field public static m3_dynamic_dark_hint_foreground:I = 0x7f060a63

.field public static m3_dynamic_dark_primary_text_disable_only:I = 0x7f060a64

.field public static m3_dynamic_default_color_primary_text:I = 0x7f060a65

.field public static m3_dynamic_default_color_secondary_text:I = 0x7f060a66

.field public static m3_dynamic_highlighted_text:I = 0x7f060a67

.field public static m3_dynamic_hint_foreground:I = 0x7f060a68

.field public static m3_dynamic_primary_text_disable_only:I = 0x7f060a69

.field public static m3_efab_ripple_color_selector:I = 0x7f060a6a

.field public static m3_elevated_chip_background_color:I = 0x7f060a6b

.field public static m3_fab_efab_background_color_selector:I = 0x7f060a6c

.field public static m3_fab_efab_foreground_color_selector:I = 0x7f060a6d

.field public static m3_fab_ripple_color_selector:I = 0x7f060a6e

.field public static m3_filled_icon_button_container_color_selector:I = 0x7f060a6f

.field public static m3_highlighted_text:I = 0x7f060a70

.field public static m3_hint_foreground:I = 0x7f060a71

.field public static m3_icon_button_icon_color_selector:I = 0x7f060a72

.field public static m3_navigation_bar_item_with_indicator_icon_tint:I = 0x7f060a73

.field public static m3_navigation_bar_item_with_indicator_label_tint:I = 0x7f060a74

.field public static m3_navigation_bar_ripple_color_selector:I = 0x7f060a75

.field public static m3_navigation_item_background_color:I = 0x7f060a76

.field public static m3_navigation_item_icon_tint:I = 0x7f060a77

.field public static m3_navigation_item_ripple_color:I = 0x7f060a78

.field public static m3_navigation_item_text_color:I = 0x7f060a79

.field public static m3_navigation_rail_item_with_indicator_icon_tint:I = 0x7f060a7a

.field public static m3_navigation_rail_item_with_indicator_label_tint:I = 0x7f060a7b

.field public static m3_navigation_rail_ripple_color_selector:I = 0x7f060a7c

.field public static m3_popupmenu_overlay_color:I = 0x7f060a7d

.field public static m3_primary_text_disable_only:I = 0x7f060a7e

.field public static m3_radiobutton_button_tint:I = 0x7f060a7f

.field public static m3_radiobutton_ripple_tint:I = 0x7f060a80

.field public static m3_ref_palette_black:I = 0x7f060a81

.field public static m3_ref_palette_dynamic_neutral0:I = 0x7f060a82

.field public static m3_ref_palette_dynamic_neutral10:I = 0x7f060a83

.field public static m3_ref_palette_dynamic_neutral100:I = 0x7f060a84

.field public static m3_ref_palette_dynamic_neutral12:I = 0x7f060a85

.field public static m3_ref_palette_dynamic_neutral17:I = 0x7f060a86

.field public static m3_ref_palette_dynamic_neutral20:I = 0x7f060a87

.field public static m3_ref_palette_dynamic_neutral22:I = 0x7f060a88

.field public static m3_ref_palette_dynamic_neutral24:I = 0x7f060a89

.field public static m3_ref_palette_dynamic_neutral30:I = 0x7f060a8a

.field public static m3_ref_palette_dynamic_neutral4:I = 0x7f060a8b

.field public static m3_ref_palette_dynamic_neutral40:I = 0x7f060a8c

.field public static m3_ref_palette_dynamic_neutral50:I = 0x7f060a8d

.field public static m3_ref_palette_dynamic_neutral6:I = 0x7f060a8e

.field public static m3_ref_palette_dynamic_neutral60:I = 0x7f060a8f

.field public static m3_ref_palette_dynamic_neutral70:I = 0x7f060a90

.field public static m3_ref_palette_dynamic_neutral80:I = 0x7f060a91

.field public static m3_ref_palette_dynamic_neutral87:I = 0x7f060a92

.field public static m3_ref_palette_dynamic_neutral90:I = 0x7f060a93

.field public static m3_ref_palette_dynamic_neutral92:I = 0x7f060a94

.field public static m3_ref_palette_dynamic_neutral94:I = 0x7f060a95

.field public static m3_ref_palette_dynamic_neutral95:I = 0x7f060a96

.field public static m3_ref_palette_dynamic_neutral96:I = 0x7f060a97

.field public static m3_ref_palette_dynamic_neutral98:I = 0x7f060a98

.field public static m3_ref_palette_dynamic_neutral99:I = 0x7f060a99

.field public static m3_ref_palette_dynamic_neutral_variant0:I = 0x7f060a9a

.field public static m3_ref_palette_dynamic_neutral_variant10:I = 0x7f060a9b

.field public static m3_ref_palette_dynamic_neutral_variant100:I = 0x7f060a9c

.field public static m3_ref_palette_dynamic_neutral_variant12:I = 0x7f060a9d

.field public static m3_ref_palette_dynamic_neutral_variant17:I = 0x7f060a9e

.field public static m3_ref_palette_dynamic_neutral_variant20:I = 0x7f060a9f

.field public static m3_ref_palette_dynamic_neutral_variant22:I = 0x7f060aa0

.field public static m3_ref_palette_dynamic_neutral_variant24:I = 0x7f060aa1

.field public static m3_ref_palette_dynamic_neutral_variant30:I = 0x7f060aa2

.field public static m3_ref_palette_dynamic_neutral_variant4:I = 0x7f060aa3

.field public static m3_ref_palette_dynamic_neutral_variant40:I = 0x7f060aa4

.field public static m3_ref_palette_dynamic_neutral_variant50:I = 0x7f060aa5

.field public static m3_ref_palette_dynamic_neutral_variant6:I = 0x7f060aa6

.field public static m3_ref_palette_dynamic_neutral_variant60:I = 0x7f060aa7

.field public static m3_ref_palette_dynamic_neutral_variant70:I = 0x7f060aa8

.field public static m3_ref_palette_dynamic_neutral_variant80:I = 0x7f060aa9

.field public static m3_ref_palette_dynamic_neutral_variant87:I = 0x7f060aaa

.field public static m3_ref_palette_dynamic_neutral_variant90:I = 0x7f060aab

.field public static m3_ref_palette_dynamic_neutral_variant92:I = 0x7f060aac

.field public static m3_ref_palette_dynamic_neutral_variant94:I = 0x7f060aad

.field public static m3_ref_palette_dynamic_neutral_variant95:I = 0x7f060aae

.field public static m3_ref_palette_dynamic_neutral_variant96:I = 0x7f060aaf

.field public static m3_ref_palette_dynamic_neutral_variant98:I = 0x7f060ab0

.field public static m3_ref_palette_dynamic_neutral_variant99:I = 0x7f060ab1

.field public static m3_ref_palette_dynamic_primary0:I = 0x7f060ab2

.field public static m3_ref_palette_dynamic_primary10:I = 0x7f060ab3

.field public static m3_ref_palette_dynamic_primary100:I = 0x7f060ab4

.field public static m3_ref_palette_dynamic_primary20:I = 0x7f060ab5

.field public static m3_ref_palette_dynamic_primary30:I = 0x7f060ab6

.field public static m3_ref_palette_dynamic_primary40:I = 0x7f060ab7

.field public static m3_ref_palette_dynamic_primary50:I = 0x7f060ab8

.field public static m3_ref_palette_dynamic_primary60:I = 0x7f060ab9

.field public static m3_ref_palette_dynamic_primary70:I = 0x7f060aba

.field public static m3_ref_palette_dynamic_primary80:I = 0x7f060abb

.field public static m3_ref_palette_dynamic_primary90:I = 0x7f060abc

.field public static m3_ref_palette_dynamic_primary95:I = 0x7f060abd

.field public static m3_ref_palette_dynamic_primary99:I = 0x7f060abe

.field public static m3_ref_palette_dynamic_secondary0:I = 0x7f060abf

.field public static m3_ref_palette_dynamic_secondary10:I = 0x7f060ac0

.field public static m3_ref_palette_dynamic_secondary100:I = 0x7f060ac1

.field public static m3_ref_palette_dynamic_secondary20:I = 0x7f060ac2

.field public static m3_ref_palette_dynamic_secondary30:I = 0x7f060ac3

.field public static m3_ref_palette_dynamic_secondary40:I = 0x7f060ac4

.field public static m3_ref_palette_dynamic_secondary50:I = 0x7f060ac5

.field public static m3_ref_palette_dynamic_secondary60:I = 0x7f060ac6

.field public static m3_ref_palette_dynamic_secondary70:I = 0x7f060ac7

.field public static m3_ref_palette_dynamic_secondary80:I = 0x7f060ac8

.field public static m3_ref_palette_dynamic_secondary90:I = 0x7f060ac9

.field public static m3_ref_palette_dynamic_secondary95:I = 0x7f060aca

.field public static m3_ref_palette_dynamic_secondary99:I = 0x7f060acb

.field public static m3_ref_palette_dynamic_tertiary0:I = 0x7f060acc

.field public static m3_ref_palette_dynamic_tertiary10:I = 0x7f060acd

.field public static m3_ref_palette_dynamic_tertiary100:I = 0x7f060ace

.field public static m3_ref_palette_dynamic_tertiary20:I = 0x7f060acf

.field public static m3_ref_palette_dynamic_tertiary30:I = 0x7f060ad0

.field public static m3_ref_palette_dynamic_tertiary40:I = 0x7f060ad1

.field public static m3_ref_palette_dynamic_tertiary50:I = 0x7f060ad2

.field public static m3_ref_palette_dynamic_tertiary60:I = 0x7f060ad3

.field public static m3_ref_palette_dynamic_tertiary70:I = 0x7f060ad4

.field public static m3_ref_palette_dynamic_tertiary80:I = 0x7f060ad5

.field public static m3_ref_palette_dynamic_tertiary90:I = 0x7f060ad6

.field public static m3_ref_palette_dynamic_tertiary95:I = 0x7f060ad7

.field public static m3_ref_palette_dynamic_tertiary99:I = 0x7f060ad8

.field public static m3_ref_palette_error0:I = 0x7f060ad9

.field public static m3_ref_palette_error10:I = 0x7f060ada

.field public static m3_ref_palette_error100:I = 0x7f060adb

.field public static m3_ref_palette_error20:I = 0x7f060adc

.field public static m3_ref_palette_error30:I = 0x7f060add

.field public static m3_ref_palette_error40:I = 0x7f060ade

.field public static m3_ref_palette_error50:I = 0x7f060adf

.field public static m3_ref_palette_error60:I = 0x7f060ae0

.field public static m3_ref_palette_error70:I = 0x7f060ae1

.field public static m3_ref_palette_error80:I = 0x7f060ae2

.field public static m3_ref_palette_error90:I = 0x7f060ae3

.field public static m3_ref_palette_error95:I = 0x7f060ae4

.field public static m3_ref_palette_error99:I = 0x7f060ae5

.field public static m3_ref_palette_neutral0:I = 0x7f060ae6

.field public static m3_ref_palette_neutral10:I = 0x7f060ae7

.field public static m3_ref_palette_neutral100:I = 0x7f060ae8

.field public static m3_ref_palette_neutral12:I = 0x7f060ae9

.field public static m3_ref_palette_neutral17:I = 0x7f060aea

.field public static m3_ref_palette_neutral20:I = 0x7f060aeb

.field public static m3_ref_palette_neutral22:I = 0x7f060aec

.field public static m3_ref_palette_neutral24:I = 0x7f060aed

.field public static m3_ref_palette_neutral30:I = 0x7f060aee

.field public static m3_ref_palette_neutral4:I = 0x7f060aef

.field public static m3_ref_palette_neutral40:I = 0x7f060af0

.field public static m3_ref_palette_neutral50:I = 0x7f060af1

.field public static m3_ref_palette_neutral6:I = 0x7f060af2

.field public static m3_ref_palette_neutral60:I = 0x7f060af3

.field public static m3_ref_palette_neutral70:I = 0x7f060af4

.field public static m3_ref_palette_neutral80:I = 0x7f060af5

.field public static m3_ref_palette_neutral87:I = 0x7f060af6

.field public static m3_ref_palette_neutral90:I = 0x7f060af7

.field public static m3_ref_palette_neutral92:I = 0x7f060af8

.field public static m3_ref_palette_neutral94:I = 0x7f060af9

.field public static m3_ref_palette_neutral95:I = 0x7f060afa

.field public static m3_ref_palette_neutral96:I = 0x7f060afb

.field public static m3_ref_palette_neutral98:I = 0x7f060afc

.field public static m3_ref_palette_neutral99:I = 0x7f060afd

.field public static m3_ref_palette_neutral_variant0:I = 0x7f060afe

.field public static m3_ref_palette_neutral_variant10:I = 0x7f060aff

.field public static m3_ref_palette_neutral_variant100:I = 0x7f060b00

.field public static m3_ref_palette_neutral_variant20:I = 0x7f060b01

.field public static m3_ref_palette_neutral_variant30:I = 0x7f060b02

.field public static m3_ref_palette_neutral_variant40:I = 0x7f060b03

.field public static m3_ref_palette_neutral_variant50:I = 0x7f060b04

.field public static m3_ref_palette_neutral_variant60:I = 0x7f060b05

.field public static m3_ref_palette_neutral_variant70:I = 0x7f060b06

.field public static m3_ref_palette_neutral_variant80:I = 0x7f060b07

.field public static m3_ref_palette_neutral_variant90:I = 0x7f060b08

.field public static m3_ref_palette_neutral_variant95:I = 0x7f060b09

.field public static m3_ref_palette_neutral_variant99:I = 0x7f060b0a

.field public static m3_ref_palette_primary0:I = 0x7f060b0b

.field public static m3_ref_palette_primary10:I = 0x7f060b0c

.field public static m3_ref_palette_primary100:I = 0x7f060b0d

.field public static m3_ref_palette_primary20:I = 0x7f060b0e

.field public static m3_ref_palette_primary30:I = 0x7f060b0f

.field public static m3_ref_palette_primary40:I = 0x7f060b10

.field public static m3_ref_palette_primary50:I = 0x7f060b11

.field public static m3_ref_palette_primary60:I = 0x7f060b12

.field public static m3_ref_palette_primary70:I = 0x7f060b13

.field public static m3_ref_palette_primary80:I = 0x7f060b14

.field public static m3_ref_palette_primary90:I = 0x7f060b15

.field public static m3_ref_palette_primary95:I = 0x7f060b16

.field public static m3_ref_palette_primary99:I = 0x7f060b17

.field public static m3_ref_palette_secondary0:I = 0x7f060b18

.field public static m3_ref_palette_secondary10:I = 0x7f060b19

.field public static m3_ref_palette_secondary100:I = 0x7f060b1a

.field public static m3_ref_palette_secondary20:I = 0x7f060b1b

.field public static m3_ref_palette_secondary30:I = 0x7f060b1c

.field public static m3_ref_palette_secondary40:I = 0x7f060b1d

.field public static m3_ref_palette_secondary50:I = 0x7f060b1e

.field public static m3_ref_palette_secondary60:I = 0x7f060b1f

.field public static m3_ref_palette_secondary70:I = 0x7f060b20

.field public static m3_ref_palette_secondary80:I = 0x7f060b21

.field public static m3_ref_palette_secondary90:I = 0x7f060b22

.field public static m3_ref_palette_secondary95:I = 0x7f060b23

.field public static m3_ref_palette_secondary99:I = 0x7f060b24

.field public static m3_ref_palette_tertiary0:I = 0x7f060b25

.field public static m3_ref_palette_tertiary10:I = 0x7f060b26

.field public static m3_ref_palette_tertiary100:I = 0x7f060b27

.field public static m3_ref_palette_tertiary20:I = 0x7f060b28

.field public static m3_ref_palette_tertiary30:I = 0x7f060b29

.field public static m3_ref_palette_tertiary40:I = 0x7f060b2a

.field public static m3_ref_palette_tertiary50:I = 0x7f060b2b

.field public static m3_ref_palette_tertiary60:I = 0x7f060b2c

.field public static m3_ref_palette_tertiary70:I = 0x7f060b2d

.field public static m3_ref_palette_tertiary80:I = 0x7f060b2e

.field public static m3_ref_palette_tertiary90:I = 0x7f060b2f

.field public static m3_ref_palette_tertiary95:I = 0x7f060b30

.field public static m3_ref_palette_tertiary99:I = 0x7f060b31

.field public static m3_ref_palette_white:I = 0x7f060b32

.field public static m3_selection_control_ripple_color_selector:I = 0x7f060b33

.field public static m3_simple_item_ripple_color:I = 0x7f060b34

.field public static m3_slider_active_track_color:I = 0x7f060b35

.field public static m3_slider_inactive_track_color:I = 0x7f060b38

.field public static m3_slider_thumb_color:I = 0x7f060b3a

.field public static m3_switch_thumb_tint:I = 0x7f060b3c

.field public static m3_switch_track_tint:I = 0x7f060b3d

.field public static m3_sys_color_dark_background:I = 0x7f060b3e

.field public static m3_sys_color_dark_error:I = 0x7f060b3f

.field public static m3_sys_color_dark_error_container:I = 0x7f060b40

.field public static m3_sys_color_dark_inverse_on_surface:I = 0x7f060b41

.field public static m3_sys_color_dark_inverse_primary:I = 0x7f060b42

.field public static m3_sys_color_dark_inverse_surface:I = 0x7f060b43

.field public static m3_sys_color_dark_on_background:I = 0x7f060b44

.field public static m3_sys_color_dark_on_error:I = 0x7f060b45

.field public static m3_sys_color_dark_on_error_container:I = 0x7f060b46

.field public static m3_sys_color_dark_on_primary:I = 0x7f060b47

.field public static m3_sys_color_dark_on_primary_container:I = 0x7f060b48

.field public static m3_sys_color_dark_on_secondary:I = 0x7f060b49

.field public static m3_sys_color_dark_on_secondary_container:I = 0x7f060b4a

.field public static m3_sys_color_dark_on_surface:I = 0x7f060b4b

.field public static m3_sys_color_dark_on_surface_variant:I = 0x7f060b4c

.field public static m3_sys_color_dark_on_tertiary:I = 0x7f060b4d

.field public static m3_sys_color_dark_on_tertiary_container:I = 0x7f060b4e

.field public static m3_sys_color_dark_outline:I = 0x7f060b4f

.field public static m3_sys_color_dark_outline_variant:I = 0x7f060b50

.field public static m3_sys_color_dark_primary:I = 0x7f060b51

.field public static m3_sys_color_dark_primary_container:I = 0x7f060b52

.field public static m3_sys_color_dark_secondary:I = 0x7f060b53

.field public static m3_sys_color_dark_secondary_container:I = 0x7f060b54

.field public static m3_sys_color_dark_surface:I = 0x7f060b55

.field public static m3_sys_color_dark_surface_bright:I = 0x7f060b56

.field public static m3_sys_color_dark_surface_container:I = 0x7f060b57

.field public static m3_sys_color_dark_surface_container_high:I = 0x7f060b58

.field public static m3_sys_color_dark_surface_container_highest:I = 0x7f060b59

.field public static m3_sys_color_dark_surface_container_low:I = 0x7f060b5a

.field public static m3_sys_color_dark_surface_container_lowest:I = 0x7f060b5b

.field public static m3_sys_color_dark_surface_dim:I = 0x7f060b5c

.field public static m3_sys_color_dark_surface_variant:I = 0x7f060b5d

.field public static m3_sys_color_dark_tertiary:I = 0x7f060b5e

.field public static m3_sys_color_dark_tertiary_container:I = 0x7f060b5f

.field public static m3_sys_color_dynamic_dark_background:I = 0x7f060b60

.field public static m3_sys_color_dynamic_dark_error:I = 0x7f060b61

.field public static m3_sys_color_dynamic_dark_error_container:I = 0x7f060b62

.field public static m3_sys_color_dynamic_dark_inverse_on_surface:I = 0x7f060b63

.field public static m3_sys_color_dynamic_dark_inverse_primary:I = 0x7f060b64

.field public static m3_sys_color_dynamic_dark_inverse_surface:I = 0x7f060b65

.field public static m3_sys_color_dynamic_dark_on_background:I = 0x7f060b66

.field public static m3_sys_color_dynamic_dark_on_error:I = 0x7f060b67

.field public static m3_sys_color_dynamic_dark_on_error_container:I = 0x7f060b68

.field public static m3_sys_color_dynamic_dark_on_primary:I = 0x7f060b69

.field public static m3_sys_color_dynamic_dark_on_primary_container:I = 0x7f060b6a

.field public static m3_sys_color_dynamic_dark_on_secondary:I = 0x7f060b6b

.field public static m3_sys_color_dynamic_dark_on_secondary_container:I = 0x7f060b6c

.field public static m3_sys_color_dynamic_dark_on_surface:I = 0x7f060b6d

.field public static m3_sys_color_dynamic_dark_on_surface_variant:I = 0x7f060b6e

.field public static m3_sys_color_dynamic_dark_on_tertiary:I = 0x7f060b6f

.field public static m3_sys_color_dynamic_dark_on_tertiary_container:I = 0x7f060b70

.field public static m3_sys_color_dynamic_dark_outline:I = 0x7f060b71

.field public static m3_sys_color_dynamic_dark_outline_variant:I = 0x7f060b72

.field public static m3_sys_color_dynamic_dark_primary:I = 0x7f060b73

.field public static m3_sys_color_dynamic_dark_primary_container:I = 0x7f060b74

.field public static m3_sys_color_dynamic_dark_secondary:I = 0x7f060b75

.field public static m3_sys_color_dynamic_dark_secondary_container:I = 0x7f060b76

.field public static m3_sys_color_dynamic_dark_surface:I = 0x7f060b77

.field public static m3_sys_color_dynamic_dark_surface_bright:I = 0x7f060b78

.field public static m3_sys_color_dynamic_dark_surface_container:I = 0x7f060b79

.field public static m3_sys_color_dynamic_dark_surface_container_high:I = 0x7f060b7a

.field public static m3_sys_color_dynamic_dark_surface_container_highest:I = 0x7f060b7b

.field public static m3_sys_color_dynamic_dark_surface_container_low:I = 0x7f060b7c

.field public static m3_sys_color_dynamic_dark_surface_container_lowest:I = 0x7f060b7d

.field public static m3_sys_color_dynamic_dark_surface_dim:I = 0x7f060b7e

.field public static m3_sys_color_dynamic_dark_surface_variant:I = 0x7f060b7f

.field public static m3_sys_color_dynamic_dark_tertiary:I = 0x7f060b80

.field public static m3_sys_color_dynamic_dark_tertiary_container:I = 0x7f060b81

.field public static m3_sys_color_dynamic_light_background:I = 0x7f060b82

.field public static m3_sys_color_dynamic_light_error:I = 0x7f060b83

.field public static m3_sys_color_dynamic_light_error_container:I = 0x7f060b84

.field public static m3_sys_color_dynamic_light_inverse_on_surface:I = 0x7f060b85

.field public static m3_sys_color_dynamic_light_inverse_primary:I = 0x7f060b86

.field public static m3_sys_color_dynamic_light_inverse_surface:I = 0x7f060b87

.field public static m3_sys_color_dynamic_light_on_background:I = 0x7f060b88

.field public static m3_sys_color_dynamic_light_on_error:I = 0x7f060b89

.field public static m3_sys_color_dynamic_light_on_error_container:I = 0x7f060b8a

.field public static m3_sys_color_dynamic_light_on_primary:I = 0x7f060b8b

.field public static m3_sys_color_dynamic_light_on_primary_container:I = 0x7f060b8c

.field public static m3_sys_color_dynamic_light_on_secondary:I = 0x7f060b8d

.field public static m3_sys_color_dynamic_light_on_secondary_container:I = 0x7f060b8e

.field public static m3_sys_color_dynamic_light_on_surface:I = 0x7f060b8f

.field public static m3_sys_color_dynamic_light_on_surface_variant:I = 0x7f060b90

.field public static m3_sys_color_dynamic_light_on_tertiary:I = 0x7f060b91

.field public static m3_sys_color_dynamic_light_on_tertiary_container:I = 0x7f060b92

.field public static m3_sys_color_dynamic_light_outline:I = 0x7f060b93

.field public static m3_sys_color_dynamic_light_outline_variant:I = 0x7f060b94

.field public static m3_sys_color_dynamic_light_primary:I = 0x7f060b95

.field public static m3_sys_color_dynamic_light_primary_container:I = 0x7f060b96

.field public static m3_sys_color_dynamic_light_secondary:I = 0x7f060b97

.field public static m3_sys_color_dynamic_light_secondary_container:I = 0x7f060b98

.field public static m3_sys_color_dynamic_light_surface:I = 0x7f060b99

.field public static m3_sys_color_dynamic_light_surface_bright:I = 0x7f060b9a

.field public static m3_sys_color_dynamic_light_surface_container:I = 0x7f060b9b

.field public static m3_sys_color_dynamic_light_surface_container_high:I = 0x7f060b9c

.field public static m3_sys_color_dynamic_light_surface_container_highest:I = 0x7f060b9d

.field public static m3_sys_color_dynamic_light_surface_container_low:I = 0x7f060b9e

.field public static m3_sys_color_dynamic_light_surface_container_lowest:I = 0x7f060b9f

.field public static m3_sys_color_dynamic_light_surface_dim:I = 0x7f060ba0

.field public static m3_sys_color_dynamic_light_surface_variant:I = 0x7f060ba1

.field public static m3_sys_color_dynamic_light_tertiary:I = 0x7f060ba2

.field public static m3_sys_color_dynamic_light_tertiary_container:I = 0x7f060ba3

.field public static m3_sys_color_dynamic_on_primary_fixed:I = 0x7f060ba4

.field public static m3_sys_color_dynamic_on_primary_fixed_variant:I = 0x7f060ba5

.field public static m3_sys_color_dynamic_on_secondary_fixed:I = 0x7f060ba6

.field public static m3_sys_color_dynamic_on_secondary_fixed_variant:I = 0x7f060ba7

.field public static m3_sys_color_dynamic_on_tertiary_fixed:I = 0x7f060ba8

.field public static m3_sys_color_dynamic_on_tertiary_fixed_variant:I = 0x7f060ba9

.field public static m3_sys_color_dynamic_primary_fixed:I = 0x7f060baa

.field public static m3_sys_color_dynamic_primary_fixed_dim:I = 0x7f060bab

.field public static m3_sys_color_dynamic_secondary_fixed:I = 0x7f060bac

.field public static m3_sys_color_dynamic_secondary_fixed_dim:I = 0x7f060bad

.field public static m3_sys_color_dynamic_tertiary_fixed:I = 0x7f060bae

.field public static m3_sys_color_dynamic_tertiary_fixed_dim:I = 0x7f060baf

.field public static m3_sys_color_light_background:I = 0x7f060bb0

.field public static m3_sys_color_light_error:I = 0x7f060bb1

.field public static m3_sys_color_light_error_container:I = 0x7f060bb2

.field public static m3_sys_color_light_inverse_on_surface:I = 0x7f060bb3

.field public static m3_sys_color_light_inverse_primary:I = 0x7f060bb4

.field public static m3_sys_color_light_inverse_surface:I = 0x7f060bb5

.field public static m3_sys_color_light_on_background:I = 0x7f060bb6

.field public static m3_sys_color_light_on_error:I = 0x7f060bb7

.field public static m3_sys_color_light_on_error_container:I = 0x7f060bb8

.field public static m3_sys_color_light_on_primary:I = 0x7f060bb9

.field public static m3_sys_color_light_on_primary_container:I = 0x7f060bba

.field public static m3_sys_color_light_on_secondary:I = 0x7f060bbb

.field public static m3_sys_color_light_on_secondary_container:I = 0x7f060bbc

.field public static m3_sys_color_light_on_surface:I = 0x7f060bbd

.field public static m3_sys_color_light_on_surface_variant:I = 0x7f060bbe

.field public static m3_sys_color_light_on_tertiary:I = 0x7f060bbf

.field public static m3_sys_color_light_on_tertiary_container:I = 0x7f060bc0

.field public static m3_sys_color_light_outline:I = 0x7f060bc1

.field public static m3_sys_color_light_outline_variant:I = 0x7f060bc2

.field public static m3_sys_color_light_primary:I = 0x7f060bc3

.field public static m3_sys_color_light_primary_container:I = 0x7f060bc4

.field public static m3_sys_color_light_secondary:I = 0x7f060bc5

.field public static m3_sys_color_light_secondary_container:I = 0x7f060bc6

.field public static m3_sys_color_light_surface:I = 0x7f060bc7

.field public static m3_sys_color_light_surface_bright:I = 0x7f060bc8

.field public static m3_sys_color_light_surface_container:I = 0x7f060bc9

.field public static m3_sys_color_light_surface_container_high:I = 0x7f060bca

.field public static m3_sys_color_light_surface_container_highest:I = 0x7f060bcb

.field public static m3_sys_color_light_surface_container_low:I = 0x7f060bcc

.field public static m3_sys_color_light_surface_container_lowest:I = 0x7f060bcd

.field public static m3_sys_color_light_surface_dim:I = 0x7f060bce

.field public static m3_sys_color_light_surface_variant:I = 0x7f060bcf

.field public static m3_sys_color_light_tertiary:I = 0x7f060bd0

.field public static m3_sys_color_light_tertiary_container:I = 0x7f060bd1

.field public static m3_sys_color_on_primary_fixed:I = 0x7f060bd2

.field public static m3_sys_color_on_primary_fixed_variant:I = 0x7f060bd3

.field public static m3_sys_color_on_secondary_fixed:I = 0x7f060bd4

.field public static m3_sys_color_on_secondary_fixed_variant:I = 0x7f060bd5

.field public static m3_sys_color_on_tertiary_fixed:I = 0x7f060bd6

.field public static m3_sys_color_on_tertiary_fixed_variant:I = 0x7f060bd7

.field public static m3_sys_color_primary_fixed:I = 0x7f060bd8

.field public static m3_sys_color_primary_fixed_dim:I = 0x7f060bd9

.field public static m3_sys_color_secondary_fixed:I = 0x7f060bda

.field public static m3_sys_color_secondary_fixed_dim:I = 0x7f060bdb

.field public static m3_sys_color_tertiary_fixed:I = 0x7f060bdc

.field public static m3_sys_color_tertiary_fixed_dim:I = 0x7f060bdd

.field public static m3_tabs_icon_color:I = 0x7f060bde

.field public static m3_tabs_icon_color_secondary:I = 0x7f060bdf

.field public static m3_tabs_ripple_color:I = 0x7f060be0

.field public static m3_tabs_ripple_color_secondary:I = 0x7f060be1

.field public static m3_tabs_text_color:I = 0x7f060be2

.field public static m3_tabs_text_color_secondary:I = 0x7f060be3

.field public static m3_text_button_background_color_selector:I = 0x7f060be4

.field public static m3_text_button_foreground_color_selector:I = 0x7f060be5

.field public static m3_text_button_ripple_color_selector:I = 0x7f060be6

.field public static m3_textfield_filled_background_color:I = 0x7f060be7

.field public static m3_textfield_indicator_text_color:I = 0x7f060be8

.field public static m3_textfield_input_text_color:I = 0x7f060be9

.field public static m3_textfield_label_color:I = 0x7f060bea

.field public static m3_textfield_stroke_color:I = 0x7f060beb

.field public static m3_timepicker_button_background_color:I = 0x7f060bec

.field public static m3_timepicker_button_ripple_color:I = 0x7f060bed

.field public static m3_timepicker_button_text_color:I = 0x7f060bee

.field public static m3_timepicker_clock_text_color:I = 0x7f060bef

.field public static m3_timepicker_display_background_color:I = 0x7f060bf0

.field public static m3_timepicker_display_ripple_color:I = 0x7f060bf1

.field public static m3_timepicker_display_text_color:I = 0x7f060bf2

.field public static m3_timepicker_secondary_text_button_ripple_color:I = 0x7f060bf3

.field public static m3_timepicker_secondary_text_button_text_color:I = 0x7f060bf4

.field public static m3_timepicker_time_input_stroke_color:I = 0x7f060bf5

.field public static m3_tonal_button_ripple_color_selector:I = 0x7f060bf6

.field public static material_blue_grey_800:I = 0x7f060c07

.field public static material_blue_grey_900:I = 0x7f060c08

.field public static material_blue_grey_950:I = 0x7f060c09

.field public static material_cursor_color:I = 0x7f060c0a

.field public static material_deep_teal_200:I = 0x7f060c0b

.field public static material_deep_teal_500:I = 0x7f060c0c

.field public static material_divider_color:I = 0x7f060c0d

.field public static material_dynamic_color_dark_error:I = 0x7f060c0e

.field public static material_dynamic_color_dark_error_container:I = 0x7f060c0f

.field public static material_dynamic_color_dark_on_error:I = 0x7f060c10

.field public static material_dynamic_color_dark_on_error_container:I = 0x7f060c11

.field public static material_dynamic_color_light_error:I = 0x7f060c12

.field public static material_dynamic_color_light_error_container:I = 0x7f060c13

.field public static material_dynamic_color_light_on_error:I = 0x7f060c14

.field public static material_dynamic_color_light_on_error_container:I = 0x7f060c15

.field public static material_dynamic_neutral0:I = 0x7f060c16

.field public static material_dynamic_neutral10:I = 0x7f060c17

.field public static material_dynamic_neutral100:I = 0x7f060c18

.field public static material_dynamic_neutral20:I = 0x7f060c19

.field public static material_dynamic_neutral30:I = 0x7f060c1a

.field public static material_dynamic_neutral40:I = 0x7f060c1b

.field public static material_dynamic_neutral50:I = 0x7f060c1c

.field public static material_dynamic_neutral60:I = 0x7f060c1d

.field public static material_dynamic_neutral70:I = 0x7f060c1e

.field public static material_dynamic_neutral80:I = 0x7f060c1f

.field public static material_dynamic_neutral90:I = 0x7f060c20

.field public static material_dynamic_neutral95:I = 0x7f060c21

.field public static material_dynamic_neutral99:I = 0x7f060c22

.field public static material_dynamic_neutral_variant0:I = 0x7f060c23

.field public static material_dynamic_neutral_variant10:I = 0x7f060c24

.field public static material_dynamic_neutral_variant100:I = 0x7f060c25

.field public static material_dynamic_neutral_variant20:I = 0x7f060c26

.field public static material_dynamic_neutral_variant30:I = 0x7f060c27

.field public static material_dynamic_neutral_variant40:I = 0x7f060c28

.field public static material_dynamic_neutral_variant50:I = 0x7f060c29

.field public static material_dynamic_neutral_variant60:I = 0x7f060c2a

.field public static material_dynamic_neutral_variant70:I = 0x7f060c2b

.field public static material_dynamic_neutral_variant80:I = 0x7f060c2c

.field public static material_dynamic_neutral_variant90:I = 0x7f060c2d

.field public static material_dynamic_neutral_variant95:I = 0x7f060c2e

.field public static material_dynamic_neutral_variant99:I = 0x7f060c2f

.field public static material_dynamic_primary0:I = 0x7f060c30

.field public static material_dynamic_primary10:I = 0x7f060c31

.field public static material_dynamic_primary100:I = 0x7f060c32

.field public static material_dynamic_primary20:I = 0x7f060c33

.field public static material_dynamic_primary30:I = 0x7f060c34

.field public static material_dynamic_primary40:I = 0x7f060c35

.field public static material_dynamic_primary50:I = 0x7f060c36

.field public static material_dynamic_primary60:I = 0x7f060c37

.field public static material_dynamic_primary70:I = 0x7f060c38

.field public static material_dynamic_primary80:I = 0x7f060c39

.field public static material_dynamic_primary90:I = 0x7f060c3a

.field public static material_dynamic_primary95:I = 0x7f060c3b

.field public static material_dynamic_primary99:I = 0x7f060c3c

.field public static material_dynamic_secondary0:I = 0x7f060c3d

.field public static material_dynamic_secondary10:I = 0x7f060c3e

.field public static material_dynamic_secondary100:I = 0x7f060c3f

.field public static material_dynamic_secondary20:I = 0x7f060c40

.field public static material_dynamic_secondary30:I = 0x7f060c41

.field public static material_dynamic_secondary40:I = 0x7f060c42

.field public static material_dynamic_secondary50:I = 0x7f060c43

.field public static material_dynamic_secondary60:I = 0x7f060c44

.field public static material_dynamic_secondary70:I = 0x7f060c45

.field public static material_dynamic_secondary80:I = 0x7f060c46

.field public static material_dynamic_secondary90:I = 0x7f060c47

.field public static material_dynamic_secondary95:I = 0x7f060c48

.field public static material_dynamic_secondary99:I = 0x7f060c49

.field public static material_dynamic_tertiary0:I = 0x7f060c4a

.field public static material_dynamic_tertiary10:I = 0x7f060c4b

.field public static material_dynamic_tertiary100:I = 0x7f060c4c

.field public static material_dynamic_tertiary20:I = 0x7f060c4d

.field public static material_dynamic_tertiary30:I = 0x7f060c4e

.field public static material_dynamic_tertiary40:I = 0x7f060c4f

.field public static material_dynamic_tertiary50:I = 0x7f060c50

.field public static material_dynamic_tertiary60:I = 0x7f060c51

.field public static material_dynamic_tertiary70:I = 0x7f060c52

.field public static material_dynamic_tertiary80:I = 0x7f060c53

.field public static material_dynamic_tertiary90:I = 0x7f060c54

.field public static material_dynamic_tertiary95:I = 0x7f060c55

.field public static material_dynamic_tertiary99:I = 0x7f060c56

.field public static material_grey_100:I = 0x7f060c57

.field public static material_grey_300:I = 0x7f060c58

.field public static material_grey_50:I = 0x7f060c59

.field public static material_grey_600:I = 0x7f060c5a

.field public static material_grey_800:I = 0x7f060c5b

.field public static material_grey_850:I = 0x7f060c5c

.field public static material_grey_900:I = 0x7f060c5d

.field public static material_harmonized_color_error:I = 0x7f060c5e

.field public static material_harmonized_color_error_container:I = 0x7f060c5f

.field public static material_harmonized_color_on_error:I = 0x7f060c60

.field public static material_harmonized_color_on_error_container:I = 0x7f060c61

.field public static material_on_background_disabled:I = 0x7f060c62

.field public static material_on_background_emphasis_high_type:I = 0x7f060c63

.field public static material_on_background_emphasis_medium:I = 0x7f060c64

.field public static material_on_primary_disabled:I = 0x7f060c65

.field public static material_on_primary_emphasis_high_type:I = 0x7f060c66

.field public static material_on_primary_emphasis_medium:I = 0x7f060c67

.field public static material_on_surface_disabled:I = 0x7f060c68

.field public static material_on_surface_emphasis_high_type:I = 0x7f060c69

.field public static material_on_surface_emphasis_medium:I = 0x7f060c6a

.field public static material_on_surface_stroke:I = 0x7f060c6b

.field public static material_personalized__highlighted_text:I = 0x7f060c6c

.field public static material_personalized__highlighted_text_inverse:I = 0x7f060c6d

.field public static material_personalized_color_background:I = 0x7f060c6e

.field public static material_personalized_color_control_activated:I = 0x7f060c6f

.field public static material_personalized_color_control_highlight:I = 0x7f060c70

.field public static material_personalized_color_control_normal:I = 0x7f060c71

.field public static material_personalized_color_error:I = 0x7f060c72

.field public static material_personalized_color_error_container:I = 0x7f060c73

.field public static material_personalized_color_on_background:I = 0x7f060c74

.field public static material_personalized_color_on_error:I = 0x7f060c75

.field public static material_personalized_color_on_error_container:I = 0x7f060c76

.field public static material_personalized_color_on_primary:I = 0x7f060c77

.field public static material_personalized_color_on_primary_container:I = 0x7f060c78

.field public static material_personalized_color_on_secondary:I = 0x7f060c79

.field public static material_personalized_color_on_secondary_container:I = 0x7f060c7a

.field public static material_personalized_color_on_surface:I = 0x7f060c7b

.field public static material_personalized_color_on_surface_inverse:I = 0x7f060c7c

.field public static material_personalized_color_on_surface_variant:I = 0x7f060c7d

.field public static material_personalized_color_on_tertiary:I = 0x7f060c7e

.field public static material_personalized_color_on_tertiary_container:I = 0x7f060c7f

.field public static material_personalized_color_outline:I = 0x7f060c80

.field public static material_personalized_color_outline_variant:I = 0x7f060c81

.field public static material_personalized_color_primary:I = 0x7f060c82

.field public static material_personalized_color_primary_container:I = 0x7f060c83

.field public static material_personalized_color_primary_inverse:I = 0x7f060c84

.field public static material_personalized_color_primary_text:I = 0x7f060c85

.field public static material_personalized_color_primary_text_inverse:I = 0x7f060c86

.field public static material_personalized_color_secondary:I = 0x7f060c87

.field public static material_personalized_color_secondary_container:I = 0x7f060c88

.field public static material_personalized_color_secondary_text:I = 0x7f060c89

.field public static material_personalized_color_secondary_text_inverse:I = 0x7f060c8a

.field public static material_personalized_color_surface:I = 0x7f060c8b

.field public static material_personalized_color_surface_bright:I = 0x7f060c8c

.field public static material_personalized_color_surface_container:I = 0x7f060c8d

.field public static material_personalized_color_surface_container_high:I = 0x7f060c8e

.field public static material_personalized_color_surface_container_highest:I = 0x7f060c8f

.field public static material_personalized_color_surface_container_low:I = 0x7f060c90

.field public static material_personalized_color_surface_container_lowest:I = 0x7f060c91

.field public static material_personalized_color_surface_dim:I = 0x7f060c92

.field public static material_personalized_color_surface_inverse:I = 0x7f060c93

.field public static material_personalized_color_surface_variant:I = 0x7f060c94

.field public static material_personalized_color_tertiary:I = 0x7f060c95

.field public static material_personalized_color_tertiary_container:I = 0x7f060c96

.field public static material_personalized_color_text_hint_foreground_inverse:I = 0x7f060c97

.field public static material_personalized_color_text_primary_inverse:I = 0x7f060c98

.field public static material_personalized_color_text_primary_inverse_disable_only:I = 0x7f060c99

.field public static material_personalized_color_text_secondary_and_tertiary_inverse:I = 0x7f060c9a

.field public static material_personalized_color_text_secondary_and_tertiary_inverse_disabled:I = 0x7f060c9b

.field public static material_personalized_hint_foreground:I = 0x7f060c9c

.field public static material_personalized_hint_foreground_inverse:I = 0x7f060c9d

.field public static material_personalized_primary_inverse_text_disable_only:I = 0x7f060c9e

.field public static material_personalized_primary_text_disable_only:I = 0x7f060c9f

.field public static material_slider_active_tick_marks_color:I = 0x7f060ca0

.field public static material_slider_active_track_color:I = 0x7f060ca1

.field public static material_slider_halo_color:I = 0x7f060ca2

.field public static material_slider_inactive_tick_marks_color:I = 0x7f060ca3

.field public static material_slider_inactive_track_color:I = 0x7f060ca4

.field public static material_slider_thumb_color:I = 0x7f060ca5

.field public static material_timepicker_button_background:I = 0x7f060ca6

.field public static material_timepicker_button_stroke:I = 0x7f060ca7

.field public static material_timepicker_clock_text_color:I = 0x7f060ca8

.field public static material_timepicker_clockface:I = 0x7f060ca9

.field public static material_timepicker_modebutton_tint:I = 0x7f060caa

.field public static mtrl_btn_bg_color_selector:I = 0x7f060cac

.field public static mtrl_btn_ripple_color:I = 0x7f060cad

.field public static mtrl_btn_stroke_color_selector:I = 0x7f060cae

.field public static mtrl_btn_text_btn_bg_color_selector:I = 0x7f060caf

.field public static mtrl_btn_text_btn_ripple_color:I = 0x7f060cb0

.field public static mtrl_btn_text_color_disabled:I = 0x7f060cb1

.field public static mtrl_btn_text_color_selector:I = 0x7f060cb2

.field public static mtrl_btn_transparent_bg_color:I = 0x7f060cb3

.field public static mtrl_calendar_item_stroke_color:I = 0x7f060cb4

.field public static mtrl_calendar_selected_range:I = 0x7f060cb5

.field public static mtrl_card_view_foreground:I = 0x7f060cb6

.field public static mtrl_card_view_ripple:I = 0x7f060cb7

.field public static mtrl_chip_background_color:I = 0x7f060cb8

.field public static mtrl_chip_close_icon_tint:I = 0x7f060cb9

.field public static mtrl_chip_surface_color:I = 0x7f060cba

.field public static mtrl_chip_text_color:I = 0x7f060cbb

.field public static mtrl_choice_chip_background_color:I = 0x7f060cbc

.field public static mtrl_choice_chip_ripple_color:I = 0x7f060cbd

.field public static mtrl_choice_chip_text_color:I = 0x7f060cbe

.field public static mtrl_error:I = 0x7f060cbf

.field public static mtrl_fab_bg_color_selector:I = 0x7f060cc0

.field public static mtrl_fab_icon_text_color_selector:I = 0x7f060cc1

.field public static mtrl_fab_ripple_color:I = 0x7f060cc2

.field public static mtrl_filled_background_color:I = 0x7f060cc3

.field public static mtrl_filled_icon_tint:I = 0x7f060cc4

.field public static mtrl_filled_stroke_color:I = 0x7f060cc5

.field public static mtrl_indicator_text_color:I = 0x7f060cc6

.field public static mtrl_navigation_bar_colored_item_tint:I = 0x7f060cc7

.field public static mtrl_navigation_bar_colored_ripple_color:I = 0x7f060cc8

.field public static mtrl_navigation_bar_item_tint:I = 0x7f060cc9

.field public static mtrl_navigation_bar_ripple_color:I = 0x7f060cca

.field public static mtrl_navigation_item_background_color:I = 0x7f060ccb

.field public static mtrl_navigation_item_icon_tint:I = 0x7f060ccc

.field public static mtrl_navigation_item_text_color:I = 0x7f060ccd

.field public static mtrl_on_primary_text_btn_text_color_selector:I = 0x7f060cce

.field public static mtrl_on_surface_ripple_color:I = 0x7f060ccf

.field public static mtrl_outlined_icon_tint:I = 0x7f060cd0

.field public static mtrl_outlined_stroke_color:I = 0x7f060cd1

.field public static mtrl_popupmenu_overlay_color:I = 0x7f060cd2

.field public static mtrl_scrim_color:I = 0x7f060cd3

.field public static mtrl_switch_thumb_icon_tint:I = 0x7f060cd4

.field public static mtrl_switch_thumb_tint:I = 0x7f060cd5

.field public static mtrl_switch_track_decoration_tint:I = 0x7f060cd6

.field public static mtrl_switch_track_tint:I = 0x7f060cd7

.field public static mtrl_tabs_colored_ripple_color:I = 0x7f060cd8

.field public static mtrl_tabs_icon_color_selector:I = 0x7f060cd9

.field public static mtrl_tabs_icon_color_selector_colored:I = 0x7f060cda

.field public static mtrl_tabs_legacy_text_color_selector:I = 0x7f060cdb

.field public static mtrl_tabs_ripple_color:I = 0x7f060cdc

.field public static mtrl_text_btn_text_color_selector:I = 0x7f060cdd

.field public static mtrl_textinput_default_box_stroke_color:I = 0x7f060cde

.field public static mtrl_textinput_disabled_color:I = 0x7f060cdf

.field public static mtrl_textinput_filled_box_default_background_color:I = 0x7f060ce0

.field public static mtrl_textinput_focused_box_stroke_color:I = 0x7f060ce1

.field public static mtrl_textinput_hovered_box_stroke_color:I = 0x7f060ce2

.field public static notification_action_color_filter:I = 0x7f060cee

.field public static notification_icon_bg_color:I = 0x7f060cef

.field public static primary_dark_material_dark:I = 0x7f060ef3

.field public static primary_dark_material_light:I = 0x7f060ef4

.field public static primary_material_dark:I = 0x7f06109f

.field public static primary_material_light:I = 0x7f0610a0

.field public static primary_text_default_material_dark:I = 0x7f0610c9

.field public static primary_text_default_material_light:I = 0x7f0610ca

.field public static primary_text_disabled_material_dark:I = 0x7f0610cb

.field public static primary_text_disabled_material_light:I = 0x7f0610cc

.field public static ripple_material_dark:I = 0x7f0612ed

.field public static ripple_material_light:I = 0x7f0612ee

.field public static secondary_text_default_material_dark:I = 0x7f061763

.field public static secondary_text_default_material_light:I = 0x7f061764

.field public static secondary_text_disabled_material_dark:I = 0x7f061765

.field public static secondary_text_disabled_material_light:I = 0x7f061766

.field public static sns_alertTint:I = 0x7f061925

.field public static sns_backgroundCommon:I = 0x7f061926

.field public static sns_backgroundCritical:I = 0x7f061927

.field public static sns_backgroundInfo:I = 0x7f061928

.field public static sns_backgroundNeutral:I = 0x7f061929

.field public static sns_backgroundOther:I = 0x7f06192a

.field public static sns_backgroundSuccess:I = 0x7f06192b

.field public static sns_backgroundWarning:I = 0x7f06192c

.field public static sns_body_text_color:I = 0x7f06192d

.field public static sns_bottomSheetBackground:I = 0x7f06192e

.field public static sns_bottomSheetHandle:I = 0x7f06192f

.field public static sns_btn_primary_bg_tint_color:I = 0x7f061930

.field public static sns_btn_secondary_bg_tint_color:I = 0x7f061931

.field public static sns_cameraBackground:I = 0x7f061932

.field public static sns_cameraBackgroundOverlay:I = 0x7f061933

.field public static sns_cameraContent:I = 0x7f061934

.field public static sns_color_background_100:I = 0x7f061935

.field public static sns_color_critical_10:I = 0x7f061936

.field public static sns_color_critical_100:I = 0x7f061937

.field public static sns_color_critical_20:I = 0x7f061938

.field public static sns_color_critical_40:I = 0x7f061939

.field public static sns_color_critical_60:I = 0x7f06193a

.field public static sns_color_critical_80:I = 0x7f06193b

.field public static sns_color_neutral_10:I = 0x7f06193c

.field public static sns_color_neutral_100:I = 0x7f06193d

.field public static sns_color_neutral_20:I = 0x7f06193e

.field public static sns_color_neutral_40:I = 0x7f06193f

.field public static sns_color_neutral_5:I = 0x7f061940

.field public static sns_color_neutral_60:I = 0x7f061941

.field public static sns_color_neutral_60_no_background:I = 0x7f061942

.field public static sns_color_neutral_80:I = 0x7f061943

.field public static sns_color_neutral_80_no_background:I = 0x7f061944

.field public static sns_color_primary_10:I = 0x7f061945

.field public static sns_color_primary_100:I = 0x7f061946

.field public static sns_color_primary_20:I = 0x7f061947

.field public static sns_color_primary_40:I = 0x7f061948

.field public static sns_color_primary_5:I = 0x7f061949

.field public static sns_color_primary_50:I = 0x7f06194a

.field public static sns_color_primary_60:I = 0x7f06194b

.field public static sns_color_primary_80:I = 0x7f06194c

.field public static sns_color_success_10:I = 0x7f06194d

.field public static sns_color_success_100:I = 0x7f06194e

.field public static sns_color_success_20:I = 0x7f06194f

.field public static sns_color_success_40:I = 0x7f061950

.field public static sns_color_success_60:I = 0x7f061951

.field public static sns_color_warning_10:I = 0x7f061952

.field public static sns_color_warning_100:I = 0x7f061953

.field public static sns_color_warning_20:I = 0x7f061954

.field public static sns_color_warning_40:I = 0x7f061955

.field public static sns_color_warning_60:I = 0x7f061956

.field public static sns_color_white_100:I = 0x7f061957

.field public static sns_color_white_20:I = 0x7f061958

.field public static sns_color_white_40:I = 0x7f061959

.field public static sns_color_white_5:I = 0x7f06195a

.field public static sns_color_white_5_on_primary:I = 0x7f06195b

.field public static sns_color_white_60:I = 0x7f06195c

.field public static sns_color_white_80:I = 0x7f06195d

.field public static sns_contentCritical:I = 0x7f06195e

.field public static sns_contentInfo:I = 0x7f06195f

.field public static sns_contentLink:I = 0x7f061960

.field public static sns_contentNeutral:I = 0x7f061961

.field public static sns_contentStrong:I = 0x7f061962

.field public static sns_contentSuccess:I = 0x7f061963

.field public static sns_contentWarning:I = 0x7f061964

.field public static sns_contentWeak:I = 0x7f061965

.field public static sns_control_normal:I = 0x7f061966

.field public static sns_edit_bg_color:I = 0x7f061967

.field public static sns_edit_text_color:I = 0x7f061968

.field public static sns_edit_text_hint_color:I = 0x7f061969

.field public static sns_edit_tint_color:I = 0x7f06196a

.field public static sns_fieldBackground:I = 0x7f06196b

.field public static sns_fieldBackgroundInvalid:I = 0x7f06196c

.field public static sns_fieldBorder:I = 0x7f06196d

.field public static sns_fieldButtonBackgroundHighlighted:I = 0x7f06196e

.field public static sns_fieldContent:I = 0x7f06196f

.field public static sns_fieldPlaceholder:I = 0x7f061970

.field public static sns_fieldTint:I = 0x7f061971

.field public static sns_file_bg_color:I = 0x7f061972

.field public static sns_file_item_color:I = 0x7f061973

.field public static sns_frame_color:I = 0x7f061974

.field public static sns_linkButtonBackgroundHighlighted:I = 0x7f061975

.field public static sns_linkButtonContent:I = 0x7f061976

.field public static sns_linkButtonContentDisabled:I = 0x7f061977

.field public static sns_listSelectedItemBackground:I = 0x7f061978

.field public static sns_listSeparator:I = 0x7f061979

.field public static sns_mrtd_tint_color:I = 0x7f06197a

.field public static sns_navigationBarItem:I = 0x7f06197b

.field public static sns_pin_bg_color:I = 0x7f06197c

.field public static sns_primaryButtonBackground:I = 0x7f06197d

.field public static sns_primaryButtonBackgroundDisabled:I = 0x7f06197e

.field public static sns_primaryButtonBackgroundHighlighted:I = 0x7f06197f

.field public static sns_primaryButtonContent:I = 0x7f061980

.field public static sns_primaryButtonContentDisabled:I = 0x7f061981

.field public static sns_primaryButtonContentHighlighted:I = 0x7f061982

.field public static sns_primary_5_color:I = 0x7f061983

.field public static sns_progressBarBackground:I = 0x7f061984

.field public static sns_progressBarShimmer:I = 0x7f061985

.field public static sns_progressBarTint:I = 0x7f061986

.field public static sns_radio_bg_color:I = 0x7f061987

.field public static sns_secondaryButtonBackground:I = 0x7f061988

.field public static sns_secondaryButtonBackgroundDisabled:I = 0x7f061989

.field public static sns_secondaryButtonBackgroundHighlighted:I = 0x7f06198a

.field public static sns_secondaryButtonContent:I = 0x7f06198b

.field public static sns_secondaryButtonContentDisabled:I = 0x7f06198c

.field public static sns_secondaryButtonContentHighlighted:I = 0x7f06198d

.field public static sns_secondary_button_stroke_color:I = 0x7f06198e

.field public static sns_step_bg_color:I = 0x7f06198f

.field public static sns_step_tint_color:I = 0x7f061990

.field public static sns_subtitle_text_color:I = 0x7f061991

.field public static sns_switch_text_color:I = 0x7f061992

.field public static sns_tint_icon_neutral:I = 0x7f061993

.field public static sns_toolbarTint:I = 0x7f061994

.field public static sns_transparent:I = 0x7f061995

.field public static sns_videoident_document_bg_color:I = 0x7f061996

.field public static sns_videoident_document_stroke_color:I = 0x7f061997

.field public static sns_videoident_language_item_stroke_color:I = 0x7f061998

.field public static switch_thumb_disabled_material_dark:I = 0x7f061af2

.field public static switch_thumb_disabled_material_light:I = 0x7f061af3

.field public static switch_thumb_material_dark:I = 0x7f061b00

.field public static switch_thumb_material_light:I = 0x7f061b01

.field public static switch_thumb_normal_material_dark:I = 0x7f061b02

.field public static switch_thumb_normal_material_light:I = 0x7f061b03

.field public static tooltip_background_dark:I = 0x7f061be0

.field public static tooltip_background_light:I = 0x7f061be1


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
