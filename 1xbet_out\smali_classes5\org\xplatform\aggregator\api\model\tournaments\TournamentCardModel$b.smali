.class public final Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0015\u0008\u0086\u0008\u0018\u00002\u00020\u0001BU\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0004\u0012\u0008\u0010\u0007\u001a\u0004\u0018\u00010\u0002\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\n\u001a\u00020\u0008\u0012\u0008\u0010\u000b\u001a\u0004\u0018\u00010\u0002\u0012\u0006\u0010\u000c\u001a\u00020\u0002\u0012\u0008\u0010\u000e\u001a\u0004\u0018\u00010\r\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0010\u0010\u0011\u001a\u00020\u0002H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0010\u0010\u0014\u001a\u00020\u0013H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u001a\u0010\u0018\u001a\u00020\u00172\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0018\u0010\u0019R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u001b\u001a\u0004\u0008\u001c\u0010\u0012R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001d\u0010\u001e\u001a\u0004\u0008\u001f\u0010 R\u0017\u0010\u0006\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008!\u0010\u001e\u001a\u0004\u0008\u001d\u0010 R\u0019\u0010\u0007\u001a\u0004\u0018\u00010\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\"\u0010\u001b\u001a\u0004\u0008#\u0010\u0012R\u0017\u0010\t\u001a\u00020\u00088\u0006\u00a2\u0006\u000c\n\u0004\u0008$\u0010%\u001a\u0004\u0008$\u0010&R\u0017\u0010\n\u001a\u00020\u00088\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001f\u0010%\u001a\u0004\u0008!\u0010&R\u0019\u0010\u000b\u001a\u0004\u0018\u00010\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010\u001b\u001a\u0004\u0008\'\u0010\u0012R\u0017\u0010\u000c\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008(\u0010\u001b\u001a\u0004\u0008\"\u0010\u0012R\u0019\u0010\u000e\u001a\u0004\u0018\u00010\r8\u0006\u00a2\u0006\u000c\n\u0004\u0008)\u0010*\u001a\u0004\u0008\u001a\u0010+\u00a8\u0006,"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;",
        "",
        "",
        "title",
        "",
        "sum",
        "currencyId",
        "mediaValue",
        "Ljava/util/Date;",
        "startDate",
        "endDate",
        "description",
        "prizeTitle",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;",
        "counter",
        "<init>",
        "(Ljava/lang/String;JJLjava/lang/String;Ljava/util/Date;Ljava/util/Date;Ljava/lang/String;Ljava/lang/String;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;)V",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "Ljava/lang/String;",
        "g",
        "b",
        "J",
        "f",
        "()J",
        "c",
        "d",
        "getMediaValue",
        "e",
        "Ljava/util/Date;",
        "()Ljava/util/Date;",
        "getDescription",
        "h",
        "i",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;",
        "()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:J

.field public final c:J

.field public final d:Ljava/lang/String;

.field public final e:Ljava/util/Date;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Ljava/util/Date;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Ljava/lang/String;

.field public final h:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;


# direct methods
.method public constructor <init>(Ljava/lang/String;JJLjava/lang/String;Ljava/util/Date;Ljava/util/Date;Ljava/lang/String;Ljava/lang/String;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Ljava/util/Date;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Ljava/util/Date;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->a:Ljava/lang/String;

    .line 5
    .line 6
    iput-wide p2, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->b:J

    .line 7
    .line 8
    iput-wide p4, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->c:J

    .line 9
    .line 10
    iput-object p6, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->d:Ljava/lang/String;

    .line 11
    .line 12
    iput-object p7, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->e:Ljava/util/Date;

    .line 13
    .line 14
    iput-object p8, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->f:Ljava/util/Date;

    .line 15
    .line 16
    iput-object p9, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->g:Ljava/lang/String;

    .line 17
    .line 18
    iput-object p10, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->h:Ljava/lang/String;

    .line 19
    .line 20
    iput-object p11, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->i:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;

    .line 21
    .line 22
    return-void
.end method


# virtual methods
.method public final a()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->i:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->c:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final c()Ljava/util/Date;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->f:Ljava/util/Date;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->h:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e()Ljava/util/Date;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->e:Ljava/util/Date;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 7

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->a:Ljava/lang/String;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->a:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-wide v3, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->b:J

    iget-wide v5, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->b:J

    cmp-long v1, v3, v5

    if-eqz v1, :cond_3

    return v2

    :cond_3
    iget-wide v3, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->c:J

    iget-wide v5, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->c:J

    cmp-long v1, v3, v5

    if-eqz v1, :cond_4

    return v2

    :cond_4
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->d:Ljava/lang/String;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->d:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_5

    return v2

    :cond_5
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->e:Ljava/util/Date;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->e:Ljava/util/Date;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_6

    return v2

    :cond_6
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->f:Ljava/util/Date;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->f:Ljava/util/Date;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_7

    return v2

    :cond_7
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->g:Ljava/lang/String;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->g:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_8

    return v2

    :cond_8
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->h:Ljava/lang/String;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->h:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_9

    return v2

    :cond_9
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->i:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;

    iget-object p1, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->i:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;

    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_a

    return v2

    :cond_a
    return v0
.end method

.method public final f()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->b:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final g()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->a:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public hashCode()I
    .locals 3

    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->a:Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-wide v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->b:J

    invoke-static {v1, v2}, Lu/l;->a(J)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-wide v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->c:J

    invoke-static {v1, v2}, Lu/l;->a(J)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->d:Ljava/lang/String;

    const/4 v2, 0x0

    if-nez v1, :cond_0

    const/4 v1, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    :goto_0
    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->e:Ljava/util/Date;

    invoke-virtual {v1}, Ljava/util/Date;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->f:Ljava/util/Date;

    invoke-virtual {v1}, Ljava/util/Date;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->g:Ljava/lang/String;

    if-nez v1, :cond_1

    const/4 v1, 0x0

    goto :goto_1

    :cond_1
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    :goto_1
    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->h:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->i:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;

    if-nez v1, :cond_2

    goto :goto_2

    :cond_2
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;->hashCode()I

    move-result v2

    :goto_2
    add-int/2addr v0, v2

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 13
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->a:Ljava/lang/String;

    iget-wide v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->b:J

    iget-wide v3, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->c:J

    iget-object v5, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->d:Ljava/lang/String;

    iget-object v6, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->e:Ljava/util/Date;

    iget-object v7, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->f:Ljava/util/Date;

    iget-object v8, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->g:Ljava/lang/String;

    iget-object v9, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->h:Ljava/lang/String;

    iget-object v10, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->i:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;

    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    const-string v12, "Header(title="

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", sum="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v0, ", currencyId="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v0, ", mediaValue="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", startDate="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", endDate="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", description="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", prizeTitle="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", counter="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
