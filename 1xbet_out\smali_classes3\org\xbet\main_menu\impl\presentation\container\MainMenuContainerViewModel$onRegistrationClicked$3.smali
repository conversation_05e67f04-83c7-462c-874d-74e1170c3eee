.class final Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.main_menu.impl.presentation.container.MainMenuContainerViewModel$onRegistrationClicked$3"
    f = "MainMenuContainerViewModel.kt"
    l = {
        0xab
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;

    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->P3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$b;

    .line 34
    .line 35
    invoke-direct {v1, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$b;-><init>(Z)V

    .line 36
    .line 37
    .line 38
    invoke-virtual {p1, v1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 42
    .line 43
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->G3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)LU80/a;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 48
    .line 49
    invoke-static {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->E3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lorg/xbet/main_menu/impl/domain/models/MainMenuCategoryModel;

    .line 50
    .line 51
    .line 52
    move-result-object v1

    .line 53
    invoke-virtual {p1, v1}, LU80/a;->j(Lorg/xbet/main_menu/impl/domain/models/MainMenuCategoryModel;)V

    .line 54
    .line 55
    .line 56
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 57
    .line 58
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->C3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lej0/d;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 63
    .line 64
    invoke-static {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->I3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lek0/o;

    .line 65
    .line 66
    .line 67
    move-result-object v1

    .line 68
    invoke-virtual {v1}, Lek0/o;->d2()Lek0/l;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    invoke-virtual {v1}, Lek0/l;->o()Z

    .line 73
    .line 74
    .line 75
    move-result v1

    .line 76
    iput v2, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;->label:I

    .line 77
    .line 78
    invoke-interface {p1, v1, p0}, Lej0/d;->a(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    if-ne p1, v0, :cond_2

    .line 83
    .line 84
    return-object v0

    .line 85
    :cond_2
    :goto_0
    check-cast p1, Ljava/util/List;

    .line 86
    .line 87
    new-instance v0, Lorg/xbet/auth/api/presentation/b;

    .line 88
    .line 89
    invoke-direct {v0}, Lorg/xbet/auth/api/presentation/b;-><init>()V

    .line 90
    .line 91
    .line 92
    invoke-virtual {v0, p1}, Lorg/xbet/auth/api/presentation/b;->a(Ljava/util/List;)Lorg/xbet/auth/api/presentation/AuthScreenParams$Registration;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 97
    .line 98
    invoke-static {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->K3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)LwX0/c;

    .line 99
    .line 100
    .line 101
    move-result-object v0

    .line 102
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 103
    .line 104
    invoke-static {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->v3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Loi/a;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    invoke-interface {v1, p1}, Loi/a;->a(Lorg/xbet/auth/api/presentation/AuthScreenParams;)Lq4/q;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    invoke-virtual {v0, p1}, LwX0/c;->m(Lq4/q;)V

    .line 113
    .line 114
    .line 115
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 116
    .line 117
    return-object p1
.end method
