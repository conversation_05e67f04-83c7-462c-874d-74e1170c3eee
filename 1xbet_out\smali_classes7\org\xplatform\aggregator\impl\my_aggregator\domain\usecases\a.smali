.class public final Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0000\u0018\u0000 \u00182\u00020\u0001:\u0001\u0011B!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ4\u0010\u0011\u001a\u0008\u0012\u0004\u0012\u00020\u00100\n2\u000c\u0010\u000c\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000f\u001a\u00020\u000bH\u0086B\u00a2\u0006\u0004\u0008\u0011\u0010\u0012R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0013R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0015R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0017\u00a8\u0006\u0019"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;",
        "",
        "Lu81/b;",
        "repository",
        "Lorg/xplatform/aggregator/impl/category/domain/usecases/p;",
        "getFilterTypeFromSavedFiltersUseCase",
        "LfX/b;",
        "testRepository",
        "<init>",
        "(Lu81/b;Lorg/xplatform/aggregator/impl/category/domain/usecases/p;LfX/b;)V",
        "",
        "",
        "filtersList",
        "",
        "brandsApi",
        "endPoint",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "a",
        "(Ljava/util/List;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lu81/b;",
        "b",
        "Lorg/xplatform/aggregator/impl/category/domain/usecases/p;",
        "c",
        "LfX/b;",
        "d",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d:Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lu81/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xplatform/aggregator/impl/category/domain/usecases/p;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LfX/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;->d:Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a$a;

    return-void
.end method

.method public constructor <init>(Lu81/b;Lorg/xplatform/aggregator/impl/category/domain/usecases/p;LfX/b;)V
    .locals 0
    .param p1    # Lu81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/category/domain/usecases/p;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LfX/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;->a:Lu81/b;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;->b:Lorg/xplatform/aggregator/impl/category/domain/usecases/p;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;->c:LfX/b;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 11
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;Z",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;->a:Lu81/b;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 4
    .line 5
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 6
    .line 7
    .line 8
    move-result-wide v1

    .line 9
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;->c:LfX/b;

    .line 10
    .line 11
    invoke-interface {v3}, LfX/b;->B0()Z

    .line 12
    .line 13
    .line 14
    move-result v5

    .line 15
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;->b:Lorg/xplatform/aggregator/impl/category/domain/usecases/p;

    .line 16
    .line 17
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 18
    .line 19
    .line 20
    move-result v4

    .line 21
    xor-int/lit8 v4, v4, 0x1

    .line 22
    .line 23
    invoke-virtual {v3, v4}, Lorg/xplatform/aggregator/impl/category/domain/usecases/p;->a(Z)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v6

    .line 27
    const/4 v9, 0x0

    .line 28
    const/16 v4, 0x8

    .line 29
    .line 30
    move-object v3, p1

    .line 31
    move v7, p2

    .line 32
    move-object v8, p3

    .line 33
    move-object v10, p4

    .line 34
    invoke-interface/range {v0 .. v10}, Lu81/b;->d(JLjava/util/List;IZLjava/lang/String;ZLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    return-object p1
.end method
