.class final Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.presentation.delegates.GiftsDelegate$getBonusesUiModels$2"
    f = "GiftsDelegate.kt"
    l = {
        0x4e,
        0x54,
        0x55
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->g(JLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Result<",
        "+",
        "Ljava/util/List<",
        "+",
        "LVX0/i;",
        ">;>;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00030\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "Lkotlin/Result;",
        "",
        "LVX0/i;",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)Lkotlin/Result;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $currentAccountId:J

.field J$0:J

.field private synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;


# direct methods
.method public constructor <init>(JLorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;",
            ">;)V"
        }
    .end annotation

    iput-wide p1, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->$currentAccountId:J

    iput-object p3, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method

.method public static synthetic a(Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;JLjava/util/List;Ljava/util/List;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->c(Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;JLjava/util/List;Ljava/util/List;)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;JLjava/util/List;Ljava/util/List;)Ljava/util/List;
    .locals 9

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    const/16 v1, 0xa

    .line 4
    .line 5
    invoke-static {p4, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    invoke-direct {v0, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 10
    .line 11
    .line 12
    invoke-interface {p4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object p4

    .line 16
    :goto_0
    invoke-interface {p4}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v2

    .line 20
    if-eqz v2, :cond_0

    .line 21
    .line 22
    invoke-interface {p4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v2

    .line 26
    move-object v3, v2

    .line 27
    check-cast v3, Lxa1/a;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->d(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;)LHX0/e;

    .line 30
    .line 31
    .line 32
    move-result-object v5

    .line 33
    invoke-virtual {v3}, Lxa1/a;->f()Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object v6

    .line 37
    move-object v4, p0

    .line 38
    move-wide v7, p2

    .line 39
    invoke-static/range {v3 .. v8}, Lla1/a;->q(Lxa1/a;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;LHX0/e;Ljava/lang/String;J)Lma1/d;

    .line 40
    .line 41
    .line 42
    move-result-object p0

    .line 43
    invoke-interface {v0, p0}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 44
    .line 45
    .line 46
    move-object p0, v4

    .line 47
    goto :goto_0

    .line 48
    :cond_0
    move-object v4, p0

    .line 49
    new-instance p0, Ljava/util/ArrayList;

    .line 50
    .line 51
    invoke-static {p5, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 52
    .line 53
    .line 54
    move-result p2

    .line 55
    invoke-direct {p0, p2}, Ljava/util/ArrayList;-><init>(I)V

    .line 56
    .line 57
    .line 58
    invoke-interface {p5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 59
    .line 60
    .line 61
    move-result-object p2

    .line 62
    :goto_1
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 63
    .line 64
    .line 65
    move-result p3

    .line 66
    if-eqz p3, :cond_1

    .line 67
    .line 68
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object p3

    .line 72
    check-cast p3, Lxa1/c;

    .line 73
    .line 74
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->d(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;)LHX0/e;

    .line 75
    .line 76
    .line 77
    move-result-object p4

    .line 78
    invoke-static {p3, v4, p4}, Lla1/b;->b(Lxa1/c;Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;LHX0/e;)Lma1/e;

    .line 79
    .line 80
    .line 81
    move-result-object p3

    .line 82
    invoke-interface {p0, p3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 83
    .line 84
    .line 85
    goto :goto_1

    .line 86
    :cond_1
    invoke-static {v0, p0}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 87
    .line 88
    .line 89
    move-result-object p0

    .line 90
    return-object p0
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;

    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->$currentAccountId:J

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;

    invoke-direct {v0, v1, v2, v3, p2}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;-><init>(JLorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x3

    .line 10
    const/4 v4, 0x2

    .line 11
    const/4 v5, 0x1

    .line 12
    if-eqz v2, :cond_3

    .line 13
    .line 14
    if-eq v2, v5, :cond_2

    .line 15
    .line 16
    if-eq v2, v4, :cond_1

    .line 17
    .line 18
    if-ne v2, v3, :cond_0

    .line 19
    .line 20
    iget-wide v1, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->J$0:J

    .line 21
    .line 22
    iget-object v3, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->L$1:Ljava/lang/Object;

    .line 23
    .line 24
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->L$0:Ljava/lang/Object;

    .line 25
    .line 26
    check-cast v4, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 27
    .line 28
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    move-wide v13, v1

    .line 32
    move-object/from16 v2, p1

    .line 33
    .line 34
    goto/16 :goto_3

    .line 35
    .line 36
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 37
    .line 38
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 39
    .line 40
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 41
    .line 42
    .line 43
    throw v1

    .line 44
    :cond_1
    iget-wide v4, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->J$0:J

    .line 45
    .line 46
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->L$1:Ljava/lang/Object;

    .line 47
    .line 48
    check-cast v2, Lkotlinx/coroutines/T;

    .line 49
    .line 50
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->L$0:Ljava/lang/Object;

    .line 51
    .line 52
    check-cast v6, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 53
    .line 54
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    move-wide v13, v4

    .line 58
    move-object/from16 v4, p1

    .line 59
    .line 60
    goto/16 :goto_1

    .line 61
    .line 62
    :cond_2
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->L$1:Ljava/lang/Object;

    .line 63
    .line 64
    check-cast v2, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 65
    .line 66
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->L$0:Ljava/lang/Object;

    .line 67
    .line 68
    check-cast v5, Lkotlinx/coroutines/N;

    .line 69
    .line 70
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 71
    .line 72
    .line 73
    move-object v6, v5

    .line 74
    move-object/from16 v5, p1

    .line 75
    .line 76
    goto :goto_0

    .line 77
    :cond_3
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 78
    .line 79
    .line 80
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->L$0:Ljava/lang/Object;

    .line 81
    .line 82
    check-cast v2, Lkotlinx/coroutines/N;

    .line 83
    .line 84
    iget-wide v6, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->$currentAccountId:J

    .line 85
    .line 86
    const-wide/16 v8, 0x0

    .line 87
    .line 88
    cmp-long v10, v6, v8

    .line 89
    .line 90
    if-eqz v10, :cond_7

    .line 91
    .line 92
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;

    .line 93
    .line 94
    invoke-static {v6}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->e(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;)Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 95
    .line 96
    .line 97
    move-result-object v6

    .line 98
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;

    .line 99
    .line 100
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->c(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;)Leu/l;

    .line 101
    .line 102
    .line 103
    move-result-object v7

    .line 104
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->L$0:Ljava/lang/Object;

    .line 105
    .line 106
    iput-object v6, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->L$1:Ljava/lang/Object;

    .line 107
    .line 108
    iput v5, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->label:I

    .line 109
    .line 110
    invoke-interface {v7, v0}, Leu/l;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 111
    .line 112
    .line 113
    move-result-object v5

    .line 114
    if-ne v5, v1, :cond_4

    .line 115
    .line 116
    goto :goto_2

    .line 117
    :cond_4
    move-object/from16 v16, v6

    .line 118
    .line 119
    move-object v6, v2

    .line 120
    move-object/from16 v2, v16

    .line 121
    .line 122
    :goto_0
    check-cast v5, Lcu/a;

    .line 123
    .line 124
    invoke-virtual {v5}, Lcu/a;->d()I

    .line 125
    .line 126
    .line 127
    move-result v5

    .line 128
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 129
    .line 130
    .line 131
    move-result-wide v13

    .line 132
    new-instance v9, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2$bonusesDeferred$1;

    .line 133
    .line 134
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;

    .line 135
    .line 136
    iget-wide v10, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->$currentAccountId:J

    .line 137
    .line 138
    const/4 v8, 0x0

    .line 139
    invoke-direct {v9, v7, v10, v11, v8}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2$bonusesDeferred$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;JLkotlin/coroutines/e;)V

    .line 140
    .line 141
    .line 142
    const/4 v10, 0x3

    .line 143
    const/4 v11, 0x0

    .line 144
    const/4 v7, 0x0

    .line 145
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->b(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/T;

    .line 146
    .line 147
    .line 148
    move-result-object v15

    .line 149
    new-instance v7, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2$freeSpinsDeferred$1;

    .line 150
    .line 151
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;

    .line 152
    .line 153
    iget-wide v9, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->$currentAccountId:J

    .line 154
    .line 155
    const/4 v12, 0x0

    .line 156
    move v11, v5

    .line 157
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2$freeSpinsDeferred$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;JILkotlin/coroutines/e;)V

    .line 158
    .line 159
    .line 160
    const/4 v10, 0x3

    .line 161
    const/4 v11, 0x0

    .line 162
    move-object v9, v7

    .line 163
    const/4 v7, 0x0

    .line 164
    const/4 v8, 0x0

    .line 165
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->b(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/T;

    .line 166
    .line 167
    .line 168
    move-result-object v5

    .line 169
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->L$0:Ljava/lang/Object;

    .line 170
    .line 171
    iput-object v5, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->L$1:Ljava/lang/Object;

    .line 172
    .line 173
    iput-wide v13, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->J$0:J

    .line 174
    .line 175
    iput v4, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->label:I

    .line 176
    .line 177
    invoke-interface {v15, v0}, Lkotlinx/coroutines/T;->b(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 178
    .line 179
    .line 180
    move-result-object v4

    .line 181
    if-ne v4, v1, :cond_5

    .line 182
    .line 183
    goto :goto_2

    .line 184
    :cond_5
    move-object v6, v2

    .line 185
    move-object v2, v5

    .line 186
    :goto_1
    check-cast v4, Lkotlin/Result;

    .line 187
    .line 188
    invoke-virtual {v4}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 189
    .line 190
    .line 191
    move-result-object v4

    .line 192
    iput-object v6, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->L$0:Ljava/lang/Object;

    .line 193
    .line 194
    iput-object v4, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->L$1:Ljava/lang/Object;

    .line 195
    .line 196
    iput-wide v13, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->J$0:J

    .line 197
    .line 198
    iput v3, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->label:I

    .line 199
    .line 200
    invoke-interface {v2, v0}, Lkotlinx/coroutines/T;->b(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 201
    .line 202
    .line 203
    move-result-object v2

    .line 204
    if-ne v2, v1, :cond_6

    .line 205
    .line 206
    :goto_2
    return-object v1

    .line 207
    :cond_6
    move-object v3, v4

    .line 208
    move-object v4, v6

    .line 209
    :goto_3
    check-cast v2, Lkotlin/Result;

    .line 210
    .line 211
    invoke-virtual {v2}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 212
    .line 213
    .line 214
    move-result-object v1

    .line 215
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate$getBonusesUiModels$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;

    .line 216
    .line 217
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/a;

    .line 218
    .line 219
    invoke-direct {v5, v4, v2, v13, v14}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/a;-><init>(Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;J)V

    .line 220
    .line 221
    .line 222
    invoke-static {v2, v3, v1, v5}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;->a(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;Ljava/lang/Object;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;

    .line 223
    .line 224
    .line 225
    move-result-object v1

    .line 226
    invoke-static {v1}, Lkotlin/Result;->box-impl(Ljava/lang/Object;)Lkotlin/Result;

    .line 227
    .line 228
    .line 229
    move-result-object v1

    .line 230
    return-object v1

    .line 231
    :cond_7
    new-instance v1, Lcom/xbet/onexcore/BadDataRequestException;

    .line 232
    .line 233
    invoke-direct {v1}, Lcom/xbet/onexcore/BadDataRequestException;-><init>()V

    .line 234
    .line 235
    .line 236
    throw v1
.end method
