.class public final Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryWithGameItemAdapterDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "Lw40/a;",
        "clickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "(Lw40/a;)LA4/c;",
        "impl_games_section_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryWithGameItemAdapterDelegateKt;->i(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LPv/o;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryWithGameItemAdapterDelegateKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LPv/o;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lw40/a;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryWithGameItemAdapterDelegateKt;->g(Lw40/a;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lw40/a;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryWithGameItemAdapterDelegateKt;->h(Lw40/a;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lw40/a;)LA4/c;
    .locals 4
    .param p0    # Lw40/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lw40/a;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LD40/g;

    .line 2
    .line 3
    invoke-direct {v0}, LD40/g;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LD40/h;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LD40/h;-><init>(Lw40/a;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryWithGameItemAdapterDelegateKt$getOneXGameCategoryWithGameItemAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryWithGameItemAdapterDelegateKt$getOneXGameCategoryWithGameItemAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryWithGameItemAdapterDelegateKt$getOneXGameCategoryWithGameItemAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryWithGameItemAdapterDelegateKt$getOneXGameCategoryWithGameItemAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LPv/o;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LPv/o;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LPv/o;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(Lw40/a;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    iget-object v0, p1, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 2
    .line 3
    new-instance v1, LD40/i;

    .line 4
    .line 5
    invoke-direct {v1, p0, p1}, LD40/i;-><init>(Lw40/a;LB4/a;)V

    .line 6
    .line 7
    .line 8
    const/4 p0, 0x1

    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-static {v0, v2, v1, p0, v2}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 11
    .line 12
    .line 13
    new-instance p0, LD40/j;

    .line 14
    .line 15
    invoke-direct {p0, p1}, LD40/j;-><init>(LB4/a;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 19
    .line 20
    .line 21
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 22
    .line 23
    return-object p0
.end method

.method public static final h(Lw40/a;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, Lz40/a$b;

    .line 6
    .line 7
    invoke-virtual {p2}, Lz40/a$b;->f()Lu40/b;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    check-cast p1, Lz40/a$b;

    .line 16
    .line 17
    invoke-virtual {p1}, Lz40/a$b;->d()I

    .line 18
    .line 19
    .line 20
    move-result p1

    .line 21
    invoke-interface {p0, p2, p1}, Lw40/a;->d3(Lu40/b;I)V

    .line 22
    .line 23
    .line 24
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 25
    .line 26
    return-object p0
.end method

.method public static final i(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 13

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LPv/o;

    .line 6
    .line 7
    iget-object p1, p1, LPv/o;->f:Landroid/widget/TextView;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, Lz40/a$b;

    .line 14
    .line 15
    invoke-virtual {v0}, Lz40/a$b;->f()Lu40/b;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {v0}, Lu40/b;->b()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    check-cast p1, LPv/o;

    .line 31
    .line 32
    iget-object p1, p1, LPv/o;->e:Landroid/widget/TextView;

    .line 33
    .line 34
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, Lz40/a$b;

    .line 39
    .line 40
    invoke-virtual {v0}, Lz40/a$b;->e()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 45
    .line 46
    .line 47
    sget-object v1, LCX0/l;->a:LCX0/l;

    .line 48
    .line 49
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    check-cast p1, LPv/o;

    .line 54
    .line 55
    iget-object v2, p1, LPv/o;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 56
    .line 57
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object p0

    .line 61
    check-cast p0, Lz40/a$b;

    .line 62
    .line 63
    invoke-virtual {p0}, Lz40/a$b;->f()Lu40/b;

    .line 64
    .line 65
    .line 66
    move-result-object p0

    .line 67
    invoke-virtual {p0}, Lu40/b;->a()Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v3

    .line 71
    sget v4, Lpb/g;->ic_games_placeholder:I

    .line 72
    .line 73
    const/4 p0, 0x0

    .line 74
    new-array v7, p0, [LYW0/d;

    .line 75
    .line 76
    const/16 v11, 0xec

    .line 77
    .line 78
    const/4 v12, 0x0

    .line 79
    const/4 v5, 0x0

    .line 80
    const/4 v6, 0x0

    .line 81
    const/4 v8, 0x0

    .line 82
    const/4 v9, 0x0

    .line 83
    const/4 v10, 0x0

    .line 84
    invoke-static/range {v1 .. v12}, LCX0/l;->w(LCX0/l;Landroid/widget/ImageView;Ljava/lang/String;IIZ[LYW0/d;LYW0/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 85
    .line 86
    .line 87
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 88
    .line 89
    return-object p0
.end method
