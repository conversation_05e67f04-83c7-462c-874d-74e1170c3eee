.class public final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\t\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0005\u0010\u0006R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0007\u0010\u0008\u001a\u0004\u0008\t\u0010\nR\u0017\u0010\u0004\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\t\u0010\u0008\u001a\u0004\u0008\u0007\u0010\n\u00a8\u0006\u000b"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a$b;",
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a;",
        "",
        "showAuthButtons",
        "showAccountSelection",
        "<init>",
        "(ZZ)V",
        "a",
        "Z",
        "b",
        "()Z",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Z

.field public final b:Z


# direct methods
.method public constructor <init>(ZZ)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-boolean p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a$b;->a:Z

    .line 5
    .line 6
    iput-boolean p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a$b;->b:Z

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a$b;->b:Z

    .line 2
    .line 3
    return v0
.end method

.method public final b()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a$b;->a:Z

    .line 2
    .line 3
    return v0
.end method
