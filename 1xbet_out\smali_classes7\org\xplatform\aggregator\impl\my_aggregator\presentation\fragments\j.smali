.class public final synthetic Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/j;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/j;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    check-cast p1, Lra1/c;

    check-cast p2, Ljava/lang/Long;

    invoke-virtual {p2}, Ljava/lang/Long;->longValue()J

    move-result-wide v1

    invoke-static {v0, p1, v1, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->t3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lra1/c;J)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
