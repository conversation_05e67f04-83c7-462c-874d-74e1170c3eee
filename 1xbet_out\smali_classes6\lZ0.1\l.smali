.class public final LlZ0/l;
.super Ljava/lang/Object;


# static fields
.field public static accent_button_cashback_amount_view:I = 0x7f0d001e

.field public static accordion_clear_view:I = 0x7f0d0020

.field public static accordion_secondary_view:I = 0x7f0d0021

.field public static accordion_view:I = 0x7f0d0022

.field public static account_control_view:I = 0x7f0d0023

.field public static account_info_large_view:I = 0x7f0d0024

.field public static account_info_small_view:I = 0x7f0d0025

.field public static account_selection_layout:I = 0x7f0d0026

.field public static authorization_buttons_view:I = 0x7f0d0082

.field public static balance_view_group:I = 0x7f0d0088

.field public static banner_bonuses_layout:I = 0x7f0d008a

.field public static bottom_bar_one_button_layout:I = 0x7f0d00aa

.field public static bottom_bar_three_vertical_buttons_view:I = 0x7f0d00ab

.field public static bottom_bar_two_horizontal_buttons_view:I = 0x7f0d00ac

.field public static bottom_bar_two_vertical_buttons_view:I = 0x7f0d00ad

.field public static button_promo_code_view:I = 0x7f0d00bf

.field public static category_card_collection:I = 0x7f0d00c5

.field public static category_card_collection_shimmer:I = 0x7f0d00c6

.field public static cell_middle_title_layout:I = 0x7f0d00cb

.field public static cell_promo_code_view:I = 0x7f0d00cc

.field public static cell_right_counter_view:I = 0x7f0d00cd

.field public static cell_right_drag_and_drop_and_button_view:I = 0x7f0d00ce

.field public static cell_right_drag_and_drop_view:I = 0x7f0d00cf

.field public static cell_right_icon_with_button_view:I = 0x7f0d00d0

.field public static cell_right_label_layout:I = 0x7f0d00d1

.field public static cell_right_medium_label_layout:I = 0x7f0d00d2

.field public static cell_right_status_layout:I = 0x7f0d00d3

.field public static chip_item:I = 0x7f0d00db

.field public static chip_view:I = 0x7f0d00de

.field public static compose_fragment:I = 0x7f0d00e8

.field public static compose_item:I = 0x7f0d00e9

.field public static custom_action_dialog_view:I = 0x7f0d0109

.field public static custom_lottie_action_dialog_view:I = 0x7f0d010b

.field public static design_system_bottom_sheet_parent_view:I = 0x7f0d0242

.field public static document_status_view:I = 0x7f0d0288

.field public static ds_bottom_sheet_preset_banner_controller:I = 0x7f0d029f

.field public static ds_bottom_sheet_preset_body_text_center:I = 0x7f0d02a0

.field public static ds_bottom_sheet_preset_body_text_left:I = 0x7f0d02a1

.field public static ds_bottom_sheet_preset_body_text_right:I = 0x7f0d02a2

.field public static ds_bottom_sheet_preset_button:I = 0x7f0d02a3

.field public static ds_bottom_sheet_preset_country_code:I = 0x7f0d02a4

.field public static ds_bottom_sheet_preset_header_large:I = 0x7f0d02a5

.field public static ds_bottom_sheet_preset_header_small:I = 0x7f0d02a6

.field public static ds_bottom_sheet_preset_loader:I = 0x7f0d02a7

.field public static ds_bottom_sheet_preset_search_field:I = 0x7f0d02a8

.field public static ds_bottom_sheet_preset_segment_control:I = 0x7f0d02a9

.field public static ds_bottom_sheet_preset_separator_long:I = 0x7f0d02aa

.field public static ds_bottom_sheet_preset_separator_short:I = 0x7f0d02ab

.field public static ds_bottom_sheet_preset_text_field_basic:I = 0x7f0d02ae

.field public static ds_bottom_sheet_preset_text_field_basic_multiline:I = 0x7f0d02af

.field public static ds_bottom_sheet_preset_text_field_filled:I = 0x7f0d02b0

.field public static ds_bottom_sheet_preset_text_field_filled_multiline:I = 0x7f0d02b1

.field public static ds_bottom_sheet_preset_three_button_horizontal:I = 0x7f0d02b2

.field public static ds_bottom_sheet_preset_three_button_vertical:I = 0x7f0d02b3

.field public static ds_bottom_sheet_preset_two_button_horizontal:I = 0x7f0d02b5

.field public static ds_bottom_sheet_preset_two_button_vertical:I = 0x7f0d02b6

.field public static ds_chip_item:I = 0x7f0d02b9

.field public static ds_chip_view:I = 0x7f0d02ba

.field public static ds_lottie_empty_container:I = 0x7f0d02bb

.field public static game_horizontal_item_view:I = 0x7f0d050f

.field public static header_large_shimmer_layout:I = 0x7f0d0525

.field public static header_large_view:I = 0x7f0d0526

.field public static header_shimmer_layout:I = 0x7f0d0529

.field public static header_small_view:I = 0x7f0d052a

.field public static header_view:I = 0x7f0d052c

.field public static item_aggregator_social_network_cells:I = 0x7f0d0565

.field public static item_aggregator_social_network_circle:I = 0x7f0d0566

.field public static item_aggregator_social_network_rectangle_horizontal:I = 0x7f0d0567

.field public static item_aggregator_social_network_rectangle_vertical:I = 0x7f0d0568

.field public static item_aggregator_social_network_square:I = 0x7f0d0569

.field public static large_button_promo_code_view:I = 0x7f0d073b

.field public static lottie_view:I = 0x7f0d0771

.field public static lottie_view_container:I = 0x7f0d0772

.field public static menu_compact_layout:I = 0x7f0d07b7

.field public static native_action_dialog_view:I = 0x7f0d07f8

.field public static navigation_bar_basic_layout:I = 0x7f0d07f9

.field public static navigation_bar_item_view:I = 0x7f0d07fa

.field public static navigation_bar_view:I = 0x7f0d07fe

.field public static page_indicator_delegate:I = 0x7f0d081d

.field public static password_requirement_view:I = 0x7f0d0828

.field public static profile_toolbar_layout:I = 0x7f0d085b

.field public static promo_banner_view:I = 0x7f0d0860

.field public static promo_store_collection_horizontal_item:I = 0x7f0d0867

.field public static promo_store_collection_vertical_item:I = 0x7f0d0868

.field public static rolling_calendar_all_times_view:I = 0x7f0d087c

.field public static rolling_calendar_item_view:I = 0x7f0d087d

.field public static rolling_calendar_view:I = 0x7f0d087e

.field public static search_field_layout:I = 0x7f0d0899

.field public static search_field_overlay_layout:I = 0x7f0d089a

.field public static settings_shimmer_layout:I = 0x7f0d08b2

.field public static small_button_promo_code_view:I = 0x7f0d08fd

.field public static snackbar_layout:I = 0x7f0d08fe

.field public static snackbar_view:I = 0x7f0d08ff

.field public static social_networks_cells_shimmer:I = 0x7f0d0957

.field public static social_networks_circle_shimmer:I = 0x7f0d0958

.field public static social_networks_rectangle_horizontal_shimmer:I = 0x7f0d0959

.field public static social_networks_square_shimmer:I = 0x7f0d095a

.field public static success_bet_alert_action_dialog:I = 0x7f0d0989

.field public static success_bet_alert_action_dialog_with_island:I = 0x7f0d098a

.field public static success_bet_alert_bottom_sheet:I = 0x7f0d098b

.field public static success_bet_info_primary:I = 0x7f0d098c

.field public static success_bet_info_secondary:I = 0x7f0d098d

.field public static tab_bar_item_view:I = 0x7f0d09f1

.field public static tab_bar_view:I = 0x7f0d09f2

.field public static tab_view:I = 0x7f0d09f4

.field public static title_toolbar_layout:I = 0x7f0d0a15

.field public static toolbar_layout:I = 0x7f0d0a18

.field public static toolbar_search_logo_view:I = 0x7f0d0a19

.field public static upload_document_card_view:I = 0x7f0d0a74


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
