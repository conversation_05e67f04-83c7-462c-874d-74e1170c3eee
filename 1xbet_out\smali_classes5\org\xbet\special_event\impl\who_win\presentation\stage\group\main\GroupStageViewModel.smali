.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;
.super Landroidx/lifecycle/b0;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000F\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0001\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u000f\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0013\u0010\r\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000b\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0015\u0010\u0011\u001a\u00020\u00082\u0006\u0010\u0010\u001a\u00020\u000f\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0015\u0010\u0015\u001a\u00020\u00082\u0006\u0010\u0014\u001a\u00020\u0013\u00a2\u0006\u0004\u0008\u0015\u0010\u0016R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010\u0018R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u001aR\u001a\u0010\u001f\u001a\u0008\u0012\u0004\u0012\u00020\u001c0\u001b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\u001e\u00a8\u0006 "
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;",
        "Landroidx/lifecycle/b0;",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lorg/xbet/special_event/impl/who_win/domain/usecase/a;",
        "getStageTableResultStreamUseCase",
        "<init>",
        "(Lm8/a;Lorg/xbet/special_event/impl/who_win/domain/usecase/a;)V",
        "",
        "q3",
        "()V",
        "Lkotlinx/coroutines/flow/e;",
        "LRy0/b;",
        "E0",
        "()Lkotlinx/coroutines/flow/e;",
        "LOy0/a;",
        "item",
        "s3",
        "(LOy0/a;)V",
        "",
        "position",
        "t3",
        "(I)V",
        "b1",
        "Lm8/a;",
        "k1",
        "Lorg/xbet/special_event/impl/who_win/domain/usecase/a;",
        "Lkotlinx/coroutines/flow/V;",
        "LRy0/a;",
        "v1",
        "Lkotlinx/coroutines/flow/V;",
        "stateModel",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final b1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k1:Lorg/xbet/special_event/impl/who_win/domain/usecase/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LRy0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lm8/a;Lorg/xbet/special_event/impl/who_win/domain/usecase/a;)V
    .locals 1
    .param p1    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/special_event/impl/who_win/domain/usecase/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Landroidx/lifecycle/b0;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->b1:Lm8/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->k1:Lorg/xbet/special_event/impl/who_win/domain/usecase/a;

    .line 7
    .line 8
    new-instance p1, LRy0/a;

    .line 9
    .line 10
    const/4 p2, 0x0

    .line 11
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-direct {p1, p2, v0}, LRy0/a;-><init>(Ljava/lang/String;Ljava/util/List;)V

    .line 16
    .line 17
    .line 18
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->v1:Lkotlinx/coroutines/flow/V;

    .line 23
    .line 24
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->q3()V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public static final synthetic o3(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->v1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic p3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->r3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final q3()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->k1:Lorg/xbet/special_event/impl/who_win/domain/usecase/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/who_win/domain/usecase/a;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel$observeStageTable$$inlined$map$1;

    .line 8
    .line 9
    invoke-direct {v1, v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel$observeStageTable$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;)V

    .line 10
    .line 11
    .line 12
    invoke-static {v1}, Lkotlinx/coroutines/flow/g;->B(Lkotlinx/coroutines/flow/e;)Lkotlinx/coroutines/flow/e;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel$observeStageTable$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel$observeStageTable$2;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel$observeStageTable$3;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel$observeStageTable$3;

    .line 27
    .line 28
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    iget-object v3, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->b1:Lm8/a;

    .line 33
    .line 34
    invoke-interface {v3}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 35
    .line 36
    .line 37
    move-result-object v3

    .line 38
    invoke-static {v2, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    invoke-static {v0, v2, v1}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method private static final synthetic r3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method


# virtual methods
.method public final E0()Lkotlinx/coroutines/flow/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LRy0/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->v1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel$getUiState$$inlined$map$1;

    .line 4
    .line 5
    invoke-direct {v1, v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel$getUiState$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;)V

    .line 6
    .line 7
    .line 8
    return-object v1
.end method

.method public final s3(LOy0/a;)V
    .locals 6
    .param p1    # LOy0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->v1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LRy0/a;

    .line 8
    .line 9
    invoke-virtual {v0}, LRy0/a;->d()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {p1}, LOy0/a;->getTitle()Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-nez v0, :cond_1

    .line 22
    .line 23
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->v1:Lkotlinx/coroutines/flow/V;

    .line 24
    .line 25
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    move-object v2, v1

    .line 30
    check-cast v2, LRy0/a;

    .line 31
    .line 32
    invoke-virtual {p1}, LOy0/a;->getTitle()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object v3

    .line 36
    const/4 v4, 0x2

    .line 37
    const/4 v5, 0x0

    .line 38
    invoke-static {v2, v3, v5, v4, v5}, LRy0/a;->b(LRy0/a;Ljava/lang/String;Ljava/util/List;ILjava/lang/Object;)LRy0/a;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    if-eqz v1, :cond_0

    .line 47
    .line 48
    :cond_1
    return-void
.end method

.method public final t3(I)V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->v1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LRy0/a;

    .line 8
    .line 9
    invoke-virtual {v0}, LRy0/a;->c()Ljava/util/List;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-static {v0, p1}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    check-cast p1, Ljava/lang/String;

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->v1:Lkotlinx/coroutines/flow/V;

    .line 20
    .line 21
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    check-cast v0, LRy0/a;

    .line 26
    .line 27
    invoke-virtual {v0}, LRy0/a;->d()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-static {v0, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    if-nez v0, :cond_1

    .line 36
    .line 37
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->v1:Lkotlinx/coroutines/flow/V;

    .line 38
    .line 39
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    move-object v2, v1

    .line 44
    check-cast v2, LRy0/a;

    .line 45
    .line 46
    const/4 v3, 0x2

    .line 47
    const/4 v4, 0x0

    .line 48
    invoke-static {v2, p1, v4, v3, v4}, LRy0/a;->b(LRy0/a;Ljava/lang/String;Ljava/util/List;ILjava/lang/Object;)LRy0/a;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 53
    .line 54
    .line 55
    move-result v1

    .line 56
    if-eqz v1, :cond_0

    .line 57
    .line 58
    :cond_1
    return-void
.end method
