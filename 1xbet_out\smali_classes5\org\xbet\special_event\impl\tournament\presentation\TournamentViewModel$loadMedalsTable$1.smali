.class final Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.tournament.presentation.TournamentViewModel$loadMedalsTable$1"
    f = "TournamentViewModel.kt"
    l = {
        0x2f6
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->J4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;

    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->J3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 34
    .line 35
    invoke-static {v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->a4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LZx0/h;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    invoke-virtual {v1}, LZx0/h;->d()I

    .line 40
    .line 41
    .line 42
    move-result v1

    .line 43
    iput v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;->label:I

    .line 44
    .line 45
    invoke-virtual {p1, v1, p0}, Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;->a(ILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    if-ne p1, v0, :cond_2

    .line 50
    .line 51
    return-object v0

    .line 52
    :cond_2
    :goto_0
    check-cast p1, Ljava/util/List;

    .line 53
    .line 54
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 55
    .line 56
    .line 57
    move-result v0

    .line 58
    if-eqz v0, :cond_3

    .line 59
    .line 60
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 61
    .line 62
    new-instance v0, LZx0/a$i;

    .line 63
    .line 64
    sget-object v1, LZx0/e;->a:LZx0/e;

    .line 65
    .line 66
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    invoke-direct {v0, v1, v2}, LZx0/a$i;-><init>(LZx0/g;Ljava/util/List;)V

    .line 71
    .line 72
    .line 73
    invoke-static {p1, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->n4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;LZx0/a;)V

    .line 74
    .line 75
    .line 76
    goto :goto_1

    .line 77
    :cond_3
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadMedalsTable$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 78
    .line 79
    new-instance v1, LZx0/a$i;

    .line 80
    .line 81
    sget-object v2, LZx0/g$a$b;->a:LZx0/g$a$b;

    .line 82
    .line 83
    invoke-direct {v1, v2, p1}, LZx0/a$i;-><init>(LZx0/g;Ljava/util/List;)V

    .line 84
    .line 85
    .line 86
    invoke-static {v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->n4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;LZx0/a;)V

    .line 87
    .line 88
    .line 89
    :goto_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 90
    .line 91
    return-object p1
.end method
