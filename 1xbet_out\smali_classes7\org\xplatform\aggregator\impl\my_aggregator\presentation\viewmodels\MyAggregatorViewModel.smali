.class public final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;
.super Lorg/xplatform/aggregator/impl/core/presentation/g;
.source "SourceFile"

# interfaces
.implements LG81/e;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a;,
        Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b;,
        Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$c;,
        Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$d;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00bc\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0010\t\n\u0002\u0008\u0007\n\u0002\u0010\u0008\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u001a\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008A\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0012\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010!\n\u0002\u0008\u0002\n\u0002\u0010%\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0011\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0012\n\u0002\u0018\u0002\n\u0002\u0008\n\u0008\u0000\u0018\u0000 \u00fc\u00022\u00020\u00012\u00020\u0002:\u0008\u00fd\u0002\u00fe\u0002\u00ff\u0002\u0080\u0003B\u0093\u0003\u0008\u0007\u0012\u0008\u0008\u0001\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0008\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000c\u001a\u00020\u000b\u0012\u0006\u0010\u000e\u001a\u00020\r\u0012\u0006\u0010\u0010\u001a\u00020\u000f\u0012\u0006\u0010\u0012\u001a\u00020\u0011\u0012\u0006\u0010\u0014\u001a\u00020\u0013\u0012\u0006\u0010\u0016\u001a\u00020\u0015\u0012\u0006\u0010\u0018\u001a\u00020\u0017\u0012\u0006\u0010\u001a\u001a\u00020\u0019\u0012\u0006\u0010\u001c\u001a\u00020\u001b\u0012\u0006\u0010\u001e\u001a\u00020\u001d\u0012\u0006\u0010 \u001a\u00020\u001f\u0012\u0006\u0010\"\u001a\u00020!\u0012\u0006\u0010$\u001a\u00020#\u0012\u0006\u0010&\u001a\u00020%\u0012\u0006\u0010(\u001a\u00020\'\u0012\u0006\u0010*\u001a\u00020)\u0012\u0006\u0010,\u001a\u00020+\u0012\u0006\u0010.\u001a\u00020-\u0012\u0006\u00100\u001a\u00020/\u0012\u0006\u00102\u001a\u000201\u0012\u0006\u00104\u001a\u000203\u0012\u0006\u00106\u001a\u000205\u0012\u0006\u00108\u001a\u000207\u0012\u0006\u0010:\u001a\u000209\u0012\u0006\u0010<\u001a\u00020;\u0012\u0006\u0010>\u001a\u00020=\u0012\u0006\u0010@\u001a\u00020?\u0012\u0006\u0010B\u001a\u00020A\u0012\u0006\u0010D\u001a\u00020C\u0012\u0006\u0010F\u001a\u00020E\u0012\u0006\u0010H\u001a\u00020G\u0012\u0006\u0010J\u001a\u00020I\u0012\u0006\u0010L\u001a\u00020K\u0012\u0006\u0010N\u001a\u00020M\u0012\u0006\u0010P\u001a\u00020O\u0012\u0006\u0010R\u001a\u00020Q\u0012\u0006\u0010T\u001a\u00020S\u0012\u0006\u0010V\u001a\u00020U\u0012\u0006\u0010X\u001a\u00020W\u0012\u0006\u0010Z\u001a\u00020Y\u0012\u0006\u0010\\\u001a\u00020[\u0012\u0006\u0010^\u001a\u00020]\u0012\u0006\u0010`\u001a\u00020_\u0012\u0006\u0010b\u001a\u00020a\u0012\u0006\u0010d\u001a\u00020c\u00a2\u0006\u0004\u0008e\u0010fJ%\u0010l\u001a\u00020k2\u0006\u0010g\u001a\u00020\u00032\u000c\u0010j\u001a\u0008\u0012\u0004\u0012\u00020i0hH\u0002\u00a2\u0006\u0004\u0008l\u0010mJ\u000f\u0010n\u001a\u00020kH\u0002\u00a2\u0006\u0004\u0008n\u0010oJ\u000f\u0010p\u001a\u00020kH\u0002\u00a2\u0006\u0004\u0008p\u0010oJ\u000f\u0010q\u001a\u00020kH\u0002\u00a2\u0006\u0004\u0008q\u0010oJ\u000f\u0010r\u001a\u00020kH\u0002\u00a2\u0006\u0004\u0008r\u0010oJ\u0017\u0010u\u001a\u00020k2\u0006\u0010t\u001a\u00020sH\u0002\u00a2\u0006\u0004\u0008u\u0010vJ\u000f\u0010w\u001a\u00020kH\u0002\u00a2\u0006\u0004\u0008w\u0010oJ\u0017\u0010y\u001a\u00020k2\u0006\u0010x\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008y\u0010zJ\u0017\u0010|\u001a\u00020k2\u0006\u0010{\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008|\u0010zJ\u0018\u0010}\u001a\u00020k2\u0006\u0010{\u001a\u00020\u0003H\u0082@\u00a2\u0006\u0004\u0008}\u0010~J\u0018\u0010\u007f\u001a\u00020k2\u0006\u0010{\u001a\u00020\u0003H\u0082@\u00a2\u0006\u0004\u0008\u007f\u0010~J.\u0010\u0084\u0001\u001a\u00020k2\u0008\u0010\u0081\u0001\u001a\u00030\u0080\u00012\u0007\u0010\u0082\u0001\u001a\u00020\u00032\u0007\u0010\u0083\u0001\u001a\u00020sH\u0002\u00a2\u0006\u0006\u0008\u0084\u0001\u0010\u0085\u0001J\u0011\u0010\u0086\u0001\u001a\u00020kH\u0002\u00a2\u0006\u0005\u0008\u0086\u0001\u0010oJ#\u0010\u0088\u0001\u001a\u00020k2\u000e\u0010\u0087\u0001\u001a\t\u0012\u0005\u0012\u00030\u0080\u00010hH\u0082@\u00a2\u0006\u0006\u0008\u0088\u0001\u0010\u0089\u0001J\u0013\u0010\u008a\u0001\u001a\u00020kH\u0082@\u00a2\u0006\u0006\u0008\u008a\u0001\u0010\u008b\u0001J\u0011\u0010\u008c\u0001\u001a\u00020kH\u0002\u00a2\u0006\u0005\u0008\u008c\u0001\u0010oJ\u0011\u0010\u008d\u0001\u001a\u00020kH\u0002\u00a2\u0006\u0005\u0008\u008d\u0001\u0010oJ\u001a\u0010\u008e\u0001\u001a\u00020k2\u0006\u0010{\u001a\u00020\u0003H\u0082@\u00a2\u0006\u0005\u0008\u008e\u0001\u0010~J3\u0010\u0090\u0001\u001a\u00030\u008f\u00012\u000e\u0010\u0087\u0001\u001a\t\u0012\u0005\u0012\u00030\u0080\u00010h2\u0006\u0010t\u001a\u00020s2\u0006\u0010{\u001a\u00020\u0003H\u0002\u00a2\u0006\u0006\u0008\u0090\u0001\u0010\u0091\u0001J\u001b\u0010\u0093\u0001\u001a\u00030\u0092\u00012\u0006\u0010t\u001a\u00020sH\u0002\u00a2\u0006\u0006\u0008\u0093\u0001\u0010\u0094\u0001J\u001d\u0010\u0096\u0001\u001a\u00020k2\u0008\u0010\u0095\u0001\u001a\u00030\u008f\u0001H\u0082@\u00a2\u0006\u0006\u0008\u0096\u0001\u0010\u0097\u0001J\u001c\u0010\u009a\u0001\u001a\u00020\u00032\u0008\u0010\u0099\u0001\u001a\u00030\u0098\u0001H\u0002\u00a2\u0006\u0006\u0008\u009a\u0001\u0010\u009b\u0001J1\u0010\u009e\u0001\u001a\u00020k2\u0008\u0010\u009c\u0001\u001a\u00030\u0092\u00012\u0008\u0010\u0081\u0001\u001a\u00030\u0080\u00012\t\u0010\u009d\u0001\u001a\u0004\u0018\u00010sH\u0002\u00a2\u0006\u0006\u0008\u009e\u0001\u0010\u009f\u0001J:\u0010\u00a3\u0001\u001a\u00020k2\u0008\u0010\u009c\u0001\u001a\u00030\u0092\u00012\u0008\u0010\u0081\u0001\u001a\u00030\u0080\u00012\u0008\u0010\u00a1\u0001\u001a\u00030\u00a0\u00012\u0008\u0010\u00a2\u0001\u001a\u00030\u0092\u0001H\u0002\u00a2\u0006\u0006\u0008\u00a3\u0001\u0010\u00a4\u0001J\u001e\u0010\u00a5\u0001\u001a\u00030\u00a0\u00012\t\u0010\u009d\u0001\u001a\u0004\u0018\u00010sH\u0002\u00a2\u0006\u0006\u0008\u00a5\u0001\u0010\u00a6\u0001J \u0010\u00a7\u0001\u001a\u0005\u0018\u00010\u0098\u00012\t\u0010\u0083\u0001\u001a\u0004\u0018\u00010sH\u0002\u00a2\u0006\u0006\u0008\u00a7\u0001\u0010\u00a8\u0001J\u0019\u0010\u00aa\u0001\u001a\t\u0012\u0005\u0012\u00030\u00a9\u00010hH\u0002\u00a2\u0006\u0006\u0008\u00aa\u0001\u0010\u00ab\u0001J\u001c\u0010\u00ac\u0001\u001a\u00030\u008f\u00012\u0007\u0010\u0083\u0001\u001a\u00020sH\u0002\u00a2\u0006\u0006\u0008\u00ac\u0001\u0010\u00ad\u0001J\u0017\u0010\u00af\u0001\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u0001\u00a2\u0006\u0006\u0008\u00af\u0001\u0010\u00b0\u0001J\u001e\u0010\u00b1\u0001\u001a\u0010\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a9\u00010h0\u00ae\u0001\u00a2\u0006\u0006\u0008\u00b1\u0001\u0010\u00b0\u0001J\u0018\u0010\u00b4\u0001\u001a\n\u0012\u0005\u0012\u00030\u00b3\u00010\u00b2\u0001\u00a2\u0006\u0006\u0008\u00b4\u0001\u0010\u00b5\u0001J\u0018\u0010\u00b7\u0001\u001a\n\u0012\u0005\u0012\u00030\u00b6\u00010\u00b2\u0001\u00a2\u0006\u0006\u0008\u00b7\u0001\u0010\u00b5\u0001J\u0017\u0010\u00b8\u0001\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u0001\u00a2\u0006\u0006\u0008\u00b8\u0001\u0010\u00b0\u0001J\u000f\u0010\u00b9\u0001\u001a\u00020k\u00a2\u0006\u0005\u0008\u00b9\u0001\u0010oJ.\u0010\u00bc\u0001\u001a\u00020k2\u0008\u0010\u00ba\u0001\u001a\u00030\u0098\u00012\u0008\u0010\u00bb\u0001\u001a\u00030\u0098\u00012\u0008\u0010\u00a2\u0001\u001a\u00030\u0092\u0001\u00a2\u0006\u0006\u0008\u00bc\u0001\u0010\u00bd\u0001J\u001a\u0010\u00be\u0001\u001a\u00020k2\u0008\u0010\u009c\u0001\u001a\u00030\u0092\u0001\u00a2\u0006\u0006\u0008\u00be\u0001\u0010\u00bf\u0001J\u001a\u0010\u00c0\u0001\u001a\u00020k2\u0008\u0010\u009c\u0001\u001a\u00030\u0092\u0001\u00a2\u0006\u0006\u0008\u00c0\u0001\u0010\u00bf\u0001J.\u0010\u00c3\u0001\u001a\u00020k2\u0008\u0010\u009c\u0001\u001a\u00030\u0092\u00012\u0008\u0010\u00c1\u0001\u001a\u00030\u00a0\u00012\u0008\u0010\u00c2\u0001\u001a\u00030\u00a0\u0001\u00a2\u0006\u0006\u0008\u00c3\u0001\u0010\u00c4\u0001J\"\u0010\u00c5\u0001\u001a\u00020k2\u0008\u0010\u009c\u0001\u001a\u00030\u0092\u00012\u0006\u0010t\u001a\u00020s\u00a2\u0006\u0006\u0008\u00c5\u0001\u0010\u00c6\u0001J#\u0010\u00c8\u0001\u001a\u00020k2\u0008\u0010\u009c\u0001\u001a\u00030\u0092\u00012\u0007\u0010\u00c7\u0001\u001a\u00020\u0003\u00a2\u0006\u0006\u0008\u00c8\u0001\u0010\u00c9\u0001J-\u0010\u00ca\u0001\u001a\u00020k2\u0008\u0010\u009c\u0001\u001a\u00030\u0092\u00012\u0007\u0010\u009d\u0001\u001a\u00020s2\u0008\u0010\u0099\u0001\u001a\u00030\u0098\u0001\u00a2\u0006\u0006\u0008\u00ca\u0001\u0010\u00cb\u0001J$\u0010\u00cd\u0001\u001a\u00020k2\u0008\u0010\u009c\u0001\u001a\u00030\u0092\u00012\u0008\u0010\u00cc\u0001\u001a\u00030\u0080\u0001\u00a2\u0006\u0006\u0008\u00cd\u0001\u0010\u00ce\u0001J6\u0010\u00cf\u0001\u001a\u00020k2\u0008\u0010\u009c\u0001\u001a\u00030\u0092\u00012\u0008\u0010\u0099\u0001\u001a\u00030\u0098\u00012\u0007\u0010\u0082\u0001\u001a\u00020\u00032\u0007\u0010\u0083\u0001\u001a\u00020s\u00a2\u0006\u0006\u0008\u00cf\u0001\u0010\u00d0\u0001J\u0011\u0010\u00d2\u0001\u001a\u00030\u00d1\u0001\u00a2\u0006\u0006\u0008\u00d2\u0001\u0010\u00d3\u0001J\u000f\u0010\u00d4\u0001\u001a\u00020k\u00a2\u0006\u0005\u0008\u00d4\u0001\u0010oJ\u000f\u0010\u00d5\u0001\u001a\u00020k\u00a2\u0006\u0005\u0008\u00d5\u0001\u0010oJ\u000f\u0010\u00d6\u0001\u001a\u00020k\u00a2\u0006\u0005\u0008\u00d6\u0001\u0010oJ\u001a\u0010\u00d8\u0001\u001a\n\u0012\u0005\u0012\u00030\u00d7\u00010\u00ae\u0001H\u0000\u00a2\u0006\u0006\u0008\u00d8\u0001\u0010\u00b0\u0001J\u001a\u0010\u00da\u0001\u001a\n\u0012\u0005\u0012\u00030\u00d9\u00010\u00ae\u0001H\u0000\u00a2\u0006\u0006\u0008\u00da\u0001\u0010\u00b0\u0001J\u0011\u0010\u00db\u0001\u001a\u00020kH\u0016\u00a2\u0006\u0005\u0008\u00db\u0001\u0010oJ\u0011\u0010\u00dc\u0001\u001a\u00020kH\u0016\u00a2\u0006\u0005\u0008\u00dc\u0001\u0010oJ\u001c\u0010\u00df\u0001\u001a\u00020k2\u0008\u0010\u00de\u0001\u001a\u00030\u00dd\u0001H\u0016\u00a2\u0006\u0006\u0008\u00df\u0001\u0010\u00e0\u0001J\u001b\u0010\u00e3\u0001\u001a\n\u0012\u0005\u0012\u00030\u00e2\u00010\u00e1\u0001H\u0096\u0001\u00a2\u0006\u0006\u0008\u00e3\u0001\u0010\u00e4\u0001J-\u0010\u00e8\u0001\u001a\u0005\u0018\u00010\u00a9\u0001*\u00030\u00e5\u00012\u0007\u0010\u00e6\u0001\u001a\u00020\u00032\u0007\u0010\u00e7\u0001\u001a\u00020\u0003H\u0096\u0001\u00a2\u0006\u0006\u0008\u00e8\u0001\u0010\u00e9\u0001J\u0012\u0010\u00ea\u0001\u001a\u00020kH\u0096\u0001\u00a2\u0006\u0005\u0008\u00ea\u0001\u0010oJ\'\u0010\u00ec\u0001\u001a\u00020k2\u0008\u0010\u00a2\u0001\u001a\u00030\u0092\u00012\u0008\u0010\u00eb\u0001\u001a\u00030\u0098\u0001H\u0096\u0001\u00a2\u0006\u0006\u0008\u00ec\u0001\u0010\u00ed\u0001J\'\u0010\u00ef\u0001\u001a\u00020k2\u0008\u0010\u00a2\u0001\u001a\u00030\u0092\u00012\u0008\u0010\u00ee\u0001\u001a\u00030\u0092\u0001H\u0096\u0001\u00a2\u0006\u0006\u0008\u00ef\u0001\u0010\u00f0\u0001J1\u0010\u00f3\u0001\u001a\u00020k2\u0008\u0010\u00a2\u0001\u001a\u00030\u0092\u00012\u0008\u0010\u00f1\u0001\u001a\u00030\u00a0\u00012\u0008\u0010\u00f2\u0001\u001a\u00030\u0098\u0001H\u0096\u0001\u00a2\u0006\u0006\u0008\u00f3\u0001\u0010\u00f4\u0001R\u0016\u0010\u0004\u001a\u00020\u00038\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00f5\u0001\u0010\u00f6\u0001R\u0016\u0010\u0006\u001a\u00020\u00058\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00f7\u0001\u0010\u00f8\u0001R\u0016\u0010\u0008\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00f9\u0001\u0010\u00fa\u0001R\u0016\u0010\n\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00fb\u0001\u0010\u00fc\u0001R\u0016\u0010\u000c\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00fd\u0001\u0010\u00fe\u0001R\u0016\u0010\u000e\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ff\u0001\u0010\u0080\u0002R\u0016\u0010\u0010\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0081\u0002\u0010\u0082\u0002R\u0016\u0010\u0012\u001a\u00020\u00118\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0083\u0002\u0010\u0084\u0002R\u0016\u0010\u0014\u001a\u00020\u00138\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0085\u0002\u0010\u0086\u0002R\u0016\u0010\u0016\u001a\u00020\u00158\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0087\u0002\u0010\u0088\u0002R\u0016\u0010\u0018\u001a\u00020\u00178\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0089\u0002\u0010\u008a\u0002R\u0016\u0010\u001a\u001a\u00020\u00198\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008b\u0002\u0010\u008c\u0002R\u0016\u0010\u001c\u001a\u00020\u001b8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008d\u0002\u0010\u008e\u0002R\u0016\u0010\u001e\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0084\u0001\u0010\u008f\u0002R\u0016\u0010 \u001a\u00020\u001f8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0090\u0002\u0010\u0091\u0002R\u0016\u0010\"\u001a\u00020!8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c8\u0001\u0010\u0092\u0002R\u0016\u0010$\u001a\u00020#8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b9\u0001\u0010\u0093\u0002R\u0016\u0010&\u001a\u00020%8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009a\u0001\u0010\u0094\u0002R\u0016\u0010(\u001a\u00020\'8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b1\u0001\u0010\u0095\u0002R\u0016\u0010*\u001a\u00020)8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0090\u0001\u0010\u0096\u0002R\u0016\u0010,\u001a\u00020+8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00af\u0001\u0010\u0097\u0002R\u0016\u0010.\u001a\u00020-8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a3\u0001\u0010\u0098\u0002R\u0016\u00100\u001a\u00020/8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a7\u0001\u0010\u0099\u0002R\u0016\u00102\u001a\u0002018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a5\u0001\u0010\u009a\u0002R\u0016\u00104\u001a\u0002038\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d8\u0001\u0010\u009b\u0002R\u0016\u00106\u001a\u0002058\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b8\u0001\u0010\u009c\u0002R\u0016\u00108\u001a\u0002078\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008d\u0001\u0010\u009d\u0002R\u0016\u0010:\u001a\u0002098\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b4\u0001\u0010\u009e\u0002R\u0016\u0010<\u001a\u00020;8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0093\u0001\u0010\u009f\u0002R\u0016\u0010>\u001a\u00020=8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d2\u0001\u0010\u00a0\u0002R\u0016\u0010@\u001a\u00020?8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a1\u0002\u0010\u00a2\u0002R\u0016\u0010B\u001a\u00020A8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00aa\u0001\u0010\u00a3\u0002R\u0016\u0010D\u001a\u00020C8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b7\u0001\u0010\u00a4\u0002R\u0016\u0010F\u001a\u00020E8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ac\u0001\u0010\u00a5\u0002R\u0016\u0010H\u001a\u00020G8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00da\u0001\u0010\u00a6\u0002R\u0018\u0010\u00aa\u0002\u001a\u00030\u00a7\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a8\u0002\u0010\u00a9\u0002R\u0017\u0010\u00ad\u0002\u001a\u00030\u00ab\u00028\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008q\u0010\u00ac\u0002R\u001e\u0010\u00b1\u0002\u001a\n\u0012\u0005\u0012\u00030\u00af\u00020\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008}\u0010\u00b0\u0002R$\u0010\u00b2\u0002\u001a\u0010\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a9\u00010h0\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008p\u0010\u00b0\u0002R\u001e\u0010\u00b4\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b3\u0002\u0010\u00b0\u0002R\u001e\u0010\u00b5\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008c\u0001\u0010\u00b0\u0002R\u001e\u0010\u00b7\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b6\u0002\u0010\u00b0\u0002R\u001d\u0010\u00b8\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008|\u0010\u00b0\u0002R\u001e\u0010\u00ba\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b9\u0002\u0010\u00b0\u0002R\u001e\u0010\u00bc\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bb\u0002\u0010\u00b0\u0002R\u001e\u0010\u00bd\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008e\u0001\u0010\u00b0\u0002R\u001d\u0010\u00be\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u007f\u0010\u00b0\u0002R\u001e\u0010\u00bf\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009e\u0001\u0010\u00b0\u0002R\u001e\u0010\u00c0\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0086\u0001\u0010\u00b0\u0002R\u001d\u0010\u00c1\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008n\u0010\u00b0\u0002R \u0010\u00c5\u0002\u001a\t\u0012\u0005\u0012\u00030\u00c2\u00020h8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00c3\u0002\u0010\u00c4\u0002R\u001c\u0010\u00c8\u0002\u001a\u0005\u0018\u00010\u00c6\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00c3\u0001\u0010\u00c7\u0002R\u001e\u0010\u00c9\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00cf\u0001\u0010\u00b0\u0002R\u001e\u0010\u00ca\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c5\u0001\u0010\u00b0\u0002R\u001d\u0010j\u001a\t\u0012\u0004\u0012\u00020i0\u00cb\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d6\u0001\u0010\u00c4\u0002R\u001f\u0010\u00cc\u0002\u001a\n\u0012\u0005\u0012\u00030\u00d7\u00010\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d5\u0001\u0010\u00b0\u0002R\u001e\u0010\u00cd\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00cd\u0001\u0010\u00b0\u0002R&\u0010\u00d0\u0002\u001a\u0011\u0012\u0005\u0012\u00030\u0098\u0001\u0012\u0005\u0012\u00030\u0080\u00010\u00ce\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ca\u0001\u0010\u00cf\u0002R9\u0010\u00d4\u0002\u001a$\u0012\u0005\u0012\u00030\u0098\u0001\u0012\u0005\u0012\u00030\u0080\u00010\u00d1\u0002j\u0011\u0012\u0005\u0012\u00030\u0098\u0001\u0012\u0005\u0012\u00030\u0080\u0001`\u00d2\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bc\u0001\u0010\u00d3\u0002R \u0010\u00d7\u0002\u001a\u000c\u0012\u0007\u0012\u0005\u0018\u00010\u008f\u00010\u00d5\u00028\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008w\u0010\u00d6\u0002R\u001e\u0010\u00d8\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0096\u0001\u0010\u00b0\u0002R4\u0010\u00d9\u0002\u001a\u001f\u0012\u001a\u0012\u0018\u0012\u0004\u0012\u00020s\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0080\u00010h\u0018\u00010\u00ce\u00020\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c0\u0001\u0010\u00b0\u0002R\u0017\u0010\u00da\u0002\u001a\u00020\u00038\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00be\u0001\u0010\u00f6\u0001R\u0018\u0010\u00dd\u0002\u001a\u00030\u00db\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d4\u0001\u0010\u00dc\u0002R\u0017\u0010\u00df\u0002\u001a\u00030\u00a0\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008r\u0010\u00de\u0002R\u0017\u0010\u00e0\u0002\u001a\u00030\u00a0\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008l\u0010\u00de\u0002R\u0018\u0010\u00e3\u0002\u001a\u00030\u00e1\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008a\u0001\u0010\u00e2\u0002R\u001b\u0010\u00e6\u0002\u001a\u0005\u0018\u00010\u00e4\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008u\u0010\u00e5\u0002R\u001b\u0010\u00e7\u0002\u001a\u0005\u0018\u00010\u00e4\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008y\u0010\u00e5\u0002R\u001c\u0010\u00e8\u0002\u001a\u0005\u0018\u00010\u00e4\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0088\u0001\u0010\u00e5\u0002R%\u0010\u00eb\u0002\u001a\u0010\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a9\u00010h0\u00ae\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e9\u0002\u0010\u00ea\u0002R\u001d\u0010g\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ec\u0002\u0010\u00ea\u0002R\u001e\u0010\u00ee\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ed\u0002\u0010\u00ea\u0002R\u001e\u0010\u00f0\u0002\u001a\t\u0012\u0004\u0012\u00020\u00030\u00ae\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ef\u0002\u0010\u00ea\u0002R\u001f\u0010\u00f2\u0002\u001a\n\u0012\u0005\u0012\u00030\u00d9\u00010\u00ae\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00f1\u0002\u0010\u00b0\u0002R%\u0010\u00f4\u0002\u001a\u0010\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a9\u00010h0\u00ae\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00f3\u0002\u0010\u00ea\u0002R%\u0010\u00f6\u0002\u001a\u0010\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a9\u00010h0\u00ae\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00f5\u0002\u0010\u00ea\u0002R\u0018\u0010\u00f9\u0002\u001a\u00030\u00f7\u00028BX\u0082\u0004\u00a2\u0006\u0008\u001a\u0006\u0008\u00a8\u0002\u0010\u00f8\u0002R\u001f\u0010\u00fb\u0002\u001a\n\u0012\u0005\u0012\u00030\u00e5\u00010\u00ae\u00018\u0016X\u0096\u0005\u00a2\u0006\u0008\u001a\u0006\u0008\u00fa\u0002\u0010\u00b0\u0001\u00a8\u0006\u0081\u0003"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;",
        "Lorg/xplatform/aggregator/impl/core/presentation/g;",
        "LG81/e;",
        "",
        "isVirtual",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
        "setNeedFavoritesReUpdateUseCase",
        "Lkc1/b;",
        "getAggregatorBannerListByCategoryScenario",
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;",
        "aggregatorGamesScenario",
        "Lv81/s;",
        "getRecommendedGamesScenario",
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;",
        "slotsGamesScenario",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;",
        "getViewedGamesScenario",
        "Lf81/a;",
        "addFavoriteUseCase",
        "Lf81/d;",
        "removeFavoriteUseCase",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "openGameDelegate",
        "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;",
        "aggregatorBannersDelegate",
        "LwX0/a;",
        "appScreenProvider",
        "LP91/b;",
        "aggregatorNavigator",
        "Le81/d;",
        "getGameToOpenScenario",
        "LwX0/C;",
        "routerHolder",
        "Lorg/xbet/analytics/domain/scope/g0;",
        "myAggregatorAnalytics",
        "LOR/a;",
        "myAggregatorFatmanLogger",
        "Lm8/a;",
        "dispatchers",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "LHX0/e;",
        "resourceManager",
        "Lcom/xbet/onexcore/utils/ext/c;",
        "networkConnectionUtil",
        "LpR/a;",
        "authFatmanLogger",
        "Le81/c;",
        "getFavoriteGamesFlowScenario",
        "LQ51/a;",
        "getCashbackUserInfoUseCase",
        "LQ51/b;",
        "getLevelInfoModelListUseCase",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "LXa0/i;",
        "setShowPopUpBonusUseCase",
        "LG81/c;",
        "dailyTaskWidgetMyAggregatorViewModelDelegate",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LnR/a;",
        "aggregatorGamesFatmanLogger",
        "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
        "getProfileUseCase",
        "Lv81/j;",
        "getCategoriesUseCase",
        "Lv81/q;",
        "getPopularGamesScenario",
        "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
        "newsAnalytics",
        "LfX/b;",
        "testRepository",
        "LGg/a;",
        "searchAnalytics",
        "Lorg/xbet/analytics/domain/scope/I;",
        "depositAnalytics",
        "Lek/d;",
        "getScreenBalanceByTypeScenario",
        "LxX0/a;",
        "blockPaymentNavigator",
        "LAR/a;",
        "depositFatmanLogger",
        "LZR/a;",
        "searchFatmanLogger",
        "Lfk/s;",
        "hasUserScreenBalanceUseCase",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "Lfk/o;",
        "observeScreenBalanceUseCase",
        "Lgk0/a;",
        "getAccountSelectionStyleConfigTypeScenario",
        "Lek/f;",
        "updateWithCheckGamesAggregatorScenario",
        "Lfk/l;",
        "getLastBalanceUseCase",
        "LC81/f;",
        "setDailyTaskRefreshScenario",
        "<init>",
        "(ZLorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lkc1/b;Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;Lv81/s;Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;Lf81/a;Lf81/d;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;LwX0/a;LP91/b;Le81/d;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LOR/a;Lm8/a;LSX0/c;LHX0/e;Lcom/xbet/onexcore/utils/ext/c;LpR/a;Le81/c;LQ51/a;LQ51/b;Lp9/c;LXa0/i;LG81/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LnR/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lv81/j;Lv81/q;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LfX/b;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lek/d;LxX0/a;LAR/a;LZR/a;Lfk/s;Lorg/xbet/remoteconfig/domain/usecases/i;Lfk/o;Lgk0/a;Lek/f;Lfk/l;LC81/f;)V",
        "allContentLoaded",
        "",
        "Lorg/xplatform/banners/api/domain/models/BannerModel;",
        "bannersModelsList",
        "",
        "M6",
        "(ZLjava/util/List;)V",
        "w6",
        "()V",
        "l6",
        "j6",
        "L6",
        "Lra1/c;",
        "gamesCategory",
        "O6",
        "(Lra1/c;)V",
        "G6",
        "authorized",
        "P6",
        "(Z)V",
        "logged",
        "p6",
        "k6",
        "(ZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "t6",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "isFavorite",
        "category",
        "M5",
        "(Lorg/xplatform/aggregator/api/model/Game;ZLra1/c;)V",
        "v6",
        "games",
        "Q6",
        "(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "N6",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "n6",
        "Z5",
        "s6",
        "Lra1/b;",
        "S5",
        "(Ljava/util/List;Lra1/c;Z)Lra1/b;",
        "",
        "b6",
        "(Lra1/c;)Ljava/lang/String;",
        "adapterUiModel",
        "H6",
        "(Lra1/b;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "gameId",
        "Q5",
        "(J)Z",
        "screenName",
        "gameCategory",
        "u6",
        "(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;Lra1/c;)V",
        "",
        "categoryId",
        "screen",
        "U5",
        "(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/String;)V",
        "W5",
        "(Lra1/c;)I",
        "V5",
        "(Lra1/c;)Ljava/lang/Long;",
        "LVX0/i;",
        "e6",
        "()Ljava/util/List;",
        "g6",
        "(Lra1/c;)Lra1/b;",
        "Lkotlinx/coroutines/flow/f0;",
        "T5",
        "()Lkotlinx/coroutines/flow/f0;",
        "R5",
        "Lkotlinx/coroutines/flow/Z;",
        "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;",
        "a6",
        "()Lkotlinx/coroutines/flow/Z;",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
        "f6",
        "Y5",
        "P5",
        "idToOpen",
        "partitionId",
        "F6",
        "(JJLjava/lang/String;)V",
        "J6",
        "(Ljava/lang/String;)V",
        "I6",
        "bannerId",
        "position",
        "y6",
        "(Ljava/lang/String;II)V",
        "A6",
        "(Ljava/lang/String;Lra1/c;)V",
        "fromDeepLink",
        "O5",
        "(Ljava/lang/String;Z)V",
        "E6",
        "(Ljava/lang/String;Lra1/c;J)V",
        "gameModel",
        "D6",
        "(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;)V",
        "z6",
        "(Ljava/lang/String;JZLra1/c;)V",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "c6",
        "()Lorg/xbet/uikit/components/lottie_empty/n;",
        "K6",
        "C6",
        "B6",
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a;",
        "X5",
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$d;",
        "h6",
        "R3",
        "d4",
        "",
        "throwable",
        "e4",
        "(Ljava/lang/Throwable;)V",
        "Lkotlinx/coroutines/flow/e;",
        "LG81/e$a;",
        "w0",
        "()Lkotlinx/coroutines/flow/e;",
        "LG81/e$b;",
        "showShimmer",
        "showHeader",
        "P1",
        "(LG81/e$b;ZZ)LVX0/i;",
        "u1",
        "taskId",
        "I1",
        "(Ljava/lang/String;J)V",
        "option",
        "Q",
        "(Ljava/lang/String;Ljava/lang/String;)V",
        "progress",
        "productId",
        "Z2",
        "(Ljava/lang/String;IJ)V",
        "z5",
        "Z",
        "A5",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
        "B5",
        "Lkc1/b;",
        "C5",
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;",
        "D5",
        "Lv81/s;",
        "E5",
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;",
        "F5",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;",
        "G5",
        "Lf81/a;",
        "H5",
        "Lf81/d;",
        "I5",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "J5",
        "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;",
        "K5",
        "LwX0/a;",
        "L5",
        "LP91/b;",
        "Le81/d;",
        "N5",
        "LwX0/C;",
        "Lorg/xbet/analytics/domain/scope/g0;",
        "LOR/a;",
        "Lm8/a;",
        "LSX0/c;",
        "LHX0/e;",
        "Lcom/xbet/onexcore/utils/ext/c;",
        "LpR/a;",
        "Le81/c;",
        "LQ51/a;",
        "LQ51/b;",
        "Lp9/c;",
        "LXa0/i;",
        "LG81/c;",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "Lorg/xbet/ui_common/utils/M;",
        "d6",
        "LnR/a;",
        "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
        "Lv81/j;",
        "Lv81/q;",
        "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
        "Lkotlinx/coroutines/sync/a;",
        "i6",
        "Lkotlinx/coroutines/sync/a;",
        "listMutex",
        "Lek0/o;",
        "Lek0/o;",
        "remoteConfigModel",
        "Lkotlinx/coroutines/flow/V;",
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b;",
        "Lkotlinx/coroutines/flow/V;",
        "mutableCashbackStateState",
        "mutableGamesListsState",
        "m6",
        "mutableErrorState",
        "recommendedGamesLoaded",
        "o6",
        "slotsGamesLoaded",
        "liveAggregatorGamesLoaded",
        "q6",
        "cashBackIsLoaded",
        "r6",
        "bannersIsLoaded",
        "dailyTasksIsLoaded",
        "gamesError",
        "recommendedGamesError",
        "slotsGamesError",
        "liveAggregatorGamesError",
        "Lg31/b;",
        "x6",
        "Ljava/util/List;",
        "cashbackLevels",
        "Lra1/a;",
        "Lra1/a;",
        "cashbackUserInfo",
        "cashBackIsError",
        "bannersIsError",
        "",
        "showAuthButtonsState",
        "mutableCheckAuthState",
        "",
        "Ljava/util/Map;",
        "gamesMap",
        "Ljava/util/LinkedHashMap;",
        "Lkotlin/collections/LinkedHashMap;",
        "Ljava/util/LinkedHashMap;",
        "favoritesGames",
        "",
        "[Lra1/b;",
        "unsortedList",
        "viewsGamesLoadedFlow",
        "virtualGamesCategoriesUiState",
        "hasAggregatorPlayerTasks",
        "Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;",
        "Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;",
        "bannerCollectionStyle",
        "I",
        "initialVirtualGamesStyle",
        "initialGamesStyle",
        "Ljava/text/DecimalFormat;",
        "Ljava/text/DecimalFormat;",
        "groupingFormatter",
        "Lkotlinx/coroutines/x0;",
        "Lkotlinx/coroutines/x0;",
        "viewedGamesJob",
        "addFavoriteJob",
        "favoritesUpdateJob",
        "R6",
        "Lkotlinx/coroutines/flow/f0;",
        "mutableVirtualContentListsState",
        "S6",
        "T6",
        "allContentNoAuthLoaded",
        "U6",
        "allContentError",
        "V6",
        "mutableAdapterBannerList",
        "W6",
        "mutableContentListsState",
        "X6",
        "aggregatorContentListsState",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;",
        "()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;",
        "vipCashbackType",
        "P2",
        "dailyTaskState",
        "Y6",
        "a",
        "d",
        "b",
        "c",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final Y6:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final A5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final A6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B5:Lkc1/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B6:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C5:Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final D5:Lv81/s;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final D6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final E5:Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final E6:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Long;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F6:Ljava/util/LinkedHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedHashMap<",
            "Ljava/lang/Long;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final G5:Lf81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final G6:[Lra1/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H5:Lf81/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/util/Map<",
            "Lra1/c;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final J5:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final J6:Z

.field public final K5:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final K6:Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final L5:LP91/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final L6:I

.field public final M5:Le81/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final M6:I

.field public final N5:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final N6:Ljava/text/DecimalFormat;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final O5:Lorg/xbet/analytics/domain/scope/g0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public O6:Lkotlinx/coroutines/x0;

.field public final P5:LOR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public P6:Lkotlinx/coroutines/x0;

.field public final Q5:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public Q6:Lkotlinx/coroutines/x0;

.field public final R5:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final R6:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S5:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S6:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final T5:Lcom/xbet/onexcore/utils/ext/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final T6:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final U5:LpR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final U6:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V5:Le81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final W5:LQ51/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final W6:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X5:LQ51/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X6:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final Y5:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final Z5:LXa0/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final a6:LG81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b6:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c6:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d6:LnR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e6:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f6:Lv81/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g6:Lv81/q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h6:Lorg/xbet/analytics/domain/scope/NewsAnalytics;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i6:Lkotlinx/coroutines/sync/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j6:Lek0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final r6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final s6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final t6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final u6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public x6:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lg31/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public y6:Lra1/a;

.field public final z5:Z

.field public final z6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$c;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$c;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Y6:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$c;

    return-void
.end method

.method public constructor <init>(ZLorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lkc1/b;Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;Lv81/s;Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;Lf81/a;Lf81/d;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;LwX0/a;LP91/b;Le81/d;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LOR/a;Lm8/a;LSX0/c;LHX0/e;Lcom/xbet/onexcore/utils/ext/c;LpR/a;Le81/c;LQ51/a;LQ51/b;Lp9/c;LXa0/i;LG81/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LnR/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lv81/j;Lv81/q;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LfX/b;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lek/d;LxX0/a;LAR/a;LZR/a;Lfk/s;Lorg/xbet/remoteconfig/domain/usecases/i;Lfk/o;Lgk0/a;Lek/f;Lfk/l;LC81/f;)V
    .locals 24
    .param p2    # Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkc1/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lv81/s;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lf81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lf81/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # LP91/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Le81/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lorg/xbet/analytics/domain/scope/g0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # LOR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # Lcom/xbet/onexcore/utils/ext/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # LpR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Le81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # LQ51/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # LQ51/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p26    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p27    # LXa0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p28    # LG81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p29    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p30    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p31    # LnR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p32    # Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p33    # Lv81/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p34    # Lv81/q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p35    # Lorg/xbet/analytics/domain/scope/NewsAnalytics;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p36    # LfX/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p37    # LGg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p38    # Lorg/xbet/analytics/domain/scope/I;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p39    # Lek/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p40    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p41    # LAR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p42    # LZR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p43    # Lfk/s;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p44    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p45    # Lfk/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p46    # Lgk0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p47    # Lek/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p48    # Lfk/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p49    # LC81/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    const/16 v21, 0x4

    const/16 v22, 0x3

    const/16 v23, 0x2

    const/4 v0, 0x6

    .line 1
    invoke-static/range {p28 .. p28}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    move-result-object v20

    move-object/from16 v0, p0

    move-object/from16 v1, p13

    move-object/from16 v8, p15

    move-object/from16 v9, p18

    move-object/from16 v10, p20

    move-object/from16 v5, p26

    move-object/from16 v2, p29

    move-object/from16 v3, p30

    move-object/from16 v6, p37

    move-object/from16 v7, p38

    move-object/from16 v15, p39

    move-object/from16 v4, p40

    move-object/from16 v11, p41

    move-object/from16 v12, p42

    move-object/from16 v16, p43

    move-object/from16 v17, p45

    move-object/from16 v13, p46

    move-object/from16 v18, p47

    move-object/from16 v14, p48

    move-object/from16 v19, p49

    .line 2
    invoke-direct/range {v0 .. v20}, Lorg/xplatform/aggregator/impl/core/presentation/g;-><init>(LP91/b;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LxX0/a;Lp9/c;LGg/a;Lorg/xbet/analytics/domain/scope/I;LwX0/C;Lm8/a;LHX0/e;LAR/a;LZR/a;Lgk0/a;Lfk/l;Lek/d;Lfk/s;Lfk/o;Lek/f;LC81/f;Ljava/util/List;)V

    move/from16 v1, p1

    .line 3
    iput-boolean v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    move-object/from16 v1, p2

    .line 4
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->A5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;

    move-object/from16 v1, p3

    .line 5
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->B5:Lkc1/b;

    move-object/from16 v1, p4

    .line 6
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->C5:Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;

    move-object/from16 v1, p5

    .line 7
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->D5:Lv81/s;

    move-object/from16 v1, p6

    .line 8
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->E5:Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;

    move-object/from16 v1, p7

    .line 9
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->F5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;

    move-object/from16 v1, p8

    .line 10
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->G5:Lf81/a;

    move-object/from16 v1, p9

    .line 11
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->H5:Lf81/d;

    move-object/from16 v1, p10

    .line 12
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->I5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    move-object/from16 v1, p11

    .line 13
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->J5:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    move-object/from16 v1, p12

    .line 14
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->K5:LwX0/a;

    move-object/from16 v1, p13

    .line 15
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->L5:LP91/b;

    move-object/from16 v1, p14

    .line 16
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->M5:Le81/d;

    .line 17
    iput-object v8, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->N5:LwX0/C;

    move-object/from16 v1, p16

    .line 18
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->O5:Lorg/xbet/analytics/domain/scope/g0;

    move-object/from16 v1, p17

    .line 19
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->P5:LOR/a;

    .line 20
    iput-object v9, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Q5:Lm8/a;

    move-object/from16 v1, p19

    .line 21
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->R5:LSX0/c;

    .line 22
    iput-object v10, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5:LHX0/e;

    move-object/from16 v1, p21

    .line 23
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->T5:Lcom/xbet/onexcore/utils/ext/c;

    move-object/from16 v1, p22

    .line 24
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->U5:LpR/a;

    move-object/from16 v1, p23

    .line 25
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->V5:Le81/c;

    move-object/from16 v1, p24

    .line 26
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->W5:LQ51/a;

    move-object/from16 v1, p25

    .line 27
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->X5:LQ51/b;

    .line 28
    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Y5:Lp9/c;

    move-object/from16 v1, p27

    .line 29
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Z5:LXa0/i;

    move-object/from16 v1, p28

    .line 30
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->a6:LG81/c;

    .line 31
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->b6:Lorg/xbet/ui_common/utils/internet/a;

    .line 32
    iput-object v3, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->c6:Lorg/xbet/ui_common/utils/M;

    move-object/from16 v1, p31

    .line 33
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->d6:LnR/a;

    move-object/from16 v1, p32

    .line 34
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->e6:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    move-object/from16 v1, p33

    .line 35
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->f6:Lv81/j;

    move-object/from16 v1, p34

    .line 36
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->g6:Lv81/q;

    move-object/from16 v1, p35

    .line 37
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->h6:Lorg/xbet/analytics/domain/scope/NewsAnalytics;

    const/4 v1, 0x0

    const/4 v2, 0x1

    const/4 v3, 0x0

    .line 38
    invoke-static {v1, v2, v3}, Lkotlinx/coroutines/sync/MutexKt;->b(ZILjava/lang/Object;)Lkotlinx/coroutines/sync/a;

    move-result-object v4

    iput-object v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->i6:Lkotlinx/coroutines/sync/a;

    .line 39
    invoke-interface/range {p44 .. p44}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    move-result-object v4

    iput-object v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j6:Lek0/o;

    .line 40
    sget-object v4, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$a;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$a;

    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v4

    iput-object v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->k6:Lkotlinx/coroutines/flow/V;

    .line 41
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->e6()Ljava/util/List;

    move-result-object v4

    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v4

    iput-object v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->l6:Lkotlinx/coroutines/flow/V;

    .line 42
    sget-object v4, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->m6:Lkotlinx/coroutines/flow/V;

    .line 43
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->n6:Lkotlinx/coroutines/flow/V;

    .line 44
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->o6:Lkotlinx/coroutines/flow/V;

    .line 45
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 46
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->q6:Lkotlinx/coroutines/flow/V;

    .line 47
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->r6:Lkotlinx/coroutines/flow/V;

    .line 48
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->s6:Lkotlinx/coroutines/flow/V;

    .line 49
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->t6:Lkotlinx/coroutines/flow/V;

    .line 50
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->u6:Lkotlinx/coroutines/flow/V;

    .line 51
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->v6:Lkotlinx/coroutines/flow/V;

    .line 52
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->w6:Lkotlinx/coroutines/flow/V;

    .line 53
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->x6:Ljava/util/List;

    .line 54
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z6:Lkotlinx/coroutines/flow/V;

    .line 55
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->A6:Lkotlinx/coroutines/flow/V;

    .line 56
    new-instance v5, Ljava/util/ArrayList;

    invoke-direct {v5}, Ljava/util/ArrayList;-><init>()V

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->B6:Ljava/util/List;

    .line 57
    sget-object v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a$a;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a$a;

    invoke-static {v5}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->C6:Lkotlinx/coroutines/flow/V;

    .line 58
    invoke-static {v4}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v4

    iput-object v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->D6:Lkotlinx/coroutines/flow/V;

    .line 59
    new-instance v4, Ljava/util/LinkedHashMap;

    invoke-direct {v4}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->E6:Ljava/util/Map;

    .line 60
    new-instance v4, Ljava/util/LinkedHashMap;

    invoke-direct {v4}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->F6:Ljava/util/LinkedHashMap;

    const/4 v4, 0x5

    .line 61
    new-array v5, v4, [Lra1/b;

    const/4 v6, 0x0

    :goto_0
    if-ge v6, v4, :cond_0

    aput-object v3, v5, v6

    add-int/2addr v6, v2

    goto :goto_0

    :cond_0
    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->G6:[Lra1/b;

    .line 62
    sget-object v5, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    invoke-static {v5}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v6

    iput-object v6, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->H6:Lkotlinx/coroutines/flow/V;

    .line 63
    invoke-static {v3}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v6

    iput-object v6, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->I6:Lkotlinx/coroutines/flow/V;

    .line 64
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j6:Lek0/o;

    invoke-virtual {v6}, Lek0/o;->q0()Z

    move-result v6

    if-nez v6, :cond_2

    iget-object v6, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j6:Lek0/o;

    invoke-virtual {v6}, Lek0/o;->r0()Z

    move-result v6

    if-eqz v6, :cond_1

    goto :goto_1

    :cond_1
    const/4 v6, 0x0

    goto :goto_2

    :cond_2
    :goto_1
    const/4 v6, 0x1

    :goto_2
    iput-boolean v6, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->J6:Z

    .line 65
    iget-boolean v6, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    if-eqz v6, :cond_3

    .line 66
    sget-object v6, Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;->SquareS:Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;

    goto :goto_3

    .line 67
    :cond_3
    sget-object v6, Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;->Companion:Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle$a;

    iget-object v7, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j6:Lek0/o;

    invoke-virtual {v7}, Lek0/o;->S1()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle$a;->a(Ljava/lang/String;)Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;

    move-result-object v6

    .line 68
    :goto_3
    iput-object v6, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->K6:Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;

    .line 69
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j6:Lek0/o;

    invoke-virtual {v7}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    move-result-object v7

    .line 70
    invoke-static {v7, v2}, Ls81/b;->b(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Z)I

    move-result v7

    iput v7, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->L6:I

    .line 71
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j6:Lek0/o;

    invoke-virtual {v7}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    move-result-object v7

    invoke-static {v7, v2}, Ls81/b;->b(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Z)I

    move-result v7

    iput v7, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->M6:I

    .line 72
    sget-object v7, Lorg/xbet/uikit/utils/C;->a:Lorg/xbet/uikit/utils/C;

    invoke-static {v7, v1, v2, v3}, Lorg/xbet/uikit/utils/C;->b(Lorg/xbet/uikit/utils/C;CILjava/lang/Object;)Ljava/text/DecimalFormat;

    move-result-object v7

    iput-object v7, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->N6:Ljava/text/DecimalFormat;

    .line 73
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->k6:Lkotlinx/coroutines/flow/V;

    .line 74
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->l6:Lkotlinx/coroutines/flow/V;

    .line 75
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->t6:Lkotlinx/coroutines/flow/V;

    .line 76
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z6:Lkotlinx/coroutines/flow/V;

    .line 77
    new-instance v11, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$mutableVirtualContentListsState$1;

    invoke-direct {v11, v0, v3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$mutableVirtualContentListsState$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    invoke-static {v7, v8, v9, v10, v11}, Lkotlinx/coroutines/flow/g;->q(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/p;)Lkotlinx/coroutines/flow/e;

    move-result-object v7

    .line 78
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    move-result-object v8

    .line 79
    sget-object v9, Lkotlinx/coroutines/flow/d0;->a:Lkotlinx/coroutines/flow/d0$a;

    const/4 v10, 0x3

    const/4 v11, 0x0

    const-wide/16 v12, 0x0

    const-wide/16 v14, 0x0

    move-object/from16 p1, v9

    move-object/from16 p7, v11

    move-wide/from16 p2, v12

    move-wide/from16 p4, v14

    const/16 p6, 0x3

    invoke-static/range {p1 .. p7}, Lkotlinx/coroutines/flow/d0$a;->b(Lkotlinx/coroutines/flow/d0$a;JJILjava/lang/Object;)Lkotlinx/coroutines/flow/d0;

    move-result-object v9

    move-object/from16 v10, p1

    .line 80
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    move-result-object v11

    .line 81
    new-instance v12, Lra1/a$c;

    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->i6()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    move-result-object v13

    invoke-direct {v12, v13}, Lra1/a$c;-><init>(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;)V

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 82
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->e6()Ljava/util/List;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 83
    sget-object v12, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 84
    invoke-static {v11}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    move-result-object v11

    .line 85
    invoke-static {v7, v8, v9, v11}, Lkotlinx/coroutines/flow/g;->v0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlinx/coroutines/flow/d0;Ljava/lang/Object;)Lkotlinx/coroutines/flow/f0;

    move-result-object v7

    iput-object v7, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->R6:Lkotlinx/coroutines/flow/f0;

    .line 86
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->n6:Lkotlinx/coroutines/flow/V;

    .line 87
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->o6:Lkotlinx/coroutines/flow/V;

    .line 88
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 89
    iget-object v12, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->q6:Lkotlinx/coroutines/flow/V;

    .line 90
    iget-object v13, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->r6:Lkotlinx/coroutines/flow/V;

    .line 91
    iget-object v14, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->s6:Lkotlinx/coroutines/flow/V;

    const/16 p8, 0x0

    const/4 v15, 0x6

    .line 92
    new-array v1, v15, [Lkotlinx/coroutines/flow/e;

    aput-object v8, v1, p8

    aput-object v9, v1, v2

    aput-object v11, v1, v23

    aput-object v12, v1, v22

    aput-object v13, v1, v21

    aput-object v14, v1, v4

    .line 93
    new-instance v8, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$1;

    invoke-direct {v8, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$1;-><init>([Lkotlinx/coroutines/flow/e;)V

    .line 94
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    move-result-object v1

    const/4 v9, 0x3

    const/4 v11, 0x0

    const-wide/16 v12, 0x0

    const-wide/16 v16, 0x0

    move-object/from16 p7, v11

    move-wide/from16 p2, v12

    move-wide/from16 p4, v16

    const/16 p6, 0x3

    .line 95
    invoke-static/range {p1 .. p7}, Lkotlinx/coroutines/flow/d0$a;->b(Lkotlinx/coroutines/flow/d0$a;JJILjava/lang/Object;)Lkotlinx/coroutines/flow/d0;

    move-result-object v9

    .line 96
    invoke-static {v8, v1, v9, v5}, Lkotlinx/coroutines/flow/g;->v0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlinx/coroutines/flow/d0;Ljava/lang/Object;)Lkotlinx/coroutines/flow/f0;

    move-result-object v1

    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S6:Lkotlinx/coroutines/flow/f0;

    .line 97
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->o6:Lkotlinx/coroutines/flow/V;

    .line 98
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 99
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->r6:Lkotlinx/coroutines/flow/V;

    .line 100
    new-instance v12, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$allContentNoAuthLoaded$1;

    invoke-direct {v12, v3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$allContentNoAuthLoaded$1;-><init>(Lkotlin/coroutines/e;)V

    invoke-static {v8, v9, v11, v12}, Lkotlinx/coroutines/flow/g;->p(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/o;)Lkotlinx/coroutines/flow/e;

    move-result-object v8

    .line 101
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    move-result-object v9

    const/4 v11, 0x3

    const/4 v12, 0x0

    const-wide/16 v13, 0x0

    move-object/from16 p7, v12

    move-wide/from16 p2, v13

    const/16 p6, 0x3

    .line 102
    invoke-static/range {p1 .. p7}, Lkotlinx/coroutines/flow/d0$a;->b(Lkotlinx/coroutines/flow/d0$a;JJILjava/lang/Object;)Lkotlinx/coroutines/flow/d0;

    move-result-object v10

    move-object/from16 v11, p1

    .line 103
    invoke-static {v8, v9, v10, v5}, Lkotlinx/coroutines/flow/g;->v0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlinx/coroutines/flow/d0;Ljava/lang/Object;)Lkotlinx/coroutines/flow/f0;

    move-result-object v8

    iput-object v8, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->T6:Lkotlinx/coroutines/flow/f0;

    .line 104
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->t6:Lkotlinx/coroutines/flow/V;

    .line 105
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z6:Lkotlinx/coroutines/flow/V;

    .line 106
    iget-object v12, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->A6:Lkotlinx/coroutines/flow/V;

    .line 107
    new-instance v13, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$allContentError$1;

    invoke-direct {v13, v0, v3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$allContentError$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    invoke-static {v9, v10, v12, v13}, Lkotlinx/coroutines/flow/g;->p(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/o;)Lkotlinx/coroutines/flow/e;

    move-result-object v3

    .line 108
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    move-result-object v9

    const/4 v10, 0x3

    const/4 v12, 0x0

    const-wide/16 v13, 0x0

    move-object/from16 p7, v12

    move-wide/from16 p2, v13

    const/16 p6, 0x3

    .line 109
    invoke-static/range {p1 .. p7}, Lkotlinx/coroutines/flow/d0$a;->b(Lkotlinx/coroutines/flow/d0$a;JJILjava/lang/Object;)Lkotlinx/coroutines/flow/d0;

    move-result-object v10

    .line 110
    invoke-static {v3, v9, v10, v5}, Lkotlinx/coroutines/flow/g;->v0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlinx/coroutines/flow/d0;Ljava/lang/Object;)Lkotlinx/coroutines/flow/f0;

    move-result-object v3

    iput-object v3, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->U6:Lkotlinx/coroutines/flow/f0;

    .line 111
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$d$a;

    .line 112
    new-instance v9, Lorg/xbet/uikit/components/bannercollection/a$b;

    .line 113
    new-instance v10, LAZ0/c;

    .line 114
    sget-object v12, LAZ0/c;->c:LAZ0/c$a;

    invoke-virtual {v12, v6}, LAZ0/c$a;->a(Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;)I

    move-result v12

    .line 115
    invoke-direct {v10, v6, v12}, LAZ0/c;-><init>(Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;I)V

    .line 116
    invoke-direct {v9, v10}, Lorg/xbet/uikit/components/bannercollection/a$b;-><init>(LAZ0/c;)V

    .line 117
    invoke-direct {v5, v9}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$d$a;-><init>(Lorg/xbet/uikit/components/bannercollection/a$b;)V

    .line 118
    invoke-static {v5}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    move-result-object v5

    iput-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->V6:Lkotlinx/coroutines/flow/V;

    .line 119
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->k6:Lkotlinx/coroutines/flow/V;

    .line 120
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->l6:Lkotlinx/coroutines/flow/V;

    .line 121
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->P2()Lkotlinx/coroutines/flow/f0;

    move-result-object v9

    .line 122
    new-array v10, v15, [Lkotlinx/coroutines/flow/e;

    aput-object v5, v10, p8

    aput-object v6, v10, v2

    aput-object v1, v10, v23

    aput-object v3, v10, v22

    aput-object v8, v10, v21

    aput-object v9, v10, v4

    .line 123
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2;

    invoke-direct {v1, v10, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$2;-><init>([Lkotlinx/coroutines/flow/e;Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V

    .line 124
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    move-result-object v2

    const/4 v3, 0x3

    const/4 v4, 0x0

    const-wide/16 v5, 0x0

    const-wide/16 v8, 0x0

    move-object/from16 p7, v4

    move-wide/from16 p2, v5

    move-wide/from16 p4, v8

    const/16 p6, 0x3

    .line 125
    invoke-static/range {p1 .. p7}, Lkotlinx/coroutines/flow/d0$a;->b(Lkotlinx/coroutines/flow/d0$a;JJILjava/lang/Object;)Lkotlinx/coroutines/flow/d0;

    move-result-object v3

    .line 126
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->e6()Ljava/util/List;

    move-result-object v4

    .line 127
    invoke-static {v1, v2, v3, v4}, Lkotlinx/coroutines/flow/g;->v0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlinx/coroutines/flow/d0;Ljava/lang/Object;)Lkotlinx/coroutines/flow/f0;

    move-result-object v1

    iput-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->W6:Lkotlinx/coroutines/flow/f0;

    .line 128
    iget-boolean v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    if-eqz v2, :cond_4

    goto :goto_4

    :cond_4
    move-object v7, v1

    :goto_4
    iput-object v7, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->X6:Lkotlinx/coroutines/flow/f0;

    .line 129
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->w6()V

    return-void
.end method

.method public static final synthetic A4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/util/List;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->x6:Ljava/util/List;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic A5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->t6(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic B4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lra1/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->y6:Lra1/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B5(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->x6(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic C4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lra1/c;)Ljava/lang/String;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->b6(Lra1/c;)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic C5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->G6()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/CoroutineExceptionHandler;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic D5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/util/List;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->x6:Ljava/util/List;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic E4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->s6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lra1/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->y6:Lra1/a;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic F4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lorg/xbet/ui_common/utils/M;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->c6:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic F5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->a4(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic G4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/util/LinkedHashMap;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->F6:Ljava/util/LinkedHashMap;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic G5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lra1/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->H6(Lra1/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic H4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Q6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic H5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->L6()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic I4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->t6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic I5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;ZLjava/util/List;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->M6(ZLjava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic J4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/util/Map;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->E6:Ljava/util/Map;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic J5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->N6(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic K4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->e6()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic K5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->P6(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic L4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lp9/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Y5:Lp9/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic L5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Q6(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic M4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)LQ51/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->W5:LQ51/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic N4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lv81/j;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->f6:Lv81/j;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final N5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Q5:Lm8/a;

    .line 6
    .line 7
    invoke-interface {p0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-interface {v0, p0, p1}, Lkotlinx/coroutines/CoroutineExceptionHandler;->handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final synthetic O4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Le81/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->M5:Le81/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic P4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)LQ51/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->X5:LQ51/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic Q4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lv81/q;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->g6:Lv81/q;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic R4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->e6:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic S4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->F5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic T4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/text/DecimalFormat;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->N6:Ljava/text/DecimalFormat;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic U4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->J6:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic V4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)I
    .locals 0

    .line 1
    iget p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->L6:I

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic W4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/sync/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->i6:Lkotlinx/coroutines/sync/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic X4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->w6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic Y4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic Z4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->k6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method private final Z5()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->B5:Lkc1/b;

    .line 2
    .line 3
    iget-boolean v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 8
    .line 9
    :goto_0
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 10
    .line 11
    .line 12
    move-result-wide v1

    .line 13
    goto :goto_1

    .line 14
    :cond_0
    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->MY_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :goto_1
    invoke-interface {v0, v1, v2}, Lkc1/b;->a(J)Lkotlinx/coroutines/flow/e;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$getBanners$1;

    .line 22
    .line 23
    const/4 v2, 0x0

    .line 24
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$getBanners$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$getBanners$2;

    .line 32
    .line 33
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$getBanners$2;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 34
    .line 35
    .line 36
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$getBanners$3;

    .line 41
    .line 42
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$getBanners$3;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 43
    .line 44
    .line 45
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->h0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$getBanners$4;

    .line 50
    .line 51
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$getBanners$4;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 52
    .line 53
    .line 54
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 59
    .line 60
    .line 61
    move-result-object v1

    .line 62
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 63
    .line 64
    .line 65
    return-void
.end method

.method public static final synthetic a5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->m6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic b5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->l6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic c5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lorg/xbet/analytics/domain/scope/g0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->O5:Lorg/xbet/analytics/domain/scope/g0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)LOR/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->P5:LOR/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final d6(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->R3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic e5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->I5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic f5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->u6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic g5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->n6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic h5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lek0/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j6:Lek0/o;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic i5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lf81/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->H5:Lf81/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic j5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic k5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)LwX0/C;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->N5:LwX0/C;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic l4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->o6(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic l5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->C6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic m4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->m6(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic m5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->v6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final m6(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->t6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    invoke-interface {v1}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-interface {v0, v1, p1}, Lkotlinx/coroutines/CoroutineExceptionHandler;->handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V

    .line 21
    .line 22
    .line 23
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->l6:Lkotlinx/coroutines/flow/V;

    .line 24
    .line 25
    new-instance v0, Lra1/d;

    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->c6()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 28
    .line 29
    .line 30
    move-result-object p0

    .line 31
    invoke-direct {v0, p0}, Lra1/d;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 32
    .line 33
    .line 34
    invoke-static {v0}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 35
    .line 36
    .line 37
    move-result-object p0

    .line 38
    invoke-interface {p1, p0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 42
    .line 43
    return-object p0
.end method

.method public static synthetic n4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->d6(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic n5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->o6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic o4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->q6(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic o5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)[Lra1/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->G6:[Lra1/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final o6(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 4
    .line 5
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->q6:Lkotlinx/coroutines/flow/V;

    .line 9
    .line 10
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->k6:Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    sget-object p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$a;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$b$a;

    .line 16
    .line 17
    invoke-interface {p0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method

.method public static synthetic p4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->N5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic p5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->H6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic q4(ZLorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->r6(ZLorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic q5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->i6()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final q6(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->n6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 4
    .line 5
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 9
    .line 10
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->o6:Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    invoke-interface {p0, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 19
    .line 20
    return-object p0
.end method

.method public static final synthetic r4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lf81/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->G5:Lf81/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic r5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->I6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final r6(ZLorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlin/Unit;
    .locals 3

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    if-eqz p0, :cond_1

    .line 4
    .line 5
    iget-object p0, p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->t6:Lkotlinx/coroutines/flow/V;

    .line 6
    .line 7
    iget-object v2, p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->u6:Lkotlinx/coroutines/flow/V;

    .line 8
    .line 9
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    check-cast v2, Ljava/lang/Boolean;

    .line 14
    .line 15
    invoke-virtual {v2}, Ljava/lang/Boolean;->booleanValue()Z

    .line 16
    .line 17
    .line 18
    move-result v2

    .line 19
    if-eqz v2, :cond_0

    .line 20
    .line 21
    iget-object v2, p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->w6:Lkotlinx/coroutines/flow/V;

    .line 22
    .line 23
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    check-cast v2, Ljava/lang/Boolean;

    .line 28
    .line 29
    invoke-virtual {v2}, Ljava/lang/Boolean;->booleanValue()Z

    .line 30
    .line 31
    .line 32
    move-result v2

    .line 33
    if-eqz v2, :cond_0

    .line 34
    .line 35
    iget-object p1, p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->v6:Lkotlinx/coroutines/flow/V;

    .line 36
    .line 37
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    check-cast p1, Ljava/lang/Boolean;

    .line 42
    .line 43
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 44
    .line 45
    .line 46
    move-result p1

    .line 47
    if-eqz p1, :cond_0

    .line 48
    .line 49
    const/4 v0, 0x1

    .line 50
    :cond_0
    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    invoke-interface {p0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    goto :goto_0

    .line 58
    :cond_1
    iget-object p0, p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->t6:Lkotlinx/coroutines/flow/V;

    .line 59
    .line 60
    iget-object v2, p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->w6:Lkotlinx/coroutines/flow/V;

    .line 61
    .line 62
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    move-result-object v2

    .line 66
    check-cast v2, Ljava/lang/Boolean;

    .line 67
    .line 68
    invoke-virtual {v2}, Ljava/lang/Boolean;->booleanValue()Z

    .line 69
    .line 70
    .line 71
    move-result v2

    .line 72
    if-eqz v2, :cond_2

    .line 73
    .line 74
    iget-object p1, p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->v6:Lkotlinx/coroutines/flow/V;

    .line 75
    .line 76
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    check-cast p1, Ljava/lang/Boolean;

    .line 81
    .line 82
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 83
    .line 84
    .line 85
    move-result p1

    .line 86
    if-eqz p1, :cond_2

    .line 87
    .line 88
    const/4 v0, 0x1

    .line 89
    :cond_2
    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 90
    .line 91
    .line 92
    move-result-object p1

    .line 93
    invoke-interface {p0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 94
    .line 95
    .line 96
    :goto_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 97
    .line 98
    return-object p0
.end method

.method public static final synthetic s4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)LP91/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->L5:LP91/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/lang/Throwable;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->Q3(Ljava/lang/Throwable;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic t4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)LwX0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->K5:LwX0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j6()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic u4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Z5()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic u5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic v4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->A6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->k6(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic w4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->r6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->l6()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic x4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Ljava/util/List;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->B6:Ljava/util/List;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->n6()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic x6(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic y4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->p6(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic z4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->q6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic z5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->s6(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public final A6(Ljava/lang/String;Lra1/c;)V
    .locals 20
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lra1/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p2

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->V5(Lra1/c;)Ljava/lang/Long;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    const/4 v3, 0x0

    .line 10
    if-eqz v2, :cond_1

    .line 11
    .line 12
    invoke-virtual {v2}, Ljava/lang/Number;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v4

    .line 16
    const-wide/16 v6, 0x0

    .line 17
    .line 18
    cmp-long v2, v4, v6

    .line 19
    .line 20
    if-nez v2, :cond_0

    .line 21
    .line 22
    move-object v2, v3

    .line 23
    goto :goto_0

    .line 24
    :cond_0
    invoke-static {v4, v5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 25
    .line 26
    .line 27
    move-result-object v2

    .line 28
    :goto_0
    if-eqz v2, :cond_1

    .line 29
    .line 30
    invoke-virtual {v2}, Ljava/lang/Number;->longValue()J

    .line 31
    .line 32
    .line 33
    move-result-wide v4

    .line 34
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->O5:Lorg/xbet/analytics/domain/scope/g0;

    .line 35
    .line 36
    invoke-virtual {v2, v4, v5}, Lorg/xbet/analytics/domain/scope/g0;->B(J)V

    .line 37
    .line 38
    .line 39
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->d6:LnR/a;

    .line 40
    .line 41
    long-to-int v5, v4

    .line 42
    move-object/from16 v4, p1

    .line 43
    .line 44
    invoke-interface {v2, v4, v5}, LnR/a;->a(Ljava/lang/String;I)V

    .line 45
    .line 46
    .line 47
    :cond_1
    iget-boolean v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 48
    .line 49
    const/4 v4, 0x0

    .line 50
    if-eqz v2, :cond_2

    .line 51
    .line 52
    instance-of v2, v1, Lra1/c$c;

    .line 53
    .line 54
    if-eqz v2, :cond_7

    .line 55
    .line 56
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->L5:LP91/b;

    .line 57
    .line 58
    new-instance v5, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 59
    .line 60
    check-cast v1, Lra1/c$c;

    .line 61
    .line 62
    invoke-virtual {v1}, Lra1/c$c;->c()Ljava/lang/String;

    .line 63
    .line 64
    .line 65
    move-result-object v6

    .line 66
    invoke-virtual {v1}, Lra1/c$c;->b()J

    .line 67
    .line 68
    .line 69
    move-result-wide v8

    .line 70
    new-instance v10, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$NewGamesFolderScreen;

    .line 71
    .line 72
    invoke-direct {v10, v4}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$NewGamesFolderScreen;-><init>(Z)V

    .line 73
    .line 74
    .line 75
    const/16 v17, 0x72

    .line 76
    .line 77
    const/16 v18, 0x0

    .line 78
    .line 79
    const/4 v7, 0x0

    .line 80
    const/4 v11, 0x0

    .line 81
    const-wide/16 v12, 0x0

    .line 82
    .line 83
    const-wide/16 v14, 0x0

    .line 84
    .line 85
    const-string v16, ""

    .line 86
    .line 87
    invoke-direct/range {v5 .. v18}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 88
    .line 89
    .line 90
    invoke-virtual {v2, v5}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 91
    .line 92
    .line 93
    return-void

    .line 94
    :cond_2
    instance-of v2, v1, Lra1/c$b;

    .line 95
    .line 96
    if-eqz v2, :cond_3

    .line 97
    .line 98
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->L5:LP91/b;

    .line 99
    .line 100
    new-instance v6, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Favorites;

    .line 101
    .line 102
    sget-object v1, Lorg/xplatform/aggregator/api/navigation/FavoriteType;->FAVORITE:Lorg/xplatform/aggregator/api/navigation/FavoriteType;

    .line 103
    .line 104
    invoke-direct {v6, v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Favorites;-><init>(Lorg/xplatform/aggregator/api/navigation/FavoriteType;)V

    .line 105
    .line 106
    .line 107
    const/4 v9, 0x4

    .line 108
    const/4 v10, 0x0

    .line 109
    const/4 v7, 0x1

    .line 110
    const/4 v8, 0x0

    .line 111
    invoke-static/range {v5 .. v10}, LP91/b;->h(LP91/b;Lorg/xplatform/aggregator/api/navigation/AggregatorTab;ZZILjava/lang/Object;)V

    .line 112
    .line 113
    .line 114
    return-void

    .line 115
    :cond_3
    instance-of v2, v1, Lra1/c$d;

    .line 116
    .line 117
    if-eqz v2, :cond_4

    .line 118
    .line 119
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->L5:LP91/b;

    .line 120
    .line 121
    new-instance v6, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Favorites;

    .line 122
    .line 123
    sget-object v1, Lorg/xplatform/aggregator/api/navigation/FavoriteType;->VIEWED:Lorg/xplatform/aggregator/api/navigation/FavoriteType;

    .line 124
    .line 125
    invoke-direct {v6, v1}, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Favorites;-><init>(Lorg/xplatform/aggregator/api/navigation/FavoriteType;)V

    .line 126
    .line 127
    .line 128
    const/4 v9, 0x4

    .line 129
    const/4 v10, 0x0

    .line 130
    const/4 v7, 0x1

    .line 131
    const/4 v8, 0x0

    .line 132
    invoke-static/range {v5 .. v10}, LP91/b;->h(LP91/b;Lorg/xplatform/aggregator/api/navigation/AggregatorTab;ZZILjava/lang/Object;)V

    .line 133
    .line 134
    .line 135
    return-void

    .line 136
    :cond_4
    instance-of v2, v1, Lra1/c$e;

    .line 137
    .line 138
    const/4 v5, 0x2

    .line 139
    if-eqz v2, :cond_5

    .line 140
    .line 141
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->L5:LP91/b;

    .line 142
    .line 143
    new-instance v6, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 144
    .line 145
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5:LHX0/e;

    .line 146
    .line 147
    sget v7, Lpb/k;->live_casino_title:I

    .line 148
    .line 149
    new-array v8, v4, [Ljava/lang/Object;

    .line 150
    .line 151
    invoke-interface {v2, v7, v8}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 152
    .line 153
    .line 154
    move-result-object v7

    .line 155
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5:LHX0/e;

    .line 156
    .line 157
    sget v8, Lpb/k;->casino_category_folder_and_section_description:I

    .line 158
    .line 159
    new-array v4, v4, [Ljava/lang/Object;

    .line 160
    .line 161
    invoke-interface {v2, v8, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 162
    .line 163
    .line 164
    move-result-object v8

    .line 165
    sget-object v2, Lorg/xplatform/aggregator/api/model/PartitionType;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 166
    .line 167
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 168
    .line 169
    .line 170
    move-result-wide v9

    .line 171
    new-instance v11, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorCategoryItemScreen;

    .line 172
    .line 173
    sget-object v2, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->POPULAR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 174
    .line 175
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 176
    .line 177
    .line 178
    move-result-wide v12

    .line 179
    invoke-static {v12, v13}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 180
    .line 181
    .line 182
    move-result-object v2

    .line 183
    invoke-static {v2}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 184
    .line 185
    .line 186
    move-result-object v2

    .line 187
    invoke-direct {v11, v2, v3, v5, v3}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorCategoryItemScreen;-><init>(Ljava/util/List;Ljava/util/List;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 188
    .line 189
    .line 190
    const/16 v18, 0xf0

    .line 191
    .line 192
    const/16 v19, 0x0

    .line 193
    .line 194
    const/4 v12, 0x0

    .line 195
    const-wide/16 v13, 0x0

    .line 196
    .line 197
    const-wide/16 v15, 0x0

    .line 198
    .line 199
    const/16 v17, 0x0

    .line 200
    .line 201
    invoke-direct/range {v6 .. v19}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 202
    .line 203
    .line 204
    invoke-virtual {v1, v6}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 205
    .line 206
    .line 207
    return-void

    .line 208
    :cond_5
    instance-of v2, v1, Lra1/c$f;

    .line 209
    .line 210
    if-eqz v2, :cond_6

    .line 211
    .line 212
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->L5:LP91/b;

    .line 213
    .line 214
    new-instance v2, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 215
    .line 216
    new-instance v7, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$RecommendedScreen;

    .line 217
    .line 218
    sget-object v3, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 219
    .line 220
    invoke-virtual {v3}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 221
    .line 222
    .line 223
    move-result-wide v3

    .line 224
    invoke-direct {v7, v3, v4}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$RecommendedScreen;-><init>(J)V

    .line 225
    .line 226
    .line 227
    const/16 v14, 0xf7

    .line 228
    .line 229
    const/4 v15, 0x0

    .line 230
    const/4 v3, 0x0

    .line 231
    const/4 v4, 0x0

    .line 232
    const-wide/16 v5, 0x0

    .line 233
    .line 234
    const/4 v8, 0x0

    .line 235
    const-wide/16 v9, 0x0

    .line 236
    .line 237
    const-wide/16 v11, 0x0

    .line 238
    .line 239
    const/4 v13, 0x0

    .line 240
    invoke-direct/range {v2 .. v15}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 241
    .line 242
    .line 243
    invoke-virtual {v1, v2}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 244
    .line 245
    .line 246
    return-void

    .line 247
    :cond_6
    instance-of v1, v1, Lra1/c$g;

    .line 248
    .line 249
    if-eqz v1, :cond_7

    .line 250
    .line 251
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->L5:LP91/b;

    .line 252
    .line 253
    new-instance v6, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 254
    .line 255
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5:LHX0/e;

    .line 256
    .line 257
    sget v7, Lpb/k;->array_slots:I

    .line 258
    .line 259
    new-array v8, v4, [Ljava/lang/Object;

    .line 260
    .line 261
    invoke-interface {v2, v7, v8}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 262
    .line 263
    .line 264
    move-result-object v7

    .line 265
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5:LHX0/e;

    .line 266
    .line 267
    sget v8, Lpb/k;->casino_category_folder_and_section_description:I

    .line 268
    .line 269
    new-array v4, v4, [Ljava/lang/Object;

    .line 270
    .line 271
    invoke-interface {v2, v8, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 272
    .line 273
    .line 274
    move-result-object v8

    .line 275
    sget-object v2, Lorg/xplatform/aggregator/api/model/PartitionType;->SLOTS:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 276
    .line 277
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 278
    .line 279
    .line 280
    move-result-wide v9

    .line 281
    new-instance v11, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorCategoryItemScreen;

    .line 282
    .line 283
    sget-object v2, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->POPULAR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 284
    .line 285
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 286
    .line 287
    .line 288
    move-result-wide v12

    .line 289
    invoke-static {v12, v13}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 290
    .line 291
    .line 292
    move-result-object v2

    .line 293
    invoke-static {v2}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 294
    .line 295
    .line 296
    move-result-object v2

    .line 297
    invoke-direct {v11, v2, v3, v5, v3}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorCategoryItemScreen;-><init>(Ljava/util/List;Ljava/util/List;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 298
    .line 299
    .line 300
    const/16 v18, 0xf0

    .line 301
    .line 302
    const/16 v19, 0x0

    .line 303
    .line 304
    const/4 v12, 0x0

    .line 305
    const-wide/16 v13, 0x0

    .line 306
    .line 307
    const-wide/16 v15, 0x0

    .line 308
    .line 309
    const/16 v17, 0x0

    .line 310
    .line 311
    invoke-direct/range {v6 .. v19}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 312
    .line 313
    .line 314
    invoke-virtual {v1, v6}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 315
    .line 316
    .line 317
    :cond_7
    return-void
.end method

.method public final B6()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Z5:LXa0/i;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-interface {v0, v1}, LXa0/i;->a(Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public final C6()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Z5:LXa0/i;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-interface {v0, v1}, LXa0/i;->a(Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public final D6(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 9
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-virtual {p0, p1, p2, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->u6(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;Lra1/c;)V

    .line 3
    .line 4
    .line 5
    iget-boolean p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    if-eqz p1, :cond_0

    .line 9
    .line 10
    goto/16 :goto_2

    .line 11
    .line 12
    :cond_0
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->G6:[Lra1/b;

    .line 13
    .line 14
    invoke-static {p1}, Lkotlin/collections/r;->f0([Ljava/lang/Object;)Ljava/util/List;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    :cond_1
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    if-eqz v2, :cond_4

    .line 27
    .line 28
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    move-object v3, v2

    .line 33
    check-cast v3, Lra1/b;

    .line 34
    .line 35
    invoke-virtual {v3}, Lra1/b;->f()Ljava/util/List;

    .line 36
    .line 37
    .line 38
    move-result-object v3

    .line 39
    invoke-static {v3}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 40
    .line 41
    .line 42
    move-result v4

    .line 43
    if-eqz v4, :cond_2

    .line 44
    .line 45
    invoke-interface {v3}, Ljava/util/Collection;->isEmpty()Z

    .line 46
    .line 47
    .line 48
    move-result v4

    .line 49
    if-eqz v4, :cond_2

    .line 50
    .line 51
    goto :goto_0

    .line 52
    :cond_2
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 53
    .line 54
    .line 55
    move-result-object v3

    .line 56
    :cond_3
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 57
    .line 58
    .line 59
    move-result v4

    .line 60
    if-eqz v4, :cond_1

    .line 61
    .line 62
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    move-result-object v4

    .line 66
    check-cast v4, LN21/k;

    .line 67
    .line 68
    invoke-virtual {v4}, LN21/k;->e()J

    .line 69
    .line 70
    .line 71
    move-result-wide v4

    .line 72
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 73
    .line 74
    .line 75
    move-result-wide v6

    .line 76
    cmp-long v8, v4, v6

    .line 77
    .line 78
    if-nez v8, :cond_3

    .line 79
    .line 80
    goto :goto_1

    .line 81
    :cond_4
    move-object v2, v0

    .line 82
    :goto_1
    check-cast v2, Lra1/b;

    .line 83
    .line 84
    if-eqz v2, :cond_5

    .line 85
    .line 86
    invoke-virtual {v2}, Lra1/b;->e()Lra1/c;

    .line 87
    .line 88
    .line 89
    move-result-object v0

    .line 90
    :cond_5
    sget-object p1, Lra1/c$f;->c:Lra1/c$f;

    .line 91
    .line 92
    invoke-static {v0, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 93
    .line 94
    .line 95
    move-result p1

    .line 96
    if-eqz p1, :cond_6

    .line 97
    .line 98
    const/16 v1, 0x1fb2

    .line 99
    .line 100
    goto :goto_2

    .line 101
    :cond_6
    sget-object p1, Lra1/c$b;->c:Lra1/c$b;

    .line 102
    .line 103
    invoke-static {v0, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 104
    .line 105
    .line 106
    move-result p1

    .line 107
    if-eqz p1, :cond_7

    .line 108
    .line 109
    const/16 v1, 0x1fbc

    .line 110
    .line 111
    goto :goto_2

    .line 112
    :cond_7
    sget-object p1, Lra1/c$d;->c:Lra1/c$d;

    .line 113
    .line 114
    invoke-static {v0, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 115
    .line 116
    .line 117
    move-result p1

    .line 118
    if-eqz p1, :cond_8

    .line 119
    .line 120
    const/16 v1, 0x1fb4

    .line 121
    .line 122
    :cond_8
    :goto_2
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->I5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 123
    .line 124
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$openGameClicked$2;

    .line 125
    .line 126
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->c6:Lorg/xbet/ui_common/utils/M;

    .line 127
    .line 128
    invoke-direct {v0, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$openGameClicked$2;-><init>(Ljava/lang/Object;)V

    .line 129
    .line 130
    .line 131
    invoke-virtual {p1, p2, v1, v0}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->u(Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;)V

    .line 132
    .line 133
    .line 134
    return-void
.end method

.method public final E6(Ljava/lang/String;Lra1/c;J)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lra1/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->E6:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p3, p4}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 4
    .line 5
    .line 6
    move-result-object p3

    .line 7
    invoke-interface {v0, p3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    check-cast p3, Lorg/xplatform/aggregator/api/model/Game;

    .line 12
    .line 13
    if-eqz p3, :cond_6

    .line 14
    .line 15
    invoke-virtual {p0, p1, p3, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->u6(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;Lra1/c;)V

    .line 16
    .line 17
    .line 18
    iget-boolean p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 19
    .line 20
    const/4 p4, 0x0

    .line 21
    const/16 v0, 0x1fb2

    .line 22
    .line 23
    if-eqz p1, :cond_0

    .line 24
    .line 25
    sget-object p1, Lra1/c$f;->c:Lra1/c$f;

    .line 26
    .line 27
    invoke-static {p2, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 28
    .line 29
    .line 30
    move-result p1

    .line 31
    if-eqz p1, :cond_5

    .line 32
    .line 33
    :goto_0
    const/16 p4, 0x1fb2

    .line 34
    .line 35
    goto :goto_1

    .line 36
    :cond_0
    sget-object p1, Lra1/c$f;->c:Lra1/c$f;

    .line 37
    .line 38
    invoke-static {p2, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 39
    .line 40
    .line 41
    move-result p1

    .line 42
    if-eqz p1, :cond_1

    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_1
    sget-object p1, Lra1/c$b;->c:Lra1/c$b;

    .line 46
    .line 47
    invoke-static {p2, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 48
    .line 49
    .line 50
    move-result p1

    .line 51
    if-eqz p1, :cond_2

    .line 52
    .line 53
    const/16 p4, 0x1fbc

    .line 54
    .line 55
    goto :goto_1

    .line 56
    :cond_2
    sget-object p1, Lra1/c$g;->c:Lra1/c$g;

    .line 57
    .line 58
    invoke-static {p2, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 59
    .line 60
    .line 61
    move-result p1

    .line 62
    if-eqz p1, :cond_3

    .line 63
    .line 64
    const/16 p4, 0x1fb6

    .line 65
    .line 66
    goto :goto_1

    .line 67
    :cond_3
    sget-object p1, Lra1/c$e;->c:Lra1/c$e;

    .line 68
    .line 69
    invoke-static {p2, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 70
    .line 71
    .line 72
    move-result p1

    .line 73
    if-eqz p1, :cond_4

    .line 74
    .line 75
    const/16 p4, 0x1fb7

    .line 76
    .line 77
    goto :goto_1

    .line 78
    :cond_4
    sget-object p1, Lra1/c$d;->c:Lra1/c$d;

    .line 79
    .line 80
    invoke-static {p2, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 81
    .line 82
    .line 83
    move-result p1

    .line 84
    if-eqz p1, :cond_5

    .line 85
    .line 86
    const/16 p4, 0x1fb4

    .line 87
    .line 88
    :cond_5
    :goto_1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->I5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 89
    .line 90
    new-instance p2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$openGameClicked$1$1;

    .line 91
    .line 92
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->c6:Lorg/xbet/ui_common/utils/M;

    .line 93
    .line 94
    invoke-direct {p2, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$openGameClicked$1$1;-><init>(Ljava/lang/Object;)V

    .line 95
    .line 96
    .line 97
    invoke-virtual {p1, p3, p4, p2}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->u(Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;)V

    .line 98
    .line 99
    .line 100
    :cond_6
    return-void
.end method

.method public final F6(JJLjava/lang/String;)V
    .locals 10
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$openScreenIfNeeded$1;

    .line 10
    .line 11
    const/4 v9, 0x0

    .line 12
    move-object v5, p0

    .line 13
    move-wide v3, p1

    .line 14
    move-wide v6, p3

    .line 15
    move-object v8, p5

    .line 16
    invoke-direct/range {v2 .. v9}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$openScreenIfNeeded$1;-><init>(JLorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;JLjava/lang/String;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/4 v4, 0x2

    .line 20
    const/4 v5, 0x0

    .line 21
    move-object v3, v2

    .line 22
    const/4 v2, 0x0

    .line 23
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final G6()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Q6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->u6:Lkotlinx/coroutines/flow/V;

    .line 7
    .line 8
    sget-object v1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 9
    .line 10
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->v6:Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->w6:Lkotlinx/coroutines/flow/V;

    .line 19
    .line 20
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 21
    .line 22
    .line 23
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z6:Lkotlinx/coroutines/flow/V;

    .line 24
    .line 25
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->A6:Lkotlinx/coroutines/flow/V;

    .line 29
    .line 30
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 31
    .line 32
    .line 33
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->t6:Lkotlinx/coroutines/flow/V;

    .line 34
    .line 35
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->n6:Lkotlinx/coroutines/flow/V;

    .line 39
    .line 40
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->o6:Lkotlinx/coroutines/flow/V;

    .line 44
    .line 45
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 46
    .line 47
    .line 48
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 49
    .line 50
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->q6:Lkotlinx/coroutines/flow/V;

    .line 54
    .line 55
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->r6:Lkotlinx/coroutines/flow/V;

    .line 59
    .line 60
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 61
    .line 62
    .line 63
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->s6:Lkotlinx/coroutines/flow/V;

    .line 64
    .line 65
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 66
    .line 67
    .line 68
    return-void
.end method

.method public final H6(Lra1/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lra1/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$setUiModel$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$setUiModel$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$setUiModel$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$setUiModel$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$setUiModel$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$setUiModel$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$setUiModel$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$setUiModel$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    const/4 v4, 0x0

    .line 35
    if-eqz v2, :cond_2

    .line 36
    .line 37
    if-ne v2, v3, :cond_1

    .line 38
    .line 39
    iget-object p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$setUiModel$1;->L$1:Ljava/lang/Object;

    .line 40
    .line 41
    check-cast p1, Lkotlinx/coroutines/sync/a;

    .line 42
    .line 43
    iget-object v0, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$setUiModel$1;->L$0:Ljava/lang/Object;

    .line 44
    .line 45
    check-cast v0, Lra1/b;

    .line 46
    .line 47
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 48
    .line 49
    .line 50
    move-object p2, p1

    .line 51
    move-object p1, v0

    .line 52
    goto :goto_1

    .line 53
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 54
    .line 55
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 56
    .line 57
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 58
    .line 59
    .line 60
    throw p1

    .line 61
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->i6:Lkotlinx/coroutines/sync/a;

    .line 65
    .line 66
    iput-object p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$setUiModel$1;->L$0:Ljava/lang/Object;

    .line 67
    .line 68
    iput-object p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$setUiModel$1;->L$1:Ljava/lang/Object;

    .line 69
    .line 70
    iput v3, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$setUiModel$1;->label:I

    .line 71
    .line 72
    invoke-interface {p2, v4, v0}, Lkotlinx/coroutines/sync/a;->g(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    if-ne v0, v1, :cond_3

    .line 77
    .line 78
    return-object v1

    .line 79
    :cond_3
    :goto_1
    :try_start_0
    invoke-virtual {p1}, Lra1/b;->f()Ljava/util/List;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 84
    .line 85
    .line 86
    move-result v0

    .line 87
    if-nez v0, :cond_4

    .line 88
    .line 89
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->G6:[Lra1/b;

    .line 90
    .line 91
    invoke-virtual {p1}, Lra1/b;->j()I

    .line 92
    .line 93
    .line 94
    move-result v1

    .line 95
    aput-object p1, v0, v1

    .line 96
    .line 97
    goto :goto_2

    .line 98
    :catchall_0
    move-exception p1

    .line 99
    goto :goto_3

    .line 100
    :cond_4
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->G6:[Lra1/b;

    .line 101
    .line 102
    invoke-virtual {p1}, Lra1/b;->j()I

    .line 103
    .line 104
    .line 105
    move-result p1

    .line 106
    aput-object v4, v0, p1

    .line 107
    .line 108
    :goto_2
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 109
    .line 110
    invoke-interface {p2, v4}, Lkotlinx/coroutines/sync/a;->h(Ljava/lang/Object;)V

    .line 111
    .line 112
    .line 113
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 114
    .line 115
    return-object p1

    .line 116
    :goto_3
    invoke-interface {p2, v4}, Lkotlinx/coroutines/sync/a;->h(Ljava/lang/Object;)V

    .line 117
    .line 118
    .line 119
    throw p1
.end method

.method public I1(Ljava/lang/String;J)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->a6:LG81/c;

    invoke-interface {v0, p1, p2, p3}, LF81/b;->I1(Ljava/lang/String;J)V

    return-void
.end method

.method public final I6(Ljava/lang/String;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->O5:Lorg/xbet/analytics/domain/scope/g0;

    .line 6
    .line 7
    invoke-virtual {v0}, Lorg/xbet/analytics/domain/scope/g0;->M()V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->U5:LpR/a;

    .line 11
    .line 12
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->MY_AGGREGATOR:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 13
    .line 14
    invoke-virtual {v1}, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->getValue()Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    invoke-interface {v0, p1, v1}, LpR/a;->f(Ljava/lang/String;Ljava/lang/String;)V

    .line 19
    .line 20
    .line 21
    :cond_0
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->N5:LwX0/C;

    .line 22
    .line 23
    invoke-virtual {p1}, LwX0/D;->a()LwX0/c;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    if-eqz p1, :cond_1

    .line 28
    .line 29
    invoke-virtual {p1}, LwX0/c;->w()V

    .line 30
    .line 31
    .line 32
    :cond_1
    return-void
.end method

.method public final J6(Ljava/lang/String;)V
    .locals 10
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->O5:Lorg/xbet/analytics/domain/scope/g0;

    .line 6
    .line 7
    invoke-virtual {v0}, Lorg/xbet/analytics/domain/scope/g0;->N()V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->U5:LpR/a;

    .line 11
    .line 12
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->MY_AGGREGATOR:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 13
    .line 14
    invoke-interface {v0, p1, v1}, LpR/a;->j(Ljava/lang/String;Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;)V

    .line 15
    .line 16
    .line 17
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    sget-object v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$showRegistrationScreen$1;->INSTANCE:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$showRegistrationScreen$1;

    .line 22
    .line 23
    new-instance v7, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$showRegistrationScreen$2;

    .line 24
    .line 25
    const/4 p1, 0x0

    .line 26
    invoke-direct {v7, p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$showRegistrationScreen$2;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    const/16 v8, 0xe

    .line 30
    .line 31
    const/4 v9, 0x0

    .line 32
    const/4 v4, 0x0

    .line 33
    const/4 v5, 0x0

    .line 34
    const/4 v6, 0x0

    .line 35
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method public final K6()V
    .locals 4

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    const/4 v2, 0x0

    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->H6:Lkotlinx/coroutines/flow/V;

    .line 8
    .line 9
    sget-object v3, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 10
    .line 11
    invoke-interface {v0, v3}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 12
    .line 13
    .line 14
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->O6:Lkotlinx/coroutines/x0;

    .line 15
    .line 16
    if-eqz v0, :cond_0

    .line 17
    .line 18
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->P6:Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    if-eqz v0, :cond_1

    .line 24
    .line 25
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    :cond_1
    return-void
.end method

.method public final L6()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->G6:[Lra1/b;

    .line 2
    .line 3
    array-length v1, v0

    .line 4
    const/4 v2, 0x0

    .line 5
    :goto_0
    if-ge v2, v1, :cond_1

    .line 6
    .line 7
    aget-object v3, v0, v2

    .line 8
    .line 9
    if-eqz v3, :cond_0

    .line 10
    .line 11
    invoke-virtual {v3}, Lra1/b;->e()Lra1/c;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    invoke-virtual {p0, v3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->O6(Lra1/c;)V

    .line 16
    .line 17
    .line 18
    :cond_0
    add-int/lit8 v2, v2, 0x1

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->l6:Lkotlinx/coroutines/flow/V;

    .line 22
    .line 23
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->G6:[Lra1/b;

    .line 24
    .line 25
    invoke-static {v1}, Lkotlin/collections/r;->f0([Ljava/lang/Object;)Ljava/util/List;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 30
    .line 31
    .line 32
    return-void
.end method

.method public final M5(Lorg/xplatform/aggregator/api/model/Game;ZLra1/c;)V
    .locals 10

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Q5:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/f;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/f;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v4, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$addFavorite$2;

    .line 17
    .line 18
    const/4 v9, 0x0

    .line 19
    move-object v7, p0

    .line 20
    move-object v8, p1

    .line 21
    move v6, p2

    .line 22
    move-object v5, p3

    .line 23
    invoke-direct/range {v4 .. v9}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$addFavorite$2;-><init>(Lra1/c;ZLorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    const/16 v6, 0xa

    .line 27
    .line 28
    const/4 v7, 0x0

    .line 29
    const/4 v2, 0x0

    .line 30
    move-object v5, v4

    .line 31
    const/4 v4, 0x0

    .line 32
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public final M6(ZLjava/util/List;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;)V"
        }
    .end annotation

    .line 1
    if-nez p1, :cond_0

    .line 2
    .line 3
    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->V6:Lkotlinx/coroutines/flow/V;

    .line 10
    .line 11
    new-instance p2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$d$a;

    .line 12
    .line 13
    new-instance v0, Lorg/xbet/uikit/components/bannercollection/a$b;

    .line 14
    .line 15
    new-instance v1, LAZ0/c;

    .line 16
    .line 17
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->K6:Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;

    .line 18
    .line 19
    sget-object v3, LAZ0/c;->c:LAZ0/c$a;

    .line 20
    .line 21
    invoke-virtual {v3, v2}, LAZ0/c$a;->a(Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;)I

    .line 22
    .line 23
    .line 24
    move-result v3

    .line 25
    invoke-direct {v1, v2, v3}, LAZ0/c;-><init>(Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;I)V

    .line 26
    .line 27
    .line 28
    invoke-direct {v0, v1}, Lorg/xbet/uikit/components/bannercollection/a$b;-><init>(LAZ0/c;)V

    .line 29
    .line 30
    .line 31
    invoke-direct {p2, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$d$a;-><init>(Lorg/xbet/uikit/components/bannercollection/a$b;)V

    .line 32
    .line 33
    .line 34
    invoke-interface {p1, p2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 35
    .line 36
    .line 37
    return-void

    .line 38
    :cond_0
    if-nez p1, :cond_1

    .line 39
    .line 40
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 41
    .line 42
    .line 43
    move-result v0

    .line 44
    if-nez v0, :cond_1

    .line 45
    .line 46
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->V6:Lkotlinx/coroutines/flow/V;

    .line 47
    .line 48
    new-instance p2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$d$a;

    .line 49
    .line 50
    new-instance v0, Lorg/xbet/uikit/components/bannercollection/a$b;

    .line 51
    .line 52
    new-instance v1, LAZ0/c;

    .line 53
    .line 54
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->K6:Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;

    .line 55
    .line 56
    sget-object v3, LAZ0/c;->c:LAZ0/c$a;

    .line 57
    .line 58
    invoke-virtual {v3, v2}, LAZ0/c$a;->a(Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;)I

    .line 59
    .line 60
    .line 61
    move-result v3

    .line 62
    invoke-direct {v1, v2, v3}, LAZ0/c;-><init>(Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;I)V

    .line 63
    .line 64
    .line 65
    invoke-direct {v0, v1}, Lorg/xbet/uikit/components/bannercollection/a$b;-><init>(LAZ0/c;)V

    .line 66
    .line 67
    .line 68
    invoke-direct {p2, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$d$a;-><init>(Lorg/xbet/uikit/components/bannercollection/a$b;)V

    .line 69
    .line 70
    .line 71
    invoke-interface {p1, p2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 72
    .line 73
    .line 74
    return-void

    .line 75
    :cond_1
    if-eqz p1, :cond_2

    .line 76
    .line 77
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 78
    .line 79
    .line 80
    move-result p1

    .line 81
    if-nez p1, :cond_2

    .line 82
    .line 83
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->V6:Lkotlinx/coroutines/flow/V;

    .line 84
    .line 85
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$d$b;

    .line 86
    .line 87
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j6:Lek0/o;

    .line 88
    .line 89
    invoke-virtual {v1}, Lek0/o;->S1()Ljava/lang/String;

    .line 90
    .line 91
    .line 92
    move-result-object v1

    .line 93
    invoke-direct {v0, p2, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$d$b;-><init>(Ljava/util/List;Ljava/lang/String;)V

    .line 94
    .line 95
    .line 96
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 97
    .line 98
    .line 99
    return-void

    .line 100
    :cond_2
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->V6:Lkotlinx/coroutines/flow/V;

    .line 101
    .line 102
    new-instance p2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$d$b;

    .line 103
    .line 104
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j6:Lek0/o;

    .line 109
    .line 110
    invoke-virtual {v1}, Lek0/o;->S1()Ljava/lang/String;

    .line 111
    .line 112
    .line 113
    move-result-object v1

    .line 114
    invoke-direct {p2, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$d$b;-><init>(Ljava/util/List;Ljava/lang/String;)V

    .line 115
    .line 116
    .line 117
    invoke-interface {p1, p2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 118
    .line 119
    .line 120
    return-void
.end method

.method public final N6(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->F6:Ljava/util/LinkedHashMap;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/util/LinkedHashMap;->values()Ljava/util/Collection;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/lang/Iterable;

    .line 8
    .line 9
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$e;

    .line 10
    .line 11
    invoke-direct {v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$e;-><init>()V

    .line 12
    .line 13
    .line 14
    invoke-static {v0, v1}, Lkotlin/collections/CollectionsKt;->l1(Ljava/lang/Iterable;Ljava/util/Comparator;)Ljava/util/List;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    const/16 v1, 0x8

    .line 19
    .line 20
    invoke-static {v0, v1}, Lkotlin/collections/CollectionsKt;->p1(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    sget-object v1, Lra1/c$b;->c:Lra1/c$b;

    .line 25
    .line 26
    const/4 v2, 0x1

    .line 27
    invoke-virtual {p0, v0, v1, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5(Ljava/util/List;Lra1/c;Z)Lra1/b;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-virtual {p0, v0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->H6(Lra1/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    if-ne p1, v0, :cond_0

    .line 40
    .line 41
    return-object p1

    .line 42
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 43
    .line 44
    return-object p1
.end method

.method public final O5(Ljava/lang/String;Z)V
    .locals 6
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    invoke-direct {v3, p0, p2, p1, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$cashbackClicked$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;ZLjava/lang/String;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    const/4 v4, 0x2

    .line 16
    const/4 v5, 0x0

    .line 17
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final O6(Lra1/c;)V
    .locals 21

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->G6:[Lra1/b;

    .line 4
    .line 5
    invoke-virtual/range {p1 .. p1}, Lra1/c;->a()I

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    aget-object v1, v1, v2

    .line 10
    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-virtual {v1}, Lra1/b;->f()Ljava/util/List;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 v1, 0x0

    .line 19
    :goto_0
    if-nez v1, :cond_1

    .line 20
    .line 21
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    :cond_1
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    .line 26
    .line 27
    .line 28
    move-result v2

    .line 29
    if-eqz v2, :cond_2

    .line 30
    .line 31
    return-void

    .line 32
    :cond_2
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Y5:Lp9/c;

    .line 33
    .line 34
    invoke-virtual {v2}, Lp9/c;->a()Z

    .line 35
    .line 36
    .line 37
    move-result v2

    .line 38
    iget-object v3, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->G6:[Lra1/b;

    .line 39
    .line 40
    invoke-virtual/range {p1 .. p1}, Lra1/c;->a()I

    .line 41
    .line 42
    .line 43
    move-result v4

    .line 44
    iget v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->M6:I

    .line 45
    .line 46
    new-instance v6, Ljava/util/ArrayList;

    .line 47
    .line 48
    const/16 v7, 0xa

    .line 49
    .line 50
    invoke-static {v1, v7}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 51
    .line 52
    .line 53
    move-result v7

    .line 54
    invoke-direct {v6, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 55
    .line 56
    .line 57
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 62
    .line 63
    .line 64
    move-result v7

    .line 65
    if-eqz v7, :cond_3

    .line 66
    .line 67
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    move-result-object v7

    .line 71
    move-object v8, v7

    .line 72
    check-cast v8, LN21/k;

    .line 73
    .line 74
    invoke-virtual {v8}, LN21/k;->e()J

    .line 75
    .line 76
    .line 77
    move-result-wide v9

    .line 78
    invoke-virtual {v0, v9, v10}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Q5(J)Z

    .line 79
    .line 80
    .line 81
    move-result v7

    .line 82
    invoke-static {v2, v7}, LQ91/b;->a(ZZ)LN21/m;

    .line 83
    .line 84
    .line 85
    move-result-object v14

    .line 86
    const/16 v19, 0x1ef

    .line 87
    .line 88
    const/16 v20, 0x0

    .line 89
    .line 90
    const-wide/16 v9, 0x0

    .line 91
    .line 92
    const/4 v11, 0x0

    .line 93
    const/4 v12, 0x0

    .line 94
    const/4 v13, 0x0

    .line 95
    const/4 v15, 0x0

    .line 96
    const/16 v16, 0x0

    .line 97
    .line 98
    const/16 v17, 0x0

    .line 99
    .line 100
    const/16 v18, 0x0

    .line 101
    .line 102
    invoke-static/range {v8 .. v20}, LN21/k;->b(LN21/k;JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;LN21/m;LL11/c;LL11/c;ILjava/lang/Integer;ILjava/lang/Object;)LN21/k;

    .line 103
    .line 104
    .line 105
    move-result-object v7

    .line 106
    invoke-interface {v6, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 107
    .line 108
    .line 109
    goto :goto_1

    .line 110
    :cond_3
    invoke-virtual/range {p0 .. p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->b6(Lra1/c;)Ljava/lang/String;

    .line 111
    .line 112
    .line 113
    move-result-object v1

    .line 114
    iget-boolean v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 115
    .line 116
    move-object/from16 v7, p1

    .line 117
    .line 118
    invoke-static {v7, v2, v5, v6, v1}, Lqa1/b;->a(Lra1/c;ZILjava/util/List;Ljava/lang/String;)Lra1/b;

    .line 119
    .line 120
    .line 121
    move-result-object v1

    .line 122
    aput-object v1, v3, v4

    .line 123
    .line 124
    return-void
.end method

.method public P1(LG81/e$b;ZZ)LVX0/i;
    .locals 1
    .param p1    # LG81/e$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->a6:LG81/c;

    .line 2
    .line 3
    invoke-interface {v0, p1, p2, p3}, LG81/e;->P1(LG81/e$b;ZZ)LVX0/i;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public P2()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "LG81/e$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->a6:LG81/c;

    invoke-interface {v0}, LG81/e;->P2()Lkotlinx/coroutines/flow/f0;

    move-result-object v0

    return-object v0
.end method

.method public final P5()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->A5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;->a()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Y5:Lp9/c;

    .line 7
    .line 8
    invoke-virtual {v0}, Lp9/c;->a()Z

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->C6:Lkotlinx/coroutines/flow/V;

    .line 13
    .line 14
    new-instance v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a$b;

    .line 15
    .line 16
    xor-int/lit8 v3, v0, 0x1

    .line 17
    .line 18
    invoke-direct {v2, v3, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a$b;-><init>(ZZ)V

    .line 19
    .line 20
    .line 21
    invoke-interface {v1, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->P6(Z)V

    .line 25
    .line 26
    .line 27
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->D6:Lkotlinx/coroutines/flow/V;

    .line 28
    .line 29
    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-interface {v1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public final P6(Z)V
    .locals 6

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$updateState$1;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    invoke-direct {v3, p0, p1, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$updateState$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;ZLkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    const/4 v4, 0x2

    .line 16
    const/4 v5, 0x0

    .line 17
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public Q(Ljava/lang/String;Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->a6:LG81/c;

    invoke-interface {v0, p1, p2}, LF81/b;->Q(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public final Q5(J)Z
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->F6:Ljava/util/LinkedHashMap;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/util/LinkedHashMap;->keySet()Ljava/util/Set;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    return p1
.end method

.method public final Q6(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Y5:Lp9/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Lp9/c;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    sget-object v1, Lra1/c$d;->c:Lra1/c$d;

    .line 8
    .line 9
    invoke-virtual {p0, p1, v1, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5(Ljava/util/List;Lra1/c;Z)Lra1/b;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->H6(Lra1/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object p2

    .line 21
    if-ne p1, p2, :cond_0

    .line 22
    .line 23
    return-object p1

    .line 24
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 25
    .line 26
    return-object p1
.end method

.method public R3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$onConnectionReload$1;

    .line 6
    .line 7
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->c6:Lorg/xbet/ui_common/utils/M;

    .line 8
    .line 9
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$onConnectionReload$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$onConnectionReload$2;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    invoke-direct {v5, p0, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$onConnectionReload$2;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    const/16 v6, 0xe

    .line 19
    .line 20
    const/4 v7, 0x0

    .line 21
    const/4 v3, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final R5()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->X6:Lkotlinx/coroutines/flow/f0;

    .line 2
    .line 3
    return-object v0
.end method

.method public final S5(Ljava/util/List;Lra1/c;Z)Lra1/b;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Lra1/c;",
            "Z)",
            "Lra1/b;"
        }
    .end annotation

    .line 1
    iget v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->M6:I

    .line 2
    .line 3
    new-instance v1, Ljava/util/ArrayList;

    .line 4
    .line 5
    const/16 v2, 0xa

    .line 6
    .line 7
    invoke-static {p1, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 12
    .line 13
    .line 14
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 19
    .line 20
    .line 21
    move-result v2

    .line 22
    if-eqz v2, :cond_1

    .line 23
    .line 24
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v2

    .line 28
    move-object v3, v2

    .line 29
    check-cast v3, Lorg/xplatform/aggregator/api/model/Game;

    .line 30
    .line 31
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->E6:Ljava/util/Map;

    .line 32
    .line 33
    invoke-virtual {v3}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 34
    .line 35
    .line 36
    move-result-wide v4

    .line 37
    invoke-static {v4, v5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 38
    .line 39
    .line 40
    move-result-object v4

    .line 41
    invoke-interface {v2, v4, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5:LHX0/e;

    .line 45
    .line 46
    sget-object v2, Lra1/c$b;->c:Lra1/c$b;

    .line 47
    .line 48
    invoke-static {p2, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 49
    .line 50
    .line 51
    move-result v2

    .line 52
    if-nez v2, :cond_0

    .line 53
    .line 54
    invoke-virtual {v3}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 55
    .line 56
    .line 57
    move-result-wide v5

    .line 58
    invoke-virtual {p0, v5, v6}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Q5(J)Z

    .line 59
    .line 60
    .line 61
    move-result v2

    .line 62
    move v7, v2

    .line 63
    goto :goto_1

    .line 64
    :cond_0
    const/4 v2, 0x1

    .line 65
    const/4 v7, 0x1

    .line 66
    :goto_1
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j6:Lek0/o;

    .line 67
    .line 68
    invoke-virtual {v2}, Lek0/o;->o()Lek0/a;

    .line 69
    .line 70
    .line 71
    move-result-object v2

    .line 72
    invoke-virtual {v2}, Lek0/a;->m()Z

    .line 73
    .line 74
    .line 75
    move-result v6

    .line 76
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j6:Lek0/o;

    .line 77
    .line 78
    invoke-virtual {v2}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 79
    .line 80
    .line 81
    move-result-object v8

    .line 82
    invoke-virtual {p2}, Lra1/c;->a()I

    .line 83
    .line 84
    .line 85
    move-result v2

    .line 86
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 87
    .line 88
    .line 89
    move-result-object v9

    .line 90
    move v5, p3

    .line 91
    invoke-static/range {v3 .. v9}, LQ91/c;->a(Lorg/xplatform/aggregator/api/model/Game;LHX0/e;ZZZLorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Ljava/lang/Integer;)LN21/k;

    .line 92
    .line 93
    .line 94
    move-result-object p3

    .line 95
    invoke-interface {v1, p3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 96
    .line 97
    .line 98
    move p3, v5

    .line 99
    goto :goto_0

    .line 100
    :cond_1
    invoke-virtual {p0, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->b6(Lra1/c;)Ljava/lang/String;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    iget-boolean p3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 105
    .line 106
    invoke-static {p2, p3, v0, v1, p1}, Lqa1/b;->a(Lra1/c;ZILjava/util/List;Ljava/lang/String;)Lra1/b;

    .line 107
    .line 108
    .line 109
    move-result-object p1

    .line 110
    return-object p1
.end method

.method public final T5()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->m6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final U5(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/String;)V
    .locals 3

    .line 1
    if-lez p3, :cond_0

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->d6:LnR/a;

    .line 4
    .line 5
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 6
    .line 7
    .line 8
    move-result-wide v1

    .line 9
    long-to-int p2, v1

    .line 10
    invoke-interface {v0, p1, p2, p3, p4}, LnR/a;->d(Ljava/lang/String;IILjava/lang/String;)V

    .line 11
    .line 12
    .line 13
    return-void

    .line 14
    :cond_0
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->d6:LnR/a;

    .line 15
    .line 16
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 17
    .line 18
    .line 19
    move-result-wide v0

    .line 20
    long-to-int p2, v0

    .line 21
    invoke-interface {p3, p1, p2, p4}, LnR/a;->h(Ljava/lang/String;ILjava/lang/String;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final V5(Lra1/c;)Ljava/lang/Long;
    .locals 2

    .line 1
    sget-object v0, Lra1/c$e;->c:Lra1/c$e;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    sget-object p1, Lorg/xplatform/aggregator/api/model/PartitionType;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 10
    .line 11
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 12
    .line 13
    .line 14
    move-result-wide v0

    .line 15
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    return-object p1

    .line 20
    :cond_0
    sget-object v0, Lra1/c$f;->c:Lra1/c$f;

    .line 21
    .line 22
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-eqz v0, :cond_1

    .line 27
    .line 28
    sget-object p1, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 29
    .line 30
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 31
    .line 32
    .line 33
    move-result-wide v0

    .line 34
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    return-object p1

    .line 39
    :cond_1
    sget-object v0, Lra1/c$g;->c:Lra1/c$g;

    .line 40
    .line 41
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 42
    .line 43
    .line 44
    move-result p1

    .line 45
    if-eqz p1, :cond_2

    .line 46
    .line 47
    sget-object p1, Lorg/xplatform/aggregator/api/model/PartitionType;->SLOTS:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 48
    .line 49
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 50
    .line 51
    .line 52
    move-result-wide v0

    .line 53
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    return-object p1

    .line 58
    :cond_2
    const/4 p1, 0x0

    .line 59
    return-object p1
.end method

.method public final W5(Lra1/c;)I
    .locals 2

    .line 1
    sget-object v0, Lra1/c$g;->c:Lra1/c$g;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_1

    .line 8
    .line 9
    sget-object v0, Lra1/c$e;->c:Lra1/c$e;

    .line 10
    .line 11
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    if-eqz p1, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 p1, -0x1

    .line 19
    return p1

    .line 20
    :cond_1
    :goto_0
    sget-object p1, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->POPULAR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 21
    .line 22
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 23
    .line 24
    .line 25
    move-result-wide v0

    .line 26
    long-to-int p1, v0

    .line 27
    return p1
.end method

.method public final X5()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->C6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final Y5()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->D6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public Z2(Ljava/lang/String;IJ)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->a6:LG81/c;

    invoke-interface {v0, p1, p2, p3, p4}, LF81/b;->Z2(Ljava/lang/String;IJ)V

    return-void
.end method

.method public final a6()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->J5:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->d()Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final b6(Lra1/c;)Ljava/lang/String;
    .locals 2

    .line 1
    instance-of v0, p1, Lra1/c$f;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5:LHX0/e;

    .line 7
    .line 8
    sget v0, Lpb/k;->recommendation:I

    .line 9
    .line 10
    new-array v1, v1, [Ljava/lang/Object;

    .line 11
    .line 12
    invoke-interface {p1, v0, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    return-object p1

    .line 17
    :cond_0
    instance-of v0, p1, Lra1/c$b;

    .line 18
    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5:LHX0/e;

    .line 22
    .line 23
    sget v0, Lpb/k;->favorites_name:I

    .line 24
    .line 25
    new-array v1, v1, [Ljava/lang/Object;

    .line 26
    .line 27
    invoke-interface {p1, v0, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    return-object p1

    .line 32
    :cond_1
    instance-of v0, p1, Lra1/c$d;

    .line 33
    .line 34
    if-eqz v0, :cond_2

    .line 35
    .line 36
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5:LHX0/e;

    .line 37
    .line 38
    sget v0, Lpb/k;->viewed_games:I

    .line 39
    .line 40
    new-array v1, v1, [Ljava/lang/Object;

    .line 41
    .line 42
    invoke-interface {p1, v0, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    return-object p1

    .line 47
    :cond_2
    instance-of v0, p1, Lra1/c$g;

    .line 48
    .line 49
    if-eqz v0, :cond_3

    .line 50
    .line 51
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5:LHX0/e;

    .line 52
    .line 53
    sget v0, Lpb/k;->slots_popular:I

    .line 54
    .line 55
    new-array v1, v1, [Ljava/lang/Object;

    .line 56
    .line 57
    invoke-interface {p1, v0, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    return-object p1

    .line 62
    :cond_3
    instance-of v0, p1, Lra1/c$e;

    .line 63
    .line 64
    if-eqz v0, :cond_4

    .line 65
    .line 66
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5:LHX0/e;

    .line 67
    .line 68
    sget v0, Lpb/k;->live_casino_popular:I

    .line 69
    .line 70
    new-array v1, v1, [Ljava/lang/Object;

    .line 71
    .line 72
    invoke-interface {p1, v0, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    return-object p1

    .line 77
    :cond_4
    instance-of v0, p1, Lra1/c$c;

    .line 78
    .line 79
    if-eqz v0, :cond_5

    .line 80
    .line 81
    check-cast p1, Lra1/c$c;

    .line 82
    .line 83
    invoke-virtual {p1}, Lra1/c$c;->c()Ljava/lang/String;

    .line 84
    .line 85
    .line 86
    move-result-object p1

    .line 87
    return-object p1

    .line 88
    :cond_5
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 89
    .line 90
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 91
    .line 92
    .line 93
    throw p1
.end method

.method public final c6()Lorg/xbet/uikit/components/lottie_empty/n;
    .locals 12
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->R5:LSX0/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 4
    .line 5
    sget v8, Lpb/k;->try_again_text:I

    .line 6
    .line 7
    sget v6, Lpb/k;->data_retrieval_error:I

    .line 8
    .line 9
    new-instance v9, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/c;

    .line 10
    .line 11
    invoke-direct {v9, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/c;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V

    .line 12
    .line 13
    .line 14
    const/16 v10, 0x5e

    .line 15
    .line 16
    const/4 v11, 0x0

    .line 17
    const/4 v2, 0x0

    .line 18
    const/4 v3, 0x0

    .line 19
    const/4 v4, 0x0

    .line 20
    const/4 v5, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    invoke-static/range {v0 .. v11}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    return-object v0
.end method

.method public d4()V
    .locals 4

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-virtual {p0, v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->a4(Z)V

    .line 3
    .line 4
    .line 5
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->C6:Lkotlinx/coroutines/flow/V;

    .line 6
    .line 7
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a$b;

    .line 8
    .line 9
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Y5:Lp9/c;

    .line 10
    .line 11
    invoke-virtual {v2}, Lp9/c;->a()Z

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    xor-int/lit8 v2, v2, 0x1

    .line 16
    .line 17
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Y5:Lp9/c;

    .line 18
    .line 19
    invoke-virtual {v3}, Lp9/c;->a()Z

    .line 20
    .line 21
    .line 22
    move-result v3

    .line 23
    invoke-direct {v1, v2, v3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$a$b;-><init>(ZZ)V

    .line 24
    .line 25
    .line 26
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public e4(Ljava/lang/Throwable;)V
    .locals 2
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->c6:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$showCustomError$1;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$showCustomError$1;-><init>(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final e6()Ljava/util/List;
    .locals 15
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x3

    .line 2
    const/4 v1, 0x2

    .line 3
    const/4 v2, 0x4

    .line 4
    const/4 v3, 0x5

    .line 5
    const/4 v4, 0x1

    .line 6
    const/4 v5, 0x0

    .line 7
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object v6

    .line 11
    iget-object v7, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Y5:Lp9/c;

    .line 12
    .line 13
    invoke-virtual {v7}, Lp9/c;->a()Z

    .line 14
    .line 15
    .line 16
    move-result v7

    .line 17
    iget-boolean v8, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 18
    .line 19
    if-eqz v8, :cond_1

    .line 20
    .line 21
    :goto_0
    if-ge v5, v3, :cond_0

    .line 22
    .line 23
    sget-object v0, Lra1/c$f;->c:Lra1/c$f;

    .line 24
    .line 25
    invoke-virtual {p0, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->g6(Lra1/c;)Lra1/b;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v6, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    add-int/2addr v5, v4

    .line 33
    goto :goto_0

    .line 34
    :cond_0
    move-object v9, p0

    .line 35
    goto/16 :goto_4

    .line 36
    .line 37
    :cond_1
    iget-boolean v8, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->J6:Z

    .line 38
    .line 39
    if-eqz v8, :cond_3

    .line 40
    .line 41
    sget-object v10, LG81/e$b$c;->a:LG81/e$b$c;

    .line 42
    .line 43
    const/4 v13, 0x2

    .line 44
    const/4 v14, 0x0

    .line 45
    const/4 v11, 0x1

    .line 46
    const/4 v12, 0x0

    .line 47
    move-object v9, p0

    .line 48
    invoke-static/range {v9 .. v14}, LG81/e$c;->a(LG81/e;LG81/e$b;ZZILjava/lang/Object;)LVX0/i;

    .line 49
    .line 50
    .line 51
    move-result-object v8

    .line 52
    if-eqz v8, :cond_2

    .line 53
    .line 54
    invoke-interface {v6, v8}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 55
    .line 56
    .line 57
    :cond_2
    sget-object v8, Lra1/c$f;->c:Lra1/c$f;

    .line 58
    .line 59
    invoke-virtual {p0, v8}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->g6(Lra1/c;)Lra1/b;

    .line 60
    .line 61
    .line 62
    move-result-object v8

    .line 63
    invoke-interface {v6, v8}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 64
    .line 65
    .line 66
    goto :goto_1

    .line 67
    :cond_3
    move-object v9, p0

    .line 68
    :goto_1
    if-eqz v7, :cond_4

    .line 69
    .line 70
    new-instance v7, Lra1/a$c;

    .line 71
    .line 72
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->i6()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 73
    .line 74
    .line 75
    move-result-object v8

    .line 76
    invoke-direct {v7, v8}, Lra1/a$c;-><init>(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;)V

    .line 77
    .line 78
    .line 79
    invoke-interface {v6, v7}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 80
    .line 81
    .line 82
    :cond_4
    iget-boolean v7, v9, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->J6:Z

    .line 83
    .line 84
    if-eqz v7, :cond_5

    .line 85
    .line 86
    new-array v2, v2, [Lra1/c;

    .line 87
    .line 88
    sget-object v3, Lra1/c$b;->c:Lra1/c$b;

    .line 89
    .line 90
    aput-object v3, v2, v5

    .line 91
    .line 92
    sget-object v3, Lra1/c$d;->c:Lra1/c$d;

    .line 93
    .line 94
    aput-object v3, v2, v4

    .line 95
    .line 96
    sget-object v3, Lra1/c$g;->c:Lra1/c$g;

    .line 97
    .line 98
    aput-object v3, v2, v1

    .line 99
    .line 100
    sget-object v1, Lra1/c$e;->c:Lra1/c$e;

    .line 101
    .line 102
    aput-object v1, v2, v0

    .line 103
    .line 104
    invoke-static {v2}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    goto :goto_2

    .line 109
    :cond_5
    new-array v3, v3, [Lra1/c;

    .line 110
    .line 111
    sget-object v7, Lra1/c$f;->c:Lra1/c$f;

    .line 112
    .line 113
    aput-object v7, v3, v5

    .line 114
    .line 115
    sget-object v5, Lra1/c$b;->c:Lra1/c$b;

    .line 116
    .line 117
    aput-object v5, v3, v4

    .line 118
    .line 119
    sget-object v4, Lra1/c$d;->c:Lra1/c$d;

    .line 120
    .line 121
    aput-object v4, v3, v1

    .line 122
    .line 123
    sget-object v1, Lra1/c$g;->c:Lra1/c$g;

    .line 124
    .line 125
    aput-object v1, v3, v0

    .line 126
    .line 127
    sget-object v0, Lra1/c$e;->c:Lra1/c$e;

    .line 128
    .line 129
    aput-object v0, v3, v2

    .line 130
    .line 131
    invoke-static {v3}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 132
    .line 133
    .line 134
    move-result-object v0

    .line 135
    :goto_2
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 136
    .line 137
    .line 138
    move-result-object v0

    .line 139
    :goto_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 140
    .line 141
    .line 142
    move-result v1

    .line 143
    if-eqz v1, :cond_6

    .line 144
    .line 145
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 146
    .line 147
    .line 148
    move-result-object v1

    .line 149
    check-cast v1, Lra1/c;

    .line 150
    .line 151
    invoke-virtual {p0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->g6(Lra1/c;)Lra1/b;

    .line 152
    .line 153
    .line 154
    move-result-object v1

    .line 155
    invoke-interface {v6, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 156
    .line 157
    .line 158
    goto :goto_3

    .line 159
    :cond_6
    :goto_4
    invoke-static {v6}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 160
    .line 161
    .line 162
    move-result-object v0

    .line 163
    return-object v0
.end method

.method public final f6()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->I5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->q()Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final g6(Lra1/c;)Lra1/b;
    .locals 8

    .line 1
    new-instance v0, Lra1/b;

    .line 2
    .line 3
    iget v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->M6:I

    .line 4
    .line 5
    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    const/4 v2, -0x1

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    invoke-virtual {p1}, Lra1/c;->a()I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    :goto_0
    iget-boolean v3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 16
    .line 17
    if-eqz v3, :cond_1

    .line 18
    .line 19
    sget-object p1, Lra1/c$f;->c:Lra1/c$f;

    .line 20
    .line 21
    :cond_1
    move-object v3, p1

    .line 22
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 23
    .line 24
    .line 25
    move-result-object v5

    .line 26
    const/4 v6, 0x1

    .line 27
    iget-boolean v7, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 28
    .line 29
    const-string v4, ""

    .line 30
    .line 31
    invoke-direct/range {v0 .. v7}, Lra1/b;-><init>(IILra1/c;Ljava/lang/String;Ljava/util/List;ZZ)V

    .line 32
    .line 33
    .line 34
    return-object v0
.end method

.method public final h6()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->V6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final i6()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->j6:Lek0/o;

    .line 2
    .line 3
    invoke-virtual {v0}, Lek0/o;->A()Lorg/xbet/remoteconfig/domain/models/VipCashbackStyleConfigType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Lqa1/a;->c(Lorg/xbet/remoteconfig/domain/models/VipCashbackStyleConfigType;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0
.end method

.method public final j6()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Q6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->V5:Le81/c;

    .line 14
    .line 15
    invoke-interface {v0}, Le81/c;->invoke()Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->B(Lkotlinx/coroutines/flow/e;)Lkotlinx/coroutines/flow/e;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$initFavoriteUpdateObserver$1;

    .line 24
    .line 25
    const/4 v2, 0x0

    .line 26
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$initFavoriteUpdateObserver$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Q5:Lm8/a;

    .line 38
    .line 39
    invoke-interface {v3}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    invoke-static {v1, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    new-instance v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$initFavoriteUpdateObserver$2;

    .line 48
    .line 49
    invoke-direct {v3, p0, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$initFavoriteUpdateObserver$2;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 50
    .line 51
    .line 52
    invoke-static {v0, v1, v3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Q6:Lkotlinx/coroutines/x0;

    .line 57
    .line 58
    return-void
.end method

.method public final k6(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x1

    .line 35
    if-eqz v2, :cond_3

    .line 36
    .line 37
    if-eq v2, v4, :cond_2

    .line 38
    .line 39
    if-ne v2, v3, :cond_1

    .line 40
    .line 41
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    goto :goto_3

    .line 45
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 46
    .line 47
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 48
    .line 49
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 50
    .line 51
    .line 52
    throw p1

    .line 53
    :cond_2
    iget-boolean p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;->Z$0:Z

    .line 54
    .line 55
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;->L$1:Ljava/lang/Object;

    .line 56
    .line 57
    check-cast v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 58
    .line 59
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;->L$0:Ljava/lang/Object;

    .line 60
    .line 61
    check-cast v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 62
    .line 63
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 64
    .line 65
    .line 66
    goto :goto_1

    .line 67
    :cond_3
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 68
    .line 69
    .line 70
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->C5:Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;

    .line 71
    .line 72
    sget-object v2, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->POPULAR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 73
    .line 74
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 75
    .line 76
    .line 77
    move-result-wide v5

    .line 78
    invoke-static {v5, v6}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 79
    .line 80
    .line 81
    move-result-object v2

    .line 82
    invoke-static {v2}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 83
    .line 84
    .line 85
    move-result-object v2

    .line 86
    iput-object p0, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;->L$0:Ljava/lang/Object;

    .line 87
    .line 88
    iput-object p0, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;->L$1:Ljava/lang/Object;

    .line 89
    .line 90
    iput-boolean p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;->Z$0:Z

    .line 91
    .line 92
    iput v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;->label:I

    .line 93
    .line 94
    invoke-virtual {p2, v2, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;->a(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 95
    .line 96
    .line 97
    move-result-object p2

    .line 98
    if-ne p2, v1, :cond_4

    .line 99
    .line 100
    goto :goto_2

    .line 101
    :cond_4
    move-object v2, p0

    .line 102
    move-object v5, v2

    .line 103
    :goto_1
    check-cast p2, Ljava/util/List;

    .line 104
    .line 105
    sget-object v6, Lra1/c$e;->c:Lra1/c$e;

    .line 106
    .line 107
    invoke-virtual {v2, p2, v6, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5(Ljava/util/List;Lra1/c;Z)Lra1/b;

    .line 108
    .line 109
    .line 110
    move-result-object p1

    .line 111
    const/4 p2, 0x0

    .line 112
    iput-object p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;->L$0:Ljava/lang/Object;

    .line 113
    .line 114
    iput-object p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;->L$1:Ljava/lang/Object;

    .line 115
    .line 116
    iput v3, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAggregator$1;->label:I

    .line 117
    .line 118
    invoke-virtual {v5, p1, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->H6(Lra1/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    if-ne p1, v1, :cond_5

    .line 123
    .line 124
    :goto_2
    return-object v1

    .line 125
    :cond_5
    :goto_3
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->p6:Lkotlinx/coroutines/flow/V;

    .line 126
    .line 127
    invoke-static {v4}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 128
    .line 129
    .line 130
    move-result-object p2

    .line 131
    invoke-interface {p1, p2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 132
    .line 133
    .line 134
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 135
    .line 136
    return-object p1
.end method

.method public final l6()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/b;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/b;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAllVirtualGames$2;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadAllVirtualGames$2;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final n6()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/a;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/a;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;

    .line 15
    .line 16
    const/4 v2, 0x0

    .line 17
    invoke-direct {v5, p0, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadCashback$2;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    const/16 v6, 0xa

    .line 21
    .line 22
    const/4 v7, 0x0

    .line 23
    const/4 v4, 0x0

    .line 24
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public final p6(Z)V
    .locals 8

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->v6()V

    .line 2
    .line 3
    .line 4
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/d;

    .line 9
    .line 10
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/d;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V

    .line 11
    .line 12
    .line 13
    new-instance v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/e;

    .line 14
    .line 15
    invoke-direct {v2, p1, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/e;-><init>(ZLorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V

    .line 16
    .line 17
    .line 18
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadOtherGames$3;

    .line 19
    .line 20
    const/4 v3, 0x0

    .line 21
    invoke-direct {v5, p0, p1, v3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadOtherGames$3;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;ZLkotlin/coroutines/e;)V

    .line 22
    .line 23
    .line 24
    const/16 v6, 0xc

    .line 25
    .line 26
    const/4 v7, 0x0

    .line 27
    const/4 v4, 0x0

    .line 28
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public final s6(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x1

    .line 35
    if-eqz v2, :cond_3

    .line 36
    .line 37
    if-eq v2, v4, :cond_2

    .line 38
    .line 39
    if-ne v2, v3, :cond_1

    .line 40
    .line 41
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    goto :goto_3

    .line 45
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 46
    .line 47
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 48
    .line 49
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 50
    .line 51
    .line 52
    throw p1

    .line 53
    :cond_2
    iget-object p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;->L$1:Ljava/lang/Object;

    .line 54
    .line 55
    check-cast p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 56
    .line 57
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;->L$0:Ljava/lang/Object;

    .line 58
    .line 59
    check-cast v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 60
    .line 61
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    goto :goto_1

    .line 65
    :cond_3
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 66
    .line 67
    .line 68
    if-eqz p1, :cond_5

    .line 69
    .line 70
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->D5:Lv81/s;

    .line 71
    .line 72
    sget-object p2, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 73
    .line 74
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 75
    .line 76
    .line 77
    move-result-wide v5

    .line 78
    iput-object p0, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;->L$0:Ljava/lang/Object;

    .line 79
    .line 80
    iput-object p0, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;->L$1:Ljava/lang/Object;

    .line 81
    .line 82
    iput v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;->label:I

    .line 83
    .line 84
    invoke-interface {p1, v5, v6, v0}, Lv81/s;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 85
    .line 86
    .line 87
    move-result-object p2

    .line 88
    if-ne p2, v1, :cond_4

    .line 89
    .line 90
    goto :goto_2

    .line 91
    :cond_4
    move-object p1, p0

    .line 92
    move-object v2, p1

    .line 93
    :goto_1
    check-cast p2, Ljava/util/List;

    .line 94
    .line 95
    sget-object v5, Lra1/c$f;->c:Lra1/c$f;

    .line 96
    .line 97
    invoke-virtual {p1, p2, v5, v4}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5(Ljava/util/List;Lra1/c;Z)Lra1/b;

    .line 98
    .line 99
    .line 100
    move-result-object p1

    .line 101
    const/4 p2, 0x0

    .line 102
    iput-object p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;->L$0:Ljava/lang/Object;

    .line 103
    .line 104
    iput-object p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;->L$1:Ljava/lang/Object;

    .line 105
    .line 106
    iput v3, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadRecommendedGames$1;->label:I

    .line 107
    .line 108
    invoke-virtual {v2, p1, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->H6(Lra1/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    if-ne p1, v1, :cond_5

    .line 113
    .line 114
    :goto_2
    return-object v1

    .line 115
    :cond_5
    :goto_3
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->n6:Lkotlinx/coroutines/flow/V;

    .line 116
    .line 117
    invoke-static {v4}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 118
    .line 119
    .line 120
    move-result-object p2

    .line 121
    invoke-interface {p1, p2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 122
    .line 123
    .line 124
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 125
    .line 126
    return-object p1
.end method

.method public final t6(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x1

    .line 35
    if-eqz v2, :cond_3

    .line 36
    .line 37
    if-eq v2, v4, :cond_2

    .line 38
    .line 39
    if-ne v2, v3, :cond_1

    .line 40
    .line 41
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    goto :goto_3

    .line 45
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 46
    .line 47
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 48
    .line 49
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 50
    .line 51
    .line 52
    throw p1

    .line 53
    :cond_2
    iget-boolean p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;->Z$0:Z

    .line 54
    .line 55
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;->L$1:Ljava/lang/Object;

    .line 56
    .line 57
    check-cast v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 58
    .line 59
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;->L$0:Ljava/lang/Object;

    .line 60
    .line 61
    check-cast v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 62
    .line 63
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 64
    .line 65
    .line 66
    goto :goto_1

    .line 67
    :cond_3
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 68
    .line 69
    .line 70
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->E5:Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;

    .line 71
    .line 72
    sget-object v2, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->POPULAR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 73
    .line 74
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 75
    .line 76
    .line 77
    move-result-wide v5

    .line 78
    invoke-static {v5, v6}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 79
    .line 80
    .line 81
    move-result-object v2

    .line 82
    invoke-static {v2}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 83
    .line 84
    .line 85
    move-result-object v2

    .line 86
    iput-object p0, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;->L$0:Ljava/lang/Object;

    .line 87
    .line 88
    iput-object p0, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;->L$1:Ljava/lang/Object;

    .line 89
    .line 90
    iput-boolean p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;->Z$0:Z

    .line 91
    .line 92
    iput v4, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;->label:I

    .line 93
    .line 94
    invoke-virtual {p2, v2, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;->a(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 95
    .line 96
    .line 97
    move-result-object p2

    .line 98
    if-ne p2, v1, :cond_4

    .line 99
    .line 100
    goto :goto_2

    .line 101
    :cond_4
    move-object v2, p0

    .line 102
    move-object v5, v2

    .line 103
    :goto_1
    check-cast p2, Ljava/util/List;

    .line 104
    .line 105
    sget-object v6, Lra1/c$g;->c:Lra1/c$g;

    .line 106
    .line 107
    invoke-virtual {v2, p2, v6, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->S5(Ljava/util/List;Lra1/c;Z)Lra1/b;

    .line 108
    .line 109
    .line 110
    move-result-object p1

    .line 111
    const/4 p2, 0x0

    .line 112
    iput-object p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;->L$0:Ljava/lang/Object;

    .line 113
    .line 114
    iput-object p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;->L$1:Ljava/lang/Object;

    .line 115
    .line 116
    iput v3, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$loadSlots$1;->label:I

    .line 117
    .line 118
    invoke-virtual {v5, p1, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->H6(Lra1/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    if-ne p1, v1, :cond_5

    .line 123
    .line 124
    :goto_2
    return-object v1

    .line 125
    :cond_5
    :goto_3
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->o6:Lkotlinx/coroutines/flow/V;

    .line 126
    .line 127
    invoke-static {v4}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 128
    .line 129
    .line 130
    move-result-object p2

    .line 131
    invoke-interface {p1, p2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 132
    .line 133
    .line 134
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 135
    .line 136
    return-object p1
.end method

.method public u1()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->a6:LG81/c;

    invoke-interface {v0}, LG81/e;->u1()V

    return-void
.end method

.method public final u6(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;Lra1/c;)V
    .locals 8

    .line 1
    invoke-virtual {p0, p3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->V5(Lra1/c;)Ljava/lang/Long;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    :goto_0
    move-wide v4, v0

    .line 12
    goto :goto_1

    .line 13
    :cond_0
    const-wide/16 v0, -0x1

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :goto_1
    invoke-virtual {p0, p3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->W5(Lra1/c;)I

    .line 17
    .line 18
    .line 19
    move-result p3

    .line 20
    const-string v3, "my_casino"

    .line 21
    .line 22
    invoke-virtual {p0, p1, p2, p3, v3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->U5(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/String;)V

    .line 23
    .line 24
    .line 25
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->O5:Lorg/xbet/analytics/domain/scope/g0;

    .line 26
    .line 27
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 28
    .line 29
    .line 30
    move-result-wide v6

    .line 31
    invoke-virtual/range {v2 .. v7}, Lorg/xbet/analytics/domain/scope/g0;->X(Ljava/lang/String;JJ)V

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public final v6()V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->O6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observeViewedGames$1;

    .line 22
    .line 23
    const/4 v0, 0x0

    .line 24
    invoke-direct {v5, p0, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observeViewedGames$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    const/4 v6, 0x2

    .line 28
    const/4 v7, 0x0

    .line 29
    const/4 v4, 0x0

    .line 30
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->O6:Lkotlinx/coroutines/x0;

    .line 35
    .line 36
    return-void
.end method

.method public w0()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LG81/e$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->a6:LG81/c;

    invoke-interface {v0}, LG81/e;->w0()Lkotlinx/coroutines/flow/e;

    move-result-object v0

    return-object v0
.end method

.method public final w6()V
    .locals 5

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Y5:Lp9/c;

    .line 7
    .line 8
    invoke-virtual {v0}, Lp9/c;->a()Z

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->I6:Lkotlinx/coroutines/flow/V;

    .line 13
    .line 14
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->V5:Le81/c;

    .line 15
    .line 16
    invoke-interface {v2}, Le81/c;->invoke()Lkotlinx/coroutines/flow/e;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    new-instance v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observedVirtualGamesIfNeeded$1;

    .line 21
    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-direct {v3, p0, v0, v4}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observedVirtualGamesIfNeeded$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;ZLkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    invoke-static {v1, v2, v3}, Lkotlinx/coroutines/flow/g;->W(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    sget-object v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observedVirtualGamesIfNeeded$2;->INSTANCE:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observedVirtualGamesIfNeeded$2;

    .line 35
    .line 36
    invoke-static {v1, v2, v3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 37
    .line 38
    .line 39
    if-eqz v0, :cond_1

    .line 40
    .line 41
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->A6:Lkotlinx/coroutines/flow/V;

    .line 42
    .line 43
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->t6:Lkotlinx/coroutines/flow/V;

    .line 44
    .line 45
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z6:Lkotlinx/coroutines/flow/V;

    .line 46
    .line 47
    new-instance v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observedVirtualGamesIfNeeded$3;

    .line 48
    .line 49
    invoke-direct {v3, p0, v4}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observedVirtualGamesIfNeeded$3;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 50
    .line 51
    .line 52
    invoke-static {v0, v1, v2, v3}, Lkotlinx/coroutines/flow/g;->p(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/o;)Lkotlinx/coroutines/flow/e;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 61
    .line 62
    .line 63
    move-result-object v2

    .line 64
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 65
    .line 66
    .line 67
    move-result-object v1

    .line 68
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 69
    .line 70
    .line 71
    return-void

    .line 72
    :cond_1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->A6:Lkotlinx/coroutines/flow/V;

    .line 73
    .line 74
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->t6:Lkotlinx/coroutines/flow/V;

    .line 75
    .line 76
    new-instance v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observedVirtualGamesIfNeeded$4;

    .line 77
    .line 78
    invoke-direct {v2, p0, v4}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observedVirtualGamesIfNeeded$4;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 79
    .line 80
    .line 81
    invoke-static {v0, v1, v2}, Lkotlinx/coroutines/flow/g;->o(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 82
    .line 83
    .line 84
    move-result-object v0

    .line 85
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 86
    .line 87
    .line 88
    move-result-object v1

    .line 89
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 90
    .line 91
    .line 92
    move-result-object v2

    .line 93
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 94
    .line 95
    .line 96
    move-result-object v1

    .line 97
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 98
    .line 99
    .line 100
    return-void
.end method

.method public final y6(Ljava/lang/String;II)V
    .locals 6
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->B6:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_1

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    move-object v2, v1

    .line 18
    check-cast v2, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 19
    .line 20
    invoke-virtual {v2}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 21
    .line 22
    .line 23
    move-result v2

    .line 24
    if-ne v2, p2, :cond_0

    .line 25
    .line 26
    goto :goto_0

    .line 27
    :cond_1
    const/4 v1, 0x0

    .line 28
    :goto_0
    check-cast v1, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 29
    .line 30
    if-nez v1, :cond_2

    .line 31
    .line 32
    return-void

    .line 33
    :cond_2
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 34
    .line 35
    if-eqz v0, :cond_3

    .line 36
    .line 37
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 38
    .line 39
    .line 40
    move-result p2

    .line 41
    :cond_3
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z5:Z

    .line 42
    .line 43
    if-eqz v0, :cond_4

    .line 44
    .line 45
    const-string v2, "cas_virtual"

    .line 46
    .line 47
    goto :goto_1

    .line 48
    :cond_4
    const-string v2, "my_casino"

    .line 49
    .line 50
    :goto_1
    if-eqz v0, :cond_5

    .line 51
    .line 52
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->h6:Lorg/xbet/analytics/domain/scope/NewsAnalytics;

    .line 53
    .line 54
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 55
    .line 56
    .line 57
    move-result v3

    .line 58
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getDeeplink()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v4

    .line 62
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getAction()Z

    .line 63
    .line 64
    .line 65
    move-result v5

    .line 66
    invoke-static {v4, v5}, LTo0/a;->a(Ljava/lang/String;Z)Ljava/lang/String;

    .line 67
    .line 68
    .line 69
    move-result-object v4

    .line 70
    const-string v5, "main_screen"

    .line 71
    .line 72
    invoke-virtual {v0, v3, v4, p3, v5}, Lorg/xbet/analytics/domain/scope/NewsAnalytics;->g(ILjava/lang/String;ILjava/lang/String;)V

    .line 73
    .line 74
    .line 75
    :cond_5
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->O5:Lorg/xbet/analytics/domain/scope/g0;

    .line 76
    .line 77
    invoke-virtual {v0, p2, p3, v2}, Lorg/xbet/analytics/domain/scope/g0;->A(IILjava/lang/String;)V

    .line 78
    .line 79
    .line 80
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->P5:LOR/a;

    .line 81
    .line 82
    invoke-interface {v0, p1, p2, p3, v2}, LOR/a;->a(Ljava/lang/String;IILjava/lang/String;)V

    .line 83
    .line 84
    .line 85
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->J5:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    .line 86
    .line 87
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 88
    .line 89
    .line 90
    move-result-object p2

    .line 91
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$onBannerClick$1;

    .line 92
    .line 93
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->c6:Lorg/xbet/ui_common/utils/M;

    .line 94
    .line 95
    invoke-direct {v0, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$onBannerClick$1;-><init>(Ljava/lang/Object;)V

    .line 96
    .line 97
    .line 98
    invoke-virtual {p1, v1, p3, p2, v0}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->f(Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V

    .line 99
    .line 100
    .line 101
    return-void
.end method

.method public final z6(Ljava/lang/String;JZLra1/c;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lra1/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->E6:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p2, p3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, Lorg/xplatform/aggregator/api/model/Game;

    .line 12
    .line 13
    if-eqz v0, :cond_1

    .line 14
    .line 15
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->O5:Lorg/xbet/analytics/domain/scope/g0;

    .line 16
    .line 17
    invoke-virtual {v1, p2, p3, p4}, Lorg/xbet/analytics/domain/scope/g0;->F(JZ)V

    .line 18
    .line 19
    .line 20
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->d6:LnR/a;

    .line 21
    .line 22
    long-to-int p3, p2

    .line 23
    invoke-interface {v1, p1, p3, p4}, LnR/a;->l(Ljava/lang/String;IZ)V

    .line 24
    .line 25
    .line 26
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->T5:Lcom/xbet/onexcore/utils/ext/c;

    .line 27
    .line 28
    invoke-interface {p1}, Lcom/xbet/onexcore/utils/ext/c;->a()Z

    .line 29
    .line 30
    .line 31
    move-result p1

    .line 32
    if-nez p1, :cond_0

    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_0
    invoke-virtual {p0, v0, p4, p5}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->M5(Lorg/xplatform/aggregator/api/model/Game;ZLra1/c;)V

    .line 36
    .line 37
    .line 38
    :cond_1
    :goto_0
    return-void
.end method
