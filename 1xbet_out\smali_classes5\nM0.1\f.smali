.class public final LnM0/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LdM0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u000b\u0018\u00002\u00020\u0001B\t\u0008\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J/\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ/\u0010\u000f\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\r2\u0006\u0010\t\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\'\u0010\u0013\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\r2\u0006\u0010\u0012\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\'\u0010\u0016\u001a\u00020\n2\u0006\u0010\u0015\u001a\u00020\r2\u0006\u0010\u0005\u001a\u00020\r2\u0006\u0010\u0007\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0016\u0010\u0017\u00a8\u0006\u0018"
    }
    d2 = {
        "LnM0/f;",
        "LdM0/a;",
        "<init>",
        "()V",
        "Lorg/xbet/statistic/stage/api/domain/TypeStageId;",
        "stageId",
        "",
        "sportId",
        "subSportId",
        "globalChampId",
        "Lq4/q;",
        "b",
        "(Lorg/xbet/statistic/stage/api/domain/TypeStageId;JJJ)Lq4/q;",
        "",
        "teamId",
        "d",
        "(Lorg/xbet/statistic/stage/api/domain/TypeStageId;JLjava/lang/String;J)Lq4/q;",
        "gameId",
        "title",
        "c",
        "(JLjava/lang/String;Ljava/lang/String;)Lq4/q;",
        "screenTitle",
        "a",
        "(Ljava/lang/String;Ljava/lang/String;J)Lq4/q;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(Ljava/lang/String;Ljava/lang/String;J)Lq4/q;
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LZM0/a;

    .line 2
    .line 3
    invoke-direct {v0, p1, p2, p3, p4}, LZM0/a;-><init>(Ljava/lang/String;Ljava/lang/String;J)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public b(Lorg/xbet/statistic/stage/api/domain/TypeStageId;JJJ)Lq4/q;
    .locals 8
    .param p1    # Lorg/xbet/statistic/stage/api/domain/TypeStageId;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LAM0/a;

    .line 2
    .line 3
    move-object v1, p1

    .line 4
    move-wide v2, p2

    .line 5
    move-wide v4, p4

    .line 6
    move-wide v6, p6

    .line 7
    invoke-direct/range {v0 .. v7}, LAM0/a;-><init>(Lorg/xbet/statistic/stage/api/domain/TypeStageId;JJJ)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public c(JLjava/lang/String;Ljava/lang/String;)Lq4/q;
    .locals 1
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LlM0/a;

    .line 2
    .line 3
    invoke-direct {v0, p1, p2, p3, p4}, LlM0/a;-><init>(JLjava/lang/String;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public d(Lorg/xbet/statistic/stage/api/domain/TypeStageId;JLjava/lang/String;J)Lq4/q;
    .locals 7
    .param p1    # Lorg/xbet/statistic/stage/api/domain/TypeStageId;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LXM0/b;

    .line 2
    .line 3
    move-object v1, p1

    .line 4
    move-wide v2, p2

    .line 5
    move-object v4, p4

    .line 6
    move-wide v5, p5

    .line 7
    invoke-direct/range {v0 .. v6}, LXM0/b;-><init>(Lorg/xbet/statistic/stage/api/domain/TypeStageId;JLjava/lang/String;J)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
