.class final Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.coupon.impl.promocode.presentation.SelectPromoCodeBottomSheetFragment$initObservers$1"
    f = "SelectPromoCodeBottomSheetFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->a3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;

    iget-object v1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;-><init>(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->invoke(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_3

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;

    .line 14
    .line 15
    sget-object v0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$a;->a:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$a;

    .line 16
    .line 17
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    const/4 v1, 0x0

    .line 22
    const/16 v2, 0x8

    .line 23
    .line 24
    if-eqz v0, :cond_0

    .line 25
    .line 26
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 27
    .line 28
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->V2()LC7/c;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    iget-object p1, p1, LC7/c;->b:Landroidx/constraintlayout/widget/Group;

    .line 33
    .line 34
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 35
    .line 36
    .line 37
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 38
    .line 39
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->V2()LC7/c;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    iget-object p1, p1, LC7/c;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 44
    .line 45
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 46
    .line 47
    .line 48
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 49
    .line 50
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->V2()LC7/c;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    iget-object p1, p1, LC7/c;->g:Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;

    .line 55
    .line 56
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 57
    .line 58
    .line 59
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 60
    .line 61
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->V2()LC7/c;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    iget-object p1, p1, LC7/c;->g:Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;

    .line 66
    .line 67
    invoke-virtual {p1}, Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;->v()V

    .line 68
    .line 69
    .line 70
    goto/16 :goto_0

    .line 71
    .line 72
    :cond_0
    sget-object v0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$b;->a:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$b;

    .line 73
    .line 74
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 75
    .line 76
    .line 77
    move-result v0

    .line 78
    if-eqz v0, :cond_1

    .line 79
    .line 80
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 81
    .line 82
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->V2()LC7/c;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    iget-object p1, p1, LC7/c;->b:Landroidx/constraintlayout/widget/Group;

    .line 87
    .line 88
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 89
    .line 90
    .line 91
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 92
    .line 93
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->V2()LC7/c;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    iget-object p1, p1, LC7/c;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 98
    .line 99
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 100
    .line 101
    .line 102
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 103
    .line 104
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->V2()LC7/c;

    .line 105
    .line 106
    .line 107
    move-result-object p1

    .line 108
    iget-object p1, p1, LC7/c;->g:Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;

    .line 109
    .line 110
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 111
    .line 112
    .line 113
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 114
    .line 115
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->V2()LC7/c;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    iget-object p1, p1, LC7/c;->g:Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;

    .line 120
    .line 121
    invoke-virtual {p1}, Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;->u()V

    .line 122
    .line 123
    .line 124
    goto :goto_0

    .line 125
    :cond_1
    instance-of v0, p1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$c;

    .line 126
    .line 127
    if-eqz v0, :cond_2

    .line 128
    .line 129
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 130
    .line 131
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->V2()LC7/c;

    .line 132
    .line 133
    .line 134
    move-result-object v0

    .line 135
    iget-object v0, v0, LC7/c;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 136
    .line 137
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 138
    .line 139
    .line 140
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 141
    .line 142
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->V2()LC7/c;

    .line 143
    .line 144
    .line 145
    move-result-object v0

    .line 146
    iget-object v0, v0, LC7/c;->b:Landroidx/constraintlayout/widget/Group;

    .line 147
    .line 148
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 149
    .line 150
    .line 151
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 152
    .line 153
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->V2()LC7/c;

    .line 154
    .line 155
    .line 156
    move-result-object v0

    .line 157
    iget-object v0, v0, LC7/c;->g:Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;

    .line 158
    .line 159
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 160
    .line 161
    .line 162
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 163
    .line 164
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->V2()LC7/c;

    .line 165
    .line 166
    .line 167
    move-result-object v0

    .line 168
    iget-object v0, v0, LC7/c;->g:Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;

    .line 169
    .line 170
    invoke-virtual {v0}, Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;->v()V

    .line 171
    .line 172
    .line 173
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 174
    .line 175
    invoke-static {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->P2(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;)LOx/a;

    .line 176
    .line 177
    .line 178
    move-result-object v0

    .line 179
    check-cast p1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$c;

    .line 180
    .line 181
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$c;->a()Ljava/util/List;

    .line 182
    .line 183
    .line 184
    move-result-object p1

    .line 185
    invoke-virtual {v0, p1}, LA4/a;->n(Ljava/lang/Object;)V

    .line 186
    .line 187
    .line 188
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 189
    .line 190
    invoke-static {p1}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->P2(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;)LOx/a;

    .line 191
    .line 192
    .line 193
    move-result-object p1

    .line 194
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->notifyDataSetChanged()V

    .line 195
    .line 196
    .line 197
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 198
    .line 199
    return-object p1

    .line 200
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 201
    .line 202
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 203
    .line 204
    .line 205
    throw p1

    .line 206
    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 207
    .line 208
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 209
    .line 210
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 211
    .line 212
    .line 213
    throw p1
.end method
