.class public final Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\t\n\u0002\u0010\u0008\n\u0002\u0008\u0015\u0008\u0080\u0008\u0018\u00002\u00020\u0001BM\u0012\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u0002\u0012\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0004\u0012\u0008\u0008\u0002\u0010\n\u001a\u00020\t\u0012\u0008\u0008\u0002\u0010\u000b\u001a\u00020\u0006\u0012\u0008\u0008\u0002\u0010\u000c\u001a\u00020\t\u00a2\u0006\u0004\u0008\r\u0010\u000eJV\u0010\u000f\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u00062\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u00042\u0008\u0008\u0002\u0010\n\u001a\u00020\t2\u0008\u0008\u0002\u0010\u000b\u001a\u00020\u00062\u0008\u0008\u0002\u0010\u000c\u001a\u00020\tH\u00c6\u0001\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0010\u0010\u0011\u001a\u00020\u0004H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0010\u0010\u0014\u001a\u00020\u0013H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u001a\u0010\u0017\u001a\u00020\t2\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0017\u0010\u0018R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u000f\u0010\u0019\u001a\u0004\u0008\u001a\u0010\u001bR\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010\u001d\u001a\u0004\u0008\u001e\u0010\u0012R\u0017\u0010\u0007\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001f\u0010 \u001a\u0004\u0008!\u0010\"R\u0017\u0010\u0008\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u001d\u001a\u0004\u0008#\u0010\u0012R\u0017\u0010\n\u001a\u00020\t8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001e\u0010$\u001a\u0004\u0008%\u0010&R\u0017\u0010\u000b\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008#\u0010 \u001a\u0004\u0008\u001f\u0010\"R\u0017\u0010\u000c\u001a\u00020\t8\u0006\u00a2\u0006\u000c\n\u0004\u0008%\u0010$\u001a\u0004\u0008\'\u0010&\u00a8\u0006("
    }
    d2 = {
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;",
        "",
        "Lorg/xbet/core/data/LuckyWheelBonusType;",
        "bonus",
        "",
        "bonusDescription",
        "",
        "winAmount",
        "currencySymbol",
        "",
        "returnHalfBonus",
        "betSum",
        "showPlayAgain",
        "<init>",
        "(Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZDZ)V",
        "a",
        "(Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZDZ)Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "other",
        "equals",
        "(Ljava/lang/Object;)Z",
        "Lorg/xbet/core/data/LuckyWheelBonusType;",
        "d",
        "()Lorg/xbet/core/data/LuckyWheelBonusType;",
        "b",
        "Ljava/lang/String;",
        "e",
        "c",
        "D",
        "i",
        "()D",
        "f",
        "Z",
        "g",
        "()Z",
        "h",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/core/data/LuckyWheelBonusType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:D

.field public final d:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Z

.field public final f:D

.field public final g:Z


# direct methods
.method public constructor <init>()V
    .locals 12

    .line 1
    const/16 v10, 0x7f

    const/4 v11, 0x0

    const/4 v1, 0x0

    const/4 v2, 0x0

    const-wide/16 v3, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const-wide/16 v7, 0x0

    const/4 v9, 0x0

    move-object v0, p0

    invoke-direct/range {v0 .. v11}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;-><init>(Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZDZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZDZ)V
    .locals 0
    .param p1    # Lorg/xbet/core/data/LuckyWheelBonusType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->a:Lorg/xbet/core/data/LuckyWheelBonusType;

    .line 4
    iput-object p2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->b:Ljava/lang/String;

    .line 5
    iput-wide p3, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->c:D

    .line 6
    iput-object p5, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->d:Ljava/lang/String;

    .line 7
    iput-boolean p6, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->e:Z

    .line 8
    iput-wide p7, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->f:D

    .line 9
    iput-boolean p9, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->g:Z

    return-void
.end method

.method public synthetic constructor <init>(Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZDZILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 3

    and-int/lit8 p11, p10, 0x1

    if-eqz p11, :cond_0

    .line 10
    sget-object p1, Lorg/xbet/core/data/LuckyWheelBonusType;->NOTHING:Lorg/xbet/core/data/LuckyWheelBonusType;

    :cond_0
    and-int/lit8 p11, p10, 0x2

    .line 11
    const-string v0, ""

    if-eqz p11, :cond_1

    move-object p2, v0

    :cond_1
    and-int/lit8 p11, p10, 0x4

    const-wide/16 v1, 0x0

    if-eqz p11, :cond_2

    move-wide p3, v1

    :cond_2
    and-int/lit8 p11, p10, 0x8

    if-eqz p11, :cond_3

    move-object p5, v0

    :cond_3
    and-int/lit8 p11, p10, 0x10

    if-eqz p11, :cond_4

    const/4 p6, 0x0

    :cond_4
    and-int/lit8 p11, p10, 0x20

    if-eqz p11, :cond_5

    move-wide p7, v1

    :cond_5
    and-int/lit8 p10, p10, 0x40

    if-eqz p10, :cond_6

    const/4 p9, 0x1

    const/4 p11, 0x1

    :goto_0
    move-wide p9, p7

    move-object p7, p5

    move p8, p6

    move-wide p5, p3

    move-object p3, p1

    move-object p4, p2

    move-object p2, p0

    goto :goto_1

    :cond_6
    move p11, p9

    goto :goto_0

    :goto_1
    invoke-direct/range {p2 .. p11}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;-><init>(Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZDZ)V

    return-void
.end method

.method public static synthetic b(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZDZILjava/lang/Object;)Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;
    .locals 0

    .line 1
    and-int/lit8 p11, p10, 0x1

    if-eqz p11, :cond_0

    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->a:Lorg/xbet/core/data/LuckyWheelBonusType;

    :cond_0
    and-int/lit8 p11, p10, 0x2

    if-eqz p11, :cond_1

    iget-object p2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->b:Ljava/lang/String;

    :cond_1
    and-int/lit8 p11, p10, 0x4

    if-eqz p11, :cond_2

    iget-wide p3, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->c:D

    :cond_2
    and-int/lit8 p11, p10, 0x8

    if-eqz p11, :cond_3

    iget-object p5, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->d:Ljava/lang/String;

    :cond_3
    and-int/lit8 p11, p10, 0x10

    if-eqz p11, :cond_4

    iget-boolean p6, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->e:Z

    :cond_4
    and-int/lit8 p11, p10, 0x20

    if-eqz p11, :cond_5

    iget-wide p7, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->f:D

    :cond_5
    and-int/lit8 p10, p10, 0x40

    if-eqz p10, :cond_6

    iget-boolean p9, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->g:Z

    :cond_6
    move p11, p9

    move-wide p9, p7

    move-object p7, p5

    move p8, p6

    move-wide p5, p3

    move-object p3, p1

    move-object p4, p2

    move-object p2, p0

    invoke-virtual/range {p2 .. p11}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->a(Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZDZ)Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final a(Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZDZ)Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;
    .locals 10
    .param p1    # Lorg/xbet/core/data/LuckyWheelBonusType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;

    move-object v1, p1

    move-object v2, p2

    move-wide v3, p3

    move-object v5, p5

    move/from16 v6, p6

    move-wide/from16 v7, p7

    move/from16 v9, p9

    invoke-direct/range {v0 .. v9}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;-><init>(Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZDZ)V

    return-object v0
.end method

.method public final c()D
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->f:D

    .line 2
    .line 3
    return-wide v0
.end method

.method public final d()Lorg/xbet/core/data/LuckyWheelBonusType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->a:Lorg/xbet/core/data/LuckyWheelBonusType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->b:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 7

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;

    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->a:Lorg/xbet/core/data/LuckyWheelBonusType;

    iget-object v3, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->a:Lorg/xbet/core/data/LuckyWheelBonusType;

    if-eq v1, v3, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->b:Ljava/lang/String;

    iget-object v3, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->b:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-wide v3, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->c:D

    iget-wide v5, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->c:D

    invoke-static {v3, v4, v5, v6}, Ljava/lang/Double;->compare(DD)I

    move-result v1

    if-eqz v1, :cond_4

    return v2

    :cond_4
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->d:Ljava/lang/String;

    iget-object v3, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->d:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_5

    return v2

    :cond_5
    iget-boolean v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->e:Z

    iget-boolean v3, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->e:Z

    if-eq v1, v3, :cond_6

    return v2

    :cond_6
    iget-wide v3, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->f:D

    iget-wide v5, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->f:D

    invoke-static {v3, v4, v5, v6}, Ljava/lang/Double;->compare(DD)I

    move-result v1

    if-eqz v1, :cond_7

    return v2

    :cond_7
    iget-boolean v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->g:Z

    iget-boolean p1, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->g:Z

    if-eq v1, p1, :cond_8

    return v2

    :cond_8
    return v0
.end method

.method public final f()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->d:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->e:Z

    .line 2
    .line 3
    return v0
.end method

.method public final h()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->g:Z

    .line 2
    .line 3
    return v0
.end method

.method public hashCode()I
    .locals 3

    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->a:Lorg/xbet/core/data/LuckyWheelBonusType;

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->b:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-wide v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->c:D

    invoke-static {v1, v2}, Landroidx/compose/ui/graphics/colorspace/F;->a(D)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->d:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->e:Z

    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-wide v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->f:D

    invoke-static {v1, v2}, Landroidx/compose/ui/graphics/colorspace/F;->a(D)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->g:Z

    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public final i()D
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->c:D

    .line 2
    .line 3
    return-wide v0
.end method

.method public toString()Ljava/lang/String;
    .locals 11
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->a:Lorg/xbet/core/data/LuckyWheelBonusType;

    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->b:Ljava/lang/String;

    iget-wide v2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->c:D

    iget-object v4, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->d:Ljava/lang/String;

    iget-boolean v5, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->e:Z

    iget-wide v6, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->f:D

    iget-boolean v8, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->g:Z

    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    const-string v10, "ViewState(bonus="

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", bonusDescription="

    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", winAmount="

    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v2, v3}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    const-string v0, ", currencySymbol="

    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", returnHalfBonus="

    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v5}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v0, ", betSum="

    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v6, v7}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    const-string v0, ", showPlayAgain="

    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v8}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
