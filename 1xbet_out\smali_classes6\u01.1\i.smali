.class public final synthetic Lu01/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lu01/j;


# direct methods
.method public synthetic constructor <init>(Lu01/j;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lu01/i;->a:Lu01/j;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lu01/i;->a:Lu01/j;

    check-cast p1, <PERSON>java/lang/Integer;

    invoke-virtual {p1}, <PERSON><PERSON><PERSON>/lang/Integer;->intValue()I

    move-result p1

    invoke-static {v0, p1}, Lu01/j;->a(Lu01/j;I)L<PERSON>lin/Unit;

    move-result-object p1

    return-object p1
.end method
