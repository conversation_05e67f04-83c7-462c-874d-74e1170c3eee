.class public final LtJ0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u001a\u0018\u00002\u00020\u0001Bi\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ/\u0010$\u001a\u00020#2\u0006\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020\u001e2\u0006\u0010!\u001a\u00020 2\u0006\u0010\"\u001a\u00020\u001eH\u0000\u00a2\u0006\u0004\u0008$\u0010%R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010&R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010(R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008)\u0010*R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008+\u0010,R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u0010.R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u00100R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00081\u00102R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u00104R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00085\u00106R\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00087\u00108R\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u0010:R\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008;\u0010<\u00a8\u0006="
    }
    d2 = {
        "LtJ0/d;",
        "LQW0/a;",
        "LQW0/c;",
        "coroutinesLib",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LHX0/e;",
        "resourceManager",
        "Li8/l;",
        "getThemeStreamUseCase",
        "LSX0/a;",
        "lottieConfigurator",
        "LTn/a;",
        "sportRepository",
        "LqJ0/a;",
        "playersStatisticLocalDataSource",
        "Lorg/xbet/onexdatabase/OnexDatabase;",
        "onexDatabase",
        "Lf8/g;",
        "serviceGenerator",
        "LEN0/f;",
        "statisticCoreFeature",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lc8/h;",
        "requestParamsDataSource",
        "<init>",
        "(LQW0/c;Lorg/xbet/ui_common/utils/M;LHX0/e;Li8/l;LSX0/a;LTn/a;LqJ0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lf8/g;LEN0/f;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;)V",
        "LwX0/c;",
        "router",
        "",
        "gameId",
        "",
        "sportId",
        "teamId",
        "LtJ0/c;",
        "a",
        "(LwX0/c;Ljava/lang/String;JLjava/lang/String;)LtJ0/c;",
        "LQW0/c;",
        "b",
        "Lorg/xbet/ui_common/utils/M;",
        "c",
        "LHX0/e;",
        "d",
        "Li8/l;",
        "e",
        "LSX0/a;",
        "f",
        "LTn/a;",
        "g",
        "LqJ0/a;",
        "h",
        "Lorg/xbet/onexdatabase/OnexDatabase;",
        "i",
        "Lf8/g;",
        "j",
        "LEN0/f;",
        "k",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "l",
        "Lc8/h;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LQW0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Li8/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:LTn/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LqJ0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lorg/xbet/onexdatabase/OnexDatabase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:LEN0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lorg/xbet/ui_common/utils/M;LHX0/e;Li8/l;LSX0/a;LTn/a;LqJ0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lf8/g;LEN0/f;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;)V
    .locals 0
    .param p1    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Li8/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LTn/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LqJ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/onexdatabase/OnexDatabase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LEN0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LtJ0/d;->a:LQW0/c;

    .line 5
    .line 6
    iput-object p2, p0, LtJ0/d;->b:Lorg/xbet/ui_common/utils/M;

    .line 7
    .line 8
    iput-object p3, p0, LtJ0/d;->c:LHX0/e;

    .line 9
    .line 10
    iput-object p4, p0, LtJ0/d;->d:Li8/l;

    .line 11
    .line 12
    iput-object p5, p0, LtJ0/d;->e:LSX0/a;

    .line 13
    .line 14
    iput-object p6, p0, LtJ0/d;->f:LTn/a;

    .line 15
    .line 16
    iput-object p7, p0, LtJ0/d;->g:LqJ0/a;

    .line 17
    .line 18
    iput-object p8, p0, LtJ0/d;->h:Lorg/xbet/onexdatabase/OnexDatabase;

    .line 19
    .line 20
    iput-object p9, p0, LtJ0/d;->i:Lf8/g;

    .line 21
    .line 22
    iput-object p10, p0, LtJ0/d;->j:LEN0/f;

    .line 23
    .line 24
    iput-object p11, p0, LtJ0/d;->k:Lorg/xbet/ui_common/utils/internet/a;

    .line 25
    .line 26
    iput-object p12, p0, LtJ0/d;->l:Lc8/h;

    .line 27
    .line 28
    return-void
.end method


# virtual methods
.method public final a(LwX0/c;Ljava/lang/String;JLjava/lang/String;)LtJ0/c;
    .locals 19
    .param p1    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, LtJ0/a;->a()LtJ0/c$a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, v0, LtJ0/d;->a:LQW0/c;

    .line 8
    .line 9
    iget-object v5, v0, LtJ0/d;->b:Lorg/xbet/ui_common/utils/M;

    .line 10
    .line 11
    iget-object v6, v0, LtJ0/d;->c:LHX0/e;

    .line 12
    .line 13
    iget-object v7, v0, LtJ0/d;->d:Li8/l;

    .line 14
    .line 15
    iget-object v8, v0, LtJ0/d;->e:LSX0/a;

    .line 16
    .line 17
    iget-object v9, v0, LtJ0/d;->f:LTn/a;

    .line 18
    .line 19
    iget-object v10, v0, LtJ0/d;->g:LqJ0/a;

    .line 20
    .line 21
    iget-object v3, v0, LtJ0/d;->j:LEN0/f;

    .line 22
    .line 23
    iget-object v11, v0, LtJ0/d;->h:Lorg/xbet/onexdatabase/OnexDatabase;

    .line 24
    .line 25
    iget-object v12, v0, LtJ0/d;->i:Lf8/g;

    .line 26
    .line 27
    iget-object v13, v0, LtJ0/d;->k:Lorg/xbet/ui_common/utils/internet/a;

    .line 28
    .line 29
    iget-object v4, v0, LtJ0/d;->l:Lc8/h;

    .line 30
    .line 31
    move-object/from16 v14, p2

    .line 32
    .line 33
    move-wide/from16 v15, p3

    .line 34
    .line 35
    move-object/from16 v17, p5

    .line 36
    .line 37
    move-object/from16 v18, v4

    .line 38
    .line 39
    move-object/from16 v4, p1

    .line 40
    .line 41
    invoke-interface/range {v1 .. v18}, LtJ0/c$a;->a(LQW0/c;LEN0/f;LwX0/c;Lorg/xbet/ui_common/utils/M;LHX0/e;Li8/l;LSX0/a;LTn/a;LqJ0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/String;JLjava/lang/String;Lc8/h;)LtJ0/c;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    return-object v1
.end method
