.class public final Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource$observeWork$2$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlinx/coroutines/flow/f;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource$observeWork$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lkotlinx/coroutines/flow/f;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;


# direct methods
.method public constructor <init>(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;)V
    .locals 0

    iput-object p1, p0, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource$observeWork$2$a;->a:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroidx/work/WorkInfo;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/work/WorkInfo;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, Landroidx/work/WorkInfo;->b()Landroidx/work/WorkInfo$State;

    .line 4
    .line 5
    .line 6
    move-result-object p2

    .line 7
    goto :goto_0

    .line 8
    :cond_0
    const/4 p2, 0x0

    .line 9
    :goto_0
    sget-object v0, Landroidx/work/WorkInfo$State;->SUCCEEDED:Landroidx/work/WorkInfo$State;

    .line 10
    .line 11
    if-ne p2, v0, :cond_1

    .line 12
    .line 13
    invoke-virtual {p1}, Landroidx/work/WorkInfo;->a()Landroidx/work/Data;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    const-string p2, "theme"

    .line 18
    .line 19
    const/4 v0, 0x0

    .line 20
    invoke-virtual {p1, p2, v0}, Landroidx/work/Data;->c(Ljava/lang/String;I)I

    .line 21
    .line 22
    .line 23
    move-result p1

    .line 24
    iget-object p2, p0, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource$observeWork$2$a;->a:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;

    .line 25
    .line 26
    invoke-static {p2}, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->c(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 27
    .line 28
    .line 29
    move-result-object p2

    .line 30
    sget-object v0, Lcom/xbet/onexcore/themes/Theme;->Companion:Lcom/xbet/onexcore/themes/Theme$a;

    .line 31
    .line 32
    invoke-virtual {v0, p1}, Lcom/xbet/onexcore/themes/Theme$a;->a(I)Lcom/xbet/onexcore/themes/Theme;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    invoke-virtual {p2, p1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 37
    .line 38
    .line 39
    :cond_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 40
    .line 41
    return-object p1
.end method

.method public bridge synthetic emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/work/WorkInfo;

    .line 2
    .line 3
    invoke-virtual {p0, p1, p2}, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource$observeWork$2$a;->a(Landroidx/work/WorkInfo;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method
