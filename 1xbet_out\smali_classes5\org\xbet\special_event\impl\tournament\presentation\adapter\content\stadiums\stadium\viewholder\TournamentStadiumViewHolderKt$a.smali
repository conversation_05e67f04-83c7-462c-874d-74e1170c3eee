.class public final Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->o(Ltx0/a;)LA4/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function1<",
        "Ljava/util/List<",
        "+",
        "Ljava/lang/Object;",
        ">;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:LB4/a;


# direct methods
.method public constructor <init>(LB4/a;LB4/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;->a:LB4/a;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;->b:LB4/a;

    .line 4
    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;->a:LB4/a;

    .line 8
    .line 9
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->e(LB4/a;)V

    .line 10
    .line 11
    .line 12
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;->a:LB4/a;

    .line 13
    .line 14
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->f(LB4/a;)V

    .line 15
    .line 16
    .line 17
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;->a:LB4/a;

    .line 18
    .line 19
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->g(LB4/a;)V

    .line 20
    .line 21
    .line 22
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;->a:LB4/a;

    .line 23
    .line 24
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->h(LB4/a;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;->a:LB4/a;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->i(LB4/a;)V

    .line 30
    .line 31
    .line 32
    return-void

    .line 33
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    .line 34
    .line 35
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 36
    .line 37
    .line 38
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    if-eqz v1, :cond_1

    .line 47
    .line 48
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    check-cast v1, Ljava/util/Collection;

    .line 53
    .line 54
    check-cast v1, Ljava/lang/Iterable;

    .line 55
    .line 56
    invoke-static {v0, v1}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 57
    .line 58
    .line 59
    goto :goto_0

    .line 60
    :cond_1
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 65
    .line 66
    .line 67
    move-result v0

    .line 68
    if-eqz v0, :cond_7

    .line 69
    .line 70
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    check-cast v0, Lux0/a$a;

    .line 75
    .line 76
    instance-of v1, v0, Lux0/a$a$a;

    .line 77
    .line 78
    if-eqz v1, :cond_2

    .line 79
    .line 80
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;->b:LB4/a;

    .line 81
    .line 82
    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->e(LB4/a;)V

    .line 83
    .line 84
    .line 85
    goto :goto_1

    .line 86
    :cond_2
    instance-of v1, v0, Lux0/a$a$b;

    .line 87
    .line 88
    if-eqz v1, :cond_3

    .line 89
    .line 90
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;->b:LB4/a;

    .line 91
    .line 92
    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->f(LB4/a;)V

    .line 93
    .line 94
    .line 95
    goto :goto_1

    .line 96
    :cond_3
    instance-of v1, v0, Lux0/a$a$c;

    .line 97
    .line 98
    if-eqz v1, :cond_4

    .line 99
    .line 100
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;->b:LB4/a;

    .line 101
    .line 102
    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->g(LB4/a;)V

    .line 103
    .line 104
    .line 105
    goto :goto_1

    .line 106
    :cond_4
    instance-of v1, v0, Lux0/a$a$d;

    .line 107
    .line 108
    if-eqz v1, :cond_5

    .line 109
    .line 110
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;->b:LB4/a;

    .line 111
    .line 112
    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->h(LB4/a;)V

    .line 113
    .line 114
    .line 115
    goto :goto_1

    .line 116
    :cond_5
    instance-of v0, v0, Lux0/a$a$e;

    .line 117
    .line 118
    if-eqz v0, :cond_6

    .line 119
    .line 120
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;->b:LB4/a;

    .line 121
    .line 122
    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt;->i(LB4/a;)V

    .line 123
    .line 124
    .line 125
    goto :goto_1

    .line 126
    :cond_6
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 127
    .line 128
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 129
    .line 130
    .line 131
    throw p1

    .line 132
    :cond_7
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/stadiums/stadium/viewholder/TournamentStadiumViewHolderKt$a;->a(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p1
.end method
