.class public final Lorg/xbet/analytics/data/repositories/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lxg/e;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/analytics/data/repositories/g$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u000c\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0007\u0018\u0000 \u001f2\u00020\u0001:\u0001\u001dB\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0017\u0010\t\u001a\u00020\u00082\u0006\u0010\u0007\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0019\u0010\u000c\u001a\u00020\u00082\u0008\u0010\u000b\u001a\u0004\u0018\u00010\u0006H\u0016\u00a2\u0006\u0004\u0008\u000c\u0010\nJ\u0019\u0010\u000e\u001a\u00020\u00082\u0008\u0010\r\u001a\u0004\u0018\u00010\u0006H\u0016\u00a2\u0006\u0004\u0008\u000e\u0010\nJ\u0017\u0010\u0010\u001a\u00020\u00082\u0006\u0010\u000f\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0010\u0010\nJ\u0017\u0010\u0012\u001a\u00020\u00082\u0006\u0010\u0011\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0012\u0010\nJ\u0017\u0010\u0014\u001a\u00020\u00082\u0006\u0010\u0013\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0014\u0010\nJ\u0017\u0010\u0017\u001a\u00020\u00082\u0006\u0010\u0016\u001a\u00020\u0015H\u0016\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0017\u0010\u001b\u001a\u00020\u00082\u0006\u0010\u001a\u001a\u00020\u0019H\u0016\u00a2\u0006\u0004\u0008\u001b\u0010\u001cR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\u001e\u00a8\u0006 "
    }
    d2 = {
        "Lorg/xbet/analytics/data/repositories/g;",
        "Lxg/e;",
        "LRf0/f;",
        "privatePreferencesWrapper",
        "<init>",
        "(LRf0/f;)V",
        "",
        "id",
        "",
        "i",
        "(Ljava/lang/String;)V",
        "promo",
        "f",
        "referral",
        "c",
        "mediaSource",
        "g",
        "postBack",
        "h",
        "siteId",
        "j",
        "",
        "time",
        "d",
        "(J)V",
        "",
        "isOrganic",
        "e",
        "(Z)V",
        "a",
        "LRf0/f;",
        "b",
        "analytics_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b:Lorg/xbet/analytics/data/repositories/g$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:LRf0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/analytics/data/repositories/g$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/analytics/data/repositories/g$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/analytics/data/repositories/g;->b:Lorg/xbet/analytics/data/repositories/g$a;

    return-void
.end method

.method public constructor <init>(LRf0/f;)V
    .locals 0
    .param p1    # LRf0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/analytics/data/repositories/g;->a:LRf0/f;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public c(Ljava/lang/String;)V
    .locals 2

    .line 1
    const-string v0, "referral_dl"

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    iget-object v1, p0, Lorg/xbet/analytics/data/repositories/g;->a:LRf0/f;

    .line 6
    .line 7
    invoke-virtual {v1, v0, p1}, LRf0/f;->putString(Ljava/lang/String;Ljava/lang/String;)V

    .line 8
    .line 9
    .line 10
    return-void

    .line 11
    :cond_0
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/g;->a:LRf0/f;

    .line 12
    .line 13
    invoke-virtual {p1, v0}, LRf0/f;->remove(Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public d(J)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/g;->a:LRf0/f;

    .line 2
    .line 3
    const-string v1, "B_TAG_RECEIVING_TIME"

    .line 4
    .line 5
    invoke-virtual {v0, v1, p1, p2}, LRf0/f;->putLong(Ljava/lang/String;J)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public e(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/g;->a:LRf0/f;

    .line 2
    .line 3
    const-string v1, "IS_ORGANIC"

    .line 4
    .line 5
    invoke-virtual {v0, v1, p1}, LRf0/f;->putBoolean(Ljava/lang/String;Z)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public f(Ljava/lang/String;)V
    .locals 2

    .line 1
    const-string v0, "promo"

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    iget-object v1, p0, Lorg/xbet/analytics/data/repositories/g;->a:LRf0/f;

    .line 6
    .line 7
    invoke-virtual {v1, v0, p1}, LRf0/f;->putString(Ljava/lang/String;Ljava/lang/String;)V

    .line 8
    .line 9
    .line 10
    return-void

    .line 11
    :cond_0
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/g;->a:LRf0/f;

    .line 12
    .line 13
    invoke-virtual {p1, v0}, LRf0/f;->remove(Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public g(Ljava/lang/String;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/g;->a:LRf0/f;

    .line 2
    .line 3
    const-string v1, "MEDIA_SOURCE_ID"

    .line 4
    .line 5
    invoke-virtual {v0, v1, p1}, LRf0/f;->putString(Ljava/lang/String;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public h(Ljava/lang/String;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/g;->a:LRf0/f;

    .line 2
    .line 3
    const-string v1, "post_back"

    .line 4
    .line 5
    invoke-virtual {v0, v1, p1}, LRf0/f;->putString(Ljava/lang/String;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public i(Ljava/lang/String;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/g;->a:LRf0/f;

    .line 2
    .line 3
    const-string v1, "APPS_FLYER_ID"

    .line 4
    .line 5
    invoke-virtual {v0, v1, p1}, LRf0/f;->putString(Ljava/lang/String;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public j(Ljava/lang/String;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/g;->a:LRf0/f;

    .line 2
    .line 3
    const-string v1, "siteId"

    .line 4
    .line 5
    invoke-virtual {v0, v1, p1}, LRf0/f;->putString(Ljava/lang/String;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method
