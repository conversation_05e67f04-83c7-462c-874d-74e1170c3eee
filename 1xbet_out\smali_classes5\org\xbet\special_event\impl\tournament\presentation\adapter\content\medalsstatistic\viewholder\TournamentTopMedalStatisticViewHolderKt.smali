.class public final Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/medalsstatistic/viewholder/TournamentTopMedalStatisticViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0003\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "LA4/c;",
        "",
        "LVX0/i;",
        "d",
        "()LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/medalsstatistic/viewholder/TournamentTopMedalStatisticViewHolderKt;->f(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LB4/a;Landroid/graphics/drawable/Drawable;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/medalsstatistic/viewholder/TournamentTopMedalStatisticViewHolderKt;->g(LB4/a;Landroid/graphics/drawable/Drawable;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/S0;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/medalsstatistic/viewholder/TournamentTopMedalStatisticViewHolderKt;->e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/S0;

    move-result-object p0

    return-object p0
.end method

.method public static final d()LA4/c;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lnx0/d;

    .line 2
    .line 3
    invoke-direct {v0}, Lnx0/d;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lnx0/e;

    .line 7
    .line 8
    invoke-direct {v1}, Lnx0/e;-><init>()V

    .line 9
    .line 10
    .line 11
    new-instance v2, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/medalsstatistic/viewholder/TournamentTopMedalStatisticViewHolderKt$tournamentTopMedalStatisticAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {v2}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/medalsstatistic/viewholder/TournamentTopMedalStatisticViewHolderKt$tournamentTopMedalStatisticAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v3, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/medalsstatistic/viewholder/TournamentTopMedalStatisticViewHolderKt$tournamentTopMedalStatisticAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/medalsstatistic/viewholder/TournamentTopMedalStatisticViewHolderKt$tournamentTopMedalStatisticAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v4, LB4/b;

    .line 19
    .line 20
    invoke-direct {v4, v0, v2, v1, v3}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v4
.end method

.method public static final e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/S0;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/S0;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/S0;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final f(LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget v1, LlZ0/h;->ic_glyph_language:I

    .line 6
    .line 7
    invoke-static {v0, v1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    sget v2, LlZ0/d;->uikitSecondary60:I

    .line 16
    .line 17
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/ExtensionsKt;->d0(Landroid/graphics/drawable/Drawable;Landroid/content/Context;I)V

    .line 18
    .line 19
    .line 20
    new-instance v1, Lnx0/f;

    .line 21
    .line 22
    invoke-direct {v1, p0, v0}, Lnx0/f;-><init>(LB4/a;Landroid/graphics/drawable/Drawable;)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p0, v1}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 26
    .line 27
    .line 28
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 29
    .line 30
    return-object p0
.end method

.method public static final g(LB4/a;Landroid/graphics/drawable/Drawable;Ljava/util/List;)Lkotlin/Unit;
    .locals 13

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, LGq0/S0;

    .line 6
    .line 7
    iget-object p2, p2, LGq0/S0;->i:Landroid/widget/TextView;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, Lmx0/b;

    .line 14
    .line 15
    invoke-virtual {v0}, Lmx0/b;->j()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-virtual {p2, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 27
    .line 28
    .line 29
    move-result-object p2

    .line 30
    check-cast p2, LGq0/S0;

    .line 31
    .line 32
    iget-object p2, p2, LGq0/S0;->j:Landroid/widget/TextView;

    .line 33
    .line 34
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, Lmx0/b;

    .line 39
    .line 40
    invoke-virtual {v0}, Lmx0/b;->getTitle()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    invoke-virtual {p2, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 45
    .line 46
    .line 47
    sget-object v1, LCX0/l;->a:LCX0/l;

    .line 48
    .line 49
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 50
    .line 51
    .line 52
    move-result-object p2

    .line 53
    check-cast p2, LGq0/S0;

    .line 54
    .line 55
    iget-object v2, p2, LGq0/S0;->f:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 56
    .line 57
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object p2

    .line 61
    check-cast p2, Lmx0/b;

    .line 62
    .line 63
    invoke-virtual {p2}, Lmx0/b;->f()Ljava/lang/String;

    .line 64
    .line 65
    .line 66
    move-result-object p2

    .line 67
    invoke-virtual {v1, p2}, LCX0/l;->K(Ljava/lang/String;)Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v3

    .line 71
    const/4 p2, 0x0

    .line 72
    new-array v7, p2, [LYW0/d;

    .line 73
    .line 74
    const/16 v11, 0xec

    .line 75
    .line 76
    const/4 v12, 0x0

    .line 77
    const/4 v5, 0x0

    .line 78
    const/4 v6, 0x0

    .line 79
    const/4 v8, 0x0

    .line 80
    const/4 v9, 0x0

    .line 81
    const/4 v10, 0x0

    .line 82
    move-object v4, p1

    .line 83
    invoke-static/range {v1 .. v12}, LCX0/l;->H(LCX0/l;Landroid/widget/ImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Z[LYW0/d;LYW0/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 84
    .line 85
    .line 86
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    check-cast p1, LGq0/S0;

    .line 91
    .line 92
    iget-object p1, p1, LGq0/S0;->b:LGq0/M;

    .line 93
    .line 94
    iget-object p1, p1, LGq0/M;->b:Landroid/widget/ImageView;

    .line 95
    .line 96
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 97
    .line 98
    .line 99
    move-result-object p2

    .line 100
    check-cast p2, Lmx0/b;

    .line 101
    .line 102
    invoke-virtual {p2}, Lmx0/b;->d()LLs0/h;

    .line 103
    .line 104
    .line 105
    move-result-object p2

    .line 106
    invoke-virtual {p2}, LLs0/h;->b()I

    .line 107
    .line 108
    .line 109
    move-result p2

    .line 110
    invoke-virtual {p1, p2}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 111
    .line 112
    .line 113
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 114
    .line 115
    .line 116
    move-result-object p1

    .line 117
    check-cast p1, LGq0/S0;

    .line 118
    .line 119
    iget-object p1, p1, LGq0/S0;->b:LGq0/M;

    .line 120
    .line 121
    iget-object p1, p1, LGq0/M;->c:Landroid/widget/TextView;

    .line 122
    .line 123
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 124
    .line 125
    .line 126
    move-result-object p2

    .line 127
    check-cast p2, Lmx0/b;

    .line 128
    .line 129
    invoke-virtual {p2}, Lmx0/b;->d()LLs0/h;

    .line 130
    .line 131
    .line 132
    move-result-object p2

    .line 133
    invoke-virtual {p2}, LLs0/h;->a()Ljava/lang/String;

    .line 134
    .line 135
    .line 136
    move-result-object p2

    .line 137
    invoke-virtual {p1, p2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 138
    .line 139
    .line 140
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 141
    .line 142
    .line 143
    move-result-object p1

    .line 144
    check-cast p1, LGq0/S0;

    .line 145
    .line 146
    iget-object p1, p1, LGq0/S0;->d:LGq0/M;

    .line 147
    .line 148
    iget-object p1, p1, LGq0/M;->b:Landroid/widget/ImageView;

    .line 149
    .line 150
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 151
    .line 152
    .line 153
    move-result-object p2

    .line 154
    check-cast p2, Lmx0/b;

    .line 155
    .line 156
    invoke-virtual {p2}, Lmx0/b;->o()LLs0/h;

    .line 157
    .line 158
    .line 159
    move-result-object p2

    .line 160
    invoke-virtual {p2}, LLs0/h;->b()I

    .line 161
    .line 162
    .line 163
    move-result p2

    .line 164
    invoke-virtual {p1, p2}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 165
    .line 166
    .line 167
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 168
    .line 169
    .line 170
    move-result-object p1

    .line 171
    check-cast p1, LGq0/S0;

    .line 172
    .line 173
    iget-object p1, p1, LGq0/S0;->d:LGq0/M;

    .line 174
    .line 175
    iget-object p1, p1, LGq0/M;->c:Landroid/widget/TextView;

    .line 176
    .line 177
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 178
    .line 179
    .line 180
    move-result-object p2

    .line 181
    check-cast p2, Lmx0/b;

    .line 182
    .line 183
    invoke-virtual {p2}, Lmx0/b;->o()LLs0/h;

    .line 184
    .line 185
    .line 186
    move-result-object p2

    .line 187
    invoke-virtual {p2}, LLs0/h;->a()Ljava/lang/String;

    .line 188
    .line 189
    .line 190
    move-result-object p2

    .line 191
    invoke-virtual {p1, p2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 192
    .line 193
    .line 194
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 195
    .line 196
    .line 197
    move-result-object p1

    .line 198
    check-cast p1, LGq0/S0;

    .line 199
    .line 200
    iget-object p1, p1, LGq0/S0;->c:LGq0/M;

    .line 201
    .line 202
    iget-object p1, p1, LGq0/M;->b:Landroid/widget/ImageView;

    .line 203
    .line 204
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 205
    .line 206
    .line 207
    move-result-object p2

    .line 208
    check-cast p2, Lmx0/b;

    .line 209
    .line 210
    invoke-virtual {p2}, Lmx0/b;->e()LLs0/h;

    .line 211
    .line 212
    .line 213
    move-result-object p2

    .line 214
    invoke-virtual {p2}, LLs0/h;->b()I

    .line 215
    .line 216
    .line 217
    move-result p2

    .line 218
    invoke-virtual {p1, p2}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 219
    .line 220
    .line 221
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 222
    .line 223
    .line 224
    move-result-object p1

    .line 225
    check-cast p1, LGq0/S0;

    .line 226
    .line 227
    iget-object p1, p1, LGq0/S0;->c:LGq0/M;

    .line 228
    .line 229
    iget-object p1, p1, LGq0/M;->c:Landroid/widget/TextView;

    .line 230
    .line 231
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 232
    .line 233
    .line 234
    move-result-object p2

    .line 235
    check-cast p2, Lmx0/b;

    .line 236
    .line 237
    invoke-virtual {p2}, Lmx0/b;->e()LLs0/h;

    .line 238
    .line 239
    .line 240
    move-result-object p2

    .line 241
    invoke-virtual {p2}, LLs0/h;->a()Ljava/lang/String;

    .line 242
    .line 243
    .line 244
    move-result-object p2

    .line 245
    invoke-virtual {p1, p2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 246
    .line 247
    .line 248
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 249
    .line 250
    .line 251
    move-result-object p1

    .line 252
    check-cast p1, LGq0/S0;

    .line 253
    .line 254
    iget-object p1, p1, LGq0/S0;->e:LGq0/M;

    .line 255
    .line 256
    iget-object p1, p1, LGq0/M;->b:Landroid/widget/ImageView;

    .line 257
    .line 258
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 259
    .line 260
    .line 261
    move-result-object p2

    .line 262
    check-cast p2, Lmx0/b;

    .line 263
    .line 264
    invoke-virtual {p2}, Lmx0/b;->s()LLs0/h;

    .line 265
    .line 266
    .line 267
    move-result-object p2

    .line 268
    invoke-virtual {p2}, LLs0/h;->b()I

    .line 269
    .line 270
    .line 271
    move-result p2

    .line 272
    invoke-virtual {p1, p2}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 273
    .line 274
    .line 275
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 276
    .line 277
    .line 278
    move-result-object p1

    .line 279
    check-cast p1, LGq0/S0;

    .line 280
    .line 281
    iget-object p1, p1, LGq0/S0;->e:LGq0/M;

    .line 282
    .line 283
    iget-object p1, p1, LGq0/M;->c:Landroid/widget/TextView;

    .line 284
    .line 285
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 286
    .line 287
    .line 288
    move-result-object p0

    .line 289
    check-cast p0, Lmx0/b;

    .line 290
    .line 291
    invoke-virtual {p0}, Lmx0/b;->s()LLs0/h;

    .line 292
    .line 293
    .line 294
    move-result-object p0

    .line 295
    invoke-virtual {p0}, LLs0/h;->a()Ljava/lang/String;

    .line 296
    .line 297
    .line 298
    move-result-object p0

    .line 299
    invoke-virtual {p1, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 300
    .line 301
    .line 302
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 303
    .line 304
    return-object p0
.end method
