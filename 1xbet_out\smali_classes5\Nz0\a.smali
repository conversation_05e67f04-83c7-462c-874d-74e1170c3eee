.class public interface abstract LNz0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LVX0/i;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LNz0/a$a;,
        LNz0/a$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0008v\u0018\u0000 \u00022\u00020\u0001:\u0001\u0003\u0082\u0001\u0002\u0004\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "LNz0/a;",
        "LVX0/i;",
        "r0",
        "a",
        "LNz0/d;",
        "LNz0/f;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final r0:LNz0/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget-object v0, LNz0/a$a;->a:LNz0/a$a;

    .line 2
    .line 3
    sput-object v0, LNz0/a;->r0:LNz0/a$a;

    .line 4
    .line 5
    return-void
.end method
