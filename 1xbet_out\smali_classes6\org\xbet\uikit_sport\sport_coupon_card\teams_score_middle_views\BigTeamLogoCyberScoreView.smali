.class public final Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LZ31/a;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0012\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u001f\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\u00072\u0006\u0010\u000c\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ7\u0010\u0016\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\u00072\u0006\u0010\u0013\u001a\u00020\u00072\u0006\u0010\u0014\u001a\u00020\u00072\u0006\u0010\u0015\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0017\u0010\u001a\u001a\u00020\r2\u0006\u0010\u0019\u001a\u00020\u0018H\u0016\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u001f\u0010\u001e\u001a\u00020\r2\u0006\u0010\u001c\u001a\u00020\u00072\u0006\u0010\u001d\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u000fJ\u001f\u0010\u001f\u001a\u00020\r2\u0006\u0010\u001c\u001a\u00020\u00072\u0006\u0010\u001d\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u001f\u0010\u000fR\u0014\u0010\"\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010!R\u0018\u0010%\u001a\u0004\u0018\u00010#8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001f\u0010$R\u0018\u0010\'\u001a\u0004\u0018\u00010#8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008&\u0010$R\u0014\u0010*\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010)R\u0016\u0010,\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008+\u0010)R\u0016\u0010.\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008-\u0010)R\u0016\u00100\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008/\u0010)R\u0016\u00102\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00081\u0010)R\u0016\u00104\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00083\u0010)\u00a8\u00065"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;",
        "Landroid/widget/FrameLayout;",
        "LZ31/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "LX31/a;",
        "scoreUiModel",
        "setScoreUiModel",
        "(LX31/a;)V",
        "winsCount",
        "totalCount",
        "a",
        "b",
        "Lorg/xbet/uikit_sport/score/SportScore;",
        "Lorg/xbet/uikit_sport/score/SportScore;",
        "scoreView",
        "Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;",
        "Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;",
        "firstTeamVictoryIndicator",
        "c",
        "secondTeamVictoryIndicator",
        "d",
        "I",
        "scoreAndVictoryIndicatorBetweenMargin",
        "e",
        "leftScoreViewPosition",
        "f",
        "topScoreViewPosition",
        "g",
        "leftSecondVictoryIndicatorPosition",
        "h",
        "topFirstVictoryIndicatorPosition",
        "i",
        "topSecondVictoryIndicatorPosition",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/uikit_sport/score/SportScore;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

.field public c:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

.field public final d:I

.field public e:I

.field public f:I

.field public g:I

.field public h:I

.field public i:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance v0, Lorg/xbet/uikit_sport/score/SportScore;

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/score/SportScore;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 6
    new-instance p1, Landroid/widget/FrameLayout$LayoutParams;

    const/4 p2, -0x2

    invoke-direct {p1, p2, p2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 7
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 8
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/score/SportScore;

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_4:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->d:I

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public final a(II)V
    .locals 7

    .line 1
    if-nez p2, :cond_1

    .line 2
    .line 3
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 4
    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    const/4 p1, 0x0

    .line 11
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 12
    .line 13
    return-void

    .line 14
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 15
    .line 16
    if-nez v0, :cond_2

    .line 17
    .line 18
    new-instance v1, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 19
    .line 20
    new-instance v2, Landroid/view/ContextThemeWrapper;

    .line 21
    .line 22
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    sget v3, Lm31/f;->Widget_SportVictoryIndicator_Left_Theme:I

    .line 27
    .line 28
    invoke-direct {v2, v0, v3}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    .line 29
    .line 30
    .line 31
    const/4 v5, 0x6

    .line 32
    const/4 v6, 0x0

    .line 33
    const/4 v3, 0x0

    .line 34
    const/4 v4, 0x0

    .line 35
    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 36
    .line 37
    .line 38
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 39
    .line 40
    .line 41
    iput-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 42
    .line 43
    :cond_2
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 44
    .line 45
    if-eqz v0, :cond_3

    .line 46
    .line 47
    invoke-virtual {v0, p2, p1}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setTotalAndWinCounts(II)V

    .line 48
    .line 49
    .line 50
    :cond_3
    return-void
.end method

.method public final b(II)V
    .locals 7

    .line 1
    if-nez p2, :cond_1

    .line 2
    .line 3
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->c:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 4
    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    const/4 p1, 0x0

    .line 11
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->c:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 12
    .line 13
    return-void

    .line 14
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->c:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 15
    .line 16
    if-nez v0, :cond_2

    .line 17
    .line 18
    new-instance v1, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 19
    .line 20
    new-instance v2, Landroid/view/ContextThemeWrapper;

    .line 21
    .line 22
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    sget v3, Lm31/f;->Widget_SportVictoryIndicator_Right_Theme:I

    .line 27
    .line 28
    invoke-direct {v2, v0, v3}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    .line 29
    .line 30
    .line 31
    const/4 v5, 0x6

    .line 32
    const/4 v6, 0x0

    .line 33
    const/4 v3, 0x0

    .line 34
    const/4 v4, 0x0

    .line 35
    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 36
    .line 37
    .line 38
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 39
    .line 40
    .line 41
    iput-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->c:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 42
    .line 43
    :cond_2
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->c:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 44
    .line 45
    if-eqz v0, :cond_3

    .line 46
    .line 47
    invoke-virtual {v0, p2, p1}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setTotalAndWinCounts(II)V

    .line 48
    .line 49
    .line 50
    :cond_3
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 1

    .line 1
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->h:I

    .line 6
    .line 7
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 8
    .line 9
    .line 10
    move-result p3

    .line 11
    add-int/2addr p3, p2

    .line 12
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 13
    .line 14
    .line 15
    move-result p4

    .line 16
    const/4 p5, 0x0

    .line 17
    invoke-virtual {p1, p5, p2, p3, p4}, Landroid/view/View;->layout(IIII)V

    .line 18
    .line 19
    .line 20
    :cond_0
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/score/SportScore;

    .line 21
    .line 22
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->e:I

    .line 23
    .line 24
    iget p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->f:I

    .line 25
    .line 26
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 27
    .line 28
    .line 29
    move-result p4

    .line 30
    add-int/2addr p4, p2

    .line 31
    iget p5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->f:I

    .line 32
    .line 33
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/score/SportScore;

    .line 34
    .line 35
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 36
    .line 37
    .line 38
    move-result v0

    .line 39
    add-int/2addr p5, v0

    .line 40
    invoke-virtual {p1, p2, p3, p4, p5}, Landroid/view/View;->layout(IIII)V

    .line 41
    .line 42
    .line 43
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->c:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 44
    .line 45
    if-eqz p1, :cond_1

    .line 46
    .line 47
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->g:I

    .line 48
    .line 49
    iget p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->i:I

    .line 50
    .line 51
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 52
    .line 53
    .line 54
    move-result p4

    .line 55
    add-int/2addr p4, p2

    .line 56
    iget p5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->i:I

    .line 57
    .line 58
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 59
    .line 60
    .line 61
    move-result v0

    .line 62
    add-int/2addr p5, v0

    .line 63
    invoke-virtual {p1, p2, p3, p4, p5}, Landroid/view/View;->layout(IIII)V

    .line 64
    .line 65
    .line 66
    :cond_1
    return-void
.end method

.method public onMeasure(II)V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/score/SportScore;

    .line 2
    .line 3
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    const/high16 v2, -0x80000000

    .line 8
    .line 9
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    invoke-virtual {v0, v1, p2}, Landroid/view/View;->measure(II)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 17
    .line 18
    if-eqz v0, :cond_0

    .line 19
    .line 20
    invoke-virtual {v0, p1, p2}, Landroid/view/View;->measure(II)V

    .line 21
    .line 22
    .line 23
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->c:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 24
    .line 25
    if-eqz v0, :cond_1

    .line 26
    .line 27
    invoke-virtual {v0, p1, p2}, Landroid/view/View;->measure(II)V

    .line 28
    .line 29
    .line 30
    :cond_1
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 31
    .line 32
    const/4 p2, 0x0

    .line 33
    if-eqz p1, :cond_2

    .line 34
    .line 35
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 36
    .line 37
    .line 38
    move-result p1

    .line 39
    goto :goto_0

    .line 40
    :cond_2
    const/4 p1, 0x0

    .line 41
    :goto_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->c:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 42
    .line 43
    if-eqz v0, :cond_3

    .line 44
    .line 45
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 46
    .line 47
    .line 48
    move-result v0

    .line 49
    goto :goto_1

    .line 50
    :cond_3
    const/4 v0, 0x0

    .line 51
    :goto_1
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 52
    .line 53
    if-eqz v1, :cond_4

    .line 54
    .line 55
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 56
    .line 57
    .line 58
    move-result v1

    .line 59
    goto :goto_2

    .line 60
    :cond_4
    const/4 v1, 0x0

    .line 61
    :goto_2
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->c:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 62
    .line 63
    if-eqz v2, :cond_5

    .line 64
    .line 65
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 66
    .line 67
    .line 68
    move-result v2

    .line 69
    goto :goto_3

    .line 70
    :cond_5
    const/4 v2, 0x0

    .line 71
    :goto_3
    iget-object v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->c:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 72
    .line 73
    if-eqz v3, :cond_6

    .line 74
    .line 75
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 76
    .line 77
    .line 78
    move-result v3

    .line 79
    goto :goto_4

    .line 80
    :cond_6
    const/4 v3, 0x0

    .line 81
    :goto_4
    add-int/2addr v3, p1

    .line 82
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->d:I

    .line 83
    .line 84
    mul-int/lit8 v4, v4, 0x2

    .line 85
    .line 86
    add-int/2addr v3, v4

    .line 87
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 88
    .line 89
    .line 90
    move-result-object v3

    .line 91
    if-lez p1, :cond_7

    .line 92
    .line 93
    if-lez v0, :cond_7

    .line 94
    .line 95
    goto :goto_5

    .line 96
    :cond_7
    const/4 v3, 0x0

    .line 97
    :goto_5
    if-eqz v3, :cond_8

    .line 98
    .line 99
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 100
    .line 101
    .line 102
    move-result p2

    .line 103
    :cond_8
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/score/SportScore;

    .line 104
    .line 105
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 106
    .line 107
    .line 108
    move-result v0

    .line 109
    add-int/2addr v0, p2

    .line 110
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/score/SportScore;

    .line 111
    .line 112
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 113
    .line 114
    .line 115
    move-result p2

    .line 116
    invoke-static {v1, v2}, Ljava/lang/Math;->max(II)I

    .line 117
    .line 118
    .line 119
    move-result v3

    .line 120
    invoke-static {p2, v3}, Ljava/lang/Math;->max(II)I

    .line 121
    .line 122
    .line 123
    move-result p2

    .line 124
    div-int/lit8 v3, v0, 0x2

    .line 125
    .line 126
    iget-object v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/score/SportScore;

    .line 127
    .line 128
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredWidth()I

    .line 129
    .line 130
    .line 131
    move-result v4

    .line 132
    div-int/lit8 v4, v4, 0x2

    .line 133
    .line 134
    sub-int/2addr v3, v4

    .line 135
    iput v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->e:I

    .line 136
    .line 137
    div-int/lit8 v3, p2, 0x2

    .line 138
    .line 139
    div-int/lit8 v1, v1, 0x2

    .line 140
    .line 141
    sub-int v1, v3, v1

    .line 142
    .line 143
    iput v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->h:I

    .line 144
    .line 145
    div-int/lit8 v2, v2, 0x2

    .line 146
    .line 147
    sub-int v1, v3, v2

    .line 148
    .line 149
    iput v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->i:I

    .line 150
    .line 151
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->d:I

    .line 152
    .line 153
    mul-int/lit8 v1, v1, 0x2

    .line 154
    .line 155
    add-int/2addr p1, v1

    .line 156
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/score/SportScore;

    .line 157
    .line 158
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 159
    .line 160
    .line 161
    move-result v1

    .line 162
    add-int/2addr p1, v1

    .line 163
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->g:I

    .line 164
    .line 165
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/score/SportScore;

    .line 166
    .line 167
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 168
    .line 169
    .line 170
    move-result p1

    .line 171
    div-int/lit8 p1, p1, 0x2

    .line 172
    .line 173
    sub-int/2addr v3, p1

    .line 174
    iput v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->f:I

    .line 175
    .line 176
    const/high16 p1, 0x40000000    # 2.0f

    .line 177
    .line 178
    invoke-static {v0, p1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 179
    .line 180
    .line 181
    move-result v0

    .line 182
    invoke-static {p2, p1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 183
    .line 184
    .line 185
    move-result p1

    .line 186
    invoke-virtual {p0, v0, p1}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 187
    .line 188
    .line 189
    return-void
.end method

.method public setScoreUiModel(LX31/a;)V
    .locals 9
    .param p1    # LX31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, LX31/a$b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, LX31/a$b;

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    const/4 p1, 0x0

    .line 9
    :goto_0
    if-nez p1, :cond_1

    .line 10
    .line 11
    return-void

    .line 12
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/score/SportScore;

    .line 13
    .line 14
    new-instance v1, Lorg/xbet/uikit_sport/score/a$a;

    .line 15
    .line 16
    invoke-virtual {p1}, LX31/a$b;->a()Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    invoke-virtual {p1}, LX31/a$b;->d()Ljava/lang/String;

    .line 21
    .line 22
    .line 23
    move-result-object v3

    .line 24
    invoke-virtual {p1}, LX31/a$b;->b()Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 25
    .line 26
    .line 27
    move-result-object v4

    .line 28
    sget-object v5, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->CHANGED:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 29
    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v7, 0x1

    .line 32
    if-ne v4, v5, :cond_2

    .line 33
    .line 34
    const/4 v4, 0x1

    .line 35
    goto :goto_1

    .line 36
    :cond_2
    const/4 v4, 0x0

    .line 37
    :goto_1
    invoke-virtual {p1}, LX31/a$b;->e()Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 38
    .line 39
    .line 40
    move-result-object v8

    .line 41
    if-ne v8, v5, :cond_3

    .line 42
    .line 43
    const/4 v6, 0x1

    .line 44
    :cond_3
    invoke-direct {v1, v2, v3, v4, v6}, Lorg/xbet/uikit_sport/score/a$a;-><init>(Ljava/lang/String;Ljava/lang/String;ZZ)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/score/SportScore;->setScore(Lorg/xbet/uikit_sport/score/a;)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {p1}, LX31/a$b;->c()LX31/g;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    invoke-virtual {v0}, LX31/g;->a()I

    .line 55
    .line 56
    .line 57
    move-result v0

    .line 58
    invoke-virtual {p1}, LX31/a$b;->c()LX31/g;

    .line 59
    .line 60
    .line 61
    move-result-object v1

    .line 62
    invoke-virtual {v1}, LX31/g;->b()I

    .line 63
    .line 64
    .line 65
    move-result v1

    .line 66
    invoke-virtual {p0, v1, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->a(II)V

    .line 67
    .line 68
    .line 69
    invoke-virtual {p1}, LX31/a$b;->f()LX31/g;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    invoke-virtual {v0}, LX31/g;->a()I

    .line 74
    .line 75
    .line 76
    move-result v0

    .line 77
    invoke-virtual {p1}, LX31/a$b;->f()LX31/g;

    .line 78
    .line 79
    .line 80
    move-result-object p1

    .line 81
    invoke-virtual {p1}, LX31/g;->b()I

    .line 82
    .line 83
    .line 84
    move-result p1

    .line 85
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/BigTeamLogoCyberScoreView;->b(II)V

    .line 86
    .line 87
    .line 88
    return-void
.end method
